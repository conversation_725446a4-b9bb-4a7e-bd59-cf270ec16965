# Connected Workers - Microservices Architecture

## Overview

This document outlines the microservices architecture for the Connected Workers system, breaking down the workflow request process into distinct phases.

## Core Microservices

1. *User Service*: Manages user data, authentication, and organizational hierarchy
2. *Workflow Engine*: Core engine defining workflow rules and logic
3. *Workflow Service*: Manages workflow instances and their execution
4. *Request Service*: Handles workflow requests lifecycle
5. *Dynamic Form Service*: Manages dynamic forms and their validation
6. *Document Template Service*: Manages document templates
7. *Document Generation Service*: Generates final documents
8. *Notification Service*: Handles email and SMS notifications

## Global Communication Schema

### High-Level Architecture

```mermaid
graph TB
    subgraph Frontend["Frontend Application"]
        UI[UI/Client]
    end

    subgraph ServiceBus["Azure Service Bus"]
        WQ[Workflow Queue]
        NQ[Notification Queue]
        DQ[Document Queue]
        DTQ[Document Template Queue]
    end

    subgraph Storage["Azure Storage"]
        BLOB[Blob Storage]
        COSMOS[Cosmos DB]
    end

    subgraph CoreServices["Core Services"]
        RS[Request Service]
        WFS[Workflow Service]
        WFE[Workflow Engine]
        US[User Service]
        DFS[Dynamic Forms Service]
    end

    subgraph DocumentServices["Document Services"]
        DTS[Document Template Service]
        DGS[Document Generation Service]
    end

    subgraph NotificationServices["Notification Services"]
        NS[Notification Service]
    end

    %% Frontend connections
    UI --> RS
    UI --> WFS
    UI --> US
    UI --> DFS

    %% Core service interconnections
    RS --> WFS
    RS --> US
    RS --> DFS
    WFS --> WFE
    
    %% Service Bus connections
    RS --> WQ
    WFS --> WQ
    WFS --> DTQ
    WFS --> NQ
    DTS --> DQ
    DGS --> WQ
    
    %% Storage connections
    DGS --> BLOB
    RS --> COSMOS
    WFS --> COSMOS
    DFS --> COSMOS
    
    %% Queue consumers
    WQ --> WFS
    DTQ --> DTS
    DQ --> DGS
    NQ --> NS

    classDef service fill:#f9f,stroke:#333,stroke-width:2px
    classDef queue fill:#bbf,stroke:#333,stroke-width:2px
    classDef storage fill:#bfb,stroke:#333,stroke-width:2px
    
    class RS,WFS,WFE,US,DFS,DTS,DGS,NS service
    class WQ,NQ,DQ,DTQ queue
    class BLOB,COSMOS storage
```

### Detailed Communication Flow

```mermaid
sequenceDiagram
    participant UI as Frontend
    participant RS as Request Service
    participant WFS as Workflow Service
    participant WFE as Workflow Engine
    participant US as User Service
    participant DFS as Dynamic Forms
    participant DTS as Doc Template Service
    participant DGS as Doc Gen Service
    participant NS as Notification Service
    participant SB as Service Bus
    participant ST as Storage

    %% HTTP Communication
    rect rgb(200,200,255)
    Note over UI,NS: Synchronous (HTTP) Communication
    UI->>US: Authentication & User Data
    UI->>WFS: Workflow Definitions
    UI->>DFS: Form Definitions
    UI->>RS: Request Operations
    RS->>WFS: Workflow Validation
    RS->>US: User Validation
    RS->>DFS: Form Validation
    end

    %% Event-Based Communication
    rect rgb(255,200,200)
    Note over UI,NS: Asynchronous (Service Bus) Communication
    RS->>SB: workflow.create
    SB->>WFS: Process workflow
    WFS->>SB: notifyApproval
    SB->>NS: Send notifications
    WFS->>SB: renderDocument
    SB->>DTS: Process template
    DTS->>SB: documentRendered
    SB->>DGS: Generate document
    DGS->>ST: Store document
    DGS->>SB: document.generated
    SB->>WFS: Update workflow
    end

    %% Storage Operations
    rect rgb(200,255,200)
    Note over UI,NS: Storage Operations
    RS->>ST: Store requests
    WFS->>ST: Store workflow instances
    DFS->>ST: Store form data
    DGS->>ST: Store documents
    end
```

### Communication Types

1. **HTTP Communication (Synchronous)**
   - User authentication and data retrieval
   - Workflow definitions and validation
   - Form operations and validation
   - Request creation and status updates
   - Instance queries and updates

2. **Service Bus Events (Asynchronous)**
   - Workflow state changes
   - Document generation pipeline
   - Notification triggers
   - Status updates

3. **Storage Operations**
   - Document storage in Blob Storage
   - Metadata and state in Cosmos DB
   - Form data persistence
   - Workflow instance data

### Queue Topics and Routing

```mermaid
graph LR
    subgraph Queues
        WQ[Workflow Queue]
        NQ[Notification Queue]
        DQ[Document Queue]
        DTQ[Doc Template Queue]
    end

    subgraph Events
        E1[workflow.create]
        E2[notifyApproval]
        E3[renderDocument]
        E4[docRendered]
        E5[document.generated]
    end

    E1 --> WQ
    E2 --> NQ
    E3 --> DTQ
    E4 --> DQ
    E5 --> WQ

    classDef queue fill:#bbf,stroke:#333,stroke-width:2px
    classDef event fill:#fbf,stroke:#333,stroke-width:2px
    
    class WQ,NQ,DQ,DTQ queue
    class E1,E2,E3,E4,E5 event
```

## Phase 1: Initial Data Loading

```mermaid
sequenceDiagram
    participant TL as Team Lead
    participant US as User Service
    participant WFS as Workflow Service

    Note over TL, WFS: After login, TL needs to load initial data

    TL->>+US: GET /users?supervisorId={TL_id}
    Note right of US: Get operators under supervision
    US-->>-TL: Returns [{id, fullName}, ...]

    TL->>+WFS: GET /workflows?role={TL_role}
    Note right of WFS: Get available workflows<br/>filtered by TL's role
    WFS-->>-TL: Returns [{<br/>workflowId,<br/>name,<br/>formId?,<br/>canOnBehalf<br/>}, ...]
```

### Phase 1 Details

- Team Lead retrieves necessary data after login
- Gets list of operators under their supervision
- Retrieves available workflows based on their role
- Workflow data includes whether requests can be made on behalf of others

## Phase 2: Form Loading and Validation

```mermaid
sequenceDiagram
    participant TL as Team Lead
    participant DFS as Dynamic Form Service

    Note over TL, DFS: If selected workflow has formId

    TL->>+DFS: GET /form/{formId}
    Note right of DFS: Get form structure<br/>and validation rules
    DFS-->>-TL: Returns form definition

    Note over TL: Frontend validates<br/>form data as user types
```

### Phase 2 Details

- Triggered when workflow with formId is selected
- Form definition includes structure and validation rules
- Frontend performs initial validation during data entry

## Phase 3: Request Creation and Validation

```mermaid
sequenceDiagram
    participant TL as Team Lead
    participant RS as Request Service
    participant WFS as Workflow Service
    participant US as User Service
    participant DFS as Dynamic Form Service
    participant SB as Service Bus

    TL->>+RS: POST /request/create
    Note right of TL: {<br/>workflowId,<br/>formId?,<br/>formData?,<br/>operatorId?<br/>}

    RS->>+WFS: GET /workflow/validate
    Note right of RS: Validate workflow,<br/>roles, and onBehalf
    WFS-->>-RS: Returns validation result

    alt onBehalf is true
        RS->>+US: GET /users/validate-supervisor
        Note right of RS: Verify TL supervises<br/>selected operator
        US-->>-RS: Returns validation result
    end

    alt has formData
        RS->>+DFS: POST /form/validate
        Note right of RS: Validate form data
        DFS-->>-RS: Returns validation result
    end

    RS->>RS: Save request (status: pending)
    RS->>SB: Publish workflow.create
    Note right of RS: Event published to<br/>workflow queue
    RS-->>-TL: Request created (200 OK)
```

### Phase 3 Details

- Comprehensive request validation process
- Multiple synchronous validations via HTTP
- Asynchronous event publishing for workflow creation
- Status tracking begins

## Phase 4: Workflow Processing

```mermaid
sequenceDiagram
    participant SB as Service Bus
    participant WFS as Workflow Service
    participant WE as Workflow Engine
    participant DTS as Doc Template Service
    participant DGS as Doc Gen Service
    participant NS as Notification Service
    participant BS as Blob Storage

    SB->>+WFS: Consume workflow.create
    WFS->>+WE: GET /workflow/{id}
    WE-->>-WFS: Returns workflow definition

    WFS->>WFS: Create workflow instance

    alt Next node is approval
        WFS->>SB: Publish notifyApproval
        Note right of WFS: {<br/>to: approver,<br/>type: approve,<br/>data: {...}<br/>}
        SB->>NS: Consume notifyApproval
        NS->>NS: Send notifications
    else Next node is generate-document
        WFS->>SB: Publish renderDocument
        SB->>DTS: Consume renderDocument
        DTS->>DTS: Render HTML
        DTS->>SB: Publish docRendered
        SB->>DGS: Consume docRendered
        DGS->>DGS: Generate PDF
        DGS->>BS: Store document
        DGS->>SB: Publish document.generated
        Note right of DGS: {<br/>url,<br/>instanceId,<br/>nodeId<br/>}
        SB->>WFS: Consume document.generated
    end
```

### Phase 4 Details

- Asynchronous workflow processing
- Multiple specialized services for document handling
- Event-driven communication via Service Bus
- Parallel processing capabilities

## Phase 5: Approval Process

```mermaid
sequenceDiagram
    participant TL as Team Lead
    participant WFS as Workflow Service
    participant DFS as Dynamic Form Service
    participant RS as Request Service
    participant NS as Notification Service
    participant SB as Service Bus

    TL->>+WFS: GET /workflow/instances?status=pending_approve
    WFS-->>-TL: Returns pending approvals

    TL->>+WFS: GET /workflow/instance/{id}
    WFS-->>-TL: Returns instance details

    alt has formId
        TL->>+DFS: GET /form/{formId}
        DFS-->>-TL: Returns form structure
    end

    TL->>+WFS: PUT /workflow/instance/{id}/handleDecision
    Note right of TL: {<br/>decision,<br/>formData?,<br/>reason?<br/>}

    alt decision is reject
        WFS->>WFS: Update instance status
        WFS->>RS: Update request status
        WFS->>SB: Publish notifyRejection
        SB->>NS: Send rejection notification
    else decision is approve
        WFS->>WFS: Update status
        WFS->>WFS: Process next node
    end

    WFS-->>-TL: Decision processed
```

### Phase 5 Details

- Approval workflow management
- Form data handling for approval process
- Status updates across services
- Notification dispatch based on decision

## Message Formats

### Notification Service Messages

#### Email Format
```json
{
  "event": "sendEmail",
  "to": "<EMAIL>",
  "typeMessage": "approve",
  "template": "approve",
  "emailData": {
    "dataToRender": {
      "first_name": "",
      "last_name": ""
    },
    "tableData": [] // Form data from start node
  }
}
```

#### SMS Format
```json
{
  "event": "sendSms",
  "to": "", // Phone number
  "typeMessage": "approve",
  "template": "approve",
  "dataToRender": {
    "first_name": "",
    "last_name": "",
    "tableData": [] // Form data from start node
  }
}
```

## Communication Patterns

### Synchronous (HTTP)
- User data retrieval
- Form validation
- Workflow validation
- Instance status updates

### Asynchronous (Service Bus)
- Workflow creation events
- Document processing pipeline
- Notification triggers
- Status update events

## Error Handling
- Each service implements retry mechanisms
- Failed events go to dead-letter queues
- Validation errors return detailed error objects
- Transaction rollback on critical failures
