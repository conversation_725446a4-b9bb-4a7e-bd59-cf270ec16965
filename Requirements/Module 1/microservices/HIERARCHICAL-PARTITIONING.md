# Hierarchical Partition Key Implementation for Azure Cosmos DB

This implementation demonstrates best practices for multi-tenant data isolation using hierarchical partition keys in Azure Cosmos DB.

## Overview

**Partition Key Strategy:** `country/tenantId/category`

**Example:** `US/aptiv-manufacturing/HR`

This approach provides excellent data isolation by country for compliance/regulatory requirements while enabling efficient querying within tenant and category boundaries.

## Key Benefits

✅ **Data Sovereignty**: Perfect isolation by country for regulatory compliance  
✅ **Multi-tenant Isolation**: Secure separation between tenants within countries  
✅ **Query Performance**: Optimal RU consumption with hierarchical filtering  
✅ **Scalability**: Handles growth across regions and tenants efficiently  
✅ **Cost Optimization**: Predictable and minimal query costs  

## Architecture

### Partition Key Structure

```
{country}/{tenantId}/{category}
```

- **Country**: ISO 3166-1 alpha-2 code (e.g., "US", "DE", "FR")
- **TenantId**: Unique identifier for organization (e.g., "aptiv-manufacturing")  
- **Category**: Template category enum (e.g., "HR", "FINANCE")

### Data Model

```typescript
export class Template extends BaseEntity {
  name: string;
  description: string;
  category: TemplateCategory;
  country: string;          // ISO 3166-1 alpha-2
  region?: string;          // Additional geographical context
  tenantId: string;         // Multi-tenant identifier
  templateContent: string;
  fieldIds: string[];
  // ... other fields
}
```

## Query Performance Hierarchy

### 1. Exact Partition Key (Fastest)
```typescript
// Single partition query - ~2.5 RUs
GET /templates?country=US&tenantId=aptiv-manufacturing&category=HR
```

### 2. Tenant-Level Queries (Fast)
```typescript
// Limited partitions - ~5-10 RUs  
GET /templates?country=US&tenantId=aptiv-manufacturing
```

### 3. Country-Level Queries (Moderate)
```typescript
// Multiple tenant partitions - ~10-50 RUs
GET /templates?country=US
```

### 4. Cross-Partition Queries (Slowest)
```typescript
// All partitions - ~50+ RUs
GET /templates
```

## API Usage Examples

### Create Template with Hierarchical Partitioning

```typescript
POST /templates
{
  "name": "Employee Onboarding Template",
  "description": "Standard onboarding process for new employees",
  "category": "HR",
  "country": "US",
  "tenantId": "aptiv-manufacturing", 
  "region": "NA-EAST",
  "templateContent": "<html>...</html>",
  "fieldIds": ["field-1", "field-2"],
  "templateVersion": "v1.0",
  "createdBy": "user-123"
}
```

### Query with Optimal Performance

```typescript
// Most efficient - exact partition match
GET /templates?country=US&tenantId=aptiv-manufacturing&category=HR

// Good performance - tenant level
GET /templates?country=US&tenantId=aptiv-manufacturing

// Moderate performance - country level  
GET /templates?country=US

// Additional filters
GET /templates?country=US&tenantId=aptiv-manufacturing&region=NA-EAST&createdBy=user-123
```

## Implementation Details

### Partition Key Utility

```typescript
// Create hierarchical partition key
const partitionKey = PartitionKeyUtils.createPartitionKey(
  'US', 
  'aptiv-manufacturing', 
  TemplateCategory.HR
);
// Result: "US/aptiv-manufacturing/HR"

// Parse partition key components
const { country, tenantId, category } = PartitionKeyUtils.parsePartitionKey(partitionKey);
```

### Repository Methods

```typescript
// Exact partition key query (fastest)
await templateRepository.findByHierarchicalPartitionKey('US', 'aptiv-manufacturing', 'HR');

// Tenant-level query (fast)
await templateRepository.findByCountryAndTenant('US', 'aptiv-manufacturing');

// Country-level query (moderate)
await templateRepository.findByCountry('US');

// Flexible hierarchical search
await templateRepository.searchTemplatesHierarchical({
  country: 'US',
  tenantId: 'aptiv-manufacturing',
  category: 'HR',
  region: 'NA-EAST'
});
```

## Best Practices

### Query Optimization
- Always provide `country` when possible for better performance
- Include `tenantId` for tenant-scoped operations  
- Specify `category` for single-partition queries
- Avoid cross-partition queries in frequently used endpoints

### Data Modeling
- Use ISO 3166-1 alpha-2 country codes consistently
- Design tenant IDs with clear naming conventions
- Consider data residency and compliance requirements
- Plan categories based on actual access patterns

### Security & Compliance
- Implement country-based access controls
- Validate tenant access permissions before queries
- Audit any cross-tenant data access attempts
- Log partition key usage for compliance reporting

### Monitoring
- Track RU consumption by partition hierarchy level
- Monitor for hot partition scenarios
- Alert on unexpected cross-partition query spikes  
- Dashboard partition key distribution patterns

## Container Configuration

```typescript
{
  id: 'templates',
  partitionKey: { 
    paths: ['/partitionKey'], 
    kind: PartitionKeyKind.Hash 
  },
  indexingPolicy: {
    includedPaths: [
      { path: '/country/?' },
      { path: '/tenantId/?' }, 
      { path: '/category/?' },
      { path: '/region/?' },
      { path: '/createdAt/?' },
      { path: '/name/?' }
    ],
    excludedPaths: [
      { path: '/templateContent/*' },
      { path: '/description/*' }
    ]
  }
}
```

## Migration Strategy

### From Single Partition to Hierarchical

1. **Analyze** current data distribution and access patterns
2. **Design** hierarchical partition key schema  
3. **Create** new container with hierarchical partitioning
4. **Update** application models to include country/tenantId
5. **Migrate** data with proper partition key assignment
6. **Update** repository and service layers
7. **Validate** performance and security
8. **Cut over** to new implementation

## Sample Data Structure

```json
{
  "id": "template-001",
  "partitionKey": "US/aptiv-manufacturing/HR",
  "name": "Employee Onboarding Template", 
  "category": "HR",
  "country": "US",
  "tenantId": "aptiv-manufacturing",
  "region": "NA-EAST",
  "templateContent": "<html>...</html>",
  "createdAt": "2024-03-20T10:00:00Z",
  "createdBy": "user-123"
}
```

## Performance Benchmarks

| Query Type | Partitions | RU Cost | Response Time | Use Case |
|------------|------------|---------|---------------|----------|
| Exact partition | 1 | ~2.5 RUs | <10ms | Single category access |
| Tenant-level | 2-5 | ~5-10 RUs | <20ms | Tenant dashboard |
| Country-level | 10-50 | ~10-50 RUs | <100ms | Country reporting |
| Cross-partition | All | ~50+ RUs | >200ms | Global analytics |

## Error Handling

```typescript
try {
  const template = await templateService.createTemplate(createDto);
} catch (error) {
  if (error.message.includes('Invalid country code')) {
    // Handle country validation error
  }
  if (error.message.includes('Invalid tenant ID')) {
    // Handle tenant validation error  
  }
}
```

## Testing

```typescript
describe('Hierarchical Partitioning', () => {
  it('should create partition key correctly', () => {
    const partitionKey = PartitionKeyUtils.createPartitionKey('US', 'aptiv-manufacturing', 'HR');
    expect(partitionKey).toBe('US/aptiv-manufacturing/HR');
  });

  it('should query by exact partition efficiently', async () => {
    const result = await repository.findByHierarchicalPartitionKey('US', 'aptiv-manufacturing', 'HR');
    expect(result.items).toBeDefined();
  });
});
```

This implementation provides a robust foundation for multi-tenant, country-isolated data storage with optimal query performance in Azure Cosmos DB.
