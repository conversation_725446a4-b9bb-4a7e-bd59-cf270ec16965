
---

# **Notification Service Design**

## **Overview**

The **Notification Service** handles the delivery of notifications to users via specified channels (email, SMS, push notifications) based on instructions from the **Workflow Engine Service**. It ensures notifications are sent efficiently and reliably, handles SLA breach alerts, and tracks notification statuses for logging and auditing.

---

## **Purpose**

- **Notification Delivery**: Sends notifications based on instructions from the Workflow Engine Service.
- **SLA Breach Alerts**: Notifies users when SLAs are at risk or breached.
- **Workflow Updates**: Delivers updates on workflow events.
- **Logging**: Tracks notification statuses for audit and troubleshooting.

---

## **High-Level Architecture**

```mermaid
graph TD
    A[Workflow Engine Service] -->|Publishes Events| B[Notification Service]
    B -->|Subscribes to Events| C[Azure Service Bus]
    B -->|Dispatch Notifications| D[Email, SMS, Push Services]
    D -->|Delivery Status| B
    B -->|Logs| E[Database]
    F[Request Service] -->|Publishes Events| C
    G[Training Service] -->|Publishes Events| C
```

---

## **Responsibilities**

1. **Message Handling**:
    - Subscribes to Azure Service Bus topics to receive events from other services.
    - Processes messages with notification details provided by the Workflow Engine Service.

2. **Notification Dispatching**:
    - Sends notifications via:
      - **Email**
      - **SMS**
      - **Push Notifications** (mobile, tablet, web app).
    - Ensures delivery through appropriate channels.

3. **Logging and Tracking**:
    - Logs notifications with statuses (`sent`, `delivered`, `failed`).
    - Tracks retry attempts and provides an interface for querying logs.

4. **SLA Monitoring**:
    - Monitors SLA-related events and sends alerts for breaches.

---

## **Data Flow**

```mermaid
sequenceDiagram
    participant WorkflowEngine as Workflow Engine
    participant AzureBus as Azure Service Bus
    participant NotificationService as Notification Service
    participant EmailService as Email Provider
    participant SMSService as SMS Gateway
    participant PushService as Push Notification

    WorkflowEngine->>AzureBus: Publish Notification Event
    AzureBus->>NotificationService: Event Received
    NotificationService->>EmailService: Send Email
    NotificationService->>SMSService: Send SMS
    NotificationService->>PushService: Send Push Notification
    EmailService-->>NotificationService: Delivery Confirmation
    SMSService-->>NotificationService: Delivery Confirmation
    PushService-->>NotificationService: Delivery Confirmation
    NotificationService->>AzureBus: Publish Delivery Status
```

---

## **Entities**

### **1. Notification**

| **Field**             | **Type**       | **Description**                              |
|------------------------|----------------|----------------------------------------------|
| `notificationId`       | UUID           | Unique identifier.                          |
| `userId`               | UUID           | Recipient user ID.                          |
| `channels`             | Array of String| Channels used (`email`, `sms`, `push`).     |
| `eventType`            | String         | Triggering event type.                      |
| `content`              | JSON           | Notification content.                       |
| `status`               | String         | Status (`pending`, `sent`, `delivered`).    |
| `attempts`             | Integer        | Number of send attempts.                    |
| `createdAt`            | Timestamp      | Creation time.                              |
| `updatedAt`            | Timestamp      | Last update time.                           |

---

## **API Endpoints**

### **1. Get Notification Logs**

| **Method** | **Endpoint**           | **Description**                     |
|------------|-------------------------|-------------------------------------|
| GET        | `/notifications/logs`  | Retrieves notification logs.         |

#### **Query Parameters**:
- `userId`: Filter logs for a specific user (admin only).
- `startDate`: Filter logs from this date.
- `endDate`: Filter logs up to this date.
- `status`: Filter by status (`sent`, `failed`).

#### **Response**:
```json
[
  {
    "notificationId": "notification-uuid",
    "userId": "user-uuid",
    "channels": ["email", "sms"],
    "eventType": "WorkflowUpdated",
    "status": "delivered",
    "createdAt": "2023-10-15T12:05:00Z"
  }
]
```

---

## **Domain Events**

### **1. NotificationSent**
- **Emitted When**: A notification is successfully sent.
- **Payload**:
  ```json
  {
    "eventType": "NotificationSent",
    "notificationId": "notification-uuid",
    "userId": "user-uuid",
    "channels": ["email", "push"],
    "timestamp": "2023-10-15T12:05:00Z"
  }
  ```

### **2. NotificationFailed**
- **Emitted When**: Notification fails after retries.
- **Payload**:
  ```json
  {
    "eventType": "NotificationFailed",
    "notificationId": "notification-uuid",
    "userId": "user-uuid",
    "channels": ["sms"],
    "reason": "Invalid phone number",
    "timestamp": "2023-10-15T12:10:00Z"
  }
  ```

---

## **Use Cases**

### **Use Case 1**: Workflow Notification
1. **Event**: Workflow Engine emits a `WorkflowUpdated` event.
2. **Notification Service**:
    - Reads channels and content from the event.
    - Dispatches notifications via email and push services.
    - Logs the status.

### **Use Case 2**: SLA Breach Alert
1. **Event**: SLA breach event is published by the Workflow Engine.
2. **Notification Service**:
    - Sends an SMS alert to the manager.
    - Logs the notification status.

---

## **Security Considerations**

- **Authentication**:
    - All API endpoints require JWT tokens.
- **Authorization**:
    - Users can only view their own logs.
    - Admin access is required for global logs.
- **Data Protection**:
    - Encrypt sensitive data at rest.
    - Use TLS for communication.
- **Input Validation**:
    - Validate all event payloads and API requests.

---

## **Error Handling**

- **Retries**:
    - Implement exponential backoff for retries.
- **Dead-Letter Queue**:
    - Failed messages are moved to a dead-letter queue.
- **Monitoring**:
    - Alerts are generated for critical failures.

---

## **Integration with Azure Service Bus**

### **Message Topics**
- **Workflow Events**: Events like `WorkflowUpdated`, `SLAAlert`.
- **Subscription**: Notification Service subscribes to relevant topics.

---

## **Additional Considerations**

### **1. Localization**
- Ensure notification templates support multiple languages.

### **2. Monitoring and Analytics**
- Track metrics like delivery rates and response times.

---

## **Summary**

The **Notification Service** is designed to deliver notifications via specified channels, track statuses, and handle SLA breach alerts. By leveraging Azure Service Bus, it efficiently processes events and ensures reliable communication.

--- 

**Next Steps**:
1. Implement the **Message Processor** and **Channel Dispatchers**.
2. Set up integration with external services (email, SMS, push).
3. Test the service thoroughly.
4. Deploy and monitor performance.


