# FORM-FIELD-SYNC-001: Automatic Field Creation and Synchronization Engine

## 📋 **Ticket Summary**

**Title:** Implement Automatic Field Creation/Update Based on Form Operations  
**Type:** Epic  
**Priority:** High  
**Story Points:** 21  
**Sprint:** 3-4 sprints

## 🎯 **Objective**

Create a comprehensive field synchronization system that automatically generates and maintains corresponding fields in the Document Template service whenever form inputs are created, updated, or deleted in the Dynamic Forms service. This ensures real-time synchronization between Dynamic Forms and Document Template services without manual intervention.

## 🏗️ **Architecture Overview**

### Current State Analysis

- **Dynamic Forms Service**: Manages form entities with field collections
- **Document Template Service**: Manages individual field entities with form references
- **Communication**: Basic gRPC setup exists but lacks event-driven synchronization
- **Field Management**: Manual field creation in Document Template service

### Target State

- **Event-Driven Architecture**: Automatic field lifecycle management based on form operations
- **Real-Time Synchronization**: Immediate field creation/update/deletion across services
- **Service Bus Integration**: Reliable message delivery for field synchronization events
- **Conflict Resolution**: Handling concurrent updates and data consistency

## 📋 **Epic Breakdown**

### 🔧 **Story 1: Event Infrastructure Setup**

**Points:** 5  
**Description:** Establish event-driven infrastructure for form field synchronization

#### Acceptance Criteria:

- [ ] Create form lifecycle event system in Dynamic Forms service
- [ ] Set up Service Bus topics for field synchronization events
- [ ] Implement event publishers for form CRUD operations
- [ ] Create event schemas for field synchronization messages
- [ ] Add event logging and monitoring capabilities

#### Technical Tasks:

```typescript
// Event Types to Implement
enum FormFieldEvent {
  FORM_CREATED = "form.created",
  FORM_UPDATED = "form.updated",
  FORM_DELETED = "form.deleted",
  FORM_PUBLISHED = "form.published",
  FORM_ARCHIVED = "form.archived",
  FIELD_ADDED = "form.field.added",
  FIELD_UPDATED = "form.field.updated",
  FIELD_REMOVED = "form.field.removed",
}

// Event Payload Structure
interface FormFieldSyncEvent {
  eventType: FormFieldEvent;
  formId: string;
  timestamp: Date;
  transactionId: string;
  payload: {
    form?: Partial<Form>;
    field?: Partial<Field>;
    previousValue?: any;
  };
}
```

#### Files to Modify/Create:

- `apps/dynamic-forms-service/src/events/form-field.events.ts`
- `apps/dynamic-forms-service/src/publishers/form-field.publisher.ts`
- `apps/dynamic-forms-service/src/services/form-event.service.ts`

---

### 🔄 **Story 2: Form Service Event Integration**

**Points:** 6  
**Description:** Integrate event publishing into existing form operations

#### Acceptance Criteria:

- [ ] Modify `FormsService.createForm()` to emit field creation events
- [ ] Update `FormsService.updateForm()` to detect field changes and emit events
- [ ] Enhance `FormsService.deleteForm()` to emit field deletion events
- [ ] Add field-specific events for `addField()`, `updateField()`, `removeField()`
- [ ] Ensure event publishing doesn't break existing functionality
- [ ] Handle event publishing failures gracefully

#### Technical Implementation:

```typescript
// Enhanced FormsService with Event Publishing
@Injectable()
export class FormsService {
  constructor(
    private readonly formRepository: FormRepository,
    private readonly formValidationService: FormValidationService,
    private readonly formEventService: FormEventService,
    private readonly logger: AppLogger
  ) {}

  async createForm(createFormDto: CreateFormDto): Promise<Form> {
    const operation = "createForm";
    try {
      const form = new Form(createFormDto);
      const savedForm = await this.formRepository.create(form);

      // Emit events for each field in the new form
      await this.formEventService.publishFormCreatedEvent(savedForm);
      for (const field of savedForm.fields) {
        await this.formEventService.publishFieldAddedEvent(savedForm.id, field);
      }

      return savedForm;
    } catch (error) {
      this.handleError(error, operation, { formData: createFormDto });
    }
  }

  async updateForm(id: string, updateFormDto: UpdateFormDto): Promise<Form> {
    const operation = "updateForm";
    try {
      const existingForm = await this.getForm(id);
      const savedForm = await this.formRepository.update(id, id, updateFormDto);

      // Detect field changes and emit appropriate events
      await this.detectAndPublishFieldChanges(existingForm, savedForm);

      return savedForm;
    } catch (error) {
      this.handleError(error, operation, {
        formId: id,
        updateData: updateFormDto,
      });
    }
  }
}
```

#### Files to Modify:

- `apps/dynamic-forms-service/src/services/forms.service.ts`
- `apps/dynamic-forms-service/src/entities/form.entity.ts`

---

### 🎯 **Story 3: Field Synchronization Service**

**Points:** 8  
**Description:** Create comprehensive field synchronization service in Document Template service

#### Acceptance Criteria:

- [ ] Create `FieldSynchronizationService` to handle form field events
- [ ] Implement automatic field creation based on form field definitions
- [ ] Add field mapping logic between form fields and template fields
- [ ] Handle field updates and property synchronization
- [ ] Implement field deletion and cleanup logic
- [ ] Add conflict resolution for concurrent updates
- [ ] Ensure data consistency and integrity

#### Technical Implementation:

```typescript
@Injectable()
export class FieldSynchronizationService {
  constructor(
    private readonly fieldService: FieldService,
    private readonly fieldMappingService: FieldMappingService,
    private readonly logger: AppLogger
  ) {}

  async handleFormCreated(event: FormFieldSyncEvent): Promise<void> {
    const { form } = event.payload;
    if (!form?.fields) return;

    for (const formField of form.fields) {
      await this.createFieldFromFormField(form.id, formField);
    }
  }

  async handleFieldAdded(event: FormFieldSyncEvent): Promise<void> {
    const { field } = event.payload;
    if (!field) return;

    await this.createFieldFromFormField(event.formId, field);
  }

  async handleFieldUpdated(event: FormFieldSyncEvent): Promise<void> {
    const { field, previousValue } = event.payload;
    if (!field) return;

    const existingField = await this.fieldService.findByFormIdAndInputId(
      event.formId,
      field.name
    );

    if (existingField) {
      const updateData =
        this.fieldMappingService.mapFormFieldToTemplateField(field);
      await this.fieldService.updateField(existingField.id, updateData);
    }
  }

  async handleFieldRemoved(event: FormFieldSyncEvent): Promise<void> {
    const { field } = event.payload;
    if (!field) return;

    const existingField = await this.fieldService.findByFormIdAndInputId(
      event.formId,
      field.name
    );

    if (existingField) {
      await this.fieldService.deleteField(existingField.id);
    }
  }

  private async createFieldFromFormField(
    formId: string,
    formField: Field
  ): Promise<void> {
    const fieldData =
      this.fieldMappingService.mapFormFieldToTemplateField(formField);
    fieldData.formId = formId;
    fieldData.inputId = formField.name;
    fieldData.source = "DYNAMIC_FORM";

    await this.fieldService.createField(fieldData);
  }
}
```

#### Files to Create:

- `apps/document-template-service/src/services/field-synchronization.service.ts`
- `apps/document-template-service/src/services/field-mapping.service.ts`
- `apps/document-template-service/src/listeners/form-field.listener.ts`

---

### 🔄 **Story 4: Field Mapping and Transformation Engine**

**Points:** 5  
**Description:** Implement intelligent field mapping between form and template fields

#### Acceptance Criteria:

- [ ] Create comprehensive field type mapping system
- [ ] Handle validation rule transformation
- [ ] Map form field configurations to template field properties
- [ ] Support custom field type mappings
- [ ] Handle complex field types (SELECT, RADIO, FILE, etc.)
- [ ] Preserve field metadata and relationships

#### Technical Implementation:

```typescript
@Injectable()
export class FieldMappingService {
  private readonly typeMapping = new Map<FieldType, string>([
    [FieldType.TEXT, "TEXT"],
    [FieldType.NUMBER, "NUMBER"],
    [FieldType.EMAIL, "EMAIL"],
    [FieldType.DATE, "DATE"],
    [FieldType.SELECT, "SELECT"],
    [FieldType.RADIO, "RADIO"],
    [FieldType.CHECKBOX, "CHECKBOX"],
    [FieldType.FILE, "FILE"],
    [FieldType.TEXTAREA, "TEXTAREA"],
  ]);

  mapFormFieldToTemplateField(formField: Field): Partial<TemplateField> {
    return {
      label: formField.label,
      type: this.typeMapping.get(formField.type) || "TEXT",
      required: formField.required || false,
      placeholder: formField.placeholder,
      description: formField.description,
      validation: this.mapValidationRules(formField.validationRules),
      config: this.mapFieldConfig(formField),
      metadata: {
        originalType: formField.type,
        variant: formField.variant,
        rowIndex: formField.rowIndex,
        syncedAt: new Date(),
      },
    };
  }

  private mapValidationRules(rules?: ValidationRule[]): any {
    if (!rules || rules.length === 0) return null;

    return rules.map((rule) => ({
      type: rule.type,
      value: rule.value,
      message: rule.message,
    }));
  }

  private mapFieldConfig(formField: Field): any {
    const config: any = {};

    if (
      formField.type === FieldType.SELECT ||
      formField.type === FieldType.RADIO
    ) {
      config.options = formField.options || [];
    }

    if (formField.type === FieldType.FILE) {
      config.acceptedTypes = formField.config?.acceptedTypes || [];
      config.maxSize = formField.config?.maxSize;
      config.maxFiles = formField.config?.maxFiles;
    }

    if (formField.type === FieldType.NUMBER) {
      config.min = formField.config?.min;
      config.max = formField.config?.max;
      config.step = formField.config?.step;
    }

    return config;
  }
}
```

#### Files to Create:

- `apps/document-template-service/src/services/field-mapping.service.ts`
- `apps/document-template-service/src/types/field-mapping.types.ts`

---

### 🔧 **Story 5: Service Bus Integration and Error Handling**

**Points:** 4  
**Description:** Implement robust Service Bus integration with error handling and retry mechanisms

#### Acceptance Criteria:

- [ ] Configure Service Bus topics and subscriptions for field sync
- [ ] Implement message publishing with error handling
- [ ] Add retry logic for failed synchronization attempts
- [ ] Create dead letter queue handling for failed messages
- [ ] Implement event ordering and deduplication
- [ ] Add monitoring and alerting for sync failures

#### Technical Implementation:

```typescript
@Injectable()
export class FormFieldEventPublisher {
  constructor(
    private readonly serviceBusService: ServiceBusService,
    private readonly logger: AppLogger
  ) {}

  async publishFieldEvent(event: FormFieldSyncEvent): Promise<void> {
    const operation = "publishFieldEvent";

    try {
      await this.serviceBusService.sendMessage("form-field-sync", {
        body: event,
        messageId: event.transactionId,
        correlationId: event.formId,
        label: event.eventType,
      });

      this.logger.debug({
        message: "Form field event published successfully",
        operation,
        eventType: event.eventType,
        formId: event.formId,
        transactionId: event.transactionId,
      });
    } catch (error) {
      this.logger.error({
        message: "Failed to publish form field event",
        operation,
        error: error.message,
        eventType: event.eventType,
        formId: event.formId,
      });

      throw new BusinessException(
        "EVENT_PUBLISH_FAILED",
        "Failed to publish field synchronization event",
        { formId: event.formId, eventType: event.eventType }
      );
    }
  }
}

@Injectable()
export class FormFieldEventListener {
  constructor(
    private readonly fieldSyncService: FieldSynchronizationService,
    private readonly logger: AppLogger
  ) {}

  @ServiceBusSubscription("form-field-sync")
  async handleFormFieldEvent(message: ServiceBusMessage): Promise<void> {
    const operation = "handleFormFieldEvent";
    const event = message.body as FormFieldSyncEvent;

    try {
      this.logger.debug({
        message: "Processing form field event",
        operation,
        eventType: event.eventType,
        formId: event.formId,
      });

      switch (event.eventType) {
        case FormFieldEvent.FORM_CREATED:
          await this.fieldSyncService.handleFormCreated(event);
          break;
        case FormFieldEvent.FIELD_ADDED:
          await this.fieldSyncService.handleFieldAdded(event);
          break;
        case FormFieldEvent.FIELD_UPDATED:
          await this.fieldSyncService.handleFieldUpdated(event);
          break;
        case FormFieldEvent.FIELD_REMOVED:
          await this.fieldSyncService.handleFieldRemoved(event);
          break;
        default:
          this.logger.warn({
            message: "Unknown event type received",
            operation,
            eventType: event.eventType,
          });
      }

      await message.complete();
    } catch (error) {
      this.logger.error({
        message: "Failed to process form field event",
        operation,
        error: error.message,
        eventType: event.eventType,
        formId: event.formId,
      });

      await message.abandon();
    }
  }
}
```

#### Files to Create:

- `apps/dynamic-forms-service/src/publishers/form-field-event.publisher.ts`
- `apps/document-template-service/src/listeners/form-field-event.listener.ts`

---

### 🧪 **Story 6: Integration Testing and Validation**

**Points:** 3  
**Description:** Comprehensive testing of field synchronization system

#### Acceptance Criteria:

- [ ] Create integration tests for form-to-field synchronization
- [ ] Test field creation, update, and deletion scenarios
- [ ] Validate data consistency across services
- [ ] Test error handling and retry mechanisms
- [ ] Performance testing for high-volume field operations
- [ ] End-to-end testing with frontend form builder

#### Test Cases:

```typescript
describe("Field Synchronization Integration Tests", () => {
  describe("Form Creation Sync", () => {
    it("should create corresponding fields when form is created", async () => {
      // Test form creation with multiple fields
      // Verify fields are created in document template service
    });

    it("should handle form creation with complex field types", async () => {
      // Test SELECT, RADIO, FILE fields with options/config
      // Verify proper mapping of field configurations
    });
  });

  describe("Field Update Sync", () => {
    it("should sync field updates across services", async () => {
      // Test field property updates (label, required, validation)
      // Verify changes are reflected in template fields
    });

    it("should handle field type changes", async () => {
      // Test field type conversion scenarios
      // Verify data integrity during type changes
    });
  });

  describe("Error Handling", () => {
    it("should handle sync failures gracefully", async () => {
      // Test service unavailability scenarios
      // Verify retry mechanisms and error logging
    });

    it("should maintain data consistency during failures", async () => {
      // Test partial failure scenarios
      // Verify rollback and recovery mechanisms
    });
  });
});
```

#### Files to Create:

- `apps/dynamic-forms-service/test/integration/field-sync.integration.test.ts`
- `apps/document-template-service/test/integration/field-listener.integration.test.ts`

---

## 🔄 **Implementation Timeline**

### Sprint 1 (Stories 1-2): Foundation

- Week 1: Event infrastructure setup
- Week 2: Form service integration

### Sprint 2 (Stories 3-4): Core Functionality

- Week 1: Field synchronization service
- Week 2: Field mapping engine

### Sprint 3 (Stories 5-6): Reliability & Testing

- Week 1: Service Bus integration
- Week 2: Testing and validation

## 📊 **Success Metrics**

### Functional Metrics:

- **Sync Accuracy:** 99.9% of form field changes result in correct template field updates
- **Sync Latency:** Field synchronization completes within 2 seconds of form changes
- **Data Consistency:** Zero data inconsistencies between services after sync completion

### Technical Metrics:

- **Event Delivery Rate:** 99.9% successful event delivery via Service Bus
- **Error Recovery:** 95% of failed syncs recover automatically within retry window
- **Performance:** System handles 1000+ concurrent field operations without degradation

### Business Metrics:

- **Manual Intervention:** Reduction from 100% to <1% manual field creation
- **Development Velocity:** 50% faster form-to-template workflow implementation
- **Data Quality:** Elimination of field mapping errors and inconsistencies

## 🚧 **Risk Assessment**

### High Risk:

- **Data Consistency:** Risk of data loss during service failures
- **Migration:** Existing forms may need field resynchronization

### Medium Risk:

- **Performance Impact:** Additional event processing overhead
- **Service Dependencies:** Increased coupling between services

### Mitigation Strategies:

- Implement comprehensive transaction logging
- Create data reconciliation tools for existing forms
- Add circuit breakers and fallback mechanisms
- Maintain backward compatibility during rollout

## 📚 **Dependencies**

### Technical Dependencies:

- INTEGRATION-001 completion (gRPC and Service Bus setup)
- Service Bus infrastructure provisioning
- Event Emitter configuration in both services

### Service Dependencies:

- Dynamic Forms Service: Form entity operations
- Document Template Service: Field CRUD operations
- Shared libraries: Event schemas and validation

## 🧪 **Testing Strategy**

### Unit Testing:

- Event publishing and handling logic
- Field mapping transformations
- Error handling scenarios

### Integration Testing:

- Cross-service field synchronization
- Service Bus message flow
- Data consistency validation

### End-to-End Testing:

- Frontend form builder to field creation workflow
- Complete form lifecycle synchronization
- Performance and reliability testing

## 📋 **Acceptance Criteria Summary**

### Must Have:

- [x] Automatic field creation when forms are created/updated
- [x] Real-time field synchronization across services
- [x] Event-driven architecture with reliable message delivery
- [x] Comprehensive error handling and retry mechanisms
- [x] Data consistency and integrity validation

### Should Have:

- [x] Field type mapping and configuration preservation
- [x] Conflict resolution for concurrent updates
- [x] Performance monitoring and alerting
- [x] Migration tools for existing forms

### Could Have:

- [x] Advanced field relationship mapping
- [x] Custom field transformation rules
- [x] Bulk synchronization operations
- [x] Real-time sync status dashboard

## 🚀 **Definition of Done**

- [ ] All stories completed and tested
- [ ] Integration tests passing with >95% coverage
- [ ] Performance requirements met
- [ ] Documentation updated
- [ ] Code review completed
- [ ] Security review passed
- [ ] Production deployment successful
- [ ] Monitoring and alerting configured
- [ ] Team training completed

---

**Created:** 2024-12-19  
**Epic Owner:** Backend Development Team  
**Stakeholders:** Dynamic Forms Team, Document Template Team, DevOps Team  
**Review Date:** Every Sprint Review

## 📝 **Additional Notes**

This epic establishes the foundation for automatic field synchronization, enabling seamless integration between Dynamic Forms and Document Template services. The implementation ensures that any form field changes automatically propagate to the template system, eliminating manual field creation and reducing the risk of data inconsistencies.

The solution is designed to be scalable, reliable, and maintainable, with comprehensive error handling and monitoring capabilities to ensure production readiness.
