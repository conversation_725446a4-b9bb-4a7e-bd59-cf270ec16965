# DocumentTemplate Management Microservice

## Overview

The **Document Template Microservice** enables administrators to manage reusable document templates that reference dynamic fields. Templates are HTML-based, store references to field IDs, and are designed to work in coordination with form management and document generation systems.

---

## Table of Contents

- [Key Features](#key-features)
- [Use Cases](#use-cases)
- [API Endpoints](#api-endpoints)
- [Data Model](#data-model)
- [Data Synchronization Architecture](#Data-synchronization-architecture)
- [Integration with Other Microservices](#integration-with-other-microservices)
- [Validation and Error Handling](#validation-and-error-handling)
- [Security Considerations](#security-considerations)
- [Examples](#examples)
- [How It Works](#How*it-works)
- [Document Template Management: User Workflow Experience](#document-template-management-user-workflow-experience)

---

## Key Features

- **Template Management**

  - Create, update, retrieve, and soft delete HTML-based templates.
  - Version control to track changes over time.

- **DOCX Import**
  - Upload `.docx` files and convert them into editable HTML.

- **Dynamic Field Management**
  - Fields are **stored into Field container**.
  - Selected fields **are stored in the template entity** (as FieldId).

- **Integrates with Form Management to Reuse Form Inputs as Document Fields**
  - When a new form is created or updated in the Form Management Microservice, each form input (e.g., text box, date picker, dropdown) is automatically transformed into a Field document and stored in the Fields container (Cosmos DB).
  - **Each Field is uniquely identified by :**
    -formId → the form it belongs to
    -inputId → the input component in that form


- **Template Rendering**
  - Combine templates with the fields to generate final documents.
  - Support for placeholders in template content.
- **Integration**
  - Fetch data from other microservices (e.g., UserData, workflow).
- **Validation**
  - Field-level validation rules (e.g., required, pattern matching).
- **Hierarchy-Based Field Management**
  - Support for hierarchical user relationships (N+1, N+2, Supervisor, etc.)
  - Dynamic field resolution based on hierarchy relationships
  - Template tags for hierarchy-specific fields without database bloat

---

## Use Cases

### 1. HR Department Generating Absence Certificates

- **Actors**: Super admin
- **Process**:
  1. Super Admin creates an "Absence Certificate" template or imports a DOCX .
  2. Defines the fields like `{{firstName}}`, `{{lastName}}` (user fields), and `{{reason}}` (local field).
  3. When an Team lead requests an absence certificate:
  4. System fields are automatically populated from the UserData microservice.
  5. Final document is rendered HTML and provided to generateDocument microservice.

---

## API Endpoints

### 1. Create Template

- **Endpoint**: `POST /templates`
- **Description**: Creates a new document template.
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Request Body**:

  ```json
  {
    "name": "string",
    "description": "string",
    "category": "string",
    "templateContent": "string",
    "fieldIds": [
      "field-uuid-firstName"
      // Additional local fields...
    ],
    "signatureId": "signature-uuid-1"
  }
  ```

- **Responses**:
  - **201 Created**: Template created successfully. Response includes the new template object with `signatureId` if provided.
  - **400 Bad Request**: Validation error in the request body.
  - **401 Unauthorized**: Authentication failed.

### 2. Retrieve Template

- **Endpoint**: `GET /templates/{id}`
- **Description**: Retrieves a specific template by its ID.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **200 OK**: Returns the template details, including the `signatureId` field if set.
  - **404 Not Found**: Template does not exist.
  - **401 Unauthorized**: Authentication failed.

### 3. List Templates

- **Endpoint**: `GET /templates`
- **Description**: Retrieves a list of templates, optionally filtered by category.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `category` (optional): Filter by template category.
- **Responses**:
  - **200 OK**: Returns a list of templates.
  - **401 Unauthorized**: Authentication failed.

### 4. Update Template

- **Endpoint**: `PUT /templates/{id}`
- **Description**: Updates an existing template.
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Request Body**:

  ```json
  {
    "name": "string",
    "description": "string",
    "category": "string",
    "templateContent": "string",
    "fieldIds": [
      /* ... */
    ]
  }
  ```

- **Responses**:
  - **200 OK**: Template updated successfully.
  - **400 Bad Request**: Validation error.
  - **404 Not Found**: Template does not exist.
  - **401 Unauthorized**: Authentication failed.

### 5. Delete Template

- **Endpoint**: `DELETE /templates/{id}`
- **Description**: Deletes a template by its ID.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **204 No Content**: Template deleted successfully.
  - **404 Not Found**: Template does not exist.
  - **401 Unauthorized**: Authentication failed.

### 6. Import DOCX Template

- **Endpoint**: `POST /templates/import-docx`
- **Description**: Uploads a .docx file, extracts its content, and converts it to HTML with detected placeholders.
- **Headers**:
  - `Authorization: Bearer <token>`

- **Request Format**:
  - **Content-Type**: multipart/form-data
  - **Body Parameters**: 
    -**File**: file (required): The .docx file to be imported. Must be uploaded under the file key as form data.

- **Responses**:
  - **200 OK**: The DOCX file was successfully parsed and converted to HTML. Returns the converted HTML.

      ```json
      {
        "htmlContent": "<p>Hello {{firstName}} {{lastName}}</p>",
      }

      ```
  - **400 Bad Request**: Invalid file format, no file uploaded, or the file is corrupted.
  - **401 Unauthorized**: Missing or invalid authentication token.
  - **415 Unsupported Media Type**: The uploaded file is not a .docx format.

### 7. Get Available Fields (Grouped by Form)

- **Endpoint**: `GET /templates/fields`
- **Description**: Returns a list of all available fields, organized by their parent form.
- **Headers**:
  - `Authorization: Bearer <token>`

- **Responses**:
  - **200 OK**: Successfully retrieved all available fields.

      ```json
      [
        {
          "formId": "form-abc",
          "formLabel": "Customer Intake",
          "fields": [
            {
              "id": "field-uuid-1",
              "inputId": "input-1",
              "label": "First Name",
              "type": "string",
              "required": true,
              "validation": {
                "regex": "^[A-Za-z]+$",
                "errorMessage": "Only letters allowed"
              }
            },
            {
              "id": "field-uuid-2",
              "inputId": "input-2",
              "label": "Start Date",
              "type": "date",
              "required": true
            }
          ]
        },
        {
          "formId": "form-def",
          "formLabel": "Project Details",
          "fields": [
            {
              "id": "field-uuid-3",
              "inputId": "input-3",
              "label": "Project Name",
              "type": "string",
              "required": true
            },
            {
              "id": "field-uuid-4",
              "inputId": "input-4",
              "label": "Budget",
              "type": "number",
              "required": false
            }
          ]
        }
      ]

      ```
  - **401 Unauthorized**: Access denied.
  ```json
      {
        "error": "Unauthorized",
        "message": "Access denied"
      }
  ```

### 8. Get Fields by Form ID

- **Endpoint**: `GET /templates/fields?formId={formId}`
- **Description**: Filters and returns all fields related to a specific form.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `formId (required): ID of the form`

- **Responses**:
  - **200 OK**: List of fields related to form.

      ```json
        [
          {
            "id": "field-uuid-1",
            "formId": "form-abc",
            "inputId": "input-1",
            "label": "First Name",
            "type": "string",
            "required": true,
            "source": "form",
            "validation": {
              "regex": "^[A-Za-z]+$",
              "errorMessage": "Only letters allowed"
            }
          },
          {
            "id": "field-uuid-2",
            "formId": "form-abc",
            "inputId": "input-2",
            "label": "Start Date",
            "type": "date",
            "required": true,
            "source": "form"
          }
        ]

      ```
  - **401 Unauthorized**: Missing or invalid authentication token || Missing or invalid formId.
  - **500 Internal Server Error**: No fields found for the given formId.
### 9. Get Full Fields for Template

- **Endpoint**: `GET /templates/:id/fields`
- **Description**: Returns full field objects from Cosmos DB for rendering/editing.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `formId (required): ID of the form`

- **Responses**:
  - **200 OK**: Returns list of field objects.

      ```json
        [
          {
            "id": "field-uuid-1",
            "formId": "form-abc",
            "inputId": "input-1",
            "label": "First Name",
            "type": "string",
            "required": true,
            "source": "form",
            "validation": {
              "regex": "^[A-Za-z]+$",
              "errorMessage": "Only letters allowed"
            }
          },
          {
            "id": "field-uuid-2",
            "formId": "form-abc",
            "inputId": "input-2",
            "label": "Start Date",
            "type": "date",
            "required": true,
            "source": "form"
          }
        ]

      ```
  - **404 Not Found**: Template or fields not found.

### 10. List Available HierarchyFields

- **Endpoint**: `GET /templates/hierarchy-fields`
- **Description**: Returns all available hierarchy fields for template field selection
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **200 OK**: List of available hierarchy fields with their fields

  ```json
  [
    {
      "id": "N_PLUS_1",
      "displayName": "Direct Manager",
      "description": "User's immediate supervisor in the organizational hierarchy",
      "availableFields": ["firstName", "lastName", "email", "department", "title"]
    },
    {
      "id": "N_PLUS_2",
      "displayName": "Second-level Manager",
      "description": "Manager's manager in the organizational hierarchy",
      "availableFields": ["firstName", "lastName", "email", "department", "title"]
    }
  ]
  ```

### 11. Get Fields For Specific HierarchyField

- **Endpoint**: `GET /templates/hierarchy-fields/{hierarchyFieldId}/fields`
- **Description**: Returns fields available for a specific hierarchy field
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **200 OK**: List of fields for the specified hierarchy field

  ```json
  {
    "hierarchyFieldId": "N_PLUS_1",
    "fields": ["firstName", "lastName", "email", "department", "title"]
  }
  ```

- **404 Not Found**: Hierarchy field does not exist.
  - **401 Unauthorized**: Authentication failed.

### Updated Document Generation Process

```mermaid
flowchart TD
    A[User] --> B[Request Document Generation]
    B --> C[Fetch Template by ID]
    C --> D[Retrieve the Fields]
    D --> E[Fetch Data from Microservices]
    E --> F[Collect User Fields from UserData Service]
    F --> G[Resolve Hierarchy-Based Fields]
    G --> H[Populate Template with All Data]
    H --> I[Return final HTML]
```

#### HierarchyField Resolution Process

1. Parse template to identify hierarchy-based tags (`{{hierarchy:HIERARCHY_FIELD_ID.FIELD_NAME}}`)
2. For each unique hierarchy field found:
   - Call UserRelationshipService to resolve the target user based on the hierarchy field
   - Fetch target user's data from UserData microservice
3. Replace all hierarchy-based tags with the resolved values
4. Continue with standard field population

---

## Data Model

### Template Object

- **Fields**:
  - `id` (string, UUID): Unique identifier.
  - `name` (string): Template name.
  - `description` (string, optional): Brief description.
  - `category` (string): Category of the template (e.g., HR, Finance).
  - `templateContent` (string): The actual content with placeholders.
  - `fieldIds` (array of fields Ids): List of fields ids used in the template.
  - `version` (string): Template version (e.g., "v1.0").
  - `createdBy` (string): ID of the super admin who created the template.
  - `createdAt` (timestamp): Creation timestamp.
  - `updatedAt` (timestamp): Last updated timestamp.
  - `containerName` (string): Sanitized template name used as Cosmos DB container name
  - `signatureId` (string, optional): ID of the signature assigned to this template

### 📋 Template Entity

Represents a single HTML document template with associated metadata and selected field references.

```json
{
  "id": "uuid",
  "name": "Absence Certificate",
  "description": "Used for employee absence validation",
  "category": "HR",
  "templateContent": "<p>Dear {{firstName}} {{lastName}}</p>",
  "fieldIds": ["field-uuid-1", "field-uuid-2"],
  "version": "v1.0",
  "createdBy": "admin-uuid",
  "createdAt": "2024-05-23T10:00:00Z",
  "updatedAt": "2024-05-23T14:00:00Z",
  "signatureId": "signature-uuid-1"
}

```

### Field Object

- **Fields**:
  - `id` (string, UUID): Unique identifier.
  - `formId` (string): The form this field belongs to.
  - `inputId` (string): Identifier of the input element in the form.
  - `label` (string): Human-readable name of the field (e.g., "First Name").
  - `type` (string): Field type such as string, number, date, boolean, email..
  - `required` (boolean): Indicates whether the field is mandatory.
  - `source` (string): Origin of the field (e.g., form, system, user).
  - `createdAt` (timestamp): ISO 8601 timestamp of when the field was created..
  - `updatedAt` (timestamp): ISO 8601 timestamp of last update.
  - `validation` (object): Optional validation rules:
    -`regex` (string): Regular expression pattern.
    -`errorMessage` (string): Message displayed on validation failure.


### 📋 Field Entity
Each form input is stored as a Field document:
```json
{
  "id": "field-uuid",
  "formId": "form-123",
  "inputId": "input-abc",
  "label": "First Name",
  "type": "string",
  "required": true,
  "source": "form",
  "validation": {
    "regex": "^[A-Za-z]+$",
    "errorMessage": "Must be a valid name"
  }
}

```
- Fields are indexed by formId.
- Template editor allows selecting these fields by linking them via fieldIds.

### HierarchyField Definition Object

- **Fields**:
  - `id` (string): Unique identifier for the hierarchy field (e.g., "N_PLUS_1", "TEAM_LEADER")
  - `displayName` (string): Human-readable name (e.g., "Direct Manager", "Team Leader")
  - `description` (string): Explanation of the hierarchy relationship
  - `availableFields` (array of strings): Field properties available for this hierarchy field
  - `resolutionStrategy` (string): Method to resolve the target user (e.g., "directManager", "skipLevel")

### 📋 HierarchyField Definition Example

```json
{
  "id": "N_PLUS_1",
  "displayName": "Direct Manager",
  "description": "User's immediate supervisor in the organizational hierarchy",
  "availableFields": ["firstName", "lastName", "email", "department", "title"],
  "resolutionStrategy": "directManager"
}
```

### Template Content with Hierarchy-Based Fields

Templates can include hierarchy-based field references using the syntax:

```
{{hierarchy:HIERARCHY_FIELD_ID.FIELD_NAME}}
```

For example:
- `{{hierarchy:N_PLUS_1.firstName}}` - Direct manager's first name
- `{{hierarchy:N_PLUS_2.email}}` - Manager's manager email
- `{{hierarchy:HR_PARTNER.department}}` - HR partner's department

These tags are resolved at document generation time through the UserRelationshipService.

## Data Synchronization Architecture

### Change Feed Processing

1.  **Template Creation Trigger**:
    - When a new template is created, a Change Feed trigger initiates the container creation process.
    - The container name is derived from the template name after sanitization (removing special characters, spaces, etc.)
2.  **Data Team Integration**:
    - The data team monitors new container creation events.
    - Uses Spark or other ETL tools to populate the container with data from the source container.
    - Maintains data synchronization using Change Feed on the source container.

### Container Name Sanitization Rules

- Convert to lowercase  
- Replace spaces with underscores  
- Remove special characters  
- Prefix with `template_` to avoid naming conflicts  
- Maximum length of 63 characters (Cosmos DB limitation)

**Example:**

```json
Original: "Employee Absence Certificate 2024"
Sanitized: "template_employee_absence_certificate_2024"

```

### Updated Process Flow

```mermaid
flowchart TD
    A[Super Admin] --> B[Create Template]
    B --> C[Define the Fields]
    C --> D[Set Field Metadata & Validation]
    D --> E[Save Template]
    E --> F[Template Saved]
    F --> G[Change Feed Trigger]
    G --> H[Create New Container]
    H --> I[Data Team ETL Process]
    I --> J[Container Populated]
```

### Data Team Responsibilities

- Monitor new container creation events
- Set up ETL pipelines for data population
- Maintain data synchronization with source container
- Handle data transformation and mapping
- Manage error handling and retry logic

### 2. Document Generation Process

```mermaid
flowchart TD
    A[User] --> B[Request Document Generation]
    B --> C[Fetch Template by ID]
    C --> D[Retrieve the Fields]
    D --> E[Fetch Data from Microservices]
    E --> F[Collect User Fields from UserData Service]
    F --> G[Resolve Hierarchy-Based Fields]
    G --> H[Populate Template with All Data]
    H --> I[Return final HTML]
```

---


## Integration with Other Microservices

- **UserData Microservice**: Provides user details like first name, last name, and email.

- **Workflow Microservice**: Provides other required data.

---

## Validation and Error Handling

- **Field-Level Validation**:
  - Enforced based on the `validation` rules defined in each field.
  - Includes checks for required fields, pattern matching, and length constraints.
- **Error Responses**:
  - **400 Bad Request**: Returned when validation fails.
  - **401 Unauthorized**: Authentication token is missing or invalid.
  - **403 Forbidden**: User lacks necessary permissions.
  - **404 Not Found**: Resource does not exist.
  - **500 Internal Server Error**: Unexpected server error.

**Example Error Response**:

```json
{
  "status": 400,
  "error": "Bad Request",
  "message": "Field 'firstName' is required."
}

```

---

## Security Considerations

- **Authentication**:
  - All endpoints require a valid JWT token.
  - Tokens are verified before processing requests.
- **Authorization**:
  - Only users with the `super_admin` role can create, update, or delete templates.
  - Regular users can only retrieve templates if necessary.

## Examples

### Creating a Template

**Request**:

```bash
POST /templates HTTP/1.1
Host: your-domain.com
Content-Type: application/json
Authorization: Bearer <token>
```
```json
{
  "name": "Absence Certificate",
  "description": "Used for employee absence validation",
  "category": "HR",
  "templateContent": "<p>Dear {{field_firstName}} {{field_lastName}}</p>",
  "fieldIds": [
    "field-uuid-firstName",
    "field-uuid-lastName",
    "field-uuid-reason"
  ],
  "signatureId": "signature-uuid-1"
}

```

**Response**:

```json
{
  "id": "template-uuid",
  "name": "Absence Certificate",
  "description": "Used for employee absence validation",
  "category": "HR",
  "templateContent": "<p>Dear {{field_firstName}} {{field_lastName}}</p>",
  "fieldIds": [
    "field-uuid-firstName",
    "field-uuid-lastName",
    "field-uuid-reason"
  ],
  "createdBy": "admin-id",
  "createdAt": "2024-05-23T10:00:00Z"
}
```

---


## How It Works

1. **local Fields Retrieval**: Fetches data from workflow microservice.

2. **user Fields Retrieval**: Fetches data from user microservice.

3. **sysetem Fields**: Automatically populated using the cirrent date and time.

4. **Data Aggregation**: Combines system fields and local fields into a unified response for the client.

---

## How We Use Service Bus and gRPC to Retrieve Data

In our Document Template microservice, we utilize a combination of service bus and gRPC to efficiently retrieve data from the workflow service and user service.

1. **Service Bus**:

-We use Azure service bus to facilitate communication between microservices. The service bus acts as a message broker, allowing different services to send and receive messages asynchronously.

-When the workflow service sends to data to the document Template microservice, it sends a message to the service bus. The document Template service listens for these messages and use the data.

1. **gRPC**:

-Document Template Service needs to retrieve user fields, it makes a gRPC call to the user service, which processes the request and returns the necessary data.

## Document Template Management: User Workflow Experience

### 1. Template Creation Workflow

#### Dashboard View
1. User logs into the Template Management dashboard
2. Views existing templates organized by category (HR, Finance, Legal)
3. Uses search/filter options to find specific templates
4. Clicks "Create New Template" button

#### Creation Method Selection
1. User selects creation method:
   - **Create from scratch**: Start with blank editor
   - **Import DOCX**: Upload existing Word document

#### Template Editor - Basic Information
1. User enters:
   - Template name (e.g., "Employee Absence Certificate")
   - Description
   - Category (dropdown: HR, Finance, etc.)
   - Version information

#### Template Editor - Content Creation

##### Left Panel: Field Selection
1. User navigates tabbed interface:
   - **Form Fields**: Grouped by form name
   - **User Fields**: Standard user properties
   - **Role Fields**: Hierarchical relationships (N+1, Team Lead)
   - **System Fields**: Date, time, etc.

##### Center: WYSIWYG Editor
1. User:
   - Types or imports document content
   - Drags and drops fields from left panel
   - Fields appear as highlighted tags (e.g., `{{firstName}}`)
   - Uses formatting tools (bold, lists, tables, etc.)

##### For Hierarchy-Based Fields:
1. User:
   - Selects "Hierarchy Fields" tab
   - Chooses hierarchy field type (Manager, HR Partner, etc.)
   - Views available fields for that hierarchy field
   - Drags desired field to the document
   - Field appears as `{{hierarchy:Manager.firstName}}`

#### Template Preview
1. User clicks "Preview" button
2. System shows template with sample data
3. User can toggle between different test profiles

#### Validation & Saving
1. System validates template (missing fields, format issues)
2. User clicks "Save Template"
3. Confirmation message shows success and next steps

### 2. Document Generation Workflow

#### Request Initiation
1. End-user selects document type from available templates
2. System pre-populates known fields (user data, system fields)

#### Data Entry
1. User completes any required form fields not auto-populated
2. Validation occurs in real-time as fields are completed
3. Error messages appear inline next to problematic fields

#### Review & Generate
1. User reviews all data in a summary view
2. Clicks "Generate Document" button
3. System processes:
   - Fetches template by ID
   - Retrieves all fields
   - Fetches data from microservices (UserData, workflow)
   - Resolves hierarchy-based fields through UserRelationshipService
   - Populates template with data
   - Returns final HTML

#### Document Delivery
1. System displays generated document
2. User can:
   - Download as PDF
   - Print document
   - Send document via email
   - Save to document repository

### 3. Template Management Workflow

#### Template Listing
1. Admin views list of all templates with:
   - Template name and category
   - Creation/modification dates
   - Usage statistics
   - Status indicators

#### Template Actions
1. For each template, admin can:
   - Edit template content and fields
   - Create a new version
   - View version history
   - Archive/deactivate template
   - View usage analytics

#### Field Management
1. Admin can:
   - View all available fields across forms
   - See which templates use specific fields
   - Manage system and user fields
   - Configure role-based fields

---

## Signature Management (LLD Enhancement)

### Overview
The Document Template Microservice supports digital signature management, allowing users to upload, manage, and assign signatures (as images) to document templates. Signatures are stored in a dedicated container and referenced in templates by ID.

### Key Features
- Add new signature (name, image file upload)
- List all available signatures
- Assign a signature to a template (by ID)

### Data Model
#### Signature Object
- `id` (string, UUID): Unique identifier
- `name` (string): Display name
- `imageUrl` (string): Path or blob storage URL for the image
- `createdBy` (string): User who uploaded the signature
- `createdAt` (timestamp): Upload timestamp

#### Template Object (Extension)
- Add `signatureId` (string, optional): References the assigned signature

### API Endpoints
- `POST /signatures` — Add a new signature (name, image file)
- `GET /signatures` — List all signatures
- `GET /signatures/{id}` — Retrieve a signature by ID
- `PUT /templates/{id}/signature` — Assign a signature to a template

### Sequence Flow
1. User uploads a signature (name + image file)
2. Signature is stored and listed
3. User assigns a signature to a template (e.g., via drag-and-drop in UI)
4. Template's `signatureId` is updated with the selected signature's ID

### Integration
- When generating a document, the assigned signature image is included in the output if `signatureId` is set on the template.

---

## HierarchyFields Container & API

### Cosmos DB Container: HierarchyFields

A dedicated Cosmos DB container named `HierarchyFields` is used to store all hierarchy field definitions. Each document in this container represents a hierarchy field (e.g., N+1, Manager, HR Partner) and its available properties.

#### HierarchyField Entity Example

```json
{
  "id": "N_PLUS_1",
  "displayName": "Direct Manager",
  "description": "User's immediate supervisor in the organizational hierarchy",
  "availableFields": ["firstName", "lastName", "email", "department", "title"],
  "resolutionStrategy": "directManager",
  "createdAt": "2024-06-23T10:00:00Z",
  "updatedAt": "2024-06-23T10:00:00Z"
}
```

### HierarchyFields API Endpoints

#### 1. Create HierarchyField
- **Endpoint**: `POST /templates/hierarchy-fields`
- **Description**: Creates a new hierarchy field definition.
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "id": "string", // e.g., "N_PLUS_1"
    "displayName": "string",
    "description": "string",
    "availableFields": ["string"],
    "resolutionStrategy": "string"
  }
  ```
- **Responses**:
  - **201 Created**: HierarchyField created successfully.
  - **400 Bad Request**: Validation error.
  - **401 Unauthorized**: Authentication failed.

#### 2. List HierarchyFields
- **Endpoint**: `GET /templates/hierarchy-fields`
- **Description**: Retrieves all hierarchy field definitions.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **200 OK**: List of hierarchy fields.
  - **401 Unauthorized**: Authentication failed.

#### 3. Get HierarchyField by ID
- **Endpoint**: `GET /templates/hierarchy-fields/{hierarchyFieldId}`
- **Description**: Retrieves a specific hierarchy field definition by ID.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **200 OK**: HierarchyField details.
  - **404 Not Found**: HierarchyField does not exist.
  - **401 Unauthorized**: Authentication failed.

#### 4. Update HierarchyField
- **Endpoint**: `PUT /templates/hierarchy-fields/{hierarchyFieldId}`
- **Description**: Updates an existing hierarchy field definition.
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "displayName": "string",
    "description": "string",
    "availableFields": ["string"],
    "resolutionStrategy": "string"
  }
  ```
- **Responses**:
  - **200 OK**: HierarchyField updated successfully.
  - **400 Bad Request**: Validation error.
  - **404 Not Found**: HierarchyField does not exist.
  - **401 Unauthorized**: Authentication failed.

#### 5. Delete HierarchyField
- **Endpoint**: `DELETE /templates/hierarchy-fields/{hierarchyFieldId}`
- **Description**: Deletes a hierarchy field definition by ID.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **204 No Content**: HierarchyField deleted successfully.
  - **404 Not Found**: HierarchyField does not exist.
  - **401 Unauthorized**: Authentication failed.

---
