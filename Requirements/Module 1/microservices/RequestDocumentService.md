

# **Request Service Design**

## **Overview**

The **Request Service** is responsible for managing the initiation and tracking of all requests within the system. It supports a variety of request types and allows for both personal requests and requests made by Team Leaders on behalf of operators.

---

## **Purpose**

- **Request Management**: Handles the creation, updating, and tracking of different request types.
- **On-Behalf Requests**: Allows Team Leaders to create requests on behalf of one or multiple operators.
- **Status Tracking**: Maintains the status and timestamps of requests throughout their lifecycle.
- **Workflow Integration**: Communicates with the Workflow Service to initiate and manage approval workflows.
- **Event Publishing**: Emits domain events like `RequestCreated` and `RequestOnBehalfCreated` for integration with other services.

---

## **High-Level Architecture**

```mermaid
graph TD
    A[User Interface] -->|Request Creation/Query| B[Request Service]
    B -->|Start Workflow| C[Workflow Service]
    B -->|Publish Events| D[Event Bus]
    D -->|Consume Events| E[Notification Service]
    B -->|Query Database| F[Database]
    C -->|Workflow Updates| B
```

---

## **Responsibilities**

1. **Request Creation and Management**:
    - Supports various request types:
      - Absence Authorization
      - Holiday Leave
      - Movement Requests
      - Overtime
      - Payroll Claims
      - Clocking Corrections
    - Stores detailed information for each request.
    - Manages request statuses (e.g., Pending, Approved, Rejected).

2. **On-Behalf Request Handling**:
    - Enables Team Leaders to submit requests on behalf of on or multiple team members.
    - Tracks both the requester (Team Leader) and the target operator.
3. **Multi-Operator Request Handling**:
    - Allows Team Leaders to create a single request that applies to multiple operators.
    - Associates the request with all specified operators while maintaining a single workflow instance.
    - Ensures that status updates are synchronized across all operators.
3. **External Integration**:
    - Initiates workflows in the Workflow Service upon request creation.
    - Publishes domain events to an event bus for other services to consume.

---

## **Data Flow**

```mermaid
sequenceDiagram
    participant User
    participant RequestService
    participant WorkflowService
    participant EventBus
    participant NotificationService

    User->>RequestService: Create Request
    RequestService->>WorkflowService: Start Approval Workflow
    WorkflowService-->>RequestService: Workflow Updates
    RequestService->>EventBus: Publish RequestCreated Event
    EventBus->>NotificationService: Forward Events
    NotificationService-->>User: Send Notifications
```

---

## **Entities**

### **1. Request**

Represents a generic request within the system.

| **Field**             | **Type**    | **Description**                                      |
|------------------------|-------------|------------------------------------------------------|
| `id`                  | UUID        | Unique identifier.                                  |
| `requestType`         | String      | Type of the request (e.g., `AbsenceAuthorization`). |
| `requesterId`         | UUID        | ID of the user who created the request.             |
| `targetOperatorIds`   | string[]    | ID of the operators for on-behalf requests.         |
| `status`              | String      | Current status of the request.                      |
| `data`                | JSON        | Specific data related to the request type.          |
| `createdAt`           | Timestamp   | Creation time.                                      |
| `updatedAt`           | Timestamp   | Last update time.                                   |
| `workflowInstanceId`  | UUID        | Associated workflow instance ID.                    |

---

## **API Endpoints**

### **1. Create Request**

| **Method** | **Endpoint**     | **Description**                       |
|------------|------------------|---------------------------------------|
| POST       | `/requests`      | Creates a new request.                |

#### **Request Body Example**:
```json
{
  "requestType": "AbsenceAuthorization",
  "data": {
    "startDate": "2023-11-01",
    "endDate": "2023-11-05",
    "reason": "Medical appointment"
  }
}
```

#### **Response**:
```json
{
  "id": "request-uuid",
  "requestType": "AbsenceAuthorization",
  "requesterId": "user-uuid",
  "status": "Pending",
  "createdAt": "2023-10-15T12:00:00Z",
  "updatedAt": "2023-10-15T12:00:00Z"
}
```

---

### **2. Create On-Behalf Request**

| **Method** | **Endpoint**          | **Description**                                   |
|------------|-----------------------|-------------------------------------------------|
| POST       | `/requests/on-behalf` | Team Leaders create requests on behalf of operators. |

#### **Request Body Example**:
```json
{
  "targetOperatorIds": ["operator-1", "operator-2"],
  "requestType": "Overtime",
  "data": {
    "date": "2023-11-10",
    "hours": 2,
    "reason": "Project deadline"
  }
}
```

#### **Response**:
```json
{
  "id": "request-uuid",
  "requestType": "Overtime",
  "requesterId": "team-leader-uuid",
  "targetOperatorIds": "operator-uuid",
  "status": "Pending",
  "createdAt": "2023-10-15T12:05:00Z",
  "updatedAt": "2023-10-15T12:05:00Z"
}
```

---

### **3. Get Request by ID**

| **Method** | **Endpoint**     | **Description**                    |
|------------|------------------|------------------------------------|
| GET        | `/requests/{id}` | Retrieves a specific request.      |

---

### **4. List Requests**

| **Method** | **Endpoint**     | **Description**                    |
|------------|------------------|------------------------------------|
| GET        | `/requests`      | Lists requests for the user.       |

---

### **5. Update Request Status**

| **Method** | **Endpoint**              | **Description**                       |
|------------|---------------------------|---------------------------------------|
| PUT        | `/requests/{id}/status`   | Updates the status of a request.      |

#### **Request Body Example**:
```json
{
  "status": "Approved",
  "comments": "Approved by manager"
}
```

---

## **Domain Events**

| **Event**                  | **Description**                                      |
|----------------------------|----------------------------------------------------|
| `RequestCreated`           | Emitted when a new request is created.              |
| `RequestOnBehalfCreated`   | Emitted when a Team Leader creates a request on behalf. |
| `MultiOperatorRequestCreated`| Emitted when a request is created for multiple operators. |
| `RequestUpdated`           | Emitted when a request is updated (e.g., status changes). |

---

## **Functionality**

- When a Team Leader creates a request for multiple operators, the Request Service generates a single request record and associates it with all the specified operators. This ensures that all operators are linked to the same request, maintaining consistency across the system.

- For multi-operator requests, a single workflow instance is initiated, and the workflow process is shared by all operators involved.

## **Process Flow**



- **Request Creation**:
 - Users can create requests for themselves.
 - Team Leaders can create requests on behalf of one or multiple operators.
 - The Team Leader provides a list of operator IDs to the Request Service
 - For multi-operator requests, a single request is created and associated with all specified operators.

- **Multi-Operator Request Handling**: 
- When a Team Leader creates a request for multiple operators:
  - A single request record is created.
  - The targetOperatorIds field stores the IDs of all affected operators.
  - A single workflow instance is started that will handle the approval process for all the operators in the list.
  - The Notification Service sends updates to all involved operators when the status changes (e.g., approval or rejection).
  - The request status is synchronized across all operators.


## **Integration with Other Services**

- **Workflow Service**: Manages approval processes.
- **Notification Service**: Alerts users of status updates or approvals.

---

## **Error Handling**

| **Code** | **Description**                              |
|----------|---------------------------------------------|
| 400      | Bad Request - Validation failed.            |
| 401      | Unauthorized - Authentication failed.       |
| 403      | Forbidden - User lacks required permissions.|
| 404      | Not Found - Resource does not exist.        |
| 500      | Internal Server Error - Unexpected issue.   |
