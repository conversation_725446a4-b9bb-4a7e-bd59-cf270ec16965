# Technology Stack

### Message queues
- Use message queues **Azure Service Bus or Azure eventHub** for asynchronous communication between microservices.
- Microservices publish messages to specific queues/topics, and other services subscribe to these queues/topics to receive messages.
- This approach decouples the services and allows for better scalability and fault tolerance.
- Implement message acknowledgment and retries to ensure message delivery.

## Database CosmosDB
- Use CosmosDB for storing workflow configurations.
- CosmosDB's document-oriented storage model allows for flexible and dynamic schema design, which is suitable for varying workflow configurations.
- It supports horizontal scaling and high availability, making it a good choice for large-scale systems.
- CosmosDB provides built-in versioning support through its change streams and versioning patterns.

### RESTful APIs
- Use RESTful APIs for synchronous communication between microservices.
- Each microservice exposes its own set of endpoints for other services to interact with.
- Use HTTP/HTTPS protocols for secure and reliable communication.
- Implement proper error handling and retries to ensure robustness.

## API Gateway with Authentication and Authorization
- Use an API Gateway (Azure API Management) to handle authentication and authorization.
- Configure the API Gateway to integrate with Azure AD for user authentication.
- Use OAuth 2.0 or JWT for token-based authentication and authorization.
- Implement role-based access control (RBAC) within the API Gateway to manage user permissions and roles.
- Store user roles and permissions in a database such as CosmosDB to allow for easy management and updates.
- Secure RESTful APIs by routing all requests through the API Gateway, ensuring only authorized users can access specific endpoints.
