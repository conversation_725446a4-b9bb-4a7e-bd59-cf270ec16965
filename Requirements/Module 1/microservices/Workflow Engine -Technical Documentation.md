# Workflow Engine Service - Technical Design and Specifications

## **The Workflow Engine Service** focuses on defining and validating complex business processes through configurable workflows. It ensures correctness, flexibility, and easy versioning of workflow definitions.

## Overview of the Data Model

1. **Workflow**: The top-level entity representing an entire business process definition.
2. **WorkflowNode**: A single step in the workflow, defined by a type and a node-specific configuration.
3. **WorkflowEdge**: Defines transitions between nodes, controlling the flow of execution.
4. **Configurations**: Various configuration objects tailor node behavior (approvals, notifications, documents, conditions, etc.).

This layered approach ensures a clean separation between the overall workflow structure (Workflow, Nodes, Edges) and the per-node logic (Configs).

---

## Core Entities

### 1. Workflow Entity

**Purpose**: Represents the entire workflow definition, containing metadata, nodes, and edges.

**Key Fields**:

- `id` (UUID): Unique identifier for the workflow.
- `name` (string): Name of the workflow.
- `description` (string, optional): Describes the workflow’s purpose.
- `isActive` (boolean): Indicates if this workflow version is active.
- `nodes` (WorkflowNode[]): An array of all nodes in this workflow.
- `edges` (WorkflowEdge[]): Defines the connections and transitions between nodes.

**Example**:

```json
{
  "id": "workflow-123",
  "name": "Leave Request Process",
  "description": "Workflow for employee leave requests",
  "isActive": false,
  "nodes": [],
  "edges": []
}
```

---

### 2. WorkflowNode Entity

**Purpose**: Each `WorkflowNode` represents one step within the workflow. The node's `type` determines which configuration object it requires.

**Key Fields**:

- `id` (UUID): Unique node identifier.
- `FormId` (string): Associated form identifier (e.g., a data collection form).
- `type` (NodeType): The node's behavior type (`START`, `END`, `SINGLE_APPROVAL`, `PARALLEL_APPROVAL`, `DOCUMENT`, `NOTIFICATION`, `CONDITION`, etc.).
- `title` (string): Human-readable title.
- `position` (NodePosition, optional): Graphical coordinates for design tools.
- `config`: Configuration object, structure depends on `type`.

**Example**:

```json
{
  "id": "node-manager-approval",
  "FormId": "leave_request_form",
  "type": "SINGLE_APPROVAL",
  "title": "Manager Approval",
  "position": { "x": 200, "y": 100 },
  "config": {
    "approver": {
      "id": "appr-1",
      "type": "ROLE",
      "value": "MANAGER"
    },
    "onReject": { "action": "STOP_FLOW" }
  }
}
```

**Supported Node Types** (Examples):

- **START**: Begins the workflow (requires a `StartNodeConfig`).
- **END**: Ends the workflow (minimal or no config).
- **SINGLE_APPROVAL**: A single approver must approve.
- **PARALLEL_APPROVAL**: Multiple approvers concurrently.
- **DOCUMENT**: Generates a document from a template.
- **NOTIFICATION**: Sends notifications to defined recipients.
- **CONDITION**: Evaluates conditions to decide which path to take next.

---

### 3. WorkflowEdge Entity

**Purpose**: `WorkflowEdge` defines how nodes connect. They represent transitions from one node to another, often conditionally or after a node completes.

**Key Fields**:

- `fromNode` (string): The ID of the starting node.
- `toNode` (string): The ID of the target node.
- `condition` (optional string): A condition to evaluate for this transition.

**Example**:

```json
{
  "fromNode": "node-manager-approval",
  "toNode": "node-document-generation",
  "condition": "approvalGranted == true"
}
```

Edges may be straightforward (sequential flow) or driven by conditions evaluated at runtime.

---

## Node Configurations

Once we’ve defined the workflow structure (Workflow, Nodes, Edges), we specify configurations that shape each node’s behavior. Below are the configuration classes and examples.

### Common Enums and Types

- **NodeType**: `START`, `END`, `SINGLE_APPROVAL`, `PARALLEL_APPROVAL`, `DOCUMENT`, `NOTIFICATION`, `CONDITION`
- **ApproverType**: `USER`, `ROLE`
- **NotificationChannel**: `EMAIL`, `SMS`, `ON APP`
- **TimeAction**: `MOVE_TO_NEXT_NODE`
- **ConditionRuleType**: `VALUE`, `HISTORICAL`, `STATE`, `ORGANIZATIONAL`

---

### 1. StartNodeConfig

**Purpose**: Determines who can initiate the workflow.

**Fields**:

- `initiators`: An object listing allowed roles, departments, or teams.

````

**Example**:
```json
{
  "initiators": {
    "roles": ["EMPLOYEE", "TEAM_MANAGER"],
  },
  "allowProxyInitiation": true
}
````

---

---

### 3. NotificationConfig

**Purpose**: Specifies how and where to send notifications.

**Fields**:

- `recipientRules` (RecipientRule[]) - Array of rules defining who receives notifications and through which channels
  - `type` (string) - Type of recipient ("DEPARTMENT" or "ROLE")
  - `value` (string) - The department name or role ID based on type
  - `channel` (string) - The notification channel (e.g., "EMAIL", "CW", "SLACK")
  - `templateId` (string) - ID or reference to the template to use for this recipient/
  - `priority` levels (HIGH, MEDIUM, LOW)
    channel combination

**Example**:

```json
{
  "recipientRules": [
    {
      "type": "DEPARTMENT",
      "value": "MAIN_GATE",
      "priority": "LOW",
      "channels": [
        {
          "type": "EMAIL",
          "templateId": "main_gate_email_template"
        },
        {
          "type": "CW",
          "templateId": "main_gate_cw_template"
        }
      ]
    },
    {
      "type": "ROLE",
      "value": "TKS_AGENT",
      "priority": "HIGH",
      "channels": [
        {
          "type": "CW",
          "templateId": "tks_agent_cw_template"
        },
        {
          "type": "EMAIL",
          "templateId": "tks_agent_email_template"
        }
      ]
    },
    {
      "type": "ROLE",
      "value": "TRANSPORT_AGENT",
      "priority": "LOW",
      "channels": [
        {
          "type": "CW",
          "templateId": "transport_agent_notification_template"
        }
      ]
    },
    {
      "type": "ROLE",
      "value": "LABOR_RELATIONS_SUPERVISOR",
      "channels": [
        {
          "type": "EMAIL",
          "templateId": "labor_relations_supervisor_template"
        }
      ]
    }
  ]
}
```

---

### 4. Escalation Configuration

**EscalationApprover**:

```json
{
  "type": "ROLE",
  "value": "SENIOR_MANAGER"
}
```

**EscalationChainItem**:

```json
{
  "approver": { "type": "USER", "value": "user-456" },
  "afterHours": 24,
  "notifyOriginal": true
}
```

**EscalationFallback**:

```json
{
  "action": "AUTO_REJECT"
}
```

**EscalationConfig**:

```json
{
  {
  "enabled": true,
  "chain": [
    {
      "approver": { "type": "ROLE", "value": "HEAD_OF_DEPT" },
      "afterMinutes": 2880,
      "notifyOriginal": false
    },
    {
      "approver": { "type": "ROLE", "value": "DIRECTOR" },
      "afterMinutes": 1440,
      "notifyOriginal": true
    },
    {
      "approver": { "type": "ROLE", "value": "DEPARTMENT_MANAGER" },
      "afterMinutes": 720,
      "notifyOriginal": true
    }
  ],
  "finalAuthority": {
    "type": "ROLE",
    "value": "DEPARTMENT_MANAGER"
  },
  "fallback": { "action": "AUTO_APPROVE" },
}
}
```

---

### 5. ApproverConfig

Defines the approver details for approval nodes.

**Example**:

```json
{
  "id": "approver-1",
  "type": "USER",
  "value": "user-123",
  "escalation": {
    "enabled": true,
    "chain": [
      {
        "approver": { "type": "ROLE", "value": "DIRECTOR" },
        "afterHours": 24
      }
    ],
    "fallback": { "action": "AUTO_REJECT" }
  }
}
```

---

### 6. DocumentGenerationConfig

**Purpose**: Used in `DOCUMENT` nodes to generate a document.

**Fields**:

- `templateId` (string)
- `outputFormat` (string)

**Example**:

```json
{
  "templateId": "offer_letter_template",
  "outputFormat": "PDF"
}
```

---

### 7. TimingConfig

**Purpose**: Defines deadlines and reminder intervals for approvals.

**Fields**:

- `deadlineHours` (number)
- `reminderIntervalHours` (number)
- `ontimeend` (TimeAction)

**Example**:

```json
{
  "deadlineHours": 48,
  "reminderIntervalHours": 24,
  "ontimeend": "ESCALATE"
}
```

---

### 8. RejectConfig

**Purpose**: Defines what happens if an approval is rejected.

**Fields**:

- `targetNodeId` (string, optional)
- `formId` (string, optional)
- `action` ("CONTINUE" or "STOP_FLOW")

**Example**:

```json
{
  "action": "STOP_FLOW",
  "formId": "rejection_reason_form"
}
```

---

### 9. SingleApprovalConfig & ParallelApprovalConfig

**SingleApprovalConfig**:

- `approver` (ApproverConfig)
- `notifications` (NotificationConfig, optional)
- `timing` (TimingConfig, optional)
- `onReject` (RejectConfig)

**Example**:

```json
{
  "approver": {
    "id": "manager-1",
    "type": "ROLE",
    "value": "MANAGER"
  },
  "onReject": { "action": "STOP_FLOW" }
}
```

**ParallelApprovalConfig**:

- `approvers` (ApproverConfig[])
- `notifications` (NotificationConfig, optional)
- `timing` (TimingConfig, optional)
- `onReject` (RejectConfig)

**Example**:

```json
{
  "approvers": [
    { "id": "appr-fin", "type": "ROLE", "value": "FINANCE_MANAGER" },
    { "id": "appr-hr", "type": "ROLE", "value": "HR_MANAGER" }
  ],
  "onReject": { "action": "STOP_FLOW" }
}
```

---

### 10. ConditionNode Config

**Purpose**: A `CONDITION` node evaluates conditions to determine the next node.

**Fields**:

- `conditions`: An array of condition definitions.
- `routing`: Defines how to route based on condition evaluation.

**Value Rules**:

```json
"valueRules": [
  {
    "field": "request.amount",
    "operator": "gt",
    "value": 5000,
    "logicalOperator": "AND"
  }
]
```

**Historical Rules**:

```json
"historicalRules": {
  "lookbackPeriodDays": 30,
  "requestCount": 5,
  "requestTypes": ["LEAVE"],
  "status": ["APPROVED"]
}
```

**State Rules**:

```json
"stateRules": [
  {
    "nodeId": "node-manager-approval",
    "status": "COMPLETED",
    "outcome": "APPROVED"
  }
]
```

**Organizational Rules**:

```json
"organizationalRules": [
  {
    "type": "ROLE",
    "value": "MANAGER",
    "condition": "EQUALS"
  }
]
```

**Routing**:

- `defaultPath` (string): The node to route to if no conditions match.
- `paths` (array): Maps `condition` IDs to `targetNode`s.

**ConditionNode Example**:

```json
{
  "id": "node-condition-check",
  "FormId": "request_form",
  "type": "CONDITION",
  "title": "Check Request Conditions",
  "config": {
    "conditions": [
      {
        "id": "cond-high-value",
        "name": "High Value Check",
        "type": "VALUE",
        "valueRules": [
          {
            "field": "request.amount",
            "operator": "gt",
            "value": 5000
          }
        ]
      },
      {
        "id": "cond-is-manager",
        "name": "Is Manager Check",
        "type": "ORGANIZATIONAL",
        "organizationalRules": [
          {
            "type": "ROLE",
            "value": "MANAGER",
            "condition": "EQUALS"
          }
        ]
      }
    ],
    "routing": {
      "defaultPath": "node-end",
      "paths": [
        {
          "condition": "cond-high-value",
          "targetNode": "node-parallel-approval"
        },
        {
          "condition": "cond-is-manager",
          "targetNode": "node-document-generation"
        }
      ]
    }
  }
}
```

### 11. UpdateUserStatus Node Configuration

#### Overview

The UpdateUserStatus node serves as a specialized node type within the workflow engine, designed to update user statuses based on form data from previous nodes in the workflow. This node type is particularly useful in workflows such as leave requests, where user status changes need to be tracked and managed throughout the process.

#### Data Model

The UpdateUserStatus node configuration extends the base node structure with specific configurations for form field mapping and status updates:

```json
{
  "id": "node-update-user-status",
  "type": "UPDATE_USER_STATUS",
  "title": "Update User Status",
  "config": {
    "mapping": {
      "sourceNode": {
        "nodeId": "node-start",
        "formId": "leave_request_form"
      },
      "fields": {
        "from": "startDate",
        "to": "endDate"
      }
    },
    "statusConfig": {
      "type": "LEAVE_STATUS",
      "value": "ON_LEAVE"
    }
  }
}
```

#### Configuration Components

1. **Source Node Mapping**

   - `nodeId`: References the source node containing the required form data
   - `formId`: Identifies the form associated with the source node
   - This dual reference ensures proper data traceability and access

2. **Field Mapping**

   - Maps target status fields to their corresponding source form fields
   - Common mappings include:
     - Date range fields (from/to)
     - Status-specific fields (type, reason, etc.)

3. **Status Configuration**
   - Defines the type and value of the status to be set
   - Supports different status types (LEAVE, AVAILABILITY, etc.)

#### Workflow Integration

The UpdateUserStatus node typically appears after approval nodes in workflows:

```json
{
  "edges": [
    { "fromNode": "node-approval", "toNode": "node-update-status" },
    { "fromNode": "node-update-status", "toNode": "node-notification" }
  ]
}
```

#### Runtime Behavior

1. **Data Resolution**

   - The workflow engine resolves the source node using nodeId
   - Validates the existence of the referenced formId
   - Extracts the mapped fields from the form data

2. **Status Update Execution**
   - Uses the extracted field values to update the user's status
   - Applies the configured status type and value
   - Records the update in the workflow history

#### Implementation Considerations

1. **Data Availability**

   - The workflow engine must ensure the source node has been executed
   - Form data must be persisted and accessible

2. **Error Handling**
   - Invalid node or form references should trigger workflow errors
   - Missing required fields should be detected and handled appropriately

#### Usage Examples

Common use cases include:

1. **Leave Request Workflow**

   ```json
   {
     "mapping": {
       "sourceNode": {
         "nodeId": "node-leave-request",
         "formId": "leave_request_form"
       },
       "fields": {
         "from": "leaveStartDate",
         "to": "leaveEndDate"
       }
     },
     "statusConfig": {
       "type": "LEAVE_STATUS",
       "value": "ON_LEAVE"
     }
   }
   ```

2. **Availability Management**
   ```json
   {
     "mapping": {
       "sourceNode": {
         "nodeId": "node-schedule-update",
         "formId": "availability_form"
       },
       "fields": {
         "from": "availableFrom",
         "to": "availableTo"
       }
     },
     "statusConfig": {
       "type": "AVAILABILITY_STATUS",
       "value": "AVAILABLE"
     }
   }
   ```

---

## Real-World Example: Leave Request Workflow

**Scenario**: An employee requests leave. A condition node checks if `request.days > 5`; if so, parallel approvals are required (HR & Dept Head). Otherwise, only the manager’s single approval is needed. After approvals, the workflow generates a leave confirmation document, then notifies the requester.

**Workflow (simplified)**:

```json
{
  "id": "workflow-leave-request",
  "name": "Employee Leave Request",
  "description": "Approvals for leave above 5 days, otherwise direct manager approval.",
  "isActive": false,
  "nodes": [
    {
      "id": "node-start",
      "FormId": "leave_form",
      "type": "START",
      "title": "Start Node",
      "config": {
        "initiators": {
          "roles": ["EMPLOYEE"]
        }
      }
    },
    {
      "id": "node-condition-check",
      "FormId": "leave_form",
      "type": "CONDITION",
      "title": "Check Leave Duration",
      "config": {
        "conditions": [
          {
            "id": "cond-long-leave",
            "name": "Long Leave Check",
            "type": "VALUE",
            "valueRules": [
              {
                "field": "request.days",
                "operator": "gt",
                "value": 5
              }
            ]
          }
        ],
        "routing": {
          "defaultPath": "node-manager-approval",
          "paths": [
            {
              "condition": "cond-long-leave",
              "targetNode": "node-parallel-approval"
            }
          ]
        }
      }
    },
    {
      "id": "node-manager-approval",
      "FormId": "leave_form",
      "type": "SINGLE_APPROVAL",
      "title": "Manager Approval",
      "config": {
        "approver": { "id": "appr-1", "type": "ROLE", "value": "MANAGER" },
        "onReject": { "action": "STOP_FLOW" }
      }
    },
    {
      "id": "node-parallel-approval",
      "FormId": "leave_form",
      "type": "PARALLEL_APPROVAL",
      "title": "HR and Dept Head Approval",
      "config": {
        "approvers": [
          { "id": "appr-hr", "type": "ROLE", "value": "HR_MANAGER" },
          { "id": "appr-dep", "type": "ROLE", "value": "DEPT_HEAD" }
        ],
        "onReject": { "action": "STOP_FLOW" }
      }
    },
    {
      "id": "node-document-generation",
      "FormId": "",
      "type": "DOCUMENT",
      "title": "Generate Approval Document",
      "config": {
        "templateId": "leave_approval_letter",
        "outputFormat": "PDF"
      }
    },
    {
      "id": "node-notification",
      "FormId": "",
      "type": "NOTIFICATION",
      "title": "Notify Employee",
      "config": {
        "channels": ["EMAIL"],
        "templates": {
          "assigned": "assigned_tpl",
          "reminder": "reminder_tpl",
          "escalated": "escalated_tpl",
          "completed": "leave_approved_notification"
        },
        "additionalRecipients": ["<EMAIL>"]
      }
    },
    {
      "id": "node-end",
      "FormId": "",
      "type": "END",
      "title": "End Node"
    }
  ],
  "edges": [
    { "fromNode": "node-start", "toNode": "node-condition-check" },
    { "fromNode": "node-condition-check", "toNode": "node-manager-approval" },
    { "fromNode": "node-condition-check", "toNode": "node-parallel-approval" },
    {
      "fromNode": "node-manager-approval",
      "toNode": "node-document-generation"
    },
    {
      "fromNode": "node-parallel-approval",
      "toNode": "node-document-generation"
    },
    { "fromNode": "node-document-generation", "toNode": "node-notification" },
    { "fromNode": "node-notification", "toNode": "node-end" }
  ]
}
```

If `request.days <= 5`, the workflow proceeds with a single manager approval. If `request.days > 5`, parallel approvals are required. After approvals, a document is generated and a notification sent to the employee.

---

## Conclusion

This documentation starts with the Workflow entity, followed by WorkflowNode and WorkflowEdge, then details each configuration type. The addition of the `CONDITION` node type and `StartNodeConfig` demonstrates how complex routing logic and initiation rules can be integrated into the workflow definition.
