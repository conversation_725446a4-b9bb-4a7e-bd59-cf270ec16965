# DRAFT need data team finish
# UserData Service Design

## Overview

The **UserData Service** is a microservice responsible for managing user information within an enterprise system. It provides functionalities to retrieve and manipulate user data, handle hierarchical relationships between users (e.g., managers and subordinates), and integrate with other services through domain events. The service uses a document-oriented database (Cosmos DB) for storage and is designed to be abstract enough to be implemented in any programming language.

---

## High-Level Architecture

### Components

1. **API Layer**: Exposes RESTful endpoints for interacting with user data.
2. **Service Layer**: Contains business logic for processing user information and relationships.
3. **Data Access Layer**: Interfaces with Cosmos DB to perform CRUD operations.
4. **Event Publisher**: Publishes domain events (e.g., `UserCreated`, `HierarchyUpdated`) to an event bus for other services to consume.
5. **Integration Layer**: Allows other microservices (e.g., Workflow Service, Notification Service) to interact with the UserData Service via APIs and event subscriptions.

### Architecture Diagram


---

## Data Model

### User Entity

Represents a user within the system.

**Fields**:

- `id` (UUID): Unique identifier of the user.
- `name` (string): Full name of the user.
- `email` (string): Email address.
- `role` (string): Role or position (e.g., "Operator", "Manager").
- `manager_id` (UUID, nullable): ID of the user's direct manager.
- `department` (string): Department name.
- `additional_info` (object, optional): Any additional metadata.

**Example**:

```json
{
  "id": "user-uuid",
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "role": "Operator",
  "manager_id": "manager-uuid",
  "department": "Sales",
  "additional_info": {
    "phone": "+1234567890",
    "location": "Building A"
  }
}
```

---

## API Endpoints

### 1. Fetch User by ID

- **Endpoint**: `GET /users/{id}`
- **Description**: Retrieves a user's details by their unique ID.

**Request Parameters**:

- `id` (path): The UUID of the user to retrieve.

**Response**:

- **200 OK**:

  ```json
  {
    "id": "user-uuid",
    "name": "Jane Doe",
    "email": "<EMAIL>",
    "role": "Operator",
    "manager_id": "manager-uuid",
    "department": "Sales"
  }
  ```

- **404 Not Found**: User does not exist.

---

### 2. Fetch All Users

- **Endpoint**: `GET /users`
- **Description**: Retrieves a list of all users, with optional filtering.

**Query Parameters** (optional):

- `department` (string): Filter users by department.
- `role` (string): Filter users by role.

**Response**:

- **200 OK**:

  ```json
  [
    {
      "id": "user-uuid-1",
      "name": "Jane Doe",
      "email": "<EMAIL>",
      "role": "Operator",
      "manager_id": "manager-uuid",
      "department": "Sales"
    },
    {
      "id": "user-uuid-2",
      "name": "John Smith",
      "email": "<EMAIL>",
      "role": "Manager",
      "manager_id": null,
      "department": "Sales"
    }
  ]
  ```

---

### 3. Fetch Subordinates of a Manager

- **Endpoint**: `GET /users/{id}/subordinates`
- **Description**: Retrieves all users who report directly or indirectly to the specified manager.

**Request Parameters**:

- `id` (path): The UUID of the manager.

**Response**:

- **200 OK**:

  ```json
  [
    {
      "id": "user-uuid-1",
      "name": "Jane Doe",
      "email": "<EMAIL>",
      "role": "Operator",
      "manager_id": "manager-uuid",
      "department": "Sales"
    },
    // Additional subordinates...
  ]
  ```

- **404 Not Found**: Manager does not exist.

---

### 4. Update User's Manager

- **Endpoint**: `PUT /users/{id}/manager`
- **Description**: Updates the manager of a user.

**Request Parameters**:

- `id` (path): The UUID of the user.

**Request Body**:

```json
{
  "manager_id": "new-manager-uuid"
}
```

**Response**:

- **200 OK**:

  ```json
  {
    "message": "Manager updated successfully."
  }
  ```

- **400 Bad Request**: Validation error (e.g., `manager_id` does not exist).
- **404 Not Found**: User does not exist.

---

## Handling Hierarchical Relationships

### Manager-Subordinate Structure

- Each user has a `manager_id` attribute pointing to their direct manager.
- This structure creates a hierarchy that can be traversed to find subordinates or superiors.

### Querying Subordinates

- **Direct Subordinates**: Users whose `manager_id` matches the manager's `id`.
- **Indirect Subordinates**: Users who are subordinates of the manager's subordinates.

### Algorithm for Fetching All Subordinates

1. **Initialize**: Start with the manager's `id`.
2. **Recursive Fetch**: Retrieve all users whose `manager_id` is in the current list of IDs.
3. **Accumulate**: Add fetched users to the result set.
4. **Repeat**: Use the newly fetched users' IDs to find further subordinates.
5. **Terminate**: When no new users are found.

---

## Integration with Other Services

### Domain Events

The UserData Service publishes domain events to an event bus (e.g., Azure Service Bus) to notify other services of changes.

#### Events Published

1. **UserCreated**

   - **When**: A new user is added to the system.
   - **Payload**:

     ```json
     {
       "eventType": "UserCreated",
       "user": {
         "id": "user-uuid",
         "name": "Jane Doe",
         "email": "<EMAIL>",
         "role": "Operator",
         "manager_id": "manager-uuid",
         "department": "Sales"
       },
       "timestamp": "2023-10-15T12:00:00Z"
     }
     ```

2. **UserUpdated**

   - **When**: An existing user's details are updated.
   - **Payload**:

     ```json
     {
       "eventType": "UserUpdated",
       "user": {
         "id": "user-uuid",
         "name": "Jane Doe",
         "email": "<EMAIL>",
         "role": "Operator",
         "manager_id": "manager-uuid",
         "department": "Sales"
       },
       "timestamp": "2023-10-16T09:00:00Z"
     }
     ```

3. **HierarchyUpdated**

   - **When**: A user's `manager_id` is changed.
   - **Payload**:

     ```json
     {
       "eventType": "HierarchyUpdated",
       "userId": "user-uuid",
       "oldManagerId": "old-manager-uuid",
       "newManagerId": "new-manager-uuid",
       "timestamp": "2023-10-17T15:30:00Z"
     }
     ```

### Consuming Services

- **Workflow Service**: Subscribes to `UserCreated` and `HierarchyUpdated` events to update approval hierarchies and assignment rules.
- **Notification Service**: Uses user data to send notifications to the correct recipients.
- **Other Services**: Any service that requires up-to-date user and hierarchy information.

### Sequence Diagram: Updating a User's Manager

```mermaid
sequenceDiagram
    participant Client
    participant UserDataService
    participant EventBus
    participant WorkflowService
    participant NotificationService

    Client->>UserDataService: PUT /users/{id}/manager
    UserDataService->>UserDataService: Update manager_id
    UserDataService->>EventBus: Publish HierarchyUpdated
    EventBus-->>WorkflowService: HierarchyUpdated Event
    EventBus-->>NotificationService: HierarchyUpdated Event
    UserDataService-->>Client: 200 OK
```

---

## Data Flow

### User Creation Flow

1. **Client** sends a request to create a new user.
2. **UserData Service** validates and stores the user in Cosmos DB.
3. **UserData Service** publishes a `UserCreated` event to the event bus.
4. **Other Services** consume the `UserCreated` event to update their data or trigger actions.

### Manager Update Flow

1. **Client** sends a `PUT` request to update a user's `manager_id`.
2. **UserData Service** validates the new manager and updates the user record.
3. **UserData Service** publishes a `HierarchyUpdated` event.
4. **Workflow Service** updates approval hierarchies based on the new manager.
5. **Notification Service** adjusts notification routes if necessary.

---

## Sequence Diagram: User Creation Integration

```mermaid
sequenceDiagram
    participant Client
    participant UserDataService
    participant EventBus
    participant WorkflowService
    participant NotificationService

    Client->>UserDataService: POST /users
    UserDataService->>UserDataService: Validate and store user
    UserDataService->>EventBus: Publish UserCreated
    EventBus-->>WorkflowService: UserCreated Event
    EventBus-->>NotificationService: UserCreated Event
    UserDataService-->>Client: 201 Created
```

---

## Error Handling

- **400 Bad Request**: Returned when the request data is invalid.
  - Examples: Missing required fields, invalid `manager_id`, incorrect data types.
  - **Response**:

    ```json
    {
      "error": "ValidationError",
      "message": "The 'email' field is required."
    }
    ```

- **404 Not Found**: Returned when the requested user or manager does not exist.
  - **Response**:

    ```json
    {
      "error": "NotFoundError",
      "message": "User with ID 'user-uuid' not found."
    }
    ```

- **500 Internal Server Error**: Returned when an unexpected error occurs.
  - **Response**:

    ```json
    {
      "error": "InternalServerError",
      "message": "An unexpected error occurred."
    }
    ```

---

## Cosmos DB Data Modeling

- **Container**: Users
- **Partition Key**: `id` (since `id` is unique and queries are mostly by `id`)

### Sample Document Structure

```json
{
  "id": "user-uuid",
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "role": "Operator",
  "manager_id": "manager-uuid",
  "department": "Sales",
  "additional_info": {
    "phone": "+1234567890",
    "location": "Building A"
  }
}
```

---

## High-Level Data Flow Diagram



## Abstract Implementation Considerations

- **Programming Language Agnostic**: The service design does not rely on any language-specific features, making it suitable for implementation in languages like Java, C#, Python, etc.
- **RESTful APIs**: Standard HTTP methods and status codes are used.
- **JSON Format**: For request and response bodies, which is widely supported.
- **Event Bus Integration**: Uses common messaging patterns that can be implemented with various technologies (e.g., Azure Service Bus, RabbitMQ, Kafka).
- **Database Abstraction**: While Cosmos DB is specified, any document-oriented database can be used with similar capabilities (e.g., MongoDB, Couchbase).

---

## Security Considerations

- **Authentication**: Ensure that all endpoints are secured and require valid authentication tokens (e.g., JWT).
- **Authorization**: Implement role-based access control (RBAC) to restrict access to sensitive operations (e.g., only admins can update user information).
- **Input Validation**: Validate all incoming data to prevent injection attacks and ensure data integrity.
- **Data Privacy**: Handle personal data according to data protection regulations (e.g., GDPR).

---

## Additional Features (Optional)

### Pagination and Sorting

- **Endpoint**: `GET /users`
- **Query Parameters**:
  - `page` (integer): Page number.
  - `pageSize` (integer): Number of items per page.
  - `sortBy` (string): Field to sort by.
  - `sortOrder` (string): `asc` or `desc`.

### Search Functionality

- Allow searching users by name, email, or other attributes using query parameters.

### Bulk Operations

- Endpoints to create, update, or delete multiple users in a single request.

---

## Conclusion

The **UserData Service** provides a robust and flexible foundation for managing user information and hierarchical relationships within an enterprise system. By integrating with other services through domain events and providing a comprehensive API, it supports the dynamic needs of modern applications while maintaining a clean and scalable architecture.

