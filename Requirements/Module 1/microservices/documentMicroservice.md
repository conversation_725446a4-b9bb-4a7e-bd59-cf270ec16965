# Document Generation Microservice

## Overview

The **Document Generation Microservice** handles:

- **Generating digital and physical documents** based on templates.
- **Converting HTML content** into formats like PDF or DOCX.
- **Storing generated documents** in Azure Blob Storage.
- **Providing endpoints** to retrieve and download stored documents.
- **Uploading and managing scanned documents**.
- **Emitting events** such as `DocumentGenerated` and `DocumentUploaded`.

---

## Entities

### Document

Represents a generated or uploaded document.

- **id**: Unique identifier of the document.
- **userId**: ID of the user associated with the document.
- **fileName**: Name of the document file.
- **fileType**: MIME type (e.g., `application/pdf`, `image/jpeg`).
- **storageUri**: URI where the document is stored in Azure Blob Storage.
- **documentType**: Indicates if the document is generated or uploaded (`GENERATED`, `UPLOADED`).
- **metadata**: Additional information (e.g., template ID for generated documents).
- **createdAt**: Timestamp when the document was created or uploaded.

#### UploadedDocument

Represents a document that has been uploaded by a user.

- **id**: Unique identifier of the uploaded document.
- **userId**: ID of the user associated with the uploaded document.
- **fileName**: Name of the uploaded document file.
- **fileType**: MIME type of the uploaded document (e.g., `application/pdf`, `image/jpeg`).
- **storageUri**: URI where the uploaded document is stored in Azure Blob Storage.
- **documentType**: Indicates that the document is uploaded (`UPLOADED`).
- **documentMetadata**: Additional information about the uploaded document (e.g., category, tags).
- **createdAt**: Timestamp when the uploaded document was created or uploaded.

#### GeneratedDocument

Represents a document that has been generated by the system. Inherits from `UploadedDocument`.

- **id**: Unique identifier of the generated document.
- **userId**: ID of the user associated with the generated document.
- **fileName**: Name of the generated document file.
- **fileType**: MIME type of the generated document (e.g., `application/pdf`, `image/jpeg`).
- **storageUri**: URI where the generated document is stored in Azure Blob Storage.
- **documentType**: Indicates that the document is generated (`GENERATED`).
- **documentMetadata**: Additional information about the generated document.
- **createdAt**: Timestamp when the generated document was created or uploaded.

**Note**: `GeneratedDocument` extends from `UploadedDocument`.

---

### Data Flow

```mermaid
flowchart TD
    A[Receive GenerateDocument Event] --> B[Fetch Template Details]
    B --> C[Get Container Name]
    C --> D[Query Template Container]
    D --> E[Merge Data with Template]
    E --> F[Generate Document]
    F --> G[Store in Blob Storage]
    G --> H[Emit DocumentGenerated Event]

```

## API Endpoints

- **Listener**: Listens to `GenerateDocument` events.
- **Description**: Generates a document from HTML content when a `GenerateDocument` event is received.

**Event Payload**:

- **eventype**: Type of event (e.g `GenerateDocument`)
- **htmlContent**: the html code of the template.
- **contextId**: Identifier for fetching rendered template data.
- **outputFormat**: Desired format (`pdf`, `docx`).
- **isPhysical** (optional): Whether a physical copy is needed.

**Example Event Payload**:

````json
{
  "eventType": "GenerateDocument",
  "htmlContent": "<!DOCTYPE html> <html><body><h1>Hello, World!</h1></body></html>",
  "contextId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "outputFormat": "pdf",
  "isPhysical": false
}

**Example Response - Event Response**:

```json
{
  "eventType" : "DocumentGenerated",
  "documentId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "storageUri": "https://blobstorage.com/documents/doc-789.pdf",
  "fileName": "Generated_Document_timestamp.pdf",
  "fileType": "application/pdf",
  "documentType": "GENERATED",
  "createdAt": "2023-10-01T12:00:00Z"
}
```

---

### 2. Upload Scanned Document

- **Endpoint**: `POST /documents/upload`
- **Description**: Uploads a scanned document to the system.

**Request Parameters**:

- **file**: The scanned document file to upload.
- **metadata** (optional): Additional information about the document (e.g., document category, tags).

**Example Request**:

```http
POST /documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": <scanned_document.pdf>,
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  }
}
```

**Example Response**:

```json
{
  "documentId": "doc-1011",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "documentType": "UPLOADED",
  "documentMetadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

### 3. Retrieve Document Metadata

- **Endpoint**: `GET /documents/{id}`
- **Description**: Retrieves metadata for a specific document.

**Example Request**:

```http
GET /documents/doc-1011
Authorization: Bearer <token>
```

**Example Response**:

```json
{
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "UPLOADED",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

### 4. Download Document

- **Endpoint**: `GET /documents/{id}/download`
- **Description**: Downloads the stored document.

**Example Request**:

```http
GET /documents/doc-1011/download
Authorization: Bearer <token>
```

**Response**:

- Returns the document file for download.

---

## Generate Document Use Case

### Purpose

Allows the system to generate documents (e.g., reports, certificates) from HTML content and store them within the system when a `GenerateDocument` event is received.

### Workflow

1. **Event Trigger**:

   - The system listens for `GenerateDocument` events.

2. **Document Generation**:

   - The service generates the document from the provided HTML content using the specified output format.
   - The generated document is stored in Azure Blob Storage, and a Storage URI is retrieved.
   - After obtaining the storage URI, the service creates a generated document object containing `documentId`, `storageUri`, `fileType`, and other relevant details, and stores it in CosmosDB.

3. **Emit Event**:

   - The service emits a `DocumentGenerated` event containing details about the generated document.

### Event Payload Request

- **eventType**: Type of event (`GenerateDocument`).
- **htmlContent**: The HTML content of the template.
- **contextId**: Identifier for fetching rendered template data.
- **outputFormat**: Desired format (`pdf`, `docx`, `html`).
- **isPhysical** (optional): Whether a physical copy is needed.

**Example Event Payload Request**:

```json
{
  "eventType": "GenerateDocument",
  "htmlContent": "<!DOCTYPE html><html><body><h1>Hello, World!</h1></body></html>",
  "contextId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "outputFormat": "pdf",
  "isPhysical": false
}

### Event Payload Response

- **eventType**: Type of event (`DocumentGenerated`).
- **documentId**: Unique identifier for the generated document.
- **storageUri**: URI where the generated document is stored.
- **fileName**: Name of the generated document file.
- **fileType**: MIME type of the generated document.
- **documentType**: Type of the document (`generated`).
- **isPhysical**: Whether a physical copy was generated.
- **createdAt**: Timestamp when the document was created.

**Example Event Payload Response**:

```json
{
  "eventType": "DocumentGenerated",
  "documentId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "storageUri": "https://blobstorage.com/documents/doc-789.pdf",
  "fileName": "Generated_Document.pdf",
  "fileType": "application/pdf",
  "documentType": "generated",
  "isPhysical": false,
  "createdAt": "2023-10-01T12:00:00Z"
}

## Scan Upload Use Case

### Purpose

Allows users to upload scanned documents (e.g., identity proofs, signed contracts) to be stored and managed within the system.

### Workflow

1. **User Uploads a Scanned Document**:

   - The user sends a `POST` request to `/documents/upload` with the scanned document file and optional metadata.

2. **Document Storage**:

- The service stores the uploaded file in Azure Blob Storage and retrieves a Storage URI.
- After obtaining the storage URI, the service creates an uploaded document object containing `documentId`, `userId`, `storageUri`, `fileType`, and other relevant details, and stores it in CosmosDB.

3. **Emit Event**:

- The service emits a `DocumentUploaded` event containing details about the uploaded document.

4. **Response to User**:

- Returns a response containing the uploaded document object.
- The response includes:
  - - `documentId`: Unique identifier for the document.
  - - `storageURI`: URI where the document is stored.
  - - `FileType`: Type of the uploaded document.
  - - Other relevant details.

### Example Scenario

- **Use Case**: A user needs to submit a scanned copy of their passport for verification.

- **Steps**:

  1. The user accesses the application interface to upload their passport scan.
  2. They select the scanned PDF file and submit it via the upload endpoint.
  3. The Document Microservice stores the file and returns the document details.
  4. The system can now use this document for verification processes.

---

## Domain Events

### DocumentGenerated

- **Emitted When**: A new document is successfully generated and stored.

- **Contains**:

  - `eventType`
  - `documentId`
  - `userId`
  - `fileName`
  - `fileType`
  - `storageUri`
  - `documentType`
  - `createdAt`

**Example Event Payload**:

```json
{
  "eventType": "DocumentUploaded",
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "GENERATED",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

### DocumentUploaded

- **Emitted When**: A scanned document is successfully uploaded and stored.

- **Contains**:

  - `eventType`
  - `documentId`
  - `userId`
  - `fileName`
  - `fileType`
  - `storageUri`
  - `documentType`
  - `metadata` (e.g., category, tags)
  - `createdAt`

**Example Event Payload**:

```json
{
  "eventType": "DocumentUploaded",
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "UPLOADED",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

## Key Points

- **Scan Upload Capability**: Users can upload scanned documents, which are stored and managed alongside generated documents.

- **Unified Document Management**: Both generated and uploaded documents are treated similarly, allowing for consistent retrieval and management.

- **Endpoints Provided**: The `/documents/upload` endpoint allows for uploading, while existing endpoints support retrieval and download.

- **Event Emission**: The `DocumentUploaded` event allows other services to react to new uploads (e.g., triggering verification workflows).

- **Security Considerations**:

  - **File Validation**: Uploaded files are validated to ensure they are of acceptable types and sizes.
  - **Access Control**: Only authorized users can upload and access documents.

---

## Additional Notes

- **Metadata Usage**: Metadata provided during upload can be used to categorize and search for documents.

- **Supported File Types**: The service may restrict uploads to certain file types (e.g., PDF, JPEG, PNG).

- **Storage**: Uploaded documents are stored securely in Azure Blob Storage, similar to generated documents.

- **Error Handling**:

  - **Invalid File Type**: Returns a `400 Bad Request` if the file type is not supported.
  - **File Too Large**: Returns a `413 Payload Too Large` if the file exceeds size limits.
  - **Authentication Errors**: Returns `401 Unauthorized` if the user is not authenticated.

---

## Examples

### Uploading a Scanned Document

**Request**:

```http
POST /documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": <passport_scan.pdf>,
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  }
}
```

**Service Actions**:

- Validates the file type and size.
- Stores the file in Azure Blob Storage.
- Saves metadata and associates the document with the user.
- Emits a `DocumentUploaded` event.

**Response**:

```json
{
  "documentId": "doc-1011",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "documentType": "uploaded",
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

### Retrieving an Uploaded Document's Metadata

**Request**:

```http
GET /documents/doc-1011
Authorization: Bearer <token>
```

**Response**:

```json
{
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "uploaded",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

## Integration with Other Services

- **Verification Processes**: Uploaded documents can be used by other services for identity verification or compliance checks.

- **Notifications**: The `DocumentUploaded` event can trigger notifications or workflows in other systems.

---
````
