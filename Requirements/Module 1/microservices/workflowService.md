# Workflow Service Design

## Overview

The **Workflow Service** is responsible for instantiating and executing workflows based on templates defined in the **Workflow Engine Microservice**. It acts as the runtime environment for workflows, managing their state, progression, and interactions with other microservices. The service is triggered by requests from the **Request Microservice**, where the request type determines which workflow template to use.

---

## Purpose

- **Workflow Instantiation**: Creates workflow instances based on templates from the Workflow Engine.
- **Workflow Execution**: Manages the progression of workflows through their defined steps, handling actions, decisions, and approvals.
- **Integration Management**: Communicates with other microservices (e.g., Notification Service, Document Generation Service) to perform specific tasks within workflows.
- **State Management**: Tracks the state of each workflow instance, including current step, history, and outcomes.
- **Scalability**: Designed to handle multiple concurrent workflow executions efficiently.

---

## Responsibilities

1. **Workflow Initialization**:
   - Retrieves the appropriate workflow template from the Workflow Engine based on the request type received from the Request Microservice.
   - Validates the workflow template and initializes workflow instances.

2. **Workflow Execution and Orchestration**:
   - Manages the execution flow according to the workflow definition.
   - Handles task assignments, approvals, condition evaluations, and transitions between nodes.
   - Supports synchronous and asynchronous execution of workflow steps.

3. **Interaction with Other Microservices**:
   - **Request Microservice**: Receives triggers to start workflows and updates request statuses.
   - **Workflow Engine**: Retrieves workflow templates and updates on changes.
   - **Notification Service**: Sends notifications to users based on workflow events.
   - **Document Generation Service**: Initiates document creation when required by the workflow.
   - **User Data Service**: Fetches user and role information for approvals and assignments.

4. **State and Persistence**:
   - Persists workflow instances, including state, variables, and history.
   - Ensures data consistency and reliability through transactions and error handling.

5. **Monitoring and Logging**:
   - Logs workflow events and actions for auditing and debugging.
   - Provides monitoring capabilities for active and completed workflows.

---

## High-Level Architecture

### Components

1. **Workflow Executor**:
   - Core component that interprets and executes workflow definitions.
   - Manages state transitions, action executions, and decision logic.

2. **Workflow Repository**:
   - Stores workflow instances and their states.
   - Provides persistence and retrieval functionalities.

3. **Integration Layer**:
   - Handles communication with other microservices.
   - Encapsulates APIs for Notification Service, Document Generation Service, etc.

4. **Event Handler**:
   - Listens to events from other services (e.g., user actions, external triggers).
   - Processes events that affect workflow execution (e.g., approval responses).

5. **API Layer**:
   - Exposes endpoints for querying workflow instances, statuses, and histories.
   - Provides administrative functions for managing workflows.

---

## Entities

### 1. WorkflowInstance

Represents an active or completed workflow.

**Fields**:

- `id` (UUID): Unique identifier of the workflow instance.
- `templateId` (UUID): ID of the workflow template used.
- `requestId` (UUID): ID of the associated request from the Request Microservice.
- `currentNodeId` (string): ID of the current node in the workflow.
- `status` (enum): `InProgress`, `Completed`, `Terminated`, `Suspended`.
- `variables` (JSON): Runtime variables and data.
- `history` (array of WorkflowHistoryEntry): Records of executed steps.
- `startedAt` (timestamp): When the workflow started.
- `completedAt` (timestamp, optional): When the workflow completed.

### 2. WorkflowHistoryEntry

Represents a record of an executed step in the workflow.

**Fields**:

- `timestamp` (timestamp): When the step was executed.
- `nodeId` (string): ID of the node.
- `action` (string): Action performed.
- `performedBy` (UUID, optional): User who performed the action.
- `details` (JSON): Additional information.

### 3. TaskAssignment

Represents a task assigned to a user or group.

**Fields**:

- `id` (UUID): Unique identifier.
- `workflowInstanceId` (UUID): Associated workflow instance.
- `nodeId` (string): ID of the node requiring action.
- `assignedTo` (UUID): User or role ID.
- `status` (enum): `Pending`, `Completed`, `Delegated`.
- `dueDate` (timestamp, optional): When the task is due.

---

## API Endpoints

### 1. **Start Workflow**

- **Endpoint**: `POST /workflows/start`
- **Description**: Initiates a workflow based on a request.
- **Request Body**:

  ```json
  {
    "requestId": "request-uuid",
    "requestType": "Overtime",
    "initiatedBy": "user-uuid",
    "inputData": {
      "overtimeHours": 2,
      "date": "2023-11-10",
      "reason": "Project deadline"
    }
  }
  ```

- **Process**:
  - Fetches the workflow template associated with the `requestType` from the Workflow Engine.
  - Creates a new `WorkflowInstance`.
  - Starts execution at the `Start Node`.

- **Response**:

  ```json
  {
    "workflowInstanceId": "workflow-instance-uuid",
    "status": "InProgress",
    "startedAt": "2023-10-15T12:00:00Z"
  }
  ```

### 2. **Get Workflow Instance**

- **Endpoint**: `GET /workflows/{id}`
- **Description**: Retrieves details of a workflow instance.
- **Response**:

  ```json
  {
    "id": "workflow-instance-uuid",
    "templateId": "workflow-template-uuid",
    "requestId": "request-uuid",
    "currentNodeId": "approval",
    "status": "InProgress",
    "variables": { /* ... */ },
    "history": [ /* ... */ ],
    "startedAt": "2023-10-15T12:00:00Z",
    "completedAt": null
  }
  ```

### 3. **Complete Task**

- **Endpoint**: `POST /workflows/{instanceId}/tasks/{taskId}/complete`
- **Description**: Completes a task assigned to a user.
- **Request Body**:

  ```json
  {
    "action": "approve", // or "reject"
    "comments": "Approved due to project urgency"
  }
  ```

- **Process**:
  - Validates that the user is assigned to the task.
  - Updates the workflow state based on the action.
  - Proceeds to the next node(s) as per the workflow definition.

- **Response**:

  ```json
  {
    "status": "TaskCompleted",
    "nextNodeId": "generateDocument"
  }
  ```

### 4. **List Active Workflows**

- **Endpoint**: `GET /workflows`
- **Description**: Retrieves a list of active workflow instances.
- **Query Parameters** (optional):

  - `status`: Filter by status (`InProgress`, `Completed`, etc.).
  - `requestId`: Filter by associated request ID.
  - `initiatedBy`: Filter by user ID.

- **Response**:

  ```json
  [
    {
      "id": "workflow-instance-uuid-1",
      "requestId": "request-uuid-1",
      "status": "InProgress",
      "currentNodeId": "approval",
      "startedAt": "2023-10-15T12:00:00Z"
    },
    {
      "id": "workflow-instance-uuid-2",
      "requestId": "request-uuid-2",
      "status": "InProgress",
      "currentNodeId": "managerApproval",
      "startedAt": "2023-10-16T09:30:00Z"
    }
  ]
  ```

---

## Interactions with Other Microservices

### 1. **Request Microservice**

- **Trigger Workflow Start**: When a request is created, the Request Microservice sends a message or API call to the Workflow Service to start the associated workflow.
- **Status Updates**: Workflow Service updates the status of the request as it progresses (e.g., Pending, Approved, Rejected).

### 2. **Workflow Engine Microservice**

- **Template Retrieval**: Fetches workflow templates based on `requestType` or specific identifiers.
- **Template Updates**: Listens for updates or changes in workflow templates.

### 3. **Notification Service**

- **Send Notifications**: Triggers notifications at specific points in the workflow (e.g., task assignments, approvals, completions).
- **Channels and Recipients**: The Workflow Service provides the content, recipients, and channels as per the workflow definition.

### 4. **Document Generation Service**

- **Generate Documents**: Calls the Document Generation Service when a workflow requires document creation.
- **Data Provision**: Supplies necessary data and template IDs for document generation.

### 5. **User Data Service**

- **User and Role Information**: Retrieves user details, roles, and organizational hierarchy for approvals and task assignments.
- **Availability Checks**: Determines if an approver is available or needs delegation.

---

## Use Cases

### **Use Case 1: Overtime Approval Workflow**

**Scenario**: An operator submits an overtime request that requires manager approval and results in an authorization document if approved.

**Steps**:

1. **Request Submission**:
   - Operator submits an overtime request via the Request Microservice.
   - Request Microservice triggers the Workflow Service to start the workflow.

2. **Workflow Initialization**:
   - Workflow Service retrieves the "Overtime Approval Workflow" template from the Workflow Engine.
   - Creates a `WorkflowInstance` with the initial variables.

3. **Manager Approval Task**:
   - Assigns an approval task to the operator's manager.
   - Sends a notification via the Notification Service.

4. **Manager Action**:
   - Manager approves the task through the Workflow Service API.
   - Workflow transitions to the next node.

5. **Document Generation**:
   - Workflow Service calls the Document Generation Service to create the overtime authorization document.
   - Stores the document reference in the workflow variables.

6. **Completion**:
   - Sends notifications to the operator and manager.
   - Updates the request status to "Approved".

### **Use Case 2: Leave Request with Conditional HR Approval**

**Scenario**: An employee submits a leave request that requires manager approval, and if the leave exceeds five days, HR approval is also required.

**Steps**:

1. **Request Submission**:
   - Employee submits a leave request via the Request Microservice.
   - Request Microservice triggers the Workflow Service.

2. **Workflow Initialization**:
   - Retrieves the "Leave Request Workflow" template.
   - Calculates the number of leave days.

3. **Manager Approval**:
   - Assigns approval task to the manager.
   - On approval, checks if leave days > 5.

4. **Conditional HR Approval**:
   - If condition met, assigns task to HR.
   - Else, proceeds to completion.

5. **Completion**:
   - Sends notifications accordingly.
   - Updates request status.

---

## Additional Considerations

### **Scalability and Performance**

- **Asynchronous Processing**: Utilize message queues for handling tasks and external service calls.
- **Load Balancing**: Deploy multiple instances with load balancing to handle high traffic.
- **Caching**: Cache workflow templates to reduce retrieval latency.

### **Error Handling and Recovery**

- **Transaction Management**: Use transactions to ensure data consistency.
- **Retries and Compensation**: Implement retry mechanisms and compensation logic for failed steps.
- **Logging and Monitoring**: Detailed logs for auditing and troubleshooting.

### **Security**

- **Authentication and Authorization**:
  - Secure APIs with JWT tokens or OAuth2.
  - Ensure users can only act on workflows they have permissions for.
- **Data Protection**:
  - Encrypt sensitive data at rest and in transit.
  - Follow best practices for securing communication between microservices.

### **Extensibility**

- **Plugin Architecture**: Allow adding new node types or actions without major changes.
- **Configuration Management**: Enable dynamic configuration of workflow behaviors.

### **Compliance and Auditing**

- **Audit Trails**: Maintain detailed history of workflow actions.
- **Compliance**: Ensure workflows comply with organizational policies and regulations.

---

## Conclusion

The **Workflow Service** acts as the operational engine that brings workflow templates to life, managing their execution and integration with other system components. By dynamically instantiating workflows based on request types and templates from the Workflow Engine, it provides a flexible and scalable solution for automating business processes across the organization.
