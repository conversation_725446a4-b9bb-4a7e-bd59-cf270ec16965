# Hierarchical Partition Key Implementation for Azure Cosmos DB

This document describes the implementation of hierarchical partition keys for multi-tenant data separation in the Document Template Service using Azure Cosmos DB.

## Overview

The hierarchical partition key strategy provides excellent data isolation and query performance for multi-tenant applications by using a structured approach: `country/tenantId/category`.

## Architecture

### Partition Key Structure

```
{country}/{tenantId}/{category}
```

**Example:** `US/aptiv-us-manufacturing/HR`

### Benefits

1. **Data Isolation**: Templates are automatically isolated by country and tenant
2. **Regulatory Compliance**: Country-level separation supports data sovereignty requirements
3. **Performance**: Single-partition queries are extremely fast
4. **Scalability**: Natural distribution across physical partitions

## Implementation Details

### 1. Entity Structure

The `Template` entity now includes hierarchical fields:

```typescript
export class Template extends BaseEntity {
  // Existing fields...
  category: TemplateCategory;
  
  // NEW: Hierarchical fields
  country: string;        // ISO 3166-1 alpha-2 country code
  region?: string;        // Optional region identifier
  tenantId: string;       // Tenant identifier
  
  // Partition key is automatically generated as: country/tenantId/category
}
```

### 2. Partition Key Utility

The `PartitionKeyUtils` class manages partition key operations:

```typescript
// Create partition key
const partitionKey = PartitionKeyUtils.createPartitionKey('US', 'aptiv-manufacturing', TemplateCategory.HR);
// Result: "US/aptiv-manufacturing/HR"

// Parse partition key
const parsed = PartitionKeyUtils.parsePartitionKey('US/aptiv-manufacturing/HR');
// Result: { country: 'US', tenantId: 'aptiv-manufacturing', category: 'HR' }
```

### 3. Cosmos DB Configuration

Updated container configuration for optimal indexing:

```typescript
{
  id: 'templates',
  partitionKey: { 
    paths: ['/partitionKey'], 
    kind: PartitionKeyKind.Hash 
  },
  indexingPolicy: {
    includedPaths: [
      { path: '/country/?', indexes: [{ kind: 'Range', dataType: 'String' }] },
      { path: '/tenantId/?', indexes: [{ kind: 'Range', dataType: 'String' }] },
      { path: '/category/?', indexes: [{ kind: 'Range', dataType: 'String' }] },
      { path: '/region/?', indexes: [{ kind: 'Range', dataType: 'String' }] },
      // ... other indexed fields
    ],
    excludedPaths: [
      { path: '/templateContent/*' }, // Exclude large content from indexing
    ],
  }
}
```

## Query Patterns

### 1. Single Partition Query (Most Efficient)

Query templates within a specific country/tenant/category:

```typescript
// TypeScript
const templates = await templateRepository.findByHierarchicalKey(
  'US', 
  'aptiv-manufacturing', 
  TemplateCategory.HR,
  paginationOptions
);
```

**Performance**: Fastest possible query - targets single partition

### 2. Tenant-Level Query

Query all templates for a tenant across categories:

```typescript
const templates = await templateRepository.findByCountryAndTenant(
  'US', 
  'aptiv-manufacturing',
  paginationOptions
);
```

**Performance**: Fast - queries specific tenant partitions only

### 3. Cross-Partition Query (Use Sparingly)

Query templates by ID when partition context is unknown:

```typescript
const template = await templateRepository.findByIdAcrossCategories(templateId);
```

**Performance**: Slower - requires cross-partition query

### 4. Advanced Filtering

Search with hierarchical and content filters:

```typescript
const templates = await templateRepository.searchTemplatesHierarchical({
  country: 'US',
  tenantId: 'aptiv-manufacturing',
  category: TemplateCategory.HR,
  region: 'NA-EAST',
  search: 'absence policy'
}, paginationOptions);
```

## API Usage

### Creating Templates

```json
POST /templates
{
  "name": "Employee Handbook Template",
  "description": "Standard employee handbook template",
  "category": "HR",
  "country": "US",
  "tenantId": "aptiv-us-manufacturing",
  "region": "NA-EAST",
  "templateContent": "<html>...</html>",
  "fieldIds": ["field-1", "field-2"],
  "templateVersion": "1.0",
  "createdBy": "user-123"
}
```

### Querying Templates

```http
GET /templates?country=US&tenantId=aptiv-us-manufacturing&category=HR&page=1&size=10
```

**Query Parameters:**
- `country`: Filter by country code
- `tenantId`: Filter by tenant identifier
- `region`: Filter by region (optional)
- `category`: Filter by template category
- `createdBy`: Filter by creator
- `search`: Full-text search in name/description

## Performance Considerations

### Best Practices

1. **Always include hierarchical context** when possible
2. **Use single-partition queries** for best performance
3. **Avoid cross-partition queries** in high-traffic scenarios
4. **Index only necessary fields** to optimize storage and performance

### Query Performance Hierarchy

1. **Single Partition** (`country/tenantId/category`) - ~1-5ms
2. **Tenant Level** (`country/tenantId`) - ~5-20ms
3. **Country Level** (`country`) - ~20-100ms
4. **Cross-Partition** (no context) - ~100-500ms

### Scaling Recommendations

1. **Partition Distribution**: Ensure even distribution across tenants
2. **Hot Partitions**: Monitor for uneven access patterns
3. **Request Units**: Scale based on query patterns and throughput requirements

## Security & Compliance

### Data Isolation

- **Country-Level**: Supports data sovereignty requirements
- **Tenant-Level**: Complete isolation between tenants
- **Category-Level**: Fine-grained access control within tenants

### Access Patterns

```typescript
// Example: Restrict queries to user's country/tenant
const userContext = getUserContext(request);
const templates = await templateRepository.findByCountryAndTenant(
  userContext.country,
  userContext.tenantId,
  paginationOptions
);
```

## Monitoring & Analytics

### Key Metrics

1. **Partition Distribution**: Monitor RU consumption across partitions
2. **Query Performance**: Track latency by query type
3. **Storage Usage**: Monitor storage per country/tenant
4. **Hot Partitions**: Identify and address uneven access patterns

### Dashboard Queries

```typescript
// Get tenant statistics
const stats = await templateRepository.getTemplateStatsByCountryAndTenant('US', 'aptiv-manufacturing');
// Returns: { totalTemplates, templatesByCategory, templatesCreatedLastWeek }
```

## Migration Guide

### From Single Partition to Hierarchical

1. **Update Entity**: Add country, tenantId, region fields
2. **Update DTOs**: Include new fields in create/update operations
3. **Update Queries**: Use hierarchical query methods
4. **Data Migration**: Populate existing records with appropriate values
5. **Container Recreation**: May require container recreation for partition key changes

### Example Migration Script

```typescript
// Pseudo-code for data migration
async function migrateToHierarchicalPartitioning() {
  const templates = await getExistingTemplates();
  
  for (const template of templates) {
    const updatedTemplate = {
      ...template,
      country: determineCountryFromUser(template.createdBy),
      tenantId: determineTenantFromUser(template.createdBy),
      region: determineRegionFromUser(template.createdBy),
    };
    
    // Create in new container with hierarchical partition key
    await createTemplateWithHierarchicalKey(updatedTemplate);
  }
}
```

## Troubleshooting

### Common Issues

1. **Partition Key Mismatch**: Ensure consistent country/tenantId formatting
2. **Cross-Partition Queries**: Review query patterns for performance
3. **Hot Partitions**: Distribute load across tenants/countries
4. **Index Optimization**: Exclude large fields from indexing

### Debug Queries

```typescript
// Check partition key parsing
try {
  const parsed = PartitionKeyUtils.parsePartitionKey(partitionKey);
  console.log('Parsed:', parsed);
} catch (error) {
  console.error('Invalid partition key format:', error.message);
}

// Validate country/tenant format
const isValidCountry = PartitionKeyUtils.validateCountryCode('US');
const isValidTenant = PartitionKeyUtils.validateTenantId('aptiv-manufacturing');
```

## Future Enhancements

1. **Automatic Geo-Distribution**: Route queries to regional Cosmos DB instances
2. **Advanced Analytics**: Cross-tenant reporting with proper aggregation
3. **Compliance Automation**: Automatic data residency enforcement
4. **Performance Optimization**: Dynamic partition key optimization based on usage patterns

## Related Documentation

- [Azure Cosmos DB Partitioning Best Practices](https://docs.microsoft.com/en-us/azure/cosmos-db/partitioning-overview)
- [Multi-Tenant SaaS Patterns](https://docs.microsoft.com/en-us/azure/architecture/patterns/)
- [Template Service API Documentation](./api-documentation.md)
