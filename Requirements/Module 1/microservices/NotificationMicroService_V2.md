# Low-Level Design: Multi-Channel Notification Microservice

## 1. Overview

The Notification Microservice is designed to handle multi-channel notifications through a unified interface. It processes events from other services and manages notification delivery across different channels while maintaining scalability and reliability.

## 2. Core Components & Rationale

### 2.1 Communication Channels

1.  **Azure Communication Services (Email & SMS)**

    - **Why**: Provides reliable, scalable infrastructure for sending emails and SMS
    - **Benefits**:
      - Built-in delivery tracking
      - Compliance with global communication regulations
      - High deliverability rates
      - Cost-effective for high volumes

2.  **Azure Notification Hubs (Push Notifications)**

    - **Why**: Manages device registration and cross-platform push delivery
    - **Benefits**:
      - Native support for iOS, Android, and web push
      - Handles device token management
      - Supports tag-based targeting
      - Automatic handling of platform-specific requirements

3.  **Web App Notifications**

    - **Why**: Provides real-time updates within the web application
    - **Benefits**:

      - Immediate user feedback
      - No external service dependencies
      - Can be persisted for offline viewing
      - Low cost as it uses existing web infrastructure

    For web notifications, there are several approaches we can implement:

    1. **Server-Sent Events (SSE)**:

       - One-way connection from server to client
       - Lightweight and simple to implement
       - Built-in reconnection handling
       - Better for scenarios where you only need server-to-client communication

    2. **WebSocket**:

       - Full-duplex communication
       - More complex but more powerful
       - Good for real-time bidirectional communication
       - Higher overhead than SSE

    3. **Azure SignalR Service**

       - Managed service that handles scaling
       - Works well with Azure infrastructure
       - Supports multiple protocols (WebSocket, SSE, Long Polling)
       - Automatic fallback handling
       - Built-in authentication integration

    **The flow would be:**

         1. Service receives notification event from Service Bus
         2. Stores notification in Cosmos DB
         3. Pushes to SignalR Hub
         4. SignalR broadcasts to connected clients
         5. Client receives and displays notification

    This approach gives you:

        - Real-time notifications through SignalR
        - Historical notification access through REST API
        - Offline support with notification persistence
        - Scalability through Azure managed services
        - Fallback mechanisms for different browsers/scenarios

### 2.2 Event Processing

**Azure Service Bus**

- **Why**: Reliable event processing and service decoupling
- **Use Cases**:
  - Processing workflow events
  - Handling notification requests
  - Managing notification scheduling
- **Benefits**:
  - Message persistence
  - Dead-letter queue support
  - FIFO message handling
  - Support for message sessions

### 2.3 Data Storage

**Cosmos DB**

- **Why**: Flexible schema for different notification types and high scalability
- **Benefits**:
  - Multi-region support
  - Schema flexibility for different notification types
  - Built-in TTL for notification expiry
  - Quick queries with indexed attributes

## 3. System Architecture

```mermaid
graph TD
    subgraph External Services
        WF[Workflow Service] --> ASB
        US[User Service] --> NS
    end

    subgraph Message Processing
        ASB[Azure Service Bus] --> NS[Notification Service]
        NS --> TE[Template Engine]
    end

    subgraph Data Storage
        NS --> CDB[(Cosmos DB)]
    end

    subgraph Notification Channels
        NS --> ACS[Azure Communication Services]
        NS --> ANH[Azure Notification Hubs]
        NS --> WN[Web Notifications]

        ACS --> Email[Email]
        ACS --> SMS[SMS]
        ANH --> Push[Push Notifications]
        WN --> Web[Web App]
    end
```

## 4. Service Integration Flow

```mermaid
sequenceDiagram
    participant WS as Workflow Service
    participant SB as Azure Service Bus
    participant NS as Notification Service
    participant US as User Service
    participant NC as Notification Channels
    participant DB as Cosmos DB

    WS->>SB: Publish Notification Event
    Note right of WS: Event includes notification type and target criteria

    SB->>NS: Process Notification Event

    NS->>US: Get User Information
    Note right of NS: Query users by ID or Role
    US-->>NS: Return User Details

    NS->>DB: Store Notification Record

    NS->>NC: Dispatch to Appropriate Channel(s)
    Note right of NS: Based on user preferences and notification type

    NC-->>NS: Delivery Status
    NS->>DB: Update Notification Status
```

## 5. Data Models

### 5.1 Cosmos DB Container Designs

```typescript
// Notification Document
interface NotificationDocument {
  id: string;
  type: "EMAIL" | "SMS" | "PUSH" | "WEB";
  status: "PENDING" | "PROCESSING" | "DELIVERED" | "FAILED";
  content: {
    templateId: string;
    variables: Record<string, any>;
    renderedContent?: string;
  };
  target: {
    userIds?: string[];
    roles?: string[];
    all?: boolean;
  };

  channels: {
    email?: {
      subject: string;
      template: string;
    };
    sms?: {
      template: string;
    };
    push?: {
      title: string;
      body: string;
      data?: Record<string, any>;
    };
    web?: {
      title: string;
      body: string;
      action?: string;
    };
  };
  tracking: {
    createdAt: Date;
    updatedAt: Date;
    deliveredAt?: Date;
    attempts: number;
    errors?: string[];
  };
}
```

### 5.2 Event Schema

```typescript
// Notification Event
interface NotificationEvent {
  eventType: "SEND_NOTIFICATION";
  payload: {
    templateId: string;
    variables: Record<string, any>;
    target: {
      userIds?: string[];
      roles?: string[];
      all?: boolean;
    };
    channels: string[];
    priority: "HIGH" | "MEDIUM" | "LOW";
    scheduling?: {
      sendAt: Date;
      recurrence?: string;
    };
  };
}
```

## 7. Failure Handling & Retry Strategy

```mermaid
stateDiagram-v2
    [*] --> PENDING
    PENDING --> PROCESSING
    PROCESSING --> DELIVERED
    PROCESSING --> RETRY
    RETRY --> PROCESSING
    RETRY --> FAILED
    FAILED --> [*]
    DELIVERED --> [*]

    note right of RETRY
        Exponential backoff:
        1st retry: 30s
        2nd retry: 5m
        3rd retry: 30m
        Then to DLQ
    end note
```

## 8. Performance Considerations

1. **Cosmos DB Partitioning Strategy**

   - Partition key: Composite of userId + date for even distribution
   - TTL based on notification type and importance
   - Indexed fields for common queries

2. **Service Bus Message Sessions**

   - Group related notifications for ordered processing
   - Maintain user notification sequence
   - Handle bulk notifications efficiently
