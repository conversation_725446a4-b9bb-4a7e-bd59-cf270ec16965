# Overview

This microservice allows users to dynamically create, read, update, and delete forms and their fields. Forms and fields metadata will be stored in Cosmos DB using an embedded data model for simplicity and performance.

## What is this microservice?

This is a specialized service that helps create and manage dynamic forms in your application. Think of it like a powerful form builder that lets users create custom forms without needing to write any code. For example, an HR manager could create an employee onboarding form with fields for personal information, department selection, and role assignments - all through a user-friendly interface.

## Why is it needed?

Traditional forms are often hardcoded, making them difficult to modify without developer intervention. This microservice solves that problem by:

- Allowing non-technical users to create and modify forms on their own
- Supporting dynamic data population (like pulling in lists of departments or employees)
- Managing relationships between form fields (like showing relevant employees after selecting a department)
- Storing all form definitions in a flexible database that can evolve over time

## Who will use it?

This service is designed for:

- Business users who need to create or modify forms
- Developers integrating form functionality into applications
- Systems that need to collect structured data in a flexible way

### Key Architectural Goals

1. **Dynamic Form Modeling**: Allow storage and retrieval of form definitions where each form can have a varying number of fields of different types, properties, and configurations.
2. **Flexible & Embedded Data Model**: Store forms as single, versioned documents containing all fields within the same JSON document, simplifying read operations and ensuring that the form definition is self-contained.
3. **Event-Driven Integrations Using Cosmos Change Feed**: When a form is created, updated, or deleted, use Cosmos DB's change feed to trigger downstream processes (e.g., caching updates, indexing, analytics, or notification services).
4. **Microservices & Domain-Driven Structure**: Implement a dedicated `FormsService` microservice using NestJS. Adhere to clear layering (Controller, Service, Repository) and well-defined interfaces.
5. **Versioning Support (Optional)**: Consider including version fields in the form documents if version history is required.
6. **Scalability & Partitioning**: Leverage Cosmos DB's partitioning scheme to scale reads and writes efficiently. Likely partition by `formId` or a suitable grouping key.

### Data Source Configuration for Dynamic Forms

#### Overview

When building forms, non-technical users need an intuitive way to create forms with fields that can be populated with data and have dependencies between them. This section describes how this is implemented.

#### Available Data Sources

The system provides a predefined list of data sources that users can select from when building forms. These are configured by developers but presented to users in a business-friendly way. For example:

- Company Data
  - Department List
  - Employee List
  - Office Locations

#### Data Storage in Cosmos DB

- **Containers**: Create separate containers in Cosmos DB for each data set you want to use in your forms. For example:

  - `Departments`
  - `Users`
  - `Locations`
  - etc.

- **Data Format**: Store each entry in these containers in a consistent format, such as:
  ```json
  {
    "id": "unique_id",
    "name": "display_name"
  }
  ```

#### Form Builder Enhancements

- **Data Source Selection**: In the form builder, provide a user-friendly interface where users can:

  - Drag and drop a "Select Box" field onto the form.
  - Choose to populate it with data from the database.
  - Select the desired data source (e.g., Departments, Users) from a dropdown list.

- **Configuration**: The form builder should save the configuration in a way that references the selected data source. For example:
  ```json
  {
    "label": "Select Department",
    "name": "department",
    "type": "select",
    "populateFrom": {
      "source": "Departments"
    }
  }
  ```

#### Backend Logic

- **Data Fetching**: Implement backend logic to fetch data from the appropriate Cosmos DB container based on the `populateFrom.source` field in the form configuration.

  - Use a generic service that can query any container and return data in the `{name: "xxx", id: "xx"}` format.

- **Caching and Optimization**: Consider caching frequently accessed data to reduce database load and improve performance.

#### Frontend Implementation

- **Dynamic Data Population**: When rendering the form:

  - Fetch the data from the backend based on the `populateFrom.source`.
  - Populate the select box with the fetched data.

- **Automatic Updates**: Ensure that any changes in the data (e.g., new departments added) are automatically reflected in the forms without requiring changes to the frontend or backend logic.

#### Example Workflow

```mermaid
sequenceDiagram
    participant User
    participant FormBuilder
    participant Backend
    participant CosmosDB

    User->>FormBuilder: Drag "Select Box" to form
    FormBuilder->>User: Choose data source (e.g., Departments)
    User->>FormBuilder: Select "Departments"
    FormBuilder->>Backend: Save form configuration

    Note over Backend: Form is used in workflow

    Backend->>CosmosDB: Fetch data from "Departments" container
    CosmosDB->>Backend: Return data in {name, id} format
    Backend->>FormBuilder: Populate select box with data
    FormBuilder->>User: Display form with populated select box
```

#### Benefits of This Approach

1. Non-technical users can easily configure complex data relationships.
2. All technical implementation details are hidden.
3. Clear visual feedback for dependencies.
4. Consistent user experience.
5. Maintainable and extensible for developers.

### Proposed Entities & Data Model

**1. Top-Level Entities:**

- **Form**: The root entity representing a complete form configuration.  
  Example:

  ```json
  {
    "id": "form_12345",
    "name": "UserRegistrationForm",
    "version": 1,
    "description": "A form for user sign-up",
    "metadata": {
      "category": "onboarding"
    },
    "fields": [
      {
        "checked": false,
        "description": "Select department",
        "disabled": false,
        "label": "Department",
        "name": "department",
        "placeholder": "Choose department",
        "required": true,
        "rowIndex": 0,
        "type": "select",
        "value": "",
        "variant": "Select",
        "className": "form-select",
        "populateFrom": {
          "source": "departments",
          "displayField": "name",
          "valueField": "id"
        }
      },
      {
        "checked": false,
        "description": "Select user from department",
        "disabled": true,
        "label": "User",
        "name": "user",
        "placeholder": "Select user",
        "required": true,
        "rowIndex": 1,
        "type": "select",
        "value": "",
        "variant": "Select",
        "className": "form-select",
        "populateFrom": {
          "source": "users",
          "displayField": "fullName",
          "valueField": "id",
          "dependsOn": {
            "field": "department",
            "filterParam": "departmentId"
          }
        }
      }
    ],
    "createdAt": "2024-12-06T10:00:00Z",
    "updatedAt": "2024-12-06T10:00:00Z"
  }
  ```

### Dynamic Form Population and Dependencies

```mermaid
graph TD
    subgraph Form Builder
        A[Drag Select Field] --> B[Configure Field]
        B --> C{Populate from Data?}
        C -->|Yes| D[Choose Data Source]
        D --> E{Depends on Another Field?}
        E -->|Yes| F[Select Parent Field]
        E -->|No| G[Complete Configuration]
        F --> G
    end

    subgraph Runtime Flow
        H[Load Form] --> I[Initialize Fields]
        I --> J[Populate Independent Fields]
        J --> K{Has Dependent Fields?}
        K -->|Yes| L[Disable Dependent Fields]
        L --> M[Wait for Parent Selection]
        M --> N[Filter & Populate Dependent Field]
        N --> O[Enable Field]
        K -->|No| P[Form Ready]
        O --> P
    end
```

### Field Population Process

```mermaid
sequenceDiagram
    participant FB as Form Builder
    participant FE as Frontend
    participant BE as Backend
    participant DB as Database

    FB->>FE: Create form with populated fields
    FE->>BE: Load form configuration
    BE->>FE: Return form structure

    Note over FE: Initialize form

    FE->>BE: Request independent field data
    BE->>DB: Query data (e.g., departments)
    DB->>BE: Return data
    BE->>FE: Populate independent fields

    Note over FE: User selects department

    FE->>BE: Request dependent data with filter
    BE->>DB: Query filtered data (e.g., users by department)
    DB->>BE: Return filtered data
    BE->>FE: Update dependent field options
```

---

### Cosmos DB Data Model

- **Container**: `Forms`
- **Partition Key**: `id` (or possibly `name` if you anticipate many forms and want to isolate them by an attribute that's likely to be queried frequently).
- **Schema**: No fixed schema; forms stored as JSON documents. Fields are embedded arrays.

**Indexing**:

- Use the default indexing for JSON. Fine-tune indexing policies based on query patterns (e.g., indexing on `name`, `metadata.category` if needed).

**Change Feed**:

- Enable the change feed on the `Forms` container.
- A separate processor (e.g., an Azure Function or a background NestJS service) can subscribe to the change feed and react to any inserts/updates/deletions.
- Potential use cases:
  - Update a cache or search index whenever a form changes.
  - Trigger notifications or analytics pipelines when forms are created or updated.

---

### NestJS Application Structure

**Directory Layout:**

```
src/
  modules/
    forms/
      forms.module.ts
      forms.controller.ts
      forms.service.ts
      forms.repository.ts
      dtos/
        create-form.dto.ts
        update-form.dto.ts
      entities/
        form.entity.ts
        field.entity.ts
    common/
      cosmos/
        cosmos.module.ts
        cosmos.service.ts
  main.ts
```

**Key Components:**

- **FormsModule**: Encapsulates all logic related to form management.
- **FormsController**: Defines REST endpoints:

  - `POST /forms` - Create a new form.
  - `GET /forms/:id` - Retrieve a specific form by ID.
  - `GET /forms` - List/search forms based on filters.
  - `PUT /forms/:id` - Update a form's metadata or fields.
  - `DELETE /forms/:id` - Delete a form.

- **FormsService**: Business logic layer. Orchestrates validation, interactions with the repository, and any domain rules (e.g., version increments).
- **FormsRepository**: Direct interaction with Cosmos DB. Uses the official `@azure/cosmos` SDK to perform CRUD operations:

  - `create(form: Form)`
  - `findById(id: string): Promise<Form | null>`
  - `update(form: Form)`
  - `delete(id: string)`

- **DTOs (Data Transfer Objects)**: Define the shape of the data expected by the controller. For example:

  - `CreateFormDto`: Specifies required and optional fields for creating a form.
  - `UpdateFormDto`: Specifies which fields can be updated.

- **Entities**:

  - `FormEntity`: TypeScript interface/class that represents the structure of a form as stored in Cosmos.
  - `FieldEntity`: Interface/class representing individual fields.

- **Cosmos Module**: A small Nest module that configures and provides a Cosmos DB client connection. Injected into `FormsRepository` for database operations.

**Code Snippet for Repository (Pseudo)**:

```typescript
@Injectable()
export class FormsRepository {
  constructor(private readonly cosmosClient: CosmosClient) {}

  private get container() {
    return this.cosmosClient.database("your-database-name").container("Forms");
  }

  async create(form: FormEntity): Promise<void> {
    await this.container.items.create(form);
  }

  async findById(id: string): Promise<FormEntity | null> {
    const { resource } = await this.container.item(id, id).read<FormEntity>();
    return resource || null;
  }

  async update(form: FormEntity): Promise<void> {
    await this.container.item(form.id, form.id).replace(form);
  }

  async delete(id: string): Promise<void> {
    await this.container.item(id, id).delete();
  }
}
```

---

### Change Feed Integration

- Deploy an Azure Function or a background NestJS service (using `@azure/cosmos` ChangeFeedProcessor) that listens to changes in the `Forms` container.
- On any change (insert, update, delete), the function:
  - Reads the updated documents.
  - Executes any domain-specific logic (e.g., refreshing a front-end cache, updating a secondary index, pushing notifications to a messaging queue).

**Example Use Case**: When a new form is created, the change feed processor sends a message to a message queue. Another microservice can listen to that queue and build a search index for forms.

---

### Documentation and DevOps Notes

- **API Documentation**: Use `@nestjs/swagger` to auto-generate OpenAPI specs for the Forms API. Document endpoints, request/response bodies, and error codes.
- **Local Development**:
  - Use the Azure Cosmos DB Emulator or a local Cosmos DB-compatible API for testing.
  - Integration tests can run against a test container.
- **CI/CD**:
  - Linting, unit tests, and integration tests before merging code.
  - Terraform or ARM templates to provision the Cosmos DB database and containers.
  - Azure DevOps Pipelines or GitHub Actions for build & deploy.
- **Configuration**:
  - Store Cosmos endpoint and keys in environment variables or use Azure Key Vault for secrets management.
- **Monitoring & Logging**:
  - Utilize NestJS Logger for request/response logging.
  - Integrate with Azure Application Insights for distributed tracing and telemetry.
  - Monitor change feed consumers with Cosmos DB diagnostic logs.

### Database Considerations - SQL vs NoSQL

While initially designed for Cosmos DB, the strong relationships between data sources (like departments and employees) suggest that a SQL database might be more appropriate. Key reasons:

1. **Structured Relationships**: Clear parent-child relationships between entities (e.g., Departments → Employees)
2. **Data Integrity**: Foreign key constraints ensure referential integrity
3. **Join Operations**: Efficient querying of related data
4. **Transaction Support**: ACID compliance for complex operations

#### Proposed Database Schema

```mermaid
erDiagram
    Department ||--o{ Employee : has
    Department {
        int id PK
        string name
        string code
        datetime created_at
        datetime updated_at
    }
    Employee {
        int id PK
        int department_id FK
        string full_name
        string email
        datetime created_at
        datetime updated_at
    }
    Form ||--o{ FormField : contains
    Form {
        int id PK
        string name
        string description
        datetime created_at
        datetime updated_at
    }
    FormField {
        int id PK
        int form_id FK
        string name
        string type
        string label
        json configuration
        int parent_field_id FK
        datetime created_at
        datetime updated_at
    }
```

### Enhanced Form Builder Flow with Nested Dependencies

```mermaid
graph TD
    A[Start Form Design] --> B[Add Field]
    B --> C{Field Type?}
    C -->|Select| D[Configure Data Source]
    D --> E[Choose Primary Entity]
    E --> F{Add Dependent Field?}
    F -->|Yes| G[Configure Child Field]
    G --> H[Select Related Entity]
    H --> I[Define Filter Conditions]
    I --> F
    F -->|No| J[Complete Field Setup]

    subgraph "Example Configuration"
        K[Department Select]
        L[Employee Select filtered by Department]
        M[Team Select filtered by Department]
        K --> L
        K --> M
    end
```

### Unified Data Fetching Architecture

```mermaid
sequenceDiagram
    participant Frontend
    participant DataController
    participant DataService
    participant Database

    Frontend->>DataController: GET /api/data?entity=departments
    DataController->>DataService: fetchData("departments")
    DataService->>Database: SELECT * FROM departments

    Frontend->>DataController: GET /api/data?entity=employees&departmentId=123
    DataController->>DataService: fetchData("employees", {departmentId: 123})
    DataService->>Database: SELECT * FROM employees WHERE department_id = 123

    Note over DataController: Single controller handles all data fetching
    Note over DataService: Applies filters based on parameters
```

### Form Builder Interface for Nested Fields

```mermaid
graph TD
    subgraph "Form Builder Interface"
        A[Field Configuration Panel]
        B[Data Source Selection]
        C[Dependency Configuration]

        A --> B
        B --> C

        subgraph "Dependency Setup"
            D[Select Parent Field]
            E[Choose Filter Type]
            F[Preview Related Data]

            D --> E
            E --> F
        end
    end
```

### Key Design Updates

1. **Unified Data Controller**:

   - Single endpoint: `/api/data`
   - Query parameters:
     - `entity`: The main entity to fetch (departments, employees, etc.)
     - `filters`: JSON object containing filter conditions
     - `relations`: Optional related data to include

2. **Enhanced Field Configuration**:

   - Support for nested dependencies
   - Multiple dependent fields from same parent
   - Cascade delete/disable options
   - Dynamic filter conditions

3. **Form Builder Improvements**:

   - Visual dependency mapper
   - Preview of filtered data
   - Drag-and-drop relationship creation
   - Validation rules for circular dependencies

4. **Data Flow Optimization**:
   - Cached queries for frequently used data
   - Batch loading of related data
   - Real-time validation of relationships

These updates provide a more robust and user-friendly system for creating complex forms with interdependent fields while maintaining data integrity through proper database relationships.
