# **Workflow Service LLD**

# **Low-Level Design (LLD) Documentation**

## **Table of Contents**

1. [Introduction](#1-introduction)
2. [System Architecture](#2-system-architecture)
3. [Core Concepts and Components](#3-core-concepts-and-components)
    - [WorkflowEngine Instance vs. Workflow Instance](#31-workflowengine-instance-vs-workflow-instance)
    - [Workflow Runner](#32-workflow-runner)
    - [Node Executors](#33-node-executors)
    - [Event Bus](#34-event-bus)
    - [Data Team Data Models](#34-data-team-data-models)
        - [Employee Data](#341-employee-data)
        - [Organizational Hierarchy](#342-organizational-hierarchy)
        - [Team Structure](#343-team-structure)
        - [User Validation](#344-user-validation)
4. [Workflow Instance and Node Definitions](#4-workflow-instance-and-node-definitions)
5. [Detailed Workflow Execution Process](#5-detailed-workflow-execution-process)
6. [Node Executors](#6-node-executors)
7. [Event System](#7-event-system)
8. [Escalation System](#8-escalation-system)
9. [Node Executor Service](#9-node-executor-service)
10. [Service Bus Integration](#10-service-bus-integration)
11. [Data Storage](#11-data-storage)
12. [Monitoring and Logging](#12-monitoring-and-logging)

## **1. Introduction**

This is the **Low-Level Design (LLD)** of **Workflow Service**. The goal of this system is to orchestrate complex workflows spanning multiple steps, for example approvals, and external processes. The system is composed of several microservices, a storage layer, and an event bus mechanism to enable communication and scalability.

### **Objectives**

- **Standardize** approvals, notifications, and document generation across different business units.
- **Provide** a unified framework for workflow definition, execution, and tracking.
- **Enable** easy extensibility and integration with new node types or external services.

### **Key Features**

- **Workflow Runner** handles the lifecycle of each request, persisting data in a centralized database.
- **Workflow Engine** defines the blueprint (nodes, transitions, routing).
- **Node Executors** encapsulate node-specific logic (e.g., approvals, document generation).
- **Service Bus** enables asynchronous communication and decoupled integration with other services.

---

## **2. System Architecture**

Below is a high-level diagram depicting the major components and their interactions:

```mermaid
graph TB
    subgraph "Client"
        FE[Frontend]
        API[Request Service]
    end

    subgraph "Core Services"
        WR[Workflow Runner Service]
        WE[Workflow Engine]
        NE[Node Executors]
        EB[Service Bus]
    end

    subgraph "Storage Layer"
        CD[(CosmosDB)]
    end

    subgraph "Node Executors Services"
        SA[Single Approval Executor]
        PA[Parallel Approval Executor]
        DG[Document Generator]
        NT[Notification Handler]
    end

    FE -->|HTTP| API
    API -->|workflow.start/EVENT| WR
    WR -->|Get/Update State| CD
    WR -->|Get Node Config| WE
    WR -->|Execute Node| NE
    NE --> SA
    NE --> PA
    NE --> DG
    NE --> NT
    SA & PA & DG & NT -->|Publish Events| EB
    SA & PA & DG & NT -->|Update State| CD
```

### **Component Descriptions**

1. **Client**

   - **Frontend (FE)**: User-facing interface for initiating requests and monitoring workflow status.
   - **Request Service (API)**: Exposes endpoints for creating or manipulating workflow requests.

2. **Core Services**

   - **Workflow Runner (WR)**: Manages the end-to-end execution of workflows (create instances, manage nodes, update state).
   - **Workflow Engine (WE)**: Stores and provides workflow definitions (node configurations, transitions, conditional logic).
   - **Node Executors (NE)**: A generic interface or service aggregator that delegates actual execution to specialized executors.

3. **Storage Layer**

   - **CosmosDB (CD)**: NoSQL database holding workflow instances, histories, node states, etc.

4. **Node Executors Services**

   - **Single Approval Executor (SA)**
   - **Parallel Approval Executor (PA)**
   - **Document Generator (DG)**
   - **Notification Handler (NT)**

5. **Event Bus (EB)**
   - Facilitates asynchronous communication (e.g., "approval requested", "document generated").

---

## **3. Core Concepts and Components**

### **3.1 WorkflowEngine Instance vs. Workflow Instance**

- A **WorkflowEngine Instance** is a template/definition describing the sequence of nodes, transitions, and conditional logic.
- A **Workflow Instance** is a _running_ or _completed_ instantiation of that workflow, specific to a request.

### **3.2 Workflow Runner**

- Orchestrates node processing based on the definition provided by the Workflow Engine.
- Persists and updates the **Workflow Instance** in the database.
- Publishes/consumes events via the Event Bus (for approvals, notifications, escalations, etc.).

### **3.3 Node Executors**

- Specialized microservices or modules that handle the logic for different node types (e.g., Single Approval, Parallel Approval).
- Each executor implements an **`INodeExecutor`** interface (or similar) to ensure consistency.

### **3.4 Event Bus**

- Decouples services via asynchronous messaging.
- Workflow Runner sends events like **`workflow.approval.requested`**. External services (or Node Executors) can subscribe and react accordingly.

### **3.4 Data Team Data Models**

Similar to the crew management module, the workflow service needs access to employee and organizational hierarchy data to properly handle workflow assignments and approvals. The following data models are required for read operations:

#### **3.4.1 Employee Data**

```typescript
interface EmployeeData {
  id: string;                    // UUID
  legacy_site: string;          // Unique employee identifier for mapping
  workday_id?: string;          // Optional Workday identifier
  legacy_id: number;            // Employee matricule
  site: string;                 // Employee's site location
  department: string;           // Department name
  role: string;                 // Employee role (e.g., Manager, Team Leader)
  firstname: string;            // First name
  lastname: string;             // Last name
  email: string;               // Employee email
  business_title: string;      // Full job title
  category: string;            // Employee category (Salaried, Direct/Indirect Hourly)
}
```

#### **3.4.2 Organizational Hierarchy**

```typescript
interface OrganizationalHierarchy {
  id: string;
  legacy_site_id: string;
  fullname: string;
  department: string;
  site: string;
  role: string;
  manager_legacy_site_id: string;
  subordinate_crew: Array<{
    sub_legacy_site_id: string;
    sub_legacy_id: number;
    sub_fullname: string;
    sub_role: string;
    sub_role_status: string;
    sub_department: string;
    in_workday: boolean;
    category: string;
    contract_type: string;
  }>;
}
```

#### **3.4.3 Team Structure**

```typescript
interface TeamStructure {
  teamleader_legacy_site: string;
  teamleader_fullname: string;
  team: string;
  department: string;
  department_type: string;
  site: string;
  operators: Array<{
    legacy_site: string;
    legacy_id: number;
    first_name: string;
    last_name: string;
    role: string;
    department: string;
  }>;
}
```

#### **3.4.4 User Validation**

```typescript
interface UserValidation {
  id: string;
  legacy_site: string;
  legacy_id: number;
  first_name: string;
  last_name: string;
  subordinate_id: string[];
  category: string;
  department: string;
  role: string;
  site: string;
  in_workday: boolean;
}
```

These data models will be used to:
1. **Validate Workflow Participants**: Ensure users have appropriate roles and permissions
2. **Route Approvals**: Determine correct approval chains based on organizational hierarchy
3. **Assign Tasks**: Properly assign workflow tasks to the correct teams and individuals
4. **Access Control**: Manage who can view and interact with specific workflow instances
5. **Notifications**: Send notifications to the correct individuals based on their roles and relationships

The workflow service will maintain read-only access to these data models, which will be updated through the crew management module's event-driven architecture.

---

## **4. Workflow Instance and Node Definitions**

### **4.1 `WorkflowInstance`**

```typescript
interface WorkflowInstance extends BaseEntity {
  requestId: string;
  requestForId: string;
  workflowEngineId: string;
  status: WorkflowStatus;
  currentNodeId: string;
  initiatorId: string;
  initialFormData: Record<string, any>;
  nodeList: WorkflowNode[];
  edges: WorkflowEdge[];
}

type NodeConfig =
  | SingleApprovalNodeConfig
  | ParallelApprovalNodeConfig
  | DocumentGenerationNodeConfig
  | StartNodeConfig
  | EndNodeConfig
  | ConditionNodeConfig
  | UpdateUserStatusConfig;

class WorkflowNode {
  id: string;
  type: NodeType;
  runtimeConfig?: RuntimeNodeConfig;
  executionHistory: Array<{
    createdAt: Date;
    updatedAt: Date;
    completedAt: Date | null;
    status: NodeStatus;
    assignedTo: string[];
    decisions: Array<{
      userId: string;
      decision: string;
      comment?: string;
      timestamp: Date;
    }>;
  }>;
}

class WorkflowEdge {
  source: string;
  target: string;
  condition?: string;
}

enum WorkflowStatus {
  PENDING_APPROVAL = "PENDING_APPROVAL",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

enum NodeType {
  START = "START",
  END = "END",
  SINGLE_APPROVAL = "SINGLE_APPROVAL",
  PARALLEL_APPROVAL = "PARALLEL_APPROVAL",
  DOCUMENT_GENERATION = "DOCUMENT_GENERATION",
  CONDITION = "CONDITION",
  UPDATE_USER_STATUS = "UPDATE_USER_STATUS",
}

enum NodeStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}
```

**Key Fields**

- **`status`**: Tracks if the workflow instance is still in progress, finished, or encountered a failure.
- **`currentNodeId`**: The ID of the node being processed.
- **`nodeshistory`**: Array of node execution records for auditing.

#### **Sample**

```typescript
const exampleWorkflowInstance: WorkflowInstance = {
  id: "WF-001",
  requestId: "REQ-1234",
  workflowId: "Absence authorizations",
  status: "IN_PROGRESS",
  currentNodeId: "NODE-AP1",
  initiator: {
    id: "USER-001",
    email: "<EMAIL>",
    department: "",
  },
  formData: {
    initiatior: "teamLeader_id",
  },
  nodeshistory: [],
  createdAt: new Date(),
  updatedAt: new Date(),
};
```

---

## **5. Detailed Workflow Execution Process**

This section outlines **how** the workflow transitions from one node to another and **how** the system responds to events.

```mermaid
sequenceDiagram
    participant RS as RequestService
    participant SB as ServiceBus
    participant WS as WorkflowService
    participant DB as CosmosDB
    participant WE as WorkflowEngine

    RS->>SB: Publish workflow.start
    SB->>WS: Handle workflow.start
    WS->>DB: Create WorkflowInstance

    loop Node Processing
        WS->>WE: Get Node (currentNodeId)
        alt Is End Node
            WS->>DB: Finish Workflow
        else Is Approval Node
            WS->>DB: Update State (PENDING_APPROVAL)
            WS->>SB: Send Approval Notification
            WS-->>WS: Await User Input
        else Is Document Node
            WS->>SB: Request Document Generation
            WS->>DB: Update State (PROCESSING)
            WS-->>WS: Await Document
        end

        WS->>WE: Get Next Node
        WS->>DB: Update Current Node
    end
```

---

- - When a workflow instance hits a **Single Approval** or **Parallel Approval** node, it **stops** at that node.
  - The **workflow runner** will **not** continue to the next node until the approval node receives a decision.- **Decision Handling**

  - A **decision handler** method will be called (e.g., `handleDecision`) once the user (or automated logic) submits approval or rejection.
  - If **approved**, the workflow continues to the next node.
  - If **rejected** (and if configured to stop on rejection), the workflow either **terminates** or redirects to an alternate path (based on the node's configuration).

### **5.1 Step 1: Workflow Initialization**

```typescript
async function executeWorkflow(
  requestId: string,
  workflowId: string,
  data: any
): Promise<void> {
  // 1. Create workflow instance
  const instance = await createWorkflowInstance({
    requestId,
    workflowId,
    data,
    status: "IN_PROGRESS",
    currentNodeId: await getInitialNodeId(workflowId),
  });

  // 2. Start processing nodes
  await processNodes(instance);
}
```

**Purpose**

- **Create** a new `WorkflowInstance`.
- Retrieve the **initial node** from the Workflow Engine.
- **Kick off** the node processing mechanism.

### **5.2 Step 2: Node Processing (Recursive)**

```typescript
async function processNodes(instance: WorkflowInstance): Promise<void> {
  // 1. Get current node configuration
  const currentNode = await workflowEngine.getNode(
    instance.workflowId,
    instance.currentNodeId
  );

  // 2. Handle workflow completion
  if (!currentNode) {
    await completeWorkflow(instance.id);
    return;
  }

  // 3. Execute current node
  try {
    const nodeExecutor = getNodeExecutor(currentNode.type);
    await nodeExecutor.execute(currentNode, instance);

    // 4. For non-blocking nodes, determine and process next node
    if (!isBlockingNode(currentNode.type)) {
      const nextNodeId = await determineNextNode(currentNode, instance);
      if (nextNodeId) {
        await updateWorkflowInstance(instance.id, {
          currentNodeId: nextNodeId,
          status: "IN_PROGRESS",
        });
        // Recursive call to process the next node
        await processNodes({
          ...instance,
          currentNodeId: nextNodeId,
        });
      }
    }
  } catch (error) {
    await handleNodeError(instance, currentNode, error);
  }
}
```

**Purpose**

- **Retrieve** the current node definition.
- **Execute** node logic (Approval, Document Generation, etc.).
- For **non-blocking** nodes, **automatically** move to the next node.
- If an error occurs, handle it (logging, retry, or marking instance as failed).

### **5.3 Step 3: Next Node Determination**

```typescript
async function determineNextNode(
  currentNode: WorkflowNode,
  instance: WorkflowInstance
): Promise<string | null> {
  // 1. Check for conditional routing
  if (currentNode.nextNodes?.conditions) {
    for (const condition of currentNode.nextNodes.conditions) {
      const result = await evaluateCondition(
        condition.expression,
        instance.data
      );
      if (result) {
        return condition.nodeId;
      }
    }
  }

  // 2. Return default next node or null for workflow completion
  return currentNode.nextNodes?.default || null;
}
```

**Purpose**

- Evaluate **conditional logic** to determine if a specific path should be taken.
- If no conditions match, use a **default** path.
- Return `null` if there is **no subsequent node**, signaling workflow completion.

### **5.4 Step 4: Node Execution Results Handling**

```typescript
async function handleNodeExecution(
  instance: WorkflowInstance,
  node: WorkflowNode,
  result: any
): Promise<void> {
  // 1. Update node history
  await updateNodeHistory(instance.id, {
    nodeId: node.id,
    status: "COMPLETED",
    endTime: new Date(),
    result,
  });

  // 2. Update workflow data if needed
  if (result.data) {
    await updateWorkflowData(instance.id, result.data);
  }

  // 3. Handle node-specific post-processing
  switch (node.type) {
    case "SINGLE_APPROVAL":
    case "PARALLEL_APPROVAL":
      if (!result.approved && node.config.stopOnRejection) {
        await terminateWorkflow(instance.id);
        return;
      }
      break;
    // Additional node-specific logic...
  }

  // 4. Continue workflow processing if not blocked
  if (!isBlockingNode(node.type)) {
    const nextNodeId = await determineNextNode(node, instance);
    if (nextNodeId) {
      await continueWorkflow(instance.id, nextNodeId);
    }
  }
}
```

**Purpose**

- **Record** the outcome (approved, rejected, completed, etc.) in the node history.
- **Mutate** the workflow's shared data based on node results (e.g., capturing user input).
- For some node types, check if the workflow should be **terminated** on rejection or error.
- If allowed, **proceed** to the next node.

---

## **6. Node Executors**

Node Executors define the **logic** to run each node type. They typically implement an interface like:

```typescript
interface INodeExecutor {
  execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void>;
  // Additional methods for handling user decisions, if needed
}
```

### **6.1 Single Approval Node Executor**

```typescript
class SingleApprovalNodeExecutor implements INodeExecutor {
  async execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void> {
    const config = node.config as SingleApprovalConfig;

    // 1. Validate approver configuration
    if (!config.approver && !config.approverId) {
      throw new Error("No approver configured");
    }

    // 2. Publish notification event
    await this.eventBus.publish("workflow.approval.requested", {
      instanceId: instance.id,
      nodeId: node.id,
      approver: config.approverId || config.approver.id,
      timing: config.timing,
    });

    // 3. Update node state
    await updateNodeState(instance.id, node.id, {
      status: "PENDING",
      metadata: {
        approverId: config.approverId || config.approver.id,
      },
    });
  }

  async handleDecision(
    instance: WorkflowInstance,
    node: WorkflowNode,
    decision: boolean,
    comment?: string
  ): Promise<void> {
    const config = node.config as SingleApprovalConfig;

    if (!decision && config.onReject?.action === "STOP_FLOW") {
      await terminateWorkflow(instance.id);
      return;
    }

    await updateNodeState(instance.id, node.id, {
      status: "COMPLETED",
      result: { approved: decision, comment },
    });

    // Continue workflow execution
    await processNodes(instance);
  }
}
```

**Purpose**

- **Block** the workflow until the single assigned approver provides a decision.
- **Publish** an event to notify the approver.
- React to the approval/rejection decision, potentially **terminating** or continuing the workflow.

### **6.2 Parallel Approval Node Executor**

```typescript
class ParallelApprovalNodeExecutor implements INodeExecutor {
  async execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void> {
    const config = node.config as ParallelApprovalConfig;

    // 1. Initialize approval states
    const approverStates = config.approvers.map((approver) => ({
      approverId: approver.id,
      status: "PENDING",
    }));

    // 2. Publish notification events for all approvers
    await Promise.all(
      config.approvers.map((approver) =>
        this.eventBus.publish("workflow.approval.requested", {
          instanceId: instance.id,
          nodeId: node.id,
          approver: approver.id,
          isParallel: true,
        })
      )
    );

    // 3. Update node state
    await updateNodeState(instance.id, node.id, {
      status: "PENDING",
      metadata: {
        approverStates,
        requiredApprovals: config.approvers.length,
      },
    });
  }

  async handleDecision(
    instance: WorkflowInstance,
    node: WorkflowNode,
    decision: boolean,
    approverId: string
  ): Promise<void> {
    const state = await getNodeState(instance.id, node.id);
    const approverStates = state.metadata.approverStates.map((ast) =>
      ast.approverId === approverId
        ? { ...ast, status: "COMPLETED", decision }
        : ast
    );

    const allCompleted = approverStates.every(
      (ast) => ast.status === "COMPLETED"
    );

    if (allCompleted) {
      await updateNodeState(instance.id, node.id, {
        status: "COMPLETED",
        metadata: { approverStates },
      });
      await processNodes(instance);
    } else {
      await updateNodeState(instance.id, node.id, {
        metadata: { approverStates },
      });
    }
  }
}
```

**Purpose**

- **Simultaneously** request approvals from multiple parties.
- Blocks until **all** designated approvers provide input.
- Once all complete, the node is marked **"COMPLETED"**; the workflow proceeds.

### **6.3 Document Generation Node Executor**

```typescript
class DocumentGenerationNodeExecutor implements INodeExecutor {
  async execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void> {
    const config = node.config as DocumentGenerationConfig;

    try {
      // 1. Generate document
      const document = await generateDocument({
        templateId: config.templateId,
        format: config.outputFormat,
        data: {
          workflowId: instance.id,
          initiator: instance.initiator,
          timestamp: new Date(),
        },
      });

      // 2. Publish document generated event
      await this.eventBus.publish("workflow.document.generated", {
        instanceId: instance.id,
        nodeId: node.id,
        documentId: document.id,
      });

      // 3. Update node state
      await updateNodeState(instance.id, node.id, {
        status: "COMPLETED",
        result: {
          documentId: document.id,
          url: document.url,
          format: config.outputFormat,
        },
      });

      // 4. Continue workflow
      await processNodes(instance);
    } catch (error) {
      await updateNodeState(instance.id, node.id, {
        status: "COMPLETED",
        error: error.message,
      });
      throw error;
    }
  }
}
```

**Purpose**

- **Automates** document generation (e.g., PDF, Word) based on templates and runtime data.
- Publishes an event once the document is successfully created.
- Non-blocking once completed; moves to the next node immediately.


### **6.4 Update User Status Node Executor**

# Update User Status Node

## Purpose
Manages user status changes within workflows (e.g., leave status, availability, work status).

## Configuration Schema

```typescript
interface UpdateUserStatusConfig {
  mapping: {
    sourceNode: {
      nodeId: string;    // Reference node containing form data
      formId: string;    // Form containing date fields
    };
    fields: {
      from: string;      // Start date field
      to: string;        // End date field
    };
  };
  statusConfig: {
    type: UserStatusType;
    value: string;
  };
}

enum UserStatusType {
  LEAVE_STATUS = "LEAVE_STATUS",
  AVAILABILITY = "AVAILABILITY",
}
```

Execution  Flow

Input  Validation

Verify  source  form  data  exists

Validate  date  range  fields

Check  status  type  validity

Status Update

Update  user  status  in  system

Set  validity  period

Record  change  history

Event  Publishing

{
  event: "user.status.updated",
  payload: {
    instanceId: string;
    nodeId: string;
    userId: string;
    status: {
      type: UserStatusType;
      value: string;
      from: Date;
      to: Date;
    }
  }
}

Example Configuration
---------------------
```json
{
  "id": "update-leave-status",
  "type": "UPDATE_USER_STATUS",
  "config": {
    "mapping": {
      "sourceNode": {
        "nodeId": "leave-request-form",
        "formId": "LEAVE_REQUEST_001"
      },
      "fields": {
        "from": "leaveStartDate",
        "to": "leaveEndDate"
      }
    },
    "statusConfig": {
      "type": "LEAVE_STATUS",
      "value": "ON_LEAVE"
    }
  }
}

---

## **7. Event System**

The system **publishes** and **subscribes** to events through an **Event Bus**. This decoupling allows various services to react to workflow changes without hard-coded dependencies.

**Example of Events Interface:**

```typescript
interface WorkflowEvents {
  "workflow.start": { instanceId: string; initiator: any };
  "workflow.approval.requested": {
    instanceId: string;
    nodeId: string;
    approver: string;
  };
  "workflow.approval.completed": {
    instanceId: string;
    nodeId: string;
    decision: boolean;
  };
  "workflow.document.generated": {
    instanceId: string;
    nodeId: string;
    documentId: string;
  };
  "workflow.completed": { instanceId: string };
}
```

## **8. Escalation System**

The escalation system enables dynamic reassignment of approvals based on various triggers and maintains a complete audit trail.

```mermaid
sequenceDiagram
    participant TS as Time Service
    participant ES as Escalation Service
    participant WS as Workflow Service
    participant SB as Service Bus
    participant DB as CosmosDB

    TS->>ES: Trigger timeout escalation
    ES->>WS: Request escalation
    WS->>DB: Get current node state
    WS->>DB: Update escalation history
    WS->>SB: Publish escalation notification
    SB->>WS: New approver notification
```

### **8.1 Escalation Types**

```typescript
interface EscalationConfig {
  enabled: boolean;
  chain: Array<{
    approverId: string;
  }>;
}
```

### **8.2 Escalation History**

Each escalation is tracked in the node's execution history:

```typescript
interface EscalationHistory {
  timestamp: Date;
  escalatedFrom: string;
  escalatedTo: string;
  reason: string;
  initiatedBy?: string;
}
```

### **8.3 Escalation Process**

1. **Trigger Detection**

   - Timeout-based (automatic)
   - Manual escalation (user-initiated)
   - Condition-based (business rules)

2. **State Preservation**

   - Current approver's state is preserved
   - New approver is assigned
   - Escalation chain is tracked

3. **Notification**
   - Previous approver is notified of escalation
   - New approver receives assignment
   - Relevant stakeholders are informed

---

## **9. Node Executor Service**

### **9.1 Base Node Executor**

```typescript
interface INodeExecutor {
  execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void>;
  handleDecision?(decision: ApprovalDecisionDto): Promise<void>;
  handleEscalation?(escalation: EscalationDto): Promise<void>;
  handleTimeout?(node: WorkflowNode): Promise<void>;
}
```

### **9.2 Approval Node States**

```typescript
enum NodeStatus {
  NOT_STARTED = "NOT_STARTED",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  REJECTED = "REJECTED",
}
```

---

## **10. Service Bus Integration**

### **10.1 Event Types**

```typescript
enum WorkflowEventName {
  STARTED = "workflow.started",
  COMPLETED = "workflow.completed",
  APPROVAL_REQUESTED = "workflow.approval.requested",
  APPROVAL_COMPLETED = "workflow.approval.completed",
  ESCALATION_REQUESTED = "workflow.escalation.requested",
  ESCALATION_COMPLETED = "workflow.escalation.completed",
}
```

### **10.2 Event Handling**

- Events are published to Azure Service Bus topics
- Subscribers process events asynchronously
- Retry policies handle temporary failures
- Dead-letter queue manages unprocessable messages

---

## **11. Data Storage**

### **11.1 CosmosDB Collections**

```typescript
const COSMOS_CONTAINERS = [
  {
    id: "workflowservice",
    partitionKey: { paths: ["/workflowsid"] },
    indexingPolicy: {
      includedPaths: [
        { path: "/createdAt/?" },
        { path: "/status/?" },
        { path: "/currentNodeId/?" },
      ],
    },
  },
];
```

### **11.2 Data Models**

- Workflow instances
- Node execution history
- Escalation records
- Approval decisions

---

## **12. Monitoring and Logging**

### **12.1 Metrics**

- Workflow completion time
- Approval response time
- Escalation frequency
- Error rates

### **12.2 Logging**

```typescript
interface LogEntry {
  timestamp: Date;
  level: "INFO" | "WARN" | "ERROR";
  operation: string;
  correlationId: string;
  data: any;
  error?: Error;
}
```

### **13.2 Authorization**

- Node-level permissions
- Escalation permissions
- Approval permissions
