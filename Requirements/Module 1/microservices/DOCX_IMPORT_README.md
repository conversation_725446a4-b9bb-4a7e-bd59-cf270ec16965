# Import DOCX Template Service

This service provides functionality to import Microsoft Word (.docx) documents and convert them into editable HTML templates with automatic placeholder detection.

## Features

- **DOCX to HTML Conversion**: Uses Mammoth.js to convert DOCX files to clean HTML
- **Automatic Placeholder Detection**: Detects various placeholder formats in the document
- **Placeholder Standardization**: Converts all placeholders to `{{fieldName}}` format
- **File Validation**: Validates file format and MIME type
- **Error Handling**: Comprehensive error handling with detailed logging

## API Endpoint

### Import DOCX Template

**Endpoint**: `POST /templates/import-docx`

**Description**: Uploads a .docx file, extracts its content, and converts it to HTML with detected placeholders.

**Headers**:
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**Request Body**:
- `file` (required): The .docx file to be imported

**Example using cURL**:
```bash
curl -X POST \
  http://localhost:3000/templates/import-docx \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -F 'file=@/path/to/your/template.docx'
```

**Example using JavaScript/Fetch**:
```javascript
const formData = new FormData();
formData.append('file', docxFile);

const response = await fetch('/templates/import-docx', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  },
  body: formData
});

const result = await response.json();
```

## Response Format

**Success Response (200 OK)**:
```json
{
  "htmlContent": "<p>Dear {{firstName}} {{lastName}},</p><p>Your absence request from {{startDate}} to {{endDate}} has been {{status}}.</p><p>Reason: {{reason}}</p>",
  "detectedPlaceholders": [
    "firstName",
    "lastName", 
    "startDate",
    "endDate",
    "status",
    "reason"
  ]
}
```

**Error Responses**:

- **400 Bad Request**: No file uploaded or invalid file
```json
{
  "statusCode": 400,
  "message": "No file uploaded"
}
```

- **415 Unsupported Media Type**: File is not a valid .docx file
```json
{
  "statusCode": 415,
  "message": "Uploaded file is not a valid .docx file"
}
```

- **500 Internal Server Error**: File processing failed
```json
{
  "statusCode": 500,
  "message": "Failed to process DOCX file. Please ensure the file is not corrupted and try again."
}
```

## Supported Placeholder Formats

The service automatically detects and converts various placeholder formats:

| Input Format | Converted To | Example |
|-------------|-------------|---------|
| `{{placeholder}}` | `{{placeholder}}` | `{{firstName}}` |
| `{placeholder}` | `{{placeholder}}` | `{firstName}` → `{{firstName}}` |
| `[placeholder]` | `{{placeholder}}` | `[firstName]` → `{{firstName}}` |
| `%placeholder%` | `{{placeholder}}` | `%firstName%` → `{{firstName}}` |
| `${placeholder}` | `{{placeholder}}` | `${firstName}` → `{{firstName}}` |

## Workflow Integration

After importing a DOCX template, you can:

1. **Review the converted HTML**: Check the `htmlContent` in the response
2. **Verify detected placeholders**: Ensure all expected fields are in `detectedPlaceholders`
3. **Create a new template**: Use the HTML content to create a new template via `POST /templates`
4. **Map placeholders to fields**: Associate detected placeholders with actual field IDs

### Example Workflow

```javascript
// 1. Import DOCX
const importResponse = await fetch('/templates/import-docx', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer TOKEN' },
  body: formData
});
const { htmlContent, detectedPlaceholders } = await importResponse.json();

// 2. Get available fields
const fieldsResponse = await fetch('/templates/fields', {
  headers: { 'Authorization': 'Bearer TOKEN' }
});
const availableFields = await fieldsResponse.json();

// 3. Map placeholders to field IDs
const fieldMapping = mapPlaceholdersToFields(detectedPlaceholders, availableFields);

// 4. Create template
const templateResponse = await fetch('/templates', {
  method: 'POST',
  headers: { 
    'Authorization': 'Bearer TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: "Imported Template",
    description: "Template imported from DOCX",
    category: "HR",
    templateContent: htmlContent,
    fieldIds: fieldMapping.fieldIds
  })
});
```

## File Requirements

- **Format**: Microsoft Word Document (.docx)
- **Size**: No specific limit (depends on server configuration)
- **Content**: Should contain text with placeholder markers
- **Structure**: Standard Word document structure

## Best Practices

1. **Use consistent placeholder naming**: Use descriptive names like `firstName`, `startDate`, etc.
2. **Test with sample documents**: Always test with a sample document first
3. **Review converted HTML**: Check the HTML output for formatting issues
4. **Verify placeholders**: Ensure all expected placeholders are detected
5. **Handle errors gracefully**: Implement proper error handling in your frontend

## Technical Details

### Dependencies
- **Mammoth.js**: For DOCX to HTML conversion
- **Multer**: For file upload handling
- **NestJS**: Framework integration

### Logging
The service provides comprehensive logging for:
- File upload events
- Conversion process
- Placeholder detection
- Error conditions

### Security
- File type validation based on MIME type and extension
- JWT authentication required
- Input sanitization and validation

## Troubleshooting

### Common Issues

1. **"Uploaded file is not a valid .docx file"**
   - Ensure the file has a .docx extension
   - Check that the file is not corrupted
   - Verify the file was created in a modern version of Microsoft Word

2. **"Failed to process DOCX file"**
   - The DOCX file may be corrupted
   - The file may contain unsupported features
   - Try saving the document in a simpler format

3. **Missing placeholders in detection**
   - Ensure placeholders follow supported formats
   - Check for extra spaces or special characters
   - Verify placeholder names contain only letters, numbers, and underscores

4. **HTML formatting issues**
   - Complex Word formatting may not convert perfectly
   - Consider simplifying the document formatting
   - Review the HTML output and adjust as needed
