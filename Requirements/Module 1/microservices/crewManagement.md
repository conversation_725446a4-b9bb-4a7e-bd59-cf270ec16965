# Crew Management Service Design

## Overview

The **Crew Management Service** implements a CQRS (Command Query Responsibility Segregation) pattern within the Connected Workers ecosystem, designed to handle crew organization, member assignments, and workforce changes at Aptiv. It integrates with Workday through event-driven architecture while maintaining high cohesion and loose coupling.

## Purpose & Business Value

- **Workforce Organization**: Streamlines team structure management and member assignments
- **Data Consistency**: Ensures synchronization with Workday through event-driven updates
- **Process Automation**: Automates crew-related workflows and approvals
- **Organizational Visibility**: Provides clear visibility of factory workforce structure through organizational charts

## Architecture Patterns

### CQRS Implementation

```mermaid
graph TD
    subgraph Commands
        CC[Command Controller]
        CH[Command Handlers]
        WM[Write Model]

        CC -->|Dispatch| CH
        CH -->|Update| WM
        WM -->|Change Feed| RM
    end

    subgraph Queries
        QC[Query Controller]
        QH[Query Handlers]
        RM[Read Model]

        QC -->|Dispatch| QH
        QH -->|Read| RM
    end

    subgraph Event Bus
        CF[Cosmos DB Change Feed]
        ASB[Azure Service Bus]

        WM -->|Trigger| CF
        CF -->|Publish| ASB
        ASB -->|Subscribe| RM
    end
```

### System Integration

```mermaid
graph TD
    subgraph External Systems
        WD[Workday] -->|Events| ESB[Enterprise Service Bus]
        OP[OPTITIME] -->|Excel Import| CMS
    end

    subgraph Connected Workers Platform
        CMS[Crew Management Service]
        WFS[Workflow Service]
        UDS[User Data Service]
        RQS[Request Service]
        NTS[Notification Service]
        VMS[Versatility Matrix Service]

        CMS -->|Commands| WM[Write Model]
        CMS -->|Queries| RM[Read Model]

        WM -->|Change Feed| CF[Cosmos DB Change Feed]
        CF -->|Events| ASB[Azure Service Bus]

        CMS -->|gRPC| UDS
        CMS -->|gRPC| WFS
        CMS -->|gRPC| RQS
        CMS -->|gRPC| VMS

        ASB -->|Events| NTS
    end

    ESB -->|Employee Updates| ASB
```

## Core Components

### 1. Write Model (Commands)

#### Command Handlers

- CreateCrewHandler
- ArchiveCrewHandler
- AssignMemberHandler
- UpdateRoleHandler
- StartTrainingHandler
- CompleteTrainingHandler

#### Write Model Schema (Cosmos DB)

```json
{
  "id": "UUID",
  "type": "Crew|Member|Training",
  "version": "number",
  "data": {
    // Domain-specific data
  },
  "metadata": {
    "createdAt": "timestamp",
    "createdBy": "UUID",
    "lastModifiedAt": "timestamp",
    "lastModifiedBy": "UUID"
  }
}
```

### 2. Read Model (Queries)

#### Projections

1. **CrewProjection**
   - Optimized for crew listing and details
   - Includes member counts and status
2. **MembershipProjection**
   - Denormalized view of crew memberships
   - Includes user details from User Service
3. **TrainingProjection**
   - Training status and progress
   - Qualification tracking

#### Read Model Updates

- Cosmos DB Change Feed triggers projection updates
- Event subscriptions maintain consistency
- Cached reference data for performance

## Core Features & Workflows

### 1. New Operator Onboarding Workflow

```mermaid
sequenceDiagram
    participant TKS as TKS Responsible
    participant CMS as Crew Management Service
    participant TR as Training Responsible
    participant T as Trainer
    participant SL as Shift Leader
    participant TL as Team Leader
    participant VMS as Versatility Matrix
    participant NTS as Notification Service

    Note over TKS: Manual Entry or OPTITIME Import
    TKS->>CMS: Create New Operator
    CMS->>NTS: Notify Training Responsible

    TR->>CMS: Assign to Trainer
    CMS->>NTS: Notify Trainer

    T->>CMS: Start OJT Training
    T->>VMS: Mark as Qualified ("O")
    CMS->>NTS: Notify Shift Leader

    SL->>CMS: Temporary Assignment
    Note over SL: Assign to fictitious Team Leader

    SL->>CMS: Final Team Leader Assignment
    CMS->>NTS: Notify Team Leader
```

### 2. Role Change Workflow

```mermaid
sequenceDiagram
    participant DS as Development Specialist
    participant CMS as Crew Management Service
    participant RQS as Request Service
    participant WFS as Workflow Service
    participant WD as Workday

    alt Permanent Change (Team Leader/Operator)
        DS->>RQS: Create Movement Notice
        RQS->>WFS: Start Approval Workflow
        WFS-->>CMS: Approved Change
        CMS->>CMS: Update Role
    else Temporary Change
        DS->>CMS: Update Temporary Role
        Note over CMS: N+2 Hierarchy Approval
    else Workday Sync (Plant Manager to Shift Leader)
        WD->>CMS: Role Update Event
        CMS->>CMS: Sync Role Change
    end
```

## Domain Model

### Entities

#### 1. Crew

```json
{
  "id": "UUID",
  "name": "string",
  "managerId": "UUID",
  "status": "Active | Archived",
  "workdayReference": "string",
  "hierarchyLevel": "PlantManager | ShiftLeader | TeamLeader | Operator",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "archivedAt": "timestamp?",
  "metadata": {
    "shift": "string?",
    "department": "string?",
    "costCenter": "string?",
    "location": "string?"
  }
}
```

#### 2. CrewMembership

```json
{
  "id": "UUID",
  "crewId": "UUID",
  "userId": "UUID",
  "workdayEmployeeId": "string",
  "role": "PlantManager | ShiftLeader | TeamLeader | Operator",
  "assignmentType": "Permanent | Temporary",
  "startDate": "timestamp",
  "endDate": "timestamp?",
  "status": "Active | Inactive | Training",
  "qualificationStatus": "Unqualified | InTraining | Qualified",
  "temporaryAssignment": {
    "reason": "string?",
    "originalCrewId": "UUID?",
    "plannedEndDate": "timestamp?",
    "approvedBy": "UUID?",
    "movementNoticeId": "string?"
  }
}
```

## Data Integration Patterns

### 1. External Data Sources

#### Workday Integration

```json
{
  "sourceSchema": {
    "employeeId": "string",
    "firstName": "string",
    "lastName": "string",
    "position": "string",
    "department": "string",
    "managerId": "string",
    "effectiveDate": "timestamp"
  },
  "transformationRules": {
    "mappings": [
      {
        "source": "position",
        "target": "role",
        "transformations": ["hierarchyLevelMapping"]
      },
      {
        "source": "department",
        "target": "metadata.department",
        "transformations": ["directMapping"]
      }
    ],
    "validations": [
      { "field": "position", "rule": "mustExistInHierarchy" },
      { "field": "employeeId", "rule": "uniqueConstraint" }
    ]
  }
}
```

#### OPTITIME Integration

```json
{
  "sourceSchema": {
    "operatorId": "string",
    "name": "string",
    "shift": "string",
    "teamLeader": "string",
    "qualifications": "string[]"
  },
  "transformationRules": {
    "mappings": [
      {
        "source": "shift",
        "target": "metadata.shift",
        "transformations": ["directMapping"]
      },
      {
        "source": "qualifications",
        "target": "qualificationStatus",
        "transformations": ["qualificationMapping"]
      }
    ]
  }
}
```

### 2. Materialized Views

#### Hierarchy View

```sql
CREATE MATERIALIZED VIEW crew_hierarchy_view AS
SELECT
    c.id as crew_id,
    c.name as crew_name,
    c.hierarchyLevel,
    m.userId,
    m.role,
    m.assignmentType,
    u.workdayEmployeeId,
    u.department
FROM crews c
JOIN crew_memberships m ON c.id = m.crewId
JOIN user_data u ON m.userId = u.id
WHERE c.status = 'Active'
WITH DATA;
```

#### Training Status View

```sql
CREATE MATERIALIZED VIEW training_status_view AS
SELECT
    m.userId,
    m.crewId,
    t.status as training_status,
    t.startDate,
    t.completionDate,
    t.trainerId
FROM crew_memberships m
LEFT JOIN training_records t ON m.userId = t.operatorId
WHERE m.status = 'Training'
WITH DATA;
```

### 3. Event Processors

```typescript
interface DataIntegrationEvent {
  source: "Workday" | "OPTITIME";
  eventType: "EmployeeUpdate" | "NewHire" | "OperatorImport";
  payload: Record<string, any>;
  metadata: {
    timestamp: Date;
    correlationId: string;
    version: string;
  };
}

class DataIntegrationProcessor {
  @EventHandler("Workday.EmployeeUpdate")
  async handleWorkdayUpdate(event: DataIntegrationEvent): Promise<void> {
    // Transform and validate data
    const transformedData = await this.transformService.transform(
      event.payload,
      "workday"
    );

    // Update write model
    await this.commandBus.execute(new UpdateEmployeeCommand(transformedData));

    // Trigger projections update
    await this.projectionManager.rebuildProjection("crew_hierarchy_view");
  }

  @EventHandler("OPTITIME.OperatorImport")
  async handleOptitimeImport(event: DataIntegrationEvent): Promise<void> {
    // Transform OPTITIME data
    const transformedData = await this.transformService.transform(
      event.payload,
      "optitime"
    );

    // Create new operators
    await this.commandBus.execute(new CreateOperatorsCommand(transformedData));

    // Update training status
    await this.projectionManager.rebuildProjection("training_status_view");
  }
}
```

## Workflow Orchestration Patterns

### 1. New Operator Onboarding Saga

```typescript
@Saga()
class OperatorOnboardingSaga {
  @StartSaga()
  @SagaEventHandler(NewOperatorCreatedEvent)
  async handle(event: NewOperatorCreatedEvent): Promise<void> {
    // 1. Notify Training Responsible
    await this.commandBus.execute(
      new NotifyTrainingResponsibleCommand({
        operatorId: event.operatorId,
        notificationType: "NEW_OPERATOR_ASSIGNMENT",
      })
    );

    // 2. Create Training Assignment
    await this.commandBus.execute(
      new CreateTrainingAssignmentCommand({
        operatorId: event.operatorId,
        status: "PENDING_TRAINER",
      })
    );
  }

  @SagaEventHandler(TrainerAssignedEvent)
  async handleTrainerAssigned(event: TrainerAssignedEvent): Promise<void> {
    // 3. Notify Trainer
    await this.commandBus.execute(
      new NotifyTrainerCommand({
        trainerId: event.trainerId,
        operatorId: event.operatorId,
      })
    );
  }

  @SagaEventHandler(TrainingCompletedEvent)
  async handleTrainingCompleted(event: TrainingCompletedEvent): Promise<void> {
    // 4. Update Versatility Matrix
    await this.commandBus.execute(
      new UpdateVersatilityMatrixCommand({
        operatorId: event.operatorId,
        qualification: "O",
      })
    );

    // 5. Notify Shift Leader
    await this.commandBus.execute(
      new NotifyShiftLeaderCommand({
        operatorId: event.operatorId,
        notificationType: "TRAINING_COMPLETED",
      })
    );
  }

  @EndSaga()
  @SagaEventHandler(OperatorFinalAssignmentEvent)
  async handleFinalAssignment(
    event: OperatorFinalAssignmentEvent
  ): Promise<void> {
    // 6. Notify Team Leader
    await this.commandBus.execute(
      new NotifyTeamLeaderCommand({
        teamLeaderId: event.teamLeaderId,
        operatorId: event.operatorId,
      })
    );
  }
}
```

### 2. Role Change Orchestration

```typescript
@Saga()
class RoleChangeSaga {
  @StartSaga()
  @SagaEventHandler(RoleChangeRequestedEvent)
  async handle(event: RoleChangeRequestedEvent): Promise<void> {
    if (event.type === "TEMPORARY") {
      await this.handleTemporaryChange(event);
    } else {
      await this.handlePermanentChange(event);
    }
  }

  private async handleTemporaryChange(
    event: RoleChangeRequestedEvent
  ): Promise<void> {
    // 1. Validate N+2 Hierarchy
    const isValid = await this.hierarchyService.validateN2Approval(
      event.requesterId,
      event.targetUserId
    );

    if (isValid) {
      // 2. Apply Role Change
      await this.commandBus.execute(
        new UpdateTemporaryRoleCommand({
          userId: event.targetUserId,
          newRole: event.newRole,
          duration: event.duration,
        })
      );
    }
  }

  private async handlePermanentChange(
    event: RoleChangeRequestedEvent
  ): Promise<void> {
    // 1. Create Movement Notice
    await this.commandBus.execute(
      new CreateMovementNoticeCommand({
        userId: event.targetUserId,
        currentRole: event.currentRole,
        newRole: event.newRole,
        reason: event.reason,
      })
    );

    // 2. Start Approval Workflow
    await this.workflowService.startWorkflow("MOVEMENT_NOTICE_APPROVAL", {
      noticeId: event.noticeId,
    });
  }

  @SagaEventHandler(MovementNoticeApprovedEvent)
  async handleMovementNoticeApproved(
    event: MovementNoticeApprovedEvent
  ): Promise<void> {
    // 3. Apply Permanent Role Change
    await this.commandBus.execute(
      new UpdatePermanentRoleCommand({
        userId: event.targetUserId,
        newRole: event.newRole,
      })
    );

    // 4. Notify Stakeholders
    await this.commandBus.execute(
      new NotifyRoleChangeCommand({
        userId: event.targetUserId,
        oldRole: event.oldRole,
        newRole: event.newRole,
      })
    );
  }
}
```

### 3. Workday Sync Orchestration

```typescript
@Saga()
class WorkdaySyncSaga {
  @StartSaga()
  @SagaEventHandler(WorkdayEmployeeUpdateEvent)
  async handle(event: WorkdayEmployeeUpdateEvent): Promise<void> {
    // 1. Transform Workday Data
    const transformedData = await this.transformService.transform(
      event.payload,
      "workday"
    );

    // 2. Validate Hierarchy Changes
    if (transformedData.hierarchyChanges) {
      await this.validateHierarchyChanges(transformedData.hierarchyChanges);
    }

    // 3. Apply Updates
    await this.commandBus.execute(
      new SyncWorkdayDataCommand({
        employeeId: event.employeeId,
        updates: transformedData,
      })
    );

    // 4. Rebuild Affected Views
    await this.projectionManager.rebuildProjection("crew_hierarchy_view");
  }

  @SagaEventHandler(WorkdayEmployeeTerminatedEvent)
  async handleTermination(
    event: WorkdayEmployeeTerminatedEvent
  ): Promise<void> {
    // 1. Archive Employee Data
    await this.commandBus.execute(
      new ArchiveEmployeeCommand({
        employeeId: event.employeeId,
        terminationDate: event.terminationDate,
      })
    );

    // 2. Update Crew Assignments
    await this.commandBus.execute(
      new RemoveFromCrewCommand({
        employeeId: event.employeeId,
        reason: "WORKDAY_TERMINATION",
      })
    );
  }
}
```

## API Contracts

### 1. Command API (Write Operations)

#### Crew Management

```http
POST /api/v1/commands/crews/create
Content-Type: application/json

{
  "commandId": "UUID",
  "name": "string",
  "managerId": "UUID",
  "metadata": {
    "shift": "string?",
    "department": "string?"
  }
}
```

```http
POST /api/v1/commands/crews/archive
Content-Type: application/json

{
  "commandId": "UUID",
  "crewId": "UUID",
  "reason": "string",
  "effectiveDate": "timestamp"
}
```

#### Member Management

```http
POST /api/v1/commands/members/assign
Content-Type: application/json

{
  "commandId": "UUID",
  "crewId": "UUID",
  "userId": "UUID",
  "role": "PlantManager | ShiftLeader | TeamLeader | Operator",
  "assignmentType": "Permanent | Temporary",
  "temporaryDetails": {
    "reason": "string?",
    "plannedEndDate": "timestamp?"
  }
}
```

#### Training Management

```http
POST /api/v1/commands/training/start
Content-Type: application/json

{
  "commandId": "UUID",
  "operatorId": "UUID",
  "tksResponsibleId": "UUID",
  "startDate": "timestamp"
}
```

### 2. Query API (Read Operations)

#### Crew Queries

```http
GET /api/v1/queries/crews?status=Active&department=Assembly
Accept: application/json

Response:
{
  "crews": [
    {
      "id": "UUID",
      "name": "string",
      "manager": {
        "id": "UUID",
        "name": "string",
        "role": "string"
      },
      "memberCount": "number",
      "metadata": {
        "shift": "string",
        "department": "string"
      }
    }
  ],
  "pagination": {
    "nextToken": "string?",
    "totalCount": "number"
  }
}
```

#### Member Queries

```http
GET /api/v1/queries/crews/{crewId}/members
Accept: application/json

Response:
{
  "members": [
    {
      "userId": "UUID",
      "name": "string",
      "role": "string",
      "assignmentType": "string",
      "assignedDate": "timestamp",
      "qualificationStatus": "string",
      "temporaryAssignment": {
        "endDate": "timestamp?",
        "reason": "string?"
      }
    }
  ]
}
```

## Mass Update Operations

### 1. Bulk Import Handlers

```typescript
@Injectable()
class BulkOperationHandler {
  @Transactional()
  async handleMassUpdate(
    operation: BulkOperation
  ): Promise<BulkOperationResult> {
    const session = await this.cosmos.startTransaction();

    try {
      // 1. Validate input data
      await this.validateBulkOperation(operation);

      // 2. Process updates in batches
      const results = await this.processBatches(operation.data, session);

      // 3. Update materialized views
      await this.refreshViews(operation.affectedViews);

      await session.commitTransaction();
      return results;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    }
  }
}

interface BulkOperation {
  type: "ORGANIZATIONAL_STRUCTURE" | "WORKDAY_SYNC" | "OPTITIME_IMPORT";
  source: "CLIENT_SCRIPT" | "WORKDAY" | "OPTITIME";
  data: Array<Record<string, any>>;
  validation: {
    rules: ValidationRule[];
    stopOnError: boolean;
  };
  affectedViews: string[];
}
```

### 2. Factory Structure Update

```typescript
@Injectable()
class FactoryStructureService {
  async updateOrganizationalStructure(
    structure: OrganizationalStructure
  ): Promise<void> {
    // 1. Validate hierarchy integrity
    await this.validateHierarchy(structure);

    // 2. Create bulk operation
    const operation: BulkOperation = {
      type: "ORGANIZATIONAL_STRUCTURE",
      source: "CLIENT_SCRIPT",
      data: this.transformToOperations(structure),
      validation: {
        rules: ["HIERARCHY_INTEGRITY", "MANAGER_ASSIGNMENT", "UNIQUE_CREWS"],
        stopOnError: true,
      },
      affectedViews: ["crew_hierarchy_view"],
    };

    // 3. Execute bulk update
    await this.bulkOperationHandler.handleMassUpdate(operation);

    // 4. Publish structure update event
    await this.eventBus.publish(new StructureUpdatedEvent(structure));
  }

  private async validateHierarchy(
    structure: OrganizationalStructure
  ): Promise<void> {
    // Validate Plant Manager to Shift Leader structure
    const isValid = await this.hierarchyValidator.validate(structure, {
      requiredLevels: [
        "PLANT_MANAGER",
        "SHIFT_LEADER",
        "TEAM_LEADER",
        "OPERATOR",
      ],
      validateManagerAssignment: true,
    });

    if (!isValid) {
      throw new InvalidHierarchyError("Invalid organizational structure");
    }
  }
}
```

### 3. OPTITIME Import Handler

```typescript
@Injectable()
class OptitimeImportService {
  async importOperators(file: Buffer): Promise<ImportResult> {
    // 1. Parse Excel file
    const operators = await this.excelParser.parse(file);

    // 2. Transform to bulk operation
    const operation: BulkOperation = {
      type: "OPTITIME_IMPORT",
      source: "OPTITIME",
      data: operators,
      validation: {
        rules: ["OPERATOR_UNIQUENESS", "REQUIRED_FIELDS", "VALID_SHIFT"],
        stopOnError: false,
      },
      affectedViews: ["training_status_view"],
    };

    // 3. Execute import
    const result = await this.bulkOperationHandler.handleMassUpdate(operation);

    // 4. Notify TKS Responsible
    await this.notificationService.notifyBulkImport(result);

    return result;
  }
}

interface ImportResult {
  totalRecords: number;
  successful: number;
  failed: number;
  errors: ImportError[];
  warnings: ImportWarning[];
}
```

### 4. Batch Processing

```typescript
class BatchProcessor {
  private readonly BATCH_SIZE = 100;

  async processBatches<T>(
    data: T[],
    session: DatabaseSession
  ): Promise<ProcessingResult> {
    const batches = this.createBatches(data);
    const results: BatchResult[] = [];

    for (const batch of batches) {
      try {
        const result = await this.processBatch(batch, session);
        results.push(result);
      } catch (error) {
        if (error instanceof FatalError) {
          throw error;
        }
        results.push({
          status: "ERROR",
          errors: [error],
        });
      }
    }

    return this.aggregateResults(results);
  }

  private createBatches<T>(data: T[]): T[][] {
    return chunk(data, this.BATCH_SIZE);
  }
}
```

## Search and Filtering

### 1. Search Service

```typescript
@Injectable()
class CrewSearchService {
  constructor(
    private readonly cosmosDb: CosmosClient,
    private readonly searchIndex: SearchIndexClient
  ) {}

  async search(criteria: SearchCriteria): Promise<SearchResult> {
    const searchQuery = this.buildSearchQuery(criteria);

    // 1. Search in Azure Search index
    const indexResults = await this.searchIndex.search(searchQuery);

    // 2. Fetch full documents from Cosmos DB
    const documents = await this.fetchDocuments(indexResults.documentIds);

    // 3. Apply security filters
    const filteredResults = await this.applySecurityFilters(
      documents,
      criteria.userId
    );

    return {
      items: filteredResults,
      total: indexResults.total,
      facets: indexResults.facets,
    };
  }

  private buildSearchQuery(criteria: SearchCriteria): SearchQuery {
    return {
      searchText: criteria.query,
      filters: [
        { field: "employeeId", value: criteria.employeeId },
        { field: "name", value: criteria.name },
        { field: "category", value: criteria.category },
        { field: "businessTitle", value: criteria.businessTitle },
      ],
      facets: ["department", "shift", "role"],
      orderBy: criteria.orderBy,
      skip: criteria.skip,
      take: criteria.take,
    };
  }
}

interface SearchCriteria {
  query?: string;
  employeeId?: string;
  name?: string;
  category?: string;
  businessTitle?: string;
  userId: string;
  orderBy?: string;
  skip?: number;
  take?: number;
}
```

### 2. Search Index Schema

```json
{
  "name": "crew-search-index",
  "fields": [
    {
      "name": "id",
      "type": "Edm.String",
      "key": true,
      "searchable": false
    },
    {
      "name": "employeeId",
      "type": "Edm.String",
      "searchable": true,
      "filterable": true
    },
    {
      "name": "name",
      "type": "Edm.String",
      "searchable": true,
      "filterable": true
    },
    {
      "name": "category",
      "type": "Edm.String",
      "searchable": true,
      "filterable": true,
      "facetable": true
    },
    {
      "name": "businessTitle",
      "type": "Edm.String",
      "searchable": true,
      "filterable": true,
      "facetable": true
    },
    {
      "name": "department",
      "type": "Edm.String",
      "searchable": true,
      "filterable": true,
      "facetable": true
    },
    {
      "name": "shift",
      "type": "Edm.String",
      "searchable": false,
      "filterable": true,
      "facetable": true
    },
    {
      "name": "role",
      "type": "Edm.String",
      "searchable": false,
      "filterable": true,
      "facetable": true
    }
  ],
  "suggesters": [
    {
      "name": "sg",
      "searchMode": "analyzingInfixMatching",
      "sourceFields": ["name", "employeeId"]
    }
  ]
}
```

### 3. Search API Endpoints

```typescript
@Controller("search")
class SearchController {
  @Get("crews")
  @UseGuards(AuthGuard, PermissionGuard)
  @Permissions("VIEW_CREWS")
  async searchCrews(
    @Query() criteria: SearchCrewsDto,
    @CurrentUser() user: User
  ): Promise<SearchResult> {
    return this.crewSearchService.search({
      ...criteria,
      userId: user.id,
    });
  }

  @Get("members")
  @UseGuards(AuthGuard, PermissionGuard)
  @Permissions("VIEW_MEMBERS")
  async searchMembers(
    @Query() criteria: SearchMembersDto,
    @CurrentUser() user: User
  ): Promise<SearchResult> {
    return this.memberSearchService.search({
      ...criteria,
      userId: user.id,
    });
  }

  @Get("suggest")
  @UseGuards(AuthGuard)
  async suggest(
    @Query("q") query: string,
    @CurrentUser() user: User
  ): Promise<SuggestionResult> {
    return this.searchService.suggest(query, user.id);
  }
}
```

### 4. Search Result Processors

```typescript
@Injectable()
class SearchResultProcessor {
  async processResults(
    results: SearchResult,
    user: User
  ): Promise<ProcessedSearchResult> {
    // 1. Enrich results with related data
    const enrichedResults = await this.enrichResults(results.items);

    // 2. Apply user-specific transformations
    const transformedResults = await this.transformForUser(
      enrichedResults,
      user
    );

    // 3. Format response
    return {
      items: transformedResults,
      facets: this.processFacets(results.facets),
      total: results.total,
      metadata: {
        executionTime: results.metadata.executionTime,
        filters: results.metadata.appliedFilters,
      },
    };
  }

  private async enrichResults(items: any[]): Promise<any[]> {
    const enrichedItems = await Promise.all(
      items.map(async (item) => {
        const [crew, manager, department] = await Promise.all([
          this.crewService.getBasicInfo(item.crewId),
          this.userService.getBasicInfo(item.managerId),
          this.departmentService.getBasicInfo(item.departmentId),
        ]);

        return {
          ...item,
          crew,
          manager,
          department,
        };
      })
    );

    return enrichedItems;
  }
}
```

## Event Patterns

### 1. Domain Events

#### Crew Events

```json
{
  "eventId": "UUID",
  "eventType": "CrewCreated",
  "version": "1.0",
  "timestamp": "ISO-8601",
  "data": {
    "crewId": "UUID",
    "name": "string",
    "managerId": "UUID"
  },
  "metadata": {
    "correlationId": "UUID",
    "causationId": "UUID",
    "userId": "UUID"
  }
}
```

#### Training Events

```json
{
  "eventId": "UUID",
  "eventType": "TrainingCompleted",
  "version": "1.0",
  "timestamp": "ISO-8601",
  "data": {
    "operatorId": "UUID",
    "trainerId": "UUID",
    "completionDate": "timestamp",
    "qualificationStatus": "Qualified",
    "assessmentDetails": {
      "score": "number",
      "comments": "string?"
    }
  },
  "metadata": {
    "correlationId": "UUID",
    "causationId": "UUID",
    "userId": "UUID"
  }
}
```

### 2. Event Flow Patterns

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant CommandHandler
    participant WriteModel
    participant ChangeFeed
    participant EventBus
    participant ReadModel
    participant Projector

    Client->>API: POST /api/v1/commands/...
    API->>CommandHandler: Handle Command
    CommandHandler->>WriteModel: Save Changes
    WriteModel->>ChangeFeed: Trigger Change
    ChangeFeed->>EventBus: Publish Event
    EventBus->>Projector: Notify Change
    Projector->>ReadModel: Update Projection

    Note over ReadModel: Eventually Consistent
```

### 3. Event Subscriptions

| Event Type        | Publisher        | Subscribers                            | Purpose                                        |
| ----------------- | ---------------- | -------------------------------------- | ---------------------------------------------- |
| CrewCreated       | Crew Service     | Notification Service, User Service     | Notify stakeholders, Update org structure      |
| MemberAssigned    | Crew Service     | Training Service, Notification Service | Start training workflow, Notify team           |
| TrainingCompleted | Training Service | Crew Service, Notification Service     | Update qualification status, Notify management |
| RoleChanged       | Crew Service     | User Service, Notification Service     | Update permissions, Notify stakeholders        |

## Security

### Roles & Permissions

| Role                   | Permissions                                     |
| ---------------------- | ----------------------------------------------- |
| Super Admin            | Full access to all operations                   |
| TKS Responsible        | Create operators, bulk imports                  |
| Training Responsible   | Assign trainers, view training status           |
| Trainer                | Update qualification status, complete training  |
| Shift Leader           | Temporary and final team assignments            |
| Development Specialist | Create movement notices, temporary role changes |
| Team Leader            | View team members, manage daily assignments     |
| Operator               | View own assignments and organizational chart   |

## Role-Based Access Control (RBAC)

### 1. Role Definitions

```typescript
enum SystemRole {
  SUPER_ADMIN = "SUPER_ADMIN",
  TKS_RESPONSIBLE = "TKS_RESPONSIBLE",
  TRAINING_RESPONSIBLE = "TRAINING_RESPONSIBLE",
  TRAINER = "TRAINER",
  SHIFT_LEADER = "SHIFT_LEADER",
  DEVELOPMENT_SPECIALIST = "DEVELOPMENT_SPECIALIST",
  TEAM_LEADER = "TEAM_LEADER",
  OPERATOR = "OPERATOR",
}

interface RolePermissions {
  role: SystemRole;
  permissions: Permission[];
  hierarchyLevel: HierarchyLevel;
  allowedActions: string[];
}
```

### 2. Permission Matrix

```typescript
const rolePermissionMatrix: Record<SystemRole, RolePermissions> = {
  SUPER_ADMIN: {
    role: SystemRole.SUPER_ADMIN,
    permissions: ["*"],
    hierarchyLevel: HierarchyLevel.SYSTEM,
    allowedActions: ["*"],
  },
  TKS_RESPONSIBLE: {
    role: SystemRole.TKS_RESPONSIBLE,
    permissions: [
      "CREATE_OPERATOR",
      "IMPORT_OPTITIME_DATA",
      "ASSIGN_TO_TRAINING_RESPONSIBLE",
    ],
    hierarchyLevel: HierarchyLevel.PLANT,
    allowedActions: ["CREATE", "IMPORT", "ASSIGN"],
  },
  TRAINING_RESPONSIBLE: {
    role: SystemRole.TRAINING_RESPONSIBLE,
    permissions: [
      "ASSIGN_TRAINER",
      "VIEW_TRAINING_STATUS",
      "MANAGE_TRAINING_PROGRAMS",
    ],
    hierarchyLevel: HierarchyLevel.PLANT,
    allowedActions: ["ASSIGN", "VIEW", "MANAGE"],
  },
  TRAINER: {
    role: SystemRole.TRAINER,
    permissions: [
      "START_TRAINING",
      "UPDATE_QUALIFICATION",
      "ASSIGN_TO_SHIFT_LEADER",
    ],
    hierarchyLevel: HierarchyLevel.DEPARTMENT,
    allowedActions: ["START", "UPDATE", "ASSIGN"],
  },
  SHIFT_LEADER: {
    role: SystemRole.SHIFT_LEADER,
    permissions: [
      "ASSIGN_TEAM_LEADER",
      "TEMPORARY_ASSIGNMENT",
      "VIEW_SHIFT_METRICS",
    ],
    hierarchyLevel: HierarchyLevel.SHIFT,
    allowedActions: ["ASSIGN", "VIEW"],
  },
  DEVELOPMENT_SPECIALIST: {
    role: SystemRole.DEVELOPMENT_SPECIALIST,
    permissions: [
      "CREATE_MOVEMENT_NOTICE",
      "INITIATE_ROLE_CHANGE",
      "VIEW_ORGANIZATION",
    ],
    hierarchyLevel: HierarchyLevel.PLANT,
    allowedActions: ["CREATE", "INITIATE", "VIEW"],
  },
  TEAM_LEADER: {
    role: SystemRole.TEAM_LEADER,
    permissions: ["VIEW_TEAM_MEMBERS", "MANAGE_DAILY_ASSIGNMENTS"],
    hierarchyLevel: HierarchyLevel.TEAM,
    allowedActions: ["VIEW", "MANAGE"],
  },
  OPERATOR: {
    role: SystemRole.OPERATOR,
    permissions: ["VIEW_ORG_CHART", "VIEW_OWN_ASSIGNMENTS"],
    hierarchyLevel: HierarchyLevel.OPERATOR,
    allowedActions: ["VIEW"],
  },
};
```

### 3. Permission Enforcement

```typescript
@Injectable()
class PermissionGuard implements CanActivate {
  constructor(
    private readonly roleService: RoleService,
    private readonly hierarchyService: HierarchyService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const requiredPermissions = this.reflector.get<string[]>(
      "permissions",
      context.getHandler()
    );

    // 1. Check role permissions
    const hasPermission = await this.roleService.hasPermissions(
      user.id,
      requiredPermissions
    );

    if (!hasPermission) return false;

    // 2. Check hierarchy level access
    if (request.params.crewId) {
      const hasHierarchyAccess = await this.hierarchyService.canAccessCrew(
        user.id,
        request.params.crewId
      );

      if (!hasHierarchyAccess) return false;
    }

    // 3. Check N+2 rule for temporary changes
    if (request.body.type === "TEMPORARY_CHANGE") {
      const isN2Valid = await this.hierarchyService.validateN2Approval(
        user.id,
        request.body.targetUserId
      );

      if (!isN2Valid) return false;
    }

    return true;
  }
}
```

### 4. Hierarchy Level Validation

```typescript
@Injectable()
class HierarchyService {
  async validateN2Approval(
    requesterId: string,
    targetUserId: string
  ): Promise<boolean> {
    const requesterLevel = await this.getUserHierarchyLevel(requesterId);
    const targetLevel = await this.getUserHierarchyLevel(targetUserId);

    return this.isN2Relationship(requesterLevel, targetLevel);
  }

  async canAccessCrew(userId: string, crewId: string): Promise<boolean> {
    const userHierarchy = await this.getUserHierarchyLevel(userId);
    const crewHierarchy = await this.getCrewHierarchyLevel(crewId);

    return this.hasHierarchyAccess(userHierarchy, crewHierarchy);
  }

  private isN2Relationship(
    requesterLevel: HierarchyLevel,
    targetLevel: HierarchyLevel
  ): boolean {
    const levelDifference = this.calculateHierarchyDifference(
      requesterLevel,
      targetLevel
    );
    return levelDifference >= 2;
  }
}
```

## Business Rules

1. **Hierarchy Management**

   - Organizational structure follows Workday hierarchy from Plant Manager to Shift Leader
   - Team Leader and Operator management handled within the system
   - Each member can belong to only one crew at a time
   - Crew names must be unique
   - Every crew must have an assigned manager

2. **Role Changes**

   - Plant Manager to Shift Leader changes sync from Workday
   - Team Leader and Operator changes require Movement Notice
   - Temporary changes need N+2 hierarchy approval
   - Training completion required for certain role assignments

3. **Training & Qualification**
   - New operators must complete OJT training
   - Qualification status must be "O" before final assignment
   - Only authorized trainers can update qualification status

## Monitoring & Observability

### Metrics

- Request latency
- Event processing rates
- Error rates by type
- Business rule violations
- Training completion rates
- Assignment completion times

### Logging

- Request/response logging
- Event publishing/processing
- Business rule validations
- Integration events
- Role change history
- Training status updates

## Infrastructure & Deployment

### 1. Azure Resources

```mermaid
graph TD
    subgraph Azure Resources
        AKS[Azure Kubernetes Service]
        COSMOS[Cosmos DB]
        ASB[Azure Service Bus]
        AI[Application Insights]
        KV[Key Vault]
        BLOB[Blob Storage]

        AKS -->|Read/Write| COSMOS
        AKS -->|Events| ASB
        AKS -->|Telemetry| AI
        AKS -->|Secrets| KV
        AKS -->|Storage| BLOB
    end

    subgraph Kubernetes Resources
        API[API Pods]
        PROJ[Projector Pods]
        CF[ChangeFeed Pods]

        API -->|Scale| HPA[HPA]
        PROJ -->|Scale| HPA
        CF -->|Scale| HPA
    end
```

### 2. Deployment Configuration

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crew-management-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crew-management
  template:
    metadata:
      labels:
        app: crew-management
    spec:
      containers:
        - name: api
          image: crew-management:v1
          ports:
            - containerPort: 80
          env:
            - name: COSMOS_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: cosmos-secrets
                  key: connection-string
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "512Mi"
              cpu: "500m"
```

### 3. Scaling Strategy

| Component       | Scaling Trigger      | Min Replicas | Max Replicas |
| --------------- | -------------------- | ------------ | ------------ |
| API Pods        | CPU > 70%            | 3            | 10           |
| Projector Pods  | Event Lag > 1000     | 2            | 8            |
| ChangeFeed Pods | Processing Time > 5s | 2            | 6            |

## Monitoring & Observability

### 1. Key Metrics

#### Business Metrics

- Crew creation rate
- Member assignment completion time
- Training completion rate
- Role change frequency
- SLA compliance rate

#### Technical Metrics

- Command processing time
- Query response time
- Event processing lag
- Projection sync delay
- Error rates by type

### 2. Logging Strategy

```json
{
  "log_type": "CommandProcessing|QueryExecution|EventHandling",
  "correlation_id": "UUID",
  "timestamp": "ISO-8601",
  "level": "INFO|WARN|ERROR",
  "component": "string",
  "message": "string",
  "data": {
    "command_type": "string?",
    "processing_time_ms": "number?",
    "error_details": "object?"
  },
  "context": {
    "user_id": "UUID?",
    "crew_id": "UUID?",
    "source_ip": "string?"
  }
}
```

### 3. Alerting Rules

| Metric                | Threshold     | Severity | Action        |
| --------------------- | ------------- | -------- | ------------- |
| Command Failure Rate  | >5% in 5m     | High     | Page On-Call  |
| Event Processing Lag  | >10000 events | Medium   | Notify Team   |
| Query Response Time   | >500ms p95    | Low      | Create Ticket |
| Projection Sync Delay | >5m           | Medium   | Notify Team   |

### 4. Dashboards

#### Operations Dashboard

```mermaid
graph TD
    subgraph Key Metrics
        A[Command Success Rate]
        B[Event Processing Lag]
        C[Query Performance]
        D[Error Rate]
    end

    subgraph Business Metrics
        E[Active Crews]
        F[Training Progress]
        G[Role Changes]
        H[SLA Compliance]
    end

    subgraph System Health
        I[Pod Status]
        J[Resource Usage]
        K[Database Performance]
        L[Network Latency]
    end
```

## Disaster Recovery

### 1. Backup Strategy

| Component    | Backup Frequency | Retention | Recovery Time Objective |
| ------------ | ---------------- | --------- | ----------------------- |
| Cosmos DB    | Continuous       | 30 days   | < 1 hour                |
| Event Log    | Real-time        | 90 days   | < 2 hours               |
| Blob Storage | Daily            | 90 days   | < 4 hours               |

### 2. Recovery Procedures

1. **Database Failure**

   - Automatic failover to secondary region
   - Verify data consistency
   - Rebuild projections if needed

2. **Service Failure**

   - Scale healthy pods
   - Replay events from last checkpoint
   - Verify projection sync

3. **Region Failure**
   - Activate secondary region
   - Update DNS records
   - Verify replication status

## Security Measures

### 1. Network Security

- Private endpoints for Azure services
- Network isolation in AKS
- TLS 1.3 enforcement
- IP whitelisting

### 2. Data Security

- Encryption at rest (Cosmos DB)
- Encryption in transit (TLS)
- Key rotation policy
- Data retention policy

### 3. Access Control

- Azure AD integration
- RBAC implementation
- Just-in-time access
- Audit logging

## Performance Optimization

### 1. Caching Strategy

- Redis for frequently accessed data
- In-memory caching for reference data
- Cache invalidation on events
- TTL-based expiration

### 2. Query Optimization

- Indexed properties in Cosmos DB
- Materialized views for complex queries
- Pagination implementation
- Query result caching

### 3. Event Processing

- Batch processing for efficiency
- Dead letter queue handling
- Retry policies
- Event ordering guarantees

## Conclusion

The Crew Management Service implements a robust CQRS architecture with event sourcing, providing a scalable and maintainable solution for Aptiv's workforce management needs. The service ensures:

- Clear separation of write and read concerns
- Eventual consistency through event-driven updates
- Scalable and resilient infrastructure
- Comprehensive monitoring and observability
- Strong security and disaster recovery measures

The implementation follows best practices for cloud-native applications while maintaining the specific business requirements of Aptiv's crew management processes.
