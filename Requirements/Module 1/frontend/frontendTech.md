# Frontend Technologies Overview

## 1. Framework

### Next.js 15

- **Description**: A React-based framework offering hybrid rendering (SSR, CSR) and a focus on performance.
- **Why Next.js?**:
  - Latest version (15) ensures optimal performance.
  - Supports TypeScript for type-safe development.
  - Includes Turbopack for faster builds.

---

## 2. Language

### TypeScript

- **Description**: A superset of JavaScript that adds static typing.
- **Why TypeScript?**:
  - Improves code quality and maintainability.
  - Detects errors during development.
  - Works seamlessly with Next.js and modern libraries.

---

## 3. State Management

### Zustand

- **Description**: A lightweight state management library for React.
- **Why Zustand?**:
  - Easy to implement and scalable.
  - Simple API for managing global state.
  - Works well with hooks for a seamless integration.

---

## 4. API Communication

### Axios

- **Description**: A promise-based HTTP client for JavaScript.
- **Why Axios?**:
  - Simplifies API calls and error handling.
  - Supports interceptors for token management.
  - Flexible and widely used in modern projects.

---

## 5. UI Components

### Shadcn UI with Tailwind CSS

- **Description**: A customizable component library built with Tailwind CSS.
- **Why Shadcn UI?**:
  - Prebuilt, accessible components.
  - Fully customizable via Tailwind's utility classes.
  - Works efficiently with Next.js and modern design workflows.

### Tailwind CSS

- **Description**: A utility-first CSS framework.
- **Why Tailwind CSS?**:
  - Speeds up styling with predefined utilities.
  - Ensures a responsive and consistent design.
  - Built into Next.js for seamless integration.

---

## 6. Form Validation

### Zod

- **Description**: A schema declaration and validation library.
- **Why Zod?**:
  - Provides type-safe schema validation.
  - Integrates seamlessly with TypeScript.
  - Lightweight and straightforward.

---

## 7. Workflow Visualization

### React Flow

- **Description**: A library for creating interactive node-based UIs, like workflows and flowcharts.
- **Why React Flow?**:
  - Supports drag-and-drop functionality for nodes and edges.
  - Highly customizable for creating unique workflows.
  - Free version includes essential features.

---

## 8. Rich Text Editing

### Tiptap

- **Description**: A headless, framework-agnostic rich text editor.
- **Why Tiptap?**:
  - Extensible and customizable with plugins.
  - Integrates seamlessly with React for modern UIs.
  - Provides a developer-friendly experience for building WYSIWYG editors.

---

## 9. Additional Libraries and Practices

### React Hooks

- **Description**: Built-in functions for managing state and lifecycle in React components.
- **Why React Hooks?**:
  - Clean and reusable component logic.
  - Simplifies state management alongside Zustand.

### Optimized Project Workflow

- Combining the latest tools and frameworks ensures:
  - Robust performance.
  - Scalability for future features.
  - Maintainability with modern development practices.

# Frontend Technologies Overview

## 1. Framework

### Next.js 15

- **Description**: A React-based framework offering hybrid rendering (SSR, CSR) and a focus on performance.
- **Why Next.js?**:
  - Latest version (15) ensures optimal performance.
  - Supports TypeScript for type-safe development.
  - Includes Turbopack for faster builds.

---

## 2. Language

### TypeScript

- **Description**: A superset of JavaScript that adds static typing.
- **Why TypeScript?**:
  - Improves code quality and maintainability.
  - Detects errors during development.
  - Works seamlessly with Next.js and modern libraries.

---

## 3. State Management

### Zustand

- **Description**: A lightweight state management library for React.
- **Why Zustand?**:
  - Easy to implement and scalable.
  - Simple API for managing global state.
  - Works well with hooks for a seamless integration.

---

## 4. API Communication

### Axios

- **Description**: A promise-based HTTP client for JavaScript.
- **Why Axios?**:
  - Simplifies API calls and error handling.
  - Supports interceptors for token management.
  - Flexible and widely used in modern projects.

---

## 5. UI Components

### Shadcn UI with Tailwind CSS

- **Description**: A customizable component library built with Tailwind CSS.
- **Why Shadcn UI?**:
  - Prebuilt, accessible components.
  - Fully customizable via Tailwind's utility classes.
  - Works efficiently with Next.js and modern design workflows.

### Tailwind CSS

- **Description**: A utility-first CSS framework.
- **Why Tailwind CSS?**:
  - Speeds up styling with predefined utilities.
  - Ensures a responsive and consistent design.
  - Built into Next.js for seamless integration.

---

## 6. Form Validation

### Zod

- **Description**: A schema declaration and validation library.
- **Why Zod?**:
  - Provides type-safe schema validation.
  - Integrates seamlessly with TypeScript.
  - Lightweight and straightforward.

---

## 7. Workflow Visualization

### React Flow

- **Description**: A library for creating interactive node-based UIs, like workflows and flowcharts.
- **Why React Flow?**:
  - Supports drag-and-drop functionality for nodes and edges.
  - Highly customizable for creating unique workflows.
  - Free version includes essential features.

---

## 8. Rich Text Editing

### Tiptap

- **Description**: A headless, framework-agnostic rich text editor.
- **Why Tiptap?**:
  - Extensible and customizable with plugins.
  - Integrates seamlessly with React for modern UIs.
  - Provides a developer-friendly experience for building WYSIWYG editors.

---

## 9. Additional Libraries and Practices

### React Hooks

- **Description**: Built-in functions for managing state and lifecycle in React components.
- **Why React Hooks?**:
  - Clean and reusable component logic.
  - Simplifies state management alongside Zustand.

### Optimized Project Workflow

- Combining the latest tools and frameworks ensures:
  - Robust performance.
  - Scalability for future features.
  - Maintainability with modern development practices.

---

# Installation

## Installation

To install the project dependencies, run the following command:

```bash

pnpm install

```

### Running the Development Server

To run the development server, use the following command:

```bash
pnpm dev
```

#### Building for Production

To create an optimized production build, use:

```bash
pnpm build

```

##### Running the Production Server

After building the project, you can start the production server with the following command:

```bash
pnpm start
```

# Formatting and Pre-commit with Husky

## Automatic Formatting with Husky

Husky is configured to format only the changed files before each commit. You can run this check with the following command:

```bash
pnpm lint-staged
```

This ensures that only the modified code is formatted as part of your pre-commit process.

### Global Project Formatting

To format the entire project with Prettier, use the following command:

```bash
pnpm format:all
```

This command will format the entire project. Use it only when necessary.
