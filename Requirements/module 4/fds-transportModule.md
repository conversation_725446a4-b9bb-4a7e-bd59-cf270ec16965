# Module 4: Transport Management

## Functional Design Specification

This functional design specification outlines the Transport Management module for APTIV, detailing the processes for managing pickup stations, transport planning, bus boarding, and country-specific variations. The goal is to provide a robust, flexible, and scalable system that ensures efficient employee transportation while integrating with existing tools like Workday and CW. Below, each feature is explored in depth with improved clarity, structure, and additional details on interfaces, workflows, validations, and system behavior.

---

## 1. Pickup Station Management

This section covers the creation, assignment, and modification of pickup stations, ensuring employees are efficiently routed to their designated transport points.

### 1.1 Creation of Pickup Stations

#### Description

Pickup stations can be created either independently by the Transport Agent or through a request from the Team Leader (TL), with approval required in the latter case.

#### Features

- **Independent Creation by Transport Agent**

  - **Interface:** A web-based form accessible to the Transport Agent, featuring fields such as:
    - Station Name (text, required)
    - Address (text, required, with mapping service integration)
    - Route Code (dropdown, linked to predefined routes)
    - GPS Coordinates (optional, auto-populated via address if available)
  - **Validation:**
    - Station Name must be unique (system checks against existing stations).
    - Address must be verifiable via an integrated mapping API (e.g., Google Maps).
    - Route Code must correspond to an active route.
  - **Process:** Upon submission, the station is immediately added to the system and available for assignment.

- **Request-Based Creation by Team Leader**
  - **Interface:** A tablet app form for TLs with fields:
    - Station Name (text, required)
    - Proposed Address (text, required)
    - Reason for Request (text, optional)
  - **Workflow:**
    1. TL submits the request via the tablet app.
    2. Request appears in the Transport Agent’s approval queue (web interface).
    3. Transport Agent reviews, approves, or rejects with a reason (e.g., "Duplicate station exists").
    4. If approved, the Transport Agent completes the creation process using the independent creation interface.
  - **Validation:** Same as independent creation, plus a check to ensure the request isn’t redundant.

#### Dynamic Behavior

- New stations are instantly reflected in the transport schedule and available for assignment.

---

### 1.2 Pickup Station Assignment

#### Description

The Transport Agent assigns pickup stations to APTIV employees across departments (DH, IH, IS).

#### Features

- **Interface:** A web-based dashboard with:
  - Employee search (by name, ID, or department).
  - List view of employees with current station assignments.
  - Dropdown to select a pickup station for each employee.
- **Functionality:**
  - **Individual Assignment:** Select an employee and assign a station from the dropdown.
  - **Bulk Assignment:** Upload a CSV file (e.g., columns: Employee ID, Station Code) or multi-select employees and assign a single station.
- **Validation:**
  - Employee must exist in the system (linked to Workday data).
  - Station must be active and not over capacity (optional capacity limit configurable per station).

#### Dynamic Behavior

- Assignments update the transport schedule in real-time, adjusting passenger counts per station and route.

---

### 1.3 Address Change Request via Team Leader Tablet

#### Description

TLs can request address changes or new pickup station assignments for operators, with Clerk involvement for IS/IH employees and Transport Agent approval.

#### Features

- **Request Types:**

  - **Address Change (REQ27):**
    - **Interface:** Tablet app form with:
      - Operator Name/ID (dropdown/search, required)
      - New Address (text, required)
      - Reason (text, optional)
    - **Process:**
      1. TL submits the request.
      2. For IS/IH, the request routes to a Clerk’s queue (web interface); for DH, it goes directly to the Transport Agent.
      3. Clerk (if applicable) reviews and forwards to the Transport Agent.
  - **Pickup Station Change (REQ26):**
    - **Interface:** Tablet app form with:
      - Operator Name/ID (dropdown/search, required)
      - New Pickup Station (dropdown of available stations, required)
      - Reason (text, optional)
    - **Process:** Same as address change, with Clerk involvement for IS/IH.

- **Approval by Transport Agent:**
  - **Interface:** Web-based approval queue showing:
    - Request details (operator, new address/station, reason).
    - Map view (for address changes) to assess proximity to existing stations.
  - **Decision:**
    - Approve: Updates the operator’s address/station; may trigger a station reassignment if proximity warrants it.
    - Reject: Returns to TL/Clerk with a reason.

#### Dynamic Behavior

- Approved changes update the operator’s profile and transport schedule instantly, recalculating route demands.

---

### 1.4 Dynamic Updates to Transport Schedule

#### Description

All changes (creation, assignment, address updates) dynamically adjust the transport schedule.

#### Features

- **Real-Time Updates:** System recalculates:
  - Number of employees per station and route.
  - Minibus requirements based on new passenger distribution.
- **Notification:** Alerts Transport Agent and TLs of significant schedule changes (e.g., minibus reallocation).

---

## 2. Transport Planning

This section details the process of planning employee transport based on shift schedules, including route management and minibus allocation.

### 2.1 Setup Working Plans and Shift Assignments

#### Description

Shift Leaders and Transport Agents collaborate to plan transport based on managerial working plans.

#### Features

- **Shift Leader Input:**

  - **Data Source:** Working plans received from managers (e.g., Excel, API, or manual entry).
  - **Interface:** Web form/dashboard to:
    - Select TLs and operators per shift/date.
    - Assign shifts (M, S, N, HC1, HC2, OT).
  - **Validation:** Ensure all operators have assigned pickup stations.

- **Transport Agent Planning:**
  - **Data Received:** Shift Leader submits a plan with:
    - **Trajet (Route):** Predefined by Transport Agent (e.g., Trajet A).
    - **Mlle (Passengers):** Number of people per station.
    - **Code:** Station identifier.
    - **Station:** Station name.
    - **Horaire (Timing):** Shift codes (M, S, N, HC1, HC2, OT).
  - **Interface:** Web dashboard displaying:
    - Table of routes, stations, and passenger counts per shift.
    - Editable fields to adjust assignments.

#### Example Data

| Trajet         | Code     | Effectif | M   | HC1 | HC2 | S   | N   |
| -------------- | -------- | -------- | --- | --- | --- | --- | --- |
| Kembah Mhedhya | Trajet A | 110      | 29  | 4   | 142 | 96  | 0   |
| Oulad Oujjih   | Trajet B | 37       | 18  | 3   | 30  | 23  | 0   |

---

### 2.2 Transport Planning Updates

#### Description

The Transport Agent can manually adjust the transport plan.

#### Features

- **Interface:** Planning dashboard with:
  - Add button: Search/select an employee and assign to a station/shift.
  - Remove button: Select an employee and delete from the plan.
- **Functionality:**
  - Drag-and-drop or button-based adjustments.
  - Audit trail logs all changes (who, what, when).
- **Validation:**
  - Adding an employee requires a valid pickup station.
  - Removal triggers a confirmation prompt.

#### Dynamic Behavior

- Updates recalculate passenger totals and minibus needs instantly.

---

### 2.3 Minibus Allocation

#### Description

Minibuses are dynamically allocated based on passenger demand per time slot.

#### Features

- **Automated Calculation:**
  - Formula: `Total Employees / 20` (20 seats per minibus).
  - Example: 906 employees at 06:00 = 46 minibuses.
- **Interface:** Dashboard showing:
  - Time slots (06:00, 08:00, 14:00, 18:00, 22:00).
  - Employee count and minibus allocation per slot.
- **Manual Override:**
  - Transport Agent can adjust minibus numbers with a logged reason (e.g., "Extra capacity needed").

#### Example Allocation

| Horaire | Nb de Minibus | Effectif |
| ------- | ------------- | -------- |
| 06:00   | 46            | 906      |
| 08:00   | 18            | 346      |
| 14:00   | 42            | 840      |

---

### 2.4 Additional Features

- **Route Identification:** Each route has a unique code and passenger total, categorized by shift (M, HC1, HC2, S, N).
- **Integration with Transport Providers:**
  - **Real-Time Updates:** API shares planning data with providers.
  - **Excel Export:** Generates a file with routes, stations, and timings post-planning.
- **CW Integration:** Tracks bus routes in real-time.
- **Excel Upload:** Transport Agent uploads files to monitor routes and passengers.
- **Special Cases:**
  - **Suppliers/Trainees/New Hires:** Manually added via Temporary IDs; Workday integration proposed (IT to assess).
- **Access Restrictions:**
  - Transport Agent and N+1 lack visual employee access; KPIs (e.g., occupancy rate) provided via dashboards.

---

## 3. Bus Boarding Process

This section outlines the steps for operators to board buses, ensuring accurate tracking.

### Steps and Features

1. **Bus Terminal Display**

   - **Interface:** Touch-screen terminal showing operators expected per shift (name, ID, station).
   - **Update:** Refreshes based on transport plan.

2. **Operator Presence Check**

   - **Absent:** Status remains "Pending" until shift start; updated to "Present" or "Delayed" based on production line check.
   - **Present:** Proceeds to badge check.

3. **Badge Check**

   - **With Badge:**
     - Operator scans badge at terminal.
     - TL tablet updates status to "On the Bus."
     - Optional nursery notification via checkbox.
   - **Without Badge:**
     - Operator enters registration number manually.
     - System validates number; triggers notifications for presence and nursery (if selected).

4. **Final Status**
   - **Confirmed:** "Present on the Bus" after check-in.
   - **Unconfirmed:** TL visually checks at shift start and updates status via tablet.

#### Dynamic Behavior

- Real-time status updates on TL tablets and central system.

---

## 4. Morocco vs. EMEA/NA Transport Planning Processes

### Comparison Table

| Process                 | Morocco | Tunisia | Turkey | Portugal | Poland |
| ----------------------- | ------- | ------- | ------ | -------- | ------ |
| Transport Planification | ✅      | ✅      | ❌     | -        | -      |
| Change Pickup Station   | ✅      | ❌      | ✅     | -        | -      |

### Country-Specific Details

- **Serbia:**
  - No predefined stations; employees choose based on address updates via HR and Transport team.
- **Tunisia:**
  - General Services Specialist consolidates transport data from emailed production plans.
- **Turkey:**
  - AI-optimized routes, manually validated weekly; upcoming QR code tracking.
- **North America:**
  - Suppliers/trainees excluded; address changes via Workday.

---

## Enhancements and Considerations

- **Notifications:** Email/SMS alerts for request approvals, schedule changes, or boarding issues.
- **Reporting:** Dashboards for occupancy rates, route efficiency, and shift coverage.
- **Mobile Access:** Tablet app for TLs; web interface optimized for mobile.
- **Error Handling:** Graceful management of missing data (e.g., unassigned stations prompt alerts).
- **Scalability:** System supports increasing employees/stations.
- **Security:** Encrypted data, role-based access (e.g., Transport Agent vs. Clerk).

This revised specification maintains the original logic while enhancing detail, usability, and system integration, providing a comprehensive blueprint for the Transport Management module.
