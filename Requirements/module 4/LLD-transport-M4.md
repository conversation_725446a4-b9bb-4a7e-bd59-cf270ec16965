# Low-Level Design Document: Transport Management Module

## Document Information

**Version:** 1.0.0  
**Last Updated:** 2025-03-26  
**Status:** Completed  
**Authors: <AUTHORS>

## Executive Summary

### Key Features

- Microservices-based architecture with three core services
- Event-driven communication using Azure Service Bus
- Configurable business rules and validation
- Multi-country support with country-specific variations
- Real-time data synchronization and monitoring
- Role-based access control and security

### Business Benefits

- Scalable and maintainable transport management system
- Flexible configuration for different country requirements
- Improved operational efficiency through automation
- Enhanced data security and compliance
- Real-time monitoring and reporting capabilities

### Document Focus

This document provides detailed technical specifications for the Transport Management module, including architecture, domain models, database design, and implementation guidelines.

## Table of Contents

1. [Overview](#1-overview)
   - [Purpose and Scope](#11-purpose-and-scope)
   - [Key Components](#12-key-components)
2. [System Architecture](#2-system-architecture)
   - [High-Level Architecture](#21-high-level-architecture)
   - [Technical Components](#22-technical-components)
3. [Domain Model Design](#3-domain-model-design)
   - [Bounded Contexts](#31-bounded-contexts)
   - [Aggregates and Entities](#32-aggregates-and-entities)
4. [Configurability and Dynamic Features](#4-configurability-and-dynamic-features)
   - [Configuration Service](#41-configuration-service)
   - [Enhanced Entity Structure](#42-enhanced-entity-structure)
5. [Microservices Implementation](#5-microservices-implementation)
   - [Pickup Station Service](#51-pickup-station-service)
   - [Transport Planning Service](#52-transport-planning-service)
   - [Bus Boarding Service](#53-bus-boarding-service)
   - [Configuration Service](#54-configuration-service)
6. [Database Design](#6-database-design)
   - [CosmosDB Container Design](#61-cosmosdb-container-design)
   - [Microservice-Specific Containers](#62-microservice-specific-containers)
   - [Data Synchronization](#63-data-synchronization)
7. [Event-Driven Architecture](#7-event-driven-architecture)
   - [Event Schema](#71-event-schema)
   - [Key Events and Event Flow](#72-key-events-and-event-flow)
8. [Security Design](#8-security-design)
   - [Authentication and Authorization](#81-authentication-and-authorization)
   - [Data Protection](#82-data-protection)
9. [Deployment and DevOps](#9-deployment-and-devops)
   - [CI/CD Pipeline](#91-cicd-pipeline)
   - [Monitoring and Alerting](#92-monitoring-and-alerting)
10. [Conclusion](#10-conclusion)

## 1. Overview

### 1.1 Purpose and Scope

This low-level design document outlines the detailed architecture for the Transport Management module based on Domain-Driven Design (DDD) principles and microservices architecture. The design prioritizes configurability, flexibility, and scalability to accommodate various client requirements across different countries.

### 1.2 Key Components

The system consists of three primary microservices:

1. Pickup Station Service
2. Transport Planning Service
3. Bus Boarding Service

Each service is supported by:

- Azure CosmosDB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Configuration Service for centralized settings

## 2. System Architecture Overview

### 2.1 High-Level Architecture

The system follows a microservices architecture with the following components:

```mermaid
flowchart TD
    ApiManagement[Azure API Management] --> PickupStation[Pickup Station Service]
    ApiManagement --> TransportPlanning[Transport Planning Service]
    ApiManagement --> BusBoarding[Bus Boarding Service]
    ApiManagement --> ConfigService[Configuration Service]

    PickupStation --> StationDB[(Station Cosmos DB)]
    TransportPlanning --> PlanningDB[(Planning Cosmos DB)]
    BusBoarding --> BoardingDB[(Boarding Cosmos DB)]
    ConfigService --> ConfigDB[(Config Cosmos DB)]

    PickupStation -.-> ConfigService
    TransportPlanning -.-> ConfigService
    BusBoarding -.-> ConfigService

    StationDB -.-> ServiceBus{Azure Service Bus}
    PlanningDB -.-> ServiceBus
    BoardingDB -.-> ServiceBus

    ServiceBus -.-> PickupStation
    ServiceBus -.-> TransportPlanning
    ServiceBus -.-> BusBoarding

    style ApiManagement fill:#f9f,stroke:#333,stroke-width:2px
    style ServiceBus fill:#bbf,stroke:#333,stroke-width:2px
    style ConfigService fill:#bfb,stroke:#333,stroke-width:2px
```

### 2.2 Key Technical Components

1. **NestJS Microservices**: Core business logic implementation
2. **CosmosDB**: NoSQL database with multi-region support
3. **Azure Service Bus**: Event-driven communication between services
4. **Change Feed Processor**: Real-time data synchronization
5. **Azure API Management**: API Gateway that provides a unified entry point for all client applications, handling routing, authentication, and rate limiting
6. **Configuration Service**: Centralized configuration management for client-specific settings

## 3. Domain Model Design

### 3.1 Bounded Contexts

The system is divided into three primary bounded contexts:

#### 3.1.1 Pickup Station Management Context

- Core domain responsible for station creation, management, and assignment
- Handles address change requests from Team Leaders

#### 3.1.2 Transport Planning Context

- Manages shift assignments and working plans
- Handles minibus allocation and route planning

#### 3.1.3 Bus Boarding Context

- Manages the bus boarding process
- Tracks employee presence and status changes

### 3.2 Aggregates, Entities, and Value Objects

#### 3.2.1 Pickup Station Context

**Class Diagram:**

```mermaid
graph TD
    PickupStation["PickupStation<br>+UUID id<br>+string name<br>+Address address<br>+string routeCode<br>+string countryCode<br>+boolean isActive<br>+number? capacity<br>+string createdBy<br>+Date createdAt<br>+Date updatedAt"]

    StationAssignment["StationAssignment<br>+UUID id<br>+string employeeId<br>+UUID stationId<br>+Date assignedDate<br>+string updatedBy"]

    AddressChangeRequest["AddressChangeRequest<br>+UUID id<br>+string employeeId<br>+Address? currentAddress<br>+Address proposedAddress<br>+string? reason<br>+RequestStatus status<br>+string requestedBy<br>+Approval? clerkApproval<br>+Approval? transportAgentApproval<br>+Date createdAt"]

    Address["Address<br>+string street<br>+string city<br>+string region<br>+string country<br>+string? postalCode<br>+GpsCoordinates? gpsCoordinates"]

    GpsCoordinates["GpsCoordinates<br>+number latitude<br>+number longitude"]

    Approval["Approval<br>+boolean approved<br>+Date date<br>+string by<br>+string? reason"]

    RequestStatus["RequestStatus<br><<enumeration>><br>PENDING<br>APPROVED<br>REJECTED"]

    PickupStation -->|contains| Address
    AddressChangeRequest -->|contains| Address
    AddressChangeRequest -->|has| Approval
    Address -->|contains| GpsCoordinates
    AddressChangeRequest -->|has| RequestStatus
```

**Aggregates:**

- PickupStation
- AddressChangeRequest

**Entities:**

```typescript
class PickupStation {
  id: UUID;
  name: string;
  address: Address;
  routeCode: string;
  countryCode: string;
  isActive: boolean;
  capacity?: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

class StationAssignment {
  id: UUID;
  employeeId: string;
  stationId: UUID;
  assignedDate: Date;
  updatedBy: string;
}

class AddressChangeRequest {
  id: UUID;
  employeeId: string;
  currentAddress?: Address;
  proposedAddress: Address;
  reason?: string;
  status: RequestStatus;
  requestedBy: string;
  clerkApproval?: Approval;
  transportAgentApproval?: Approval;
  createdAt: Date;
}
```

**Value Objects:**

```typescript
class Address {
  street: string;
  city: string;
  region: string;
  country: string;
  postalCode?: string;
  gpsCoordinates?: GpsCoordinates;
}

class GpsCoordinates {
  latitude: number;
  longitude: number;
}

class Approval {
  approved: boolean;
  date: Date;
  by: string;
  reason?: string;
}

enum RequestStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}
```

#### 3.2.2 Transport Planning Context

**Class Diagram:**

```mermaid
graph TD
    TransportPlan["TransportPlan<br>+UUID id<br>+Date date<br>+PlanStatus status<br>+string countryCode<br>+string createdBy<br>+Date createdAt<br>+Date updatedAt"]

    ShiftAssignment["ShiftAssignment<br>+UUID id<br>+UUID planId<br>+string employeeId<br>+UUID stationId<br>+ShiftCode shiftCode<br>+string routeCode"]

    MinibusAllocation["MinibusAllocation<br>+UUID id<br>+UUID planId<br>+string timeSlot<br>+number employeeCount<br>+number minibusCount<br>+boolean isManuallyAdjusted<br>+string? adjustmentReason<br>+string updatedBy<br>+Date updatedAt"]

    PlanStatus["PlanStatus<br><<enumeration>><br>DRAFT<br>PUBLISHED<br>ARCHIVED"]

    ShiftCode["ShiftCode<br><<enumeration>><br>MORNING(M)<br>SPECIAL_HC1(HC1)<br>SPECIAL_HC2(HC2)<br>SWING(S)<br>NIGHT(N)<br>OVERTIME(OT)"]

    TransportPlan -->|has status| PlanStatus
    ShiftAssignment -->|has code| ShiftCode
    TransportPlan -->|contains many| ShiftAssignment
    TransportPlan -->|has many| MinibusAllocation
```

**Aggregates:**

- TransportPlan
- MinibusAllocation

**Entities:**

```typescript
class TransportPlan {
  id: UUID;
  date: Date;
  status: PlanStatus;
  countryCode: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

class ShiftAssignment {
  id: UUID;
  planId: UUID;
  employeeId: string;
  stationId: UUID;
  shiftCode: ShiftCode;
  routeCode: string;
}

class MinibusAllocation {
  id: UUID;
  planId: UUID;
  timeSlot: string;
  employeeCount: number;
  minibusCount: number;
  isManuallyAdjusted: boolean;
  adjustmentReason?: string;
  updatedBy: string;
  updatedAt: Date;
}
```

**Value Objects:**

```typescript
enum PlanStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED",
  ARCHIVED = "ARCHIVED",
}

enum ShiftCode {
  MORNING = "M",
  SPECIAL_HC1 = "HC1",
  SPECIAL_HC2 = "HC2",
  SWING = "S",
  NIGHT = "N",
  OVERTIME = "OT",
}
```

#### 3.2.3 Bus Boarding Context

**Class Diagram:**

```mermaid
graph TD
    BoardingRecord["BoardingRecord<br>+UUID id<br>+string employeeId<br>+ShiftCode shiftCode<br>+Date date<br>+UUID stationId<br>+string routeCode<br>+BoardingStatus status<br>+CheckInMethod checkInMethod<br>+boolean nurseryNotification<br>+Date? checkInTime<br>+string? updatedBy<br>+Date updateTimestamp"]

    BoardingStatus["BoardingStatus<br><<enumeration>><br>PENDING<br>ON_BUS<br>ABSENT<br>DELAYED"]

    CheckInMethod["CheckInMethod<br><<enumeration>><br>BADGE<br>MANUAL<br>TL_UPDATE"]

    BoardingRecord -->|has status| BoardingStatus
    BoardingRecord -->|has method| CheckInMethod
    BoardingRecord -->|has shift| ShiftCode
```

**Aggregates:**

- BoardingRecord

**Entities:**

```typescript
class BoardingRecord {
  id: UUID;
  employeeId: string;
  shiftCode: ShiftCode;
  date: Date;
  stationId: UUID;
  routeCode: string;
  status: BoardingStatus;
  checkInMethod: CheckInMethod;
  nurseryNotification: boolean;
  checkInTime?: Date;
  updatedBy?: string;
  updateTimestamp: Date;
}
```

**Value Objects:**

```typescript
enum BoardingStatus {
  PENDING = "PENDING",
  ON_BUS = "ON_BUS",
  ABSENT = "ABSENT",
  DELAYED = "DELAYED",
}

enum CheckInMethod {
  BADGE = "BADGE",
  MANUAL = "MANUAL",
  TL_UPDATE = "TL_UPDATE",
}
```

## 4. Configurability and Dynamic Features

### 4.1 Configuration Service

The system implements a dedicated Configuration Service that centralizes client-specific configurations:

```mermaid
graph TD
    ClientConfiguration["ClientConfiguration<br>+string clientId<br>+string countryCode<br>+FeatureFlags features<br>+BusinessRules businessRules<br>+InterfaceOptions interfaces<br>+CountryVariations countryVariations<br>+ValidationRules validationRules<br>+IntegrationSettings integrations"]

    FeatureFlags["FeatureFlags<br>+boolean transportPlanning<br>+boolean changePickupStation<br>+boolean busBoarding<br>+boolean requireClerkApproval<br>+boolean integrateWithWorkday<br>+boolean integrateWithCW"]

    BusinessRules["BusinessRules<br>+number minibusCapacity<br>+number defaultStationCapacity<br>+number needsManualApprovalThreshold<br>+string[] shiftCodes<br>+string[] timeSlots<br>+string[] boardingStatusTypes<br>+string allocationAlgorithm"]

    InterfaceOptions["InterfaceOptions<br>+boolean enableTabletApp<br>+boolean enableTerminals<br>+boolean enableExcelUpload<br>+boolean enableNurseryNotification"]

    CountryVariations["CountryVariations<br>+boolean hasPredefinedStations<br>+boolean requiresAddressValidation<br>+boolean allowsAddressChangeRequests"]

    ValidationRules["ValidationRules<br>+object stationRules<br>+object employeeRules<br>+object planRules"]

    IntegrationSettings["IntegrationSettings<br>+object workday<br>+object mapping<br>+object cwSystem"]

    ClientConfiguration -->|contains| FeatureFlags
    ClientConfiguration -->|contains| BusinessRules
    ClientConfiguration -->|contains| InterfaceOptions
    ClientConfiguration -->|contains| CountryVariations
    ClientConfiguration -->|contains| ValidationRules
    ClientConfiguration -->|contains| IntegrationSettings
```

#### 4.1.1 Configuration Service Database

**Containers:**

1. **client_configurations**
   - Partition Key: `/clientId`
   - Purpose: Stores client-specific configuration settings
   - Sample Item:

```json
{
  "id": "config-123",
  "partitionKey": "client-morocco",
  "clientId": "client-morocco",
  "countryCode": "MOROCCO",
  "features": {
    "transportPlanning": true,
    "changePickupStation": true,
    "busBoarding": true,
    "requireClerkApproval": true,
    "integrateWithWorkday": true,
    "integrateWithCW": true
  },
  "businessRules": {
    "minibusCapacity": 20,
    "defaultStationCapacity": 50,
    "needsManualApprovalThreshold": 5,
    "shiftCodes": ["M", "S", "N", "HC1", "HC2", "OT"],
    "timeSlots": ["06:00", "08:00", "14:00", "18:00", "22:00"],
    "boardingStatusTypes": ["PENDING", "ON_BUS", "ABSENT", "DELAYED"],
    "allocationAlgorithm": "simpleDivision"
  },
  "interfaces": {
    "enableTabletApp": true,
    "enableTerminals": true,
    "enableExcelUpload": true,
    "enableNurseryNotification": true
  },
  "countryVariations": {
    "hasPredefinedStations": true,
    "requiresAddressValidation": true,
    "allowsAddressChangeRequests": true
  },
  "validationRules": {
    "stationRules": {
      "name": { "required": true, "maxLength": 100 },
      "address": { "required": true },
      "routeCode": { "required": true }
    },
    "employeeRules": {
      "badgeId": { "required": true, "unique": true }
    },
    "planRules": {
      "date": { "required": true, "future": true }
    }
  },
  "integrations": {
    "workday": {
      "enabled": true,
      "apiUrl": "https://workday-api.aptiv.com",
      "apiKey": "encrypted-key-123"
    },
    "mapping": {
      "enabled": true,
      "provider": "google",
      "apiKey": "encrypted-key-456"
    },
    "cwSystem": {
      "enabled": true,
      "apiUrl": "https://cw-api.aptiv.com"
    }
  },
  "createdAt": "2023-06-15T10:00:00Z",
  "updatedAt": "2023-10-01T15:30:00Z",
  "type": "client_configuration"
}
```

#### 4.1.2 Configuration Service API

```
GET /api/configurations/:clientId
PUT /api/configurations/:clientId
GET /api/configurations/:clientId/features
GET /api/configurations/:clientId/business-rules
GET /api/configurations/:clientId/validation-rules
```

### 4.2 Enhanced Entity Structure

All domain entities now support client-specific customization through a `customAttributes` field:

```typescript
interface BaseEntity {
  id: UUID;
  clientId: string;
  customAttributes?: Record<string, any>;
  // ... other common fields
}

class PickupStation extends BaseEntity {
  name: string;
  address: Address;
  routeCode: string;
  countryCode: string;
  isActive: boolean;
  capacity?: number;
  // ... other fields
}
```

This allows for dynamic extension of entities without schema changes, supporting diverse client requirements.

### 4.3 Dynamic Business Rules Engine

The system implements a rules engine that allows for dynamic business rules configuration:

```typescript
class BusinessRulesEngine {
  rules: Map<string, Rule>;

  evaluateRules(context: RuleContext, entityType: string): boolean {
    // Fetch applicable rules for entity type and country
    const applicableRules = this.getRulesForEntity(
      entityType,
      context.countryCode
    );

    // Evaluate each rule in sequence
    for (const rule of applicableRules) {
      if (!rule.evaluate(context)) {
        return false;
      }
    }

    return true;
  }
}
```

### 4.4 Feature Toggles

The system supports feature toggles to enable/disable features at runtime:

```typescript
class FeatureManager {
  features: Map<string, boolean>;

  isFeatureEnabled(featureName: string, countryCode: string): boolean {
    const key = `${countryCode}.${featureName}`;
    return this.features.get(key) || false;
  }
}
```

## 5. Microservices Implementation

### 5.1 Pickup Station Service

#### 5.1.1 Core Responsibilities

- Station creation and management
- Employee-station assignments
- Address change request workflows
- **Route management and mapping using Azure Maps**
- **Geospatial queries and calculations**
- **Bus GPS tracking and location analytics**

#### 5.1.2 Key Components

**Enhanced Domain Model for Geospatial Support:**

```typescript
// Geospatial Types

enum GeoJSONType {
  POINT = "Point",
  LINESTRING = "LineString",
  POLYGON = "Polygon",
}

interface GeoJSON {
  type: GeoJSONType;
  coordinates: any;
}

class Point implements GeoJSON {
  type: GeoJSONType.POINT;
  coordinates: [number, number]; // [longitude, latitude]
}

class LineString implements GeoJSON {
  type: GeoJSONType.LINESTRING;
  coordinates: [number, number][]; // Array of [longitude, latitude] points
}

class Polygon implements GeoJSON {
  type: GeoJSONType.POLYGON;
  coordinates: [number, number][][]; // Array of LinearRings (closed LineStrings)
}

// Enhanced GpsCoordinates to support GeoJSON
class GpsCoordinates {
  latitude: number;
  longitude: number;

  toGeoJSON(): Point {
    return {
      type: GeoJSONType.POINT,
      coordinates: [this.longitude, this.latitude],
    };
  }
}

// New Route Management Entities

class TransportRoute {
  id: UUID;
  name: string;
  routeCode: string;
  countryCode: string;
  path: LineString;
  stations: UUID[]; // Array of station IDs along this route
  distance: number; // Total distance in meters
  estimatedDuration: number; // Duration in minutes
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

class RouteSegment {
  id: UUID;
  routeId: UUID;
  startStationId: UUID;
  endStationId: UUID;
  path: LineString;
  distance: number; // Distance in meters
  estimatedDuration: number; // Duration in minutes
}

class BusLocation {
  id: UUID;
  busId: string;
  routeId: UUID;
  location: Point;
  timestamp: Date;
  speed: number;
  direction: number; // Heading in degrees
  status: string; // e.g., "IN_TRANSIT", "AT_STATION", "IDLE"
}

// Enhanced PickupStation with geospatial support
class PickupStation {
  id: UUID;
  name: string;
  address: Address;
  routeCode: string;
  countryCode: string;
  isActive: boolean;
  capacity?: number;
  location: Point; // GeoJSON Point
  coverageArea?: Polygon; // Optional area around station for geofencing
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Enhanced Address with geospatial support
class Address {
  street: string;
  city: string;
  region: string;
  country: string;
  postalCode?: string;
  gpsCoordinates?: GpsCoordinates;
  location?: Point; // GeoJSON Point for spatial indexing
}
```

**CQRS Implementation:**

**Commands:**

```typescript
class CreateStationCommand {
  name: string;
  address: Address;
  routeCode: string;
  countryCode: string;
  capacity?: number;
  location: Point;
  coverageArea?: Polygon;
  createdBy: string;
}

class CreateRouteCommand {
  name: string;
  routeCode: string;
  countryCode: string;
  path: LineString;
  stations: UUID[];
  createdBy: string;
}

class UpdateRouteCommand {
  routeId: UUID;
  name?: string;
  path?: LineString;
  stations?: UUID[];
  isActive?: boolean;
  updatedBy: string;
}

class TrackBusLocationCommand {
  busId: string;
  routeId: UUID;
  location: Point;
  speed: number;
  direction: number;
  status: string;
}

class AssignStationCommand {
  employeeId: string;
  stationId: UUID;
  assignedBy: string;
}

class RequestAddressChangeCommand {
  employeeId: string;
  proposedAddress: Address;
  reason?: string;
  requestedBy: string;
}

class ApproveAddressChangeCommand {
  requestId: UUID;
  approvedBy: string;
  approvalType: "CLERK" | "TRANSPORT_AGENT";
}
```

**Queries:**

```typescript
class GetStationsQuery {
  countryCode?: string;
  routeCode?: string;
  isActive?: boolean;
}

class GetEmployeeAssignmentQuery {
  employeeId: string;
}

class GetPendingAddressChangeRequestsQuery {
  approvalType: "CLERK" | "TRANSPORT_AGENT";
  countryCode?: string;
}

// New Geospatial Queries
class GetNearbyStationsQuery {
  location: Point;
  maxDistance: number; // In meters
  countryCode?: string;
}

class GetStationsWithinAreaQuery {
  area: Polygon;
  countryCode?: string;
}

class GetRouteByIdQuery {
  routeId: UUID;
}

class GetRoutesQuery {
  countryCode: string;
  isActive?: boolean;
}

class GetBusLocationQuery {
  busId: string;
}

class GetBusesOnRouteQuery {
  routeId: UUID;
}

class CalculateDistanceQuery {
  fromLocation: Point;
  toLocation: Point;
}
```

**Handlers:**

```typescript
@CommandHandler(CreateStationCommand)
class CreateStationHandler {
  execute(command: CreateStationCommand): Promise<Result<UUID>> {
    // Create station logic
    // Validate address with mapping service
    // Store GeoJSON data
    // Publish StationCreatedEvent
  }
}

@CommandHandler(CreateRouteCommand)
class CreateRouteHandler {
  execute(command: CreateRouteCommand): Promise<Result<UUID>> {
    // Create route logic
    // Validate that stations exist
    // Calculate distances and duration using Azure Maps
    // Generate route segments
    // Publish RouteCreatedEvent
  }
}

@QueryHandler(GetNearbyStationsQuery)
class GetNearbyStationsHandler {
  execute(query: GetNearbyStationsQuery): Promise<PickupStation[]> {
    // Use ST_DISTANCE to find stations within maxDistance meters of location
  }
}

@QueryHandler(GetStationsWithinAreaQuery)
class GetStationsWithinAreaHandler {
  execute(query: GetStationsWithinAreaQuery): Promise<PickupStation[]> {
    // Use ST_WITHIN to find stations within the specified area
  }
}

@QueryHandler(CalculateDistanceQuery)
class CalculateDistanceHandler {
  execute(query: CalculateDistanceQuery): Promise<number> {
    // Use ST_DISTANCE to calculate distance between points
  }
}
```

**Domain Events:**

```typescript
class StationCreatedEvent {
  stationId: UUID;
  name: string;
  address: Address;
  routeCode: string;
  countryCode: string;
  location: Point;
}

class RouteCreatedEvent {
  routeId: UUID;
  name: string;
  routeCode: string;
  countryCode: string;
  path: LineString;
  stations: UUID[];
}

class RouteUpdatedEvent {
  routeId: UUID;
  name?: string;
  path?: LineString;
  stations?: UUID[];
  isActive?: boolean;
}

class BusLocationUpdatedEvent {
  busId: string;
  routeId: UUID;
  location: Point;
  timestamp: Date;
  status: string;
}
```

#### 5.1.3 API Endpoints

```
POST /api/pickup-stations
GET /api/pickup-stations
GET /api/pickup-stations/{id}
PUT /api/pickup-stations/{id}
POST /api/pickup-stations/assignments
POST /api/pickup-stations/address-change-requests
GET /api/pickup-stations/address-change-requests
PUT /api/pickup-stations/address-change-requests/{id}/approve
PUT /api/pickup-stations/address-change-requests/{id}/reject

# New Route Management Endpoints
POST /api/routes
GET /api/routes
GET /api/routes/{id}
PUT /api/routes/{id}
DELETE /api/routes/{id}
GET /api/routes/{id}/stations
PUT /api/routes/{id}/stations
GET /api/routes/search/nearby?lat={latitude}&long={longitude}&distance={meters}
GET /api/routes/{id}/buses

# Bus Location Tracking
POST /api/buses/{id}/location
GET /api/buses/{id}/location
GET /api/buses/{id}/location/history?from={timestamp}&to={timestamp}

# Spatial Analysis
GET /api/spatial/distance?from={station1Id}&to={station2Id}
GET /api/spatial/nearest-station?lat={latitude}&long={longitude}
```

#### 5.1.4 Geospatial Features and Azure Maps Integration

The Pickup Station Service integrates with Azure Maps for route planning and geospatial analysis:

1. **Azure Maps Integration**:

   - Route planning and optimization
   - Address geocoding and validation
   - Distance and travel time calculations

2. **Geospatial Operations**:

   - Calculating distances between stations using ST_DISTANCE
   - Finding stations within a specified area using ST_WITHIN
   - Determining if a bus is at a pickup station using proximity detection
   - Route optimization based on station locations

3. **Location Validation**:
   - Validating check-in locations against station coordinates
   - Ensuring employees board at their assigned stations
   - Configurable proximity thresholds based on country and station type

**Sequence Diagram - Route Creation Flow:**

```mermaid
sequenceDiagram
    actor TransportAgent
    participant APIMgmt as Azure API Management
    participant StationService as Pickup Station Service
    participant AzureMaps as Azure Maps
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus

    TransportAgent->>APIMgmt: Create Route Request
    APIMgmt->>StationService: Forward Request
    StationService->>StationService: Validate Station IDs

    StationService->>DB: Get Station Locations
    DB-->>StationService: Return Locations

    StationService->>AzureMaps: Calculate Route
    AzureMaps-->>StationService: Return Path, Distance, Duration

    StationService->>StationService: Generate Route Segments
    StationService->>DB: Save Route with GeoJSON Path
    DB-->>StationService: Confirm Save

    StationService->>Bus: Publish RouteCreated Event
    Bus-->>StationService: Confirm Publication

    StationService-->>APIMgmt: Return Route ID
    APIMgmt-->>TransportAgent: Return Success Response
```

#### 5.1.5 Station Location Validation System

The system maintains station location data to validate bus terminal check-ins:

```typescript
class StationLocationValidator {
  constructor(
    private stationRepository: StationRepository,
    private spatialService: SpatialService,
    private configService: ConfigurationService
  ) {}

  async validateLocationAtStation(
    checkInLocation: Point,
    stationId: UUID
  ): Promise<ValidationResult> {
    // Get station location data
    const station = await this.stationRepository.findById(stationId);

    if (!station || !station.location) {
      return {
        isValid: false,
        reason: "STATION_LOCATION_UNKNOWN",
      };
    }

    // Get proximity validation threshold from configuration
    const proximityThreshold =
      await this.configService.getStationProximityThreshold(
        station.countryCode
      );

    // Calculate distance using ST_DISTANCE
    const distance = await this.spatialService.calculateDistance(
      checkInLocation,
      station.location
    );

    // Check if within threshold
    const isWithinThreshold = distance <= proximityThreshold;

    // Check if within station's coverage area if defined
    let isWithinCoverageArea = false;
    if (station.coverageArea) {
      isWithinCoverageArea = await this.spatialService.isPointWithinPolygon(
        checkInLocation,
        station.coverageArea
      );
    }

    // Return validation result
    return {
      isValid: isWithinThreshold || isWithinCoverageArea,
      distance: distance,
      threshold: proximityThreshold,
      reason:
        isWithinThreshold || isWithinCoverageArea
          ? undefined
          : "LOCATION_OUTSIDE_STATION_AREA",
    };
  }
}
```

**Station Location Data Storage:**

The system maintains accurate location data for all pickup stations:

```typescript
// Sample station location record
{
  "id": "station-123",
  "name": "North Campus Station",
  "location": {
    "type": "Point",
    "coordinates": [-7.5898, 33.5731]
  },
  "coverageArea": {
    "type": "Polygon",
    "coordinates": [
      [
        [-7.592, 33.571],
        [-7.592, 33.575],
        [-7.588, 33.575],
        [-7.588, 33.571],
        [-7.592, 33.571]
      ]
    ]
  },
  "proximityThreshold": 50, // meters
  "updatedAt": "2023-08-15T14:30:00Z"
}
```

This approach ensures accurate validation of employee check-ins at bus terminals without requiring continuous tracking of bus locations.

#### 5.1.6 Bus Terminal Location Functionality

The bus terminals capture location data during employee check-in:

1. **Terminal Location Capture**:

   - Bus terminals are equipped with GPS capabilities
   - Terminal captures its current location at check-in time
   - Location is sent along with employee badge data for validation

2. **Check-in Validation**:

   - System verifies terminal location is at or near the correct pickup station
   - Validates employee is assigned to that pickup station
   - Records location data with each check-in for audit purposes

3. **Location Data Security**:
   - Location data is only captured during check-in events
   - Data is encrypted in transit and at rest
   - Location history maintained according to retention policies

**API Endpoints for Terminal Location Validation:**

```
POST /api/terminal/validate-location
Body: {
  "terminalId": "string",
  "stationId": "uuid",
  "location": { "type": "Point", "coordinates": [longitude, latitude] }
}

POST /api/boarding/badge-scan
Body: {
  "employeeId": "string",
  "terminalId": "string",
  "location": { "type": "Point", "coordinates": [longitude, latitude] },
  "timestamp": "ISO date string"
}
```

#### 5.1.7 Real-Time Bus Tracking System

**Enhanced Domain Model for Bus Tracking:**

```typescript
class BusTracking {
  id: UUID;
  busId: string;
  routeId: UUID;
  driverId?: string;
  vehicleId?: string;
  currentLocation: Point;
  status: BusStatus;
  speed: number; // km/h
  bearing: number; // degrees
  timestamp: Date;
  nextStationId?: UUID;
  estimatedArrivalTime?: Date;
  distanceToNextStation?: number; // meters
  routeCompletionPercentage?: number; // 0-100
}

enum BusStatus {
  PREPARING = "PREPARING",
  EN_ROUTE = "EN_ROUTE",
  AT_STATION = "AT_STATION",
  COMPLETED = "COMPLETED",
  DELAYED = "DELAYED",
  OUT_OF_SERVICE = "OUT_OF_SERVICE",
}

class RouteProgress {
  routeId: UUID;
  date: Date;
  totalStations: number;
  visitedStations: number;
  totalDistance: number;
  coveredDistance: number;
  status: RouteStatus;
  startTime?: Date;
  estimatedEndTime?: Date;
  actualEndTime?: Date;
}

enum RouteStatus {
  SCHEDULED = "SCHEDULED",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
}
```

**CQRS Implementation:**

**Commands:**

```typescript
class UpdateBusLocationCommand {
  busId: string;
  routeId: UUID;
  location: Point;
  speed: number;
  bearing: number;
  status?: BusStatus;
  timestamp: Date;
}

class StartRouteCommand {
  busId: string;
  routeId: UUID;
  driverId: string;
  vehicleId: string;
  startLocation: Point;
  timestamp: Date;
}

class CompleteRouteCommand {
  busId: string;
  routeId: UUID;
  endLocation: Point;
  timestamp: Date;
}

class ArriveAtStationCommand {
  busId: string;
  routeId: UUID;
  stationId: UUID;
  arrivalTime: Date;
}

class DepartFromStationCommand {
  busId: string;
  routeId: UUID;
  stationId: UUID;
  departureTime: Date;
}
```

**Handlers:**

```typescript
@CommandHandler(UpdateBusLocationCommand)
class UpdateBusLocationHandler {
  constructor(
    private busTrackingRepository: BusTrackingRepository,
    private routeRepository: RouteRepository,
    private stationRepository: StationRepository,
    private spatialService: SpatialService,
    private eventBus: EventBus
  ) {}

  async execute(command: UpdateBusLocationCommand): Promise<Result<void>> {
    // 1. Get route information
    const route = await this.routeRepository.findById(command.routeId);
    if (!route) {
      return Result.fail("Route not found");
    }

    // 2. Find nearest position on route path
    const routePosition = await this.spatialService.findNearestPositionOnRoute(
      command.location,
      route.path
    );

    // 3. Calculate route completion percentage
    const completionPercentage =
      await this.spatialService.calculateRouteCompletionPercentage(
        routePosition,
        route.path
      );

    // 4. Find next station on route
    const nextStation = await this.findNextStationOnRoute(
      route,
      completionPercentage
    );

    // 5. Calculate distance to next station and ETA
    let distanceToNext = 0;
    let estimatedArrival = null;

    if (nextStation) {
      distanceToNext = await this.spatialService.calculateDistanceAlongRoute(
        command.location,
        nextStation.location,
        route.path
      );

      // Calculate ETA based on current speed and distance
      if (command.speed > 0) {
        const timeInHours = distanceToNext / 1000 / command.speed;
        estimatedArrival = new Date(
          command.timestamp.getTime() + timeInHours * 60 * 60 * 1000
        );
      }
    }

    // 6. Check if bus is at a station
    let status = command.status || BusStatus.EN_ROUTE;
    if (nextStation) {
      const distanceToStation = await this.spatialService.calculateDistance(
        command.location,
        nextStation.location
      );

      // If within 50 meters of a station and not already marked as AT_STATION
      if (distanceToStation <= 50 && status !== BusStatus.AT_STATION) {
        status = BusStatus.AT_STATION;

        // Publish station arrival event
        await this.eventBus.publish(
          new BusArrivedAtStationEvent({
            busId: command.busId,
            routeId: command.routeId,
            stationId: nextStation.id,
            arrivalTime: command.timestamp,
          })
        );
      }
      // If moving away from a station that was previously AT_STATION
      else if (distanceToStation > 50 && status === BusStatus.AT_STATION) {
        status = BusStatus.EN_ROUTE;

        // Publish station departure event
        await this.eventBus.publish(
          new BusDepartedFromStationEvent({
            busId: command.busId,
            routeId: command.routeId,
            stationId: nextStation.id,
            departureTime: command.timestamp,
          })
        );
      }
    }

    // 7. Update bus tracking record
    const tracking = {
      id: UUID.generate(),
      busId: command.busId,
      routeId: command.routeId,
      currentLocation: command.location,
      status: status,
      speed: command.speed,
      bearing: command.bearing,
      timestamp: command.timestamp,
      nextStationId: nextStation?.id,
      estimatedArrivalTime: estimatedArrival,
      distanceToNextStation: distanceToNext,
      routeCompletionPercentage: completionPercentage,
    };

    await this.busTrackingRepository.save(tracking);

    // 8. Publish bus location updated event
    await this.eventBus.publish(
      new BusLocationUpdatedEvent({
        busId: command.busId,
        routeId: command.routeId,
        location: command.location,
        status: status,
        speed: command.speed,
        bearing: command.bearing,
        timestamp: command.timestamp,
        nextStationId: nextStation?.id,
        estimatedArrivalTime: estimatedArrival,
        distanceToNextStation: distanceToNext,
        routeCompletionPercentage: completionPercentage,
      })
    );

    return Result.ok();
  }

  private async findNextStationOnRoute(
    route: TransportRoute,
    completionPercentage: number
  ): Promise<PickupStation | null> {
    // Find the next station on the route based on completion percentage
    // Implementation depends on how stations are ordered on the route
    const stations = await this.stationRepository.findByIds(route.stations);
    // Sort stations by their order on the route
    // Return the next station the bus will arrive at
    // ...
  }
}
```

**Queries:**

```typescript
class GetBusLocationQuery {
  busId: string;
}

class GetActiveBusesOnRouteQuery {
  routeId: UUID;
}

class GetRouteProgressQuery {
  routeId: UUID;
  date: Date;
}

class GetBusLocationHistoryQuery {
  busId: string;
  startTime: Date;
  endTime: Date;
}

@QueryHandler(GetActiveBusesOnRouteQuery)
class GetActiveBusesOnRouteHandler {
  constructor(private busTrackingRepository: BusTrackingRepository) {}

  async execute(query: GetActiveBusesOnRouteQuery): Promise<BusTracking[]> {
    return this.busTrackingRepository.findActiveByRouteId(query.routeId);
  }
}
```

**Domain Events:**

```typescript
class BusLocationUpdatedEvent {
  busId: string;
  routeId: UUID;
  location: Point;
  status: BusStatus;
  speed: number;
  bearing: number;
  timestamp: Date;
  nextStationId?: UUID;
  estimatedArrivalTime?: Date;
  distanceToNextStation?: number;
  routeCompletionPercentage?: number;
}

class BusArrivedAtStationEvent {
  busId: string;
  routeId: UUID;
  stationId: UUID;
  arrivalTime: Date;
}

class BusDepartedFromStationEvent {
  busId: string;
  routeId: UUID;
  stationId: UUID;
  departureTime: Date;
}

class RouteStartedEvent {
  busId: string;
  routeId: UUID;
  driverId: string;
  vehicleId: string;
  startTime: Date;
  startLocation: Point;
}

class RouteCompletedEvent {
  busId: string;
  routeId: UUID;
  endTime: Date;
  endLocation: Point;
  totalDuration: number; // minutes
  totalDistance: number; // meters
}
```

**Enhanced API Endpoints:**

```
# Bus Tracking APIs
POST /api/buses/{busId}/location
Body: {
  "routeId": "uuid",
  "location": { "type": "Point", "coordinates": [longitude, latitude] },
  "speed": number,
  "bearing": number,
  "timestamp": "ISO date string"
}

GET /api/buses/{busId}/location
GET /api/buses/{busId}/history?from={timestamp}&to={timestamp}
GET /api/routes/{routeId}/buses
GET /api/routes/{routeId}/progress?date={ISO date}

# Geofencing APIs
POST /api/stations/{stationId}/geofence
GET /api/buses/{busId}/inside-station/{stationId}
```

#### 5.1.8 Real-Time Visualization Dashboard

The system provides a real-time visualization dashboard for monitoring bus locations and route progress:

1. **Real-Time Map Display**:

   - Shows current bus positions on the route
   - Displays pickup stations with status indicators
   - Updates every 5-10 seconds with WebSocket connection

2. **Route Analytics**:

   - Route completion percentage
   - Estimated time of arrival at next station
   - Bus speed and status monitoring
   - Historical route performance comparison

3. **Employee Boarding Status**:

   - Summary of employees boarded
   - Employees yet to board at each station
   - Check-in compliance statistics

4. **WebSocket Implementation**:

```typescript
class DashboardSocketService {
  constructor(
    private busTrackingRepository: BusTrackingRepository,
    private eventBus: EventBus
  ) {
    // Subscribe to relevant events
    this.eventBus.subscribe(
      BusLocationUpdatedEvent,
      this.handleBusLocationUpdate
    );
    this.eventBus.subscribe(PassengerBoardedEvent, this.handlePassengerBoarded);
    this.eventBus.subscribe(RouteStartedEvent, this.handleRouteStarted);
    this.eventBus.subscribe(RouteCompletedEvent, this.handleRouteCompleted);
  }

  // Broadcast updates to connected clients
  private handleBusLocationUpdate = (event: BusLocationUpdatedEvent) => {
    this.broadcast(`bus:${event.busId}:location`, {
      busId: event.busId,
      routeId: event.routeId,
      location: event.location,
      status: event.status,
      timestamp: event.timestamp,
      nextStation: event.nextStationId,
      eta: event.estimatedArrivalTime,
      completion: event.routeCompletionPercentage,
    });
  };

  // Handle other events...

  private broadcast(channel: string, data: any): void {
    // Use WebSockets to broadcast to subscribed clients
  }

  // Client subscription handler
  public handleClientSubscription(client: WebSocket, channels: string[]): void {
    // Subscribe client to requested channels
  }
}
```

**Sequence Diagram - Real-Time Bus Tracking Flow:**

```mermaid
sequenceDiagram
    participant Bus as Bus GPS Device
    participant API as API Management
    participant TrackingService as Bus Tracking Service
    participant SpatialService as Spatial Service
    participant DB as Cosmos DB
    participant EventBus as Event Bus
    participant Dashboard as Real-Time Dashboard
    participant Mobile as Mobile Apps

    Bus->>API: Send Location Update
    API->>TrackingService: Process Location Update

    TrackingService->>SpatialService: Find Position on Route
    SpatialService->>SpatialService: Calculate Route Progress
    SpatialService-->>TrackingService: Return Progress Information

    TrackingService->>SpatialService: Calculate Distance to Next Station
    SpatialService->>DB: Execute ST_DISTANCE Query
    DB-->>SpatialService: Return Distance
    SpatialService-->>TrackingService: Return Distance and ETA

    TrackingService->>DB: Save Tracking Record
    DB-->>TrackingService: Confirm Save

    TrackingService->>EventBus: Publish BusLocationUpdatedEvent

    EventBus-->>Dashboard: Push Update to Visualization
    EventBus-->>Mobile: Push Notification to Apps

    alt Bus Near Station
        TrackingService->>EventBus: Publish BusArrivedAtStationEvent
        EventBus-->>Dashboard: Update Station Status
        EventBus-->>Mobile: Send Arrival Notification
    end

    TrackingService-->>API: Confirm Update Processed
    API-->>Bus: Acknowledge Update
```

#### 5.1.9 Integration with Employee Check-In Validation

The bus tracking system integrates with employee check-in validation to provide a comprehensive solution:

1. **Automated Station Validation**:

   - System automatically validates if an employee is boarding at the correct station
   - Compares employee's check-in location with bus location
   - Ensures employees only board buses assigned to their route

2. **Proximity-Based Validation**:

   - As buses approach stations, the system preloads expected passenger lists
   - Check-ins are validated against the bus's current location
   - Terminal displays only show employees expected at that station

3. **Real-Time Synchronization**:
   - Bus arrival/departure events trigger station terminal status updates
   - Employees can only check in when a bus is present at the station
   - Check-in window configurable (e.g., 10 minutes before/after bus arrival)

```typescript
class IntegratedValidationService {
  constructor(
    private busTrackingRepository: BusTrackingRepository,
    private stationRepository: StationRepository,
    private employeeAssignmentRepository: EmployeeAssignmentRepository,
    private spatialService: SpatialService,
    private configService: ConfigurationService
  ) {}

  async validateCheckInWithBusPresence(
    employeeId: string,
    stationId: UUID,
    checkInLocation: Point
  ): Promise<ValidationResult> {
    // 1. Validate employee is assigned to this station
    const assignment =
      await this.employeeAssignmentRepository.findByEmployeeAndStation(
        employeeId,
        stationId
      );

    if (!assignment) {
      return {
        isValid: false,
        reason: "EMPLOYEE_NOT_ASSIGNED_TO_STATION",
      };
    }

    // 2. Get station details
    const station = await this.stationRepository.findById(stationId);

    // 3. Check if any bus is currently at the station
    const activeBuses = await this.busTrackingRepository.findByStationProximity(
      stationId,
      50 // 50 meters radius
    );

    if (activeBuses.length === 0) {
      return {
        isValid: false,
        reason: "NO_BUS_AT_STATION",
      };
    }

    // 4. Check if employee's route matches any of the buses at the station
    const matchingBus = activeBuses.find(
      (bus) => bus.routeId === station.routeCode
    );

    if (!matchingBus) {
      return {
        isValid: false,
        reason: "NO_MATCHING_BUS_ROUTE",
      };
    }

    // 5. Validate employee's check-in location against station location
    const locationValid = await this.validateCheckInLocation(
      checkInLocation,
      station.location
    );

    return {
      isValid: locationValid.isValid,
      reason: locationValid.isValid ? undefined : locationValid.reason,
      busId: matchingBus.busId,
      routeId: matchingBus.routeId,
    };
  }

  private async validateCheckInLocation(
    checkInLocation: Point,
    stationLocation: Point
  ): Promise<{ isValid: boolean; reason?: string }> {
    // Implementation similar to CheckInValidator
    // ...
  }
}
```

#### 5.1.7 Station Proximity Validation

The system validates if a bus is at the proper station during employee check-in:

```typescript
/**
 * Validates if a bus is at the correct station during employee check-in
 */
class StationProximityValidator {
  constructor(
    private stationRepository: StationRepository,
    private spatialService: SpatialService,
    private configService: ConfigurationService
  ) {}

  /**
   * Checks if a bus terminal is at the specified station based on its GPS coordinates
   * @param terminalLocation Current GPS location of the bus terminal
   * @param stationId ID of the station to validate against
   * @returns Validation result with distance information
   */
  async isBusAtStation(
    terminalLocation: Point,
    stationId: string
  ): Promise<ValidationResult> {
    // Get station details
    const station = await this.stationRepository.findById(stationId);

    if (!station || !station.location) {
      return {
        isValid: false,
        reason: "STATION_LOCATION_UNKNOWN",
      };
    }

    // Get the configured proximity threshold for this country/station
    const proximityThreshold =
      await this.configService.getStationProximityThreshold(
        station.countryCode
      );

    // Calculate distance between terminal and station
    const distance = await this.spatialService.calculateDistance(
      terminalLocation,
      station.location
    );

    // Check if within the allowed threshold
    const isWithinThreshold = distance <= proximityThreshold;

    return {
      isValid: isWithinThreshold,
      distance: distance,
      threshold: proximityThreshold,
      reason: isWithinThreshold ? undefined : "BUS_NOT_AT_STATION",
    };
  }
}
```

This function is used during employee check-in to validate that the bus is at the correct station, using the terminal's GPS coordinates captured at the moment of check-in.

#### 5.1.8 Employee Check-In Location Validation

The system validates employee check-ins based on their assigned station:

```typescript
/**
 * Validates employee check-in against their assigned station
 */
class EmployeeStationValidator {
  constructor(
    private employeeAssignmentRepository: EmployeeAssignmentRepository,
    private stationRepository: StationRepository,
    private spatialService: SpatialService,
    private configService: ConfigurationService
  ) {}

  /**
   * Validates if an employee is checking in at their assigned station
   * @param employeeId Employee identifier
   * @param checkInLocation Location where check-in is happening
   * @returns Validation result with details
   */
  async validateEmployeeLocation(
    employeeId: string,
    checkInLocation: Point
  ): Promise<ValidationResult> {
    // Get employee's assigned station
    const assignment =
      await this.employeeAssignmentRepository.findByEmployeeId(employeeId);

    if (!assignment) {
      return {
        isValid: false,
        reason: "EMPLOYEE_NOT_ASSIGNED",
      };
    }

    // Get the assigned station details
    const station = await this.stationRepository.findById(assignment.stationId);

    if (!station || !station.location) {
      return {
        isValid: false,
        reason: "ASSIGNED_STATION_UNKNOWN",
      };
    }

    // Get maximum allowed distance from configuration
    const maxAllowedDistance = await this.configService.getMaxCheckInDistance(
      station.countryCode
    );

    // Calculate distance to assigned station
    const distance = await this.spatialService.calculateDistance(
      checkInLocation,
      station.location
    );

    // Check if within allowed distance
    const isWithinAllowedDistance = distance <= maxAllowedDistance;

    return {
      isValid: isWithinAllowedDistance,
      distance: distance,
      threshold: maxAllowedDistance,
      stationId: station.id,
      stationName: station.name,
      reason: isWithinAllowedDistance ? undefined : "NOT_AT_ASSIGNED_STATION",
    };
  }
}
```

#### 5.1.9 Integrated Check-In Validation

For employee check-ins at bus terminals, the system performs an integrated validation:

```typescript
/**
 * Integrates bus location and employee assignment validation for check-ins
 */
class IntegratedCheckInValidator {
  constructor(
    private stationProximityValidator: StationProximityValidator,
    private employeeStationValidator: EmployeeStationValidator,
    private employeeAssignmentRepository: EmployeeAssignmentRepository
  ) {}

  /**
   * Validates that an employee is checking in at their assigned station and
   * the bus is at the correct location
   * @param employeeId Employee identifier
   * @param checkInLocation Location from bus terminal GPS
   * @returns Validation result with detailed information
   */
  async validateCheckIn(
    employeeId: string,
    checkInLocation: Point
  ): Promise<IntegratedValidationResult> {
    // First validate that the employee is assigned correctly
    const employeeValidation =
      await this.employeeStationValidator.validateEmployeeLocation(
        employeeId,
        checkInLocation
      );

    if (!employeeValidation.isValid) {
      return {
        isValid: false,
        reason: employeeValidation.reason,
        employeeValidation: employeeValidation,
        busValidation: null,
      };
    }

    // Then validate that the bus is at the correct station
    const busValidation = await this.stationProximityValidator.isBusAtStation(
      checkInLocation,
      employeeValidation.stationId
    );

    // The check-in is only valid if both validations pass
    return {
      isValid: busValidation.isValid,
      reason: busValidation.isValid ? undefined : busValidation.reason,
      employeeValidation: employeeValidation,
      busValidation: busValidation,
      stationId: employeeValidation.stationId,
      stationName: employeeValidation.stationName,
    };
  }
}
```

This unified approach ensures that:

1. The employee is checking in at their assigned station
2. The bus terminal is physically at the correct station
3. Location is only captured at the point of check-in, not continuously

### Bus Location for Station Validation API

```typescript
interface BusLocationData {
  busId: string;
  routeId: string;
  terminalId: string;
  location: Point; // GeoJSON Point
  timestamp: Date;
}

// Simple validation endpoint to check if a bus is at a station
GET /api/validation/bus-at-station
Query parameters:
  - location: coordinates as "lat,long"
  - stationId: UUID of station to check
```

This simplified approach focuses exclusively on validating location at check-in time, eliminating continuous tracking while maintaining the core validation functionality.

#### 5.1.10 Simplified Bus Location Tracking

To support pickup station validation, the system implements a streamlined bus location tracking mechanism:

1. **GPS Tracking Integration**:

   - Fixed GPS devices installed in each bus
   - Automatic location transmission at configurable intervals
   - Low-bandwidth data transmission with just essential location data

2. **Bus Location Data Structure**:

   ```typescript
   interface BusLocationData {
     busId: string;
     routeId: string;
     location: Point; // GeoJSON Point
     timestamp: Date;
     speed?: number; // Optional speed data in km/h
   }
   ```

3. **Location Validation API**:

   ```
   GET /api/validation/bus-at-station?busId={busId}&stationId={stationId}
   POST /api/buses/{busId}/location-update
   Body: {
     "location": { "type": "Point", "coordinates": [longitude, latitude] },
     "timestamp": "ISO date string"
   }
   ```

4. **Station Proximity Detection**:

   ```typescript
   class StationProximityChecker {
     constructor(
       private stationRepository: StationRepository,
       private spatialService: SpatialService
     ) {}

     async isBusAtStation(
       busLocation: Point,
       stationId: string
     ): Promise<boolean> {
       // Get station details
       const station = await this.stationRepository.findById(stationId);

       if (!station || !station.location) {
         return false;
       }

       // Calculate distance using ST_DISTANCE in Cosmos DB
       const distance = await this.spatialService.calculateDistance(
         busLocation,
         station.location
       );

       // Check if within proximity threshold (configurable)
       const proximityThreshold = 50; // meters
       return distance <= proximityThreshold;
     }
   }
   ```

This simplified approach focuses exclusively on using bus location data for station validation purposes, eliminating the need for complex mobile applications while maintaining the core functionality needed for employee check-in validation.

### 5.2 Transport Planning Service

#### 5.2.1 Core Responsibilities

- Manage transport plans
- Handle shift assignments
- Calculate and adjust minibus allocations

#### 5.2.2 Key Components

**CQRS Implementation:**

**Commands:**

```typescript
class CreateTransportPlanCommand {
  date: Date;
  countryCode: string;
  createdBy: string;
}

class AddShiftAssignmentCommand {
  planId: UUID;
  employeeId: string;
  stationId: UUID;
  shiftCode: ShiftCode;
  routeCode: string;
}

class UpdateMinibusAllocationCommand {
  planId: UUID;
  timeSlot: string;
  minibusCount: number;
  isManuallyAdjusted: boolean;
  adjustmentReason?: string;
  updatedBy: string;
}

class PublishTransportPlanCommand {
  planId: UUID;
  publishedBy: string;
}
```

**Queries:**

```typescript
class GetTransportPlanQuery {
  planId: UUID;
}

class GetTransportPlansByDateQuery {
  date: Date;
  countryCode: string;
}

class GetMinibusAllocationsQuery {
  planId: UUID;
}

class GetEmployeeScheduleQuery {
  employeeId: string;
  fromDate: Date;
  toDate: Date;
}
```

**Handlers:**

```typescript
@CommandHandler(UpdateMinibusAllocationCommand)
class UpdateMinibusAllocationHandler {
  execute(command: UpdateMinibusAllocationCommand): Promise<Result<boolean>> {
    // Update minibus allocation
    // Recalculate if not manually adjusted
    // Publish MinibusAllocationUpdatedEvent
  }
}

@QueryHandler(GetTransportPlansByDateQuery)
class GetTransportPlansByDateHandler {
  execute(query: GetTransportPlansByDateQuery): Promise<TransportPlan[]> {
    // Query plans by date and country
  }
}
```

**Domain Events:**

```typescript
class TransportPlanCreatedEvent {
  planId: UUID;
  date: Date;
  countryCode: string;
}

class ShiftAssignmentAddedEvent {
  planId: UUID;
  employeeId: string;
  shiftCode: ShiftCode;
  stationId: UUID;
  routeCode: string;
}

class MinibusAllocationUpdatedEvent {
  planId: UUID;
  timeSlot: string;
  employeeCount: number;
  minibusCount: number;
  isManuallyAdjusted: boolean;
}

class TransportPlanPublishedEvent {
  planId: UUID;
  date: Date;
  countryCode: string;
}
```

**Sequence Diagram - Transport Plan Creation:**

```mermaid
sequenceDiagram
    actor ShiftLeader
    participant WebApp as Web Interface
    participant APIMgmt as Azure API Management
    participant PlanService as Transport Planning Service
    participant StationService as Pickup Station Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus

    ShiftLeader->>WebApp: Create Transport Plan
    WebApp->>APIMgmt: POST /transport-plans
    APIMgmt->>PlanService: Forward Request

    PlanService->>StationService: Get Stations Data
    StationService-->>PlanService: Return Stations

    PlanService->>DB: Create Plan (Status: DRAFT)
    DB-->>PlanService: Confirm Save

    ShiftLeader->>WebApp: Add Shift Assignments
    WebApp->>APIMgmt: POST /transport-plans/{id}/assignments
    APIMgmt->>PlanService: Forward Request

    PlanService->>PlanService: Calculate Employee Counts
    PlanService->>PlanService: Calculate Minibus Allocations

    PlanService->>DB: Save Assignments & Allocations
    DB-->>PlanService: Confirm Save

    ShiftLeader->>WebApp: Publish Plan
    WebApp->>APIMgmt: POST /transport-plans/{id}/publish
    APIMgmt->>PlanService: Forward Request

    PlanService->>DB: Update Status to PUBLISHED
    DB-->>PlanService: Confirm Update

    PlanService->>Bus: Publish TransportPlanPublished Event
    Bus-->>PlanService: Confirm Publication

    PlanService-->>APIMgmt: Return Success
    APIMgmt-->>WebApp: Return Response
    WebApp-->>ShiftLeader: Show Confirmation
```

### 5.3 Bus Boarding Service

#### 5.3.1 Core Responsibilities

- Manage the bus boarding process
- Track employee presence and status
- Handle badge scanning and manual check-ins
- Support special cases (forgotten badges, exceptions)
- **Validate employee location during check-in**
- **Ensure employees board at their assigned stations**
- **Verify bus is at the correct pickup location**

#### 5.3.2 Key Components

**Enhanced Domain Model:**

```typescript
// Enhanced BoardingRecord with geolocation
class BoardingRecord {
  id: UUID;
  employeeId: string;
  shiftCode: ShiftCode;
  date: Date;
  stationId: UUID;
  routeCode: string;
  status: BoardingStatus;
  checkInMethod: CheckInMethod;
  nurseryNotification: boolean;
  checkInLocation?: Point; // GeoJSON Point capturing check-in location
  distanceFromStation?: number; // Distance in meters from assigned station
  locationValidated: boolean; // Whether location was validated
  locationOverrideReason?: string; // Reason if location check was overridden
  registrationNumber?: string; // For manual entry
  exceptionReason?: string; // Documents reason for exception
  exceptionType?: string; // Categorizes the exception
  checkInTime?: Date;
  updatedBy?: string;
  updateTimestamp: Date;
}

// Location validation result
class LocationValidationResult {
  isValid: boolean;
  distance?: number; // Distance from assigned station
  maximumAllowedDistance?: number;
  reason?: string; // If invalid, why
}

// Enhanced validation result for on-bus check-in
class BusCheckInValidationResult {
  isValid: boolean;
  reason?: string;
  actualDistance?: number;
  maxAllowedDistance?: number;
  busId?: string;
  routeId?: string;
}

// Check-in validator specifically for on-bus terminals
class BusCheckInValidator {
  constructor(
    private employeeAssignmentRepository: EmployeeAssignmentRepository,
    private stationRepository: StationRepository,
    private routeRepository: RouteRepository,
    private spatialService: SpatialService,
    private configService: ConfigurationService
  ) {}

  async validateBusCheckIn(
    employeeId: string,
    currentLocation: Point,
    shiftCode: ShiftCode,
    date: Date
  ): Promise<BusCheckInValidationResult> {
    // 1. Get employee's assigned station
    const assignment =
      await this.employeeAssignmentRepository.findByEmployeeAndDate(
        employeeId,
        date
      );

    if (!assignment) {
      return { isValid: false, reason: "EMPLOYEE_NOT_ASSIGNED" };
    }

    // 2. Get the assigned station location
    const assignedStation = await this.stationRepository.findById(
      assignment.stationId
    );

    // 3. Get bus route information
    const route = await this.routeRepository.findByRouteCode(
      assignedStation.routeCode
    );

    // 4. Verify bus is at the correct station on its route
    const distanceToAssignedStation =
      await this.spatialService.calculateDistance(
        currentLocation,
        assignedStation.location
      );

    // 5. Get configuration for maximum allowed distance
    const maxDistanceThreshold =
      await this.configService.getBusStationProximityThreshold(
        assignedStation.countryCode
      );

    // 6. Check if within acceptable distance threshold
    if (distanceToAssignedStation > maxDistanceThreshold) {
      return {
        isValid: false,
        reason: "BUS_NOT_AT_CORRECT_STATION",
        actualDistance: distanceToAssignedStation,
        maxAllowedDistance: maxDistanceThreshold,
      };
    }

    // 7. Verify assigned shift matches the provided shift
    if (assignment.shiftCode !== shiftCode) {
      return {
        isValid: false,
        reason: "WRONG_SHIFT_CODE",
      };
    }

    return {
      isValid: true,
      actualDistance: distanceToAssignedStation,
      maxAllowedDistance: maxDistanceThreshold,
      busId: route.activeBusId,
      routeId: route.id,
    };
  }
}
```

**CQRS Implementation:**

**Commands:**

```typescript
class CheckInPassengerCommand {
  employeeId: string;
  shiftCode: ShiftCode;
  date: Date;
  checkInMethod: CheckInMethod;
  checkInLocation: Point; // GPS location from bus terminal
  nurseryNotification: boolean;
  checkedInBy: string;
  registrationNumber?: string; // Optional for manual entry
  exceptionReason?: string; // For documenting exceptions
  bypassLocationValidation?: boolean; // For Team Leaders only
  bypassReason?: string; // Required if bypassing location validation
}

class UpdateBoardingStatusCommand {
  boardingId: UUID;
  status: BoardingStatus;
  updatedBy: string;
  updatedLocation?: Point; // Location where update was performed
}
```

**Handlers:**

```typescript
@CommandHandler(CheckInPassengerCommand)
class CheckInPassengerHandler {
  constructor(
    private stationRepository: StationRepository,
    private employeeAssignmentRepository: EmployeeAssignmentRepository,
    private boardingRecordRepository: BoardingRecordRepository,
    private busCheckInValidator: BusCheckInValidator,
    private configService: ConfigurationService,
    private eventBus: EventBus
  ) {}

  async execute(command: CheckInPassengerCommand): Promise<Result<UUID>> {
    // 1. Validate bus location and employee assignment
    let validationResult: BusCheckInValidationResult = { isValid: true };
    let distanceFromStation: number | undefined;

    if (command.checkInLocation) {
      validationResult = await this.busCheckInValidator.validateBusCheckIn(
        command.employeeId,
        command.checkInLocation,
        command.shiftCode,
        command.date
      );

      distanceFromStation = validationResult.actualDistance;

      // 2. Handle location mismatch based on rules
      if (!validationResult.isValid && !command.bypassLocationValidation) {
        // Check if user has override privileges for bypassing
        if (command.checkInMethod !== CheckInMethod.TL_UPDATE) {
          return Result.fail(
            `Location validation failed: ${validationResult.reason}`
          );
        }
      }
    }

    // 3. Get employee assignment to retrieve station ID
    const assignment =
      await this.employeeAssignmentRepository.findByEmployeeAndDate(
        command.employeeId,
        command.date
      );

    if (!assignment) {
      return Result.fail("Employee not assigned to any station for this date");
    }

    // 4. Get assigned station
    const assignedStation = await this.stationRepository.findById(
      assignment.stationId
    );

    // 5. Create boarding record
    const boardingRecord: BoardingRecord = {
      id: UUID.generate(),
      employeeId: command.employeeId,
      shiftCode: command.shiftCode,
      date: command.date,
      stationId: assignment.stationId,
      routeCode: assignedStation.routeCode,
      status: BoardingStatus.ON_BUS,
      checkInMethod: command.checkInMethod,
      nurseryNotification: command.nurseryNotification,
      checkInLocation: command.checkInLocation,
      distanceFromStation: distanceFromStation,
      locationValidated: validationResult.isValid,
      locationOverrideReason: command.bypassLocationValidation
        ? command.bypassReason
        : undefined,
      registrationNumber: command.registrationNumber,
      exceptionReason:
        command.exceptionReason ||
        (!validationResult.isValid ? validationResult.reason : undefined),
      exceptionType: !validationResult.isValid
        ? "LOCATION_VALIDATION_FAILURE"
        : command.checkInMethod === CheckInMethod.MANUAL
          ? "MANUAL_CHECK_IN"
          : undefined,
      checkInTime: new Date(),
      updatedBy: command.checkedInBy,
      updateTimestamp: new Date(),
    };

    // 6. Save the boarding record
    await this.boardingRecordRepository.save(boardingRecord);

    // 7. Publish events
    await this.eventBus.publish(
      new PassengerBoardedEvent({
        boardingId: boardingRecord.id,
        employeeId: command.employeeId,
        stationId: assignment.stationId,
        shiftCode: command.shiftCode,
        date: command.date,
        checkInMethod: command.checkInMethod,
        checkInLocation: command.checkInLocation,
        locationValidated: validationResult.isValid,
        nurseryNotification: command.nurseryNotification,
        exceptionLogged: !!command.exceptionReason || !validationResult.isValid,
      })
    );

    if (command.nurseryNotification) {
      await this.eventBus.publish(
        new NurseryNotificationEvent({
          employeeId: command.employeeId,
          boardingId: boardingRecord.id,
          timestamp: new Date(),
        })
      );
    }

    return Result.ok(boardingRecord.id);
  }
}
```

**Enhanced API Endpoints:**

```
POST /api/boarding/badge-scan
Body: {
  "employeeId": "string",
  "shiftCode": "string",
  "checkInLocation": { "type": "Point", "coordinates": [longitude, latitude] },
  "nurseryNotification": boolean
}

POST /api/boarding/manual-checkin
Body: {
  "employeeId": "string",
  "shiftCode": "string",
  "registrationNumber": "string",
  "exceptionReason": "string",
  "checkInLocation": { "type": "Point", "coordinates": [longitude, latitude] },
  "nurseryNotification": boolean
}

POST /api/boarding/team-leader-update
Body: {
  "employeeId": "string",
  "shiftCode": "string",
  "status": "string",
  "checkInLocation": { "type": "Point", "coordinates": [longitude, latitude] },
  "bypassLocationValidation": boolean,
  "bypassReason": "string"
}

GET /api/boarding/validate-location?lat=number&long=number&employeeId=string
```

#### 5.3.3 On-Bus Terminal Check-in Process

**Badge Scanning Inside Bus:**

The system is designed with terminals installed on buses, not at stations. This approach ensures:

1. **Location Validation at Check-in Time:**

   - When an employee scans their badge, the system captures the bus's current GPS location
   - This single location represents both the bus and employee position simultaneously

2. **Dual Validation:**

   - System verifies the bus is physically at the correct pickup station on its route
   - System confirms this is the right station for this specific employee

3. **Critical Features:**
   - Terminals are equipped with GPS capabilities to determine current location
   - Check-in is only valid when the bus is at or near the expected pickup station
   - Location proximity is configurable by country and station
   - Team Leaders can bypass location validation in exceptional circumstances

**Sequence Diagram - On-Bus Badge Scanning and Location Validation:**

```mermaid
sequenceDiagram
    actor Employee
    participant BusTerminal as Bus Terminal
    participant APIMgmt as API Management
    participant BoardingService as Bus Boarding Service
    participant BusValidator as Bus Check-In Validator
    participant AssignmentRepo as Employee Assignment Repository
    participant StationRepo as Station Repository
    participant SpatialService as Spatial Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus
    participant TLTablet as Team Leader Tablet

    Employee->>BusTerminal: Scan Badge Inside Bus
    BusTerminal->>BusTerminal: Get Current GPS Coordinates
    BusTerminal->>APIMgmt: POST /boarding/badge-scan with location
    APIMgmt->>BoardingService: Forward Request

    BoardingService->>BusValidator: validateBusCheckIn(employeeId, location)

    BusValidator->>AssignmentRepo: Find Employee Assignment
    AssignmentRepo->>DB: Query Assignment
    DB-->>AssignmentRepo: Return Assignment
    AssignmentRepo-->>BusValidator: Return Assignment

    BusValidator->>StationRepo: Get Assigned Station
    StationRepo->>DB: Query Station
    DB-->>StationRepo: Return Station
    StationRepo-->>BusValidator: Return Station

    BusValidator->>SpatialService: Calculate Distance to Station
    SpatialService->>DB: Execute ST_DISTANCE Query
    DB-->>SpatialService: Return Distance
    SpatialService-->>BusValidator: Return Distance

    BusValidator->>BusValidator: Apply Distance Threshold Rules
    BusValidator-->>BoardingService: Return Validation Result

    alt Location Valid
        BoardingService->>DB: Create Boarding Record (status: ON_BUS)
        DB-->>BoardingService: Confirm Save

        BoardingService->>Bus: Publish PassengerBoarded Event
        Bus-->>BoardingService: Confirm Publication
        Bus-->>TLTablet: Update Status (ON_BUS)

        BoardingService-->>APIMgmt: Return Success
        APIMgmt-->>BusTerminal: Confirm Boarding
        BusTerminal-->>Employee: Show Success Message
    else Location Invalid
        BoardingService-->>APIMgmt: Return Location Error
        APIMgmt-->>BusTerminal: Show Error Details
        BusTerminal-->>Employee: Show Error Message

        Note over BusTerminal,TLTablet: Team Leader may override
    end
```

**Example Validation Scenarios:**

1. **Successful Check-in:**

   - Bus is at the employee's assigned pickup station
   - GPS coordinates are within the configured proximity threshold
   - Check-in is recorded with status ON_BUS

2. **Failed Check-in - Wrong Station:**

   - Bus is not at the employee's assigned pickup station
   - Error message indicates employee is at wrong pickup location
   - Team Leader can override if necessary (e.g., alternate pickup arrangement)

3. **Failed Check-in - Bus Not at Station:**
   - Bus GPS shows it's not near any defined pickup station
   - Error indicates bus is not at a valid pickup location
   - Prevents fraudulent check-ins when bus is not at designated stops

This approach ensures employees only check in when they're physically at their assigned pickup locations, while the bus is also present at the same location, providing a robust validation mechanism.

### 5.4 Configuration Service

#### 5.4.1 Core Responsibilities

- Centralized management of client-specific configurations
- Dynamic validation rules
- Feature toggles and business rules
- Country-specific variations

#### 5.4.2 Key Components

**APIs:**

```typescript
@Controller("configurations")
class ConfigurationsController {
  @Get(":clientId")
  getConfiguration(
    @Param("clientId") clientId: string
  ): Promise<ClientConfiguration> {
    // Get full configuration for client
  }

  @Put(":clientId")
  updateConfiguration(
    @Param("clientId") clientId: string,
    @Body() config: ClientConfiguration
  ): Promise<ClientConfiguration> {
    // Update client configuration
  }

  @Get(":clientId/features")
  getFeatures(@Param("clientId") clientId: string): Promise<FeatureFlags> {
    // Get feature flags for client
  }

  @Get(":clientId/business-rules")
  getBusinessRules(
    @Param("clientId") clientId: string
  ): Promise<BusinessRules> {
    // Get business rules for client
  }

  @Get(":clientId/validation-rules/:entityType")
  getValidationRules(
    @Param("clientId") clientId: string,
    @Param("entityType") entityType: string
  ): Promise<object> {
    // Get validation rules for specific entity type
  }
}
```

**Services:**

```typescript
@Injectable()
class ConfigurationService {
  async getConfiguration(clientId: string): Promise<ClientConfiguration> {
    // Retrieve client configuration from database
    // Apply any default values for missing properties
    return configuration;
  }

  async updateConfiguration(
    clientId: string,
    config: ClientConfiguration
  ): Promise<ClientConfiguration> {
    // Validate configuration structure
    // Save to database
    // Publish ConfigurationUpdated event
    return savedConfig;
  }

  async validateEntity(
    clientId: string,
    entityType: string,
    entity: any
  ): Promise<ValidationResult> {
    // Get validation rules for entity type
    // Apply rules to entity
    // Return validation result
  }
}
```

**Domain Events:**

```typescript
class ConfigurationUpdatedEvent {
  clientId: string;
  configurationId: string;
  updatedBy: string;
  timestamp: Date;
  changedSections: string[];
}
```

#### 5.4.3 API Endpoints

```
GET /api/configurations/:clientId
PUT /api/configurations/:clientId
GET /api/configurations/:clientId/features
GET /api/configurations/:clientId/business-rules
GET /api/configurations/:clientId/validation-rules/:entityType
```

#### 5.4.4 Sequence Diagram - Client Configuration

```mermaid
sequenceDiagram
    actor Admin
    participant APIMgmt as Azure API Management
    participant ConfigService as Configuration Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus
    participant OtherServices as Other Microservices

    Admin->>APIMgmt: Update Configuration Request
    APIMgmt->>ConfigService: Forward Request
    ConfigService->>ConfigService: Validate Configuration
    ConfigService->>DB: Save Configuration
    DB-->>ConfigService: Confirm Save

    ConfigService->>Bus: Publish ConfigurationUpdated Event
    Bus-->>OtherServices: Notify of Configuration Change

    ConfigService-->>APIMgmt: Return Configuration
    APIMgmt-->>Admin: Return Success Response
```

## 6. Database Design

### 6.1 CosmosDB Container Design

Each microservice has its own Cosmos DB database with containers specific to its domain. The system incorporates all necessary data within these containers, including data that would typically come from external systems.

**Enhanced Entity Relationship Diagram with Geospatial Support:**

```mermaid
erDiagram
    PICKUP_STATION {
        string id PK
        string name
        object address
        string routeCode
        string countryCode
        boolean isActive
        number capacity
        object location
        object coverageArea
        string createdBy
        date createdAt
        date updatedAt
    }

    TRANSPORT_ROUTE {
        string id PK
        string name
        string routeCode
        string countryCode
        object path
        array stations
        number distance
        number estimatedDuration
        boolean isActive
        string createdBy
        date createdAt
        date updatedAt
    }

    ROUTE_SEGMENT {
        string id PK
        string routeId FK
        string startStationId FK
        string endStationId FK
        object path
        number distance
        number estimatedDuration
    }

    BUS_LOCATION {
        string id PK
        string busId
        string routeId FK
        object location
        date timestamp
        number speed
        number direction
        string status
    }

    STATION_ASSIGNMENT {
        string id PK
        string employeeId
        string stationId FK
        date assignedDate
        string updatedBy
    }

    // ... existing entity relationships ...

    TRANSPORT_ROUTE ||--o{ ROUTE_SEGMENT : "contains"
    TRANSPORT_ROUTE ||--o{ PICKUP_STATION : "includes"
    TRANSPORT_ROUTE ||--o{ BUS_LOCATION : "tracks"
    PICKUP_STATION ||--o{ ROUTE_SEGMENT : "starts/ends"
```

#### 6.2.1 Pickup Station Service Database

**Enhanced Containers:**

1. **stations**
   - Partition Key: `/countryCode`
   - Purpose: Stores pickup station information with geospatial data
   - Spatial Index: Enhanced with spatial indexing for the `location` field
   - Sample Item:

```json
{
  "id": "station-123",
  "partitionKey": "MOROCCO",
  "name": "Kembah Mhedhya",
  "address": {
    "street": "123 Main Street",
    "city": "Casablanca",
    "region": "Casablanca-Settat",
    "country": "Morocco",
    "postalCode": "20250"
  },
  "location": {
    "type": "Point",
    "coordinates": [-7.5898, 33.5731]
  },
  "coverageArea": {
    "type": "Polygon",
    "coordinates": [
      [
        [-7.595, 33.57],
        [-7.595, 33.576],
        [-7.585, 33.576],
        [-7.585, 33.57],
        [-7.595, 33.57]
      ]
    ]
  },
  "routeCode": "Trajet A",
  "isActive": true,
  "capacity": 50,
  "createdBy": "user-456",
  "createdAt": "2023-08-15T14:30:00Z",
  "updatedAt": "2023-09-20T10:15:00Z",
  "type": "station"
}
```

2. **routes**
   - Partition Key: `/countryCode`
   - Purpose: Stores transport route information with GeoJSON path data
   - Spatial Index: Enhanced with spatial indexing for the `path` field
   - Sample Item:

```json
{
  "id": "route-456",
  "partitionKey": "MOROCCO",
  "name": "Morning Plant Route",
  "routeCode": "Trajet A",
  "countryCode": "MOROCCO",
  "path": {
    "type": "LineString",
    "coordinates": [
      [-7.5898, 33.5731],
      [-7.592, 33.575],
      [-7.597, 33.579],
      [-7.605, 33.583]
    ]
  },
  "stations": ["station-123", "station-124", "station-125"],
  "distance": 8500,
  "estimatedDuration": 25,
  "isActive": true,
  "createdBy": "user-456",
  "createdAt": "2023-08-20T10:30:00Z",
  "updatedAt": "2023-09-25T14:15:00Z",
  "type": "route"
}
```

3. **route_segments**
   - Partition Key: `/routeId`
   - Purpose: Stores individual segments between stations
   - Sample Item:

```json
{
  "id": "segment-789",
  "partitionKey": "route-456",
  "routeId": "route-456",
  "startStationId": "station-123",
  "endStationId": "station-124",
  "path": {
    "type": "LineString",
    "coordinates": [
      [-7.5898, 33.5731],
      [-7.592, 33.575],
      [-7.597, 33.579]
    ]
  },
  "distance": 2500,
  "estimatedDuration": 8,
  "type": "route_segment"
}
```

4. **bus_locations**
   - Partition Key: `/busId`
   - Purpose: Tracks bus GPS coordinates over time
   - TTL: 30 days for historical data
   - Spatial Index: Enhanced with spatial indexing for the `location` field
   - Sample Item:

```json
{
  "id": "location-123",
  "partitionKey": "bus-001",
  "busId": "bus-001",
  "routeId": "route-456",
  "location": {
    "type": "Point",
    "coordinates": [-7.592, 33.575]
  },
  "timestamp": "2023-10-15T06:25:30Z",
  "speed": 35,
  "bearing": 45,
  "status": "EN_ROUTE",
  "nextStationId": "station-124",
  "estimatedArrivalTime": "2023-10-15T06:32:15Z",
  "distanceToNextStation": 1250,
  "routeCompletionPercentage": 43.5,
  "type": "bus_location"
}
```

#### 6.2.2 Spatial Indexing Configuration

To optimize geospatial queries, the CosmosDB containers are configured with spatial indexes:

```json
{
  "indexingMode": "consistent",
  "automatic": true,
  "includedPaths": [
    {
      "path": "/*"
    }
  ],
  "excludedPaths": [
    {
      "path": "/\"_etag\"/?"
    }
  ],
  "spatialIndexes": [
    {
      "path": "/location/*",
      "types": ["Point", "Polygon"]
    },
    {
      "path": "/path/*",
      "types": ["Point", "LineString", "Polygon"]
    }
  ]
}
```

## 7. Event-Driven Architecture

### 7.1 Event Schema

```typescript
interface DomainEvent {
  id: string;
  type: string;
  timestamp: Date;
  correlationId: string;
  data: any;
  metadata: {
    producer: string;
    version: string;
  };
}
```

### 7.2 Key Events and Event Flow

1. **Station Management Events:**

   - `StationCreated`
   - `StationUpdated`
   - `StationAssigned`
   - `AddressChangeRequested`
   - `AddressChangeApproved`

2. **Transport Planning Events:**

   - `TransportPlanCreated`
   - `TransportPlanUpdated`
   - `TransportPlanPublished`
   - `ShiftAssignmentAdded`
   - `ShiftAssignmentRemoved`
   - `MinibusAllocationUpdated`

3. **Bus Boarding Events:**
   - `PassengerBoarded`
   - `BoardingStatusChanged`

### 7.3 Event Flow Diagram

```mermaid
flowchart LR
    PickupService[Pickup Station Service] --> |produces| Events1[StationCreated\nStationUpdated\nStationAssigned\nAddressChangeRequested\nAddressChangeApproved]
    TransportService[Transport Planning Service] --> |produces| Events2[TransportPlanCreated\nTransportPlanPublished\nShiftAssignmentAdded\nMinibusAllocationUpdated]
    BoardingService[Bus Boarding Service] --> |produces| Events3[PassengerBoarded\nBoardingStatusChanged]

    Events1 --> ServiceBus{Azure Service Bus}
    Events2 --> ServiceBus
    Events3 --> ServiceBus

    ServiceBus --> |consumes| PickupService
    ServiceBus --> |consumes| TransportService
    ServiceBus --> |consumes| BoardingService

    subgraph "Event Topics"
        ServiceBus
    end
```

## 8. Security Design

### 8.1 Authentication and Authorization

**Role-Based Access Control:**

```typescript
enum UserRole {
  TRANSPORT_AGENT = "TRANSPORT_AGENT",
  TEAM_LEADER = "TEAM_LEADER",
  CLERK = "CLERK",
  MANAGER = "MANAGER",
}

interface Permission {
  resource: string;
  actions: string[];
}

const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.TRANSPORT_AGENT]: [
    { resource: "station", actions: ["create", "read", "update", "delete"] },
    {
      resource: "transport-plan",
      actions: ["create", "read", "update", "delete", "publish"],
    },
    {
      resource: "address-change-request",
      actions: ["read", "approve", "reject"],
    },
  ],
  [UserRole.TEAM_LEADER]: [
    { resource: "station", actions: ["read"] },
    { resource: "transport-plan", actions: ["read"] },
    { resource: "address-change-request", actions: ["create", "read"] },
    { resource: "boarding", actions: ["read", "update"] },
  ],
  // Other roles...
};
```

### 8.2 Data Protection

- Encrypt sensitive data at rest and in transit
- Implement data retention policies
- Anonymize data for reporting

## 9. Deployment and DevOps

### 9.1 CI/CD Pipeline

**CI/CD Pipeline Diagram:**

```mermaid
flowchart LR
    Code[Code Repository] --> Build[Build & Test]
    Build --> UnitTests[Unit Tests]
    UnitTests --> IntegrationTests[Integration Tests]
    IntegrationTests --> Package[Package]
    Package --> Deploy[Deploy]

    subgraph Environments
        Dev[Development]
        QA[QA]
        Staging[Staging]
        Prod[Production]
    end

    Deploy --> Dev
    Dev --> QA
    QA --> Staging
    Staging --> Prod

    style Prod fill:#f96,stroke:#333,stroke-width:2px
```

### 9.2 Monitoring and Alerting

**Key Metrics:**

- API response times through Azure API Management
- Event processing latency
- Database query performance
- Error rates by service

**Health Checks:**

```typescript
class HealthCheck {
  async checkService(): Promise<HealthStatus> {
    const dbStatus = await this.checkDatabase();
    const msgBusStatus = await this.checkMessageBus();
    const externalSystems = await this.checkExternalSystems();

    return {
      status: this.calculateOverallStatus([
        dbStatus,
        msgBusStatus,
        ...externalSystems,
      ]),
      components: {
        database: dbStatus,
        messageBus: msgBusStatus,
        externalSystems,
      },
      timestamp: new Date(),
    };
  }
}
```

## 10. Conclusion

This low-level design provides a comprehensive blueprint for implementing the Transport Management module using DDD and microservices architecture. The design emphasizes:

1. **Modularity**: Clear separation of concerns across bounded contexts
2. **Configurability**: Dynamic configurations for country-specific requirements
3. **Scalability**: Event-driven architecture with independent scaling
4. **Maintainability**: Clean domain models and business logic encapsulation
5. **Adaptability**: Extensible design to accommodate future requirements

The implementation follows industry best practices for cloud-native applications, ensuring reliability, performance, and security while providing the flexibility needed to meet specific client requirements across different countries.
