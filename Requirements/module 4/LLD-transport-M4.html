<!doctype html>
<html>
  <head>
    <title>LLD-transport-M4.md</title>
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />

    <style>
      /* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
      /*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

      body {
        font-family: var(
          --vscode-markdown-font-family,
          -apple-system,
          BlinkMacSystemFont,
          "Segoe WPC",
          "Segoe UI",
          "Ubuntu",
          "Droid Sans",
          sans-serif
        );
        font-size: var(--vscode-markdown-font-size, 14px);
        padding: 0 26px;
        line-height: var(--vscode-markdown-line-height, 22px);
        word-wrap: break-word;
      }

      #code-csp-warning {
        position: fixed;
        top: 0;
        right: 0;
        color: white;
        margin: 16px;
        text-align: center;
        font-size: 12px;
        font-family: sans-serif;
        background-color: #444444;
        cursor: pointer;
        padding: 6px;
        box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.25);
      }

      #code-csp-warning:hover {
        text-decoration: none;
        background-color: #007acc;
        box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.25);
      }

      body.scrollBeyondLastLine {
        margin-bottom: calc(100vh - 22px);
      }

      body.showEditorSelection .code-line {
        position: relative;
      }

      body.showEditorSelection .code-active-line:before,
      body.showEditorSelection .code-line:hover:before {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: -12px;
        height: 100%;
      }

      body.showEditorSelection li.code-active-line:before,
      body.showEditorSelection li.code-line:hover:before {
        left: -30px;
      }

      .vscode-light.showEditorSelection .code-active-line:before {
        border-left: 3px solid rgba(0, 0, 0, 0.15);
      }

      .vscode-light.showEditorSelection .code-line:hover:before {
        border-left: 3px solid rgba(0, 0, 0, 0.4);
      }

      .vscode-light.showEditorSelection .code-line .code-line:hover:before {
        border-left: none;
      }

      .vscode-dark.showEditorSelection .code-active-line:before {
        border-left: 3px solid rgba(255, 255, 255, 0.4);
      }

      .vscode-dark.showEditorSelection .code-line:hover:before {
        border-left: 3px solid rgba(255, 255, 255, 0.6);
      }

      .vscode-dark.showEditorSelection .code-line .code-line:hover:before {
        border-left: none;
      }

      .vscode-high-contrast.showEditorSelection .code-active-line:before {
        border-left: 3px solid rgba(255, 160, 0, 0.7);
      }

      .vscode-high-contrast.showEditorSelection .code-line:hover:before {
        border-left: 3px solid rgba(255, 160, 0, 1);
      }

      .vscode-high-contrast.showEditorSelection
        .code-line
        .code-line:hover:before {
        border-left: none;
      }

      img {
        max-width: 100%;
        max-height: 100%;
      }

      a {
        text-decoration: none;
      }

      a:hover {
        text-decoration: underline;
      }

      a:focus,
      input:focus,
      select:focus,
      textarea:focus {
        outline: 1px solid -webkit-focus-ring-color;
        outline-offset: -1px;
      }

      hr {
        border: 0;
        height: 2px;
        border-bottom: 2px solid;
      }

      h1 {
        padding-bottom: 0.3em;
        line-height: 1.2;
        border-bottom-width: 1px;
        border-bottom-style: solid;
      }

      h1,
      h2,
      h3 {
        font-weight: normal;
      }

      table {
        border-collapse: collapse;
      }

      table > thead > tr > th {
        text-align: left;
        border-bottom: 1px solid;
      }

      table > thead > tr > th,
      table > thead > tr > td,
      table > tbody > tr > th,
      table > tbody > tr > td {
        padding: 5px 10px;
      }

      table > tbody > tr + tr > td {
        border-top: 1px solid;
      }

      blockquote {
        margin: 0 7px 0 5px;
        padding: 0 16px 0 10px;
        border-left-width: 5px;
        border-left-style: solid;
      }

      code {
        font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New",
          monospace, "Droid Sans Fallback";
        font-size: 1em;
        line-height: 1.357em;
      }

      body.wordWrap pre {
        white-space: pre-wrap;
      }

      pre:not(.hljs),
      pre.hljs code > div {
        padding: 16px;
        border-radius: 3px;
        overflow: auto;
      }

      pre code {
        color: var(--vscode-editor-foreground);
        tab-size: 4;
      }

      /** Theming */

      .vscode-light pre {
        background-color: rgba(220, 220, 220, 0.4);
      }

      .vscode-dark pre {
        background-color: rgba(10, 10, 10, 0.4);
      }

      .vscode-high-contrast pre {
        background-color: rgb(0, 0, 0);
      }

      .vscode-high-contrast h1 {
        border-color: rgb(0, 0, 0);
      }

      .vscode-light table > thead > tr > th {
        border-color: rgba(0, 0, 0, 0.69);
      }

      .vscode-dark table > thead > tr > th {
        border-color: rgba(255, 255, 255, 0.69);
      }

      .vscode-light h1,
      .vscode-light hr,
      .vscode-light table > tbody > tr + tr > td {
        border-color: rgba(0, 0, 0, 0.18);
      }

      .vscode-dark h1,
      .vscode-dark hr,
      .vscode-dark table > tbody > tr + tr > td {
        border-color: rgba(255, 255, 255, 0.18);
      }
    </style>

    <style>
      /* Tomorrow Theme */
      /* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
      /* Original theme - https://github.com/chriskempson/tomorrow-theme */

      /* Tomorrow Comment */
      .hljs-comment,
      .hljs-quote {
        color: #8e908c;
      }

      /* Tomorrow Red */
      .hljs-variable,
      .hljs-template-variable,
      .hljs-tag,
      .hljs-name,
      .hljs-selector-id,
      .hljs-selector-class,
      .hljs-regexp,
      .hljs-deletion {
        color: #c82829;
      }

      /* Tomorrow Orange */
      .hljs-number,
      .hljs-built_in,
      .hljs-builtin-name,
      .hljs-literal,
      .hljs-type,
      .hljs-params,
      .hljs-meta,
      .hljs-link {
        color: #f5871f;
      }

      /* Tomorrow Yellow */
      .hljs-attribute {
        color: #eab700;
      }

      /* Tomorrow Green */
      .hljs-string,
      .hljs-symbol,
      .hljs-bullet,
      .hljs-addition {
        color: #718c00;
      }

      /* Tomorrow Blue */
      .hljs-title,
      .hljs-section {
        color: #4271ae;
      }

      /* Tomorrow Purple */
      .hljs-keyword,
      .hljs-selector-tag {
        color: #8959a8;
      }

      .hljs {
        display: block;
        overflow-x: auto;
        color: #4d4d4c;
        padding: 0.5em;
      }

      .hljs-emphasis {
        font-style: italic;
      }

      .hljs-strong {
        font-weight: bold;
      }
    </style>

    <style>
      /*
 * Markdown PDF CSS
 */

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI",
          "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
        padding: 0 12px;
      }

      pre {
        background-color: #f8f8f8;
        border: 1px solid #cccccc;
        border-radius: 3px;
        overflow-x: auto;
        white-space: pre-wrap;
        overflow-wrap: break-word;
      }

      pre:not(.hljs) {
        padding: 23px;
        line-height: 19px;
      }

      blockquote {
        background: rgba(127, 127, 127, 0.1);
        border-color: rgba(0, 122, 204, 0.5);
      }

      .emoji {
        height: 1.4em;
      }

      code {
        font-size: 14px;
        line-height: 19px;
      }

      /* for inline code */
      :not(pre):not(.hljs) > code {
        color: #c9ae75; /* Change the old color so it seems less like an error */
        font-size: inherit;
      }

      /* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
      .page {
        page-break-after: always;
      }
    </style>

    <script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
  </head>
  <body>
    <script>
      mermaid.initialize({
        startOnLoad: true,
        theme:
          document.body.classList.contains("vscode-dark") ||
          document.body.classList.contains("vscode-high-contrast")
            ? "dark"
            : "default",
      });
    </script>
    <h1 id="low-level-design-document-transport-management-module">
      Low-Level Design Document: Transport Management Module
    </h1>
    <h2 id="document-information">Document Information</h2>
    <p>
      <strong>Version:</strong> 1.0.0<br />
      <strong>Last Updated:</strong> 2025-03-26<br />
      <strong>Status:</strong> Completed<br />
      <strong>Authors: <AUTHORS>
    </p>
    <h2 id="executive-summary">Executive Summary</h2>
    <h3 id="key-features">Key Features</h3>
    <ul>
      <li>Microservices-based architecture with three core services</li>
      <li>Event-driven communication using Azure Service Bus</li>
      <li>Configurable business rules and validation</li>
      <li>Multi-country support with country-specific variations</li>
      <li>Real-time data synchronization and monitoring</li>
      <li>Role-based access control and security</li>
    </ul>
    <h3 id="business-benefits">Business Benefits</h3>
    <ul>
      <li>Scalable and maintainable transport management system</li>
      <li>Flexible configuration for different country requirements</li>
      <li>Improved operational efficiency through automation</li>
      <li>Enhanced data security and compliance</li>
      <li>Real-time monitoring and reporting capabilities</li>
    </ul>
    <h3 id="document-focus">Document Focus</h3>
    <p>
      This document provides detailed technical specifications for the Transport
      Management module, including architecture, domain models, database design,
      and implementation guidelines.
    </p>
    <h2 id="table-of-contents">Table of Contents</h2>
    <ol>
      <li>
        <a href="#1-overview">Overview</a>
        <ul>
          <li><a href="#11-purpose-and-scope">Purpose and Scope</a></li>
          <li><a href="#12-key-components">Key Components</a></li>
        </ul>
      </li>
      <li>
        <a href="#2-system-architecture">System Architecture</a>
        <ul>
          <li>
            <a href="#21-high-level-architecture">High-Level Architecture</a>
          </li>
          <li><a href="#22-technical-components">Technical Components</a></li>
        </ul>
      </li>
      <li>
        <a href="#3-domain-model-design">Domain Model Design</a>
        <ul>
          <li><a href="#31-bounded-contexts">Bounded Contexts</a></li>
          <li>
            <a href="#32-aggregates-and-entities">Aggregates and Entities</a>
          </li>
        </ul>
      </li>
      <li>
        <a href="#4-configurability-and-dynamic-features"
          >Configurability and Dynamic Features</a
        >
        <ul>
          <li><a href="#41-configuration-service">Configuration Service</a></li>
          <li>
            <a href="#42-enhanced-entity-structure"
              >Enhanced Entity Structure</a
            >
          </li>
        </ul>
      </li>
      <li>
        <a href="#5-microservices-implementation"
          >Microservices Implementation</a
        >
        <ul>
          <li>
            <a href="#51-pickup-station-service">Pickup Station Service</a>
          </li>
          <li>
            <a href="#52-transport-planning-service"
              >Transport Planning Service</a
            >
          </li>
          <li><a href="#53-bus-boarding-service">Bus Boarding Service</a></li>
          <li><a href="#54-configuration-service">Configuration Service</a></li>
        </ul>
      </li>
      <li>
        <a href="#6-database-design">Database Design</a>
        <ul>
          <li>
            <a href="#61-cosmosdb-container-design"
              >CosmosDB Container Design</a
            >
          </li>
          <li>
            <a href="#62-microservice-specific-containers"
              >Microservice-Specific Containers</a
            >
          </li>
          <li><a href="#63-data-synchronization">Data Synchronization</a></li>
        </ul>
      </li>
      <li>
        <a href="#7-event-driven-architecture">Event-Driven Architecture</a>
        <ul>
          <li><a href="#71-event-schema">Event Schema</a></li>
          <li>
            <a href="#72-key-events-and-event-flow"
              >Key Events and Event Flow</a
            >
          </li>
        </ul>
      </li>
      <li>
        <a href="#8-security-design">Security Design</a>
        <ul>
          <li>
            <a href="#81-authentication-and-authorization"
              >Authentication and Authorization</a
            >
          </li>
          <li><a href="#82-data-protection">Data Protection</a></li>
        </ul>
      </li>
      <li>
        <a href="#9-deployment-and-devops">Deployment and DevOps</a>
        <ul>
          <li><a href="#91-cicd-pipeline">CI/CD Pipeline</a></li>
          <li>
            <a href="#92-monitoring-and-alerting">Monitoring and Alerting</a>
          </li>
        </ul>
      </li>
      <li><a href="#10-conclusion">Conclusion</a></li>
    </ol>
    <h2 id="1-overview">1. Overview</h2>
    <h3 id="11-purpose-and-scope">1.1 Purpose and Scope</h3>
    <p>
      This low-level design document outlines the detailed architecture for the
      Transport Management module based on Domain-Driven Design (DDD) principles
      and microservices architecture. The design prioritizes configurability,
      flexibility, and scalability to accommodate various client requirements
      across different countries.
    </p>
    <h3 id="12-key-components">1.2 Key Components</h3>
    <p>The system consists of three primary microservices:</p>
    <ol>
      <li>Pickup Station Service</li>
      <li>Transport Planning Service</li>
      <li>Bus Boarding Service</li>
    </ol>
    <p>Each service is supported by:</p>
    <ul>
      <li>Azure CosmosDB for data storage</li>
      <li>Azure Service Bus for event-driven communication</li>
      <li>Azure API Management for API gateway</li>
      <li>Configuration Service for centralized settings</li>
    </ul>
    <h2 id="2-system-architecture-overview">2. System Architecture Overview</h2>
    <h3 id="21-high-level-architecture">2.1 High-Level Architecture</h3>
    <p>
      The system follows a microservices architecture with the following
      components:
    </p>
    <pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    ApiManagement[Azure API Management] --> PickupStation[Pickup Station Service]
    ApiManagement --> TransportPlanning[Transport Planning Service]
    ApiManagement --> BusBoarding[Bus Boarding Service]
    ApiManagement --> ConfigService[Configuration Service]

    PickupStation --> StationDB[(Station Cosmos DB)]
    TransportPlanning --> PlanningDB[(Planning Cosmos DB)]
    BusBoarding --> BoardingDB[(Boarding Cosmos DB)]
    ConfigService --> ConfigDB[(Config Cosmos DB)]

    PickupStation -.-> ConfigService
    TransportPlanning -.-> ConfigService
    BusBoarding -.-> ConfigService

    StationDB -.-> ServiceBus{Azure Service Bus}
    PlanningDB -.-> ServiceBus
    BoardingDB -.-> ServiceBus

    ServiceBus -.-> PickupStation
    ServiceBus -.-> TransportPlanning
    ServiceBus -.-> BusBoarding

    style ApiManagement fill:#f9f,stroke:#333,stroke-width:2px
    style ServiceBus fill:#bbf,stroke:#333,stroke-width:2px
    style ConfigService fill:#bfb,stroke:#333,stroke-width:2px
</div></code></pre>
    <h3 id="22-key-technical-components">2.2 Key Technical Components</h3>
    <ol>
      <li>
        <strong>NestJS Microservices</strong>: Core business logic
        implementation
      </li>
      <li>
        <strong>CosmosDB</strong>: NoSQL database with multi-region support
      </li>
      <li>
        <strong>Azure Service Bus</strong>: Event-driven communication between
        services
      </li>
      <li>
        <strong>Change Feed Processor</strong>: Real-time data synchronization
      </li>
      <li>
        <strong>Azure API Management</strong>: API Gateway that provides a
        unified entry point for all client applications, handling routing,
        authentication, and rate limiting
      </li>
      <li>
        <strong>Configuration Service</strong>: Centralized configuration
        management for client-specific settings
      </li>
    </ol>
    <h2 id="3-domain-model-design">3. Domain Model Design</h2>
    <h3 id="31-bounded-contexts">3.1 Bounded Contexts</h3>
    <p>The system is divided into three primary bounded contexts:</p>
    <h4 id="311-pickup-station-management-context">
      3.1.1 Pickup Station Management Context
    </h4>
    <ul>
      <li>
        Core domain responsible for station creation, management, and assignment
      </li>
      <li>Handles address change requests from Team Leaders</li>
    </ul>
    <h4 id="312-transport-planning-context">
      3.1.2 Transport Planning Context
    </h4>
    <ul>
      <li>Manages shift assignments and working plans</li>
      <li>Handles minibus allocation and route planning</li>
    </ul>
    <h4 id="313-bus-boarding-context">3.1.3 Bus Boarding Context</h4>
    <ul>
      <li>Manages the bus boarding process</li>
      <li>Tracks employee presence and status changes</li>
    </ul>
    <h3 id="32-aggregates-entities-and-value-objects">
      3.2 Aggregates, Entities, and Value Objects
    </h3>
    <h4 id="321-pickup-station-context">3.2.1 Pickup Station Context</h4>
    <p><strong>Class Diagram:</strong></p>

    <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px">
      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          PickupStation
        </div>
        <div>+ UUID id</div>
        <div>+ string name</div>
        <div>+ Address address</div>
        <div>+ string routeCode</div>
        <div>+ string countryCode</div>
        <div>+ boolean isActive</div>
        <div>+ number? capacity</div>
        <div>+ string createdBy</div>
        <div>+ Date createdAt</div>
        <div>+ Date updatedAt</div>
      </div>

      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          StationAssignment
        </div>
        <div>+ UUID id</div>
        <div>+ string employeeId</div>
        <div>+ UUID stationId</div>
        <div>+ Date assignedDate</div>
        <div>+ string updatedBy</div>
      </div>

      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          AddressChangeRequest
        </div>
        <div>+ UUID id</div>
        <div>+ string employeeId</div>
        <div>+ Address? currentAddress</div>
        <div>+ Address proposedAddress</div>
        <div>+ string? reason</div>
        <div>+ RequestStatus status</div>
        <div>+ string requestedBy</div>
        <div>+ Approval? clerkApproval</div>
        <div>+ Approval? transportAgentApproval</div>
        <div>+ Date createdAt</div>
      </div>

      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          Address
        </div>
        <div>+ string street</div>
        <div>+ string city</div>
        <div>+ string region</div>
        <div>+ string country</div>
        <div>+ string? postalCode</div>
        <div>+ GpsCoordinates? gpsCoordinates</div>
      </div>

      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          GpsCoordinates
        </div>
        <div>+ number latitude</div>
        <div>+ number longitude</div>
      </div>

      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          Approval
        </div>
        <div>+ boolean approved</div>
        <div>+ Date date</div>
        <div>+ string by</div>
        <div>+ string? reason</div>
      </div>

      <div
        style="
          border: 2px solid #4682b4;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0f8ff;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #4682b4;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          RequestStatus (enum)
        </div>
        <div>PENDING</div>
        <div>APPROVED</div>
        <div>REJECTED</div>
      </div>
    </div>

    <div style="margin-bottom: 25px">
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #4682b4;
            margin-right: 5px;
          "
        ></span>
        PickupStation → Address (contains)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #4682b4;
            margin-right: 5px;
          "
        ></span>
        AddressChangeRequest → Address (contains)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #4682b4;
            margin-right: 5px;
          "
        ></span>
        AddressChangeRequest → Approval (has)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #4682b4;
            margin-right: 5px;
          "
        ></span>
        Address → GpsCoordinates (contains)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #4682b4;
            margin-right: 5px;
          "
        ></span>
        AddressChangeRequest → RequestStatus (has)
      </div>
    </div>
    <p><strong>Aggregates:</strong></p>
    <ul>
      <li>PickupStation</li>
      <li>AddressChangeRequest</li>
    </ul>
    <p><strong>Entities:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> PickupStation {
  id: UUID;
  name: <span class="hljs-built_in">string</span>;
  address: Address;
  routeCode: <span class="hljs-built_in">string</span>;
  countryCode: <span class="hljs-built_in">string</span>;
  isActive: <span class="hljs-built_in">boolean</span>;
  capacity?: <span class="hljs-built_in">number</span>;
  createdBy: <span class="hljs-built_in">string</span>;
  createdAt: <span class="hljs-built_in">Date</span>;
  updatedAt: <span class="hljs-built_in">Date</span>;
}

<span class="hljs-keyword">class</span> StationAssignment {
  id: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  assignedDate: <span class="hljs-built_in">Date</span>;
  updatedBy: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> AddressChangeRequest {
  id: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  currentAddress?: Address;
  proposedAddress: Address;
  reason?: <span class="hljs-built_in">string</span>;
  status: RequestStatus;
  requestedBy: <span class="hljs-built_in">string</span>;
  clerkApproval?: Approval;
  transportAgentApproval?: Approval;
  createdAt: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p><strong>Value Objects:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> Address {
  street: <span class="hljs-built_in">string</span>;
  city: <span class="hljs-built_in">string</span>;
  region: <span class="hljs-built_in">string</span>;
  country: <span class="hljs-built_in">string</span>;
  postalCode?: <span class="hljs-built_in">string</span>;
  gpsCoordinates?: GpsCoordinates;
}

<span class="hljs-keyword">class</span> GpsCoordinates {
  latitude: <span class="hljs-built_in">number</span>;
  longitude: <span class="hljs-built_in">number</span>;
}

<span class="hljs-keyword">class</span> Approval {
  approved: <span class="hljs-built_in">boolean</span>;
  date: <span class="hljs-built_in">Date</span>;
  by: <span class="hljs-built_in">string</span>;
  reason?: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">enum</span> RequestStatus {
  PENDING = <span class="hljs-string">"PENDING"</span>,
  APPROVED = <span class="hljs-string">"APPROVED"</span>,
  REJECTED = <span class="hljs-string">"REJECTED"</span>,
}
</div></code></pre>
    <h4 id="322-transport-planning-context">
      3.2.2 Transport Planning Context
    </h4>
    <p><strong>Class Diagram:</strong></p>

    <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px">
      <div
        style="
          border: 2px solid #228b22;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0fff0;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #228b22;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          TransportPlan
        </div>
        <div>+ UUID id</div>
        <div>+ Date date</div>
        <div>+ PlanStatus status</div>
        <div>+ string countryCode</div>
        <div>+ string createdBy</div>
        <div>+ Date createdAt</div>
        <div>+ Date updatedAt</div>
      </div>

      <div
        style="
          border: 2px solid #228b22;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0fff0;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #228b22;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          ShiftAssignment
        </div>
        <div>+ UUID id</div>
        <div>+ UUID planId</div>
        <div>+ string employeeId</div>
        <div>+ UUID stationId</div>
        <div>+ ShiftCode shiftCode</div>
        <div>+ string routeCode</div>
      </div>

      <div
        style="
          border: 2px solid #228b22;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0fff0;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #228b22;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          MinibusAllocation
        </div>
        <div>+ UUID id</div>
        <div>+ UUID planId</div>
        <div>+ string timeSlot</div>
        <div>+ number employeeCount</div>
        <div>+ number minibusCount</div>
        <div>+ boolean isManuallyAdjusted</div>
        <div>+ string? adjustmentReason</div>
        <div>+ string updatedBy</div>
        <div>+ Date updatedAt</div>
      </div>

      <div
        style="
          border: 2px solid #228b22;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0fff0;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #228b22;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          PlanStatus (enum)
        </div>
        <div>DRAFT</div>
        <div>PUBLISHED</div>
        <div>ARCHIVED</div>
      </div>

      <div
        style="
          border: 2px solid #228b22;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #f0fff0;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #228b22;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          ShiftCode (enum)
        </div>
        <div>MORNING (M)</div>
        <div>SPECIAL_HC1 (HC1)</div>
        <div>SPECIAL_HC2 (HC2)</div>
        <div>SWING (S)</div>
        <div>NIGHT (N)</div>
        <div>OVERTIME (OT)</div>
      </div>
    </div>

    <div style="margin-bottom: 25px">
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #228b22;
            margin-right: 5px;
          "
        ></span>
        TransportPlan → PlanStatus (has status)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #228b22;
            margin-right: 5px;
          "
        ></span>
        ShiftAssignment → ShiftCode (has code)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #228b22;
            margin-right: 5px;
          "
        ></span>
        TransportPlan → ShiftAssignment (contains many)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #228b22;
            margin-right: 5px;
          "
        ></span>
        TransportPlan → MinibusAllocation (has many)
      </div>
    </div>
    <p><strong>Aggregates:</strong></p>
    <ul>
      <li>TransportPlan</li>
      <li>MinibusAllocation</li>
    </ul>
    <p><strong>Entities:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> TransportPlan {
  id: UUID;
  date: <span class="hljs-built_in">Date</span>;
  status: PlanStatus;
  countryCode: <span class="hljs-built_in">string</span>;
  createdBy: <span class="hljs-built_in">string</span>;
  createdAt: <span class="hljs-built_in">Date</span>;
  updatedAt: <span class="hljs-built_in">Date</span>;
}

<span class="hljs-keyword">class</span> ShiftAssignment {
  id: UUID;
  planId: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  shiftCode: ShiftCode;
  routeCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> MinibusAllocation {
  id: UUID;
  planId: UUID;
  timeSlot: <span class="hljs-built_in">string</span>;
  employeeCount: <span class="hljs-built_in">number</span>;
  minibusCount: <span class="hljs-built_in">number</span>;
  isManuallyAdjusted: <span class="hljs-built_in">boolean</span>;
  adjustmentReason?: <span class="hljs-built_in">string</span>;
  updatedBy: <span class="hljs-built_in">string</span>;
  updatedAt: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p><strong>Value Objects:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">enum</span> PlanStatus {
  DRAFT = <span class="hljs-string">"DRAFT"</span>,
  PUBLISHED = <span class="hljs-string">"PUBLISHED"</span>,
  ARCHIVED = <span class="hljs-string">"ARCHIVED"</span>,
}

<span class="hljs-keyword">enum</span> ShiftCode {
  MORNING = <span class="hljs-string">"M"</span>,
  SPECIAL_HC1 = <span class="hljs-string">"HC1"</span>,
  SPECIAL_HC2 = <span class="hljs-string">"HC2"</span>,
  SWING = <span class="hljs-string">"S"</span>,
  NIGHT = <span class="hljs-string">"N"</span>,
  OVERTIME = <span class="hljs-string">"OT"</span>,
}
</div></code></pre>
    <h4 id="323-bus-boarding-context">3.2.3 Bus Boarding Context</h4>
    <p><strong>Class Diagram:</strong></p>

    <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px">
      <div
        style="
          border: 2px solid #8b4513;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #fff8dc;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          BoardingRecord
        </div>
        <div>+ UUID id</div>
        <div>+ string employeeId</div>
        <div>+ ShiftCode shiftCode</div>
        <div>+ Date date</div>
        <div>+ UUID stationId</div>
        <div>+ string routeCode</div>
        <div>+ BoardingStatus status</div>
        <div>+ CheckInMethod checkInMethod</div>
        <div>+ boolean nurseryNotification</div>
        <div>+ Date? checkInTime</div>
        <div>+ string? updatedBy</div>
        <div>+ Date updateTimestamp</div>
      </div>

      <div
        style="
          border: 2px solid #8b4513;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #fff8dc;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          BoardingStatus (enum)
        </div>
        <div>PENDING</div>
        <div>ON_BUS</div>
        <div>ABSENT</div>
        <div>DELAYED</div>
      </div>

      <div
        style="
          border: 2px solid #8b4513;
          border-radius: 8px;
          padding: 10px;
          min-width: 250px;
          background-color: #fff8dc;
        "
      >
        <div
          style="
            font-weight: bold;
            border-bottom: 1px solid #8b4513;
            padding-bottom: 5px;
            margin-bottom: 10px;
          "
        >
          CheckInMethod (enum)
        </div>
        <div>BADGE</div>
        <div>MANUAL</div>
        <div>TL_UPDATE</div>
      </div>
    </div>

    <div style="margin-bottom: 25px">
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #8b4513;
            margin-right: 5px;
          "
        ></span>
        BoardingRecord → BoardingStatus (has status)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #8b4513;
            margin-right: 5px;
          "
        ></span>
        BoardingRecord → CheckInMethod (has method)
      </div>
      <div style="margin-bottom: 10px">
        <span
          style="
            display: inline-block;
            width: 15px;
            height: 15px;
            background-color: #8b4513;
            margin-right: 5px;
          "
        ></span>
        BoardingRecord → ShiftCode (has shift)
      </div>
    </div>
    <p><strong>Aggregates:</strong></p>
    <ul>
      <li>BoardingRecord</li>
    </ul>
    <p><strong>Entities:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> BoardingRecord {
  id: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  shiftCode: ShiftCode;
  date: <span class="hljs-built_in">Date</span>;
  stationId: UUID;
  routeCode: <span class="hljs-built_in">string</span>;
  status: BoardingStatus;
  checkInMethod: CheckInMethod;
  nurseryNotification: <span class="hljs-built_in">boolean</span>;
  checkInTime?: <span class="hljs-built_in">Date</span>;
  updatedBy?: <span class="hljs-built_in">string</span>;
  updateTimestamp: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p><strong>Value Objects:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">enum</span> BoardingStatus {
  PENDING = <span class="hljs-string">"PENDING"</span>,
  ON_BUS = <span class="hljs-string">"ON_BUS"</span>,
  ABSENT = <span class="hljs-string">"ABSENT"</span>,
  DELAYED = <span class="hljs-string">"DELAYED"</span>,
}

<span class="hljs-keyword">enum</span> CheckInMethod {
  BADGE = <span class="hljs-string">"BADGE"</span>,
  MANUAL = <span class="hljs-string">"MANUAL"</span>,
  TL_UPDATE = <span class="hljs-string">"TL_UPDATE"</span>,
}
</div></code></pre>
    <h2 id="4-configurability-and-dynamic-features">
      4. Configurability and Dynamic Features
    </h2>
    <h3 id="41-configuration-service">4.1 Configuration Service</h3>
    <p>
      The system implements a dedicated Configuration Service that centralizes
      client-specific configurations:
    </p>
    <pre><code class="language-mermaid"><div class="mermaid">graph TD
    ClientConfiguration["ClientConfiguration<br>+string clientId<br>+string countryCode<br>+FeatureFlags features<br>+BusinessRules businessRules<br>+InterfaceOptions interfaces<br>+CountryVariations countryVariations<br>+ValidationRules validationRules<br>+IntegrationSettings integrations"]

    FeatureFlags["FeatureFlags<br>+boolean transportPlanning<br>+boolean changePickupStation<br>+boolean busBoarding<br>+boolean requireClerkApproval<br>+boolean integrateWithWorkday<br>+boolean integrateWithCW"]

    BusinessRules["BusinessRules<br>+number minibusCapacity<br>+number defaultStationCapacity<br>+number needsManualApprovalThreshold<br>+string[] shiftCodes<br>+string[] timeSlots<br>+string[] boardingStatusTypes<br>+string allocationAlgorithm"]

    InterfaceOptions["InterfaceOptions<br>+boolean enableTabletApp<br>+boolean enableTerminals<br>+boolean enableExcelUpload<br>+boolean enableNurseryNotification"]

    CountryVariations["CountryVariations<br>+boolean hasPredefinedStations<br>+boolean requiresAddressValidation<br>+boolean allowsAddressChangeRequests"]

    ValidationRules["ValidationRules<br>+object stationRules<br>+object employeeRules<br>+object planRules"]

    IntegrationSettings["IntegrationSettings<br>+object workday<br>+object mapping<br>+object cwSystem"]

    ClientConfiguration -->|contains| FeatureFlags
    ClientConfiguration -->|contains| BusinessRules
    ClientConfiguration -->|contains| InterfaceOptions
    ClientConfiguration -->|contains| CountryVariations
    ClientConfiguration -->|contains| ValidationRules
    ClientConfiguration -->|contains| IntegrationSettings
</div></code></pre>
    <h4 id="411-configuration-service-database">
      4.1.1 Configuration Service Database
    </h4>
    <p><strong>Containers:</strong></p>
    <ol>
      <li>
        <strong>client_configurations</strong>
        <ul>
          <li>Partition Key: <code>/clientId</code></li>
          <li>Purpose: Stores client-specific configuration settings</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"config-123"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"client-morocco"</span>,
  <span class="hljs-attr">"clientId"</span>: <span class="hljs-string">"client-morocco"</span>,
  <span class="hljs-attr">"countryCode"</span>: <span class="hljs-string">"MOROCCO"</span>,
  <span class="hljs-attr">"features"</span>: {
    <span class="hljs-attr">"transportPlanning"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"changePickupStation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"busBoarding"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireClerkApproval"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"integrateWithWorkday"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"integrateWithCW"</span>: <span class="hljs-literal">true</span>
  },
  <span class="hljs-attr">"businessRules"</span>: {
    <span class="hljs-attr">"minibusCapacity"</span>: <span class="hljs-number">20</span>,
    <span class="hljs-attr">"defaultStationCapacity"</span>: <span class="hljs-number">50</span>,
    <span class="hljs-attr">"needsManualApprovalThreshold"</span>: <span class="hljs-number">5</span>,
    <span class="hljs-attr">"shiftCodes"</span>: [<span class="hljs-string">"M"</span>, <span class="hljs-string">"S"</span>, <span class="hljs-string">"N"</span>, <span class="hljs-string">"HC1"</span>, <span class="hljs-string">"HC2"</span>, <span class="hljs-string">"OT"</span>],
    <span class="hljs-attr">"timeSlots"</span>: [<span class="hljs-string">"06:00"</span>, <span class="hljs-string">"08:00"</span>, <span class="hljs-string">"14:00"</span>, <span class="hljs-string">"18:00"</span>, <span class="hljs-string">"22:00"</span>],
    <span class="hljs-attr">"boardingStatusTypes"</span>: [<span class="hljs-string">"PENDING"</span>, <span class="hljs-string">"ON_BUS"</span>, <span class="hljs-string">"ABSENT"</span>, <span class="hljs-string">"DELAYED"</span>],
    <span class="hljs-attr">"allocationAlgorithm"</span>: <span class="hljs-string">"simpleDivision"</span>
  },
  <span class="hljs-attr">"interfaces"</span>: {
    <span class="hljs-attr">"enableTabletApp"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enableTerminals"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enableExcelUpload"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enableNurseryNotification"</span>: <span class="hljs-literal">true</span>
  },
  <span class="hljs-attr">"countryVariations"</span>: {
    <span class="hljs-attr">"hasPredefinedStations"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requiresAddressValidation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowsAddressChangeRequests"</span>: <span class="hljs-literal">true</span>
  },
  <span class="hljs-attr">"validationRules"</span>: {
    <span class="hljs-attr">"stationRules"</span>: {
      <span class="hljs-attr">"name"</span>: { <span class="hljs-attr">"required"</span>: <span class="hljs-literal">true</span>, <span class="hljs-attr">"maxLength"</span>: <span class="hljs-number">100</span> },
      <span class="hljs-attr">"address"</span>: { <span class="hljs-attr">"required"</span>: <span class="hljs-literal">true</span> },
      <span class="hljs-attr">"routeCode"</span>: { <span class="hljs-attr">"required"</span>: <span class="hljs-literal">true</span> }
    },
    <span class="hljs-attr">"employeeRules"</span>: {
      <span class="hljs-attr">"badgeId"</span>: { <span class="hljs-attr">"required"</span>: <span class="hljs-literal">true</span>, <span class="hljs-attr">"unique"</span>: <span class="hljs-literal">true</span> }
    },
    <span class="hljs-attr">"planRules"</span>: {
      <span class="hljs-attr">"date"</span>: { <span class="hljs-attr">"required"</span>: <span class="hljs-literal">true</span>, <span class="hljs-attr">"future"</span>: <span class="hljs-literal">true</span> }
    }
  },
  <span class="hljs-attr">"integrations"</span>: {
    <span class="hljs-attr">"workday"</span>: {
      <span class="hljs-attr">"enabled"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"apiUrl"</span>: <span class="hljs-string">"https://workday-api.aptiv.com"</span>,
      <span class="hljs-attr">"apiKey"</span>: <span class="hljs-string">"encrypted-key-123"</span>
    },
    <span class="hljs-attr">"mapping"</span>: {
      <span class="hljs-attr">"enabled"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"provider"</span>: <span class="hljs-string">"google"</span>,
      <span class="hljs-attr">"apiKey"</span>: <span class="hljs-string">"encrypted-key-456"</span>
    },
    <span class="hljs-attr">"cwSystem"</span>: {
      <span class="hljs-attr">"enabled"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"apiUrl"</span>: <span class="hljs-string">"https://cw-api.aptiv.com"</span>
    }
  },
  <span class="hljs-attr">"createdAt"</span>: <span class="hljs-string">"2023-06-15T10:00:00Z"</span>,
  <span class="hljs-attr">"updatedAt"</span>: <span class="hljs-string">"2023-10-01T15:30:00Z"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"client_configuration"</span>
}
</div></code></pre>
    <h4 id="412-configuration-service-api">4.1.2 Configuration Service API</h4>
    <pre class="hljs"><code><div>GET /api/configurations/:clientId
PUT /api/configurations/:clientId
GET /api/configurations/:clientId/features
GET /api/configurations/:clientId/business-rules
GET /api/configurations/:clientId/validation-rules
</div></code></pre>
    <h3 id="42-enhanced-entity-structure">4.2 Enhanced Entity Structure</h3>
    <p>
      All domain entities now support client-specific customization through a
      <code>customAttributes</code> field:
    </p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">interface</span> BaseEntity {
  id: UUID;
  clientId: <span class="hljs-built_in">string</span>;
  customAttributes?: Record&lt;<span class="hljs-built_in">string</span>, <span class="hljs-built_in">any</span>&gt;;
  <span class="hljs-comment">// ... other common fields</span>
}

<span class="hljs-keyword">class</span> PickupStation <span class="hljs-keyword">extends</span> BaseEntity {
  name: <span class="hljs-built_in">string</span>;
  address: Address;
  routeCode: <span class="hljs-built_in">string</span>;
  countryCode: <span class="hljs-built_in">string</span>;
  isActive: <span class="hljs-built_in">boolean</span>;
  capacity?: <span class="hljs-built_in">number</span>;
  <span class="hljs-comment">// ... other fields</span>
}
</div></code></pre>
    <p>
      This allows for dynamic extension of entities without schema changes,
      supporting diverse client requirements.
    </p>
    <h3 id="43-dynamic-business-rules-engine">
      4.3 Dynamic Business Rules Engine
    </h3>
    <p>
      The system implements a rules engine that allows for dynamic business
      rules configuration:
    </p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> BusinessRulesEngine {
  rules: Map&lt;<span class="hljs-built_in">string</span>, Rule&gt;;

  evaluateRules(context: RuleContext, entityType: <span class="hljs-built_in">string</span>): <span class="hljs-built_in">boolean</span> {
    <span class="hljs-comment">// Fetch applicable rules for entity type and country</span>
    <span class="hljs-keyword">const</span> applicableRules = <span class="hljs-keyword">this</span>.getRulesForEntity(
      entityType,
      context.countryCode
    );

    <span class="hljs-comment">// Evaluate each rule in sequence</span>
    <span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> rule of applicableRules) {
      <span class="hljs-keyword">if</span> (!rule.evaluate(context)) {
        <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
      }
    }

    <span class="hljs-keyword">return</span> <span class="hljs-literal">true</span>;
  }
}
</div></code></pre>
    <h3 id="44-feature-toggles">4.4 Feature Toggles</h3>
    <p>
      The system supports feature toggles to enable/disable features at runtime:
    </p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> FeatureManager {
  features: Map&lt;<span class="hljs-built_in">string</span>, <span class="hljs-built_in">boolean</span>&gt;;

  isFeatureEnabled(featureName: <span class="hljs-built_in">string</span>, countryCode: <span class="hljs-built_in">string</span>): <span class="hljs-built_in">boolean</span> {
    <span class="hljs-keyword">const</span> key = <span class="hljs-string">`<span class="hljs-subst">${countryCode}</span>.<span class="hljs-subst">${featureName}</span>`</span>;
    <span class="hljs-keyword">return</span> <span class="hljs-keyword">this</span>.features.get(key) || <span class="hljs-literal">false</span>;
  }
}
</div></code></pre>
    <h2 id="5-microservices-implementation">5. Microservices Implementation</h2>
    <h3 id="51-pickup-station-service">5.1 Pickup Station Service</h3>
    <h4 id="511-core-responsibilities">5.1.1 Core Responsibilities</h4>
    <ul>
      <li>Station creation and management</li>
      <li>Employee-station assignments</li>
      <li>Address change request workflows</li>
    </ul>
    <h4 id="512-key-components">5.1.2 Key Components</h4>
    <p><strong>CQRS Implementation:</strong></p>
    <p><strong>Commands:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> CreateStationCommand {
  name: <span class="hljs-built_in">string</span>;
  address: Address;
  routeCode: <span class="hljs-built_in">string</span>;
  countryCode: <span class="hljs-built_in">string</span>;
  capacity?: <span class="hljs-built_in">number</span>;
  createdBy: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> AssignStationCommand {
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  assignedBy: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> RequestAddressChangeCommand {
  employeeId: <span class="hljs-built_in">string</span>;
  proposedAddress: Address;
  reason?: <span class="hljs-built_in">string</span>;
  requestedBy: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> ApproveAddressChangeCommand {
  requestId: UUID;
  approvedBy: <span class="hljs-built_in">string</span>;
  approvalType: <span class="hljs-string">"CLERK"</span> | <span class="hljs-string">"TRANSPORT_AGENT"</span>;
}
</div></code></pre>
    <p><strong>Queries:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> GetStationsQuery {
  countryCode?: <span class="hljs-built_in">string</span>;
  routeCode?: <span class="hljs-built_in">string</span>;
  isActive?: <span class="hljs-built_in">boolean</span>;
}

<span class="hljs-keyword">class</span> GetEmployeeAssignmentQuery {
  employeeId: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> GetPendingAddressChangeRequestsQuery {
  approvalType: <span class="hljs-string">"CLERK"</span> | <span class="hljs-string">"TRANSPORT_AGENT"</span>;
  countryCode?: <span class="hljs-built_in">string</span>;
}
</div></code></pre>
    <p><strong>Handlers:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-meta">@CommandHandler</span>(CreateStationCommand)
<span class="hljs-keyword">class</span> CreateStationHandler {
  execute(command: CreateStationCommand): <span class="hljs-built_in">Promise</span>&lt;Result&lt;UUID&gt;&gt; {
    <span class="hljs-comment">// Create station logic</span>
    <span class="hljs-comment">// Validate address with mapping service</span>
    <span class="hljs-comment">// Publish StationCreatedEvent</span>
  }
}

<span class="hljs-meta">@QueryHandler</span>(GetStationsQuery)
<span class="hljs-keyword">class</span> GetStationsHandler {
  execute(query: GetStationsQuery): <span class="hljs-built_in">Promise</span>&lt;PickupStation[]&gt; {
    <span class="hljs-comment">// Query stations with filters</span>
  }
}
</div></code></pre>
    <p><strong>Domain Events:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> StationCreatedEvent {
  stationId: UUID;
  name: <span class="hljs-built_in">string</span>;
  address: Address;
  routeCode: <span class="hljs-built_in">string</span>;
  countryCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> StationAssignedEvent {
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  assignedBy: <span class="hljs-built_in">string</span>;
  timestamp: <span class="hljs-built_in">Date</span>;
}

<span class="hljs-keyword">class</span> AddressChangeApprovedEvent {
  requestId: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  newAddress: Address;
  approvedBy: <span class="hljs-built_in">string</span>;
  timestamp: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <h4 id="513-api-endpoints">5.1.3 API Endpoints</h4>
    <pre class="hljs"><code><div>POST /api/pickup-stations
GET /api/pickup-stations
GET /api/pickup-stations/{id}
PUT /api/pickup-stations/{id}
POST /api/pickup-stations/assignments
POST /api/pickup-stations/address-change-requests
GET /api/pickup-stations/address-change-requests
PUT /api/pickup-stations/address-change-requests/{id}/approve
PUT /api/pickup-stations/address-change-requests/{id}/reject
</div></code></pre>
    <p><strong>Sequence Diagram - Station Creation Flow:</strong></p>
    <pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor TransportAgent
    participant APIMgmt as Azure API Management
    participant StationService as Pickup Station Service
    participant MappingAPI as Mapping Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus

    TransportAgent->>APIMgmt: Create Station Request
    APIMgmt->>StationService: Forward Request
    StationService->>MappingAPI: Validate Address
    MappingAPI-->>StationService: Return Coordinates

    StationService->>StationService: Apply Business Rules
    StationService->>DB: Save Station
    DB-->>StationService: Confirm Save

    StationService->>Bus: Publish StationCreated Event
    Bus-->>StationService: Confirm Publication

    StationService-->>APIMgmt: Return Station ID
    APIMgmt-->>TransportAgent: Return Success Response
</div></code></pre>
    <p><strong>Sequence Diagram - Address Change Request Flow:</strong></p>
    <pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor TeamLeader
    participant TabletApp as Tablet App
    participant APIMgmt as Azure API Management
    participant StationService as Pickup Station Service
    participant ClerkApp as Clerk Interface
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus

    TeamLeader->>TabletApp: Submit Address Change
    TabletApp->>APIMgmt: POST /address-change-requests
    APIMgmt->>StationService: Forward Request

    StationService->>DB: Save Request (Status: PENDING)
    DB-->>StationService: Confirm Save

    alt IS/IH Employee
        StationService->>Bus: Publish RequestCreated Event
        Bus-->>ClerkApp: Notify Clerk
        ClerkApp->>APIMgmt: Approve Request
        APIMgmt->>StationService: Process Clerk Approval
        StationService->>DB: Update Request
        DB-->>StationService: Confirm Update
    end

    StationService->>APIMgmt: Transport Agent Review

    alt Approve
        StationService->>DB: Update Status to APPROVED
        StationService->>Bus: Publish AddressChangeApproved Event
        Bus-->>StationService: Confirm Publication
    else Reject
        StationService->>DB: Update Status to REJECTED
    end

    StationService-->>APIMgmt: Return Result
    APIMgmt-->>TabletApp: Return Response
    TabletApp-->>TeamLeader: Show Result
</div></code></pre>
    <h3 id="52-transport-planning-service">5.2 Transport Planning Service</h3>
    <h4 id="521-core-responsibilities">5.2.1 Core Responsibilities</h4>
    <ul>
      <li>Manage transport plans</li>
      <li>Handle shift assignments</li>
      <li>Calculate and adjust minibus allocations</li>
    </ul>
    <h4 id="522-key-components">5.2.2 Key Components</h4>
    <p><strong>CQRS Implementation:</strong></p>
    <p><strong>Commands:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> CreateTransportPlanCommand {
  date: <span class="hljs-built_in">Date</span>;
  countryCode: <span class="hljs-built_in">string</span>;
  createdBy: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> AddShiftAssignmentCommand {
  planId: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  shiftCode: ShiftCode;
  routeCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> UpdateMinibusAllocationCommand {
  planId: UUID;
  timeSlot: <span class="hljs-built_in">string</span>;
  minibusCount: <span class="hljs-built_in">number</span>;
  isManuallyAdjusted: <span class="hljs-built_in">boolean</span>;
  adjustmentReason?: <span class="hljs-built_in">string</span>;
  updatedBy: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> PublishTransportPlanCommand {
  planId: UUID;
  publishedBy: <span class="hljs-built_in">string</span>;
}
</div></code></pre>
    <p><strong>Queries:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> GetTransportPlanQuery {
  planId: UUID;
}

<span class="hljs-keyword">class</span> GetTransportPlansByDateQuery {
  date: <span class="hljs-built_in">Date</span>;
  countryCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> GetMinibusAllocationsQuery {
  planId: UUID;
}

<span class="hljs-keyword">class</span> GetEmployeeScheduleQuery {
  employeeId: <span class="hljs-built_in">string</span>;
  fromDate: <span class="hljs-built_in">Date</span>;
  toDate: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p><strong>Handlers:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-meta">@CommandHandler</span>(UpdateMinibusAllocationCommand)
<span class="hljs-keyword">class</span> UpdateMinibusAllocationHandler {
  execute(command: UpdateMinibusAllocationCommand): <span class="hljs-built_in">Promise</span>&lt;Result&lt;<span class="hljs-built_in">boolean</span>&gt;&gt; {
    <span class="hljs-comment">// Update minibus allocation</span>
    <span class="hljs-comment">// Recalculate if not manually adjusted</span>
    <span class="hljs-comment">// Publish MinibusAllocationUpdatedEvent</span>
  }
}

<span class="hljs-meta">@QueryHandler</span>(GetTransportPlansByDateQuery)
<span class="hljs-keyword">class</span> GetTransportPlansByDateHandler {
  execute(query: GetTransportPlansByDateQuery): <span class="hljs-built_in">Promise</span>&lt;TransportPlan[]&gt; {
    <span class="hljs-comment">// Query plans by date and country</span>
  }
}
</div></code></pre>
    <p><strong>Domain Events:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> TransportPlanCreatedEvent {
  planId: UUID;
  date: <span class="hljs-built_in">Date</span>;
  countryCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> ShiftAssignmentAddedEvent {
  planId: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  shiftCode: ShiftCode;
  stationId: UUID;
  routeCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> MinibusAllocationUpdatedEvent {
  planId: UUID;
  timeSlot: <span class="hljs-built_in">string</span>;
  employeeCount: <span class="hljs-built_in">number</span>;
  minibusCount: <span class="hljs-built_in">number</span>;
  isManuallyAdjusted: <span class="hljs-built_in">boolean</span>;
}

<span class="hljs-keyword">class</span> TransportPlanPublishedEvent {
  planId: UUID;
  date: <span class="hljs-built_in">Date</span>;
  countryCode: <span class="hljs-built_in">string</span>;
}
</div></code></pre>
    <p><strong>Sequence Diagram - Transport Plan Creation:</strong></p>
    <pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor ShiftLeader
    participant WebApp as Web Interface
    participant APIMgmt as Azure API Management
    participant PlanService as Transport Planning Service
    participant StationService as Pickup Station Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus

    ShiftLeader->>WebApp: Create Transport Plan
    WebApp->>APIMgmt: POST /transport-plans
    APIMgmt->>PlanService: Forward Request

    PlanService->>StationService: Get Stations Data
    StationService-->>PlanService: Return Stations

    PlanService->>DB: Create Plan (Status: DRAFT)
    DB-->>PlanService: Confirm Save

    ShiftLeader->>WebApp: Add Shift Assignments
    WebApp->>APIMgmt: POST /transport-plans/{id}/assignments
    APIMgmt->>PlanService: Forward Request

    PlanService->>PlanService: Calculate Employee Counts
    PlanService->>PlanService: Calculate Minibus Allocations

    PlanService->>DB: Save Assignments & Allocations
    DB-->>PlanService: Confirm Save

    ShiftLeader->>WebApp: Publish Plan
    WebApp->>APIMgmt: POST /transport-plans/{id}/publish
    APIMgmt->>PlanService: Forward Request

    PlanService->>DB: Update Status to PUBLISHED
    DB-->>PlanService: Confirm Update

    PlanService->>Bus: Publish TransportPlanPublished Event
    Bus-->>PlanService: Confirm Publication

    PlanService-->>APIMgmt: Return Success
    APIMgmt-->>WebApp: Return Response
    WebApp-->>ShiftLeader: Show Confirmation
</div></code></pre>
    <h3 id="53-bus-boarding-service">5.3 Bus Boarding Service</h3>
    <h4 id="531-core-responsibilities">5.3.1 Core Responsibilities</h4>
    <ul>
      <li>Manage the bus boarding process</li>
      <li>Track employee presence and status</li>
      <li>Handle badge scanning and manual check-ins</li>
      <li>Support special cases (forgotten badges, exceptions)</li>
    </ul>
    <h4 id="532-key-components">5.3.2 Key Components</h4>
    <p><strong>CQRS Implementation:</strong></p>
    <p><strong>Commands:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> CheckInPassengerCommand {
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  shiftCode: ShiftCode;
  date: <span class="hljs-built_in">Date</span>;
  checkInMethod: CheckInMethod;
  nurseryNotification: <span class="hljs-built_in">boolean</span>;
  checkedInBy: <span class="hljs-built_in">string</span>;
  registrationNumber?: <span class="hljs-built_in">string</span>; <span class="hljs-comment">// Optional for manual entry</span>
  exceptionReason?: <span class="hljs-built_in">string</span>; <span class="hljs-comment">// For documenting exceptions</span>
}

<span class="hljs-keyword">class</span> UpdateBoardingStatusCommand {
  boardingId: UUID;
  status: BoardingStatus;
  updatedBy: <span class="hljs-built_in">string</span>;
}
</div></code></pre>
    <p><strong>Queries:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> GetStationPassengersQuery {
  stationId: UUID;
  date: <span class="hljs-built_in">Date</span>;
  shiftCode?: ShiftCode;
}

<span class="hljs-keyword">class</span> GetShiftBoardingRecordsQuery {
  date: <span class="hljs-built_in">Date</span>;
  shiftCode: ShiftCode;
  countryCode: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">class</span> GetEmployeeBoardingHistoryQuery {
  employeeId: <span class="hljs-built_in">string</span>;
  fromDate: <span class="hljs-built_in">Date</span>;
  toDate: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p><strong>Handlers:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-meta">@CommandHandler</span>(CheckInPassengerCommand)
<span class="hljs-keyword">class</span> CheckInPassengerHandler {
  execute(command: CheckInPassengerCommand): <span class="hljs-built_in">Promise</span>&lt;Result&lt;UUID&gt;&gt; {
    <span class="hljs-comment">// Check in passenger logic</span>
    <span class="hljs-comment">// Validate employee exists and is assigned to the station</span>
    <span class="hljs-comment">// For MANUAL check-in, verify registration number matches employee record</span>
    <span class="hljs-comment">// Log exception if checkInMethod is MANUAL</span>
    <span class="hljs-comment">// Publish PassengerBoardedEvent</span>
  }
}

<span class="hljs-meta">@QueryHandler</span>(GetStationPassengersQuery)
<span class="hljs-keyword">class</span> GetStationPassengersHandler {
  execute(query: GetStationPassengersQuery): <span class="hljs-built_in">Promise</span>&lt;BoardingRecord[]&gt; {
    <span class="hljs-comment">// Query passengers by station, date, and shift</span>
  }
}
</div></code></pre>
    <p><strong>Domain Events:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> PassengerBoardedEvent {
  boardingId: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  stationId: UUID;
  shiftCode: ShiftCode;
  date: <span class="hljs-built_in">Date</span>;
  checkInMethod: CheckInMethod; <span class="hljs-comment">// Captures if badge or manual</span>
  nurseryNotification: <span class="hljs-built_in">boolean</span>;
  exceptionLogged: <span class="hljs-built_in">boolean</span>; <span class="hljs-comment">// Indicates if an exception was recorded</span>
}

<span class="hljs-keyword">class</span> BoardingStatusChangedEvent {
  boardingId: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  status: BoardingStatus;
  updatedBy: <span class="hljs-built_in">string</span>;
  timestamp: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p><strong>Sequence Diagram - Badge Scan Process:</strong></p>
    <pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor Employee
    participant Terminal as Bus Terminal
    participant APIMgmt as Azure API Management
    participant BoardingService as Bus Boarding Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus
    participant TLTablet as Team Leader Tablet

    Employee->>Terminal: Scan Badge
    Terminal->>APIMgmt: POST /boarding/badge-scan
    APIMgmt->>BoardingService: Forward Request

    BoardingService->>BoardingService: Validate Employee Assignment

    BoardingService->>DB: Create/Update Boarding Record<br/>(checkInMethod: BADGE)
    DB-->>BoardingService: Confirm Save

    BoardingService->>Bus: Publish PassengerBoarded Event
    Bus-->>BoardingService: Confirm Publication
    Bus-->>TLTablet: Update Status (ON_BUS)

    alt Nursery Notification Selected
        BoardingService->>Bus: Publish NurseryNotification Event
    end

    BoardingService-->>APIMgmt: Return Success
    APIMgmt-->>Terminal: Confirm Boarding
    Terminal-->>Employee: Show Confirmation
</div></code></pre>
    <p>
      <strong
        >Sequence Diagram - Manual Registration Process (Forgotten
        Badge):</strong
      >
    </p>
    <pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor Employee
    participant Terminal as Bus Terminal
    participant APIMgmt as Azure API Management
    participant BoardingService as Bus Boarding Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus
    participant TLTablet as Team Leader Tablet

    Employee->>Terminal: Enter Registration Number
    Terminal->>APIMgmt: POST /boarding/manual-checkin
    APIMgmt->>BoardingService: Forward Request

    BoardingService->>BoardingService: Validate Registration Number
    BoardingService->>BoardingService: Validate Employee Assignment

    BoardingService->>DB: Create/Update Boarding Record<br/>(checkInMethod: MANUAL)
    BoardingService->>DB: Log Exception (Forgotten Badge)
    DB-->>BoardingService: Confirm Save

    BoardingService->>Bus: Publish PassengerBoarded Event<br/>(with exceptionLogged: true)
    Bus-->>BoardingService: Confirm Publication
    Bus-->>TLTablet: Update Status (ON_BUS)<br/>Flag as Manual Entry

    alt Nursery Notification Selected
        BoardingService->>Bus: Publish NurseryNotification Event
    end

    BoardingService-->>APIMgmt: Return Success
    APIMgmt-->>Terminal: Confirm Boarding
    Terminal-->>Employee: Show Confirmation
</div></code></pre>
    <h4 id="533-api-endpoints">5.3.3 API Endpoints</h4>
    <pre class="hljs"><code><div>POST /api/boarding/badge-scan
POST /api/boarding/manual-checkin
PUT /api/boarding/{id}/status
GET /api/boarding/stations/{stationId}/passengers
GET /api/boarding/shifts/{shiftCode}/date/{date}
GET /api/boarding/employees/{employeeId}/history
GET /api/boarding/exceptions/report
</div></code></pre>
    <h3 id="534-special-cases-handling">5.3.4 Special Cases Handling</h3>
    <p>The Bus Boarding Service is designed to handle several special cases:</p>
    <ol>
      <li>
        <p><strong>Forgotten Badges</strong>:</p>
        <ul>
          <li>
            Employees can manually enter their registration number through the
            terminal
          </li>
          <li>
            The system validates the registration number against employee
            records
          </li>
          <li>
            The check-in is recorded with the <code>MANUAL</code> check-in
            method
          </li>
          <li>An exception is logged for reporting purposes</li>
        </ul>
      </li>
      <li>
        <p><strong>Team Leader Updates</strong>:</p>
        <ul>
          <li>
            Team Leaders can update boarding status for employees via tablet app
          </li>
          <li>
            These updates are recorded with the <code>TL_UPDATE</code> check-in
            method
          </li>
        </ul>
      </li>
      <li>
        <p><strong>Exception Reporting</strong>:</p>
        <ul>
          <li>
            The system provides reporting on exceptions (e.g., frequency of
            forgotten badges)
          </li>
          <li>Reports can be filtered by date range, station, or employee</li>
        </ul>
      </li>
    </ol>
    <h3 id="535-enhanced-boarding-record-structure">
      5.3.5 Enhanced Boarding Record Structure
    </h3>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> BoardingRecord {
  id: UUID;
  employeeId: <span class="hljs-built_in">string</span>;
  shiftCode: ShiftCode;
  date: <span class="hljs-built_in">Date</span>;
  stationId: UUID;
  routeCode: <span class="hljs-built_in">string</span>;
  status: BoardingStatus;
  checkInMethod: CheckInMethod;
  nurseryNotification: <span class="hljs-built_in">boolean</span>;
  registrationNumber?: <span class="hljs-built_in">string</span>; <span class="hljs-comment">// For manual entry</span>
  exceptionReason?: <span class="hljs-built_in">string</span>; <span class="hljs-comment">// Documents reason for exception</span>
  exceptionType?: <span class="hljs-built_in">string</span>; <span class="hljs-comment">// Categorizes the exception</span>
  checkInTime?: <span class="hljs-built_in">Date</span>;
  updatedBy?: <span class="hljs-built_in">string</span>;
  updateTimestamp: <span class="hljs-built_in">Date</span>;
}
</div></code></pre>
    <p>
      This enhanced structure captures all necessary information for both
      regular badge scanning and special cases where manual registration is
      required.
    </p>
    <h3 id="54-configuration-service">5.4 Configuration Service</h3>
    <h4 id="541-core-responsibilities">5.4.1 Core Responsibilities</h4>
    <ul>
      <li>Centralized management of client-specific configurations</li>
      <li>Dynamic validation rules</li>
      <li>Feature toggles and business rules</li>
      <li>Country-specific variations</li>
    </ul>
    <h4 id="542-key-components">5.4.2 Key Components</h4>
    <p><strong>APIs:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-meta">@Controller</span>(<span class="hljs-string">"configurations"</span>)
<span class="hljs-keyword">class</span> ConfigurationsController {
  <span class="hljs-meta">@Get</span>(<span class="hljs-string">":clientId"</span>)
  getConfiguration(
    <span class="hljs-meta">@Param</span>(<span class="hljs-string">"clientId"</span>) clientId: <span class="hljs-built_in">string</span>
  ): <span class="hljs-built_in">Promise</span>&lt;ClientConfiguration&gt; {
    <span class="hljs-comment">// Get full configuration for client</span>
  }

  <span class="hljs-meta">@Put</span>(<span class="hljs-string">":clientId"</span>)
  updateConfiguration(
    <span class="hljs-meta">@Param</span>(<span class="hljs-string">"clientId"</span>) clientId: <span class="hljs-built_in">string</span>,
    <span class="hljs-meta">@Body</span>() config: ClientConfiguration
  ): <span class="hljs-built_in">Promise</span>&lt;ClientConfiguration&gt; {
    <span class="hljs-comment">// Update client configuration</span>
  }

  <span class="hljs-meta">@Get</span>(<span class="hljs-string">":clientId/features"</span>)
  getFeatures(<span class="hljs-meta">@Param</span>(<span class="hljs-string">"clientId"</span>) clientId: <span class="hljs-built_in">string</span>): <span class="hljs-built_in">Promise</span>&lt;FeatureFlags&gt; {
    <span class="hljs-comment">// Get feature flags for client</span>
  }

  <span class="hljs-meta">@Get</span>(<span class="hljs-string">":clientId/business-rules"</span>)
  getBusinessRules(
    <span class="hljs-meta">@Param</span>(<span class="hljs-string">"clientId"</span>) clientId: <span class="hljs-built_in">string</span>
  ): <span class="hljs-built_in">Promise</span>&lt;BusinessRules&gt; {
    <span class="hljs-comment">// Get business rules for client</span>
  }

  <span class="hljs-meta">@Get</span>(<span class="hljs-string">":clientId/validation-rules/:entityType"</span>)
  getValidationRules(
    <span class="hljs-meta">@Param</span>(<span class="hljs-string">"clientId"</span>) clientId: <span class="hljs-built_in">string</span>,
    <span class="hljs-meta">@Param</span>(<span class="hljs-string">"entityType"</span>) entityType: <span class="hljs-built_in">string</span>
  ): <span class="hljs-built_in">Promise</span>&lt;object&gt; {
    <span class="hljs-comment">// Get validation rules for specific entity type</span>
  }
}
</div></code></pre>
    <p><strong>Services:</strong></p>
    <pre class="hljs"><code><div><span class="hljs-meta">@Injectable</span>()
<span class="hljs-keyword">class</span> ConfigurationService {
  <span class="hljs-keyword">async</span> getConfiguration(clientId: <span class="hljs-built_in">string</span>): <span class="hljs-built_in">Promise</span>&lt;ClientConfiguration&gt; {
    <span class="hljs-comment">// Retrieve client configuration from database</span>
    <span class="hljs-comment">// Apply any default values for missing properties</span>
    <span class="hljs-keyword">return</span> configuration;
  }

  <span class="hljs-keyword">async</span> updateConfiguration(
    clientId: <span class="hljs-built_in">string</span>,
    config: ClientConfiguration
  ): <span class="hljs-built_in">Promise</span>&lt;ClientConfiguration&gt; {
    <span class="hljs-comment">// Validate configuration structure</span>
    <span class="hljs-comment">// Save to database</span>
    <span class="hljs-comment">// Publish ConfigurationUpdated event</span>
    <span class="hljs-keyword">return</span> savedConfig;
  }

  <span class="hljs-keyword">async</span> validateEntity(
    clientId: <span class="hljs-built_in">string</span>,
    entityType: <span class="hljs-built_in">string</span>,
    entity: <span class="hljs-built_in">any</span>
  ): <span class="hljs-built_in">Promise</span>&lt;ValidationResult&gt; {
    <span class="hljs-comment">// Get validation rules for entity type</span>
    <span class="hljs-comment">// Apply rules to entity</span>
    <span class="hljs-comment">// Return validation result</span>
  }
}
</div></code></pre>
    <p><strong>Domain Events:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> ConfigurationUpdatedEvent {
  clientId: <span class="hljs-built_in">string</span>;
  configurationId: <span class="hljs-built_in">string</span>;
  updatedBy: <span class="hljs-built_in">string</span>;
  timestamp: <span class="hljs-built_in">Date</span>;
  changedSections: <span class="hljs-built_in">string</span>[];
}
</div></code></pre>
    <h4 id="543-api-endpoints">5.4.3 API Endpoints</h4>
    <pre class="hljs"><code><div>GET /api/configurations/:clientId
PUT /api/configurations/:clientId
GET /api/configurations/:clientId/features
GET /api/configurations/:clientId/business-rules
GET /api/configurations/:clientId/validation-rules/:entityType
</div></code></pre>
    <h4 id="544-sequence-diagram---client-configuration">
      5.4.4 Sequence Diagram - Client Configuration
    </h4>
    <pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor Admin
    participant APIMgmt as Azure API Management
    participant ConfigService as Configuration Service
    participant DB as Cosmos DB
    participant Bus as Azure Service Bus
    participant OtherServices as Other Microservices

    Admin->>APIMgmt: Update Configuration Request
    APIMgmt->>ConfigService: Forward Request
    ConfigService->>ConfigService: Validate Configuration
    ConfigService->>DB: Save Configuration
    DB-->>ConfigService: Confirm Save

    ConfigService->>Bus: Publish ConfigurationUpdated Event
    Bus-->>OtherServices: Notify of Configuration Change

    ConfigService-->>APIMgmt: Return Configuration
    APIMgmt-->>Admin: Return Success Response
</div></code></pre>
    <h2 id="6-database-design">6. Database Design</h2>
    <h3 id="61-cosmosdb-container-design">6.1 CosmosDB Container Design</h3>
    <p>
      Each microservice has its own Cosmos DB database with containers specific
      to its domain. The system incorporates all necessary data within these
      containers, including data that would typically come from external
      systems.
    </p>
    <p><strong>Entity Relationship Diagram:</strong></p>
    <pre><code class="language-mermaid"><div class="mermaid">erDiagram
    PICKUP_STATION {
        string id PK
        string name
        object address
        string routeCode
        string countryCode
        boolean isActive
        number capacity
        string createdBy
        date createdAt
        date updatedAt
    }

    STATION_ASSIGNMENT {
        string id PK
        string employeeId
        string stationId FK
        date assignedDate
        string updatedBy
    }

    ADDRESS_CHANGE_REQUEST {
        string id PK
        string employeeId
        object proposedAddress
        string reason
        string status
        string requestedBy
        object clerkApproval
        object transportAgentApproval
        date createdAt
    }

    EMPLOYEE {
        string id PK
        string workdayId
        string firstName
        string lastName
        string department
        string position
        object address
        string badgeId
        boolean isActive
        date joinDate
    }

    GPS_LOCATION {
        string id PK
        string addressReference
        number latitude
        number longitude
        date lastUpdated
        string locationType
    }

    TRANSPORT_PLAN {
        string id PK
        date date
        string status
        string countryCode
        string createdBy
        date createdAt
        date updatedAt
    }

    SHIFT_ASSIGNMENT {
        string id PK
        string planId FK
        string employeeId
        string stationId FK
        string shiftCode
        string routeCode
    }

    MINIBUS_ALLOCATION {
        string id PK
        string planId FK
        string timeSlot
        number employeeCount
        number minibusCount
        boolean isManuallyAdjusted
        string adjustmentReason
        string updatedBy
        date updatedAt
    }

    ROUTE_TRACKING {
        string id PK
        string routeCode
        date trackingDate
        string status
        timestamp departureTime
        timestamp estimatedArrivalTime
        number passengerCount
        string driverId
        string vehicleId
    }

    BOARDING_RECORD {
        string id PK
        string employeeId
        string shiftCode
        date date
        string stationId FK
        string routeCode
        string status
        string checkInMethod
        boolean nurseryNotification
        date checkInTime
        string updatedBy
        date updateTimestamp
    }

    PICKUP_STATION ||--o{ STATION_ASSIGNMENT : "assigned to"
    PICKUP_STATION ||--o{ ADDRESS_CHANGE_REQUEST : "requested for"
    EMPLOYEE ||--o{ STATION_ASSIGNMENT : "is assigned to"
    EMPLOYEE ||--o{ BOARDING_RECORD : "boards"
    EMPLOYEE ||--o{ ADDRESS_CHANGE_REQUEST : "requests"
    GPS_LOCATION ||--o{ PICKUP_STATION : "located at"
    TRANSPORT_PLAN ||--o{ SHIFT_ASSIGNMENT : "contains"
    TRANSPORT_PLAN ||--o{ MINIBUS_ALLOCATION : "allocates"
    PICKUP_STATION ||--o{ BOARDING_RECORD : "boards at"
    SHIFT_ASSIGNMENT }o--|| BOARDING_RECORD : "records boarding for"
    ROUTE_TRACKING ||--o{ BOARDING_RECORD : "tracked for"
</div></code></pre>
    <h3 id="62-microservice-specific-containers">
      6.2 Microservice-Specific Containers
    </h3>
    <h4 id="621-pickup-station-service-database">
      6.2.1 Pickup Station Service Database
    </h4>
    <p><strong>Containers:</strong></p>
    <ol>
      <li>
        <strong>stations</strong>
        <ul>
          <li>Partition Key: <code>/countryCode</code></li>
          <li>Purpose: Stores pickup station information</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"station-123"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"MOROCCO"</span>,
  <span class="hljs-attr">"name"</span>: <span class="hljs-string">"Kembah Mhedhya"</span>,
  <span class="hljs-attr">"address"</span>: {
    <span class="hljs-attr">"street"</span>: <span class="hljs-string">"123 Main Street"</span>,
    <span class="hljs-attr">"city"</span>: <span class="hljs-string">"Casablanca"</span>,
    <span class="hljs-attr">"region"</span>: <span class="hljs-string">"Casablanca-Settat"</span>,
    <span class="hljs-attr">"country"</span>: <span class="hljs-string">"Morocco"</span>,
    <span class="hljs-attr">"postalCode"</span>: <span class="hljs-string">"20250"</span>
  },
  <span class="hljs-attr">"gpsCoordinates"</span>: {
    <span class="hljs-attr">"latitude"</span>: <span class="hljs-number">33.5731</span>,
    <span class="hljs-attr">"longitude"</span>: <span class="hljs-number">-7.5898</span>
  },
  <span class="hljs-attr">"routeCode"</span>: <span class="hljs-string">"Trajet A"</span>,
  <span class="hljs-attr">"isActive"</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">"capacity"</span>: <span class="hljs-number">50</span>,
  <span class="hljs-attr">"createdBy"</span>: <span class="hljs-string">"user-456"</span>,
  <span class="hljs-attr">"createdAt"</span>: <span class="hljs-string">"2023-08-15T14:30:00Z"</span>,
  <span class="hljs-attr">"updatedAt"</span>: <span class="hljs-string">"2023-09-20T10:15:00Z"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"station"</span>
}
</div></code></pre>
    <ol start="2">
      <li>
        <strong>station_assignments</strong>
        <ul>
          <li>Partition Key: <code>/employeeId</code></li>
          <li>Purpose: Links employees to stations</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"assignment-789"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"emp-123"</span>,
  <span class="hljs-attr">"employeeId"</span>: <span class="hljs-string">"emp-123"</span>,
  <span class="hljs-attr">"stationId"</span>: <span class="hljs-string">"station-123"</span>,
  <span class="hljs-attr">"assignedDate"</span>: <span class="hljs-string">"2023-09-01T00:00:00Z"</span>,
  <span class="hljs-attr">"updatedBy"</span>: <span class="hljs-string">"user-456"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"station_assignment"</span>
}
</div></code></pre>
    <ol start="3">
      <li>
        <strong>address_change_requests</strong>
        <ul>
          <li>Partition Key: <code>/status</code></li>
          <li>Purpose: Handles requests for address changes</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"request-456"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"PENDING"</span>,
  <span class="hljs-attr">"employeeId"</span>: <span class="hljs-string">"emp-123"</span>,
  <span class="hljs-attr">"proposedAddress"</span>: {
    <span class="hljs-attr">"street"</span>: <span class="hljs-string">"456 New Street"</span>,
    <span class="hljs-attr">"city"</span>: <span class="hljs-string">"Casablanca"</span>,
    <span class="hljs-attr">"region"</span>: <span class="hljs-string">"Casablanca-Settat"</span>,
    <span class="hljs-attr">"country"</span>: <span class="hljs-string">"Morocco"</span>,
    <span class="hljs-attr">"postalCode"</span>: <span class="hljs-string">"20250"</span>
  },
  <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Moved to a new apartment"</span>,
  <span class="hljs-attr">"status"</span>: <span class="hljs-string">"PENDING"</span>,
  <span class="hljs-attr">"requestedBy"</span>: <span class="hljs-string">"tl-789"</span>,
  <span class="hljs-attr">"clerkApproval"</span>: {
    <span class="hljs-attr">"approved"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"date"</span>: <span class="hljs-string">"2023-09-25T11:30:00Z"</span>,
    <span class="hljs-attr">"by"</span>: <span class="hljs-string">"clerk-456"</span>
  },
  <span class="hljs-attr">"transportAgentApproval"</span>: <span class="hljs-literal">null</span>,
  <span class="hljs-attr">"createdAt"</span>: <span class="hljs-string">"2023-09-24T09:15:00Z"</span>,
  <span class="hljs-attr">"countryCode"</span>: <span class="hljs-string">"MOROCCO"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"address_change_request"</span>
}
</div></code></pre>
    <ol start="4">
      <li>
        <strong>employees</strong>
        <ul>
          <li>Partition Key: <code>/countryCode</code></li>
          <li>Purpose: Stores employee data (replaces Workday integration)</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"emp-123"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"MOROCCO"</span>,
  <span class="hljs-attr">"workdayId"</span>: <span class="hljs-string">"WD-987654"</span>,
  <span class="hljs-attr">"firstName"</span>: <span class="hljs-string">"Mohammed"</span>,
  <span class="hljs-attr">"lastName"</span>: <span class="hljs-string">"Alami"</span>,
  <span class="hljs-attr">"department"</span>: <span class="hljs-string">"Production"</span>,
  <span class="hljs-attr">"position"</span>: <span class="hljs-string">"Operator"</span>,
  <span class="hljs-attr">"address"</span>: {
    <span class="hljs-attr">"street"</span>: <span class="hljs-string">"123 Main Street"</span>,
    <span class="hljs-attr">"city"</span>: <span class="hljs-string">"Casablanca"</span>,
    <span class="hljs-attr">"region"</span>: <span class="hljs-string">"Casablanca-Settat"</span>,
    <span class="hljs-attr">"country"</span>: <span class="hljs-string">"Morocco"</span>,
    <span class="hljs-attr">"postalCode"</span>: <span class="hljs-string">"20250"</span>
  },
  <span class="hljs-attr">"badgeId"</span>: <span class="hljs-string">"APTIV-5678"</span>,
  <span class="hljs-attr">"isActive"</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">"joinDate"</span>: <span class="hljs-string">"2022-03-15T00:00:00Z"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"employee"</span>
}
</div></code></pre>
    <ol start="5">
      <li>
        <strong>gps_locations</strong>
        <ul>
          <li>Partition Key: <code>/locationType</code></li>
          <li>Purpose: Stores mapping data (replaces mapping service)</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"loc-456"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"STATION"</span>,
  <span class="hljs-attr">"addressReference"</span>: <span class="hljs-string">"station-123"</span>,
  <span class="hljs-attr">"latitude"</span>: <span class="hljs-number">33.5731</span>,
  <span class="hljs-attr">"longitude"</span>: <span class="hljs-number">-7.5898</span>,
  <span class="hljs-attr">"lastUpdated"</span>: <span class="hljs-string">"2023-08-15T14:30:00Z"</span>,
  <span class="hljs-attr">"locationType"</span>: <span class="hljs-string">"STATION"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"gps_location"</span>
}
</div></code></pre>
    <h4 id="622-transport-planning-service-database">
      6.2.2 Transport Planning Service Database
    </h4>
    <p><strong>Containers:</strong></p>
    <ol>
      <li>
        <strong>transport_plans</strong>
        <ul>
          <li>Partition Key: <code>/date</code></li>
          <li>Purpose: Stores transport plan information</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"plan-456"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"2023-10-15"</span>,
  <span class="hljs-attr">"date"</span>: <span class="hljs-string">"2023-10-15T00:00:00Z"</span>,
  <span class="hljs-attr">"status"</span>: <span class="hljs-string">"PUBLISHED"</span>,
  <span class="hljs-attr">"countryCode"</span>: <span class="hljs-string">"MOROCCO"</span>,
  <span class="hljs-attr">"createdBy"</span>: <span class="hljs-string">"user-789"</span>,
  <span class="hljs-attr">"createdAt"</span>: <span class="hljs-string">"2023-10-10T09:00:00Z"</span>,
  <span class="hljs-attr">"updatedAt"</span>: <span class="hljs-string">"2023-10-14T16:45:00Z"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"transport_plan"</span>
}
</div></code></pre>
    <ol start="2">
      <li>
        <strong>shift_assignments</strong>
        <ul>
          <li>Partition Key: <code>/planId</code></li>
          <li>Purpose: Tracks employee shifts within plans</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"shift-123"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"plan-456"</span>,
  <span class="hljs-attr">"planId"</span>: <span class="hljs-string">"plan-456"</span>,
  <span class="hljs-attr">"employeeId"</span>: <span class="hljs-string">"emp-123"</span>,
  <span class="hljs-attr">"stationId"</span>: <span class="hljs-string">"station-123"</span>,
  <span class="hljs-attr">"shiftCode"</span>: <span class="hljs-string">"M"</span>,
  <span class="hljs-attr">"routeCode"</span>: <span class="hljs-string">"Trajet A"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"shift_assignment"</span>
}
</div></code></pre>
    <ol start="3">
      <li>
        <strong>minibus_allocations</strong>
        <ul>
          <li>Partition Key: <code>/planId</code></li>
          <li>Purpose: Determines vehicle resources needed</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"allocation-789"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"plan-456"</span>,
  <span class="hljs-attr">"planId"</span>: <span class="hljs-string">"plan-456"</span>,
  <span class="hljs-attr">"timeSlot"</span>: <span class="hljs-string">"06:00"</span>,
  <span class="hljs-attr">"employeeCount"</span>: <span class="hljs-number">906</span>,
  <span class="hljs-attr">"minibusCount"</span>: <span class="hljs-number">46</span>,
  <span class="hljs-attr">"isManuallyAdjusted"</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">"updatedBy"</span>: <span class="hljs-string">"user-789"</span>,
  <span class="hljs-attr">"updatedAt"</span>: <span class="hljs-string">"2023-10-14T16:30:00Z"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"minibus_allocation"</span>
}
</div></code></pre>
    <h4 id="623-bus-boarding-service-database">
      6.2.3 Bus Boarding Service Database
    </h4>
    <p><strong>Containers:</strong></p>
    <ol>
      <li>
        <strong>boarding_records</strong>
        <ul>
          <li>Partition Key: <code>/date</code></li>
          <li>Purpose: Tracks employee boarding activities</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"boarding-123"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"2023-10-15"</span>,
  <span class="hljs-attr">"employeeId"</span>: <span class="hljs-string">"emp-123"</span>,
  <span class="hljs-attr">"shiftCode"</span>: <span class="hljs-string">"M"</span>,
  <span class="hljs-attr">"date"</span>: <span class="hljs-string">"2023-10-15T00:00:00Z"</span>,
  <span class="hljs-attr">"stationId"</span>: <span class="hljs-string">"station-123"</span>,
  <span class="hljs-attr">"routeCode"</span>: <span class="hljs-string">"Trajet A"</span>,
  <span class="hljs-attr">"status"</span>: <span class="hljs-string">"ON_BUS"</span>,
  <span class="hljs-attr">"checkInMethod"</span>: <span class="hljs-string">"BADGE"</span>,
  <span class="hljs-attr">"nurseryNotification"</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">"checkInTime"</span>: <span class="hljs-string">"2023-10-15T05:45:30Z"</span>,
  <span class="hljs-attr">"updatedBy"</span>: <span class="hljs-string">"user-456"</span>,
  <span class="hljs-attr">"updateTimestamp"</span>: <span class="hljs-string">"2023-10-15T05:45:30Z"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"boarding_record"</span>
}
</div></code></pre>
    <ol start="2">
      <li>
        <strong>route_tracking</strong>
        <ul>
          <li>Partition Key: <code>/trackingDate</code></li>
          <li>Purpose: Bus tracking information (replaces CW integration)</li>
          <li>Sample Item:</li>
        </ul>
      </li>
    </ol>
    <pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"tracking-456"</span>,
  <span class="hljs-attr">"partitionKey"</span>: <span class="hljs-string">"2023-10-15"</span>,
  <span class="hljs-attr">"routeCode"</span>: <span class="hljs-string">"Trajet A"</span>,
  <span class="hljs-attr">"trackingDate"</span>: <span class="hljs-string">"2023-10-15T00:00:00Z"</span>,
  <span class="hljs-attr">"status"</span>: <span class="hljs-string">"IN_PROGRESS"</span>,
  <span class="hljs-attr">"departureTime"</span>: <span class="hljs-string">"2023-10-15T06:00:00Z"</span>,
  <span class="hljs-attr">"estimatedArrivalTime"</span>: <span class="hljs-string">"2023-10-15T06:45:00Z"</span>,
  <span class="hljs-attr">"passengerCount"</span>: <span class="hljs-number">42</span>,
  <span class="hljs-attr">"driverId"</span>: <span class="hljs-string">"driver-789"</span>,
  <span class="hljs-attr">"vehicleId"</span>: <span class="hljs-string">"bus-123"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"route_tracking"</span>
}
</div></code></pre>
    <h3 id="63-data-synchronization">6.3 Data Synchronization</h3>
    <p>Data is synchronized across microservices using:</p>
    <ol>
      <li>
        <p><strong>Change Feed Processors</strong>:</p>
        <ul>
          <li>
            When a pickup station is created or updated, the change is
            propagated to the Transport Planning service
          </li>
          <li>
            When a transport plan is published, the change is propagated to the
            Bus Boarding service
          </li>
        </ul>
      </li>
      <li>
        <p><strong>Event-Driven Communication</strong>:</p>
        <ul>
          <li>
            Domain events published to Azure Service Bus trigger updates in
            dependent services
          </li>
          <li>
            Each service maintains its own read model optimized for its specific
            queries
          </li>
        </ul>
      </li>
    </ol>
    <h2 id="7-event-driven-architecture">7. Event-Driven Architecture</h2>
    <h3 id="71-event-schema">7.1 Event Schema</h3>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">interface</span> DomainEvent {
  id: <span class="hljs-built_in">string</span>;
  <span class="hljs-keyword">type</span>: <span class="hljs-built_in">string</span>;
  timestamp: <span class="hljs-built_in">Date</span>;
  correlationId: <span class="hljs-built_in">string</span>;
  data: <span class="hljs-built_in">any</span>;
  metadata: {
    producer: <span class="hljs-built_in">string</span>;
    version: <span class="hljs-built_in">string</span>;
  };
}
</div></code></pre>
    <h3 id="72-key-events-and-event-flow">7.2 Key Events and Event Flow</h3>
    <ol>
      <li>
        <p><strong>Station Management Events:</strong></p>
        <ul>
          <li><code>StationCreated</code></li>
          <li><code>StationUpdated</code></li>
          <li><code>StationAssigned</code></li>
          <li><code>AddressChangeRequested</code></li>
          <li><code>AddressChangeApproved</code></li>
        </ul>
      </li>
      <li>
        <p><strong>Transport Planning Events:</strong></p>
        <ul>
          <li><code>TransportPlanCreated</code></li>
          <li><code>TransportPlanUpdated</code></li>
          <li><code>TransportPlanPublished</code></li>
          <li><code>ShiftAssignmentAdded</code></li>
          <li><code>ShiftAssignmentRemoved</code></li>
          <li><code>MinibusAllocationUpdated</code></li>
        </ul>
      </li>
      <li>
        <p><strong>Bus Boarding Events:</strong></p>
        <ul>
          <li><code>PassengerBoarded</code></li>
          <li><code>BoardingStatusChanged</code></li>
        </ul>
      </li>
    </ol>
    <h3 id="73-event-flow-diagram">7.3 Event Flow Diagram</h3>
    <pre><code class="language-mermaid"><div class="mermaid">flowchart LR
    PickupService[Pickup Station Service] --> |produces| Events1[StationCreated\nStationUpdated\nStationAssigned\nAddressChangeRequested\nAddressChangeApproved]
    TransportService[Transport Planning Service] --> |produces| Events2[TransportPlanCreated\nTransportPlanPublished\nShiftAssignmentAdded\nMinibusAllocationUpdated]
    BoardingService[Bus Boarding Service] --> |produces| Events3[PassengerBoarded\nBoardingStatusChanged]

    Events1 --> ServiceBus{Azure Service Bus}
    Events2 --> ServiceBus
    Events3 --> ServiceBus

    ServiceBus --> |consumes| PickupService
    ServiceBus --> |consumes| TransportService
    ServiceBus --> |consumes| BoardingService

    subgraph "Event Topics"
        ServiceBus
    end
</div></code></pre>
    <h2 id="8-security-design">8. Security Design</h2>
    <h3 id="81-authentication-and-authorization">
      8.1 Authentication and Authorization
    </h3>
    <p><strong>Role-Based Access Control:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">enum</span> UserRole {
  TRANSPORT_AGENT = <span class="hljs-string">"TRANSPORT_AGENT"</span>,
  TEAM_LEADER = <span class="hljs-string">"TEAM_LEADER"</span>,
  CLERK = <span class="hljs-string">"CLERK"</span>,
  MANAGER = <span class="hljs-string">"MANAGER"</span>,
}

<span class="hljs-keyword">interface</span> Permission {
  resource: <span class="hljs-built_in">string</span>;
  actions: <span class="hljs-built_in">string</span>[];
}

<span class="hljs-keyword">const</span> rolePermissions: Record&lt;UserRole, Permission[]&gt; = {
  [UserRole.TRANSPORT_AGENT]: [
    { resource: <span class="hljs-string">"station"</span>, actions: [<span class="hljs-string">"create"</span>, <span class="hljs-string">"read"</span>, <span class="hljs-string">"update"</span>, <span class="hljs-string">"delete"</span>] },
    {
      resource: <span class="hljs-string">"transport-plan"</span>,
      actions: [<span class="hljs-string">"create"</span>, <span class="hljs-string">"read"</span>, <span class="hljs-string">"update"</span>, <span class="hljs-string">"delete"</span>, <span class="hljs-string">"publish"</span>],
    },
    {
      resource: <span class="hljs-string">"address-change-request"</span>,
      actions: [<span class="hljs-string">"read"</span>, <span class="hljs-string">"approve"</span>, <span class="hljs-string">"reject"</span>],
    },
  ],
  [UserRole.TEAM_LEADER]: [
    { resource: <span class="hljs-string">"station"</span>, actions: [<span class="hljs-string">"read"</span>] },
    { resource: <span class="hljs-string">"transport-plan"</span>, actions: [<span class="hljs-string">"read"</span>] },
    { resource: <span class="hljs-string">"address-change-request"</span>, actions: [<span class="hljs-string">"create"</span>, <span class="hljs-string">"read"</span>] },
    { resource: <span class="hljs-string">"boarding"</span>, actions: [<span class="hljs-string">"read"</span>, <span class="hljs-string">"update"</span>] },
  ],
  <span class="hljs-comment">// Other roles...</span>
};
</div></code></pre>
    <h3 id="82-data-protection">8.2 Data Protection</h3>
    <ul>
      <li>Encrypt sensitive data at rest and in transit</li>
      <li>Implement data retention policies</li>
      <li>Anonymize data for reporting</li>
    </ul>
    <h2 id="9-deployment-and-devops">9. Deployment and DevOps</h2>
    <h3 id="91-cicd-pipeline">9.1 CI/CD Pipeline</h3>
    <p><strong>CI/CD Pipeline Diagram:</strong></p>
    <pre><code class="language-mermaid"><div class="mermaid">flowchart LR
    Code[Code Repository] --> Build[Build & Test]
    Build --> UnitTests[Unit Tests]
    UnitTests --> IntegrationTests[Integration Tests]
    IntegrationTests --> Package[Package]
    Package --> Deploy[Deploy]

    subgraph Environments
        Dev[Development]
        QA[QA]
        Staging[Staging]
        Prod[Production]
    end

    Deploy --> Dev
    Dev --> QA
    QA --> Staging
    Staging --> Prod

    style Prod fill:#f96,stroke:#333,stroke-width:2px
</div></code></pre>
    <h3 id="92-monitoring-and-alerting">9.2 Monitoring and Alerting</h3>
    <p><strong>Key Metrics:</strong></p>
    <ul>
      <li>API response times through Azure API Management</li>
      <li>Event processing latency</li>
      <li>Database query performance</li>
      <li>Error rates by service</li>
    </ul>
    <p><strong>Health Checks:</strong></p>
    <pre
      class="hljs"
    ><code><div><span class="hljs-keyword">class</span> HealthCheck {
  <span class="hljs-keyword">async</span> checkService(): <span class="hljs-built_in">Promise</span>&lt;HealthStatus&gt; {
    <span class="hljs-keyword">const</span> dbStatus = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.checkDatabase();
    <span class="hljs-keyword">const</span> msgBusStatus = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.checkMessageBus();
    <span class="hljs-keyword">const</span> externalSystems = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.checkExternalSystems();

    <span class="hljs-keyword">return</span> {
      status: <span class="hljs-keyword">this</span>.calculateOverallStatus([
        dbStatus,
        msgBusStatus,
        ...externalSystems,
      ]),
      components: {
        database: dbStatus,
        messageBus: msgBusStatus,
        externalSystems,
      },
      timestamp: <span class="hljs-keyword">new</span> <span class="hljs-built_in">Date</span>(),
    };
  }
}
</div></code></pre>
    <h2 id="10-conclusion">10. Conclusion</h2>
    <p>
      This low-level design provides a comprehensive blueprint for implementing
      the Transport Management module using DDD and microservices architecture.
      The design emphasizes:
    </p>
    <ol>
      <li>
        <strong>Modularity</strong>: Clear separation of concerns across bounded
        contexts
      </li>
      <li>
        <strong>Configurability</strong>: Dynamic configurations for
        country-specific requirements
      </li>
      <li>
        <strong>Scalability</strong>: Event-driven architecture with independent
        scaling
      </li>
      <li>
        <strong>Maintainability</strong>: Clean domain models and business logic
        encapsulation
      </li>
      <li>
        <strong>Adaptability</strong>: Extensible design to accommodate future
        requirements
      </li>
    </ol>
    <p>
      The implementation follows industry best practices for cloud-native
      applications, ensuring reliability, performance, and security while
      providing the flexibility needed to meet specific client requirements
      across different countries.
    </p>
  </body>
</html>
