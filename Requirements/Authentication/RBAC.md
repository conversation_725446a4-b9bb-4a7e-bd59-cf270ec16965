# RBAC Implementation with Azure APIM and a Dedicated Permissions Microservice

This document outlines the approach to implementing Role-Based Access Control (RBAC) using Azure API Management (APIM) as a gateway. The roles and permissions are stored in a separate microservice that has access to the database. The permissions need to be verified based on identifiers such as user ID, email, UPN, or matriculation number.

---

## Overview

- **Authentication & Authorization in APIM:**  
  APIM can validate JWT tokens. It is suggested to use OAuth 2.0 or OpenID Connect with a token issuer like Azure AD. The token should include claims such as user ID or UPN, which can then be forwarded to the backend.

- **Separation of Concerns:**  
  Since the roles and permissions are stored in a separate microservice, APIM cannot directly access the database. There are two primary approaches:
  - Calling the microservice from APIM policies to check permissions.
  - Caching the roles within APIM to reduce latency.

---

## Technical Considerations

### 1. APIM Handling of Authentication and Authorization

- **JWT Validation:**  
  APIM validates the JWT token issued by Azure AD or a similar token issuer. The token includes claims (e.g., user ID, UPN) that are critical for further permission checks.

- **Claims Extraction:**  
  After validation, APIM extracts the user identifier (such as UPN) from the token for use in subsequent authorization steps.

### 2. Permissions Microservice

- **Role and Permission Retrieval:**  
  Since the roles and permissions reside in a separate microservice, APIM must either:

  - Invoke the microservice during each request using APIM policies.
  - Utilize a caching mechanism to store permissions temporarily, reducing the need for repeated calls.

- **Security of the Microservice:**  
  The microservice must be secure. APIM should authenticate to it using methods such as client certificates or OAuth. All communications must occur over HTTPS.

### 3. Caching Strategy

- **Caching Considerations:**

  - A call to the microservice during every request can introduce latency.
  - APIM's built-in cache may be used to store permissions with a defined TTL (Time-To-Live).
  - On a cache miss, APIM will call the microservice to retrieve the permissions and then cache them.

- **Distributed Cache Alternative:**  
  If multiple APIM instances are in use, note that the built-in cache is per instance. An external distributed cache like Redis might be considered if shared caching is required.

### 4. Mapping Permissions to API Operations

- **Policy-Based Permission Mapping:**  
  APIM policies can be used with variables to map required permissions to API operations. Each API operation may have a policy that defines the required permission, which is then compared against the permissions returned from the cache or microservice.

---

## Proposed Request Flow

1. **User Authentication:**

   - The user authenticates (e.g., via Azure AD) and obtains a JWT token.

2. **JWT Validation by APIM:**

   - APIM validates the JWT and extracts the user identifier (e.g., UPN).

3. **Cache Lookup:**

   - APIM checks its cache for the user's permissions using the extracted identifier.

4. **Microservice Call on Cache Miss:**

   - If the permissions are not found in the cache, APIM calls the permissions microservice (ensuring proper authentication) to retrieve them.

5. **Cache Storage:**

   - The retrieved permissions are stored in the cache with an appropriate TTL.

6. **Permission Evaluation:**

   - APIM evaluates whether the user's permissions include the required permission for the API operation.

7. **Request Handling:**
   - Based on the evaluation, the request is either allowed to proceed or denied (e.g., with a 403 status code).

---

## APIM Policy Components

The following policies may be used in APIM:

- **validate-jwt:** To validate the JWT token and extract necessary claims.
- **cache-lookup-value:** To check for the user permissions in the cache.
- **send-request:** To call the permissions microservice when a cache miss occurs.
- **cache-store-value:** To store the retrieved permissions.
- **validate-content:** To parse and validate the response from the microservice.

---

## Security and Performance Best Practices

- **Secure Communication:**

  - Ensure that the microservice endpoint is secured with mutual TLS or client certificates.
  - Enforce HTTPS for all communications.

- **Latency Management:**

  - While caching improves performance, the initial call may introduce latency.
  - Set an appropriate TTL to balance data freshness with performance.

- **Error Handling:**

  - Implement retries and a circuit breaker pattern in APIM policies to handle temporary microservice failures.
  - Log authorization failures and monitor latency using tools such as Azure Monitor.

- **Testing Scenarios:**
  - Evaluate cache hit and miss scenarios.
  - Test behavior when the microservice is down, ensuring APIM handles the error gracefully (e.g., by denying access by default or using stale cache entries if possible).

---

## Summary

The approach involves using APIM to validate JWT tokens, extract user identifiers, and then either retrieve or cache the user's permissions from a dedicated microservice. The policies in APIM perform the critical operations of JWT validation, caching, microservice calls, and permission evaluation. Security best practices include securing communication between APIM and the microservice and monitoring performance to adjust the caching strategy as needed.

This document outlines the steps and considerations for implementing RBAC in a system where permissions are maintained in a separate microservice, ensuring both performance and security best practices are met.

## Solution Steps Implementation Guide

To implement RBAC in Azure API Management (APIM) with roles/permissions stored in a separate microservice, follow this structured approach:

---

### **1. Authentication & Token Validation**

- **Use OAuth 2.0/OpenID Connect**:  
  Authenticate users via Azure AD or another IdP. APIM validates the JWT token and extracts claims (e.g., `UPN`, `email`, `sub`).
- **APIM Policy Example**:
  ```xml
  <validate-jwt header-name="Authorization" failed-validation-httpcode="401">
    <openid-config url="https://login.microsoftonline.com/tenant/v2.0/.well-known/openid-configuration" />
    <required-claims>
      <claim name="upn" match="any" />
    </required-claims>
  </validate-jwt>
  ```

---

### **2. Fetch Roles/Permissions from Microservice**

- **Call the Microservice Securely**:  
  Use APIM policies to call the RBAC microservice endpoint. Secure communication with:
  - **HTTPS** for encryption.
  - **Client Certificates** or **OAuth Client Credentials** for mutual authentication.
- **Example Policy**:
  ```xml
  <send-request mode="new" response-variable-name="rbacResponse" timeout="10" ignore-error="false">
    <set-url>https://rbac-service.example.com/permissions?upn=@(context.Request.Headers["Authorization"].First().Split(' ')[1].AsJwt().Claims["upn"])</set-url>
    <set-method>GET</set-method>
    <authentication-certificate thumbprint="..." />
  </send-request>
  ```

---

### **3. Caching for Performance**

- **Cache Permissions in APIM**:  
  Store fetched permissions in APIM's built-in cache (in-memory or external Redis) to minimize latency.
  - **Cache Key**: Use `UPN` or `sub` from the JWT.
  - **TTL**: Set based on permission volatility (e.g., 5–15 minutes).
- **Policy Example**:
  ```xml
  <cache-lookup-value key="@("perms-" + context.Request.Headers["Authorization"].First().Split(' ')[1].AsJwt().Claims["upn"])" variable-name="cachedPermissions" />
  <choose>
    <when condition="@(!context.Variables.ContainsKey("cachedPermissions"))">
      <!-- Fetch from microservice -->
      <cache-store-value key="@("perms-" + upn)" value="@((string)rbacResponse.Body)" duration="300" />
    </when>
  </choose>
  ```

---

### **4. Authorization Policy**

- **Map Permissions to Operations**:  
  Define required permissions per API operation using APIM policies.
- **Example Policy**:
  ```xml
  <choose>
    <when condition="@(!context.Variables.GetValueOrDefault("cachedPermissions", "").Contains("required_permission"))">
      <return-response>
        <set-status code="403" reason="Forbidden" />
        <set-body>{"error": "Insufficient permissions"}</set-body>
      </return-response>
    </when>
  </choose>
  ```

---

### **5. Security Best Practices**

- **Secure the Microservice**:
  - Enforce mutual TLS (mTLS) or API keys.
  - Rate-limit requests to prevent abuse.
- **Least Privilege**:  
  Assign minimal required permissions to APIM for accessing the RBAC microservice.
- **Audit Logs**:  
  Log authorization decisions and failures in Azure Monitor or Application Insights.

---

### **6. Error Handling & Resilience**

- **Circuit Breaker**:  
  Use policies to avoid overloading the RBAC microservice during failures:
  ```xml
  <retry condition="@(context.Response == null || context.Response.StatusCode >= 500)" count="3" interval="1" />
  ```
- **Fallback**:  
  Return `403` if the RBAC microservice is unreachable (avoid caching stale data unless acceptable).

---

### **7. Performance Optimization**

- **Bulk Requests**:  
  If possible, fetch all permissions in a single call to reduce round trips.
- **Edge Caching**:  
  Use Azure Front Door or CDN for geographically distributed caching if applicable.

---

### **8. Testing & Validation**

- **Test Scenarios**:
  - Cache hit/miss.
  - Microservice downtime.
  - Permission changes (ensure cache TTL aligns with business needs).
- **Load Testing**:  
  Simulate high traffic to validate latency impact.

---

### **Summary**

- **Authentication**: Validate JWT tokens with claims.
- **Authorization**: Fetch permissions from the microservice, cache them, and enforce via policies.
- **Performance**: Cache aggressively, use resilient communication.
- **Security**: Secure all endpoints, follow least privilege, and monitor logs.

This approach balances security, performance, and maintainability while adhering to Azure APIM best practices.

## System Architecture Diagram

```mermaid
sequenceDiagram
    participant Client
    participant APIM as Azure APIM Gateway
    participant AD as Azure AD
    participant Cache as Redis Cache
    participant RBAC as RBAC Microservice
    participant DB as Database
    participant API as Backend APIs

    Client->>APIM: Request with JWT
    activate APIM

    APIM->>AD: Validate JWT Token
    AD-->>APIM: Token Valid + Claims

    APIM->>Cache: Check Permissions Cache
    alt Cache Hit
        Cache-->>APIM: Return Cached Permissions
    else Cache Miss
        APIM->>RBAC: Request Permissions
        activate RBAC
        RBAC->>DB: Query Permissions
        DB-->>RBAC: Return Permissions
        RBAC-->>APIM: Return Permissions
        deactivate RBAC
        APIM->>Cache: Store Permissions (TTL)
    end

    alt Authorized
        APIM->>API: Forward Request
        API-->>APIM: Response
        APIM-->>Client: Success Response
    else Unauthorized
        APIM-->>Client: 403 Forbidden
    end

    deactivate APIM
```

## Architecture Components Summary

### Flow Description

1. **Client Request**:

   - Client sends request with JWT token
   - Token contains claims (UPN, email, etc.)

2. **Authentication (Azure AD)**:

   - APIM validates JWT with Azure AD
   - Extracts user claims for authorization

3. **Permission Check**:

   - First checks Redis cache
   - On cache miss, calls RBAC microservice
   - Caches new permissions with TTL

4. **Authorization**:
   - Evaluates permissions against requirements
   - Either forwards request or returns 403

### Key Benefits

- **High Performance**: Multi-level caching strategy
- **Security**: JWT validation, mTLS, least privilege
- **Scalability**: Distributed architecture
- **Resilience**: Circuit breakers, retries
- **Maintainability**: Clear separation of concerns

## Network Security and Implementation Details

### 1. Network Security Architecture

**Private Network Setup**:

- Keep APIM and AKS in the **same virtual network** or peered networks
- Use **Azure Private Link** for private endpoint connectivity to AKS
- Ensure **Network Security Groups (NSGs)** restrict traffic to only required ports/sources

**AKS Security**:

- Deploy AKS as a **private cluster** (no public API server endpoint)
- Use **Azure Network Policies** or **Calico** for pod-level network segmentation

### 2. Secure Communication Implementation

#### Option A: Mutual TLS (mTLS) with Client Certificates

1. **Certificate Setup**:

   ```yaml
   # NGINX Ingress Configuration
   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     annotations:
       nginx.ingress.kubernetes.io/auth-tls-verify-client: "on"
       nginx.ingress.kubernetes.io/auth-tls-secret: "rbac-ns/mtls-secret"
   spec:
     tls:
       - hosts:
           - rbac-service.internal
         secretName: tls-secret
   ```

2. **APIM Policy for mTLS**:
   ```xml
   <send-request>
     <set-url>https://rbac-service.internal/permissions</set-url>
     <set-method>GET</set-method>
     <authentication-certificate thumbprint="{{APIM_CLIENT_CERT_THUMBPRINT}}" />
   </send-request>
   ```

#### Option B: OAuth 2.0 Client Credentials Flow

- APIM authenticates to Azure AD using client ID/secret
- Token passed via Authorization header
- Microservice validates token with Azure AD

### 3. Enhanced Security Policies

#### NGINX Ingress Security Configuration

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/whitelist-source-range: "<APIM_PRIVATE_IP>/32"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "<APIM-IP>"
spec:
  ingressClassName: nginx
  rules:
    - host: rbac-service.internal
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rbac-service
                port:
                  number: 80
```

#### Network Policy for Pod Security

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: rbac-service-policy
spec:
  podSelector:
    matchLabels:
      app: rbac-service
  policyTypes:
    - Ingress
  ingress:
    - from:
        - ipBlock:
            cidr: "<APIM-SUBNET-RANGE>"
      ports:
        - protocol: TCP
          port: 80
```

### 4. Resilience and Caching Policies

#### Circuit Breaker Implementation

```xml
<retry condition="@(context.Response.StatusCode >= 500)" count="3" interval="1" />
<set-variable name="circuitBreaker" value="@(context.Variables.GetValueOrDefault("cb", 0))" />
<choose>
  <when condition="@((int)context.Variables["circuitBreaker"] > 5)">
    <return-response>
      <set-status code="503" />
    </return-response>
  </when>
</choose>
```

#### Enhanced Caching Strategy

```xml
<cache-lookup-value key="@("perms-" + context.User.Id)" variable-name="cachedPerms" />
<cache-store-value key="@("perms-" + context.User.Id)" value="@(rbacResponse.Body)" duration="600" />
```

### 5. Security Hardening Steps

1. **Azure Key Vault Integration**:

   - Store all certificates and secrets
   - Use APIM's managed identity for secure access

2. **AKS Pod Identity**:

   - Implement Azure Workload Identity
   - Secure access to Azure resources

3. **Comprehensive Logging**:
   - Enable Azure Monitor
   - Track all authorization decisions
   - Set up alerts for security events

### 6. Private Network Configuration

1. **DNS Configuration**:

   - Set up Azure Private DNS zones
   - Configure service discovery

2. **APIM Setup**:

   - Deploy in Internal mode
   - Configure private endpoints

3. **AKS Configuration**:
   - Use internal load balancer
   - Configure private cluster endpoints

### 7. Implementation Commands

```bash
# Create namespace and configure
kubectl create namespace rbac-system
kubectl label namespace rbac-system purpose=rbac

# Apply security policies
kubectl apply -f rbac-network-policy.yaml

# Configure service account
kubectl create serviceaccount rbac-service-account
kubectl create role rbac-service-role --verb=get,list --resource=secrets,configmaps
kubectl create rolebinding rbac-service-binding --role=rbac-service-role --serviceaccount=rbac-system:rbac-service-account

# Enable monitoring
az aks enable-addons -a monitoring -n cluster-name -g resource-group
```

### 8. Performance Optimization Guidelines

1. **Connection Management**:

   - Configure keep-alive settings
   - Optimize connection pooling

2. **API Design**:

   - Implement bulk permission fetching
   - Minimize round trips

3. **Cache Optimization**:
   - Use external Redis cache for scaling
   - Implement proper TTL strategy

### Security Architecture Summary

```mermaid
graph LR
    A[Client] -->|JWT| B[APIM Internal VNet]
    B -->|mTLS + Client Cert| C[NGINX Ingress]
    C -->|Internal| D[AKS RBAC Service]
    D -->|Private Endpoint| E[Database]

    subgraph Private Network
    B
    C
    D
    E
    end
```

This implementation provides:

- Zero-trust security model
- Defense-in-depth approach
- High availability and resilience
- Comprehensive monitoring and auditing
- Performance optimization
- Secure service-to-service communication
