<!DOCTYPE html>
<html>
<head>
<title>auth-LLD.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="authentication-authorization-and-delegation-lld">Authentication, Authorization, and Delegation LLD</h1>
<h2 id="document-information">Document Information</h2>
<p><strong>Version:</strong> 2.0.0<br>
<strong>Last Updated:</strong> 2025-03-04<br>
<strong>Status:</strong> Completed
<strong>Authors: <AUTHORS>
<h2 id="executive-summary">Executive Summary</h2>
<p>This Low-Level Design (LLD) document provides a comprehensive technical specification for implementing a secure, scalable, and integrated authentication, authorization, and delegation system for the Connected Workers platform. The system leverages Microsoft Entra ID (formerly Azure AD) for identity management, combining SAML-based Single Sign-On (SSO) with JWT-based session management and just-in-time user provisioning through Microsoft Graph API.</p>
<h3 id="key-features">Key Features</h3>
<ul>
<li><strong>Unified Authentication</strong>: SAML SSO with JWT token management</li>
<li><strong>Role-Based Access Control</strong>: Hierarchical roles with granular permissions</li>
<li><strong>Profile-Based Authorization</strong>: Support for multiple role contexts per user</li>
<li><strong>Flexible Delegation</strong>: Secure authority transfer with hierarchical rules</li>
<li><strong>Real-Time Synchronization</strong>: Continuous user data sync with Azure AD</li>
<li><strong>Enterprise-Grade Security</strong>: Industry-standard encryption and security practices</li>
</ul>
<h3 id="business-benefits">Business Benefits</h3>
<ul>
<li><strong>Enhanced Security</strong>: Centralized identity management with Azure AD</li>
<li><strong>Improved User Experience</strong>: Seamless authentication and profile switching</li>
<li><strong>Operational Flexibility</strong>: Structured delegation for business continuity</li>
<li><strong>Reduced Administration</strong>: Automated user provisioning and role assignment</li>
<li><strong>Compliance Ready</strong>: Comprehensive audit logging and security controls</li>
</ul>
<h3 id="document-focus">Document Focus</h3>
<p>This LLD focuses on five core areas of the authentication, authorization, and delegation system:</p>
<ol>
<li><strong>Authentication Architecture</strong>: SAML SSO and JWT token management</li>
<li><strong>Authorization Framework</strong>: Role-based access with profile contexts</li>
<li><strong>Delegation Framework</strong>: Hierarchical delegation with approval workflows</li>
<li><strong>Microsoft Graph API Integration</strong>: Real-time user data synchronization</li>
<li><strong>Data Model and Best Practices</strong>: Foundational design recommendations</li>
</ol>
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li>
<p><a href="#1-overview">Overview</a></p>
<ul>
<li><a href="#11-purpose-and-scope">Purpose and Scope</a></li>
<li><a href="#12-key-components">Key Components</a></li>
</ul>
</li>
<li>
<p><a href="#2-authentication-architecture">Authentication Architecture</a></p>
<ul>
<li><a href="#21-saml-based-single-sign-on">SAML-based Single Sign-On</a>
<ul>
<li><a href="#211-core-components">Core Components</a></li>
<li><a href="#212-sp-initiated-flow">SP-Initiated Flow</a></li>
<li><a href="#213-idp-initiated-flow">IdP-Initiated Flow</a></li>
<li><a href="#214-saml-message-structure">SAML Message Structure</a></li>
</ul>
</li>
<li><a href="#22-jwt-authentication">JWT Authentication</a>
<ul>
<li><a href="#221-token-types-and-structure">Token Types and Structure</a></li>
<li><a href="#222-token-management">Token Management</a></li>
<li><a href="#223-token-storage-strategy">Token Storage Strategy</a></li>
</ul>
</li>
<li><a href="#23-token-refresh-implementation">Token Refresh Implementation</a>
<ul>
<li><a href="#231-refresh-process">Refresh Process</a></li>
<li><a href="#232-user-data-synchronization">User Data Synchronization</a></li>
<li><a href="#233-error-handling">Error Handling</a></li>
</ul>
</li>
</ul>
</li>
<li>
<p><a href="#3-authorization-framework">Authorization Framework</a></p>
<ul>
<li><a href="#31-core-authorization-concepts">Core Authorization Concepts</a>
<ul>
<li><a href="#311-role-management">Role Management</a></li>
<li><a href="#312-permission-framework">Permission Framework</a></li>
<li><a href="#313-scope-management">Scope Management</a></li>
</ul>
</li>
<li><a href="#32-role-based-access-control">Role-Based Access Control</a>
<ul>
<li><a href="#321-role-types-and-hierarchy">Role Types and Hierarchy</a></li>
<li><a href="#322-permission-management">Permission Management</a></li>
</ul>
</li>
<li><a href="#33-group-based-authorization">Group-Based Authorization</a>
<ul>
<li><a href="#331-dynamic-groups">Dynamic Groups</a></li>
<li><a href="#332-group-to-role-mapping">Group to Role Mapping</a></li>
</ul>
</li>
<li><a href="#34-profile-based-authorization">Profile-Based Authorization</a>
<ul>
<li><a href="#341-profile-types-and-lifecycle">Profile Types and Lifecycle</a></li>
<li><a href="#342-profile-switching">Profile Switching</a></li>
<li><a href="#343-profile-context-in-tokens">Profile Context in Tokens</a></li>
</ul>
</li>
</ul>
</li>
<li>
<p><a href="#4-delegation-framework">Delegation Framework</a></p>
<ul>
<li><a href="#41-delegation-process">Delegation Process</a></li>
<li><a href="#42-delegation-restrictions">Delegation Restrictions</a></li>
<li><a href="#43-delegation-lifecycle">Delegation Lifecycle</a></li>
<li><a href="#44-hierarchical-delegation-rules">Hierarchical Delegation Rules</a></li>
<li><a href="#45-delegation-authorization-model">Delegation Authorization Model</a></li>
<li><a href="#46-cross-level-and-same-level-delegation">Cross-Level and Same-Level Delegation</a></li>
<li><a href="#47-advanced-delegation-scenarios">Advanced Delegation Scenarios</a>
<ul>
<li><a href="#471-emergency-access">Emergency Access</a></li>
<li><a href="#472-role-conflict-management">Role Conflict Management</a></li>
<li><a href="#473-temporary-access-management">Temporary Access Management</a></li>
</ul>
</li>
</ul>
</li>
<li>
<p><a href="#5-microsoft-graph-api-integration">Microsoft Graph API Integration</a></p>
<ul>
<li><a href="#51-service-principal-configuration">Service Principal Configuration</a></li>
<li><a href="#52-user-data-management">User Data Management</a></li>
<li><a href="#53-group-membership-management">Group Membership Management</a></li>
<li><a href="#54-delta-query-implementation">Delta Query Implementation</a></li>
</ul>
</li>
<li>
<p><a href="#6-data-model">Data Model</a></p>
<ul>
<li><a href="#61-core-entities">Core Entities</a></li>
<li><a href="#62-database-tables">Database Tables</a></li>
<li><a href="#63-key-relationships">Key Relationships</a></li>
</ul>
</li>
<li>
<p><a href="#7-conclusion">Conclusion</a></p>
<ul>
<li><a href="#71-key-architecture-benefits">Key Architecture Benefits</a></li>
<li><a href="#72-implementation-strategy">Implementation Strategy</a></li>
<li><a href="#73-success-criteria">Success Criteria</a></li>
</ul>
</li>
</ol>
<h2 id="1-overview">1. Overview</h2>
<h3 id="11-purpose-and-scope">1.1 Purpose and Scope</h3>
<h4 id="purpose">Purpose</h4>
<p>This document serves as a technical blueprint for implementing the authentication, authorization, and delegation system. It provides detailed specifications for:</p>
<ol>
<li>
<p><strong>Authentication Mechanisms</strong></p>
<ul>
<li>SAML-based Single Sign-On integration with Azure AD</li>
<li>JWT token management for session handling</li>
<li>Token refresh and synchronization processes</li>
</ul>
</li>
<li>
<p><strong>Authorization Framework</strong></p>
<ul>
<li>Role-based access control implementation</li>
<li>Permission and scope management</li>
<li>Profile-based authorization system</li>
</ul>
</li>
<li>
<p><strong>Delegation System</strong></p>
<ul>
<li>Hierarchical delegation rules</li>
<li>Temporary access management</li>
<li>profile Delegation with restrictions</li>
</ul>
</li>
</ol>
<h4 id="scope">Scope</h4>
<p>The system encompasses:</p>
<ul>
<li>User authentication and session management</li>
<li>Role and permission management</li>
<li>Profile-based access control</li>
<li>Delegation workflows</li>
<li>Integration with Microsoft Graph API</li>
<li>Security implementations</li>
<li>Operational considerations</li>
</ul>
<h3 id="12-key-components">1.2 Key Components</h3>
<h4 id="core-services">Core Services</h4>
<ol>
<li>
<p><strong>Authentication Service</strong></p>
<ul>
<li>SAML SSO processing</li>
<li>JWT token issuance and validation</li>
<li>Session management</li>
<li>Token refresh handling</li>
</ul>
</li>
<li>
<p><strong>Authorization Service</strong></p>
<ul>
<li>Permission evaluation</li>
<li>Role management</li>
<li>Scope validation</li>
<li>Profile context handling</li>
</ul>
</li>
<li>
<p><strong>Delegation Service</strong></p>
<ul>
<li>Delegation request processing</li>
<li>Authority transfer management</li>
<li>Temporary access control</li>
</ul>
</li>
<li>
<p><strong>Profile Service</strong></p>
<ul>
<li>Profile lifecycle management</li>
<li>Context switching</li>
<li>Profile synchronization</li>
<li>Role assignment management</li>
</ul>
</li>
<li>
<p><strong>Sync Service</strong></p>
<ul>
<li>Graph API integration</li>
<li>User data synchronization</li>
<li>Group membership management</li>
<li>Delta query handling</li>
</ul>
</li>
</ol>
<h2 id="2-authentication-architecture">2. Authentication Architecture</h2>
<h3 id="21-saml-based-single-sign-on">2.1 SAML-based Single Sign-On</h3>
<p>The SAML-based Single Sign-On (SSO) implementation provides secure authentication using Microsoft Entra ID as the Identity Provider (IdP). This section details the core components, authentication flows, and technical specifications for the Connected Workers platform.</p>
<h4 id="211-core-components">2.1.1 Core Components</h4>
<ol>
<li>
<p><strong>Identity Provider (IdP) - Microsoft Entra ID</strong></p>
<ul>
<li>Central authentication authority for the Connected Workers platform</li>
<li>Manages user credentials and verification processes</li>
<li>Generates and cryptographically signs SAML assertions</li>
<li>Provides multi-factor authentication capabilities</li>
<li>Manages user sessions at the IdP level</li>
<li>Generates claims based on user attributes and group memberships</li>
<li>Maintains certificate for signing SAML responses (valid until February 18, 2028)</li>
</ul>
</li>
<li>
<p><strong>Service Provider (SP) - Connected Workers Application</strong></p>
<ul>
<li>Manages protected resources and access control</li>
<li>Generates and signs SAML authentication requests</li>
<li>Validates and processes SAML responses</li>
<li>Establishes and manages user sessions</li>
<li>Issues JWT tokens for authenticated sessions</li>
<li>Manages certificate and key pairs for secure communication</li>
<li>Implements certificate rotation and expiration monitoring</li>
</ul>
</li>
<li>
<p><strong>Assertion Consumer Service (ACS)</strong></p>
<ul>
<li>Dedicated endpoint (<code>/api/v1/auth/acs</code>) for processing SAML responses</li>
<li>Validates response signatures using Entra ID's certificate</li>
<li>Extracts user attributes and identity information</li>
<li>Generates session tokens (JWT) for authenticated users</li>
<li>Establishes security context for the user session</li>
<li>Implements comprehensive error handling and security logging</li>
<li>Supports both development and production environments with flexible configuration</li>
</ul>
</li>
</ol>
<h4 id="212-sp-initiated-flow">2.1.2 SP-Initiated Flow</h4>
<p>The SP-initiated flow is triggered when a user attempts to access the application directly:</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant SP as Service Provider
    participant IdP as Entra ID

    User->>SP: 1. Access Protected Resource
    Note over SP: 2. Check for valid session
    SP->>SP: 3. Generate SAML Request with ID, issuer, ACS URL
    SP->>User: 4. Redirect to Entra ID with SAML Request
    User->>IdP: 5. Forward SAML Request
    Note over IdP: 6. Validate Request (issuer, signature)
    alt User Not Authenticated
        IdP->>User: 7a. Present Authentication UI
        User->>IdP: 8a. Provide Credentials
        Note over IdP: 9a. Validate Credentials
    else User Has Active Session
        Note over IdP: 7b. Identify User from Session
    end
    Note over IdP: 10. Generate SAML Response
    Note over IdP: 11. Sign Response & Assertions
    IdP->>User: 12. Redirect to SP's ACS URL with SAML Response
    User->>SP: 13. Forward SAML Response
    Note over SP: 14. Validate Response (signature, conditions, audience)
    Note over SP: 15. Extract User Attributes & Create Session
    SP->>User: 16. Grant Access & Set Session Cookie/JWT
</div></code></pre>
<p><strong>Implementation Details:</strong></p>
<ol>
<li>
<p><strong>Initial Application Access</strong></p>
<ul>
<li>When a user navigates to a protected resource, the application checks for valid JWT tokens</li>
<li>If no valid token exists, the authentication flow is initiated</li>
<li>The application's authentication guard redirects to the login endpoint</li>
</ul>
</li>
<li>
<p><strong>SAML Request Generation</strong></p>
<ul>
<li>The <code>SamlStrategy</code> class generates a SAML AuthnRequest with:
<ul>
<li>Unique request ID for tracking and security</li>
<li>Issuer identifier configured in the application</li>
<li>ACS URL where Entra ID should send the response</li>
<li>Signature using the service provider's private key</li>
</ul>
</li>
<li>The request is configured with SHA-256 signature and digest algorithms</li>
</ul>
</li>
<li>
<p><strong>Redirection to Identity Provider</strong></p>
<ul>
<li>The application redirects the user to Entra ID's SSO endpoint</li>
<li>The request includes RelayState to preserve the original URL</li>
<li>The SAML request is compressed and encoded for transmission</li>
</ul>
</li>
<li>
<p><strong>Response Processing</strong></p>
<ul>
<li>The ACS endpoint receives the SAML response via HTTP POST</li>
<li>The <code>validate</code> method in <code>SamlStrategy</code> processes the response:
<ul>
<li>Verifies the digital signature using Entra ID's certificate</li>
<li>Validates the issuer matches the expected value</li>
<li>Extracts user information (email, name, groups, etc.)</li>
</ul>
</li>
<li>The <code>findOrCreateUserFromSaml</code> method in <code>AuthService</code> creates or updates the user record</li>
</ul>
</li>
<li>
<p><strong>Session Establishment</strong></p>
<ul>
<li>The <code>generateTokens</code> method creates JWT access and refresh tokens</li>
<li>Tokens include user identity, roles, and standard JWT claims</li>
<li>The access token has a short expiration (typically 15 minutes)</li>
<li>The refresh token has a longer expiration (typically 7 days)</li>
<li>Secure HTTP-only cookies are set with appropriate security flags</li>
</ul>
</li>
</ol>
<h4 id="213-idp-initiated-flow">2.1.3 IdP-Initiated Flow</h4>
<p>The IdP-initiated flow starts when users access the application through the Microsoft Entra ID portal:</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant IdP as Entra ID
    participant SP as Service Provider

    User->>IdP: 1. Access Entra ID Portal/MyApps
    IdP->>User: 2. Display Available Applications
    User->>IdP: 3. Select Application Tile
    Note over IdP: 4. Check User Authentication
    alt User Not Authenticated
        IdP->>User: 5a. Present Authentication UI
        User->>IdP: 6a. Provide Credentials
        Note over IdP: 7a. Validate Credentials
    end
    Note over IdP: 8. Generate SAML Response
    Note over IdP: 9. Include App-specific Attributes & Roles
    IdP->>User: 10. Redirect to SP with SAML Response
    User->>SP: 11. Submit SAML Response
    Note over SP: 12. Validate Response
    Note over SP: 13. Extract User Identity & Attributes
    Note over SP: 14. Create User Session
    SP->>User: 15. Redirect to Default Landing Page
</div></code></pre>
<p><strong>Implementation Details:</strong></p>
<ol>
<li>
<p><strong>Portal Access and Application Selection</strong></p>
<ul>
<li>User logs into Microsoft Entra ID portal (myapplications.microsoft.com)</li>
<li>Selects the Connected Workers application tile</li>
<li>Entra ID initiates the SAML flow without a prior request</li>
</ul>
</li>
<li>
<p><strong>SAML Response Processing</strong></p>
<ul>
<li>The application's ACS endpoint receives an unsolicited SAML response</li>
<li>The <code>SamlStrategy</code> validates the response with additional security checks</li>
<li>The system extracts user attributes including:
<ul>
<li>Email address (nameID)</li>
<li>Display name</li>
<li>First and last name</li>
<li>Object ID (unique identifier in Azure AD)</li>
<li>Group memberships for role mapping</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>User Session Creation</strong></p>
<ul>
<li>The application creates a user session with appropriate permissions</li>
<li>JWT tokens are generated with the same process as SP-initiated flow</li>
<li>The user is redirected to the application's default landing page</li>
<li>The session includes all necessary context for authorization</li>
</ul>
</li>
</ol>
<h4 id="214-saml-message-structure">2.1.4 SAML Message Structure</h4>
<ol>
<li><strong>SAML Request Structure</strong></li>
</ol>
<pre class="hljs"><code><div>   <span class="hljs-tag">&lt;<span class="hljs-name">samlp:AuthnRequest</span>
     <span class="hljs-attr">xmlns:samlp</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:protocol"</span>
     <span class="hljs-attr">xmlns:saml</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:assertion"</span>
     <span class="hljs-attr">ID</span>=<span class="hljs-string">"${requestID}"</span>
     <span class="hljs-attr">Version</span>=<span class="hljs-string">"2.0"</span>
     <span class="hljs-attr">IssueInstant</span>=<span class="hljs-string">"${timestamp}"</span>
     <span class="hljs-attr">Destination</span>=<span class="hljs-string">"${ssoEndpoint}"</span>
     <span class="hljs-attr">AssertionConsumerServiceURL</span>=<span class="hljs-string">"${acsUrl}"</span>
     <span class="hljs-attr">ProtocolBinding</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"</span>&gt;</span>
     <span class="hljs-tag">&lt;<span class="hljs-name">saml:Issuer</span>&gt;</span>${spEntityID}<span class="hljs-tag">&lt;/<span class="hljs-name">saml:Issuer</span>&gt;</span>
     <span class="hljs-tag">&lt;<span class="hljs-name">samlp:NameIDPolicy</span>
       <span class="hljs-attr">Format</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"</span>
       <span class="hljs-attr">AllowCreate</span>=<span class="hljs-string">"true"</span> /&gt;</span>
   <span class="hljs-tag">&lt;/<span class="hljs-name">samlp:AuthnRequest</span>&gt;</span>
</div></code></pre>
<ol start="2">
<li>
<p><strong>SAML Response Structure</strong></p>
<p>Based on the EMEA-EDS-Connected Workers System XML file, the SAML response follows this structure:</p>
</li>
</ol>
<pre class="hljs"><code><div>   <span class="hljs-tag">&lt;<span class="hljs-name">samlp:Response</span>
     <span class="hljs-attr">xmlns:samlp</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:protocol"</span>
     <span class="hljs-attr">xmlns:saml</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:assertion"</span>
     <span class="hljs-attr">ID</span>=<span class="hljs-string">"${responseID}"</span>
     <span class="hljs-attr">Version</span>=<span class="hljs-string">"2.0"</span>
     <span class="hljs-attr">IssueInstant</span>=<span class="hljs-string">"${timestamp}"</span>
     <span class="hljs-attr">Destination</span>=<span class="hljs-string">"${acsUrl}"</span>&gt;</span>
     <span class="hljs-tag">&lt;<span class="hljs-name">saml:Issuer</span>&gt;</span>https://sts.windows.net/6b1311e5-123f-49db-acdf-8847c2d00bed/<span class="hljs-tag">&lt;/<span class="hljs-name">saml:Issuer</span>&gt;</span>
     <span class="hljs-tag">&lt;<span class="hljs-name">Signature</span> <span class="hljs-attr">xmlns</span>=<span class="hljs-string">"http://www.w3.org/2000/09/xmldsig#"</span>&gt;</span>
       <span class="hljs-comment">&lt;!-- Signature details with SHA-256 algorithm --&gt;</span>
     <span class="hljs-tag">&lt;/<span class="hljs-name">Signature</span>&gt;</span>
     <span class="hljs-tag">&lt;<span class="hljs-name">samlp:Status</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">samlp:StatusCode</span> <span class="hljs-attr">Value</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:status:Success"</span> /&gt;</span>
     <span class="hljs-tag">&lt;/<span class="hljs-name">samlp:Status</span>&gt;</span>
     <span class="hljs-tag">&lt;<span class="hljs-name">saml:Assertion</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">saml:Issuer</span>&gt;</span>https://sts.windows.net/6b1311e5-123f-49db-acdf-8847c2d00bed/<span class="hljs-tag">&lt;/<span class="hljs-name">saml:Issuer</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">Signature</span> <span class="hljs-attr">xmlns</span>=<span class="hljs-string">"http://www.w3.org/2000/09/xmldsig#"</span>&gt;</span>
         <span class="hljs-comment">&lt;!-- Assertion signature --&gt;</span>
       <span class="hljs-tag">&lt;/<span class="hljs-name">Signature</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">saml:Subject</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:NameID</span> <span class="hljs-attr">Format</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"</span>&gt;</span><EMAIL><span class="hljs-tag">&lt;/<span class="hljs-name">saml:NameID</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:SubjectConfirmation</span> <span class="hljs-attr">Method</span>=<span class="hljs-string">"urn:oasis:names:tc:SAML:2.0:cm:bearer"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:SubjectConfirmationData</span>
             <span class="hljs-attr">NotOnOrAfter</span>=<span class="hljs-string">"${expiryTime}"</span>
             <span class="hljs-attr">Recipient</span>=<span class="hljs-string">"${acsUrl}"</span> /&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:SubjectConfirmation</span>&gt;</span>
       <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Subject</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">saml:Conditions</span> <span class="hljs-attr">NotBefore</span>=<span class="hljs-string">"${notBefore}"</span> <span class="hljs-attr">NotOnOrAfter</span>=<span class="hljs-string">"${notOnOrAfter}"</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:AudienceRestriction</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:Audience</span>&gt;</span>${spEntityID}<span class="hljs-tag">&lt;/<span class="hljs-name">saml:Audience</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:AudienceRestriction</span>&gt;</span>
       <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Conditions</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">saml:AuthnStatement</span> <span class="hljs-attr">AuthnInstant</span>=<span class="hljs-string">"${authnInstant}"</span> <span class="hljs-attr">SessionIndex</span>=<span class="hljs-string">"${sessionIndex}"</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:AuthnContext</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AuthnContextClassRef</span>&gt;</span>urn:oasis:names:tc:SAML:2.0:ac:classes:Password<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AuthnContextClassRef</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:AuthnContext</span>&gt;</span>
       <span class="hljs-tag">&lt;/<span class="hljs-name">saml:AuthnStatement</span>&gt;</span>
       <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeStatement</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:Attribute</span> <span class="hljs-attr">Name</span>=<span class="hljs-string">"http://schemas.microsoft.com/identity/claims/displayname"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span>John Doe<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Attribute</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:Attribute</span> <span class="hljs-attr">Name</span>=<span class="hljs-string">"http://schemas.microsoft.com/identity/claims/objectidentifier"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span>a1b2c3d4-e5f6-7890-abcd-ef1234567890<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Attribute</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:Attribute</span> <span class="hljs-attr">Name</span>=<span class="hljs-string">"http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span>John<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Attribute</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:Attribute</span> <span class="hljs-attr">Name</span>=<span class="hljs-string">"http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span>Doe<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Attribute</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:Attribute</span> <span class="hljs-attr">Name</span>=<span class="hljs-string">"http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span><EMAIL><span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Attribute</span>&gt;</span>
         <span class="hljs-tag">&lt;<span class="hljs-name">saml:Attribute</span> <span class="hljs-attr">Name</span>=<span class="hljs-string">"http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span>Plant Managers<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
           <span class="hljs-tag">&lt;<span class="hljs-name">saml:AttributeValue</span>&gt;</span>Connected Workers Users<span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeValue</span>&gt;</span>
         <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Attribute</span>&gt;</span>
       <span class="hljs-tag">&lt;/<span class="hljs-name">saml:AttributeStatement</span>&gt;</span>
     <span class="hljs-tag">&lt;/<span class="hljs-name">saml:Assertion</span>&gt;</span>
   <span class="hljs-tag">&lt;/<span class="hljs-name">samlp:Response</span>&gt;</span>
</div></code></pre>
<h3 id="22-jwt-authentication">2.2 JWT Authentication</h3>
<p>After the initial SAML authentication, the system uses JSON Web Tokens (JWT) for session management and ongoing authentication. JWTs provide a stateless, secure method for maintaining user sessions and authorization information.</p>
<h4 id="221-token-types-and-structure">2.2.1 Token Types and Structure</h4>
<p>The authentication system implements two types of tokens with distinct purposes and structures:</p>
<p><strong>Token Types Comparison:</strong></p>
<table>
<thead>
<tr>
<th>Characteristic</th>
<th>Access Token</th>
<th>Refresh Token</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Purpose</strong></td>
<td>API authorization and resource access</td>
<td>Obtaining new access tokens</td>
</tr>
<tr>
<td><strong>Lifetime</strong></td>
<td>Short-lived (15-60 minutes)</td>
<td>Longer-lived (hours to days)</td>
</tr>
<tr>
<td><strong>Storage Location</strong></td>
<td>Application memory</td>
<td>HTTP-only secure cookie</td>
</tr>
<tr>
<td><strong>Claim Richness</strong></td>
<td>Contains complete user profile and permissions</td>
<td>Contains minimal identifying information</td>
</tr>
<tr>
<td><strong>Usage Frequency</strong></td>
<td>Every API call</td>
<td>Only during token refresh</td>
</tr>
<tr>
<td><strong>Exposure</strong></td>
<td>Limited to application frontend</td>
<td>Server-side only</td>
</tr>
<tr>
<td><strong>Revocation</strong></td>
<td>Self-expires quickly</td>
<td>Can be invalidated server-side</td>
</tr>
</tbody>
</table>
<p><strong>Access Token Structure:</strong></p>
<pre class="hljs"><code><div>{
  &quot;header&quot;: {
    &quot;alg&quot;: &quot;RS256&quot;,
    &quot;typ&quot;: &quot;JWT&quot;,
    &quot;kid&quot;: &quot;[certificate identifier]&quot;
  },
  &quot;payload&quot;: {
    &quot;sub&quot;: &quot;<EMAIL>&quot;,
    &quot;name&quot;: &quot;John Doe&quot;,
    &quot;given_name&quot;: &quot;John&quot;,
    &quot;family_name&quot;: &quot;Doe&quot;,
    &quot;email&quot;: &quot;<EMAIL>&quot;,
    &quot;roles&quot;: [&quot;User&quot;, &quot;ProjectManager&quot;],
    &quot;department&quot;: &quot;Engineering&quot;,
    &quot;location&quot;: &quot;Paris&quot;,
    &quot;employeeId&quot;: &quot;EMP123456&quot;,
    &quot;profile_id&quot;: &quot;profile_12345&quot;,
    &quot;iat&quot;: 1675091348,
    &quot;exp&quot;: 1675094948,
    &quot;aud&quot;: &quot;connected-workers-api&quot;,
    &quot;iss&quot;: &quot;connected-workers-auth&quot;
  }
}
</div></code></pre>
<p><strong>Refresh Token Structure:</strong></p>
<pre class="hljs"><code><div>{
  &quot;header&quot;: {
    &quot;alg&quot;: &quot;RS256&quot;,
    &quot;typ&quot;: &quot;JWT&quot;,
    &quot;kid&quot;: &quot;[certificate identifier]&quot;
  },
  &quot;payload&quot;: {
    &quot;sub&quot;: &quot;<EMAIL>&quot;,
    &quot;jti&quot;: &quot;unique-token-id-12345&quot;,
    &quot;iat&quot;: 1675091348,
    &quot;exp&quot;: 1675177748,
    &quot;iss&quot;: &quot;connected-workers-auth&quot;
  }
}
</div></code></pre>
<p><strong>Token Claim Descriptions:</strong></p>
<table>
<thead>
<tr>
<th>Claim</th>
<th>Description</th>
<th style="text-align:center">Access Token</th>
<th style="text-align:center">Refresh Token</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>sub</code></td>
<td>Subject identifier (typically email)</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✓</td>
</tr>
<tr>
<td><code>name</code></td>
<td>User's full name</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>given_name</code></td>
<td>User's first name</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>family_name</code></td>
<td>User's last name</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>email</code></td>
<td>User's email address</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>roles</code></td>
<td>User's assigned roles</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>department</code></td>
<td>User's department</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>employeeId</code></td>
<td>Employee identifier</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>profile_id</code></td>
<td>Active profile context</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>iat</code></td>
<td>Issued at timestamp</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✓</td>
</tr>
<tr>
<td><code>exp</code></td>
<td>Expiration timestamp</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✓</td>
</tr>
<tr>
<td><code>aud</code></td>
<td>Intended audience</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✗</td>
</tr>
<tr>
<td><code>iss</code></td>
<td>Token issuer</td>
<td style="text-align:center">✓</td>
<td style="text-align:center">✓</td>
</tr>
<tr>
<td><code>jti</code></td>
<td>JWT ID (unique identifier)</td>
<td style="text-align:center">✗</td>
<td style="text-align:center">✓</td>
</tr>
</tbody>
</table>
<h4 id="222-token-management">2.2.2 Token Management</h4>
<p>Token management encompasses the complete lifecycle of JWTs within the system, including generation, validation, renewal, and revocation.</p>
<p><strong>Token Generation Security Considerations:</strong></p>
<table>
<thead>
<tr>
<th>Consideration</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Signing Algorithm</strong></td>
<td>RS256 (asymmetric) with 2048-bit key</td>
</tr>
<tr>
<td><strong>Key Rotation</strong></td>
<td>90-day rotation schedule with overlap period</td>
</tr>
<tr>
<td><strong>Entropy Source</strong></td>
<td>Hardware-based random number generation</td>
</tr>
<tr>
<td><strong>Claim Minimization</strong></td>
<td>Include only necessary claims per token type</td>
</tr>
<tr>
<td><strong>Clock Synchronization</strong></td>
<td>NTP with maximum 30-second clock skew tolerance</td>
</tr>
<tr>
<td><strong>Identifier Uniqueness</strong></td>
<td>UUIDv4 for token identifiers (jti claim)</td>
</tr>
</tbody>
</table>
<p><strong>Token Validation Process:</strong></p>
<ol>
<li>
<p><strong>Signature Verification</strong>:</p>
<ul>
<li>Verify token is properly signed with the correct key</li>
<li>Check signing algorithm matches expected algorithm</li>
</ul>
</li>
<li>
<p><strong>Standard Claims Validation</strong>:</p>
<ul>
<li>Verify token has not expired (<code>exp</code> claim)</li>
<li>Verify token was issued at a reasonable time (<code>iat</code> claim)</li>
<li>Verify issuer matches expected value (<code>iss</code> claim)</li>
<li>Verify audience is correct (<code>aud</code> claim)</li>
</ul>
</li>
<li>
<p><strong>Application-Specific Validation</strong>:</p>
<ul>
<li>Check for required claims based on resource access</li>
<li>Validate role claims for authorization</li>
<li>Verify profile context is valid</li>
</ul>
</li>
</ol>
<h4 id="223-token-storage-strategy">2.2.3 Token Storage Strategy</h4>
<p>Proper token storage is critical for security. The system implements a defense-in-depth approach to protect tokens from various attack vectors:</p>
<p><strong>Token Storage Locations:</strong></p>
<table>
<thead>
<tr>
<th>Token Type</th>
<th>Storage Location</th>
<th>Technical Implementation</th>
<th>Security Considerations</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Access Token</strong></td>
<td>Memory (frontend)</td>
<td>JavaScript variable in auth state</td>
<td>Not persisted across page refreshes</td>
</tr>
<tr>
<td><strong>Access Token Backup</strong></td>
<td>SessionStorage</td>
<td>Encrypted, used only for page refreshes</td>
<td>Cleared when browser closes</td>
</tr>
<tr>
<td><strong>Refresh Token</strong></td>
<td>HTTP-only Cookie</td>
<td>Secure, SameSite=Strict</td>
<td>Not accessible via JavaScript</td>
</tr>
<tr>
<td><strong>Token Metadata</strong></td>
<td>localStorage</td>
<td>Expiry time only (no actual tokens)</td>
<td>Used to detect when refresh is needed</td>
</tr>
</tbody>
</table>
<p><strong>Security Measures:</strong></p>
<table>
<thead>
<tr>
<th>Threat</th>
<th>Mitigation</th>
</tr>
</thead>
<tbody>
<tr>
<td>XSS Attack</td>
<td>Memory-only access tokens, HTTP-only cookies for refresh tokens</td>
</tr>
<tr>
<td>CSRF Attack</td>
<td>SameSite cookie policy, CSRF tokens for authentication endpoints</td>
</tr>
<tr>
<td>Token Leakage</td>
<td>No tokens in localStorage, no tokens in URLs</td>
</tr>
<tr>
<td>Session Hijacking</td>
<td>Short token lifetimes, secure cookie flags</td>
</tr>
<tr>
<td>Local Storage Access</td>
<td>Only non-sensitive metadata stored</td>
</tr>
</tbody>
</table>
<h3 id="23-token-refresh-implementation">2.3 Token Refresh Implementation</h3>
<p>The token refresh mechanism enables continuous user sessions without requiring re-authentication while simultaneously keeping user data synchronized with Microsoft Entra ID.</p>
<h4 id="231-refresh-process">2.3.1 Refresh Process</h4>
<p>Token refresh is initiated automatically when an access token approaches expiration:</p>
<p><strong>Refresh Flow:</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
 participant Client
 participant API as Auth API
 participant Graph as Microsoft Graph API
 participant DB as Database

 Note over Client: Detect access token<br/>approaching expiration
 Client->>API: Request token refresh
 Note right of Client: Refresh token included<br/>in HTTP-only cookie
 API->>API: Validate refresh token

 alt Valid Refresh Token
     API->>Graph: Fetch latest user data
     Graph->>API: Return user profile & groups
     API->>DB: Update user data
     API->>API: Generate new tokens
     API->>Client: Return new access token
     Note right of API: Set new refresh token<br/>in HTTP-only cookie
 else Invalid Refresh Token
     API->>Client: Return 401 Unauthorized
     Client->>Client: Redirect to login
 end
</div></code></pre>
<p><strong>Expiration Detection Strategy:</strong></p>
<table>
<thead>
<tr>
<th>Strategy Component</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Client-Side Detection</strong></td>
<td>Track token expiry time in memory</td>
</tr>
<tr>
<td><strong>Refresh Timing</strong></td>
<td>Initiate at 75% of token lifetime</td>
</tr>
<tr>
<td><strong>Background Refresh</strong></td>
<td>Refresh happens in background without UI disruption</td>
</tr>
<tr>
<td><strong>Proactive Detection</strong></td>
<td>Check before critical operations</td>
</tr>
<tr>
<td><strong>Fallback Detection</strong></td>
<td>Handle 401 responses from API calls</td>
</tr>
</tbody>
</table>
<p><strong>Refresh Endpoint Security:</strong></p>
<table>
<thead>
<tr>
<th>Security Feature</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Rate Limiting</strong></td>
<td>Maximum 10 refresh attempts per minute per user</td>
</tr>
<tr>
<td><strong>Jitter</strong></td>
<td>Random delay (1-3s) to prevent timing attacks</td>
</tr>
<tr>
<td><strong>IP Validation</strong></td>
<td>Optional validation against previous request IPs</td>
</tr>
<tr>
<td><strong>Refresh Token Rotation</strong></td>
<td>New refresh token with each successful refresh</td>
</tr>
<tr>
<td><strong>Suspicious Activity Detection</strong></td>
<td>Block after multiple rapid refresh attempts</td>
</tr>
</tbody>
</table>
<h4 id="232-user-data-synchronization">2.3.2 User Data Synchronization</h4>
<p>The Connected Workers platform implements a comprehensive user data synchronization strategy that ensures continuous alignment between Microsoft Entra ID and the application's user context. This synchronization occurs during each token refresh operation, maintaining data consistency and security across the entire system.</p>
<p><strong>Attribute Synchronization Framework</strong></p>
<p>The synchronization process encompasses multiple categories of user attributes, each serving distinct purposes within the application. At the foundation, basic profile information synchronization ensures accurate user identification and presentation throughout the interface. This includes essential identifiers such as display names, email addresses, organization and contact information, which are crucial for user recognition and communication.</p>
<p>Security and authorization data synchronization focuses on role assignments and group memberships. This critical security layer ensures that user permissions accurately reflect their current organizational responsibilities and access requirements. The system continuously updates these assignments to maintain precise access control and security boundaries.</p>
<p><strong>Role Authorization Framework</strong></p>
<p>The Connected Workers platform implements a sophisticated role-based access control system that translates Azure AD group memberships into application-specific roles and permissions. This mapping creates a clear, hierarchical authorization structure that aligns with organizational responsibilities while maintaining security and operational efficiency.</p>
<p>The system maintains a baseline access level through the Connected Workers Users group, ensuring all authorized users have access to essential platform functionality. This foundational access layer provides basic system interaction capabilities while maintaining security boundaries.</p>
<p>This role mapping framework is dynamically maintained during each token refresh operation, ensuring that user permissions always reflect current Azure AD group memberships. The system's ability to maintain this synchronization in real-time ensures that access control remains current and accurate, while the hierarchical structure supports clear lines of authority and responsibility within the organization.</p>
<h4 id="233-error-handling">2.3.3 Error Handling</h4>
<p>Robust error handling ensures the system can gracefully recover from authentication failures:</p>
<p><strong>Common Error Scenarios:</strong></p>
<table>
<thead>
<tr>
<th>Error Scenario</th>
<th>Detection Method</th>
<th>Handling Strategy</th>
<th>User Experience</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Expired Refresh Token</strong></td>
<td>JWT expiration validation</td>
<td>Redirect to login</td>
<td>Clear authentication message</td>
</tr>
<tr>
<td><strong>Invalid Signature</strong></td>
<td>Signature validation failure</td>
<td>Security alert, login redirect</td>
<td>Security message</td>
</tr>
<tr>
<td><strong>User Not Found</strong></td>
<td>Database lookup failure</td>
<td>JIT provisioning attempt</td>
<td>Transparent retry</td>
</tr>
<tr>
<td><strong>Graph API Unavailable</strong></td>
<td>API timeout/error</td>
<td>Use cached data, retry later</td>
<td>Notification of potentially outdated data</td>
</tr>
<tr>
<td><strong>Role Mapping Failure</strong></td>
<td>Exception in mapping process</td>
<td>Log issue, use default/previous roles</td>
<td>Limited functionality notice</td>
</tr>
<tr>
<td><strong>Database Connectivity</strong></td>
<td>Database exception</td>
<td>Retry with backoff, use cached JWT data</td>
<td>Transaction-specific error</td>
</tr>
</tbody>
</table>
<p><strong>Recovery Strategies:</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    A[Detect Refresh Error] --> B{Error Type}
    B -->|Token Expired/Invalid| C[Redirect to Login]
    B -->|Graph API Unavailable| D[Use Cached User Data]
    B -->|Database Error| E[Retry with Exponential Backoff]
    B -->|Network Error| F[Queue Refresh for Later]

    D --> G[Flag Data as Potentially Outdated]
    D --> H[Schedule Background Retry]
    E --> I{Retry Successful?}
    I -->|Yes| J[Resume Normal Operation]
    I -->|No, Max Retries| K[Degrade Gracefully]
    F --> L[Monitor Connectivity]
    L --> M{Connection Restored?}
    M -->|Yes| N[Execute Queued Operations]
    M -->|No| O[Notify User]
</div></code></pre>
<h2 id="3-authorization-framework">3. Authorization Framework</h2>
<p>This section outlines the comprehensive authorization framework for managing access control within the application, leveraging JWT authentication with SAML SSO and Microsoft Graph API synchronization.</p>
<h3 id="31-core-authorization-concepts">3.1 Core Authorization Concepts</h3>
<h4 id="311-role-management">3.1.1 Role Management</h4>
<p>Roles are collections of permissions derived from Azure AD group memberships, providing a direct mapping between organizational structure and system permissions.</p>
<h5 id="role-types">Role Types</h5>
<table>
<thead>
<tr>
<th>Role Type</th>
<th>Description</th>
<th>Examples</th>
</tr>
</thead>
<tbody>
<tr>
<td>Organizational</td>
<td>Based on position in company hierarchy</td>
<td>PLANT_MANAGER, SHIFT_LEADER, TEAM_LEADER</td>
</tr>
<tr>
<td>Functional</td>
<td>Based on specific job functions</td>
<td>QUALITY_INSPECTOR, MAINTENANCE_TECH</td>
</tr>
<tr>
<td>Administrative</td>
<td>System administration capabilities</td>
<td>SYSTEM_ADMIN, USER_MANAGER</td>
</tr>
<tr>
<td>Specialized</td>
<td>Temporary or special-purpose access</td>
<td>EMERGENCY_RESPONDER, AUDITOR</td>
</tr>
</tbody>
</table>
<h5 id="role-hierarchy">Role Hierarchy</h5>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    SA[SUPER_ADMIN] --> PM[PLANT_MANAGER]
    PM --> SL[SHIFT_LEADER]
    SL --> TL[TEAM_LEADER]
    TL --> OP[OPERATOR]
    SA --> TR[TRAINING_RESPONSIBLE]
    TR --> T[TRAINER]
    SA --> QI[QUALITY_INSPECTOR]
    SA --> MT[MAINTENANCE_TECH]
</div></code></pre>
<h5 id="hierarchical-role-structure">Hierarchical Role Structure</h5>
<p>Roles have parent-child relationships that define an organization's management structure. Each role has:</p>
<ul>
<li>A hierarchical level (1 being the highest, increasing numbers for lower levels)</li>
<li>Parent role references (which roles are above in the hierarchy)</li>
<li>Child role references (which roles are below in the hierarchy)</li>
</ul>
<p>This hierarchy structure enables:</p>
<ol>
<li><strong>Direct Permission Assignment</strong>: Each role has explicitly assigned permissions based on its level</li>
<li><strong>Delegation Control</strong>: Delegation can be restricted to follow the hierarchy</li>
<li><strong>Organizational Alignment</strong>: Authorization reflects organizational structure</li>
<li><strong>Validation Rules</strong>: Hierarchical authorization checks (e.g., a role can only manage users with roles below it)</li>
</ol>
<h5 id="role-definition-with-hierarchy-support">Role Definition with Hierarchy Support</h5>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"role"</span>: {
    <span class="hljs-attr">"id"</span>: <span class="hljs-string">"SHIFT_LEADER"</span>,
    <span class="hljs-attr">"name"</span>: <span class="hljs-string">"Shift Leader"</span>,
    <span class="hljs-attr">"description"</span>: <span class="hljs-string">"Manages production shift and team leaders"</span>,
    <span class="hljs-attr">"hierarchyLevel"</span>: <span class="hljs-number">3</span>,
    <span class="hljs-attr">"parentRoles"</span>: [<span class="hljs-string">"PLANT_MANAGER"</span>],
    <span class="hljs-attr">"childRoles"</span>: [<span class="hljs-string">"TEAM_LEADER"</span>],
    <span class="hljs-attr">"azureAdGroups"</span>: [<span class="hljs-string">"Shift Leaders"</span>]
  }
}
</div></code></pre>
<h4 id="312-permission-framework">3.1.2 Permission Framework</h4>
<p>Permissions follow a simple resource:action format that clearly defines what operations a user can perform on specific resources.</p>
<h5 id="permission-structure">Permission Structure</h5>
<table>
<thead>
<tr>
<th>Component</th>
<th>Description</th>
<th>Example</th>
</tr>
</thead>
<tbody>
<tr>
<td>Resource</td>
<td>The entity being accessed</td>
<td><code>team</code>, <code>schedule</code>, <code>training</code> , <code>operator</code></td>
</tr>
<tr>
<td>Action</td>
<td>The operation being performed</td>
<td><code>create</code>, <code>read</code>, <code>update</code>, <code>delete</code></td>
</tr>
</tbody>
</table>
<h5 id="permission-examples">Permission Examples</h5>
<table>
<thead>
<tr>
<th>Permission</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>team:manage</code></td>
<td>Manage team information and membership</td>
</tr>
<tr>
<td><code>operator:manage</code></td>
<td>Manage operator information and membership</td>
</tr>
<tr>
<td><code>schedule:view</code></td>
<td>View schedules</td>
</tr>
<tr>
<td><code>training:assign</code></td>
<td>Assign training to users</td>
</tr>
<tr>
<td><code>equipment:control</code></td>
<td>Control equipment and machinery</td>
</tr>
<tr>
<td><code>shift:manage</code></td>
<td>Manage shift schedules and assignments</td>
</tr>
<tr>
<td><code>quality:inspect</code></td>
<td>Perform quality inspection tasks</td>
</tr>
<tr>
<td><code>quality:approve</code></td>
<td>Approve quality-related documentation</td>
</tr>
</tbody>
</table>
<h4 id="313-scope-management">3.1.3 Scope Management</h4>
<p>Scopes define the boundaries within which permissions can be exercised, allowing for fine-grained access control.</p>
<h5 id="scope-types">Scope Types</h5>
<table>
<thead>
<tr>
<th>Scope Type</th>
<th>Description</th>
<th>Examples</th>
</tr>
</thead>
<tbody>
<tr>
<td>Organizational</td>
<td>Based on company structure</td>
<td>Site, Department, Team</td>
</tr>
<tr>
<td>Temporal</td>
<td>Based on time periods</td>
<td>Shift, Date Range</td>
</tr>
<tr>
<td>Functional</td>
<td>Based on functional areas</td>
<td>Process Area, Equipment Type</td>
</tr>
<tr>
<td>Resource</td>
<td>Based on specific resources</td>
<td>Machine ID, Material Type</td>
</tr>
</tbody>
</table>
<h5 id="scope-implementation">Scope Implementation</h5>
<p>Scopes are implemented as attributes on user profiles, role assignments, and delegations. They can be derived from:</p>
<ol>
<li><strong>Azure AD Attributes</strong>: Department, location, job title</li>
<li><strong>Application Metadata</strong>: Team assignments, shift assignments</li>
<li><strong>Delegation Restrictions</strong>: Explicitly defined in delegation requests</li>
</ol>
<h3 id="32-role-based-access-control">3.2 Role-Based Access Control</h3>
<h4 id="321-role-types-and-hierarchy">3.2.1 Role Types and Hierarchy</h4>
<p>The RBAC system implements a hierarchical role structure that reflects organizational management levels and functional specializations.</p>
<h5 id="hierarchical-role-relationships">Hierarchical Role Relationships</h5>
<p>Role hierarchy is a fundamental aspect of the authorization system, providing clear lines of authority and permission inheritance:</p>
<ol>
<li><strong>Vertical Relationships</strong>: Define reporting lines (Manager → Supervisor → Team Lead → Operator)</li>
<li><strong>Horizontal Relationships</strong>: Define peer relationships within the same hierarchy level</li>
<li><strong>Functional Relationships</strong>: Define specialized cross-cutting concerns (Quality, Safety, Maintenance)</li>
</ol>
<h5 id="role-hierarchy-configuration">Role Hierarchy Configuration</h5>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"roleHierarchyConfig"</span>: {
    <span class="hljs-attr">"enforceHierarchicalRestrictions"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowCrossLevelOperations"</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">"maxLevelSkipForOperations"</span>: <span class="hljs-number">1</span>,
    <span class="hljs-attr">"inheritPermissionsFromParent"</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">"permissionOverrideRules"</span>: {
      <span class="hljs-attr">"allowChildOverride"</span>: <span class="hljs-literal">false</span>,
      <span class="hljs-attr">"allowScopedOverride"</span>: <span class="hljs-literal">true</span>
    }
  }
}
</div></code></pre>
<h4 id="322-permission-management">3.2.2 Permission Management</h4>
<p>Permissions are managed at multiple levels to provide flexible yet secure access control.</p>
<h5 id="permission-assignment-model">Permission Assignment Model</h5>
<p>The system uses a layered permission assignment model:</p>
<ol>
<li><strong>Role-Based Permissions</strong>: Core permissions assigned to roles</li>
<li><strong>Context-Based Permissions</strong>: Permissions that vary based on context (e.g., location, time)</li>
<li><strong>Attribute-Based Rules</strong>: Dynamic permissions based on user or resource attributes</li>
<li><strong>Policy-Based Overrides</strong>: Global security policies that can override standard permissions</li>
</ol>
<h5 id="permission-resolution-process">Permission Resolution Process</h5>
<pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    A[Permission Check] --> B[Collect Role Permissions]
    B --> C[Apply Context Constraints]
    C --> D[Apply Attribute Rules]
    D --> E[Apply Policy Overrides]
    E --> F{Permission Granted?}
    F -->|Yes| G[Allow Operation]
    F -->|No| H[Deny Operation]
</div></code></pre>
<h5 id="permission-conflict-resolution">Permission Conflict Resolution</h5>
<table>
<thead>
<tr>
<th>Conflict Type</th>
<th>Resolution Strategy</th>
</tr>
</thead>
<tbody>
<tr>
<td>Role vs. Context</td>
<td>Context restrictions override role permissions</td>
</tr>
<tr>
<td>Multiple Roles</td>
<td>Most permissive access granted (union of permissions)</td>
</tr>
<tr>
<td>Hierarchical Conflicts</td>
<td>Explicit permissions override inherited permissions</td>
</tr>
<tr>
<td>Delegation vs. Direct Assignment</td>
<td>More restrictive permission applies</td>
</tr>
<tr>
<td>Policy vs. Role</td>
<td>Security policies always override role-based permissions</td>
</tr>
<tr>
<td>Cross-functional Role Conflicts</td>
<td>Domain-specific permissions take precedence in their domain</td>
</tr>
</tbody>
</table>
<h3 id="33-group-based-authorization">3.3 Group-Based Authorization</h3>
<h4 id="331-dynamic-groups">3.3.1 Dynamic Groups</h4>
<p>Dynamic groups in Azure AD provide flexible, attribute-based group membership that automatically updates based on user properties.</p>
<h5 id="dynamic-group-rules">Dynamic Group Rules</h5>
<table>
<thead>
<tr>
<th>Rule Type</th>
<th>Description</th>
<th>Example Rule</th>
</tr>
</thead>
<tbody>
<tr>
<td>Department-Based</td>
<td>Users from specific department</td>
<td><code>user.department -eq &quot;Manufacturing&quot;</code></td>
</tr>
<tr>
<td>Location-Based</td>
<td>Users at specific locations</td>
<td><code>user.physicalDeliveryOfficeName -eq &quot;Plant A&quot;</code></td>
</tr>
<tr>
<td>Title-Based</td>
<td>Users with specific job titles</td>
<td><code>user.jobTitle -contains &quot;Shift Leader&quot;</code></td>
</tr>
<tr>
<td>Combined Rules</td>
<td>Multiple conditions for membership</td>
<td><code>(user.department -eq &quot;Quality&quot;) -and (user.country -eq &quot;US&quot;)</code></td>
</tr>
<tr>
<td>Exclusion Rules</td>
<td>Exclude specific users from groups</td>
<td><code>NOT(user.employeeId -in [&quot;1234&quot;, &quot;5678&quot;])</code></td>
</tr>
</tbody>
</table>
<h5 id="benefits-of-dynamic-groups">Benefits of Dynamic Groups</h5>
<ol>
<li><strong>Automatic Membership Management</strong>: Group membership automatically updated as user attributes change</li>
<li><strong>Reduced Administrative Overhead</strong>: No manual group membership management required</li>
<li><strong>Consistent Access Control</strong>: Users with same attributes have same access</li>
<li><strong>Simplified Onboarding/Offboarding</strong>: Access granted/removed automatically based on user attributes</li>
</ol>
<h4 id="332-group-to-role-mapping">3.3.2 Group to Role Mapping</h4>
<p>Azure AD security groups are mapped directly to application roles, creating a seamless integration between identity management and authorization.</p>
<h5 id="configuration-example">Configuration Example</h5>
<table>
<thead>
<tr>
<th>Azure AD Group</th>
<th>Application Role</th>
<th>Auto-Assign</th>
<th>Scope Attributes</th>
</tr>
</thead>
<tbody>
<tr>
<td>Plant Managers</td>
<td>PLANT_MANAGER</td>
<td>Yes</td>
<td>site</td>
</tr>
<tr>
<td>Shift Leaders</td>
<td>SHIFT_LEADER</td>
<td>Yes</td>
<td>site, shift</td>
</tr>
<tr>
<td>Team Leaders</td>
<td>TEAM_LEADER</td>
<td>Yes</td>
<td>team</td>
</tr>
<tr>
<td>Quality Inspectors</td>
<td>QUALITY_INSPECTOR</td>
<td>Yes</td>
<td>department, site</td>
</tr>
<tr>
<td>Maintenance Technicians</td>
<td>MAINTENANCE_TECH</td>
<td>Yes</td>
<td>site</td>
</tr>
</tbody>
</table>
<h5 id="mapping-process-flow">Mapping Process Flow</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
participant User
participant App as Application
participant API as API Backend
participant AD as Azure AD
participant Graph as Microsoft Graph API
participant DB as Database

User->>App: Authenticate with SAML
App->>API: Process SAML Response
API->>Graph: Query User Group Memberships
Graph->>API: Return Group Memberships
API->>DB: Lookup Group-Role Mappings
API->>API: Apply Role Assignments
API->>App: Return JWT with Roles
App->>User: Authorized Session
</div></code></pre>
<h5 id="mapping-configuration">Mapping Configuration</h5>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"groupMappings"</span>: [
    {
      <span class="hljs-attr">"azureAdGroupId"</span>: <span class="hljs-string">"a1b2c3d4-e5f6-7890-abcd-ef1234567890"</span>,
      <span class="hljs-attr">"displayName"</span>: <span class="hljs-string">"Plant Managers"</span>,
      <span class="hljs-attr">"applicationRole"</span>: <span class="hljs-string">"PLANT_MANAGER"</span>,
      <span class="hljs-attr">"autoAssign"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"scopeAttributes"</span>: [<span class="hljs-string">"site"</span>]
    },
    {
      <span class="hljs-attr">"azureAdGroupId"</span>: <span class="hljs-string">"b2c3d4e5-f6a7-8901-bcde-f12345678901"</span>,
      <span class="hljs-attr">"displayName"</span>: <span class="hljs-string">"Quality Inspectors"</span>,
      <span class="hljs-attr">"applicationRole"</span>: <span class="hljs-string">"QUALITY_INSPECTOR"</span>,
      <span class="hljs-attr">"autoAssign"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"scopeAttributes"</span>: [<span class="hljs-string">"department"</span>, <span class="hljs-string">"site"</span>]
    }
  ]
}
</div></code></pre>
<p>This configuration is loaded when the microservice starts and is used during user synchronization with Microsoft Graph API to assign appropriate roles based on Azure AD group memberships.</p>
<h3 id="34-profile-based-authorization">3.4 Profile-Based Authorization</h3>
<h4 id="341-profile-types-and-lifecycle">3.4.1 Profile Types and Lifecycle</h4>
<p>Profiles provide contextualized role containers that users can switch between.</p>
<h5 id="profile-types">Profile Types</h5>
<table>
<thead>
<tr>
<th>Profile Type</th>
<th>Source</th>
<th>Description</th>
<th>Creation</th>
</tr>
</thead>
<tbody>
<tr>
<td>Direct</td>
<td>Assigned</td>
<td>Based on user's Azure AD group memberships</td>
<td>Created during first authentication</td>
</tr>
<tr>
<td>Delegated</td>
<td>Temporary</td>
<td>Created through delegation from another user</td>
<td>Created during delegation process</td>
</tr>
<tr>
<td>System</td>
<td>Automatic</td>
<td>Generated for special purposes</td>
<td>Created for specific scenarios</td>
</tr>
</tbody>
</table>
<h5 id="profile-attributes">Profile Attributes</h5>
<p>Each profile contains:</p>
<ul>
<li>Unique identifier</li>
<li>User reference</li>
<li>Name and description</li>
<li>Profile type</li>
<li>Active status</li>
<li>Validity period (for delegated profiles)</li>
<li>Associated roles and restrictions</li>
<li>Contextual metadata</li>
</ul>
<h5 id="profile-lifecycle-states">Profile Lifecycle States</h5>
<pre><code class="language-mermaid"><div class="mermaid">stateDiagram-v2
    [*] --> Inactive : Create
    Inactive --> Active : Activate
    Active --> Inactive : Deactivate
    Inactive --> [*] : Delete/Expire
    Active --> [*] : Delete/Expire
</div></code></pre>
<h4 id="342-profile-switching">3.4.2 Profile Switching</h4>
<p>Users can switch between available profiles to change their operating context.</p>
<h5 id="profile-switching-process">Profile Switching Process</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
participant User
participant App as Application
participant API as API Backend
participant DB as Database

User->>App: Request Profile Switch
App->>API: Switch Profile Request
API->>DB: Validate Profile Access
API->>DB: Update Active Profile
API->>API: Generate New JWT with Profile Context
API->>App: Return New JWT
App->>User: Updated Authorization Context
</div></code></pre>
<h5 id="profile-switch-implementation">Profile Switch Implementation</h5>
<p>The profile switching mechanism follows these steps:</p>
<ol>
<li><strong>Profile Selection</strong>: User selects target profile from available profiles</li>
<li><strong>Backend Validation</strong>: API validates user can access the requested profile</li>
<li><strong>Context Switch</strong>: Profile is marked active, previous active profile is deactivated</li>
<li><strong>Token Refresh</strong>: New JWT tokens are issued with updated profile context</li>
<li><strong>Client Update</strong>: Application updates authorization context with new token</li>
</ol>
<h5 id="profile-switch-security-controls">Profile Switch Security Controls</h5>
<table>
<thead>
<tr>
<th>Control</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td>Switch Validation</td>
<td>Verify user owns the profile before switching</td>
</tr>
<tr>
<td>Automatic Expiry</td>
<td>Enforce automatic expiry for temporary profiles</td>
</tr>
<tr>
<td>Privilege Escalation Check</td>
<td>Prevent switching to profiles with excessive privileges</td>
</tr>
<tr>
<td>Anomaly Detection</td>
<td>Track unusual profile switching patterns</td>
</tr>
<tr>
<td>Context-Aware Restrictions</td>
<td>Limit profile switching based on location, time, or device</td>
</tr>
<tr>
<td>Mandatory Re-Authentication</td>
<td>Require re-authentication for switching to sensitive profiles</td>
</tr>
</tbody>
</table>
<h4 id="343-profile-context-in-tokens">3.4.3 Profile Context in Tokens</h4>
<p>JWT tokens serve as the carrier for profile context and authorization information.</p>
<h5 id="profile-context-in-jwt">Profile Context in JWT</h5>
<p>Each JWT contains:</p>
<ul>
<li>User identity information</li>
<li>Active profile identifier and type</li>
<li>Roles associated with active profile</li>
<li>Permissions derived from those roles</li>
<li>Scope restrictions</li>
<li>Delegation reference (if applicable)</li>
</ul>
<h5 id="example-token-payload">Example Token Payload</h5>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"sub"</span>: <span class="hljs-string">"<EMAIL>"</span>,
  <span class="hljs-attr">"name"</span>: <span class="hljs-string">"John Doe"</span>,
  <span class="hljs-attr">"email"</span>: <span class="hljs-string">"<EMAIL>"</span>,
  <span class="hljs-attr">"department"</span>: <span class="hljs-string">"Production"</span>,
  <span class="hljs-attr">"jobTitle"</span>: <span class="hljs-string">"Team Leader"</span>,
  <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"TEAM_LEADER"</span>, <span class="hljs-string">"TRAINER"</span>],
  <span class="hljs-attr">"permissions"</span>: [<span class="hljs-string">"team:manage"</span>, <span class="hljs-string">"schedule:view"</span>, <span class="hljs-string">"training:assign"</span>],
  <span class="hljs-attr">"activeProfileId"</span>: <span class="hljs-string">"profile123"</span>,
  <span class="hljs-attr">"profileType"</span>: <span class="hljs-string">"DELEGATED"</span>,
  <span class="hljs-attr">"delegationId"</span>: <span class="hljs-string">"delegation456"</span>,
  <span class="hljs-attr">"scope"</span>: {
    <span class="hljs-attr">"sites"</span>: [<span class="hljs-string">"SITE_A"</span>],
    <span class="hljs-attr">"teams"</span>: [<span class="hljs-string">"TEAM_X"</span>, <span class="hljs-string">"TEAM_Y"</span>]
  },
  <span class="hljs-attr">"restrictions"</span>: {
    <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"budget:manage"</span>]
  },
  <span class="hljs-attr">"iat"</span>: <span class="hljs-number">1675091348</span>,
  <span class="hljs-attr">"exp"</span>: <span class="hljs-number">1675094948</span>
}
</div></code></pre>
<p><strong>Profile Context Security Considerations:</strong></p>
<table>
<thead>
<tr>
<th>Consideration</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Profile Validation</strong></td>
<td>Verify profile exists and is active before honoring claims</td>
</tr>
<tr>
<td><strong>Temporal Checks</strong></td>
<td>Validate profile has not expired (especially for delegated profiles)</td>
</tr>
<tr>
<td><strong>Scope Enforcement</strong></td>
<td>Always apply scope restrictions from active profile</td>
</tr>
<tr>
<td><strong>Restriction Precedence</strong></td>
<td>Profile restrictions override role permissions</td>
</tr>
<tr>
<td><strong>Delegation Validation</strong></td>
<td>For delegated profiles, verify delegation is still valid</td>
</tr>
</tbody>
</table>
<h2 id="4-delegation-framework">4. Delegation Framework</h2>
<p>The delegation framework enables secure, controlled transfer of authority between users while maintaining clear lines of responsibility and accountability. This system supports business continuity, operational flexibility, and allows for efficient handling of absence scenarios.</p>
<h3 id="41-delegation-process">4.1 Delegation Process</h3>
<p>Delegation enables temporary transfer of authority from one user to another through a streamlined process that includes approval mechanisms and clear documentation.</p>
<h4 id="411-delegation-creation-flow">4.1.1 Delegation Creation Flow</h4>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Delegator as Delegator
    participant N1Delegator as Delegator's N+1
    participant API as API Backend
    participant DB as Database
    participant N1Delegate as Delegate's N+1
    participant Delegate as Delegate

    Delegator->>API: Create Delegation Request
    API->>DB: Validate Delegator Authority
    API->>N1Delegator: Request Approval
    N1Delegator->>API: Approve Delegation
    API->>N1Delegate: Request Approval
    N1Delegate->>API: Approve Delegation
    API->>DB: Create Delegation Record
    API->>DB: Create Delegated Profile
    API->>Delegate: Notify of New Delegation
    API->>Delegator: Confirm Creation
</div></code></pre>
<h4 id="412-delegation-request-structure">4.1.2 Delegation Request Structure</h4>
<p>The delegation request captures all information necessary to define the delegation scope, duration, and justification:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"delegatorId"</span>: <span class="hljs-string">"user123"</span>,
  <span class="hljs-attr">"delegateId"</span>: <span class="hljs-string">"user456"</span>,
  <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"SHIFT_LEADER"</span>,
  <span class="hljs-attr">"validFrom"</span>: <span class="hljs-string">"2025-03-20T00:00:00Z"</span>,
  <span class="hljs-attr">"validUntil"</span>: <span class="hljs-string">"2025-03-27T23:59:59Z"</span>,
  <span class="hljs-attr">"profileName"</span>: <span class="hljs-string">"Shift Leader Coverage"</span>,
  <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Annual leave coverage"</span>,
  <span class="hljs-attr">"permissions"</span>: {
    <span class="hljs-attr">"included"</span>: [<span class="hljs-string">"shift:manage"</span>, <span class="hljs-string">"incidents:handle"</span>],
    <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"budget:manage"</span>, <span class="hljs-string">"performance:review"</span>]
  },
  <span class="hljs-attr">"restrictions"</span>: {
    <span class="hljs-attr">"scope"</span>: {
      <span class="hljs-attr">"teams"</span>: [<span class="hljs-string">"TEAM_A"</span>, <span class="hljs-string">"TEAM_B"</span>],
      <span class="hljs-attr">"shifts"</span>: [<span class="hljs-string">"MORNING_SHIFT"</span>]
    }
  },
  <span class="hljs-attr">"requiresApproval"</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">"approvers"</span>: {
    <span class="hljs-attr">"delegatorSupervisorId"</span>: <span class="hljs-string">"manager789"</span>,
    <span class="hljs-attr">"delegateeSupervisorId"</span>: <span class="hljs-string">"manager012"</span>
  },
  <span class="hljs-attr">"metadata"</span>: {
    <span class="hljs-attr">"businessUnit"</span>: <span class="hljs-string">"Manufacturing"</span>,
    <span class="hljs-attr">"requestTimestamp"</span>: <span class="hljs-string">"2025-03-15T09:30:00Z"</span>
  }
}
</div></code></pre>
<h4 id="413-approval-workflow">4.1.3 Approval Workflow</h4>
<p>The delegation process includes a structured approval workflow to ensure proper oversight:</p>
<table>
<thead>
<tr>
<th>Approval Stage</th>
<th>Approver</th>
<th>Responsibility</th>
<th>Fallback Mechanism</th>
</tr>
</thead>
<tbody>
<tr>
<td>Initial Review</td>
<td>Delegator's N+1</td>
<td>Validate business need and delegation scope</td>
<td>Auto-approval after 24h if critical</td>
</tr>
<tr>
<td>Secondary Review</td>
<td>Delegate's N+1</td>
<td>Confirm delegate capacity and suitability</td>
<td>Auto-approval after 24h if critical</td>
</tr>
<tr>
<td>Emergency Override</td>
<td>Security Admin</td>
<td>Bypass approval in emergency situations</td>
<td>Limited to predefined emergency scenarios</td>
</tr>
</tbody>
</table>
<p>For time-sensitive delegations, a streamlined approval workflow can be configured with appropriate controls:</p>
<h3 id="42-delegation-restrictions">4.2 Delegation Restrictions</h3>
<p>Restrictions limit the scope and capabilities of delegated authority, ensuring the principle of least privilege is maintained throughout the delegation lifecycle.</p>
<h4 id="421-restriction-types">4.2.1 Restriction Types</h4>
<table>
<thead>
<tr>
<th>Restriction Type</th>
<th>Description</th>
<th>Example</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Scope Limitations</strong></td>
<td>Where authority can be exercised</td>
<td><code>{&quot;teams&quot;: [&quot;TEAM_A&quot;, &quot;TEAM_B&quot;]}</code></td>
<td>Limit access to specific organizational units</td>
</tr>
<tr>
<td><strong>Permission Inclusions</strong></td>
<td>Explicitly allowed operations</td>
<td><code>[&quot;shift:manage&quot;, &quot;incidents:handle&quot;]</code></td>
<td>Only specific permissions from the role are delegated</td>
</tr>
<tr>
<td><strong>Permission Exclusions</strong></td>
<td>Explicitly forbidden operations</td>
<td><code>[&quot;budget:manage&quot;, &quot;performance:review&quot;]</code></td>
<td>Specific high-sensitivity permissions excluded from delegation</td>
</tr>
<tr>
<td><strong>Time Constraints</strong></td>
<td>When delegation is active</td>
<td><code>{&quot;validFrom&quot;: &quot;2025-03-01&quot;, &quot;validUntil&quot;: &quot;2025-03-15&quot;}</code></td>
<td>Enforce strict temporal boundaries</td>
</tr>
<tr>
<td><strong>Approval Requirements</strong></td>
<td>Approval workflow configuration</td>
<td><code>{&quot;requireN1Approval&quot;: true}</code></td>
<td>Additional verification for sensitive delegations</td>
</tr>
<tr>
<td><strong>Quantity Limits</strong></td>
<td>Numeric limits on operations</td>
<td><code>{&quot;maxDailyOperations&quot;: 20}</code></td>
<td>Prevent abuse of delegated authority</td>
</tr>
</tbody>
</table>
<h4 id="422-non-delegable-permissions">4.2.2 Non-Delegable Permissions</h4>
<p>Certain permissions can be configured as non-delegable to protect sensitive operations:</p>
<table>
<thead>
<tr>
<th>Permission</th>
<th>Rationale</th>
<th>Configuration</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>performance:review</code></td>
<td>Requires direct knowledge of employee</td>
<td>System-defined non-delegable</td>
</tr>
<tr>
<td><code>budget:approve</code></td>
<td>Financial control requirement</td>
<td>Organization-defined non-delegable</td>
</tr>
<tr>
<td><code>hr:terminate</code></td>
<td>Critical HR function</td>
<td>Organization-defined non-delegable</td>
</tr>
<tr>
<td><code>security:admin</code></td>
<td>Security risk</td>
<td>System-defined non-delegable</td>
</tr>
</tbody>
</table>
<p>Non-delegable permissions are configured at the system level by administrators:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"nonDelegablePermissions"</span>: {
    <span class="hljs-attr">"systemDefined"</span>: [<span class="hljs-string">"security:admin"</span>, <span class="hljs-string">"performance:review"</span>],
    <span class="hljs-attr">"organizationDefined"</span>: [<span class="hljs-string">"budget:approve"</span>, <span class="hljs-string">"hr:terminate"</span>],
    <span class="hljs-attr">"override"</span>: {
      <span class="hljs-attr">"allowed"</span>: <span class="hljs-literal">false</span>,
      <span class="hljs-attr">"requiredApprovals"</span>: [<span class="hljs-string">"SECURITY_ADMIN"</span>, <span class="hljs-string">"IT_DIRECTOR"</span>]
    }
  }
}
</div></code></pre>
<h4 id="423-partial-role-delegation">4.2.3 Partial Role Delegation</h4>
<p>The system supports delegation of a subset of permissions within a role, allowing for granular authority transfer:</p>
<pre><code class="language-mermaid"><div class="mermaid">flowchart LR
    A[Full Role] --> B{Delegation Filter}
    B -->|Include| C[Permission 1]
    B -->|Include| D[Permission 2]
    B -->|Exclude| E[Permission 3]
    B -->|Include| F[Permission 4]
    C --> G[Delegated Profile]
    D --> G
    F --> G
</div></code></pre>
<p>Example of partial role delegation configuration:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"SHIFT_LEADER"</span>,
  <span class="hljs-attr">"fullPermissions"</span>: [
    <span class="hljs-string">"shift:manage"</span>,
    <span class="hljs-string">"team:view"</span>,
    <span class="hljs-string">"incidents:handle"</span>,
    <span class="hljs-string">"reports:generate"</span>,
    <span class="hljs-string">"performance:review"</span>,
    <span class="hljs-string">"budget:view"</span>
  ],
  <span class="hljs-attr">"delegatedPermissions"</span>: {
    <span class="hljs-attr">"included"</span>: [<span class="hljs-string">"shift:manage"</span>, <span class="hljs-string">"team:view"</span>, <span class="hljs-string">"incidents:handle"</span>],
    <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"performance:review"</span>, <span class="hljs-string">"budget:view"</span>, <span class="hljs-string">"reports:generate"</span>]
  }
}
</div></code></pre>
<h3 id="43-delegation-lifecycle">4.3 Delegation Lifecycle</h3>
<p>Delegations follow a defined lifecycle with clear state transitions and governance controls.</p>
<h4 id="431-delegation-states">4.3.1 Delegation States</h4>
<pre><code class="language-mermaid"><div class="mermaid">stateDiagram-v2
    [*] --> Draft: Create
    Draft --> PendingApproval: Submit
    PendingApproval --> Approved: Approve
    PendingApproval --> Rejected: Reject
    Approved --> Active: Start Date Reached
    Active --> Expired: End Date Reached
    Active --> Revoked: Manual Revoke
    Draft --> Cancelled: Cancel
    PendingApproval --> Cancelled: Cancel
    Expired --> [*]: Cleanup
    Revoked --> [*]: Cleanup
    Rejected --> [*]: Cleanup
    Cancelled --> [*]: Cleanup
</div></code></pre>
<h4 id="432-state-transitions">4.3.2 State Transitions</h4>
<table>
<thead>
<tr>
<th>From</th>
<th>To</th>
<th>Trigger</th>
<th>Action</th>
<th>Notification</th>
</tr>
</thead>
<tbody>
<tr>
<td>-</td>
<td>Draft</td>
<td>Initial creation</td>
<td>Create draft record</td>
<td>None</td>
</tr>
<tr>
<td>Draft</td>
<td>PendingApproval</td>
<td>Submission</td>
<td>Send to approvers</td>
<td>Approvers notified</td>
</tr>
<tr>
<td>PendingApproval</td>
<td>Approved</td>
<td>All approvals received</td>
<td>Update status</td>
<td>Delegator and delegate notified</td>
</tr>
<tr>
<td>PendingApproval</td>
<td>Rejected</td>
<td>Rejection by approver</td>
<td>Update status with reason</td>
<td>Delegator notified</td>
</tr>
<tr>
<td>Approved</td>
<td>Active</td>
<td>Start date reached</td>
<td>Create delegated profile</td>
<td>Delegate notified</td>
</tr>
<tr>
<td>Active</td>
<td>Expired</td>
<td>End date reached</td>
<td>Deactivate profile</td>
<td>Both parties notified</td>
</tr>
<tr>
<td>Active</td>
<td>Revoked</td>
<td>Manual revocation</td>
<td>Deactivate with reason</td>
<td>Both parties notified</td>
</tr>
<tr>
<td>Draft/PendingApproval</td>
<td>Cancelled</td>
<td>Cancellation request</td>
<td>Update status</td>
<td>Relevant parties notified</td>
</tr>
</tbody>
</table>
<h4 id="433-automatic-delegation">4.3.3 Automatic Delegation</h4>
<p>For predefined scenarios, the system supports automatic delegation when specific conditions are met:</p>
<pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    A[Detect User Absence] --> B{Has Predefined Delegation?}
    B -->|Yes| C[Activate Predefined Delegation]
    B -->|No| D{N+1 Available?}
    D -->|Yes| E[Create Auto-Delegation to N+1]
    D -->|No| F[Escalate to System Admin]
    C --> G[Notify Delegate of Activation]
    E --> G
</div></code></pre>
<p>Automatic delegation configuration:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"automaticDelegations"</span>: [
    {
      <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"teamlead1"</span>,
      <span class="hljs-attr">"absenceType"</span>: <span class="hljs-string">"ANY"</span>,
      <span class="hljs-attr">"defaultDelegateId"</span>: <span class="hljs-string">"shiftlead1"</span>,
      <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
      <span class="hljs-attr">"permissions"</span>: <span class="hljs-string">"ALL"</span>,
      <span class="hljs-attr">"maxDuration"</span>: <span class="hljs-string">"7D"</span>,
      <span class="hljs-attr">"requiresConfirmation"</span>: <span class="hljs-literal">false</span>
    }
  ]
}
</div></code></pre>
<p>When a Team Leader (TL) is absent, their responsibilities automatically delegate to their Shift Leader (SL). The SL can either:</p>
<ul>
<li>Execute the delegated TL role directly</li>
<li>Further delegate the TL role to another qualified TL</li>
</ul>
<h3 id="44-hierarchical-delegation-rules">4.4 Hierarchical Delegation Rules</h3>
<p>Delegation within hierarchical organizational structures follows specific rules to maintain management chain integrity and ensure proper authorization.</p>
<h4 id="441-hierarchical-delegation-principles">4.4.1 Hierarchical Delegation Principles</h4>
<ol>
<li><strong>Delegation Flow Direction</strong>: Delegations typically flow downward in the hierarchy</li>
<li><strong>Level Restrictions</strong>: Delegations are usually limited to adjacent levels</li>
<li><strong>Scope Limitations</strong>: Delegated authority cannot exceed the delegator's scope</li>
<li><strong>Contextual Validation</strong>: Delegation may require validation against organizational context</li>
</ol>
<h4 id="442-hierarchical-delegation-configuration">4.4.2 Hierarchical Delegation Configuration</h4>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"delegationRules"</span>: {
    <span class="hljs-attr">"hierarchyValidation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowSkipLevel"</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">"maxSkipLevels"</span>: <span class="hljs-number">0</span>,
    <span class="hljs-attr">"validateHierarchicalScope"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowDelegationChains"</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">"validateOrganizationalContext"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<h4 id="443-delegation-rules-matrix">4.4.3 Delegation Rules Matrix</h4>
<table>
<thead>
<tr>
<th>Delegator Role</th>
<th>Delegate Role</th>
<th>Permitted</th>
<th>Reason</th>
<th>Restrictions</th>
</tr>
</thead>
<tbody>
<tr>
<td>PLANT_MANAGER</td>
<td>SHIFT_LEADER</td>
<td>Yes</td>
<td>Direct hierarchical relationship</td>
<td>Limited to delegator's site scope</td>
</tr>
<tr>
<td>SHIFT_LEADER</td>
<td>TEAM_LEADER</td>
<td>Yes</td>
<td>Direct hierarchical relationship</td>
<td>Limited to delegator's shift and teams</td>
</tr>
<tr>
<td>PLANT_MANAGER</td>
<td>TEAM_LEADER</td>
<td>No</td>
<td>Skip level (configurable)</td>
<td>N/A</td>
</tr>
<tr>
<td>TEAM_LEADER</td>
<td>SHIFT_LEADER</td>
<td>No</td>
<td>Against hierarchy direction</td>
<td>N/A</td>
</tr>
<tr>
<td>QUALITY_INSPECTOR</td>
<td>TEAM_LEADER</td>
<td>No</td>
<td>No hierarchical relationship</td>
<td>N/A</td>
</tr>
</tbody>
</table>
<h3 id="45-delegation-authorization-model">4.5 Delegation Authorization Model</h3>
<p>The delegation authorization model defines how delegation permissions are evaluated against delegation requests, ensuring proper authorization throughout the delegation lifecycle.</p>
<h4 id="451-permission-evaluation-process">4.5.1 Permission Evaluation Process</h4>
<pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    A[Delegation Request] --> B{Has Required Permission?}
    B -->|No| C[Deny: Insufficient Permission]
    B -->|Yes| D{Validate Hierarchy Rules}
    D -->|Fail| E[Deny: Hierarchy Violation]
    D -->|Pass| F{Validate Scope Rules}
    F -->|Fail| G[Deny: Scope Violation]
    F -->|Pass| H{Validate Context Rules}
    H -->|Fail| I[Deny: Context Violation]
    H -->|Pass| J{Validate Permission Rules}
    J -->|Fail| K[Deny: Permission Violation]
    J -->|Pass| L[Approve Delegation]
</div></code></pre>
<h4 id="452-authorization-decision-factors">4.5.2 Authorization Decision Factors</h4>
<ol>
<li><strong>Permission Check</strong>: Verify the delegator has the appropriate delegation permission</li>
<li><strong>Hierarchy Validation</strong>: Ensure the delegation follows hierarchical constraints</li>
<li><strong>Scope Validation</strong>: Confirm the delegation is within appropriate scope boundaries</li>
<li><strong>Context Validation</strong>: Check business context rules (e.g., qualifications, conflicts)</li>
<li><strong>Permission Validation</strong>: Verify no non-delegable permissions are included</li>
</ol>
<h4 id="453-delegation-permission-structure">4.5.3 Delegation Permission Structure</h4>
<p>Delegation permissions follow the format <code>delegate:{direction}:{role}</code>, where:</p>
<ul>
<li><code>delegate</code> is the resource</li>
<li><code>{direction}</code> defines the delegation direction relative to the hierarchy (same, down, up, manage)</li>
<li><code>{role}</code> specifies the target role for delegation (or <code>any</code> for multiple roles)</li>
</ul>
<table>
<thead>
<tr>
<th>Permission</th>
<th>Description</th>
<th>Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>delegate:same:any</code></td>
<td>Delegate own role to someone with the same role</td>
<td>Team Leader to Team Leader</td>
</tr>
<tr>
<td><code>delegate:down:TEAM_LEADER</code></td>
<td>Delegate own role to a TEAM_LEADER</td>
<td>Shift Leader to Team Leader</td>
</tr>
<tr>
<td><code>delegate:same:SHIFT_LEADER</code></td>
<td>Delegate own role to another SHIFT_LEADER</td>
<td>Shift Leader to Shift Leader</td>
</tr>
<tr>
<td><code>delegate:manage:TEAM_LEADER</code></td>
<td>Manage delegations between TEAM_LEADER roles</td>
<td>For supervisors</td>
</tr>
<tr>
<td><code>delegate:temporary:any</code></td>
<td>Create temporary delegations (limited duration)</td>
<td>For vacation coverage</td>
</tr>
<tr>
<td><code>delegate:partial:any</code></td>
<td>Create partial role delegations</td>
<td>For specific task coverage</td>
</tr>
</tbody>
</table>
<h4 id="454-permission-mapping-examples">4.5.4 Permission Mapping Examples</h4>
<table>
<thead>
<tr>
<th>Scenario</th>
<th>Required Permission</th>
<th>Additional Validation</th>
</tr>
</thead>
<tbody>
<tr>
<td>Shift Leader delegates to another Shift Leader</td>
<td><code>delegate:same:SHIFT_LEADER</code></td>
<td>Validate shift scope</td>
</tr>
<tr>
<td>Shift Leader delegates to Team Leader</td>
<td><code>delegate:down:TEAM_LEADER</code></td>
<td>Validate hierarchical relationship and shared scope</td>
</tr>
<tr>
<td>Plant Manager manages delegation between Shift Leaders</td>
<td><code>delegate:manage:SHIFT_LEADER</code></td>
<td>Validate supervisory relationship and shared scope</td>
</tr>
<tr>
<td>Team Leader creates partial delegation to Team Leader</td>
<td><code>delegate:partial:TEAM_LEADER</code></td>
<td>Validate partial permission set</td>
</tr>
</tbody>
</table>
<h4 id="455-delegation-permission-assignment">4.5.5 Delegation Permission Assignment</h4>
<p>Delegation permissions are assigned to roles based on organizational policies:</p>
<table>
<thead>
<tr>
<th>Role</th>
<th>Delegation Permissions</th>
</tr>
</thead>
<tbody>
<tr>
<td>PLANT_MANAGER</td>
<td><code>delegate:same:PLANT_MANAGER</code>, <code>delegate:down:SHIFT_LEADER</code>, <code>delegate:manage:SHIFT_LEADER</code>, <code>delegate:partial:any</code></td>
</tr>
<tr>
<td>SHIFT_LEADER</td>
<td><code>delegate:same:SHIFT_LEADER</code>, <code>delegate:down:TEAM_LEADER</code>, <code>delegate:manage:TEAM_LEADER</code>, <code>delegate:partial:any</code></td>
</tr>
<tr>
<td>TEAM_LEADER</td>
<td><code>delegate:same:TEAM_LEADER</code> (within shift), <code>delegate:temporary:OPERATOR</code>, <code>delegate:partial:TEAM_LEADER</code></td>
</tr>
<tr>
<td>TRAINER_RESPONSIBLE</td>
<td><code>delegate:same:TRAINER_RESPONSIBLE</code>, <code>delegate:down:TRAINER</code>, <code>delegate:manage:TRAINER</code>, <code>delegate:partial:any</code></td>
</tr>
<tr>
<td>TRAINER</td>
<td><code>delegate:same:TRAINER</code> (within specialization), <code>delegate:partial:TRAINER</code></td>
</tr>
</tbody>
</table>
<h3 id="46-cross-level-and-same-level-delegation">4.6 Cross-Level and Same-Level Delegation</h3>
<p>The framework supports both cross-level (hierarchical) and same-level (peer) delegations with appropriate controls.</p>
<h4 id="461-same-level-delegation-peer-delegation">4.6.1 Same-Level Delegation (Peer Delegation)</h4>
<p>Same-level delegation allows users with the same role to delegate authority to each other, typically within the same organizational scope:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"sameLevelDelegationRules"</span>: {
    <span class="hljs-attr">"enforceSharedScope"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowedScopeDifferences"</span>: [<span class="hljs-string">"team"</span>],
    <span class="hljs-attr">"requireExplicitPermission"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"maxDuration"</span>: <span class="hljs-string">"30D"</span>,
    <span class="hljs-attr">"requireApproval"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"approvalRequiredFrom"</span>: [<span class="hljs-string">"delegatorSupervisor"</span>, <span class="hljs-string">"delegateeSupervisor"</span>],
    <span class="hljs-attr">"reasonRequired"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<p><strong>Example: Team Leader to Team Leader</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"delegationRequest"</span>: {
    <span class="hljs-attr">"delegatorId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"delegateId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
    <span class="hljs-attr">"validFrom"</span>: <span class="hljs-string">"2025-04-01T00:00:00Z"</span>,
    <span class="hljs-attr">"validUntil"</span>: <span class="hljs-string">"2025-04-14T23:59:59Z"</span>,
    <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Coverage during scheduled training"</span>,
    <span class="hljs-attr">"approvals"</span>: {
      <span class="hljs-attr">"delegatorSupervisor"</span>: {
        <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"PENDING"</span>
      },
      <span class="hljs-attr">"delegateeSupervisor"</span>: {
        <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"PENDING"</span>
      }
    },
    <span class="hljs-attr">"restrictions"</span>: {
      <span class="hljs-attr">"scope"</span>: {
        <span class="hljs-attr">"site"</span>: <span class="hljs-string">"SITE_A"</span>,
        <span class="hljs-attr">"department"</span>: <span class="hljs-string">"PRODUCTION"</span>,
        <span class="hljs-attr">"shift"</span>: <span class="hljs-string">"MORNING"</span>,
        <span class="hljs-attr">"team"</span>: <span class="hljs-string">"ASSEMBLY_1"</span>
      },
      <span class="hljs-attr">"permissions"</span>: {
        <span class="hljs-attr">"included"</span>: [<span class="hljs-string">"team:view"</span>, <span class="hljs-string">"operator:view"</span>, <span class="hljs-string">"schedule:view"</span>],
        <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"team:manage"</span>, <span class="hljs-string">"performance:review"</span>]
      }
    }
  }
}
</div></code></pre>
<h4 id="462-cross-level-delegation-hierarchical-delegation">4.6.2 Cross-Level Delegation (Hierarchical Delegation)</h4>
<p>Cross-level delegation follows the organizational hierarchy, allowing delegation to flow downward with appropriate permissions:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"crossLevelDelegationRules"</span>: {
    <span class="hljs-attr">"allowDirection"</span>: <span class="hljs-string">"downward"</span>,
    <span class="hljs-attr">"maxLevelDistance"</span>: <span class="hljs-number">1</span>,
    <span class="hljs-attr">"requireExplicitPermission"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enforceStrictScopeValidation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"maxDuration"</span>: <span class="hljs-string">"90D"</span>,
    <span class="hljs-attr">"requireApproval"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"approvalRequiredFrom"</span>: [<span class="hljs-string">"delegatorSupervisor"</span>, <span class="hljs-string">"delegateeSupervisor"</span>],
    <span class="hljs-attr">"reasonRequired"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<p><strong>Example: Shift Leader to Team Leader</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"delegationRequest"</span>: {
    <span class="hljs-attr">"delegatorId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"delegateId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"SHIFT_LEADER"</span>,
    <span class="hljs-attr">"validFrom"</span>: <span class="hljs-string">"2025-04-01T00:00:00Z"</span>,
    <span class="hljs-attr">"validUntil"</span>: <span class="hljs-string">"2025-04-14T23:59:59Z"</span>,
    <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Leadership development opportunity"</span>,
    <span class="hljs-attr">"approvals"</span>: {
      <span class="hljs-attr">"delegatorSupervisor"</span>: {
        <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"APPROVED"</span>
      },
      <span class="hljs-attr">"delegateeSupervisor"</span>: {
        <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"APPROVED"</span>
      }
    },
    <span class="hljs-attr">"restrictions"</span>: {
      <span class="hljs-attr">"scope"</span>: {
        <span class="hljs-attr">"site"</span>: <span class="hljs-string">"SITE_A"</span>,
        <span class="hljs-attr">"department"</span>: <span class="hljs-string">"PRODUCTION"</span>,
        <span class="hljs-attr">"shift"</span>: <span class="hljs-string">"MORNING"</span>
      },
      <span class="hljs-attr">"permissions"</span>: {
        <span class="hljs-attr">"included"</span>: [<span class="hljs-string">"team:manage"</span>, <span class="hljs-string">"schedule:view"</span>, <span class="hljs-string">"incidents:handle"</span>],
        <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"performance:review"</span>, <span class="hljs-string">"budget:manage"</span>]
      }
    }
  }
}
</div></code></pre>
<h4 id="463-supervisor-managed-delegation">4.6.3 Supervisor-Managed Delegation</h4>
<p>Supervisors can manage delegations between their direct reports with the appropriate delegation management permission:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"managedDelegationRules"</span>: {
    <span class="hljs-attr">"requireDirectSupervisor"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireSharedScope"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowDelegationOutsideTeam"</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">"requireExplicitPermission"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"notifyParticipants"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"reasonRequired"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<p><strong>Example: Shift Leader Managing Team Leader Delegations</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"managedDelegationRequest"</span>: {
    <span class="hljs-attr">"managerId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"delegatorId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"delegateId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
    <span class="hljs-attr">"validFrom"</span>: <span class="hljs-string">"2025-04-01T00:00:00Z"</span>,
    <span class="hljs-attr">"validUntil"</span>: <span class="hljs-string">"2025-04-14T23:59:59Z"</span>,
    <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Coverage during scheduled training"</span>,
    <span class="hljs-attr">"restrictions"</span>: {
      <span class="hljs-attr">"scope"</span>: {
        <span class="hljs-attr">"team"</span>: <span class="hljs-string">"ASSEMBLY_1"</span>
      },
      <span class="hljs-attr">"permissions"</span>: {
        <span class="hljs-attr">"included"</span>: [<span class="hljs-string">"team:view"</span>, <span class="hljs-string">"schedule:view"</span>],
        <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"performance:review"</span>]
      }
    }
  }
}
</div></code></pre>
<h4 id="464-partial-role-delegation">4.6.4 Partial Role Delegation</h4>
<p>The system supports delegation of a subset of permissions within a role, allowing for fine-grained responsibility transfer:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"partialDelegationRules"</span>: {
    <span class="hljs-attr">"allowPartialDelegation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireExplicitPermission"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"minimumPermissionsRequired"</span>: <span class="hljs-number">1</span>,
    <span class="hljs-attr">"restrictedPermissions"</span>: [
      <span class="hljs-string">"budget:approve"</span>,
      <span class="hljs-string">"performance:finalize"</span>,
      <span class="hljs-string">"admin:access"</span>
    ],
    <span class="hljs-attr">"organizationConfigurableRestrictions"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"reasonRequired"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireApproval"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<p><strong>Example: Partial Team Leader Delegation</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"partialDelegationRequest"</span>: {
    <span class="hljs-attr">"delegatorId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"delegateId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"roleId"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
    <span class="hljs-attr">"validFrom"</span>: <span class="hljs-string">"2025-04-01T00:00:00Z"</span>,
    <span class="hljs-attr">"validUntil"</span>: <span class="hljs-string">"2025-04-14T23:59:59Z"</span>,
    <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Need assistance with schedule management while focusing on quality issues"</span>,
    <span class="hljs-attr">"approvals"</span>: {
      <span class="hljs-attr">"delegatorSupervisor"</span>: {
        <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"APPROVED"</span>
      }
    },
    <span class="hljs-attr">"restrictions"</span>: {
      <span class="hljs-attr">"scope"</span>: {
        <span class="hljs-attr">"team"</span>: <span class="hljs-string">"ASSEMBLY_1"</span>
      },
      <span class="hljs-attr">"permissions"</span>: {
        <span class="hljs-attr">"included"</span>: [<span class="hljs-string">"schedule:view"</span>, <span class="hljs-string">"schedule:manage"</span>, <span class="hljs-string">"schedule:approve"</span>],
        <span class="hljs-attr">"excluded"</span>: [<span class="hljs-string">"team:manage"</span>, <span class="hljs-string">"performance:review"</span>, <span class="hljs-string">"budget:request"</span>]
      }
    }
  }
}
</div></code></pre>
<h3 id="47-advanced-delegation-scenarios">4.7 Advanced Delegation Scenarios</h3>
<h4 id="471-emergency-access">4.7.1 Emergency Access</h4>
<p>Emergency situations may require expedited access to critical functions, bypassing standard delegation procedures.</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Requester
    participant API as API Backend
    participant Approver
    participant Delegate

    Requester->>API: Create Emergency Delegation Request
    API->>Approver: Send Approval Request (urgent)
    Approver->>API: Approve Emergency Request
    API->>API: Create Emergency Delegation & Profile
    API->>Delegate: Notify of Emergency Access
    API->>Requester: Confirm Emergency Delegation
</div></code></pre>
<p><strong>Emergency Delegation Configuration</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"emergencyDelegationRules"</span>: {
    <span class="hljs-attr">"allowEmergencyDelegation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowedRequesters"</span>: [<span class="hljs-string">"PLANT_MANAGER"</span>, <span class="hljs-string">"SHIFT_LEADER"</span>, <span class="hljs-string">"SYSTEM_ADMIN"</span>],
    <span class="hljs-attr">"expeditedApproval"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"maxDuration"</span>: <span class="hljs-string">"24H"</span>,
    <span class="hljs-attr">"autoExpire"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enhancedLogging"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"bypassScopeValidation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requirePostIncidentReview"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"notifySecurity"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<p><strong>Emergency Access Controls</strong></p>
<ol>
<li>Pre-defined emergency profiles with appropriate permissions</li>
<li>Short duration (typically 8-24 hours)</li>
<li>Enhanced monitoring and logging</li>
<li>Automatic notifications to security team</li>
<li>Post-incident review required</li>
<li>Clearly documented reason for emergency access</li>
</ol>
<h4 id="472-role-conflict-management">4.7.2 Role Conflict Management</h4>
<p>Some role combinations may create conflicts of interest and should be managed through the delegation framework.</p>
<p><strong>Conflict Resolution Strategies</strong></p>
<ol>
<li><strong>Profile Separation</strong>: Keep conflicting roles in separate profiles</li>
<li><strong>Scope Restrictions</strong>: Limit scope where conflicting roles apply</li>
<li><strong>Operational Exclusions</strong>: Remove specific permissions that create conflicts</li>
<li><strong>Approval Requirements</strong>: Require additional approval for conflicting actions</li>
<li><strong>Enhanced Logging</strong>: Increase audit detail for potential conflict scenarios</li>
</ol>
<p><strong>Conflict Management Configuration</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"roleConflictRules"</span>: {
    <span class="hljs-attr">"conflictingRolePairs"</span>: [
      {
        <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"QUALITY_INSPECTOR"</span>, <span class="hljs-string">"PRODUCTION_MANAGER"</span>],
        <span class="hljs-attr">"resolutionStrategy"</span>: <span class="hljs-string">"SEPARATION"</span>
      },
      {
        <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"FINANCIAL_APPROVER"</span>, <span class="hljs-string">"BUDGET_REQUESTER"</span>],
        <span class="hljs-attr">"resolutionStrategy"</span>: <span class="hljs-string">"APPROVAL_REQUIRED"</span>
      },
      {
        <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"SYSTEM_ADMIN"</span>, <span class="hljs-string">"AUDITOR"</span>],
        <span class="hljs-attr">"resolutionStrategy"</span>: <span class="hljs-string">"OPERATIONAL_EXCLUSION"</span>
      }
    ],
    <span class="hljs-attr">"delegationBehavior"</span>: {
      <span class="hljs-attr">"preventConflictingDelegations"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"additionalApprovalRequired"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"enhancedLoggingForConflicts"</span>: <span class="hljs-literal">true</span>
    }
  }
}
</div></code></pre>
<h4 id="473-temporary-access-management">4.7.3 Temporary Access Management</h4>
<p>Short-term access needs can be handled through streamlined delegation with appropriate controls.</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Manager
    participant API as API Backend
    participant Temporary as Temporary User

    Manager->>API: Create Temporary Access Request
    API->>API: Validate Manager Authority
    API->>API: Create Time-Limited Delegation
    API->>API: Create Restricted Profile
    API->>Temporary: Notify of Temporary Access
    API->>Manager: Confirm Temporary Access Creation
</div></code></pre>
<p><strong>Temporary Access Configuration</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"temporaryAccessRules"</span>: {
    <span class="hljs-attr">"maxDuration"</span>: <span class="hljs-string">"30D"</span>,
    <span class="hljs-attr">"requireJustification"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enforceRestrictions"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireApproval"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"automaticExpiration"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"allowRenewal"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"maxRenewals"</span>: <span class="hljs-number">2</span>,
    <span class="hljs-attr">"enhancedLogging"</span>: <span class="hljs-literal">true</span>
  }
}
</div></code></pre>
<h4 id="474-automatic-delegation-n1-substitution">4.7.4 Automatic Delegation (N+1 Substitution)</h4>
<p>The system supports automatic delegation to a supervisor (N+1) when an employee (N) is absent, ensuring business continuity.</p>
<p><strong>Automatic Delegation Flow</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant HRIS as HR System
    participant System as Delegation System
    participant Supervisor as N+1 Supervisor
    participant Alternate as Alternate Delegate

    HRIS->>System: Employee Absence Notification
    System->>System: Check Automatic Delegation Policy
    System->>System: Create Automatic Delegation to N+1
    System->>Supervisor: Notify of Automatic Delegation
    Note over Supervisor: Two options
    Supervisor->>System: Accept Delegation
    Supervisor->>System: Redirect Delegation
    System->>Alternate: Notify of Redirected Delegation
    Alternate->>System: Accept Delegation
    System->>System: Update Delegation Records
</div></code></pre>
<p><strong>Automatic Delegation Configuration</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"automaticDelegationRules"</span>: {
    <span class="hljs-attr">"enabledForRoles"</span>: [<span class="hljs-string">"TEAM_LEADER"</span>, <span class="hljs-string">"SHIFT_LEADER"</span>, <span class="hljs-string">"QUALITY_INSPECTOR"</span>],
    <span class="hljs-attr">"triggerEvents"</span>: [<span class="hljs-string">"ABSENCE"</span>, <span class="hljs-string">"VACATION"</span>, <span class="hljs-string">"SICK_LEAVE"</span>],
    <span class="hljs-attr">"defaultDelegateLevel"</span>: <span class="hljs-string">"DIRECT_SUPERVISOR"</span>,
    <span class="hljs-attr">"allowRedirection"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireRedirectionApproval"</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">"notifyHierarchy"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"scope"</span>: <span class="hljs-string">"SAME_AS_ABSENT_USER"</span>,
    <span class="hljs-attr">"permissions"</span>: {
      <span class="hljs-attr">"strategy"</span>: <span class="hljs-string">"FULL_ROLE"</span>,
      <span class="hljs-attr">"excludeRestrictedPermissions"</span>: <span class="hljs-literal">true</span>
    },
    <span class="hljs-attr">"automaticExpiration"</span>: {
      <span class="hljs-attr">"onUserReturn"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"gracePeriod"</span>: <span class="hljs-string">"8H"</span>
    }
  }
}
</div></code></pre>
<p><strong>Example: Automatic Delegation When Team Leader Is Absent</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"automaticDelegation"</span>: {
    <span class="hljs-attr">"absentUserId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"absentUserRole"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
    <span class="hljs-attr">"delegateId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
    <span class="hljs-attr">"delegateRole"</span>: <span class="hljs-string">"SHIFT_LEADER"</span>,
    <span class="hljs-attr">"delegationType"</span>: <span class="hljs-string">"AUTOMATIC"</span>,
    <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Sick leave (automatic)"</span>,
    <span class="hljs-attr">"validFrom"</span>: <span class="hljs-string">"2025-04-01T00:00:00Z"</span>,
    <span class="hljs-attr">"validUntil"</span>: <span class="hljs-literal">null</span>,
    <span class="hljs-attr">"expirationTrigger"</span>: <span class="hljs-string">"USER_RETURN"</span>,
    <span class="hljs-attr">"scope"</span>: {
      <span class="hljs-attr">"site"</span>: <span class="hljs-string">"SITE_A"</span>,
      <span class="hljs-attr">"department"</span>: <span class="hljs-string">"PRODUCTION"</span>,
      <span class="hljs-attr">"shift"</span>: <span class="hljs-string">"MORNING"</span>,
      <span class="hljs-attr">"team"</span>: <span class="hljs-string">"ASSEMBLY_1"</span>
    },
    <span class="hljs-attr">"redirectionStatus"</span>: {
      <span class="hljs-attr">"redirected"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"redirectedTo"</span>: <span class="hljs-string">"<EMAIL>"</span>,
      <span class="hljs-attr">"redirectionReason"</span>: <span class="hljs-string">"Higher priority tasks"</span>,
      <span class="hljs-attr">"redirectionTime"</span>: <span class="hljs-string">"2025-04-01T08:30:00Z"</span>
    }
  }
}
</div></code></pre>
<h3 id="48-approval-workflow">4.8 Approval Workflow</h3>
<p>The delegation framework includes a comprehensive approval process for delegation requests, ensuring proper oversight and control.</p>
<h4 id="482-required-approvers">4.8.2 Required Approvers</h4>
<p>The system supports multiple approval scenarios based on organizational requirements:</p>
<table>
<thead>
<tr>
<th>Approval Type</th>
<th>Description</th>
<th>Required For</th>
</tr>
</thead>
<tbody>
<tr>
<td>Delegator's Supervisor</td>
<td>Direct manager of the delegating user</td>
<td>Most delegation types</td>
</tr>
<tr>
<td>Delegatee's Supervisor</td>
<td>Direct manager of the user receiving delegation</td>
<td>Cross-department delegations</td>
</tr>
<tr>
<td>Department Head</td>
<td>Head of affected department</td>
<td>Delegations affecting critical functions</td>
</tr>
<tr>
<td>Role Administrator</td>
<td>Administrator of the role being delegated</td>
<td>High-security or critical roles</td>
</tr>
<tr>
<td>Security Officer</td>
<td>Security team member</td>
<td>Emergency access or sensitive system access</td>
</tr>
<tr>
<td>Multiple Level Management</td>
<td>Multiple levels of management</td>
<td>Executive role delegations</td>
</tr>
</tbody>
</table>
<h4 id="483-approval-configuration">4.8.3 Approval Configuration</h4>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"approvalConfiguration"</span>: {
    <span class="hljs-attr">"defaultRequiredApprovers"</span>: [<span class="hljs-string">"delegatorSupervisor"</span>],
    <span class="hljs-attr">"roleSpecificApprovals"</span>: {
      <span class="hljs-attr">"PLANT_MANAGER"</span>: [<span class="hljs-string">"delegatorSupervisor"</span>, <span class="hljs-string">"securityOfficer"</span>],
      <span class="hljs-attr">"FINANCIAL_APPROVER"</span>: [
        <span class="hljs-string">"delegatorSupervisor"</span>,
        <span class="hljs-string">"financeHead"</span>,
        <span class="hljs-string">"delegateeSupervisor"</span>
      ]
    },
    <span class="hljs-attr">"crossDepartmentApprovals"</span>: [
      <span class="hljs-string">"delegatorSupervisor"</span>,
      <span class="hljs-string">"delegateeSupervisor"</span>,
      <span class="hljs-string">"departmentHead"</span>
    ],
    <span class="hljs-attr">"emergencyApprovals"</span>: [<span class="hljs-string">"securityOfficer"</span>],
    <span class="hljs-attr">"approvalTimeouts"</span>: {
      <span class="hljs-attr">"standard"</span>: <span class="hljs-string">"72H"</span>,
      <span class="hljs-attr">"emergency"</span>: <span class="hljs-string">"1H"</span>,
      <span class="hljs-attr">"escalation"</span>: {
        <span class="hljs-attr">"enabled"</span>: <span class="hljs-literal">true</span>,
        <span class="hljs-attr">"escalateAfter"</span>: <span class="hljs-string">"24H"</span>,
        <span class="hljs-attr">"escalateTo"</span>: <span class="hljs-string">"nextLevelManager"</span>
      }
    },
    <span class="hljs-attr">"delegationStartBehavior"</span>: {
      <span class="hljs-attr">"requireAllApprovalsBeforeStart"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"allowScheduledStartWithApprovals"</span>: <span class="hljs-literal">true</span>
    }
  }
}
</div></code></pre>
<h4 id="484-approval-records">4.8.4 Approval Records</h4>
<p>Each delegation retains detailed approval records to maintain audit trails:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"delegationApprovals"</span>: {
    <span class="hljs-attr">"delegationId"</span>: <span class="hljs-string">"delegation-123"</span>,
    <span class="hljs-attr">"status"</span>: <span class="hljs-string">"APPROVED"</span>,
    <span class="hljs-attr">"approvalSteps"</span>: [
      {
        <span class="hljs-attr">"approverType"</span>: <span class="hljs-string">"delegatorSupervisor"</span>,
        <span class="hljs-attr">"approverId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"APPROVED"</span>,
        <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-03-25T14:30:00Z"</span>,
        <span class="hljs-attr">"comments"</span>: <span class="hljs-string">"Approved for training coverage"</span>
      },
      {
        <span class="hljs-attr">"approverType"</span>: <span class="hljs-string">"delegateeSupervisor"</span>,
        <span class="hljs-attr">"approverId"</span>: <span class="hljs-string">"<EMAIL>"</span>,
        <span class="hljs-attr">"status"</span>: <span class="hljs-string">"APPROVED"</span>,
        <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-03-25T16:45:00Z"</span>,
        <span class="hljs-attr">"comments"</span>: <span class="hljs-string">"Delegatee has required qualifications"</span>
      }
    ],
    <span class="hljs-attr">"finalApprovalDate"</span>: <span class="hljs-string">"2025-03-25T16:45:00Z"</span>,
    <span class="hljs-attr">"delegationExecutionDate"</span>: <span class="hljs-string">"2025-04-01T00:00:00Z"</span>
  }
}
</div></code></pre>
<h3 id="49-organization-configurable-restrictions">4.9 Organization-Configurable Restrictions</h3>
<p>The system allows organizations to define global delegation restrictions and configure which permissions can or cannot be delegated.</p>
<h4 id="491-global-delegation-configuration">4.9.1 Global Delegation Configuration</h4>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"organizationDelegationConfiguration"</span>: {
    <span class="hljs-attr">"allowDelegation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"maxDelegationDuration"</span>: <span class="hljs-string">"90D"</span>,
    <span class="hljs-attr">"enablePartialDelegation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enableAutomaticDelegation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"enableEmergencyDelegation"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireApprovals"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requireReason"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"neverDelegatePermissions"</span>: [
      <span class="hljs-string">"user:delete"</span>,
      <span class="hljs-string">"role:create"</span>,
      <span class="hljs-string">"system:configure"</span>,
      <span class="hljs-string">"security:override"</span>
    ],
    <span class="hljs-attr">"restrictedDelegationPermissions"</span>: [
      <span class="hljs-string">"budget:approve:above:10000"</span>,
      <span class="hljs-string">"performance:finalize"</span>,
      <span class="hljs-string">"disciplinary:initiate"</span>
    ],
    <span class="hljs-attr">"delegationCreationRoles"</span>: [
      <span class="hljs-string">"SUPER_ADMIN"</span>,
      <span class="hljs-string">"PLANT_MANAGER"</span>,
      <span class="hljs-string">"SHIFT_LEADER"</span>,
      <span class="hljs-string">"TEAM_LEADER"</span>
    ],
    <span class="hljs-attr">"hierarchyRules"</span>: {
      <span class="hljs-attr">"respectHierarchy"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"maxLevelSkip"</span>: <span class="hljs-number">1</span>,
      <span class="hljs-attr">"allowCrossDepartment"</span>: <span class="hljs-literal">false</span>,
      <span class="hljs-attr">"allowCrossSite"</span>: <span class="hljs-literal">false</span>
    },
    <span class="hljs-attr">"notificationRules"</span>: {
      <span class="hljs-attr">"notifyDelegator"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"notifyDelegatee"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"notifyDelegatorSupervisor"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"notifyDelegateeSupervisor"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"notifySecurityTeam"</span>: [<span class="hljs-string">"EMERGENCY"</span>, <span class="hljs-string">"SENSITIVE_ROLE"</span>]
    }
  }
}
</div></code></pre>
<h4 id="492-role-specific-delegation-restrictions">4.9.2 Role-Specific Delegation Restrictions</h4>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"roleDelegationRestrictions"</span>: {
    <span class="hljs-attr">"PLANT_MANAGER"</span>: {
      <span class="hljs-attr">"canBeDelegated"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"requiresApproval"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"requiredApprovers"</span>: [<span class="hljs-string">"superAdmin"</span>, <span class="hljs-string">"securityOfficer"</span>],
      <span class="hljs-attr">"maxDelegationDuration"</span>: <span class="hljs-string">"30D"</span>,
      <span class="hljs-attr">"nonDelegablePermissions"</span>: [
        <span class="hljs-string">"budget:approve:above:50000"</span>,
        <span class="hljs-string">"employee:terminate"</span>,
        <span class="hljs-string">"facility:decommission"</span>
      ],
      <span class="hljs-attr">"allowedDelegateeRoles"</span>: [<span class="hljs-string">"PLANT_MANAGER"</span>, <span class="hljs-string">"ASSISTANT_PLANT_MANAGER"</span>]
    },
    <span class="hljs-attr">"FINANCIAL_APPROVER"</span>: {
      <span class="hljs-attr">"canBeDelegated"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"requiresApproval"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"requiredApprovers"</span>: [<span class="hljs-string">"financeHead"</span>, <span class="hljs-string">"complianceOfficer"</span>],
      <span class="hljs-attr">"maxDelegationDuration"</span>: <span class="hljs-string">"14D"</span>,
      <span class="hljs-attr">"nonDelegablePermissions"</span>: [
        <span class="hljs-string">"budget:approve:above:10000"</span>,
        <span class="hljs-string">"payment:authorize:above:5000"</span>
      ],
      <span class="hljs-attr">"allowedDelegateeRoles"</span>: [<span class="hljs-string">"FINANCIAL_APPROVER"</span>, <span class="hljs-string">"FINANCE_MANAGER"</span>]
    }
  }
}
</div></code></pre>
<h4 id="493-permission-delegation-matrix">4.9.3 Permission Delegation Matrix</h4>
<p>The organization can define a detailed matrix of which permissions can be delegated by which roles:</p>
<table>
<thead>
<tr>
<th>Permission</th>
<th style="text-align:center">SUPER_ADMIN</th>
<th style="text-align:center">PLANT_MANAGER</th>
<th style="text-align:center">SHIFT_LEADER</th>
<th style="text-align:center">TEAM_LEADER</th>
<th style="text-align:center">QUALITY_INSPECTOR</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Core Operations</strong></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
</tr>
<tr>
<td>team:view</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
</tr>
<tr>
<td>team:manage</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td>schedule:view</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
</tr>
<tr>
<td>schedule:manage</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td><strong>Critical Functions</strong></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
</tr>
<tr>
<td>budget:view</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td>budget:approve</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Partial</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td>performance:review</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Delegate</td>
<td style="text-align:center">Partial</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td><strong>Administrative</strong></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
<td style="text-align:center"></td>
</tr>
<tr>
<td>user:create</td>
<td style="text-align:center">Partial</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td>role:assign</td>
<td style="text-align:center">Partial</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
<tr>
<td>system:configure</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
<td style="text-align:center">No Delegate</td>
</tr>
</tbody>
</table>
<p>Legend:</p>
<ul>
<li><strong>Delegate</strong>: Can be fully delegated</li>
<li><strong>Partial</strong>: Can be delegated with restrictions</li>
<li><strong>No Delegate</strong>: Cannot be delegated</li>
</ul>
<h2 id="5-microsoft-graph-api-integration">5. Microsoft Graph API Integration</h2>
<p>This section details the integration between our authentication system and Microsoft Graph API, which enables real-time user provisioning, profile synchronization, and group-based role management with Microsoft Entra ID (formerly Azure AD).</p>
<h3 id="51-service-principal-configuration">5.1. Service Principal Configuration</h3>
<p>The integration with Microsoft Graph API requires a properly configured service principal in Microsoft Entra ID with appropriate permissions and security settings.</p>
<h4 id="511-required-permissions">5.1.1. Required Permissions</h4>
<table>
<thead>
<tr>
<th>Permission</th>
<th>Type</th>
<th>Description</th>
<th>Justification</th>
</tr>
</thead>
<tbody>
<tr>
<td>User.Read.All</td>
<td>Application</td>
<td>Read all user profiles</td>
<td>Required to fetch comprehensive user data during JIT provisioning and refresh</td>
</tr>
<tr>
<td>GroupMember.Read.All</td>
<td>Application</td>
<td>Read all group memberships</td>
<td>Required to map Azure AD groups to application roles</td>
</tr>
<tr>
<td>Directory.Read.All</td>
<td>Application</td>
<td>Read directory data</td>
<td>Required for advanced directory queries and delta operations</td>
</tr>
</tbody>
</table>
<h4 id="512-authentication-methods">5.1.2. Authentication Methods</h4>
<p>Two primary authentication methods are supported for the service principal:</p>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    A[Service Principal Authentication] --> B[Client Credentials Flow]
    A --> C[Certificate-Based Authentication]
    B --> D[Uses Client ID and Secret]
    C --> E[Uses Client ID and Certificate]
    D --> F[Lower Setup Complexity]
    E --> G[Higher Security]
</div></code></pre>
<p><strong>Client Credentials Implementation:</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"tenant_id"</span>: <span class="hljs-string">"your-tenant-id"</span>,
  <span class="hljs-attr">"client_id"</span>: <span class="hljs-string">"your-client-id"</span>,
  <span class="hljs-attr">"client_secret"</span>: <span class="hljs-string">"your-client-secret"</span>,
  <span class="hljs-attr">"scope"</span>: <span class="hljs-string">"https://graph.microsoft.com/.default"</span>
}
</div></code></pre>
<p><strong>Certificate-Based Implementation:</strong></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"tenant_id"</span>: <span class="hljs-string">"your-tenant-id"</span>,
  <span class="hljs-attr">"client_id"</span>: <span class="hljs-string">"your-client-id"</span>,
  <span class="hljs-attr">"certificate_thumbprint"</span>: <span class="hljs-string">"certificate-thumbprint"</span>,
  <span class="hljs-attr">"private_key"</span>: <span class="hljs-string">"reference-to-private-key"</span>,
  <span class="hljs-attr">"scope"</span>: <span class="hljs-string">"https://graph.microsoft.com/.default"</span>
}
</div></code></pre>
<h4 id="513-security-best-practices">5.1.3. Security Best Practices</h4>
<table>
<thead>
<tr>
<th>Best Practice</th>
<th>Implementation</th>
</tr>
</thead>
<tbody>
<tr>
<td>Least Privilege</td>
<td>Assign only required permissions; avoid global admin roles</td>
</tr>
<tr>
<td>Credential Rotation</td>
<td>Implement 90-day rotation for client secrets</td>
</tr>
<tr>
<td>Secure Storage</td>
<td>Store credentials in a secure vault (Azure Key Vault, HashiCorp Vault)</td>
</tr>
<tr>
<td>Access Auditing</td>
<td>Enable audit logs for service principal activity</td>
</tr>
<tr>
<td>IP Restrictions</td>
<td>Apply conditional access policies to restrict access to known IP ranges</td>
</tr>
</tbody>
</table>
<h3 id="52-user-data-management">5.2. User Data Management</h3>
<p>This section details how user profile data is retrieved, processed, and synchronized between Microsoft Entra ID and the application database.</p>
<h4 id="521-user-profile-retrieval">5.2.1. User Profile Retrieval</h4>
<p><strong>Graph API Endpoint:</strong></p>
<pre class="hljs"><code><div>GET https://graph.microsoft.com/v1.0/users/{userPrincipalName or id}
</div></code></pre>
<p><strong>Requested User Attributes:</strong></p>
<table>
<thead>
<tr>
<th>Attribute</th>
<th>Description</th>
<th>Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td>displayName</td>
<td>User's full name</td>
<td>User profile display</td>
</tr>
<tr>
<td>givenName</td>
<td>First name</td>
<td>Personalization</td>
</tr>
<tr>
<td>surname</td>
<td>Last name</td>
<td>User identification</td>
</tr>
<tr>
<td>mail</td>
<td>Primary email</td>
<td>Communication, unique identifier</td>
</tr>
<tr>
<td>userPrincipalName</td>
<td>UPN</td>
<td>Alternative identifier</td>
</tr>
<tr>
<td>jobTitle</td>
<td>Position</td>
<td>Role-based access control</td>
</tr>
<tr>
<td>department</td>
<td>Organizational unit</td>
<td>Departmental permissions</td>
</tr>
<tr>
<td>officeLocation</td>
<td>Physical location</td>
<td>Regional settings</td>
</tr>
<tr>
<td>employeeId</td>
<td>HR identifier</td>
<td>Integration with other systems</td>
</tr>
<tr>
<td>id</td>
<td>Object ID</td>
<td>Graph API reference</td>
</tr>
</tbody>
</table>
<p><strong>Synchronization Flow:</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant App as Application
    participant Graph as Microsoft Graph API
    participant DB as Application Database

    App->>Graph: Request user profile data
    Graph->>App: Return user attributes
    App->>App: Transform data to application model
    App->>DB: Store/update user profile
    Note over App,DB: During each token refresh
</div></code></pre>
<h4 id="522-just-in-time-jit-user-provisioning">5.2.2. Just-In-Time (JIT) User Provisioning</h4>
<p>The system implements Just-In-Time user provisioning to create user accounts automatically upon first login:</p>
<ol>
<li>User authenticates with SAML SSO</li>
<li>System checks if user exists in the application database</li>
<li>If not found, retrieves comprehensive user profile from Graph API</li>
<li>Creates new user record with complete profile information</li>
<li>Maps Azure AD groups to application roles</li>
<li>Issues JWT tokens with user profile and role information</li>
</ol>
<h4 id="523-profile-synchronization-strategy">5.2.3. Profile Synchronization Strategy</h4>
<table>
<thead>
<tr>
<th>Event</th>
<th>Synchronization Action</th>
</tr>
</thead>
<tbody>
<tr>
<td>First Authentication</td>
<td>Full profile creation from Graph API</td>
</tr>
<tr>
<td>Token Refresh</td>
<td>Differential update of profile attributes</td>
</tr>
<tr>
<td>Group Membership Change</td>
<td>Update of roles and permissions</td>
</tr>
<tr>
<td>Periodic Background Job</td>
<td>Full synchronization for inactive users</td>
</tr>
</tbody>
</table>
<h3 id="53-group-membership-management">5.3. Group Membership Management</h3>
<p>This section details how Azure AD group memberships are retrieved and mapped to application roles.</p>
<h4 id="531-group-membership-retrieval">5.3.1. Group Membership Retrieval</h4>
<p><strong>Graph API Endpoint:</strong></p>
<pre class="hljs"><code><div>GET https://graph.microsoft.com/v1.0/users/{id}/memberOf
</div></code></pre>
<p><strong>Optional Filtering:</strong></p>
<pre class="hljs"><code><div>GET https://graph.microsoft.com/v1.0/users/{id}/memberOf?$filter=securityEnabled eq true
</div></code></pre>
<p><strong>Implementation Considerations:</strong></p>
<table>
<thead>
<tr>
<th>Consideration</th>
<th>Approach</th>
</tr>
</thead>
<tbody>
<tr>
<td>Large Group Sets</td>
<td>Implement pagination handling for users with many groups</td>
</tr>
<tr>
<td>Nested Groups</td>
<td>Process transitive memberships when required</td>
</tr>
<tr>
<td>Response Caching</td>
<td>Cache group memberships with appropriate TTL</td>
</tr>
<tr>
<td>Error Handling</td>
<td>Implement fallback for group retrieval failures</td>
</tr>
</tbody>
</table>
<h4 id="532-group-to-role-mapping-configuration">5.3.2. Group-to-Role Mapping Configuration</h4>
<p>The system maintains a configuration mapping between Azure AD groups and application roles:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"group-mappings"</span>: [
    {
      <span class="hljs-attr">"group-id"</span>: <span class="hljs-string">"11b0131d-43c9-4734-9673-f2bc7f14f5e2"</span>,
      <span class="hljs-attr">"group-name"</span>: <span class="hljs-string">"Application Administrators"</span>,
      <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"admin"</span>, <span class="hljs-string">"user"</span>],
      <span class="hljs-attr">"permissions"</span>: [<span class="hljs-string">"full_access"</span>]
    },
    {
      <span class="hljs-attr">"group-id"</span>: <span class="hljs-string">"a8294692-bda8-41d5-91c8-58e3c54c9a2b"</span>,
      <span class="hljs-attr">"group-name"</span>: <span class="hljs-string">"Finance Department"</span>,
      <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"finance"</span>, <span class="hljs-string">"user"</span>],
      <span class="hljs-attr">"permissions"</span>: [<span class="hljs-string">"finance_read"</span>, <span class="hljs-string">"finance_write"</span>]
    },
    {
      <span class="hljs-attr">"group-id"</span>: <span class="hljs-string">"d4c46c76-7a5e-4aed-9a1f-ab72d7a39aa9"</span>,
      <span class="hljs-attr">"group-name"</span>: <span class="hljs-string">"HR Managers"</span>,
      <span class="hljs-attr">"roles"</span>: [<span class="hljs-string">"hr_manager"</span>, <span class="hljs-string">"user"</span>],
      <span class="hljs-attr">"permissions"</span>: [<span class="hljs-string">"employee_read"</span>, <span class="hljs-string">"employee_write"</span>]
    }
  ]
}
</div></code></pre>
<h4 id="533-dynamic-role-assignment">5.3.3. Dynamic Role Assignment</h4>
<p>The system dynamically assigns roles based on group membership during:</p>
<ol>
<li>Initial JIT provisioning</li>
<li>Each token refresh operation</li>
<li>Periodic background synchronization</li>
</ol>
<h3 id="54-delta-query-implementation">5.4. Delta Query Implementation</h3>
<p>This section details how the system uses Microsoft Graph API's delta query functionality to efficiently track and apply changes from Microsoft Entra ID.</p>
<h4 id="541-delta-query-fundamentals">5.4.1. Delta Query Fundamentals</h4>
<p>The delta query feature allows the system to request only changes to users and groups since the last synchronization, significantly reducing bandwidth and processing requirements.</p>
<p><strong>Initial Delta Query:</strong></p>
<pre class="hljs"><code><div>GET https://graph.microsoft.com/v1.0/users/delta
</div></code></pre>
<p><strong>Subsequent Delta Query using Delta Link:</strong></p>
<pre class="hljs"><code><div>GET {deltaLink-from-previous-response}
</div></code></pre>
<h4 id="542-tracking-changes">5.4.2. Tracking Changes</h4>
<p>The system maintains delta tokens to track the state of synchronization:</p>
<table>
<thead>
<tr>
<th>Entity Type</th>
<th>Tracked Changes</th>
<th>Delta Token Storage</th>
</tr>
</thead>
<tbody>
<tr>
<td>Users</td>
<td>Profile attributes</td>
<td>Database table with timestamp</td>
</tr>
<tr>
<td>Groups</td>
<td>Membership changes</td>
<td>Database table with timestamp</td>
</tr>
<tr>
<td>Group members</td>
<td>Role assignments</td>
<td>Database table with timestamp</td>
</tr>
</tbody>
</table>
<h4 id="543-synchronization-process">5.4.3. Synchronization Process</h4>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Scheduler as Sync Scheduler
    participant Delta as Delta Service
    participant Graph as Microsoft Graph API
    participant DB as Database

    Scheduler->>Delta: Initiate sync cycle
    Delta->>DB: Retrieve latest delta token
    Delta->>Graph: Request changes using delta token
    Graph->>Delta: Return changed entities
    Delta->>DB: Process and apply changes
    Delta->>DB: Store new delta token
    Note over Delta,DB: Repeat on schedule or event
</div></code></pre>
<h4 id="544-implementation-optimizations">5.4.4. Implementation Optimizations</h4>
<table>
<thead>
<tr>
<th>Optimization</th>
<th>Description</th>
<th>Benefit</th>
</tr>
</thead>
<tbody>
<tr>
<td>Background Processing</td>
<td>Execute delta sync in background workers</td>
<td>Improves application responsiveness</td>
</tr>
<tr>
<td>Selective Application</td>
<td>Apply only relevant changes</td>
<td>Reduces database load</td>
</tr>
<tr>
<td>Change Batching</td>
<td>Process changes in batches</td>
<td>Improves efficiency</td>
</tr>
<tr>
<td>Conflict Resolution</td>
<td>Implement resolution for conflicting changes</td>
<td>Maintains data integrity</td>
</tr>
<tr>
<td>Webhook Triggers</td>
<td>Trigger sync on directory change notifications</td>
<td>Ensures near real-time updates</td>
</tr>
</tbody>
</table>
<h4 id="545-error-handling-and-resilience">5.4.5. Error Handling and Resilience</h4>
<table>
<thead>
<tr>
<th>Error Scenario</th>
<th>Handling Strategy</th>
</tr>
</thead>
<tbody>
<tr>
<td>Delta token expired</td>
<td>Fall back to full synchronization</td>
</tr>
<tr>
<td>Temporary API unavailability</td>
<td>Implement exponential backoff retry</td>
</tr>
<tr>
<td>Rate limiting</td>
<td>Adaptive throttling with prioritization</td>
</tr>
<tr>
<td>Conflicting changes</td>
<td>Apply business rules for resolution</td>
</tr>
<tr>
<td>Service degradation</td>
<td>Circuit breaker pattern to prevent cascading failures</td>
</tr>
</tbody>
</table>
<h2 id="6-data-model">6. Data Model</h2>
<p>This section defines the data model for the authentication, authorization, and delegation microservice. The model is designed for simplicity, performance, and maintainability while supporting all required functionality.</p>
<h3 id="61-core-entities">6.1. Core Entities</h3>
<p>The data model consists of essential entities that support the integrated authentication, authorization, and delegation framework:</p>
<p>The data model consists of the following core entities and their relationships:</p>
<ol>
<li>
<p><strong>User</strong>: Represents an authenticated user in the system with attributes synchronized from Microsoft Entra ID.</p>
<ul>
<li>Key attributes: id, email, displayName, givenName, surname, jobTitle, department, employeeId, officeLocation, lastSyncTime, isActive</li>
</ul>
</li>
<li>
<p><strong>Role</strong>: Defines a set of permissions and position in the organizational hierarchy.</p>
<ul>
<li>Key attributes: id, name, description, hierarchyLevel, parentRoles, childRoles, isSystem</li>
</ul>
</li>
<li>
<p><strong>Permission</strong>: Represents a specific action that can be performed on a resource.</p>
<ul>
<li>Key attributes: id, resource, action, description</li>
</ul>
</li>
<li>
<p><strong>Profile</strong>: A container for role assignments that provides context for a user's permissions.</p>
<ul>
<li>Key attributes: id, userId, name, description, type, isActive, validFrom, validUntil, metadata</li>
</ul>
</li>
<li>
<p><strong>RoleAssignment</strong>: Links profiles to roles with specific restrictions and source information.</p>
<ul>
<li>Key attributes: id, profileId, roleId, source, delegationId, restrictions, assignedAt</li>
</ul>
</li>
<li>
<p><strong>Delegation</strong>: Records the temporary transfer of authority from one user to another.</p>
<ul>
<li>Key attributes: id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil, restrictions, reason, approverIds, approvalStatus, createdAt, lastModifiedAt</li>
</ul>
</li>
</ol>
<p>Key entity relationships include:</p>
<ul>
<li>One User can have multiple Profiles</li>
<li>Each Profile can contain multiple RoleAssignments</li>
<li>Each RoleAssignment links to exactly one Role</li>
<li>Roles can have parent-child relationships with other Roles</li>
<li>Roles grant multiple Permissions</li>
<li>Delegations connect a delegator User to a delegate User</li>
<li>Each Delegation creates exactly one Profile</li>
</ul>
<p>The model also includes several enumeration types:</p>
<ul>
<li>ProfileType (DIRECT, DELEGATED, SYSTEM)</li>
<li>RoleSource (DIRECT_ASSIGNMENT, AZURE_AD_GROUP, DELEGATION, SYSTEM)</li>
<li>DelegationStatus (PENDING, ACTIVE, EXPIRED, REVOKED)</li>
<li>DelegationApprovalStatus (PENDING, APPROVED, REJECTED)</li>
</ul>
<h3 id="62-database-tables">6.2. Database Tables</h3>
<p>The microservice uses the following database tables to store authentication, authorization, and delegation data:</p>
<table>
<thead>
<tr>
<th>Table Name</th>
<th>Primary Purpose</th>
<th>Key Fields</th>
</tr>
</thead>
<tbody>
<tr>
<td>Users</td>
<td>Store user information</td>
<td>id, email, displayName, givenName, surname, jobTitle, department, employeeId, lastSyncTime</td>
</tr>
<tr>
<td>Roles</td>
<td>Define available roles</td>
<td>id, name, description, hierarchyLevel, parentRoles, childRoles, isSystem</td>
</tr>
<tr>
<td>Permissions</td>
<td>Define available permissions</td>
<td>id, resource, action, description</td>
</tr>
<tr>
<td>RolePermissions</td>
<td>Map roles to permissions</td>
<td>roleId, permissionId</td>
</tr>
<tr>
<td>Profiles</td>
<td>Store user profiles</td>
<td>id, userId, name, description, type, isActive, validFrom, validUntil, metadata</td>
</tr>
<tr>
<td>RoleAssignments</td>
<td>Assign roles to profiles</td>
<td>id, profileId, roleId, source, delegationId, restrictions, assignedAt</td>
</tr>
<tr>
<td>Delegations</td>
<td>Track delegations</td>
<td>id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil, restrictions, reason, approverIds, approvalStatus, createdAt, lastModifiedAt</td>
</tr>
</tbody>
</table>
<h4 id="table-schemas">Table Schemas</h4>
<p><strong>Users Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> <span class="hljs-keyword">users</span> (
    <span class="hljs-keyword">id</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    email <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span> <span class="hljs-keyword">UNIQUE</span>,
    display_name <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    given_name <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>),
    surname <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>),
    job_title <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>),
    department <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>),
    employee_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">50</span>),
    office_location <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>),
    last_sync_time <span class="hljs-built_in">TIMESTAMP</span>,
    is_active <span class="hljs-built_in">BOOLEAN</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-literal">TRUE</span>,
    created_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    updated_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>
);
</div></code></pre>
<p><strong>Roles Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> <span class="hljs-keyword">roles</span> (
    <span class="hljs-keyword">id</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    <span class="hljs-keyword">name</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">100</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span> <span class="hljs-keyword">UNIQUE</span>,
    description <span class="hljs-built_in">TEXT</span>,
    hierarchy_level <span class="hljs-built_in">INT</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    parent_roles <span class="hljs-keyword">JSON</span>,
    child_roles <span class="hljs-keyword">JSON</span>,
    is_system <span class="hljs-built_in">BOOLEAN</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-literal">FALSE</span>,
    created_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    updated_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>
);
</div></code></pre>
<p><strong>Permissions Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> permissions (
    <span class="hljs-keyword">id</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    <span class="hljs-keyword">resource</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">100</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    <span class="hljs-keyword">action</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">100</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    description <span class="hljs-built_in">TEXT</span>,
    created_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    <span class="hljs-keyword">UNIQUE</span>(<span class="hljs-keyword">resource</span>, <span class="hljs-keyword">action</span>)
);
</div></code></pre>
<p><strong>RolePermissions Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> role_permissions (
    role_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    permission_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    created_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    PRIMARY <span class="hljs-keyword">KEY</span> (role_id, permission_id),
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (role_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">roles</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (permission_id) <span class="hljs-keyword">REFERENCES</span> permissions(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>
);
</div></code></pre>
<p><strong>Profiles Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> <span class="hljs-keyword">profiles</span> (
    <span class="hljs-keyword">id</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    user_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    <span class="hljs-keyword">name</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    description <span class="hljs-built_in">TEXT</span>,
    <span class="hljs-keyword">type</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>, <span class="hljs-comment">-- DIRECT, DELEGATED, SYSTEM</span>
    is_active <span class="hljs-built_in">BOOLEAN</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-literal">FALSE</span>,
    valid_from <span class="hljs-built_in">TIMESTAMP</span>,
    valid_until <span class="hljs-built_in">TIMESTAMP</span>,
    metadata <span class="hljs-keyword">JSON</span>,
    created_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    updated_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (user_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">users</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>
);
</div></code></pre>
<p><strong>RoleAssignments Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> role_assignments (
    <span class="hljs-keyword">id</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    profile_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    role_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    <span class="hljs-keyword">source</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>, <span class="hljs-comment">-- DIRECT_ASSIGNMENT, AZURE_AD_GROUP, DELEGATION, SYSTEM</span>
    delegation_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>),
    restrictions <span class="hljs-keyword">JSON</span>,
    assigned_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (profile_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">profiles</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (role_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">roles</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (delegation_id) <span class="hljs-keyword">REFERENCES</span> delegations(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">SET</span> <span class="hljs-literal">NULL</span>
);
</div></code></pre>
<p><strong>Delegations Table</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> delegations (
    <span class="hljs-keyword">id</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    delegator_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    delegate_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    role_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    profile_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>),
    <span class="hljs-keyword">status</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>, <span class="hljs-comment">-- PENDING, ACTIVE, EXPIRED, REVOKED</span>
    valid_from <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    valid_until <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    restrictions <span class="hljs-keyword">JSON</span>,
    reason <span class="hljs-built_in">TEXT</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    approver_ids <span class="hljs-keyword">JSON</span>,
    approval_status <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-keyword">DEFAULT</span> <span class="hljs-string">'PENDING'</span>, <span class="hljs-comment">-- PENDING, APPROVED, REJECTED</span>
    created_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    last_modified_at <span class="hljs-built_in">TIMESTAMP</span> <span class="hljs-keyword">DEFAULT</span> <span class="hljs-keyword">CURRENT_TIMESTAMP</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (delegator_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">users</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (delegate_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">users</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (role_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">roles</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">CASCADE</span>,
    <span class="hljs-keyword">FOREIGN</span> <span class="hljs-keyword">KEY</span> (profile_id) <span class="hljs-keyword">REFERENCES</span> <span class="hljs-keyword">profiles</span>(<span class="hljs-keyword">id</span>) <span class="hljs-keyword">ON</span> <span class="hljs-keyword">DELETE</span> <span class="hljs-keyword">SET</span> <span class="hljs-literal">NULL</span>
);
</div></code></pre>
<h3 id="63-key-relationships">6.3. Key Relationships</h3>
<p>The data model is structured around these core relationships:</p>
<ol>
<li>
<p><strong>User → Profiles</strong>: One user can have multiple profiles (direct, delegated, or system)</p>
<ul>
<li>Enables context switching between different role sets</li>
<li>Supports multiple delegated profiles from different delegators</li>
</ul>
</li>
<li>
<p><strong>Profile → RoleAssignments</strong>: Each profile contains multiple role assignments</p>
<ul>
<li>Clearly separates roles in different contexts</li>
<li>Supports different sources for role assignments (direct, Azure AD group-derived, delegation)</li>
</ul>
</li>
<li>
<p><strong>RoleAssignment → Role</strong>: Role assignments link profiles to specific roles</p>
<ul>
<li>Includes source tracking for auditing</li>
<li>Contains scope and restriction information</li>
</ul>
</li>
<li>
<p><strong>Role → Permissions</strong>: Roles aggregate multiple permissions</p>
<ul>
<li>Many-to-many relationship via RolePermissions table</li>
<li>Enables flexible permission management</li>
</ul>
</li>
<li>
<p><strong>Role → Role</strong>: Roles maintain hierarchical relationships</p>
<ul>
<li>Parent-child relationships stored as JSON arrays</li>
<li>Supports organizational hierarchy alignment</li>
</ul>
</li>
<li>
<p><strong>Delegation → User</strong>: Tracks both delegator and delegate</p>
<ul>
<li>Records the user who delegated authority</li>
<li>Records the user receiving delegated authority</li>
</ul>
</li>
<li>
<p><strong>Delegation → Profile</strong>: Delegations create delegated profiles</p>
<ul>
<li>One-to-one relationship between delegation and profile</li>
<li>Profile inherits restrictions from delegation</li>
</ul>
</li>
</ol>
<p><strong>Microservice Data Flow</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    A[Authentication\nRequest] --> B[JWT\nToken Service]
    B --> C{User\nExists?}
    C -->|No| D[Create\nUser]
    C -->|Yes| E[Get User\nProfiles]
    D --> E
    E --> F[Load Active\nProfile]
    F --> G[Include Role\nAssignments]
    G --> H[Generate\nJWT]
    H --> I[Response with\nToken]

    J[Delegation\nRequest] --> K[Delegation\nService]
    K --> L{Validate\nPermissions}
    L -->|Invalid| M[Reject\nRequest]
    L -->|Valid| N[Create\nDelegation]
    N --> O[Create Delegated\nProfile]
    O --> P[Add Role\nAssignments]
    P --> Q[Notify\nDelegate]
</div></code></pre>
<p><strong>Group-to-Role Mapping (in Configuration)</strong></p>
<p>Rather than storing Azure AD group mappings in the database, the microservice uses a configuration file that maps Azure AD groups to application roles:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"groupMappings"</span>: [
    {
      <span class="hljs-attr">"azureAdGroupId"</span>: <span class="hljs-string">"a1b2c3d4-e5f6-7890-abcd-ef1234567890"</span>,
      <span class="hljs-attr">"displayName"</span>: <span class="hljs-string">"Plant Managers"</span>,
      <span class="hljs-attr">"applicationRole"</span>: <span class="hljs-string">"PLANT_MANAGER"</span>,
      <span class="hljs-attr">"autoAssign"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"scopeAttributes"</span>: [<span class="hljs-string">"site"</span>]
    },
    {
      <span class="hljs-attr">"azureAdGroupId"</span>: <span class="hljs-string">"b2c3d4e5-f6a7-8901-bcde-f12345678901"</span>,
      <span class="hljs-attr">"displayName"</span>: <span class="hljs-string">"Quality Inspectors"</span>,
      <span class="hljs-attr">"applicationRole"</span>: <span class="hljs-string">"QUALITY_INSPECTOR"</span>,
      <span class="hljs-attr">"autoAssign"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"scopeAttributes"</span>: [<span class="hljs-string">"department"</span>, <span class="hljs-string">"site"</span>]
    }
  ]
}
</div></code></pre>
<p>This configuration is loaded when the microservice starts and is used during user synchronization with Microsoft Graph API to assign appropriate roles based on Azure AD group memberships.</p>
<h2 id="7-conclusion">7. Conclusion</h2>
<p>This Authentication, Authorization, and Delegation Guide provides a comprehensive framework for implementing a secure, scalable, and flexible identity and access management solution for enterprise applications. By integrating SAML-based Single Sign-On with JWT authentication and Microsoft Graph API synchronization, the system delivers a robust foundation for modern applications with complex organizational structures.</p>
<h3 id="71-key-architecture-benefits">7.1. Key Architecture Benefits</h3>
<p>The integrated approach described in this document offers several significant advantages:</p>
<ol>
<li>
<p><strong>Seamless User Experience</strong></p>
<ul>
<li>Single authentication event with persistent session management</li>
<li>Profile-based context switching without re-authentication</li>
<li>Reduced authentication redirects and interruptions</li>
</ul>
</li>
<li>
<p><strong>Enterprise-Grade Security</strong></p>
<ul>
<li>Centralized identity management through Microsoft Entra ID</li>
<li>Secure token handling with appropriate storage strategies</li>
<li>Comprehensive certificate and key management</li>
<li>Fine-grained permission control with role-based access</li>
</ul>
</li>
<li>
<p><strong>Operational Flexibility</strong></p>
<ul>
<li>Just-In-Time user provisioning from authoritative source</li>
<li>Real-time synchronization of user attributes and group memberships</li>
<li>Delegation framework supporting business continuity</li>
<li>Support for complex organizational hierarchies</li>
</ul>
</li>
<li>
<p><strong>Reduced Administrative Overhead</strong></p>
<ul>
<li>Automated user provisioning and deprovisioning</li>
<li>Group-based role assignment through Azure AD</li>
<li>Self-service delegation with appropriate controls</li>
<li>Streamlined profile and role management</li>
</ul>
</li>
<li>
<p><strong>Technical Resilience</strong></p>
<ul>
<li>Stateless architecture supporting horizontal scaling</li>
<li>Fault tolerance through token-based authentication</li>
<li>Optimized Graph API integration with delta queries</li>
<li>Comprehensive error handling and recovery strategies</li>
</ul>
</li>
</ol>
<h3 id="72-implementation-strategy">7.2. Implementation Strategy</h3>
<p>To successfully implement this architecture, we recommend a phased approach:</p>
<ol>
<li>
<p><strong>Foundation Phase</strong></p>
<ul>
<li>Configure Azure AD for SAML authentication</li>
<li>Implement JWT token service with refresh capabilities</li>
<li>Establish basic role and permission framework</li>
<li>Create core database schema</li>
</ul>
</li>
<li>
<p><strong>Integration Phase</strong></p>
<ul>
<li>Implement Microsoft Graph API synchronization</li>
<li>Develop group-to-role mapping functionality</li>
<li>Build profile management capabilities</li>
<li>Create basic authorization middleware</li>
</ul>
</li>
<li>
<p><strong>Advanced Features Phase</strong></p>
<ul>
<li>Implement delegation framework</li>
<li>Add profile switching functionality</li>
<li>Develop hierarchical role management</li>
<li>Create audit and monitoring systems</li>
</ul>
</li>
<li>
<p><strong>Optimization Phase</strong></p>
<ul>
<li>Implement performance optimizations</li>
<li>Enhance security features</li>
<li>Add advanced error handling</li>
<li>Create comprehensive monitoring and alerting</li>
</ul>
</li>
</ol>
<h3 id="73-success-criteria">7.3. Success Criteria</h3>
<p>The implementation will be considered successful when it achieves:</p>
<ol>
<li>
<p><strong>Security Objectives</strong></p>
<ul>
<li>Zero unauthorized access events</li>
<li>Complete audit trail of all authentication and authorization events</li>
<li>Secure handling of all credentials and tokens</li>
</ul>
</li>
<li>
<p><strong>Performance Targets</strong></p>
<ul>
<li>Authentication response time under 500ms</li>
<li>Token refresh operations under 200ms</li>
<li>Authorization checks under 50ms</li>
<li>Profile switching under 300ms</li>
</ul>
</li>
<li>
<p><strong>User Experience Goals</strong></p>
<ul>
<li>Seamless authentication flow</li>
<li>Intuitive delegation interface</li>
<li>Simple profile management</li>
<li>Transparent authorization behavior</li>
</ul>
</li>
<li>
<p><strong>Business Outcomes</strong></p>
<ul>
<li>Reduced administrative overhead</li>
<li>Improved security posture</li>
<li>Enhanced operational flexibility</li>
<li>Simplified compliance management</li>
</ul>
</li>
</ol>
<p>By following this guide, organizations can implement a modern, secure, and user-friendly authentication and authorization system that meets the complex needs of enterprise applications while maintaining seamless integration with Microsoft identity platforms.</p>

</body>
</html>
