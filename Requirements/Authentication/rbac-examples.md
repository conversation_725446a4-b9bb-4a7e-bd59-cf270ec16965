# RBAC Usage Examples

## 1. Basic User and Profile Setup

### 1.1 User with Direct Profile

```typescript
// Basic user setup
const teamLeader = {
  id: "user123",
  email: "<EMAIL>",
  matriculationNumber: "EMP456",
  siteId: "SITE_A",
  status: "ACTIVE",
  authMethod: "AZURE_AD",
};

// User's default profile with direct roles
const teamLeaderProfile = {
  id: "profile123",
  userId: "user123",
  name: "Team Leader Default Profile",
  type: "DIRECT",
  isActive: true,
  roles: [
    {
      roleId: "TEAM_LEADER",
      source: "ASSIGNED",
      validFrom: "2024-01-01",
      permissions: ["team:manage", "schedule:view", "training:assign"],
    },
    {
      roleId: "TRAINER",
      source: "ASSIGNED",
      validFrom: "2024-02-01",
      validUntil: "2024-12-31",
      permissions: ["training:conduct", "training:evaluate"],
    },
  ],
  metadata: {
    department: "Production",
    shift: "Morning",
  },
};
```

## 2. Delegation Scenarios

### 2.1 Shift Leader Delegating to Team Leader

```typescript
// Original Shift Leader Profile
const shiftLeaderProfile = {
  id: "profile789",
  userId: "user789",
  name: "Shift Leader Default Profile",
  type: "DIRECT",
  isActive: true,
  roles: [
    {
      roleId: "SHIFT_LEADER",
      source: "ASSIGNED",
      permissions: [
        "shift:manage",
        "incidents:handle",
        "schedule:approve",
        "performance:review",
        "budget:manage",
      ],
    },
  ],
};

// Create Delegation Request (Profile will be created automatically)
const delegationRequest = {
  delegatorId: "user789", // Shift Leader
  delegateId: "user123", // Team Leader
  roleId: "SHIFT_LEADER",
  validFrom: "2024-03-20T00:00:00Z",
  validUntil: "2024-03-27T23:59:59Z",
  profileName: "Shift Leader Coverage Profile", // Optional: custom name for auto-created profile
  restrictions: {
    scope: {
      teams: ["TEAM_A", "TEAM_B"],
      shifts: ["MORNING_SHIFT"],
    },
    operations: {
      included: ["shift:manage", "incidents:handle", "schedule:approve"],
      excluded: ["budget:manage", "performance:review"],
    },
    limits: {
      maxDailyOperations: 20,
      maxConcurrentSessions: 1,
    },
  },
  metadata: {
    purpose: "Shift Leader Coverage - Week 12",
    delegatedBy: "user789",
  },
};

// System Response - Automatically Created Profile and Active Delegation
const systemResponse = {
  delegation: {
    id: "delegation123",
    delegatorId: "user789",
    delegateId: "user123",
    roleId: "SHIFT_LEADER",
    profileId: "profile456",
    status: "ACTIVE",
    validFrom: "2024-03-20T00:00:00Z",
    validUntil: "2024-03-27T23:59:59Z",
    restrictions: delegationRequest.restrictions,
    metadata: delegationRequest.metadata,
  },
  profile: {
    id: "profile456",
    userId: "user123",
    name: "Shift Leader Coverage Profile",
    type: "DELEGATED",
    isActive: true,
    validFrom: "2024-03-20T00:00:00Z",
    validUntil: "2024-03-27T23:59:59Z",
    roles: [
      {
        roleId: "SHIFT_LEADER",
        source: "DELEGATED",
        delegationId: "delegation123",
        restrictions: delegationRequest.restrictions,
      },
    ],
    metadata: delegationRequest.metadata,
  },
};
```

### 2.2 Multiple Profiles Example

```typescript
// User's Available Profiles
const userProfiles = {
  userId: "user123",
  profiles: [
    {
      id: "profile123",
      name: "Default Profile",
      type: "DIRECT",
      active: true,
      roles: [
        {
          roleId: "TEAM_LEADER",
          source: "ASSIGNED",
        },
        {
          roleId: "TRAINER",
          source: "ASSIGNED",
          validUntil: "2024-12-31",
        },
      ],
    },
    {
      id: "profile456",
      name: "Shift Leader Coverage Profile",
      type: "DELEGATED",
      active: false,
      validFrom: "2024-03-20T00:00:00Z",
      validUntil: "2024-03-27T23:59:59Z",
      roles: [
        {
          roleId: "SHIFT_LEADER",
          source: "DELEGATED",
          delegationId: "delegation123",
          restrictions: {
            scope: {
              teams: ["TEAM_A", "TEAM_B"],
              shifts: ["MORNING_SHIFT"],
            },
            operations: {
              included: [
                "shift:manage",
                "incidents:handle",
                "schedule:approve",
              ],
              excluded: ["budget:manage", "performance:review"],
            },
          },
        },
      ],
    },
  ],
};
```

## 3. Permission Validation Examples

### 3.1 Profile-Based Permission Check

```typescript
// Permission check with default profile
const canManageTeam = await rbacService.validatePermission({
  userId: "user123",
  profileId: "profile123", // Default profile
  permission: "team:manage",
  context: {
    teamId: "TEAM_A",
    action: "update_schedule",
  },
});

// Permission check with delegated profile
const canApproveSchedule = await rbacService.validatePermission({
  userId: "user123",
  profileId: "profile456", // Delegated profile
  permission: "schedule:approve",
  context: {
    teamId: "TEAM_A",
    shiftId: "MORNING_SHIFT",
    action: "approve_next_week",
  },
});
```

## 4. API Usage Examples

### 4.1 Creating a Delegation with Automatic Profile Creation

```typescript
// Create delegation request (profile will be created automatically)
POST /api/rbac/delegations
{
  "delegateId": "user123",
  "roleId": "SHIFT_LEADER",
  "validFrom": "2024-03-20T00:00:00Z",
  "validUntil": "2024-03-27T23:59:59Z",
  "profileName": "Shift Leader Coverage Profile", // Optional
  "restrictions": {
    "scope": {
      "teams": ["TEAM_A", "TEAM_B"],
      "shifts": ["MORNING_SHIFT"]
    },
    "operations": {
      "included": [
        "shift:manage",
        "incidents:handle",
        "schedule:approve"
      ],
      "excluded": [
        "budget:manage",
        "performance:review"
      ]
    },
    "limits": {
      "maxDailyOperations": 20,
      "maxConcurrentSessions": 1
    }
  },
  "metadata": {
    "purpose": "Shift Leader Coverage - Week 12"
  }
}

// System Response (Immediate Creation and Activation)
{
  "delegation": {
    "id": "delegation123",
    "status": "ACTIVE",
    "profileId": "profile456",
    // ... other delegation fields
  },
  "profile": {
    "id": "profile456",
    "type": "DELEGATED",
    "name": "Shift Leader Coverage Profile",
    "isActive": true,
    // ... other profile fields
  }
}
```

### 4.2 Switching Between Profiles

```typescript
// API request to switch to a different profile
PUT /api/rbac/users/user123/profiles/profile456/activate
{
  "reason": "Taking over shift leader responsibilities",
  "metadata": {
    "switchedAt": "2024-03-20T08:00:00Z",
    "context": "Shift Leader Coverage"
  }
}
```

### 4.3 Revoking a Delegation

```typescript
// API request to revoke delegation
PUT /api/rbac/delegations/delegation123/status
{
  "status": "REVOKED",
  "reason": "Early return of delegator"
}

// System automatically deactivates the associated profile
```

## 5. Common Scenarios and Solutions

### 5.1 Emergency Access Profile

```typescript
// Create emergency delegation with automatic profile creation
POST /api/rbac/delegations/emergency
{
  "delegateId": "user234",
  "roleId": "TEAM_LEADER",
  "validFrom": new Date().toISOString(),
  "validUntil": new Date(Date.now() + 8 * 3600000).toISOString(), // 8 hours
  "profileName": "Emergency Response Profile",
  "restrictions": {
    "scope": {
      "sites": ["SITE_A"],
      "teams": ["PROD_LINE_X"],
      "shifts": ["NIGHT"]
    },
    "operations": {
      "included": [
        "team:manage",
        "incidents:handle",
        "equipment:control",
        "safety:override"
      ],
      "excluded": []
    },
    "limits": {
      "maxDailyOperations": 100,
      "maxConcurrentSessions": 1
    }
  },
  "metadata": {
    "emergencyAccess": true,
    "incident": "PROD_LINE_X_SHUTDOWN",
    "priority": "CRITICAL",
    "emergencyReason": "Production line critical failure",
    "postIncidentReviewRequired": true
  }
}

// System Response - Immediate Emergency Access Grant
{
  "delegation": {
    "id": "emergency_delegation_001",
    "status": "ACTIVE",
    "profileId": "profile_emergency_001",
    // ... other delegation fields
  },
  "profile": {
    "id": "profile_emergency_001",
    "type": "DELEGATED",
    "name": "Emergency Response Profile",
    "isActive": true,
    "roles": [{
      "roleId": "TEAM_LEADER",
      "source": "DELEGATED",
      "delegationId": "emergency_delegation_001",
      "restrictions": {
        // ... restrictions from request
      }
    }],
    "metadata": {
      "emergencyAccess": true,
      "incident": "PROD_LINE_X_SHUTDOWN",
      "priority": "CRITICAL"
    }
  },
  "jwt": "eyJhbG..." // JWT with emergency profile context
}
```

### 5.2 Role Conflict Resolution

```typescript
// Check for profile role conflicts before creating delegation
const roleConflicts = await rbacService.checkProfileRoleConflicts({
  userId: "user123",
  roleId: "QUALITY_INSPECTOR",
  context: {
    siteId: "SITE_A",
    department: "PRODUCTION"
  }
});

// If no conflicts, create delegation with non-conflicting scope
POST /api/rbac/delegations
{
  "delegateId": "user123",
  "roleId": "QUALITY_INSPECTOR",
  "profileName": "Quality Inspector Profile",
  "validFrom": "2024-03-01T00:00:00Z",
  "validUntil": "2024-12-31T23:59:59Z",
  "restrictions": {
    "scope": {
      "teams": ["TEAM_C", "TEAM_D"],
      "shifts": ["NIGHT_SHIFT"]
    },
    "operations": {
      "included": ["quality:inspect", "quality:report"],
      "excluded": ["quality:approve"]
    }
  },
  "metadata": {
    "conflictResolution": "Separate team scope",
    "department": "PRODUCTION"
  }
}

// System Response - Profile Created with Non-Conflicting Scope
{
  "delegation": {
    "id": "delegation789",
    "status": "ACTIVE",
    "profileId": "profile789",
    // ... other delegation fields
  },
  "profile": {
    "id": "profile789",
    "type": "DELEGATED",
    "name": "Quality Inspector Profile",
    "isActive": true,
    "roles": [{
      "roleId": "QUALITY_INSPECTOR",
      "source": "DELEGATED",
      "delegationId": "delegation789",
      "restrictions": {
        // ... restrictions from request
      }
    }],
    "metadata": {
      "conflictResolution": "Separate team scope",
      "department": "PRODUCTION"
    }
  }
}
```

These examples demonstrate:

1. Immediate delegation activation
2. Automatic profile creation
3. Clear separation between direct and delegated profiles
4. Proper profile-delegation relationships
5. Comprehensive metadata for auditing
6. Profile switching capabilities
7. Role conflict prevention

## 6. Process Flow Diagrams

### 6.1 Profile Creation and Delegation Flow

```mermaid
sequenceDiagram
    participant D as Delegator
    participant RBAC as RBAC Service
    participant DB as Database
    participant Del as Delegate
    participant Audit as Audit Service

    D->>RBAC: Create Delegation Request
    activate RBAC
    RBAC->>RBAC: Validate Delegator's Rights
    RBAC->>DB: Check Role Hierarchy
    DB-->>RBAC: Confirm Hierarchy

    alt Valid Request
        RBAC->>DB: Create Active Delegation
        RBAC->>DB: Create Active Profile
        RBAC->>DB: Add Role to Profile
        RBAC->>Audit: Log Delegation Creation
        RBAC-->>D: Return Success Response
        RBAC->>Del: Send Notification
    else Invalid Request
        RBAC-->>D: Return Error
        RBAC->>Audit: Log Failed Attempt
    end
    deactivate RBAC
```

### 6.2 Profile Switching Flow

```mermaid
sequenceDiagram
    participant U as User
    participant RBAC as RBAC Service
    participant DB as Database
    participant Auth as Auth Service
    participant Audit as Audit Service

    U->>RBAC: Request Profile Switch
    activate RBAC

    RBAC->>DB: Get Profile Details
    DB-->>RBAC: Profile Data

    RBAC->>RBAC: Validate Profile Status
    RBAC->>DB: Check Active Delegations
    DB-->>RBAC: Delegation Status

    alt Valid Profile & Active Delegation
        RBAC->>DB: Deactivate Current Profile
        RBAC->>DB: Activate Requested Profile
        RBAC->>Auth: Update Session Context
        RBAC->>Audit: Log Profile Switch
        RBAC-->>U: Return New JWT with Profile Context
    else Invalid Request
        RBAC-->>U: Return Error
        RBAC->>Audit: Log Failed Switch
    end
    deactivate RBAC
```

### 6.3 Permission Validation Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant RBAC as RBAC Service
    participant Cache as Permission Cache
    participant DB as Database
    participant Audit as Audit Service

    C->>RBAC: Validate Permission Request
    activate RBAC

    RBAC->>DB: Get Active Profile
    DB-->>RBAC: Profile Data

    RBAC->>Cache: Check Cached Profile Permissions
    alt Cache Hit
        Cache-->>RBAC: Return Cached Result
    else Cache Miss
        RBAC->>DB: Get Profile Roles
        DB-->>RBAC: Role Data

        RBAC->>DB: Get Role Permissions
        DB-->>RBAC: Permission Data

        alt Has Delegated Roles
            RBAC->>DB: Get Delegation Restrictions
            DB-->>RBAC: Restriction Data
            RBAC->>RBAC: Apply Restrictions
        end

        RBAC->>Cache: Store Profile Permissions
    end

    RBAC->>RBAC: Evaluate Permission
    RBAC->>Audit: Log Access Attempt
    RBAC-->>C: Return Decision
    deactivate RBAC
```

### 6.4 Emergency Access Flow

```mermaid
sequenceDiagram
    participant R as Requestor
    participant RBAC as RBAC Service
    participant DB as Database
    participant Audit as Audit Service

    R->>RBAC: Request Emergency Access
    activate RBAC

    RBAC->>RBAC: Validate Emergency Criteria
    RBAC->>DB: Create Emergency Profile
    RBAC->>DB: Create Active Emergency Delegation
    RBAC->>DB: Add Emergency Role to Profile
    RBAC->>Audit: Log Emergency Access Grant
    RBAC-->>R: Return Emergency Profile & JWT

    deactivate RBAC
```

## 7. Complex Profile and Delegation Examples

### 7.1 Multi-Role Profile with Mixed Sources

```typescript
// Example of a profile with both assigned and delegated roles
const complexProfile = {
  id: "profile_complex_001",
  userId: "user345",
  name: "Production Specialist with Quality Oversight",
  type: "DIRECT",
  isActive: true,
  validFrom: "2024-01-01T00:00:00Z",
  validUntil: "2024-12-31T23:59:59Z",
  roles: [
    // Base assigned role
    {
      roleId: "DEVELOPMENT_SPECIALIST",
      source: "ASSIGNED",
      restrictions: {
        scope: {
          sites: ["SITE_B"],
          teams: ["PROD_LINE_X", "PROD_LINE_Y"]
        }
      }
    }
  ],
  metadata: {
    department: "Production",
    certifications: ["Quality Management", "Six Sigma"],
    projectAssignments: ["Process Optimization"]
  }
};

// Create quality oversight delegation with automatic profile
POST /api/rbac/delegations
{
  "delegateId": "user345",
  "roleId": "QUALITY_INSPECTOR",
  "validFrom": "2024-03-01T00:00:00Z",
  "validUntil": "2024-05-31T23:59:59Z",
  "profileName": "Quality Inspector Coverage",
  "restrictions": {
    "scope": {
      "teams": ["PROD_LINE_X"],
      "shifts": ["MORNING", "AFTERNOON"]
    },
    "operations": {
      "included": ["quality:inspect", "quality:report"],
      "excluded": ["quality:approve"]
    },
    "limits": {
      "maxDailyOperations": 10,
      "maxConcurrentSessions": 1
    }
  },
  "metadata": {
    "projectId": "QI_2024_Q1",
    "priority": "HIGH",
    "purpose": "Quality oversight for Q1 improvement project"
  }
}

// System Response - Automatically Created Profile and Active Delegation
{
  "delegation": {
    "id": "delegation_qi_123",
    "status": "ACTIVE",
    "profileId": "profile_quality_002",
    // ... other delegation fields
  },
  "profile": {
    "id": "profile_quality_002",
    "type": "DELEGATED",
    "name": "Quality Inspector Coverage",
    "isActive": true,
    "roles": [{
      "roleId": "QUALITY_INSPECTOR",
      "source": "DELEGATED",
      "delegationId": "delegation_qi_123",
      "restrictions": {
        // ... restrictions from request
      }
    }],
    "metadata": {
      "projectId": "QI_2024_Q1",
      "priority": "HIGH",
      "purpose": "Quality oversight for Q1 improvement project"
    }
  }
}

// Switch to quality oversight profile when needed
PUT /api/rbac/users/user345/profiles/profile_quality_002/activate
{
  "reason": "Starting quality inspection tasks",
  "metadata": {
    "switchedAt": new Date().toISOString(),
    "projectPhase": "Quality Assessment"
  }
}
```
