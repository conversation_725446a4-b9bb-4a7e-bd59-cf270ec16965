# Hierarchical Roles Implementation Guide

## Document Information

**Version:** 2.0.0  
**Last Updated:** 2026-03-15  
**Status:** Approved

## Table of Contents

1. [Overview](#1-overview)
2. [Hierarchical Role Architecture](#2-hierarchical-role-architecture)
3. [Permission Framework](#3-permission-framework)
4. [Profile Implementation](#4-profile-implementation)
5. [Delegation Model](#5-delegation-model)
6. [JWT Implementation](#6-jwt-implementation)
7. [Authorization Rules](#7-authorization-rules)
8. [Practical Examples](#8-practical-examples)
9. [Implementation Checklist](#9-implementation-checklist)
10. [Troubleshooting](#10-troubleshooting)

## 1. Overview

This guide provides detailed implementation instructions for hierarchical roles within the Connected Workers platform. It serves as a practical companion to the authorization-delegation framework, focusing specifically on how to implement role hierarchies with clear permission boundaries, proper delegation flows, and effective scope management.

### Key Features of Hierarchical Implementation

- **Organizational Alignment**: Role structures that mirror your organizational hierarchy
- **Direct Permission Assignment**: Clear permission assignments based on role level
- **Contextual Delegation**: Delegation rules that respect organizational boundaries
- **Scope Enforcement**: Validation of scope restrictions across the hierarchy
- **Business Logic Integration**: Blueprint for integrating with specific business rules

### Implementation Benefits

- Reduced administrative overhead through level-based permission management
- Streamlined delegation processes that follow organizational structure
- Clear boundaries for authority delegation with proper validation
- Simplified configuration through hierarchical templates
- Enhanced security through consistent hierarchy-based validation rules

## 2. Hierarchical Role Architecture

### 2.1 Role Hierarchy Visualization

```mermaid
graph TD
    SA[SUPER_ADMIN] --> PM[PLANT_MANAGER]
    PM --> SL[SHIFT_LEADER]
    SL --> TL[TEAM_LEADER]
    TL --> OP[OPERATOR]
    SA --> TR[TRAINING_RESPONSIBLE]
    TR --> T[TRAINER]
    SA --> QI[QUALITY_INSPECTOR]
    SA --> MT[MAINTENANCE_TECH]
```

### 2.2 Role Definition with Hierarchical Attributes

```json
{
  "role": {
    "id": "SHIFT_LEADER",
    "name": "Shift Leader",
    "description": "Manages production shift and team leaders",
    "hierarchyLevel": 3,
    "parentRoles": ["PLANT_MANAGER"],
    "childRoles": ["TEAM_LEADER"],
    "azureAdGroups": ["Shift_Leaders"],
    "delegationCapabilities": [
      "delegate:same:SHIFT_LEADER",
      "delegate:down:TEAM_LEADER",
      "delegate:manage:TEAM_LEADER"
    ],
    "scopeAttributes": ["site", "department", "shift"]
  }
}
```

### 2.3 Complete Role Hierarchy Configuration

```json
{
  "roleHierarchy": {
    "SUPER_ADMIN": {
      "hierarchyLevel": 1,
      "childRoles": [
        "PLANT_MANAGER",
        "TRAINING_RESPONSIBLE",
        "QUALITY_INSPECTOR",
        "MAINTENANCE_TECH"
      ]
    },
    "PLANT_MANAGER": {
      "hierarchyLevel": 2,
      "parentRoles": ["SUPER_ADMIN"],
      "childRoles": ["SHIFT_LEADER"]
    },
    "SHIFT_LEADER": {
      "hierarchyLevel": 3,
      "parentRoles": ["PLANT_MANAGER"],
      "childRoles": ["TEAM_LEADER"]
    },
    "TEAM_LEADER": {
      "hierarchyLevel": 4,
      "parentRoles": ["SHIFT_LEADER"],
      "childRoles": ["OPERATOR"]
    },
    "OPERATOR": {
      "hierarchyLevel": 5,
      "parentRoles": ["TEAM_LEADER"],
      "childRoles": []
    },
    "TRAINING_RESPONSIBLE": {
      "hierarchyLevel": 2,
      "parentRoles": ["SUPER_ADMIN"],
      "childRoles": ["TRAINER"]
    },
    "TRAINER": {
      "hierarchyLevel": 3,
      "parentRoles": ["TRAINING_RESPONSIBLE"],
      "childRoles": []
    },
    "QUALITY_INSPECTOR": {
      "hierarchyLevel": 2,
      "parentRoles": ["SUPER_ADMIN"],
      "childRoles": []
    },
    "MAINTENANCE_TECH": {
      "hierarchyLevel": 2,
      "parentRoles": ["SUPER_ADMIN"],
      "childRoles": []
    }
  }
}
```

### 2.4 Hierarchical Relationship Types

| Relationship Type | Description                     | Use Case                           |
| ----------------- | ------------------------------- | ---------------------------------- |
| Direct Supervisor | Immediate superior in hierarchy | Validation of delegation authority |
| Skip-Level        | Superior more than one level up | Emergency delegation scenarios     |
| Peer              | Same hierarchical level         | Same-level delegations             |
| Cross-Branch      | Different branches of hierarchy | Cross-functional delegations       |

## 3. Permission Framework

### 3.1 Permission Structure with Hierarchical Context

```json
{
  "permission": {
    "id": "shift:manage",
    "resource": "shift",
    "action": "manage",
    "description": "Manage shift schedules and assignments",
    "hierarchicalRestrictions": {
      "minLevel": 3,
      "maxLevel": 4,
      "restrictToOwnScope": true
    }
  }
}
```

### 3.2 Comprehensive Permission Matrix

| Permission                   | SUPER_ADMIN | PLANT_MANAGER | SHIFT_LEADER | TEAM_LEADER | OPERATOR |
| ---------------------------- | :---------: | :-----------: | :----------: | :---------: | :------: |
| **Viewing Permissions**      |             |               |              |             |          |
| team:view                    |     All     |      All      |  Own Shift   |  Own Team   |    -     |
| operator:view                |     All     |      All      |  Own Shift   |  Own Team   |   Self   |
| performance:view             |     All     |      All      |  Own Shift   |  Own Team   |   Self   |
| quality:view                 |     All     |      All      |  Own Shift   |  Own Team   | Own Work |
| **Management Permissions**   |             |               |              |             |          |
| team:manage                  |     All     |      All      |  Own Shift   |      -      |    -     |
| operator:assign              |     All     |      All      |  Own Shift   |  Own Team   |    -     |
| schedule:create              |     All     |      All      |  Own Shift   |  Own Team   |    -     |
| budget:manage                |     All     |      All      |      -       |      -      |    -     |
| **Delegate Permissions**     |             |               |              |             |          |
| delegate:same:SUPER_ADMIN    |      ✓      |       -       |      -       |      -      |    -     |
| delegate:same:PLANT_MANAGER  |      -      |       ✓       |      -       |      -      |    -     |
| delegate:same:SHIFT_LEADER   |      -      |       -       |      ✓       |      -      |    -     |
| delegate:same:TEAM_LEADER    |      -      |       -       |      -       |      ✓      |    -     |
| delegate:down:PLANT_MANAGER  |      ✓      |       -       |      -       |      -      |    -     |
| delegate:down:SHIFT_LEADER   |      -      |       ✓       |      -       |      -      |    -     |
| delegate:down:TEAM_LEADER    |      -      |       -       |      ✓       |      -      |    -     |
| delegate:manage:SHIFT_LEADER |      ✓      |       ✓       |      -       |      -      |    -     |
| delegate:manage:TEAM_LEADER  |      -      |       -       |      ✓       |      -      |    -     |

### 3.3 Role-Specific Permission Templates

```json
{
  "rolePermissionTemplates": {
    "SHIFT_LEADER": {
      "corePermissions": [
        "team:view",
        "team:manage",
        "operator:view",
        "operator:assign",
        "schedule:view",
        "schedule:create",
        "performance:view",
        "training:assign"
      ],
      "delegationPermissions": [
        "delegate:same:SHIFT_LEADER",
        "delegate:down:TEAM_LEADER",
        "delegate:manage:TEAM_LEADER"
      ],
      "scopedTo": ["site", "department", "shift"]
    }
  }
}
```

## 4. Profile Implementation

### 4.1 Hierarchical Profile Structure

```json
{
  "profile": {
    "id": "profile-sl-123",
    "userId": "<EMAIL>",
    "type": "DIRECT",
    "name": "Shift Leader - Day Shift",
    "isActive": true,
    "roles": [
      {
        "roleId": "SHIFT_LEADER",
        "source": "AZURE_AD",
        "scopeContext": {
          "site": "SITE_A",
          "department": "MANUFACTURING",
          "shift": "DAY_SHIFT"
        }
      }
    ],
    "hierarchyContext": {
      "level": 3,
      "supervisorId": "<EMAIL>",
      "supervisorRoleId": "PLANT_MANAGER",
      "directReports": [
        {
          "userId": "<EMAIL>",
          "roleId": "TEAM_LEADER"
        },
        {
          "userId": "<EMAIL>",
          "roleId": "TEAM_LEADER"
        }
      ]
    }
  }
}
```

### 4.2 Profile Examples for Each Role Level

#### Super Admin Profile

```json
{
  "profile": {
    "id": "profile-sa-123",
    "userId": "<EMAIL>",
    "type": "DIRECT",
    "name": "System Administrator",
    "isActive": true,
    "roles": [
      {
        "roleId": "SUPER_ADMIN",
        "source": "AZURE_AD",
        "scopeContext": {
          "system": "ALL"
        }
      }
    ],
    "hierarchyContext": {
      "level": 1,
      "directReports": [
        {
          "userId": "<EMAIL>",
          "roleId": "PLANT_MANAGER"
        },
        {
          "userId": "<EMAIL>",
          "roleId": "TRAINING_RESPONSIBLE"
        },
        {
          "userId": "<EMAIL>",
          "roleId": "QUALITY_INSPECTOR"
        }
      ]
    }
  }
}
```

#### Plant Manager Profile

```json
{
  "profile": {
    "id": "profile-pm-123",
    "userId": "<EMAIL>",
    "type": "DIRECT",
    "name": "Plant Manager - Site A",
    "isActive": true,
    "roles": [
      {
        "roleId": "PLANT_MANAGER",
        "source": "AZURE_AD",
        "scopeContext": {
          "site": "SITE_A"
        }
      }
    ],
    "hierarchyContext": {
      "level": 2,
      "supervisorId": "<EMAIL>",
      "supervisorRoleId": "SUPER_ADMIN",
      "directReports": [
        {
          "userId": "<EMAIL>",
          "roleId": "SHIFT_LEADER"
        }
      ]
    }
  }
}
```

#### Team Leader Profile

```json
{
  "profile": {
    "id": "profile-tl-123",
    "userId": "<EMAIL>",
    "type": "DIRECT",
    "name": "Team Leader - Assembly Team",
    "isActive": true,
    "roles": [
      {
        "roleId": "TEAM_LEADER",
        "source": "AZURE_AD",
        "scopeContext": {
          "site": "SITE_A",
          "department": "MANUFACTURING",
          "shift": "DAY_SHIFT",
          "team": "ASSEMBLY"
        }
      }
    ],
    "hierarchyContext": {
      "level": 4,
      "supervisorId": "<EMAIL>",
      "supervisorRoleId": "SHIFT_LEADER",
      "directReports": [
        {
          "userId": "<EMAIL>",
          "roleId": "OPERATOR"
        },
        {
          "userId": "<EMAIL>",
          "roleId": "OPERATOR"
        }
      ]
    }
  }
}
```

### 4.3 Delegated Profile with Hierarchical Context

```json
{
  "profile": {
    "id": "profile-sl-delegated-123",
    "userId": "<EMAIL>",
    "type": "DELEGATED",
    "name": "Delegated Shift Leader - Day Shift",
    "isActive": false,
    "validFrom": "2026-04-01T00:00:00Z",
    "validUntil": "2026-04-14T23:59:59Z",
    "roles": [
      {
        "roleId": "SHIFT_LEADER",
        "source": "DELEGATED",
        "delegationId": "delegation-123",
        "scopeContext": {
          "site": "SITE_A",
          "department": "MANUFACTURING",
          "shift": "DAY_SHIFT"
        },
        "restrictions": {
          "operations": {
            "included": ["team:manage", "schedule:create", "incidents:handle"],
            "excluded": ["budget:manage", "performance:review"]
          },
          "quantityLimits": {
            "maxDailyOperations": 20
          }
        }
      }
    ],
    "hierarchyContext": {
      "level": 3,
      "supervisorId": "<EMAIL>",
      "supervisorRoleId": "PLANT_MANAGER",
      "delegator": {
        "userId": "<EMAIL>",
        "roleId": "SHIFT_LEADER"
      },
      "temporaryDirectReports": [
        {
          "userId": "<EMAIL>",
          "roleId": "TEAM_LEADER"
        }
      ]
    },
    "metadata": {
      "purpose": "Vacation Coverage",
      "delegationReason": "Annual leave coverage"
    }
  }
}
```

## 5. Delegation Model

### 5.1 Hierarchical Delegation Rules Matrix

| Delegator Role | Delegate Role | Delegation Type | Permission Required         | Scope Validation                    |
| -------------- | ------------- | --------------- | --------------------------- | ----------------------------------- |
| SUPER_ADMIN    | SUPER_ADMIN   | Same-Level      | delegate:same:SUPER_ADMIN   | System-wide                         |
| SUPER_ADMIN    | PLANT_MANAGER | Downward        | delegate:down:PLANT_MANAGER | System-wide                         |
| PLANT_MANAGER  | PLANT_MANAGER | Same-Level      | delegate:same:PLANT_MANAGER | Site must match                     |
| PLANT_MANAGER  | SHIFT_LEADER  | Downward        | delegate:down:SHIFT_LEADER  | Site must match                     |
| SHIFT_LEADER   | SHIFT_LEADER  | Same-Level      | delegate:same:SHIFT_LEADER  | Site & shift must match             |
| SHIFT_LEADER   | TEAM_LEADER   | Downward        | delegate:down:TEAM_LEADER   | Site, shift & department must match |
| TEAM_LEADER    | TEAM_LEADER   | Same-Level      | delegate:same:TEAM_LEADER   | Site, shift & team must match       |

### 5.2 Same-Level Delegation Configuration

```json
{
  "sameLevelDelegation": {
    "SHIFT_LEADER": {
      "permissionRequired": "delegate:same:SHIFT_LEADER",
      "scopeValidation": ["site", "department"],
      "timeRestrictions": {
        "maxDuration": "30D",
        "requireApprovalAfter": "14D"
      },
      "operationalRestrictions": {
        "allowExclusions": ["budget:manage"]
      }
    }
  }
}
```

### 5.3 Downward Delegation Configuration

```json
{
  "downwardDelegation": {
    "PLANT_MANAGER_TO_SHIFT_LEADER": {
      "delegatorRole": "PLANT_MANAGER",
      "delegateRole": "SHIFT_LEADER",
      "permissionRequired": "delegate:down:SHIFT_LEADER",
      "scopeValidation": {
        "required": ["site"],
        "delegateInScope": true,
        "directHierarchicalRelationship": true
      },
      "timeRestrictions": {
        "maxDuration": "90D",
        "requireApprovalAfter": "30D"
      }
    },
    "SHIFT_LEADER_TO_TEAM_LEADER": {
      "delegatorRole": "SHIFT_LEADER",
      "delegateRole": "TEAM_LEADER",
      "permissionRequired": "delegate:down:TEAM_LEADER",
      "scopeValidation": {
        "required": ["site", "department", "shift"],
        "delegateInScope": true,
        "directHierarchicalRelationship": true
      },
      "timeRestrictions": {
        "maxDuration": "60D",
        "requireApprovalAfter": "14D"
      }
    }
  }
}
```

### 5.4 Delegation Management Configuration

```json
{
  "delegationManagement": {
    "SUPER_ADMIN_MANAGES_PLANT_MANAGERS": {
      "managerRole": "SUPER_ADMIN",
      "targetRole": "PLANT_MANAGER",
      "permissionRequired": "delegate:manage:PLANT_MANAGER",
      "validationRules": {
        "directSupervisorOnly": true,
        "targetsMustShareScope": ["site"],
        "requireExplicitReason": true,
        "maxDelegationDuration": "90D",
        "requireApprovalAfter": "30D",
        "allowedOperationExclusions": ["budget:approve", "performance:review"]
      }
    },
    "PLANT_MANAGER_MANAGES_SHIFT_LEADERS": {
      "managerRole": "PLANT_MANAGER",
      "targetRole": "SHIFT_LEADER",
      "permissionRequired": "delegate:manage:SHIFT_LEADER",
      "validationRules": {
        "directSupervisorOnly": true,
        "targetsMustShareScope": ["site"],
        "requireExplicitReason": true,
        "maxDelegationDuration": "60D",
        "requireApprovalAfter": "14D",
        "allowedOperationExclusions": ["budget:manage", "performance:review"]
      }
    },
    "SHIFT_LEADER_MANAGES_TEAM_LEADERS": {
      "managerRole": "SHIFT_LEADER",
      "targetRole": "TEAM_LEADER",
      "permissionRequired": "delegate:manage:TEAM_LEADER",
      "validationRules": {
        "directSupervisorOnly": true,
        "targetsMustShareScope": ["site", "department", "shift"],
        "requireExplicitReason": true,
        "maxDelegationDuration": "30D",
        "requireApprovalAfter": "7D",
        "allowedOperationExclusions": ["team:manage", "performance:review"]
      }
    }
  }
}
```

### 5.5 Real-World Delegation Request Examples

#### Example 1: Shift Leader to Shift Leader (Same-Level)

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "SHIFT_LEADER",
    "validFrom": "2026-04-01T00:00:00Z",
    "validUntil": "2026-04-14T23:59:59Z",
    "profileName": "Vacation Coverage - Day Shift Leader",
    "reason": "Annual leave coverage",
    "scope": {
      "site": "SITE_A",
      "department": "MANUFACTURING",
      "shift": "DAY_SHIFT"
    },
    "restrictions": {
      "operations": {
        "included": ["team:manage", "schedule:create", "incidents:handle"],
        "excluded": ["budget:manage", "performance:review"]
      },
      "quantityLimits": {
        "maxDailyOperations": 20
      }
    }
  }
}
```

#### Example 2: Plant Manager to Shift Leader (Downward)

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "PLANT_MANAGER",
    "validFrom": "2026-04-15T00:00:00Z",
    "validUntil": "2026-04-30T23:59:59Z",
    "profileName": "Acting Plant Manager",
    "reason": "Leadership development opportunity",
    "scope": {
      "site": "SITE_A"
    },
    "restrictions": {
      "operations": {
        "included": ["team:manage", "schedule:approve", "incidents:handle"],
        "excluded": ["budget:approve", "performance:review"]
      },
      "departments": ["MANUFACTURING", "LOGISTICS"]
    }
  }
}
```

#### Example 3: Shift Leader Managing Team Leader Delegations

```json
{
  "managedDelegationRequest": {
    "managerId": "<EMAIL>",
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2026-04-03T00:00:00Z",
    "validUntil": "2026-04-05T23:59:59Z",
    "profileName": "Weekend Team Leader Coverage",
    "reason": "Weekend shift coverage",
    "scope": {
      "site": "SITE_A",
      "department": "MANUFACTURING",
      "shift": "DAY_SHIFT",
      "team": "ASSEMBLY"
    },
    "restrictions": {
      "operations": {
        "included": ["operator:assign", "schedule:view", "incidents:report"],
        "excluded": ["team:manage", "performance:review"]
      }
    }
  }
}
```

## 6. JWT Implementation

### 6.1 JWT Structure with Hierarchical Context

```json
{
  "sub": "<EMAIL>",
  "name": "John Doe",
  "email": "<EMAIL>",
  "roles": ["SHIFT_LEADER"],
  "permissions": [
    "team:view",
    "team:manage",
    "operator:view",
    "operator:assign",
    "schedule:view",
    "schedule:create",
    "performance:view",
    "training:assign",
    "delegate:same:SHIFT_LEADER",
    "delegate:down:TEAM_LEADER",
    "delegate:manage:TEAM_LEADER"
  ],
  "activeProfileId": "profile-sl-123",
  "profileType": "DIRECT",
  "hierarchyContext": {
    "level": 3,
    "supervisorId": "<EMAIL>",
    "supervisorRole": "PLANT_MANAGER",
    "directReports": ["<EMAIL>", "<EMAIL>"]
  },
  "scope": {
    "site": "SITE_A",
    "department": "MANUFACTURING",
    "shift": "DAY_SHIFT"
  },
  "iat": 1675091348,
  "exp": 1675094948
}
```

### 6.2 Delegated JWT Example

```json
{
  "sub": "<EMAIL>",
  "name": "Mark Johnson",
  "email": "<EMAIL>",
  "roles": ["SHIFT_LEADER"],
  "permissions": [
    "team:view",
    "team:manage",
    "operator:view",
    "operator:assign",
    "schedule:view",
    "schedule:create",
    "performance:view",
    "training:assign",
    "delegate:same:SHIFT_LEADER"
  ],
  "activeProfileId": "profile-sl-delegated-123",
  "profileType": "DELEGATED",
  "delegationId": "delegation-123",
  "delegatedBy": "<EMAIL>",
  "delegatedUntil": "2026-04-14T23:59:59Z",
  "hierarchyContext": {
    "level": 3,
    "supervisorId": "<EMAIL>",
    "supervisorRole": "PLANT_MANAGER",
    "temporaryDirectReports": ["<EMAIL>"]
  },
  "scope": {
    "site": "SITE_A",
    "department": "MANUFACTURING",
    "shift": "DAY_SHIFT"
  },
  "restrictions": {
    "operations": {
      "included": ["team:manage", "schedule:create", "incidents:handle"],
      "excluded": ["budget:manage", "performance:review"]
    },
    "quantityLimits": {
      "maxDailyOperations": 20
    }
  },
  "metadata": {
    "purpose": "Vacation Coverage",
    "delegationReason": "Annual leave coverage"
  },
  "iat": 1675091348,
  "exp": 1675094948
}
```

### 6.3 JWT Token Validation with Hierarchy Checks

```typescript
function validateHierarchicalPermission(
  jwt: JwtToken,
  requiredPermission: string,
  resourceId: string
): boolean {
  // 1. Check basic permission
  if (!jwt.permissions.includes(requiredPermission)) {
    return false;
  }

  // 2. Check hierarchical context
  const hierarchyContext = jwt.hierarchyContext;
  const targetResource = getResourceById(resourceId);

  // 3. Validate based on hierarchy level
  if (requiredPermission.startsWith("manage:")) {
    // Management permissions require appropriate hierarchy level
    return isHierarchicallyAuthorized(hierarchyContext, targetResource);
  }

  // 4. Validate scope constraints
  return isScopeAuthorized(jwt.scope, targetResource);
}

function isHierarchicallyAuthorized(
  hierarchyContext: HierarchyContext,
  targetResource: any
): boolean {
  // Direct reports check
  if (targetResource.type === "user" && targetResource.role === "TEAM_LEADER") {
    return (
      hierarchyContext.directReports.includes(targetResource.id) ||
      hierarchyContext.temporaryDirectReports.includes(targetResource.id)
    );
  }

  // Team management check
  if (targetResource.type === "team") {
    return (
      hierarchyContext.level <= 3 ||
      isTeamInDirectReports(hierarchyContext, targetResource.id)
    );
  }

  return false;
}
```

## 7. Authorization Rules

### 7.1 Hierarchical Permission Check Flow

```mermaid
flowchart TD
    A[Request with JWT] --> B{Valid Token?}
    B -->|No| C[Reject: Invalid Token]
    B -->|Yes| D{Has Required Permission?}
    D -->|No| E[Reject: Insufficient Permission]
    D -->|Yes| F{Is Target in Scope?}
    F -->|No| G[Reject: Out of Scope]
    F -->|Yes| H{Hierarchical Validation}
    H -->|Fail| I[Reject: Hierarchy Violation]
    H -->|Pass| J[Authorized]
```

### 7.2 Resource Access Validation Rules

For each hierarchical role, define access patterns based on hierarchical relationships:

```json
{
  "hierarchicalAccessRules": {
    "SUPER_ADMIN": {
      "viewAccess": ["all"],
      "manageAccess": ["all"],
      "delegateAccess": ["sameLevel", "directReports", "allLowerLevels"]
    },
    "PLANT_MANAGER": {
      "viewAccess": ["ownLevel", "directReports", "allLowerLevels"],
      "manageAccess": ["directReports", "allLowerLevels"],
      "delegateAccess": ["sameLevel", "directReports"]
    },
    "SHIFT_LEADER": {
      "viewAccess": ["ownLevel", "directReports", "directReportsSubordinates"],
      "manageAccess": ["directReports"],
      "delegateAccess": ["sameLevel", "directReports"]
    },
    "TEAM_LEADER": {
      "viewAccess": ["ownLevel", "directReports"],
      "manageAccess": ["directReports"],
      "delegateAccess": ["sameLevel"]
    }
  }
}
```

### 7.3 Authorization Middleware Implementation

```typescript
async function hierarchicalAuthorizationMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const jwt = extractAndVerifyJwt(req);
    if (!jwt) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    // Action requiring authorization
    const action = determineAction(req);
    const targetResource = determineTargetResource(req);
    const requiredPermission = `${targetResource.type}:${action}`;

    // Check basic permission
    if (!jwt.permissions.includes(requiredPermission)) {
      return res.status(403).json({
        error: "Forbidden",
        detail: "Missing required permission",
      });
    }

    // Check scope
    if (!isScopeAuthorized(jwt.scope, targetResource)) {
      return res.status(403).json({
        error: "Forbidden",
        detail: "Resource is outside authorized scope",
      });
    }

    // Check hierarchical authorization
    if (!isHierarchicallyAuthorized(jwt.hierarchyContext, targetResource)) {
      return res.status(403).json({
        error: "Forbidden",
        detail: "Hierarchical authorization failed",
      });
    }

    next();
  } catch (error) {
    return res.status(500).json({ error: "Authorization error" });
  }
}
```

## 8. Practical Examples

### 8.1 Team Leader Delegation Scenario

```mermaid
sequenceDiagram
    participant TL1 as Team Leader A
    participant SL as Shift Leader
    participant TL2 as Team Leader B
    participant Auth as Auth Service
    participant API as API Endpoints

    TL1->>SL: Request coverage during vacation
    SL->>Auth: Create managed delegation
    Auth->>Auth: Validate permissions & hierarchy
    Auth->>Auth: Create delegation & profile
    Auth->>TL2: Notify of delegation

    Note over TL2: Time passes, delegation starts

    TL2->>Auth: Activate delegated Team Leader profile
    Auth->>Auth: Issue JWT with Team Leader A's team scope

    TL2->>API: Manage Team Leader A's team
    API->>Auth: Validate JWT & delegation
    Auth->>API: Confirm authorization
    API->>TL2: Allow team management
```

### 8.2 Cross-Department Authorization Example

A Quality Inspector needs access to Production resources:

```mermaid
sequenceDiagram
    participant QI as Quality Inspector
    participant SA as Super Admin
    participant PM as Plant Manager
    participant Auth as Auth Service
    participant Prod as Production API

    QI->>SA: Request Production access for audit
    SA->>Auth: Create cross-dept temporary profile
    Auth->>Auth: Create limited Production access profile
    Auth->>QI: Notify of new profile

    QI->>Auth: Activate cross-dept profile
    Auth->>Auth: Issue JWT with limited Production scope

    QI->>Prod: Request production data
    Prod->>Auth: Validate JWT with scopes
    Auth->>Prod: Confirm limited authorization
    Prod->>QI: Return limited production data
```

### 8.3 Emergency Access Example

When Super Admin needs to grant emergency access to a Maintenance Technician:

```mermaid
sequenceDiagram
    participant SA as Super Admin
    participant Auth as Auth Service
    participant MT as Maintenance Tech
    participant System as Critical Systems

    SA->>Auth: Create emergency delegation
    Auth->>Auth: Apply emergency policy (bypass normal rules)
    Auth->>Auth: Create emergency profile with elevated permissions
    Auth->>MT: Push emergency notification

    MT->>Auth: Activate emergency profile
    Auth->>Auth: Issue JWT with elevated permissions & emergency flag
    Auth->>Auth: Start enhanced audit logging

    MT->>System: Access critical systems
    System->>Auth: Validate emergency JWT
    Auth->>System: Confirm emergency authorization
    System->>MT: Grant access with emergency logging
```

## 9. Implementation Checklist

### 9.1 Hierarchical Role Setup

- [ ] Define complete role hierarchy with levels and relationships
  - [ ] Configure SUPER_ADMIN as level 1
  - [ ] Configure PLANT_MANAGER as level 2
  - [ ] Configure SHIFT_LEADER as level 3
  - [ ] Configure TEAM_LEADER as level 4
  - [ ] Configure OPERATOR as level 5
  - [ ] Configure other roles with appropriate levels
- [ ] Map Azure AD groups to hierarchical roles
  - [ ] Create Azure AD groups for each role if not existing
  - [ ] Configure group-to-role mappings in the application
- [ ] Configure delegation permissions for each role
  - [ ] Define same-level delegation permissions
  - [ ] Define downward delegation permissions
  - [ ] Define delegation management permissions
- [ ] Set up scope attributes for each hierarchical level
  - [ ] Define system-wide scope for SUPER_ADMIN
  - [ ] Define site scope for PLANT_MANAGER
  - [ ] Define site, department, shift scope for SHIFT_LEADER
  - [ ] Define site, department, shift, team scope for TEAM_LEADER

### 9.2 Database Schema Updates

- [ ] Add hierarchyLevel field to Roles table
- [ ] Create RoleHierarchy table for parent-child relationships
  - [ ] Include parentRoleId and childRoleId fields
  - [ ] Add constraints to ensure hierarchy integrity
- [ ] Add hierarchyContext object to Profiles table
  - [ ] Include level, supervisorId, and directReports fields
  - [ ] Add delegator information for delegated profiles
- [ ] Update DelegationRules table with hierarchical constraints
  - [ ] Add hierarchical validation rules
  - [ ] Configure level-specific delegation restrictions
- [ ] Add validation rule configurations to Settings table
  - [ ] Store hierarchical access rules

### 9.3 Authorization Logic Implementation

- [ ] Implement hierarchical permission validation
  - [ ] Create functions to validate hierarchical relationships
  - [ ] Implement level-based permission checks
- [ ] Build scope validation with direct scope checking
  - [ ] Create scope validation functions
- [ ] Create delegation validation for hierarchical rules
  - [ ] Implement same-level delegation validation
  - [ ] Implement downward delegation validation
  - [ ] Implement delegation management validation
- [ ] Implement JWT generation with hierarchy context
  - [ ] Include hierarchyContext in JWT payload
  - [ ] Add delegation-specific fields for delegated profiles
- [ ] Build middleware with hierarchical validation
  - [ ] Create hierarchical authorization middleware
  - [ ] Implement resource-specific validation rules

### 9.4 API Endpoints

- [ ] Create `/api/roles/hierarchy` endpoint
  - [ ] Implement GET to retrieve role hierarchy
  - [ ] Implement PUT to update hierarchy relationships
- [ ] Implement `/api/delegations/hierarchical-validate` endpoint
  - [ ] Create validation logic for delegation requests
  - [ ] Return detailed validation results
- [ ] Update `/api/users/profiles` to include hierarchy data
  - [ ] Include supervisors and direct reports in response
  - [ ] Add hierarchical context to profile data
- [ ] Add `/api/authorization/validate-hierarchical` endpoint
  - [ ] Create endpoint to test hierarchical authorization
  - [ ] Return detailed authorization decision information

## 10. Troubleshooting

### 10.1 Common Implementation Issues

| Issue                                              | Possible Cause                              | Solution                                                   |
| -------------------------------------------------- | ------------------------------------------- | ---------------------------------------------------------- |
| Delegation rejected despite proper permissions     | Hierarchical relationship validation failed | Check if delegator is direct supervisor of delegate        |
| User cannot see resources despite role permission  | Scope mismatch                              | Verify user's scope includes the resource's scope          |
| Cross-level delegation failing                     | Skip-level delegation disabled              | Check maxSkipLevels in delegation configuration            |
| JWT missing hierarchical context                   | Token generated without hierarchy data      | Update token generation to include hierarchyContext        |
| Profile switching not preserving hierarchy         | Hierarchy context not transferred           | Ensure profile switching copies hierarchical relationships |
| SUPER_ADMIN unable to delegate to PLANT_MANAGER    | Missing delegation permission               | Add delegate:down:PLANT_MANAGER permission to SUPER_ADMIN  |
| SHIFT_LEADER cannot manage TEAM_LEADER delegations | Incorrect hierarchy level configuration     | Verify SHIFT_LEADER is level 3 and TEAM_LEADER is level 4  |

### 10.2 Validation Rule Debugging

```typescript
// Helper function to debug hierarchical validation failures
function debugHierarchicalValidation(
  delegator: User,
  delegate: User,
  role: Role,
  delegationRequest: DelegationRequest
): ValidationResult {
  const results = {
    hasRequiredPermission: checkDelegationPermission(delegator, role, delegate),
    hierarchyLevelValid: validateHierarchyLevel(delegator, delegate, role),
    scopeValid: validateDelegationScope(delegator, delegationRequest.scope),
    directSupervisor: isDirectSupervisor(delegator, delegate),
    sameLevel: isSameHierarchyLevel(delegator.role, delegate.role),
    timeConstraintsValid: validateTimeConstraints(delegationRequest),
  };

  return {
    valid: Object.values(results).every((r) => r === true),
    details: results,
  };
}
```

### 10.3 JWT Debugging

Sample debugging tool for JWT hierarchical content:

```javascript
function parseJwtHierarchy(token) {
  const decoded = decodeJwt(token);

  console.log("=== JWT HIERARCHY DEBUG ===");
  console.log("User:", decoded.sub);
  console.log("Role:", decoded.roles.join(", "));
  console.log("Hierarchy Level:", decoded.hierarchyContext?.level);
  console.log("Supervisor:", decoded.hierarchyContext?.supervisorId);
  console.log("Supervisor Role:", decoded.hierarchyContext?.supervisorRole);
  console.log("Direct Reports:", decoded.hierarchyContext?.directReports);
  console.log("Scope:", JSON.stringify(decoded.scope, null, 2));

  // Delegation specific
  if (decoded.profileType === "DELEGATED") {
    console.log("Delegated By:", decoded.delegatedBy);
    console.log(
      "Valid Until:",
      new Date(decoded.delegatedUntil).toLocaleString()
    );
    console.log("Restrictions:", JSON.stringify(decoded.restrictions, null, 2));
  }

  return decoded;
}
```

### 10.4 Hierarchy-Specific Troubleshooting

#### Debugging Hierarchy Level Issues

```typescript
function validateHierarchyConfiguration() {
  const roles = getAllRoles();
  const issues = [];

  // Check for missing parent-child relationships
  roles.forEach((role) => {
    if (role.parentRoles && role.parentRoles.length > 0) {
      role.parentRoles.forEach((parentId) => {
        const parent = roles.find((r) => r.id === parentId);
        if (!parent) {
          issues.push(
            `Role ${role.id} references non-existent parent ${parentId}`
          );
        } else if (!parent.childRoles || !parent.childRoles.includes(role.id)) {
          issues.push(
            `Parent role ${parentId} does not reference ${role.id} as child`
          );
        }
      });
    }

    // Verify hierarchy level is consistent with parent-child relationships
    if (role.parentRoles && role.parentRoles.length > 0) {
      const parents = role.parentRoles
        .map((parentId) => roles.find((r) => r.id === parentId))
        .filter(Boolean);

      const expectedLevel =
        Math.min(...parents.map((p) => p.hierarchyLevel)) + 1;
      if (role.hierarchyLevel !== expectedLevel) {
        issues.push(
          `Role ${role.id} has inconsistent hierarchy level. Expected ${expectedLevel}, got ${role.hierarchyLevel}`
        );
      }
    } else if (role.hierarchyLevel !== 1) {
      issues.push(`Role ${role.id} has no parents but is not at level 1`);
    }
  });

  return {
    valid: issues.length === 0,
    issues,
  };
}
```

#### Delegation Permission Validation

```typescript
function validateDelegationPermissions() {
  const roles = getAllRoles();
  const issues = [];

  roles.forEach((role) => {
    // Check if role has appropriate delegation permissions
    const delegationPermissions = getPermissionsForRole(role.id).filter((p) =>
      p.startsWith("delegate:")
    );

    // Validate same-level delegation permissions
    const sameLevelPerm = `delegate:same:${role.id}`;
    if (!delegationPermissions.includes(sameLevelPerm)) {
      issues.push(
        `Role ${role.id} is missing same-level delegation permission: ${sameLevelPerm}`
      );
    }

    // Validate downward delegation permissions for non-leaf roles
    if (role.childRoles && role.childRoles.length > 0) {
      role.childRoles.forEach((childId) => {
        const downwardPerm = `delegate:down:${childId}`;
        if (!delegationPermissions.includes(downwardPerm)) {
          issues.push(
            `Role ${role.id} is missing downward delegation permission: ${downwardPerm}`
          );
        }
      });
    }
  });

  return {
    valid: issues.length === 0,
    issues,
  };
}
```
