# Integrated Authorization and Delegation Framework

## Document Information

**Version:** 2.0.0  
**Last Updated:** 2026-02-27  
**Status:** Draft

## Table of Contents

1. [Overview](#1-overview)
2. [Core Authorization Concepts](#2-core-authorization-concepts)
   - [Role Management](#21-role-management)
   - [Permission Framework](#22-permission-framework)
   - [Scope Management](#23-scope-management)
   - [Delegation Permissions](#24-delegation-permissions)
3. [Azure AD Integration](#3-azure-ad-integration)
   - [Group to Role Mapping](#31-group-to-role-mapping)
   - [Just-In-Time Synchronization](#32-just-in-time-synchronization)
   - [Graph API Integration](#33-graph-api-integration)
4. [Profile-Based Authorization](#4-profile-based-authorization)
   - [Profile Types and Lifecycle](#41-profile-types-and-lifecycle)
   - [Profile Switching](#42-profile-switching)
   - [JWT Integration](#43-jwt-integration)
5. [Delegation Framework](#5-delegation-framework)
   - [Delegation Process](#51-delegation-process)
   - [Delegation Restrictions](#52-delegation-restrictions)
   - [Delegation Lifecycle](#53-delegation-lifecycle)
   - [Hierarchical Delegation Rules](#54-hierarchical-delegation-rules)
   - [Delegation Authorization Model](#55-delegation-authorization-model)
   - [Cross-Level and Same-Level Delegation](#56-cross-level-and-same-level-delegation)
6. [JWT Token Structure](#6-jwt-token-structure)
   - [Claims Structure](#61-claims-structure)
   - [Profile Context in Tokens](#62-profile-context-in-tokens)
7. [Implementation Architecture](#7-implementation-architecture)
   - [System Components](#71-system-components)
   - [Authorization Flow](#72-authorization-flow)
8. [Security Considerations](#8-security-considerations)
9. [Advanced Scenarios](#9-advanced-scenarios)
10. [Data Model](#10-data-model)
11. [Best Practices](#11-best-practices)
    - [Delegation Permission Assignment](#111-delegation-permission-assignment)
    - [Scope Boundary Management](#112-scope-boundary-management)

## 1. Overview

This document outlines the integrated authorization and delegation framework for Connected Workers, leveraging JWT authentication with SAML SSO and Microsoft Graph API synchronization. The framework provides a seamless approach to role-based access control, profile management, and secure delegation while maintaining continuous synchronization with Microsoft Entra ID (formerly Azure AD).

### Key Features

- **Azure AD Integration**: Central source of truth for user identity and group memberships
- **Real-time Synchronization**: User attributes and role assignments updated via Graph API
- **Profile-Based Authorization**: Multiple role contexts per user with simple switching
- **Streamlined Delegation**: Secure, time-bound transfer of authority between users
- **JWT-Based Authorization**: Stateless authorization using claims in JWT tokens
- **Hierarchical Role Management**: Support for organizational role hierarchies with clear level-based permissions
- **Delegation Permissions Model**: Granular control over who can delegate what to whom

### Business Benefits

- **Simplified User Management**: Leverage existing Azure AD for identity and access
- **Reduced Administrative Overhead**: Automate user provisioning and role assignment
- **Enhanced Security**: Maintain current user data with continuous synchronization
- **Operational Flexibility**: Enable users to operate in different role contexts as needed
- **Business Continuity**: Ensure critical functions continue through secure delegation
- **Organizational Alignment**: Authorization structure reflects organizational hierarchy
- **Controlled Delegation**: Precise control over delegation capabilities within the organization

## 2. Core Authorization Concepts

### 2.1 Role Management

Roles are collections of permissions derived from Azure AD group memberships, providing a direct mapping between organizational structure and system permissions.

#### Role Types

| Role Type      | Description                            | Examples                                 |
| -------------- | -------------------------------------- | ---------------------------------------- |
| Organizational | Based on position in company hierarchy | PLANT_MANAGER, SHIFT_LEADER, TEAM_LEADER |
| Functional     | Based on specific job functions        | QUALITY_INSPECTOR, MAINTENANCE_TECH      |
| Administrative | System administration capabilities     | SYSTEM_ADMIN, USER_MANAGER               |
| Specialized    | Temporary or special-purpose access    | EMERGENCY_RESPONDER, AUDITOR             |

#### Role Hierarchy

```mermaid
graph TD
    SA[SUPER_ADMIN] --> PM[PLANT_MANAGER]
    PM --> SL[SHIFT_LEADER]
    SL --> TL[TEAM_LEADER]
    TL --> OP[OPERATOR]
    SA --> TR[TRAINING_RESPONSIBLE]
    TR --> T[TRAINER]
    SA --> QI[QUALITY_INSPECTOR]
    SA --> MT[MAINTENANCE_TECH]
```

#### Hierarchical Role Structure

Roles can have parent-child relationships that define an organization's management structure. Each role has:

- A hierarchical level (1 being the highest, increasing numbers for lower levels)
- Parent role references (which roles are above in the hierarchy)
- Child role references (which roles are below in the hierarchy)

This hierarchy structure enables:

1. **Direct Permission Assignment**: Each role has explicitly assigned permissions based on its level
2. **Delegation Control**: Delegation can be restricted to follow the hierarchy
3. **Organizational Alignment**: Authorization reflects organizational structure
4. **Validation Rules**: Hierarchical authorization checks (e.g., a role can only manage users with roles below it)

#### Role Definition with Hierarchy Support

```json
{
  "role": {
    "id": "SHIFT_LEADER",
    "name": "Shift Leader",
    "description": "Manages production shift and team leaders",
    "hierarchyLevel": 3,
    "parentRoles": ["PLANT_MANAGER"],
    "childRoles": ["TEAM_LEADER"],
    "azureAdGroups": ["Shift Leaders"]
  }
}
```

### 2.2 Permission Framework

Permissions follow a simple resource:action format that clearly defines what operations a user can perform on specific resources.

#### Permission Structure

| Component | Description                   | Example                              |
| --------- | ----------------------------- | ------------------------------------ |
| Resource  | The entity being accessed     | `team`, `schedule`, `training`       |
| Action    | The operation being performed | `create`, `read`, `update`, `delete` |

#### Permission Examples

| Permission          | Description                            |
| ------------------- | -------------------------------------- |
| `team:manage`       | Manage team information and membership |
| `schedule:view`     | View schedules                         |
| `training:assign`   | Assign training to users               |
| `equipment:control` | Control equipment and machinery        |
| `incidents:handle`  | Manage and respond to incident reports |
| `budget:manage`     | Create and modify budget-related items |
| `shift:manage`      | Manage shift schedules and assignments |
| `reports:generate`  | Create and run reports                 |
| `quality:inspect`   | Perform quality inspection tasks       |
| `quality:approve`   | Approve quality-related documentation  |

### 2.3 Scope Management

Scopes define the boundaries within which permissions can be exercised, allowing for fine-grained access control.

#### Scope Types

| Scope Type     | Description                 | Examples                     |
| -------------- | --------------------------- | ---------------------------- |
| Organizational | Based on company structure  | Site, Department, Team       |
| Temporal       | Based on time periods       | Shift, Date Range            |
| Functional     | Based on functional areas   | Process Area, Equipment Type |
| Resource       | Based on specific resources | Machine ID, Material Type    |

#### Scope Implementation

Scopes are implemented as attributes on user profiles, role assignments, and delegations. They can be derived from:

1. **Azure AD Attributes**: Department, location, job title
2. **Application Metadata**: Team assignments, shift assignments
3. **Delegation Restrictions**: Explicitly defined in delegation requests

### 2.4 Delegation Permissions

Delegation permissions govern who can delegate authority, to whom, and under what circumstances. These permissions follow a structured format and operate within the constraints of hierarchical relationships and scope boundaries.

#### Delegation Permission Structure

Delegation permissions follow the format `delegate:{direction}:{role}`, where:

- `delegate` is the resource
- `{direction}` defines the delegation direction relative to the hierarchy (same, down, up, manage)
- `{role}` specifies the target role for delegation (or `any` for multiple roles)

#### Delegation Permission Examples

| Permission                    | Description                                                                  |
| ----------------------------- | ---------------------------------------------------------------------------- |
| `delegate:same:any`           | Delegate own role to someone with the same role                              |
| `delegate:down:TEAM_LEADER`   | Delegate own role to a TEAM_LEADER (only valid for roles above TEAM_LEADER)  |
| `delegate:same:SHIFT_LEADER`  | Delegate own role to another SHIFT_LEADER (only valid for SHIFT_LEADER role) |
| `delegate:manage:TEAM_LEADER` | Manage delegations between TEAM_LEADER roles (for supervisors)               |
| `delegate:temporary:any`      | Create temporary delegations (limited duration)                              |
| `delegate:emergency:any`      | Create emergency delegations (special case with reduced restrictions)        |

#### Delegation Permission Scope

Each delegation permission operates within:

1. **Hierarchical Boundaries**: Determined by role hierarchy relationships
2. **Scope Restrictions**: Limited by the delegator's organizational scope
3. **Time Constraints**: May have restrictions on delegation duration
4. **Operational Context**: May be limited to specific business contexts

#### Delegation Permission Assignment

Delegation permissions are assigned to roles based on organizational policies:

| Role                | Delegation Permissions                                                                      |
| ------------------- | ------------------------------------------------------------------------------------------- |
| PLANT_MANAGER       | `delegate:same:PLANT_MANAGER`, `delegate:down:SHIFT_LEADER`, `delegate:manage:SHIFT_LEADER` |
| SHIFT_LEADER        | `delegate:same:SHIFT_LEADER`, `delegate:down:TEAM_LEADER`, `delegate:manage:TEAM_LEADER`    |
| TEAM_LEADER         | `delegate:same:TEAM_LEADER` (within shift), `delegate:temporary:OPERATOR`                   |
| TRAINER_RESPONSIBLE | `delegate:same:TRAINER_RESPONSIBLE`, `delegate:down:TRAINER`, `delegate:manage:TRAINER`     |
| TRAINER             | `delegate:same:TRAINER` (within specialization)                                             |

## 3. Azure AD Integration

### 3.1 Group to Role Mapping

Azure AD security groups are mapped directly to application roles, creating a seamless integration between identity management and authorization.

#### Configuration Example

| Azure AD Group          | Application Role  | Auto-Assign | Scope Attributes |
| ----------------------- | ----------------- | ----------- | ---------------- |
| Plant Managers          | PLANT_MANAGER     | Yes         | site             |
| Shift Leaders           | SHIFT_LEADER      | Yes         | site, shift      |
| Team Leaders            | TEAM_LEADER       | Yes         | team             |
| Quality Inspectors      | QUALITY_INSPECTOR | Yes         | department, site |
| Maintenance Technicians | MAINTENANCE_TECH  | Yes         | site             |

#### Mapping Configuration

```json
{
  "groupMappings": [
    {
      "azureAdGroupId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "displayName": "Plant Managers",
      "applicationRole": "PLANT_MANAGER",
      "autoAssign": true,
      "scopeAttributes": ["site"]
    },
    {
      "azureAdGroupId": "b2c3d4e5-f6a7-8901-bcde-f12345678901",
      "displayName": "Quality Inspectors",
      "applicationRole": "QUALITY_INSPECTOR",
      "autoAssign": true,
      "scopeAttributes": ["department", "site"]
    }
  ]
}
```

### 3.2 Just-In-Time Synchronization

User data is synchronized with Azure AD at critical moments to ensure current information while minimizing API calls.

#### Initial Authentication (JIT Provisioning)

When a user first authenticates via SAML SSO:

1. SAML response provides initial user information
2. System checks if user exists in the database
3. For new users:
   - Create user record with basic SAML claims
   - Query Microsoft Graph API for complete profile and group memberships
   - Map Azure AD groups to application roles
   - Create default profile with appropriate roles
   - Set up access and refresh tokens

```mermaid
sequenceDiagram
    participant User
    participant App as Application
    participant API as API Backend
    participant AAD as Azure AD
    participant Graph as Microsoft Graph API
    participant DB as Database

    User->>App: Access Application
    App->>AAD: SAML Authentication Request
    User->>AAD: Login (if no session)
    AAD->>App: SAML Response with Claims
    App->>API: Exchange SAML for JWT
    API->>DB: Check if User Exists

    alt User Does Not Exist (First Login)
        API->>Graph: Fetch User Details & Group Membership
        Graph->>API: Complete User Profile
        API->>API: Map Groups to Roles
        API->>DB: Create User & Default Profile
    else User Exists
        API->>DB: Update Login Timestamp
    end

    API->>App: Issue Access & Refresh Tokens
    App->>User: Authenticated Session
```

#### Token Refresh (Continuous Synchronization)

When refreshing authentication tokens:

1. Receive refresh token request
2. Query Microsoft Graph API for latest user data
3. Update user attributes and group memberships
4. Adjust role assignments based on current groups
5. Issue new JWT tokens with updated information

```mermaid
sequenceDiagram
    participant App as Application
    participant API as API Backend
    participant Graph as Microsoft Graph API
    participant DB as Database

    App->>API: Request Token Refresh
    API->>Graph: Fetch Latest User Data & Groups
    Graph->>API: Return Updated Information
    API->>API: Map Groups to Roles
    API->>DB: Update User Profile
    API->>App: Issue New Tokens with Updated Roles
```

### 3.3 Graph API Integration

Microsoft Graph API provides rich user and group data that enhances the authorization framework.

#### Key Graph API Endpoints

| Endpoint                         | Purpose                   | Retrieved Information            |
| -------------------------------- | ------------------------- | -------------------------------- |
| `/users/{id}`                    | Get user details          | User attributes, job information |
| `/users/{id}/memberOf`           | Get group memberships     | Direct and transitive groups     |
| `/groups/{id}/members`           | Get group members         | Users in specific groups         |
| `/users/{id}/transitiveMemberOf` | Get all group memberships | Include nested groups            |

#### Synchronization Strategy

To balance data freshness with API usage:

1. **Full Synchronization**: On first login and significant events (role change)
2. **Partial Synchronization**: During token refresh (groups only)
3. **Batch Processing**: Group multiple user queries when possible
4. **Caching**: Cache Graph API responses with appropriate TTL

## 4. Profile-Based Authorization

### 4.1 Profile Types and Lifecycle

Profiles provide contextualized role containers that users can switch between.

#### Profile Types

| Profile Type | Source    | Description                                  | Creation                            |
| ------------ | --------- | -------------------------------------------- | ----------------------------------- |
| Direct       | Assigned  | Based on user's Azure AD group memberships   | Created during first authentication |
| Delegated    | Temporary | Created through delegation from another user | Created during delegation process   |
| System       | Automatic | Generated for special purposes               | Created for specific scenarios      |

#### Profile Attributes

Each profile contains:

- Unique identifier
- User reference
- Name and description
- Profile type
- Active status
- Validity period (for delegated profiles)
- Associated roles and restrictions
- Contextual metadata

#### Profile Lifecycle States

```mermaid
stateDiagram-v2
    [*] --> Inactive : Create
    Inactive --> Active : Activate
    Active --> Inactive : Deactivate
    Inactive --> [*] : Delete/Expire
    Active --> [*] : Delete/Expire
```

### 4.2 Profile Switching

Users can switch between available profiles to change their operating context.

#### Profile Switching Process

```mermaid
sequenceDiagram
    participant User
    participant App as Application
    participant API as API Backend
    participant DB as Database

    User->>App: Request Profile Switch
    App->>API: Switch Profile Request
    API->>DB: Validate Profile Access
    API->>DB: Update Active Profile
    API->>API: Generate New JWT with Profile Context
    API->>App: Return New JWT
    App->>User: Updated Authorization Context
```

### 4.3 JWT Integration

JWT tokens serve as the carrier for profile context and authorization information.

#### Profile Context in JWT

Each JWT contains:

- User identity information
- Active profile identifier and type
- Roles associated with active profile
- Permissions derived from those roles
- Scope restrictions
- Delegation reference (if applicable)

#### Profile Selection Logic

When multiple profiles are available:

1. Default profile is activated on authentication
2. User can explicitly switch to another profile
3. System can suggest appropriate profile based on context
4. JWT is always refreshed after profile switching

## 5. Delegation Framework

### 5.1 Delegation Process

Delegation enables temporary transfer of authority from one user to another through a streamlined process.

#### Delegation Creation Flow

```mermaid
sequenceDiagram
    participant Delegator
    participant API as API Backend
    participant DB as Database
    participant Delegate

    Delegator->>API: Create Delegation Request
    API->>DB: Validate Delegator Authority
    API->>DB: Create Delegation Record
    API->>DB: Create Delegated Profile
    API->>Delegate: Notify of New Delegation
    API->>Delegator: Confirm Creation
```

#### Delegation Request Structure

```json
{
  "delegateId": "user123",
  "roleId": "SHIFT_LEADER",
  "validFrom": "2025-03-20T00:00:00Z",
  "validUntil": "2025-03-27T23:59:59Z",
  "profileName": "Shift Leader Coverage",
  "restrictions": {
    "scope": {
      "teams": ["TEAM_A", "TEAM_B"],
      "shifts": ["MORNING_SHIFT"]
    },
    "operations": {
      "included": ["shift:manage", "incidents:handle"],
      "excluded": ["budget:manage"]
    }
  },
  "metadata": {
    "purpose": "Vacation Coverage"
  }
}
```

### 5.2 Delegation Restrictions

Restrictions limit the scope and capabilities of delegated authority.

#### Restriction Types

| Restriction Type     | Description                      | Example                                   |
| -------------------- | -------------------------------- | ----------------------------------------- |
| Scope Limitations    | Where authority can be exercised | `{"teams": ["TEAM_A", "TEAM_B"]}`         |
| Operation Inclusions | Explicitly allowed operations    | `["shift:manage", "incidents:handle"]`    |
| Operation Exclusions | Explicitly forbidden operations  | `["budget:manage", "performance:review"]` |
| Quantity Limits      | Numeric limits on operations     | `{"maxDailyOperations": 20}`              |

### 5.3 Delegation Lifecycle

Delegations follow a simple lifecycle with clear state transitions.

#### Delegation States

```mermaid
stateDiagram-v2
    [*] --> Active: Create
    Active --> Expired: Time Elapsed
    Active --> Revoked: Manual Revoke
    Expired --> [*]
    Revoked --> [*]
```

#### State Transitions

| From    | To      | Trigger                   | Action                            |
| ------- | ------- | ------------------------- | --------------------------------- |
| -       | Active  | Creation                  | Create delegation and profile     |
| Active  | Expired | Time boundary reached     | Automatically deactivate profile  |
| Active  | Revoked | Manual revocation request | Deactivate profile, record reason |
| Expired | -       | Cleanup process           | Archive delegation record         |
| Revoked | -       | Cleanup process           | Archive delegation record         |

### 5.4 Hierarchical Delegation Rules

Delegation within hierarchical organizational structures follows specific rules to maintain management chain integrity.

#### Hierarchical Delegation Principles

1. **Delegation Flow Direction**: Delegations typically flow downward in the hierarchy
2. **Level Restrictions**: Delegations are usually limited to adjacent levels
3. **Scope Limitations**: Delegated authority cannot exceed the delegator's scope
4. **Contextual Validation**: Delegation may require validation against organizational context

#### Hierarchical Validation Rules

```mermaid
flowchart TD
    A[Delegation Request] --> B{Delegator has role?}
    B -->|No| C[Deny: Delegator lacks role]
    B -->|Yes| D{Valid hierarchy level?}
    D -->|No| E[Deny: Invalid hierarchy level]
    D -->|Yes| F{Within delegator scope?}
    F -->|No| G[Deny: Out of scope]
    F -->|Yes| H{Contextually valid?}
    H -->|No| I[Deny: Context validation failed]
    H -->|Yes| J[Create Delegation]
```

#### Hierarchical Delegation Configuration

```json
{
  "delegationRules": {
    "hierarchyValidation": true,
    "allowSkipLevel": false,
    "maxSkipLevels": 0,
    "validateHierarchicalScope": true,
    "allowDelegationChains": false,
    "validateOrganizationalContext": true
  }
}
```

#### Delegation Rules Matrix Example

| Delegator Role    | Delegate Role | Permitted | Reason                           |
| ----------------- | ------------- | --------- | -------------------------------- |
| PLANT_MANAGER     | SHIFT_LEADER  | Yes       | Direct hierarchical relationship |
| SHIFT_LEADER      | TEAM_LEADER   | Yes       | Direct hierarchical relationship |
| PLANT_MANAGER     | TEAM_LEADER   | No        | Skip level (configurable)        |
| TEAM_LEADER       | SHIFT_LEADER  | No        | Against hierarchy direction      |
| QUALITY_INSPECTOR | TEAM_LEADER   | No        | No hierarchical relationship     |

### 5.5 Delegation Authorization Model

The delegation authorization model defines how delegation permissions are evaluated against delegation requests, ensuring proper authorization throughout the delegation lifecycle.

#### Permission Evaluation Process

```mermaid
flowchart TD
    A[Delegation Request] --> B{Has Required Permission?}
    B -->|No| C[Deny: Insufficient Permission]
    B -->|Yes| D{Validate Hierarchy Rules}
    D -->|Fail| E[Deny: Hierarchy Violation]
    D -->|Pass| F{Validate Scope Rules}
    F -->|Fail| G[Deny: Scope Violation]
    F -->|Pass| H{Validate Context Rules}
    H -->|Fail| I[Deny: Context Violation]
    H -->|Pass| J[Approve Delegation]
```

#### Authorization Decision Factors

1. **Permission Check**: Verify the delegator has the appropriate delegation permission
2. **Hierarchy Validation**: Ensure the delegation follows hierarchical constraints
3. **Scope Validation**: Confirm the delegation is within appropriate scope boundaries
4. **Context Validation**: Check business context rules (e.g., qualifications, conflicts)

#### Permission Mapping Examples

| Scenario                                                | Required Permission              | Additional Validation                               |
| ------------------------------------------------------- | -------------------------------- | --------------------------------------------------- |
| Shift Leader delegates to another Shift Leader          | `delegate:same:SHIFT_LEADER`     | Validate shift scope                                |
| Shift Leader delegates to Team Leader                   | `delegate:down:TEAM_LEADER`      | Validate hierarchical relationship and shared scope |
| Plant Manager manages delegation between Shift Leaders  | `delegate:manage:SHIFT_LEADER`   | Validate supervisory relationship and shared scope  |
| Team Leader creates temporary delegation to Team Leader | `delegate:temporary:TEAM_LEADER` | Validate time constraints and scope                 |

### 5.6 Cross-Level and Same-Level Delegation

The framework supports both cross-level (hierarchical) and same-level (peer) delegations with appropriate controls.

#### Same-Level Delegation (Peer Delegation)

Same-level delegation allows users with the same role to delegate authority to each other, typically within the same organizational scope:

```json
{
  "sameLevelDelegationRules": {
    "enforceSharedScope": true,
    "allowedScopeDifferences": ["team"],
    "requireExplicitPermission": true,
    "maxDuration": "30D",
    "requireApproval": false
  }
}
```

**Example: Team Leader to Team Leader**

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "restrictions": {
      "scope": {
        "site": "SITE_A",
        "department": "PRODUCTION",
        "shift": "MORNING",
        "team": "ASSEMBLY_1"
      }
    }
  }
}
```

#### Cross-Level Delegation (Hierarchical Delegation)

Cross-level delegation follows the organizational hierarchy, allowing delegation to flow downward with appropriate permissions:

```json
{
  "crossLevelDelegationRules": {
    "allowDirection": "downward",
    "maxLevelDistance": 1,
    "requireExplicitPermission": true,
    "enforceStrictScopeValidation": true,
    "maxDuration": "90D",
    "requireApproval": true
  }
}
```

**Example: Shift Leader to Team Leader**

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "SHIFT_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "restrictions": {
      "scope": {
        "site": "SITE_A",
        "department": "PRODUCTION",
        "shift": "MORNING"
      },
      "operations": {
        "included": ["team:manage", "schedule:view"],
        "excluded": ["performance:review"]
      }
    }
  }
}
```

#### Supervisor-Managed Delegation

Supervisors can manage delegations between their direct reports with the appropriate delegation management permission:

```json
{
  "managedDelegationRules": {
    "requireDirectSupervisor": true,
    "requireSharedScope": true,
    "allowDelegationOutsideTeam": false,
    "requireExplicitPermission": true,
    "notifyParticipants": true
  }
}
```

**Example: Shift Leader Managing Team Leader Delegations**

```json
{
  "managedDelegationRequest": {
    "managerId": "<EMAIL>",
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "restrictions": {
      "scope": {
        "team": "ASSEMBLY_1"
      }
    },
    "reason": "Coverage during scheduled training"
  }
}
```

## 6. JWT Token Structure

### 6.1 Claims Structure

JWT tokens contain claims that define user identity, roles, and permissions.

#### Core Claims

| Claim         | Description               | Example                            |
| ------------- | ------------------------- | ---------------------------------- |
| `sub`         | User identifier           | `"<EMAIL>"`           |
| `name`        | User's display name       | `"John Doe"`                       |
| `email`       | User's email address      | `"<EMAIL>"`           |
| `roles`       | Array of role IDs         | `["TEAM_LEADER", "TRAINER"]`       |
| `permissions` | Flattened permission list | `["team:manage", "schedule:view"]` |

### 6.2 Profile Context in Tokens

JWT tokens include profile context to support profile-based authorization.

#### Profile Claims

| Claim             | Description                      | Example                                      |
| ----------------- | -------------------------------- | -------------------------------------------- |
| `activeProfileId` | ID of active profile             | `"profile123"`                               |
| `profileType`     | Type of active profile           | `"DELEGATED"`                                |
| `delegationId`    | ID of active delegation (if any) | `"delegation456"`                            |
| `scope`           | Scope limitations                | `{"sites": ["SITE_A"], "teams": ["TEAM_X"]}` |
| `restrictions`    | Operational restrictions         | `{"excluded": ["budget:manage"]}`            |

#### Example Token Payload

```json
{
  "sub": "<EMAIL>",
  "name": "John Doe",
  "email": "<EMAIL>",
  "department": "Production",
  "jobTitle": "Team Leader",
  "roles": ["TEAM_LEADER", "TRAINER"],
  "permissions": ["team:manage", "schedule:view", "training:assign"],
  "activeProfileId": "profile123",
  "profileType": "DELEGATED",
  "delegationId": "delegation456",
  "scope": {
    "sites": ["SITE_A"],
    "teams": ["TEAM_X", "TEAM_Y"]
  },
  "restrictions": {
    "excluded": ["budget:manage"]
  },
  "iat": 1675091348,
  "exp": 1675094948
}
```

## 7. Implementation Architecture

### 7.1 System Components

The authorization system consists of several streamlined components that work together.

| Component                | Responsibility                    | Integration Points                    |
| ------------------------ | --------------------------------- | ------------------------------------- |
| Authentication Service   | Handle SAML SSO and JWT issuance  | Azure AD, Token Service               |
| Profile Service          | Manage user profiles and context  | User Service, Role Service            |
| Role Service             | Define roles and permissions      | Azure AD Groups, Permission Database  |
| Delegation Service       | Handle delegation lifecycle       | Profile Service, Notification Service |
| Authorization Middleware | Validate permissions for requests | JWT Tokens, Permission Cache          |
| Synchronization Service  | Sync user data with Azure AD      | Microsoft Graph API, Database         |

```mermaid
graph TD
    A[Authentication Service] --- B[Profile Service]
    A --- C[Synchronization Service]
    B --- D[Role Service]
    B --- E[Delegation Service]
    F[Authorization Middleware] --- B
    F --- D
    C --- G[Microsoft Graph API]
    A --- H[Azure AD]
```

### 7.2 Authorization Flow

The authorization flow integrates with the JWT authentication process:

```mermaid
flowchart TB
    A[User Request] --> B[Authorization Middleware]
    B --> C{JWT Valid?}
    C -->|No| D[Token Refresh]
    C -->|Yes| E[Extract Profile Context]
    E --> F{Permission Check}
    F -->|Pass| G[Execute Request]
    F -->|Fail| H[Permission Denied]

    D --> I[Fetch User from Graph API]
    I --> J[Sync Groups & Roles]
    J --> K[Update Profiles]
    K --> L[Issue New JWT]
    L --> E
```

#### Permission Check Process

1. Extract JWT from request header
2. Verify token signature and validity
3. Extract active profile and permissions from claims
4. Compare required permission against token permissions
5. Apply scope restrictions if applicable
6. Return authorization decision

## 8. Security Considerations

| Security Aspect          | Implementation                                                                                               |
| ------------------------ | ------------------------------------------------------------------------------------------------------------ |
| Token Security           | - Asymmetric RS256 signing<br>- Short-lived access tokens (1h)<br>- Refresh token in HTTP-only secure cookie |
| Delegation Controls      | - Mandatory time constraints<br>- Required scope limitations<br>- Prevention of delegation chains            |
| Audit Logging            | - Authentication events<br>- Profile switches<br>- Delegation lifecycle events<br>- Permission checks        |
| Synchronization Security | - Least privilege service accounts<br>- Secure token handling<br>- Rate limiting and throttling              |

## 9. Advanced Scenarios

### 9.1 Emergency Access

Emergency situations may require expedited access to critical functions.

#### Emergency Delegation Process

```mermaid
sequenceDiagram
    participant Requester
    participant API as API Backend
    participant Approver
    participant Delegate

    Requester->>API: Create Emergency Delegation Request
    API->>Approver: Send Approval Request (if needed)
    Approver->>API: Approve Emergency Request
    API->>API: Create Emergency Delegation & Profile
    API->>Delegate: Notify of Emergency Access
    API->>Requester: Confirm Emergency Delegation
```

#### Emergency Access Controls

1. Pre-defined emergency profiles with appropriate permissions
2. Short duration (typically 8-24 hours)
3. Enhanced monitoring and logging
4. Automatic notifications to security team
5. Post-incident review required

### 9.2 Role Conflict Management

Some role combinations may create conflicts of interest and should be managed.

#### Conflict Resolution Strategies

1. **Profile Separation**: Keep conflicting roles in separate profiles
2. **Scope Restrictions**: Limit scope where conflicting roles apply
3. **Operational Exclusions**: Remove specific permissions that create conflicts
4. **Approval Requirements**: Require additional approval for conflicting actions
5. **Enhanced Logging**: Increase audit detail for potential conflict scenarios

### 9.3 Temporary Access Management

Short-term access needs can be handled through streamlined delegation.

#### Temporary Access Process

```mermaid
sequenceDiagram
    participant Manager
    participant API as API Backend
    participant Temporary as Temporary User

    Manager->>API: Create Temporary Access Request
    API->>API: Validate Manager Authority
    API->>API: Create Time-Limited Delegation
    API->>API: Create Restricted Profile
    API->>Temporary: Notify of Temporary Access
    API->>Manager: Confirm Temporary Access Creation
```

## 10. Data Model

The data model supports the integrated authentication and authorization framework with well-defined relationships between entities.

### 10.1 Core Entities

```mermaid
classDiagram
    User "1" --> "*" Profile
    Profile "1" --> "*" RoleAssignment
    RoleAssignment --> "1" Role
    Role "1" --> "*" Permission
    Role "1" --> "*" Role : parentOf
    Delegation --> "1" User : delegator
    Delegation --> "1" User : delegate
    Delegation --> "1" Role
    Delegation --> "1" Profile : creates
    AzureADGroup "1" --> "*" Role

    class User {
        +String id
        +String email
        +String displayName
        +String employeeId
        +Map attributes
        +Date lastSyncTime
    }

    class Role {
        +String id
        +String name
        +String description
        +int hierarchyLevel
        +String[] parentRoles
        +String[] childRoles
    }

    class Permission {
        +String id
        +String name
        +String resource
        +String action
    }

    class Profile {
        +String id
        +String userId
        +String name
        +ProfileType type
        +Boolean isActive
        +Date validFrom
        +Date validUntil
        +Map metadata
    }

    class RoleAssignment {
        +String id
        +String profileId
        +String roleId
        +RoleSource source
        +String delegationId
        +Map restrictions
    }

    class Delegation {
        +String id
        +String delegatorId
        +String delegateId
        +String roleId
        +String profileId
        +DelegationStatus status
        +Date validFrom
        +Date validUntil
        +Map restrictions
        +Map metadata
    }

    class AzureADGroup {
        +String id
        +String azureAdId
        +String displayName
    }
```

### 10.2 Database Tables

| Table Name        | Primary Purpose              | Key Fields                                                                    |
| ----------------- | ---------------------------- | ----------------------------------------------------------------------------- |
| Users             | Store user information       | id, email, employeeId, attributes, lastSyncTime                               |
| Roles             | Define available roles       | id, name, description, hierarchyLevel, parentRoles, childRoles                |
| Permissions       | Define available permissions | id, resource, action, description                                             |
| RolePermissions   | Map roles to permissions     | roleId, permissionId                                                          |
| Profiles          | Store user profiles          | id, userId, name, type, isActive, validFrom, validUntil                       |
| RoleAssignments   | Assign roles to profiles     | id, profileId, roleId, source, delegationId, restrictions                     |
| Delegations       | Track delegations            | id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil |
| AzureADGroups     | Map Azure AD groups          | id, azureAdId, displayName                                                    |
| GroupRoleMappings | Map groups to roles          | groupId, roleId, autoAssign, scopeAttributes                                  |

### 10.3 Key Relationships

1. **User → Profiles**: One user can have multiple profiles
2. **Profile → Roles**: Profiles contain role assignments
3. **Role → Permissions**: Roles aggregate permissions
4. **Role → Role**: Roles have hierarchical relationships (parent/child)
5. **Delegation → Profile**: Delegations create delegated profiles
6. **Azure AD Group → Role**: Azure AD groups map to application roles

## 11. Best Practices

### 11.1 Delegation Permission Assignment

Follow these best practices when assigning delegation permissions to roles:

1. **Principle of Least Privilege**: Assign the minimum delegation permissions needed for a role to function
2. **Hierarchical Alignment**: Ensure delegation permissions respect the organizational hierarchy
3. **Scope Boundaries**: Always enforce scope boundaries to prevent unauthorized access
4. **Explicit Permissions**: Use explicit permission assignments for each role level
5. **Time Constraints**: Apply appropriate time limits on delegations

#### Permission Assignment Matrix Example

| Role                | Same-Level Delegation | Downward Delegation | Delegation Management | Temporary Delegation |
| ------------------- | :-------------------: | :-----------------: | :-------------------: | :------------------: |
| PLANT_MANAGER       |       ✓ (site)        |  ✓ (SHIFT_LEADER)   |   ✓ (SHIFT_LEADER)    |       ✓ (any)        |
| SHIFT_LEADER        |       ✓ (shift)       |   ✓ (TEAM_LEADER)   |    ✓ (TEAM_LEADER)    |   ✓ (TEAM_LEADER)    |
| TEAM_LEADER         |       ✓ (team)        |    ✓ (OPERATOR)     |           ✗           |     ✓ (OPERATOR)     |
| TRAINER_RESPONSIBLE |    ✓ (department)     |     ✓ (TRAINER)     |      ✓ (TRAINER)      |     ✓ (TRAINER)      |
| TRAINER             |  ✓ (specialization)   |          ✗          |           ✗           |          ✗           |

### 11.2 Scope Boundary Management

Proper management of scope boundaries is essential for secure delegation:

1. **Scope Narrowing**: Always enforce scope narrowing for delegations
2. **Required Parameters**: Validate all required scope parameters are present
3. **Scope Limitations**: Delegated authority must respect the delegator's scope limitations
4. **Context Validation**: Consider business context when validating scope
5. **Audit Boundaries**: Log all scope boundary decisions for audit purposes

#### Scope Validation Example

```javascript
// Example: Validating shift leader to team leader delegation scope
function validateShiftLeaderToTeamLeaderScope(
  shiftLeader,
  teamLeader,
  requestedScope
) {
  // 1. Verify shared department and site
  if (
    shiftLeader.department !== teamLeader.department ||
    shiftLeader.site !== teamLeader.site
  ) {
    return false;
  }

  // 2. Verify team leader works in shift leader's shift
  if (teamLeader.shift !== shiftLeader.shift) {
    return false;
  }

  // 3. Verify requested scope is within shift leader's authority
  if (requestedScope.shift && requestedScope.shift !== shiftLeader.shift) {
    return false;
  }

  // 4. Verify team limitations if specified
  if (
    requestedScope.teams &&
    !requestedScope.teams.every((team) => shiftLeader.teams.includes(team))
  ) {
    return false;
  }

  return true;
}
```

#### Best Practice: Delegation Configuration

Configure delegation rules centrally to ensure consistent policy enforcement:

```json
{
  "delegationConfig": {
    "globalRules": {
      "requireExplicitPermission": true,
      "enforceTimeConstraints": true,
      "maxDelegationDuration": "90D",
      "requireScopeValidation": true,
      "allowDelegationChaining": false
    },
    "roleSpecificRules": {
      "SHIFT_LEADER": {
        "allowedDelegationTargets": ["SHIFT_LEADER", "TEAM_LEADER"],
        "maxDownwardDelegationDuration": "30D",
        "requireApprovalForDuration": "14D"
      },
      "TEAM_LEADER": {
        "allowedDelegationTargets": ["TEAM_LEADER"],
        "maxDelegationDuration": "14D",
        "requireSharedShift": true
      }
    }
  }
}
```
