# RBAC (Role-Based Access Control) Microservice Documentation

## Overview

The RBAC microservice provides centralized authentication and authorization management for the Connected Workers platform. It handles user identity, role management, and permission control across different user types and authentication methods.

### Authentication Flow with Azure AD SSO & APIM

```mermaid
sequenceDiagram
    participant U as User
    participant B as Browser
    participant APIM as Azure API Management
    participant AD as Azure AD
    participant Cache as APIM Cache
    participant RBAC as RBAC Service
    participant DB as Database

    U->>B: Access Application
    B->>AD: Redirect to Azure AD Login
    AD->>B: Present Login Form
    U->>AD: Enter Credentials
    AD->>B: SAML Response with Claims
    Note over B,APIM: Claims include email identifier
    B->>APIM: Forward SAML Token

    rect rgb(0, 0, 0)
        Note over APIM,Cache: APIM Policy Execution
        APIM->>APIM: Validate SAML Token
        APIM->>Cache: Check User Profiles Cache
        alt Cache Hit
            Cache-->>APIM: Return Cached Profiles & Active Profile
        else Cache Miss
            APIM->>RBAC: Get User Profiles (email)
            RBAC->>DB: Query User & Profiles
            DB-->>RBAC: User Data & Profiles
            alt First Time User
                RBAC->>DB: Create Default Profile
                RBAC->>DB: Set Default Roles
                RBAC->>DB: Activate Default Profile
            end
            RBAC-->>APIM: Return User Profiles & Active Profile
            APIM->>Cache: Store in Cache (TTL: 15min)
        end
    end

    APIM->>APIM: Generate JWT with Active Profile Context
    APIM-->>B: Return JWT Token
    Note over B: Store JWT for subsequent requests
```

### APIM Caching Policy

```xml
<policies>
    <inbound>
        <!-- Validate SAML Token -->
        <validate-azure-ad-token tenant-id="{{tenant-id}}" />

        <!-- Extract email from claims -->
        <set-variable name="userEmail" value="@(context.Request.Headers.GetValueOrDefault("X-MS-CLIENT-PRINCIPAL-NAME",""))" />

        <!-- Check cache -->
        <cache-lookup-value key="@((string)context.Variables["userEmail"])" variable-name="cachedProfiles" />

        <!-- If cache miss, call RBAC service -->
        <choose>
            <when condition="@(!context.Variables.ContainsKey("cachedProfiles"))">
                <!-- Call RBAC service -->
                <send-request mode="new">
                    <set-url>@(context.Api.ServiceUrl + "/rbac/users/profiles")</set-url>
                    <set-method>GET</set-method>
                    <set-header name="X-User-Email" exists-action="override">
                        <value>@((string)context.Variables["userEmail"])</value>
                    </set-header>
                </send-request>

                <!-- Store in cache -->
                <cache-store-value key="@((string)context.Variables["userEmail"])" value="@(context.Response.Body.As<string>())" duration="900" />
            </when>
        </choose>
    </inbound>
    <backend>
        <forward-request />
    </backend>
    <outbound>
        <!-- Handle cache invalidation for error responses -->
        <choose>
            <when condition="@(context.Response.StatusCode >= 400)">
                <cache-remove-value key="@((string)context.Variables["userEmail"])" />
            </when>
        </choose>

        <!-- Generate JWT with active profile context -->
        <set-header name="Authorization" exists-action="override">
            <value>@{
                var profiles = context.Variables.ContainsKey("cachedProfiles")
                    ? (string)context.Variables["cachedProfiles"]
                    : context.Response.Body.As<string>();

                var activeProfile = GetActiveProfile(profiles);
                return GenerateJWTWithProfile(activeProfile);
            }</value>
        </set-header>
    </outbound>
    <on-error>
        <!-- Remove cache on any errors -->
        <cache-remove-value key="@((string)context.Variables["userEmail"])" />
        <return-response>
            <set-status code="500" reason="Internal Server Error" />
            <set-header name="Content-Type" exists-action="override">
                <value>application/json</value>
            </set-header>
            <set-body>{"error": "Authentication failed", "message": "Please try again later"}</set-body>
        </return-response>
    </on-error>
</policies>
```

### Cache Configuration

```typescript
interface CacheConfig {
  // Cache settings
  TTL: 900; // 15 minutes in seconds
  maxSize: "1GB";
  distributed: true;

  // Cache key format
  keyPattern: "user:profiles:{email}";

  // Invalidation triggers
  invalidateOn: [
    "PROFILE_CHANGE",
    "ROLE_CHANGE",
    "USER_STATUS_CHANGE",
    "DELEGATION_CHANGE",
    "PROFILE_SWITCH",
  ];
}

interface CachedProfileData {
  profiles: UserProfile[];
  activeProfile: {
    id: string;
    roles: ProfileRole[];
    permissions: Permission[];
    restrictions: DelegationRestrictions[];
  };
  lastUpdated: string;
}
```

## 1. User Identity Management

### 1.1 User Types and Authentication Methods

1. **Corporate Users**:

   - Authentication: Azure AD SSO
   - Primary Identifier: Corporate Email (UPN)
   - Use Cases: Management, TKS, Training staff
   - Features: Full system access based on role

2. **Factory Workers**:
   - Authentication: NFC Badge
   - Primary Identifier: Matriculation Number + Site ID
   - Use Cases: Operators, Team Leaders
   - Features: Site-specific access, physical presence validation

### 1.2 User Identity Model

```typescript
interface UserIdentity {
  id: string; // Primary identifier (UUID)
  email?: string; // Corporate email (UPN for Azure AD)
  matriculationNumber?: string; // Employee ID
  siteId?: string; // Factory/Site identifier
  externalId?: string; // External system reference
  sourceSystem?: string; // Identity source (e.g., "WORKDAY", "OPTITIME")
  lastSyncTimestamp?: Date; // Last sync timestamp
  authMethod: AuthenticationMethod;
  status: UserStatus; // Active, Inactive, Suspended
  lastLoginAt?: Date; // Last successful login
  metadata?: Record<string, any>; // Additional user metadata
}

enum AuthenticationMethod {
  AZURE_AD = "AZURE_AD",
  NFC_BADGE = "NFC_BADGE",
}

enum UserStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  SUSPENDED = "SUSPENDED",
}
```

### 1.3 Identity Mapping Strategy

- **Primary Key**: UUID (internal identifier)
- **Lookup Indices**:
  - Email → Corporate Users
  - Matriculation Number + Site ID → Factory Workers
  - External ID → Third-party Integration
  - Source System + External ID → ETL Sync

## 2. Role Management

### 2.1 Role Hierarchy and Structure

```mermaid
graph TD
    SA[Super Admin] --> TKS[TKS Responsible]
    SA --> TR[Training Responsible]
    TKS --> T[Trainer]
    TR --> T
    SA --> SL[Shift Leader]
    SL --> DS[Development Specialist]
    DS --> TL[Team Leader]
    TL --> OP[Operator]
    SA --> QI[Quality Inspector]
    SA --> MT[Maintenance Tech]
```

### 2.2 Role Hierarchy

```typescript
enum SystemRole {
  // System Administration
  SUPER_ADMIN = "SUPER_ADMIN", // Full system access

  // Training Management
  TKS_RESPONSIBLE = "TKS_RESPONSIBLE", // Training coordination
  TRAINING_RESPONSIBLE = "TRAINING_RESPONSIBLE",
  TRAINER = "TRAINER",

  // Production Management
  SHIFT_LEADER = "SHIFT_LEADER",
  DEVELOPMENT_SPECIALIST = "DEVELOPMENT_SPECIALIST",
  TEAM_LEADER = "TEAM_LEADER",
  OPERATOR = "OPERATOR",

  // Specialized Roles
  QUALITY_INSPECTOR = "QUALITY_INSPECTOR",
  MAINTENANCE_TECH = "MAINTENANCE_TECH",
}

interface Role {
  id: string;
  name: string;
  description: string;
  isAutoAssignable: boolean;
  metadata?: Record<string, any>;
  permissions: Permission[];
  createdAt: Date;
  updatedAt: Date;
}
```

### 2.3 Profile-Based Delegation Management

The delegation process follows a streamlined workflow with immediate profile creation and role activation. Below are the state transitions and sequence of operations:

#### 2.3.1 Delegation State Diagram

```mermaid
stateDiagram-v2
    [*] --> Active: Create & Validate
    Active --> Expired: Time Elapsed
    Active --> Revoked: Manual Revoke
    Expired --> [*]
    Revoked --> [*]
```

#### 2.3.2 Delegation Sequence Diagram

```mermaid
sequenceDiagram
    participant D as Delegator
    participant RBAC as RBAC Service
    participant DB as Database
    participant Del as Delegate
    participant Audit as Audit Service

    D->>RBAC: Create Delegation Request
    activate RBAC
    RBAC->>RBAC: Validate Delegator's Rights
    RBAC->>DB: Check Role Hierarchy
    DB-->>RBAC: Confirm Hierarchy

    alt Valid Request
        RBAC->>DB: Create Active Delegation
        RBAC->>DB: Create Active Profile
        RBAC->>DB: Add Role to Profile
        RBAC->>Audit: Log Delegation Creation
        RBAC-->>D: Return Success Response
        RBAC->>Del: Send Notification
    else Invalid Request
        RBAC-->>D: Return Error
        RBAC->>Audit: Log Failed Attempt
    end
    deactivate RBAC
```

#### 2.3.3 Delegation Process

1. **Immediate Delegation Creation**

   - Delegator submits delegation request
   - System validates delegator's rights and role hierarchy
   - Upon validation, delegation is created in ACTIVE state

2. **Automatic Profile Creation**

   - System automatically creates and activates delegate's profile
   - Delegated role is immediately added to the profile
   - Profile is ready for immediate use

3. **Delegation Management**
   - Active delegations can be:
     - Revoked manually by the delegator
     - Expired automatically based on time constraints
   - System maintains audit trail of all actions

#### 2.3.4 Profile and Delegation Models

```typescript
interface UserProfile {
  id: string;
  userId: string;
  name: string;
  type: ProfileType;
  roles: ProfileRole[];
  isActive: boolean;
  validFrom?: Date;
  validUntil?: Date;
  metadata?: Record<string, any>;
}

interface ProfileRole {
  roleId: string;
  source: RoleSource;
  delegationId?: string;
  restrictions?: DelegationRestrictions;
}

interface RoleDelegation {
  id: string;
  delegatorId: string;
  delegateId: string;
  roleId: string;
  profileId: string; // Required: Links delegation to a specific profile
  validFrom: Date;
  validUntil: Date;
  restrictions: DelegationRestrictions;
  status: DelegationStatus;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

interface DelegationRestrictions {
  scope: {
    sites?: string[];
    teams?: string[];
    shifts?: string[];
  };
  operations: {
    included: string[];
    excluded: string[];
  };
  limits: {
    maxDailyOperations?: number;
    maxConcurrentSessions?: number;
  };
}

enum DelegationStatus {
  DRAFT = "DRAFT", // Initial state when delegation is being created
  ACTIVE = "ACTIVE", // Delegation is active
  EXPIRED = "EXPIRED", // Delegation has expired
  REVOKED = "REVOKED", // Delegation was manually revoked
}

enum ProfileType {
  DIRECT = "DIRECT",
  DELEGATED = "DELEGATED",
}

enum RoleSource {
  ASSIGNED = "ASSIGNED",
  DELEGATED = "DELEGATED",
}
```

## Profile-Based Delegation Process

The delegation process follows these steps:

1. A user (delegator) with delegation rights creates a delegation request by calling POST /api/rbac/delegations with:

   - Required: delegateId, roleId, validFrom, validUntil, restrictions
   - Optional: profileName (custom name for auto-created profile), metadata

2. The system immediately:

   - Validates the delegator's rights
   - Creates an active delegation
   - Creates and activates a new profile for the delegate
   - Adds the delegated role to the profile
   - Notifies the delegate of their new active profile

3. The delegate receives a notification containing:

   - Active delegation details (role, restrictions, validity period)
   - New profile information
   - Instructions for using the profile

4. The delegate can switch between profiles as needed:

   - Only one profile can be active at a time
   - Profile switching updates the user's effective permissions

5. The delegation and profile automatically expire at the end of validity period

Key features:

- Immediate delegation activation
- Automatic profile creation and activation
- No approval steps required
- Profile-based delegation management
- Direct delegation from delegator to delegate (no chaining)
- Time-bound delegations with automatic expiration
- Granular scope and operation restrictions
- Resource usage limits for delegated roles
- Comprehensive audit logging
- Real-time permission validation
- Profile-based role activation

Delegation Rules:

1. A user can only delegate roles they currently possess
2. Only direct delegation is allowed (no delegation of delegated roles)
3. Each delegation automatically creates an active profile
4. A profile can contain multiple delegated roles from different delegators
5. Only one active delegation per role is allowed for a delegate
6. Delegations must have explicit time boundaries
7. Delegations and profiles are immediately active upon creation
8. A user can have multiple profiles but only one active profile at a time
9. Profile names must be unique per user
10. Delegated profiles are automatically deactivated upon expiration or revocation

## 3. Permissions Framework

### 3.1 Permission Model

```typescript
enum PermissionScope {
  GLOBAL = "GLOBAL", // System-wide permissions
  SITE = "SITE", // Factory-specific permissions
  TEAM = "TEAM", // Team-level permissions
  SHIFT = "SHIFT", // Shift-specific permissions
}

interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string; // Target resource (e.g., "user", "training")
  action: string; // Allowed action (e.g., "create", "read")
  scope: PermissionScope; // Access scope level
}
```

### 3.2 Core Permission Sets

```typescript
const CorePermissions = {
  USER_MANAGEMENT: {
    CREATE_USER: {
      name: "user:create",
      scope: PermissionScope.SITE,
    },
    UPDATE_USER: {
      name: "user:update",
      scope: PermissionScope.SITE,
    },
    DELETE_USER: {
      name: "user:delete",
      scope: PermissionScope.GLOBAL,
    },
    VIEW_USER: {
      name: "user:view",
      scope: PermissionScope.TEAM,
    },
    MANAGE_ROLES: {
      name: "user:manage:roles",
      scope: PermissionScope.GLOBAL,
    },
  },
  TRAINING: {
    CREATE_TRAINING: {
      name: "training:create",
      scope: PermissionScope.SITE,
    },
    ASSIGN_TRAINING: {
      name: "training:assign",
      scope: PermissionScope.TEAM,
    },
    COMPLETE_TRAINING: {
      name: "training:complete",
      scope: PermissionScope.TEAM,
    },
    VIEW_TRAINING: {
      name: "training:view",
      scope: PermissionScope.TEAM,
    },
    EVALUATE_TRAINING: {
      name: "training:evaluate",
      scope: PermissionScope.SITE,
    },
  },
  WORKFLOW: {
    CREATE_WORKFLOW: {
      name: "workflow:create",
      scope: PermissionScope.SITE,
    },
    EXECUTE_WORKFLOW: {
      name: "workflow:execute",
      scope: PermissionScope.SHIFT,
    },
    MONITOR_WORKFLOW: {
      name: "workflow:monitor",
      scope: PermissionScope.TEAM,
    },
  },
  REPORTING: {
    VIEW_REPORTS: {
      name: "reports:view",
      scope: PermissionScope.SITE,
    },
    GENERATE_REPORTS: {
      name: "reports:generate",
      scope: PermissionScope.SITE,
    },
    EXPORT_DATA: {
      name: "data:export",
      scope: PermissionScope.GLOBAL,
    },
  },
};
```

## 4. Database Schema

### 4.1 Class Diagram

```mermaid
classDiagram
    class Users {
        +UUID id
        +VARCHAR(255) email
        +VARCHAR(50) matriculation_number
        +VARCHAR(50) site_id
        +VARCHAR(255) external_id
        +VARCHAR(50) source_system
        +VARCHAR(20) auth_method
        +VARCHAR(20) status
        +TIMESTAMP last_login_at
        +TIMESTAMP last_sync_timestamp
        +JSONB metadata
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
        +validateEmail() bool
        +validateMatriculation() bool
        +getActiveProfile()
        +getDelegations()
    }

    class Roles {
        +UUID id
        +VARCHAR(50) name
        +TEXT description
        +BOOLEAN is_auto_assignable
        +JSONB metadata
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
        +validateName() bool
        +checkPermissions() bool
        +validateDelegation()
    }

    class Permissions {
        +UUID id
        +VARCHAR(100) name
        +TEXT description
        +VARCHAR(50) resource
        +VARCHAR(50) action
        +VARCHAR(20) scope
        +TIMESTAMP created_at
        +validateScope() bool
        +checkResourceAction() bool
    }

    class RolePermissions {
        +UUID role_id
        +UUID permission_id
        +VARCHAR(20) scope_override
        +JSONB conditions
        +TIMESTAMP created_at
        +validateScopeOverride() bool
    }

    class UserRoles {
        +UUID user_id
        +UUID role_id
        +TIMESTAMP valid_from
        +TIMESTAMP valid_until
        +BOOLEAN is_temporary
        +VARCHAR(50) source_system
        +VARCHAR(255) sync_identifier
        +JSONB metadata
        +TIMESTAMP created_at
        +checkValidity() bool
    }

    class RoleDelegations {
        +UUID id
        +UUID delegator_id
        +UUID delegate_id
        +UUID role_id
        +UUID profile_id
        +TIMESTAMP valid_from
        +TIMESTAMP valid_until
        +JSONB restrictions
        +VARCHAR(20) status
        +JSONB metadata
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
        +validate()
        +checkScope()
        +validateProfile()
    }

    class UserProfiles {
        +UUID id
        +UUID user_id
        +VARCHAR(100) name
        +VARCHAR(20) type
        +BOOLEAN active
        +TIMESTAMP valid_from
        +TIMESTAMP valid_until
        +JSONB metadata
        +TIMESTAMP created_at
        +TIMESTAMP updated_at
        +activate()
        +validateExpiry()
        +validateDelegations()
    }

    class ProfileRoles {
        +UUID profile_id
        +UUID role_id
        +VARCHAR(20) source
        +UUID delegation_id
        +JSONB restrictions
        +TIMESTAMP created_at
        +getEffectivePermissions()
        +validateDelegation()
    }

    Users "1" -- "0..*" UserRoles : has
    Roles "1" -- "0..*" UserRoles : assigned to
    Roles "1" -- "0..*" RolePermissions : has
    Permissions "1" -- "0..*" RolePermissions : assigned to
    Users "1" -- "0..*" RoleDelegations : delegates
    Users "1" -- "0..*" UserProfiles : maintains
    UserProfiles "1" -- "0..*" ProfileRoles : contains
    Roles "1" -- "0..*" ProfileRoles : assigned
    RoleDelegations "1" -- "1" ProfileRoles : grants
```

### 4.2 Database Design Best Practices

1. **Primary Keys**

   - Use UUID for all primary keys to ensure global uniqueness
   - Enables easier data migration and distributed systems
   - Prevents sequential ID enumeration attacks

2. **Foreign Keys**

   - All relationships are properly constrained with foreign keys
   - ON DELETE CASCADE for dependent records (e.g., user_roles when user is deleted)
   - ON DELETE RESTRICT for critical relationships

3. **Indexing Strategy**

   ```sql
   -- Users table indexes
   CREATE INDEX idx_users_email ON users(email) WHERE email IS NOT NULL;
   CREATE INDEX idx_users_matriculation ON users(matriculation_number, site_id);
   CREATE INDEX idx_users_external ON users(external_id, source_system);

   -- Roles table indexes
   CREATE INDEX idx_roles_name ON roles(name);

   -- Permissions table indexes
   CREATE INDEX idx_permissions_resource_action ON permissions(resource, action);
   CREATE INDEX idx_permissions_scope ON permissions(scope);

   -- UserRoles table indexes
   CREATE INDEX idx_user_roles_validity ON user_roles(valid_from, valid_until);
   CREATE INDEX idx_user_roles_user ON user_roles(user_id);
   CREATE INDEX idx_user_roles_role ON user_roles(role_id);
   ```

4. **Data Types Best Practices**

   - Use `VARCHAR` with appropriate length limits
   - `TIMESTAMP` for all date/time fields (with timezone awareness)
   - `JSONB` for flexible metadata storage
   - `UUID` for all IDs
   - `BOOLEAN` for flag fields
   - `TEXT` for unlimited length strings

5. **Constraints**

   ```sql
   -- Example constraints
   ALTER TABLE users ADD CONSTRAINT chk_auth_method
       CHECK (auth_method IN ('AZURE_AD', 'NFC_BADGE'));

   ALTER TABLE users ADD CONSTRAINT chk_status
       CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'));

   ALTER TABLE user_roles ADD CONSTRAINT chk_validity_dates
       CHECK (valid_from < valid_until);

   ALTER TABLE permissions ADD CONSTRAINT chk_scope
       CHECK (scope IN ('GLOBAL', 'SITE', 'TEAM', 'SHIFT'));
   ```

### 4.3 Entity Relationship Diagram

```mermaid
erDiagram
    USERS ||--o{ USER_ROLES : has
    ROLES ||--o{ USER_ROLES : assigned_to
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : assigned_to
    AUDIT_LOGS ||--o{ USERS : references

    USERS {
        uuid id PK
        string email UK
        string matriculation_number
        string site_id
        string status
    }

    ROLES {
        uuid id PK
        string name UK
        text description
        boolean is_auto_assignable
    }

    PERMISSIONS {
        uuid id PK
        string name UK
        string resource
        string action
        string scope
    }
```

### 4.4 Core Tables

```sql
-- Users table with enhanced tracking
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) NULL,
    matriculation_number VARCHAR(50),
    site_id VARCHAR(50),
    external_id VARCHAR(255),
    source_system VARCHAR(50),
    auth_method VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    last_login_at TIMESTAMP,
    last_sync_timestamp TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(email),
    UNIQUE(matriculation_number, site_id)
);

-- Enhanced roles table
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(50) UNIQUE,
    description TEXT,
    is_auto_assignable BOOLEAN DEFAULT false,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permissions with conditions and scope
CREATE TABLE permissions (
    id UUID PRIMARY KEY,
    name VARCHAR(100) UNIQUE,
    description TEXT,
    resource VARCHAR(50),
    action VARCHAR(50),
    scope VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Role-Permission mapping with validity and scope override
CREATE TABLE role_permissions (
    role_id UUID REFERENCES roles(id),
    permission_id UUID REFERENCES permissions(id),
    scope_override VARCHAR(20),
    conditions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id)
);

-- User-Role mapping with enhanced tracking
CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id),
    role_id UUID REFERENCES roles(id),
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    is_temporary BOOLEAN,
    source_system VARCHAR(50),
    sync_identifier VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

-- Role Delegations table
CREATE TABLE role_delegations (
    id UUID PRIMARY KEY,
    delegator_id UUID REFERENCES users(id),
    delegate_id UUID REFERENCES users(id),
    role_id UUID REFERENCES roles(id),
    profile_id UUID NULL, -- Will be set immediately when profile is created
    valid_from TIMESTAMP NOT NULL,
    valid_until TIMESTAMP NOT NULL,
    restrictions JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(20) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_delegation_dates CHECK (valid_from < valid_until),
    CONSTRAINT chk_delegation_status CHECK (status IN ('DRAFT', 'ACTIVE', 'EXPIRED', 'REVOKED')),
    CONSTRAINT unique_active_delegation UNIQUE (delegate_id, role_id) WHERE status = 'ACTIVE'
);

-- Updated trigger for immediate profile creation and activation
CREATE OR REPLACE FUNCTION create_delegation_profile()
RETURNS TRIGGER AS $$
DECLARE
    role_name VARCHAR;
    profile_id UUID;
BEGIN
    -- Create profile immediately when delegation is created
    IF NEW.profile_id IS NULL THEN
        -- Get role name for default profile name
        SELECT name INTO role_name FROM roles WHERE id = NEW.role_id;

        -- Create new profile (active by default)
        INSERT INTO user_profiles (
            id,
            user_id,
            name,
            type,
            is_active,
            valid_from,
            valid_until,
            metadata
        ) VALUES (
            gen_random_uuid(),
            NEW.delegate_id,
            COALESCE(NEW.metadata->>'profileName', 'Delegated ' || role_name || ' Profile'),
            'DELEGATED',
            true, -- Profile is immediately active
            NEW.valid_from,
            NEW.valid_until,
            jsonb_build_object(
                'delegationId', NEW.id,
                'delegatorId', NEW.delegator_id,
                'createdAt', CURRENT_TIMESTAMP
            ) || COALESCE(NEW.metadata, '{}'::jsonb)
        ) RETURNING id INTO profile_id;

        -- Update delegation with created profile_id and set to ACTIVE
        NEW.profile_id := profile_id;
        NEW.status := 'ACTIVE';

        -- Immediately add role to profile
        INSERT INTO profile_roles (
            profile_id,
            role_id,
            source,
            delegation_id,
            restrictions
        ) VALUES (
            profile_id,
            NEW.role_id,
            'DELEGATED',
            NEW.id,
            NEW.restrictions
        );
    END IF;

    -- Handle status changes
    IF TG_OP = 'UPDATE' THEN
        -- When delegation is revoked or expired, deactivate profile
        IF NEW.status IN ('REVOKED', 'EXPIRED') AND OLD.status = 'ACTIVE' THEN
            UPDATE user_profiles
            SET is_active = false,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = NEW.profile_id;

            -- Remove role from profile
            DELETE FROM profile_roles
            WHERE profile_id = NEW.profile_id AND delegation_id = NEW.id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_create_delegation_profile
    BEFORE INSERT OR UPDATE ON role_delegations
    FOR EACH ROW
    EXECUTE FUNCTION create_delegation_profile();

-- User Profiles table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT false,
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_profile_type CHECK (type IN ('DIRECT', 'DELEGATED')),
    CONSTRAINT chk_profile_dates CHECK (valid_from < valid_until),
    CONSTRAINT unique_active_profile UNIQUE (user_id) WHERE is_active = true
);

-- Profile Roles table
CREATE TABLE profile_roles (
    profile_id UUID REFERENCES user_profiles(id),
    role_id UUID REFERENCES roles(id),
    source VARCHAR(20) NOT NULL,
    delegation_id UUID REFERENCES role_delegations(id),
    restrictions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (profile_id, role_id),
    CONSTRAINT chk_role_source CHECK (source IN ('ASSIGNED', 'DELEGATED'))
);

-- Add indexes for performance
CREATE INDEX idx_delegations_profile ON role_delegations(profile_id);
CREATE INDEX idx_delegations_delegator ON role_delegations(delegator_id);
CREATE INDEX idx_delegations_delegate ON role_delegations(delegate_id);
CREATE INDEX idx_delegations_status ON role_delegations(status);
CREATE INDEX idx_delegations_validity ON role_delegations(valid_from, valid_until);
CREATE INDEX idx_profiles_user ON user_profiles(user_id);
CREATE INDEX idx_profiles_active ON user_profiles(is_active);
CREATE INDEX idx_profile_roles_delegation ON profile_roles(delegation_id);
CREATE INDEX idx_delegations_status_dates ON role_delegations(status, valid_from, valid_until);
CREATE INDEX idx_active_profiles ON user_profiles(user_id) WHERE is_active = true;
CREATE INDEX idx_profile_roles_source ON profile_roles(source, delegation_id) WHERE source = 'DELEGATED';
```

## 5. API Endpoints

### 5.1 Permission Guards

```typescript
// Permission guard implementation
@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private readonly rbacService: RBACService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const requiredPermissions = this.getRequiredPermissions(context);
    const user = request.user;

    if (!requiredPermissions || !user) {
      return false;
    }

    return await this.validatePermissions(
      user,
      requiredPermissions,
      request.params
    );
  }

  private getRequiredPermissions(
    context: ExecutionContext
  ): RequiredPermission[] {
    const handler = context.getHandler();
    return Reflect.getMetadata("required_permissions", handler) || [];
  }

  private async validatePermissions(
    user: UserIdentity,
    permissions: RequiredPermission[],
    params: any
  ): Promise<boolean> {
    for (const permission of permissions) {
      const hasPermission = await this.rbacService.validatePermission(
        user,
        permission.name
      );

      if (!hasPermission) return false;

      if (
        permission.validateContext &&
        !permission.validateContext(user, params)
      ) {
        return false;
      }
    }
    return true;
  }
}

// Permission decorator
export interface RequiredPermission {
  name: string;
  scope: PermissionScope;
  validateContext?: (user: UserIdentity, params: any) => boolean;
}

export const RequiredPermissions = (permissions: RequiredPermission[]) =>
  SetMetadata("required_permissions", permissions);
```

### 5.2 Core APIs

```typescript
@Controller("rbac")
export class RBACController {
  constructor(private readonly rbacService: RBACService) {}

  // User Management
  @Post("users")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "user:create",
      scope: PermissionScope.SITE,
      validateContext: (user, params) => user.siteId === params.siteId,
    },
  ])
  createUser(@Body() user: CreateUserDto): Promise<UserIdentity> {
    // Implementation
  }

  @Get("users/:id")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "user:view",
      scope: PermissionScope.TEAM,
      validateContext: (user, params) => user.teamId === params.teamId,
    },
  ])
  getUser(@Param("id") id: string): Promise<UserIdentity> {
    // Implementation
  }

  @Put("users/:id")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "user:update",
      scope: PermissionScope.SITE,
      validateContext: (user, params) => user.siteId === params.siteId,
    },
  ])
  updateUser(
    @Param("id") id: string,
    @Body() updates: UpdateUserDto
  ): Promise<UserIdentity> {
    // Implementation
  }

  // Role Management
  @Post("roles")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "role:create",
      scope: PermissionScope.GLOBAL,
    },
  ])
  createRole(@Body() role: CreateRoleDto): Promise<Role> {
    // Implementation
  }

  @Get("roles/:id/permissions")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "role:view",
      scope: PermissionScope.GLOBAL,
    },
  ])
  getRolePermissions(@Param("id") roleId: string): Promise<Permission[]> {
    // Implementation
  }

  // User-Role Management
  @Post("users/:userId/roles")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "user:manage:roles",
      scope: PermissionScope.GLOBAL,
    },
  ])
  assignRole(
    @Param("userId") userId: string,
    @Body() assignment: RoleAssignmentDto
  ): Promise<void> {
    // Implementation
  }

  @Delete("users/:userId/roles/:roleId")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "user:manage:roles",
      scope: PermissionScope.GLOBAL,
    },
  ])
  removeRole(
    @Param("userId") userId: string,
    @Param("roleId") roleId: string
  ): Promise<void> {
    // Implementation
  }

  // Permission Validation
  @Post("permissions/validate")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "permission:validate",
      scope: PermissionScope.GLOBAL,
    },
  ])
  validatePermission(
    @Body() validation: ValidatePermissionDto
  ): Promise<ValidationResponse> {
    // Implementation
  }

  // Delegation Management
  @Post("delegations")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "role:delegate",
      scope: PermissionScope.GLOBAL,
      validateContext: (user, params) => this.canDelegateRole(user, params),
    },
  ])
  createDelegation(
    @Body() delegation: CreateDelegationDto
  ): Promise<DelegationResponse> {
    return this.delegationService.createDelegation(delegation);
  }

  @Put("delegations/:id/status")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "delegation:manage",
      scope: PermissionScope.GLOBAL,
    },
  ])
  updateDelegationStatus(
    @Param("id") id: string,
    @Body() update: UpdateDelegationStatusDto
  ): Promise<RoleDelegation> {
    return this.delegationService.updateStatus(id, update.status);
  }

  @Get("delegations/:id")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "delegation:view",
      scope: PermissionScope.GLOBAL,
    },
  ])
  getDelegation(@Param("id") id: string): Promise<RoleDelegation> {
    return this.delegationService.getDelegation(id);
  }

  // Profile Management
  @Post("users/:userId/profiles")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "profile:create",
      scope: PermissionScope.GLOBAL,
    },
  ])
  createProfile(
    @Param("userId") userId: string,
    @Body() profile: CreateProfileDto
  ): Promise<UserProfile> {
    return this.profileService.createProfile(userId, profile);
  }

  @Put("users/:userId/profiles/:profileId/activate")
  @UseGuards(PermissionGuard)
  @RequiredPermissions([
    {
      name: "profile:activate",
      scope: PermissionScope.GLOBAL,
    },
  ])
  activateProfile(
    @Param("userId") userId: string,
    @Param("profileId") profileId: string
  ): Promise<void> {
    return this.profileService.activateProfile(userId, profileId);
  }
}

// Updated DTOs
interface CreateUserDto {
  email?: string;
  matriculationNumber?: string;
  siteId?: string;
  teamId?: string;
  authMethod: AuthenticationMethod;
  externalId?: string;
  sourceSystem?: string;
  metadata?: Record<string, any>;
}

interface UpdateUserDto extends Partial<CreateUserDto> {
  status?: UserStatus;
}

interface RoleAssignmentDto {
  roleId: string;
  validFrom?: Date;
  validUntil?: Date;
  isTemporary?: boolean;
  metadata?: Record<string, any>;
}

interface ValidatePermissionDto {
  userId: string;
  permission: string;
  resource?: string;
  context?: Record<string, any>;
}

interface CreateDelegationDto {
  delegateId: string;
  roleId: string;
  validFrom: Date;
  validUntil: Date;
  profileName?: string; // Optional custom name for auto-created profile
  restrictions: {
    scope?: {
      sites?: string[];
      teams?: string[];
      shifts?: string[];
    };
    operations?: {
      included?: string[];
      excluded?: string[];
    };
    limits?: {
      maxDailyOperations?: number;
      maxConcurrentSessions?: number;
    };
  };
  metadata?: Record<string, any>;
}

interface UpdateDelegationStatusDto {
  status: DelegationStatus; // Only allows REVOKED status for manual revocation
  reason?: string;
}

enum DelegationStatus {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  EXPIRED = "EXPIRED",
  REVOKED = "REVOKED",
}

interface CreateProfileDto {
  name: string;
  type: ProfileType;
  roles: ProfileRoleDto[];
  validFrom?: Date;
  validUntil?: Date;
  metadata?: Record<string, any>;
}

interface ProfileRoleDto {
  roleId: string;
  source: RoleSource;
  delegationId?: string;
  restrictions?: DelegationRestrictions;
}

interface DelegationResponse {
  delegation: RoleDelegation;
  profile: UserProfile; // Include the automatically created profile in response
}
```

## 6. Security Implementation

### 6.1 Authentication Security

1. **Azure AD Integration**:

   - OAuth 2.0 / OpenID Connect flow
   - JWT validation with Azure public keys
   - Automatic token refresh
   - MFA enforcement for sensitive operations

2. **NFC Badge Security**:
   - Cryptographic badge validation
   - Anti-replay protection
   - Physical presence verification
   - Rate limiting per badge/site

### 6.2 Authorization Security

1. **Permission Evaluation**:

   - Real-time permission validation
   - Condition-based access control
   - Temporal role validation
   - Site-specific access enforcement

2. **Security Headers**:
   - CORS configuration
   - CSP implementation
   - Rate limiting
   - Request validation

## 7. Integration Guidelines

### 7.1 External System Integration

1. **Identity Synchronization**:

   - Batch user import/export
   - Real-time user updates
   - Role mapping configuration
   - Conflict resolution strategy

2. **API Integration**:
   - OAuth2 client credentials
   - API key management
   - Rate limiting policies
   - Webhook notifications

## 8. Deployment & Operations

### 8.1 Configuration Management

1. **Environment Configuration**:

   - Environment-specific settings
   - Feature flags
   - Security policies
   - Integration endpoints

2. **Operational Procedures**:
   - Backup procedures
   - Recovery processes
   - Scaling guidelines
   - Monitoring setup

### 8.2 Performance Optimization

1. **Caching Strategy**:

   - Permission cache
   - User profile cache
   - Token validation cache
   - Role hierarchy cache

2. **Database Optimization**:
   - Index optimization
   - Query performance
   - Connection pooling
   - Replication setup
