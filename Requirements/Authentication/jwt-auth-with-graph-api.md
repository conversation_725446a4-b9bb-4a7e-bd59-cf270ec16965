# JWT Authentication with JIT Provisioning for Azure AD

## Document Information

**Version:** 1.0.0  
**Last Updated:** 2025-02-26  
**Status:** Pending

## Executive Summary

This documentation describes a comprehensive authentication architecture that combines SAML-based Single Sign-On (SSO) with Just-In-Time (JIT) user provisioning through Microsoft Graph API. The solution uses JSON Web Tokens (JWT) for ongoing application authentication while maintaining continuous synchronization with Microsoft Entra ID (formerly Azure AD).

The architecture addresses several key requirements:

1. **Seamless Authentication**: Users authenticate once via SAML and maintain sessions through token refresh
2. **Up-to-date User Data**: User profiles remain synchronized with Entra ID during token refresh
3. **Reduced Authentication Overhead**: Minimizes redirects to identity providers
4. **Role-based Access Control**: Automatically maps Azure AD group memberships to application roles
5. **Enterprise-grade Security**: Implements secure token handling practices

This approach is particularly suitable for enterprise applications requiring:

- Centralized user management
- Real-time reflection of directory changes
- Scalable authentication without compromising user experience
- Integrated access management across complex organizational structures

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture Overview](#2-architecture-overview)
   - [Authentication Flow Diagram](#21-authentication-flow-diagram)
3. [SAML to JWT Conversion](#3-saml-to-jwt-conversion)
   - [Initial Authentication Process](#31-initial-authentication-process)
4. [Token Structure and Management](#4-token-structure-and-management)
   - [JWT Token Types](#41-jwt-token-types)
   - [Token Storage Strategy](#42-token-storage-strategy)
5. [Token Refresh Implementation with User Synchronization](#5-token-refresh-implementation-with-user-synchronization)
   - [Refresh Process](#51-refresh-process)
     - [Detecting Expiration](#511-detecting-expiration)
     - [Refresh Mechanism with User Data Synchronization](#512-refresh-mechanism-with-user-data-synchronization)
     - [Handling Failures](#513-handling-failures)
6. [Just-In-Time Provisioning with Microsoft Graph API](#6-just-in-time-provisioning-with-microsoft-graph-api)
   - [Core JIT Process Flow](#61-core-jit-process-flow)
   - [Microsoft Graph API Integration](#62-microsoft-graph-api-integration)
     - [User Data Retrieval](#621-user-data-retrieval)
     - [Group Membership for Role Assignment](#622-group-membership-for-role-assignment)
   - [Advanced Features](#63-advanced-features)
7. [Implementation Considerations](#7-implementation-considerations)
   - [Service Account Requirements](#71-service-account-requirements)
   - [Performance Optimization](#72-performance-optimization)
   - [Security Best Practices](#73-security-best-practices)
8. [Error Handling and Resilience](#8-error-handling-and-resilience)
   - [Common Error Scenarios](#81-common-error-scenarios)
   - [Logging and Monitoring](#82-logging-and-monitoring)
9. [Conclusion](#9-conclusion)
10. [References](#10-references)
11. [Appendix](#11-appendix)
    - [Implementation Checklist](#111-implementation-checklist)
    - [Troubleshooting Guide](#112-troubleshooting-guide)
    - [Performance Optimization Guidelines](#113-performance-optimization-guidelines)

## 1. Overview

This document provides a comprehensive guide for implementing stateless JWT authentication with Just-In-Time (JIT) user provisioning for Microsoft Entra ID (formerly Azure AD). This approach leverages SAML SSO for initial authentication and Microsoft Graph API for enhanced user provisioning and role management.

**Key Components:**

- Initial authentication via SAML SSO with Microsoft Entra ID
- Conversion of SAML claims to stateless JWT (access and refresh tokens)
- Automatic token refresh with seamless user experience
- Just-In-Time (JIT) user provisioning from Azure AD via Microsoft Graph API
- Continuous user profile synchronization during token refresh
- Role mapping between Azure AD groups and application roles

**Core Benefits:**

- **Improved User Experience**: Users remain authenticated without interruption
- **Reduced Authentication Overhead**: Minimizes authentication requests to Azure AD
- **Real-Time User Data**: Always synchronized with authoritative source (Azure AD)
- **Enhanced Security**: Secure token handling with proper storage strategies
- **Scalability**: Stateless architecture supports horizontal scaling
- **Centralized Identity Management**: Single source of truth for user data

## 2. Architecture Overview

### 2.1. Authentication Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant App as Application Frontend
    participant API as API Backend
    participant AAD as Azure AD
    participant Graph as Microsoft Graph
    participant DB as Database

    %% Initial Authentication
    User->>App: 1. Access Application
    App->>AAD: 2. SAML Authentication Request
    User->>AAD: 3. Login (if no session)
    AAD->>App: 4. SAML Response with Claims
    App->>API: 5. Exchange SAML for JWT
    API->>DB: 6. Check if User Exists

    alt 7a. User Does Not Exist (First Login)
        API->>Graph: 8. Fetch User Details & Group Membership
        Graph->>API: 9. Complete User Profile
        API->>DB: 10. Create User (JIT Provisioning)
    else 7b. User Exists
        API->>DB: 11. Update Latest Login Timestamp
    end

    API->>App: 12. Issue Access & Refresh Tokens
    App->>User: 13. Authenticated Session Established

    %% Token Refresh
    Note over User,App: Later: Access Token Expiring
    App->>API: 14. Request New Tokens (with Refresh Token)
    API->>DB: 15. Validate Refresh Token

    alt 16a. Token Valid
        API->>Graph: 17. Fetch Latest User Data
        Graph->>API: 18. Updated User Profile & Group Membership
        API->>DB: 19. Synchronize User Data in DB
        API->>App: 20. Issue New Access & Refresh Tokens
    else 16b. Token Invalid
        API->>App: 21. Authentication Error
        App->>AAD: 22. Redirect to Login
    end
```

## 3. SAML to JWT Conversion

### 3.1. Initial Authentication Process

1. **SAML Authentication**

   - User initiates login through your application
   - Application redirects to Microsoft Entra ID for SAML authentication
   - User authenticates with their credentials
   - Entra ID returns a signed SAML assertion with user claims

2. **SAML to JWT Conversion**

   - Application backend receives the SAML assertion
   - Validates the SAML signature and claims
   - Extracts core user identity (email, name, ID)
   - Generates initial JWT tokens (access and refresh)

3. **Implementation Example**

   ```javascript
   // Example backend code for SAML to JWT conversion
   app.post('/api/auth/saml', (req, res) => {
     try {
       // Parse and validate SAML response
       const samlResponse = parseSamlResponse(req.body.SAMLResponse);

       // Extract user information from SAML claims
       const userEmail = samlResponse.claims.nameID;
       const userDisplayName = samlResponse.claims.displayName;

       // Check if user exists in database
       let user = await userRepository.findByEmail(userEmail);

       if (!user) {
         // User doesn't exist - fetch complete profile from Graph API
         const graphApiClient = createGraphApiClient();
         const userDetails = await graphApiClient.getUserByEmail(userEmail);
         const userGroups = await graphApiClient.getUserGroups(userDetails.id);

         // Create user in database with information from Graph API
         user = await userRepository.createUser({
           email: userEmail,
           displayName: userDetails.displayName,
           givenName: userDetails.givenName,
           surname: userDetails.surname,
           department: userDetails.department,
           jobTitle: userDetails.jobTitle,
           // Map groups to roles
           roles: mapGroupsToRoles(userGroups)
         });
       }

       // Generate JWT tokens
       const tokens = generateTokens(user);

       // Set tokens in cookies/response
       res.cookie('refresh_token', tokens.refreshToken, {
         httpOnly: true,
         secure: true,
         sameSite: 'strict'
       });

       res.json({ accessToken: tokens.accessToken });
     } catch (error) {
       // Handle errors
       res.status(401).json({ error: 'Authentication failed' });
     }
   });
   ```

## 4. Token Structure and Management

### 4.1. JWT Token Types

1. **Access Token**

   - Short-lived (15-60 minutes)
   - Contains essential user data and permissions
   - Used for API authorization
   - Example structure:
     ```json
     {
       "sub": "<EMAIL>",
       "name": "John Doe",
       "given_name": "John",
       "family_name": "Doe",
       "email": "<EMAIL>",
       "roles": ["User", "ProjectManager"],
       "department": "Engineering",
       "location": "Paris",
       "employeeId": "EMP123456",
       "iat": 1675091348,
       "exp": 1675094948,
       "aud": "your-api-identifier",
       "iss": "your-app-authority"
     }
     ```

2. **Refresh Token**
   - Longer-lived (hours to days)
   - Used solely to obtain new access tokens
   - Contains minimal claims (primarily user identifier)
   - Secured with appropriate storage methods
   - Example structure (typically encrypted in production):
     ```json
     {
       "sub": "<EMAIL>",
       "jti": "unique-token-id-12345",
       "iat": 1675091348,
       "exp": 1675177748, // 24 hours later
       "iss": "your-app-authority"
     }
     ```

### 4.2. Token Storage Strategy

For maximum security:

- **Access Token**: Store in memory variables (never in localStorage)
- **Refresh Token**: Store in HTTP-only, secure cookies with appropriate SameSite settings
- **Token Status**: Maintain a small client-side timestamp to detect token expiration

```javascript
// Frontend code example for secure token storage
const saveTokens = (accessToken) => {
  // Store access token in memory only (not in localStorage)
  authState.accessToken = accessToken;
  authState.accessTokenExpiry = getTokenExpiry(accessToken);

  // Note: Refresh token is automatically stored in HTTP-only cookie
  // by the backend and not accessible via JavaScript
};
```

## 5. Token Refresh Implementation with User Synchronization

### 5.1. Refresh Process

#### 5.1.1. Detecting Expiration

- Client monitors access token expiration time
- Initiates refresh before token expires (e.g., at 75% of lifetime)
- Prevents interruption in user experience

```javascript
// Frontend code - check if token needs refresh
const needsRefresh = () => {
  if (!authState.accessToken) return false;

  // Get current time
  const now = Math.floor(Date.now() / 1000);

  // Calculate 75% of token lifetime
  const bufferTime =
    (authState.accessTokenExpiry - authState.accessTokenIssued) * 0.25;

  // Return true if we're in the last 25% of token lifetime
  return now >= authState.accessTokenExpiry - bufferTime;
};
```

#### 5.1.2. Refresh Mechanism with User Data Synchronization

- Uses a dedicated endpoint (e.g., `/api/auth/refresh`)
- Sends the refresh token (automatically included in cookie)
- Server validates refresh token and retrieves latest user data from Graph API
- Synchronizes user data in database with latest information from Entra ID
- Generates new token pair containing up-to-date user information
- Client updates stored access token

```javascript
// Backend code for token refresh with user data synchronization
app.post("/api/auth/refresh", async (req, res) => {
  try {
    // Get refresh token from cookie
    const refreshToken = req.cookies.refresh_token;

    // Validate refresh token
    const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET);

    // Get user from database
    const user = await userRepository.findByEmail(decoded.sub);

    if (!user) {
      throw new Error("User not found");
    }

    // Fetch latest user data from Graph API to ensure sync
    const graphApiClient = createGraphApiClient();
    const userDetails = await graphApiClient.getUserByEmail(user.email);
    const userGroups = await graphApiClient.getUserGroups(userDetails.id);

    // Update user in database with latest information
    await userRepository.updateUser(user.id, {
      displayName: userDetails.displayName,
      department: userDetails.department,
      jobTitle: userDetails.jobTitle,
      // Update other attributes
      roles: mapGroupsToRoles(userGroups),
    });

    // Generate new tokens with updated user info
    const tokens = generateTokens({
      ...user,
      ...userDetails,
      roles: mapGroupsToRoles(userGroups),
    });

    // Set new refresh token in cookie
    res.cookie("refresh_token", tokens.refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
    });

    // Return new access token
    res.json({ accessToken: tokens.accessToken });
  } catch (error) {
    // Clear tokens on error
    res.clearCookie("refresh_token");
    res.status(401).json({ error: "Invalid refresh token" });
  }
});
```

#### 5.1.3. Handling Failures

- If refresh fails (invalid token, user disabled, etc.)
- Clear tokens and cached authentication data
- Redirect to the Azure AD login page for reauthentication

```javascript
// Frontend code for handling refresh failures
const handleRefreshFailure = () => {
  // Clear local auth state
  authState.accessToken = null;
  authState.accessTokenExpiry = null;

  // Redirect to login
  window.location.href = "/login";
};
```

## 6. Just-In-Time Provisioning with Microsoft Graph API

### 6.1. Core JIT Process Flow

1. **Initial Authentication**

   - User authenticates with Azure AD via SAML
   - Application receives SAML assertion with claims
   - Backend verifies if user exists in application database

2. **User Lookup & Creation (First Login)**

   - If user doesn't exist, initiate JIT provisioning
   - Retrieve comprehensive user information from Microsoft Graph API
   - Create user record with complete profile information
   - Map Azure AD groups to application roles

3. **Continuous Profile Synchronization**
   - During each token refresh, re-fetch user data from Graph API
   - Update user information in database to match Microsoft Entra ID
   - Apply any changes to roles or permissions based on group membership
   - Generate new tokens with the latest user information
   - This ensures user data stays synchronized even if changed in Azure AD

### 6.2. Microsoft Graph API Integration

#### 6.2.1. User Data Retrieval

1. **Authentication to Graph API**

   - Use service principal authentication (client credentials flow)
   - Obtain access token for Microsoft Graph API
   - Store token with appropriate caching strategy

   ```javascript
   // Example of obtaining Graph API token
   const getGraphApiToken = async () => {
     const tokenResponse = await axios.post(
       `https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/token`,
       new URLSearchParams({
         client_id: GRAPH_CLIENT_ID,
         client_secret: GRAPH_CLIENT_SECRET,
         scope: "https://graph.microsoft.com/.default",
         grant_type: "client_credentials",
       })
     );

     return tokenResponse.data.access_token;
   };
   ```

2. **User Profile Endpoint**

   - Call `/v1.0/users/{userPrincipalName or id}` endpoint
   - Request specific properties based on application needs:
     ```
     /users/{id}?$select=displayName,givenName,surname,mail,userPrincipalName,jobTitle,department,officeLocation,employeeId,userType,id
     ```
   - Process response to extract required user attributes

   ```javascript
   // Example of retrieving user data from Graph API
   const getUserDetails = async (userEmail) => {
     const token = await getGraphApiToken();

     const response = await axios.get(
       `https://graph.microsoft.com/v1.0/users/${userEmail}?$select=displayName,givenName,surname,mail,userPrincipalName,jobTitle,department,officeLocation,employeeId,id`,
       {
         headers: {
           Authorization: `Bearer ${token}`,
         },
       }
     );

     return response.data;
   };
   ```

3. **Data Transformation**
   - Map Graph API attributes to application user model
   - Handle missing or null values appropriately
   - Apply any business rules or data cleaning processes

#### 6.2.2. Group Membership for Role Assignment

1. **Retrieving User's Groups**

   - Call `/v1.0/users/{id}/memberOf` endpoint
   - Filter for security groups only if needed:
     ```
     /users/{id}/memberOf?$filter=securityEnabled eq true
     ```
   - Handle pagination for users with many group memberships

   ```javascript
   // Example of retrieving user group membership
   const getUserGroups = async (userId) => {
     const token = await getGraphApiToken();

     const response = await axios.get(
       `https://graph.microsoft.com/v1.0/users/${userId}/memberOf?$filter=securityEnabled eq true`,
       {
         headers: {
           Authorization: `Bearer ${token}`,
         },
       }
     );

     return response.data.value;
   };
   ```

2. **Mapping Groups to Roles**

   - Maintain mapping configuration between AAD groups and application roles
   - Example mapping configuration:
     ```json
     {
       "AAD-Admin-Group-ID": ["admin", "user"],
       "AAD-Finance-Group-ID": ["finance", "user"],
       "AAD-Manager-Group-ID": ["manager", "user"]
     }
     ```
   - Assign appropriate roles based on group membership

   ```javascript
   // Example of mapping groups to roles
   const mapGroupsToRoles = (groups, groupToRoleMapping) => {
     const roles = new Set();

     groups.forEach((group) => {
       const mappedRoles = groupToRoleMapping[group.id] || [];
       mappedRoles.forEach((role) => roles.add(role));
     });

     return Array.from(roles);
   };
   ```

3. **Role Assignment**
   - Apply mapped roles to user in application database
   - Consider role hierarchy and inheritance if applicable
   - Include roles in JWT for authorization purposes

### 6.3. Advanced Features

1. **Delta Queries for Efficient Updates**

   - Implement delta queries for efficient updates
   - Track only changes since last synchronization
   - Reduce bandwidth and processing requirements

   ```javascript
   // Example of using delta queries
   const getUserDelta = async (deltaLink) => {
     const token = await getGraphApiToken();

     const url = deltaLink || "https://graph.microsoft.com/v1.0/users/delta";
     const response = await axios.get(url, {
       headers: {
         Authorization: `Bearer ${token}`,
       },
     });

     return {
       changes: response.data.value,
       deltaLink: response.data["@odata.deltaLink"],
     };
   };
   ```

2. **Batch Requests for Performance**

   - Use batch requests for multiple Graph API operations
   - Improve performance for complex provisioning scenarios
   - Reduce number of HTTP requests

3. **Webhooks for Real-Time Updates**
   - Subscribe to change notifications for users and groups
   - Receive real-time updates when user attributes change
   - Keep application user data continuously synchronized

## 7. Implementation Considerations

### 7.1. Service Account Requirements

For Microsoft Graph API access, create a service principal with the following:

1. **Required Permissions**:

   - `User.Read.All` - Read all user profiles
   - `GroupMember.Read.All` - Read group memberships
   - `Directory.Read.All` - Read directory data (if needed)

2. **Authentication Method**:

   - Client credentials flow with client ID and secret
   - Certificate-based authentication for higher security

3. **Security Considerations**:
   - Limit access with least-privilege principle
   - Rotate credentials regularly
   - Monitor and audit API usage

### 7.2. Performance Optimization

**Request Optimization**

- Use `$select` to retrieve only needed properties
- Implement batching for multiple operations
- Consider asynchronous processing for non-blocking operations

### 7.3. Security Best Practices

**Token Security**

- Use appropriate algorithms (RS256) for token signing
- Set reasonable expiration times (15-60 minutes for access tokens)
- Include only necessary claims in tokens

**Credential Protection**

- Store service principal credentials securely
- Use key vault solutions for secrets (Azure Key Vault, HashiCorp Vault, AWS Secrets Manager)
- Implement credential rotation procedures

## 8. Error Handling and Resilience

### 8.1. Common Error Scenarios

1. **Graph API Unavailability**

   - Implement circuit breaker pattern
   - Fall back to basic authentication with available claims
   - Queue synchronization for later retry
   - Example resilience pattern:

   ```javascript
   // Example of implementing circuit breaker pattern
   const getUserDetailsWithResilience = async (userEmail) => {
     try {
       // Try to get from Graph API
       return await getUserDetails(userEmail);
     } catch (error) {
       // Check if Graph API is unavailable
       if (isServiceUnavailable(error)) {
         // Log the issue
         logger.warn("Graph API unavailable, using fallback data");

         // Fall back to basic data from tokens/database
         return getFallbackUserData(userEmail);

         // Queue for later retry
         syncQueue.add({ operation: "syncUser", userEmail });
       }

       // Rethrow other errors
       throw error;
     }
   };
   ```

2. **User Not Found in AAD**

   - Handle deleted or transferred users
   - Implement proper deprovisioning procedures
   - Maintain audit logs of provisioning attempts

3. **Permission Issues**
   - Monitor for insufficient permissions errors
   - Implement clear logging of permission failures
   - Establish escalation procedures for permission issues

### 8.2. Logging and Monitoring

1. **Authentication Events**

   - Log all authentication attempts (successful and failed)
   - Track token issuance and refreshes
   - Monitor for unusual patterns

2. **Provisioning Operations**

   - Log user creation and updates
   - Track Graph API calls and responses
   - Monitor provisioning performance metrics

3. **Error Tracking**
   - Implement structured error logging
   - Include correlation IDs across components
   - Set up alerts for critical failures

## 9. Conclusion

Implementing JWT authentication with SAML SSO and continuous user provisioning using Microsoft Graph API provides a robust, scalable, and user-friendly authentication system. By combining these approaches:

- Users enjoy uninterrupted access to applications
- User profile data is automatically kept in sync with Azure AD
- Administrators maintain centralized control through Azure AD
- Applications always have up-to-date user information
- Security is enhanced through proper token handling and minimal data exposure

This approach balances security requirements with user experience, while leveraging Microsoft's identity platform and Graph API capabilities for comprehensive identity management.

## 10. References

- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/overview)
- [Azure AD Authentication Libraries](https://docs.microsoft.com/en-us/azure/active-directory/develop/active-directory-authentication-libraries)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [Microsoft Identity Platform Best Practices](https://docs.microsoft.com/en-us/azure/active-directory/develop/identity-platform-integration-checklist)
- [SAML 2.0 Technical Overview](https://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html)

## 11. Appendix

### 11.1. Implementation Checklist

Use this checklist to ensure you've covered all critical aspects of the implementation:

#### SAML Configuration

- [ ] Register application in Microsoft Entra ID
- [ ] Configure reply URL (ACS endpoint)
- [ ] Download federation metadata XML
- [ ] Configure signing certificate
- [ ] Define required claims to be included in the SAML response
- [ ] Enable application in Entra ID
- [ ] Assign users/groups to the application

#### Application Backend Setup

- [ ] Implement SAML assertion consumer service endpoint
- [ ] Configure SAML validation with proper certificate
- [ ] Create JWT token generation service
- [ ] Implement refresh token endpoint with Graph API integration
- [ ] Create database schema for user profiles
- [ ] Implement group-to-role mapping configuration
- [ ] Set up secure token storage mechanisms

#### Microsoft Graph API Integration

- [ ] Create service principal with appropriate permissions
- [ ] Secure credential storage
- [ ] Implement Graph API client with error handling
- [ ] Create user profile synchronization service
- [ ] Set up group membership retrieval and mapping

#### Security & Operations

- [ ] Implement comprehensive logging
- [ ] Set up monitoring for authentication events
- [ ] Create alerting for suspicious activities
- [ ] Document certificate rotation procedures
- [ ] Perform security review
- [ ] Create runbook for common issues
- [ ] Train support personnel on troubleshooting

### 11.2. Troubleshooting Guide

#### Common SAML Issues

| Issue                      | Possible Cause                             | Solution                                                                           |
| -------------------------- | ------------------------------------------ | ---------------------------------------------------------------------------------- |
| "Invalid SAML Response"    | Incorrectly configured signing certificate | Verify certificate thumbprint matches, ensure clock skew is within limits          |
| "Invalid Audience"         | Misconfigured entity ID                    | Check that the audience in the SAML response matches your application's entity ID  |
| "Response expired"         | Clock skew between IdP and SP              | Synchronize clocks, check timezone settings, consider increasing validation window |
| "No user identifier found" | Missing nameID in SAML response            | Check SAML response format, ensure nameID or appropriate claim is included         |

#### JWT Authentication Problems

| Issue                     | Possible Cause                 | Solution                                                                     |
| ------------------------- | ------------------------------ | ---------------------------------------------------------------------------- |
| "Token signature invalid" | Wrong signing key or algorithm | Verify signing key and algorithm match between generation and validation     |
| "Token expired"           | Client/server clock difference | Check time synchronization, consider adjusting token lifetime                |
| "Missing required claims" | Incomplete token generation    | Review token generation code to ensure all required claims are included      |
| "Invalid refresh token"   | Token tampered or expired      | Check token validation, verify storage security, look for signs of tampering |

#### Graph API Synchronization Issues

| Issue                      | Possible Cause                            | Solution                                                                           |
| -------------------------- | ----------------------------------------- | ---------------------------------------------------------------------------------- |
| "Insufficient permissions" | Missing permissions for service principal | Review Graph API permissions, ensure admin consent has been granted                |
| "User not found"           | User deleted from Entra ID                | Implement proper error handling for deleted users, consider deprovisioning flow    |
| "Too many requests"        | Hitting rate limits                       | Implement exponential backoff, optimize queries, batch requests where possible     |
| "Invalid groups claim"     | Group claim configuration issue           | Check group claim setup in Entra ID, verify correct API is used to retrieve groups |

#### Common Implementation Errors

| Issue                    | Possible Cause                         | Solution                                                                                   |
| ------------------------ | -------------------------------------- | ------------------------------------------------------------------------------------------ |
| Token leakage            | Storing tokens in insecure locations   | Use memory-only storage for access tokens, HTTP-only cookies for refresh tokens            |
| Excessive authentication | Poor token refresh implementation      | Implement proper token refresh before expiration, check refresh timing                     |
| User profile out of sync | Infrequent synchronization             | Ensure user data is refreshed during token refresh, implement webhook for critical changes |
| Permission drift         | Group membership changes not reflected | Synchronize group membership during token refresh, implement proper role mapping           |

### 11.3. Performance Optimization Guidelines

#### Reducing Graph API Calls

- Use `$select` to retrieve only needed user properties
- Implement batching for multiple user lookups
- Cache frequent queries with appropriate TTL
- Consider webhooks for real-time updates to reduce polling
- Use delta queries for efficient synchronization

#### Optimizing Token Operations

- Keep JWT payloads minimal (only include essential claims)
- Consider using reference tokens for very large user contexts
- Implement efficient token validation algorithms
- Use appropriate key sizes for optimal security/performance balance

#### Database Optimization

- Index user identifiers used in lookups
- Consider read replicas for high-volume authentication scenarios
- Implement efficient role lookup mechanisms
- Balance data synchronization frequency with application needs
