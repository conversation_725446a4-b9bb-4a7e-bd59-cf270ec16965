### **SSO SAML Integration Requirements for "Connected Workers" (Aptiv)**

This document outlines the step-by-step process, prerequisites, and technical requirements for integrating **SSO authentication (SAML with Entra ID)** for the "Connected Workers" project at Aptiv. This implementation aligns with Aptiv's **defined processes and terminology** while ensuring **compliance with their security and business policies**.

---

## **1. Key Components & Terminology**

| Component                             | Description                                                                |
| ------------------------------------- | -------------------------------------------------------------------------- |
| **Identity Provider (IdP)**           | Azure AD (Microsoft Entra ID)                                              |
| **Service Provider (SP)**             | "Connected Workers" Application                                            |
| **Authentication Protocol**           | **SAML 2.0** (Primary), OAuth/OpenID as fallback                           |
| **User Identification**               | **UPN (User Principal Name)** - Note: UPN differs from communication email |
| **Multi-Factor Authentication (MFA)** | Mandatory for all users                                                    |
| **AD Groups & Roles**                 | Used for **Role-Based Access Control (RBAC)**                              |
| **Guest Account Support**             | Limited B2B/B2C support (vendor restrictions apply)                        |

---

## **2. Business & Security Drivers**

### **2.1. Business Benefits**

✅ **Single Sign-On (SSO):** Users authenticate once and gain access to multiple applications.  
✅ **Centralized Identity Management:** No need for multiple user credentials.  
✅ **Efficiency in User Provisioning & Deprovisioning:** Reduces manual administrative tasks.  
✅ **Compliance with Aptiv's Account Inactivity Policy:** Accounts disabled after **60 days** of inactivity.

### **2.2. Security & Compliance Benefits**

🔒 **Immediate Access Removal:** When a user's Aptiv ID is terminated, access is revoked from all **IdP-integrated** applications.  
🔒 **Centralized Security Control:** Security policies (password complexity, expiry, MFA) enforced across all systems.  
🔒 **Audit & Compliance Readiness:** Easy tracking of authentication and access control logs.

---

## **3. ServiceNow Request Process for SSO Onboarding**

To integrate **"Connected Workers"** with Azure AD SSO, the following **ServiceNow** requests must be raised:

### **3.1. Prerequisites**

✅ **Vendor provides SSO Onboarding Template** with application details.  
✅ **Vendor shares SAML Metadata XML** (includes signing certificate).  
✅ **Valid User Email List** (Aptiv **UPN** format).  
✅ **CMDB Request** must be raised for the new application.

### **3.2. Steps to Raise a Service Catalogue Request**

1. **Submit the ServiceNow Request:**

   - **Request Type:** "**MyApps Portal Applications – Single Sign-On (SSO)**"
   - **Request Link:** [Aptiv Service Catalogue](https://aptiv.service-now.com/sp/?id=sc_cat_item&sys_id=0d9fbc30dbf4fb00f7fef3651d9619b2)
   - **Requester:** Must be an **Aptiv associate** (BRM/PM only)

2. **Metadata Upload:**

   - Attach **SAML Metadata file** in ServiceNow.
   - Ensure it includes **Entity ID, ACS URL, and Certificate**.

3. **Approval Workflow:**

   - The **HCL-Aptiv-EUC-AD Team** (`<EMAIL>`) **reviews & approves metadata**.
   - Metadata is **shared with the vendor** (application provider).

4. **Application Configuration:**

   - Vendor integrates **metadata with the Connected Workers app**.
   - **Handshake Testing** performed with Azure AD.

5. **Escalation Path (if needed):**

   - If any issues arise, a **joint troubleshooting session** must be scheduled with:
     - **HCL-Aptiv AD Team**
     - **Vendor Application Team**
     - **Aptiv AD Architects** (**Craig or Patric**)

6. **Go-Live & Monitoring:**
   - Deploy SSO integration.
   - Perform **SAML assertion validation & API token exchange testing**.

---

## **4. SSO Configuration Requirements**

The following parameters must be configured when registering the **Connected Workers App** in **Azure AD**:

| **Configuration Parameter** | **Requirement**                                   | **Mandatory?** |
| --------------------------- | ------------------------------------------------- | -------------- |
| **Sign-On URL**             | Endpoint for login requests                       | ❌ Optional    |
| **Identifier (Entity ID)**  | Unique application identifier                     | ✅ Yes         |
| **Replay/Redirect URL**     | Callback URL for authentication response          | ✅ Yes         |
| **Relay State URL**         | Resource identifier post-login                    | ❌ Optional    |
| **SAML Signing Algorithm**  | **SHA-256** (SHA-1 supported but not recommended) | ✅ Yes         |

📌 **Notes:**

- Certain vendors (TCS, HCL, DXC) may have restrictions on guest user access
- Any incorrect email IDs in the user list will cause approval delays
- For non-SSO compatible applications, an SSO Exception Form must be submitted

---

## **5. Authentication Flow (IdP-Initiated SSO)**

This project follows **IdP-initiated SSO**, where authentication starts from the **Aptiv MyApps portal**.

### **Step-by-Step Flow:**

1. **User signs into MyApps portal** → Authenticated via **Azure AD**.
2. **User selects the Connected Workers App** → Request is sent to **Application Proxy** with the **SAML Response**.
3. **Application Proxy temporarily caches SAML response** → Redirects request to **Azure AD** for **pre-authentication**.
4. **Azure AD authenticates user** → Redirects back to **Application Proxy**, then forwards request with **SAML Response** to **Connected Workers App**.
5. **Connected Workers App validates SAML response** → **User is logged in**.

---

## **6. APIM & Secure API Access**

Azure **API Management (APIM)** is required to **secure API calls** after user authentication.

### **6.1. APIM Token Validation**

- **After SAML authentication, Connected Workers app generates a JWT token**
- **APIM validates the JWT token** before API access
- **Token lifetime and refresh mechanisms must align with Aptiv's 60-day inactivity policy**

### **6.2. APIM Policy for Token Validation**

```xml
<validate-jwt header-name="Authorization" failed-validation-httpcode="401">
    <openid-config url="https://login.microsoftonline.com/{tenant-id}/v2.0/.well-known/openid-configuration" />
    <issuer-signing-keys>
        <key>MIIBIjANB...</key>
    </issuer-signing-keys>
</validate-jwt>
```

### **6.3. Role-Based Access (RBAC)**

- **User AD Groups** are mapped in Azure AD.
- API access **is restricted based on group membership**.

---

## **7. Testing & Deployment**

### **7.1. Testing Checklist**

✔ **SAML Metadata Validation** – Ensure correct **Entity ID & ACS URL**.  
✔ **Handshake Testing** – Validate **SAML assertions** between Azure AD and the Connected Workers App.  
✔ **APIM Token Validation** – Ensure API calls include **valid JWT tokens**.  
✔ **Role-Based Access Testing** – Verify **AD group-based access control**.

### **7.2. Deployment Process**

1. **Submit ServiceNow Request IDs:**
   - **Non-Production:** `RITM1452651`
   - **Production:** `RITM1463869`
2. **Confirm Change Management Approval** before production deployment.
3. **Perform monitored rollout** with logging enabled.

---

## **8. Outstanding Questions for Aptiv**

Before implementation, we need **clarifications** from Aptiv:

### **General SSO Setup**

1. Do we need to support **SP-Initiated SSO**, or is **IdP-Initiated** sufficient?
2. How does Aptiv expect the **SAML assertion to be exchanged for a JWT token** for API access?

### **Metadata & Certificates**

3. Is there an **automated certificate rollover process** for SAML signing?
4. Should we configure **Relay State URLs** for deep linking?

### **API Security & APIM**

5. Should **APIM validate JWT tokens only, or also handle SAML assertions**?
6. Do you require **OpenID Connect configuration** for additional API security?

### **User Provisioning & Guest Access**

7. Does **SCIM-based provisioning** apply to the Connected Workers app?
8. How are **B2B guest users** managed for SSO access?

---

## **9. Next Steps**

- 📌 **Submit ServiceNow request** for SSO onboarding.
- 🔧 **Perform handshake testing** after metadata exchange.
- ✅ **Deploy & validate APIM integration** for secure API access.
- 🛠 **Address any Aptiv feedback** and finalize production rollout.

---

### **Conclusion**

This document ensures the **Connected Workers** application is fully integrated with **Aptiv's SSO framework**, aligns with **Azure AD security policies**, and provides **seamless authentication & API security**.

🚀 **Once we get Aptiv's responses, we will proceed with implementation.**

### **Authentication Flow Diagram**

The following diagram illustrates the simplified SSO integration workflow between key stakeholders and systems:

```mermaid
sequenceDiagram
    autonumber
    participant PM as Aptiv PM
    participant SP as Service Provider<br/>(Connected Workers)
    participant IdP as Identity Provider<br/>(Azure AD)
    participant APIM as API Management

    %% Initial Setup
    PM->>SP: Initiate SSO Integration
    SP->>IdP: Register App & Configure SAML
    IdP-->>SP: Return SAML Metadata

    %% Authentication Flow
    SP->>IdP: Configure SSO Settings
    IdP-->>SP: Verify Configuration

    %% API Security Setup
    SP->>APIM: Setup Token Validation
    APIM->>IdP: Configure JWT Validation
    IdP-->>APIM: Confirm Security Settings

    %% Final Verification
    SP->>PM: Complete Integration
    note right of PM: Deployment Ready
```

**Key Flow Steps:**

1. Project Manager initiates SSO integration process
2. Service Provider (Connected Workers) registers with Azure AD
3. SAML metadata exchange and configuration
4. SSO settings verification
5. API security setup with token validation
6. Final integration verification and deployment readiness
