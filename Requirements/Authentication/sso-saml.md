# Authentication Guide

## Table of Contents

1. [Overview](#overview)
2. [SAML Authentication](#saml-authentication)
   - [Core Components](#core-components)
   - [Authentication Flows](#authentication-flows)
   - [SAML Messages](#saml-messages)
   - [Trust Components](#trust-components)
   - [Additional Features](#additional-features)
   - [SAML Configuration](#saml-configuration)
   - [Token Structure](#token-structure)
3. [Security: Certificate Management](#security-certificate-management)
   - [Certificate Types and Usage](#certificate-types-and-usage)
   - [Certificate Workflows](#certificate-workflows)
   - [Certificate Generation](#certificate-generation)
   - [Certificate Security Best Practices](#certificate-security-best-practices)
4. [Azure AD Role Management](#azure-ad-role-management)
   - [Role Types and Assignment](#role-types-and-assignment)
   - [Dynamic Groups](#dynamic-groups)
   - [HR Integration](#hr-integration)
5. [Security Best Practices](#security-best-practices)
   - [XML Security](#xml-security)
   - [Signature Validation](#signature-validation)
   - [Response Validation](#response-validation)
   - [Additional Security Measures](#additional-security-measures)
6. [Conclusion](#conclusion)

## Overview

This document provides a comprehensive technical guide for implementing secure authentication with Microsoft Entra ID (formerly Azure AD) using SAML-based Single Sign-On (SSO). It covers the complete authentication lifecycle, security practices, and implementation details for enterprise applications.

## SAML Authentication

### Core Components

1. **Identity Provider (IdP)**

   - Microsoft Entra ID acts as the central authentication point
   - Stores and verifies user credentials
   - Issues and digitally signs SAML assertions

2. **Service Provider (SP)**

   - Your application requiring authentication
   - Processes SAML responses
   - Manages user sessions

3. **Assertion Consumer Service (ACS)**
   - SP endpoint that receives and processes SAML responses
   - Validates assertions and signatures
   - Handles session creation after successful authentication

### Authentication Flows

#### SP-Initiated Flow

The SP-initiated flow begins when a user attempts to access your application. This is the most common authentication pattern for web applications:

```mermaid
sequenceDiagram
    participant User
    participant SP as Service Provider
    participant IdP as Entra ID

    User->>SP: 1. Access Protected Resource
    Note over SP: 2. Check for valid session
    SP->>SP: 3. Generate SAML Request with ID, issuer, ACS URL
    SP->>User: 4. Redirect to Entra ID with SAML Request
    User->>IdP: 5. Forward SAML Request
    Note over IdP: 6. Validate Request (issuer, signature)
    alt User Not Authenticated
        IdP->>User: 7a. Present Authentication UI
        User->>IdP: 8a. Provide Credentials
        Note over IdP: 9a. Validate Credentials
    else User Has Active Session
        Note over IdP: 7b. Identify User from Session
    end
    Note over IdP: 10. Generate SAML Response
    Note over IdP: 11. Sign Response & Assertions
    IdP->>User: 12. Redirect to SP's ACS URL with SAML Response
    User->>SP: 13. Forward SAML Response
    Note over SP: 14. Validate Response (signature, conditions, audience)
    Note over SP: 15. Extract User Attributes & Create Session
    SP->>User: The 16. Grant Access & Set Session Cookie/JWT
```

**SP-Initiated Flow - Detailed Explanation:**

1. **Initial Application Access**

   - User navigates to a protected resource or clicks login
   - SP determines authentication is required
   - Technical implementation: Application validates session cookies/tokens

2. **SAML Request Generation**

   - SP creates a SAML AuthnRequest XML document containing:
     - Unique request ID (for tracking/security)
     - Issuer identifier (your application's entity ID)
     - ACS URL (where Entra ID should send the response)
     - Request signing (optional but recommended)
   - Technical implementation: `/login` endpoint generates the request

3. **Redirection to Identity Provider**

   - SP redirects the user's browser to Entra ID's SSO endpoint
   - Request is typically compressed and encoded
   - Optional RelayState parameter preserves application state
   - Technical implementation: HTTP 302 redirect with SAMLRequest parameter

4. **Entra ID Processing**

   - Entra ID validates the request (signature, issuer)
   - Checks if the user already has an active session
   - Security consideration: Validate allowed requesting applications

5. **User Authentication**

   - If no active session exists, user is prompted for credentials
   - May include multi-factor authentication if configured
   - Entra ID validates credentials against directory
   - Technical implementation: Managed by Entra ID platform

6. **SAML Response Generation**

   - Entra ID creates a SAML Response containing:
     - User identity (NameID)
     - Authentication context (method, time)
     - User attributes (groups, email, etc.)
     - Cryptographic signature
   - Security consideration: Response encrypted for sensitive attributes

7. **Returning to Service Provider**

   - User browser is redirected to the ACS URL specified in the request
   - SAML Response is typically POSTed via an auto-submitted form
   - RelayState is included if originally provided
   - Technical implementation: HTTP POST to ACS endpoint

8. **Response Validation and Session Creation**

   - SP validates the SAML Response:
     - Verifies signature using Entra ID's certificate
     - Checks response is within valid time window
     - Verifies intended audience matches SP's entity ID
     - Extracts user identity and attributes
   - Technical implementation: Validate at `/acs` endpoint

9. **Session Establishment**
   - SP creates a session for the authenticated user
   - May generate a JWT containing user claims
   - Sets secure, HTTP-only session cookie
   - Redirects user to originally requested resource
   - Technical implementation: Generate JWT with 1-hour expiry

**Implementation Considerations:**

- Error handling for each step (timeout, validation failure)
- Logging of authentication events for security monitoring
- Proper CSRF protection for the ACS endpoint
- Secure cookie settings (SameSite, Secure flags)
- Session timeout and renewal strategies

#### IdP-Initiated Flow

The IdP-initiated flow begins when a user starts from the Microsoft Entra ID portal. This provides a convenient application launch experience from a central portal:

```mermaid
sequenceDiagram
    participant User
    participant IdP as Entra ID
    participant SP as Service Provider

    User->>IdP: 1. Access Entra ID Portal/MyApps
    IdP->>User: 2. Display Available Applications
    User->>IdP: 3. Select Application Tile
    Note over IdP: 4. Check User Authentication
    alt User Not Authenticated
        IdP->>User: 5a. Present Authentication UI
        User->>IdP: 6a. Provide Credentials
        Note over IdP: 7a. Validate Credentials
    end
    Note over IdP: 8. Generate SAML Response
    Note over IdP: 9. Include App-specific Attributes & Roles
    IdP->>User: 10. Redirect to SP with SAML Response
    User->>SP: 11. Submit SAML Response
    Note over SP: 12. Validate Response (signature, conditions)
    Note over SP: 13. Extract User Identity & Attributes
    Note over SP: 14. Create User Session (JWT/Cookie)
    SP->>User: 15. Redirect to Default Landing Page
```

**IdP-Initiated Flow - Detailed Explanation:**

1. **Portal Access and Application Selection**

   - User logs into Microsoft Entra ID portal (myapplications.microsoft.com)
   - User is presented with tiles for authorized applications
   - User selects the application they wish to access
   - Technical implementation: Managed by Entra ID platform

2. **Authentication Verification**

   - Entra ID verifies the user has an active session
   - If not authenticated, prompts for credentials
   - Checks user's authorization for the selected application
   - Security consideration: Access reviews for application permissions

3. **SAML Response Generation**

   - Entra ID generates a SAML Response without a corresponding Request
   - Response includes the same elements as in SP-initiated flow:
     - User identity (NameID)
     - Authentication context
     - User attributes and group memberships
     - Digital signature
   - Technical consideration: Can include default landing page URL

4. **Submission to Service Provider**

   - Browser is redirected to the application's ACS URL
   - SAML Response is typically POSTed via an auto-submitted form
   - Technical implementation: HTTP POST with SAMLResponse parameter

5. **Validation and Session Creation**

   - SP validates the SAML Response using same checks as SP-initiated flow
   - Creates user session and sets appropriate cookies/tokens
   - Key difference: No RelayState to determine return URL
   - Technical implementation: Redirect to default application page

6. **User Access**
   - User is directed to the application's default landing page
   - Application loads with full user context and permissions
   - Technical implementation: Default route in application

**Implementation Considerations:**

- Security for unsolicited SAML responses (prevent SAML injection)
- Configuration of default landing page in application
- Proper handling of application context without RelayState
- Cross-domain considerations for enterprise portals
- Strict audience validation to prevent response redirection attacks

**Technical Configuration:**

- Entra ID application registration must have proper redirect URIs
- Application must support IdP-initiated flow (some libraries require configuration)
- Consider deep linking capabilities for better user experience
- Monitor usage patterns and security anomalies

### SAML Messages

#### SAML Request

The SAML request initiates the authentication process:

- Contains a unique request identifier and timestamp
- Specifies required response parameters
- May include relay state for context preservation
- Defines authentication requirements

#### SAML Response

The SAML response contains the authentication result:

- Includes signed assertions about user identity
- Contains user attributes (name, email, roles, etc.)
- Is processed by the ACS endpoint
- Validates user session establishment

### Trust Components

#### SAML Trust Establishment

Trust between Entra ID and your application is established through:

- Shared certificates for signing and validation
- Entity IDs/issuers for identity verification
- Secure communication protocols
- Proper metadata exchange

#### Metadata Management

Metadata files simplify configuration between IdP and SP:

- XML files containing endpoints, certificates, and settings
- Enable self-configuration and reduce manual setup
- Facilitate trust updates and maintenance
- Can be generated and consumed automatically

### Additional Features

#### Context Management

These features enhance the authentication process:

- **Relay State**: Preserves user context during authentication
- **Session State**: Manages user session lifecycle
- **Return URL**: Enables return to original location after login
- **Context Parameters**: Passes additional authentication context

#### User Attributes

Attributes provide essential user information:

- Profile data transferred in SAML responses
- Standard attributes (name, email, groups)
- Custom attributes for specific requirements
- Role and permission mappings

### SAML Configuration

#### Metadata Example

```xml
<EntityDescriptor xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
                 entityID="https://your-app.example.com">
    <SPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <KeyDescriptor use="signing">
            <!-- Certificate details -->
        </KeyDescriptor>
        <AssertionConsumerService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://your-app.example.com/saml/acs"
            index="0"
            isDefault="true" />
    </SPSSODescriptor>
</EntityDescriptor>
```

#### Key Configuration Parameters

- `ID`: Unique identifier for the request
- `IssueInstant`: Timestamp of request generation
- `AssertionConsumerServiceURL`: Where Entra ID should send the response
- `ProtocolBinding`: Specifies how the response should be sent (usually HTTP-POST)
- `Issuer`: Entity ID of the requesting SP
- `NameIDPolicy`: Requested format for the user identifier
- `RequestedAuthnContext`: Required authentication strength/method

### Token Structure

SAML tokens contain user information and authentication proof:

```xml
<SAMLResponse>
    <Issuer>https://sts.windows.net/{tenant-id}/</Issuer>
    <Assertion>
        <Subject>
            <NameID><EMAIL></NameID>
        </Subject>
        <AttributeStatement>
            <Attribute Name="displayname">
                <AttributeValue>John Doe</AttributeValue>
            </Attribute>
            <Attribute Name="department">
                <AttributeValue>IT</AttributeValue>
            </Attribute>
            <Attribute Name="country">
                <AttributeValue>France</AttributeValue>
            </Attribute>
            <Attribute Name="city">
                <AttributeValue>Paris</AttributeValue>
            </Attribute>
            <Attribute Name="site">
                <AttributeValue>HQ Building</AttributeValue>
            </Attribute>
            <Attribute Name="legacyId">
                <AttributeValue>EMP123456</AttributeValue>
            </Attribute>
            <!-- Additional attributes -->
        </AttributeStatement>
    </Assertion>
</SAMLResponse>
```

## Security: Certificate Management

### Certificate Types and Usage

1. **Service Provider (SP) Certificates**

   - `sp-cert.pem`: Public certificate shared with Entra ID
   - `sp-key.pem`: Private key kept secure by the SP
   - Used for signing SAML requests and verifying responses

2. **Identity Provider (IdP) Certificate**
   - `idp-cert.pem`: Entra ID's public certificate
   - Used to validate SAML responses from Entra ID
   - Periodically rotated by Microsoft

### Certificate Workflows

1. **SAML Request Signing**

   ```mermaid
   sequenceDiagram
       participant SP as Service Provider
       participant IdP as Entra ID

       SP->>IdP: 1. Signs request with SP private key
       Note over IdP: 2. IdP verifies with SP public cert
   ```

2. **SAML Response Verification**

   ```mermaid
   sequenceDiagram
       participant SP as Service Provider
       participant IdP as Entra ID

       IdP->>SP: 1. Signs response with IdP private key
       Note over SP: 2. SP verifies with IdP cert
   ```

### Certificate Generation

```bash
# Generate new SP certificate and key
openssl req -x509 -newkey rsa:2048 -keyout sp-key.pem -out sp-cert.pem -days 365 -nodes

# Remove passphrase if needed
openssl rsa -in sp-key.pem -out sp-key.pem
```

### Certificate Security Best Practices

1. **Private Key Security**

   - Never share SP private keys
   - Use secure storage with restricted access
   - Consider key management services for enterprise environments

2. **Certificate Distribution**

   - Share only public certificates with Entra ID
   - Verify certificate fingerprints after exchange
   - Use secure methods for certificate distribution

3. **Certificate Lifecycle**
   - Plan regular certificate rotation (annually or biannually)
   - Maintain emergency rotation procedures
   - Use separate certificates per environment (dev, test, prod)
   - Monitor certificate expiration dates

## Azure AD Role Management

### Role Types and Assignment

Entra ID provides multiple mechanisms for role assignment:

```mermaid
graph TB
    A[Entra ID] --> B[Roles]
    A --> C[Groups]
    B --> D[Built-in Roles]
    B --> E[Custom Roles]
    C --> F[Security Groups]
    C --> G[Microsoft 365 Groups]
    C --> H[Dynamic Groups]
```

- **Directory Roles**: Predefined or custom roles granting specific permissions within Entra ID
- **Application Roles**: Custom roles defined in your application registration
- **Group-based Assignment**: Roles assigned via security group membership
- **Direct Assignment**: Roles assigned directly to individual users

### Dynamic Groups

Dynamic groups automatically include or exclude members based on user attributes:

#### Configuration Options

1. **User Dynamic Groups**

   - Based on user attributes (department, location, job title)
   - Automatically updated when attributes change
   - Expression-based membership rules

2. **Device Dynamic Groups**
   - Based on device attributes (OS version, compliance state)
   - Used for conditional access policies
   - Device management automation

#### Example Rule Syntax

```
user.department -eq "Engineering" and user.country -eq "USA"
```

#### Benefits

- Reduced administrative overhead
- Consistent group membership
- Automated access management
- Integration with HR systems
- Real-time updates based on attribute changes

### HR Integration

HR system integration enables automated user lifecycle management:

```mermaid
sequenceDiagram
    participant Workday
    participant AzureAD
    participant DynamicGroup
    participant User

    Workday->>AzureAD: Sync Employee Data
    AzureAD->>DynamicGroup: Apply Membership Rules
    DynamicGroup->>User: Auto-assign Membership
```

- **Automated Provisioning**: Create accounts when employees join
- **Attribute Synchronization**: Keep user attributes current
- **Group Membership**: Dynamically assign to correct groups
- **Account Deactivation**: Automatically disable access when employees leave

## Security Best Practices

### XML Security

1. **XML Canonicalization**

   - Always canonicalize XML responses before processing
   - Use standard canonicalization algorithms (C14N)
   - Prevents manipulation through different XML representations

2. **Schema Validation**
   - Strictly validate SAML assertion schema
   - Reject malformed or non-compliant XML
   - Prevent XML Signature Wrapping (XSW) attacks

### Signature Validation

1. **Assertion Signatures**

   - Require signatures on all assertions
   - Reject partially signed responses
   - Validate each signature independently
   - Ensure signature covers critical elements

2. **Algorithm Restrictions**
   - Accept only secure signature algorithms
   - Prefer SHA-256 or stronger
   - Explicitly reject weak algorithms (MD5, SHA-1)
   - Maintain an allowlist of accepted algorithms

### Response Validation

1. **Party Verification**

   - Strictly validate destination URLs
   - Verify audience restrictions
   - Match recipient information
   - Validate issuer identity
   - Compare against expected ACS endpoints

2. **Temporal Validation**
   - Check assertion validity period
   - Verify issue instant timestamps
   - Implement strict clock skew tolerance
   - Reject expired assertions
   - Monitor for replay attempts

### Additional Security Measures

1. **Access Controls**

   - Implement least privilege principle
   - Regular access review
   - Role-based access control
   - Session management
   - Activity monitoring

2. **Audit and Monitoring**
   - Log all authentication events
   - Monitor for suspicious patterns
   - Regular security assessments
   - Incident response procedures
   - Compliance reporting

## Conclusion

Implementing SAML-based Single Sign-On with Microsoft Entra ID provides a robust, secure, and user-friendly authentication solution for enterprise applications. This guide has covered the essential components and processes required for a successful implementation.
