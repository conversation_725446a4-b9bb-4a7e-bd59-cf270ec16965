# Connected Workers SSO Integration: Key Questions & Requirements

This document gathers essential information from Aptiv to ensure smooth SAML-based SSO integration and secure API access via APIM for the Connected Workers application. Note that users may access Connected Workers directly. In such cases, the application should redirect the user to the login page for SAML authentication.

---

## 1. SAML Configuration

### A. Service Provider (SP) Setup

- **Entity ID & ACS URL**

  - What Entity ID should the Connected Workers app use?
  - What is the correct Assertion Consumer Service (ACS) URL?

- **Metadata & SSO Flow**

  - Will Aptiv provide the SAML metadata XML for Azure AD?
  - Should we support both SP-initiated and IdP-initiated SSO flows?
    - _Note:_ For SP-initiated flows (when users access Connected Workers directly), the app should redirect the user to the Aptiv login page.

- **Certificate & Relay State**
  - What is the certificate rollover process for SAML signing certificates?
  - Should we configure Relay State URLs for deep linking to preserve the intended destination after authentication?

### B. SAML Response Attributes

- **Mandatory User Attributes**  
  Confirm that the following attributes will be included in the SAML response:
  ```xml
  <saml:Attribute Name="upn">
  <saml:Attribute Name="employeeId">
  <saml:Attribute Name="email">
  <saml:Attribute Name="groups">
  ```
- **MFA Requirements**
  - Are there any additional MFA requirements beyond Azure AD MFA?
  - Are certain user groups exempt from MFA, or should sensitive operations trigger step-up authentication?

---

## 2. User Identification & Role Mapping

### A. Unique User Identifier

- **Identifier Choice**

  - Which identifier should be used to link SAML users with Workday data?
    - _Example identifiers:_ `upn`, `employeeId`, or `email`
  - What is the expected format (e.g., `<EMAIL>` or an employee number)?

- **Fallback Strategy**
  - If the primary identifier is missing, what alternative should be used?

### B. Mapping to Workday & RBAC

- **Data Integration**

  - How should attributes from Azure AD be mapped to Workday data (e.g., Employee ID, Department, Manager)?

- **Role & Group Mapping**

  - How are roles and groups from the SAML assertion or Azure AD translated into Connected Workers application roles?
  - Please provide an example mapping for roles such as `ConnectedWorkers_Admin` versus `ConnectedWorkers_User`.

- **Inactivity Policy Handling**
  - How do we address the 60-day inactivity policy?
  - _Example JSON structure:_
    ```json
    {
      "inactivityHandling": {
        "warningPeriod": "SPECIFY_DAYS",
        "notificationProcess": "SPECIFY_METHOD",
        "reactivationProcess": "SPECIFY_PROCESS"
      }
    }
    ```

---

## 3. API Security (APIM) & JWT Generation

### A. JWT Token Requirements

- **Token Claims**  
  After successful SAML authentication, our system will generate a JWT. Which claims must be included?

  ```json
  {
    "sub": "<EMAIL>",
    "employeeId": "12345",
    "roles": ["FROM_WORKDAY"],
    "site": "FROM_WORKDAY",
    "additionalClaims": "SPECIFY_REQUIRED_CLAIMS"
  }
  ```

- **Token Lifetime & Validation**
  - What is the expected JWT lifetime?
  - Should APIM validate JWT tokens exclusively, or also handle SAML assertions?

### B. APIM Policy & Error Handling

- **APIM Configuration**

  - What is the APIM gateway URL for Connected Workers?
  - Are there specific policies (e.g., token expiration, IP restrictions) we need to implement?

- **Error Scenarios**
  - How should we handle cases where:
    - A SAML-authenticated user is not found in Workday data?
    - User roles do not match expected formats?
    - ETL data is temporarily unavailable?
  - _Example JSON for error handling:_
    ```json
    {
      "noEtlMatch": {
        "action": "SPECIFY_ACTION",
        "fallback": "SPECIFY_FALLBACK"
      },
      "etlDataUnavailable": {
        "temporaryAccess": "SPECIFY_POLICY",
        "retryStrategy": "SPECIFY_STRATEGY"
      }
    }
    ```

---

## 4. Testing & Deployment

### A. Test Environment & Artifacts

- **Test Users & Data**
  - Can Aptiv provide sample SAML assertions and test user profiles (with required attributes)?
  - What endpoints or URLs should be used in the non-production environment?

### B. Error Handling & Support

- **Troubleshooting & Escalation**
  - What is the escalation process if SAML handshake errors occur or if there are issues mapping user data?
  - Who is the primary contact for Azure AD or integration issues?

### C. Deployment Process

- **Change Management**
  - What is the change management and rollback process for production deployment?
  - Are there specific maintenance windows we need to be aware of?

---

## 5. Implementation Flow Overview

Below is an updated flow that handles both SP-initiated and IdP-initiated scenarios:

```mermaid
sequenceDiagram
    participant User
    participant CW as Connected Workers
    participant IdP as Aptiv Identity Provider
    participant DB as ETL Database
    participant APIM as API Gateway

    %% SP-Initiated Flow
    User->>CW: 1. Access Connected Workers via direct link
    CW->>IdP: 2. Redirect to Aptiv Login (SP-initiated)
    IdP-->>User: 3. Prompt login & authenticate
    User->>IdP: 4. Submit credentials
    IdP-->>CW: 5. Return SAML Response
    Note over CW: 6. Validate SAML assertion
    CW->>DB: 7. Fetch Workday Data & Roles
    DB-->>CW: 8. Return Workday Information
    CW->>CW: 9. Generate JWT with Workday data
    User->>APIM: 10. API calls using JWT

    %% IdP-Initiated Flow (for completeness)
    User->>IdP: A. Start at Aptiv MyApps portal
    IdP-->>CW: B. Redirect with SAML Response
    Note over CW: C. Validate SAML & continue as above
```

---

## Next Steps & Deliverables from Aptiv

1. **SAML Configuration:**

   - Provide the SAML metadata XML and a sample SAML assertion.
   - Confirm the required SAML attributes and flow details for both SP-initiated and IdP-initiated scenarios.

2. **User & Role Mapping:**

   - Define the primary user identifier and any fallback options.
   - Provide details and examples for mapping Azure AD attributes to Workday data and application roles.

3. **APIM Setup:**

   - Share APIM gateway details and any required token validation policies.
   - Provide a sample JWT payload.

4. **Testing & Deployment:**
   - Supply test environment access, endpoints, and sample user profiles.
   - Outline the process for handling errors and escalations during integration.
