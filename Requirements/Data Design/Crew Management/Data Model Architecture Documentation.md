<h1 style="text-align: center;">Crew Management : Data Architecture Documentation</h1>

## 1. Introduction

This document provides a comprehensive overview of the data model architecture for **Crew Management**. It encompasses the structural design, storage methodologies, and integration strategies to ensure seamless operational functionality and robust reporting capabilities.

##### Scope:
- **Entity definitions and relational mappings:** clear delineation of entities and their relationships within the data model.
- **Database and container usage:** implementation of SQL and Cosmos DB for optimized data storage and retrieval.
- **Indexing and partitioning strategies:** efficient data organization to enhance query performance and scalability.
- **Data integration across microservices:** strategies for seamless data exchange and synchronization between distributed services.  

## Table of Contents

1. [Introduction](#1-introduction)
2. [Data Model Overview](#2-data-model-overview)
    - [Crew Management Module](#21-crew-management-module)
3. [Detailed Table Definitions](#3-detailed-tables-definition)
    - [Indexing & Partitioning Strategies](#31-indexing--partitioning-strategies)
    - [Crew Management Module](#32-crew-management-module)
4. [Conclusion](#conclusion)

## 2. Data Model Overview
#### 2.1 Crew Management Module
##### a. Entity-Relationship Model (ERD)
This Module is designed with a **data-centric** perspective, ensuring seamless integration, scalability, and accessibility of workforce data. It consolidates disparate sources—**Workday** (high management structure), **DH Walk** (crew hierarchy), and **CSV-based new hires**—into a unified data model that support dynamic workforce management. The module is structured into the following key microservices:

- **MS1: Operator Management**  
Ingests and harmonizes employee data from Workday and CSV-based new hires. It serves as the source of truth for operator identity and core attributes, ensuring accurate and consistent employee information across systems.  

- **MS2: Direct Dependents**  
Maintains hierarchical relationships and reporting structures (n+1, n-1, etc.) derived from DH Walk and Workday. The hierarchy can be categorized into two types: ***role-based*** and ***project-based***, providing flexibility in managing organizational structures.  

- **MS3: Operator Skills**  
Tracks operator qualifications, certifications, and training assignments to support continuous skill management and workforce optimization. This ensures that operators are equipped with the necessary skills for their roles.  

- **MS4: Workstation**  
Manages skills associated with workstations, ensuring that each station is aligned with the required competencies and roles for efficient operations.  

- **MS5: Operator Assignment**  
Orchestrates and track new operator assignments across organizational hierarchies, ensuring seamless coordination between differents roles like trainers, shift leaders, team leaders. Leverages Cosmos DB for high-volume, transactional data handling, ensuring scalability and reliability in managing operator assignments.  


##### b. Databases Architecture

| Microservice | Storage Type | Database   | Tables/Containers               |
|---------------------------|------------------------|--------------------------|---------------------------------------------------------------------------------------|
| Operator Management  | SQL      | CWDB  | [aptiv_active_employee](#a-aptiv_active_employee)<br>  [aptiv_new_hires](#b-aptiv_new_hires) |
| Operator Management         | CosmosDB   | CWDB|  [operator_updates](#c-operator_updates)|
| Direct Dependents   | CosmosDB     | CWDB  | [user_role_hierarchy](#e-user_role_hierarchy)<br> [subordinate_roles_counts](#f-subordinate_roles_counts)<br> [teamleader_team_crew](#d-teamleader_team_crew)<br>  |
| Operator Skills        | SQL      | CWDB   | [Operator_skills](#g-operator_skills) |
| Operator Skills            | CosmosDB      | CWDB | [skills_validation](#h-skills_validation)|
| Workstation            | CosmosDB      | CWDB          | [workstation](#i-workstation)<br> [teamleader_line_assignement](#j-teamleader_line_assignement) <br>[zoning](#m-zoning) |
| Workstation            | SQL      | CWDB          | [zoning](#m-zoning) |
| Operator Assignment         | CosmosDB   | CWDB|  [user_validation](#k-user_validation)|
| Operator Assignment         | CosmosDB   | CWDB|  [operator_assignments](#l-operator_assignments)|
| Zoning       | SQL   | CWDB|  [zoning](#m-zoning)|
| Zoning         | CosmosDB   | CWDB|  [zoning_updates](#n-zoning_updates) <br>[create_team](#o-create_team)|

## 3. Detailed Tables Definition
#### 3.1 Indexing & Partitioning Strategies

| **Strategy Type**     | **Details**                                                                                     |
|-----------------------|-------------------------------------------------------------------------------------------------|
| Clustered Indexes     | Primarily on *site* columns to filter data by geographical/operational regions.                 |
| Non-Clustered Indexes | On primary keys and frequently queried fields (e.g., *employee_id*, *team*, *project*).          |
| Partition Keys        | Logical partitioning (using *site*) ensures even data distribution and high query performance.   |

#### 3.2 Crew Management Module
##### a. Aptiv_active_employee
###### Purpose:
Holds active employee data from Workday and DH Walk bronze tables, serving as the authoritative source for employee metadata and role details. It integrates and transforms data from both sources to ensure a unified and consistent representation of employee metadata and organizational hierarchy, spanning from senior management (plant manager, department managers) to operational staff (team leaders and operators). 
###### Schema:

| Column Name     | Data Type | Description                                           |
|----------------|----------|-------------------------------------------------------|
| id     | STRING   | uuid identifier |
| legacy_site     | STRING   | unique employee identifier to be used in mapping purposes|
| workday_id     | STRING   | Workday identifier (nullable for non-Workday records) |
| legacy_id      | INTEGER  | matricule of employee                                   |
| site          | STRING   | Employee’s site (e.g., "MAR Morocco 3")              |
| siteResponsibleId          | STRING   | id of the trainer responsible at the site              |
| department     | STRING   | Department name                                      |
| role          | STRING   | Derived role (e.g., Hr. manager, Team Leader, Operator) |
| firstname      | STRING   | First name                                          |
| lastname       | STRING   | Last name                                           |
| business_title | STRING   | Full job title                                      |
| category       | STRING   | Employee category (Salaried, Direct/Indirect Hourly) |
| hire_date      | DATE     | Hire date |
| email      | STRING     | employee's email |

> <p style="font-size: small;"><em>
> - Clustered Index on legacy_site → Because queries often filter by legacy_site.<br>
> - Non-Clustered Index on department → Because queries often look up employees by department.
> </em></p>

##### b. Aptiv_new_hires
###### Purpose:
Captures onboarding data for new hires. An additional field `in_workday`, flags whether the new hire exists in Workday—determining readiness for shift leader assignment.
###### Schema:

| Column Name  | Data Type | Description                                  |
|-------------|----------|----------------------------------------------|
| id     | STRING   | uuid identifier |
| legacy_site | STRING  | unique employee identifier to be used in mapping purposes |
| new_hire_id | INTEGER  | Matricula of the new hire; equivalent to legacy ID |
| first_name  | STRING   | First name                                  |
| last_name   | STRING   | Surname                                     |
| hiring_date | DATE     | Hiring date                                |
| category    | STRING   | Employee category                          |
| department  | STRING   | Department code or name                    |
| function    | STRING   | Employee role                              |
| Site        | STRING   | New hire’s site                            |
| in_workday  | STRING  | Flag indicating if the new hire is in Workday |

> <p style="font-size: small;"><em>
> - Clustered Index on site → Because queries often filter by site.<br>
> - Non-Clustered Index on employee_id → Because queries often look up employees by ID.
> </em></p>

##### c. operator_updates
###### Purpose:
Its designed to streamline the onboarding of new hires.

###### Document Structure Example:
```json
{
  "id": "ASSIGN-************",    // Unique identifier for the assignment record
  "type": "OperatorDeleted",  
  "timestamp": "2025-02-12T10:00:00Z", 
  "payload": {// Target assignment details
    "legacyId": "OP123",
    "siteId": "SITE-001",
    "department": "Manufacturing",
    "deletedAt": "2024-01-01T00:00:00Z",
    "deletedBy": "<EMAIL>",
    "reason": "User requested deletion",
    "lastKnownState": {
      "firstName": "John",
      "lastName": "Doe",
      "skills": ["Welding", "Assembly"],
      "source": "CW"}
  }
  // or
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "eventType": "OperatorCreated",
  "timestamp": "2025-02-12T10:00:00Z",
  "payload": {
    "firstName": "John",
    "lastName": "Doe",
    "siteId": "SITE-001",
    "department": "Manufacturing",
    "hiringDate": "2024-01-01T00:00:00Z",
    "legacyId": "LEG-001",
    "legacySiteId": "LSITE-001",
    "categories": "DH",
    "operatorFunction": "Senior Operator",
    "source": "CW"
  }
}
```
> <p style="font-size: small;"><em>
> We utilize the Cosmos DB change feed to capture new or updated assignment records in real time. An automated process (via Databricks) retrieves these changes and promptly updates our centralized employee data, ensuring alignment of operator inserts or updates with  details.
> </em></p>

##### d. teamleader_team_crew
###### Purpose:
Captures the team leader's team structure.
###### Document Structure Example:
```json
{
  "teamleader_legacy_site	": "E12345_MAR Moroocco 3",
  "teamleader_name": "cxxxxx",
  "teams": "team 1",
  "department": "E12345_MAR Moroocco 3",
  "department_type": "E12345_MAR Moroocco 3",
  "operator_legacy_site": "cxxxxx",
  "operator_fullname":"xxxxxxxxx",
  "role": "team 1",
  "role_status": "xxx 1",
  "site":"ssssss"
}
```
> <p style="font-size: small;"><em>
> - Partitioning by Site → Ensures efficient filtering at the site level
> </em></p>

##### e. user_role_hierarchy
###### Purpose:
It is designed to maintain and manage the organizational **role-based hierarchy** by capturing reporting relationships, including n+1 (manager) and all subordinate levels (n-1, n-2, etc.). Serving as a centralized reference for mapping reporting structures, it enables efficient navigation of organizational lines and supports role-based decision-making across the organization.
###### Document Structure Example:
```json
{
  "id": "E12345", //employee_id
  "lecacy_site_id":"12132",
  "fullname":"xxxxxxx",
  "department":"yyyyyy",
  "site": "Casablanca",
  "role": "shift leader",
  "manager_lecacy_site_id":"3244",
  "subordinate_crew": [
        {
          "sub_lecacy_site_id": "12132",
          "sub_fullname":"xxxxxxx",
          "sub_role": "Team lead",
          "sub_role_status":"",
          "in_workday":"true or false",
          "category":"",
          "contract_type":"",
          "skills":[skill 1,skille2],
          }]
}
```
> <p style="font-size: small;"><em>
> <p style="font-size: small;"><em>
> - 3 level Hierarchical Partitioning with : <br>
> - lecacy_site_id → Ensures efficient filtering at the top level.<br>
> - department as the second partition key → Groups all work under an employee_id by department.<br>
> - role as the third partition key → Further refines data organization within role.<br>
> - Supports efficient hierarchy traversal for team structures and reporting.<br>
> </em></p>

##### f. subordinate_roles_counts
###### Purpose:
It maintains **role-based hierarchy statistics** by tracking the counts of subordinates for each role within the organizational structure. This table provides essential insights into role distribution and supports decision-making processes.
###### Document Structure Example:
```json
{
  "id": "E12345",
  "lecacy_site_id": "12345",
  "role": "plant manager",
  "subordinate_roles_counts": [
    {
      "subordinate_role": "team leader",
      "subordinate_role_counts": 3}
  ]
}
```
> <p style="font-size: small;"><em>
> - Hierarchical Partitioning with lecacy_site_id → Ensures efficient filtering at the top level.<br>
> - role as the second partition key → Groups all work under a role.<br>
> - Supports efficient hierarchy traversal for team structures and reporting.<br>
> </em></p>

##### g. Operator_skills
###### Purpose:
It maintains operator skill and certification data, providing a detailed overview of each employee's competencies and qualifications.
###### Schema:

| Column Name    | Data Type | Description                      |
|--------------|----------|----------------------------------|
| id     | STRING   | uuid identifier |
| legacy_site     | STRING   | unique employee identifier to be used in mapping purposes|
| workday_id     | STRING   | Workday identifier (nullable for non-Workday records) |
| legacy_id      | INTEGER  | matricule of employee                                   |
| name     | STRING   | employee fullname |
| Site        | STRING   | Employee’s site                   |
| qualification_type | STRING   | Employee’s qualification_type      |
| skill      | STRING   | skill type within project |
| process_type | STRING   | Employee’s process_type      |

> <p style="font-size: small;"><em>
> - Clustered Index on site → Because queries often filter by site.<br>
> - Non-Clustered Index on employee_id → Because queries often look up employees by ID.
> </em></p>

##### h. skills_validation
###### Purpose:
It ensures data validation for skills-related fields.
###### Document Structure Example:
```json
{
  "id":"ASSIGN-************",                      // Operator's unique ID from MS1
  "eventType": "new_skills",  
  "timestamp": "2025-02-12T10:00:00Z", 
  "payload": {// Target assignment details
    "legacySiteId":"legacy_site",
    "site": "SiteA",
    "skills": ["Skill1", "Skill2"]
  } 
}
```
> <p style="font-size: small;"><em>
> We utilize the Cosmos DB change feed to capture new or updated assignment records in real time. An automated process (via Databricks) retrieves these changes and promptly updates our centralized employee data, ensuring alignment of operator inserts or updates with  details.
> </em></p>

##### i. workstation
###### Purpose:
It maintains workstation-related data, including roles and skills associated with each station. This table supports efficient workstation management, role assignment, and skill alignment, ensuring optimal resource utilization within the site.
###### Schema: (it gonna be in cosmos)

| Column Name    | Data Type | Description               |
|--------------|----------|---------------------------|
| customer     | STRING   | customer name                                |
| projet      | STRING   | project assigned to the station |
| famille     | STRING   | famille assigned to the station |
| value_stream      | STRING   | value_stream assigned to the station |
| area      | STRING   | area assigned to the station |
| station      | STRING   | Station                   |
| site        | STRING   | Employee’s site           |
| department | STRING   | department           |
| role_station | STRING   | Role of station           |
| skills      | Array   | Station skills            |
| criticity      | STRING   | Station criticity            |
| capacity      | INTEGER   | Station capacity            |
| is_polyvalent | BOOLEAN     |    |
| is_rework | BOOLEAN     |    |
| effective_date | DATE     | effective_date of document |
| version | STRING     | version of document |

> <p style="font-size: small;"><em>
> - Clustered Index on station → Because queries often filter by station.<br>
> - Non-Clustered Index on project, familly, value_stream, area, version → Because queries often look up project, familly, value_stream, area, version.
> </em></p>

##### j. teamleader_line_assignement

###### Purpose:  
It provide the operators line assignment structure for under each team leader.  

###### Document Structure Example:
```json
{
  "id	": "E12345dzdede",
  "teamleader_legacy_site": "cxxxxx",
  "teamleader_fullname": "xxx 1",
  "customer": "xxx 1",
  "projet": "xxx 1",
  "famille	": "xxxxx",
  "value_stream": "xxxxx",
  "area": "xxxxx",
  "team": "team 1",
  "station": "xxx 1",
  "station_role": "xxxxx",
  "operator_legacy_site":"xxxxx",
  "operator_fullname": "xxxxx ",
  "version":"nxxxxx",
  "department": "xxxxx 1",
  "effective_date": 2025-03-03
}
```
> - Hierarchical Partitioning with teamleader_legacy_site → Ensures efficient filtering at the top level.<br>
> - projet as the second partition key → Groups all work under a projet.<br>
> - famille as the second partition key → Groups all work under a famille.<br>
> - Supports efficient hierarchy traversal for team structures and reporting.<br>

##### k. user_validation

###### Purpose:  
It ensures data validation for user-related fields, maintaining consistency and integrity within the system. This table helps validate employee information, track reporting relationships, and determine user activity status for access control and workflow assignments.  
###### Document Structure Example:
```json
{
  "id	": "E12345_MAR Moroocco 3",
  "legacy_site": "cxxxxx",
  "legacy_id": 1234,
  "first_name": "xxx 1",
  "last_name	": "E12345_MAR Moroocco 3",
  "subordinate_id": ["legacy_site_id","rzzll"],
  "skills": ["skills1","rzzll"],
  "category": "team 1",
  "department": "xxx 1",
  "role":"coordinator",
  "site": "xxx 1",
  "in_workday": False or True,
  "is_rework": true or false
}
```
> <p style="font-size: small;"><em>
> - Partitioning by Site → Ensures efficient filtering at the site level
> </em></p>

##### l. operator_assignments
###### Purpose:
Its designed to manage and track the assignment of operators across organizational hierarchies, ensuring seamless coordination between differents roles like trainers, shift leaders, team leaders. This structure supports dynamic role assignment, enhances operational clarity, and ensures optimal resource allocation within the site.
###### Document Structure Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToEmployee", 
  "timestamp": "2025-02-12T10:00:00Z",
  "site": "MAR_Morocco  1",
  "payload": {
    "assigner": {
      "legacySiteId": "123_MOROCCO MAR 1",
      "role": "TRAINER_RESPONSIBLE",
      "firstName": "John",
      "lastName": "Doe",
      "department": "engineering"
    },
    "assignTo": {
      "legacySiteId": "123_MOROCCO MAR 1",
      "role": "TRAINER_RESPONSIBLE",
      "firstName": "John",
      "lastName": "Doe",
      "department": "engineering"
    },
    "assignee": {
      "legacySiteId": "123_MOROCCO MAR 1",
      "role": "TRAINER_RESPONSIBLE",
      "firstName": "John",
      "lastName": "Doe",
      "department": "engineering"
    }
  }
}
```
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToWorkstation", 
  "timestamp": "2025-02-12T10:00:00Z",
  "site": "MAR_Morocco  1",
  "payload": {
    "assigner": {
      "legacySiteId": "123_MOROCCO MAR 1",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe"
    },
    "workstation": {
      "customer": "customer",
      "project": "Project A",
      "family": "Family A",
      "valueStream": "Value Stream A",
      "meDefinition": "version",
      "effectiveDate":"23/03/2025",
      "area": "Area A",
      "station": "CELL 1",
      "stationId": "xxxxxx",
      "roleStation": "ME STRUCTURE",
      "department":"ssssssss"
    },
    "assigned": {
      "legacySiteId": "789_MOROCCO MAR 1",
      "role": "OPERATOR",
      "firstName": "Alice",
      "lastName": "Johnson",
      "team": "Team A",
      "teamLeaderLegacySite": "456_MOROCCO MAR 1",
      "teamLeaderRole": "team leader",
      "teamLeaderFirstName": "Bob",
      "teamLeaderLastName": "Smith",
      "teamLeaderDepartment": "assembly"
    }
  }
}
```
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "ReassignedToWorkstation", 
  "timestamp": "2025-02-12T10:00:00Z",
  "site": "MAR_Morocco  1",
  "payload": {
    "assigner": {
      "legacySiteId": "123_MOROCCO MAR 1",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe"
    },
    "workstation": {
      "customer": "customer",
      "project": "Project A",
      "family": "Family A",
      "valueStream": "Value Stream A",
      "meDefinition": "version",
      "effectiveDate":"23/03/2025",
      "area": "Area A",
      "station": "CELL 1",
      "stationId": "xxxxxx",
      "roleStation": "ME STRUCTURE",
      "department":"ssssssss"
    },
    "assigned": {
      "legacySiteId": "789_MOROCCO MAR 1",
      "role": "containment operator",
      "firstName": "Alice",
      "lastName": "Johnson",
      "team": "Team B",
      "teamLeaderLegacySite": "456_MOROCCO MAR 1",
      "teamLeaderRole": "team leader",
      "teamLeaderFirstName": "Bob",
      "teamLeaderLastName": "Smith",
      "teamLeaderDepartment": "assembly"
    },
    "unassigned": {
      "legacySiteId": "123_MOROCCO MAR 1",
      "role": "OPERATOR",
      "firstName": "John",
      "lastName": "Doe",
      "team": "Team A" // if role = containement in re-assigne we remove the team
    }
  }
}
```

```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToTeam", 
  "timestamp": "2025-02-12T10:00:00Z",
  "site": "MAR_Morocco  1",
  "payload": {
      "assigner": {
        "legacySiteId": "string",
        "role": "string",
        "firstName": "string",
        "lastName": "string",
        "department": "string"
      },
      "team": {
        "teamName": "string"
      },
      "assignee": {
        "legacySiteId": "string",
        "role": "string",
        "firstName": "string",
        "lastName": "string",
        "department": "string"
      },
  }
}
```

> <p style="font-size: small;"><em>
> We utilize the Cosmos DB change feed to capture new or updated assignment records in real time. An automated process (via Databricks) retrieves these changes and promptly updates our centralized employee data, ensuring alignment of operator assignments with project, value stream, area, and team details.
> </em></p>

##### m. zoning

###### Purpose:  
Providing zonning data
###### Schema:

| Column Name     | Data Type | Description                           |
|---------------|----------|------------------------------------------|
| uuid    | STRING   | uuid                     |
| customer     | STRING   | customer name                                |
| projet     | STRING   | project name                                |
| famille | STRING  | family name                                      |
| value_stream | STRING  | value_stream                               |
| area  | STRING   | Area                                             |
| Coor_legacy_site_id  | STRING   | unique ID of the coordinator      |
| Coor_fullname   | STRING   | the coordinator fullname               |
| SL_legacy_site_id  | STRING   | unique ID of the shiftleader        |
| SL_fullname   | STRING   | the shiftleader fullname                 |
| TL_legacy_site_id  | STRING   | unique ID of the teamleader         |
| TL_fullname   | STRING   | the teamleader fullname                  |
| team_name    | STRING   | the name of the team                      |
| department_Coor    | STRING   | the name of the department                      |
| department_SL    | STRING   | the name of the department                      |
| department_TL    | STRING   | the name of the department                      |



> <p style="font-size: small;"><em>  
> - Clustered Index on projet → Ensures quick lookup for projet.<br>  
> - Non-Clustered Index on Coor_legacy_site_id,SL_legacy_site_id,TL_legacy_site_id, familly, value_stream, area, version → Because queries often look up project, familly, value_stream, area, version.  
> </em></p>

##### n. zoning_updates
###### Purpose:
Its designed to manage and track the assignment of coordinators, shif leaders and team leaders with their teams across each zone.
###### Document Structure Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToZone", // 
  "timestamp": "2025-02-12T10:00:00Z",
  "payload": {
    "uuid":"xxxxxx",
    "customer":"xxxxx",
    "project":"xxxxx",
    "family":"xxxxx",
    "value_stream":"xxxxx",
    "area":"xxxxx",
    "Coor_legacy_site_id":"xxxxx",
    "Coor_fullname":"xxxxx",
    "SL_legacy_site_id":"xxxxx",
    "SL_fullname":"xxxxx",
    "TL_legacy_site_id":"xxxxx",
    "TL_fullname":"xxxxx",
    "team_name":"xxxxx",
    "site": "xxxxxx"
  }
}
```
> <p style="font-size: small;"><em>
> We utilize the Cosmos DB change feed to capture new or updated assignment records in real time. An automated process (via Databricks) retrieves these changes and promptly updates our centralized employee data, ensuring alignment of assignments with project, value stream, area, and team.
> </em></p>

##### o. create_team
###### Purpose:
Its designed to manage and track the assignment of coordinators, shif leaders and team leaders with their teams across each zone.
###### Document Structure Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "CreateTeam", // 
  "timestamp": "2025-02-12T10:00:00Z",
  "payload": {
    "TL_legacy_site_id":"xxxxx",
    "TL_fullname":"xxxxx",
    "team_name":"xxxxx",
  }
}
```
> <p style="font-size: small;"><em>
> We utilize the Cosmos DB change feed to capture new or updated assignment records in real time. An automated process (via Databricks) retrieves these changes and promptly updates our centralized employee data, ensuring alignment of assignments with project, value stream, area, and team.
> </em></p>

## Conclusion

This documentation presents a comprehensive and scalable data model architecture specifically designed for the **Crew Management** module. By strategically leveraging dedicated SQL databases and Cosmos DB, the architecture delivers a solution that not only meets current operational demands but also provides the flexibility to evolve with future business needs. This approach ensures a reliable, efficient, and future-proof foundation, aligning with the organization’s objectives and supporting long-term growth and scalability.
