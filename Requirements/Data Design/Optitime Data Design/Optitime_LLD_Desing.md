# Optitime Low-Level Design (LLD)

## Overview
This document provides a comprehensive overview of the **Optitime Data Integration Design**, detailing the low-level architecture: data flow, encryption/decryption workflows, and data transformation processes within the Azure ecosystem. The solution is built on a **Lakehouse Architecture** and leverages a combination of Azure-native services, including ***Azure Data Lake Storage Gen2***, ***Azure Databricks*** and ***Azure Cosmos DB***, alongside third-party tools like ***Qlik*** for analytics and ***machine learning frameworks*** for advanced modelling. Together, these components implement a secure, scalable, and automated **E**xtract **L**oad **T**ransform (**ELT**) pipeline. 
The design incorporates robust data orchestration, storage, and processing mechanisms to enable seamless integration of Workday data into the Connected Workers platform.

**Key Goals:**
- **Incremental, reliable ingestion** of JSON payloads from Optitime APIs  
- **Automated orchestration** with Databricks Workflows  
- **Layered processing** (bronze, silver, gold) with data quality, enrichment, and conformance  
- **Serving** via ML feature tables, Qlik dashboards, and Cosmos DB  

---

## Dictionary of Optitime Data Fields

Below is an excerpt of the field dictionary. 

## 2. Dictionary of Optitime Data Fields

| Field                     | Description                                                        | Source API         |
|---------------------------|--------------------------------------------------------------------|--------------------|
| **Employee ID**           | Unique Optitime identifier for each employee                       | Realtime Data API  |
| **Legacy ID**             | Matricule identifier (from Sage/Workday) used per site            | Realtime, Batch API|
| **Site**                  | Code or name of the location/site where the event occurs           | Realtime, Batch API|
| **First Name**            | Employee’s given (first) name                                      | Realtime, Batch API|
| **Last Name**             | Employee’s family (last) name                                      | Realtime, Batch API|
| **Time in (Badged Entrance)**  | Timestamp when the employee badges in (entrance event)         | Realtime Data API  |
| **Time out (Badged Exit)**     | Timestamp when the employee badges out (exit event)            | Realtime Data API  |
| **Reader Location**       | Identifier of the badge reader (door/gate) where the badge event occurred | Realtime Data API  |
| **Operators Reporting line** | Code or name of the employee’s operational reporting line       | Batch API          |
| **TLO (Hours)**           | TLO hours for the period                       | Batch API          |
| **Theoretical hours**     | Expected working hours (scheduled) for the same period              | Batch API          |
| **Absenteeism Hours**     | Total hours absent (leave, no‐show) within the period               | Batch API          |

---

## Architecture Diagram

The architecture follows this flow:

![optitime Data Design](Optitime_Design.png)
---

## Key Technologies

| Component                          | Role                                                                          |
|------------------------------------|-------------------------------------------------------------------------------|
| **Optitime Real-time & Batch APIs**| Source systems exposing JSON over REST                                       |
| **Databricks Autoloader**          | Incremental ingestion from staging into Bronze Delta tables                   |
| **Databricks Workflows**           | Orchestration: Bronze → Silver → Gold notebooks                               |
| **Azure Data Lake Storage Gen2**   | Delta storage for Bronze, Silver, Gold layers                                 |
| **Azure Cosmos DB (via Spark)**    | Real-time sink of curated records for operational use                         |
| **Azure Machine Learning**         | Downstream model training & feature store                                     |
| **Qlik Sense**                     | BI reporting on Gold Delta tables                                             |

---

## Data Flow

1. **Data Extraction**  
   - Real-time & Batch GET API calls to Optitime → JSON files dropped into two Blob Storage containers.

2. **Incremental Ingestion**  
   - Databricks **Autoloader** streams new JSON files from staging into the **Bronze** Delta table.

3. **Orchestration**  
   - A Databricks **Workflow**  executes three notebooks in sequence:  
     1. **Bronze Notebook** (raw ingestion + schema capture)  
     2. **Silver Notebook** (validation, dedupe, enrichment)  
     3. **Gold Notebook** (aggregations, conformed business views)  

4. **Serving**  
   - **Cosmos DB sink**: Spark writes curated JSON records for low-latency operational reads  
   - **ML**: Gold tables feed feature engineering pipelines  
   - **Qlik**: Dashboards query Gold Delta tables directly  

---

### **Bronze Layer**

The Bronze Layer is the foundational layer in the Lakehouse architecture, where raw, unprocessed data is ingested and stored in Delta format. It serves as the single source of truth, preserving the original data as ingested from various source systems. Delta tables are created in Data Lake Gen2 to serve as the raw data repository. This is a one-time process that establishes the foundational structure for data storage. Once the tables are set up, they are populated using an upsert mechanism, which ensures that new records are added, and existing ones are updated as necessary. To handle incoming data efficiently, an incremental process is implemented using Autoloader's streaming mechanism, enabling seamless ingestion and synchronization of data as it arrives. 
The data from the Bronze layer is then sent to the Silver layer in batch to prepare it for further processing in subsequent layers.

#### **Databricks Autoloader for Incremental Data Ingestion**

Databricks Autoloader is used for continuous and efficient ingestion of incremental data into the Bronze Layer.

a. **File Detection**:
   - Autoloader monitors a specified path in Azure Blob Storage or Azure Data Lake Storage Gen2.
   - It detects newly added or updated files in near real-time using the cloudFiles source option.

b. **Schema Evolution**:
   - Automatically infers and evolves schemas as new columns or changes are detected in the incoming data.
   - Prevents manual intervention when schema changes occur.

c. **Incremental Ingestion**:
   - Processes only new or updated files, avoiding redundant reprocessing.
   - Utilizes checkpoint files to track processed files and ensure idempotency.

#### Upserts in the Bronze Layer

Upserts (updates and inserts) are critical for maintaining an accurate and up-to-date Bronze Layer. They ensure that new and modified data is reflected correctly in the Delta tables.

- **Merge Operation**: A Delta Lake MERGE INTO statement is used to upsert data into the primary Bronze table (`bronze_table`).
   - **WHEN MATCHED**: Updates existing records in the Bronze table if changes are detected in the source.
   - **WHEN NOT MATCHED**: Inserts new records into the Bronze table.

- **Schema Enforcement**:  The schema is enforced during the merge to maintain consistency and prevent invalid data from being ingested.

- **Checkpointing and Idempotency**:  
   - Checkpoint files and Delta transaction logs ensure that:
   - Duplicate processing is avoided.
   - The pipeline can recover seamlessly from failures.

- **Advantages of Upserts**:  
   - Data Freshness: Ensures the Bronze table always reflects the latest source data.
   - Efficiency: Processes only new and modified records, reducing compute overhead.
   - Accuracy: Maintains data consistency by updating existing records and adding new ones as needed.

#### Example Configuration in Databricks

```python
from pyspark.sql.functions import *

autoloader_df = (spark.readStream
                 .format("cloudFiles")
                 .option("cloudFiles.format", "csv")
                 .option("cloudFiles.schemaLocation", "<schema_checkpoint_path>")
                 .option("cloudFiles.checkpointLocation", "<checkpoint_path>")
                 .load("<source_path>"))

autoloader_df.writeStream \
    .format("delta") \
    .foreachBatch(lambda batch_df, batch_id: batch_df.createOrReplaceTempView("batch_data")  # Create temp view for the batch
                  # Perform the MERGE operation to upsert into the bronze table
                  spark.sql("""
                      MERGE INTO bronze_table AS target
                      USING batch_data AS source
                      ON target.id = source.id
                      WHEN MATCHED THEN UPDATE SET *
                      WHEN NOT MATCHED THEN INSERT *
                  """)) \
    .option("checkpointLocation", "/path/to/checkpoints")  # Ensure checkpointing is enabled
    .outputMode("update")  # Ensures that only updates/inserts happen in each micro-batch
    .start()
```

### **Silver Layer**

In the Silver layer, Silver Delta tables are created to process and transform raw data into structured formats. This transformation is achieved using the upsert method across multiple staging tables (clean tables and join tables). After the data is cleaned, the process involves joining three key tables: `workday_optitime`, `workday_sage`, and `workday_sage_optitime`. Some of these staging tables are sinked to Cosmos DB using the Spark driver for Cosmos DB, enabling the dev-team to efficiently access and query the data for downstream applications.

#### Processing Steps

a. **Data Validation**: Ensures that incoming data meets the predefined quality standards:
   - **Schema Validation**: Verify that the data adheres to the expected schema. For example, required fields such as employee ID or timestamp are checked for presence and data type correctness.
   - **Domain Validation**: Confirm that specific fields fall within expected ranges. For instance, dates must be valid and fall within acceptable ranges, and numeric fields like hours worked cannot be negative.
   - **Custom Rules**: Implement business-specific rules, such as ensuring job titles match predefined values or rejecting rows with invalid employee status.
   - **Implementation**: Validation is typically performed using Databricks notebooks with PySpark or SQL logic. Invalid rows are redirected to error tables or logs for further inspection.

b. **Deduplication**: Ensures the removal of duplicate records based on defined keys.
   - **Duplicate Detection**: Use composite keys such as employee ID, date, and job title to identify duplicate records to remove them.
   - **Record Prioritization**: When duplicates are detected, prioritize records based on attributes like the most recent timestamp or data source reliability.
   - **Implementation**: PySpark functions or SQL logic.

c. **Data Enrichment and Consolidation**: Adds context to data and integrates data from multiple sources to create unified tables:
   - **Join Operations**: Merge datasets using Left Outer join by employee id from `Workday_cleansed`, `Sage_cleansed`, and `Optitime_cleansed` to produce consolidated tables such as `Workday_Optitime` or `Workday_Sage_Optitime`.
   - **Derived Fields**: Add new attributes based on existing data.
   - **Key Alignment**: Ensure consistent primary and foreign keys across datasets for seamless integration.
   - **Implementation**: Enrichment is performed using PySpark functions/Spark SQL join operations or transformations in Databricks notebooks.

d. **Sinking into Cosmos db**: We chose to use Change Data Feed (CDF) to send incremental changes from the silver layer to Cosmos DB because Cosmos DB lacks native versioning capabilities. By leveraging CDF in Delta format, we can efficiently identify and propagate only the changes (inserts, updates, and deletes) from the silver layer, ensuring that Cosmos DB stays in sync without requiring a full data reload. This approach minimizes data transfer costs and processing time while maintaining data consistency. Additionally, CDF allows us to track historical changes in the silver layer, providing flexibility and reliability for downstream systems that depend on Cosmos DB.

We chose batch processing to transform data from the silver layer to the gold layer because the gold-layer transformations and aggregations depend on data with less frequent changes in the silver layer. By processing data in batches, we avoid the overhead of continuous updates while ensuring efficient and reliable data transformations. Additionally, the Delta format's versioning capabilities allow us to track and manage changes over time, ensuring data consistency, reproducibility, and the ability to roll back if needed. This approach supports stable and accurate gold-layer tables for downstream analytics and reporting.


### **Gold Layer**

In the Gold layer, advanced aggregations and calculations are performed using PySpark functions or SQL logic to create business-ready tables tailored for analytics and decision-making. These tables are modeled (Star Schema) and designed to support analytics and reporting tools like Qlik. Additionally, some of these tables may be sinked to Cosmos DB for operational applications, while others might be leveraged in machine learning workflows, enabling predictive insights. Materialized views will be applied across these aggregated tables, precomputing and storing query results to further enhance performance for operational reporting, machine learning, and business intelligence.


#### Processing Steps

a. **Aggregation**  
   - **Objective**: Aggregate data from the Silver layer's enriched and consolidated tables.
   - **Details**:  
     - Perform grouping, summarization, and calculation of key metrics (e.g., averages, sums, counts, or time-series aggregates).  
     - Apply business rules and calculations specific to organizational needs.
   - **Examples**:  
     - Calculate employee working hours per department.  
     - Summarize financial transactions across regions.

b. **Data Conformance**  
   - **Objective**: Ensure datasets meet enterprise standards for schema consistency and naming conventions.
   - **Details**:  
     - Align table structures with downstream systems (e.g., Cosmos DB schema).  
     - Rename columns to meet standard business terminology.  
     - Map fields to consistent data types (e.g., ensuring all timestamps use UTC).

c. **Partitioning and Indexing**  
   - **Objective**: Optimize data storage and retrieval for performance.
   - **Details**:  
     - Partition tables based on access patterns (e.g., by date or region).  
     - Use Z-Order indexing in Delta tables to speed up queries.

d. **Quality Checks**  
   - **Objective**: Verify data accuracy and completeness.
   - **Details**:  
     - Apply checks to ensure metrics align with expectations (e.g., comparing row counts between Silver and Gold layers).  
     - Flag and isolate anomalies for further review.

e. **Sinking Data to External Systems**  
   - **Objective**: Export Gold layer datasets to downstream systems for consumption.
   - **Details**:  
     - Use Databricks Spark driver to sink data into Azure Cosmos DB for operational reporting.  
     - Provide datasets in Qlik-compatible formats for BI dashboards.  
     - Export data to ML pipelines for advanced analytics and predictive modeling.

#### Gold Layer Table Types

- **Aggregated Tables for Cosmos DB**:  
  Contain operational datasets tailored for transactional and reporting use cases.  
  **Example**: Employee attendance summaries or financial records.

- **Aggregated Tables for ML**:  
  Serve as feature stores for ML workflows.  
  **Example**: Historical time-series data for anomaly detection models.

- **Aggregated Tables for BI Tools (e.g., Qlik View)**:  
  Optimized for ad hoc queries and visualization tools, utilizing a star schema with fact and dimension tables.  
  **Example**: Sales summaries by product category and region.

#### Materialized Views:
- In our project, materialized views will be used in the Gold layer to improve performance for frequently queried aggregated data. Materialized views store the result of a query physically, enabling faster access to precomputed aggregates and calculations. This is particularly useful for complex reports and dashboards that require frequent access to the same aggregated data, ensuring efficient performance even as the data grows.


---

## Conclusion

This design ensures a **secure, scalable, and automated pipeline** to handle Workday data efficiently. Data flows through multiple layers of transformation, maintaining data integrity and delivering valuable insights for downstream analytics.

---
