# Workday Data Design Documentation

## Overview

This document provides a comprehensive overview of the **Workday Data Integration Design**, detailing the low-level architecture: data flow, encryption/decryption workflows, and data transformation processes within the Azure ecosystem. The solution is built on a **Lakehouse Architecture** and leverages a combination of Azure-native services, including ***Azure Data Lake Storage Gen2***, ***Azure Databricks***, ***Azure Key Vault***, and ***Azure Cosmos DB***, alongside third-party tools like ***Qlik*** for analytics and ***machine learning frameworks*** for advanced modelling. Together, these components implement a secure, scalable, and automated **E**xtract **L**oad **T**ransform (**ELT**) pipeline. 
The design incorporates robust data orchestration, storage, and processing mechanisms to enable seamless integration of Workday data into the Connected Workers platform.

---

## Dictionary of Workday Data Fields

| Field                      | Explanation                                                                                       | ELT Transformation                                                                                                                                                     |
|----------------------------|---------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Employee ID                | Unique identifier assigned to each employee in Workday.                                          | No                                                                                                                                                                    |
| Legacy ID                  | Legacy identifier                                                                                | No                                                                                                                                                                    |
| Status                     | Employee status (e.g., Active, Terminated, On Leave).                                            | Ignore records with Prehire; map Terminated to Terminated Status; other statuses to Active. For External Staff – Change to Active or Terminated                       |
| Employee Type              | Classification of the employee (e.g., Regular, Intern, Contractor). NB : equivalent to Contract Type N°                             | Map "Intern" to Intern or intrn; other employees = Regular; Outsourced staff = Contractor Agency.                                                                     |
| Country                    | Country where the employee is located.                                                          | Convert to ISO-2 format.                                                                                                                                              |
| Last Name                  | Employee’s preferred last name, including special language characters.                           | No                                                                                                                                                                    |
| First Name                 | Employee’s preferred first name, including special language characters.                          | No                                                                                                                                                                    |
| Company                    | The company assigned to the employee.                                                           | TBD.                                                                                                                                                                  |
| Cost Center                | The employee’s assigned cost center or department.                                              | TBD.                                                                                                                                                                  |
| Manager Employee ID        | ID of the employee’s manager; extracted from the supervisory organization field.                 | No                                                                                                                                                                    |
| Job Title                  | The employee’s job title.                                                                       | No                                                                                                                                                                    |
| Management Level           | The employee’s career stage or level in the organization.                                       | Map per Appendix                                                                                                                                                      |
| Business Title             | The employee’s business title.                                                                  | No                                                                                                                                                                    |
| Termination Date           | The date the employee's employment ends.                                                        | Format to MM/DD/YYYY; adjust for invalid dates by clearing if hire date > termination date. For all other dates, subtract 1 day to get the true termination date.     |
| Hire Date                  | The most recent hire date of the employee.                                                      | Format to MM/DD/YYYY.                                                                                                                                                 |
| Primary Work Email         | The employee’s email address for official communication.                                        | No                                                                                                                                                                    |
| Home Address               | The employee’s personal residential address.                                                    | No                                                                                                                                                                    |
| Work Address               | The employee’s professional address.                                                            | No                                                                                                                                                                    |
| Home Phone                 | The employee’s personal contact number.                                                         | No                                                                                                                                                                    |
| National ID Number         | The employee’s CIN.                                                                             | No                                                                                                                                                                    |
| Social Security Number     | The employee’s social security number.                                                          | No                                                                                                                                                                    |
| Retirement Number          | The number assigned to the employee for retirement benefits.                                    | No                                                                                                                                                                    |
| Insurance Number           | The ID for the employee’s insurance plan.                                                       | No                                                                                                                                                                    |
| Badge System ID            | The employee’s security badge identifier.                                                       | No                                                                                                                                                                    |
| Payroll ID                 | Identifier for payroll processing.                                                              | No                                                                                                                                                                    |
| Degrees                    | The highest level of education attained by the employee.                                        | No                                                                                                                                                                    |
| Job History                | Record of the employee's work history, limited to Aptiv job history for privacy compliance.     | No                                                                                                                                                                    |
| Supervisory Organization   | Organizational hierarchy or supervisory group for the employee.                                 | No                                                                                                                                                                    |
| Worker Type                | Classification as Employee or Contingent.                                                       | No                                                                                                                                                                    |
| FTE%                       | Full-time equivalent percentage for the employee (e.g., 50%, 100%).                             | No                                                                                                                                                                    |
| Location Name              | Name of the employee’s work location.                                                           | No                                                                                                                                                                    |
| Contract End Date          | The end date of the employee’s contract.                                                        | Format to MM/DD/YYYY.                                                                                                                                                                    |
| Original Hire Date         | The original hire date of the employee.                                                         | Format to MM/DD/YYYY.                                                                                                                                                                    |
| Continuous Service Date    | The date marking the start of the employee’s continuous service.                                | Format to MM/DD/YYYY.                                                                                                                                                                    |
| Length of Service          | The seniority level or years of service for the employee.                                       | No                                                                                                                                                                    |
| Time in Position           | The date the employee began their current position.                                             | No                                                                                                                                                                    |
| Time in Job Profile        | The date the employee began their current job profile.                                          | No                                                                                                                                                                    |
| Gender                     | The employee’s gender, reviewed for privacy compliance.                                         | No                                                                                                                                                                    |
| Age                        | The employee’s age, reviewed for privacy compliance.                                            | No                                                                                                                                                                    |
| Date of Birth              | The employee’s birth date, reviewed for privacy compliance.                                     | Format to MM/DD/YYYY.                                                                                                                                                                    |
| City of Birth              | The employee’s birth city, reviewed for privacy compliance.                                     | No                                                                                                                                                                    |
| Primary Nationality        | The employee’s nationality or citizenship, reviewed for privacy compliance.                     | No                                                                                                                                                                    |
| Cost Center Name           | Name of the cost center or department assigned to the employee (e.g., SPS EDS ASSEMBLY-5-Morocco). | No                                                                                                                                                                    |
| Transfer Date              | The date marking the employee's transfer (location or position change).                         | Format to MM/DD/YYYY.                                                                                                                                                                    |
| Remaining Holidays         | The number of vacation or holiday days remaining for the employee.                              | No                                                                                                                                                                    |
| Pay Rate Type              | Employee classification based on pay rate type (e.g., Salaried, Direct Hourly, Indirect Hourly).| No                                                                                                                                                                    |
| Contract ID                | The contract ID of the employee.                                                                | No                                                                                                                                                                    |
| Bank Name                  | The name of the employee’s bank for payroll deposits.                                           | No                                                                                                                                                                    |
| Account Number             | The employee’s direct deposit account number for payroll processing.                            | No                                                                                                                                                                    |
| Civility                   | The employee’s title or form of address (e.g., Mr., Ms.).                                       | No                                                                                                                                                                    |
| Manager Name               | The name of the employee’s manager, extracted from Supervisory Organization or Manager Level 1 Field. | No                                                                                                                                                                    |

---

## Key Technologies in the architecture

| Component                        | Description                                                   |
|----------------------------------|---------------------------------------------------------------|
| **Workday**                      | Employee Master Data source system.          |
| **Middleware**                   | Serves as a network bridge, securely sending files to Azure Blob via SFTP.|
| **SFTP**                         | Secure File Transfer Protocol.|
| **Azure Blob Storage**           | Storage location for encrypted and decrypted files.           |
| **Azure Key Vault**              | Manages and securely stores decryption keys.                  |
| **Databricks Workflows**         | Orchestrates the ELT process.       |
| **Python Decryption Code**       | Script used to decrypt the encrypted files.                   |
| **Azure Data Lake Storage Gen2** | Hierarchical storage for Bronze, Silver, and Gold data.       |
| **Databricks Notebooks**         | Transform and process data across Bronze, Silver, and Gold.   |
| **Databricks Autoloader**        | Incremental refresh for new files.            |
| **Azure Cosmos DB**              | Stores processed data for the microservices.|
| **ML**                           |Analytics tool for machine learning driven insights.            |
| **Qlik**                         | BI tool used for data visualization and reporting.            |

---

## Architecture Diagram

The architecture follows this flow:

![Workday Data Design](Workday_Low_Level_Design.png)
---

## Data Flow

1. **File Ingestion**:
Files are sent from the Workday system to a **Blob Storage SFTP Server** and defined by the following characteristics:
   - **Filename pattern**: "wkday_employee_DDMMYYYY_HHMMSS.csv" and "wkday_employee_JobHistory_DDMMYYYY_HHMMSS.csv".
   - **Output**: Encrypted CSV files (separated by ",").
   - **Encryption Standard**: Encryption at rest (PGP) & Encryption at Transit (AES 256).
   - **Frequency**: Daily Transfer.
   - **Protocol**: SFTP for file transfer.
   - **File header**: Column names.
   - **File footer**: Number of records.
  
2. **Middleware**:
Middleware serves as an intermediate step to facilitate and control the file transfers under the following details:
   - **Description**: Middleware layer processes incoming encrypted files before storing them.
   - **Output**: CSV files remain encrypted with PGP and AES 256-bit encryption.
   - **Protocol**: SFTP transfer to Azure Blob Storage.
   - **Transformations**: No.

3. **Databricks Workflows**:
   - Databricks Workflows orchestrates the process, using **Event Driven** feature to trigger a **File Arrival** event.
   - This File Arrival event triggers the execution of decryption job as well as bronze job.

4. **File Decryption**:
   - Decryption is performed via **Python code** that retrieves the decryption keys stored in Azure Key Vault.
   - **Output**: Decrypted CSV files are stored in **Blob Storage for Decrypted Files**.

5. **Lakehouse Architecture**:
The architecture follows this flow:

![Lakehouse architecture](Lakehouse%20Architecture.png)
  
### **Bronze Layer**

The Bronze Layer is the foundational layer in the Lakehouse architecture, where raw, unprocessed data is ingested and stored in Delta format. It serves as the single source of truth, preserving the original data as ingested from various source systems. Delta tables are created in Data Lake Gen2 to serve as the raw data repository. This is a one-time process that establishes the foundational structure for data storage. Once the tables are set up, they are populated using an upsert mechanism, which ensures that new records are added, and existing ones are updated as necessary. To handle incoming data efficiently, an incremental process is implemented using Autoloader's streaming mechanism, enabling seamless ingestion and synchronization of data as it arrives. 
The data from the Bronze layer is then sent to the Silver layer in batch to prepare it for further processing in subsequent layers.

#### **Databricks Autoloader for Incremental Data Ingestion**

Databricks Autoloader is used for continuous and efficient ingestion of incremental data into the Bronze Layer.

a. **File Detection**:
   - Autoloader monitors a specified path in Azure Blob Storage or Azure Data Lake Storage Gen2.
   - It detects newly added or updated files in near real-time using the cloudFiles source option.

b. **Schema Evolution**:
   - Automatically infers and evolves schemas as new columns or changes are detected in the incoming data.
   - Prevents manual intervention when schema changes occur.

c. **Incremental Ingestion**:
   - Processes only new or updated files, avoiding redundant reprocessing.
   - Utilizes checkpoint files to track processed files and ensure idempotency.

#### Upserts in the Bronze Layer

Upserts (updates and inserts) are critical for maintaining an accurate and up-to-date Bronze Layer. They ensure that new and modified data is reflected correctly in the Delta tables.

- **Merge Operation**: A Delta Lake MERGE INTO statement is used to upsert data into the primary Bronze table (`bronze_table`).
   - **WHEN MATCHED**: Updates existing records in the Bronze table if changes are detected in the source.
   - **WHEN NOT MATCHED**: Inserts new records into the Bronze table.

- **Schema Enforcement**:  The schema is enforced during the merge to maintain consistency and prevent invalid data from being ingested.

- **Checkpointing and Idempotency**:  
   - Checkpoint files and Delta transaction logs ensure that:
   - Duplicate processing is avoided.
   - The pipeline can recover seamlessly from failures.

- **Advantages of Upserts**:  
   - Data Freshness: Ensures the Bronze table always reflects the latest source data.
   - Efficiency: Processes only new and modified records, reducing compute overhead.
   - Accuracy: Maintains data consistency by updating existing records and adding new ones as needed.

#### Example Configuration in Databricks

```python
from pyspark.sql.functions import *

autoloader_df = (spark.readStream
                 .format("cloudFiles")
                 .option("cloudFiles.format", "csv")
                 .option("cloudFiles.schemaLocation", "<schema_checkpoint_path>")
                 .option("cloudFiles.checkpointLocation", "<checkpoint_path>")
                 .load("<source_path>"))

autoloader_df.writeStream \
    .format("delta") \
    .foreachBatch(lambda batch_df, batch_id: batch_df.createOrReplaceTempView("batch_data")  # Create temp view for the batch
                  # Perform the MERGE operation to upsert into the bronze table
                  spark.sql("""
                      MERGE INTO bronze_table AS target
                      USING batch_data AS source
                      ON target.id = source.id
                      WHEN MATCHED THEN UPDATE SET *
                      WHEN NOT MATCHED THEN INSERT *
                  """)) \
    .option("checkpointLocation", "/path/to/checkpoints")  # Ensure checkpointing is enabled
    .outputMode("update")  # Ensures that only updates/inserts happen in each micro-batch
    .start()
```

### **Silver Layer**

In the Silver layer, Silver Delta tables are created to process and transform raw data into structured formats. This transformation is achieved using the upsert method across multiple staging tables (clean tables and join tables). After the data is cleaned, the process involves joining three key tables: `workday_optitime`, `workday_sage`, and `workday_sage_optitime`. Some of these staging tables are sinked to Cosmos DB using the Spark driver for Cosmos DB, enabling the dev-team to efficiently access and query the data for downstream applications.

#### Processing Steps

a. **Data Validation**: Ensures that incoming data meets the predefined quality standards:
   - **Schema Validation**: Verify that the data adheres to the expected schema. For example, required fields such as employee ID or timestamp are checked for presence and data type correctness.
   - **Domain Validation**: Confirm that specific fields fall within expected ranges. For instance, dates must be valid and fall within acceptable ranges, and numeric fields like hours worked cannot be negative.
   - **Custom Rules**: Implement business-specific rules, such as ensuring job titles match predefined values or rejecting rows with invalid employee status.
   - **Implementation**: Validation is typically performed using Databricks notebooks with PySpark or SQL logic. Invalid rows are redirected to error tables or logs for further inspection.

b. **Deduplication**: Ensures the removal of duplicate records based on defined keys.
   - **Duplicate Detection**: Use composite keys such as employee ID, date, and job title to identify duplicate records to remove them.
   - **Record Prioritization**: When duplicates are detected, prioritize records based on attributes like the most recent timestamp or data source reliability.
   - **Implementation**: PySpark functions or SQL logic.

c. **Data Enrichment and Consolidation**: Adds context to data and integrates data from multiple sources to create unified tables:
   - **Join Operations**: Merge datasets using Left Outer join by employee id from `Workday_cleansed`, `Sage_cleansed`, and `Optitime_cleansed` to produce consolidated tables such as `Workday_Optitime` or `Workday_Sage_Optitime`.
   - **Derived Fields**: Add new attributes based on existing data.
   - **Key Alignment**: Ensure consistent primary and foreign keys across datasets for seamless integration.
   - **Implementation**: Enrichment is performed using PySpark functions/Spark SQL join operations or transformations in Databricks notebooks.

d. **Sinking into Cosmos db**: We chose to use Change Data Feed (CDF) to send incremental changes from the silver layer to Cosmos DB because Cosmos DB lacks native versioning capabilities. By leveraging CDF in Delta format, we can efficiently identify and propagate only the changes (inserts, updates, and deletes) from the silver layer, ensuring that Cosmos DB stays in sync without requiring a full data reload. This approach minimizes data transfer costs and processing time while maintaining data consistency. Additionally, CDF allows us to track historical changes in the silver layer, providing flexibility and reliability for downstream systems that depend on Cosmos DB.

We chose batch processing to transform data from the silver layer to the gold layer because the gold-layer transformations and aggregations depend on data with less frequent changes in the silver layer. By processing data in batches, we avoid the overhead of continuous updates while ensuring efficient and reliable data transformations. Additionally, the Delta format's versioning capabilities allow us to track and manage changes over time, ensuring data consistency, reproducibility, and the ability to roll back if needed. This approach supports stable and accurate gold-layer tables for downstream analytics and reporting.


### **Gold Layer**

In the Gold layer, advanced aggregations and calculations are performed using PySpark functions or SQL logic to create business-ready tables tailored for analytics and decision-making. These tables are modeled (Star Schema) and designed to support analytics and reporting tools like Qlik. Additionally, some of these tables may be sinked to Cosmos DB for operational applications, while others might be leveraged in machine learning workflows, enabling predictive insights. Materialized views will be applied across these aggregated tables, precomputing and storing query results to further enhance performance for operational reporting, machine learning, and business intelligence.


#### Processing Steps

a. **Aggregation**  
   - **Objective**: Aggregate data from the Silver layer's enriched and consolidated tables.
   - **Details**:  
     - Perform grouping, summarization, and calculation of key metrics (e.g., averages, sums, counts, or time-series aggregates).  
     - Apply business rules and calculations specific to organizational needs.
   - **Examples**:  
     - Calculate employee working hours per department.  
     - Summarize financial transactions across regions.

b. **Data Conformance**  
   - **Objective**: Ensure datasets meet enterprise standards for schema consistency and naming conventions.
   - **Details**:  
     - Align table structures with downstream systems (e.g., Cosmos DB schema).  
     - Rename columns to meet standard business terminology.  
     - Map fields to consistent data types (e.g., ensuring all timestamps use UTC).

c. **Partitioning and Indexing**  
   - **Objective**: Optimize data storage and retrieval for performance.
   - **Details**:  
     - Partition tables based on access patterns (e.g., by date or region).  
     - Use Z-Order indexing in Delta tables to speed up queries.

d. **Quality Checks**  
   - **Objective**: Verify data accuracy and completeness.
   - **Details**:  
     - Apply checks to ensure metrics align with expectations (e.g., comparing row counts between Silver and Gold layers).  
     - Flag and isolate anomalies for further review.

e. **Sinking Data to External Systems**  
   - **Objective**: Export Gold layer datasets to downstream systems for consumption.
   - **Details**:  
     - Use Databricks Spark driver to sink data into Azure Cosmos DB for operational reporting.  
     - Provide datasets in Qlik-compatible formats for BI dashboards.  
     - Export data to ML pipelines for advanced analytics and predictive modeling.

#### Gold Layer Table Types

- **Aggregated Tables for Cosmos DB**:  
  Contain operational datasets tailored for transactional and reporting use cases.  
  **Example**: Employee attendance summaries or financial records.

- **Aggregated Tables for ML**:  
  Serve as feature stores for ML workflows.  
  **Example**: Historical time-series data for anomaly detection models.

- **Aggregated Tables for BI Tools (e.g., Qlik View)**:  
  Optimized for ad hoc queries and visualization tools, utilizing a star schema with fact and dimension tables.  
  **Example**: Sales summaries by product category and region.

#### Materialized Views:
- In our project, materialized views will be used in the Gold layer to improve performance for frequently queried aggregated data. Materialized views store the result of a query physically, enabling faster access to precomputed aggregates and calculations. This is particularly useful for complex reports and dashboards that require frequent access to the same aggregated data, ensuring efficient performance even as the data grows.


---

## Conclusion

This design ensures a **secure, scalable, and automated pipeline** to handle Workday data efficiently. Data flows through multiple layers of transformation, maintaining data integrity and delivering valuable insights for downstream analytics.

---
