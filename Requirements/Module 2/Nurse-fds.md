# 📄 Functional Design Specifications (FDS)

## Connected Workers Project — Module 2: Nurse

---

## Table of Contents

1. [Introduction](#introduction)
2. [General Description](#general-description)
   - [Overview](#overview)
   - [Key Functions](#key-functions)
   - [Target Users](#target-users)
3. [Main Features](#main-features)
   - [1. Submit Illness Period](#1-submit-illness-period)
   - [2. Submit Work Accident](#2-submit-work-accident)
   - [3. Pregnancy Announcement](#3-pregnancy-announcement)
   - [4. Able and Enable to Work Status](#4-able-and-enable-to-work-status)
4. [Regional Variations](#regional-variations)
5. [Document Version Control](#document-version-control)

---

## Introduction

This document outlines the functional specifications for the **Nurse Module**, a critical component of APTIV's **Connected Workers** project. The module digitizes and streamlines administrative processes related to employee health and well-being, ensuring compliance with company policies and enhancing operational efficiency. It serves as a comprehensive guide for development teams to implement features aligned with APTIV's business requirements and strategic objectives.

---

## General Description

### Overview

The **Nurse Module** is integral to APTIV's digital transformation of workforce health management. It efficiently tracks and manages employee health statuses related to illness, work accidents, pregnancy, and work capability, integrating seamlessly with the **Headcount Control and Time Keeping System (TKS)** for real-time updates.

The module supports:

- Reducing manual tracking efforts
- Improving visibility for stakeholders
- Ensuring policy compliance

### Key Functions

- **Illness Period Submission**: Records and communicates employee sick leave, assigning 'MA' (Maladie) status
- **Work Accident Submission**: Documents work-related injuries, assigning 'AT' (Accident de Travail) status
- **Pregnancy Announcement**: Identifies pregnant employees for workforce planning
- **Work Capability Status**: Assesses and updates employees' ability to perform tasks or operate machinery

### Target Users

| Role                        | Responsibilities                                                       |
| :-------------------------- | :--------------------------------------------------------------------- |
| Nurse                       | Enters, validates, and communicates health-related data                |
| Team Leader                 | Receives absence notifications and views work capability comments      |
| Shift Leader                | Notified of absences affecting operators or team leaders               |
| Department Clerk            | Updates absence statuses in the Visual Check interface                 |
| Employee                    | Submits medical or pregnancy certificates for processing               |
| Health & Safety Responsible | Monitors work accident statuses with alerts for extensions or closures |

---

## Main Features

### 1. Submit Illness Period

**Description:**  
This feature enables the nurse to record and communicate an employee's sick leave duration, updating the system and notifying stakeholders.

**Workflow:**

1. **Initiate Submission:** Nurse selects "Submit Illness Period"
2. **Employee Identification:**
   - Enter Operator/Employee ID
   - System integrates with Workday to auto-populate name, department, and supervisor
3. **Input Dates:** Enter start and end dates of sick leave
4. **Certificate Submission Method:**
   - **By Bailiff (region-specific):** Enter bailiff's name and phone number
   - **By Employee:** Note direct submission
5. **Doctor Details:**
   - Record issuing doctor's details (specialty, name, phone number)
   - Information accessible only to the nurse for confidentiality
6. **Certificate Upload:**
   - Scan and upload the medical certificate
   - Stored securely and accessible only to the nurse
7. **Validation:**
   - Submit final data
   - System notifies Team Leader, Shift Leader, and Department Clerk
   - TKS updated with 'MA' status

**Follow-up Visit (Region-Specific):**

- Access a filtered employee list with pending follow-ups
- Select an employee; pop-up displays their details and initial certificate date (read-only)
- Enter new sick leave dates post-visit
- View employees submitting certificates at the bus terminal, with ID, name, and tracking code

**Reporting:**

- Generate and export reports (e.g., certificate counts by ID or department) to Excel

---

### 2. Submit Work Accident

**Description:**  
This feature records and communicates absences due to work accidents, updating the system and notifying stakeholders.

**Workflow:**

1. **Initiate Submission:** Nurse selects "Submit Work Accident"
2. **Employee Identification:**
   - Enter Operator/Employee ID, name, and surname
3. **Input Dates:** Enter start and end dates of absence
4. **Documentation:** Upload supporting justification documents
5. **Submission:**
   - System notifies Team Leader, Shift Leader, and Department Clerk
   - TKS updated with 'AT' status

**Sick Leave Extension:**

- Filter and select an employee from the list
- Click "Extend Sick Leave" to view current leave details in a pop-up
- Enter the new end date and submit

---

### 3. Pregnancy Announcement

**Description:**  
This feature identifies pregnant employees for workforce planning, notifying team leaders without affecting attendance tracking.

**Workflow:**

1. **Initiate Announcement:** Nurse selects "Submit Pregnancy Announcement"
2. **Employee Identification:**
   - Enter Operator/Employee ID, name, surname
3. **Record Details:**
   - Enter declaration date and weeks of pregnancy
4. **Certificate Upload:** Upload the pregnancy certificate
5. **Submission:**
   - System notifies the Team Leader (and N+1 for IS employees)
   - Clocking Sheet updated with a "Pink" color code

**Termination of Pregnancy Status:**

- Select an employee from the filtered list
- Click "Terminate Pregnancy Status" to revert the color code to normal

---

### 4. Able and Enable to Work Status

**Description:**  
This feature allows the nurse to assess and record an employee's work capability, providing visibility to team leaders.

**Workflow:**

1. **Status Designation:** Mark employee as 'Able' or 'Unable' to work
2. **Specific Limitations:** Add comments (e.g., "cannot work standing up," "no heavy lifting," "audiotest required")
3. **Submission:** Comments visible to Team Leaders in the replacement process and Clocking Sheet

---

## Regional Variations

The Nurse Module adapts to regional differences in health management processes:

| Country       | Sickness Absenteeism | Process Specificities                                                                                  |
| :------------ | :------------------- | :----------------------------------------------------------------------------------------------------- |
| Morocco       | Yes                  | Direct nurse submission                                                                                |
| Tunisia       | No                   | HR agent initiates the process by receiving certificates                                               |
| Portugal      | No                   | Honor declarations accepted; certificates managed by TKS agent                                         |
| Poland        | No                   | Certificates retrieved from the national social insurance platform                                     |
| Turkey        | No                   | Certificates sourced from government portal, then deposited at medical centers                         |
| Serbia        | No                   | Employee uploads sick leave picture in Ethar; manual entry by HR                                       |
| North America | No                   | Nurse submits certificates to HR; final validation by doctor and HR coordination with area supervisors |

### Key Improvements I made:

- Reorganized for **clarity** and **consistency**.
- Professionalized **language and tone**.
- Improved **tables, formatting, and workflows**.
- Added **headings**, **section links**, and **markdown structure** for easy navigation.
- Standardized terms like "Nurse," "Employee," "Certificate," etc.
