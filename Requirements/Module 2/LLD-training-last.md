# Training Process Microservice - Low-Level Design (LLD)

## Document Information

**Version:** 1.0.0
**Last Updated:** 2025-04-21
**Status:** Draft
**Authors: <AUTHORS>

## Executive Summary

This document details the low-level design for the Training Process microservice, which orchestrates operator training progression through phases (O, LC, V, C, R, F) based entirely on manual triggers from users (<PERSON>ers, Team Leaders). The service follows event-driven architecture principles, leveraging Azure Cosmos DB Change Feed for consuming data from other modules (Crew Management, Skills Matrix, Versatility Matrix) and Azure Service Bus for event communication (publishing status updates, triggering/receiving workflow events).

The design implements a NestJS-based microservices architecture with TypeScript, following the CQRS pattern to separate read and write operations. This approach enables strong typing, dependency injection, and modular development while optimizing for the specific needs of each operation type.

### Key Features

- Manages operator training lifecycle phases (O, LC, V, C, R, F) without automatic timelines
- Manual initiation of all training phases and evaluations by authorized users
- Change Feed patterns for consuming and duplicating necessary data locally from Crew Management, Skills Matrix, and Versatility Matrix (for read operations)
- Event-driven status updates **published to** Versatility Matrix using strongly-typed event objects
- Integration with Validation Submodule (REQ 32/33) for form templates and scoring via REST API clients
- Triggers approval workflows in Module 1 for certification, recertification, and other processes
- Receives and processes workflow completion events from Module 1 to update internal status
- Clear domain boundaries with other modules to prevent redundancy, following Domain-Driven Design principles
- CQRS pattern with separate read and write models using TypeScript interfaces and classes

### Key Integration Points

- **Crew Management**: Consumes and duplicates operator data via Change Feed for local use
- **Skills Matrix**: Consumes and duplicates process definitions via Change Feed for local use
- **Versatility Matrix**: Publishes status updates via events. Consumes and duplicates Versatility data via Change Feed for local read operations (e.g., displaying current assignments if needed)
- **Validation Submodule (REQ 32/33)**: Requests templates and scoring via API
- **Module 1 Workflow Engine**: Triggers workflows via events. Receives workflow completion results via events/callbacks

### Microservices Architecture

The Training Process domain is composed of several NestJS microservices working together:

1. **Training Core Service**: Handles core training domain logic, processes commands, and maintains the write model
2. **Training Query Service**: Manages read-optimized projections and query operations
3. **Change Feed Processor Service**: Consumes changes from external Cosmos DB containers
4. **Event Processing Service**: Handles incoming events from Service Bus topics
5. **API Gateway**: Exposes RESTful endpoints and routes requests to appropriate services

Each microservice is containerized and deployable independently, allowing for scalability and resilience. Services communicate via well-defined interfaces, using TypeScript interfaces for type safety.

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture](#2-architecture)
3. [Detailed Component Design](#3-detailed-component-design)
4. [Data Models](#4-data-models)
5. [API Specification](#5-api-specification)
6. [Event Communication](#6-event-communication)
7. [Azure Services Integration](#7-azure-services-integration)
8. [Cross-Cutting Concerns](#8-cross-cutting-concerns)
9. [Implementation Considerations](#9-implementation-considerations)
10. [Open Questions and Risks](#10-open-questions-and-risks)

---

## 1. Overview

### 1.1 Purpose and Scope

The Training Process microservice's primary responsibility is to orchestrate and track an operator's progression through training phases based on manual triggers. It integrates with other modules via Change Feed (consuming data) and event-driven patterns (publishing status, triggering workflows, receiving results), avoiding direct synchronous coupling where possible. It maintains a local, duplicated copy of necessary data from external sources (like operator details) for efficient operation.

**In Scope:**

- Recording and tracking operator training phase transitions (O, LC, V, C, R, F)
- Processing manually initiated training phase actions from Trainers and Team Leaders
- Consuming operator data from Crew Management via Change Feed and maintaining a local copy
- Consuming process definitions from Skills Matrix via Change Feed and maintaining a local copy
- Consuming data from Versatility Matrix via Change Feed for read-only purposes (e.g., enriching UI views) (_Specific data fields need confirmation_)
- Integrating with Validation Submodule for form templates and scoring
- Triggering approval workflows in Module 1 via events
- Receiving workflow completion results from Module 1 (via events or callbacks) and updating operator training status accordingly
- Publishing status updates (`TrainingStatusChangedEvent`) to Versatility Matrix

**Out of Scope:**

- Operator onboarding (handled by Crew Management)
- Skills/process definition management (handled by Skills Matrix)
- Form rendering and scoring logic (handled by Validation Submodule)
- Workflow execution and approvals (handled by Module 1)
- Versatility Matrix maintenance, calculations, and acting as the source of truth for workstation assignments
- Automatic timelines, escalations, or retries
- Notification delivery (handled by central Notification Service)

### 1.2 Domain Context Diagram

```mermaid
graph TD
    subgraph Training Process Microservices
        TP_Core[Training Core Service]
        TP_Query[Training Query Service]
        TP_API[API Gateway]
        TP_CFP[Change Feed Processor Service]
        TP_EVT[Event Processing Service]
        TP_DB[(Cosmos DB Containers)]

        TP_API -- "Commands" --> TP_Core
        TP_Core -- "Domain Events" --> TP_EVT
        TP_Core -- "Write Operations" --> TP_DB
        TP_Query -- "Read Operations" --> TP_DB
        TP_API -- "Queries" --> TP_Query
        TP_CFP -- "Change Commands" --> TP_Core
        TP_EVT -- "Event Commands" --> TP_Core
    end

    CM_DB[(Crew Management DB)] -- "Operator Data (Change Feed)" --> TP_CFP
    SM_DB[(Skills Matrix DB)] -- "Process Definitions (Change Feed)" --> TP_CFP
    VM_DB[(Versatility Matrix DB)] -- "Versatility Data (Change Feed)" --> TP_CFP

    TP_EVT -- "Status Updates (Events)" --> VM_SB[Versatility Matrix Service Bus]
    TP_EVT -- "Workflow Triggers (Events)" --> M1_SB[Module 1 Service Bus]
    TP_EVT -- "Notification Events" --> NS_SB[Notification Service Bus]

    M1_SB -- "Workflow Results (Events)" --> TP_EVT

    VS_API[Validation Service API] <-- "HTTP/REST" --> TP_API

    Client[Client Apps / Users] -- "REST API Calls" --> TP_API
```

---

## 2. Architecture

### 2.1 Architectural Patterns

The Training Process microservice implements the following architectural patterns using TypeScript and NestJS:

1. **Microservices Architecture**:

   - A set of domain-focused NestJS microservices
   - Each service has a single responsibility
   - Services communicate via message patterns (using `@nestjs/microservices`)
   - TypeScript interfaces define service boundaries

2. **Event-Driven Architecture**:

   - Async communication via Azure Service Bus
   - Strongly-typed event classes with TypeScript
   - Event listeners using NestJS `@EventPattern()` decorators
   - Custom event serialization/deserialization

3. **CQRS Pattern**:

   - Commands and queries separated into different modules
   - Commands use `@CommandHandler()` decorators
   - Queries use `@QueryHandler()` decorators
   - DTOs for command inputs and query results

4. **Domain-Driven Design**:

   - TypeScript classes for aggregates, entities, and value objects
   - Domain events as first-class citizens
   - Repository interfaces with dependency injection
   - Clear bounded contexts with typed interfaces

5. **Change Feed Pattern**:

   - Cosmos DB SDK for Change Feed processing
   - TypeScript interfaces for document schemas
   - Transformation of Change Feed events to domain commands

6. **RESTful API**:
   - NestJS controllers with typed request/response objects
   - Swagger documentation with `@nestjs/swagger`
   - OpenAPI specification generation
   - DTO validation using class-validator and class-transformer

### 2.2 High-Level Component Diagram

```mermaid
graph TD
    Client[Client Applications] -- "HTTP/REST" --> APIGw[Azure API Management]
    APIGw --> NestGateway[NestJS API Gateway]

    subgraph Training Process Microservices
        NestGateway -- "GraphQL/REST" --> QueryAPI[Query API Module]
        NestGateway -- "REST" --> CommandAPI[Command API Module]

        CommandAPI -- "@CommandHandler()" --> CommandHandlers[Command Handlers]
        QueryAPI -- "@QueryHandler()" --> QueryHandlers[Query Handlers]

        CommandHandlers --> DomainModel[Domain Models]
        DomainModel --> Repository[Repository Services]

        QueryHandlers --> ReadModels[Read Model Services]

        Repository --> CosmosWrite[CosmosDB Repository]
        ReadModels --> CosmosRead[CosmosDB Query Service]

        CosmosWrite -- "Write Operations" --> TrainingDB[(Training Process DB)]
        CosmosRead -- "Read Operations" --> TrainingDB

        DomainModel -- "Domain Events" --> EventPublisher[Event Publisher Service]
        EventPublisher --> ServiceBusPub[Azure Service Bus Client]

        ChangeFeedService[Change Feed Processor] -- "Commands" --> CommandHandlers
        EventSubscriber[Event Subscriber Service] -- "Commands" --> CommandHandlers
    end

    CrewMgtDB[(Crew Management DB)] -- "Change Feed" --> ChangeFeedService
    SkillsMatrixDB[(Skills Matrix DB)] -- "Change Feed" --> ChangeFeedService
    VersatilityMatrixDB[(Versatility Matrix DB)] -- "Change Feed" --> ChangeFeedService

    ServiceBusPub -- "Status Events" --> VM_SB[Versatility Matrix Topic]
    ServiceBusPub -- "Workflow Events" --> M1_SB[Module 1 Workflow Topic]
    ServiceBusPub -- "Notification Events" --> Notification_SB[Notification Topic]

    M1_Result_SB[Module 1 Result Topic] -- "Subscription" --> EventSubscriber

    ValidationAPI[Validation Service API] <-- "HTTP Client" --> CommandHandlers
```

### 2.3 Technology Stack

| Component                  | Technology                                             | Purpose                                          |
| -------------------------- | ------------------------------------------------------ | ------------------------------------------------ |
| **Core Language**          | TypeScript 5.x                                         | Type-safe development                            |
| **API Framework**          | NestJS 10.x                                            | Modular architecture with dependency injection   |
| **API Documentation**      | @nestjs/swagger                                        | OpenAPI documentation generation                 |
| **GraphQL API**            | @nestjs/graphql                                        | Query optimization & type generation             |
| **Data Validation**        | class-validator & class-transformer                    | DTO validation and transformation                |
| **Data Storage**           | Azure Cosmos DB SDK for Node.js                        | Database operations with TypeScript support      |
| **CQRS Implementation**    | @nestjs/cqrs                                           | Command and query handlers                       |
| **Messaging**              | @nestjs/microservices with Azure Service Bus transport | Event publishing and subscription                |
| **Event Handling**         | Custom event bus with TypeScript typing                | Strongly-typed event objects                     |
| **Change Feed Processing** | @azure/cosmos                                          | Change Feed processor with TypeScript interfaces |
| **API Gateway**            | NestJS with custom proxy module                        | API orchestration                                |
| **Authentication**         | @nestjs/passport with Azure AD strategy                | JWT validation                                   |
| **Containerization**       | Docker with Node.js images                             | Microservice deployment                          |
| **Orchestration**          | Kubernetes                                             | Container orchestration                          |
| **Monitoring**             | Application Insights SDK for Node.js                   | Telemetry and metrics                            |
| **Testing**                | Jest with TypeScript                                   | Unit and integration testing                     |

### 2.4 CQRS Implementation

The CQRS pattern separates the command and query responsibilities using NestJS's CQRS module with TypeScript:

**Command Side (Write Model):**

```typescript
// Example command
export class CompleteOjtTrainingCommand {
  constructor(
    public readonly operatorId: string,
    public readonly processId: string,
    public readonly result: string,
    public readonly checklistUrl: string,
    public readonly userId: string
  ) {}
}

// Example command handler
@CommandHandler(CompleteOjtTrainingCommand)
export class CompleteOjtTrainingHandler
  implements ICommandHandler<CompleteOjtTrainingCommand>
{
  constructor(
    private readonly repository: OperatorTrainingRepository,
    private readonly eventBus: EventBus
  ) {}

  async execute(command: CompleteOjtTrainingCommand): Promise<void> {
    // Load aggregate
    const operatorTraining = await this.repository.findById(command.operatorId);

    // Execute domain logic
    const result = operatorTraining.completeOjtTraining(
      command.processId,
      command.result,
      command.checklistUrl,
      command.userId
    );

    // Save changes
    await this.repository.save(operatorTraining);

    // Publish domain events
    operatorTraining.events.forEach((event) => this.eventBus.publish(event));
  }
}
```

**Query Side (Read Model):**

```typescript
// Example query
export class GetOperatorTrainingDetailsQuery {
  constructor(public readonly operatorId: string) {}
}

// Example query handler
@QueryHandler(GetOperatorTrainingDetailsQuery)
export class GetOperatorTrainingDetailsHandler
  implements IQueryHandler<GetOperatorTrainingDetailsQuery>
{
  constructor(private readonly trainingQueries: OperatorTrainingQueries) {}

  async execute(
    query: GetOperatorTrainingDetailsQuery
  ): Promise<OperatorTrainingDetailsDto> {
    return this.trainingQueries.getDetails(query.operatorId);
  }
}
```

**Read/Write Model Separation:**

- Both models use Cosmos DB containers with different access patterns
- Separate TypeScript interfaces for read and write operations
- Domain events automatically trigger read model projections
- Read models are denormalized for query optimization

---

## 3. Detailed Component Design

### 3.1 API Layer

The API layer is implemented using NestJS controllers with RESTful endpoints for commands and queries:

```typescript
// Example NestJS controller for training operations
@ApiTags("training")
@Controller("training")
export class TrainingController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus
  ) {}

  // Command endpoint example
  @ApiOperation({ summary: "Complete On Job Training phase" })
  @ApiResponse({ status: 202, description: "OJT completion accepted" })
  @ApiResponse({ status: 400, description: "Invalid input" })
  @Post(":operatorId/ojt")
  async completeOjt(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteOjtDto
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteOjtTrainingCommand(
        operatorId,
        dto.processId,
        dto.result,
        dto.checklistUrl,
        dto.userId
      )
    );
    return;
  }

  // Query endpoint example
  @ApiOperation({ summary: "Get operator training details" })
  @ApiResponse({
    status: 200,
    description: "Training details retrieved",
    type: OperatorTrainingDetailsDto,
  })
  @ApiResponse({ status: 404, description: "Operator not found" })
  @Get(":operatorId")
  async getOperatorTraining(
    @Param("operatorId") operatorId: string
  ): Promise<OperatorTrainingDetailsDto> {
    return this.queryBus.execute(
      new GetOperatorTrainingDetailsQuery(operatorId)
    );
  }

  // Additional endpoints...
}
```

The API Layer includes:

- **Controllers**: NestJS controllers with typed request/response objects
- **Route Guards**: Authentication and authorization via `@nestjs/passport`
- **Validation Pipes**: DTO validation using `class-validator`
- **Swagger Documentation**: Using `@nestjs/swagger` decorators
- **Exception Filters**: Consistent error handling across endpoints
- **Interceptors**: Request/response logging and transformation

### 3.2 Command Processing

Command processing uses the NestJS CQRS module with TypeScript:

```typescript
// Example command DTOs
export class CompleteOjtDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  processId: string;

  @ApiProperty({ description: 'Training result (e.g., "85%")' })
  @IsString()
  result: string;

  @ApiProperty({ description: "URL to uploaded checklist", required: false })
  @IsOptional()
  @IsString()
  checklistUrl?: string;

  @ApiProperty({ description: "User ID of trainer submitting the result" })
  @IsString()
  userId: string;

  @ApiProperty({ description: "Additional comments", required: false })
  @IsOptional()
  @IsString()
  comments?: string;
}

// Example command handler
@CommandHandler(CompleteOjtTrainingCommand)
export class CompleteOjtTrainingHandler
  implements ICommandHandler<CompleteOjtTrainingCommand>
{
  constructor(private readonly repository: OperatorTrainingRepository) {}

  async execute(command: CompleteOjtTrainingCommand): Promise<void> {
    const { operatorId, processId, result, checklistUrl, userId } = command;

    // Load aggregate
    const record = await this.repository.findById(operatorId);
    if (!record) {
      throw new NotFoundException(
        `Operator training record not found for ID: ${operatorId}`
      );
    }

    // Execute domain logic
    const opResult = record.completeOjtTraining(
      processId,
      result,
      checklistUrl,
      userId
    );
    if (!opResult.success) {
      throw new BadRequestException(opResult.message);
    }

    // Persist changes
    await this.repository.save(record);
  }
}
```

**Key Command Handlers:**

```typescript
// Command handler examples - these translate API requests to domain operations
@CommandHandler(CompleteOjtTrainingCommand)
export class CompleteOjtTrainingHandler
  implements ICommandHandler<CompleteOjtTrainingCommand> {}

@CommandHandler(CompleteLearningCurveCommand)
export class CompleteLearningCurveHandler
  implements ICommandHandler<CompleteLearningCurveCommand> {}

@CommandHandler(InitiateValidationCommand)
export class InitiateValidationHandler
  implements ICommandHandler<InitiateValidationCommand> {}

@CommandHandler(ProcessValidationResultCommand)
export class ProcessValidationResultHandler
  implements ICommandHandler<ProcessValidationResultCommand> {}

@CommandHandler(InitiateCertificationCommand)
export class InitiateCertificationHandler
  implements ICommandHandler<InitiateCertificationCommand> {}

@CommandHandler(ProcessCertificationResultCommand)
export class ProcessCertificationResultHandler
  implements ICommandHandler<ProcessCertificationResultCommand> {}

// Additional command handlers...

// Change Feed sync handlers
@CommandHandler(SyncOperatorDataCommand)
export class SyncOperatorDataHandler
  implements ICommandHandler<SyncOperatorDataCommand> {}

@CommandHandler(SyncProcessDefinitionCommand)
export class SyncProcessDefinitionHandler
  implements ICommandHandler<SyncProcessDefinitionCommand> {}

@CommandHandler(SyncVersatilityDataCommand)
export class SyncVersatilityDataHandler
  implements ICommandHandler<SyncVersatilityDataCommand> {}
```

### 3.3 Domain Model

The domain model is implemented using TypeScript classes following Domain-Driven Design principles.

#### 3.3.1 Aggregates and Entities

```typescript
// Domain enums
export enum QualificationStatus {
  NONE = 'None',
  O = 'O',               // On Job Training completed
  LC = 'LC',             // Learning Curve completed
  V_PENDING = 'V_Pending', // Validation in progress
  V = 'V',               // Validated
  V_FAILED = 'V_Failed', // Validation failed
  C_PENDING = 'C_Pending', // Certification in progress
  C = 'C',               // Certified
  C_FAILED = 'C_Failed', // Certification failed
  R_PENDING = 'R_Pending', // Recertification in progress
  R = 'R',               // Recertified
  R_FAILED = 'R_Failed', // Recertification failed
  F = 'F',               // New qualification in familiarization
  REMOVED = 'Removed'    // Qualification removed
}

export enum TrainingStatus {
  NEW = 'New',
  IN_TRAINING = 'InTraining',
  VALIDATED = 'Validated',
  CERTIFIED = 'Certified',
  NEEDS_RECERTIFICATION = 'NeedsRecertification'
}

export enum TrainingPhase {
  O = 'O',
  LC = 'LC',
  V = 'V',
  C = 'C',
  R = 'R',
  F = 'F'
}

// Result type for domain operations
export class Result {
  private constructor(
    public readonly success: boolean,
    public readonly message?: string
  ) {}

  static success(): Result {
    return new Result(true);
  }

  static failure(message: string): Result {
    return new Result(false, message);
  }
}

// Aggregate root
export class OperatorTrainingRecord extends AggregateRoot {
  private _id: string;
  private _operatorId: string;
  private _operatorName: string;
  private _department: string;
  private _function: string;
  private _hiringDate?: Date;
  private _currentOverallStatus: TrainingStatus;
  private _qualifications: Qualification[];
  private _history: TrainingHistoryEntry[];

  // Getters
  get id(): string { return this._id; }
  get operatorId(): string { return this._operatorId; }
  get operatorName(): string { return this._operatorName; }
  get department(): string { return this._department; }
  get currentOverallStatus(): TrainingStatus { return this._currentOverallStatus; }
  get qualifications(): Qualification[] { return [...this._qualifications]; }
  get history(): TrainingHistoryEntry[] { return [...this._history]; }

  // Factory method for creating a new record
  static create(operatorId: string, name: string, department: string, function?: string): OperatorTrainingRecord {
    const record = new OperatorTrainingRecord();
    record._id = operatorId;
    record._operatorId = operatorId;
    record._operatorName = name;
    record._department = department;
    record._function = function || '';
    record._currentOverallStatus = TrainingStatus.NEW;
    record._qualifications = [];
    record._history = [];

    // Add domain event
    record.apply(new OperatorTrainingRecordCreatedEvent(operatorId));

    return record;
  }

  // Domain methods
  completeOjtTraining(processId: string, result: string, checklistUrl?: string, userId?: string): Result {
    // Business rule validation
    const qualification = this.findQualification(processId);
    if (!qualification) {
      return Result.failure(`Qualification with process ID ${processId} not found`);
    }

    // State change
    qualification.updateStatus(QualificationStatus.O);

    // Record history
    this.addHistoryEntry({
      eventType: 'OjtCompleted',
      phase: TrainingPhase.O,
      processId,
      result,
      details: { checklistUrl },
      userId
    });

    // Raise domain events
    this.apply(new OjtCompletedEvent(this._operatorId, processId, result, userId));
    this.apply(new TrainingStatusChangedEvent(
      this._operatorId,
      processId,
      QualificationStatus.NONE,
      QualificationStatus.O
    ));

    // Recalculate overall status
    this.calculateOverallStatus();

    return Result.success();
  }

  // Additional domain methods with similar structure...
  completeLearningCurve(processId: string, result: string, checklistUrl?: string, userId?: string): Result {
    // Similar implementation...
    return Result.success();
  }

  initiateValidation(processId: string, userId: string): Result {
    // Similar implementation...
    return Result.success();
  }

  // More domain methods...

  // Helper methods
  private findQualification(processId: string): Qualification | undefined {
    return this._qualifications.find(q => q.processId === processId);
  }

  private addHistoryEntry(data: Partial<TrainingHistoryEntry>): void {
    const entry = new TrainingHistoryEntry(
      v4(), // UUID
      data.eventType,
      data.phase,
      data.processId,
      new Date(),
      data.result,
      data.details || {},
      data.userId
    );
    this._history.push(entry);
  }

  private calculateOverallStatus(): void {
    // Calculate overall status based on qualification statuses
    const activeQualifications = this._qualifications.filter(q => q.isActive);

    if (activeQualifications.length === 0) {
      this._currentOverallStatus = TrainingStatus.NEW;
      return;
    }

    // Calculate based on qualification statuses...
    // Implementation details...
  }
}

// Entity
export class Qualification {
  constructor(
    private _processId: string,
    private _processName: string,
    private _status: QualificationStatus = QualificationStatus.NONE,
    private _lastCertificationDate?: Date,
    private _lastRecertificationDate?: Date,
    private _isActive: boolean = true
  ) {}

  // Getters
  get processId(): string { return this._processId; }
  get processName(): string { return this._processName; }
  get status(): QualificationStatus { return this._status; }
  get lastCertificationDate(): Date | undefined { return this._lastCertificationDate; }
  get lastRecertificationDate(): Date | undefined { return this._lastRecertificationDate; }
  get isActive(): boolean { return this._isActive; }

  // Methods
  updateStatus(newStatus: QualificationStatus): void {
    this._status = newStatus;

    if (newStatus === QualificationStatus.C) {
      this._lastCertificationDate = new Date();
    } else if (newStatus === QualificationStatus.R) {
      this._lastRecertificationDate = new Date();
    }
  }

  updateProcessName(newName: string): void {
    this._processName = newName;
  }

  deactivate(): void {
    this._isActive = false;
  }
}

// Value Object
export class TrainingHistoryEntry {
  constructor(
    private _eventId: string,
    private _eventType: string,
    private _phase: TrainingPhase,
    private _processId: string,
    private _timestamp: Date,
    private _result: string,
    private _details: Record<string, any>,
    private _userId?: string
  ) {}

  // Getters only (immutable)
  get eventId(): string { return this._eventId; }
  get eventType(): string { return this._eventType; }
  get phase(): TrainingPhase { return this._phase; }
  get processId(): string { return this._processId; }
  get timestamp(): Date { return this._timestamp; }
  get result(): string { return this._result; }
  get details(): Record<string, any> { return { ...this._details }; }
  get userId(): string | undefined { return this._userId; }
}
```

#### 3.3.2 Domain Events

```typescript
// Base domain event
export abstract class DomainEvent {
  constructor(
    public readonly eventId: string = v4(),
    public readonly timestamp: Date = new Date()
  ) {}
}

// Specific domain events
export class OperatorTrainingRecordCreatedEvent extends DomainEvent {
  constructor(public readonly operatorId: string) {
    super();
  }
}

export class OjtCompletedEvent extends DomainEvent {
  constructor(
    public readonly operatorId: string,
    public readonly processId: string,
    public readonly result: string,
    public readonly userId?: string
  ) {
    super();
  }
}

export class TrainingStatusChangedEvent extends DomainEvent {
  constructor(
    public readonly operatorId: string,
    public readonly processId: string,
    public readonly oldStatus: QualificationStatus,
    public readonly newStatus: QualificationStatus
  ) {
    super();
  }
}

export class ValidationInitiatedEvent extends DomainEvent {
  constructor(
    public readonly operatorId: string,
    public readonly processId: string,
    public readonly userId: string
  ) {
    super();
  }
}

export class CertificationWorkflowRequestedEvent extends DomainEvent {
  constructor(
    public readonly operatorId: string,
    public readonly processId: string,
    public readonly initiatedByUserId: string,
    public readonly context: Record<string, any> = {},
    public readonly correlationId: string = v4()
  ) {
    super();
  }
}

// Additional domain events...
```

### 3.4 Repository Pattern

The repository pattern is implemented using TypeScript interfaces and concrete classes:

```typescript
// Repository interface
export interface OperatorTrainingRepository {
  findById(operatorId: string): Promise<OperatorTrainingRecord | null>;
  save(record: OperatorTrainingRecord): Promise<void>;
  exists(operatorId: string): Promise<boolean>;
}

// Cosmos DB implementation
@Injectable()
export class CosmosDbOperatorTrainingRepository
  implements OperatorTrainingRepository
{
  constructor(
    @Inject("COSMOS_DB_CLIENT") private cosmosClient: CosmosClient,
    @Inject("COSMOS_DB_CONFIG") private config: CosmosDbConfig,
    private eventBus: EventBus
  ) {}

  async findById(operatorId: string): Promise<OperatorTrainingRecord | null> {
    try {
      const container = this.getContainer();
      const { resource } = await container
        .item(operatorId, operatorId)
        .read<TrainingRecordDocument>();

      if (!resource) {
        return null;
      }

      return this.mapToAggregate(resource);
    } catch (error) {
      if (error.code === 404) {
        return null;
      }
      throw error;
    }
  }

  async save(record: OperatorTrainingRecord): Promise<void> {
    const container = this.getContainer();
    const document = this.mapToDocument(record);

    await container.items.upsert(document);

    // Publish domain events
    record.events.forEach((event) => this.eventBus.publish(event));

    // Clear events after publishing
    record.clearEvents();
  }

  async exists(operatorId: string): Promise<boolean> {
    try {
      const container = this.getContainer();
      const { resource } = await container.item(operatorId, operatorId).read();
      return !!resource;
    } catch (error) {
      if (error.code === 404) {
        return false;
      }
      throw error;
    }
  }

  private getContainer(): Container {
    return this.cosmosClient
      .database(this.config.databaseId)
      .container(this.config.containerId);
  }

  private mapToAggregate(
    document: TrainingRecordDocument
  ): OperatorTrainingRecord {
    // Map from document to domain aggregate
    const record = OperatorTrainingRecord.create(
      document.operatorId,
      document.operatorName,
      document.department
    );

    // Restore state (private method in actual implementation)
    // This is simplified; actual implementation would be more complex
    // and might use reflection or a factory method instead
    (record as any)._id = document.id;
    (record as any)._function = document.function;
    (record as any)._hiringDate = document.hiringDate
      ? new Date(document.hiringDate)
      : undefined;
    (record as any)._currentOverallStatus = document.currentOverallStatus;
    (record as any)._qualifications = document.qualifications.map(
      (q) =>
        new Qualification(
          q.processId,
          q.processName,
          q.status as QualificationStatus,
          q.lastCertificationDate
            ? new Date(q.lastCertificationDate)
            : undefined,
          q.lastRecertificationDate
            ? new Date(q.lastRecertificationDate)
            : undefined,
          q.isActive
        )
    );
    (record as any)._history = document.history.map(
      (h) =>
        new TrainingHistoryEntry(
          h.eventId,
          h.eventType,
          h.phase as TrainingPhase,
          h.processId,
          new Date(h.timestamp),
          h.result,
          h.details,
          h.userId
        )
    );

    return record;
  }

  private mapToDocument(
    record: OperatorTrainingRecord
  ): TrainingRecordDocument {
    return {
      id: record.id,
      type: "OperatorTrainingRecord",
      partitionKey: record.id,
      operatorId: record.operatorId,
      operatorName: record.operatorName,
      department: record.department,
      function: (record as any)._function,
      hiringDate: (record as any)._hiringDate?.toISOString(),
      currentOverallStatus: record.currentOverallStatus,
      qualifications: record.qualifications.map((q) => ({
        processId: q.processId,
        processName: q.processName,
        status: q.status,
        lastCertificationDate: q.lastCertificationDate?.toISOString(),
        lastRecertificationDate: q.lastRecertificationDate?.toISOString(),
        isActive: q.isActive,
      })),
      history: record.history.map((h) => ({
        eventId: h.eventId,
        eventType: h.eventType,
        phase: h.phase,
        processId: h.processId,
        timestamp: h.timestamp.toISOString(),
        result: h.result,
        details: h.details,
        userId: h.userId,
      })),
      _etag: "",
      _ts: 0,
    };
  }
}

// Cosmos DB document interface
export interface TrainingRecordDocument {
  id: string;
  type: string;
  partitionKey: string;
  operatorId: string;
  operatorName: string;
  department: string;
  function?: string;
  hiringDate?: string;
  currentOverallStatus: string;
  qualifications: {
    processId: string;
    processName: string;
    status: string;
    lastCertificationDate?: string;
    lastRecertificationDate?: string;
    isActive: boolean;
  }[];
  history: {
    eventId: string;
    eventType: string;
    phase: string;
    processId: string;
    timestamp: string;
    result: string;
    details: Record<string, any>;
    userId?: string;
  }[];
  _etag: string;
  _ts: number;
}
```

### 3.5 Read Model

The read model uses specialized query services for optimized data retrieval:

```typescript
// Read model interfaces
export interface OperatorTrainingQueries {
  getSummary(operatorId: string): Promise<OperatorTrainingSummaryDto | null>;
  getDetails(operatorId: string): Promise<OperatorTrainingDetailsDto | null>;
  getByDepartment(
    department: string,
    status?: string
  ): Promise<OperatorTrainingSummaryDto[]>;
  getByStatus(
    status: string,
    department?: string
  ): Promise<OperatorTrainingSummaryDto[]>;
  getQualifications(operatorId: string): Promise<QualificationDto[]>;
  getTrainingHistory(operatorId: string): Promise<TrainingHistoryEntryDto[]>;
}

// Query DTOs
export class OperatorTrainingSummaryDto {
  @ApiProperty()
  operatorId: string;

  @ApiProperty()
  operatorName: string;

  @ApiProperty()
  department: string;

  @ApiProperty({ enum: TrainingStatus })
  currentTrainingStatus: string;

  @ApiProperty()
  qualificationCount: number;

  @ApiProperty({ type: [QualificationSummaryDto] })
  activeQualifications: QualificationSummaryDto[];

  @ApiProperty()
  lastUpdated: Date;
}

export class QualificationSummaryDto {
  @ApiProperty()
  processId: string;

  @ApiProperty()
  processName: string;

  @ApiProperty({ enum: QualificationStatus })
  status: string;
}

export class OperatorTrainingDetailsDto extends OperatorTrainingSummaryDto {
  @ApiProperty({ type: [QualificationDto] })
  qualifications: QualificationDto[];

  @ApiProperty({ type: [TrainingHistoryEntryDto] })
  trainingHistory: TrainingHistoryEntryDto[];
}

export class QualificationDto {
  @ApiProperty()
  processId: string;

  @ApiProperty()
  processName: string;

  @ApiProperty({ enum: QualificationStatus })
  status: string;

  @ApiProperty({ required: false, nullable: true, type: Date })
  lastCertificationDate?: Date;

  @ApiProperty({ required: false, nullable: true, type: Date })
  lastRecertificationDate?: Date;

  @ApiProperty()
  isActive: boolean;
}

export class TrainingHistoryEntryDto {
  @ApiProperty()
  eventId: string;

  @ApiProperty()
  eventType: string;

  @ApiProperty({ enum: TrainingPhase })
  phase: string;

  @ApiProperty()
  processId: string;

  @ApiProperty()
  timestamp: Date;

  @ApiProperty()
  result: string;

  @ApiProperty({ type: Object })
  details: Record<string, any>;

  @ApiProperty({ required: false, nullable: true })
  userId?: string;
}

// Cosmos DB implementation
@Injectable()
export class CosmosDbOperatorTrainingQueries
  implements OperatorTrainingQueries
{
  constructor(
    @Inject("COSMOS_DB_CLIENT") private cosmosClient: CosmosClient,
    @Inject("COSMOS_DB_CONFIG") private config: CosmosDbConfig
  ) {}

  async getSummary(
    operatorId: string
  ): Promise<OperatorTrainingSummaryDto | null> {
    try {
      const container = this.getContainer();

      const query = {
        query: "SELECT * FROM c WHERE c.type = @type AND c.id = @id",
        parameters: [
          { name: "@type", value: "OperatorTrainingSummary" },
          { name: "@id", value: operatorId },
        ],
      };

      const { resources } = await container.items.query<any>(query).fetchAll();

      if (resources.length === 0) {
        return null;
      }

      return this.mapToSummaryDto(resources[0]);
    } catch (error) {
      throw error;
    }
  }

  // Additional query methods...
  // getDetails(), getByDepartment(), getByStatus(), etc.

  private getContainer(): Container {
    return this.cosmosClient
      .database(this.config.databaseId)
      .container(this.config.readContainerId);
  }

  private mapToSummaryDto(document: any): OperatorTrainingSummaryDto {
    const dto = new OperatorTrainingSummaryDto();
    dto.operatorId = document.operatorId;
    dto.operatorName = document.operatorName;
    dto.department = document.department;
    dto.currentTrainingStatus = document.currentTrainingStatus;
    dto.qualificationCount = document.qualificationCount;
    dto.activeQualifications = document.activeQualifications.map((q) => {
      const summary = new QualificationSummaryDto();
      summary.processId = q.processId;
      summary.processName = q.processName;
      summary.status = q.status;
      return summary;
    });
    dto.lastUpdated = new Date(document.lastUpdated);
    return dto;
  }

  // Additional mapping methods...
}
```

### 3.6 Change Feed Processors

Change Feed processors are implemented using Azure Cosmos SDK with TypeScript:

```typescript
// Change Feed processor for Crew Management
@Injectable()
export class CrewManagementChangeFeedProcessor {
  constructor(
    @Inject('COSMOS_DB_CLIENT') private cosmosClient: CosmosClient,
    @Inject('COSMOS_DB_CONFIG') private config: CosmosDbConfig,
    private readonly commandBus: CommandBus,
    private readonly logger: Logger
  ) {}

  @OnModuleInit()
  async start(): Promise<void> {
    const changeFeedProcessor = this.cosmosClient
      .database(this.config.externalDatabases.crewManagement.databaseId)
      .container(this.config.externalDatabases.crewManagement.containerId)
      .changeFeed.getChangeFeedProcessorBuilder('training-crew-management-processor', this.handleChanges.bind(this))
      .withLeaseContainer(this.getLeaseContainer())
      .withInstanceName('training-change-feed-instance')
      .withStartTime(new Date(Date.now() - 60 * 1000)) // Last minute
      .build();

    await changeFeedProcessor.start();
    this.logger.log('Crew Management Change Feed Processor started');
  }

  @OnModuleDestroy()
  async stop(): Promise<void> {
    // Stop the processor
  }

  private async handleChanges(changes: any[], context: any): Promise<void> {
    this.logger.log(`Processing ${changes.length} changes from Crew Management`);

    for (const document of changes) {
      try {
        // Extract operator data and create command
        if (document.type === 'OperatorRecord') {
          await this.commandBus.execute(
            new SyncOperatorDataCommand(
              document.id,
              document.name,
              document.department,
              document.function
            )
          );
        }
      } catch (error) {
        this.logger.error(`Error processing change for document ${document.id}`, error.stack);
        // Continue with next document (don't stop processing)
      }
    }
  }

  private getLeaseContainer(): Container {
    return this.cosmosClient
      .database(this.config.databaseId)
      .container(this.config.leaseContainerId);
  }
}

// Similar implementations for SkillsMatrixChangeFeedProcessor and VersatilityMatrixChangeFeedProcessor

// Commands dispatched by Change Feed processors
export class SyncOperatorDataCommand {
  constructor(
    public readonly operatorId: string,
    public readonly name: string,
    public readonly department: string,
    public readonly function?: string
  ) {}
}

export class SyncProcessDefinitionCommand {
  constructor(
    public readonly processId: string,
    public readonly processName: string
  ) {}
}

export class SyncVersatilityDataCommand {
  constructor(
    public readonly operatorId: string,
    public readonly workstationId?: string,
    public readonly data?: Record<string, any>
  ) {}
}
```

### 3.7 Event Publishing

Event publishing uses Azure Service Bus with NestJS:

```typescript
// Event publisher interface
export interface EventPublisher {
  publish<T extends DomainEvent>(event: T): Promise<void>;
}

// Service Bus implementation
@Injectable()
export class ServiceBusEventPublisher implements EventPublisher {
  constructor(
    @Inject("SERVICE_BUS_CLIENT") private serviceBusClient: ServiceBusClient,
    @Inject("EVENT_MAPPING_CONFIG") private eventMapping: EventMappingConfig,
    private logger: Logger
  ) {}

  async publish<T extends DomainEvent>(event: T): Promise<void> {
    try {
      const eventType = event.constructor.name;
      const topicName = this.getTopicForEvent(eventType);

      if (!topicName) {
        this.logger.warn(`No topic configured for event type: ${eventType}`);
        return;
      }

      const sender = this.serviceBusClient.createSender(topicName);

      try {
        const message: ServiceBusMessage = {
          body: this.serializeEvent(event),
          contentType: "application/json",
          subject: eventType,
          messageId: event.eventId,
          correlationId: (event as any).correlationId || event.eventId,
          applicationProperties: {
            eventType,
          },
        };

        await sender.sendMessages(message);
        this.logger.log(`Published ${eventType} to ${topicName}`);
      } finally {
        await sender.close();
      }
    } catch (error) {
      this.logger.error(
        `Error publishing event ${event.constructor.name}`,
        error.stack
      );
      throw error;
    }
  }

  private getTopicForEvent(eventType: string): string | undefined {
    // Map event types to topics
    if (eventType.endsWith("WorkflowRequestedEvent")) {
      return this.eventMapping.topics.workflowTrigger;
    }

    if (eventType === "TrainingStatusChangedEvent") {
      return this.eventMapping.topics.trainingStatus;
    }

    if (eventType.endsWith("RequiredEvent")) {
      return this.eventMapping.topics.notification;
    }

    return this.eventMapping.topics.default;
  }

  private serializeEvent(event: DomainEvent): any {
    return {
      ...event,
      // Add metadata for event handling
      _eventType: event.constructor.name,
      _timestamp: new Date().toISOString(),
    };
  }
}

// Configuration interface
export interface EventMappingConfig {
  topics: {
    default: string;
    trainingStatus: string;
    workflowTrigger: string;
    notification: string;
  };
}
```

### 3.8 Integration with Validation Submodule

Integration with the Validation Submodule is implemented using HTTP clients in NestJS:

```typescript
// Validation service interface
export interface ValidationService {
  getValidationTemplate(
    department: string,
    processId: string
  ): Promise<ValidationTemplateDto>;
  submitValidationResults(
    submission: ValidationSubmissionDto
  ): Promise<ValidationResultDto>;
  getCertificationTemplate(
    department: string,
    processId: string
  ): Promise<CertificationTemplateDto>;
}

// Data transfer objects
export class ValidationTemplateDto {
  @ApiProperty()
  templateId: string;

  @ApiProperty()
  processId: string;

  @ApiProperty()
  department: string;

  @ApiProperty({ type: [ValidationCriteriaDto] })
  criteria: ValidationCriteriaDto[];
}

export class ValidationCriteriaDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  weight: number;
}

export class ValidationSubmissionDto {
  @ApiProperty()
  templateId: string;

  @ApiProperty()
  operatorId: string;

  @ApiProperty()
  processId: string;

  @ApiProperty({ type: [CriteriaResultDto] })
  results: CriteriaResultDto[];

  @ApiProperty()
  validatorId: string;
}

export class CriteriaResultDto {
  @ApiProperty()
  criteriaId: string;

  @ApiProperty()
  score: number;

  @ApiProperty({ required: false })
  comments?: string;
}

export class ValidationResultDto {
  @ApiProperty()
  operatorId: string;

  @ApiProperty()
  processId: string;

  @ApiProperty()
  totalScore: number;

  @ApiProperty()
  isValidated: boolean;

  @ApiProperty()
  validatedBy: string;

  @ApiProperty()
  validatedAt: Date;
}

// HTTP client implementation
@Injectable()
export class ValidationServiceClient implements ValidationService {
  constructor(
    private readonly httpService: HttpService,
    @Inject("VALIDATION_SERVICE_CONFIG")
    private config: ValidationServiceConfig,
    private readonly logger: Logger
  ) {}

  async getValidationTemplate(
    department: string,
    processId: string
  ): Promise<ValidationTemplateDto> {
    try {
      const response = await firstValueFrom(
        this.httpService.get<ValidationTemplateDto>(
          `${this.config.baseUrl}/templates/validation/${department}/${processId}`
        )
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error fetching validation template for ${department}/${processId}`,
        error.stack
      );
      throw new ServiceUnavailableException("Validation service unavailable");
    }
  }

  async submitValidationResults(
    submission: ValidationSubmissionDto
  ): Promise<ValidationResultDto> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<ValidationResultDto>(
          `${this.config.baseUrl}/validation/submit`,
          submission
        )
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error submitting validation results for operator ${submission.operatorId}`,
        error.stack
      );
      throw new ServiceUnavailableException("Validation service unavailable");
    }
  }

  async getCertificationTemplate(
    department: string,
    processId: string
  ): Promise<CertificationTemplateDto> {
    try {
      const response = await firstValueFrom(
        this.httpService.get<CertificationTemplateDto>(
          `${this.config.baseUrl}/templates/certification/${department}/${processId}`
        )
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error fetching certification template for ${department}/${processId}`,
        error.stack
      );
      throw new ServiceUnavailableException("Validation service unavailable");
    }
  }
}

// Configuration interface
export interface ValidationServiceConfig {
  baseUrl: string;
  timeoutMs: number;
}
```

### 3.9 Module 1 Workflow Integration

Integration with Module 1 Workflow involves triggering workflows and processing results:

```typescript
// Service for triggering workflows
@Injectable()
export class WorkflowTriggerService {
  constructor(
    private readonly eventPublisher: EventPublisher,
    private readonly logger: Logger
  ) {}

  async triggerCertificationWorkflow(
    operatorId: string,
    processId: string,
    initiatedByUserId: string,
    context: Record<string, any> = {}
  ): Promise<string> {
    try {
      const correlationId = v4();

      // Create and publish event
      const event = new CertificationWorkflowRequestedEvent(
        operatorId,
        processId,
        initiatedByUserId,
        { ...context },
        correlationId
      );

      await this.eventPublisher.publish(event);

      this.logger.log(`Triggered certification workflow for operator ${operatorId}, process ${processId}`);

      return correlationId;
    } catch (error) {
      this.logger.error(`Error triggering certification workflow for operator ${operatorId}`, error.stack);
      throw new ServiceUnavailableException('Unable to trigger workflow');
    }
  }

  // Similar methods for other workflow types...
  async triggerRecertificationWorkflow(...): Promise<string> { /* Similar implementation */ }
  async triggerQualificationRemovalWorkflow(...): Promise<string> { /* Similar implementation */ }
  async triggerIpeWorkflow(...): Promise<string> { /* Similar implementation */ }
}

// Service Bus subscription handler
@Injectable()
export class WorkflowResultProcessor {
  constructor(
    @Inject('SERVICE_BUS_CLIENT') private serviceBusClient: ServiceBusClient,
    @Inject('SERVICE_BUS_CONFIG') private config: ServiceBusConfig,
    private readonly commandBus: CommandBus,
    private readonly logger: Logger
  ) {}

  @OnModuleInit()
  async start(): Promise<void> {
    const receiver = this.serviceBusClient.createReceiver(
      this.config.topics.workflowCompletion,
      this.config.subscriptions.workflowResults
    );

    receiver.subscribe({
      processMessage: async (message) => {
        try {
          await this.processWorkflowResult(message);
          await receiver.completeMessage(message);
        } catch (error) {
          this.logger.error(`Error processing workflow result`, error.stack);
          await receiver.abandonMessage(message);
        }
      },
      processError: async (error) => {
        this.logger.error(`Error in workflow result processor`, error);
      }
    });

    this.logger.log('Workflow result processor started');
  }

  private async processWorkflowResult(message: ServiceBusReceivedMessage): Promise<void> {
    const body = message.body;
    const eventType = message.applicationProperties?.eventType as string;

    this.logger.log(`Received workflow result: ${eventType}`);

    if (eventType === 'CertificationWorkflowCompletedEvent') {
      await this.commandBus.execute(
        new ProcessCertificationResultCommand(
          body.operatorId,
          body.processId,
          body.outcome === 'Approved',
          new Date(body.completionDate),
          body.approvedByUserId
        )
      );
    }
    else if (eventType === 'RecertificationWorkflowCompletedEvent') {
      await this.commandBus.execute(
        new ProcessRecertificationResultCommand(
          body.operatorId,
          body.processId,
          body.outcome === 'Approved',
          new Date(body.completionDate),
          body.approvedByUserId
        )
      );
    }
    // Handle other workflow types...
  }
}

// Controller for handling callback endpoints
@ApiTags('callbacks')
@Controller('training/callbacks')
export class WorkflowCallbackController {
  constructor(private readonly commandBus: CommandBus) {}

  @ApiOperation({ summary: 'Receive workflow result callback' })
  @ApiResponse({ status: 200, description: 'Workflow result processed' })
  @Post('workflow-result')
  async processWorkflowResult(
    @Body() result: WorkflowResultCallbackDto
  ): Promise<void> {
    // Process based on workflow type
    if (result.workflowType === 'Certification') {
      await this.commandBus.execute(
        new ProcessCertificationResultCommand(
          result.operatorId,
          result.processId,
          result.outcome === 'Approved',
          new Date(result.completionDate),
          result.approvedByUserId
        )
      );
    }
    else if (result.workflowType === 'Recertification') {
      // Similar implementation...
    }
    // Handle other workflow types...
  }
}

// DTO for workflow result callback
export class WorkflowResultCallbackDto {
  @ApiProperty()
  @IsString()
  workflowType: string;

  @ApiProperty()
  @IsString()
  operatorId: string;

  @ApiProperty()
  @IsString()
  processId: string;

  @ApiProperty({ enum: ['Approved', 'Rejected'] })
  @IsString()
  outcome: string;

  @ApiProperty()
  @IsDateString()
  completionDate: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  approvedByUserId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  rejectionReason?: string;

  @ApiProperty()
  @IsString()
  correlationId: string;
}
```

---

## 4. Data Models

### 4.1 Cosmos DB Data Models

#### 4.1.1 Write Model (`OperatorTrainingRecord`)

This model represents the aggregate root persisted in the write store. It contains all data necessary for enforcing business rules and tracking training history. Includes duplicated data from other modules for efficiency.

```typescript
// TypeScript interface for Cosmos DB document
export interface TrainingRecordDocument {
  id: string; // Operator ID as document ID
  type: string; // Document type discriminator
  partitionKey: string; // Partitioned by operator ID
  operatorId: string;

  // Duplicated from Crew Management (via Change Feed)
  operatorName: string;
  department: string;
  function?: string;
  hiringDate?: string;

  // Business state
  currentOverallStatus: string; // Enum value as string

  // Collections
  qualifications: {
    processId: string;
    // Duplicated from Skills Matrix (via Change Feed)
    processName: string;
    // Training status
    status: string; // Enum value as string (O, LC, V, etc.)
    lastCertificationDate?: string; // ISO string
    lastRecertificationDate?: string; // ISO string
    isActive: boolean;
  }[];

  // Training history as event sourcing
  history: {
    eventId: string;
    eventType: string;
    phase: string; // Enum value as string
    processId: string;
    timestamp: string; // ISO string
    result: string; // Score or status result
    details: Record<string, any>; // Extensible properties as JSON
    userId?: string; // User who initiated/completed the action
  }[];

  // Cosmos DB metadata
  _etag: string;
  _ts: number;
}
```

Example document:

```json
{
  "id": "op-123",
  "type": "OperatorTrainingRecord",
  "partitionKey": "op-123",
  "operatorId": "op-123",
  "operatorName": "John Doe",
  "department": "Assembling",
  "function": "Wire Harness Operator",
  "hiringDate": "2024-01-01T00:00:00Z",
  "currentOverallStatus": "Certified",
  "qualifications": [
    {
      "processId": "PROC-ASY-001",
      "processName": "Wire Harness Assembly",
      "status": "C",
      "lastCertificationDate": "2024-03-15T00:00:00Z",
      "lastRecertificationDate": "2024-03-15T00:00:00Z",
      "isActive": true
    },
    {
      "processId": "PROC-ASY-005",
      "processName": "Advanced Soldering",
      "status": "LC",
      "isActive": true
    }
  ],
  "history": [
    {
      "eventId": "evt-001",
      "eventType": "OjtCompleted",
      "phase": "O",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-01-15T10:30:00Z",
      "result": "85%",
      "details": {
        "checklistUrl": "/attachments/checklist-123.pdf",
        "comments": "Performed well on basic tasks"
      },
      "userId": "trainer-456"
    },
    {
      "eventId": "evt-002",
      "eventType": "LcCompleted",
      "phase": "LC",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-02-10T14:15:00Z",
      "result": "OK",
      "details": {
        "checklistUrl": "/attachments/checklist-456.pdf"
      },
      "userId": "trainer-456"
    },
    {
      "eventId": "evt-003",
      "eventType": "ValidationInitiated",
      "phase": "V",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-02-20T09:00:00Z",
      "result": null,
      "details": {},
      "userId": "tl-789"
    },
    {
      "eventId": "evt-004",
      "eventType": "ValidationCompleted",
      "phase": "V",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-02-25T11:00:00Z",
      "result": "Score: 88%",
      "details": { "validatedBy": "validator-abc" },
      "userId": "tl-789"
    },
    {
      "eventId": "evt-005",
      "eventType": "CertificationInitiated",
      "phase": "C",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-03-01T10:00:00Z",
      "result": null,
      "details": { "workflowInstanceId": "wf-xyz" },
      "userId": "tl-789"
    },
    {
      "eventId": "evt-006",
      "eventType": "CertificationCompleted",
      "phase": "C",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-03-15T16:30:00Z",
      "result": "Approved",
      "details": {
        "workflowInstanceId": "wf-xyz",
        "approvedBy": "approver-1"
      },
      "userId": "system"
    }
  ],
  "_etag": "\"00000000-0000-0000-0000-000000000000\"",
  "_ts": 1618847477
}
```

#### 4.1.2 Read Model Projections

These denormalized projections are optimized for query performance.

**Operator Training Summary Projection:**

```typescript
// TypeScript interface for the Summary projection
export interface OperatorTrainingSummaryDocument {
  id: string; // Operator ID
  type: string; // "OperatorTrainingSummary"
  partitionKey: string; // Operator ID
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingStatus: string; // Overall status
  qualificationCount: number; // Total active qualifications
  activeQualifications: {
    processId: string;
    processName: string;
    status: string; // Enum as string
  }[];
  lastUpdated: string; // ISO string
}
```

Example document:

```json
{
  "id": "op-123",
  "type": "OperatorTrainingSummary",
  "partitionKey": "op-123",
  "operatorId": "op-123",
  "operatorName": "John Doe",
  "department": "Assembling",
  "currentTrainingStatus": "Certified",
  "qualificationCount": 2,
  "activeQualifications": [
    {
      "processId": "PROC-ASY-001",
      "processName": "Wire Harness Assembly",
      "status": "C"
    },
    {
      "processId": "PROC-ASY-005",
      "processName": "Advanced Soldering",
      "status": "LC"
    }
  ],
  "lastUpdated": "2024-03-15T16:30:00Z"
}
```

**Department Summary Projection:**

```typescript
// TypeScript interface for Department Summary
export interface DepartmentSummaryDocument {
  id: string; // "dept-{DepartmentName}"
  type: string; // "DepartmentSummary"
  partitionKey: string; // Department name
  department: string;
  operatorCount: number; // Total operators in department
  operatorsByPhase: {
    O: number; // Count of operators in OJT phase
    LC: number; // Count of operators in Learning Curve
    V: number; // Count of validated operators
    C: number; // Count of certified operators
    R: number; // Count of recertified operators
    F: number; // Count of operators in familiarization
  };
  qualificationsByStatus: {
    [status: string]: number; // Counts by status
  };
  lastUpdated: string; // ISO string
}
```

Example document:

```json
{
  "id": "dept-Assembling",
  "type": "DepartmentSummary",
  "partitionKey": "Assembling",
  "department": "Assembling",
  "operatorCount": 50,
  "operatorsByPhase": {
    "O": 10,
    "LC": 15,
    "V": 5,
    "C": 20,
    "R": 0,
    "F": 0
  },
  "qualificationsByStatus": {
    "O": 25,
    "LC": 30,
    "V": 15,
    "C": 40,
    "R": 0,
    "F": 5,
    "V_Pending": 2,
    "C_Pending": 3
  },
  "lastUpdated": "2024-03-15T16:30:00Z"
}
```

**Process Qualification Summary Projection:**

```typescript
// TypeScript interface for Process Qualification Summary
export interface ProcessQualificationSummaryDocument {
  id: string; // "proc-{ProcessId}"
  type: string; // "ProcessQualificationSummary"
  partitionKey: string; // Process ID
  processId: string;
  processName: string;
  totalQualifiedOperators: number;
  operatorsByStatus: {
    [status: string]: number; // Counts by status
  };
  lastUpdated: string; // ISO string
}
```

Example document:

```json
{
  "id": "proc-PROC-ASY-001",
  "type": "ProcessQualificationSummary",
  "partitionKey": "PROC-ASY-001",
  "processId": "PROC-ASY-001",
  "processName": "Wire Harness Assembly",
  "totalQualifiedOperators": 35,
  "operatorsByStatus": {
    "O": 5,
    "LC": 10,
    "V": 5,
    "C": 15,
    "R": 0,
    "F": 0
  },
  "lastUpdated": "2024-03-15T16:30:00Z"
}
```

### 4.2 Command DTOs

Command DTOs define the structure of incoming command requests. They are validated using class-validator decorators:

```typescript
// OJT completion command
export class CompleteOjtDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: 'Training result (e.g., "85%")' })
  @IsString()
  @IsNotEmpty()
  result: string;

  @ApiProperty({ description: "URL to uploaded checklist", required: false })
  @IsOptional()
  @IsString()
  checklistUrl?: string;

  @ApiProperty({ description: "User ID of trainer submitting the result" })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: "Additional comments", required: false })
  @IsOptional()
  @IsString()
  comments?: string;
}

// Learning curve completion command
export class CompleteLearningCurveDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: 'Training result (e.g., "OK", "NOK")' })
  @IsString()
  @IsNotEmpty()
  result: string;

  @ApiProperty({ description: "URL to uploaded checklist", required: false })
  @IsOptional()
  @IsString()
  checklistUrl?: string;

  @ApiProperty({ description: "User ID of trainer submitting the result" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// Validation initiation command
export class InitiateValidationDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: "User ID initiating validation" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// Certification initiation command
export class InitiateCertificationDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: "User ID initiating certification" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// Recertification initiation command
export class InitiateRecertificationDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: "User ID initiating recertification" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// New qualification initiation command
export class InitiateNewQualificationDto {
  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: "Process name" })
  @IsString()
  @IsNotEmpty()
  processName: string;

  @ApiProperty({ description: "User ID initiating new qualification" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// Qualification removal command
export class RemoveQualificationDto {
  @ApiProperty({ description: "Operator ID" })
  @IsString()
  @IsNotEmpty()
  operatorId: string;

  @ApiProperty({ description: "Process ID" })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: "Reason for removal" })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({ description: "User ID requesting removal" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

// Individual Performance Evaluation initiation command
export class InitiateIpeDto {
  @ApiProperty({ description: "Process ID (optional)" })
  @IsOptional()
  @IsString()
  processId?: string;

  @ApiProperty({ description: "User ID initiating IPE" })
  @IsString()
  @IsNotEmpty()
  userId: string;
}
```

### 4.3 Query DTOs

Query DTOs define the structure of response data:

```typescript
// Basic qualification summary
export class QualificationSummaryDto {
  @ApiProperty()
  processId: string;

  @ApiProperty()
  processName: string;

  @ApiProperty({ enum: QualificationStatus })
  status: string;
}

// Detailed qualification information
export class QualificationDto extends QualificationSummaryDto {
  @ApiProperty({ required: false, nullable: true, type: Date })
  lastCertificationDate?: Date;

  @ApiProperty({ required: false, nullable: true, type: Date })
  lastRecertificationDate?: Date;

  @ApiProperty()
  isActive: boolean;
}

// Training history entry
export class TrainingHistoryEntryDto {
  @ApiProperty()
  eventId: string;

  @ApiProperty()
  eventType: string;

  @ApiProperty({ enum: TrainingPhase })
  phase: string;

  @ApiProperty()
  processId: string;

  @ApiProperty()
  timestamp: Date;

  @ApiProperty()
  result: string;

  @ApiProperty({ type: Object })
  details: Record<string, any>;

  @ApiProperty({ required: false, nullable: true })
  userId?: string;
}

// Operator training summary
export class OperatorTrainingSummaryDto {
  @ApiProperty()
  operatorId: string;

  @ApiProperty()
  operatorName: string;

  @ApiProperty()
  department: string;

  @ApiProperty({ enum: TrainingStatus })
  currentTrainingStatus: string;

  @ApiProperty()
  qualificationCount: number;

  @ApiProperty({ type: [QualificationSummaryDto] })
  activeQualifications: QualificationSummaryDto[];

  @ApiProperty()
  lastUpdated: Date;
}

// Detailed operator training information
export class OperatorTrainingDetailsDto extends OperatorTrainingSummaryDto {
  @ApiProperty({ type: [QualificationDto] })
  qualifications: QualificationDto[];

  @ApiProperty({ type: [TrainingHistoryEntryDto] })
  trainingHistory: TrainingHistoryEntryDto[];
}

// Department summary
export class DepartmentSummaryDto {
  @ApiProperty()
  department: string;

  @ApiProperty()
  operatorCount: number;

  @ApiProperty({ type: Object })
  operatorsByPhase: Record<string, number>;

  @ApiProperty({ type: Object })
  qualificationsByStatus: Record<string, number>;

  @ApiProperty()
  lastUpdated: Date;
}

// Process qualification summary
export class ProcessQualificationSummaryDto {
  @ApiProperty()
  processId: string;

  @ApiProperty()
  processName: string;

  @ApiProperty()
  totalQualifiedOperators: number;

  @ApiProperty({ type: Object })
  operatorsByStatus: Record<string, number>;

  @ApiProperty()
  lastUpdated: Date;
}
```

### 4.4 Event Schemas

TypeScript interfaces for domain events and integration events used in the system:

```typescript
// Base domain event
export interface DomainEventBase {
  eventId: string;
  timestamp: string; // ISO string
}

// Training status events
export interface OjtCompletedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  result: string;
  userId?: string;
}

export interface LearningCurveCompletedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  result: string;
  userId?: string;
}

export interface ValidationInitiatedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  userId: string;
}

export interface ValidationCompletedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  score: number;
  isValidated: boolean;
  userId?: string;
}

export interface CertificationInitiatedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  userId: string;
}

export interface CertificationCompletedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  isCertified: boolean;
  certificationDate: string; // ISO string
  userId?: string;
}

export interface RecertificationInitiatedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  userId: string;
}

export interface RecertificationCompletedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  isRecertified: boolean;
  recertificationDate: string; // ISO string
  userId?: string;
}

export interface NewQualificationInitiatedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  processName: string;
  userId: string;
}

export interface NewQualificationCompletedEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  result: string;
  userId?: string;
}

// Status change event (for Versatility Matrix)
export interface TrainingStatusChangedEvent extends DomainEventBase {
  operatorId: string;
  operatorName?: string; // Optional denormalized field
  processId: string;
  processName?: string; // Optional denormalized field
  oldStatus: string;
  newStatus: string;
  statusChangeDate: string; // ISO string
  correlationId?: string;
}

// Workflow trigger events
export interface CertificationWorkflowRequestedEvent extends DomainEventBase {
  workflowType: "Certification";
  operatorId: string;
  processId: string;
  initiatedByUserId: string;
  context: Record<string, any>;
  correlationId: string;
}

export interface RecertificationWorkflowRequestedEvent extends DomainEventBase {
  workflowType: "Recertification";
  operatorId: string;
  processId: string;
  initiatedByUserId: string;
  context: Record<string, any>;
  correlationId: string;
}

export interface QualificationRemovalWorkflowRequestedEvent
  extends DomainEventBase {
  workflowType: "QualificationRemoval";
  operatorId: string;
  processId: string;
  reason: string;
  initiatedByUserId: string;
  context: Record<string, any>;
  correlationId: string;
}

export interface IpeWorkflowRequestedEvent extends DomainEventBase {
  workflowType: "IPE";
  operatorId: string;
  processId?: string;
  initiatedByUserId: string;
  context: Record<string, any>;
  correlationId: string;
}

// Workflow result events
export interface WorkflowCompletedEventBase extends DomainEventBase {
  workflowType: string;
  operatorId: string;
  processId: string;
  outcome: "Approved" | "Rejected";
  completionDate: string; // ISO string
  approvedByUserId?: string;
  rejectionReason?: string;
  correlationId: string;
}

export interface CertificationWorkflowCompletedEvent
  extends WorkflowCompletedEventBase {
  workflowType: "Certification";
}

export interface RecertificationWorkflowCompletedEvent
  extends WorkflowCompletedEventBase {
  workflowType: "Recertification";
}

export interface QualificationRemovalWorkflowCompletedEvent
  extends WorkflowCompletedEventBase {
  workflowType: "QualificationRemoval";
}

export interface IpeWorkflowCompletedEvent extends WorkflowCompletedEventBase {
  workflowType: "IPE";
  evaluationScore?: number;
}

// Notification events
export interface ManualActionRequiredEvent extends DomainEventBase {
  operatorId: string;
  processId: string;
  actionType: string;
  message: string;
  assignedToUserId?: string;
  dueDate?: string; // ISO string
  priority: "Low" | "Medium" | "High";
}
```
