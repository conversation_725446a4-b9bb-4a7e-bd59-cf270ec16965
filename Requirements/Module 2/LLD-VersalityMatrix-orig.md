# Versatility Matrix - Low-Level Design (LLD)

## Document Information

**Version:** 1.2.0
**Last Updated:** 2023-04-22
**Status:** Draft
**Authors: <AUTHORS>

## 1. Executive Summary

The Versatility Matrix microservice is a critical component of the Connected Workers (CW) application, providing a dynamic visualization and management system for tracking operator skills, certifications, and workstation experience across the Assembling and Cutting & Lead Prep departments. It enables Team Leaders and other stakeholders to quickly identify qualified operators for workstation replacements while maintaining compliance with training requirements.

### 1.1 Key Features

- Dual-table matrix system for the Assembling department (Qualifications and Polyvalence)
- Single-table qualification tracking for the Cutting & Lead Prep department
- Role-based access control with differentiated permissions
- Real-time qualification status updates from the Training Process
- Team Leader-managed polyvalence level tracking
- Dynamic filtering, sorting, and searching capabilities
- Criticality-based operator coverage tracking for workstations
- Comprehensive audit logging and compliance reporting
- Export capabilities for offline analysis and reporting

### 1.2 Key Integration Points

- **Training Process**: Consumes qualification status updates via Service Bus events
- **Crew Management**: Consumes operator data changes via Change Feed
- **Replacement Process**: Provides qualified operator suggestions for absent workers
- **Manufacturing Engineering**: Consumes workstation definitions and criticality settings

## 2. Architecture

### 2.1 Architectural Patterns

The Versatility Matrix implements the following architectural patterns:

1. **Microservices Architecture**:

   - Independent service within the Connected Workers ecosystem
   - Well-defined boundaries with specific responsibilities
   - Self-contained with its own data store and processing capabilities

2. **CQRS Pattern**:

   - **Commands**: Focused on updates (UpdatePolyvalence, SyncQualification)
   - **Queries**: Optimized for different views (Matrix views, Coverage reports)
   - **Write Model**: Normalized data structure for updates
   - **Read Model**: Denormalized projections for quick retrieval

3. **Event-Driven Architecture**:

   - Consumes events from Training Process
   - Publishes internal domain events
   - Exposes state changes via Cosmos DB Change Feed

4. **Domain-Driven Design**:
   - Clear aggregate boundaries (Qualification, Polyvalence, Operator)
   - Value objects like PolyvalenceLevel, QualificationStatus
   - Domain Services for complex business logic

### 2.2 High-Level Component Diagram

```mermaid
graph TB
    subgraph "External Systems"
        HR["HR System"]
        TP["Training Process"]
        MES["Manufacturing Execution System<br>(APRISO)"]
        ME["Manufacturing Engineering"]
    end

    subgraph "Versatility Matrix Service"
        API["API Layer"]
        QS["Qualification Service"]
        PV["Polyvalence Service"]
        AZ["Authorization Service"]
        AS["Analytics Service"]
        REPO["Repository Layer"]

        API --> QS
        API --> PV
        API --> AZ
        API --> AS

        QS --> REPO
        PV --> REPO
        AS --> REPO
    end

    subgraph "Storage"
        DB[(Cosmos DB)]
    end

    subgraph "Event Infrastructure"
        SB["Azure Service Bus"]
        CF["Change Feed Processor"]
        EG["Event Grid"]
    end

    subgraph "Client Applications"
        UI["Web UI"]
        MUI["Mobile UI"]
    end

    %% External System Connections
    HR -- "Operator Data" --> API
    TP -- "Qualification Updates" --> SB
    MES -. "Optional Workstation Time Data" .-> API
    ME -- "Workstation Definitions" --> API

    %% Service Bus Integration
    SB --> API

    %% Data Storage
    REPO --> DB

    %% Change Notification
    DB --> CF
    CF --> EG

    %% Client Connections
    EG --> UI
    EG --> MUI
    UI -- "API Calls" --> API
    MUI -- "API Calls" --> API

    %% Integration with other Connected Workers modules
    RP["Replacement Process"] -- "Query Qualified Operators" --> API

    classDef external fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#bfb,stroke:#333,stroke-width:1px;
    classDef events fill:#fbb,stroke:#333,stroke-width:1px;
    classDef client fill:#bbf,stroke:#333,stroke-width:1px;

    class HR,TP,MES,ME,RP external;
    class API,QS,PV,AZ,AS,REPO service;
    class DB storage;
    class SB,CF,EG events;
    class UI,MUI client;
```

### 2.3 Technology Stack

- **Backend Framework**: NestJS with TypeScript
- **Data Storage**: Azure Cosmos DB with SQL API
- **Message Broker**: Azure Service Bus
- **Change Feed Processing**: Azure Functions
- **Authentication**: Azure AD with RBAC
- **API Documentation**: OpenAPI/Swagger
- **Monitoring**: Application Insights
- **Deployment**: Azure Kubernetes Service (AKS)
- **CI/CD**: Azure DevOps Pipelines
- **Secret Management**: Azure Key Vault

## 3. Core Domain Model

### 3.1 Domain Entities

```mermaid
classDiagram
    class Operator {
        +string id
        +string employeeNumber
        +string name
        +Date hiringDate
        +string areaId
        +string departmentId
        +boolean isActive
    }

    class Qualification {
        +string id
        +string operatorId
        +string skillCode
        +QualificationStatus status
        +Date certificationDate
        +Date expiryDate
        +string updatedBy
    }

    class Polyvalence {
        +string id
        +string operatorId
        +string workstationId
        +PolyvalenceLevel level
        +string updatedBy
        +string notes
    }

    class Workstation {
        +string id
        +string name
        +string departmentId
        +string areaId
        +CriticalityLevel criticalityLevel
        +number targetBackupOperators
        +string[] requiredSkills
    }

    class WorkstationCoverage {
        +string workstationId
        +number targetBackupOperators
        +number actualBackupOperators
        +number gap
    }

    class AuditLog {
        +string id
        +Date timestamp
        +string userId
        +string action
        +string entityType
        +string entityId
        +string oldValue
        +string newValue
    }

    %% Enums
    class QualificationStatus {
        <<enumeration>>
        O (On Job Training)
        LC (Learning Curve)
        V (Validated)
        C (Certified)
        R (Re-certified)
        F (Formé)
    }

    class PolyvalenceLevel {
        <<enumeration>>
        X (Owner)
        1 (<1 month experience)
        2 (1-2 months experience)
        3 (2-6 months experience)
        4 (>6 months experience)
    }

    class CriticalityLevel {
        <<enumeration>>
        Critical (3 backups)
        Medium (1 backup)
        Normal (0 backups)
    }

    %% Relationships
    Operator "1" -- "many" Qualification : has
    Operator "1" -- "many" Polyvalence : has
    Polyvalence "many" -- "1" Workstation : rates
    Qualification -- QualificationStatus
    Polyvalence -- PolyvalenceLevel
    Workstation -- CriticalityLevel
    Workstation "1" -- "1" WorkstationCoverage : has
```

### 3.2 Data Storage

The Versatility Matrix uses the following Cosmos DB containers:

| Container            | Partition Key | Purpose                       |
| -------------------- | ------------- | ----------------------------- |
| operators            | /departmentId | Operator master data          |
| skills               | /departmentId | Skill definitions             |
| workstations         | /departmentId | Workstation definitions       |
| qualifications       | /operatorId   | Qualification status records  |
| polyvalence          | /operatorId   | Polyvalence level records     |
| workstation-coverage | /departmentId | Pre-computed coverage metrics |
| audit-logs           | /entityType   | Audit trail entries           |

## 4. Key Workflows

### 4.1 Qualification Update Flow

```mermaid
sequenceDiagram
    participant TP as Training Process
    participant SB as Azure Service Bus
    participant API as Versatility Matrix API
    participant QS as Qualification Service
    participant DB as Cosmos DB
    participant CF as Change Feed Processor
    participant UI as User Interface

    TP->>SB: Publish TrainingQualificationUpdated
    SB->>API: Deliver Event
    API->>QS: ProcessQualificationUpdate(payload)
    QS->>DB: Get/Update Qualification
    QS->>DB: Update Workstation Coverage
    DB-->>CF: Trigger on change
    CF->>UI: Push update to connected clients
    QS-->>API: Update success
    API-->>SB: Acknowledge message
```

### 4.2 Polyvalence Update Flow

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant UI as User Interface
    participant API as API Layer
    participant Auth as Authorization Service
    participant PV as Polyvalence Service
    participant DB as Cosmos DB
    participant CF as Change Feed Processor
    participant Audit as Audit Service

    TL->>UI: Update Polyvalence Level
    UI->>API: PUT /api/polyvalence/{operatorId}/{workstationId}

    API->>Auth: VerifyPermission(userId, "UpdatePolyvalence")
    Auth-->>API: Permission Granted

    API->>PV: UpdatePolyvalence(operatorId, workstationId, level)
    PV->>DB: Get/Update Polyvalence
    DB-->>PV: Confirmation

    PV->>Audit: LogAction(userId, "UpdatePolyvalence", details)
    Audit->>DB: Save Audit Log

    DB->>CF: Trigger on change
    CF->>UI: Push update to connected clients

    API-->>UI: Success Response
    UI-->>TL: Show Success Message
```

## 5. API Design

### 5.1 Key REST Endpoints

| Method | Endpoint                                        | Description                       | Access Roles                |
| ------ | ----------------------------------------------- | --------------------------------- | --------------------------- |
| GET    | `/api/matrix`                                   | Retrieve matrix data with filters | All (area-based)            |
| GET    | `/api/matrix/export`                            | Export matrix to Excel/CSV        | All (area-based)            |
| PUT    | `/api/polyvalence/{operatorId}/{workstationId}` | Update polyvalence level          | Team Leader                 |
| GET    | `/api/polyvalence/history/{operatorId}`         | Get polyvalence history           | All (area-based)            |
| GET    | `/api/qualification/{operatorId}`               | Get operator qualifications       | All (area-based)            |
| GET    | `/api/qualification/expiring`                   | Get expiring certifications       | Center Trainer, Team Leader |
| GET    | `/api/workstation/coverage`                     | Get workstation coverage metrics  | All (area-based)            |
| GET    | `/api/reports/*`                                | Various reports endpoints         | All (area-based)            |

### 5.2 CQRS Commands and Queries

#### Key Commands:

- `UpdatePolyvalenceCommand`
- `SyncQualificationStatusCommand`
- `RecalculateWorkstationCoverageCommand`

#### Key Queries:

- `GetMatrixQuery`
- `GetWorkstationCoverageQuery`
- `GetOperatorDetailsQuery`

## 6. Security Model

### 6.1 Role-Based Access Control

| Role                       | Description                   | Permissions                                                       |
| -------------------------- | ----------------------------- | ----------------------------------------------------------------- |
| Center Trainer             | Training department personnel | Full read access to all matrices, no edit rights                  |
| Team Leader                | Production line supervisors   | Read access to team's matrices, edit polyvalence for team members |
| Shift Leader & Coordinator | Production managers           | Read-only access to assigned areas                                |
| Quality Auditor            | Quality personnel             | Read-only access for certification verification                   |

### 6.2 Implementation

- Authentication via Azure AD B2C
- Authorization middleware to enforce permissions
- Team/area-based row-level security
- Comprehensive audit logging

## 7. Cross-Cutting Concerns

### 7.1 Audit Logging

- Log all data changes (who, what, when)
- Track API access and usage patterns
- Record authorization decisions
- Support timeline views of qualification history

### 7.2 Compliance Features

- Monitor certification expiry dates
- Generate alerts for upcoming expirations
- Track coverage against targets based on criticality
- Support compliance reporting for audits

### 7.3 Resilience Patterns

- Circuit breaker for external service calls
- Retry with exponential backoff for transient failures
- Dead-letter queues for failed event processing
- Scheduled reconciliation jobs to ensure data consistency

### 7.4 Monitoring

- Application Insights integration
- SLA compliance monitoring
- Business metrics tracking
- Proactive alerts for system issues

## 8. Azure Services Integration

The Versatility Matrix integrates with various Azure services to ensure reliable and scalable operations.

### 8.1 Azure Cosmos DB

The Versatility Matrix uses Azure Cosmos DB for data storage and retrieval. It implements a repository pattern to abstract data access logic from business logic.

### 8.2 Azure Service Bus

The Versatility Matrix consumes qualification status updates from the Training Process via Azure Service Bus. It also publishes domain events internally for audit logging and potential external consumption.

### 8.3 Azure Event Grid

The Versatility Matrix integrates with Azure Event Grid for event-driven architecture. It consumes Change Feed events from the Crew Management service and publishes domain events internally.

### 8.4 Azure Functions

The Versatility Matrix uses Azure Functions for processing Change Feed events and triggering domain events.

## 9. Testing Strategy

A comprehensive testing strategy ensures the Versatility Matrix meets functional and non-functional requirements:

### 9.1 Unit Testing

- **Components**: Individual services, handlers, and repositories
- **Coverage Target**: 80%+ code coverage
- **Focus Areas**: Business logic, validation rules, calculation algorithms
- **Tools**: xUnit/Jest, Moq/Sinon

### 9.2 Integration Testing

- **Scope**: Service interactions, database operations, message handling
- **Approach**: In-memory databases, containerized dependencies
- **Key Scenarios**: Event processing flows, repository patterns, API controllers
- **Tools**: TestContainers, LocalStack/Azurite

### 9.3 API Testing

- **Coverage**: All API endpoints
- **Validation**: Request/response contracts, authorization rules, error handling
- **Automation**: Postman collections, automated API tests
- **Performance**: Load testing for key endpoints

### 9.4 UI Testing

- **Component Tests**: Individual UI components
- **E2E Tests**: Critical user journeys
- **Visual Regression**: UI appearance validation
- **Accessibility**: WCAG 2.1 AA compliance

### 9.5 Performance Testing

- **Load Testing**: Simulated user load (normal and peak)
- **Stress Testing**: System behavior under extreme conditions
- **Endurance Testing**: System stability over time
- **Scaling Validation**: Auto-scaling behavior

### 9.6 Security Testing

- **Penetration Testing**: Regular security assessments
- **SAST/DAST**: Static and dynamic security analysis
- **Dependency Scanning**: Vulnerable package detection
- **Access Control Validation**: Permission boundary testing

### 9.7 Data Migration Testing

- **Initial Load Testing**: Validation of data import procedures
- **Data Quality Checks**: Verification of imported data
- **Performance Validation**: Load times with production-scale data
- **Reconciliation Testing**: Verification of data consistency

### 9.8 Data Storage and Migration

The Versatility Matrix will use both Azure SQL Database and Azure Cosmos DB for data storage:

1. **Azure SQL Database**: For transactional data with relations, audit trails, and complex queries
2. **Azure Cosmos DB**: For high-availability read models and integration with other services via Change Feed

Database migration will be handled using Entity Framework Core migrations for the SQL Database, with schema version control in the codebase.

## 10. Domain Model - Class Diagram

The following class diagram illustrates the core domain model of the Versatility Matrix and the relationships between entities:

```mermaid
classDiagram
    class Operator {
        +string Id
        +string OperatorId
        +string Name
        +Department Department
        +ShiftType Shift
        +bool IsActive
        +DateTime LastUpdated
        +List~Qualification~ Qualifications
        +List~Polyvalence~ Polyvalences
    }

    class Qualification {
        +string Id
        +string OperatorId
        +string QualificationType
        +DateTime AcquiredDate
        +DateTime ExpiryDate
        +QualificationStatus Status
        +string IssuedBy
        +string Comments
    }

    class Polyvalence {
        +string Id
        +string OperatorId
        +string WorkstationId
        +int SkillLevel
        +string AssessedBy
        +DateTime AssessmentDate
        +string Comments
    }

    class Workstation {
        +string Id
        +string WorkstationId
        +string Name
        +string AreaId
        +bool RequiresQualification
        +string QualificationType
        +bool IsActive
        +WorkstationStatus Status
    }

    class Area {
        +string Id
        +string Name
        +string DepartmentId
        +List~Workstation~ Workstations
    }

    class Department {
        +string Id
        +string Name
        +List~Area~ Areas
        +List~Operator~ Operators
    }

    class WorkstationCoverage {
        +string Id
        +string WorkstationId
        +int TotalOperators
        +int Level1Count
        +int Level2Count
        +int Level3Count
        +int Level4Count
        +int Level5Count
        +float CoverageIndex
        +DateTime CalculatedAt
    }

    class AuditEntry {
        +string Id
        +string EntityType
        +string EntityId
        +string Action
        +string UserId
        +DateTime Timestamp
        +string PreviousState
        +string NewState
    }

    Operator "1" -- "many" Qualification : has
    Operator "1" -- "many" Polyvalence : has
    Department "1" -- "many" Area : contains
    Area "1" -- "many" Workstation : contains
    Department "1" -- "many" Operator : belongs to
    Polyvalence "many" -- "1" Workstation : rates
    Workstation "1" -- "1" WorkstationCoverage : has
```

This class diagram shows the core entities in the Versatility Matrix domain model and their relationships. The Operator entity represents a worker and has collections of Qualifications and Polyvalences. Each Polyvalence links an Operator to a Workstation with a specific skill level. Workstations belong to Areas, which in turn belong to Departments. The WorkstationCoverage entity tracks metrics about how well each workstation is covered by qualified operators. The AuditEntry entity supports the audit trail functionality by tracking all changes to domain entities.

## 11. Event Handling

### 11.1 Class Diagram - Domain Model

The following class diagram illustrates the key entities and their relationships in the Versatility Matrix domain:
