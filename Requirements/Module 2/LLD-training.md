# Training Process Microservice - Low-Level Design (LLD)

## Document Information

**Version:** 1.2.0  
**Last Updated:** 2025-05-05  
**Status:** Draft  
**Authors: <AUTHORS>

## Executive Summary

This document details the low-level design for the Training Process microservice, which orchestrates operator training progression through phases (O, LC, V, C, R, F) based entirely on manual triggers from users (<PERSON>ers, Team Leaders). The service follows event-driven architecture principles, leveraging Azure Cosmos DB Change Feed for consuming data from other modules (Crew Management, Skills Matrix, Versatility Matrix) and Azure Service Bus for event communication (publishing status updates, triggering internal notifications). **Internal workflow capabilities manage multi-step approval processes for certification, recertification, and qualification removal.** The design employs a CQRS pattern to separate read and write operations, optimizing for the specific needs of each operation type.

### Key Features

- Manages operator training lifecycle phases (O, LC, V, C, R, F) without automatic timelines
- Manual initiation of all training phases and evaluations by authorized users
- Change Feed patterns for consuming and duplicating necessary data locally from Crew Management, Skills Matrix, and Versatility Matrix (for read operations).
- Event-driven status updates **published to** Versatility Matrix
- Integration with Validation Submodule (REQ 32/33) for form templates and scoring
- **Internal handling of approval workflows for certification, recertification, qualification removal, and IPE processes.**
- Clear domain boundaries with other modules to prevent redundancy
- CQRS pattern with separate read and write models

### Key Integration Points

- **Crew Management**: Consumes and duplicates operator data via Change Feed for local use.
- **Skills Matrix**: Consumes and duplicates process definitions via Change Feed for local use.
- **Versatility Matrix**: Publishes status updates via events. Consumes and duplicates Versatility data via Change Feed for local read operations (e.g., displaying current assignments if needed).
- **Validation Submodule (REQ 32/33)**: Requests templates and scoring via API.
- **Notification Service**: Publishes notification events for user actions (e.g., pending approvals).

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture](#2-architecture)
   1. [Architectural Patterns](#21-architectural-patterns)
   2. [High-Level Component Diagram](#22-high-level-component-diagram)
   3. [Technology Stack](#23-technology-stack)
   4. [CQRS Implementation](#24-cqrs-implementation)
   5. [Microservices Breakdown](#25-microservices-breakdown)
3. [Detailed Component Design](#3-detailed-component-design)
4. [Data Models](#4-data-models)
5. [API Specification](#5-api-specification)
6. [Event Communication](#6-event-communication)
7. [Azure Services Integration](#7-azure-services-integration)
8. [Cross-Cutting Concerns](#8-cross-cutting-concerns)
9. [Implementation Considerations](#9-implementation-considerations)
10. [Open Questions and Risks](#10-open-questions-and-risks)

---

## 1. Overview

### 1.1 Purpose and Scope

The Training Process microservice's primary responsibility is to orchestrate and track an operator's progression through training phases based on manual triggers. It integrates with other modules via Change Feed (consuming data) and event-driven patterns (publishing status, triggering workflows, receiving results), avoiding direct synchronous coupling where possible. It maintains a local, duplicated copy of necessary data from external sources (like operator details) for efficient operation.

**In Scope:**

- Recording and tracking operator training phase transitions (O, LC, V, C, R, F)
- Processing manually initiated training phase actions from Trainers and Team Leaders
- Consuming operator data from Crew Management via Change Feed and maintaining a local copy.
- Consuming process definitions from Skills Matrix via Change Feed and maintaining a local copy.
- Consuming data from Versatility Matrix via Change Feed for read-only purposes (e.g., enriching UI views). (_Specific data fields need confirmation_).
- Integrating with Validation Submodule for form templates and scoring
- **Managing internal approval workflows for certification, recertification, qualification removal, and IPE.**
- Publishing status updates (`TrainingStatusChanged` event) to Versatility Matrix
- **Publishing notification events to a central Notification Service for workflow steps (e.g., pending approvals).**

**Out of Scope:**

- Operator onboarding (handled by Crew Management)
- Skills/process definition management (handled by Skills Matrix)
- Form rendering and scoring logic (handled by Validation Submodule)
- ~~Workflow execution and approvals
- Versatility Matrix maintenance, calculations, and acting as the source of truth for workstation assignments.
- Automatic timelines, escalations, or retries
- Notification delivery (handled by central Notification Service)

### 1.2 Domain Context Diagram

```mermaid
graph TD
    subgraph Training Process Microservice
        direction LR
        TP_Core[Core Logic / Domain / Internal Workflows]
        TP_WriteDB[(Cosmos DB - Write Model)]
        TP_ReadDB[(Cosmos DB - Read Model)]
        TP_API[API Layer]
        TP_CFP[Change Feed Processors]
        TP_EVTPUB[Event Publisher]
        TP_EVTSUB[Event Subscriber]

        TP_API -- Commands/Approval Actions --> TP_Core
        TP_Core --> TP_WriteDB
        TP_Core -- Domain Events / Notification Requests --> TP_EVTPUB
        TP_CFP -- Commands --> TP_Core
        TP_EVTSUB -- Commands --> TP_Core
        TP_API -- Queries --> TP_ReadDB
    end

    CM_DB[(Crew Management DB)] -- Operator Data (Change Feed) --> TP_CFP
    SM_DB[(Skills Matrix DB)] -- Process Definitions (Change Feed) --> TP_CFP
    VM_DB[(Versatility Matrix DB)] -- Versatility Data (Change Feed) --> TP_CFP

    TP_EVTPUB -- Status Updates (Events) --> VersatilityMatrix[Versatility Matrix]
    TP_EVTPUB -- Notification Events --> NotificationSvc[Notification Service]

    VS[Validation Submodule REQ32/33] <-- API Call --> TP_API

    Client[Client Apps / Users] -- Manual Actions / Queries / Approvals --> TP_API

```

---

## 2. Architecture

### 2.1 Architectural Patterns

The Training Process microservice implements the following architectural patterns:

1. **Microservices Architecture**: Self-contained service focused on training process domain
2. **Event-Driven Architecture**: Async communication via Azure Service Bus
3. **CQRS Pattern**: Separation of command (write) and query (read) responsibilities
4. **Domain-Driven Design**: Focus on training domain concepts and bounded contexts
5. **Change Feed Pattern**: For integration with external data sources
6. **Event Sourcing** (partial): Training history as a sequence of events
7. **RESTful API**: For command and query operations

### 2.2 High-Level Component Diagram

```mermaid
graph TD
    Client[Client Applications] -- API Calls / Approval Actions --> APIGw[Azure API Management]
    APIGw --> APILyr[API Layer]

    subgraph Training Process Microservice
        APILyr -- Commands / Approval Actions --> CMD[Command Handlers]
        APILyr -- Queries --> QRY[Query Handlers]

        CMD --> Domain[Domain Model / Internal Workflows]
        Domain --> REPO[Write Repository]

        QRY --> ReadRepo[Read Model Repository]

        REPO --> CosmosWrite[(Cosmos DB Write Model)]
        ReadRepo --> CosmosRead[(Cosmos DB Read Model)]

        Domain -- Domain Events / Notification Requests --> EVTPUB[Event Publisher]
        EVTPUB --> ServiceBusPub[Azure Service Bus - Publish]

        CFP[Change Feed Processors] -- Update Commands --> CMD
    end

    CrewMgtDB[(Crew Management DB)] -- Operator Data (Change Feed) --> CFP
    SkillsMatrixDB[(Skills Matrix DB)] -- Process Definitions (Change Feed) --> CFP
    VersatilityMatrixDB[(Versatility Matrix DB)] -- Versatility Data (Change Feed) --> CFP

    ServiceBusPub -- Training Status Events --> VersatilityMatrix[Versatility Matrix]
    ServiceBusPub -- Notification Events --> NotificationSvc[Notification Service]

    ValidationAPI[Validation Service API] <--> APILyr
```

### 2.3 Technology Stack

- **Data Storage**: Azure Cosmos DB
- **Messaging**: Azure Service Bus
- **API Gateway**: Azure API Management
- **Compute**: Azure App Service / Azure Functions
- **Authentication**: Azure Active Directory
- **Monitoring**: Azure Application Insights
- **Secret Management**: Azure Key Vault
- **CQRS Implementation**: MediatR pattern

### 2.4 CQRS Implementation

The CQRS pattern separates the command and query responsibilities:

**Command Side (Write Model):**

- Handles all state-changing operations
- Rich domain model with business logic
- Optimized for consistency and correctness
- Publishes events upon successful state changes
- Uses a normalized data model in Cosmos DB

**Query Side (Read Model):**

- Handles all read operations
- Lightweight query handlers
- Optimized for query performance
- Reads from denormalized view models
- No business logic

**Initial Implementation:**

- Both models will share the same Cosmos DB container
- Separate read model projections to optimize for query scenarios
- Future option to physically separate read/write stores if needed

### 2.5 Microservices Breakdown

The Training Process domain is divided into multiple focused microservices following DDD principles and NestJS modular architecture. Each microservice has its own bounded context, database containers, and clearly defined integration points.

#### 2.5.1 Core Training Orchestration Service

**Primary Responsibility:** Manages the overall operator training lifecycle, orchestrates state transitions, **and handles internal multi-step approval workflows.**

**NestJS Module Structure:**

```
src/
├── modules/
│   ├── training/
│   │   ├── commands/                  # Command handlers (CQRS)
│   │   │   ├── complete-ojt.handler.ts
│   │   │   ├── complete-learning-curve.handler.ts
│   │   │   ├── initiate-certification.handler.ts // Starts internal workflow
│   │   │   ├── approve-workflow-step.handler.ts // Handles approval actions
│   │   │   └── ...
│   │   ├── queries/                   # Query handlers (CQRS)
│   │   │   ├── get-operator-training.handler.ts
│   │   │   ├── get-pending-approvals.handler.ts // Example query for workflows
│   │   │   └── ...
│   │   ├── entities/                  # Domain entities
│   │   │   ├── operator-training-record.entity.ts
│   │   │   ├── qualification.entity.ts
│   │   │   ├── training-history-entry.entity.ts
│   │   │   └── internal-workflow.entity.ts // New entity for workflow state
│   │   ├── repositories/              # Repository interfaces and implementations
│   │   │   ├── training-record.repository.ts
│   │   │   ├── workflow.repository.ts // Repository for workflow entity
│   │   │   └── cosmos-training-record.repository.ts
│   │   ├── dto/                       # Data Transfer Objects
│   │   │   ├── complete-ojt.dto.ts
│   │   │   ├── approve-workflow-step.dto.ts // DTO for approval action
│   │   │   └── ...
│   │   ├── events/                    # Domain events
│   │   │   ├── ojt-completed.event.ts
│   │   │   ├── workflow-step-completed.event.ts
│   │   │   ├── notification-requested.event.ts // Replaces specific workflow events
│   │   │   └── ...
│   │   ├── controllers/               # API controllers
│   │   │   ├── training.controller.ts
│   │   │   ├── workflow.controller.ts // Controller for workflow actions (approvals)
│   │   │   └── // Removed callback.controller.ts
│   │   └── training.module.ts         # Module definition
└── ...
```

**Cosmos DB Containers:**

- `training-records` (Main container)
  - Partition Key: `/partitionKey`
- `internal-workflows` (Optional: Can also be embedded in training-records)
  - Partition Key: `/partitionKey` (e.g., workflow instance ID or operator ID)

**Communication:**

- **Publishes Events:** Training status events, notification request events
- **Consumes Change Feed:** From Crew Management, Skills Matrix, Versatility Matrix
- **REST API:** Exposes training lifecycle management and **workflow action** endpoints

**Main Responsibilities:**

- Operator training progression (O → LC → V → C → R)
- Qualification addition/management
- Integration with validation results
- **Initiating and managing internal workflows for certification, recertification, removal, IPE.**
- **Processing approval steps from authorized users.**
- Publishing status updates to Versatility Matrix
- **Publishing notification requests.**
- Managing new qualification requests (F) and qualification removal processes
- Publishing qualification status changes to Versatility Matrix

#### 2.5.2 Training Data Synchronization Service

**Primary Responsibility:** Consumes change feeds from other modules and updates local records.

**NestJS Module Structure:**

```
src/
├── modules/
│   ├── sync/
│   │   ├── functions/                 # Azure Functions
│   │   │   ├── crew-sync.function.ts
│   │   │   ├── skills-sync.function.ts
│   │   │   └── versatility-sync.function.ts
│   │   ├── commands/                  # Commands to update local records
│   │   │   ├── sync-operator.command.ts
│   │   │   └── ...
│   │   ├── mappers/                   # Data mapping logic
│   │   │   ├── operator.mapper.ts
│   │   │   └── ...
│   │   ├── models/                    # External data models
│   │   │   ├── crew-management-operator.model.ts
│   │   │   └── ...
│   │   └── sync.module.ts
└── ...
```

**Cosmos DB Containers:**

- Uses `training-records` for writing
- Leases containers for change feed processing:
  - `crew-management-lease`
  - `skills-matrix-lease`
  - `versatility-matrix-lease`

**Communication:**

- **Consumes Change Feed:** Primary integration method
- **Publishes Commands:** Internal commands to Core Training service

**Main Responsibilities:**

- Monitors external module changes via Change Feed
- Transforms external data to internal domain models
- Maintains data consistency across bounded contexts
- Updates cached/duplicated data in training records
- Handles specific operator data from different sources (OPTITIME, Workday for North Africa)
- Processes operator assignment changes from Crew Management

#### 2.5.3 Internal Workflow Service

**Primary Responsibility:** Handles the management and execution of internal approval workflows.

**NestJS Module Structure:**

```
src/
├── modules/
│   ├── workflow/
│   │   ├── functions/                 # Azure Functions
│   │   │   ├── notification-processor.function.ts
│   │   │   └── ...
│   │   ├── commands/                  # Command handlers
│   │   │   ├── approve-workflow-step.handler.ts
│   │   │   ├── reject-workflow-step.handler.ts
│   │   │   └── ...
│   │   ├── dto/                       # Data transfer objects
│   │   │   ├── workflow-approval.dto.ts
│   │   │   └── ...
│   │   ├── services/                  # Services for workflow management
│   │   │   ├── workflow-state.service.ts
│   │   │   └── ...
│   │   └── workflow.module.ts
└── ...
```

**Cosmos DB Containers:**

- `internal-workflows` (Optional separate container for workflow state)
  - Partition Key: `/partitionKey` (workflowId)
- Alternatively, uses `training-records` for embedding workflow state

**Communication:**

- **Publishes Events:** Notification events to the Notification Service
- **REST API:** Exposes workflow action endpoints (approvals, rejections)

**Main Responsibilities:**

- Managing internal approval workflows for certification, recertification, qualification removal, etc.
- Processing approval/rejection decisions
- Updating workflow state and qualification status based on approvals
- Sending notification requests via the Notification Service
- Maintaining workflow history and audit trail

#### 2.5.4 Training Query Service

**Primary Responsibility:** Handles read operations and provides optimized query capabilities.

**NestJS Module Structure:**

```
src/
├── modules/
│   ├── query/
│   │   ├── queries/                   # Query handlers
│   │   │   ├── get-operator-summary.handler.ts
│   │   │   └── ...
│   │   ├── projections/               # Read model projections
│   │   │   ├── operator-summary.projection.ts
│   │   │   └── ...
│   │   ├── dto/                       # Response DTOs
│   │   │   ├── operator-summary.dto.ts
│   │   │   └── ...
│   │   ├── repositories/              # Read model repositories
│   │   │   ├── training-query.repository.ts
│   │   │   └── ...
│   │   ├── controllers/               # API controllers
│   │   │   ├── query.controller.ts
│   │   │   └── ...
│   │   └── query.module.ts
└── ...
```

**Cosmos DB Containers:**

- `training-read-models` (Read model container)
  - Contains denormalized projections for different query scenarios
  - Optimized for query performance

**Communication:**

- **REST API:** Exposes query endpoints
- **Consumes Events:** From Core Training service for read model updates
- **Read Models:** Maintains denormalized projections

**Main Responsibilities:**

- Provides efficient read operations
- Maintains denormalized read models
- Offers various query options (by operator, department, status)
- Optimizes data for UI consumption

---

## 3. Detailed Component Design

### 3.1 API Layer

The API layer provides RESTful endpoints for commands and queries:

- **Command Endpoints**: Accept requests that change system state
- **Query Endpoints**: Return data without side effects
- **Authentication**: Azure AD integration
- **Authorization**: Role-based authorization (Trainer, Team Leader, etc.)
- **Input Validation**: FluentValidation for DTO validation
- **Response Formatting**: Consistent REST conventions
- **Error Handling**: Problem Details standard (RFC 7807)

**Implementation Notes:**

- Commands/queries dispatched via MediatR
- Swagger/OpenAPI documentation
- Correlation IDs for request tracing

### 3.2 Command Processing

The command processing flow:

1. API layer receives command request
2. Command is validated
3. MediatR dispatches command to appropriate handler
4. Handler loads domain entity/aggregate from repository
5. Domain logic is executed
6. Changes are persisted to Cosmos DB
7. Domain events are published to Service Bus
8. Response is returned to client

**Key Command Handlers:**

- `CompleteOjtTrainingHandler`
- `CompleteLearningCurveHandler`
- `InitiateValidationHandler`
- `ProcessValidationResultHandler` (Handles result from Validation Submodule callback/event)
- `InitiateCertificationHandler` // Initiates the internal certification workflow
- `InitiateRecertificationHandler` // Initiates the internal recertification workflow
- `InitiateNewQualificationHandler`
- `CompleteNewQualificationTrainingHandler`
- `InitiateQualificationRemovalHandler` // Initiates the internal removal workflow
- `InitiateIpeHandler` // Initiates the internal IPE workflow
- `ApproveWorkflowStepHandler` // Processes approval/rejection actions from users
- `SyncOperatorDataHandler` (Processes data from Crew Management Change Feed to update local record)
- `SyncProcessDefinitionHandler` (Processes data from Skills Matrix Change Feed to update local process info)
- `SyncVersatilityDataHandler` (Processes data from Versatility Matrix Change Feed to update local read model/cache - _Specific logic TBD_)

### 3.3 Domain Model

The core domain model revolves around the `OperatorTrainingRecord` aggregate. This aggregate maintains the state related to an operator's training journey, **including the status of any ongoing internal approval workflows.** It includes locally cached (duplicated) operator details from Crew Management for operational efficiency.

```
// Pseudocode representation of OperatorTrainingRecord
class OperatorTrainingRecord {
    // Identity and basic data (Locally Cached from Crew Management)
    Id                 // Aggregate Root ID (e.g., Operator ID)
    OperatorId         // Referenced Operator ID
    OperatorName
    Department

    // Training State
    CurrentOverallPhase // Calculated or explicit overall phase

    // Collections
    Qualifications (List<Qualification>)
    TrainingHistory (List<TrainingHistoryEntry>)
    // InternalWorkflows (List<InternalWorkflow>) // Or embed workflow state in Qualification

    // Domain methods - Encapsulate business logic and raise events
    CompleteOjtTraining(processId, result, checklistUrl, userId) {
        // Rules: Check prerequisites, validate inputs
        // Find or create qualification entry
        // Update qualification status to "O"
        // Add history entry
        // Raise OjtCompletedEvent
        // Raise TrainingStatusChangedEvent
    }

    CompleteLearningCurve(processId, result, checklistUrl, userId) {
        // Rules: Must be in 'O' status for this process
        // Find qualification entry
        // Update qualification status to "LC"
        // Add history entry
        // Raise LcCompletedEvent
        // Raise TrainingStatusChangedEvent
    }

    InitiateValidation(processId, userId) {
        // Rules: Must be in 'LC' status
        // Find qualification entry
        // Get if process is key or basic (qualification.isKeyProcess)
        // If key process:
        //   Update qualification status to "V_KeyProcessPending"
        //   Create/update InternalWorkflow entity/state for Key Process Validation
        //   Define initial approval steps for key process validation
        // If basic process:
        //   Update qualification status to "V_Pending"
        //   Call ValidationService for form template if needed
        // Add history entry
        // Raise appropriate ValidationInitiatedEvent
        // Raise NotificationRequestedEvent for key process approvers if needed
    }

    RecordValidationResult(processId, score, isValidated, validationUserId) {
        // Rules: Must be in 'V_Pending' or 'V_KeyProcessPending' status
        // Find qualification entry
        // Check if key or basic process
        // Process result accordingly based on process type
        // If isValidated: Update status to "V"
        // Else: Revert status or update to appropriate failed status
        // Add history entry
        // Raise ValidationCompletedEvent
        // If isValidated: Raise TrainingStatusChangedEvent
    }

    SubmitKeyProcessEvaluation(processId, score, checklistUrl, comments, userId) {
        // Rules: Must be in key process validation status
        // Find qualification entry and verify it's a key process
        // Record score, checklist, and comments
        // Update qualification status based on score and criteria
        // Add history entry
        // Raise KeyProcessEvaluationCompletedEvent
        // If passed: Raise TrainingStatusChangedEvent
    }

    InitiateCertification(processId, userId) {
        // Rules: Must be in 'V' status
        // Find qualification entry
        // Check if key or basic process (qualification.isKeyProcess)
        // Update qualification status to appropriate pending status
        //   (e.g., "C_PendingApproval" or "C_KeyProcessPendingApproval")
        // Create/Update InternalWorkflow entity/state with appropriate steps
        //   based on process type (key or basic)
        // Add history entry
        // Raise appropriate certification event
        // Raise NotificationRequestedEvent for appropriate approvers
    }

    InitiateRecertification(processId, userId) {
        // Rules: Must be 'C' and recertification due
        // Find qualification entry
        // Check if key or basic process (qualification.isKeyProcess)
        // Update qualification status to appropriate pending status
        //   (e.g., "R_PendingApproval" or "R_KeyProcessPendingApproval")
        // Create/Update InternalWorkflow entity/state with appropriate steps
        //   based on process type (key or basic)
        // Add history entry
        // Raise RecertificationInitiatedEvent
        // Raise NotificationRequestedEvent for first approver
    }

    InitiateQualificationRemoval(processId, reason, removalType, evidence, userId) {
        // Find qualification entry
        // Update qualification status to "Removal_PendingApproval"
        // Create/Update InternalWorkflow entity/state for Removal
        // Define complex parallel approval steps (Production, Quality, HR)
        // Add history entry
        // Raise QualificationRemovalInitiatedEvent // Internal event if needed
        // Raise NotificationRequestedEvent (for initial approvers)
    }

    ApproveWorkflowStep(workflowId, processId, approverUserId, role, decision, comments) {
        // Find the relevant InternalWorkflow or Qualification
        // Verify approverUserId and role match the current step
        // Record the decision (Approved/Rejected)
        // If Approved:
        //   Check if more steps exist
        //   If yes: Update workflow to next step, Raise NotificationRequestedEvent (for next approver)
        //   If no (final approval): Update Qualification status (e.g., 'C', 'R', 'Removed'), Raise TrainingStatusChangedEvent / QualificationRemovedEvent
        // If Rejected:
        //   Update Qualification status (e.g., back to 'V', 'C', or 'Removal_Rejected')
        //   Terminate workflow
        //   Raise NotificationRequestedEvent (to initiator/relevant parties)
        // Add history entry
        // Raise WorkflowStepCompleted event
    }

    // Similar methods for Recertification (R), New Qualification (F), Removal, IPE
    // ...

    // Method to update cached operator data
    UpdateOperatorDetails(name, department) {
        // Update local fields if changed
    }
}

// Value Object/Entity
class Qualification {
    ProcessId
    ProcessName // (Locally cached from Skills Matrix)
    IsKeyProcess // Boolean flag to differentiate key vs basic processes
    Status      // O, LC, V_Pending, V_KeyProcessPending, V, C_PendingApproval, C_KeyProcessPendingApproval, C, R_PendingApproval, R_KeyProcessPendingApproval, R, F, Removal_PendingApproval, Removed, *_Rejected
    LastCertificationDate
    LastRecertificationDate
    IsActive
    // Embedded Workflow State (Alternative to separate entity)
    CurrentWorkflowId?: string
    CurrentWorkflowStatus?: string // Pending, Approved, Rejected
    CurrentApproverRole?: string
    ApprovalHistory?: ApprovalStep[]
}

// Example separate Workflow Entity (Alternative)
class InternalWorkflow {
    WorkflowId
    Type // Certification, KeyProcessCertification, Recertification, KeyProcessRecertification, Removal, IPE
    OperatorId
    ProcessId
    IsKeyProcess // Boolean flag copied from qualification
    Status // Pending, InProgress, Approved, Rejected, Cancelled
    CurrentStepIndex // For sequential workflows
    ApprovalSteps: ApprovalStep[]
    InitiatedByUserId
    InitiationTimestamp
    CompletionTimestamp?
    KeyProcessData?: { // Additional data for key processes
        Score?: number,
        ChecklistUrl?: string,
        Comments?: string
    }
}

class ApprovalStep {
    StepIndex // Or identifier for parallel steps
    Role // QualityAuditor, Trainer, ShiftLeader, ProdCoordinator, QualSupervisor, QualCoordinator, HRManager
    Status // Pending, Approved, Rejected
    DecisionByUserId?
    DecisionTimestamp?
    Comments?
}

// Value Object
class TrainingHistoryEntry {
    EventId
    EventType
    Phase       // O, LC, V, V_KeyProcess, C, C_KeyProcess, R, R_KeyProcess, F, etc.
    ProcessId
    Timestamp
    Result      // Score, OK/NOK, Status
    Details     // (e.g., Checklist URL, Comments)
    UserId      // User initiating/completing the action
    WorkflowId?: string
    ApprovalRole?: string // Role performing the action
}
```

### 3.4 Repository Pattern

The repository abstracts data access for the domain model:

```
// Pseudocode interface
interface OperatorTrainingRepository {
    GetById(operatorId)
    Save(record)
    Exists(operatorId)
}

// Implementation for Cosmos DB
class CosmosDbOperatorTrainingRepository implements OperatorTrainingRepository {
    // Implementation methods
}
```

### 3.5 Read Model

The read model is optimized for query scenarios:

```
// Pseudocode interface
interface OperatorTrainingQueries {
    GetSummary(operatorId)
    GetDetails(operatorId)
    GetByDepartment(department)
    GetByStatus(status)
    GetQualifications(operatorId)
    GetTrainingHistory(operatorId)
}

// Implementation for Cosmos DB
class CosmosDbOperatorTrainingQueries implements OperatorTrainingQueries {
    // Implementation methods with optimized queries
}
```

### 3.6 Change Feed Processors

Change Feed processors (likely implemented as Azure Functions) listen to Cosmos DB Change Feeds from external containers and dispatch commands to update the local state within the Training Process microservice.

```
// Pseudocode for Change Feed processor
class CrewManagementChangeFeedProcessor {
    // Triggered by changes in Crew Management Cosmos DB container
    ProcessChanges(documents) {
        foreach (document in documents) {
            // Extract relevant operator data (ID, Name, Department)
            // Create SyncOperatorDataCommand with complete operator information:
            // - ID, Name, Surname, Hiring Date, Department, Category, Function
            // - Also capture trainer assignment if present

            // Check document type and handle accordingly
            if (document.type === 'OperatorCreated' || document.type === 'OperatorUpdated') {
                // Create or update local operator record
                dispatcher.dispatch(new SyncOperatorDataCommand(operatorData));
            }
            else if (document.type === 'OperatorAssignmentUpdated') {
                // Update trainer assignment and organizational hierarchy
                dispatcher.dispatch(new SyncOperatorAssignmentCommand(assignmentData));
            }
        }
    }
}

// Pseudocode for Change Feed processor
class SkillsMatrixChangeFeedProcessor {
    // Triggered by changes in Skills Matrix Cosmos DB container
    ProcessChanges(documents) {
        foreach (document in documents) {
            // Extract process data including:
            // - Process ID, Name, Department
            // - IsKeyProcess flag to differentiate key vs basic processes
            // - For Assembling: Polyvalence levels (X, 1-4)
            // - For Cutting & Leadprep: Machine zoning information

            dispatcher.dispatch(new SyncProcessDefinitionCommand(processData));
        }
    }
}

// Pseudocode for Change Feed processor
class VersatilityMatrixChangeFeedProcessor {
    // Triggered by changes in Versatility Matrix Cosmos DB container
    // PURPOSE: To maintain read-only view of training statuses for queries
    ProcessChanges(documents) {
        foreach (document in documents) {
            // Extract Versatility data:
            // - Operator status at each workstation
            // - Current qualifications with statuses (O, LC, V, C, R)
            // - Timestamp of last status change

            dispatcher.dispatch(new SyncVersatilityDataCommand(versatilityData));
        }
    }
}
```

### 3.7 Event Publishing

Event publishing uses Azure Service Bus to communicate status changes to other modules **and trigger notifications via the Notification Service.**

```
// Pseudocode interface
interface EventPublisher {
    Publish(event)
}

// Implementation for Azure Service Bus
class ServiceBusEventPublisher implements EventPublisher {
    Publish(event) {
        // Handle different event types with appropriate topics
        switch (event.type) {
            case 'TrainingStatusChanged':
            case 'QualificationRemovedEvent': // Ensure this informs Versatility Matrix
                // Publish to Versatility Matrix topic
                this.publishToTopic('versatility-matrix-updates', event);
                break;


            case 'NotificationRequested':
                // Publish generic notification request to Notification Service topic
                // Payload includes recipient(s), context, message template key, etc.
                this.publishToTopic('notification-events', event);
                break;

            default:
                // Internal events for read model updates or other purposes
                this.publishToTopic('training-internal-events', event);
        }
    }

    private publishToTopic(topicName, event) {
        // Serialize event
        // Create message with metadata
        // Send to Service Bus topic
    }
}
```

### 3.8 Integration with Validation Submodule (REQ 32/33)

The Validation Submodule integration uses HTTP clients to request form templates and submit results for basic processes:

```
// Pseudocode interface for Validation Service
interface ValidationService {
    // Get validation form template based on department-specific logic
    getValidationTemplate(department, processId): Promise<{
        templateId: string,
        templateContent: object,
        scoringRules: object,
        validationThreshold: number, // 80% for validation
        grayAreaChecks: object[] // Rules to verify no gray areas exist
    }>;

    // Get certification form template based on department-specific logic
    getCertificationTemplate(department, processId): Promise<{
        templateId: string,
        templateContent: object,
        scoringRules: object,
        certificationThreshold: number // 85% for certification
    }>;

    // Submit validation results for scoring
    submitValidationResults(submission): Promise<{
        score: number,
        passed: boolean,
        grayAreasDetected: boolean,
        details: object
    }>;

    // Get department-specific configuration
    getDepartmentConfig(department): Promise<{
        requiresPolyvalence: boolean, // true for Assembling
        polyvalenceLevels?: string[], // ['X', '1', '2', '3', '4'] for Assembling
        requiresMachineZoning: boolean, // true for Cutting & Leadprep
        machineZones?: string[] // Zone identifiers for Cutting & Leadprep
    }>;
}

// Implementation for HTTP client
class ValidationServiceClient implements ValidationService {
    // Implementation methods calling REQ 32/33 API endpoints
    async getValidationTemplate(department, processId) {
        // HTTP call to REQ 32/33 endpoint for validation template
        const response = await this.httpClient.get(
            `/api/validation/templates/validation/${department}/${processId}`
        );
        return response.data;
    }

    async getCertificationTemplate(department, processId) {
        // HTTP call to REQ 32/33 endpoint for certification template
        const response = await this.httpClient.get(
            `/api/validation/templates/certification/${department}/${processId}`
        );
        return response.data;
    }

    async submitValidationResults(submission) {
        // HTTP call to REQ 32/33 endpoint for scoring
        const response = await this.httpClient.post(
            '/api/validation/score',
            submission
        );
        return response.data;
    }

    async getDepartmentConfig(department) {
        // HTTP call to REQ 32/33 endpoint for department config
        const response = await this.httpClient.get(
            `/api/validation/department-config/${department}`
        );
        return response.data;
    }
}
```

### 3.8.1 Key Process Evaluation Service

For key processes, a separate service handles the scoring, checklist storage, and validation:

```
// Pseudocode interface for Key Process Evaluation
interface KeyProcessEvaluationService {
    // Submit key process evaluation results
    submitKeyProcessEvaluation(evaluation: {
        operatorId: string,
        processId: string,
        score: number,
        checklistUrl: string,
        comments?: string,
        evaluatedByUserId: string
    }): Promise<{
        passed: boolean,
        details: object
    }>;

    // Store checklist file
    storeChecklist(file: File, operatorId: string, processId: string): Promise<string>;

    // Get stored checklist
    getChecklist(checklistUrl: string): Promise<File>;

    // Get key process evaluation form template
    getKeyProcessEvaluationForm(processId: string): Promise<{
        formTemplate: object,
        scoringCriteria: object
    }>;
}

// Implementation for Key Process service
class KeyProcessEvaluationServiceImpl implements KeyProcessEvaluationService {
    constructor(
        private readonly blobStorage: BlobStorageService,
        private readonly configService: ConfigurationService
    ) {}

    async submitKeyProcessEvaluation(evaluation) {
        // Apply scoring logic for key process
        const passingThreshold = this.configService.getKeyProcessThreshold(evaluation.processId);
        const passed = evaluation.score >= passingThreshold;

        // Record evaluation details
        // Return result
        return {
            passed,
            details: {
                score: evaluation.score,
                threshold: passingThreshold,
                checklistUrl: evaluation.checklistUrl,
                evaluationDate: new Date(),
                evaluatedBy: evaluation.evaluatedByUserId
            }
        };
    }

    async storeChecklist(file, operatorId, processId) {
        // Generate unique filename
        const filename = `${operatorId}/${processId}/${Date.now()}-${file.name}`;

        // Upload to blob storage
        await this.blobStorage.uploadFile('checklists', filename, file);

        // Return URL
        return this.blobStorage.getUrl('checklists', filename);
    }

    async getChecklist(checklistUrl) {
        // Extract container and filename from URL
        const { container, filename } = this.parseChecklistUrl(checklistUrl);

        // Download from blob storage
        return this.blobStorage.downloadFile(container, filename);
    }

    async getKeyProcessEvaluationForm(processId) {
        // Return form template for key process
        // This could be stored in a database or config
        return {
            formTemplate: {
                // Form fields for key process evaluation
                // Score input, checklist upload, comments, etc.
            },
            scoringCriteria: {
                // Criteria specific to this key process
                passingThreshold: this.configService.getKeyProcessThreshold(processId)
            }
        };
    }

    private parseChecklistUrl(url) {
        // Parse URL to extract container and filename
        // Implementation details
    }
}
```

### 3.9 Internal Workflow Management

The Training Process microservice implements a robust internal workflow engine to manage complex approval flows required for certification, recertification, qualification removal, and IPE processes. This approach eliminates external dependencies and provides full control over workflow progression.

#### 3.9.1 Certification/Recertification Workflow

1.  **Initiation**: Team Leader initiates certification/recertification via API (`/api/training/{operatorId}/certification` or `/recertification`).
2.  **Internal Workflow Start**: The command handler updates the `Qualification` status (e.g., to `C_PendingApproval` or `R_PendingApproval`) and creates/updates an internal workflow state (either embedded or in a separate `InternalWorkflow` entity).
3.  **First Approval Step**: The workflow identifies the first approver (e.g., Quality Auditor). A `NotificationRequested` event is published for this user.
4.  **Approver Action**: The Quality Auditor uses an API endpoint (e.g., `POST /api/training/workflows/{workflowId}/approve`) to submit their decision.
5.  **Subsequent Steps**: The `ApproveWorkflowStepHandler` processes the decision.
    - If approved and more steps exist (e.g., Trainer approval), the workflow state is updated to the next step, and a `NotificationRequested` event is published for the next approver.
    - If approved and it's the final step, the `Qualification` status is updated to `C` or `R`, the workflow is marked completed, and a `TrainingStatusChanged` event is published.
    - If rejected at any step, the workflow is terminated, the `Qualification` status is updated (e.g., `C_Rejected` or back to `V`), and relevant notifications are sent.
6.  **History**: Each step and decision is recorded in the `TrainingHistory` or `InternalWorkflow` entity.

```
// Pseudocode internal event for notification
class NotificationRequestedEvent {
    eventId: string;
    recipientUserId: string; // Or Role/Group ID
    notificationType: "ApprovalPending" | "WorkflowCompleted" | "WorkflowRejected";
    context: { // Information for the notification message
        operatorId: string;
        operatorName: string;
        processId: string;
        processName: string;
        workflowType: string; // Certification, Removal, etc.
        workflowId: string;
        currentStep?: string; // e.g., "Quality Auditor Approval"
        initiatorName?: string;
        rejectionReason?: string;
    };
    correlationId: string;
    timestamp: Date;
}

// Pseudocode event structure for workflow completion
class CertificationCompletedEvent {
    eventId: string;
    workflowType: "Certification";
    operatorId: string;
    processId: string;
    outcome: "Approved" | "Rejected";
    approvalChain: [
        { role: "QualityAuditor", userId: string, decision: "Approved" | "Rejected", timestamp: Date },
        { role: "Trainer", userId: string, decision: "Approved" | "Rejected", timestamp: Date }
    ];
    completionDate: Date;
    qualityScore: number;
    rejectionReason?: string;
    correlationId: string;
    timestamp: Date;
}
```

#### 3.9.2 Qualification Removal Workflow

The qualification removal workflow is complex and involves multiple parallel approval paths:

1.  **Initiation**: Team Leader submits removal request via API (`/api/training/qualification-removal`).
2.  **Internal Workflow Start**: Command handler updates `Qualification` status to `Removal_PendingApproval` and creates an `InternalWorkflow` instance for removal, defining the complex parallel/sequential approval paths (Production, Quality, HR).
3.  **Parallel Approval Steps**: Notifications are sent to the first approvers in _each_ required path (e.g., Shift Leader and Quality Supervisor).
4.  **Approver Actions**: Approvers submit decisions via the API.
5.  **Workflow Progression**: The `ApproveWorkflowStepHandler` tracks the state of each path.
    - A path completes when its final approver (e.g., Production Coordinator, Quality Coordinator) approves.
    - The overall workflow requires all mandatory paths to be approved before proceeding to the final HR approval step.
    - If any mandatory approver rejects, the entire workflow is terminated, the status updated (e.g., `Removal_Rejected`), and notifications sent.
6.  **Final Approval (HR)**: Once Production and Quality paths are approved, a notification is sent to the HR Manager.
7.  **HR Decision**: HR Manager submits decision via API.
    - If approved, the `Qualification` status becomes `Removed`, the workflow completes, and a `QualificationRemovedEvent` (which triggers a `TrainingStatusChanged` message for Versatility) is published.
    - If rejected, workflow terminates, status becomes `Removal_Rejected`, and notifications sent.
8.  **History**: All decisions are logged.

```
// Pseudocode event structure for qualification removal completion
class QualificationRemovedEvent {
    eventId: string;
    operatorId: string;
    processId: string;
    status: "Removed";
    removalReason: string;
    removalType: "FTQ_ISSUE" | "CUSTOMER_COMPLAINT" | "OTHER";
    removalDate: Date;
    approvalChain: {
        production: [
            { role: "ShiftLeader", userId: string, decision: "Approved", timestamp: Date },
            { role: "ProductionCoordinator", userId: string, decision: "Approved", timestamp: Date }
        ],
        quality: [
            { role: "QualitySupervisor", userId: string, decision: "Approved", timestamp: Date },
            { role: "QualityCoordinator", userId: string, decision: "Approved", timestamp: Date }
        ],
        hr: { role: "HRManager", userId: string, decision: "Approved", timestamp: Date }
    };
    correlationId: string;
    timestamp: Date;
}
```

#### 3.9.3 New Qualification Workflow

The new qualification workflow is simpler, primarily requiring Trainer approval:

1.  **Initiation**: Team Leader requests via API (`/api/training/new-qualification/request`).
2.  **Notification**: `NotificationRequestedEvent` published for the designated Trainer.
3.  **Trainer Review**: Trainer uses an API endpoint (e.g., `/api/training/qualification-request/approval`) to approve/reject.
4.  **Handler Logic**: `ApproveQualificationRequestCommand` handler processes the decision.
    - If approved, `NewQualificationApprovedEvent` is published (internal signal), and training can proceed.
    - If rejected, appropriate notification sent.
5.  **Training & Familiarization**: Proceeds as before (Trainer inputs status 'F' via API).
6.  **Certification**: When ready, the Team Leader initiates the internal certification workflow described in 3.9.1.

```
// Pseudocode event structure for new qualification request notification
class NewQualificationRequestedEvent {
    eventId: string;
    operatorId: string;
    operatorName: string;
    processId: string;
    processName: string;
    department: string;
    teamLeaderId: string; // Who requested
    trainerId: string;    // Who needs to approve
    requestDate: Date;
    reason: string;
    correlationId: string;
    timestamp: Date;
}

// Pseudocode event structure for familiarization completion
class FamiliarizationCompletedEvent {
    eventId: string;
    operatorId: string;
    operatorName: string;
    processId: string;
    processName: string;
    department: string;
    status: "F";
    completedByUserId: string; // Trainer ID
    completionDate: Date;
    correlationId: string;
    timestamp: Date;
}
```

#### 3.9.4 Individual Performance Evaluation (IPE) Workflow

This workflow is customized for performance evaluation scenarios and follows a defined approval sequence:

1.  **Initiation**: Initiated via API (`/api/training/{operatorId}/ipe`).
2.  **Internal Workflow**: An internal workflow is created with a specific sequence of steps:
    - Team Leader completes initial evaluation form
    - Trainer reviews and provides input
    - Department Manager gives final approval
3.  **Notification Flow**: Each step triggers notifications to the appropriate role
4.  **Approval Actions**: Each approver submits their evaluation via dedicated API endpoints
5.  **Completion**: Upon final approval, evaluation results are recorded and IPE status is updated
6.  **History**: Complete history of the IPE process is maintained for audit and reference purposes

---

## 4. Data Models

### 4.1 Cosmos DB Data Models

#### 4.1.1 Write Model (`OperatorTrainingRecord`)

This model represents the aggregate root persisted in the write store. It contains all data necessary for enforcing business rules and tracking training history. Includes duplicated data from other modules for efficiency.

**NestJS Entity Definition:**

```typescript
// operator-training-record.entity.ts
export class OperatorTrainingRecord {
  id: string; // Operator ID as document ID & partition key
  type: string = "OperatorTrainingRecord"; // Document type discriminator
  partitionKey: string; // Operator ID as partition key
  operatorId: string;
  // Duplicated from Crew Management (updated via Change Feed)
  operatorName: string;
  department: string;
  // ---
  currentOverallPhase: TrainingPhase; // Represents highest achieved status
  qualifications: Qualification[];
  trainingHistory: TrainingHistoryEntry[];

  // Domain methods (implemented in TypeScript)
  completeOjtTraining(params: CompleteOjtParams): void {
    /* ... */
  }
  completeLearningCurve(params: CompleteLcParams): void {
    /* ... */
  }
  initiateValidation(params: InitiateValidationParams): void {
    /* ... */
  }
  // ... additional methods
}

// qualification.entity.ts
export class Qualification {
  processId: string;
  processName: string; // Duplicated from Skills Matrix
  isKeyProcess: boolean; // Flag to identify key processes that need special handling
  status: QualificationStatus; // O, LC, V_Pending, V_KeyProcessPending, V, C_Pending, C_KeyProcessPending, etc.
  lastCertificationDate?: Date;
  lastRecertificationDate?: Date;
  isActive: boolean;
}

// training-history-entry.entity.ts
export class TrainingHistoryEntry {
  eventId: string;
  eventType: TrainingEventType;
  phase: TrainingPhase;
  processId: string;
  timestamp: Date;
  result?: string; // Score, OK/NOK, Status
  details?: Record<string, any>; // Checklist URL, Comments, etc.
  userId: string; // User initiating/completing action
}

// training.types.ts
export enum TrainingPhase {
  OJT = "O",
  LEARNING_CURVE = "LC",
  VALIDATION_PENDING = "V_Pending",
  VALIDATION_KEY_PROCESS_PENDING = "V_KeyProcessPending",
  VALIDATION = "V",
  CERTIFICATION_PENDING = "C_Pending",
  CERTIFICATION_KEY_PROCESS_PENDING = "C_KeyProcessPending",
  CERTIFICATION = "C",
  RECERTIFICATION_PENDING = "R_Pending",
  RECERTIFICATION_KEY_PROCESS_PENDING = "R_KeyProcessPending",
  RECERTIFICATION = "R",
  FAMILIARIZATION = "F",
}

export enum QualificationStatus {
  OJT = "O",
  LEARNING_CURVE = "LC",
  VALIDATION_PENDING = "V_Pending",
  VALIDATION_KEY_PROCESS_PENDING = "V_KeyProcessPending",
  VALIDATION = "V",
  CERTIFICATION_PENDING = "C_Pending",
  CERTIFICATION_KEY_PROCESS_PENDING = "C_KeyProcessPending",
  CERTIFICATION = "C",
  RECERTIFICATION_PENDING = "R_Pending",
  RECERTIFICATION_KEY_PROCESS_PENDING = "R_KeyProcessPending",
  RECERTIFICATION = "R",
  FAMILIARIZATION = "F",
  REMOVED = "Removed",
}

export enum TrainingEventType {
  OJT_COMPLETED = "OjtCompleted",
  LC_COMPLETED = "LcCompleted",
  VALIDATION_INITIATED = "ValidationInitiated",
  KEY_PROCESS_VALIDATION_INITIATED = "KeyProcessValidationInitiated",
  VALIDATION_COMPLETED = "ValidationCompleted",
  KEY_PROCESS_EVALUATION_COMPLETED = "KeyProcessEvaluationCompleted",
  CERTIFICATION_INITIATED = "CertificationInitiated",
  KEY_PROCESS_CERTIFICATION_INITIATED = "KeyProcessCertificationInitiated",
  CERTIFICATION_COMPLETED = "CertificationCompleted",
  KEY_PROCESS_CERTIFICATION_COMPLETED = "KeyProcessCertificationCompleted",
  // ... other event types
}
```

**Cosmos DB Document Example:**

```json
{
  "id": "op-123",
  "type": "OperatorTrainingRecord",
  "partitionKey": "op-123",
  "operatorId": "op-123",
  "operatorName": "John Doe",
  "surname": "Doe",
  "hiringDate": "2023-05-15T00:00:00Z",
  "department": "Assembling",
  "category": "Operator",
  "function": "Assembler",
  "trainerId": "trainer-456",
  "teamLeaderId": "tl-789",
  "shiftLeaderId": "sl-123",
  "teamId": "team-45",
  "currentOverallPhase": "C",
  "qualifications": [
    {
      "processId": "PROC-ASY-001",
      "processName": "Wire Harness Assembly",
      "isKeyProcess": true,
      "status": "C",
      "lastCertificationDate": "2024-03-15T00:00:00Z",
      "lastRecertificationDate": "2024-03-15T00:00:00Z",
      "isActive": true,
      "department": "Assembling",
      "polyvalenceLevel": "3",
      "removalInfo": null
    },
    {
      "processId": "PROC-ASY-002",
      "processName": "Terminal Crimping",
      "isKeyProcess": false,
      "status": "F",
      "lastCertificationDate": null,
      "lastRecertificationDate": null,
      "isActive": true,
      "department": "Assembling",
      "polyvalenceLevel": "X",
      "familiarizationDate": "2024-04-10T00:00:00Z",
      "familiarizedByUserId": "trainer-456"
    },
    {
      "processId": "PROC-ASY-003",
      "processName": "Connector Assembly",
      "isKeyProcess": false,
      "status": "Removed",
      "lastCertificationDate": "2023-10-22T00:00:00Z",
      "lastRecertificationDate": null,
      "isActive": false,
      "department": "Assembling",
      "polyvalenceLevel": "2",
      "removalInfo": {
        "removalDate": "2024-02-20T00:00:00Z",
        "removalReason": "Recurring quality issues with connector assembly",
        "removalType": "FTQ_ISSUE",
        "removedByWorkflow": "wf-rem-789",
        "approvalChain": [
          { "role": "TeamLeader", "userId": "tl-123", "decision": "Approved", "timestamp": "2024-02-15T09:00:00Z" },
          { "role": "ShiftLeader", "userId": "sl-456", "decision": "Approved", "timestamp": "2024-02-16T14:30:00Z" },
          { "role": "ProductionCoordinator", "userId": "pc-789", "decision": "Approved", "timestamp": "2024-02-17T11:15:00Z" },
          { "role": "QualitySupervisor", "userId": "qs-123", "decision": "Approved", "timestamp": "2024-02-16T10:45:00Z" },
          { "role": "QualityCoordinator", "userId": "qc-456", "decision": "Approved", "timestamp": "2024-02-17T16:20:00Z" },
          { "role": "HRManager", "userId": "hr-789", "decision": "Approved", "timestamp": "2024-02-19T09:30:00Z" }
        ]
      }
    }
  ],
  "trainingHistory": [
    {
      "eventId": "evt-001",
      "eventType": "OjtCompleted",
      "phase": "O",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-01-15T10:30:00Z",
      "result": "85%",
      "details": {
        "checklistUrl": "/attachments/checklist-123.pdf",
        "comments": "Performed well on basic tasks",
        "uploadedBy": "trainer-456"
      },
      "userId": "trainer-456"
    },
    {
      "eventId": "evt-020",
      "eventType": "KeyProcessEvaluationCompleted",
      "phase": "V_KeyProcessPending",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-02-10T14:30:00Z",
      "result": "92%",
      "details": {
        "checklistUrl": "/attachments/key-process-checklist-456.pdf",
        "comments": "Excellent performance on key process evaluation",
        "uploadedBy": "trainer-456"
      },
      "userId": "trainer-456"
    },
    {
      "eventId": "evt-007",
      "eventType": "NewQualificationRequested",
      "phase": "F_Requested",
      "processId": "PROC-ASY-002",
      "timestamp": "2024-04-01T09:00:00Z",
      "result": null,
      "details": {
        "requestReason": "Need for additional versatility in terminal crimping process"
      },
      "userId": "tl-789"
    }
  ],
  "_etag": ""...",
  "_ts": 1618847477
}
```

#### 4.1.2 Read Model Projections

**NestJS Projection Definition:**

```typescript
// operator-summary.projection.ts
export class OperatorTrainingSummary {
  id: string;
  type: string = "OperatorTrainingSummary";
  partitionKey: string;
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingPhase: string;
  qualificationCount: number;
  activeQualifications: QualificationSummary[];
  lastUpdated: Date;
}

// qualification-summary.projection.ts
export class QualificationSummary {
  processId: string;
  processName: string;
  status: string;
}
```

**Cosmos DB Document Examples:**

**Operator Training Summary Projection:**

```json
{
  "id": "op-123",
  "type": "OperatorTrainingSummary",
  "partitionKey": "op-123",
  "operatorId": "op-123",
  "operatorName": "John Doe",
  "department": "Assembling",
  "currentTrainingPhase": "LC",
  "qualificationCount": 2,
  "activeQualifications": [
    {
      "processId": "PROC-ASY-001",
      "processName": "Wire Harness Assembly",
      "status": "C"
    },
    {
      "processId": "PROC-ASY-005",
      "processName": "Advanced Soldering",
      "status": "LC"
    }
  ],
  "lastUpdated": "2024-02-10T14:15:00Z"
}
```

**Department Summary Projection:**

```json
{
  "id": "dept-Assembling",
  "type": "DepartmentSummary",
  "partitionKey": "Assembling",
  "department": "Assembling",
  "operatorCount": 50,
  "operatorsByPhase": {
    "O": 10,
    "LC": 15,
    "V": 5,
    "C": 20,
    "R": 0,
    "F": 0
  },
  "lastUpdated": "2024-02-10T14:15:00Z"
}
```

### 4.2 Command DTOs

**NestJS DTO Definitions:**

```typescript
// complete-ojt.dto.ts
export class CompleteOjtRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsString()
  result: string;

  @IsOptional()
  @IsString()
  checklistUrl?: string;

  @IsOptional()
  @IsString()
  comments?: string;
}

// complete-learning-curve.dto.ts
export class CompleteLearningCurveRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsString()
  @IsIn(["OK", "NOK"])
  result: string;

  @IsOptional()
  @IsString()
  checklistUrl?: string;
}

// initiate-validation.dto.ts
export class InitiateValidationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
}

// initiate-certification.dto.ts
export class InitiateCertificationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
  // No callbackUrl needed
}

// initiate-recertification.dto.ts (Assuming similar structure)
export class InitiateRecertificationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
}

// remove-qualification.dto.ts
export class RemoveQualificationRequestDto {
  @IsString()
  @IsNotEmpty()
  operatorId: string;

  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsString()
  @IsNotEmpty()
  reason: string;

  @IsEnum(["FTQ_ISSUE", "CUSTOMER_COMPLAINT", "OTHER"])
  removalType: string;

  @IsObject()
  @IsOptional()
  evidenceDetails?: Record<string, any>;
  // No callbackUrl or explicit approval paths needed in request DTO
  // Workflow configuration is internal logic
}

// initiate-ipe.dto.ts (Assuming structure)
export class InitiateIpeRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string; // Or other relevant IPE context
}

// approve-workflow-step.dto.ts - NEW DTO
export class ApproveWorkflowStepDto {
  @IsString()
  @IsNotEmpty()
  workflowId: string; // Identifier for the specific workflow instance

  @IsString()
  @IsIn(["Approved", "Rejected"])
  decision: string;

  @IsString()
  @IsOptional()
  comments?: string;
}

// --- DTOs for receiving results from external systems are removed ---
// Removed: ValidationResultCallbackDto (Still needed for Validation Submodule)
// Removed: WorkflowResultCallbackDto

// --- Existing DTOs remain relevant ---
// assign-trainer.dto.ts
// approve-qualification-request.dto.ts // Used by Trainer for New Qual request
// new-qualification-request.dto.ts
// familiarization-completion.dto.ts

// New DTO for key process evaluation
export class SubmitKeyProcessEvaluationDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsNumber()
  @Min(0)
  @Max(100)
  score: number;

  @IsString()
  @IsUrl()
  checklistUrl: string;

  @IsOptional()
  @IsString()
  comments?: string;
}

// Modified initiate-validation.dto.ts
export class InitiateValidationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  // No additional fields needed - isKeyProcess is determined internally
  // based on the process definition
}

// Modified initiate-certification.dto.ts
export class InitiateCertificationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
  // No additional fields needed - isKeyProcess is determined internally
}

// ... Other existing DTOs
```

### 4.3 Query DTOs

**NestJS DTO Definitions:**

```typescript
// operator-training-summary.dto.ts
export class OperatorTrainingSummaryDto {
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingPhase: string;
  qualifications: QualificationSummaryDto[];
}

// qualification-summary.dto.ts
export class QualificationSummaryDto {
  processId: string;
  processName: string;
  status: string;
}

// operator-training-details.dto.ts
export class OperatorTrainingDetailsDto {
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingPhase: string;
  qualifications: QualificationDto[];
  trainingHistory: TrainingHistoryEntryDto[];
}

// training-history-entry.dto.ts
export class TrainingHistoryEntryDto {
  eventType: string;
  phase: string;
  processId: string;
  processName: string;
  timestamp: Date;
  result: string;
  userId: string;
  details: Record<string, any>;
}

// recertification-due.dto.ts
export class RecertificationDueDto {
  operatorId: string;
  operatorName: string;
  processId: string;
  processName: string;
  lastCertificationDate: Date;
  dueDate: Date; // Calculated based on certification date
}

// qualification-removal-history.dto.ts
export class QualificationRemovalHistoryDto {
  operatorId: string;
  operatorName: string;
  processId: string;
  processName: string;
  removalDate: Date;
  removalReason: string;
  removalType: string;
  approvalChain: {
    role: string;
    userId: string;
    userName: string;
    decision: string;
    timestamp: Date;
  }[];
}
```

### 4.4 Event Schemas

**NestJS Event Definitions:**

```typescript
// ojt-completed.event.ts
export class OjtCompletedEvent {
  eventId: string;
  operatorId: string;
  operatorName: string;
  department: string;
  processId: string;
  processName: string;
  result: string;
  userId: string;
  details: Record<string, any>;
  correlationId: string;
  timestamp: Date;
}

// training-status-changed.event.ts (Remains important for Versatility Matrix)
export class TrainingStatusChangedEvent {
  eventId: string;
  operatorId: string;
  processId: string;
  processName: string;
  oldStatus: string;
  newStatus: string; // O, LC, V, C, R, F, Removed, etc.
  statusChangeDate: Date;
  correlationId: string;
  timestamp: Date;
}

// qualification-removed.event.ts (Published internally upon successful removal workflow)
export class QualificationRemovedEvent {
  eventId: string;
  operatorId: string;
  processId: string;
  status: string = "Removed";
  removalReason: string;
  removalType: string;
  removalDate: Date;
  correlationId: string;
  timestamp: Date;
}

// notification-requested.event.ts - NEW Generic Event for Notifications
export class NotificationRequestedEvent {
  eventId: string;
  recipientUserIds: string[]; // Can be one or many
  recipientRole?: string; // Alternative targeting
  notificationType: string; // e.g., "ApprovalPending", "Info", "Reminder"
  templateKey: string; // Identifier for message template in Notification Service
  context: Record<string, any>; // Data for the template
  correlationId: string;
  timestamp: Date;
}

// --- Events for triggering/completing EXTERNAL workflows removed ---
// Removed: CertificationWorkflowRequestedEvent
// Removed: CertificationWorkflowCompletedEvent
// Removed: RecertificationWorkflowRequestedEvent
// Removed: RecertificationWorkflowCompletedEvent
// Removed: QualificationRemovalWorkflowRequestedEvent
// Removed: IpeWorkflowRequestedEvent
// Removed: IpeWorkflowCompletedEvent

// --- Other existing events remain relevant ---
// LearningCurveCompletedNotificationEvent (Could potentially be replaced by generic NotificationRequestedEvent)
// NewQualificationRequestedEvent (Could be replaced by generic NotificationRequestedEvent)
// NewQualificationApprovedEvent (Internal signal)
// FamiliarizationCompletedEvent (Important for Versatility Matrix)

// New events for key process handling
export class KeyProcessEvaluationCompletedEvent {
  eventId: string;
  operatorId: string;
  operatorName: string;
  department: string;
  processId: string;
  processName: string;
  score: number;
  passed: boolean;
  checklistUrl: string;
  comments?: string;
  userId: string;
  correlationId: string;
  timestamp: Date;
}

export class KeyProcessCertificationCompletedEvent {
  eventId: string;
  operatorId: string;
  processId: string;
  processName: string;
  outcome: "Approved" | "Rejected";
  score: number;
  checklistUrl: string;
  approvalChain: Array<{
    role: string;
    userId: string;
    decision: string;
    timestamp: Date;
  }>;
  completionDate: Date;
  correlationId: string;
  timestamp: Date;
}
```

**Service Bus Message Examples:**

// Remove examples for \*WorkflowRequested events
// Keep/Update examples for TrainingStatusChangedEvent, QualificationRemovedEvent
// Add example for NotificationRequestedEvent

**NotificationRequestedEvent Example:**

```json
{
  "eventId": "evt-notify-123",
  "recipientUserIds": ["qa-555"],
  "recipientRole": "QualityAuditor",
  "notificationType": "ApprovalPending",
  "templateKey": "CERTIFICATION_APPROVAL_REQUIRED",
  "context": {
    "operatorId": "op-123",
    "operatorName": "John Doe",
    "processName": "Wire Harness Assembly",
    "workflowId": "wf-cert-987",
    "initiatorName": "Jane Smith (TL)",
    "submissionDate": "2024-03-01T10:00:00Z"
  },
  "correlationId": "corr-7890",
  "timestamp": "2024-03-01T10:01:00Z"
}
```

## 5. API Specification

### 5.1 Command Endpoints

**Training Process API:**

```typescript
// training.controller.ts
@Controller("api/training")
export class TrainingController {
  constructor(private commandBus: CommandBus) {}

  @Post(":operatorId/ojt")
  @HttpCode(202)
  async completeOjt(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteOjtRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteOjtCommand(operatorId, dto, req.user.id)
    );
  }

  @Post(":operatorId/learning-curve")
  @HttpCode(202)
  async completeLearningCurve(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteLearningCurveRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteLearningCurveCommand(operatorId, dto, req.user.id)
    );
  }

  @Post(":operatorId/validation")
  @HttpCode(202)
  async initiateValidation(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateValidationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new InitiateValidationCommand(operatorId, dto.processId, req.user.id)
    );
  }

  @Post(":operatorId/certification")
  @HttpCode(202)
  async initiateCertification(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateCertificationRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateCertificationCommand(operatorId, dto.processId, req.user.id)
    );
  }

  @Post(":operatorId/recertification")
  @HttpCode(202)
  async initiateRecertification(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateRecertificationRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateRecertificationCommand(operatorId, dto.processId, req.user.id)
    );
  }

  @Post(":operatorId/new-qualification")
  @HttpCode(202)
  async initiateNewQualification(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateNewQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new InitiateNewQualificationCommand(operatorId, dto, req.user.id)
    );
  }

  @Post(":operatorId/qualification-completion")
  @HttpCode(202)
  async completeNewQualification(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteNewQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteNewQualificationCommand(operatorId, dto, req.user.id)
    );
  }

  @Post("qualification-removal")
  @HttpCode(202)
  async removeQualification(
    @Body() dto: RemoveQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateQualificationRemovalCommand(dto, req.user.id) // Renamed command
    );
  }

  @Post(":operatorId/ipe")
  @HttpCode(202)
  async initiateIpe(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateIpeRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateIpeCommand(operatorId, dto, req.user.id)
    );
  }

  @Post("trainer-assignment")
  @HttpCode(202)
  async assignTrainer(
    @Body() dto: AssignTrainerRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(new AssignTrainerCommand(dto, req.user.id));
  }

  @Post("qualification-request/approval")
  @HttpCode(202)
  async approveQualificationRequest(
    @Body() dto: ApproveQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new ApproveQualificationRequestCommand(dto, req.user.id)
    );
  }

  @Post("new-qualification/request")
  @HttpCode(202)
  async requestNewQualification(
    @Body() dto: NewQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new RequestNewQualificationCommand(dto, req.user.id)
    );
  }

  @Post("familiarization/complete")
  @HttpCode(202)
  async completeFamiliarization(
    @Body() dto: FamiliarizationCompletionDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteFamiliarizationCommand(dto, req.user.id)
    );
  }

  @Post(":operatorId/key-process-evaluation")
  @HttpCode(202)
  async submitKeyProcessEvaluation(
    @Param("operatorId") operatorId: string,
    @Body() dto: SubmitKeyProcessEvaluationDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new SubmitKeyProcessEvaluationCommand(operatorId, dto, req.user.id)
    );
  }

  @Post("key-process/checklist-upload")
  @HttpCode(200)
  @UseInterceptors(FileInterceptor("file"))
  async uploadKeyProcessChecklist(
    @UploadedFile() file: Express.Multer.File,
    @Body("operatorId") operatorId: string,
    @Body("processId") processId: string,
    @Request() req
  ): Promise<{ checklistUrl: string }> {
    const checklistUrl = await this.keyProcessService.storeChecklist(
      file,
      operatorId,
      processId
    );
    return { checklistUrl };
  }
}

// workflow.controller.ts - NEW Controller for workflow actions
@Controller("api/training/workflows")
export class WorkflowController {
  constructor(private commandBus: CommandBus) {}

  @Post(":workflowId/approve") // Or a more general '/decision' endpoint
  @HttpCode(200) // Or 202 if action is async
  async approveWorkflowStep(
    @Param("workflowId") workflowId: string,
    @Body() dto: ApproveWorkflowStepDto, // Contains decision (Approve/Reject) & comments
    @Request() req // Get approver user ID
  ): Promise<void> {
    await this.commandBus.execute(
      new ApproveWorkflowStepCommand(workflowId, dto, req.user.id)
    );
  }
}

// Removed: callback.controller.ts
```

**API Swagger Documentation Summary:**

| Endpoint                                              | Method | Description                                                       | Request                            | Response        |
| ----------------------------------------------------- | ------ | ----------------------------------------------------------------- | ---------------------------------- | --------------- |
| `/api/training/{operatorId}/ojt`                      | POST   | Completes On Job Training (O) phase                               | CompleteOjtRequestDto              | 202 Accepted    |
| `/api/training/{operatorId}/learning-curve`           | POST   | Completes Learning Curve (LC) phase                               | CompleteLearningCurveRequestDto    | 202 Accepted    |
| `/api/training/{operatorId}/validation`               | POST   | Initiates Validation (V) phase                                    | InitiateValidationRequestDto       | 202 Accepted    |
| `/api/training/{operatorId}/certification`            | POST   | Initiates **Internal** Certification (C) workflow                 | InitiateCertificationRequestDto    | 202 Accepted    |
| `/api/training/{operatorId}/recertification`          | POST   | Initiates **Internal** Recertification (R) workflow               | InitiateRecertificationRequestDto  | 202 Accepted    |
| `/api/training/{operatorId}/new-qualification`        | POST   | Initiates New Qualification (F) training                          | InitiateNewQualificationRequestDto | 202 Accepted    |
| `/api/training/{operatorId}/qualification-completion` | POST   | Completes New Qualification training                              | CompleteNewQualificationRequestDto | 202 Accepted    |
| `/api/training/qualification-removal`                 | POST   | Initiates **Internal** qualification removal workflow             | RemoveQualificationRequestDto      | 202 Accepted    |
| `/api/training/{operatorId}/ipe`                      | POST   | Initiates **Internal** IPE workflow                               | InitiateIpeRequestDto              | 202 Accepted    |
| `/api/training/trainer-assignment`                    | POST   | Assigns a trainer to an operator                                  | AssignTrainerRequestDto            | 202 Accepted    |
| `/api/training/qualification-request/approval`        | POST   | Approves or rejects a new qualification request (by Trainer)      | ApproveQualificationRequestDto     | 202 Accepted    |
| `/api/training/new-qualification/request`             | POST   | Requests a new qualification for an operator (for Trainer review) | NewQualificationRequestDto         | 202 Accepted    |
| `/api/training/familiarization/complete`              | POST   | Completes familiarization training for a qualification            | FamiliarizationCompletionDto       | 202 Accepted    |
| `/api/training/workflows/{workflowId}/approve`        | POST   | Submits an approval/rejection decision for a workflow step        | ApproveWorkflowStepDto             | 200 OK          |
| `/api/training/{operatorId}/key-process-evaluation`   | POST   | Submits evaluation for a key process                              | SubmitKeyProcessEvaluationDto      | 202 Accepted    |
| `/api/training/key-process/checklist-upload`          | POST   | Uploads checklist document for key process                        | Multipart form data                | 200 OK with URL |

### 5.2 Query Endpoints

**Query API:**

```typescript
// query.controller.ts
@Controller("api/training")
export class QueryController {
  constructor(private queryBus: QueryBus) {}

  @Get("qualification-removal/history/{operatorId}")
  async getQualificationRemovalHistory(
    @Param("operatorId") operatorId: string,
    @Query("processId") processId?: string
  ): Promise<QualificationRemovalHistoryDto[]> {
    return this.queryBus.execute(
      new GetQualificationRemovalHistoryQuery(operatorId, processId)
    );
  }
}
```

**API Swagger Documentation Summary:**

| Endpoint                                                   | Method | Description                                        | Query Params         | Response                         |
| ---------------------------------------------------------- | ------ | -------------------------------------------------- | -------------------- | -------------------------------- |
| `/api/training/qualification-removal/history/{operatorId}` | GET    | Gets qualification removal history for an operator | processId (optional) | QualificationRemovalHistoryDto[] |

## 6. Event Communication

### 6.1 Published Events

#### 6.1.1 Training Status & Related Events (Topic: "training-status-events" or specific topics)

- `OjtCompleted`
- `LearningCurveCompleted`
- `ValidationInitiated`
- `ValidationCompleted`
- `CertificationInitiated` // Internal event, might not need external publishing
- `CertificationCompleted` // Signals completion of internal workflow
- `RecertificationInitiated` // Internal event
- `RecertificationCompleted` // Signals completion of internal workflow
- `NewQualificationInitiated` // Internal event
- `NewQualificationCompleted`
- `TrainingStatusChanged` (Key event for Versatility Matrix)
- `NewQualificationApproved` // Internal signal
- `FamiliarizationCompleted` (Key event for Versatility Matrix status "F")
- `QualificationRemovalInitiated` // Internal event
- `QualificationRemoved` (Key event for Versatility Matrix status "Removed")
- `WorkflowStepCompleted` // Internal event

// Removed: Workflow Trigger Events (Topic: "workflow-trigger-events")
// - CertificationWorkflowRequested
// - RecertificationWorkflowRequested
// - QualificationRemovalWorkflowRequested
// - IpeWorkflowRequested

#### 6.1.2 Notification Events (Topic: "notification-events")

- `NotificationRequested` (Generic event to trigger user notifications via Notification Service for pending approvals, workflow outcomes etc.)
  // Removed: Specific notification events like NewQualificationRequestedNotification, QualificationRemovedNotification

## 7. Azure Services Integration

### 7.1 Azure Cosmos DB

- **Container Name**: `training-process`
- **Partition Key**: `/partitionKey` (operator ID)
- **Throughput**: Autoscale (1000-10000 RU/s)
- **Indexing Policy**: Optimized for common queries
- **TTL**: Not enabled (permanent data)
- **Change Feed**: Used for read model synchronization

### 7.2 Azure Service Bus

- **Namespace**: `connected-workers-sb`
- **Topics**:

  - `training-status-events`: For training phase/status updates (Consumed by Versatility Matrix, potentially read models).
  - `notification-events`: For sending notification requests to the Notification Service.
  - `training-internal-events`: Optional topic for internal communication (e.g., read model updates if not using Change Feed directly).
    // Removed: workflow-trigger-events
    // Removed: workflow-completion-events

- **Subscriptions**:
  // Removed: training-process-workflow-results subscription
  // Add subscriptions needed by internal components (e.g., read model updater if using internal events topic)

### 7.3 Azure Functions

- **Change Feed Processors**: (Remain as is)
  - `CrewManagementChangeFeedProcessor`
  - `SkillsMatrixChangeFeedProcessor`
  - `VersatilityMatrixChangeFeedProcessor`
- **Service Bus Trigger Functions**:
  // Removed: WorkflowCompletionProcessor
  // Add any functions needed to process internal events if using `training-internal-events` topic.

### 7.4 Azure API Management

- **API**: `training-process-api`
- **Operations**: Command and query endpoints
- **Policies**:
  - JWT validation
  - Rate limiting
  - Caching (for queries)
  - Correlation ID generation/propagation

### 7.5 Azure Monitor (Application Insights)

- **Logging**: Structured logging with Serilog
- **Metrics**: Service health, performance, and business metrics
- **Distributed Tracing**: End-to-end tracing across services
- **Alerts**: Configured for critical failures

### 7.6 Azure Key Vault

- **Secrets**:
  - Cosmos DB connection strings
  - Service Bus connection strings
  - API keys for external services

### 7.7 Cosmos DB Container Structure

| Container Name             | Purpose                      | Partition Key                 | TTL      | Primary Uses                              |
| -------------------------- | ---------------------------- | ----------------------------- | -------- | ----------------------------------------- |
| `training-records`         | Main write model             | `/partitionKey` (operatorId)  | Disabled | Stores `OperatorTrainingRecord` aggregate |
| `training-read-models`     | Read models/projections      | `/partitionKey` (varies)      | Disabled | Stores various read model projections     |
| `internal-workflows`       | **Optional:** Workflow state | `/partitionKey` (workflowId?) | Disabled | Stores `InternalWorkflow` entity state    |
| `crew-management-lease`    | Change Feed lease            | `/id`                         | Disabled | Tracks change feed processing progress    |
| `skills-matrix-lease`      | Change Feed lease            | `/id`                         | Disabled | Tracks change feed processing progress    |
| `versatility-matrix-lease` | Change Feed lease            | `/id`                         | Disabled | Tracks change feed processing progress    |

**Schema Management:**

- NestJS entities define the document schemas
- Service-driven models with no schema enforcement at database level
- Indexing policy optimized for common queries

## 8. Cross-Cutting Concerns

### 8.1 Security

- **Authentication**: Azure AD OAuth 2.0
- **Authorization**: Role-based access control
  - Trainer role
  - Team Leader role
  - Admin role
- **Data Protection**: HTTPS, encryption at rest and in transit
- **Secrets Management**: Azure Key Vault

### 8.2 Monitoring and Observability

- **Application Logging**: Serilog with structured logging
- **Distributed Tracing**: Correlation IDs across system boundaries
- **Performance Metrics**: Request durations, dependency calls
- **Business Metrics**: Training phase transitions, qualification counts
- **Health Checks**: Service and dependency health monitoring

### 8.3 Error Handling and Resilience

- **API Error Responses**: RFC 7807 Problem Details
- **Retry Policies**: For transient errors in external service calls
- **Circuit Breakers**: For failing external dependencies
- **Fallback Strategies**: For critical operations
- **Global Exception Handling**: Consistent error handling throughout the service

### 8.4 Scalability and Performance

- **Cosmos DB Scaling**: Autoscale provisioning
- **App Service Scaling**: Based on CPU/memory
- **Function Scaling**: Consumption plan with optimized instance count
- **Query Optimization**: Efficient indexes and projections
- **CQRS Separation**: Optimized read models
- **Asynchronous Processing**: Non-blocking operations

### 8.5 Testability

- **Unit Tests**: Domain model, command handlers, query handlers
- **Integration Tests**: Repository, event publishing, API endpoints
- **End-to-End Tests**: Key user scenarios
- **Load Tests**: Performance under load
- **Resilience Tests**: Chaos testing for failure scenarios

## 9. Implementation Considerations

### 9.1 Phased Implementation Approach

1. **Phase 1: Core Training Process**

   - Basic operator training record management
   - OJT and LC phase implementation
   - Integration with Crew Management via Change Feed
   - Basic event publishing to Service Bus

2. **Phase 2: Validation & Certification Integration**

   - Integration with Validation Submodule (REQ 32/33)
   - Implementation of V, C, R phases

3. **Phase 3: Enhanced Qualifications Management**

   - New qualification (F) process
   - Qualification removal integration
   - IPE workflow integration

4. **Phase 4: Read Model Optimization**
   - Dedicated read models for performance
   - Enhanced query capabilities
   - Dashboard views and reporting

### 9.2 Migration Considerations

If migrating from an existing system:

- Data migration strategy
- Parallel running period
- Cutover planning
- Rollback procedures

## 10. Open Questions and Risks

- **Specific data fields required from Versatility Matrix Change Feed need to be defined.** What information is necessary for read operations within the Training module?
- Performance concerns with high volume of operator/process/versatility changes via Change Feeds. Need efficient handlers and potentially batching.
- Handling potential race conditions or out-of-order events from Change Feeds or Service Bus.
- **Complexity of managing multi-step, potentially parallel internal approval workflows.** Need robust state management and error handling.
- Defining specific internal workflow steps and approvers for all scenarios (esp. IPE).
- Scalability of internal workflow processing under load.
- Availability requirements and SLA targets for the service and its dependencies.
- Data retention policies and archiving strategies for training history **and completed workflows.**
- Defining the exact logic for status updates when **internal** validation/certification/recertification fails (e.g., revert to previous status or maintain a 'failed' status?).

- **Definition and management of what constitutes a "key process"** - Need clear criteria for designating processes as key vs basic.
- **Integration approach for key process checklists** - Need to define file format, storage solution (blob storage), and retention policies.
- **Handling upgrades/downgrades between key and basic processes** - Need to define how to handle processes that change classification.
- **User experience for key process evaluations** - Need to design appropriate UIs and workflows that clearly communicate the differences.
- **Training history visualization** - Need to update dashboards and reports to clearly show key process evaluation history.

## Appendix A: External Dependencies

| Dependency           | Interface Type | Integration Method | Direction | Purpose                                                     |
| -------------------- | -------------- | ------------------ | --------- | ----------------------------------------------------------- |
| Crew Management      | Cosmos DB      | Change Feed        | In        | Consume & Duplicate Operator data                           |
| Skills Matrix        | Cosmos DB      | Change Feed        | In        | Consume & Duplicate Process definitions + IsKeyProcess flag |
| Versatility Matrix   | Cosmos DB      | Change Feed        | In        | Consume & Duplicate Versatility data (Read)                 |
| Versatility Matrix   | Service Bus    | Event Publish      | Out       | Publish Training Status updates                             |
| Validation Submodule | REST API       | HTTP Client        | Out/In    | Request Form templates, scoring (for basic processes)       |
| Notification Service | Service Bus    | Event Publish      | Out       | Send User notifications                                     |
| Blob Storage         | REST API       | SDK Client         | Out/In    | Store & retrieve key process checklists                     |
