**Functional Requirements Document: Operator Replacement Process**

**Document Version:** 1.0

**Date:** 2025-05-08

**Author:** NMILI Abdelali

**1. Introduction**

- **1.1. Purpose:** This document outlines the functional requirements for the Operator Replacement Process within the Connected Workers (CW) system for the Final Assembly or any other type of area. The process aims to ensure production continuity by efficiently managing planned and unplanned operator absenteeism, leveraging system automation, predefined backup structures, operator skill matrices, and AI-driven suggestions.
- **1.2. Audience:** This document is intended for Business Stakeholders (Production Management, Team Leaders, Shift Leaders), Technical Teams (Development, QA), and System Administrators involved in the implementation and maintenance of the CW system.
- **1.3. Key Performance Indicators (KPIs):**
  - **Replacement Time:** Average time to complete the replacement process at each level (<5 minutes).
  - **Suggestion Accuracy:** Percentage of AI-generated replacement suggestions accepted by users (>90%).
  - **Report Accuracy:** Error rate in replacement history reports (<5%).

**2. Scope**

- **2.1. In Scope:**
  - The process for identifying absences by Team Leaders (TLs).
  - Workflows for handling different levels of absenteeism severity (Low, Medium, High – as detailed herein).
  - Utilization of pre-defined Backup Structures (MFG Role-based) for replacements.
  - Utilization of Polyvalence/Versatility Matrices (ME Role-based) and operator qualifications for replacements.
  - Integration of AI suggestions based on qualifications, position needs, and historical TL scoring.
  - Interaction between TLs and Shift Leaders (SLs) for replacement management, including the use of "SL Backup Structure Panier" and "Department Backup Panier".
  - User interface interactions specific to the replacement process within CW (e.g., "My Workspace", popups, buttons, notifications).
  - Time constraints for completing replacement tasks.
  - End-of-shift evaluation of the replacement process by TLs.
  - Reporting requirements related to replacement history.
  - Impacts on the CW Clocking Sheet related to temporary assignments during replacement.
- **2.2. Out of Scope:**
  - The detailed process for initial operator clocking-in/out (Covered in FDS Module 3).
  - The detailed process for managing crew assignments and N+1 relationships (Covered in Crew Management - Module 1 workflow).
  - Detailed algorithms used by the integrated AI component.
  - The specific workflow for "Critical" level absenteeism (To be detailed in a future version of this document).
  - Configuration and maintenance of Backup Structures and Polyvalence/Versatility Matrices.
  - The underlying technical architecture of the Connected Workers system.

**2.3. Definitions and Terminology**

- **Value Stream:** A sequence of steps in the manufacturing process that adds value to the product.
- **Backup Structure:** A predefined list of operators available as replacements, categorized as:
  - **MFG Structure:** Operators trained specifically for manufacturing tasks.
  - **ME Structure:** Operators skilled in maintenance/engineering tasks.
- **Polyvalence Matrix:** A matrix indicating operators' versatility levels across different workstations.
- **Polyvalent Operator:** An operator certified for multiple qualifications within a value stream.
- **Qualification Levels:**
  - **V (Validated):** Initial validation of skill.
  - **C (Certified):** Formal certification.
  - **R (Recertified):** Renewed certification.
  - **F (Formé/Trained):** Basic training completed.
- **Station Criticity Levels:**
  - **C (Critical):** Requires high skill level (Polyvalence 3-4).
  - **M (Moderately Critical):** Requires medium skill level (Polyvalence 2-4).
  - **N (Not Critical):** Requires basic skill level (Polyvalence 1) or can operate without replacement.
- **Rework Qualification:** Certification for handling rework tasks.
- **SOP:** Standard Operating Procedure.
- **Clocking Sheet:** Record of operators' attendance and assignments.
- **N+1:** Immediate supervisor (TL for operators, SL for TLs).
- **Panier:** A pool or collection of available backup operators.

**3. Actors / Roles**

- **3.1. Team Leader (TL):** Responsible for verifying team presence, initiating and performing initial replacements for absences within their Value Stream(s), escalating requests, and evaluating the process.
- **3.2. Shift Leader (SL):** Responsible for managing escalated replacement requests, utilizing the SL Backup Panier and Department Backup Panier, and overseeing replacements across multiple teams/TLs.
- **3.3. Operator:** Production staff who may be absent or serve as replacements.
- **3.4. Coordinator:** Oversees multiple teams, has access to view replacement history reports, and may be involved in resource allocation decisions during high-level absenteeism scenarios.
- **3.5. Production Manager:** Responsible for monitoring replacement effectiveness across the production area, has access to reports and may influence resource allocation policies.
- **3.6. Department Clerk:** Administrative role with access to replacement history reports for record-keeping and reporting purposes.
- **3.7. Plant Manager:** Executive oversight role with access to all replacement data and reports for strategic decision-making.
- **3.8. Connected Workers (CW) System:** The platform hosting the process, providing interfaces, data, notifications, and AI suggestions.

**4. Assumptions & Dependencies**

- **4.1. System Availability:** The Connected Workers (CW) system is operational and accessible to relevant users (TLs, SLs) via tablets or desktops.
- **4.2. Data Integrity:** Operator schedules, qualifications (including validation, certification, recertification status: V, C, R, F), station criticity, Backup Structures, and Polyvalence/Versatility Matrices are accurately maintained in the system.
- **4.3. Module Integration:** Clocking data (Module 3) and Crew Management data (Module 1) are available and integrated as required by this process.
- **4.4. User Training:** TLs and SLs are trained on how to use the CW system for the replacement process.
- **4.5. Role Definitions:** User roles (TL, SL, MFG Structure, ME Structure) are correctly assigned within the CW system.

**4.6. Process Overview**

The Replacement Process activates upon detecting absenteeism, with workflows varying by severity level:

1. **Low Level** (Handled by TL):

   - TL identifies absence in "My Workspace"
   - TL selects from three options:
     - Option 1: Use Backup Structure (MFG)
     - Option 2: Use Polyvalence Matrix (ME)
     - Option 3: No replacement needed
   - TL completes action within 5 minutes
   - TL sends remaining backups to SL Panier

2. **Medium Level** (Handled by SL):

   - Triggered by TL escalation or system alert
   - SL accesses "Backup Replacement" menu
   - SL reviews SL Backup Panier
   - System suggests replacements using AI
   - SL assigns replacement within 5 minutes
   - SL notifies requesting TL

3. **High Level** (Department-wide):

   - Triggered when SL Panier is insufficient
   - SL transfers backups to Department Panier
   - Department Panier operates on FIFO basis
   - System recommends best options
   - SL assigns department-level replacement
   - Requesting TL/SL is notified

4. **Critical Level** (To be defined in future version)

At shift end, TLs evaluate replacements (1-5 scale). All replacement activities are recorded for reporting.

**5. Functional Requirements**

- **5.1. Absence Identification & Process Initiation**

  - **FR-REP-001: Display Absence Status**
    - **Description:** The system shall display the real-time presence and absence status of scheduled operators within the TL's "My Workspace" view after the TL verifies presence.
    - **Trigger:** TL accesses and updates "My Workspace" after verifying operator presence for their Value Stream(s). (Dependency: FDS Module 3 for TL clocking/presence verification details).
    - **Pre-conditions:** TL is logged into CW; Operator schedules for the shift exist; Operator presence/absence data is available.
    - **Workflow:**
      1.  TL verifies operator presence for their assigned Value Stream(s).
      2.  TL interacts with "My Workspace" to confirm presence verification.
      3.  The system updates the workspace, clearly indicating operators with an "Absent" status.
    - **Post-conditions (Success):** Absence statuses are correctly displayed; TL can identify operators needing replacement.
  - **FR-REP-002: Initiate Replacement for Absence**
    - **Description:** The TL shall be able to initiate the replacement process for each identified absent operator.
    - **Trigger:** TL identifies an operator with "Absent" status in "My Workspace".
    - **Pre-conditions:** FR-REP-001 completed; At least one operator is marked as absent.
    - **Workflow:**
      1.  TL selects an absent operator instance in "My Workspace".
      2.  TL activates the replacement function for that specific absence.
      3.  The system presents the appropriate replacement workflow based on the defined absenteeism level (initially focusing on Low Level).
    - **Post-conditions (Success):** The replacement workflow for the selected absence is initiated.

- **5.2. Low-Level Absenteeism Workflow (Team Leader)**

  - **Description:** Workflow executed by the TL when absenteeism level is categorized as "Low". The TL has multiple options.
  - **Trigger:** FR-REP-002 completed for an absence categorized as Low Level.
  - **Pre-conditions:** Absence identified; absenteeism level determined as Low.
  - **Business Rules:** BR-REP-001, BR-REP-002, BR-REP-003, BR-REP-004, BR-REP-005, BR-REP-006, BR-REP-012
  - **Workflow:** The system presents the TL with options based on available strategies:

    1.  Option 1: Use Backup Structure (MFG Role operators) - See FR-REP-003.
    2.  Option 2: Use Polyvalence Matrix (ME Role operators) - See FR-REP-004.
    3.  Option 3: No Replacement Needed - See FR-REP-005.

  - **FR-REP-003: Low Absenteeism - Option 1: Backup Structure Replacement**

    - **Description:** TL uses the predefined Backup Structure associated with their Value Stream(s) to find a replacement. Operators in this structure have the MFG Structure role and specific qualifications.
    - **Sub-Requirement FR-REP-003.1: Single Value Stream**
      - **Workflow:**
        1.  System displays the TL's Backup Structure list for their team/VS within the workspace.
        2.  TL reviews the list (containing operators designated as Polyvalent, Rework, or SOP-qualified per BR-REP-001).
        3.  TL selects a suitable operator from the list for the replacement.
        4.  TL confirms the selection.
        5.  TL proceeds to finalize and send unused backup operators (if any) to the SL Panier (See FR-REP-006).
    - **Sub-Requirement FR-REP-003.2: Multiple Value Streams**
      - **Workflow:**
        1.  TL initiates replacement for an absence in one VS.
        2.  TL can choose to:
            - a) Use the Backup Structure from the _same_ VS. System displays the relevant list.
            - b) Use the Backup Structure from _another_ VS under their responsibility. System displays a popup listing available backup operators across all TL's VSs.
        3.  TL selects a suitable operator and confirms the selection.
        4.  TL repeats steps 1-3 for all absences across their VSs requiring this replacement type.
        5.  Once all replacements using this method are defined, TL proceeds to finalize and send the consolidated list of unused backup operators to the SL Panier (See FR-REP-006). If the final unused backup list is empty, this step may be skipped allowing the TL to proceed to "Start Shift".

  - **FR-REP-004: Low Absenteeism - Option 2: Polyvalence Matrix Replacement**

    - **Description:** TL uses the Polyvalence/Versatility Matrix (associating ME Structure role operators) to find a replacement based on skills and station criticity. AI assists in suggesting suitable operators.
    - **Sub-Requirement FR-REP-004.1: Replacement within Same Team**
      - **Workflow:**
        1.  System presents replacement suggestions based on Polyvalence Matrix, considering:
            - Required Qualification Level (V, C, R, or F) (BR-REP-002).
            - Station Criticity (C, M, N) and required polyvalence level (BR-REP-003).
            - AI input based on qualifications, suitability, and TL scoring (BR-REP-012).
        2.  TL reviews suggestions.
        3.  A confirmation popup appears, allowing the TL to:
            - Option 1: Assign an operator from one station to another (move).
            - Option 2: Assign an operator to the vacant station while they retain their original assignment (multi-station).
            - Option 3: Assign one or more specific operators manually to the station.
            - Option 4: Select "Others" and choose a suitable operator from a dropdown list (filtered based on qualification/criticity criteria).
        4.  TL confirms the chosen action.
    - **Sub-Requirement FR-REP-004.2: Replacement from Different Team**
      - **Workflow:**
        1.  System presents replacement suggestions based primarily on the Versatility Matrix, considering:
            - Required Qualification Level (V, C, R, or F) (BR-REP-002). (_Note: Source text states station criticity/polyvalence level is NOT considered here, focus is on skill validation_).
            - AI input based on qualifications, suitability, and TL scoring (BR-REP-012).
        2.  TL reviews suggestions.
        3.  A confirmation popup appears, allowing the TL to:
            - Option 1: Assign an operator from one station to another (move).
            - Option 2: Assign an operator to the vacant station while they retain their original assignment (multi-station).
            - Option 3: Assign one or more specific operators manually to the station.
        4.  TL confirms the chosen action.

  - **FR-REP-005: Low Absenteeism - Option 3: No Replacement Needed**
    - **Description:** The TL determines that, given the low level of absenteeism, no replacement action is necessary for a specific absence.
    - **Workflow:**
      1.  TL selects the option indicating no replacement is needed for the specific absence.
      2.  System records this decision.
      3.  TL proceeds with managing other absences or finalizing the process.

- **5.3. Finalizing Team Leader Actions**

  - **FR-REP-006: Send Backup Structure to Shift Leader Panier**
    - **Description:** After completing replacements using Option 1 (Backup Structure), the TL must transfer any remaining/unused operators from their Backup Structure list to the central SL Backup Structure Panier.
    - **Trigger:** TL completes selections/assignments using FR-REP-003.
    - **Pre-conditions:** TL has potentially used some operators from their Backup Structure for replacements.
    - **Workflow:**
      1.  TL clicks the "Send to Shift Leader Backup Structure (Panier)" button. _(Constraint: This button must be actioned before the "Start a Shift" button becomes enabled, unless the backup structure list was entirely unused/empty)._
      2.  A confirmation popup appears: "Are you sure you want to transfer your backup structure to the shift leader?".
      3.  TL confirms ("Yes").
      4.  System transfers the list of remaining operators to the SL Panier.
      5.  System sends a notification to the SL indicating receipt of the TL's backup structure.
      6.  If TL selects "No" on the popup, the structure remains with the TL (TL retains responsibility).
    - **Post-conditions (Success):** Unused backup operators are transferred to the SL Panier; SL is notified; TL can proceed.
    - **Exception:** If the TL's backup structure is empty (all used or none existed), this step is bypassed, and the TL can proceed to "Start Shift".
  - **FR-REP-007: Display Backup Usage Summary**
    - **Description:** The TL's replacement interface shall display a summary of backup structure utilization.
    - **Workflow:** During/after the replacement process, the interface displays:
      1.  Total number of operators initially in the TL's Backup Structure.
      2.  Number/list of backup operators used for replacement.
      3.  Number/list of remaining backup operators transferred (or to be transferred) to the SL Panier.
    - **Post-conditions (Success):** TL has visibility into backup resource allocation.
  - **FR-REP-008: Reminder to Start Shift**
    - **Description:** After completing all necessary replacement actions and confirmations (including FR-REP-006 if applicable), the system shall prompt the TL to start their shift.
    - **Trigger:** Completion of replacement workflows and confirmations for all absences managed by the TL.
    - **Workflow:** A final popup appears reminding the TL to click the "Start a Shift" button.
    - **Post-conditions (Success):** TL is reminded to formally start the shift monitoring/management in CW.

- **5.4. Escalation and Higher-Level Absenteeism Workflows**

  - **FR-REP-009: Escalate Replacement Request to Shift Leader (TL Action)**
    - **Description:** If a TL cannot find a suitable replacement using the Low-Level options (e.g., no suitable backups, no qualified operators via matrix), they can escalate the request to the SL.
    - **Trigger:** TL determines no suitable replacement is available via FR-REP-003 or FR-REP-004 and clicks the "Proceed to replacement process request" button.
    - **Pre-conditions:** TL has attempted Low-Level replacement options unsuccessfully.
    - **Workflow:**
      1.  TL clicks the "Proceed to replacement process request" button.
      2.  A confirmation popup appears: "Are you sure you want to move to the replacement process request?".
      3.  TL confirms ("Yes").
      4.  System sends a notification to the SL. The notification includes details of the required replacement (e.g., workstation, necessary qualifications).
    - **Post-conditions (Success):** Replacement request is escalated; SL is notified with required details.
  - **FR-REP-010: Handle Medium-Level Absenteeism (Shift Leader)**
    - **Description:** Workflow executed by the SL when absenteeism level is categorized as "Medium" or when a TL escalates a request (FR-REP-009).
    - **Trigger:** System alert for Medium level absenteeism OR notification received from TL (FR-REP-009).
    - **Pre-conditions:** SL is logged into CW; SL has access to the "Backup Replacement" menu and the SL Backup Panier.
    - **Business Rules:** BR-REP-001, BR-REP-002, BR-REP-005, BR-REP-012
    - **Workflow:**
      1.  SL receives alert/notification.
      2.  SL accesses the "Backup Replacement" menu and reviews their SL Backup Structure Panier (contains SL's own support backups + backups transferred from TLs via FR-REP-006).
      3.  The system suggests suitable replacement operators from the SL Panier, considering:
          - Operator profiles (Support, Polyvalent, Repairers - _Source implies these are potential roles/types within the Panier_).
          - Versatility Matrix qualifications (BR-REP-002).
          - AI suggestions (based on need, qualifications, historical TL scoring) (BR-REP-012).
      4.  SL selects one of the suggested operators (or potentially manually selects if needed, _implied_).
      5.  SL confirms the choice.
      6.  System sends a notification to the requesting TL confirming the replacement assignment.
      7.  The CW interface ("backup replacement" box) displays the total number of structure backups available to the SL.
    - **Post-conditions (Success):** Replacement assigned by SL; Requesting TL is notified.
  - **FR-REP-011: Handle High-Level Absenteeism (Shift Leader / Dept)**
    - **Description:** Workflow executed when absenteeism is categorized as "High" or if the SL cannot find a suitable replacement within their own SL Backup Panier (FR-REP-010). Involves using a shared Department level backup pool.
    - **Trigger:** System alert for High level absenteeism OR SL determines their Panier is insufficient during Medium level handling.
    - **Pre-conditions:** Medium-level replacement attempt (FR-REP-010) failed or High level triggered; Department Backup Panier exists and is accessible.
    - **Business Rules:** BR-REP-001, BR-REP-002, BR-REP-005, BR-REP-007, BR-REP-012
    - **Workflow:**
      1.  SL determines their own Panier is insufficient.
      2.  SL initiates transfer of their _own_ Shift Leader Backup Structure Panier to the "Shift Leader Backup Panier of Department".
      3.  The Department Panier operates on a "First Come, First Served" (FIFO) basis for requests/allocations (BR-REP-007).
      4.  The CW system recommends the best replacement option from the Department Panier based on job/workstation requirements, operator qualifications (Versatility Matrix - BR-REP-002), AI input, and TL scoring (BR-REP-012).
      5.  (_Assumption: An authorized role, likely SL or potentially Coordinator/Manager, selects and confirms the replacement from the Department Panier suggestions._)
      6.  (_Assumption: Notification sent to relevant TL/SL upon assignment._)
    - **Post-conditions (Success):** Replacement assigned from Department Panier; Requesting TL/SL notified. _Note: Details on selection/confirmation from Dept Panier need further clarification._
  - **FR-REP-011B: Critical-Level Absenteeism (To Be Defined)**
    - **Description:** Placeholder for Critical-level absenteeism process. This section will be detailed in a future version of this document.
    - **Trigger:** To be defined.
    - **Pre-conditions:** To be defined.
    - **Workflow:** To be defined. May involve Plant Manager, Production Manager, and additional resource allocation strategies beyond the department level.
    - **Post-conditions:** To be defined.

- **5.5. System Constraints & Supporting Functions**
  - **FR-REP-012: Time Constraints for Replacement Actions**
    - **Description:** TLs and SLs have specific time limits to complete their respective replacement process steps to ensure timely action.
    - **Workflow:**
      1.  **Team Leader:** A 5-minute countdown timer starts when the TL begins the replacement process for their area. Within this time, the TL should complete replacements and be ready to send their backup structure (if applicable via FR-REP-006).
      2.  **Shift Leader:** A 5-minute countdown timer starts when the SL begins processing a Medium-level replacement request. Within this time, the SL should complete the replacement assignment using their Panier or decide to escalate (initiating FR-REP-011).
    - **Post-conditions (Success):** Timers are displayed; system potentially flags delays if timers expire (_Action on expiry needs definition - e.g., auto-escalation, notification_).
    - **Business Rule:** BR-REP-008
  - **FR-REP-013: Replacement Evaluation by Team Leader**
    - **Description:** At the end of the shift, the TL is prompted to evaluate the effectiveness/suitability of the replacement(s) that occurred for their team. This feeds back into the AI suggestions.
    - **Trigger:** End of shift.
    - **Workflow:**
      1.  System sends a "Replacement Evaluation Request" notification/task to the TL.
      2.  TL provides an evaluation score (Scale 1 to 5) for the replacement(s) handled during the shift.
      3.  System stores the evaluation score, associating it with the specific replacement instance (operator, workstation, date/shift).
    - **Post-conditions (Success):** Evaluation captured; data available for AI learning/TL scoring input (BR-REP-012).
    - **Business Rule:** BR-REP-009
  - **FR-REP-014: Replacement History/Clocking Report**
    - **Description:** A report shall be available providing a historical log of all replacement activities and related clocking adjustments.
    - **Access Roles:** Team Leader, Shift Leader, Coordinator, Production Manager, Department Clerk, Plant Manager.
    - **Content:** Logs all changes due to absenteeism, including:
      - Original absent operator.
      - Replacement operator assigned.
      - Workstation/Value Stream involved.
      - Operators reassigned between workstations.
      - Operators temporarily moved between teams/TLs.
      - Relevant timestamps.
    - **Functionality:** Users shall be able to filter the report by: Date, Shift, Customer, Project, Family, Value Stream, Area, Team, Line Assignment, Operator ID/Name, Coordinator ID/Name, Shift Leader, Team Leader.
    - **Post-conditions (Success):** Authorized users can access and filter the report to track replacement activities and temporary assignments.
  - **FR-REP-015: Clocking Sheet Impact Management**
    - **Description:** Defines how the replacement process interacts with the operator Clocking Sheet within CW.
    - **Workflow & Rules:**
      1.  **TL Clocking Sheet:** Updated initially based on verified presence/absence _before_ the replacement process begins.
      2.  **Provisional Movements (TL initiated):** If a TL moves an operator temporarily (e.g., Low Level - Option 2, Case 1 or 2), the _original_ TL's clocking sheet is NOT modified to reflect this temporary assignment change. The movement is tracked in the Replacement History report (FR-REP-014).
      3.  **SL Backup Panier Clocking:** The SL is responsible for clocking in the operators within their Backup Panier _after_ confirming their physical presence (post-turnstile clock-in). If Panier operators are ultimately _not_ used for replacement, the SL must update their status (e.g., to "CTN" - Chômage technique non planifié/Unplanned Technical Unemployment) once the replacement process is complete before potentially sending them back. (BR-REP-010)
      4.  **Department Panier Clocking (Unused Operators):**
          - Unused operators originally from a _TL's_ Backup Structure but placed in the Dept Panier: Clocking responsibility remains with the operator's original SL (N+1 of the operator). (BR-REP-011)
          - Unused operators originally from an _SL's_ Backup Structure placed in the Dept Panier: Clocking responsibility remains with the operator's original SL (N+2 of the operator - _assuming N+1 is TL_). (BR-REP-011)
      5.  **TL Visibility of SL Replacements:** When an SL assigns a replacement from their Panier to a TL's team, the operator appears on the TL's clocking sheet (likely marked as present/assigned). The TL retains the ability to update this operator's status during the shift if needed (e.g., if the replacement leaves early).
    - **Post-conditions (Success):** Clocking sheet accurately reflects permanent assignments while temporary replacement moves are logged separately; Responsibility for clocking unused backups is clear.
  - **FR-REP-016: N+1 Assignment Stability**
    - **Description:** The replacement process itself does not change an operator's permanent N+1 (direct supervisor) assignment.
    - **Workflow & Rules:**
      1.  Temporary movements for replacement do not alter the N+1 relationship stored in the system.
      2.  Any permanent changes to N+1 assignment must be requested by the operator's _current_ assigned N+1 and processed through the Crew Management (Module 1) workflow. (BR-REP-006)
      3.  _(Regional Note): This process applies universally. The note about Morocco vs. other countries regarding document retrieval from kiosks seems related to Crew Management/HR processes, not this functional replacement workflow itself._
    - **Post-conditions (Success):** N+1 assignments remain stable unless explicitly changed via the correct Crew Management process.
  - **FR-REP-017: Exception Handling - System Failure**
    - **Description:** Defines the process for handling system failures or technical issues during the replacement process.
    - **Trigger:** CW system unavailability, application errors, or connectivity issues during any replacement workflow.
    - **Workflow:**
      1. **Detection:** User encounters system error/unavailability during the replacement process.
      2. **Manual Override:** TL/SL should implement a manual paper-based or verbal replacement process according to the established escalation paths (Low → Medium → High).
      3. **Notification:** TL/SL must notify IT support about the system issue.
      4. **Record Keeping:** TL/SL should document all manual replacement decisions.
      5. **Data Synchronization:** Once the system is restored, TL/SL should update the CW system with the manual replacements completed during the outage.
    - **Post-conditions (Success):** Production continuity maintained despite system issues; Replacement data eventually synchronized in CW.

**6. Business Rules**

- **BR-REP-001: Backup Structure (MFG) Qualifications:** Operators in a TL's Backup Structure must meet specific criteria: Polyvalent (trained, certified, recertified for all VS qualifications), Rework (possess rework qualification), or SOP/SOS Assemblage qualified. Defined by the Production team.
- **BR-REP-002: Qualification Level Requirement:** For matrix-based replacements, the replacement operator must generally have a qualification status of V (Validated), C (Certified), R (Recertified), or F (Formé - Trained) for the required skill/station.
- **BR-REP-003: Station Criticity Levels (Polyvalence Matrix):** Used for _intra-team_ replacements (FR-REP-004.1):
  - C (Critical): Requires Polyvalence Level 3 or 4.
  - M (Moderately Critical): Requires Polyvalence Level 2, 3, or 4.
  - N (Not Critical): Requires Polyvalence Level 1, or replacement may be skipped.
- **BR-REP-004: TL Backup Transfer Prerequisite:** The TL cannot activate "Start a Shift" until they have actioned the "Send to Shift Leader Backup Structure (Panier)" button (FR-REP-006), unless the backup structure was empty/unused.
- **BR-REP-005: AI Suggestion Factors:** AI recommendations for replacements consider: required position/workstation skills, operator qualifications/versatility, and historical TL evaluation scores.
- **BR-REP-006: N+1 Change Process:** Permanent N+1 changes are outside this process and must use Crew Management (Module 1), initiated by the current N+1.
- **BR-REP-007: Department Panier Allocation Rule:** The Department Backup Panier operates on a First Come, First Served (FIFO) basis.
- **BR-REP-008: Replacement Time Limits:** TLs have 5 minutes to complete initial replacements; SLs have 5 minutes to handle Medium-level requests.
- **BR-REP-009: Replacement Evaluation Score:** TLs evaluate replacements on a scale of 1 to 5 at end-of-shift.
- **BR-REP-010: SL Clocking Responsibility (Panier):** SLs must clock-in their Panier operators after confirming presence and update status if unused.
- **BR-REP-011: Clocking Responsibility (Unused Dept Panier Operators):** Clocking for unused operators in the Dept Panier depends on their origin (TL backup vs SL backup) and falls to the relevant SL (N+1 or N+2).
- **BR-REP-012: AI Suggestion Application:** AI assists in suggesting replacements in FR-REP-004 (Low-Option 2), FR-REP-010 (Medium), and FR-REP-011 (High).

**7. Acceptance Criteria (Examples)**

- **AC-REP-001 (for FR-REP-001):** Given a TL logs into CW and completes presence verification for their VS, WHEN the TL views "My Workspace", THEN all scheduled operators are listed, AND their status (Present/Absent) is correctly displayed based on the verification.
- **AC-REP-002 (for FR-REP-003.1):** Given a TL initiates replacement for an absence (Low Level) and chooses Option 1 (Backup Structure) for a single VS, WHEN the TL selects an operator from the displayed Backup list and confirms, THEN the system assigns the operator, AND the option to "Send to Shift Leader Backup Structure (Panier)" is presented.
- **AC-REP-003 (for FR-REP-004.1):** Given a TL initiates replacement (Low Level, Option 2, Same Team) for a station with Criticity 'C', WHEN the system suggests replacements, THEN only operators with Polyvalence Level 3 or 4 AND appropriate Qualification (V/C/R/F) are suggested.
- **AC-REP-004 (for FR-REP-009):** Given a TL clicks "Proceed to replacement process request" and confirms, WHEN the SL checks their notifications, THEN a notification exists detailing the required workstation and qualifications for the replacement needed by the TL.
- **AC-REP-005 (for FR-REP-010):** Given an SL receives a Medium level request and selects an operator from the SL Panier suggestions, WHEN the SL confirms the assignment, THEN the requesting TL receives a notification confirming the replacement.
- **AC-REP-006 (for FR-REP-012):** Given a TL starts the replacement process, WHEN 5 minutes elapse without completion/confirmation, THEN a visual indicator/alert is triggered on the TL's interface (_Exact behavior on expiry TBD_).
- **AC-REP-007 (for FR-REP-014):** Given a Production Manager accesses the "Replacement History/Clocking" report and filters by a specific Date and Team Leader, THEN only replacement records matching those criteria are displayed.
- **AC-REP-008 (for FR-REP-015):** Given a TL temporarily assigns Operator A from Station X to Station Y (Low Level, Option 2), WHEN viewing the TL's official Clocking Sheet, THEN Operator A still appears assigned to Station X, AND WHEN viewing the Replacement History Report, THEN an entry exists showing Operator A temporarily worked at Station Y.
- **AC-REP-009 (for FR-REP-013):** Given a shift has ended with at least one replacement operator assigned, WHEN the TL accesses CW, THEN the system prompts the TL to evaluate the replacement on a scale of 1-5.
- **AC-REP-010 (for FR-REP-017):** Given the CW system becomes unavailable during the replacement process, WHEN the TL/SL follows the manual override procedure and the system is later restored, THEN all manual replacement decisions are accurately recorded in the system.

**8. Traceability Matrix (Requirement to Business Rule/KPI)**

| Requirement ID | Requirement Description Snippet                   | Related Business Rule(s)                                   | Related Business Objective / KPI                                    |
| :------------- | :------------------------------------------------ | :--------------------------------------------------------- | :------------------------------------------------------------------ |
| FR-REP-001     | Display Absence Status                            | -                                                          | Timely identification of attendance issues                          |
| FR-REP-002     | Initiate Replacement for Absence                  | -                                                          | Initiate corrective action for absenteeism                          |
| FR-REP-003     | Low Absenteeism - Option 1: Backup Structure      | BR-REP-001, BR-REP-004                                     | Utilize predefined backup resources; Ensure process completion      |
| FR-REP-004     | Low Absenteeism - Option 2: Polyvalence Matrix    | BR-REP-002, BR-REP-003, BR-REP-005, BR-REP-012             | Utilize operator skills efficiently; Leverage AI; Maintain quality  |
| FR-REP-005     | Low Absenteeism - Option 3: No Replacement Needed | -                                                          | Allow TL discretion based on operational impact                     |
| FR-REP-006     | Send Backup Structure to Shift Leader Panier      | BR-REP-004                                                 | Centralize unused backup resources; Enable SL oversight             |
| FR-REP-007     | Display Backup Usage Summary                      | -                                                          | Provide visibility into resource utilization                        |
| FR-REP-008     | Reminder to Start Shift                           | -                                                          | Ensure timely transition to shift monitoring                        |
| FR-REP-009     | Escalate Replacement Request to Shift Leader      | -                                                          | Provide mechanism for unresolved TL-level issues                    |
| FR-REP-010     | Handle Medium-Level Absenteeism (SL)              | BR-REP-001, BR-REP-002, BR-REP-005, BR-REP-012             | Efficient handling of escalated issues; Utilize SL resources & AI   |
| FR-REP-011     | Handle High-Level Absenteeism (SL / Dept)         | BR-REP-001, BR-REP-002, BR-REP-005, BR-REP-007, BR-REP-012 | Manage significant absenteeism; Ensure fairness (FIFO); Leverage AI |
| FR-REP-011B    | Critical-Level Absenteeism (TBD)                  | TBD                                                        | TBD - Will address extreme absenteeism scenarios                    |
| FR-REP-012     | Time Constraints for Replacement Actions          | BR-REP-008                                                 | Replacement Time KPI (<5 min); Minimize production downtime         |
| FR-REP-013     | Replacement Evaluation by Team Leader             | BR-REP-009, BR-REP-005, BR-REP-012                         | Suggestion Accuracy KPI (>90%); Improve AI suggestion quality       |
| FR-REP-014     | Replacement History/Clocking Report               | -                                                          | Report Accuracy KPI (<5% error); Track temporary assignments        |
| FR-REP-015     | Clocking Sheet Impact Management                  | BR-REP-010, BR-REP-011                                     | Maintain accurate attendance records; Clarify responsibilities      |
| FR-REP-016     | N+1 Assignment Stability                          | BR-REP-006                                                 | Maintain organizational structure integrity                         |
| FR-REP-017     | Exception Handling - System Failure               | -                                                          | Ensure production continuity during system outages                  |
