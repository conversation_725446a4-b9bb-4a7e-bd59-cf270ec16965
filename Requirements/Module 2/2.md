### Deep Dive: Training Process Submodule Design

Based on the client's detailed specifications and automotive wiring/cabling industry practices, the **Training Process** submodule is structured to manage operator skill development while minimizing overlap with other submodules (e.g., Skills Matrix, Versatility Matrix). Below is a refined breakdown of its components, features, and workflows, ensuring loose coupling and domain specificity.

---

### **Core Features of the Training Process**

#### 1. **Operator Onboarding & Lifecycle Management : this part gonna be handled by the Crew Management module**

- **New Operator Creation**

  - **Use Case**: TKS Responsible creates operators manually or via OPTITIME Excel import.
  - **Fields**: ID, Name, Surname, Hiring Date, Department, Category, Function.
  - **Integration**:
    - Sync with HR systems (OPTITIME) for initial data.
    - **Exception**: For North Africa (Morocco & Tunisia), operator data must be sourced from Workday as the operator role already exists there.
    - **Loose Coupling**: Import/export via standardized schemas (e.g., CSV/Excel templates).
  - **Output**: Triggers notifications to Training Responsible (no direct dependency on HR systems).

- **Assignment to Trainers**
  - **Use Case**: Training Responsible assigns new operators to trainers by department.
  - **Workflow**:
    - Notification sent to trainers upon assignment.
    - Trainer initiates On Job Training (O).

#### 2. **Structured Training Workflows**

- **Phases**:

  1.  **On Job Training (O)**

      - **Use Case**: Trainer fills the form for the basic process, then submits basic/key process training results via form. (data of operators is gonna be duplicated from the Crew Management module to the Training Process module using change feed cosmos db)
      - **Form Fields**: Operator ID, Process (dropdown), Result (e.g., 85%), Checklist upload, Comments.
      - **Workflow**:
        - Trainers assign qualified operators to Shift Leaders according to department/qualifications (Crew Management module).
        - Shift Leaders allocate operators to Team Leaders (Crew Management module).
        - Team Leaders assign operators to teams through the organization chart (Crew Management module).
      - **Output**:
        - Operator added to Versatility Matrix with status **"O"**.

  2.  **Learning Curve (LC)**

      - **Use Case**: Trainer fills the form for LC results submission when the operator is ready.
      - **Form Fields**: Operator ID, Key Process, Result (OK/NOK), Checklist upload.
      - **Output**:
        - Versatility Matrix updated to **"LC"**.
        - Notification sent to Team Leader of status change.

  3.  **Validation (V)**

      - **Use Case**: Team Leader initiates validation when the operator is deemed ready after the LC phase. (we get the form template and calculation of the score are handled by the Validation submodule REQ 32/33 based on the Department-Specific Logic)
      - **Validation Requirements**:
        - Gray areas must not appear during validation.
        - Score ≥80% (REQ 32/33 calculation).
      - **Escalation Workflow**:
        <!-- todo : remove retries and escalation workflow , it s gonna be handled manually by the team leader -->
        - number of retries and conditions.
        - If score <80%, the evaluation may be attempted again.
        - After multiple failed attempts, escalation to Shift Leader is activated.
      - **Output**: Versatility Matrix updated to **"V"**.

  4.  **Certification (C)**

      - **Use Case**: Team Leader initiates certification when the operator is deemed ready after the Validation phase. (we get the form template and calculation of the score are handled by the Validation submodule REQ 32/33 based on the Department-Specific Logic)
      - **Validation Criteria**:
        - Score ≥85% (REQ 32/33 calculation).
      - **Quality Approval Workflow**: (approval workflow is handled in the module 1)
        - Team Leader submits certification request (REQ 32/33) : this is must the trigger of the approval workflow module 1.
        - Once approved, notification sent to Quality Auditor.
        - Quality Auditor submits process-specific form and approve/reject.
        - Notification sent to Team Leader and Trainer.
        - Trainer validates (approve/reject the certification) the end of module 1 request workflow approvals.
      - **Escalation Workflow**:
        - number of retries and attempts conditions.
        - After multiple failed attempts (score <85% or approval workflow rejected) , escalation to Shift Leader is activated.
      - **Output**: Versatility Matrix updated to **"C"**.

  5.  **Recertification (R)** (same as certification process)
      - **Use Case**: Recertification is required periodically (e.g., annually) for each key process and is initiated manually by the team leader or the trainer. (trigger of the approval workflow module 1)
      - **Workflow**: (approval/notification workflow is handled in the module 1)
        - Quality Auditor receives notification when recertification is initiated.
        - Quality Auditor submits process-specific form.
        - Notification sent to Team Leader and Trainers.
        - Trainer validates (approve/reject the recertification) the end of module 1 request workflow approvals.
      - **Output**: Versatility Matrix updated to **"R"** upon successful recertification.

- **Department-Specific Logic**: (handled in the Validation submodule REQ 32/33)
  - **Assembling**: Polyvalence levels tracked (X, 1-4).
  - **Cutting & Leadprep**: Simplified machine zoning (no polyvalence).

#### 3. **Existing Operator Qualification Management**

- **New Qualification (F → C → R)**

  - **Use Case**: Team Leader submits request for new qualification for an existing operator.
  - **Workflow**:
    1.  TL fills form (Operator ID, Qualification dropdown).
    2.  Notification sent to Trainer who approves/rejects (with justification).
    3.  If approved, training begins.
    4.  When training completes, Trainer inputs new qualification with result.
    5.  Versatility Matrix updated to **"F"**.
    6.  Team Leader manually initiates the certification process when the operator is ready.
    7.  Certification (C) and Recertification (R) follow standard workflow.

<!-- Todo : add the qualification removal workflow details -->

- **Qualification Removal** (handled in the module 1 raise a request, we need to add a node for handlingg versatility matrix update in the workflow engine)
  - **Use Case**: Remove qualification due to FTQ/Customer Complaints.
  - **Parallel Approvals**:
    - **Production Path**: Team Leader → Shift Leader (APP1) → Production Coordinator (APP2).
    - **Quality Path**: Team Leader → Quality Supervisor (APP1) → Quality Coordinator (APP2).
  - **Final Approval**: HR Manager (APP3).
  - **Notifications**: After final validation, notification sent to HR & Production/Cutting managers, Quality manager, and Trainer.
  - **Output**: Versatility Matrix updated automatically.

#### 4. **Individual Performance Evaluation (IPE)** (handled in the module 1)

<!-- need more details on the IPE workflow, status update , verification and validation workflow -->

- **Annual Evaluation**: Conducted periodically (e.g., annually) as per company policy, initiated manually.
- **Template**: Reuses REQ 32/33 certification/validation forms.
- **Integration**: Syncs with Versatility Matrix for status tracking.

---

### **Key Interfaces & Loose Coupling**

To ensure independence and clear responsibilities across submodules:

#### 1. **Inter-Module Interactions & APIs**

- **Crew Management (Module 1):**
  - Receives operator data via Change Feed from Crew Management (handles onboarding, basic data).
  - Consumes information about operator assignments (Trainer → Shift Leader → Team Leader → Team) managed by Crew Management.
- **Skills Matrix API** (Read-only):
  - Fetches skill/process lists for form dropdowns (e.g., "basic process," "key process").
  - Does _not_ modify Skills Matrix data.
- **Versatility Matrix (Module 1 or dedicated service):**
  - Publishes operator status updates (O, LC, V, C, R, F) to be reflected in the Versatility Matrix (e.g., via events or API calls).
  - Training Process does _not_ manage operator-workstation assignments.
- **Validation Submodule (REQ 32/33):**
  - Requests form templates and scoring logic for Validation (V) and Certification (C) phases based on department.
  - Receives results (Score, OK/NOK) from the Validation submodule after evaluation.
- **Workflow Engine (Module 1):**
  - Triggers specific workflows in Module 1 for processes requiring approvals:
    - Certification (C)
    - Recertification (R)
    - Qualification Removal
    - Individual Performance Evaluation (IPE)
  - This module is responsible for initiating these workflows but does not manage the approval steps internally.

#### 2. **Notification Service Integration**

- **Triggered Events**:
  - `TrainingAssigned` (to Trainer).
  - `LCStatusChange` (to Team Leader).
  - `ValidationReady` (to Team Leader, indicating operator can be validated).
  - `CertificationReady` (to Team Leader, indicating operator can be certified).
  - `Module1WorkflowTriggered` (e.g., for Certification, Recertification, QualRemoval, IPE - potentially notifying initiator/relevant parties).
  - `StatusChangeNotification` (to relevant stakeholders upon Versatility Matrix update).
  - `ManualActionRequired` (e.g., if validation/certification fails, notifying TL/SL for manual intervention instead of automated retry/escalation).
- **Payload**: Operator ID, Department, relevant context (e.g., Process, Link to record). (Note: `due_date` removed as timelines are manual).

#### 3. **Logging Service Integration**

- **Events Logged**:
  - Training phase completions (O, LC).
  - Initiation of Validation (V), Certification (C), Recertification (R).
  - Final status updates (V, C, R) received after validation/workflow completion.
  - Qualification requests (New/Removal initiation).
  - IPE initiation.
  - Triggering of Module 1 workflows.

---

### **Avoiding Redundancy with Other Submodules**

1.  **Versatility Matrix**:
    - Training Process provides status updates (O, LC, V, C, R, F) but does not own the matrix itself or workstation mapping.
2.  **Skills Matrix**:
    - Consumes skill definitions; does not create or modify them.
3.  **Crew Management**:
    - Consumes operator data and assignments; does not handle onboarding or hierarchical management.
4.  **Validation Submodule (REQ 32/33)**:
    - Initiates validation/certification but relies on REQ 32/33 for forms, scoring logic, and department-specific rules.
5.  **Workflow Engine (Module 1)**:
    - Triggers approval workflows but does not execute the approval steps (handled by Module 1).
6.  **Notification/Logging**:
    - Generates events/logs related to its processes but relies on central services for delivery/storage.
7.  **Replacement Process**:
    - Does not influence training logic; replacement decisions use Versatility Matrix data.

---

### **Data Models**

#### 1. **Operator Training Record**

```json
{
  "operator_id": "DH-123", // Linked to Crew Management record
  "department": "Assembling",
  "current_training_phase": "C", // Overall status reflecting latest completed/in-progress phase within this module
  "qualifications": [
    {
      "process_id": "PROC-ASY-001", // Linked to Skills Matrix
      "process_name": "Wire Harness Assembly",
      "status": "C", // Status (O, LC, V, C, R, F) from Versatility Matrix
      "last_certification_date": "2024-03-15", // Updated upon C or R completion
      "last_recertification_date": "2024-03-15" // Updated upon R completion
    }
    // Potentially add other relevant qualifications here
  ],
  "training_history": [
    // Log of phase transitions within this module
    {
      "phase": "O",
      "result": "90%", // Score or OK/NOK
      "checklist_url": "/attachments/checklist_123.pdf", // Optional attachment link
      "completed_by_user_id": "Trainer-456",
      "completion_date": "2024-01-10"
    },
    {
      "phase": "LC",
      "result": "OK",
      "checklist_url": "/attachments/checklist_lc_123.pdf",
      "completed_by_user_id": "Trainer-456",
      "completion_date": "2024-02-01"
    },
    {
      "phase": "V", // Validation initiated
      "triggered_by_user_id": "TL-789",
      "initiation_date": "2024-02-15"
      // Result (Score >=80%) comes from REQ 32/33, status updated upon completion
    },
    {
      "phase": "C", // Certification initiated
      "triggered_by_user_id": "TL-789",
      "initiation_date": "2024-03-01"
      // Result (Score >=85%) comes from REQ 32/33, status updated upon Module 1 workflow completion
    }
    // Add entries for R, F phases as needed
  ]
}
```

#### 2. **Training Workflow Configuration**

```yaml
# Removed - Department-specific logic (forms, scoring) handled by REQ 32/33.
# Timelines are manually managed by users.
# Polyvalence levels (Assembling) or Zoning (Cutting) are reflected in Versatility Matrix,
# potentially influenced by data from Skills Matrix, not configured here.
```

---

### **Unique Value Additions of this Submodule**

1.  **Structured Training Progression Orchestration**:
    - Manages the sequence of training phases (O → LC → V → C → R) for operators.
    - Tracks operator progress through these phases.
2.  **Integration Hub**:
    - Acts as a central point connecting Crew Management, Skills Matrix, Versatility Matrix, Validation (REQ 32/33), and Module 1 Workflows for training-related processes.
3.  **Initiation Point for Key Processes**:
    - Triggers Validation, Certification, Recertification, New Qualification Training, Qualification Removal, and IPE processes, leveraging other modules/submodules for execution.
4.  **Manual Process Facilitation**:
    - Provides interfaces and triggers notifications to support the manual initiation and progression of training steps by Trainers and Team Leaders, replacing previous automated timelines.

_(Removed previous points related to internal "Automated Escalation Engine" and department-specific configurations within this module)_

---

### **Conclusion**

The Training Process submodule focuses on orchestrating the operator's journey through defined training phases (O, LC, V, C, R, F). It integrates tightly with other modules:

- Consumes operator data and assignments from **Crew Management**.
- Reads process definitions from the **Skills Matrix**.
- Requests validation/certification forms and scoring from the **Validation Submodule (REQ 32/33)**.
- Triggers approval workflows (Certification, Recertification, Qualification Removal, IPE) in **Module 1**.
- Publishes status updates to the **Versatility Matrix**.

Key characteristics include:

- Manual initiation of all training phases and evaluations by users (Trainers, Team Leaders).
- No internal automated timelines, retries, or escalation logic for validation/certification failures (requires manual intervention).
- Clear separation of concerns, ensuring loose coupling and avoiding redundancy with other system components.

This design supports a standardized yet flexible approach to skill development, empowering users to manage training progression according to operational needs.

---

### Summary of Removed Timelines and Automation

The following timelines, time-based triggers, and related configurations were removed from the Training Process submodule requirements to transition timeline management to manual user actions:

1.  **On Job Training (O) Phase:**

    - Removed department-specific durations (e.g., 5 days for Assembling, 10 days for Cutting/Leadprep).

2.  **Learning Curve (LC) Phase:**

    - Removed automatic notification trigger for trainers to submit LC results after a set period (e.g., 5 days + key process days).
    - Removed department-specific durations (e.g., 5 days for Assembling, 20 days for Cutting/Leadprep).
    - Removed business rule allowing duration extension (as duration is now undefined).

3.  **Validation (V) Phase:**

    - Removed automatic notification trigger for Team Leaders to validate after a set period from LC start (e.g., 10 days).
    - Removed time-based escalation rules (e.g., second notification after 1 week if score < 80%, maximum delay of 4 days twice).

4.  **Certification (C) Phase:**

    - Removed automatic notification trigger for Team Leaders to certify after a set period from V start (e.g., 40 days).
    - Removed time-based escalation rules (e.g., maximum delay of 4 days twice).

5.  **Recertification (R) Phase:**

    - Removed automatic annual trigger based on 12 months after the certification date. Recertification is now manually initiated when required.
    - Removed automatic update of Versatility Matrix status to "R"; it's now updated upon successful manual recertification.

6.  **New Qualification Workflow:**

    - Removed automatic notification trigger for Team Leaders to start certification after a set period (e.g., 5 days) following the update to status "F".

7.  **Individual Performance Evaluation (IPE):**

    - Removed automatic annual trigger based on 12 months after the hiring date. IPE is now manually initiated when required.

8.  **Notification Payloads:**

    - Removed `due_date` field from notification payloads as triggers are manual.

9.  **Data Models:**

    - Removed `recertification_due` field from the `Operator Training Record` JSON example.
    - Removed the entire `Training Workflow Configuration` YAML example, which defined department-specific durations and triggers.

10. **Department-Specific Logic:**

    - Removed the mention of "Configurable Timelines" differing by department.

11. **Unique Value Additions / Conclusion:**
    - References to internal "Automated Escalation Engine" and department-specific configurations within this module were removed or updated to reflect reliance on manual triggers, the Validation Submodule (REQ 32/33), and Module 1 Workflows.

This change centralizes the responsibility for progressing operators through training phases onto the users (Trainers, Team Leaders, etc.) rather than relying on system-defined time limits and automated prompts.
