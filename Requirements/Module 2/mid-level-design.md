# Module 2: Absenteeism and Replacement Management System

## Summary
This document provides a comprehensive overview of Module 2, which manages operator absences and the replacement process. The system is designed to automate and streamline the replacement workflow while ensuring optimal operator-post matching based on skills and qualifications.

### Key Components
- **Core Services**: 5 microservices (Absenteeism, Backup List, Versatility Matrix, Notification, Operator Status)
- **Data Sources**: SQL Database for core data, Workday integration for operator data
- **User Roles**: TL, SL, Coordinator, Manager, Training Team, Nurse
- **Main Features**: Intelligent replacement suggestions, escalation workflow, skill matching

### Technical Stack
- **Backend**: Microservices architecture with event-driven communication
- **Database**: SQL Server for core data, Cosmos DB for no structured data
- **Infrastructure**: Azure Cloud (Kubernetes, Event Bus, API Management)
- **Integration**: Workday (current), TKS/Optitime (future)

### Development Approach
- Phase-based implementation (Foundation → Core Services → Advanced Features → Production)
- Data team preparation critical for initial setup
- Training team responsible for all CRUD operations
- TL exclusive rights for status/absence management

## Table of Contents
1. [Overview](#1-overview)
2. [Functionalities](#2-functionalities)
   - [Core Functionalities](#21-core-functionalities)
3. [Roles and Permissions](#3-roles-and-permissions)
   - [Role Hierarchy](#31-role-hierarchy)
   - [Permission Matrix](#32-permission-matrix)
4. [Services and Communication](#4-services-and-communication)
   - [Core Services](#41-core-services)
5. [Data Sources](#5-data-sources)
   - [Internal Data Sources](#51-internal-data-sources)
   - [External Data Sources](#52-external-data-sources)
6. [Data Model](#6-data-model)
   - [SQL Database Schema](#61-sql-database-schema)
   - [Database Entity Relationship Diagram](#62-database-entity-relationship-diagram)
   - [Data Sources and CRUD Specifications](#63-data-sources-and-crud-specifications)
7. [Use Cases](#7-use-cases)
   - [Data Updates](#71-data-updates)
   - [CRUD Operations](#72-crud-operations)
   - [Replacement Process](#73-replacement-process)
   - [Notifications](#74-notifications)
8. [Microservices Architecture](#8-microservices-architecture)
   - [Core Microservices](#81-core-microservices)
   - [Communication Diagram](#82-microservices-communication-diagram)
   - [Replacement Request Flow](#83-replacement-request-sequence-diagram)
   - [Event-Driven Communication](#84-event-driven-communication-patterns)
   - [Service Integration](#85-service-integration-points)
9. [Release Plan](#9-release-plan)
   - [Data Preparation](#91-data-preparation-phase)
   - [Development Phase](#92-development-phase)
   - [Testing Strategy](#93-testing-strategy)
   - [CI/CD Pipeline](#94-cicd-pipeline)
   - [Infrastructure Requirements](#95-infrastructure-requirements)
   - [Release Phases](#96-release-phases)
   - [Post-Release Support](#97-post-release-support)

## Global Overview Document

## 1. Overview
The Absenteeism and Replacement Management System (Module 2) is designed to manage the replacement process when operators are marked as absent. The system integrates with Module 3 for operator status updates and leverages the Versatility Matrix for intelligent operator-post matching based on skills and experience.

### Key Features
- Intelligent replacement process based on operator qualifications and availability
- Hierarchical escalation workflow (TL → SL → Coord → Manager)
- Integration with Module 3 for operator status tracking
- Medical certificate and sick leave processing
- Integration with external systems (Workday, NEXT: TKS/Optitime)
- Comprehensive audit trail and reporting

## 2. Functionalities

### 2.1 Core Functionalities
1. **Replacement Process Management**
   - Receive absence notifications from Module 3
   - Generate qualified replacement suggestions (3 operators)
   - Prioritize appropriately qualified operators over overqualified ones
   - Team-based search hierarchy
   - Automated escalation workflow

2. **Replacement Criteria**
   - Check operator current status (present/absent)
   - Verify skill qualifications match requirements
   - Validate experience requirements
   - Prioritize operators with matching qualifications over overqualified operators
   - Restrict search to appropriate team scope

3. **Medical Workflow**
   - Process medical certificates
   - Manage sick leave durations
   - Track follow-up appointments
   - Synchronize medical updates

4. **Notifications and Alerts**
   - Escalation notifications to SL/Coord
   - Manager email and web notifications
   - Critical situation alerts

## 3. Roles and Permissions

### 3.1 Role Hierarchy
1. **Team Leader (TL)**
   - Exclusive right to update operator status
   - Exclusive right to record absences
   - Select replacements from backup list
   - Handle low-severity situations
   - Provide replacement feedback

2. **Shift Leader (SL)**
   - Handle medium-severity escalations
   - Override replacement decisions if needed
   - Manage cross-team replacements

3. **Coordinator (Coord)**
   - Handle high-severity situations
   - Make strategic replacement decisions
   - Override SL decisions if needed

4. **Plant Manager**
   - Handle critical situations
   - Make strategic decisions
   - Access all system features

5. **Training Team**
   - Manage all CRUD operations for Skills
   - Manage all CRUD operations for PositionSkillRequirements
   - Manage CRUD operations for Positions
   - Access to bulk import/export functionality
   - Maintain system master data

6. **Nurse**
   - Process medical certificates
   - Manage sick leave records
   - Schedule follow-up appointments

### 3.2 Permission Matrix
| Feature                    | TL  | SL  | Coord | Manager | Training | Nurse |
|---------------------------|-----|-----|--------|---------|----------|-------|
| Update Status/Set Absence | ✓   | -   | -      | -       | -        | -     |
| Select Replacement        | ✓   | ✓   | ✓      | ✓       | -        | -     |
| Override Decisions        | -   | ✓   | ✓      | ✓       | -        | -     |
| Process Medical Docs      | -   | -   | -      | -       | -        | ✓     |
| Skills CRUD              | -   | -   | -      | -       | ✓        | -     |
| Positions CRUD           | -   | -   | -      | -       | ✓        | -     |
| Requirements CRUD        | -   | -   | -      | -       | ✓        | -     |
| Bulk Import/Export       | -   | -   | -      | -       | ✓        | -     |

## 4. Services and Communication

### 4.1 Core Services
1. **Absenteeism Service**
   - Processes absence notifications from Module 3
   - Manages replacement workflow
   - Handles escalation process
   - Tracks replacement decisions

2. **Backup List Service**
   - **Responsibilities:**
     * Generate qualified replacement suggestions
     * Apply qualification matching algorithms
     * Validate operator availability
     * Enforce team scope restrictions
   - **Key Endpoints:**
     * GET `/api/backup-list/{absenceId}` - Get replacement suggestions
     * GET `/api/backup-list/validate/{operatorId}/{positionId}` - Validate operator-position match

3. **Versatility Matrix Service**
   - **Responsibilities:**
     * Manage skills and qualifications data
     * Handle position requirements
     * Provide skill matching algorithms
     * Support CRUD operations for Training team
   - **Key Endpoints:**
     * GET `/api/matrix/operators/{id}/skills` - Get operator skills
     * GET `/api/matrix/positions/{id}/requirements` - Get position requirements
     * POST `/api/matrix/bulk-import` - Handle CSV imports
     * GET `/api/matrix/export` - Generate CSV exports

4. **Nurse Workflow Service**
   - Processes medical documents
   - Manages sick leave records
   - Tracks follow-ups

5. **Notification Service**
   - Sends real-time alerts
   - Manages subscriptions
   - Handles email notifications

6. **Operator Status Service**
   - **Responsibilities:**
     * Track current operator status (present/absent)
     * Maintain team member status lists
     * Provide status validation for replacement search
     * Track historical status changes
   - **Key Endpoints:**
     * GET `/api/status/operator/{id}` - Get operator's current status
     * GET `/api/status/team/{tlId}` - Get status of all team members
     * GET `/api/status/absences/{tlId}` - Get active absences in team
     * GET `/api/status/available/{teamId}` - Get available operators for replacement


## 5. Data Sources

### 5.1 Internal Data Sources
1. **SQL Database**
   - Versatility Matrix data
   - Operator skills and certifications
   - Position requirements
   - Absence records

### 5.2 External Data Sources
1. **Workday**
   - Operator profiles
   - Job history
   - Experience levels
   - Organizational structure

2. **(NEXT): TKS/Optitime**
   - Scheduling data
   - Time tracking
   - Attendance records

## 6. Data Model

### 6.1 SQL Database Schema

```sql
-- Operators table (YELLOW - Workday Data)
-- No CRUD operations allowed - Data managed by Data Team via Workday
CREATE TABLE Operators (
    operator_id VARCHAR(50) PRIMARY KEY,
    operator_code VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    shift_id VARCHAR(50),
    department_id VARCHAR(50),
    supervisor_n1_id VARCHAR(50),    -- N+1 supervisor (TL) - from Workday
    supervisor_n2_id VARCHAR(50),    -- N+2 supervisor (SL) - from Workday
    supervisor_n3_id VARCHAR(50),    -- N+3 supervisor (Coord) - from Workday
    current_status VARCHAR(20) NOT NULL,  -- present, absent
    current_substatus VARCHAR(20) NOT NULL,  -- on_post, available, reserve
    is_active BIT DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    created_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT GETDATE(),
    updated_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_Operators_Shift FOREIGN KEY (shift_id) REFERENCES Shifts(shift_id),
    CONSTRAINT FK_Operators_Department FOREIGN KEY (department_id) REFERENCES Departments(department_id),
    CONSTRAINT CHK_Status CHECK (current_status IN ('present', 'absent')),
    CONSTRAINT CHK_Substatus CHECK (current_substatus IN ('on_post', 'available', 'reserve'))
);

-- Skills table (GREEN - CW CRUD)
-- Full CRUD operations available via Module 2 interface
CREATE TABLE Skills (
    skill_id VARCHAR(50) PRIMARY KEY,
    skill_code VARCHAR(20) UNIQUE NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    is_active BIT DEFAULT 1,
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    created_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT GETDATE(),
    updated_by VARCHAR(50) NOT NULL
);

-- Positions table (RED - Mixed Source)
-- Initial data from Workday, updatable via Module 2 CRUD
CREATE TABLE Positions (
    position_id VARCHAR(50) PRIMARY KEY,
    position_code VARCHAR(20) UNIQUE NOT NULL,
    position_name VARCHAR(100) NOT NULL,
    department_id VARCHAR(50),
    criticality_level CHAR(1) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    created_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT GETDATE(),
    updated_by VARCHAR(50) NOT NULL,
    CONSTRAINT CHK_Position_Criticality CHECK (criticality_level IN ('C', 'M', 'N')),
    CONSTRAINT FK_Positions_Department FOREIGN KEY (department_id) REFERENCES Departments(department_id)
);

-- Operator Skills (YELLOW - Workday Data)
-- No direct CRUD - Data managed by Data Team
CREATE TABLE OperatorSkills (
    operator_id VARCHAR(50),
    skill_id VARCHAR(50),
    skill_level CHAR(1) NOT NULL,
    certification_date DATE,
    expiry_date DATE,
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    created_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT GETDATE(),
    updated_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_OperatorSkills PRIMARY KEY (operator_id, skill_id),
    CONSTRAINT FK_OperatorSkills_Operator FOREIGN KEY (operator_id) REFERENCES Operators(operator_id),
    CONSTRAINT FK_OperatorSkills_Skill FOREIGN KEY (skill_id) REFERENCES Skills(skill_id),
    CONSTRAINT CHK_Skill_Level CHECK (skill_level IN ('F', 'C', 'R'))
);

-- Position Skill Requirements (GREEN - CW CRUD)
-- Full CRUD operations available via Module 2 interface
CREATE TABLE PositionSkillRequirements (
    position_id VARCHAR(50),
    skill_id VARCHAR(50),
    criticality_level CHAR(1) NOT NULL,
    minimum_level CHAR(1) NOT NULL,
    required_experience_months INT,
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    created_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT GETDATE(),
    updated_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_PositionSkillRequirements PRIMARY KEY (position_id, skill_id),
    CONSTRAINT FK_PositionSkillRequirements_Position FOREIGN KEY (position_id) REFERENCES Positions(position_id),
    CONSTRAINT FK_PositionSkillRequirements_Skill FOREIGN KEY (skill_id) REFERENCES Skills(skill_id),
    CONSTRAINT CHK_Requirement_Criticality CHECK (criticality_level IN ('C', 'M', 'N')),
    CONSTRAINT CHK_Minimum_Level CHECK (minimum_level IN ('F', 'C', 'R'))
);

-- Operator Position History (YELLOW - Workday Data)
-- No CRUD operations allowed - Data managed by Data Team
CREATE TABLE OperatorPositionHistory (
    history_id VARCHAR(50) PRIMARY KEY,
    operator_id VARCHAR(50) NOT NULL,
    position_id VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    occupancy_level CHAR(1) NOT NULL,
    performance_rating DECIMAL(3,2),
    comments TEXT,
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    created_by VARCHAR(50) NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT GETDATE(),
    updated_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_PositionHistory_Operator FOREIGN KEY (operator_id) REFERENCES Operators(operator_id),
    CONSTRAINT FK_PositionHistory_Position FOREIGN KEY (position_id) REFERENCES Positions(position_id),
    CONSTRAINT CHK_Occupancy_Level CHECK (occupancy_level IN ('X', '1', '2', '3', '4'))
);
```

### 6.2 Database Entity Relationship Diagram

```mermaid
erDiagram
    %% Color coding:
    %% Yellow - Workday Data (no CRUD)
    %% Green - CW CRUD enabled
    %% Red - Mixed source

    Operators ||--o{ OperatorSkills : has
    Operators ||--o{ OperatorPostHistory : works_in

    Skills ||--o{ OperatorSkills : assigned_to
    Skills ||--o{ PostSkillRequirements : required_for
    Posts ||--o{ PostSkillRequirements : requires
    Posts ||--o{ OperatorPostHistory : filled_by

    %% Yellow - Workday Data
    Operators {
        varchar operator_id PK
        varchar operator_code UK
        varchar first_name
        varchar last_name
        varchar shift_id FK
        varchar department_id FK
        varchar supervisor_n1_id FK
        varchar supervisor_n2_id FK
        varchar supervisor_n3_id FK
        varchar current_status
        varchar current_substatus
        bit is_active
    }

    %% Green - CW CRUD
    Skills {
        varchar skill_id PK
        varchar skill_code UK
        varchar skill_name
        text description
        varchar category
        bit is_active
    }

    %% Red - Mixed Source
    Posts {
        varchar post_id PK
        varchar post_code UK
        varchar post_name
        varchar department_id FK
        char criticality_level
        varchar status
    }

    %% Yellow - Workday Data
    OperatorSkills {
        varchar operator_id PK,FK
        varchar skill_id PK,FK
        char skill_level
        date certification_date
        date expiry_date
    }

    %% Green - CW CRUD
    PostSkillRequirements {
        varchar post_id PK,FK
        varchar skill_id PK,FK
        char criticality_level
        char minimum_level
        int required_experience_months
    }

    %% Yellow - Workday Data
    OperatorPostHistory {
        varchar history_id PK
        varchar operator_id FK
        varchar post_id FK
        date start_date
        date end_date
        char occupancy_level
        decimal performance_rating
    }
```

### 6.3 Data Sources and CRUD Specifications

#### Workday Data (Yellow)
1. **Operators Table**
   - All data managed by Data Team via Workday
   - Status/Absence updates exclusively by TL
   - No other CRUD operations allowed in CW application
   - Includes hierarchical supervision structure (N+1, N+2, N+3)

2. **OperatorPositionHistory**
   - Historical position assignments
   - Performance data
   - Occupancy levels
   - No direct CRUD operations

#### CW Application Data (Green)
1. **Skills**
   - Full CRUD operations via Module 2 interface
   - Access restricted to Training Team role
   - Bulk import/export via CSV
   - CSV Format:
     ```csv
     skill_code,skill_name,description,category
     SK001,Electrical Inspection,Basic electrical inspection,Technical
     SK002,Quality Control,Quality control procedures,Quality
     ```

2. **PositionSkillRequirements**
   - Full CRUD operations via Module 2 interface
   - Access restricted to Training Team role
   - Bulk import/export via CSV
   - CSV Format:
     ```csv
     position_code,skill_code,criticality_level,minimum_level,required_experience_months
     POSITION001,SK001,C,F,6
     POSITION001,SK002,M,C,12
     ```

#### Mixed Source Data (Red)
1. **Positions**
   - Initial data from Workday (Data Team)
   - CRUD operations restricted to Training Team role
   - Bulk import/export via CSV
   - CSV Format:
     ```csv
     position_code,position_name,department_code,criticality_level
     POSITION001,Quality Inspector,DEPT001,C
     POSITION002,Line Operator,DEPT002,M
     ```

## 7. Use Cases

### 7.1 Data Updates
1. **Skills Update**
   - Via CSV import
   - Triggered by training completion
   - Manual updates by authorized users

2. **Position Updates**
   - Initial sync with Workday
   - Training team CRUD operations
   - Bulk updates via CSV

3. **Operator Updates**
   - Synchronization with Workday
   - Status updates from CW platform
   - Manual updates by TL/SL

### 7.2 CRUD Operations
1. **Operators Status/Absence**
   - Update Status: TL only
   - Set Absence: TL only
   - Read: All roles
   - Other operations: No direct CRUD (managed by Data Team)

2. **Skills Management**
   - Create: Training Team
   - Read: All roles
   - Update: Training Team
   - Delete: Training Team
   - Bulk Operations: Training Team

3. **Positions Management**
   - Create: Training Team
   - Read: All roles
   - Update: Training Team
   - Delete: Training Team
   - Bulk Operations: Training Team

4. **PositionSkillRequirements Management**
   - Create: Training Team
   - Read: All roles
   - Update: Training Team
   - Delete: Training Team
   - Bulk Operations: Training Team

### 7.3 Replacement Process
1. **Initial Replacement Flow**
   - System receives absence notification from Module 3
   - TL initiates replacement request
   - System checks current team members:
     * Must be present (status from Module 3)
     * Must have required skills/experience
     * Prioritize operators whose qualifications match requirements
     * Limited to TL's team scope
   - System suggests 3 qualified operators
   - TL selects replacement or escalates

2. **Escalation Flow**
   - TL chooses to escalate (no suitable replacement)
   - SL receives notification
   - System expands search to SL's team scope
   - Same qualification checks but broader scope
   - If no replacement found, escalates to Coord
   - Manager receives final notification (email + web)
   - No automatic system action at manager level

3. **Operator Qualification Priority**
   - System first searches for operators whose qualifications match the requirements ("normal" operators)
   - Only suggests overqualified ("best") operators if no matching operators found
   - Overqualified operators are those whose skill levels significantly exceed the position requirements
   - Maintains this qualification-based priority through all escalation levels
   - This prioritization helps optimize resource allocation by avoiding assignment of overqualified operators to positions where their additional skills aren't required

### 7.4 Notifications
1. **Email Notifications**
   - Absence alerts
   - Escalation notices
   - Critical situations
   - Follow-up reminders

2. **System Notifications**
   - Real-time alerts
   - Dashboard updates
   - Status changes
   - Task assignments 

## 8. Microservices Architecture

### 8.1 Core Microservices

1. **Absenteeism Service**
   - **Responsibilities:**
     * Process absence notifications from TL
     * Manage replacement workflow
     * Handle escalation process
     * Track replacement decisions
     * Maintain absence history
   - **Key Endpoints:**
     * POST `/api/absences` - Record new absence
     * PUT `/api/absences/{id}/escalate` - Escalate to next level
     * POST `/api/absences/{id}/replacement` - Assign replacement
     * GET `/api/absences/active` - Get active absences

2. **Backup List Service**
   - **Responsibilities:**
     * Generate qualified replacement suggestions
     * Apply qualification matching algorithms
     * Validate operator availability
     * Enforce team scope restrictions
   - **Key Endpoints:**
     * GET `/api/backup-list/{absenceId}` - Get replacement suggestions
     * GET `/api/backup-list/validate/{operatorId}/{positionId}` - Validate operator-position match

3. **Versatility Matrix Service**
   - **Responsibilities:**
     * Manage skills and qualifications data
     * Handle position requirements
     * Provide skill matching algorithms
     * Support CRUD operations for Training team
   - **Key Endpoints:**
     * GET `/api/matrix/operators/{id}/skills` - Get operator skills
     * GET `/api/matrix/positions/{id}/requirements` - Get position requirements
     * POST `/api/matrix/bulk-import` - Handle CSV imports
     * GET `/api/matrix/export` - Generate CSV exports

4. **Notification Service**
   - **Responsibilities:**
     * Subscribe to event bus for notification events
     * Handle notification routing and delivery
     * Manage notification templates
     * Track notification status
   - **Events Handled:**
     * `absence.created`
     * `absence.escalated`
     * `replacement.assigned`
     * `replacement.needed`

5. **Operator Status Service**
   - **Responsibilities:**
     * Track current operator status (present/absent)
     * Maintain team member status lists
     * Provide status validation for replacement search
     * Track historical status changes
   - **Key Endpoints:**
     * GET `/api/status/operator/{id}` - Get operator's current status
     * GET `/api/status/team/{tlId}` - Get status of all team members
     * GET `/api/status/absences/{tlId}` - Get active absences in team
     * GET `/api/status/available/{teamId}` - Get available operators for replacement

### 8.2 Microservices Communication Diagram

```mermaid
graph TB
    subgraph Frontend
        UI[Web Interface]
    end

    subgraph Gateway
        API[API Gateway]
    end

    subgraph Core Services
        ABS[Absenteeism Service]
        BLS[Backup List Service]
        VMS[Versatility Matrix Service]
        NS[Notification Service]
        OSS[Operator Status Service]
    end

    subgraph Message Bus
        ESB[Event Service Bus]
    end

    %% Frontend to Gateway
    UI --> API

    %% Gateway to Services
    API --> ABS
    API --> BLS
    API --> VMS
    API --> OSS

    %% Inter-service Communication
    ABS --> ESB
    BLS --> ESB
    VMS --> ESB
    NS --> ESB
    OSS --> ESB

    %% Service Dependencies
    BLS --> VMS
    BLS --> OSS
    ABS --> BLS
    NS -.-> ESB

    %% Styling
    classDef gateway fill:#f9f,stroke:#333,stroke-width:2px
    classDef service fill:#bbf,stroke:#333,stroke-width:1px
    
    class API gateway
    class ABS,BLS,VMS,NS,OSS service
```

### 8.3 Replacement Request Sequence Diagram

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Gateway
    participant ABS as Absenteeism Service
    participant BLS as Backup List Service
    participant VMS as Versatility Matrix Service
    participant OSS as Operator Status Service
    participant ESB as Event Service Bus
    participant NS as Notification Service

    TL->>+API: Request Replacement
    API->>+ABS: POST /api/absences/{id}/replacement
    
    ABS->>+BLS: Get Replacement Suggestions
    BLS->>+VMS: Get Skills Requirements
    VMS-->>-BLS: Return Position Requirements
    BLS->>+OSS: Get Available Operators
    OSS-->>-BLS: Return Present Operators
    BLS->>+VMS: Get Qualified Operators
    VMS-->>-BLS: Return Qualified Operators
    
    BLS->>BLS: Apply Matching Algorithm
    Note over BLS: Filter by:<br/>1. Present Status<br/>2. Required Skills<br/>3. Team Scope<br/>4. Qualification Priority
    
    BLS-->>-ABS: Return Top 3 Suggestions
    
    alt Suggestions Found
        ABS-->>API: Return Suggestions
        API-->>TL: Display Suggestions
        ABS->>ESB: Publish replacement.suggestions_ready
        ESB->>NS: Notify TL
    else No Suggestions
        ABS->>ESB: Publish absence.escalated
        ESB->>NS: Notify SL
        ABS-->>API: Return Escalation Status
        API-->>TL: Show Escalation Message
    end
```

### 8.4 Event-Driven Communication Patterns

1. **Absence Management Events**
   - `absence.created` - New absence recorded
   - `absence.escalated` - Absence escalated to next level
   - `replacement.assigned` - Replacement operator assigned
   - `replacement.suggestions_ready` - Replacement suggestions available
   - `replacement.completed` - Replacement process completed

2. **Status Events**
   - `status.changed` - Operator status changed
   - `status.team.updated` - Team status list updated
   - `status.absence.recorded` - New absence status recorded

3. **Notification Events**
   - `notification.escalation` - Escalation notification needed
   - `notification.critical` - Critical situation alert
   - `notification.delivered` - Notification successfully delivered

4. **Matrix Events**
   - `matrix.updated` - Versatility matrix data updated
   - `bulk.import.completed` - Bulk data import finished

### 8.5 Service Integration Points

1. **Internal Integration**
   - Backup List Service depends on:
     * Versatility Matrix Service for skill matching
     * Operator Status Service for availability checking
   - Absenteeism Service uses Backup List Service for replacement suggestions
   - All services communicate via Event Service Bus
   - Notification Service subscribes to relevant events for notification routing

2. **Event Bus Integration**
   - All services publish domain events to Event Service Bus
   - Notification Service subscribes to all notification-worthy events
   - Event-driven architecture ensures loose coupling
   - Asynchronous communication for better scalability 

## 9. Release Plan

### 9.1 Data Preparation Phase

1. **Data Team Tasks**
   - Extract and validate operator data from Workday
     * Basic operator information
     * Hierarchical structure (N+1, N+2, N+3)
     * Team assignments
     * Current roles and positions
   - Prepare initial skills matrix data
     * Current operator skills
     * Certification levels
     * Historical data
   - Setup data validation rules
   - Prepare data migration scripts

2. **Data Quality Checks**
   - Validate supervisor hierarchies
   - Verify team structures
   - Check skill assignments
   - Validate post requirements
   - Test data consistency

3. **Change Detection System**
   - Setup Workday change detection
   - Define change propagation rules
   - Implement data sync validation
   - Setup monitoring for data sync issues

### 9.2 Development Phase

1. **Microservices Development**
   - Priority Order:
     1. Operator Status Service (dependency for others)
     2. Versatility Matrix Service
     3. Backup List Service
     4. Absenteeism Service
     5. Notification Service

2. **Service Dependencies**
   ```mermaid
   graph TD
       OSS[Operator Status Service]
       VMS[Versatility Matrix Service]
       BLS[Backup List Service]
       ABS[Absenteeism Service]
       NS[Notification Service]
       ESB[Event Service Bus]
       DB[(Database SQL)]
       NOSQLDB[(Cosmos DB)]
       
       OSS --> DB
       OSS --> NOSQLDB
       VMS --> DB
       BLS --> OSS
       BLS --> VMS
       ABS --> BLS
       NS --> ESB
       
       classDef primary fill:#f9f,stroke:#333
       classDef secondary fill:#bbf,stroke:#333
       class OSS,VMS primary
       class BLS,ABS,NS secondary
   ```

3. **Frontend Development**
   - **Training Team Interface**
     * Skills management dashboard
     * Bulk import/export interface
     * Data validation views
     * Audit logging interface

   - **TL Interface**
     * Absence management dashboard
     * Replacement suggestion interface
     * Team status overview
     * Escalation management

   - **SL/Coord Interface**
     * Escalation dashboard
     * Cross-team replacement view
     * Performance monitoring

   - **Common Components**
     * Authentication module
     * Notification center
     * Audit trail viewer
     * Report generator

### 9.3 Testing Strategy

1. **Unit Testing**
   - Service-level unit tests
   - Component testing
   - Data access layer testing
   - Business logic validation

2. **Integration Testing**
   - Service interaction tests
   - Event bus communication
   - Data sync validation

3. **End-to-End Testing**
   - Complete workflow testing
   - UI/UX validation
   - Performance testing
   - Load testing

4. **Data Migration Testing**
   - Initial load testing
   - Change detection validation
   - Rollback procedures
   - Data consistency checks

### 9.4 CI/CD Pipeline

1. **Build Pipeline**
   ```yaml
   stages:
     - build
     - test
     - quality
     - package
     - deploy-dev
     - deploy-qa
     - deploy-prod

   services:
     - microservices-build
     - frontend-build
     - test-execution
     - sonar-analysis
     - container-build
     - deployment
   ```

2. **Deployment Strategy**
   - Blue-Green deployment
   - Canary releases for critical services
   - Feature flags for gradual rollout
   - Automated rollback capability

3. **Monitoring Setup**
   - Service health monitoring
   - Performance metrics
   - Error tracking
   - User activity monitoring
   - Data sync monitoring

### 9.5 Infrastructure Requirements

1. **Kubernetes Cluster**
   - Production cluster
   - QA/Staging cluster
   - Development cluster
   - Monitoring tools

2. **Database Infrastructure**
   - SQL Server
   - Cosmos DB
   - Backup solutions
   - Disaster recovery setup

3. **Message Bus**
   - Azue Event bus
   - Message persistence
   - Dead letter queues
   - Message monitoring

4. **Security Infrastructure**
   - API Management Gateway
   - SSL certificates
   - Network policies

### 9.6 Release Phases

1. **Phase 1: Foundation**
   - Core database setup
   - Data team preparations
   - Initial data migration
   - Basic service infrastructure

2. **Phase 2: Core Services**
   - Deploy core microservices
   - Setup service communication
   - Basic frontend functionality
   - Initial testing

3. **Phase 3: Advanced Features**
   - Full frontend deployment
   - Integration testing
   - Performance optimization
   - User acceptance testing

4. **Phase 4: Production Release**
   - Production deployment
   - Monitoring setup
   - User training
   - Support documentation

### 9.7 Post-Release Support

1. **Monitoring**
   - Service health
   - Performance metrics
   - Error rates
   - User feedback

2. **Maintenance**
   - Regular updates
   - Security patches
   - Performance optimization
   - Bug fixes

3. **Documentation**
   - User guides
   - API documentation
   - Troubleshooting guides
   - Release notes 