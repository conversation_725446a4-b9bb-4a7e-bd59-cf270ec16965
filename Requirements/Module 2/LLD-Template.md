# Template Validation Microservice - Low-Level Design (LLD)

## Document Information

**Version:** 1.2.0  
**Last Updated:** 2025-05-02  
**Status:** Draft  
**Authors: <AUTHORS>

## Executive Summary

This document details the low-level design for the Template Validation microservice, which provides dynamic, configurable evaluation templates (REQ 32/33) used for operator validation, certification, and performance evaluation. The microservice implements a flexible schema system that supports template customization without code changes, automated score calculation, and integration with the broader Training Process ecosystem. The service follows domain-driven design principles and RESTful architecture to provide template management, form generation, and validation scoring.

Templates use a rating-based system (using 1-4 scale criteria) with a weighted average scoring method.

### Key Features

- Dynamic template generation for different evaluation types (Validation, Certification, IPE)
- Configurable evaluation criteria with customizable categories and scoring
- Automated calculation engine enforcing business rules (minimum thresholds, flagging '1' ratings)
- Multi-evaluator support (Team Leader and Trainer evaluations)
- Complete audit trail of all template changes and evaluation results
- Integration with Training Process microservice and Versatility Matrix

### Key Integration Points

- **Training Process**: Provides templates and scoring for validation/certification processes
- **Versatility Matrix**: Receives status updates based on validation/certification outcomes
- **User Management**: Authenticates and authorizes users (Team Leaders, Trainers, Admins)

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture](#2-architecture)
3. [Data Models](#3-data-models)
4. [API Specification](#4-api-specification)
5. [Business Logic](#5-business-logic)
6. [Integration Patterns](#6-integration-patterns)
7. [Cross-Cutting Concerns](#7-cross-cutting-concerns)
8. [Implementation Considerations](#8-implementation-considerations)
9. [Open Questions and Risks](#9-open-questions-and-risks)

---

## 1. Overview

### 1.1 Purpose and Scope

The Template Validation microservice manages evaluation templates for assessing operator skills during validation, certification, and performance evaluation processes. It provides configurable templates, scoring algorithms, and business rules that ensure consistent evaluation across departments.

**In Scope:**

- Template management (CRUD operations for templates, categories, criteria)
- Dynamic form generation based on template configurations
- Multi-evaluator support for training assessment
- Automated calculation of evaluation scores
- Enforcement of business rules (minimum thresholds, retraining flags)
- Storage of evaluation results and audit history
- Integration with Training Process microservice

**Key Design Constraints:**

- All templates use the RATING criterion type (1-4 scale)
- The scoring method used is WEIGHTED_AVERAGE for all templates

**Out of Scope:**

- User management and authentication (uses central identity service)
- Workflow orchestration (handled by Training Process microservice)
- Notification delivery (handled by central Notification Service)
- Operator data management (handled by Crew Management)

### 1.2 Domain Context Diagram

```mermaid
graph TD
    subgraph Template Validation Microservice
        direction LR
        TV_Core[Template Core Logic]
        TV_Calc[Calculation Engine]
        TV_DB[(Database)]
        TV_API[API Layer]

        TV_API -- Commands --> TV_Core
        TV_Core -- Store/Retrieve --> TV_DB
        TV_Core -- Calculate Scores --> TV_Calc
        TV_DB -- Read Templates --> TV_API
    end

    TrainingProcess[Training Process Service] -- Request Templates/Score --> TV_API
    TV_API -- Templates/Scoring Results --> TrainingProcess

    Admin[Admin Users] -- Template Management --> TV_API
    Evaluators[Team Leaders/Trainers] -- Submit Evaluations --> TV_API

    IdentityService[Identity Service] -- Authentication --> TV_API
```

### 1.3 Key Capabilities

1. **Template Management**: Create, update, and version control evaluation templates
2. **Dynamic Form Generation**: Generate UI-friendly template definitions for rendering forms
3. **Multi-Evaluator Support**: Enable separate evaluations from Team Leader and Trainer
4. **Calculation Engine**: Compute scores based on template-defined algorithms
5. **Business Rules Enforcement**: Apply validation rules and thresholds
6. **Results Storage**: Maintain evaluation history and audit trail
7. **API Integration**: Provide templates and scoring to Training Process microservice

---

## 2. Architecture

### 2.1 Architectural Patterns

The Template Validation microservice implements the following architectural patterns:

1. **Domain-Driven Design**: Focuses on the core domain concepts of templates and evaluations
2. **RESTful Architecture**: Exposes HTTP-based API for template and evaluation operations
3. **Repository Pattern**: Abstracts data access for domain entities
4. **Factory Pattern**: For template and form generation
5. **Strategy Pattern**: For applying different business rules based on template type

### 2.2 High-Level Component Diagram

```mermaid
graph TD
    Client[Client Applications] --> APILyr[API Layer]

    subgraph Template Validation Microservice
        APILyr --> TemplateController[Template Controller]
        APILyr --> EvaluationController[Evaluation Controller]
        APILyr --> ScoringController[Scoring Controller]

        TemplateController --> TemplateSvc[Template Service]
        EvaluationController --> EvaluationSvc[Evaluation Service]
        ScoringController --> ScoringSvc[Scoring Service]

        TemplateSvc --> TemplateRepo[Template Repository]
        EvaluationSvc --> EvaluationRepo[Evaluation Repository]
        ScoringSvc --> CalcEngine[Calculation Engine]

        TemplateRepo --> DB[(Database)]
        EvaluationRepo --> DB

        CalcEngine --> RuleEngine[Business Rule Engine]
    end

    TrainingSvc[Training Process Service] --> APILyr
```

### 2.3 Technology Stack

- **Framework**: NestJS (Node.js TypeScript framework)
- **Database**: MongoDB (for flexible schema support)
- **API Layer**: RESTful API with JSON
- **Authentication**: JWT with Azure AD integration
- **Documentation**: OpenAPI/Swagger
- **Testing**: Jest, Supertest
- **Monitoring**: Prometheus, Grafana
- **Containerization**: Docker
- **Hosting**: Azure Container Apps or Kubernetes

### 2.4 Component Details

#### 2.4.1 Template Module

**Responsibility**: Manages template definitions, categories, criteria, and versioning.

**Key Components**:

- Template Repository: CRUD operations for templates
- Template Factory: Creates template definitions from configurations
- Template Validator: Ensures templates meet requirements (completeness, structure, etc.)

  ```typescript
  // Example validation rules for template structure
  function validateTemplateStructure(
    template: CreateTemplateDto
  ): ValidationResult {
    // Ensure scoringMethod is WEIGHTED_AVERAGE
    if (template.scoringMethod !== "WEIGHTED_AVERAGE") {
      return {
        isValid: false,
        errors: ["Templates must use WEIGHTED_AVERAGE scoring method"],
      };
    }

    // Ensure all categories have at least one criterion
    for (const category of template.categories) {
      if (!category.criteria || category.criteria.length === 0) {
        return {
          isValid: false,
          errors: [
            `Category '${category.name}' must have at least one criterion`,
          ],
        };
      }

      // Ensure all criteria have complete rating descriptions
      for (const criterion of category.criteria) {
        if (!criterion.ratingDescriptions) {
          return {
            isValid: false,
            errors: [
              `Criterion '${criterion.description}' must have rating descriptions for all levels (1-4)`,
            ],
          };
        }

        // Check that all rating levels are defined
        const levels = ["1", "2", "3", "4"];
        for (const level of levels) {
          if (!criterion.ratingDescriptions[level]) {
            return {
              isValid: false,
              errors: [
                `Criterion '${criterion.description}' missing description for rating level ${level}`,
              ],
            };
          }
        }
      }
    }

    return { isValid: true };
  }
  ```

**Key Interfaces**:

- `ITemplateRepository`: Data access for templates
- `ITemplateService`: Business logic for template management
- `ITemplateFactory`: Template creation and transformation

#### 2.4.2 Evaluation Module

**Responsibility**: Handles evaluation submissions, validations, and storage.

**Key Components**:

- Evaluation Repository: Stores evaluation results
- Evaluation Validator: Validates evaluation submissions
- Multi-Evaluator Reconciler: Handles multiple evaluator inputs

**Key Interfaces**:

- `IEvaluationRepository`: Data access for evaluations
- `IEvaluationService`: Business logic for evaluation submission
- `IMultiEvaluatorService`: Reconciliation of multiple evaluator inputs

#### 2.4.3 Calculation Engine

**Responsibility**: Performs score calculations and applies business rules.

**Key Components**:

- Score Calculator: Implements the weighted average scoring method for rating-based templates (1-4 scale)
- Business Rule Engine: Applies thresholds and special rules
- Result Processor: Generates final evaluation results

**Key Interfaces**:

- `ICalculationService`: Score calculation logic
- `IBusinessRuleEngine`: Rule application and enforcement
- `IResultProcessor`: Final evaluation result generation

#### 2.4.4 API Layer

**Responsibility**: Exposes RESTful endpoints for template and evaluation operations.

**Key Controllers**:

- Template Controller: Template CRUD operations
- Evaluation Controller: Submission and retrieval of evaluations
- Scoring Controller: On-demand scoring and rule checking

**Middleware**:

- Authentication: JWT validation
- Authorization: Role-based access control
- Validation: Request payload validation
- Error Handling: Consistent error responses
- Logging: Request/response logging

---

## 3. Data Models

### 3.1 MongoDB Collections

#### 3.1.1 Templates Collection

Stores template definitions with categories, criteria, and rules.

```typescript
// Template
interface Template {
  _id: ObjectId; // Unique identifier
  name: string; // Template name
  description: string; // Template description
  type: "VALIDATION" | "CERTIFICATION" | "IPE"; // Template type
  version: string; // Semantic version
  isActive: boolean; // Whether template is active
  department: string; // Department this template applies to
  processes: string[]; // Processes this template evaluates
  skillType: "BASIC" | "KEY"; // Whether this is a basic or key skill
  scoringMethod: "WEIGHTED_AVERAGE"; // Method used for scoring
  categories: Category[]; // Categories in this template
  minThreshold: number; // Minimum score threshold (e.g., 80 for validation)
  maxPossibleScore: number; // Maximum possible score
  createdBy: string; // User who created the template
  createdAt: Date; // Creation timestamp
  updatedBy: string; // User who last updated the template
  updatedAt: Date; // Last update timestamp
}

// Category
interface Category {
  id: string; // Unique identifier within template
  name: string; // Category name
  order: number; // Display order
  criteria: Criterion[]; // Criteria in this category
}

// Criterion
interface Criterion {
  id: string; // Unique identifier within category
  description: string; // Criterion description
  order: number; // Display order
  ratingDescriptions: {
    // Descriptions for each rating level
    "1": string;
    "2": string;
    "3": string;
    "4": string;
  };
  weight: number; // Weight of this criterion in scoring (default: 1)
  applicableTo: ("VALIDATION" | "CERTIFICATION" | "IPE")[]; // Which evaluation types this applies to
}
```

Example Document:

```json
{
  "_id": "60a12c5e2f7d8b1c98765432",
  "name": "Assembly Line Validation Template",
  "description": "Template for validating operators on the assembly line",
  "type": "VALIDATION",
  "version": "1.0.0",
  "isActive": true,
  "department": "Assembling",
  "processes": ["Wire Harness Assembly"],
  "skillType": "BASIC",
  "scoringMethod": "WEIGHTED_AVERAGE",
  "categories": [
    {
      "id": "cat-001",
      "name": "Santé et Sécurité",
      "order": 1,
      "criteria": [
        {
          "id": "crit-001",
          "description": "Utilisation correcte des outils de connexion",
          "order": 1,
          "ratingDescriptions": {
            "1": "N'utilise pas correctement les outils, risque de blessure",
            "2": "Utilise les outils correctement mais avec hésitation",
            "3": "Utilise les outils correctement et avec assurance",
            "4": "Maîtrise parfaitement l'utilisation des outils et peut former d'autres"
          },
          "weight": 1,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        }
      ]
    },
    {
      "id": "cat-002",
      "name": "Qualité",
      "order": 2,
      "criteria": [
        {
          "id": "crit-002",
          "description": "Inspection visuelle des connexions",
          "order": 1,
          "ratingDescriptions": {
            "1": "Ne vérifie pas visuellement les connexions",
            "2": "Vérifie occasionnellement les connexions",
            "3": "Vérifie systématiquement les connexions",
            "4": "Vérifie systématiquement et peut détecter des défauts subtils"
          },
          "weight": 1,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        }
      ]
    }
  ],
  "minThreshold": 80,
  "maxPossibleScore": 48,
  "createdBy": "admin-user-001",
  "createdAt": "2023-01-15T10:30:00Z",
  "updatedBy": "admin-user-001",
  "updatedAt": "2023-01-15T10:30:00Z"
}
```

#### 3.1.2 Evaluations Collection

Stores evaluation submissions and results.

```typescript
// Evaluation
interface Evaluation {
  _id: ObjectId; // Unique identifier
  templateId: ObjectId; // Reference to template used
  templateVersion: string; // Version of template used
  templateType: "VALIDATION" | "CERTIFICATION" | "IPE"; // Type of evaluation
  operatorId: string; // ID of operator being evaluated
  operatorName: string; // Name of operator
  department: string; // Department
  process: string; // Process being evaluated
  evaluations: EvaluatorInput[]; // Inputs from evaluators
  finalScore: number; // Calculated final score
  percentage: number; // Score as percentage
  passed: boolean; // Whether operator passed
  hasRatingOne: boolean; // Whether any criteria received a '1' rating
  retrainingNeeded: boolean; // Whether retraining is needed (failed or has '1' rating)
  retrainingCriteria: string[]; // IDs of criteria needing retraining
  status: "DRAFT" | "SUBMITTED" | "APPROVED" | "REJECTED"; // Status
  submittedBy: string; // User who submitted evaluation
  submittedAt: Date; // Submission timestamp
  approvedBy: string; // User who approved evaluation
  approvedAt: Date; // Approval timestamp
  comments: string; // General comments
}

// Evaluator Input
interface EvaluatorInput {
  evaluatorId: string; // ID of evaluator
  evaluatorName: string; // Name of evaluator
  evaluatorRole: "TEAM_LEADER" | "TRAINER"; // Role of evaluator
  ratings: Rating[]; // Ratings provided by this evaluator
  comments: string; // Comments from this evaluator
  timestamp: Date; // When evaluation was provided
}

// Rating
interface Rating {
  criterionId: string; // ID of criterion being rated
  numericValue: 1 | 2 | 3 | 4; // Rating value
  comments: string; // Optional comments for this rating
}
```

Example Document:

```json
{
  "_id": "60b23d6f3a8d9c2e87654321",
  "templateId": "60a12c5e2f7d8b1c98765432",
  "templateVersion": "1.0.0",
  "templateType": "VALIDATION",
  "operatorId": "OP-12345",
  "operatorName": "John Doe",
  "department": "Assembling",
  "process": "Wire Harness Assembly",
  "evaluations": [
    {
      "evaluatorId": "TL-001",
      "evaluatorName": "Jane Smith",
      "evaluatorRole": "TEAM_LEADER",
      "ratings": [
        {
          "criterionId": "crit-001",
          "numericValue": 3,
          "comments": "Good technique with crimping tool"
        },
        {
          "criterionId": "crit-002",
          "numericValue": 2,
          "comments": "Needs to be more consistent with inspection"
        }
      ],
      "comments": "Shows potential but needs more practice on quality checks",
      "timestamp": "2023-02-10T14:30:00Z"
    },
    {
      "evaluatorId": "TR-001",
      "evaluatorName": "Bob Johnson",
      "evaluatorRole": "TRAINER",
      "ratings": [
        {
          "criterionId": "crit-001",
          "numericValue": 3,
          "comments": "Handles tools safely and effectively"
        },
        {
          "criterionId": "crit-002",
          "numericValue": 3,
          "comments": "Good visual inspection habits"
        }
      ],
      "comments": "Good overall performance",
      "timestamp": "2023-02-10T15:45:00Z"
    }
  ],
  "finalScore": 42,
  "percentage": 87.5,
  "passed": true,
  "hasRatingOne": false,
  "retrainingNeeded": false,
  "retrainingCriteria": [],
  "status": "APPROVED",
  "submittedBy": "TL-001",
  "submittedAt": "2023-02-10T16:00:00Z",
  "approvedBy": "TR-001",
  "approvedAt": "2023-02-11T09:15:00Z",
  "comments": "Operator meets validation requirements"
}
```

#### 3.1.3 TemplateHistory Collection

Tracks changes to templates for audit purposes.

```typescript
// Template History
interface TemplateHistory {
  _id: ObjectId; // Unique identifier
  templateId: ObjectId; // Reference to template
  version: string; // Version snapshot
  changeType: "CREATE" | "UPDATE" | "DELETE" | "ACTIVATE" | "DEACTIVATE"; // Type of change
  changedBy: string; // User who made the change
  timestamp: Date; // When change occurred
  previousState: any; // Previous state (for updates)
  newState: any; // New state
  comments: string; // Reason for change
}
```

### 3.2 Data Transfer Objects (DTOs)

#### 3.2.1 Template DTOs

```typescript
// Create Template DTO
interface CreateTemplateDto {
  name: string; // Template name
  description: string; // Template description
  type: "VALIDATION" | "CERTIFICATION" | "IPE"; // Template type
  department: string; // Department
  processes: string[]; // Processes
  skillType: "BASIC" | "KEY"; // Whether this is a basic or key skill
  scoringMethod: "WEIGHTED_AVERAGE"; // Method used for scoring
  categories: CategoryDto[]; // Categories
  minThreshold: number; // Minimum threshold
}

// Category DTO
interface CategoryDto {
  name: string; // Category name
  order: number; // Display order
  criteria: CriterionDto[]; // Criteria
}

// Criterion DTO
interface CriterionDto {
  description: string; // Criterion description
  order: number; // Display order
  ratingDescriptions: {
    // Rating descriptions
    "1": string;
    "2": string;
    "3": string;
    "4": string;
  };
  weight: number; // Weight of this criterion in scoring (default: 1)
  applicableTo: ("VALIDATION" | "CERTIFICATION" | "IPE")[]; // Applicable to
}

// Template Response DTO
interface TemplateResponseDto {
  id: string; // Template ID
  name: string; // Template name
  description: string; // Template description
  type: string; // Template type
  version: string; // Template version
  isActive: boolean; // Is active
  department: string; // Department
  processes: string[]; // Processes
  skillType: "BASIC" | "KEY"; // Whether this is a basic or key skill
  scoringMethod: "WEIGHTED_AVERAGE"; // Method used for scoring
  categories: CategoryResponseDto[]; // Categories
  minThreshold: number; // Minimum threshold
  maxPossibleScore: number; // Maximum possible score
  createdAt: string; // Creation date
  updatedAt: string; // Last update date
}

// Template List Item DTO
interface TemplateListItemDto {
  id: string; // Template ID
  name: string; // Template name
  type: string; // Template type
  version: string; // Template version
  isActive: boolean; // Is active
  department: string; // Department
  processes: string[]; // Processes
  updatedAt: string; // Last update date
}
```

#### 3.2.2 Evaluation DTOs

```typescript
// Submit Evaluation DTO
interface SubmitEvaluationDto {
  templateId: string; // Template ID
  operatorId: string; // Operator ID
  operatorName: string; // Operator name
  ratings: RatingDto[]; // Ratings
  evaluatorRole: "TEAM_LEADER" | "TRAINER"; // Evaluator role
  comments: string; // Comments
}

// Rating DTO
interface RatingDto {
  criterionId: string; // Criterion ID
  numericValue: 1 | 2 | 3 | 4; // Rating value
  comments?: string; // Optional comments
}

// Evaluation Response DTO
interface EvaluationResponseDto {
  id: string; // Evaluation ID
  templateInfo: {
    // Template info
    id: string;
    name: string;
    type: string;
    version: string;
  };
  operatorInfo: {
    // Operator info
    id: string;
    name: string;
  };
  process: string; // Process
  department: string; // Department
  evaluators: {
    // Evaluators
    id: string;
    name: string;
    role: string;
  }[];
  score: {
    // Score info
    raw: number;
    percentage: number;
    passed: boolean;
    hasRatingOne: boolean;
    retrainingNeeded: boolean;
  };
  status: string; // Status
  submittedAt: string; // Submission date
  comments: string; // Comments
}

// Score Summary DTO
interface ScoreSummaryDto {
  raw: number; // Raw score
  percentage: number; // Percentage
  passed: boolean; // Passed flag
  hasRatingOne: boolean; // Has rating of 1
  retrainingNeeded: boolean; // Retraining needed
  retrainingCriteria: {
    // Criteria needing retraining
    id: string;
    description: string;
    rating: number;
  }[];
  breakdown: {
    // Score breakdown
    category: string;
    averageScore: number;
    ratings: {
      criterionId: string;
      description: string;
      rating: number;
    }[];
  }[];
}
```

#### 3.2.3 Scoring DTOs

```typescript
// Calculate Score DTO
interface CalculateScoreDto {
  templateId: string; // Template ID
  ratings: RatingDto[]; // Ratings
}

// Score Response DTO
interface ScoreResponseDto {
  rawScore: number; // Raw score
  maxPossibleScore: number; // Maximum possible score
  percentage: number; // Percentage
  passed: boolean; // Passed flag
  minThreshold: number; // Minimum threshold
  hasRatingOne: boolean; // Has rating of 1
  retrainingNeeded: boolean; // Retraining needed
  retrainingCriteria: {
    // Criteria needing retraining
    id: string;
    description: string;
  }[];
}
```

---

## 4. API Specification

### 4.1 Template Management API

#### 4.1.1 Create Template

Creates a new template with rating-based criteria.

- **URL**: `/api/templates`
- **Method**: `POST`
- **Auth Required**: Yes (Admin)
- **Request Body**: `CreateTemplateDto`
- **Success Response**:
  - **Code**: 201 CREATED
  - **Content**: `TemplateResponseDto`
- **Error Response**:
  - **Code**: 400 BAD REQUEST
  - **Content**:
  ```json
  {
    "error": "Validation failed",
    "details": [
      "Templates must use WEIGHTED_AVERAGE scoring method",
      "Criterion 'Assembly technique' missing description for rating level 4"
    ]
  }
  ```

#### 4.1.2 Get Templates

Retrieves a list of templates with filtering options.

- **URL**: `/api/templates`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `type`: Template type (VALIDATION, CERTIFICATION, IPE)
  - `department`: Department
  - `process`: Process name (will match any template that includes this process)
  - `isActive`: Active status (true/false)
- **Success Response**:
  - **Code**: 200 OK
  - **Content**: `TemplateListItemDto[]`

#### 4.1.3 Get Template by ID

Retrieves a template by ID.

- **URL**: `/api/templates/{templateId}`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**:
  - `templateId`: ID of the template
- **Success Response**:
  - **Code**: 200 OK
  - **Content**: `TemplateResponseDto`

---

## 5. Business Logic

### 5.1 Template Management

#### 5.1.1 Template Creation and Validation

1. Template definitions are validated for required fields, structure, and consistency
2. Categories must have at least one criterion
3. Each criterion must have descriptions for all four rating levels
4. All templates use the WEIGHTED_AVERAGE scoring method
5. The template type determines which criteria are included in scoring (validation vs. certification)
6. Template versioning follows semantic versioning (major.minor.patch)
7. When a template is updated, a new version is created and history is tracked

#### 5.1.2 Template Version Management

1. Multiple versions of templates can exist, but only one version can be active at a time
2. Templates can be activated or deactivated
3. Templates can be deprecated but not deleted to maintain historical data integrity
4. Major version changes require administrator approval

### 5.2 Evaluation Processing

#### 5.2.1 Multi-Evaluator Handling

1. Evaluations can be submitted by Team Leaders or Trainers
2. Multiple evaluations for the same operator/template will be reconciled using a standard method:
   - Use average scores across evaluators for all criteria
   - In case of significant discrepancies (difference > 1 point), flag for review
3. Conflicting evaluations (large discrepancies) are flagged for review

#### 5.2.2 Evaluation Workflow

1. Team Leader submits initial evaluation
2. Trainer reviews and can submit their own evaluation
3. Final score is calculated using the standard averaging method
4. Evaluation is approved or rejected by authorized users
5. Approved evaluations are considered final

### 5.3 Score Calculation

#### 5.3.1 Standard Calculation Algorithm

##### Weighted Average Method

1. When multiple evaluators are involved:
   - For each criterion, calculate the average rating across all evaluators
   - Flag any criterion with a discrepancy > 1 between evaluators for review
2. Calculate the score using the weighted average method:
   - Count the total number of each rating value (how many 1s, 2s, 3s, and 4s)
   - Multiply each count by its rating value (count of 1s × 1, count of 2s × 2, etc.)
   - Multiply by each criterion's weight (default is 1)
   - Sum these products to get the raw score
   - Divide by the total number of criteria rated to get the average score
3. Calculate the percentage:
   - Divide the raw score by the maximum possible score (total criteria × 4) and multiply by 100
4. Compare against the minimum threshold to determine pass/fail

#### 5.3.2 Special Rule Handling

1. Any criterion rated as '1' triggers a retraining flag, regardless of overall score
2. For validation, the minimum threshold is 80%
3. For certification, the minimum threshold is 85%
4. For IPE, the minimum threshold is 90%
5. If certain criteria are not applicable for a template type, they are excluded from the calculation

### 5.4 Business Rules Enforcement

1. Validate that all required criteria have ratings
2. Check for invalid rating values (must be 1-4)
3. Enforce department-specific rules based on template configuration
4. For "Flexibilité" and "Productivité" categories:
   - Include in Certification evaluations
   - Exclude from Validation evaluations
5. Mandatory comments for any criteria rated as '1' or '2'

---

## 6. Integration Patterns

### 6.1 Training Process Integration

The Template Validation microservice integrates with the Training Process microservice to provide templates and scoring for validation/certification processes.

### 6.2 Versatility Matrix Integration

The Template Validation microservice integrates with the Versatility Matrix to receive status updates based on validation/certification outcomes.

### 6.3 User Management Integration

The Template Validation microservice integrates with the User Management microservice to authenticate and authorize users (Team Leaders, Trainers, Admins).

---

## 7. Cross-Cutting Concerns

### 7.1 Logging

The Template Validation microservice implements logging to track and monitor operations.

### 7.2 Error Handling

The Template Validation microservice implements error handling to ensure consistent and reliable service operation.

### 7.3 Security

The Template Validation microservice implements security measures to protect data and operations.

---

## 8. Implementation Considerations

### 8.1 Performance Considerations

The Template Validation microservice implements performance considerations to ensure efficient and responsive service operation.

### 8.2 Scalability Considerations

The Template Validation microservice implements scalability considerations to support growth and expansion.

### 8.3 Deployment Considerations

The Template Validation microservice implements deployment considerations to ensure seamless and reliable service operation.

---

## 9. Open Questions and Risks

### 9.1 Open Questions

1. ~~How should conflicts between multiple evaluator ratings be resolved? (Average, highest, lowest, etc.)~~
2. Should the system allow for customization of rating scales beyond the 1-4 range?
3. How should template versioning affect existing evaluations if criteria change?
4. Should there be a mechanism for appeals or reassessments if an operator disagrees with evaluation?

### 9.2 Risks

1. **Data Integrity**: Ensuring consistency between template versions and evaluations
2. **User Adoption**: Transitioning from paper-based to digital evaluation processes
3. **Integration Complexity**: Ensuring smooth interaction with Training Process microservice
4. **Performance Under Load**: Handling high volumes of evaluations during busy periods
5. **Rule Complexity**: Managing evolving business rules and department-specific variations

### 9.3 Example JSON Objects

#### 9.3.1 Rating-based Template Example

```json
{
  "_id": "61d45e8a9c3f0a4b5d789012",
  "name": "Assembly Skills Evaluation Template",
  "description": "Skills evaluation for assembly operators",
  "type": "CERTIFICATION",
  "version": "1.0.0",
  "isActive": true,
  "department": "Assembly",
  "processes": ["Final Assembly", "Quality Check"],
  "skillType": "KEY",
  "scoringMethod": "WEIGHTED_AVERAGE",
  "categories": [
    {
      "id": "cat-001",
      "name": "Technical Skills",
      "order": 1,
      "criteria": [
        {
          "id": "crit-001",
          "description": "Assembly technique",
          "order": 1,
          "ratingDescriptions": {
            "1": "Poor technique, frequent errors",
            "2": "Basic technique with some errors",
            "3": "Good technique with minimal errors",
            "4": "Excellent technique with no errors"
          },
          "weight": 2,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        },
        {
          "id": "crit-002",
          "description": "Tool handling",
          "order": 2,
          "ratingDescriptions": {
            "1": "Unsafe tool handling",
            "2": "Safe but inefficient tool handling",
            "3": "Safe and efficient tool handling",
            "4": "Expert tool handling and maintenance"
          },
          "weight": 1,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        }
      ]
    },
    {
      "id": "cat-002",
      "name": "Quality Focus",
      "order": 2,
      "criteria": [
        {
          "id": "crit-003",
          "description": "Attention to detail",
          "order": 1,
          "ratingDescriptions": {
            "1": "Misses significant details",
            "2": "Catches major issues but misses minor ones",
            "3": "Identifies most quality issues",
            "4": "Catches all quality issues consistently"
          },
          "weight": 2,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        },
        {
          "id": "crit-004",
          "description": "Documentation accuracy",
          "order": 2,
          "ratingDescriptions": {
            "1": "Documentation contains many errors",
            "2": "Documentation has some errors",
            "3": "Documentation is generally accurate",
            "4": "Documentation is always thorough and accurate"
          },
          "weight": 1,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        }
      ]
    }
  ],
  "minThreshold": 85,
  "maxPossibleScore": 24,
  "createdBy": "admin-user-003",
  "createdAt": "2023-04-20T11:30:00Z",
  "updatedBy": "admin-user-003",
  "updatedAt": "2023-04-20T11:30:00Z"
}
```

#### 9.3.2 Evaluation Submission Example

```json
{
  "templateId": "61d45e8a9c3f0a4b5d789012",
  "operatorId": "OP-56789",
  "operatorName": "Jane Wilson",
  "ratings": [
    {
      "criterionId": "crit-001",
      "numericValue": 3,
      "comments": "Good assembly technique with occasional minor errors"
    },
    {
      "criterionId": "crit-002",
      "numericValue": 4,
      "comments": "Excellent tool handling and maintenance"
    },
    {
      "criterionId": "crit-003",
      "numericValue": 2,
      "comments": "Needs improvement in catching minor quality issues"
    },
    {
      "criterionId": "crit-004",
      "numericValue": 3,
      "comments": "Documentation generally accurate with few errors"
    }
  ],
  "evaluatorRole": "TEAM_LEADER",
  "comments": "Operator performs well but needs to focus more on quality checks"
}
```

#### 9.3.3 Score Response Example

```json
{
  "rawScore": 19,
  "maxPossibleScore": 24,
  "percentage": 79.17,
  "passed": false,
  "minThreshold": 85,
  "hasRatingOne": false,
  "retrainingNeeded": true,
  "retrainingCriteria": [
    {
      "id": "crit-003",
      "description": "Attention to detail"
    }
  ]
}
```

#### 9.3.4 Template Form Definition Example (UI-friendly format)

```json
{
  "id": "61d45e8a9c3f0a4b5d789012",
  "name": "Assembly Skills Evaluation Template",
  "description": "Skills evaluation for assembly operators",
  "type": "CERTIFICATION",
  "formSections": [
    {
      "id": "cat-001",
      "title": "Technical Skills",
      "order": 1,
      "questions": [
        {
          "id": "crit-001",
          "question": "Assembly technique",
          "order": 1,
          "type": "RATING",
          "options": [
            {
              "value": 1,
              "label": "Poor technique, frequent errors"
            },
            {
              "value": 2,
              "label": "Basic technique with some errors"
            },
            {
              "value": 3,
              "label": "Good technique with minimal errors"
            },
            {
              "value": 4,
              "label": "Excellent technique with no errors"
            }
          ],
          "required": true,
          "commentsRequired": false
        }
      ]
    }
  ],
  "minThreshold": 85,
  "maxPossibleScore": 24,
  "metadata": {
    "department": "Assembly",
    "processes": ["Final Assembly", "Quality Check"],
    "skillType": "KEY",
    "scoringMethod": "WEIGHTED_AVERAGE"
  }
}
```

---

## Appendix A: Glossary

- **REQ 32/33**: The reference code for the evaluation form used for validation, certification, and IPE
- **Validation**: The process of verifying an operator's skills, requiring a minimum score of 80%
- **Certification**: The formal certification of an operator's skills, requiring a minimum score of 85%
- **IPE (Individual Performance Evaluation)**: Annual performance review, typically requiring 90% score
- **Template**: A configurable form definition including categories, criteria, and scoring rules
- **Category**: A grouping of related criteria in an evaluation template (e.g., "Quality", "Safety")
- **Criterion**: A specific skill or behavior being evaluated on a 1-4 scale
- **Rating**: A score (1-4) assigned to a criterion during evaluation
