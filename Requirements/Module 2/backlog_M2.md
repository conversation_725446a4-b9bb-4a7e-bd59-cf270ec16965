# Training Process Microservice - Product Backlog

## Overall Epic

| Epic                          | Feature                                               | User Stories / Tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | Time Estimate (SP) |
| :---------------------------- | :---------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- |
| Training Process Microservice |                                                       | **Epic Total**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | **205 SP**         |
|                               | **Feature 1: Core Training Orchestration Service**    | Design `OperatorTrainingRecord` aggregate & related entities (`Qualification`, `TrainingHistoryEntry`, `InternalWorkflow`); Implement domain methods for all training phases (O,LC,V,C,R,F) & internal approval workflows (Certification, Recertification, Removal, IPE); Implement `ApproveWorkflowStep` logic; Develop repositories for write model; Implement Command Handlers for all state changes (e.g., `CompleteOjtTrainingHandler`, `InitiateCertificationHandler`, `ApproveWorkflowStepHandler`); Develop API controllers (`TrainingController`, `WorkflowController`) for all operations; Implement Key Process Evaluation service, checklist storage & related API; Implement event publishing for status changes and notification requests; Define all necessary DTOs and Event schemas. | 98 SP              |
|                               | **Feature 2: Training Data Synchronization Service**  | Implement `CrewManagementChangeFeedProcessor` (Azure Function) to sync operator data; Implement `SkillsMatrixChangeFeedProcessor` (Azure Function) to sync process definitions (incl. `isKeyProcess`); Implement `VersatilityMatrixChangeFeedProcessor` (Azure Function) to sync versatility data for local reads; Develop `SyncOperatorDataHandler`, `SyncProcessDefinitionHandler`, `SyncVersatilityDataHandler` commands/handlers; Define data mappers for external models; Setup Cosmos DB lease containers.                                                                                                                                                                                                                                                                                      | 31 SP              |
|                               | **Feature 3: Internal Workflow Module Enhancements**  | Implement `WorkflowQueryService` (or `workflow-state.service.ts`) for specific queries on workflow instances or pending tasks (if not covered by Core Query Service); Setup and configure dedicated `internal-workflows` Cosmos DB container if chosen as the persistence strategy for workflow states (distinct from embedding in `training-records`). _(Note: This Feature's tasks might be merged into Feature 1 or Feature 4 depending on final architectural decisions during implementation.)_                                                                                                                                                                                                                                                                                                  | 6 SP               |
|                               | **Feature 4: Training Query Service**                 | Design and implement read model projections (`OperatorTrainingSummary`, `DepartmentSummary`); Develop `OperatorTrainingQueries` interface and CosmosDB implementation for read models; Implement Query Handlers (e.g., `GetOperatorSummaryHandler`, `GetTrainingHistoryHandler`, `GetQualificationRemovalHistoryQuery`); Develop `QueryController` API endpoints for all read operations; Define Query DTOs; Setup `training-read-models` Cosmos DB container; Implement mechanism for read model updates (e.g., event consumption).                                                                                                                                                                                                                                                                  | 31 SP              |
|                               | **Feature 5: Cross-Cutting Concerns & Initial Setup** | Setup NestJS project, base modules, and MediatR for CQRS; Configure Azure resources: Cosmos DB (write model), Service Bus (topics), API Management (policies), Monitor (AppInsights, Serilog), Key Vault; Implement Authentication (Azure AD) & Authorization (Role-based) middleware; Implement global error handling (RFC 7807); Implement Validation Service Client (REQ 32/33 integration); Setup initial CI/CD pipeline for Dev/Staging deployments.                                                                                                                                                                                                                                                                                                                                             | 39 SP              |

# Template Validation Microservice - Product Backlog

## Overall Epic

| Epic                                 | Feature                                                    | User Stories / Tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | Time Estimate (SP) |
| :----------------------------------- | :--------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- |
| **Template Validation Microservice** |                                                            | **Epic Total**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | **138 SP**         |
|                                      | **Feature 1: Core Template Management**                    | Define `Template`, `Category`, `Criterion` data models for MongoDB (schema based on LLD 3.1.1); Implement `TemplateRepository` for CRUD operations; Implement `TemplateService` (business logic for template creation, update, semantic versioning, activation/deactivation); Implement `TemplateValidator` (structure, `WEIGHTED_AVERAGE` scoring, criteria descriptions as per LLD 2.4.1, 5.1.1); Implement `TemplateFactory` (if needed); Develop `TemplateController` with REST endpoints (`POST /api/templates`, `GET /api/templates`, `GET /api/templates/{templateId}`, implied PUT/PATCH/DELETE); Define DTOs (`CreateTemplateDto`, `CategoryDto`, `CriterionDto`, `TemplateResponseDto`, `TemplateListItemDto` as per LLD 3.2.1); Implement logic for `maxPossibleScore` calculation. | 35 SP              |
|                                      | **Feature 2: Evaluation Processing & Submission**          | Define `Evaluation`, `EvaluatorInput`, `Rating` data models for MongoDB (LLD 3.1.2); Implement `EvaluationRepository`; Implement `EvaluationService` (submission logic, multi-evaluator handling - averaging scores, discrepancy flagging LLD 5.2.1, evaluation workflow statuses LLD 5.2.2, 3.1.2); Implement `EvaluationValidator` (all required criteria rated, valid rating values 1-4); Develop `EvaluationController` with REST endpoints (`POST /api/evaluations`, `GET /api/evaluations/{evaluationId}`, query evaluations, implied PUT for approval/rejection); Define DTOs (`SubmitEvaluationDto`, `RatingDto`, `EvaluationResponseDto` LLD 3.2.2); Link evaluations to `templateId` and `templateVersion`.                                                                          | 30 SP              |
|                                      | **Feature 3: Scoring & Business Rules Engine**             | Implement `ScoringService` (or `CalculationService` LLD 2.4.3) to calculate weighted average score (LLD 5.3.1), determine `percentage`, `passed` status, identify `hasRatingOne`, `retrainingNeeded`, `retrainingCriteria`; Implement `BusinessRuleEngine` for special rules ('1' rating triggers retraining LLD 5.3.2, `minThreshold` for VALIDATION/CERTIFICATION/IPE LLD 5.3.2, criteria exclusion LLD 5.3.2, mandatory comments for '1'/'2' ratings LLD 5.4); Develop `ScoringController` with `POST /api/scores/calculate` endpoint; Define DTOs (`CalculateScoreDto`, `ScoreResponseDto` LLD 3.2.3, `ScoreSummaryDto` LLD 3.2.2); Ensure `finalScore`, `percentage`, etc., are stored in `Evaluation` document.                                                                          | 25 SP              |
|                                      | **Feature 4: Template Auditing & History**                 | Define `TemplateHistory` data model for MongoDB (LLD 3.1.3); Implement `TemplateHistoryRepository`; Integrate with `TemplateService` to automatically log changes (CREATE, UPDATE, DEACTIVATE) to `TemplateHistory`, storing `previousState` and `newState`; (Optional Task) Develop API endpoint (`GET /api/templates/{templateId}/history`) to retrieve audit history.                                                                                                                                                                                                                                                                                                                                                                                                                       | 8 SP               |
|                                      | **Feature 5: Foundational Setup & Cross-Cutting Concerns** | Initialize NestJS project (structure, modules); Configure MongoDB connection; Implement Authentication middleware (JWT, Azure AD integration LLD 2.3); Implement Authorization middleware (Role-based access); Implement global error handling middleware (e.g., RFC 7807); Implement request/response logging middleware; Integrate OpenAPI/Swagger; Setup basic CI/CD pipeline (build, lint, test, deploy to Dev/Staging on Azure); Configure basic monitoring and logging (Prometheus/Grafana stubs or AppInsights LLD 2.3); Create Dockerfile and docker-compose.                                                                                                                                                                                                                          | 40 SP              |

# Versatility Matrix Microservice - Product Backlog

## Overall Epic

| Epic                            | Feature                                                        | User Stories / Tasks                                                                                                                                                                                                                                                                                                                   | Time Estimate (SP) |
| :------------------------------ | :------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- |
| Versatility Matrix Microservice |                                                                | **Epic Total**                                                                                                                                                                                                                                                                                                                         | **215 SP**         |
|                                 | **Feature 1: Core Setup & Cross-Cutting Concerns**             | US1.1: Init NestJS project and core modules; US1.2: Implement Azure AD B2C AuthN & RBAC middleware; US1.3: Global error handling middleware; US1.4: Logging middleware & Swagger integration; US1.5: CI/CD pipeline setup; US1.6: Application Insights monitoring; US1.7: Audit logging mechanism; US1.8: Dockerfile & docker-compose. | 40 SP              |
|                                 | **Feature 2: Master Data Management**                          | US2.1: Operator, Area, Department entities & repositories; US2.2: Skill, Workstation entities & repositories; US2.3: CrewManagementChangeFeedProcessor; US2.4: Workstation & Skill ingestion mechanism; US2.5: Admin diagnostics API.                                                                                                  | 25 SP              |
|                                 | **Feature 3: Qualification Management & Training Integration** | US3.1: Qualification entity & repository; US3.2: QualificationService; US3.3: SyncQualificationStatusCommand & Handler; US3.4: TrainingStatusChangedEventHandler; US3.5: QualificationController endpoints; US3.6: QualificationStatusUpdatedEvent; US3.7: ProcessQualificationChanges CF processor.                                   | 35 SP              |
|                                 | **Feature 4: Polyvalence Management**                          | US4.1: Polyvalence entity & repository; US4.2: PolyvalenceService; US4.3: UpdatePolyvalenceCommand & Handler; US4.4: PolyvalenceController endpoints; US4.5: PolyvalenceUpdatedEvent; US4.6: ProcessPolyvalenceChanges CF processor.                                                                                                   | 30 SP              |
|                                 | **Feature 5: Matrix View & Core Query Functionality**          | US5.1: MatrixView read model; US5.2: GetMatrixQuery & Handler; US5.3: MatrixController endpoints; US5.4: MatrixProjectionRepository; US5.5: Matrix export functionality; US5.6: Projection maintenance.                                                                                                                                | 30 SP              |
|                                 | **Feature 6: Workstation Coverage & Analytics**                | US6.1: WorkstationCoverageView & OperatorDetailView projections; US6.2: WorkstationCoverageService; US6.3: WorkstationCoverage queries & endpoints; US6.4: ReportController endpoints; US6.5: WorkstationCoverageChangedEvent & projection updates.                                                                                    | 35 SP              |
|                                 | **Feature 7: Outbound Events & External Integrations**         | US7.1: External Change Feed processors; US7.2: Alert event publication; US7.3: FindQualifiedOperatorsQuery & API; US7.4: Centralized logging integration.                                                                                                                                                                              | 20 SP              |

# Operator Replacement Process - Product Backlog

## Overall Epic

| Epic                         | Feature                                                    | User Stories / Tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | Time Estimate (SP) |
| :--------------------------- | :--------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- |
| Operator Replacement Process |                                                            | **Epic Total (Note: All tasks below are 'Not Ready': Acceptance Criteria and Test Cases undefined. LLD (draft) requires final review.)**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | **220 SP**         |
|                              | **Feature 1: Replacement Service (Core)**                  | Design Write Models (`Replacement`, `BackupStructure`, `Panier`, etc. per LLD 6.1); Implement Command Handlers (`InitiateReplacement`, `AssignBackupOperator`, etc. per LLD 4.1.2) including business/validation rules (LLD 4.1.3, 4.1.4); Define & publish Event Contracts (`ReplacementInitiatedEvent`, etc. per LLD 4.2.1); Design Read Models (TL Workspace, SL Panier, Dept Panier, Replacement History per LLD 4.3.1); Implement Change Feed Processors (`TLWorkspaceProcessor`, etc. per LLD 4.3.2); Develop Command APIs (`POST /api/replacement/...` per LLD 7.1); Develop Query APIs (`GET /api/workspace/...`, `GET /api/panier/...` per LLD 7.1.4, 7.2.1); Develop Recommendation Engine (sync versality matrix, matching algorithm, ranking) & Recommendation APIs (`GET /api/replacement/suggestions/...` per LLD 7.1.3); Implement Panier operator status update API (LLD 7.2.2). | 100 SP             |
|                              | **Feature 2: Notification Service**                        | Implement event consumption from Service Bus for notification triggers (LLD 4.2.2, 9.1.1); Develop WebSocket endpoint and logic for real-time updates; Implement notification persistence, status tracking, and multi-channel support (in-app initial, stubs for email); Develop APIs: `GET /api/notifications/user/{userId}`, `POST /api/notifications/{notificationId}/read` (LLD 5.1.2).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | 20 SP              |
|                              | **Feature 3: Evaluation Service**                          | Design & implement `Evaluation` Write Model (LLD 6.1); Develop logic for collecting TL evaluations and processing data for recommendation feedback; Implement evaluation workflows & historical performance tracking; Develop APIs: `POST /api/evaluation/replacement/{replacementId}`, `GET /api/evaluation/pending/{tlId}` (LLD 5.1.3, 7.3); Publish `evaluation-events` to Service Bus (LLD 4.2.2, 9.1.1).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | 15 SP              |
|                              | **Feature 4: Reporting Service**                           | Consume events for reporting data aggregation; Design/implement read models for reporting queries; Develop APIs: `GET /api/reports/replacement-history`, KPI queries (`GET /api/reports/kpi/...` per LLD 5.2.1, 7.4); Implement filtering, aggregation, KPI calculation logic; Implement data export functionality (e.g., CSV).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 25 SP              |
|                              | **Feature 5: Clocking Integration Service**                | Implement logic to synchronize clocking data with external system; Process attendance information and update temporary assignments; Implement clocking responsibility rules (LLD 4.1.4 BR-REP-010/011); Develop APIs: `GET /api/clocking/operator/{operatorId}`, `GET /api/clocking/team/{tlId}` (LLD 5.2.2); Manage `clocking-events` (LLD 4.2.2, 9.1.1).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | 20 SP              |
|                              | **Feature 6: Cross-Cutting Concerns & Foundational Setup** | Setup ASP.NET Core Web API project structures for all new services; Implement base CQRS components; Configure Azure Service Bus (topics, subscriptions per LLD Sec 9) & event utilities; Implement RBAC security middleware (Azure AD B2C, JWT, permissions per LLD Sec 10); Setup Cosmos DB (containers, indexing per LLD Sec 8) & data access layers; Define shared DTOs/Event contracts; Setup initial CI/CD pipelines (Dev/Staging); Implement basic monitoring (AppInsights) & structured logging; Implement global error handling; Configure API Gateway routes.                                                                                                                                                                                                                                                                                                                           | 40 SP              |

# Nurse Microservice - Product Backlog

## Overall Epic

| Epic               | Feature                                            | User Stories / Tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | Time Estimate (SP) |
| :----------------- | :------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------- |
| Nurse Microservice |                                                    | **Epic Total**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | **200 SP**         |
|                    | **Feature 1: Core Setup & Cross-Cutting Concerns** | US1.1: Initialize NestJS project, setup base modules, configure CQRS. (Not Ready: Detailed AC & TC needed); US1.2: Configure Azure Cosmos DB connection and repository patterns. (Not Ready: Detailed AC & TC needed); US1.3: Implement Authentication (Azure AD/JWT) & Authorization (Role-based) middleware. (Not Ready: Detailed AC & TC needed); US1.4: Implement global error handling (RFC 7807). (Not Ready: Detailed AC & TC needed); US1.5: Implement request/response logging middleware & integrate OpenAPI/Swagger. (Not Ready: Detailed AC & TC needed); US1.6: Setup basic CI/CD pipeline (build, lint, test, deploy to Dev/Staging). (Not Ready: Detailed AC & TC needed); US1.7: Configure basic monitoring and logging (AppInsights). (Not Ready: Detailed AC & TC needed); US1.8: Create Dockerfile and docker-compose setup. (Not Ready: Detailed AC & TC needed); US1.9: Define core event schema (`NurseServiceEvent`) and event publishing utilities (Azure Service Bus). (Not Ready: Detailed AC & TC needed) | 25 SP              |
|                    | **Feature 2: Employee Data Management**            | US2.1: Define `Employee` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US2.2: Implement `EmployeeChangeProcessor` to sync denormalized employee data from HR/Organization microservices. (Not Ready: Detailed AC & TC needed); US2.3: Handle employee data updates and deletions (mark as inactive, TTL management). (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | 10 SP              |
|                    | **Feature 3: Illness Period Management**           | US3.1: Define `IllnessRecord` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US3.2: Implement `IllnessPeriodService` for business logic. (Not Ready: Detailed AC & TC needed); US3.3: Develop `IllnessPeriodController` with REST endpoints (POST, GET all, GET by ID, PATCH, GET by employeeId, POST extend). (Not Ready: Detailed AC & TC needed); US3.4: Define and implement DTOs (`CreateIllnessPeriodDto`, `IllnessPeriodResponseDto`). (Not Ready: Detailed AC & TC needed); US3.5: Implement event publishing for illness period state changes. (Not Ready: Detailed AC & TC needed); US3.6: Implement logic for `bailiffDetails`, `doctorDetails`, `followUpRequired`, `followUpDate`. (Not Ready: Detailed AC & TC needed); US3.7: Implement logic to link `DocumentRecord` IDs. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                      | 25 SP              |
|                    | **Feature 4: Work Accident Processing**            | US4.1: Define `WorkAccidentRecord` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US4.2: Implement `WorkAccidentService` for business logic. (Not Ready: Detailed AC & TC needed); US4.3: Develop `WorkAccidentController` with REST endpoints (POST, GET all, GET by ID, PATCH, GET by employeeId, POST extend). (Not Ready: Detailed AC & TC needed); US4.4: Define and implement DTOs for Work Accident. (Not Ready: Detailed AC & TC needed); US4.5: Implement event publishing for work accident state changes. (Not Ready: Detailed AC & TC needed); US4.6: Implement logic for `accidentDescription`, `extensions`. (Not Ready: Detailed AC & TC needed); US4.7: Implement logic to link `DocumentRecord` IDs. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                           | 25 SP              |
|                    | **Feature 5: Pregnancy Announcement Handling**     | US5.1: Define `PregnancyRecord` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US5.2: Implement `PregnancyService` for business logic. (Not Ready: Detailed AC & TC needed); US5.3: Develop `PregnancyController` with REST endpoints (POST, GET all, GET by employeeId, PATCH, POST terminate). (Not Ready: Detailed AC & TC needed); US5.4: Define and implement DTOs for Pregnancy. (Not Ready: Detailed AC & TC needed); US5.5: Implement event publishing for pregnancy record state changes. (Not Ready: Detailed AC & TC needed); US5.6: Implement logic for `specialAccommodations`, `expectedDueDate`. (Not Ready: Detailed AC & TC needed); US5.7: Implement logic to link `DocumentRecord` ID. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                                       | 20 SP              |
|                    | **Feature 6: Work Capability Assessment**          | US6.1: Define `WorkCapabilityRecord` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US6.2: Implement `WorkCapabilityService` for business logic. (Not Ready: Detailed AC & TC needed); US6.3: Develop `WorkCapabilityController` with REST endpoints (POST, GET all, GET by employeeId, PATCH). (Not Ready: Detailed AC & TC needed); US6.4: Define and implement DTOs for Work Capability. (Not Ready: Detailed AC & TC needed); US6.5: Implement event publishing for work capability state changes. (Not Ready: Detailed AC & TC needed); US6.6: Implement logic for `limitations`, `effectiveFrom/To`, `visibleToTeamLeader`. (Not Ready: Detailed AC & TC needed); US6.7: Implement logic to link `DocumentRecord` IDs. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                    | 20 SP              |
|                    | **Feature 7: Document Management**                 | US7.1: Define `DocumentRecord` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US7.2: Implement `DocumentService` for business logic (upload, download metadata, delete). (Not Ready: Detailed AC & TC needed); US7.3: Develop `DocumentController` with REST endpoints (POST, GET by ID, GET by related record, DELETE). (Not Ready: Detailed AC & TC needed); US7.4: Integrate with Azure Blob Storage for file storage. (Not Ready: Detailed AC & TC needed); US7.5: Implement access restriction logic. (Not Ready: Detailed AC & TC needed); US7.6: Implement TTL and `isDeleted` flag logic. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                                                                                                                                               | 20 SP              |
|                    | **Feature 8: Regional Variation Management**       | US8.1: Define `RegionalSettings` entity/collection schema and repository. (Not Ready: Detailed AC & TC needed); US8.2: Implement service logic to load/cache `RegionalSettings`. (Not Ready: Detailed AC & TC needed); US8.3: Adapt core services to apply regional variations (processing flows, required fields, notifications). (Not Ready: Detailed AC & TC needed); US8.4: Design mechanism for runtime workflow selection based on regional settings. (Not Ready: Detailed AC & TC needed); US8.5: (Optional) Develop admin API endpoints to manage `RegionalSettings`. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                                                                                                                                                                                                   | 15 SP              |
|                    | **Feature 9: Reporting & Querying**                | US9.1: Develop `ReportingController` with GET endpoints for various reports. (Not Ready: Detailed AC & TC needed); US9.2: Implement query services/handlers for common query patterns (LLD Sec 6). (Not Ready: Detailed AC & TC needed); US9.3: Implement pagination and filtering for reporting endpoints. (Not Ready: Detailed AC & TC needed); US9.4: Ensure query performance by leveraging defined indexing strategies. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | 15 SP              |
|                    | **Feature 10: Integration Services**               | US10.1: Status Tracking (Module 3) Integration: Publish `StatusUpdateEvent`, implement status mapping, priority, and conflict resolution. (Not Ready: Detailed AC & TC needed); US10.2: Transport Module Integration: Publish `MedicalCertificateDepositEvent`, consume `CertificateReceivedEvent`, use `TransportRequest` DTO. (Not Ready: Detailed AC & TC needed); US10.3: Team Leader/Shift Leader UI Support: Develop API endpoints for `EmployeeStatusResponse`. (Not Ready: Detailed AC & TC needed); US10.4: Define and implement event consumption logic for events from other services if needed. (Not Ready: Detailed AC & TC needed); US10.5: Manage notification logic based on `notificationStatus` fields and `RegionalSettings`. (Not Ready: Detailed AC & TC needed)                                                                                                                                                                                                                                                | 25 SP              |

## Epic Summary and Time Estimates

| Epic                             | Total Story Points (SP) | Estimated Days (at 0.125 days/SP) | LLD Refinement and code reviews (days) | Adjusted Estimated Days |
| :------------------------------- | :---------------------- | :-------------------------------- | :------------------------------------- | :---------------------- |
| Training Process Microservice    | 205                     | 25                                | 5                                      | 30                      |
| Template Validation Microservice | 138                     | 17                                | 3                                      | 20                      |
| Versatility Matrix Microservice  | 215                     | 25                                | 5                                      | 30                      |
| Operator Replacement Process     | 220                     | 30                                | 5                                      | 35                      |
| Nurse Microservice               | 200                     | 25                                | 5                                      | 30                      |
| **TOTALS**                       | **978**                 | **122**                           | **23**                                 | **145**                 |
