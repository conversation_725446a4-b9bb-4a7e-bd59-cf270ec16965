# Skills Matrix - Low-Level Design (LLD)

## Document Information

**Version:** 1.0.0  
**Last Updated:** 2025-05-13  
**Status:** Draft  
**Authors: <AUTHORS>

## Executive Summary

This document details the low-level design for the Skills Matrix module within the Connected Workers (CW) platform. The Skills Matrix is a foundational component for managing workforce capabilities, specifically designed for the automobile cabling/wiring industry context. It serves as the central repository for defining skills required per workstation across different project phases (Prototype, Pre-Series, Series).

### Key Features

- **Centralized Skill Definition**: Management of a "Skills Master Table" containing all necessary operational skills, their descriptions, codes, and criticality (Basic, Key, Specific/Non-Critical), maintained by the Training Department.
- **Workstation Criticality Management**: Maintenance of a "Work Center Criticality Table" defining the operational criticality (Critical, Medium, Normal) of each workstation, managed by the Quality Department.
- **Skill-to-Workstation Mapping**: Enables the Manufacturing Engineering (ME) department to link specific skills from the Skills Master to individual workstations.
- **Project Phase Adaptability**: Supports distinct skill requirement configurations for Prototype, Pre-Series, and Series phases of a project.
- **Compliance Integration**: Allows tagging skills linked to industry standards (e.g., IATF 16949, IPC/WHMA-A-620) and customer requirements.
- **Data Management**: Supports import/export capabilities (e.g., Excel) for Skills Master and Work Center data, facilitating integration with external systems like MES/PLM.
- **Notification System**: Automated notifications to Team Leaders and Trainers upon relevant updates to skill or workstation data.
- **Role-Based Access & Management**: Defined responsibilities for ME (initial setup, skill mapping), Training (Skills Master), and Quality (Work Center Criticality).

### Key Integration Points

- **Versatility Matrix**: Provides the foundational skill and workstation requirement data used by the Versatility Matrix to track operator qualifications and polyvalence. Changes in the Skills Matrix propagate to the Versatility Matrix.
- **Training Process**: Serves as the reference for skill definitions, categories, and compliance requirements needed by the Training Process module.
- **Manufacturing Execution Systems (MES) / Product Lifecycle Management (PLM)**: Acts as a potential source for importing initial workstation and skill data (e.g., via structured Excel sheets derived from systems like Apriso).
- **Connected Workers Platform**: Leverages core platform services for user management, authentication, notifications, and potentially UI components.
- **HR Systems (Indirect)**: Provides data that informs HR about required skills for recruitment and workforce planning.

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture](#2-architecture)
   1. [Architectural Patterns](#21-architectural-patterns)
   2. [High-Level Component Diagram](#22-high-level-component-diagram)
   3. [CQRS Implementation](#23-cqrs-implementation)
   4. [Change Feed and synchronization](#24-change-feed)
   5. [Microservices Breakdown](#25-microservices-breakdown)
3. [Detailed Component Design](#3-detailed-component-design)
4. [Data Models](#4-data-models)
   1. [Write Models](#41-write-models)
   2. [Read Models](#42-read-models)
5. [API Specification](#5-api-specification)
6. [Event Communication](#6-event-communication)
   1. [Event Schema](#61-event-schema)
   2. [Domain Events](#62-domain-events)
   3. [Service Bus Topic Structure](#63-service-bus-topic-structure)
   4. [Key Operations Sequence](#64-key-operations-sequence)
7. [Azure Services Integration](#7-azure-services-integration)
8. [Security and Permission Models](#8-security-and-permission-models)
   1. [Authorization Model](#81-authorization-model)
   2. [Security Implementation](#83-security-implementation)
9. [Testing Strategy](#9-testing-strategy)

10. [Conclusion](#10-conclusion)
    1. [Design Summary](#101-design-summary)

---

## 1. Overview

The Skills Matrix module is a critical component of the broader Absenteeism and Replacement Management capability within the Connected Workers platform. Its primary purpose is to define, manage, and communicate the specific skills required to operate each workstation within a manufacturing environment, particularly tailored for automobile cabling/wiring production lines.

The module addresses the need for a dynamic and reliable system to track skill requirements across the lifecycle of a project (Prototype, Pre-Series, Series). It provides distinct functionalities managed by different departments:

- **Manufacturing Engineering (ME)**: Defines workstations, identifies necessary skills (Basic, Key, Specific/Non-Critical), links skills to workstations, and manages initial setup.
- **Training Department**: Manages the central **Skills Master Table**, defining all available skills, their codes, descriptions, and categories. This table functions on an overwrite basis without version control within CW.
- **Quality Department**: Manages the **Work Center Criticality Table**, assigning criticality levels (C, M, N) to workstations.

Data can be sourced initially or updated via imports (e.g., Excel sheets potentially originating from MES/PLM systems like Apriso, as detailed in the FDS), but the canonical definitions within CW reside in the Skills Master and Work Center Criticality tables.

The Skills Matrix serves as a foundational data source for other modules, notably the **Versatility Matrix** (tracking operator qualifications against these requirements) and the **Training Process** (defining training needs based on required skills). It ensures consistency in skill definitions and requirements across the platform, supporting efficient operations, training planning, and workforce management.

## 2. Architecture

### 2.1 Architectural Patterns

The Skills Matrix module implements several key architectural patterns to ensure scalability, maintainability, and performance:

1. **Command Query Responsibility Segregation (CQRS)** - Separates write operations (commands) from read operations (queries) with distinct models, enabling optimized performance for each concern.

2. **Event-Driven Architecture** - Leverages asynchronous event communication for inter-service coordination, ensuring loose coupling and resilience.

3. **Microservices Architecture** - Decomposes the Skills Matrix functionality into discrete, independently deployable services with specific responsibilities.

4. **Change Feed Pattern** - Utilizes Cosmos DB's change feed to maintain consistency between write and read models, enabling efficient data synchronization.

5. **Domain-Driven Design** - Organizes functionality around business domains and implements a bounded context for the Skills Matrix module.

### 2.2 High-Level Component Diagram

The Skills Matrix module consists of several key components that work together to implement the CQRS pattern and event-driven architecture:

```mermaid
flowchart TD
    User((User)) --> SCAPI[Skills Command API]
    User --> SQAPI[Skills Query API]

    subgraph Write Path
        SCAPI --> VALIDATE[Validation Layer]
        VALIDATE --> BLogic[Business Logic]
        BLogic --> CWRepo[Cosmos Write Repository]
        CWRepo --> WDB[(Cosmos DB\nWrite Container)]
        BLogic --> PUBLISHER[Event Publisher]
        PUBLISHER --> SB[(Azure Service Bus)]
    end

    subgraph Read Path
        SQAPI --> QHandlers[Query Handlers]
        QHandlers --> CRRepo[Cosmos Read Repository]
        CRRepo --> RDB[(Cosmos DB\nRead Containers)]
    end

    subgraph Synchronization
        CF[Change Feed Processor] --> WDB
        CF --> RDB
        CF --> SB
    end

    subgraph Event Consumers
        SB --> Sync[Sync Service]
        SB --> Notif[Notification Service]
        SB --> VM[Versatility Matrix]
        SB --> TP[Training Process]
    end

    classDef apiNodes fill:#90CAF9,stroke:#0D47A1,color:black
    classDef dbNodes fill:#AED581,stroke:#33691E,color:black
    classDef eventNodes fill:#FFD54F,stroke:#FF6F00,color:black
    classDef processorNodes fill:#CE93D8,stroke:#6A1B9A,color:black

    class SCAPI,SQAPI apiNodes
    class WDB,RDB dbNodes
    class SB,PUBLISHER eventNodes
    class CF,Sync,Notif,VM,TP processorNodes
```

This diagram illustrates the key components of the Skills Matrix module:

1. **Command Path (Write Operations)**:

   - **Skills Command API**: Exposes REST endpoints for write operations
   - **Validation Layer**: Validates incoming commands against business rules
   - **Business Logic**: Implements core domain logic
   - **Cosmos Write Repository**: Interacts with Cosmos DB write container
   - **Event Publisher**: Publishes domain events to Azure Service Bus

2. **Query Path (Read Operations)**:

   - **Skills Query API**: Exposes REST endpoints for read operations
   - **Query Handlers**: Processes read requests with optimized models
   - **Cosmos Read Repository**: Retrieves data from read-optimized containers

3. **Synchronization**:

   - **Change Feed Processor**: Detects changes in write container and synchronizes read models
   - Publishes events to Service Bus when relevant changes occur

4. **Event Consumers**:
   - **Sync Service**: Synchronizes data between write and read models
   - **Notification Service**: Delivers notifications to stakeholders
   - **Versatility Matrix**: Consumes skill changes to update operator qualifications
   - **Training Process**: Updates training requirements based on skill changes

### 2.3 CQRS Implementation

The Command Query Responsibility Segregation (CQRS) pattern is a fundamental architectural principle in the Skills Matrix module, separating write and read operations to optimize for their different requirements.

```mermaid
flowchart LR
    subgraph "Command Side (Write Operations)"
        direction TB
        CMD[Commands]
        VLD[Validation]
        BL[Business Logic]
        WR[Write Repository]
        DB1[(Write Container)]
        EP[Event Publisher]

        CMD --> VLD
        VLD --> BL
        BL --> WR
        WR --> DB1
        BL --> EP

        classDef cmd fill:#FF8A65,stroke:#D84315,color:black
        class CMD,VLD,BL,WR,EP cmd
    end

    subgraph "Query Side (Read Operations)"
        direction TB
        QRY[Queries]
        QH[Query Handlers]
        RR[Read Repository]
        DB2[(Read Containers)]

        QRY --> QH
        QH --> RR
        RR --> DB2

        classDef qry fill:#81D4FA,stroke:#0288D1,color:black
        class QRY,QH,RR qry
    end

    subgraph "Synchronization"
        direction TB
        CF[Change Feed]
        TF[Model Transformer]
        UP[Read Model Updater]

        DB1 --> CF
        CF --> TF
        TF --> UP
        UP --> DB2

        classDef sync fill:#CE93D8,stroke:#8E24AA,color:black
        class CF,TF,UP sync
    end

    EP --> SB[(Service Bus)]
    SB --> ES[Event Subscribers]
    ES --> UP
```

#### 1. Command Side (Write Operations)

The command side is responsible for processing all data modifications:

- **Commands**: Represent intentions to change the system state (e.g., CreateSkill, UpdateWorkcenter)
- **Validation**: Validates commands against business rules before execution
- **Business Logic**: Implements domain logic and ensures data consistency
- **Write Repository**: Persists data in the normalized write model
- **Event Publisher**: Publishes domain events for successful operations

The write model is optimized for consistency and atomic updates, with normalized data structures that prevent duplication and maintain referential integrity.

#### 2. Query Side (Read Operations)

The query side is dedicated to efficient data retrieval:

- **Queries**: Represent requests for information without state changes
- **Query Handlers**: Process specific query types with optimized data access patterns
- **Read Repository**: Retrieves data from read-optimized models
- **Read Models**: Denormalized views designed for specific query scenarios

Read models are denormalized and structured for query performance, often containing pre-computed data that would otherwise require complex joins or aggregations.

#### 3. Synchronization

The synchronization process maintains consistency between write and read models:

- **Change Feed**: Detects changes in the write container
- **Model Transformer**: Converts write models to appropriate read model formats
- **Read Model Updater**: Updates read models with transformed data

This design provides several benefits:

- **Optimized Performance**: Each side is tuned for its specific operations
- **Scalability**: Read and write workloads can scale independently
- **Flexibility**: Read models can evolve without impacting the write model
- **Resilience**: Read operations remain available even during write-side issues

### 2.4 Change Feed and Synchronization

The Skills Matrix module leverages Azure Cosmos DB's change feed to implement data synchronization between write and read models, ensuring eventual consistency in the CQRS pattern.

```mermaid
flowchart TD
    WC[(Cosmos DB\nWrite Container)] --> |Changes| CF[Change Feed]

    subgraph "Change Feed Processors"
        CF --> SCFP[Skill Change Feed Processor]
        CF --> WCFP[Workcenter Change Feed Processor]
        CF --> MSFP[Mapping Change Feed Processor]
    end

    SCFP --> |Transform| SRM[Skill Read Model]
    WCFP --> |Transform| WRM[Workcenter Read Model]
    MSFP --> |Update Skill Relations| SRM
    MSFP --> |Update Workcenter Relations| WRM
    MSFP --> |Aggregate| CLRM[ChildLine Read Model]

    SRM --> RC1[(Cosmos DB\nSkills Read Container)]
    WRM --> RC2[(Cosmos DB\nWorkcenters Read Container)]
    CLRM --> RC3[(Cosmos DB\nChildLines Read Container)]

    subgraph "Leasing & Checkpointing"
        LC[(Lease Container)]
        SCFP <--> |Track Progress| LC
        WCFP <--> |Track Progress| LC
        MSFP <--> |Track Progress| LC
    end

    classDef container fill:#AED581,stroke:#33691E,color:black
    classDef processor fill:#CE93D8,stroke:#6A1B9A,color:black
    classDef model fill:#FFCC80,stroke:#E65100,color:black

    class WC,RC1,RC2,RC3,LC container
    class CF,SCFP,WCFP,MSFP processor
    class SRM,WRM,CLRM model
```

#### 1. Change Feed Processor

The change feed processor is the core component responsible for:

- **Monitoring Changes**: Continuously polls the write container for document changes (creates, updates, deletes)
- **Batched Processing**: Processes changes in configurable batches (typically 100 items) for efficiency
- **Idempotent Handling**: Ensures the same change is not applied multiple times to read models
- **Distributed Processing**: Uses lease mechanism to enable processing scale-out across multiple instances

#### 2. Synchronization Flow

When changes occur in the write container, the following process takes place:

1. **Change Detection**: The change feed processor identifies modified documents
2. **Document Categorization**: Each document is categorized based on its type (Skill, Workcenter, or Mapping)
3. **Model Transformation**: The write model is transformed to the appropriate read model format(s)
   - For skills: Updates the denormalized skill read model
   - For workcenters: Updates the workcenter read model
   - For mappings: Updates both skill and workcenter read models to maintain relationship integrity
4. **Read Model Update**: The transformed document is upserted into the appropriate read container(s)
5. **Event Publishing** (optional): Domain events are published to Service Bus for cross-service communication

#### 3. Deduplication Strategy

To prevent duplicate processing, the system implements:

- **Event ID Tracking**: Each event includes a unique event ID (UUID)
- **Processing History**: Services maintain a record of processed event IDs (with TTL-based expiration)
- **Idempotency Check**: Before processing, services verify if the event ID has been processed before
- **Safe Retry**: If an event was received but processing failed, safe retry logic allows reprocessing

#### 4. Resilience Mechanisms

The change feed architecture includes several resilience features:

- **Checkpoint-Based Progress Tracking**: Maintains progress via lease documents in a dedicated container
- **Automatic Retries**: Implements exponential backoff for transient failures
- **Partition Distribution**: Distributes processing across multiple instances for scalability
- **Monitor and Alert**: Provides metrics for processing lag and error rates
- **Circuit Breaking**: Prevents cascading failures by breaking circuits when downstream systems fail

Each change feed processor instance can process multiple partitions, and multiple instances can work together to process all partitions in the source container. The lease container manages coordination between processors, ensuring each change is processed exactly once even during scale-out operations.

### 2.5 Microservices Breakdown

The Skills Matrix functionality is decomposed into the following microservices:

1. **SkillsMatrix.CommandAPI**:

   - Responsible for handling all write operations
   - Exposes REST endpoints for creating/updating Skills, Workcenters, and their relationships
   - Implements validation logic and business rules
   - Writes to the source-of-truth write container
   - Publishes events for successful operations

2. **SkillsMatrix.QueryAPI**:

   - Handles all read operations from optimized read models
   - Supports various query patterns (by skill type, by workcenter, etc.)
   - Provides view-specific endpoints for UI consumption
   - Read-only access to data

3. **SkillsMatrix.SyncService**:

   - Hosts change feed processors
   - Manages synchronization between write and read models
   - Handles event processing and publishing
   - Implements the deduplication logic
   - Manages resilience and retry policies

4. **SkillsMatrix.NotificationService**:
   - Subscribes to relevant events
   - Generates and sends notifications to Team Leaders, Trainers, etc.
   - Maintains notification preferences and delivery tracking

## 3. Detailed Component Design

### 3.1 SkillsMatrix.CommandAPI

**Responsibility**: Handles all write operations and maintains the source of truth for Skills Matrix data.

**Key Components**:

- **Controllers**: REST API endpoints for commands
- **Command Handlers**: Process commands and apply business logic
- **Validators**: Validate incoming commands against business rules
- **Domain Models**: Represent the core business entities
- **Repository**: Interacts with Cosmos DB write container
- **Event Publishers**: Publish domain events to Service Bus

**Main Endpoints**:

- `/api/skills` - Manage Skills Master data
- `/api/workcenters` - Manage Work Center data
- `/api/skill-workstation-mappings` - Manage relationships between skills and workstations

### 3.2 SkillsMatrix.QueryAPI

**Responsibility**: Provides optimized read access to Skills Matrix data.

**Key Components**:

- **Controllers**: REST API endpoints for queries
- **Query Handlers**: Process queries and retrieve data
- **View Models**: Represent data optimized for specific query scenarios
- **Repository**: Interacts with Cosmos DB read containers
- **Caching Layer**: Improves performance for frequently accessed data

**Main Endpoints**:

- `/api/skills` - Query Skills Master data
- `/api/workcenters` - Query Work Center data
- `/api/workcenters/{id}/required-skills` - Get skills required for specific workcenters
- `/api/skills/{id}/workcenters` - Get workcenters requiring a specific skill

### 3.3 SkillsMatrix.SyncService

**Responsibility**: Maintains consistency between write and read models.

**Key Components**:

- **Change Feed Processors**: Monitor write containers for changes
- **Model Transformers**: Convert between write and read models
- **Event Processors**: Process and publish domain events
- **Lease Management**: Track change feed processing progress
- **Retry Policies**: Handle transient failures

### 3.4 SkillsMatrix.NotificationService

**Responsibility**: Manages notification workflows.

**Key Components**:

- **Event Subscribers**: Listen for domain events
- **Notification Templates**: Define message formats
- **Notification Dispatcher**: Send notifications via appropriate channels
- **Delivery Tracking**: Record notification statuses

## 4. Data Models

The Skills Matrix module implements CQRS with distinct write and read models. The write models are normalized for consistency and integrity, while read models are denormalized for query performance.

### 4.0 Entity Relationship Diagram

The following diagram illustrates the relationships between key entities in the Skills Matrix module:

```mermaid
erDiagram
    Skill ||--o{ SkillWorkcenterMapping : "is required for"
    Workcenter ||--o{ SkillWorkcenterMapping : "requires"

    Skill {
        string id PK
        string skillId
        string skillDescription
        string shortDescription
        string skillCriticality
        array complianceRequirements
    }

    Workcenter {
        string id PK
        string childLine
        string workcenterId
        string workstationName
        string description
        int numberOfOperators
        string workstationCriticality
    }

    SkillWorkcenterMapping {
        string id PK
        string skillId FK
        string workcenterId FK
        string projectPhase
    }

    SkillReadModel {
        string id PK
        string skillId
        string skillDescription
        string shortDescription
        string skillCriticality
        array complianceRequirements
        array workcenters
    }

    WorkcenterReadModel {
        string id PK
        string childLine
        string workcenterId
        string workstationName
        string description
        int numberOfOperators
        string workstationCriticality
        array requiredSkills
    }

    ChildLineSkillsReadModel {
        string id PK
        string childLine
        array workcenters
    }
```

### 4.1 Write Models

These models represent the source of truth for the Skills Matrix data.

#### Skill Entity

```json
{
  "id": "string", // Unique identifier (e.g., "S1")
  "type": "Skill", // Entity type discriminator
  "partitionKey": "Skill", // Partition key for Cosmos DB
  "skillId": "string", // Business identifier (e.g., "S1")
  "skillDescription": "string", // Full description (e.g., "ULTRASONIC SPLICE")
  "shortDescription": "string", // Code/abbreviation (e.g., "USW")
  "skillCriticality": "string", // "Basic Skills", "Critical Skills", "Specific/Non-Critical"
  "complianceRequirements": [
    // Associated standards (optional)
    {
      "standardName": "string", // e.g., "IPC/WHMA-A-620"
      "description": "string" // Details of the compliance requirement
    }
  ],
  "createdBy": "string", // User who created the record
  "createdDate": "string", // ISO 8601 date format
  "lastModifiedBy": "string", // User who last modified the record
  "lastModifiedDate": "string", // ISO 8601 date format
  "isDeleted": false, // Soft delete flag
  "etag": "string" // For optimistic concurrency
}
```

#### Workcenter Entity

```json
{
  "id": "string", // Unique identifier
  "type": "Workcenter", // Entity type discriminator
  "partitionKey": "Workcenter", // Partition key for Cosmos DB
  "childLine": "string", // Production line identifier (e.g., "200")
  "workcenterId": "string", // Business identifier (e.g., "200_SOP")
  "description": "string", // Description of the workcenter function
  "workstationName": "string", // User-friendly name (e.g., "Crimping Station")
  "numberOfOperators": 1, // Required operators for this workstation
  "workstationCriticality": "string", // "C" (Critical), "M" (Medium), "N" (Normal)
  "createdBy": "string", // User who created the record
  "createdDate": "string", // ISO 8601 date format
  "lastModifiedBy": "string", // User who last modified the record
  "lastModifiedDate": "string", // ISO 8601 date format
  "isDeleted": false, // Soft delete flag
  "etag": "string" // For optimistic concurrency
}
```

#### Skill-Workcenter Mapping Entity

```json
{
  "id": "string", // Unique identifier
  "type": "SkillWorkcenterMapping", // Entity type discriminator
  "partitionKey": "SkillWorkcenterMapping", // Partition key for Cosmos DB
  "skillId": "string", // Reference to Skill
  "workcenterId": "string", // Reference to Workcenter
  "projectPhase": "string", // "Prototype", "Pre-Series", or "Series"
  "createdBy": "string", // User who created the record
  "createdDate": "string", // ISO 8601 date format
  "lastModifiedBy": "string", // User who last modified the record
  "lastModifiedDate": "string", // ISO 8601 date format
  "isDeleted": false, // Soft delete flag
  "etag": "string" // For optimistic concurrency
}
```

### 4.2 Read Models

These models are optimized for various query scenarios.

#### SkillReadModel

```json
{
  "id": "string", // Same as write model id
  "type": "SkillReadModel", // Entity type discriminator
  "partitionKey": "Skill", // Partition key for Cosmos DB
  "skillId": "string", // Business identifier
  "skillDescription": "string", // Full description
  "shortDescription": "string", // Code/abbreviation
  "skillCriticality": "string", // Criticality category
  "complianceRequirements": [
    // Associated standards (if any)
    {
      "standardName": "string",
      "description": "string"
    }
  ],
  "workcenters": [
    // Denormalized list of associated workcenters
    {
      "workcenterId": "string",
      "workstationName": "string",
      "childLine": "string",
      "projectPhase": "string"
    }
  ],
  "lastModifiedDate": "string" // For tracking freshness
}
```

#### WorkcenterReadModel

```json
{
  "id": "string", // Same as write model id
  "type": "WorkcenterReadModel", // Entity type discriminator
  "partitionKey": "Workcenter", // Partition key for Cosmos DB
  "childLine": "string", // Production line identifier
  "workcenterId": "string", // Business identifier
  "workstationName": "string", // User-friendly name
  "description": "string", // Description of workcenter function
  "numberOfOperators": 1, // Required operators
  "workstationCriticality": "string", // Criticality level
  "requiredSkills": [
    // Denormalized list of required skills
    {
      "skillId": "string",
      "skillDescription": "string",
      "shortDescription": "string",
      "skillCriticality": "string",
      "projectPhase": "string" // Phase when this skill is required
    }
  ],
  "lastModifiedDate": "string" // For tracking freshness
}
```

#### ChildLineSkillsReadModel

```json
{
  "id": "string", // Unique identifier (childLine value)
  "type": "ChildLineSkillsReadModel", // Entity type discriminator
  "partitionKey": "ChildLine", // Partition key for Cosmos DB
  "childLine": "string", // Production line identifier
  "workcenters": [
    // All workcenters in this childLine
    {
      "workcenterId": "string",
      "workstationName": "string",
      "workstationCriticality": "string",
      "numberOfOperators": 1,
      "requiredSkills": [
        // Skills required for this workcenter
        {
          "skillId": "string",
          "shortDescription": "string",
          "skillCriticality": "string"
        }
      ]
    }
  ],
  "lastModifiedDate": "string" // For tracking freshness
}
```

## 5. API Specification

### 5.1 Command API Endpoints

#### Create Skill

- **Endpoint**: `POST /api/skills`
- **Purpose**: Add a new skill to the Skills Master
- **Authorization**: Requires `skills:create` permission
- **Request Body**:
  ```json
  {
    "skillId": "S25",
    "skillDescription": "Terminal Crimping",
    "shortDescription": "CRMP",
    "skillCriticality": "Critical Skills",
    "complianceRequirements": [
      {
        "standardName": "IPC/WHMA-A-620",
        "description": "Class 3 High Performance Electronic Products"
      }
    ]
  }
  ```
- **Response**: 201 Created with the created resource

#### Update Skill

- **Endpoint**: `PUT /api/skills/{id}`
- **Purpose**: Update an existing skill
- **Authorization**: Requires `skills:update` permission
- **Request Body**: Same as Create with updated values
- **Response**: 200 OK with updated resource

#### Create Workcenter

- **Endpoint**: `POST /api/workcenters`
- **Purpose**: Add a new workcenter
- **Authorization**: Requires `workcenters:create` permission
- **Request Body**:
  ```json
  {
    "childLine": "200",
    "workcenterId": "200_P25",
    "workstationName": "Terminal Crimp Station 5",
    "description": "Crimping of terminals onto 18-22 AWG wires",
    "numberOfOperators": 2,
    "workstationCriticality": "C"
  }
  ```
- **Response**: 201 Created with the created resource

#### Map Skill to Workcenter

- **Endpoint**: `POST /api/skill-workstation-mappings`
- **Purpose**: Associate a skill with a workcenter
- **Authorization**: Requires `skillmapping:create` permission
- **Request Body**:
  ```json
  {
    "skillId": "S25",
    "workcenterId": "200_P25",
    "projectPhase": "Series"
  }
  ```
- **Response**: 201 Created with the created mapping

### 5.2 Query API Endpoints

#### Get All Skills

- **Endpoint**: `GET /api/skills`
- **Purpose**: Retrieve all skills with optional filtering
- **Authorization**: Requires `skills:read` permission
- **Query Parameters**:
  - `criticality`: Filter by skill criticality
  - `page`: Page number (default: 1)
  - `pageSize`: Items per page (default: 20)
- **Response**: 200 OK with paginated list of skills

#### Get Workcenter with Required Skills

- **Endpoint**: `GET /api/workcenters/{id}`
- **Purpose**: Get workcenter details including required skills
- **Authorization**: Requires `workcenters:read` permission
- **Query Parameters**:
  - `projectPhase`: Optional filter by project phase
- **Response**: 200 OK with workcenter and its skills

#### Get Skills for ChildLine

- **Endpoint**: `GET /api/childlines/{id}/skills`
- **Purpose**: Get all skills required for a production line
- **Authorization**: Requires `childlines:read` permission
- **Query Parameters**:
  - `projectPhase`: Optional filter by project phase
- **Response**: 200 OK with skills grouped by workcenter

## 6. Event Communication

The Skills Matrix module uses Azure Service Bus for reliable event-based communication.

### 6.1 Event Schema

All events follow a common envelope format:

```json
{
  "id": "string", // Unique event identifier (UUID)
  "eventType": "string", // Type of event
  "eventTime": "string", // ISO 8601 timestamp
  "subject": "string", // Subject of the event (e.g., "Skill/S25")
  "dataVersion": "1.0", // Schema version
  "data": {
    // Event-specific payload
    // Varies by event type
  }
}
```

### 6.2 Domain Events

#### SkillCreatedEvent

```json
{
  "id": "a0b1c2d3-e4f5-6789-abcd-ef0123456789",
  "eventType": "SkillCreated",
  "eventTime": "2023-05-15T14:30:00Z",
  "subject": "Skill/S25",
  "dataVersion": "1.0",
  "data": {
    "skillId": "S25",
    "skillDescription": "Terminal Crimping",
    "shortDescription": "CRMP",
    "skillCriticality": "Critical Skills",
    "complianceRequirements": [
      {
        "standardName": "IPC/WHMA-A-620",
        "description": "Class 3 High Performance Electronic Products"
      }
    ]
  }
}
```

#### SkillUpdatedEvent

```json
{
  "id": "b1c2d3e4-f5g6-7890-abcd-ef0123456789",
  "eventType": "SkillUpdated",
  "eventTime": "2023-05-16T09:45:00Z",
  "subject": "Skill/S25",
  "dataVersion": "1.0",
  "data": {
    "skillId": "S25",
    "skillDescription": "Terminal Crimping - Updated",
    "shortDescription": "CRMP",
    "skillCriticality": "Critical Skills",
    "complianceRequirements": [
      {
        "standardName": "IPC/WHMA-A-620",
        "description": "Class 3 High Performance Electronic Products"
      }
    ]
  }
}
```

#### WorkcenterCreatedEvent

```json
{
  "id": "c2d3e4f5-g6h7-8901-abcd-ef0123456789",
  "eventType": "WorkcenterCreated",
  "eventTime": "2023-05-17T11:15:00Z",
  "subject": "Workcenter/200_P25",
  "dataVersion": "1.0",
  "data": {
    "childLine": "200",
    "workcenterId": "200_P25",
    "workstationName": "Terminal Crimp Station 5",
    "description": "Crimping of terminals onto 18-22 AWG wires",
    "numberOfOperators": 2,
    "workstationCriticality": "C"
  }
}
```

#### SkillWorkcenterMappingCreatedEvent

```json
{
  "id": "d3e4f5g6-h7i8-9012-abcd-ef0123456789",
  "eventType": "SkillWorkcenterMappingCreated",
  "eventTime": "2023-05-18T13:30:00Z",
  "subject": "SkillWorkcenterMapping/S25_200_P25",
  "dataVersion": "1.0",
  "data": {
    "skillId": "S25",
    "workcenterId": "200_P25",
    "projectPhase": "Series"
  }
}
```

### 6.3 Service Bus Topic Structure

The Skills Matrix module uses the following topics and subscriptions:

- **Topic**: `skillsmatrix-events`
  - **Subscription**: `sync-service` - Handles synchronization between write and read models
  - **Subscription**: `notification-service` - Processes events for notifications
  - **Subscription**: `versatility-matrix` - Updates the Versatility Matrix based on skill changes
  - **Subscription**: `training-process` - Updates training requirements based on skill changes

### 6.4 Key Operations Sequence

The following sequence diagram illustrates the flow of a typical write operation (creating a new skill) through the system, showcasing the CQRS pattern and event-driven architecture:

```mermaid
sequenceDiagram
    participant Client
    participant CommandAPI as Skills Command API
    participant WriteRepo as Write Repository
    participant CDB as Cosmos DB Write Container
    participant EventPub as Event Publisher
    participant ASB as Azure Service Bus
    participant CFP as Change Feed Processor
    participant ReadRepo as Read Repository
    participant ReadDB as Cosmos DB Read Containers
    participant Subscribers as Event Subscribers

    Client->>CommandAPI: POST /api/skills
    CommandAPI->>CommandAPI: Validate Request
    CommandAPI->>WriteRepo: CreateSkill(skillData)
    WriteRepo->>CDB: Save Skill Document
    CDB-->>WriteRepo: Success
    WriteRepo-->>CommandAPI: Skill Created
    CommandAPI->>EventPub: Publish SkillCreatedEvent
    EventPub->>ASB: Send Event Message
    CommandAPI-->>Client: 201 Created Response

    Note over CDB,CFP: Asynchronous Processing

    CDB->>CFP: Change Feed Event
    CFP->>CFP: Transform Write Model to Read Model
    CFP->>ReadRepo: Update Read Models
    ReadRepo->>ReadDB: Upsert Read Documents

    ASB->>Subscribers: Process SkillCreatedEvent
    Subscribers->>Subscribers: Process Notifications
```

This diagram shows:

1. **Synchronous Path**:

   - Client submits a request to create a skill
   - Command API validates and processes the request
   - Write Repository persists the data in Cosmos DB
   - Event is published to Service Bus
   - Client receives success response

2. **Asynchronous Processing**:
   - Change Feed Processor detects the document change
   - Transforms the write model to appropriate read model(s)
   - Updates read containers with the new data
   - Event subscribers process the event and trigger notifications

This separation of synchronous and asynchronous processing is key to the CQRS implementation, allowing for immediate response to the client while background processes handle read model updates and downstream effects.

## 7. Azure Services Integration

The Skills Matrix module is deployed as a cloud-native solution on Azure, leveraging managed services for scalability, resilience, and operational efficiency.

### 7.0 Azure Deployment Architecture

The following diagram illustrates how the Skills Matrix components are deployed on Azure:

```mermaid
flowchart TB
    subgraph "Azure App Service Plan"
        SCAPI[Skills Command API\nApp Service]
        SQAPI[Skills Query API\nApp Service]
        SSYNC[Skills Sync Service\nApp Service]
        SNOTIF[Skills Notification\nApp Service]
    end

    subgraph "Azure Cosmos DB Account"
        direction LR
        CDB[(Cosmos DB)]
        WC[Write Container]
        RC[Read Containers]
        LC[Lease Container]
        CDB --- WC
        CDB --- RC
        CDB --- LC
    end

    subgraph "Azure Service Bus"
        NS[Service Bus Namespace]
        T[skillsmatrix-events Topic]
        S1[sync-service Subscription]
        S2[notification-service Subscription]
        S3[versatility-matrix Subscription]
        S4[training-process Subscription]
        DLQ[Dead-Letter Queue]

        NS --- T
        T --- S1
        T --- S2
        T --- S3
        T --- S4
        S1 --- DLQ
        S2 --- DLQ
        S3 --- DLQ
        S4 --- DLQ
    end

    subgraph "Azure Key Vault"
        KV[Key Vault\nSecrets & Keys]
    end

    subgraph "Azure Monitor"
        AM[Application Insights]
        LOGS[Log Analytics]
        DASH[Dashboards]

        AM --- LOGS
        LOGS --- DASH
    end

    subgraph "Azure API Management"
        APIM[API Management]
    end

    SCAPI --> WC
    SCAPI --> T
    SSYNC --> WC
    SSYNC --> RC
    SSYNC --> LC
    SSYNC --> S1
    SQAPI --> RC
    SNOTIF --> S2

    APIM --> SCAPI
    APIM --> SQAPI

    SCAPI --> KV
    SQAPI --> KV
    SSYNC --> KV
    SNOTIF --> KV

    SCAPI --> AM
    SQAPI --> AM
    SSYNC --> AM
    SNOTIF --> AM

    classDef appService fill:#0078D7,stroke:#004E8C,color:white
    classDef cosmos fill:#3CA9F5,stroke:#0078D7,color:white
    classDef serviceBus fill:#8C4799,stroke:#5C2D62,color:white
    classDef keyVault fill:#4CAF50,stroke:#2E7D32,color:white
    classDef monitor fill:#FF8F00,stroke:#EF6C00,color:white
    classDef apim fill:#00BCD4,stroke:#00838F,color:white

    class SCAPI,SQAPI,SSYNC,SNOTIF appService
    class CDB,WC,RC,LC cosmos
    class NS,T,S1,S2,S3,S4,DLQ serviceBus
    class KV keyVault
    class AM,LOGS,DASH monitor
    class APIM apim
```

This architecture implements a cloud-native approach with:

- **Compute**: Azure App Services for hosting the microservices
- **Data Storage**: Azure Cosmos DB for document storage with separate containers for write and read models
- **Messaging**: Azure Service Bus for reliable, asynchronous event communication
- **Security**: Azure Key Vault for secret management and credential storage
- **Monitoring**: Application Insights and Log Analytics for telemetry and operational insights
- **API Gateway**: API Management for API exposure, documentation, and access control

### 7.1 Cosmos DB Configuration

#### Containers and Partitioning

1. **Write Container**:

   - **Name**: `SkillsMatrixWrite`
   - **Partition Key**: `/partitionKey`
   - **Throughput**: 400 RU/s (autoscale enabled)
   - **Indexing**: Optimized for write operations

2. **Read Containers**:

   - **Name**: `SkillsMatrixReadSkills`
     - **Partition Key**: `/partitionKey`
     - **Throughput**: 400 RU/s (autoscale enabled)
     - **Indexing**: Optimized for skill queries
   - **Name**: `SkillsMatrixReadWorkcenters`
     - **Partition Key**: `/partitionKey`
     - **Throughput**: 400 RU/s (autoscale enabled)
     - **Indexing**: Optimized for workcenter queries
   - **Name**: `SkillsMatrixReadChildLines`
     - **Partition Key**: `/partitionKey`
     - **Throughput**: 400 RU/s (autoscale enabled)
     - **Indexing**: Optimized for production line queries

#### Change Feed Processors

1. **SkillChangeFeedProcessor**:

   - **Source**: Write container
   - **Target**: Skills read container
   - **Lease Container**: `SkillsMatrixLeases`
   - **Batch Size**: 100 items
   - **Polling Interval**: 5 seconds

2. **WorkcenterChangeFeedProcessor**:

   - **Source**: Write container
   - **Target**: Workcenters read container
   - **Lease Container**: `SkillsMatrixLeases`
   - **Batch Size**: 100 items
   - **Polling Interval**: 5 seconds

3. **SkillWorkcenterMappingChangeFeedProcessor**:
   - **Source**: Write container
   - **Target**: Updates both Skills and Workcenters read containers
   - **Lease Container**: `SkillsMatrixLeases`
   - **Batch Size**: 100 items
   - **Polling Interval**: 5 seconds

### 7.2 Service Bus Configuration

#### Namespace and Topic

- **Namespace**: `connected-workers-servicebus`
- **Topic**: `skillsmatrix-events`
  - **Max Size**: 5 GB
  - **Message TTL**: 14 days
  - **Duplicate Detection**: Enabled, 10-minute window

#### Subscriptions

1. **sync-service**:

   - **Max Delivery Count**: 10
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 5 minutes

2. **notification-service**:

   - **Max Delivery Count**: 5
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 2 minutes

3. **versatility-matrix**:

   - **Max Delivery Count**: 8
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 3 minutes

4. **training-process**:
   - **Max Delivery Count**: 8
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 3 minutes

## 8. Security and Permission Models

### 8.1 Authorization Model

The Skills Matrix module implements a resource-based permission model where:

- **Resources** are the entities in the system (skills, workcenters, mappings)
- **Actions** are operations that can be performed on resources (create, read, update, delete)
- **Permissions** are defined as `resource:action` pairs
- **Roles** are collections of permissions assigned to users

#### Resources

1. `skills` - Skills Master data
2. `workcenters` - Work Center data
3. `skillmapping` - Skill-to-Workcenter mappings
4. `childlines` - Production line data

#### Actions

1. `create` - Create a new resource
2. `read` - View resource data
3. `update` - Modify existing resource
4. `delete` - Remove a resource
5. `export` - Export resource data
6. `import` - Import resource data

#### Permissions

| Permission            | Description                      |
| --------------------- | -------------------------------- |
| `skills:create`       | Create new skills                |
| `skills:read`         | View skills data                 |
| `skills:update`       | Modify existing skills           |
| `skills:delete`       | Remove skills                    |
| `skills:export`       | Export skills data               |
| `skills:import`       | Import skills data               |
| `workcenters:create`  | Create new workcenters           |
| `workcenters:read`    | View workcenter data             |
| `workcenters:update`  | Modify existing workcenters      |
| `workcenters:delete`  | Remove workcenters               |
| `workcenters:export`  | Export workcenter data           |
| `workcenters:import`  | Import workcenter data           |
| `skillmapping:create` | Create skill-workcenter mappings |
| `skillmapping:read`   | View mapping data                |
| `skillmapping:update` | Modify existing mappings         |
| `skillmapping:delete` | Remove mappings                  |
| `childlines:read`     | View production line data        |
| `childlines:export`   | Export production line data      |

#### Roles

| Role                 | Description                         | Permissions                                                                                                                                                                                                     |
| -------------------- | ----------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **ME_Engineer**      | Manufacturing Engineering personnel | `skills:read`, `workcenters:create`, `workcenters:read`, `workcenters:update`, `skillmapping:create`, `skillmapping:read`, `skillmapping:update`, `skillmapping:delete`, `childlines:read`, `childlines:export` |
| **Training_Manager** | Training Department personnel       | `skills:create`, `skills:read`, `skills:update`, `skills:delete`, `skills:export`, `skills:import`, `workcenters:read`, `skillmapping:read`, `childlines:read`                                                  |
| **Quality_Engineer** | Quality Department personnel        | `workcenters:read`, `workcenters:update`, `skills:read`, `skillmapping:read`, `childlines:read`                                                                                                                 |
| **Team_Leader**      | Production Team Leaders             | `skills:read`, `workcenters:read`, `skillmapping:read`, `childlines:read`                                                                                                                                       |
| **HR_Manager**       | Human Resources personnel           | `skills:read`, `workcenters:read`, `skillmapping:read`, `childlines:read`, `childlines:export`                                                                                                                  |
| **System_Admin**     | IT system administrators            | All permissions                                                                                                                                                                                                 |

### 8.2 Security Implementation

1. **Authentication**:

   - Azure Active Directory (AAD) integration
   - JWT token-based authentication
   - Token validation middleware in API services

2. **Authorization**:

   - Permission checking middleware
   - Role-based access control (RBAC)
   - Claims-based authorization
   - Auditing of security-relevant events

3. **Data Protection**:
   - Azure Key Vault for secret management
   - Data encryption at rest and in transit
   - Appropriate retention policies
   - Data access auditing

## 9. Testing Strategy

The Skills Matrix module will be tested at multiple levels to ensure functionality, performance, and security:

### 9.1 Unit Testing

- Tests individual components in isolation
- Focuses on business logic and validation rules
- Uses mocking frameworks for dependencies
- Implemented for command handlers, query handlers, and domain models

### 9.2 Integration Testing

- Tests interaction between components
- Verifies Cosmos DB operations and change feed processing
- Tests Service Bus event publishing and subscriptions
- Uses containerized testing environments

### 9.3 API Testing

- Validates API contracts and responses
- Tests authorization and permission enforcement
- Covers error handling and edge cases
- Implements automated API tests with Postman/Newman

### 9.4 Performance Testing

- Load testing for expected and peak traffic
- Verifies Cosmos DB scaling and throughput
- Measures event processing latency
- Tests read model query performance

### 9.5 Security Testing

- Authentication and authorization testing
- Penetration testing for API endpoints
- Validation of data protection measures
- Security code reviews

## 10. Conclusion

### 10.1 Design Summary

The Skills Matrix module is designed as a robust, scalable system that leverages CQRS, event-driven architecture, and microservices principles to deliver a reliable and maintainable solution. Key design decisions include:

1. **Data Segregation**: Separate write and read models optimized for their respective purposes, improving performance and scalability.
2. **Eventual Consistency**: Change feed-based synchronization ensures read models reflect write model updates, with appropriate mechanisms for handling temporary inconsistencies.
3. **Loose Coupling**: Event-based communication enables independent development, deployment, and scaling of services.
4. **Resilience**: Comprehensive retry policies, error handling, and monitoring ensure system reliability even during partial failures.
5. **Security**: Fine-grained permissions and role-based access control protect data while enabling appropriate access.
6. **Performance**: Optimized data models, efficient query patterns, and appropriate scaling of Azure resources deliver responsive user experiences.

This architecture balances complexity with pragmatism, introducing the benefits of CQRS and event-driven design without unnecessary abstraction. The system is designed to be:

- **Maintainable**: Clear separation of concerns and modular components
- **Extensible**: Easy to add new features or modify existing functionality
- **Observable**: Comprehensive logging and monitoring
- **Scalable**: Ability to handle growing data and user loads
- **Reliable**: Resilient to partial failures and transient issues

### 10.2 Key Success Metrics

The implementation of the Skills Matrix module will be evaluated against the following metrics:

1. **Performance**:

   - API response times < 500ms for 95th percentile
   - Read model synchronization delay < 2 seconds
   - Support for 500+ concurrent users

2. **Reliability**:

   - 99.9% service availability
   - Zero data loss
   - Graceful degradation during partial outages

3. **Scalability**:

   - Support for 100,000+ skills and workcenters
   - Linear cost scaling with data growth
   - Ability to handle 10x traffic spikes

4. **Business Value**:
   - Reduced time to update skill requirements by 75%
   - Improved visibility of skill-workstation relationships
   - Seamless integration with Training and Versatility Matrix modules

The Skills Matrix module provides a solid foundation for the Connected Workers platform, enabling effective management of skills, workcenters, and their relationships within the automobile cabling/wiring industry context.
