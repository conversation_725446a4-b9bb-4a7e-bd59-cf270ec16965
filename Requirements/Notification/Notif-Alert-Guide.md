# Service Bus Notification Payloads Guide

## Overview

This guide explains how to format payloads to send through Azure Service Bus to the NotificationSignalR service. The system supports two main types of messages:

1. **Notifications** (`notification.in.app`) – General information notifications  
2. **Alerts** (`alert.in.app`) – Action-requiring notifications

---

## Message Structure

All messages must follow this basic structure:

```
{
"subject": "[message_type]",
"body": {
// Message-specific properties
}
}
```    
Where [message_type] is one of:

notification.in.app

alert.in.app
# 1. Notification Payloads (notification.in.app)
Example Payload
```
{
  "subject": "notification.in.app",
  "body": {
    "title": "Document",
    "actionMessage": "Generate a",
    "message": "The system has been updated to version 2.3.0",
    "type": "INFORMATION",
    "severity": "LOW",
    "requesterID": "system",
    "requesterName": "System Administrator",
    "timestamp": "2025-07-28T14:30:00Z",
    "recipientsId": ["user1", "user2", "admin"]
  }
}
```
```mermaid
classDiagram
    class NotificationBody {
        String title <"required"> : Short title of the notification
        String actionMessage : Optional subject line
        String message <"required"> : Main notification content
        String type : Notification type (INFORMATION or WARNING, default: INFORMATION)
        String severity : Severity level (LOW, MEDIUM, HIGH, CRITICAL, default: MEDIUM)
        String requesterID : ID of the requester or system
        String requesterName : Name of the requester
        DateTime timestamp : Created time (ISO-8601, default: current UTC)
        String[] recipientsId <"required"> : Recipients of the notification
    }
```
 # 2. Alert Payloads (alert.in.app)
Alerts are more actionable notifications that may require user response.
### Example 1: Approval Alert
```
{
  "subject": "alert.in.app",
  "body": {
    "title": "Expense Approval Required",
    "actionMessage": "Travel Expense Approval",
    "message": "Please review and approve the expense report for $1,250.00",
    "type": "APPROVAL",
    "severity": "MEDIUM",
    "actionIdentifierId": "expense-123456",
    "requesterID": "employee-789",
    "requesterName": "Jane Smith",
    "timestamp": "2023-08-15T11:45:00Z",
    "recipientsId": ["manager-456"]
  }
}

```
### Example 2: Redirect Alert
```
{
  "subject": "alert.in.app",
  "body": {
    "title": "Document Review Needed",
    "actionMessage": "Contract Ready for Review",
    "message": "A new contract document requires your review",
    "type": "REDIRECT",
    "severity": "HIGH",
    "redirectTarget": "VISUAL_CHECK",
    "actionIdentifierId": "document-89012",
    "requesterID": "legal-team",
    "requesterName": "Legal Department",
    "timestamp": "2023-08-15T14:30:00Z",
    "recipientsId": ["reviewer-123", "manager-456"]
  }
}
```
```mermaid
classDiagram
    class AlertBody {
        String title <"required"> : Short title of the alert
        String actionMessage : Optional subject line
        String message <"required"> : Main alert content
        String type <"required"> : Alert type (APPROVAL or REDIRECT)
        String severity : Severity level (LOW, MEDIUM, HIGH, CRITICAL, default: MEDIUM)
        String redirectTarget <"conditional"> : Target to redirect to (required for REDIRECT type)
        String actionIdentifierId : Identifier for the action required
        String requesterID : ID of the requester, It can be either an employee or the system sending the notification.
        String requesterName : Name of the requester
        DateTime timestamp : Created time (ISO-8601, default: current UTC)
        String[] recipientsId <"required"> : Recipients of the alert
    }
```
Enum Values Reference
#### NotificationType
- INFORMATION - General informational notification
- WARNING - Warning notification that needs attention
-   Both types enum values are case sensitive
#### NotificationSeverity & AlertSeverity
-   LOW - Low importance
-   MEDIUM - Medium importance (default)
-   HIGH - High importance
-   CRITICAL - Critical importance, requires immediate attention
-   All types enum values are case sensitive

#### AlertType
-   APPROVAL - Alert that requires user approval
-   REDIRECT - Alert that redirects user to another view
-   Both types enum values are case sensitive  

#### RedirectTarget (Required when AlertType is REDIRECT)
-   VISUAL_CHECK - Redirects to visual inspection screen
-   OVERTIME_APPROVAL - Redirects to overtime approval screen
Important Notes
1.  Recipients: Both notifications and alerts must include at least one recipient user ID in the recipientsId array.
2.  Timestamps: Provide timestamps in ISO-8601 format (e.g., 2023-08-15T14:30:00Z). If omitted, the current UTC time will be used.
3.  Type Case Sensitivity:


#### ActionIdentifierId (Required when AlertType is APPROVAL)
1. When setting AlertType to APPROVAL, you must provide a valid actionIdentifierId value

### Error Handling
-   If invalid enum values are provided (e.g., invalid alert type), the system will fall back to default values.
-   Messages without required fields may not be processed correctly.
-   Invalid Payloads structures will be rejected
-   Null values are accepted for non required fields but are not recommanded

Sample Payloads For Common Scenarios
### System Maintenance Notification (Information)

```
{
  "subject": "notification.in.app",
  "body": {
    "title": "Scheduled Maintenance",
    "actionMessage": "System Downtime",
    "message": "The system will be unavailable on Sunday from 2-4 AM for scheduled maintenance.",
    "type": "Information",
    "severity": "MEDIUM",
    "requesterID": "system",
    "requesterName": "System Administrator",
    "recipientsId": ["all-users"],
    "timestamp": "2025-07-28T14:30:00Z",
  }
}

```
### Critical Security Warning (Warning)
```
{
  "subject": "notification.in.app",
  "body": {
    "title": "Security Alert",
    "actionMessage": "Unauthorized Access Attempt",
    "message": "Multiple failed login attempts detected for your account from an unknown device.",
    "type": "Warning",
    "severity": "CRITICAL",
    "requesterID": "security-system",
    "requesterName": "Security Monitor",
    "recipientsId": ["user-123"],
    "timestamp": "2025-07-28T14:30:00Z",
  }
}

```
### Expense Approval Alert
```
{
  "subject": "alert.in.app",
  "body": {
    "title": "Expense Approval Required",
    "actionMessage": "Travel Expense Report",
    "message": "An expense report for $1,250.00 requires your approval",
    "type": "APPROVAL",
    "severity": "MEDIUM",
    "actionIdentifierId": "expense-123456",
    "requesterID": "employee-789",
    "requesterName": "Jane Smith",
    "recipientsId": ["manager-456"],
    "timestamp": "2025-07-28T14:30:00Z",
  }
}

```
### Visual Check Redirect Alert
```
{
  "subject": "alert.in.app",
  "body": {
    "title": "Equipment Inspection Required",
    "actionMessage": "Safety Check",
    "message": "Equipment XYZ-123 requires visual inspection before operation",
    "type": "REDIRECT",
    "severity": "HIGH",
    "redirectTarget": "VISUAL_CHECK",
    "actionIdentifierId": "inspection-789",
    "requesterID": "safety-system",
    "requesterName": "Safety Monitor",
    "recipientsId": ["TeamLeader1Id", "TeamLeader2Id"],
    "timestamp": "2025-07-28T14:30:00Z",
  }
}

```
### Overtime Approval Redirect Alert
```
{
  "subject": "alert.in.app",
  "body": {
    "title": "Overtime Approval Required",
    "actionMessage": "Team Overtime Request",
    "message": "Development team requests overtime approval for sprint completion",
    "type": "REDIRECT",
    "severity": "MEDIUM",
    "redirectTarget": "OVERTIME_APPROVAL",
    "actionIdentifierId": "overtime-234",
    "requesterID": "team-lead-123",
    "requesterName": "Development Team Lead",
    "recipientsId": ["manager-456"],
     "timestamp": "2025-07-28T14:30:00Z",
  }
}
```


