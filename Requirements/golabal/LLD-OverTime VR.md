# Low-Level Design Document: [Architecture for the OverTime Management ]

## Document Information

**Version:** [1.1.0]  
**Last Updated:** [09/05/2025]  
**Status:** [In Progress]  
**Authors: <AUTHORS>

## Executive Summary

### Key Features
- Microservices-based architecture with three core services
- Event-driven communication using Azure Service Bus
- Configurable business rules and validation
- Multi-country support with country-specific variations
- Real-time data synchronization and monitoring
- Role-based access control and security

### Business Benefits
- Scalable and maintainable Overtime management system
- Flexible configuration for different country requirements
- Improved operational efficiency through automation
- Enhanced data security and compliance
- Real-time monitoring and reporting capabilities

### Document Focus
- This document provides detailed technical specifications for the OverTime Management module, including architecture, domain models, database design.

## Table of Contents
## Table of Contents
1. [Overview](#1-overview)
   
2. [Architecture Overview](#2-system-architecture-overview)
  
3. [Core components](#3-core-components)
    1. [OvertimeRequest Service](#31-overtimerequest-service)
    2. [ApprovalWorkflow Service](#32-approvalworkflow-service) 
    3. [Shift Management Service](#33-shift-management-service)
    4. [TKSIntegration Services](#34-tksintegration-services)
    5. [Ends points](#5-ends-points)
6. [Conclusion](#6-conclusion)
# 1. Overview

. OvertimeRequest Service
2. ApprovalWorkflow Service
3. ShiftManagement Service
4. Notification Services
5. TKSIntegration Services
6. 
## 1.1 Purpose and Scope

This low-level design details the technical architecture and functional components of the overtime management module.based on Domain-Driven Design (DDD) principles and microservices architecture.
The design prioritizes configurability, flexibility. 
The system aims to implement complete digitalization via TKS, the TL tablet, and Optitime. It covers all types of personnel: DH, IH, IS.

## 1.2 Functional architecture
## 1.2.1 Key modules

```mermaid
flowchart TD
    Soumission["Soumission OT Planifié<br>Input of overtime requests with employee selection, project, activity, etc."]
    Validation["Validation OT<br>Hierarchical validation chain (Coordinator → Department Manager → Project Manager) with possible modifications"]
    Execution["Exécution OT<br>Marking and closing of shifts with indicators (e.g., X, Y, W)"]
    CrossCheck["Cross-check OT<br>Comparison between planned and executed hours"]
    Reporting["Reporting & Historique<br>Generation of reports (PDF/Excel) and complete audit history"]

    Soumission --> Validation
    Validation --> Execution
    Execution --> CrossCheck
    CrossCheck --> Reporting
```

## 1.2.2 Explanation of the Diagram

1. Soumission OT Planifié:

    - Input of overtime requests with employee selection, project, activity, etc.
2. Validation OT:

- Hierarchical validation chain (Coordinator → Department Manager → Project Manager) with possible modifications.
3. Exécution OT:

- Marking and closing of shifts with indicators (e.g., X, Y, W).
4. Cross-check OT:

- Comparison between planned and executed hours.
5. Reporting & Historique:

- Generation of reports (PDF/Excel) and complete audit history.
## 1.2.3 Actors and roles
```mermaid
flowchart TD
    TeamLeader[Team Leader<br>Saisit les demandes DH, clôture les shifts]
    Clerk[Clerk<br>Saisie OT IH/IS, mise à jour du système]
    ShiftLeader[Shift Leader<br>Valide OT exécuté]
    Coordinator[Coordinator<br>Valide/modifie les OT]
    DepartmentManager[Department Manager<br>Modère les demandes avant envoi au PM]
    PlantManager[Plant Manager<br>Dernier niveau de validation]
    TKSAgent[TKS Agent<br>Contrôle des données et rapprochement plan/exécuté]

    TeamLeader --> Clerk
    Clerk --> ShiftLeader
    ShiftLeader --> Coordinator
    Coordinator --> DepartmentManager
    DepartmentManager --> PlantManager
    PlantManager --> TKSAgent
```
## 1.3 Key Components

The system consists of three primary microservices:

1. OvertimeRequest Service
2. ApprovalWorkflow Service
3. ShiftManagement Service
4. Notification Services
5. TKSIntegration Services

Each service is supported by:

- Azure CosmosDB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Configuration Service for centralized settings

## 2. System Architecture Overview

## 2.1 High-Level Architecture
The system follows a microservices architecture with the following components:

```mermaid
flowchart TD
    ApiGateway[Azure API Management] --> OvertimeRequestService[Overtime Request Service]
    ApiGateway --> ApprovalWorkflowService[Approval Workflow Service]
    ApiGateway --> ShiftManagementService[Shift Management Service]
    ApiGateway --> NotificationService[Notification Service]
    ApiGateway --> TKSIntegrationService[TKS Integration Service]

    OvertimeRequestService --> OvertimeDB[(Overtime Request CosmosDB)]
    ApprovalWorkflowService --> WorkflowDB[(Approval Workflow CosmosDB)]
    ShiftManagementService --> ShiftDB[(Shift Management CosmosDB)]
    NotificationService --> NotificationDB[(Notification CosmosDB)]
    TKSIntegrationService --> TKSDB[(TKS Integration CosmosDB)]

```
## 2.1.1 Explanation of the Diagram
1. **API Gateway (Azure API Management):**

- Acts as the single entry point for all client requests.
- Routes requests to the appropriate microservices.

2. **Overtime Request Service:**

- Handles the creation, modification, and management of overtime requests (planned/unplanned).
- Stores data in Overtime Request CosmosDB.
3. **Approval Workflow Service:**

- Manages the multi-level approval process (Coordinator → Department Manager → Project Manager).
- Stores data in Approval Workflow CosmosDB.
4. **Shift Management Service:**

- Handles shift closures and assigns statuses (X/Y/W).
- Stores data in Shift Management CosmosDB.
5. **Notification Service:**

- Sends notifications to users via SignalR, email, or other channels.
- Stores data in Notification CosmosDB.
6. **TKS Integration Service:**

- Performs cross-checks between planned and executed overtime.
- Synchronizes data with TKS.
- Stores data in TKS Integration CosmosDB.
7. **Azure Service Bus** :
   - Facilitates asynchronous communication between microservices.
   -Manages events like station updates, schedule changes, etc..

8. **Bases de données CosmosDB** :
   - Each microservice has its own database to respect the principle of data separation.
   - CosmosDB is used for its scalability, low latency, and multi-region support.
  
## 3. Core components

Here is a description of the possible actions for each microservice, based on the data provided. This will help to better structure the **High-Level Architecture**.

### **3.1 OvertimeRequest Service**
1. **Responsibilities:**
- Manage the creation, modification, and deletion of overtime requests.
- Handle both planned and unplanned overtime requests.
- Validate input data (e.g., employee selection, project, activity).
- Publish events for downstream services (e.g., approval workflows).
2. **Key Entities:**
- OvertimeRequest: Represents an overtime request with details like employee, project, activity, and hours.
- Employee: Represents the employee associated with the overtime request.
3. **Aggregates:**

- **OvertimeRequest Aggregate:**
  - **Root Entity**: OvertimeRequest
  - **Entities**: Employee
  - **Description**: Manages overtime request details and associated employees.

4. **Class Diagram**

```mermaid
classDiagram
    class OvertimeRequest {
        +String id
        +String employeeId
        +String projectId
        +String activityId
        +int hours
        +String status
        +createRequest()
        +updateRequest()
        +deleteRequest()
    }

    class Employee {
        +String id
        +String name
        +String department
        +String role
    }

    OvertimeRequest --> Employee : "Associated with"
```

**Explanation of the Diagram**
1. **OvertimeRequest:**
- Represents an overtime request with attributes like employee, project, activity, hours, and status.
- Actions:
    - createRequest(): Creates a new overtime request.
    - updateRequest(): Updates an existing overtime request.
    - deleteRequest(): Deletes an overtime request.
2. **Employee:**
    - Represents the employee associated with the overtime request.
    - Attributes include id, name, department, and role.
3. **Relationship:**
    - The OvertimeRequest class is associated with the Employee class, indicating that each overtime request is linked to a specific employee.
### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant OvertimeRequestService as Overtime Request Service
    participant ApprovalWorkflowService as Approval Workflow Service
    participant NotificationService as Notification Service
    participant Database as Overtime Request CosmosDB

    User->>OvertimeRequestService: Create Overtime Request (POST /overtime-requests)
    OvertimeRequestService->>Database: Save Overtime Request
    OvertimeRequestService->>ApprovalWorkflowService: Publish OvertimeRequestCreated Event
    ApprovalWorkflowService->>NotificationService: Notify Approver (Coordinator)
    NotificationService->>User: Send Notification (Request Submitted)
    ApprovalWorkflowService->>User: Approve/Reject Request (POST /approvals/{id}/approve or reject)
    ApprovalWorkflowService->>OvertimeRequestService: Update Request Status
    OvertimeRequestService->>Database: Update Request in Database
    OvertimeRequestService->>NotificationService: Notify Requester (Approval/Rejection Status)
    NotificationService->>User: Send Notification (Approval/Rejection)
```
### **Explanation of the Sequence**
1. **User Action:**
    - The user submits a new overtime request via the Overtime Request Service.
2. **Overtime Request Service:**
    - Saves the request in the Overtime Request CosmosDB.
    - Publishes an OvertimeRequestCreated event to the Approval Workflow Service.
3. **Approval Workflow Service:**
    - Notifies the first approver (e.g., Coordinator) via the Notification Service.
    - Handles the approval or rejection of the request.
4. **Notification Service:**
    - Sends notifications to the requester and approvers about the status of the request.
5. **Database Updates:**
    - The Overtime Request Service updates the request status in the database after approval or rejection.
  

### **Validation and Business Logic**
1. **Validate employee eligibility:**
- Ensure the selected employee is eligible for overtime based on company policies.
2. **Check project/activity validity:**
- Verify that the selected project or activity is valid and active.
3. **Enforce business rules:**
- Ensure overtime hours do not exceed predefined limits.
- Validate that the request aligns with company policies.
### **Use Cases**
1. **Planned Overtime:**
- Create and manage overtime requests for scheduled activities.
2. **Unplanned Overtime:**
- Handle urgent overtime requests for unexpected tasks.
3. **Audit and Reporting:**
- Provide a history of overtime requests for auditing and reporting purposes.
  
### **3.2 ApprovalWorkflow Service**

- **Responsibilities:**
   - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
   - **Allow** modifications to requests during the approval process.
   - **Publish** events for downstream services (e.g., notifications, shift management).
- **Key Entities:**
   - **Approval**: Represents the  - approval process for an overtime request.
  - **Approver**: Represents the individual responsible for approving or rejecting a request.
- **Aggregates:**
  - **Approval** Aggregate:
    - **Root Entity**: Approval
    - **Entities**: Approver
    - **Description**: Manages the approval process and tracks the status of overtime requests.


### **3.3 Shift Management Service**
### - **Responsibilities:**
- Manage the closure of shifts and assign statuses (X/Y/W).
- Handle planned and unplanned shift updates.
- Publish events for downstream services (e.g., TKS Integration, Notification Service).
- **Key Entities:**
   - **Shift**: Represents a specific shift with start and end times.
   - **EmployeeStatus**: Tracks the status of employees (e.g., present, absent).
- **Aggregates:**
  - **Shift Aggregate:**
    - **Root Entity**: Shift
    - **Entities**: EmployeeStatus
    - **Description**: Manages shift details and tracks employee statuses.

    4. **Class Diagram**

```mermaid
classDiagram
    class Shift {
        +String id
        +String shiftDate
        +String status
        +closeShift()
        +updateShift()
    }

    class EmployeeStatus {
        +String employeeId
        +String shiftId
        +String status
        +updateStatus()
    }

    class Notification {
        +String id
        +String message
        +String recipient
        +sendNotification()
    }

    Shift --> EmployeeStatus : "Tracks"
    Shift --> Notification : "Notifies"
```

Explanation of the Class Diagram
Shift:

Represents a shift with attributes like id, shiftDate, and status (e.g., X, Y, W).
Actions:
closeShift(): Closes a shift.
updateShift(): Updates shift details.
EmployeeStatus:

Tracks the status of employees for a specific shift (e.g., present, absent).
Attributes include employeeId, shiftId, and status.
Actions:
updateStatus(): Updates the status of an employee for a shift.
Notification:

Represents notifications sent to users about shift closures or updates.
Attributes include id, message, and recipient.
Actions:
sendNotification(): Sends a notification to the recipient.
Relationships:

Shift → EmployeeStatus: A shift tracks the status of employees.
Shift → Notification: A shift triggers notifications to users.

### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant ShiftManagementService as Shift Management Service
    participant NotificationService as Notification Service
    participant Database as Shift Management CosmosDB
    participant TKSIntegrationService as TKS Integration Service

    User->>ShiftManagementService: Close Shift (POST /shifts/{id}/close)
    ShiftManagementService->>Database: Save Shift Closure and Status (X/Y/W)
    ShiftManagementService->>NotificationService: Notify Users (Shift Closed)
    NotificationService->>User: Send Notification (Shift Closure Confirmation)
    ShiftManagementService->>TKSIntegrationService: Publish ShiftClosed Event
    TKSIntegrationService->>Database: Synchronize Planned vs Executed Shifts
    TKSIntegrationService->>NotificationService: Notify Admin (Synchronization Status)
```
### **Explanation of the Sequence**

1. **User Action:**
    - The user (e.g., Team Leader or Shift Leader) closes a shift via the **Shift Management Service.**
2. **Shift Management Service:**
    - Saves the shift closure and assigns a status (e.g., X, Y, W) in the **Shift Management CosmosDB**.
    - Publishes a ShiftClosed event to the TKS Integration Service.
3. **Notification Service:**
    - Sends notifications to relevant users (e.g., confirmation of shift closure).
4. **TKS Integration Service:**
    - Synchronizes planned vs. executed shifts in the database.
    - Notifies administrators about the synchronization status.
  

### **3.4. TKSIntegration Services**
- **Responsibilities:**
 - Perform cross-checks between planned and executed overtime.
 - Synchronize data with the TKS system.
 - Publish events for downstream services (e.g., notifications, reporting).
- **Key Entities:**
 - **TKSData**: Represents the data synchronized with the TKS system.
 - **Discrepancy**: Represents any mismatch between planned and executed overtime.
- **Aggregates:**
  - **TKS Integration Aggregate:**
    - **Root Entity**: TKSData
    - **Entities**: Discrepancy
    - **Description**: Manages synchronization and reconciliation with the TKS system.


### Summary of Bounded Contexts

| **Bounded Context**   | **Responsibilities**                                                   | **Key Entities**           | **Key Actions**                                                                 |
|------------------------|-----------------------------------------------------------------------|-----------------------------|---------------------------------------------------------------------------------|
| **Overtime Request**   | Manage overtime requests (creation, modification, deletion).          | OvertimeRequest, Employee   | Create, update, delete requests; publish request-related events.               |
| **Approval Workflow**  | Manage multi-level approval processes for overtime requests.          | Approval, Approver          | Approve, reject, modify requests; publish approval-related events.             |
| **Shift Management**   | Manage shift closures and assign statuses (X/Y/W).                   | Shift, EmployeeStatus       | Close, update, delete shifts; publish shift-related events.                    |
| **TKS Integration**    | Synchronize planned vs. executed overtime and resolve discrepancies. | TKSData, Discrepancy        | Synchronize data, resolve discrepancies; publish synchronization-related events.|

---


### 3.4. Shift Management Service
#### **Responsibilities:**
- Manage the closure of shifts and assign statuses (X/Y/W).
- Handle planned and unplanned shift updates.
- Publish events for downstream services (e.g., TKS Integration, Notification Service).
- Ensure compliance with business rules for shift management.

### ***API Actions**
1. **Close a shift:**
- **Endpoint**: POST /shifts/{id}/close
- **Description** : Closes a shift and assigns a status (e.g., X, Y, W).
2. **Retrieve a list of shifts:**
- **Endpoint**: GET /shifts
- **Description**: Fetches all shifts, optionally filtered by date, status, or employee.
3. **Retrieve details of a specific shift:**
- **Endpoint**: GET /shifts/{id}
- **Description**: Fetches detailed information about a specific shift.
4. **Update a shift:**
- **Endpoint**: PUT /shifts/{id}
- **Description**: Updates the details of an existing shift (e.g., start time, end time, or assigned employees).
5. **Delete a shift:**
- **Endpoint**: DELETE /shifts/{id}
- **Description**: Deletes a shift that is no longer needed.
### **Event-Driven Actions**
1. **Publish ShiftClosed event:**
- Triggered when a shift is closed.
- Consumed by the TKS Integration Service to reconcile planned vs. executed shifts.
2. **Publish ShiftUpdated event:**
- Triggered when a shift is updated.
- Consumed by downstream services to reflect changes in the shift.
3. **Publish ShiftDeleted event:**
- Triggered when a shift is deleted.
- Consumed by downstream services to remove the shift from workflows.

### **Validation and Business Logic**
1. **Validate shift closure:**
- Ensure all required tasks are completed before closing a shift.
2. **Check employee assignments:**
- Validate that employees assigned to the shift are eligible and available.
3. **Enforce business rules:**
- Ensure that shift statuses (X/Y/W) comply with company policies.
4. **Audit trail:**
- Maintain a complete history of all actions taken during shift management for auditing purposes.

### **Use Cases**
1. **Planned Shifts:**
- Manage and close shifts that are part of the planned schedule.
2. **Unplanned Shifts:**
- Handle urgent or unexpected shifts.
3. **Audit and Reporting:**
- Provide a history of shift closures and statuses for auditing and reporting purposes.
4. **Integration with TKS:**
- Publish events to synchronize planned vs. executed shifts with the TKS system.

### ********* TKS Integration Service**
#### **Responsibilities:**
- Perform cross-checks between planned and executed overtime.
- Synchronize data with the TKS system.
- Publish events for downstream services (e.g., notifications, reporting).
- Ensure data consistency between the OverTime Management system and TKS.

#### **API Actions**
1. **Synchronize planned vs. executed overtime:**
- Endpoint: POST /tks/synchronize
- Description: Synchronizes planned overtime data with executed data in the TKS system.
2. **Retrieve synchronization status:**
- Endpoint: GET /tks/synchronization-status
- Description: Fetches the status of the last synchronization process.
3. **Retrieve discrepancies:**
-Endpoint: GET /tks/discrepancies
-Description: Retrieves a list of discrepancies between planned and executed overtime.
4. **Force a manual synchronization:**
- Endpoint: POST /tks/manual-sync
- Description: Allows administrators to trigger a manual synchronization with TKS.
5. **Retrieve TKS integration logs:**
- Endpoint: GET /tks/logs
- Description: Fetches logs related to TKS integration for auditing and debugging purposes.
### **Event-Driven Actions**
1. **Publish TKSDataSynchronized event:**
- Triggered when data is successfully synchronized with TKS.
- Consumed by downstream services to confirm synchronization.
2. **Publish TKSDiscrepancyDetected event:**
- Triggered when discrepancies are found between planned and executed overtime.
- Consumed by the Notification Service to alert relevant users.
3. **Publish TKSManualSyncTriggered event:**
- Triggered when a manual synchronization is initiated.
Consumed by downstream services to log the action.

### **Validation and Business Logic**
1. **Validate synchronization data:**
- Ensure that the data being synchronized is complete and accurate.
2. **Check for discrepancies:**
- Identify and log any mismatches between planned and executed overtime.
3. **Enforce synchronization rules:**
- Ensure that synchronization processes comply with company policies and TKS requirements.
4. **Audit trail:**
- Maintain a complete history of synchronization actions and results for auditing purposes.

### **Use Cases**
1. **Planned vs. Executed Reconciliation:**
- Compare planned overtime with executed overtime and resolve discrepancies.
2. **Real-Time Synchronization:**
- Automatically synchronize data with TKS in real-time or on a scheduled basis.
3. **Manual Synchronization:**
- Allow administrators to trigger manual synchronization processes when needed.
4. **Audit and Reporting:**
- Provide a history of synchronization actions and discrepancies for auditing and reporting purposes.


2. **ApprovalWorkflow Service**
**Responsibilities:**
    - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
    - Allow modifications to requests during the approval process.
    - Publish events for downstream services (e.g., notifications, shift management).
**Key Components:**
    - **Database:** Approval Workflow CosmosDB.
    - **API Endpoints:**
        - POST /approvals/{id}/approve: Approve an overtime request.
        - POST /approvals/{id}/reject: Reject an overtime request.
        - GET /approvals/pending: Retrieve pending approvals.
        - GET /approvals/history: Retrieve approval history.
    - **Event-Driven Actions:**
        - Publish ApprovalStarted, ApprovalCompleted, ApprovalRejected.
3. **ShiftManagement Service**
**Responsibilities:**
    - Manage the closure of shifts and assign statuses (X/Y/W).
    - Handle planned and unplanned shift updates.
    - Publish events for downstream services (e.g., TKS Integration, Notification Service).
**Key Components:**
    - **Database:** Shift Management CosmosDB.
    - **API Endpoints:**
        - POST /shifts/{id}/close: Close a shift.
        - GET /shifts: Retrieve all shifts.
        - PUT /shifts/{id}: Update a shift.
        - DELETE /shifts/{id}: Delete a shift.
    - **Event-Driven Actions:**
        - Publish ShiftClosed, ShiftUpdated, ShiftDeleted.

### 5. **Ends points**

description approfondie des responsabilités, composants clés, actions possibles, et événements pour chaque microservice :

1. **OvertimeRequest Service**
**Responsabilités :**
    - Manage the creation, modification, and deletion of overtime requests.
    - Handle both planned and unplanned overtime requests.
    - Validate input data (e.g., employee selection, project, activity).
    - Publish events for downstream services (e.g., approval workflows).
**Key Components:**
    - **Database:** Overtime Request CosmosDB.
    - **API Endpoints:**
        - POST /overtime-requests: Create a new overtime request.
        - GET /overtime-requests: Retrieve all overtime requests.
        - PUT /overtime-requests/{id}: Update an existing overtime request.
        - DELETE /overtime-requests/{id}: Delete an overtime request.
    - **Event-Driven Actions:**
        - Publish OvertimeRequestCreated, OvertimeRequestUpdated, OvertimeRequestDeleted.
2. **ApprovalWorkflow Service**
**Responsibilities:**
    - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
    - Allow modifications to requests during the approval process.
    - Publish events for downstream services (e.g., notifications, shift management).
**Key Components:**
    - **Database:** Approval Workflow CosmosDB.
    - **API Endpoints:**
        - POST /approvals/{id}/approve: Approve an overtime request.
        - POST /approvals/{id}/reject: Reject an overtime request.
        - GET /approvals/pending: Retrieve pending approvals.
        - GET /approvals/history: Retrieve approval history.
    - **Event-Driven Actions:**
        - Publish ApprovalStarted, ApprovalCompleted, ApprovalRejected.
3. **ShiftManagement Service**
**Responsibilities:**
    - Manage the closure of shifts and assign statuses (X/Y/W).
    - Handle planned and unplanned shift updates.
    - Publish events for downstream services (e.g., TKS Integration, Notification Service).
**Key Components:**
    - **Database:** Shift Management CosmosDB.
    - **API Endpoints:**
        - POST /shifts/{id}/close: Close a shift.
        - GET /shifts: Retrieve all shifts.
        - PUT /shifts/{id}: Update a shift.
        - DELETE /shifts/{id}: Delete a shift.
    - **Event-Driven Actions:**
        - Publish ShiftClosed, ShiftUpdated, ShiftDeleted.
  
  Certainly! Here is a concise conclusion for your **OverTime Management System** LLD:

---

## 6. **Conclusion**

The **OverTime Management** System is designed as a robust, scalable, and maintainable solution leveraging a **microservices architecture**. Each core services OvertimeRequest, ApprovalWorkflow, ShiftManagement, and TKSIntegration—operates independently, ensuring clear separation of concerns and facilitating easier maintenance and future enhancements.

**Key technical components** such as Azure CosmosDB, Azure Service Bus, and Azure API Management provide reliable data storage, event-driven communication, and secure API management. The system supports complex business processes, including multi-level approvals, real-time shift management, and automated reconciliation with external systems like TKS.

By adopting **domain-driven design** and **clean architecture** principles, the solution ensures flexibility, configurability, and testability. This approach enables the organization to efficiently manage overtime processes, comply with business rules, and provide comprehensive audit and reporting capabilities, ultimately improving operational efficiency and supporting digital transformation goals.