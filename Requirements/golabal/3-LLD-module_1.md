# Connected Workers Platform - Module 1 Microservices Architecture

## Table of Contents

1. [Overview](#overview)
2. [Architecture Overview](#architecture-overview)
3. [Technology Stack](#technology-stack)
4. [Core Components](#core-components)

   - [Workflow Engine Service](#workflow-engine-service)
   - [Document Generation Service](#document-generation-service)
   - [Document Template Service](#document-template-service)
   - [Notification Service](#notification-service)
   - [Request Service](#request-service)
   - [UserData Service](#userdata-service)
   - [Workflow Service](#workflow-service)

5. [Conclusion](#conclusion)

## Overview

The Connected Workers Platform Module 1 implements a microservices architecture to handle various business processes efficiently and scalably. Each microservice is designed with specific responsibilities and communicates with others through well-defined interfaces using Azure Service Bus and RESTful APIs.

### Key Features

- Workflow automation and orchestration
- Document generation and management
- Multi-channel notifications
- User data management
- Request handling and tracking

### Business Benefits

- Improved process efficiency
- Better scalability and maintainability
- Enhanced user experience
- Robust error handling and monitoring
- Flexible integration capabilities

## Technology Stack

### Message queues

- Use message queues **Azure Service Bus or Azure eventHub** for asynchronous communication between microservices.
- Microservices publish messages to specific queues/topics, and other services subscribe to these queues/topics to receive messages.
- This approach decouples the services and allows for better scalability and fault tolerance.
- Implement message acknowledgment and retries to ensure message delivery.

## Database CosmosDB

- Use CosmosDB for storing workflow configurations.
- CosmosDB's document-oriented storage model allows for flexible and dynamic schema design, which is suitable for varying workflow configurations.
- It supports horizontal scaling and high availability, making it a good choice for large-scale systems.
- CosmosDB provides built-in versioning support through its change streams and versioning patterns.

### RESTful APIs

- Use RESTful APIs for synchronous communication between microservices.
- Each microservice exposes its own set of endpoints for other services to interact with.
- Use HTTP/HTTPS protocols for secure and reliable communication.
- Implement proper error handling and retries to ensure robustness.

## API Gateway with Authentication and Authorization

- Use an API Gateway (Azure API Management) to handle authentication and authorization.
- Configure the API Gateway to integrate with Azure AD for user authentication.
- Use OAuth 2.0 or JWT for token-based authentication and authorization.
- Implement role-based access control (RBAC) within the API Gateway to manage user permissions and roles.
- Store user roles and permissions in a database such as CosmosDB to allow for easy management and updates.
- Secure RESTful APIs by routing all requests through the API Gateway, ensuring only authorized users can access specific endpoints.

# Workflow Engine Service

**The Workflow Engine Service** focuses on defining and validating complex business processes through configurable workflows. It ensures correctness, flexibility, and easy versioning of workflow definitions.

## Overview of the Data Model

1. **Workflow**: The top-level entity representing an entire business process definition.
2. **WorkflowNode**: A single step in the workflow, defined by a type and a node-specific configuration.
3. **WorkflowEdge**: Defines transitions between nodes, controlling the flow of execution.
4. **Configurations**: Various configuration objects tailor node behavior (approvals, notifications, documents, conditions, etc.).

This layered approach ensures a clean separation between the overall workflow structure (Workflow, Nodes, Edges) and the per-node logic (Configs).

## Core Entities

### 1. Workflow Entity

**Purpose**: Represents the entire workflow definition, containing metadata, nodes, and edges.

**Key Fields**:

- `id` (UUID): Unique identifier for the workflow.
- `name` (string): Name of the workflow.
- `description` (string, optional): Describes the workflow's purpose.
- `isActive` (boolean): Indicates if this workflow version is active.
- `nodes` (WorkflowNode[]): An array of all nodes in this workflow.
- `edges` (WorkflowEdge[]): Defines the connections and transitions between nodes.

**Example**:

```json
{
  "id": "workflow-123",
  "name": "Leave Request Process",
  "description": "Workflow for employee leave requests",
  "isActive": false,
  "nodes": [],
  "edges": []
}
```

---

### 2. WorkflowNode Entity

**Purpose**: Each `WorkflowNode` represents one step within the workflow. The node's `type` determines which configuration object it requires.

**Key Fields**:

- `id` (UUID): Unique node identifier.
- `FormId` (string): Associated form identifier (e.g., a data collection form).
- `type` (NodeType): The node's behavior type (`START`, `END`, `SINGLE_APPROVAL`, `PARALLEL_APPROVAL`, `DOCUMENT`, `NOTIFICATION`, `CONDITION`, etc.).
- `title` (string): Human-readable title.
- `position` (NodePosition, optional): Graphical coordinates for design tools.
- `config`: Configuration object, structure depends on `type`.

**Example**:

```json
{
  "id": "node-manager-approval",
  "FormId": "leave_request_form",
  "type": "SINGLE_APPROVAL",
  "title": "Manager Approval",
  "position": { "x": 200, "y": 100 },
  "config": {
    "approver": {
      "id": "appr-1",
      "type": "ROLE",
      "value": "MANAGER"
    },
    "onReject": { "action": "STOP_FLOW" }
  }
}
```

**Supported Node Types** (Examples):

- **START**: Begins the workflow (requires a `StartNodeConfig`).
- **END**: Ends the workflow (minimal or no config).
- **SINGLE_APPROVAL**: A single approver must approve.
- **PARALLEL_APPROVAL**: Multiple approvers concurrently.
- **DOCUMENT**: Generates a document from a template.
- **NOTIFICATION**: Sends notifications to defined recipients.
- **CONDITION**: Evaluates conditions to decide which path to take next.

---

### 3. WorkflowEdge Entity

**Purpose**: `WorkflowEdge` defines how nodes connect. They represent transitions from one node to another, often conditionally or after a node completes.

**Key Fields**:

- `fromNode` (string): The ID of the starting node.
- `toNode` (string): The ID of the target node.
- `condition` (optional string): A condition to evaluate for this transition.

**Example**:

```json
{
  "fromNode": "node-manager-approval",
  "toNode": "node-document-generation",
  "condition": "approvalGranted == true"
}
```

Edges may be straightforward (sequential flow) or driven by conditions evaluated at runtime.

---

## Node Configurations

Once we've defined the workflow structure (Workflow, Nodes, Edges), we specify configurations that shape each node's behavior. Below are the configuration classes and examples.

### Common Enums and Types

- **NodeType**: `START`, `END`, `SINGLE_APPROVAL`, `PARALLEL_APPROVAL`, `DOCUMENT`, `NOTIFICATION`, `CONDITION`
- **ApproverType**: `USER`, `ROLE`
- **NotificationChannel**: `EMAIL`, `SMS`, `ON APP`
- **TimeAction**: `MOVE_TO_NEXT_NODE`
- **ConditionRuleType**: `VALUE`, `HISTORICAL`, `STATE`, `ORGANIZATIONAL`

---

### 1. StartNodeConfig

**Purpose**: Determines who can initiate the workflow.

**Fields**:

- `initiators`: An object listing allowed roles, departments, or teams.

````

**Example**:
```json
{
  "initiators": {
    "roles": ["EMPLOYEE", "TEAM_MANAGER"],
  },
  "allowProxyInitiation": true
}
````

---

---

### 3. NotificationConfig

**Purpose**: Specifies how and where to send notifications.

**Fields**:

- `recipientRules` (RecipientRule[]) - Array of rules defining who receives notifications and through which channels
  - `type` (string) - Type of recipient ("DEPARTMENT" or "ROLE")
  - `value` (string) - The department name or role ID based on type
  - `channel` (string) - The notification channel (e.g., "EMAIL", "CW", "SLACK")
  - `templateId` (string) - ID or reference to the template to use for this recipient/
  - `priority` levels (HIGH, MEDIUM, LOW)
    channel combination

**Example**:

```json
{
  "recipientRules": [
    {
      "type": "DEPARTMENT",
      "value": "MAIN_GATE",
      "priority": "LOW",
      "channels": [
        {
          "type": "EMAIL",
          "templateId": "main_gate_email_template"
        },
        {
          "type": "CW",
          "templateId": "main_gate_cw_template"
        }
      ]
    },
    {
      "type": "ROLE",
      "value": "TKS_AGENT",
      "priority": "HIGH",
      "channels": [
        {
          "type": "CW",
          "templateId": "tks_agent_cw_template"
        },
        {
          "type": "EMAIL",
          "templateId": "tks_agent_email_template"
        }
      ]
    },
    {
      "type": "ROLE",
      "value": "TRANSPORT_AGENT",
      "priority": "LOW",
      "channels": [
        {
          "type": "CW",
          "templateId": "transport_agent_notification_template"
        }
      ]
    },
    {
      "type": "ROLE",
      "value": "LABOR_RELATIONS_SUPERVISOR",
      "channels": [
        {
          "type": "EMAIL",
          "templateId": "labor_relations_supervisor_template"
        }
      ]
    }
  ]
}
```

---

### 4. Escalation Configuration

**EscalationApprover**:

```json
{
  "type": "ROLE",
  "value": "SENIOR_MANAGER"
}
```

**EscalationChainItem**:

```json
{
  "approver": { "type": "USER", "value": "user-456" },
  "afterHours": 24,
  "notifyOriginal": true
}
```

**EscalationFallback**:

```json
{
  "action": "AUTO_REJECT"
}
```

**EscalationConfig**:

```json
{
  {
  "enabled": true,
  "chain": [
    {
      "approver": { "type": "ROLE", "value": "HEAD_OF_DEPT" },
      "afterMinutes": 2880,
      "notifyOriginal": false
    },
    {
      "approver": { "type": "ROLE", "value": "DIRECTOR" },
      "afterMinutes": 1440,
      "notifyOriginal": true
    },
    {
      "approver": { "type": "ROLE", "value": "DEPARTMENT_MANAGER" },
      "afterMinutes": 720,
      "notifyOriginal": true
    }
  ],
  "finalAuthority": {
    "type": "ROLE",
    "value": "DEPARTMENT_MANAGER"
  },
  "fallback": { "action": "AUTO_APPROVE" },
}
}
```

---

### 5. ApproverConfig

Defines the approver details for approval nodes.

**Example**:

```json
{
  "id": "approver-1",
  "type": "USER",
  "value": "user-123",
  "escalation": {
    "enabled": true,
    "chain": [
      {
        "approver": { "type": "ROLE", "value": "DIRECTOR" },
        "afterHours": 24
      }
    ],
    "fallback": { "action": "AUTO_REJECT" }
  }
}
```

---

### 6. DocumentGenerationConfig

**Purpose**: Used in `DOCUMENT` nodes to generate a document.

**Fields**:

- `templateId` (string)
- `outputFormat` (string)

**Example**:

```json
{
  "templateId": "offer_letter_template",
  "outputFormat": "PDF"
}
```

---

### 7. TimingConfig

**Purpose**: Defines deadlines and reminder intervals for approvals.

**Fields**:

- `deadlineHours` (number)
- `reminderIntervalHours` (number)
- `ontimeend` (TimeAction)

**Example**:

```json
{
  "deadlineHours": 48,
  "reminderIntervalHours": 24,
  "ontimeend": "ESCALATE"
}
```

---

### 8. RejectConfig

**Purpose**: Defines what happens if an approval is rejected.

**Fields**:

- `targetNodeId` (string, optional)
- `formId` (string, optional)
- `action` ("CONTINUE" or "STOP_FLOW")

**Example**:

```json
{
  "action": "STOP_FLOW",
  "formId": "rejection_reason_form"
}
```

---

### 9. SingleApprovalConfig & ParallelApprovalConfig

**SingleApprovalConfig**:

- `approver` (ApproverConfig)
- `notifications` (NotificationConfig, optional)
- `timing` (TimingConfig, optional)
- `onReject` (RejectConfig)

**Example**:

```json
{
  "approver": {
    "id": "manager-1",
    "type": "ROLE",
    "value": "MANAGER"
  },
  "onReject": { "action": "STOP_FLOW" }
}
```

**ParallelApprovalConfig**:

- `approvers` (ApproverConfig[])
- `notifications` (NotificationConfig, optional)
- `timing` (TimingConfig, optional)
- `onReject` (RejectConfig)

**Example**:

```json
{
  "approvers": [
    { "id": "appr-fin", "type": "ROLE", "value": "FINANCE_MANAGER" },
    { "id": "appr-hr", "type": "ROLE", "value": "HR_MANAGER" }
  ],
  "onReject": { "action": "STOP_FLOW" }
}
```

---

### 10. ConditionNode Config

**Purpose**: A `CONDITION` node evaluates conditions to determine the next node.

**Fields**:

- `conditions`: An array of condition definitions.
- `routing`: Defines how to route based on condition evaluation.

**Value Rules**:

```json
"valueRules": [
  {
    "field": "request.amount",
    "operator": "gt",
    "value": 5000,
    "logicalOperator": "AND"
  }
]
```

**Historical Rules**:

```json
"historicalRules": {
  "lookbackPeriodDays": 30,
  "requestCount": 5,
  "requestTypes": ["LEAVE"],
  "status": ["APPROVED"]
}
```

**State Rules**:

```json
"stateRules": [
  {
    "nodeId": "node-manager-approval",
    "status": "COMPLETED",
    "outcome": "APPROVED"
  }
]
```

**Organizational Rules**:

```json
"organizationalRules": [
  {
    "type": "ROLE",
    "value": "MANAGER",
    "condition": "EQUALS"
  }
]
```

**Routing**:

- `defaultPath` (string): The node to route to if no conditions match.
- `paths` (array): Maps `condition` IDs to `targetNode`s.

**ConditionNode Example**:

```json
{
  "id": "node-condition-check",
  "FormId": "request_form",
  "type": "CONDITION",
  "title": "Check Request Conditions",
  "config": {
    "conditions": [
      {
        "id": "cond-high-value",
        "name": "High Value Check",
        "type": "VALUE",
        "valueRules": [
          {
            "field": "request.amount",
            "operator": "gt",
            "value": 5000
          }
        ]
      },
      {
        "id": "cond-is-manager",
        "name": "Is Manager Check",
        "type": "ORGANIZATIONAL",
        "organizationalRules": [
          {
            "type": "ROLE",
            "value": "MANAGER",
            "condition": "EQUALS"
          }
        ]
      }
    ],
    "routing": {
      "defaultPath": "node-end",
      "paths": [
        {
          "condition": "cond-high-value",
          "targetNode": "node-parallel-approval"
        },
        {
          "condition": "cond-is-manager",
          "targetNode": "node-document-generation"
        }
      ]
    }
  }
}
```

### 11. UpdateUserStatus Node Configuration

#### Overview

The UpdateUserStatus node serves as a specialized node type within the workflow engine, designed to update user statuses based on form data from previous nodes in the workflow. This node type is particularly useful in workflows such as leave requests, where user status changes need to be tracked and managed throughout the process.

#### Data Model

The UpdateUserStatus node configuration extends the base node structure with specific configurations for form field mapping and status updates:

```json
{
  "id": "node-update-user-status",
  "type": "UPDATE_USER_STATUS",
  "title": "Update User Status",
  "config": {
    "mapping": {
      "sourceNode": {
        "nodeId": "node-start",
        "formId": "leave_request_form"
      },
      "fields": {
        "from": "startDate",
        "to": "endDate"
      }
    },
    "statusConfig": {
      "type": "LEAVE_STATUS",
      "value": "ON_LEAVE"
    }
  }
}
```

#### Configuration Components

1. **Source Node Mapping**

   - `nodeId`: References the source node containing the required form data
   - `formId`: Identifies the form associated with the source node
   - This dual reference ensures proper data traceability and access

2. **Field Mapping**

   - Maps target status fields to their corresponding source form fields
   - Common mappings include:
     - Date range fields (from/to)
     - Status-specific fields (type, reason, etc.)

3. **Status Configuration**
   - Defines the type and value of the status to be set
   - Supports different status types (LEAVE, AVAILABILITY, etc.)

#### Workflow Integration

The UpdateUserStatus node typically appears after approval nodes in workflows:

```json
{
  "edges": [
    { "fromNode": "node-approval", "toNode": "node-update-status" },
    { "fromNode": "node-update-status", "toNode": "node-notification" }
  ]
}
```

#### Runtime Behavior

1. **Data Resolution**

   - The workflow engine resolves the source node using nodeId
   - Validates the existence of the referenced formId
   - Extracts the mapped fields from the form data

2. **Status Update Execution**
   - Uses the extracted field values to update the user's status
   - Applies the configured status type and value
   - Records the update in the workflow history

#### Implementation Considerations

1. **Data Availability**

   - The workflow engine must ensure the source node has been executed
   - Form data must be persisted and accessible

2. **Error Handling**
   - Invalid node or form references should trigger workflow errors
   - Missing required fields should be detected and handled appropriately

#### Usage Examples

Common use cases include:

1. **Leave Request Workflow**

   ```json
   {
     "mapping": {
       "sourceNode": {
         "nodeId": "node-leave-request",
         "formId": "leave_request_form"
       },
       "fields": {
         "from": "leaveStartDate",
         "to": "leaveEndDate"
       }
     },
     "statusConfig": {
       "type": "LEAVE_STATUS",
       "value": "ON_LEAVE"
     }
   }
   ```

2. **Availability Management**
   ```json
   {
     "mapping": {
       "sourceNode": {
         "nodeId": "node-schedule-update",
         "formId": "availability_form"
       },
       "fields": {
         "from": "availableFrom",
         "to": "availableTo"
       }
     },
     "statusConfig": {
       "type": "AVAILABILITY_STATUS",
       "value": "AVAILABLE"
     }
   }
   ```

---

## Real-World Example: Leave Request Workflow

**Scenario**: An employee requests leave. A condition node checks if `request.days > 5`; if so, parallel approvals are required (HR & Dept Head). Otherwise, only the manager's single approval is needed. After approvals, the workflow generates a leave confirmation document, then notifies the requester.

**Workflow (simplified)**:

```json
{
  "id": "workflow-leave-request",
  "name": "Employee Leave Request",
  "description": "Approvals for leave above 5 days, otherwise direct manager approval.",
  "isActive": false,
  "nodes": [
    {
      "id": "node-start",
      "FormId": "leave_form",
      "type": "START",
      "title": "Start Node",
      "config": {
        "initiators": {
          "roles": ["EMPLOYEE"]
        }
      }
    },
    {
      "id": "node-condition-check",
      "FormId": "leave_form",
      "type": "CONDITION",
      "title": "Check Leave Duration",
      "config": {
        "conditions": [
          {
            "id": "cond-long-leave",
            "name": "Long Leave Check",
            "type": "VALUE",
            "valueRules": [
              {
                "field": "request.days",
                "operator": "gt",
                "value": 5
              }
            ]
          }
        ],
        "routing": {
          "defaultPath": "node-manager-approval",
          "paths": [
            {
              "condition": "cond-long-leave",
              "targetNode": "node-parallel-approval"
            }
          ]
        }
      }
    },
    {
      "id": "node-manager-approval",
      "FormId": "leave_form",
      "type": "SINGLE_APPROVAL",
      "title": "Manager Approval",
      "config": {
        "approver": { "id": "appr-1", "type": "ROLE", "value": "MANAGER" },
        "onReject": { "action": "STOP_FLOW" }
      }
    },
    {
      "id": "node-parallel-approval",
      "FormId": "leave_form",
      "type": "PARALLEL_APPROVAL",
      "title": "HR and Dept Head Approval",
      "config": {
        "approvers": [
          { "id": "appr-hr", "type": "ROLE", "value": "HR_MANAGER" },
          { "id": "appr-dep", "type": "ROLE", "value": "DEPT_HEAD" }
        ],
        "onReject": { "action": "STOP_FLOW" }
      }
    },
    {
      "id": "node-document-generation",
      "FormId": "",
      "type": "DOCUMENT",
      "title": "Generate Approval Document",
      "config": {
        "templateId": "leave_approval_letter",
        "outputFormat": "PDF"
      }
    },
    {
      "id": "node-notification",
      "FormId": "",
      "type": "NOTIFICATION",
      "title": "Notify Employee",
      "config": {
        "channels": ["EMAIL"],
        "templates": {
          "assigned": "assigned_tpl",
          "reminder": "reminder_tpl",
          "escalated": "escalated_tpl",
          "completed": "leave_approved_notification"
        },
        "additionalRecipients": ["<EMAIL>"]
      }
    },
    {
      "id": "node-end",
      "FormId": "",
      "type": "END",
      "title": "End Node"
    }
  ],
  "edges": [
    { "fromNode": "node-start", "toNode": "node-condition-check" },
    { "fromNode": "node-condition-check", "toNode": "node-manager-approval" },
    { "fromNode": "node-condition-check", "toNode": "node-parallel-approval" },
    {
      "fromNode": "node-manager-approval",
      "toNode": "node-document-generation"
    },
    {
      "fromNode": "node-parallel-approval",
      "toNode": "node-document-generation"
    },
    { "fromNode": "node-document-generation", "toNode": "node-notification" },
    { "fromNode": "node-notification", "toNode": "node-end" }
  ]
}
```

If `request.days <= 5`, the workflow proceeds with a single manager approval. If `request.days > 5`, parallel approvals are required. After approvals, a document is generated and a notification sent to the employee.

---

## NB:

There are no changes for EMEA and NA, as they will continue to be handled within the workflowEngine/documentTemplate. All existing processes and configurations will remain unchanged, ensuring seamless operations without any disruptions.

## Conclusion

This documentation starts with the Workflow entity, followed by WorkflowNode and WorkflowEdge, then details each configuration type. The addition of the `CONDITION` node type and `StartNodeConfig` demonstrates how complex routing logic and initiation rules can be integrated into the workflow definition.

# **Workflow Service**

## **1. Overview**

This is the **Low-Level Design (LLD)** of **Workflow Service**. The goal of this system is to orchestrate complex workflows spanning multiple steps, for example approvals, and external processes. The system is composed of several microservices, a storage layer, and an event bus mechanism to enable communication and scalability.

### **Objectives**

- **Standardize** approvals, notifications, and document generation across different business units.
- **Provide** a unified framework for workflow definition, execution, and tracking.
- **Enable** easy extensibility and integration with new node types or external services.

### **Key Features**

- **Workflow Runner** handles the lifecycle of each request, persisting data in a centralized database.
- **Workflow Engine** defines the blueprint (nodes, transitions, routing).
- **Node Executors** encapsulate node-specific logic (e.g., approvals, document generation).
- **Service Bus** enables asynchronous communication and decoupled integration with other services.

---

## **2. System Architecture**

Below is a high-level diagram depicting the major components and their interactions:

```mermaid
graph TB
    subgraph "Client"
        FE[Frontend]
        API[Request Service]
    end

    subgraph "Core Services"
        WR[Workflow Runner Service]
        WE[Workflow Engine]
        NE[Node Executors]
        EB[Service Bus]
    end

    subgraph "Storage Layer"
        CD[(CosmosDB)]
    end

    subgraph "Node Executors Services"
        SA[Single Approval Executor]
        PA[Parallel Approval Executor]
        DG[Document Generator]
        NT[Notification Handler]
    end

    FE -->|HTTP| API
    API -->|workflow.start/EVENT| WR
    WR -->|Get/Update State| CD
    WR -->|Get Node Config| WE
    WR -->|Execute Node| NE
    NE --> SA
    NE --> PA
    NE --> DG
    NE --> NT
    SA & PA & DG & NT -->|Publish Events| EB
    SA & PA & DG & NT -->|Update State| CD
```

### **Component Descriptions**

1. **Client**

   - **Frontend (FE)**: User-facing interface for initiating requests and monitoring workflow status.
   - **Request Service (API)**: Exposes endpoints for creating or manipulating workflow requests.

2. **Core Services**

   - **Workflow Runner (WR)**: Manages the end-to-end execution of workflows (create instances, manage nodes, update state).
   - **Workflow Engine (WE)**: Stores and provides workflow definitions (node configurations, transitions, conditional logic).
   - **Node Executors (NE)**: A generic interface or service aggregator that delegates actual execution to specialized executors.

3. **Storage Layer**

   - **CosmosDB (CD)**: NoSQL database holding workflow instances, histories, node states, etc.

4. **Node Executors Services**

   - **Single Approval Executor (SA)**
   - **Parallel Approval Executor (PA)**
   - **Document Generator (DG)**
   - **Notification Handler (NT)**

5. **Event Bus (EB)**
   - Facilitates asynchronous communication (e.g., "approval requested", "document generated").

---

## **3. Core Concepts and Components**

### **3.1 WorkflowEngine Instance vs. Workflow Instance**

- A **WorkflowEngine Instance** is a template/definition describing the sequence of nodes, transitions, and conditional logic.
- A **Workflow Instance** is a _running_ or _completed_ instantiation of that workflow, specific to a request.

### **3.2 Workflow Runner**

- Orchestrates node processing based on the definition provided by the Workflow Engine.
- Persists and updates the **Workflow Instance** in the database.
- Publishes/consumes events via the Event Bus (for approvals, notifications, escalations, etc.).

### **3.3 Node Executors**

- Specialized microservices or modules that handle the logic for different node types (e.g., Single Approval, Parallel Approval).
- Each executor implements an **`INodeExecutor`** interface (or similar) to ensure consistency.

### **3.4 Event Bus**

- Decouples services via asynchronous messaging.
- Workflow Runner sends events like **`workflow.approval.requested`**. External services (or Node Executors) can subscribe and react accordingly.

---

## **4. Workflow Instance and Node Definitions**

### **4.1 `WorkflowInstance`**

```typescript
interface WorkflowInstance extends BaseEntity {
  requestId: string;
  requestForId: string;
  workflowEngineId: string;
  status: WorkflowStatus;
  currentNodeId: string;
  initiatorId: string;
  initialFormData: Record<string, any>;
  nodeList: WorkflowNode[];
  edges: WorkflowEdge[];
}

type NodeConfig =
  | SingleApprovalNodeConfig
  | ParallelApprovalNodeConfig
  | DocumentGenerationNodeConfig
  | StartNodeConfig
  | EndNodeConfig
  | ConditionNodeConfig
  | UpdateUserStatusConfig;

class WorkflowNode {
  id: string;
  type: NodeType;
  runtimeConfig?: RuntimeNodeConfig;
  executionHistory: Array<{
    createdAt: Date;
    updatedAt: Date;
    completedAt: Date | null;
    status: NodeStatus;
    assignedTo: string[];
    decisions: Array<{
      userId: string;
      decision: string;
      comment?: string;
      timestamp: Date;
    }>;
  }>;
}

class WorkflowEdge {
  source: string;
  target: string;
  condition?: string;
}

enum WorkflowStatus {
  PENDING_APPROVAL = "PENDING_APPROVAL",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

enum NodeType {
  START = "START",
  END = "END",
  SINGLE_APPROVAL = "SINGLE_APPROVAL",
  PARALLEL_APPROVAL = "PARALLEL_APPROVAL",
  DOCUMENT_GENERATION = "DOCUMENT_GENERATION",
  CONDITION = "CONDITION",
  UPDATE_USER_STATUS = "UPDATE_USER_STATUS",
}

enum NodeStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}
```

**Key Fields**

- **`status`**: Tracks if the workflow instance is still in progress, finished, or encountered a failure.
- **`currentNodeId`**: The ID of the node being processed.
- **`nodeshistory`**: Array of node execution records for auditing.

#### **Sample**

```typescript
const exampleWorkflowInstance: WorkflowInstance = {
  id: "WF-001",
  requestId: "REQ-1234",
  workflowId: "Absence authorizations",
  status: "IN_PROGRESS",
  currentNodeId: "NODE-AP1",
  initiator: {
    id: "USER-001",
    email: "<EMAIL>",
    department: "",
  },
  formData: {
    initiatior: "teamLeader_id",
  },
  nodeshistory: [],
  createdAt: new Date(),
  updatedAt: new Date(),
};
```

---

## **5. Detailed Workflow Execution Process**

This section outlines **how** the workflow transitions from one node to another and **how** the system responds to events.

```mermaid
sequenceDiagram
    participant RS as RequestService
    participant SB as ServiceBus
    participant WS as WorkflowService
    participant DB as CosmosDB
    participant WE as WorkflowEngine

    RS->>SB: Publish workflow.start
    SB->>WS: Handle workflow.start
    WS->>DB: Create WorkflowInstance

    loop Node Processing
        WS->>WE: Get Node (currentNodeId)
        alt Is End Node
            WS->>DB: Finish Workflow
        else Is Approval Node
            WS->>DB: Update State (PENDING_APPROVAL)
            WS->>SB: Send Approval Notification
            WS-->>WS: Await User Input
        else Is Document Node
            WS->>SB: Request Document Generation
            WS->>DB: Update State (PROCESSING)
            WS-->>WS: Await Document
        end

        WS->>WE: Get Next Node
        WS->>DB: Update Current Node
    end
```

---

- - When a workflow instance hits a **Single Approval** or **Parallel Approval** node, it **stops** at that node.
  - The **workflow runner** will **not** continue to the next node until the approval node receives a decision.- **Decision Handling**

  - A **decision handler** method will be called (e.g., `handleDecision`) once the user (or automated logic) submits approval or rejection.
  - If **approved**, the workflow continues to the next node.
  - If **rejected** (and if configured to stop on rejection), the workflow either **terminates** or redirects to an alternate path (based on the node's configuration).

### **5.1 Step 1: Workflow Initialization**

```typescript
async function executeWorkflow(
  requestId: string,
  workflowId: string,
  data: any
): Promise<void> {
  // 1. Create workflow instance
  const instance = await createWorkflowInstance({
    requestId,
    workflowId,
    data,
    status: "IN_PROGRESS",
    currentNodeId: await getInitialNodeId(workflowId),
  });

  // 2. Start processing nodes
  await processNodes(instance);
}
```

**Purpose**

- **Create** a new `WorkflowInstance`.
- Retrieve the **initial node** from the Workflow Engine.
- **Kick off** the node processing mechanism.

### **5.2 Step 2: Node Processing (Recursive)**

```typescript
async function processNodes(instance: WorkflowInstance): Promise<void> {
  // 1. Get current node configuration
  const currentNode = await workflowEngine.getNode(
    instance.workflowId,
    instance.currentNodeId
  );

  // 2. Handle workflow completion
  if (!currentNode) {
    await completeWorkflow(instance.id);
    return;
  }

  // 3. Execute current node
  try {
    const nodeExecutor = getNodeExecutor(currentNode.type);
    await nodeExecutor.execute(currentNode, instance);

    // 4. For non-blocking nodes, determine and process next node
    if (!isBlockingNode(currentNode.type)) {
      const nextNodeId = await determineNextNode(currentNode, instance);
      if (nextNodeId) {
        await updateWorkflowInstance(instance.id, {
          currentNodeId: nextNodeId,
          status: "IN_PROGRESS",
        });
        // Recursive call to process the next node
        await processNodes({
          ...instance,
          currentNodeId: nextNodeId,
        });
      }
    }
  } catch (error) {
    await handleNodeError(instance, currentNode, error);
  }
}
```

**Purpose**

- **Retrieve** the current node definition.
- **Execute** node logic (Approval, Document Generation, etc.).
- For **non-blocking** nodes, **automatically** move to the next node.
- If an error occurs, handle it (logging, retry, or marking instance as failed).

### **5.3 Step 3: Next Node Determination**

```typescript
async function determineNextNode(
  currentNode: WorkflowNode,
  instance: WorkflowInstance
): Promise<string | null> {
  // 1. Check for conditional routing
  if (currentNode.nextNodes?.conditions) {
    for (const condition of currentNode.nextNodes.conditions) {
      const result = await evaluateCondition(
        condition.expression,
        instance.data
      );
      if (result) {
        return condition.nodeId;
      }
    }
  }

  // 2. Return default next node or null for workflow completion
  return currentNode.nextNodes?.default || null;
}
```

**Purpose**

- Evaluate **conditional logic** to determine if a specific path should be taken.
- If no conditions match, use a **default** path.
- Return `null` if there is **no subsequent node**, signaling workflow completion.

### **5.4 Step 4: Node Execution Results Handling**

```typescript
async function handleNodeExecution(
  instance: WorkflowInstance,
  node: WorkflowNode,
  result: any
): Promise<void> {
  // 1. Update node history
  await updateNodeHistory(instance.id, {
    nodeId: node.id,
    status: "COMPLETED",
    endTime: new Date(),
    result,
  });

  // 2. Update workflow data if needed
  if (result.data) {
    await updateWorkflowData(instance.id, result.data);
  }

  // 3. Handle node-specific post-processing
  switch (node.type) {
    case "SINGLE_APPROVAL":
    case "PARALLEL_APPROVAL":
      if (!result.approved && node.config.stopOnRejection) {
        await terminateWorkflow(instance.id);
        return;
      }
      break;
    // Additional node-specific logic...
  }

  // 4. Continue workflow processing if not blocked
  if (!isBlockingNode(node.type)) {
    const nextNodeId = await determineNextNode(node, instance);
    if (nextNodeId) {
      await continueWorkflow(instance.id, nextNodeId);
    }
  }
}
```

**Purpose**

- **Record** the outcome (approved, rejected, completed, etc.) in the node history.
- **Mutate** the workflow's shared data based on node results (e.g., capturing user input).
- For some node types, check if the workflow should be **terminated** on rejection or error.
- If allowed, **proceed** to the next node.

---

## **6. Node Executors**

Node Executors define the **logic** to run each node type. They typically implement an interface like:

```typescript
interface INodeExecutor {
  execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void>;
  // Additional methods for handling user decisions, if needed
}
```

### **6.1 Single Approval Node Executor**

```typescript
class SingleApprovalNodeExecutor implements INodeExecutor {
  async execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void> {
    const config = node.config as SingleApprovalConfig;

    // 1. Validate approver configuration
    if (!config.approver && !config.approverId) {
      throw new Error("No approver configured");
    }

    // 2. Publish notification event
    await this.eventBus.publish("workflow.approval.requested", {
      instanceId: instance.id,
      nodeId: node.id,
      approver: config.approverId || config.approver.id,
      timing: config.timing,
    });

    // 3. Update node state
    await updateNodeState(instance.id, node.id, {
      status: "PENDING",
      metadata: {
        approverId: config.approverId || config.approver.id,
      },
    });
  }

  async handleDecision(
    instance: WorkflowInstance,
    node: WorkflowNode,
    decision: boolean,
    comment?: string
  ): Promise<void> {
    const config = node.config as SingleApprovalConfig;

    if (!decision && config.onReject?.action === "STOP_FLOW") {
      await terminateWorkflow(instance.id);
      return;
    }

    await updateNodeState(instance.id, node.id, {
      status: "COMPLETED",
      result: { approved: decision, comment },
    });

    // Continue workflow execution
    await processNodes(instance);
  }
}
```

**Purpose**

- **Block** the workflow until the single assigned approver provides a decision.
- **Publish** an event to notify the approver.
- React to the approval/rejection decision, potentially **terminating** or continuing the workflow.

### **6.2 Parallel Approval Node Executor**

```typescript
class ParallelApprovalNodeExecutor implements INodeExecutor {
  async execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void> {
    const config = node.config as ParallelApprovalConfig;

    // 1. Initialize approval states
    const approverStates = config.approvers.map((approver) => ({
      approverId: approver.id,
      status: "PENDING",
    }));

    // 2. Publish notification events for all approvers
    await Promise.all(
      config.approvers.map((approver) =>
        this.eventBus.publish("workflow.approval.requested", {
          instanceId: instance.id,
          nodeId: node.id,
          approver: approver.id,
          isParallel: true,
        })
      )
    );

    // 3. Update node state
    await updateNodeState(instance.id, node.id, {
      status: "PENDING",
      metadata: {
        approverStates,
        requiredApprovals: config.approvers.length,
      },
    });
  }

  async handleDecision(
    instance: WorkflowInstance,
    node: WorkflowNode,
    decision: boolean,
    approverId: string
  ): Promise<void> {
    const state = await getNodeState(instance.id, node.id);
    const approverStates = state.metadata.approverStates.map((ast) =>
      ast.approverId === approverId
        ? { ...ast, status: "COMPLETED", decision }
        : ast
    );

    const allCompleted = approverStates.every(
      (ast) => ast.status === "COMPLETED"
    );

    if (allCompleted) {
      await updateNodeState(instance.id, node.id, {
        status: "COMPLETED",
        metadata: { approverStates },
      });
      await processNodes(instance);
    } else {
      await updateNodeState(instance.id, node.id, {
        metadata: { approverStates },
      });
    }
  }
}
```

**Purpose**

- **Simultaneously** request approvals from multiple parties.
- Blocks until **all** designated approvers provide input.
- Once all complete, the node is marked **"COMPLETED"**; the workflow proceeds.

### **6.3 Document Generation Node Executor**

```typescript
class DocumentGenerationNodeExecutor implements INodeExecutor {
  async execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void> {
    const config = node.config as DocumentGenerationConfig;

    try {
      // 1. Generate document
      const document = await generateDocument({
        templateId: config.templateId,
        format: config.outputFormat,
        data: {
          workflowId: instance.id,
          initiator: instance.initiator,
          timestamp: new Date(),
        },
      });

      // 2. Publish document generated event
      await this.eventBus.publish("workflow.document.generated", {
        instanceId: instance.id,
        nodeId: node.id,
        documentId: document.id,
      });

      // 3. Update node state
      await updateNodeState(instance.id, node.id, {
        status: "COMPLETED",
        result: {
          documentId: document.id,
          url: document.url,
          format: config.outputFormat,
        },
      });

      // 4. Continue workflow
      await processNodes(instance);
    } catch (error) {
      await updateNodeState(instance.id, node.id, {
        status: "COMPLETED",
        error: error.message,
      });
      throw error;
    }
  }
}
```

**Purpose**

- **Automates** document generation (e.g., PDF, Word) based on templates and runtime data.
- Publishes an event once the document is successfully created.
- Non-blocking once completed; moves to the next node immediately.

### **6.4 Update User Status Node Executor**

# Update User Status Node

## Purpose

Manages user status changes within workflows (e.g., leave status, availability, work status).

## Configuration Schema

```typescript
interface UpdateUserStatusConfig {
  mapping: {
    sourceNode: {
      nodeId: string; // Reference node containing form data
      formId: string; // Form containing date fields
    };
    fields: {
      from: string; // Start date field
      to: string; // End date field
    };
  };
  statusConfig: {
    type: UserStatusType;
    value: string;
  };
}

enum UserStatusType {
  LEAVE_STATUS = "LEAVE_STATUS",
  AVAILABILITY = "AVAILABILITY",
}
```

Execution Flow

Input Validation

Verify source form data exists

Validate date range fields

Check status type validity

Status Update

Update user status in system

Set validity period

Record change history

Event Publishing

{
event: "user.status.updated",
payload: {
instanceId: string;
nodeId: string;
userId: string;
status: {
type: UserStatusType;
value: string;
from: Date;
to: Date;
}
}
}

## Example Configuration

````json
{
  "id": "update-leave-status",
  "type": "UPDATE_USER_STATUS",
  "config": {
    "mapping": {
      "sourceNode": {
        "nodeId": "leave-request-form",
        "formId": "LEAVE_REQUEST_001"
      },
      "fields": {
        "from": "leaveStartDate",
        "to": "leaveEndDate"
      }
    },
    "statusConfig": {
      "type": "LEAVE_STATUS",
      "value": "ON_LEAVE"
    }
  }
}

---

## **7. Event System**

The system **publishes** and **subscribes** to events through an **Event Bus**. This decoupling allows various services to react to workflow changes without hard-coded dependencies.

**Example of Events Interface:**

```typescript
interface WorkflowEvents {
  "workflow.start": { instanceId: string; initiator: any };
  "workflow.approval.requested": {
    instanceId: string;
    nodeId: string;
    approver: string;
  };
  "workflow.approval.completed": {
    instanceId: string;
    nodeId: string;
    decision: boolean;
  };
  "workflow.document.generated": {
    instanceId: string;
    nodeId: string;
    documentId: string;
  };
  "workflow.completed": { instanceId: string };
}
````

## **8. Escalation System**

The escalation system enables dynamic reassignment of approvals based on various triggers and maintains a complete audit trail.

```mermaid
sequenceDiagram
    participant TS as Time Service
    participant ES as Escalation Service
    participant WS as Workflow Service
    participant SB as Service Bus
    participant DB as CosmosDB

    TS->>ES: Trigger timeout escalation
    ES->>WS: Request escalation
    WS->>DB: Get current node state
    WS->>DB: Update escalation history
    WS->>SB: Publish escalation notification
    SB->>WS: New approver notification
```

### **8.1 Escalation Types**

```typescript
interface EscalationConfig {
  enabled: boolean;
  chain: Array<{
    approverId: string;
  }>;
}
```

### **8.2 Escalation History**

Each escalation is tracked in the node's execution history:

```typescript
interface EscalationHistory {
  timestamp: Date;
  escalatedFrom: string;
  escalatedTo: string;
  reason: string;
  initiatedBy?: string;
}
```

### **8.3 Escalation Process**

1. **Trigger Detection**

   - Timeout-based (automatic)
   - Manual escalation (user-initiated)
   - Condition-based (business rules)

2. **State Preservation**

   - Current approver's state is preserved
   - New approver is assigned
   - Escalation chain is tracked

3. **Notification**
   - Previous approver is notified of escalation
   - New approver receives assignment
   - Relevant stakeholders are informed

---

## **9. Node Executor Service**

### **9.1 Base Node Executor**

```typescript
interface INodeExecutor {
  execute(node: WorkflowNode, instance: WorkflowInstance): Promise<void>;
  handleDecision?(decision: ApprovalDecisionDto): Promise<void>;
  handleEscalation?(escalation: EscalationDto): Promise<void>;
  handleTimeout?(node: WorkflowNode): Promise<void>;
}
```

### **9.2 Approval Node States**

```typescript
enum NodeStatus {
  NOT_STARTED = "NOT_STARTED",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  REJECTED = "REJECTED",
}
```

---

## **10. Service Bus Integration**

### **10.1 Event Types**

```typescript
enum WorkflowEventName {
  STARTED = "workflow.started",
  COMPLETED = "workflow.completed",
  APPROVAL_REQUESTED = "workflow.approval.requested",
  APPROVAL_COMPLETED = "workflow.approval.completed",
  ESCALATION_REQUESTED = "workflow.escalation.requested",
  ESCALATION_COMPLETED = "workflow.escalation.completed",
}
```

### **10.2 Event Handling**

- Events are published to Azure Service Bus topics
- Subscribers process events asynchronously
- Retry policies handle temporary failures
- Dead-letter queue manages unprocessable messages

---

## **11. Data Storage**

### **11.1 CosmosDB Collections**

```typescript
const COSMOS_CONTAINERS = [
  {
    id: "workflowservice",
    partitionKey: { paths: ["/workflowsid"] },
    indexingPolicy: {
      includedPaths: [
        { path: "/createdAt/?" },
        { path: "/status/?" },
        { path: "/currentNodeId/?" },
      ],
    },
  },
];
```

### **11.2 Data Models**

- Workflow instances
- Node execution history
- Escalation records
- Approval decisions

---

## **13. Monitoring and Logging**

### **13.1 Metrics**

- Workflow completion time
- Approval response time
- Escalation frequency
- Error rates

### **13.2 Logging**

```typescript
interface LogEntry {
  timestamp: Date;
  level: "INFO" | "WARN" | "ERROR";
  operation: string;
  correlationId: string;
  data: any;
  error?: Error;
}
```

### **14.2 Authorization**

- Node-level permissions
- Escalation permissions
- Approval permissions

# Dynamic Forms Microservice

## Overview

This microservice allows users to dynamically create, read, update, and delete forms and their fields. Forms and fields metadata will be stored in Cosmos DB using an embedded data model for simplicity and performance.

### Key Architectural Goals

1. **Dynamic Form Modeling**: Allow storage and retrieval of form definitions where each form can have a varying number of fields of different types, properties, and configurations.
2. **Flexible & Embedded Data Model**: Store forms as single, versioned documents containing all fields within the same JSON document, simplifying read operations and ensuring that the form definition is self-contained.
3. **Event-Driven Integrations Using Cosmos Change Feed**: When a form is created, updated, or deleted, use Cosmos DB's change feed to trigger downstream processes (e.g., caching updates, indexing, analytics, or notification services).
4. **Microservices & Domain-Driven Structure**: Implement a dedicated `FormsService` microservice using NestJS. Adhere to clear layering (Controller, Service, Repository) and well-defined interfaces.
5. **Versioning Support (Optional)**: Consider including version fields in the form documents if version history is required.
6. **Scalability & Partitioning**: Leverage Cosmos DB's partitioning scheme to scale reads and writes efficiently. Likely partition by `formId` or a suitable grouping key.

// ... rest of the Dynamic Forms Microservice content ...

# Document Generation Service

## Overview

The **Document Generation Microservice** handles:

- **Generating digital and physical documents** based on templates.
- **Converting HTML content** into formats like PDF or DOCX.
- **Storing generated documents** in Azure Blob Storage.
- **Providing endpoints** to retrieve and download stored documents.
- **Uploading and managing scanned documents**.
- **Emitting events** such as `DocumentGenerated` and `DocumentUploaded`.

---

## Entities

### Document

Represents a generated or uploaded document.

- **id**: Unique identifier of the document.
- **userId**: ID of the user associated with the document.
- **fileName**: Name of the document file.
- **fileType**: MIME type (e.g., `application/pdf`, `image/jpeg`).
- **storageUri**: URI where the document is stored in Azure Blob Storage.
- **documentType**: Indicates if the document is generated or uploaded (`GENERATED`, `UPLOADED`).
- **metadata**: Additional information (e.g., template ID for generated documents).
- **createdAt**: Timestamp when the document was created or uploaded.

#### UploadedDocument

Represents a document that has been uploaded by a user.

- **id**: Unique identifier of the uploaded document.
- **userId**: ID of the user associated with the uploaded document.
- **fileName**: Name of the uploaded document file.
- **fileType**: MIME type of the uploaded document (e.g., `application/pdf`, `image/jpeg`).
- **storageUri**: URI where the uploaded document is stored in Azure Blob Storage.
- **documentType**: Indicates that the document is uploaded (`UPLOADED`).
- **documentMetadata**: Additional information about the uploaded document (e.g., category, tags).
- **createdAt**: Timestamp when the uploaded document was created or uploaded.

#### GeneratedDocument

Represents a document that has been generated by the system. Inherits from `UploadedDocument`.

- **id**: Unique identifier of the generated document.
- **userId**: ID of the user associated with the generated document.
- **fileName**: Name of the generated document file.
- **fileType**: MIME type of the generated document (e.g., `application/pdf`, `image/jpeg`).
- **storageUri**: URI where the generated document is stored in Azure Blob Storage.
- **documentType**: Indicates that the document is generated (`GENERATED`).
- **documentMetadata**: Additional information about the generated document.
- **createdAt**: Timestamp when the generated document was created or uploaded.

**Note**: `GeneratedDocument` extends from `UploadedDocument`.

---

### Data Flow

```mermaid
flowchart TD
    A[Receive GenerateDocument Event] --> B[Fetch Template Details]
    B --> C[Get Container Name]
    C --> D[Query Template Container]
    D --> E[Merge Data with Template]
    E --> F[Generate Document]
    F --> G[Store in Blob Storage]
    G --> H[Emit DocumentGenerated Event]

```

## API Endpoints

- **Listener**: Listens to `GenerateDocument` events.
- **Description**: Generates a document from HTML content when a `GenerateDocument` event is received.

**Event Payload**:

- **eventype**: Type of event (e.g `GenerateDocument`)
- **htmlContent**: the html code of the template.
- **contextId**: Identifier for fetching rendered template data.
- **outputFormat**: Desired format (`pdf`, `docx`).
- **isPhysical** (optional): Whether a physical copy is needed.

**Example Event Payload**:

```json
{
  "eventType": "GenerateDocument",
  "htmlContent": "<!DOCTYPE html> <html><body><h1>Hello, World!</h1></body></html>",
  "contextId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "outputFormat": "pdf",
  "isPhysical": false
}
```

**Example Response - Event Response**:

```json
{
  "eventType": "DocumentGenerated",
  "documentId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "storageUri": "https://blobstorage.com/documents/doc-789.pdf",
  "fileName": "Generated_Document_timestamp.pdf",
  "fileType": "application/pdf",
  "documentType": "GENERATED",
  "createdAt": "2023-10-01T12:00:00Z"
}
```

---

### 2. Upload Scanned Document

- **Endpoint**: `POST /documents/upload`
- **Description**: Uploads a scanned document to the system.

**Request Parameters**:

- **file**: The scanned document file to upload.
- **metadata** (optional): Additional information about the document (e.g., document category, tags).

**Example Request**:

```http
POST /documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": <scanned_document.pdf>,
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  }
}
```

**Example Response**:

```json
{
  "documentId": "doc-1011",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "documentType": "UPLOADED",
  "documentMetadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

### 3. Retrieve Document Metadata

- **Endpoint**: `GET /documents/{id}`
- **Description**: Retrieves metadata for a specific document.

**Example Request**:

```http
GET /documents/doc-1011
Authorization: Bearer <token>
```

**Example Response**:

```json
{
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "UPLOADED",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

### 4. Download Document

- **Endpoint**: `GET /documents/{id}/download`
- **Description**: Downloads the stored document.

**Example Request**:

```http
GET /documents/doc-1011/download
Authorization: Bearer <token>
```

**Response**:

- Returns the document file for download.

---

## Generate Document Use Case

### Purpose

Allows the system to generate documents (e.g., reports, certificates) from HTML content and store them within the system when a `GenerateDocument` event is received.

### Workflow

1. **Event Trigger**:

   - The system listens for `GenerateDocument` events.

2. **Document Generation**:

   - The service generates the document from the provided HTML content using the specified output format.
   - The generated document is stored in Azure Blob Storage, and a Storage URI is retrieved.
   - After obtaining the storage URI, the service creates a generated document object containing `documentId`, `storageUri`, `fileType`, and other relevant details, and stores it in CosmosDB.

3. **Emit Event**:

   - The service emits a `DocumentGenerated` event containing details about the generated document.

### Event Payload Request

- **eventType**: Type of event (`GenerateDocument`).
- **htmlContent**: The HTML content of the template.
- **contextId**: Identifier for fetching rendered template data.
- **outputFormat**: Desired format (`pdf`, `docx`, `html`).
- **isPhysical** (optional): Whether a physical copy is needed.

**Example Event Payload Request**:

```json
{
  "eventType": "GenerateDocument",
  "htmlContent": "<!DOCTYPE html><html><body><h1>Hello, World!</h1></body></html>",
  "contextId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "outputFormat": "pdf",
  "isPhysical": false
}
```

### Event Payload Response

- **eventType**: Type of event (`DocumentGenerated`).
- **documentId**: Unique identifier for the generated document.
- **storageUri**: URI where the generated document is stored.
- **fileName**: Name of the generated document file.
- **fileType**: MIME type of the generated document.
- **documentType**: Type of the document (`generated`).
- **isPhysical**: Whether a physical copy was generated.
- **createdAt**: Timestamp when the document was created.

**Example Event Payload Response**:

```json
{
  "eventType": "DocumentGenerated",
  "documentId": "0bcf75d6-d328-4ff2-be04-2090e5d537d4",
  "storageUri": "https://blobstorage.com/documents/doc-789.pdf",
  "fileName": "Generated_Document.pdf",
  "fileType": "application/pdf",
  "documentType": "generated",
  "isPhysical": false,
  "createdAt": "2023-10-01T12:00:00Z"
}
```

## Scan Upload Use Case

### Purpose

Allows users to upload scanned documents (e.g., identity proofs, signed contracts) to be stored and managed within the system.

### Workflow

1. **User Uploads a Scanned Document**:

   - The user sends a `POST` request to `/documents/upload` with the scanned document file and optional metadata.

2. **Document Storage**:

- The service stores the uploaded file in Azure Blob Storage and retrieves a Storage URI.
- After obtaining the storage URI, the service creates an uploaded document object containing `documentId`, `userId`, `storageUri`, `fileType`, and other relevant details, and stores it in CosmosDB.

3. **Emit Event**:

- The service emits a `DocumentUploaded` event containing details about the uploaded document.

4. **Response to User**:

- Returns a response containing the uploaded document object.
- The response includes:
  - - `documentId`: Unique identifier for the document.
  - - `storageURI`: URI where the document is stored.
  - - `FileType`: Type of the uploaded document.
  - - Other relevant details.

### Example Scenario

- **Use Case**: A user needs to submit a scanned copy of their passport for verification.

- **Steps**:

  1. The user accesses the application interface to upload their passport scan.
  2. They select the scanned PDF file and submit it via the upload endpoint.
  3. The Document Microservice stores the file and returns the document details.
  4. The system can now use this document for verification processes.

---

## Domain Events

### DocumentGenerated

- **Emitted When**: A new document is successfully generated and stored.

- **Contains**:

  - `eventType`
  - `documentId`
  - `userId`
  - `fileName`
  - `fileType`
  - `storageUri`
  - `documentType`
  - `createdAt`

**Example Event Payload**:

```json
{
  "eventType": "DocumentUploaded",
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "GENERATED",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

### DocumentUploaded

- **Emitted When**: A scanned document is successfully uploaded and stored.

- **Contains**:

  - `eventType`
  - `documentId`
  - `userId`
  - `fileName`
  - `fileType`
  - `storageUri`
  - `documentType`
  - `metadata` (e.g., category, tags)
  - `createdAt`

**Example Event Payload**:

```json
{
  "eventType": "DocumentUploaded",
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "UPLOADED",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

## Key Points

- **Scan Upload Capability**: Users can upload scanned documents, which are stored and managed alongside generated documents.

- **Unified Document Management**: Both generated and uploaded documents are treated similarly, allowing for consistent retrieval and management.

- **Endpoints Provided**: The `/documents/upload` endpoint allows for uploading, while existing endpoints support retrieval and download.

- **Event Emission**: The `DocumentUploaded` event allows other services to react to new uploads (e.g., triggering verification workflows).

- **Security Considerations**:

  - **File Validation**: Uploaded files are validated to ensure they are of acceptable types and sizes.
  - **Access Control**: Only authorized users can upload and access documents.

---

## Additional Notes

- **Metadata Usage**: Metadata provided during upload can be used to categorize and search for documents.

- **Supported File Types**: The service may restrict uploads to certain file types (e.g., PDF, JPEG, PNG).

- **Storage**: Uploaded documents are stored securely in Azure Blob Storage, similar to generated documents.

- **Error Handling**:

  - **Invalid File Type**: Returns a `400 Bad Request` if the file type is not supported.
  - **File Too Large**: Returns a `413 Payload Too Large` if the file exceeds size limits.
  - **Authentication Errors**: Returns `401 Unauthorized` if the user is not authenticated.

---

## Examples

### Uploading a Scanned Document

**Request**:

```http
POST /documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

{
  "file": <passport_scan.pdf>,
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  }
}
```

**Service Actions**:

- Validates the file type and size.
- Stores the file in Azure Blob Storage.
- Saves metadata and associates the document with the user.
- Emits a `DocumentUploaded` event.

**Response**:

```json
{
  "documentId": "doc-1011",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "documentType": "uploaded",
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

### Retrieving an Uploaded Document's Metadata

**Request**:

```http
GET /documents/doc-1011
Authorization: Bearer <token>
```

**Response**:

```json
{
  "documentId": "doc-1011",
  "userId": "user-456",
  "fileName": "Passport_Scan.pdf",
  "fileType": "application/pdf",
  "storageUri": "https://blobstorage.com/documents/doc-1011.pdf",
  "documentType": "uploaded",
  "metadata": {
    "category": "Identity Proof",
    "tags": ["passport", "verification"]
  },
  "createdAt": "2023-10-01T13:00:00Z"
}
```

---

## Integration with Other Services

- **Verification Processes**: Uploaded documents can be used by other services for identity verification or compliance checks.

- **Notifications**: The `DocumentUploaded` event can trigger notifications or workflows in other systems.

---

### Document Template Service

# DocumentTemplate Management Microservice

## Overview

The **DocumentTemplate Management Microservice** enables super administrators to create, manage, and render document templates with fields. These templates can include both system-fetched data from other microservices and local user-provided inputs. This microservice facilitates the generation of personalized documents.

---

## Table of Contents

- [Key Features](#key-features)
- [Use Cases](#use-cases)
- [API Endpoints](#api-endpoints)
- [Data Model](#data-model)
- [Activity Diagrams](#activity-diagrams)
- [Integration with Other Microservices](#integration-with-other-microservices)
- [Validation and Error Handling](#validation-and-error-handling)
- [Security Considerations](#security-considerations)
- [Getting Started](#getting-started)
- [Examples](#examples)

---

## Key Features

- **Template Management**
  - Create, update, retrieve, and soft delete document templates.
  - Version control to track changes over time.
- **Template Fields**
  - **user Fields**: Data fetched from user microservice.
  - **Local Fields**: Data fetched from workflow microservice. -**system Fields**: Data fetched from the current date and time
- **Template Rendering**
  - Combine templates with the fields to generate final documents.
  - Support for placeholders in template content.
- **Integration**
  - Fetch data from other microservices (e.g., UserData, workflow).
- **Validation**
  - Field-level validation rules (e.g., required, pattern matching).

---

## Use Cases

### 1. HR Department Generating Absence Certificates

- **Actors**: Super admin
- **Process**:
  1. Super Admin creates an "Absence Certificate" template.
  2. Defines the fields like `{{firstName}}`, `{{lastName}}` (user fields), and `{{reason}}` (local field).
  3. When an Team lead requests an absence certificate:
  4. System fields are automatically populated from the UserData microservice.
  5. Final document is rendered HTML and provided to generateDocument microservice.

---

## API Endpoints

### 1. Create Template

- **Endpoint**: `POST /templates`
- **Description**: Creates a new document template.
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Request Body**:

  ```json
  {
    "name": "string",
    "description": "string",
    "category": "string",
    "templateContent": "string",
    "localFields": [
      {
        "name": "string",
        "type": "string" | "number" | "date" | "boolean",
        "validation": {
          "required": true,
          "pattern": "string",
          "minLength": 0,
          "maxLength": 255
        }
      }
      // Additional local fields...
    ],
    "userFields": [
      {
        "name": "string",
        "sourceDetails": {
          "serviceName": "string",
          "endpoint": "string",
          "key": "string"
        },
        "type": "string" | "number",
        "validation": {
          "required": true,
          "pattern": "string",
          "minLength": 0,
          "maxLength": 255
        }
      }
      // Additional user fields...
    ],
    "systemFields": [
      {
        "name": "string",
        "type": "string" | "number" | "date" ,
        "validation": {
          "required": true,
          "pattern": "string",
          "minLength": 0,
          "maxLength": 255
        }
      }
      // Additional system fields...
    ]
  }
  ```

- **Responses**:
  - **201 Created**: Template created successfully.
  - **400 Bad Request**: Validation error in the request body.
  - **401 Unauthorized**: Authentication failed.

### 2. Retrieve Template

- **Endpoint**: `GET /templates/{id}`
- **Description**: Retrieves a specific template by its ID.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **200 OK**: Returns the template details.
  - **404 Not Found**: Template does not exist.
  - **401 Unauthorized**: Authentication failed.

### 3. List Templates

- **Endpoint**: `GET /templates`
- **Description**: Retrieves a list of templates, optionally filtered by category.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `category` (optional): Filter by template category.
- **Responses**:
  - **200 OK**: Returns a list of templates.
  - **401 Unauthorized**: Authentication failed.

### 4. Update Template

- **Endpoint**: `PUT /templates/{id}`
- **Description**: Updates an existing template.
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Request Body**:

  ```json
  {
    "name": "string",
    "description": "string",
    "category": "string",
    "templateContent": "string",
    "userFields": [
      /* ... */
    ],
    "localFields": [
      /* ... */
    ],
    "systemFields": [
      /* ... */
    ]
  }
  ```

- **Responses**:
  - **200 OK**: Template updated successfully.
  - **400 Bad Request**: Validation error.
  - **404 Not Found**: Template does not exist.
  - **401 Unauthorized**: Authentication failed.

### 5. Delete Template

- **Endpoint**: `DELETE /templates/{id}`
- **Description**: Deletes a template by its ID.
- **Headers**:
  - `Authorization: Bearer <token>`
- **Responses**:
  - **204 No Content**: Template deleted successfully.
  - **404 Not Found**: Template does not exist.
  - **401 Unauthorized**: Authentication failed.

---

## Data Model

### Template Object

- **Fields**:
  - `id` (string, UUID): Unique identifier.
  - `name` (string): Template name.
  - `description` (string, optional): Brief description.
  - `category` (string): Category of the template (e.g., HR, Finance).
  - `templateContent` (string): The actual content with placeholders.
  - `userFields` (array of fields): List of user fields used in the template.
  - `localFields` (array of fields): List of local fields used in the template.
  - `systemFields` (array of fields): List of system fields used in the template.
  - `version` (string): Template version (e.g., "v1.0").
  - `createdBy` (string): ID of the super admin who created the template.
  - `createdAt` (timestamp): Creation timestamp.
  - `updatedAt` (timestamp): Last updated timestamp.
  - `containerName` (string): Sanitized template name used as Cosmos DB container name

## Data Synchronization Architecture

### Change Feed Processing

1.  **Template Creation Trigger**:
    - When a new template is created, a Change Feed trigger initiates the container creation process
    - The container name is derived from the template name after sanitization (removing special characters, spaces, etc.)
2.  **Data Team Integration**:
    - The data team monitors new container creation events
    - Uses Spark or other ETL tools to populate the container with data from the source container
    - Maintains data synchronization using Change Feed on the source container

### Container Name Sanitization Rules

- Convert to lowercase
- Replace spaces with underscores
- Remove special characters
- Prefix with 'template\_' to avoid naming conflicts
- Maximum length of 63 characters (Cosmos DB limitation)

Example:

```json
Original: "Employee Absence Certificate 2024"
Sanitized: "template_employee_absence_certificate_2024"
```

### Updated Process Flow

```mermaid
flowchart TD
    A[Super Admin] --> B[Create Template]
    B --> C[Define the Fields]
    C --> D[Set Field Metadata & Validation]
    D --> E[Save Template]
    E --> F[Template Saved]
    F --> G[Change Feed Trigger]
    G --> H[Create New Container]
    H --> I[Data Team ETL Process]
    I --> J[Container Populated]
```

### Data Team Responsibilities

- Monitor new container creation events
- Set up ETL pipelines for data population
- Maintain data synchronization with source container
- Handle data transformation and mapping
- Manage error handling and retry logic

### 2. Document Generation Process

```mermaid
flowchart TD
    A[User] --> B[Request Document Generation]
    B --> C[Fetch Template by ID]
    C --> D[Retrieve the Fields]
    D --> E[Fetch Data from Microservices]
    E --> F[Collect user Fields from User]
    F --> G[Populate Template with Data]
    G --> H[Return final HTML]
```

---

## Integration with Other Microservices

- **UserData Microservice**: Provides user details like first name, last name, and email.

- **Workflow Microservice**: Provides other required data.

---

## Validation and Error Handling

- **Field-Level Validation**:
  - Enforced based on the `validation` rules defined in each field.
  - Includes checks for required fields, pattern matching, and length constraints.
- **Error Responses**:
  - **400 Bad Request**: Returned when validation fails.
  - **401 Unauthorized**: Authentication token is missing or invalid.
  - **403 Forbidden**: User lacks necessary permissions.
  - **404 Not Found**: Resource does not exist.
  - **500 Internal Server Error**: Unexpected server error.

**Example Error Response**:

```json
{
  "status": 400,
  "error": "Bad Request",
  "message": "Field 'name' is required."
}
```

---

## Security Considerations

- **Authentication**:
  - All endpoints require a valid JWT token.
  - Tokens are verified before processing requests.
- **Authorization**:
  - Only users with the `super_admin` role can create, update, or delete templates.
  - Regular users can only retrieve templates if necessary.

## Examples

### Creating a Template

**Request**:

```bash
POST /templates HTTP/1.1
Host: your-domain.com
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Absence Certificate",
  "description": "Used for employee absence validation",
  "category": "HR",
  "templateContent": "<html><body>Dear {{firstName}} {{lastName}},</body></html>",
  "localFields": [
    {
      "name": "reason",
      "type": "string",
      "validation": { "required": true, "maxLength": 255 }
    }
  ],
  "userFields": [
    {
      "name": "firstName",
      "sourceDetails": {
        "serviceName": "UserData",
        "endpoint": "/users/{id}",
        "key": "firstName"
      },
      "type": "string",
      "validation": { "required": true }
    }
  ],
  "systemFields": [
    {
      "name": "lastName",
      "type": "string",
      "validation": { "required": true }
    }
  ]
}
```

**Response**:

```json
{
  "name": "Absence Certificate",
  "description": "Used for employee absence validation",
  "category": "HR",
  "templateContent": "<html><body>Dear {{firstName}} {{lastName}},</body></html>",
  "localFields": [
    {
      "name": "reason",
      "type": "string",
      "validation": { "required": true, "maxLength": 255 }
    }
  ],
  "userFields": [
    {
      "name": "firstName",
      "sourceDetails": {
        "serviceName": "UserData",
        "endpoint": "/users/{id}",
        "key": "firstName"
      },
      "type": "string",
      "validation": { "required": true }
    }
  ],
  "systemFields": [
    {
      "name": "lastName",
      "type": "string",
      "validation": { "required": true }
    }
  ]
}
```

---

# New Endpoint: Get Template Fields and Data

## Endpoint

```
GET /templates/{id}/fields or by workflow id
```

## Description

Retrieves all fields for a specific template, including:

- **System Fields**: Data fetched from other microservices (e.g., UserData).
- **Local Fields**: Inputs from the Workflow microservice, possibly with pre-filled values.

This endpoint consolidates the data needed by the client to render forms or generate documents based on a template.

## Request Parameters

- **Path Parameters**:

  - `id` (string, UUID): The ID of the template.

- **Query Parameters**:
  - `contextId` (string, optional): Identifier used to fetch data from Workflow and system microservices (e.g., user ID, workflow instance ID).

## Headers

- `Authorization: Bearer <token>`

## Responses

- **200 OK**

  ```json
  {
    "templateId": "string",
    "templateName": "string",
    "systemFields": {
      "fieldName1": "value1",
      "fieldName2": "value2"
    },
    "localFields": [
      {
        "name": "string",
        "type": "string | number | date | boolean",
        "validation": {
          "required": true,
          "pattern": "string",
          "minLength": 0,
          "maxLength": 255
        },
        "value": "string | number | boolean | null"
      }
      // Additional local fields...
    ],
    "metadata": {
      "createdBy": "string",
      "createdAt": "timestamp",
      "version": "string"
    }
  }
  ```

- **400 Bad Request**: Invalid parameters or missing required information.
- **401 Unauthorized**: Authentication failed.
- **404 Not Found**: Template not found.

## Example Request

```http
GET /templates/123e4567-e89b-12d3-a456-426614174000/fields?contextId=user-123 HTTP/1.1
Host: your-domain.com
Authorization: Bearer <token>
```

## Example Response

```json
{
  "templateId": "123e4567-e89b-12d3-a456-426614174000",
  "templateName": "Absence Certificate",
  "systemFields": {
    "firstName": "John",
    "lastName": "Doe",
    "employeeId": "E12345"
  },
  "localFields": [
    {
      "name": "reason",
      "type": "string",
      "validation": {
        "required": true,
        "maxLength": 255
      },
      "value": "Medical Appointment"
    },
    {
      "name": "startDate",
      "type": "date",
      "validation": {
        "required": true
      },
      "value": null
    }
  ],
  "metadata": {
    "createdBy": "admin-uuid",
    "createdAt": "2023-10-01T12:00:00Z",
    "version": "v1.0"
  }
}
```

## How It Works

1. **local Fields Retrieval**: Fetches data from workflow microservice.

2. **user Fields Retrieval**: Fetches data from user microservice.

3. **sysetem Fields**: Automatically populated using the cirrent date and time.

4. **Data Aggregation**: Combines system fields and local fields into a unified response for the client.

---

## How We Use Service Bus and gRPC to Retrieve Data

In our Document Template microservice, we utilize a combination of service bus and gRPC to efficiently retrieve data from the workflow service and user service.

1. **Service Bus**:

-We use Azure service bus to facilitate communication between microservices. The service bus acts as a message broker, allowing different services to send and receive messages asynchronously.

-When the workflow service sends to data to the document Template microservice, it sends a message to the service bus. The document Template service listens for these messages and use the data.

1. **gRPC**:

-Document Template Service needs to retrieve user fields, it makes a gRPC call to the user service, which processes the request and returns the necessary data.

### Notification Service

# Low-Level Design: Multi-Channel Notification Microservice

## 1. Overview

The Notification Microservice is designed to handle multi-channel notifications through a unified interface. It processes events from other services and manages notification delivery across different channels while maintaining scalability and reliability.

## 2. Core Components & Rationale

### 2.1 Communication Channels

1.  **Azure Communication Services (Email & SMS)**

    - **Why**: Provides reliable, scalable infrastructure for sending emails and SMS
    - **Benefits**:
      - Built-in delivery tracking
      - Compliance with global communication regulations
      - High deliverability rates
      - Cost-effective for high volumes

2.  **Azure Notification Hubs (Push Notifications)**

    - **Why**: Manages device registration and cross-platform push delivery
    - **Benefits**:
      - Native support for iOS, Android, and web push
      - Handles device token management
      - Supports tag-based targeting
      - Automatic handling of platform-specific requirements

3.  **Web App Notifications**

    - **Why**: Provides real-time updates within the web application
    - **Benefits**:

      - Immediate user feedback
      - No external service dependencies
      - Can be persisted for offline viewing
      - Low cost as it uses existing web infrastructure

    For web notifications, there are several approaches we can implement:

    1. **Server-Sent Events (SSE)**:

       - One-way connection from server to client
       - Lightweight and simple to implement
       - Built-in reconnection handling
       - Better for scenarios where you only need server-to-client communication

    2. **WebSocket**:

       - Full-duplex communication
       - More complex but more powerful
       - Good for real-time bidirectional communication
       - Higher overhead than SSE

    3. **Azure SignalR Service**

       - Managed service that handles scaling
       - Works well with Azure infrastructure
       - Supports multiple protocols (WebSocket, SSE, Long Polling)
       - Automatic fallback handling
       - Built-in authentication integration

    **The flow would be:**

         1. Service receives notification event from Service Bus
         2. Stores notification in Cosmos DB
         3. Pushes to SignalR Hub
         4. SignalR broadcasts to connected clients
         5. Client receives and displays notification

    This approach gives you:

        - Real-time notifications through SignalR
        - Historical notification access through REST API
        - Offline support with notification persistence
        - Scalability through Azure managed services
        - Fallback mechanisms for different browsers/scenarios

### 2.2 Event Processing

**Azure Service Bus**

- **Why**: Reliable event processing and service decoupling
- **Use Cases**:
  - Processing workflow events
  - Handling notification requests
  - Managing notification scheduling
- **Benefits**:
  - Message persistence
  - Dead-letter queue support
  - FIFO message handling
  - Support for message sessions

### 2.3 Data Storage

**Cosmos DB**

- **Why**: Flexible schema for different notification types and high scalability
- **Benefits**:
  - Multi-region support
  - Schema flexibility for different notification types
  - Built-in TTL for notification expiry
  - Quick queries with indexed attributes

## 3. System Architecture

```mermaid
graph TD
    subgraph External Services
        WF[Workflow Service] --> ASB
        US[User Service] --> NS
    end

    subgraph Message Processing
        ASB[Azure Service Bus] --> NS[Notification Service]
        NS --> TE[Template Engine]
    end

    subgraph Data Storage
        NS --> CDB[(Cosmos DB)]
    end

    subgraph Notification Channels
        NS --> ACS[Azure Communication Services]
        NS --> ANH[Azure Notification Hubs]
        NS --> WN[Web Notifications]

        ACS --> Email[Email]
        ACS --> SMS[SMS]
        ANH --> Push[Push Notifications]
        WN --> Web[Web App]
    end
```

## 4. Service Integration Flow

```mermaid
sequenceDiagram
    participant WS as Workflow Service
    participant SB as Azure Service Bus
    participant NS as Notification Service
    participant US as User Service
    participant NC as Notification Channels
    participant DB as Cosmos DB

    WS->>SB: Publish Notification Event
    Note right of WS: Event includes notification type and target criteria

    SB->>NS: Process Notification Event

    NS->>US: Get User Information
    Note right of NS: Query users by ID or Role
    US-->>NS: Return User Details

    NS->>DB: Store Notification Record

    NS->>NC: Dispatch to Appropriate Channel(s)
    Note right of NS: Based on user preferences and notification type

    NC-->>NS: Delivery Status
    NS->>DB: Update Notification Status
```

## 5. Data Models

### 5.1 Cosmos DB Container Designs

```typescript
// Notification Document
interface NotificationDocument {
  id: string;
  type: "EMAIL" | "SMS" | "PUSH" | "WEB";
  status: "PENDING" | "PROCESSING" | "DELIVERED" | "FAILED";
  content: {
    templateId: string;
    variables: Record<string, any>;
    renderedContent?: string;
  };
  target: {
    userIds?: string[];
    roles?: string[];
    all?: boolean;
  };

  channels: {
    email?: {
      subject: string;
      template: string;
    };
    sms?: {
      template: string;
    };
    push?: {
      title: string;
      body: string;
      data?: Record<string, any>;
    };
    web?: {
      title: string;
      body: string;
      action?: string;
    };
  };
  tracking: {
    createdAt: Date;
    updatedAt: Date;
    deliveredAt?: Date;
    attempts: number;
    errors?: string[];
  };
}
```

### 5.2 Event Schema

```typescript
// Notification Event
interface NotificationEvent {
  eventType: "SEND_NOTIFICATION";
  payload: {
    templateId: string;
    variables: Record<string, any>;
    target: {
      userIds?: string[];
      roles?: string[];
      all?: boolean;
    };
    channels: string[];
    priority: "HIGH" | "MEDIUM" | "LOW";
    scheduling?: {
      sendAt: Date;
      recurrence?: string;
    };
  };
}
```

## 7. Failure Handling & Retry Strategy

```mermaid
stateDiagram-v2
    [*] --> PENDING
    PENDING --> PROCESSING
    PROCESSING --> DELIVERED
    PROCESSING --> RETRY
    RETRY --> PROCESSING
    RETRY --> FAILED
    FAILED --> [*]
    DELIVERED --> [*]

    note right of RETRY
        Exponential backoff:
        1st retry: 30s
        2nd retry: 5m
        3rd retry: 30m
        Then to DLQ
    end note
```

## 8. Performance Considerations

1. **Cosmos DB Partitioning Strategy**

   - Partition key: Composite of userId + date for even distribution
   - TTL based on notification type and importance
   - Indexed fields for common queries

2. **Service Bus Message Sessions**

   - Group related notifications for ordered processing
   - Maintain user notification sequence
   - Handle bulk notifications efficiently

## Request Service

# **Request Service Design**

## **Overview**

The **Request Service** is responsible for managing the initiation and tracking of all requests within the system. It supports a variety of request types and allows for both personal requests and requests made by Team Leaders on behalf of operators.

---

## **Purpose**

- **Request Management**: Handles the creation, updating, and tracking of different request types.
- **On-Behalf Requests**: Allows Team Leaders to create requests on behalf of one or multiple operators.
- **Status Tracking**: Maintains the status and timestamps of requests throughout their lifecycle.
- **Workflow Integration**: Communicates with the Workflow Service to initiate and manage approval workflows.
- **Event Publishing**: Emits domain events like `RequestCreated` and `RequestOnBehalfCreated` for integration with other services.

---

## **High-Level Architecture**

```mermaid
graph TD
    A[User Interface] -->|Request Creation/Query| B[Request Service]
    B -->|Start Workflow| C[Workflow Service]
    B -->|Publish Events| D[Event Bus]
    D -->|Consume Events| E[Notification Service]
    B -->|Query Database| F[Database]
    C -->|Workflow Updates| B
```

---

## **Responsibilities**

1. **Request Creation and Management**:

   - Supports various request types:
     - Absence Authorization
     - Holiday Leave
     - Movement Requests
     - Overtime
     - Payroll Claims
     - Clocking Corrections
   - Stores detailed information for each request.
   - Manages request statuses (e.g., Pending, Approved, Rejected).

2. **On-Behalf Request Handling**:
   - Enables Team Leaders to submit requests on behalf of on or multiple team members.
   - Tracks both the requester (Team Leader) and the target operator.
3. **Multi-Operator Request Handling**:
   - Allows Team Leaders to create a single request that applies to multiple operators.
   - Associates the request with all specified operators while maintaining a single workflow instance.
   - Ensures that status updates are synchronized across all operators.
4. **External Integration**:
   - Initiates workflows in the Workflow Service upon request creation.
   - Publishes domain events to an event bus for other services to consume.

---

## **Data Flow**

```mermaid
sequenceDiagram
    participant User
    participant RequestService
    participant WorkflowService
    participant ServiceBus
    participant NotificationService

    User->>RequestService: Create Request
    RequestService->>WorkflowService: Start Approval Workflow
    WorkflowService-->>RequestService: Workflow Updates
    RequestService->>ServiceBus: Publish RequestCreated Event
    ServiceBus->>NotificationService: Forward Events
    NotificationService-->>User: Send Notifications
```

---

## **Entities**

### **1. Request**

Represents a generic request within the system.

| **Field**            | **Type**  | **Description**                                     |
| -------------------- | --------- | --------------------------------------------------- |
| `id`                 | UUID      | Unique identifier.                                  |
| `requestType`        | String    | Type of the request (e.g., `AbsenceAuthorization`). |
| `requesterId`        | UUID      | ID of the user who created the request.             |
| `targetOperatorIds`  | string[]  | ID of the operators for on-behalf requests.         |
| `status`             | String    | Current status of the request.                      |
| `data`               | JSON      | Specific data related to the request type.          |
| `createdAt`          | Timestamp | Creation time.                                      |
| `updatedAt`          | Timestamp | Last update time.                                   |
| `workflowInstanceId` | UUID      | Associated workflow instance ID.                    |

---

## **API Endpoints**

### **1. Create Request**

| **Method** | **Endpoint** | **Description**        |
| ---------- | ------------ | ---------------------- |
| POST       | `/requests`  | Creates a new request. |

#### **Request Body Example**:

```json
{
  "requestType": "AbsenceAuthorization",
  "data": {
    "startDate": "2023-11-01",
    "endDate": "2023-11-05",
    "reason": "Medical appointment"
  }
}
```

#### **Response**:

```json
{
  "id": "request-uuid",
  "requestType": "AbsenceAuthorization",
  "requesterId": "user-uuid",
  "status": "Pending",
  "createdAt": "2023-10-15T12:00:00Z",
  "updatedAt": "2023-10-15T12:00:00Z"
}
```

---

### **2. Create On-Behalf Request**

| **Method** | **Endpoint**          | **Description**                                      |
| ---------- | --------------------- | ---------------------------------------------------- |
| POST       | `/requests/on-behalf` | Team Leaders create requests on behalf of operators. |

#### **Request Body Example**:

```json
{
  "targetOperatorIds": ["operator-1", "operator-2"],
  "requestType": "Overtime",
  "data": {
    "date": "2023-11-10",
    "hours": 2,
    "reason": "Project deadline"
  }
}
```

#### **Response**:

```json
{
  "id": "request-uuid",
  "requestType": "Overtime",
  "requesterId": "team-leader-uuid",
  "targetOperatorIds": "operator-uuid",
  "status": "Pending",
  "createdAt": "2023-10-15T12:05:00Z",
  "updatedAt": "2023-10-15T12:05:00Z"
}
```

---

### **3. Get Request by ID**

| **Method** | **Endpoint**     | **Description**               |
| ---------- | ---------------- | ----------------------------- |
| GET        | `/requests/{id}` | Retrieves a specific request. |

---

### **4. List Requests**

| **Method** | **Endpoint** | **Description**              |
| ---------- | ------------ | ---------------------------- |
| GET        | `/requests`  | Lists requests for the user. |

---

### **5. Update Request Status**

| **Method** | **Endpoint**            | **Description**                  |
| ---------- | ----------------------- | -------------------------------- |
| PUT        | `/requests/{id}/status` | Updates the status of a request. |

#### **Request Body Example**:

```json
{
  "status": "Approved",
  "comments": "Approved by manager"
}
```

---

## **Domain Events**

| **Event**                     | **Description**                                           |
| ----------------------------- | --------------------------------------------------------- |
| `RequestCreated`              | Emitted when a new request is created.                    |
| `RequestOnBehalfCreated`      | Emitted when a Team Leader creates a request on behalf.   |
| `MultiOperatorRequestCreated` | Emitted when a request is created for multiple operators. |
| `RequestUpdated`              | Emitted when a request is updated (e.g., status changes). |

---

## **Functionality**

- When a Team Leader creates a request for multiple operators, the Request Service generates a single request record and associates it with all the specified operators. This ensures that all operators are linked to the same request, maintaining consistency across the system.

- For multi-operator requests, a single workflow instance is initiated, and the workflow process is shared by all operators involved.

## **Process Flow**

- **Request Creation**:
- Users can create requests for themselves.
- Team Leaders can create requests on behalf of one or multiple operators.
- The Team Leader provides a list of operator IDs to the Request Service
- For multi-operator requests, a single request is created and associated with all specified operators.

- **Multi-Operator Request Handling**:
- When a Team Leader creates a request for multiple operators:
  - A single request record is created.
  - The targetOperatorIds field stores the IDs of all affected operators.
  - A single workflow instance is started that will handle the approval process for all the operators in the list.
  - The Notification Service sends updates to all involved operators when the status changes (e.g., approval or rejection).
  - The request status is synchronized across all operators.

## **Integration with Other Services**

- **Workflow Service**: Manages approval processes.
- **Notification Service**: Alerts users of status updates or approvals.

---

## **Error Handling**

| **Code** | **Description**                              |
| -------- | -------------------------------------------- |
| 400      | Bad Request - Validation failed.             |
| 401      | Unauthorized - Authentication failed.        |
| 403      | Forbidden - User lacks required permissions. |
| 404      | Not Found - Resource does not exist.         |
| 500      | Internal Server Error - Unexpected issue.    |

## UserData Service

# UserData Service Design

## Overview

The **UserData Service** is a microservice responsible for managing user information within an enterprise system. It provides functionalities to retrieve and manipulate user data, handle hierarchical relationships between users (e.g., managers and subordinates), and integrate with other services through domain events. The service uses a document-oriented database (Cosmos DB) for storage and is designed to be abstract enough to be implemented in any programming language.

---

## High-Level Architecture

### Components

1. **API Layer**: Exposes RESTful endpoints for interacting with user data.
2. **Service Layer**: Contains business logic for processing user information and relationships.
3. **Data Access Layer**: Interfaces with Cosmos DB to perform CRUD operations.
4. **Event Publisher**: Publishes domain events (e.g., `UserCreated`, `HierarchyUpdated`) to an event bus for other services to consume.
5. **Integration Layer**: Allows other microservices (e.g., Workflow Service, Notification Service) to interact with the UserData Service via APIs and event subscriptions.

### Architecture Diagram

---

## Data Model

### User Entity

Represents a user within the system.

**Fields**:

- `id` (UUID): Unique identifier of the user.
- `name` (string): Full name of the user.
- `email` (string): Email address.
- `role` (string): Role or position (e.g., "Operator", "Manager").
- `manager_id` (UUID, nullable): ID of the user's direct manager.
- `department` (string): Department name.
- `additional_info` (object, optional): Any additional metadata.

**Example**:

```json
{
  "id": "user-uuid",
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "role": "Operator",
  "manager_id": "manager-uuid",
  "department": "Sales",
  "additional_info": {
    "phone": "+1234567890",
    "location": "Building A"
  }
}
```

---

## API Endpoints

### 1. Fetch User by ID

- **Endpoint**: `GET /users/{id}`
- **Description**: Retrieves a user's details by their unique ID.

**Request Parameters**:

- `id` (path): The UUID of the user to retrieve.

**Response**:

- **200 OK**:

  ```json
  {
    "id": "user-uuid",
    "name": "Jane Doe",
    "email": "<EMAIL>",
    "role": "Operator",
    "manager_id": "manager-uuid",
    "department": "Sales"
  }
  ```

- **404 Not Found**: User does not exist.

---

### 2. Fetch All Users

- **Endpoint**: `GET /users`
- **Description**: Retrieves a list of all users, with optional filtering.

**Query Parameters** (optional):

- `department` (string): Filter users by department.
- `role` (string): Filter users by role.

**Response**:

- **200 OK**:

  ```json
  [
    {
      "id": "user-uuid-1",
      "name": "Jane Doe",
      "email": "<EMAIL>",
      "role": "Operator",
      "manager_id": "manager-uuid",
      "department": "Sales"
    },
    {
      "id": "user-uuid-2",
      "name": "John Smith",
      "email": "<EMAIL>",
      "role": "Manager",
      "manager_id": null,
      "department": "Sales"
    }
  ]
  ```

---

### 3. Fetch Subordinates of a Manager

- **Endpoint**: `GET /users/{id}/subordinates`
- **Description**: Retrieves all users who report directly or indirectly to the specified manager.

**Request Parameters**:

- `id` (path): The UUID of the manager.

**Response**:

- **200 OK**:

  ```json
  [
    {
      "id": "user-uuid-1",
      "name": "Jane Doe",
      "email": "<EMAIL>",
      "role": "Operator",
      "manager_id": "manager-uuid",
      "department": "Sales"
    }
  ]
  ```

- **404 Not Found**: Manager does not exist.

---

### 4. Update User's Manager

- **Endpoint**: `PUT /users/{id}/manager`
- **Description**: Updates the manager of a user.

**Request Parameters**:

- `id` (path): The UUID of the user.

**Request Body**:

```json
{
  "manager_id": "new-manager-uuid"
}
```

**Response**:

- **200 OK**:

  ```json
  {
    "message": "Manager updated successfully."
  }
  ```

- **400 Bad Request**: Validation error (e.g., `manager_id` does not exist).
- **404 Not Found**: User does not exist.

---

## Handling Hierarchical Relationships

### Manager-Subordinate Structure

- Each user has a `manager_id` attribute pointing to their direct manager.
- This structure creates a hierarchy that can be traversed to find subordinates or superiors.

### Querying Subordinates

- **Direct Subordinates**: Users whose `manager_id` matches the manager's `id`.
- **Indirect Subordinates**: Users who are subordinates of the manager's subordinates.

### Algorithm for Fetching All Subordinates

1. **Initialize**: Start with the manager's `id`.
2. **Recursive Fetch**: Retrieve all users whose `manager_id` is in the current list of IDs.
3. **Accumulate**: Add fetched users to the result set.
4. **Repeat**: Use the newly fetched users' IDs to find further subordinates.
5. **Terminate**: When no new users are found.

---

## Integration with Other Services

### Domain Events

The UserData Service publishes domain events to an event bus (e.g., Azure Service Bus) to notify other services of changes.

#### Events Published

1. **UserCreated**

   - **When**: A new user is added to the system.
   - **Payload**:

     ```json
     {
       "eventType": "UserCreated",
       "user": {
         "id": "user-uuid",
         "name": "Jane Doe",
         "email": "<EMAIL>",
         "role": "Operator",
         "manager_id": "manager-uuid",
         "department": "Sales"
       },
       "timestamp": "2023-10-15T12:00:00Z"
     }
     ```

2. **UserUpdated**

   - **When**: An existing user's details are updated.
   - **Payload**:

     ```json
     {
       "eventType": "UserUpdated",
       "user": {
         "id": "user-uuid",
         "name": "Jane Doe",
         "email": "<EMAIL>",
         "role": "Operator",
         "manager_id": "manager-uuid",
         "department": "Sales"
       },
       "timestamp": "2023-10-16T09:00:00Z"
     }
     ```

3. **HierarchyUpdated**

   - **When**: A user's `manager_id` is changed.
   - **Payload**:

     ```json
     {
       "eventType": "HierarchyUpdated",
       "userId": "user-uuid",
       "oldManagerId": "old-manager-uuid",
       "newManagerId": "new-manager-uuid",
       "timestamp": "2023-10-17T15:30:00Z"
     }
     ```

### Consuming Services

- **Workflow Service**: Subscribes to `UserCreated` and `HierarchyUpdated` events to update approval hierarchies and assignment rules.
- **Notification Service**: Uses user data to send notifications to the correct recipients.
- **Other Services**: Any service that requires up-to-date user and hierarchy information.

### Sequence Diagram: Updating a User's Manager

```mermaid
sequenceDiagram
    participant Client
    participant UserDataService
    participant EventBus
    participant WorkflowService
    participant NotificationService

    Client->>UserDataService: PUT /users/{id}/manager
    UserDataService->>UserDataService: Update manager_id
    UserDataService->>EventBus: Publish HierarchyUpdated
    EventBus-->>WorkflowService: HierarchyUpdated Event
    EventBus-->>NotificationService: HierarchyUpdated Event
    UserDataService-->>Client: 200 OK
```

---

## Data Flow

### User Creation Flow

1. **Client** sends a request to create a new user.
2. **UserData Service** validates and stores the user in Cosmos DB.
3. **UserData Service** publishes a `UserCreated` event to the event bus.
4. **Other Services** consume the `UserCreated` event to update their data or trigger actions.

### Manager Update Flow

1. **Client** sends a `PUT` request to update a user's `manager_id`.
2. **UserData Service** validates the new manager and updates the user record.
3. **UserData Service** publishes a `HierarchyUpdated` event.
4. **Workflow Service** updates approval hierarchies based on the new manager.
5. **Notification Service** adjusts notification routes if necessary.

---

## Sequence Diagram: User Creation Integration

```mermaid
sequenceDiagram
    participant Client
    participant UserDataService
    participant EventBus
    participant WorkflowService
    participant NotificationService

    Client->>UserDataService: POST /users
    UserDataService->>UserDataService: Validate and store user
    UserDataService->>EventBus: Publish UserCreated
    EventBus-->>WorkflowService: UserCreated Event
    EventBus-->>NotificationService: UserCreated Event
    UserDataService-->>Client: 201 Created
```

---

## Error Handling

- **400 Bad Request**: Returned when the request data is invalid.

  - Examples: Missing required fields, invalid `manager_id`, incorrect data types.
  - **Response**:

    ```json
    {
      "error": "ValidationError",
      "message": "The 'email' field is required."
    }
    ```

- **404 Not Found**: Returned when the requested user or manager does not exist.

  - **Response**:

    ```json
    {
      "error": "NotFoundError",
      "message": "User with ID 'user-uuid' not found."
    }
    ```

- **500 Internal Server Error**: Returned when an unexpected error occurs.

  - **Response**:

    ```json
    {
      "error": "InternalServerError",
      "message": "An unexpected error occurred."
    }
    ```

---

## Cosmos DB Data Modeling

- **Container**: Users
- **Partition Key**: `id` (since `id` is unique and queries are mostly by `id`)

### Sample Document Structure

```json
{
  "id": "user-uuid",
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "role": "Operator",
  "manager_id": "manager-uuid",
  "department": "Sales",
  "additional_info": {
    "phone": "+1234567890",
    "location": "Building A"
  }
}
```

---

## High-Level Data Flow Diagram

## Abstract Implementation Considerations

- **Programming Language Agnostic**: The service design does not rely on any language-specific features, making it suitable for implementation in languages like Java, C#, Python, etc.
- **RESTful APIs**: Standard HTTP methods and status codes are used.
- **JSON Format**: For request and response bodies, which is widely supported.
- **Event Bus Integration**: Uses common messaging patterns that can be implemented with various technologies (e.g., Azure Service Bus, RabbitMQ, Kafka).
- **Database Abstraction**: While Cosmos DB is specified, any document-oriented database can be used with similar capabilities (e.g., MongoDB, Couchbase).

---

## Security Considerations

- **Authentication**: Ensure that all endpoints are secured and require valid authentication tokens (e.g., JWT).
- **Authorization**: Implement role-based access control (RBAC) to restrict access to sensitive operations (e.g., only admins can update user information).
- **Input Validation**: Validate all incoming data to prevent injection attacks and ensure data integrity.
- **Data Privacy**: Handle personal data according to data protection regulations (e.g., GDPR).

---

## Additional Features (Optional)

### Pagination and Sorting

- **Endpoint**: `GET /users`
- **Query Parameters**:
  - `page` (integer): Page number.
  - `pageSize` (integer): Number of items per page.
  - `sortBy` (string): Field to sort by.
  - `sortOrder` (string): `asc` or `desc`.

### Search Functionality

- Allow searching users by name, email, or other attributes using query parameters.

### Bulk Operations

- Endpoints to create, update, or delete multiple users in a single request.

---

## Conclusion

The **UserData Service** provides a robust and flexible foundation for managing user information and hierarchical relationships within an enterprise system. By integrating with other services through domain events and providing a comprehensive API, it supports the dynamic needs of modern applications while maintaining a clean and scalable architecture.
