# 📄 Low-Level Design (LLD) - Nurse Microservice

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture Overview](#2-architecture-overview)
3. [Data Model](#3-data-model)
   - [Core Collections](#31-core-collections)
   - [Entity Relationships](#32-entity-relationships)
4. [Collection Schemas](#4-collection-schemas)
   - [Employee Collection](#41-employee-collection)
   - [IllnessRecord Collection](#42-illnessrecord-collection)
   - [WorkAccidentRecord Collection](#43-workaccidentrecord-collection)
   - [PregnancyRecord Collection](#44-pregnancyrecord-collection)
   - [WorkCapabilityRecord Collection](#45-workcapabilityrecord-collection)
   - [DocumentRecord Collection](#46-documentrecord-collection)
   - [RegionalSettings Collection](#47-regionalsettings-collection)
5. [API Endpoints](#5-api-endpoints)
   - [Illness Period APIs](#51-illness-period-apis)
   - [Work Accident APIs](#52-work-accident-apis)
   - [Pregnancy APIs](#53-pregnancy-apis)
   - [Work Capability APIs](#54-work-capability-apis)
   - [Document APIs](#55-document-apis)
   - [Reporting APIs](#56-reporting-apis)
6. [Indexing Strategy](#6-indexing-strategy)
7. [Query Patterns](#7-query-patterns)
8. [Integration Patterns](#8-integration-patterns)
   - [Event Schema](#81-event-schema)
   - [Change Feed Integration](#82-change-feed-integration)
   - [Integration with Module 3 (Status Tracking)](#83-integration-with-module-3-status-tracking)
   - [Integration with Transport Module](#84-integration-with-transport-module)
   - [Team Leader/Shift Leader UI Integration](#85-team-leader-shift-leader-ui-integration)
9. [Workflow Orchestration](#9-workflow-orchestration)
   - [Illness Period Workflow](#91-illness-period-workflow)
   - [Work Accident Workflow](#92-work-accident-workflow)
   - [Pregnancy Announcement Workflow](#93-pregnancy-announcement-workflow)
   - [Work Capability Assessment Workflow](#94-work-capability-assessment-workflow)
10. [Regional Variation Implementation](#10-regional-variation-implementation)

    - [Decision Process](#101-decision-process)
    - [Configuration-Driven Workflows](#102-configuration-driven-workflows)

11. [Conclusion](#11-conclusion)

---

## 5.2.6 Nursing Management

### 1. Overview

The Nurse Microservice is a critical component of the Connected Workers system that manages all health-related data and processes for employees across multiple regions. Built using NestJS and Azure Cosmos DB, this microservice provides a comprehensive solution for tracking and managing employee health statuses including illness periods, work accidents, pregnancy accommodations, and work capability assessments. The system employs an event-driven architecture with CQRS patterns to ensure data consistency across services while maintaining employee privacy and regional compliance. By centralizing health status management, the Nurse Microservice enables efficient workforce planning, ensures proper accommodations for health-related situations, and streamlines communication between medical staff, team leaders, and administrative personnel.

#### Key Features

- **Illness Period Management**: Complete tracking of employee sick leave with medical certificate verification and follow-up scheduling
- **Work Accident Processing**: Documentation and management of workplace injuries with investigation tracking and extension capabilities
- **Pregnancy Announcement Handling**: Processing of pregnancy declarations with special accommodation tracking and privacy controls
- **Work Capability Assessment**: Management of employee work restrictions and limitations with visibility controls for team leaders
- **Document Management**: Secure storage and retrieval of medical certificates and other health documentation with access controls
- **Regional Variation Support**: Configuration-driven workflows adaptable to different regional requirements and regulations
- **Team Leader Visibility**: Controlled sharing of relevant health status information to team leaders without exposing confidential medical details
- **Status Code Integration**: Standardized status codes for integration with time keeping and other administrative systems
- **Event-Driven Architecture**: Real-time updates and notifications across the enterprise system

#### Key Integration Points

- **Status Tracking Module (Module 3)**: Publishes employee status updates for attendance tracking with standardized status codes (MA, AT)
- **Time Keeping System**: Integrates attendance data based on medical status updates for payroll and scheduling
- **Transport Module**: Coordinates transportation for employees needing to submit medical certificates at medical centers
- **Team Leader/Shift Leader UI**: Provides status tags and relevant information for supervisors without exposing confidential details
- **Employee Master Data**: Consumes employee data via Change Feed from HR systems to maintain consistency
- **Organizational Structure**: Synchronizes with department and team hierarchies for proper notification routing
- **Document Storage**: Interfaces with Azure Blob Storage for secure medical document retention
- **Regional Systems**: Connects with region-specific government portals or national healthcare platforms as required

### 2. Architecture Overview

The Nurse microservice is built using NestJS with TypeScript, leveraging Azure Cosmos DB as the NoSQL database. The system follows an event-driven architecture with CQRS pattern implementation for efficient data handling:

- **Read Operations**: Utilize duplicated data via Change Feed to minimize cross-service dependencies
- **Write Operations**: Emit domain events via Azure Service Bus that are consumed by relevant services
- **Data Consistency**: Maintained asynchronously through eventual consistency patterns

```mermaid
graph TD
    A[Nurse Application] --> B[NestJS API Layer]
    B --> C[Service Layer]
    C --> D[Repository Layer]
    D --> E[Azure Cosmos DB]
    C --> F[Event Publisher]
    F --> G[Azure Service Bus]
    G --> H[Time Keeping System]
    G --> I[Other Microservices]
    J[Change Feed Processor] --> E
    J --> K[Local Read Models]
    L[External Services] --> M[Change Feed]
    M --> E
```

---

### 3. Data Model

#### Core Collections

The Nurse microservice's data model consists of the following core collections:

1. **Employee**: Denormalized employee data synced from the primary HR system
2. **IllnessRecord**: Records of employee illness periods
3. **WorkAccidentRecord**: Work-related accident and injury records
4. **PregnancyRecord**: Pregnancy announcements and tracking
5. **WorkCapabilityRecord**: Employee work ability and restriction status
6. **DocumentRecord**: Metadata for stored medical certificates and documentation
7. **RegionalSettings**: Region-specific process configurations and rules

#### Entity Relationships

```mermaid
classDiagram
    Employee "1" --> "0..*" IllnessRecord
    Employee "1" --> "0..*" WorkAccidentRecord
    Employee "1" --> "0..1" PregnancyRecord
    Employee "1" --> "0..*" WorkCapabilityRecord
    IllnessRecord "1" --> "0..*" DocumentRecord
    WorkAccidentRecord "1" --> "0..*" DocumentRecord
    PregnancyRecord "1" --> "0..1" DocumentRecord
    WorkCapabilityRecord "1" --> "0..*" DocumentRecord
    RegionalSettings "1" --> "0..*" Employee

    class Employee {
        +string id
        +string employeeId
        +string firstName
        +string lastName
        +string departmentId
        +string departmentName
        +string position
        +string teamLeaderId
        +string teamLeaderName
        +string shiftLeaderId
        +string shiftLeaderName
        +string region
        +string hireDate
        +boolean isActive
        +string lastUpdated
        +number ttl
    }

    class IllnessRecord {
        +string id
        +string recordId
        +string employeeId
        +string employeeName
        +string departmentId
        +string startDate
        +string endDate
        +string submissionDate
        +string submittedBy
        +string certificateSubmissionMethod
        +object bailiffDetails
        +object doctorDetails
        +string[] documentIds
        +string status
        +string statusCode
        +object notificationStatus
        +boolean followUpRequired
        +string followUpDate
        +string comments
        +object regionSpecificData
        +string createdAt
        +string updatedAt
        +number ttl
    }

    class WorkAccidentRecord {
        +string id
        +string recordId
        +string employeeId
        +string employeeName
        +string departmentId
        +string startDate
        +string endDate
        +string submissionDate
        +string submittedBy
        +string accidentDescription
        +string[] documentIds
        +string status
        +string statusCode
        +object notificationStatus
        +object[] extensions
        +string comments
        +object regionSpecificData
        +string createdAt
        +string updatedAt
        +number ttl
    }

    class PregnancyRecord {
        +string id
        +string recordId
        +string employeeId
        +string employeeName
        +string departmentId
        +string declarationDate
        +number weeksOfPregnancy
        +string expectedDueDate
        +string documentId
        +string status
        +object notificationStatus
        +string[] specialAccommodations
        +string terminationDate
        +string comments
        +object regionSpecificData
        +string createdAt
        +string updatedAt
    }

    class WorkCapabilityRecord {
        +string id
        +string recordId
        +string employeeId
        +string employeeName
        +string departmentId
        +string assessmentDate
        +string assessedBy
        +string status
        +string[] limitations
        +string effectiveFrom
        +string effectiveTo
        +string[] documentIds
        +boolean visibleToTeamLeader
        +string comments
        +string createdAt
        +string updatedAt
        +boolean isActive
    }

    class DocumentRecord {
        +string id
        +string recordType
        +string relatedRecordId
        +string employeeId
        +string documentType
        +string filename
        +string contentType
        +string storageUrl
        +string uploadDate
        +string uploadedBy
        +string accessRestriction
        +string expiryDate
        +boolean isDeleted
        +number ttl
    }

    class RegionalSettings {
        +string id
        +string region
        +boolean sicknessAbsenteeismEnabled
        +object processingFlow
        +object requiredFields
        +object notificationConfig
        +object integrationConfig
        +string updatedAt
        +number version
    }
```

---

### 4. Collection Schemas

#### Employee Collection

```typescript
interface Employee {
  id: string; // Partition key: employeeId
  employeeId: string; // Unique ID from HR system
  firstName: string;
  lastName: string;
  departmentId: string;
  departmentName: string;
  position: string;
  teamLeaderId: string; // Direct supervisor ID
  teamLeaderName: string;
  shiftLeaderId: string;
  shiftLeaderName: string;
  region: string; // Country/region code
  hireDate: string;
  isActive: boolean;
  lastUpdated: string; // ISO datetime
  ttl?: number; // Time-to-live for inactive employees
}
```

#### IllnessRecord Collection

```typescript
interface IllnessRecord {
  id: string; // Partition key: employeeId
  recordId: string; // Unique record identifier
  employeeId: string;
  employeeName: string; // Denormalized for queries
  departmentId: string; // Denormalized for queries
  startDate: string; // ISO date
  endDate: string; // ISO date
  submissionDate: string; // ISO datetime
  submittedBy: string; // User ID of nurse
  certificateSubmissionMethod: "bailiff" | "employee" | "other";
  bailiffDetails?: {
    name: string;
    phoneNumber: string;
  };
  doctorDetails?: {
    specialty: string;
    name: string;
    phoneNumber: string;
  };
  documentIds: string[]; // References to associated documents
  status: "active" | "closed" | "extended";
  statusCode: "MA"; // Status code for TKS integration
  notificationStatus: {
    teamLeader: boolean;
    shiftLeader: boolean;
    departmentClerk: boolean;
  };
  followUpRequired: boolean;
  followUpDate?: string;
  comments?: string;
  regionSpecificData?: Record<string, any>;
  createdAt: string; // ISO datetime
  updatedAt: string; // ISO datetime
  ttl?: number; // Optional TTL for record expiration
}
```

#### WorkAccidentRecord Collection

```typescript
interface WorkAccidentRecord {
  id: string; // Partition key: employeeId
  recordId: string; // Unique record identifier
  employeeId: string;
  employeeName: string; // Denormalized for queries
  departmentId: string; // Denormalized for queries
  startDate: string; // ISO date
  endDate: string; // ISO date
  submissionDate: string; // ISO datetime
  submittedBy: string; // User ID of nurse
  accidentDescription?: string;
  documentIds: string[]; // References to associated documents
  status: "active" | "closed" | "extended";
  statusCode: "AT"; // Status code for TKS integration
  notificationStatus: {
    teamLeader: boolean;
    shiftLeader: boolean;
    departmentClerk: boolean;
    healthAndSafetyResponsible: boolean;
  };
  extensions: {
    date: string;
    extendedEndDate: string;
    reason?: string;
  }[];
  comments?: string;
  regionSpecificData?: Record<string, any>;
  createdAt: string; // ISO datetime
  updatedAt: string; // ISO datetime
  ttl?: number; // Optional TTL for record expiration
}
```

#### PregnancyRecord Collection

```typescript
interface PregnancyRecord {
  id: string; // Partition key: employeeId
  recordId: string; // Unique record identifier
  employeeId: string;
  employeeName: string; // Denormalized for queries
  departmentId: string; // Denormalized for queries
  declarationDate: string; // ISO date
  weeksOfPregnancy: number;
  expectedDueDate?: string; // ISO date
  documentId?: string; // Reference to pregnancy certificate
  status: "active" | "terminated" | "completed";
  notificationStatus: {
    teamLeader: boolean;
    nPlusOne: boolean; // Only for IS employees
  };
  specialAccommodations?: string[];
  terminationDate?: string; // ISO date, if status changed
  comments?: string;
  regionSpecificData?: Record<string, any>;
  createdAt: string; // ISO datetime
  updatedAt: string; // ISO datetime
}
```

#### WorkCapabilityRecord Collection

```typescript
interface WorkCapabilityRecord {
  id: string; // Partition key: employeeId
  recordId: string; // Unique record identifier
  employeeId: string;
  employeeName: string; // Denormalized for queries
  departmentId: string; // Denormalized for queries
  assessmentDate: string; // ISO date
  assessedBy: string; // User ID of nurse
  status: "able" | "unable" | "restricted";
  limitations: string[]; // Array of specific restrictions
  effectiveFrom: string; // ISO date
  effectiveTo?: string; // ISO date, if temporary
  documentIds?: string[]; // References to associated documents
  visibleToTeamLeader: boolean; // Visibility control
  comments?: string;
  createdAt: string; // ISO datetime
  updatedAt: string; // ISO datetime
  isActive: boolean;
}
```

#### DocumentRecord Collection

```typescript
interface DocumentRecord {
  id: string; // Unique document ID
  recordType: "illness" | "workAccident" | "pregnancy" | "workCapability";
  relatedRecordId: string; // ID of the related record
  employeeId: string;
  documentType: string; // Type of document (e.g., "medical_certificate")
  filename: string;
  contentType: string;
  storageUrl: string; // Azure Blob Storage URL
  uploadDate: string; // ISO datetime
  uploadedBy: string; // User ID of nurse
  accessRestriction: "nurse_only" | "team_leader" | "public";
  expiryDate?: string; // ISO date
  isDeleted: boolean;
  ttl?: number; // Time-to-live for document expiration
}
```

#### RegionalSettings Collection

```typescript
interface RegionalSettings {
  id: string; // Region code (e.g., "morocco", "poland")
  region: string;
  sicknessAbsenteeismEnabled: boolean;
  processingFlow: {
    illnessSubmission:
      | "direct_nurse"
      | "hr_agent"
      | "honor_declaration"
      | "national_platform"
      | "government_portal"
      | "ethar"
      | "hr_doctor";
    workAccidentSubmission: string;
    pregnancyAnnouncement: string;
    workCapabilityAssessment: string;
  };
  requiredFields: {
    illness: string[];
    workAccident: string[];
    pregnancy: string[];
    workCapability: string[];
  };
  notificationConfig: {
    recipients: Record<string, boolean>;
    channels: string[];
  };
  integrationConfig: {
    tksMapping: Record<string, string>;
    externalSystems: string[];
  };
  updatedAt: string; // ISO datetime
  version: number;
}
```

---

### 5. API Endpoints

#### Illness Period APIs

```
POST /api/v1/illness-periods
GET /api/v1/illness-periods
GET /api/v1/illness-periods/:recordId
PATCH /api/v1/illness-periods/:recordId
GET /api/v1/illness-periods/employee/:employeeId
GET /api/v1/illness-periods/follow-ups
POST /api/v1/illness-periods/:recordId/extend
```

**Request DTO for Illness Period Creation:**

```typescript
interface CreateIllnessPeriodDto {
  employeeId: string;
  startDate: string; // ISO date
  endDate: string; // ISO date
  certificateSubmissionMethod: "bailiff" | "employee" | "other";
  bailiffDetails?: {
    name: string;
    phoneNumber: string;
  };
  doctorDetails?: {
    specialty: string;
    name: string;
    phoneNumber: string;
  };
  followUpRequired: boolean;
  followUpDate?: string;
  comments?: string;
}
```

**Response DTO for Illness Period:**

```typescript
interface IllnessPeriodResponseDto {
  recordId: string;
  employeeId: string;
  employeeName: string;
  departmentId: string;
  departmentName: string;
  startDate: string;
  endDate: string;
  status: "active" | "closed" | "extended";
  statusCode: "MA";
  submissionDate: string;
  documentIds: string[];
  followUpRequired: boolean;
  followUpDate?: string;
}
```

#### Work Accident APIs

```
POST /api/v1/work-accidents
GET /api/v1/work-accidents
GET /api/v1/work-accidents/:recordId
PATCH /api/v1/work-accidents/:recordId
GET /api/v1/work-accidents/employee/:employeeId
POST /api/v1/work-accidents/:recordId/extend
```

#### Pregnancy APIs

```
POST /api/v1/pregnancies
GET /api/v1/pregnancies
GET /api/v1/pregnancies/employee/:employeeId
PATCH /api/v1/pregnancies/:recordId
POST /api/v1/pregnancies/:recordId/terminate
```

#### Work Capability APIs

```
POST /api/v1/work-capabilities
GET /api/v1/work-capabilities
GET /api/v1/work-capabilities/employee/:employeeId
PATCH /api/v1/work-capabilities/:recordId
```

#### Document APIs

```
POST /api/v1/documents
GET /api/v1/documents/:id
GET /api/v1/documents/record/:recordType/:recordId
DELETE /api/v1/documents/:id
```

#### Reporting APIs

```
GET /api/v1/reports/illness-periods
GET /api/v1/reports/work-accidents
GET /api/v1/reports/pregnancies
GET /api/v1/reports/work-capabilities
```

---

### 6. Indexing Strategy

Azure Cosmos DB indexing policies for optimal query performance:

```json
{
  "indexingMode": "consistent",
  "automatic": true,
  "includedPaths": [
    {
      "path": "/*"
    }
  ],
  "excludedPaths": [
    {
      "path": "/regionSpecificData/*"
    },
    {
      "path": "/comments"
    },
    {
      "path": "/documentIds/*"
    }
  ],
  "compositeIndexes": [
    [
      {
        "path": "/employeeId",
        "order": "ascending"
      },
      {
        "path": "/startDate",
        "order": "descending"
      }
    ],
    [
      {
        "path": "/departmentId",
        "order": "ascending"
      },
      {
        "path": "/status",
        "order": "ascending"
      },
      {
        "path": "/startDate",
        "order": "descending"
      }
    ]
  ]
}
```

#### Additional Indexes

- Create a spatial index for any location-based queries
- Create composite indexes for frequently combined query parameters
- Index properties supporting range queries and sorting (dates, status)

---

### 7. Query Patterns

Common query patterns supported by the data model:

1. **Get employee health records by ID and date range**

   ```typescript
   const records = await container.items
     .query({
       query:
         "SELECT * FROM c WHERE c.employeeId = @employeeId AND c.startDate >= @startDate AND c.endDate <= @endDate",
       parameters: [
         { name: "@employeeId", value: "123456" },
         { name: "@startDate", value: "2023-01-01" },
         { name: "@endDate", value: "2023-12-31" },
       ],
     })
     .fetchAll();
   ```

2. **Find all active illness records by department**

   ```typescript
   const records = await container.items
     .query({
       query:
         "SELECT * FROM c WHERE c.departmentId = @departmentId AND c.status = 'active'",
       parameters: [{ name: "@departmentId", value: "DEP123" }],
     })
     .fetchAll();
   ```

3. **Get employees with work capability restrictions**

   ```typescript
   const records = await container.items
     .query({
       query:
         "SELECT * FROM c WHERE c.status = 'restricted' AND c.isActive = true",
     })
     .fetchAll();
   ```

4. **Find records requiring follow-up**
   ```typescript
   const records = await container.items
     .query({
       query:
         "SELECT * FROM c WHERE c.followUpRequired = true AND c.followUpDate <= @today",
       parameters: [{ name: "@today", value: "2023-09-30" }],
     })
     .fetchAll();
   ```

---

### 8. Integration Patterns

#### Event Schema

Events emitted for integration with other services:

```typescript
interface NurseServiceEvent {
  id: string; // Unique event ID
  type: string; // Event type
  source: "nurse-service";
  timestamp: string; // ISO datetime
  dataVersion: string; // Schema version
  data: {
    employeeId: string;
    recordType: "illness" | "workAccident" | "pregnancy" | "workCapability";
    recordId: string;
    action: "created" | "updated" | "closed" | "extended";
    statusCode?: string; // Integration code (e.g., 'MA', 'AT')
    effectiveDate: string; // ISO date
    endDate?: string; // ISO date
    metadata?: Record<string, any>;
  };
  routing: {
    targetServices: string[]; // List of services that should consume this event
    priority: "high" | "medium" | "low";
    notificationTargets?: {
      teamLeader?: boolean;
      shiftLeader?: boolean;
      departmentClerk?: boolean;
      healthAndSafetyResponsible?: boolean;
      nPlusOne?: boolean;
    };
  };
}
```

#### Change Feed Integration

Data synchronized from other services:

1. **Employee Data** - Synced from HR microservice:

   - Basic employee information
   - Department and hierarchical data
   - Employment status changes

2. **Organization Structure** - Synced from Organization microservice:
   - Team leader and shift leader information
   - Department structure changes
   - Role assignments

Typical Change Feed processor:

```typescript
export class EmployeeChangeProcessor {
  async processChanges(changes: any[]) {
    for (const change of changes) {
      if (
        change.operationType === "insert" ||
        change.operationType === "replace"
      ) {
        await this.employeeRepository.upsert({
          id: change.fullDocument.employeeId,
          employeeId: change.fullDocument.employeeId,
          firstName: change.fullDocument.firstName,
          lastName: change.fullDocument.lastName,
          departmentId: change.fullDocument.departmentId,
          departmentName: change.fullDocument.departmentName,
          // Additional fields...
          lastUpdated: new Date().toISOString(),
        });
      } else if (change.operationType === "delete") {
        // Handle employee deletion or mark as inactive
        await this.employeeRepository.markAsInactive(change.documentKey._id);
      }
    }
  }
}
```

#### Integration with Module 3 (Status Tracking)

The Nurse microservice integrates with Module 3 (Status Tracking) through event-driven architecture to ensure attendance and employee status are consistently updated across the system.

```mermaid
sequenceDiagram
    participant N as Nurse Module
    participant SB as Azure Service Bus
    participant ST as Status Tracking Module
    participant TKS as Time Keeping System

    N->>N: Create/Update Health Record
    N->>SB: Publish StatusUpdateEvent
    SB->>ST: Consume StatusUpdateEvent
    ST->>ST: Update Employee Status
    ST->>SB: Publish StatusUpdatedEvent
    SB->>TKS: Consume StatusUpdatedEvent
    TKS->>TKS: Update Attendance Record
```

**Status Update Strategy:**

1. **Status Mapping**: Each record type and status in the Nurse module maps to a specific status code in the Status Tracking module:

   - Illness Record (active) → "MA" (Maladie)
   - Work Accident (active) → "AT" (Accident de Travail)
   - Pregnancy (active) → "Special Status" with color tag "Pink"
   - Work Capability (unable) → "Restricted Duty"

2. **Conflict Resolution**:

   - Priority-based resolution (Work Accident > Illness > Pregnancy > Work Capability)
   - Timestamp-based resolution for same-priority conflicts (most recent update wins)
   - Manual override capabilities for administrative purposes

3. **Synchronization Mechanism**:
   - Real-time updates via events for critical status changes
   - Periodic reconciliation process to catch missed events and ensure consistency
   - Daily validation job to verify status consistency across systems

**Status Update Event Schema:**

```typescript
interface StatusUpdateEvent {
  id: string;
  employeeId: string;
  source: "nurse-module";
  statusCode: string;
  startDate: string;
  endDate: string;
  priority: number;
  metadata: {
    recordType: string;
    recordId: string;
    comments?: string;
  };
}
```

#### Integration with Transport Module

The Nurse microservice integrates with the Transport Module to track and manage employees who use company transportation for medical purposes, specifically those coming to deposit medical certificates at medical centers.

```mermaid
sequenceDiagram
    participant N as Nurse Module
    participant SB as Azure Service Bus
    participant TM as Transport Module
    participant M as Medical Center

    N->>N: Record certificate pending deposit
    N->>SB: Publish MedicalCertificateDepositEvent
    SB->>TM: Consume MedicalCertificateDepositEvent
    TM->>TM: Add employee to bus manifest
    M->>N: Confirm certificate receipt
    N->>SB: Publish CertificateReceivedEvent
    SB->>TM: Consume CertificateReceivedEvent
    TM->>TM: Remove employee from pending list
```

**Key Features:**

1. **Site-Based Filtering**: The Transport Module lists employees filtered by siteID, showing only those who:

   - Are currently using company transportation
   - Are traveling for medical certificate deposit purposes
   - Are assigned to the specific site/location

2. **Tracking Process**:

   - When a new illness period is registered requiring certificate deposit, a tracking code is generated
   - The Nurse module sends an event to the Transport Module with employee details, tracking code, and required arrival time
   - The Transport Module adds the employee to the appropriate bus manifest
   - Upon certificate receipt at the medical center, the status is updated across both systems

3. **Data Exchange**: The Nurse module shares minimal required information:

   ```typescript
   interface TransportRequest {
     employeeId: string;
     employeeName: string;
     siteId: string;
     trackingCode: string;
     purpose: "medical_certificate_deposit";
     scheduledArrival: string; // ISO datetime
     medicalCenterId: string;
     status: "pending" | "completed" | "cancelled";
   }
   ```

4. **Bus Terminal Integration**:
   - Medical staff at bus terminals can access a filtered list of expected certificate deposits
   - Staff can scan or enter tracking codes to confirm receipt
   - The interface shows employee ID, name, and tracking code for verification

This integration ensures efficient coordination between the medical team and the transportation system, particularly in regions where employees must physically deposit medical certificates at designated medical centers.

#### Team Leader/Shift Leader UI Integration

The Nurse microservice provides data to support the Team Leader and Shift Leader workspaces, where supervisors need visibility into their team members' medical statuses without exposing confidential medical details.

**Status Tag Visibility in UI:**

```mermaid
flowchart LR
    A[Nurse Module] --> B[API Layer]
    B --> C[Team Leader UI]
    B --> D[Shift Leader UI]
    C --> E[Employee List View with Status Tags]
    D --> E
```

**Key Features:**

1. **Status Tag Implementation**:

   - Status tags are implemented entirely in the frontend for optimal user experience
   - Backend provides standardized status codes and visibility flags without exposing sensitive details
   - Employee list cards display color-coded visual indicators based on medical status

2. **Tag Types and Visibility**:

   - Pregnancy Status: Pink tag indicating special accommodations may be needed
   - Work Capability Issues: Yellow or red tags indicating restrictions or inability to work
   - Illness/Work Accident: Status indicators showing absence type without medical details
   - Return-to-Work: Indicators for employees recently returned from medical leave

3. **Privacy Controls**:

   - Medical details are never exposed, only status types and restrictions
   - Work capability comments are only visible if the `visibleToTeamLeader` flag is set to true
   - System enforces role-based access control to sensitive information

4. **API Support**:

   ```typescript
   interface EmployeeStatusResponse {
     employeeId: string;
     statusIndicators: {
       hasActiveIllness: boolean;
       hasActiveWorkAccident: boolean;
       isPregnant: boolean;
       hasWorkRestrictions: boolean;
       returnedFromLeaveDate?: string;
     };
     workRestrictions?: string[]; // Only populated if visibleToTeamLeader is true
     specialConsiderations?: string[]; // Non-medical accommodations
   }
   ```

5. **Implementation Strategy**:
   - Status data is provided via dedicated API endpoints for team/shift leaders
   - Polling or websocket updates ensure real-time status visibility
   - Status changes trigger UI notifications for immediate leader awareness

This integration ensures team leaders have the necessary visibility for workforce planning and accommodation while preserving employee medical privacy. The status tags provide an at-a-glance view of team member availability and any special considerations, improving operational efficiency.

---

### 9. Workflow Orchestration

#### Illness Period Workflow

```mermaid
stateDiagram-v2
    [*] --> Initiated
    Initiated --> Submitted: Nurse submits form
    Submitted --> Active: Start date reached
    Active --> Extended: Extended by nurse
    Extended --> Active: Update status
    Active --> Closed: End date reached
    Closed --> [*]
```

**Illness Period Sequence:**

```mermaid
sequenceDiagram
    actor Nurse
    participant API as API Layer
    participant Service as Service Layer
    participant Repo as Repository
    participant Bus as Service Bus
    participant Notify as Notification Service

    Nurse->>API: Submit Illness Period
    API->>Service: Create Illness Record
    Service->>Repo: Save Illness Record
    Service->>Bus: Publish IllnessPeriodCreated Event
    Bus->>Notify: Send notifications
    Notify->>Nurse: Confirmation

    Note over Nurse,Notify: Based on regional settings
```

#### Work Accident Workflow

```mermaid
stateDiagram-v2
    [*] --> Reported
    Reported --> Active: Validated
    Active --> Extended: Extended by nurse
    Extended --> Active: Update records
    Active --> Closed: End date reached
    Active --> InvestigationRequired: Flagged for investigation
    InvestigationRequired --> Closed: Investigation complete
    Closed --> [*]
```

#### Pregnancy Announcement Workflow

```mermaid
stateDiagram-v2
    [*] --> Announced
    Announced --> Active: Certificate validated
    Active --> Terminated: Terminated by nurse
    Active --> Completed: Maternity leave started
    Completed --> [*]
    Terminated --> [*]
```

#### Work Capability Assessment Workflow

```mermaid
stateDiagram-v2
    [*] --> Assessed
    Assessed --> Able: No restrictions
    Assessed --> Unable: Cannot work
    Assessed --> Restricted: Limited capability
    Able --> Reassessed: New assessment
    Unable --> Reassessed: New assessment
    Restricted --> Reassessed: New assessment
    Reassessed --> Able
    Reassessed --> Unable
    Reassessed --> Restricted
```

---

### 10. Regional Variation Implementation

#### Decision Process

The Nurse microservice uses a configuration-driven approach to handle regional variations in health management processes:

```mermaid
flowchart TD
    A[Employee Record] --> B{Determine Region}
    B --> C[Load Regional Settings]
    C --> D{Process Type?}
    D -->|Illness| E{Illness Process Enabled?}
    E -->|Yes| F[Apply Region-Specific Process]
    E -->|No| G[Skip Process]
    D -->|Work Accident| H[Apply Region-Specific Process]
    D -->|Pregnancy| I[Apply Region-Specific Process]
    D -->|Work Capability| J[Apply Region-Specific Process]
    F --> K[Execute Workflow]
    H --> K
    I --> K
    J --> K
```

#### Configuration-Driven Workflows

1. **Runtime Workflow Selection**:

   - System loads appropriate workflow based on employee's region
   - Dynamically adapts UI, required fields, and process steps

2. **Field Configuration**:

   - Required fields are dynamically determined from RegionalSettings
   - UI elements appear/disappear based on regional requirements

3. **Integration Points**:
   - Integration with national systems (e.g., Poland's national social insurance)
   - Integration with region-specific document management systems

**Example Region-Specific Logic:**

```typescript
async createIllnessPeriod(dto: CreateIllnessPeriodDto) {
  // Get employee data
  const employee = await this.employeeRepository.findById(dto.employeeId);

  // Load regional settings
  const regionalSettings = await this.settingsRepository.findByRegion(employee.region);

  // Check if process is enabled for this region
  if (!regionalSettings.sicknessAbsenteeismEnabled) {
    throw new RegionalProcessNotEnabledError();
  }

  // Apply regional workflow
  const workflow = this.workflowFactory.createIllnessWorkflow(
    regionalSettings.processingFlow.illnessSubmission
  );

  // Execute workflow with regional variations
  return workflow.execute(dto, regionalSettings);
}
```

---

### 11. Conclusion

The Nurse microservice provides a comprehensive solution for managing employee health statuses across multiple regions, integrating with key administrative systems to streamline communication and support workforce planning. The system's flexible configuration-driven workflows and event-driven architecture ensure regional compliance and efficient data handling while maintaining employee privacy.
