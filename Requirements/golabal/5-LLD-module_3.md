# Connected Workers Platform - Module 3 Microservices Architecture

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture Overview](#2-architecture-overview)
3. [Core Components](#3-core-components)
   - [Annual Calendar Service](#31-annual-calendar-service)
   - [Working Plan Service](#41-working-plan-service)
   - [HeadCount Service](#42-headcount-service)
4. [Integration Patterns](#4-integration-patterns)
5. [Conclusion](#5-conclusion)

## 1. Overview

The Connected Workers Platform Module 3 implements a microservices architecture to handle workforce planning, scheduling, and tracking. Each microservice is designed with specific responsibilities and communicates with others through well-defined interfaces using Azure Service Bus and RESTful APIs.

### Key Features

- Annual calendar management with holiday tracking
- Working plan and shift management
- Real-time operator status tracking and management
- Multi-tenant support with country-specific configurations
- Event-driven architecture with CQRS patterns

### Business Benefits

- Improved workforce planning efficiency
- Better tracking of operator attendance and status
- Enhanced integration between planning and execution
- Robust error handling and monitoring
- Flexible configuration for different regions

## 2. Architecture Overview

```mermaid
graph TD
    subgraph "Client Applications"
        UI[Web UI]
        Mobile[Mobile Apps]
    end

    subgraph "API Layer"
        APIM[API Management]
    end

    subgraph "Core Services"
        ACS[Annual Calendar Service]
        WPS[Working Plan Service]
        HCS[HeadCount Service]
        SB[Service Bus]
    end

    subgraph "Storage Layer"
        CDB[(Cosmos DB)]
        SQLDB[(SQL Database)]
        Redis[(Redis Cache)]
    end

    UI --> APIM
    Mobile --> APIM
    APIM --> ACS
    APIM --> WPS
    APIM --> HCS
    
    ACS --> SB
    WPS --> SB
    HCS --> SB
    
    ACS --> SQLDB
    ACS --> CDB
    WPS --> CDB
    HCS --> CDB
    
    ACS --> Redis
    WPS --> Redis
    HCS --> Redis
```

## Technology Stack

### Message Queues
- Azure Service Bus for asynchronous communication
- Event-driven integration between services
- Support for pub/sub patterns and message sessions
- Dead-letter queue handling and retry policies

### Databases
- Cosmos DB for event sourcing and document storage
  - Multi-tenant support
  - Document-based storage
  - Change Feed for event processing
  - Optimized partition strategies
- SQL Database for relational data and reporting
  - Structured calendar data
  - Complex querying capabilities
  - Maintaining read models
- Redis Cache for performance optimization
  - Caching frequently accessed data
  - Distributed caching support

### RESTful APIs
- Azure API Management for API gateway
- Standardized error handling
- Request/response validation
- Rate limiting and throttling

### API Gateway with Authentication
- Azure AD integration
- JWT token validation
- Role-based access control
- Multi-tenant security

## 3. Core Components

### 3.1 Annual Calendar Service

#### 3.1.1 Overview

The Annual Calendar service manages holiday schedules, overtime events, and working day statistics across different regions. The design uses an event-sourcing approach with Cosmos DB as the primary data store and SQL read replicas for efficient querying.

#### 3.1.2 Key Components

- Calendar API
- Excel Import Service
- Change Feed Processor
- SQL Read Database

#### 3.1.3 Architecture Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Calendar API    │────▶│ Cosmos DB       │────▶│ Change Feed     │
│                 │     │ (Event Store)   │     │ Processor       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                                                │
        │                                                ▼
┌───────┴───────┐       ┌─────────────────┐     ┌───────┴───────┐
│ Excel Import  │       │ Production Plan │     │ Calendar SQL  │
│ Service       │       │ SQL Database    │◀────│ Database      │
└───────────────┘       └─────────────────┘     └───────────────┘
```

#### 3.1.4 Data Models

### CalendarEvents (Cosmos DB)
```json
{
  "id": "holiday-2025-12-25-DE-NUREMBERG",
  "type": "HOLIDAY",
  "eventDate": "2025-12-25",
  "name": "Christmas Day",
  "description": "National holiday",
  "category": "NATIONAL",
  "color": "#FF0000",
  "isWorkingDay": false,
  "isRecurring": true,
  "recurringType": "YEARLY",
  "recurringMonth": 12,
  "recurringDay": 25,
  "hours": null,
  "department": null,
  "country": "DE",
  "site": "NUREMBERG",
  "createdBy": "john.doe",
  "createdAt": "2024-11-15T10:30:00Z",
  "updatedAt": "2024-11-15T10:30:00Z"
}
```

### YearSummary (Cosmos DB)
```json
{
  "id": "2025-DE-NUREMBERG",
  "year": 2025,
  "country": "DE",
  "site": "NUREMBERG",
  "status": "ACTIVE",
  "totalDays": 365,
  "sundays": 52,
  "saturdays": 52,
  "nationalHolidays": 9,
  "religiousHolidays": 4,
  "unpaidPublicHolidays": 3,
  "vacationDays": 18,
  "tlodays": 0,
  "fourthSaturdays": 12,
  "totalPlantHolidays": 0,
  "workedDaysPlant": 268,
  "lastCalculated": "2024-11-15T10:30:00Z",
  "createdBy": "john.doe",
  "createdAt": "2024-11-01T08:00:00Z",
  "updatedAt": "2024-11-15T10:30:00Z",
  "monthSummaries": [
    {
      "month": 1,
      "name": "January",
      "totalDays": 31,
      "sundays": 4,
      "saturdays": 4,
      "nationalHolidays": 1,
      "religiousHolidays": 0,
      "unpaidPublicHolidays": 0,
      "vacationDays": 0,
      "tlodays": 0,
      "fourthSaturdays": 1,
      "totalPlantHolidays": 0,
      "workedDaysPlant": 24
    }
    // Additional months...
  ]
}
```

### SQL Read Model
```sql
CREATE TABLE CalendarEvents (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Type VARCHAR(20) NOT NULL,
    EventDate DATE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(200) NULL,
    Category VARCHAR(30) NOT NULL,
    Color VARCHAR(20) NOT NULL,
    IsWorkingDay BIT NOT NULL,
    IsRecurring BIT NOT NULL,
    RecurringType VARCHAR(20) NULL,
    RecurringMonth TINYINT NULL,
    RecurringDay TINYINT NULL,
    Hours DECIMAL(5,2) NULL,
    Department VARCHAR(100) NULL,
    Country VARCHAR(2) NOT NULL,
    Site VARCHAR(50) NOT NULL,
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    EventId VARCHAR(36) NOT NULL,
    
    INDEX idx_event_date (EventDate),
    INDEX idx_country_site (Country, Site)
);
```

#### 3.1.5 Event Types

| Event Type | Description | Key Properties |
|------------|-------------|----------------|
| `HolidayCreated` | New holiday added | id, date, name, category, country, site |
| `HolidayUpdated` | Holiday modified | id, date, name, category, country, site |
| `HolidayDeleted` | Holiday removed | id |
| `OvertimeCreated` | New overtime added | id, date, hours, department, country, site |
| `YearInitialized` | Year configuration created | year, country, site, status |
| `YearStatusChanged` | Year status updated | year, country, site, oldStatus, newStatus |
| `YearSummaryCalculated` | Summary statistics updated | year, country, site, summaryData |
| `CalendarImported` | Bulk import completed | source, fileName, importedEvents |

#### 3.1.6 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/calendar/years/{year}/countries/{country}/sites/{site}` | GET | Get year summary data |
| `/api/calendar/years/{year}/countries/{country}/sites/{site}` | POST | Initialize or update year |
| `/api/calendar/years/{year}/countries/{country}/sites/{site}/status` | PUT | Update year status |
| `/api/calendar/events` | GET | Get calendar events |
| `/api/calendar/events/holidays` | POST | Create holiday |
| `/api/calendar/events/holidays/{id}` | PUT | Update holiday |
| `/api/calendar/events/holidays/{id}` | DELETE | Delete holiday |
| `/api/calendar/events/overtime` | POST | Create overtime |
| `/api/calendar/import` | POST | Import calendar from Excel |

#### 3.1.7 Excel Import Feature

The microservice supports bulk import of calendar data through Excel files. The system validates the data, creates appropriate events, and updates the year summary automatically.

### Import Template
The Excel template includes these columns:
- Date
- Name
- Category
- Is Working Day
- Country
- Site
- Color
- Description

### Import Process Flow
1. User uploads Excel file
2. System validates file format and content
3. Valid entries are converted to events and stored
4. Year summary is recalculated
5. Import result with success/failure details is returned

#### 3.1.8 Performance Optimizations

- Cosmos DB indexing for common query patterns
- Caching for frequently accessed year summaries
- Batched processing in Change Feed
- Optimized bulk import with background processing
- Pre-calculation of summary statistics

## 4. Core Components

### 4.1 Working Plan Service

#### 4.1.1 Overview

The Working Plan service manages weekly work plans, shift assignments, and operator scheduling across different manufacturing sites. It implements an optimized Sourced CQRS (SCQRS) pattern with advanced event-driven architecture to improve scalability, auditability, and performance.

#### 4.1.2 Key Components

- Command API
- Event Store
- Specialized Read Models
- Integration Framework
- Notification Service

#### 4.1.3 Architecture Enhancements

- **Multi-tenant Support**: Added country and region support for global deployment
- **Advanced Domain Modeling**: Refined entities with clear boundaries and relationships
- **Enhanced Event Sourcing**: Optimized event store with efficient partition keys
- **Improved Projections**: Specialized read models for different query patterns
- **Integration Framework**: Connectors for HR systems, production planning, and transport planning
- **Real-time Notifications**: Event-driven notification system for stakeholders

#### 4.1.4 External Data Models (provided by data team)

The Working Plan Service consumes the following data from external sources:

### 1. Operator Data
**Source**: Crew Management Module (from aptiv_active_employee)
**Purpose**: Provides operator identity and core attributes for shift assignment
**Update Frequency**: Daily with real-time updates
**Storage**: Cosmos DB (provided by data team)

```json
{
  "id": "OP123456",
  "type": "Operator",
  "partitionKey": "MAR_TEAM-123", 
  "badgeId": "B12345",
  "firstName": "John",
  "lastName": "Smith",
  "department": "Assembly",
  "category": "DH", 
  "role": "Operator",
  "emailAddress": "<EMAIL>",
  "isActive": true,
  "country": "MAR",
  "workdayId": "WD987654",
  "legacyId": "LEG12345",
  "site": "MAR Morocco 3",
  "lastUpdated": "2025-01-15T10:30:00Z",
  "skills": ["Welding", "Assembly"],
}
```

### 2. User Role Hierarchy
**Source**: Crew Management Module (from user_role_hierarchy)
**Purpose**: Provides organizational hierarchy for planning permissions and validations
**Storage**: Cosmos DB (provided by data team)
**Document Structure**:
```json
{
  "id": "E12345",
  "lecacy_site_id": "12132",
  "fullname": "John Smith",
  "department": "Assembly",
  "site": "MAR Morocco 3",
  "role": "shift leader",
  "manager_lecacy_site_id": "3244",
  "subordinate_crew": [
    {
      "sub_lecacy_site_id": "12132",
      "sub_fullname": "James Brown",
      "sub_role": "Team lead",
      "sub_role_status": "Active",
      "in_workday": "true",
      "category": "DH",
      "contract_type": "Permanent",
      "skills": ["Welding", "Assembly"]
    }
  ]
}
```

### 3. Teams Data
**Source**: Crew Management Module (new aggregation)
**Purpose**: Provides team definitions for shift planning
**Update Frequency**: Daily with intraday updates
**Storage**: Cosmos DB (provided by data team)
**Document Structure**:
```json
{
  "id": "TEAM-123",
  "team_name": "Door Assembly Team A",
  "project": "P12345",
  "family_zone": "FRONT DOOR 526",
  "line": "DOOR-ASSY-1",
  "activityType": "WP",
  "teamleader_id": "E12345",
  "teamleader_name": "John Smith",
  "site": "MAR Morocco 3",
  "country": "MAR",
  "department": "Assembly",
  "createdDate": "2024-03-01T08:00:00Z",
  "lastUpdated": "2024-05-15T14:30:00Z",
  "isActive": true
}
```

### 4. Team Leader Team Crew
**Source**: Crew Management Module (from teamleader_team_crew)
**Purpose**: Provides team structure under team leaders
**Update Frequency**: Daily with real-time updates
**Storage**: Cosmos DB (provided by data team)
**Document Structure**:
```json
{
  "teamleader_legacy_site": "E12345_MAR Morocco 3",
  "teamleader_name": "John Smith",
  "teams": "Door Assembly Team A",
  "department": "Assembly",
  "department_type": "Production",
  "operator_legacy_site": "E67890_MAR Morocco 3",
  "operator_fullname": "Jane Doe",
  "role": "Operator",
  "role_status": "Active",
  "site": "MAR Morocco 3"
}
```

#### 4.1.5 Data Models

### Shift Document
```typescript
interface ShiftDocument {
    id: string;                    // "SHIFT_FR_SITE1_2025_09_MON_M"
    type: "Shift";
    country: string;               // "FR", "DE", etc.
    region: string;                // "EMEA", "APAC", etc.
    site: string;                  // "SITE1"
    week: string;                  // "2025_09"
    date: string;                  // "2025-02-24"
    dayOfWeek: string;             // "Monday"
    shiftType: ShiftType;          // "Morning" | "Afternoon" | "Night"
    code: string;                  // "M", "A", "N"
    startTime: string;             // "06:00"
    endTime: string;               // "14:00"
    status: ShiftStatus;           // "Draft" | "Planned" | "Published" | "Completed"
    totalPlannedOperators: number; // Total operators across all teams
    teams: {
        project: string;               // Project identifier
        team_name: string;
        family_zone: string;       // "FRONT DOOR 526", etc.
        line: string;              // Production line
        activityType: string;      // "WP", "SA", "SA-S", "SA-P", "SA-B"
        teamleader_id: string;
        teamleader_name: string;
        totalOperators: number;    // Total operators for this team
        assignedOperators: {
            operator_id: string;
            operator_name: string;
            skills: string[];
            status: string;        // "Active", "TLO", "OT", etc.
        }[];
        unassignedOperators: {     // Operators not assigned to any team (TLO)
            operator_id: string;
            operator_name: string;
            skills: string[];
            previousTeam: string;  // Last team assignment before unassignment
            reasonCode: string;    // Why operator was unassigned
        }[];
    }[];
}
```

### Event Document
```typescript
interface EventDocument {
    id: string;                    // UUID
    type: "Event";
    eventType: EventType;          // Specific event type
    aggregateId: string;           // ID of the entity this event affects
    aggregateType: string;         // "Shift", "Team", "Operator"
    country: string;               // For partition key
    site: string;                  // For partition key
    week: string;                  // For weekly grouping
    timestamp: string;             // When the event occurred
    userId: string;                // Who triggered this event
    payload: any;                  // Event-specific data
}
```

#### 4.1.6 Cosmos DB Configuration

| Container Name | Purpose | Partition Key | Indexing Policy |
|----------------|---------|---------------|-----------------|
| `events` | Event store | `/country,/site` | Optimize for timestamp and aggregate queries |
| `shifts` | Shift read models | `/country,/site` | Optimize for date and week queries |
| `teamLeader_team_crew` | Team definitions | `customer` | Default |
| `operators` | Operator definitions | `/country,/site` | Optimize for skill queries |

#### 4.1.7 Sample Implementation Flows

### DH Category Planning Flow
```mermaid
sequenceDiagram
    actor SL as Shift Leader
    participant UI as Web UI
    participant API as Command API
    participant ES as Event Store
    participant RM as Read Models
    participant NS as Notification Service
    
    SL->>UI: Access calendar interface
    UI->>API: GET /weeks/{weekId}/calendar
    API->>RM: Get calendar data
    RM-->>API: Return calendar
    API-->>UI: Display calendar
    
    SL->>UI: Select week and shift
    
    SL->>UI: Assign team leaders to zones
    UI->>API: POST /shifts/{id}/teams
    API->>ES: Save TeamAssigned event
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    UI->>API: POST /shifts/{id}/teams/{teamId}/teamleader
    API->>ES: Save TeamleaderAssigned event
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    SL->>UI: Assign operators to teams
    UI->>API: POST /shifts/{id}/teams/{teamId}/operators
    API->>ES: Save OperatorAssigned events
    ES-->>API: Confirm
    API-->>UI: Update UI with team members
    
    SL->>UI: Complete planning
    UI->>API: POST /shifts/{id}/validate
    API->>ES: Save ShiftValidated event
    ES->>RM: Update read models
    API->>NS: Trigger notifications
    NS-->>SL: Send email notification
    NS-->>+Team Leaders: Send notifications
    API-->>UI: Show confirmation
```

#### 4.1.8 API Endpoints

### Command API

| Endpoint | Method | Description | Request Body Example |
|----------|--------|-------------|-----------------|
| `/api/v1/shifts` | POST | Create new shift | `{ "country": "FR", "site": "SITE1", "date": "2025-05-06", "shiftType": "Morning" }` |
| `/api/v1/shifts/{id}/teams` | POST | Add team to shift | `{ "team_id": "TEAM123", "team_name": "Assembly A", "family_zone": "FRONT DOOR 526", "line": "L1", "activityType": "WP" }` |
| `/api/v1/shifts/{id}/teams/{teamId}/teamleader` | POST | Set team leader | `{ "teamleader_id": "EMP789", "teamleader_name": "John Smith" }` |
| `/api/v1/shifts/{id}/teams/{teamId}/operators` | POST | Add operator | `{ "operator_id": "EMP456", "operator_name": "Jane Doe", "skills": ["cutting", "assembly"] }` |
| `/api/v1/shifts/{id}/operators/tlo` | POST | Move operator to TLO | `{ "operator_id": "EMP456", "previousTeam": "TEAM123", "reasonCode": "SKILL_MISMATCH" }` |
| `/api/v1/shifts/{id}/validate` | POST | Validate shift plan | `{ "validateAllocation": true }` |
| `/api/v1/shifts/{id}/emergency-update` | POST | Emergency update | `{ "reason": "Production line change", "approvedBy": "SUPERVISOR1", "changes": [{"field": "team.line", "value": "L3"}] }` |

### Query API

| Endpoint | Method | Description | Query Parameters |
|----------|--------|-------------|-----------------|
| `/api/v1/sites/{site}/shifts` | GET | Get shifts for site | `country`, `week?`, `date?`, `category?` |
| `/api/v1/shifts/{id}` | GET | Get shift details | N/A |
| `/api/v1/sites/{site}/calendar/{week}` | GET | Get calendar view | `country`, `category?` |
| `/api/v1/teams` | GET | Get teams | `country`, `site`, `family?` |
| `/api/v1/operators` | GET | Get operators | `country`, `site`, `status?`, `skill?` |
| `/api/v1/shifts/{id}/unassigned` | GET | Get TLO operators | N/A |
| `/api/v1/reports/utilization` | GET | Get utilization report | `country`, `site`, `week`, `department?` |

#### 4.1.9 Integration Points

1. HR System Integration
2. Production Planning Integration
3. Transport Planning Integration

#### 4.1.10 Business Rules and Validations

### Planning Timeline Rules

| Rule ID | Rule Description | Validation Logic |
|---------|------------------|-----------------|
| TR-01 | Initial planning deadline: Friday 5 PM | `if (currentTime < fridayDeadline && shiftWeek == nextWeek) allow else deny` |
| TR-02 | Weekend planning deadline: Friday 5 PM | `if (currentTime < fridayDeadline && isWeekendShift && shiftWeek == nextWeek) allow else deny` |
| TR-03 | J-1 modification: before 5 PM | `if (currentTime < dailyDeadline && shiftDate == tomorrow) allow else deny` |
| TR-04 | Evening shift same-day mod: 4h before | `if (currentTime < (shiftStart - 4h) && shiftType == "Evening" && shiftDate == today) allow else deny` |
| TR-05 | Night shift same-day mod: before 5 PM | `if (currentTime < dailyDeadline && shiftType == "Night" && shiftDate == today) allow else deny` |
| TR-06 | Overtime addition: before 11 AM | `if (currentTime < overtimeDeadline && shiftDate == today) allow else deny` |

### Validation Rules

| Rule ID | Rule Description | Validation Logic |
|---------|------------------|-----------------|
| VR-01 | Operator eligibility check | `if (operator.status not in ["SICK_LEAVE", "DISCIPLINARY"]) allow else deny` |
| VR-02 | Transport planning auth | `if (user.role == "TRANSPORT_RESPONSIBLE") allow else deny` |
| VR-03 | Team skill requirements | `if (team.requiredSkills.every(skill => team.operators.some(op => op.skills.includes(skill)))) allow else deny` |
| VR-04 | Special activity authorization | `if (activityType.startsWith("SA") && user.hasPermission("SPECIAL_ACTIVITY")) allow else deny` |
| VR-05 | Min/max staffing levels | `if (team.operatorCount >= team.minOperators && team.operatorCount <= team.maxOperators) allow else deny` |
| VR-06 | No overlapping assignments | `if (!hasOperatorOverlappingShift(operator, shift)) allow else deny` |

### 4.2 HeadCount Service

#### 4.2.1 Overview

The HeadCount service handles real-time operator tracking and status management. It implements a multi-tenant architecture with comprehensive event processing and support for various employee categories, shift patterns, and country-specific workflows.

#### 4.2.2 Key Components

- Command and Query APIs
- Event-driven status management
- Workflow approval engine
- Integration with Working Plan
- Multi-tenant configuration

#### 4.2.3 External Data Models (provided by data team)

The HeadCount Service consumes the following data from external sources:

### 1. Operator Data
**Source**: Crew Management Module (from aptiv_active_employee)
**Purpose**: Provides operator identity and core attributes for attendance tracking
**Update Frequency**: Daily with real-time updates
**Storage**: Cosmos DB (provided by data team)

```json
{
  "id": "OP123456",
  "type": "Operator",
  "partitionKey": "MAR_TEAM-123", 
  "badgeId": "B12345",
  "firstName": "John",
  "lastName": "Smith",
  "department": "Assembly",
  "category": "DH", 
  "role": "Operator",
  "emailAddress": "<EMAIL>",
  "isActive": true,
  "country": "MAR",
  "workdayId": "WD987654",
  "legacyId": "LEG12345",
  "site": "MAR Morocco 3",
  "lastUpdated": "2025-01-15T10:30:00Z",
  "skills": ["Welding", "Assembly"],
}
```

### 2. Teams Data
**Source**: Crew Management Module (new aggregation)
**Purpose**: Provides team definitions for attendance tracking and reporting
**Update Frequency**: Daily with intraday updates
**Storage**: Cosmos DB (provided by data team)
**Document Structure**:
```json
{
  "id": "TEAM-123",
  "team_name": "Door Assembly Team A",
  "project": "P12345",
  "family_zone": "FRONT DOOR 526",
  "line": "DOOR-ASSY-1",
  "activityType": "WP",
  "teamleader_id": "E12345",
  "teamleader_name": "John Smith",
  "site": "MAR Morocco 3",
  "country": "MAR",
  "department": "Assembly",
  "createdDate": "2024-03-01T08:00:00Z",
  "lastUpdated": "2024-05-15T14:30:00Z",
  "isActive": true
}
```

### 3. Real-Time Transport Data
**Source**: Transport Management System
**Purpose**: Provides bus transporation schedules and operator assignments
**Update Frequency**: Real-time
**Storage**: Cosmos DB (provided by data team)
**Document Structure**:
```json
{
  "id": "TRANS-20250301-ROUTE123",
  "type": "TransportRoute",
  "date": "2025-03-01",
  "route": "ROUTE123",
  "routeName": "City Center - Plant",
  "busId": "BUS456",
  "driverId": "DRIVER789",
  "departureTime": "05:30:00",
  "arrivalTime": "06:15:00",
  "scheduledOperators": [
    {
      "operatorId": "OP123",
      "operatorName": "Jane Smith",
      "badgeId": "B12345",
      "department": "Assembly",
      "teamId": "TEAM-123",
      "pickupLocation": "Stop A",
      "pickupTime": "05:35:00",
      "status": "SCHEDULED"
    },
    {
      "operatorId": "OP124",
      "operatorName": "John Doe",
      "badgeId": "B12346",
      "department": "Assembly",
      "teamId": "TEAM-124",
      "pickupLocation": "Stop B",
      "pickupTime": "05:45:00",
      "status": "SCHEDULED"
    }
  ],
  "actualDepartureTime": null,
  "actualArrivalTime": null,
  "status": "SCHEDULED",
  "site": "MAR Morocco 3",
  "country": "MAR",
  "lastUpdated": "2025-02-28T18:00:00Z"
}
```

### 4. Carousel Badging Data
**Source**: Factory Access Control System
**Purpose**: Provides real-time badging data for operator attendance
**Update Frequency**: Real-time
**Storage**: SQL Database (provided by data team)
**Schema**:
```sql
CREATE TABLE CarouselBadgingData (
    id BIGINT PRIMARY KEY IDENTITY,
    badgeId VARCHAR(50) NOT NULL,
    operatorId VARCHAR(50) NOT NULL,
    operatorName VARCHAR(100) NOT NULL,
    badgeTimestamp DATETIME2 NOT NULL,
    terminalId VARCHAR(20) NOT NULL,
    terminalLocation VARCHAR(50) NOT NULL,
    badgeDirection VARCHAR(10) NOT NULL, -- IN, OUT
    shiftCode VARCHAR(5) NULL,
    scheduledShiftStart DATETIME2 NULL,
    scheduledShiftEnd DATETIME2 NULL,
    lateMinutes INT NULL,
    site VARCHAR(50) NOT NULL,
    country VARCHAR(2) NOT NULL,
    
    INDEX idx_badge_timestamp (badgeId, badgeTimestamp),
    INDEX idx_operator_date (operatorId, CAST(badgeTimestamp AS DATE)),
    INDEX idx_terminal (terminalId, badgeTimestamp)
);
```

#### 4.2.4 Data Model

### Primary Entities

```typescript
interface Operator {
    id: string;                  
    partitionKey: string;        // Format: "country_teamId"
    badgeId: string;
    firstName: string;
    lastName: string;
    teamId: string;
    teamLeaderId: string;        
    department: string;
    category: string;            // "DH", "IS", "IH"
    isActive: boolean;
    country: string;             
}

interface LongTermStatus {
    id: string;
    partitionKey: string;        // Format: "country_operatorId"
    operatorId: string;
    type: LongTermStatusType;
    startDate: date;
    endDate: date;
    reason: string;
    approvedBy: string;
    approvalRequestId: string;   
    createdAt: datetime;
    updatedAt: datetime;
    metadata: object;
    country: string;             
}

interface ShiftDocument {
    id: string;
    partitionKey: string;        // Format: "country_date" 
    date: date;
    shiftCode: string;           // "M" (Morning), "E" (Evening), "N" (Night)
    departmentId: string;
    teamId: string;
    workingPlanId: string;       // Reference to source working plan
    operators: [
        {
            operatorId: string;
            fullName: string;     // Added operator's full name for easier UI display
            status: ShiftStatusType;  // Actual status for the shift
            plannedStatus: string;    // Pre-assigned status from working plan
            isTLO: boolean;       // Technical layoff indicator
            isContainment: boolean;
            recordedBy: string;   // Who recorded this status
            recordedAt: datetime; // When the status was recorded
        }
    ];
    shiftStarted: boolean;
    shiftStartTime: datetime;
    shiftClosed: boolean;
    shiftCloseTime: datetime;
    validated: boolean;
    validatedBy: string;
    validatedAt: datetime;
    country: string;
    updatedAt: datetime;
}
```

#### 4.2.5 Status Types and Workflows

| Status Code | Status Name | Type | Description | Triggering Microservice/Process | Applicable Categories |
|-------------|-------------|------|-------------|--------------------------------|----------------------|
| P | Present | Shift | Operator is present for shift | HeadCount | All |
| A | Absent | Shift | Operator is absent without justification | HeadCount | All |
| R | Delayed | Shift | Operator is late with approved entry authorization | Request | DH, IH, IS |
| T | Temporary Authorization | Operator | Temporary status pending evidence | Request | All |
| TL | Legal Authorization | Operator | Legal authorization (e.g., Breastfeeding) | Request | DH |
| CR | Leave Holiday | Operator | Planned vacation/leave | Request | All |
| CTP | Planned TLO | Operator | Planned Technical Layoff | Working Plan | DH, IS, IH |
| CTN | Unplanned TLO | Operator | Unplanned Technical Layoff | Request | All |

## 5. Visual Check Responsibility Matrix

| Employee Category | Visual Check Responsibility | Validation Path | Timing |
|-------------------|----------------------------|----------------|--------|
| DH Operators | Team Leader | TL → SL → TKS | During shift |
| IS/IH Employees | Department Clerk | Clerk → Department Manager → TKS | D+1 |
| Team Leaders | Shift Leader | SL → TKS | During shift |
| Shift Leader Backup Structure | Shift Leader | SL → TKS | During shift |
| Backup Structure in Replacement Process | Team Leader | TL → SL → TKS | During shift |
| Team Leader Backups (not in replacement) | Shift Leader | SL → TKS | During shift |
| Shift Leader Backups (not in replacement) | Shift Leader | SL → TKS | During shift |
| Containment Operators | Team Leader + Quality Supervisor | TL → SL → TKS | During shift |

## 6. Time-Based Rules and Constraints

| Rule Type | Description | Timing | Action on Violation |
|-----------|-------------|--------|---------------------|
| Status Modification Window | Team Leaders can modify operator status | Until D+1 at 7:00 AM for night shift | Submit Correction of Clocking request |
| Shift Start Alert | Alert if Team Leader doesn't click "Start Shift" | Before end of shift | Notification to Shift Leader |
| Dashboard Update | Show Team Leaders who clicked/didn't click Start/Close Shift | Real-time | Visible to Shift Leader |
| Prolonged Absence | Alert for absence without justification | After 4 consecutive days | Notification to HR (potential job abandonment) |
| Status Closure | Team Leader must close all operator statuses | End of shift | Popup warning if statuses left empty |

## 7. CQRS Architecture Components

## 7.1 Core Components

#### 7.1.1 Command Side
- **Commands**: 
  - `StartShiftCommand` - Initiated by Team Leader
  - `RecordVisualCheckCommand` - Used for visual checks by TL/Clerk/SL
  - `CloseShiftCommand` - Initiated by Team Leader at end of shift
  - `RequestStatusChangeCommand` - For all request-based status changes
  - `ValidateShiftRecordsCommand` - For SL/Department Manager validation
  - `CorrectClockingCommand` - For corrections after deadline
  - `ScheduleStatusChangeCommand` - For future-dated status changes

- **Command Handlers**: 
  - Process business logic and validation rules
  - Apply category-specific approval workflows
  - Generate appropriate events

- **Domain Models**:
  - `Operator` - Core entity for DH category employees
  - `Employee` - Core entity for IS/IH category employees
  - `Team` - Group of operators under a Team Leader
  - `Shift` - Represents a work shift (Morning, Evening, Night)
  - `ShiftDocument` - Represents shift plan imported from Working Plan system

#### 7.1.2 Event Store Containers

1. **StatusChangeEvents**
   - PartitionKey: OperatorId/EmployeeId
   - Properties:
     - EventId (GUID)
     - EventType (string) - "ShiftStarted", "VisualCheckRecorded", "StatusChangeRequested", etc.
     - OperatorId/EmployeeId (string)
     - Category (string) - "DH", "IS", "IH"
     - OldStatus (string)
     - NewStatus (string)
     - StatusType (string) - "Shift" or "Operator"
     - EffectiveDate (datetime)
     - ExpiryDate (datetime, nullable)
     - Reason (string)
     - RequestId (string, nullable)
     - RequestType (string, nullable) - "REQ01", "REQ02", etc.
     - SourceMicroservice (string)
     - Timestamp (datetime)
     - Actor (string) - who initiated the change
     - ActorRole (string) - "TeamLeader", "ShiftLeader", "Clerk", "Nurse", etc.
     - ApprovalState (string) - "Initiated", "Approved", "Forwarded", "Completed"
     - ApprovalStep (int)
     - NextApprover (string, nullable)

2. **ScheduledStatusEvents**
   - PartitionKey: EffectiveDate
   - Properties:
     - EventId (GUID)
     - OperatorId/EmployeeId (string)
     - Category (string) - "DH", "IS", "IH"
     - StatusCode (string)
     - EffectiveDate (datetime)
     - ExpiryDate (datetime, nullable)
     - Reason (string)
     - RequestId (string, nullable)
     - RequestType (string, nullable)
     - CreatedBy (string)
     - CreatedTimestamp (datetime)
     - ApprovalState (string)
     - ApprovalWorkflow (string) - JSON representation of approval steps

3. **ApprovalEvents (triggered b the workflow microservice)**
   - PartitionKey: RequestId
   - Properties:
     - EventId (GUID)
     - RequestId (string)
     - ApprovalStep (int)
     - ApproverRole (string)
     - ApproverId (string)
     - Decision (string) - "Approved", "Rejected"
     - Comments (string)
     - Timestamp (datetime)

4. **ShiftDocumentEvents**
   - PartitionKey: ShiftDate
   - Properties:
     - EventId (GUID)
     - EventType (string) - "ShiftDocumentImported", "ShiftDocumentUpdated"
     - ShiftDocumentId (string)
     - ShiftDate (date)
     - ShiftCode (string)
     - DepartmentId (string)
     - TeamId (string)
     - WorkingPlanId (string)
     - OperatorStatusUpdates (array) - Operator status changes
     - ImportedBy (string)
     - ImportTimestamp (datetime)
     - Version (int)

#### 7.1.3 Read Model Containers

1. **OperatorStatus**
   - PartitionKey: DepartmentId
   - Properties:
     - OperatorId/EmployeeId (string)
     - Name (string)
     - Category (string) - "DH", "IS", "IH"
     - DepartmentId (string)
     - TeamId (string, nullable)
     - TeamLeaderId (string, nullable)
     - ShiftLeaderId (string, nullable)
     - CurrentShiftStatus (string) - "Present", "Absent", "Delayed"
     - CurrentOperatorStatus (string) - Extended status code
     - StatusType (string) - "Shift" or "Operator"
     - EffectiveFrom (datetime)
     - EffectiveTo (datetime, nullable)
     - LastUpdateTimestamp (datetime)
     - SourceEventId (string)
     - RequestId (string, nullable)
     - IsContainment (boolean)
     - ResponsibleForVisualCheck (string) - PersonId of responsible checker
     - StatusModifiable (boolean) - Based on time window rules

2. **ShiftSession**
   - PartitionKey: ShiftDate
   - Properties:
     - SessionId (string)
     - TeamId (string)
     - TeamLeaderId (string)
     - ShiftDate (date)
     - ShiftType (string) - "Morning", "Evening", "Night"
     - SessionStarted (boolean)
     - SessionStartTime (datetime, nullable)
     - SessionClosed (boolean)
     - SessionCloseTime (datetime, nullable)
     - TotalOperators (int)
     - StatusBreakdown (JSON object) - Counts of each status
     - ValidationStatus (string) - "Pending", "Validated", "Forwarded"
     - ValidatedBy (string, nullable)
     - ValidatedTimestamp (datetime, nullable)

7. **ShiftDocumentView**
   - PartitionKey: ShiftDate
   - Properties:
     - DocumentId (string)
     - ShiftDate (date)
     - ShiftCode (string)
     - DepartmentId (string)
     - TeamId (string)
     - WorkingPlanId (string)
     - ExpectedOperators (array)
     - StatusBreakdown (JSON object)
     - ImportedAt (datetime)
     - LastUpdatedAt (datetime)
     - IsActive (boolean)
     - Version (int)

### Command Side
- `StartShiftCommand` - Initiated by Team Leader
- `RecordVisualCheckCommand` - Used for visual checks
- `CloseShiftCommand` - Initiated by Team Leader at end of shift
- `RequestStatusChangeCommand` - For request-based status changes
- `ValidateShiftRecordsCommand` - For validation processes

## 8. Core System Workflows

### Start Shift Process
1. Team Leader clicks "Start Shift" button
2. System creates events, initializes records
3. Pre-loads statuses from Working Plan
4. Notifies Shift Leader

### Visual Check Process
1. Team Leader/Department Clerk performs visual check
2. Status recorded for each operator
3. System creates events and updates statuses
4. Different validation paths based on employee category

### Status Change Request Workflow
1. Requester initiates status change request
2. System creates initial event
3. Request follows approval workflow based on:
   - Employee category
   - Time of day
   - Country-specific rules
4. Upon final approval, status updated and notifications sent

### 8.1 Headount Workflows

#### 8.1.1 Start Shift Process
1. Team Leader clicks "Start Shift" button
2. System:
   - Creates `ShiftStarted` event
   - Creates a new ShiftSession record
   - Initializes ShiftRecords for all operators in team
   - Pre-loads any effective operator statuses
   - Imports ShiftDocument from Working Plan if available
   - Updates ShiftLeaderDashboard
3. If Team Leader doesn't click "Start Shift":
   - System automatically checks 1 hour after shift start time
   - Sends alert to Shift Leader
   - Adds notification to ShiftLeaderDashboard

#### 8.1.2 Visual Check Process
**For DH Category (Team Leader):**
1. Team Leader sees list of operators including:
   - Regular team members
   - Containment operators (grayed out)
   - Backup structure operators (if replacement process completed)
   - Pre-filled statuses from ShiftDocument if available
2. For each operator, Team Leader:
   - Verifies physical presence
   - Confirms or updates pre-filled status
   - System creates `VisualCheckRecorded` event
   - Updates ShiftRecords and OperatorStatus

**For IS/IH Category (Department Clerk):**
1. Department Clerk performs visual check on D+1
2. System follows same process but with different approval workflow
3. Final approval comes from Department Manager

**For Team Leaders (by Shift Leader):**
1. Shift Leader performs visual check for Team Leaders
2. Shift Leader also checks backup structure operators not in replacement process

#### 8.1.3 Close Shift Process
1. Team Leader clicks "Close My Shift"
2. System:
   - Validates all operators have status assigned
   - If incomplete, shows warning popup
   - Creates `ShiftClosed` event
   - Updates ShiftSession and ShiftLeaderDashboard
   - Sends notification to Shift Leader
3. Shift Leader validation process:
   - Reviews all team statuses
   - Can override/correct if needed
   - Approves with `ValidateShiftRecords` command
   - System creates `ShiftRecordsValidated` event
   - Sends notification to TKS
4. For IS/IH categories (Department Clerk):
   - Clerk closes on D+1
   - Sends notification to Department Manager
   - Department Manager validates
   - System forwards to TKS

## ShiftDocument Integration with Working Plan

1. **Automatic Import Process**:
   - Working Plan microservice publishes ShiftDocument event
   - ShiftImporter function triggered to process incoming document
   - Creates `ShiftDocumentImported` event
   - Updates ShiftDocumentView read model
   - Flags any pre-assigned statuses (e.g., TLO, planned absences)

2. **Manual Import Process**:
   - Shift Leader can manually trigger import via UI
   - Validates against current shift data
   - Resolves conflicts with current operator statuses
   - Creates `ShiftDocumentUpdated` event

3. **Status Pre-loading**:
   - During shift start, system consults ShiftDocument
   - Pre-fills operator statuses based on planned data
   - Team Leader confirms or overrides during visual check
   - Differences between planned and actual are recorded for reporting

4. **Planned TLO (CTP) Processing**:
   - Working Plan includes TLO assignments from Capacity Study
   - System extracts TLO-assigned operators
   - Pre-fills status as "CTP" for those operators
   - Team Leader confirms during visual check
   - Statistics on planned vs. actual TLO usage tracked

## Status Change Request Workflows

#### Absence Authorization (T → TL) Workflow
1. Team Leader/Requester initiates absence authorization request (REQ 01)
   ```json
   {
     "requestId": "REQ01-123",
     "requestType": "REQ01",
     "operatorId": "OP456",
     "statusCode": "T",
     "reason": "Medical Appointment",
     "proofType": "Medical Certificate",
     "effectiveFrom": "2025-05-01T08:00:00Z",
     "effectiveTo": "2025-05-01T16:00:00Z",
     "requester": "TL789"
   }
   ```
2. System:
   - Creates `StatusChangeRequested` event
   - Adds to approval workflow based on:
     - Employee category (DH/IS/IH)
     - Time (administrative vs. non-administrative hours)
   - Updates StatusRequests read model

3. Approval Process:
   - For DH categories during administrative hours:
     - Team Leader → Shift Leader → Admin Coordinator
   - For DH categories during non-administrative hours:
     - Team Leader → Shift Leader
   - For IH categories during administrative hours:
     - Requester → N+1 → Admin Coordinator
   - For IH categories during non-administrative hours:
     - Requester → N+1
   - For IS categories:
     - Follow approval matrix in Module 1

4. After final approval:
   - System creates `StatusApproved` event
   - Updates operator status to "TL"
   - Sends notification to TKS
   - TKS validates proof (scanned at kiosk)
   - Status change reflected in Optitime system

5. Breastfeeding-specific process:
   - Request with reason "BREASTFEEDING"
   - Declaration of honor scanned at kiosk
   - TKS approval
   - Status updated to "TL"
   - For IS/IH category, notification sent to N+1
   - Status updated in clerk interface

#### Leave Holiday (CR) Workflow
1. Team Leader initiates Leave Request (REQ 07)
2. System:
   - Creates `LeaveRequested` event
   - Follows approval workflow from FDS Module 1
   - For IH category, requester raises request directly
3. Upon approval:
   - Creates `LeaveApproved` event
   - Updates OperatorStatus
   - If future-dated, adds to ScheduledStatusEvents
   - Updates connected to Workday for balance verification (M-1)

####  Travel Orders (DI/DN) Workflow
1. Team Leader/Requester initiates Travel Order request (REQ 03)
2. System:
   - Creates `TravelOrderRequested` event
   - Follows approval workflow from SFD Module 1
3. Upon approval:
   - Creates `TravelOrderApproved` event
   - Updates status to "DI" (day) or "DN" (night)
   - Sends to TKS for validation

####  External Work (TE) Workflow
1. Team Leader/Requester initiates External Work request (REQ 02)
2. System:
   - Creates `ExternalWorkRequested` event
   - Follows approval workflow from SFD Module 1
3. Upon approval:
   - Creates `ExternalWorkApproved` event
   - Updates status to "TE"
   - Sends to TKS for validation

####  Unplanned TLO (CTN) Workflow
1. Team Leader initiates Unplanned TLO request
2. System:
   - Creates `UnplannedTLORequested` event
   - Captures reason (raw material shortage, technical issue)
3. Complex approval workflow:
   - Team Leader → Shift Leader → Coordinator → Manager
4. Upon approval:
   - Creates `UnplannedTLOApproved` event
   - Updates status to "CTN"
   - Notifies TKS agent for validation
   - Updates Optitime system

#### Workplace Accident (AT) Workflow
1. Nurse submits workplace accident request
   - Includes operator ID, start date, end date
   - Option to extend absence period
   - Scans medical proof
2. System:
   - Creates `WorkplaceAccidentReported` event
   - For DH: Notifies TL, SL, TKS, Social Services, Safety responsible
   - For IH/IS: Notifies N+1, Clerk, TKS, Social Services, Safety responsible
3. Status automatically updated to "AT"
4. Extensions can be initiated by nurse, TL, N+1, clerk, or HR

#### Maternity Leave (MT) Workflow
1. Team Leader submits request using "Annual Leave" template (REQ 07)
   - Specifies reason as "Maternity"
2. System:
   - Creates `MaternityLeaveRequested` event
   - For DH: Team Leader manages operators
   - For other employees: Department Clerk manages
3. Follows country-specific approval workflow
4. Upon approval:
   - Creates `MaternityLeaveApproved` event
   - TKS updates system with leave start date
   - Updates status to "MT"
   - Notifies relevant stakeholders

#### Suspension (AP) Workflow
1. Employee Relations Supervisor submits suspension request
2. System:
   - Creates `SuspensionRequested` event
   - Automatically notifies TL, TKS agent, and Shift Leader
3. Status automatically updated to "AP"
4. No further approval needed (pre-authorized)

#### Exceptional Leave (AE) Workflow
1. Team Leader submits exceptional leave request (e.g., COVID)
2. Approval workflow:
   - Shift Leader → HR Manager
3. System:
   - Creates `ExceptionalLeaveApproved` event upon final approval
   - Notifies TKS Agent
   - Updates status to "AE"

## Integration with Working Plan for Planned TLO (CTP)

1. Capacity Study process:
   - Working Plan microservice conducts Capacity Study
   - Develops Production Plan with TLO requirements
   - Shares work plan with Shift Leaders

2. TLO Assignment process:
   - Shift Leader assigns operators to TLOs for N+1 period
   - Assignments recorded in Working Plan system
   - Exported to HeadCount as ShiftDocument
   - For DH: Shift Leader manages operators
   - For IS/IH: Department clerk handles planning
   - Follows work plan update rules

3. Status Update process:
   - ShiftDocument imported at shift start
   - Operators with TLO assignments pre-marked as "CTP"
   - Team Leader can modify during shift if needed
   - Changes synchronized with Optitime system

## Special Case Workflows

#### Badge Loss/Forgetting Process
1. If badge forgotten/lost:
   - Operator manually enters ID at bus terminal
   - If not scheduled on bus, access denied
   - Automatic email sent to guardhouse
   - Operator reports to guardhouse on arrival

2. For badge loss:
   - Team Leader raises Declaration of Loss of Badge request (REQ 08)
   - Includes sworn statement from operator
   - Information sent to TKS Responsible

#### Delay Handling Process
1. If operator delayed due to transport/personal issue:
   - Status updated to "Absent" not "Delayed"
   - Updated status reflected in CW

2. "Delayed" status only applicable with approved entry authorization:
   - Triggered when operator exceeds 1 min for starting shift
   - Team Leader initiates "autorisation d'entrée" request
   - Once approved, guardhouse notified to grant access
   - Status updated to "R"

#### Prolonged Absence Alert Process
1. System monitors consecutive absence days
2. If operator absent without justification for > 4 days:
   - Alert sent to HR department
   - Flagged as potential job abandonment
   - Country-specific legal regulations applied

#### Status Correction Process
1. Team Leader can modify operator status until deadline:
   - D+1 at 7:00 AM for night shift
2. If deadline passed:
   - Team Leader submits Correction of Clocking request
   - Scans absence justification
   - Requires TKS approval
   - Status updated after approval

#### Shift Change Request Process
1. Team Leader submits shift change request to:
   - Shift Leader
   - TKS supervisor
2. For IH category:
   - Requires approval from employee's N+1
   - Once approved, processed in TKS
   - Change reflected in Optitime system

## Country-Specific Implementation EMEA/NA

### Regional Matrix
| Feature | Morocco | Tunisia | Turkey | Portugal | Poland | Serbia | North Americas |
|---------|----------|----------|----------|-----------|---------|---------|----------------|
| Visual check | ✓ | ✓ | ✓ | ✓ | ✗ | ✗ | ✗ |
| TLO | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ | ✗ |
| Unplanned TLO | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ |
| Payroll claims | ✓ | ✓ | ✓ | ✓ | ✗ | ✓ | ✓ |

### Implementation Strategy
1. **Configuration Repository** with feature toggles
2. **Workflow Engine Parameters** for country-specific approval chains
3. **UI Adaptations** for regional differences
4. **Validation Rules** for country-specific compliance

Implementation Strategy

1. **Configuration Repository**:
   ```typescript
   interface CountryConfig {
     countryCode: string;
     features: {
       visualCheck: boolean;
       tlo: boolean;
       unplannedTlo: boolean;
       payrollClaims: boolean;
       absenceAuthorization: boolean;
       badgeLoss: boolean;
     };
     workflows: {
       visualCheckApproval: WorkflowDefinition;
       tloApproval: WorkflowDefinition;
       absenceApproval: WorkflowDefinition;
       // ... other workflow definitions
     };
   }
   ```

2. **Feature Toggles**:
   - Each country configuration stored in Cosmos DB
   - Feature flags control availability of functionality
   - Dynamic workflow routing based on country settings

3. **Workflow Engine Parameters**:
   - Country-specific approval chains
   - Different validation requirements per region
   - Custom notification rules
   - Region-specific documentation requirements

4. **UI Adaptations**:
   - Dynamic menu items based on country features
   - Country-specific form fields
   - Localized validation messages
   - Regional compliance warnings

## API Endpoints

| Endpoint | Method | Description | Required Role |
|----------|--------|-------------|--------------|
| `/api/shift/start` | POST | Start shift process | TeamLeader, ShiftLeader |
| `/api/shift/close` | POST | Close shift process | TeamLeader, ShiftLeader |
| `/api/operator/status` | POST | Update operator status | TeamLeader, ShiftLeader |
| `/api/operator/status` | GET | Get operator status | All authenticated |
| `/api/dashboard` | GET | Get dashboard data | ShiftLeader, Manager |

## Integration Patterns

### Service Bus Topics

1. **Calendar Events Topic**
   - YearInitialized
   - HolidayCreated
   - WeekSummaryCalculated

2. **Working Plan Events Topic**
   - PlanCreated
   - ShiftAssigned
   - OperatorAssigned

3. **HeadCount Events Topic**
   - ShiftStarted
   - VisualCheckRecorded
   - StatusChangeRequested

### Event Flow

```mermaid
sequenceDiagram
    participant ACS as Annual Calendar Service
    participant WPS as Working Plan Service
    participant HCS as HeadCount Service
    participant SB as Service Bus
    participant DB as Databases

    ACS->>SB: Publish Calendar Events
    SB->>WPS: Consume Calendar Events
    WPS->>DB: Update Working Plans

    WPS->>SB: Publish Plan Events
    SB->>HCS: Consume Plan Events
    HCS->>DB: Update Operator Assignments

    HCS->>SB: Publish Status Events
    SB->>WPS: Consume Status Events
    WPS->>DB: Update Plan Status
```

## Security & Authorization

### Role-Based Access Control

```typescript
enum Role {
    ADMIN = "ADMIN",
    PLANNER = "PLANNER",
    TEAM_LEADER = "TEAM_LEADER",
    SHIFT_LEADER = "SHIFT_LEADER",
    DEPARTMENT_CLERK = "DEPARTMENT_CLERK",
    TKS_AGENT = "TKS_AGENT",
    VIEWER = "VIEWER"
}

const rolePermissions = {
    [Role.ADMIN]: [
        "manage:calendar",
        "manage:workingplan",
        "manage:headcount"
    ],
    [Role.TEAM_LEADER]: [
        "manage:operators",
        "read:workingplan",
        "perform:visualcheck",
        "start:shift",
        "close:shift"
    ],
    [Role.SHIFT_LEADER]: [
        "validate:shifts",
        "manage:teams",
        "emergency:update",
        "read:dashboard"
    ]
};
```

### Database Optimization

1. **Cosmos DB**
   - Efficient partition keys
   - Optimized indexing policies
   - Change Feed processors
   - Multi-tenant partitioning strategies

2. **SQL Database**
   - Indexed views for common queries
   - Optimized stored procedures
   - Proper indexing strategy

## Error Handling

```typescript
interface ErrorResponse {
    code: string;
    message: string;
    details?: any;
    correlationId: string;
    timestamp: string;
}

enum ErrorCode {
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    NOT_FOUND = 'NOT_FOUND',
    UNAUTHORIZED = 'UNAUTHORIZED',
    CONFLICT = 'CONFLICT',
    INTERNAL_ERROR = 'INTERNAL_ERROR'
}
```

## 4. Integration Patterns

### 4.1 Service Bus Topics

1. **Calendar Events Topic**
   - YearInitialized
   - HolidayCreated
   - WeekSummaryCalculated

2. **Working Plan Events Topic**
   - PlanCreated
   - ShiftAssigned
   - OperatorAssigned

3. **HeadCount Events Topic**
   - ShiftStarted
   - VisualCheckRecorded
   - StatusChangeRequested

### 4.2 Event Flow

```mermaid
sequenceDiagram
    participant ACS as Annual Calendar Service
    participant WPS as Working Plan Service
    participant HCS as HeadCount Service
    participant SB as Service Bus
    participant DB as Databases

    ACS->>SB: Publish Calendar Events
    SB->>WPS: Consume Calendar Events
    WPS->>DB: Update Working Plans

    WPS->>SB: Publish Plan Events
    SB->>HCS: Consume Plan Events
    HCS->>DB: Update Operator Assignments

    HCS->>SB: Publish Status Events
    SB->>WPS: Consume Status Events
    WPS->>DB: Update Plan Status
```

## 5. Conclusion

## 5. Conclusion

The Connected Workers Platform Module 3 represents a robust and scalable microservices architecture designed to handle complex workforce management requirements across global manufacturing sites. Key achievements and benefits of this architecture include:

### Technical Excellence
- Implementation of event-driven architecture using Azure Service Bus ensures reliable communication between services
- Adoption of CQRS pattern with event sourcing provides excellent audit capabilities and system scalability
- Multi-tenant design supports country-specific configurations while maintaining core functionality
- Comprehensive data modeling and storage strategies optimize performance and data access patterns

### Business Value
- Streamlined workforce planning and management processes
- Real-time visibility into operator status and attendance
- Flexible configuration supporting different regional requirements
- Improved compliance through automated workflow enforcement
- Enhanced integration between planning and execution systems

### Operational Benefits
- Reduced manual intervention through automated workflows
- Improved accuracy in attendance tracking and reporting
- Better resource utilization through integrated planning
- Enhanced decision-making with real-time dashboards
- Simplified maintenance through modular service design

This architecture provides a solid foundation for future enhancements while meeting current business requirements effectively. The modular design allows for easy integration of new features and adaptation to changing business needs.