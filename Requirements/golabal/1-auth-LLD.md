# Authentication, Authorization, and Delegation LLD

## Document Information

**Version:** 2.0.0  
**Last Updated:** 2025-03-04  
**Status:** Completed
**Authors: <AUTHORS>

## Executive Summary

This Low-Level Design (LLD) document provides a comprehensive technical specification for implementing a secure, scalable, and integrated authentication, authorization, and delegation system for the Connected Workers platform. The system leverages Microsoft Entra ID (formerly Azure AD) for identity management, combining SAML-based Single Sign-On (SSO) with JWT-based session management and just-in-time user provisioning through Microsoft Graph API.

### Key Features

- **Unified Authentication**: SAML SSO with JWT token management
- **Role-Based Access Control**: Hierarchical roles with granular permissions
- **Profile-Based Authorization**: Support for multiple role contexts per user
- **Flexible Delegation**: Secure authority transfer with hierarchical rules
- **Real-Time Synchronization**: Continuous user data sync with Azure AD
- **Enterprise-Grade Security**: Industry-standard encryption and security practices

### Business Benefits

- **Enhanced Security**: Centralized identity management with Azure AD
- **Improved User Experience**: Seamless authentication and profile switching
- **Operational Flexibility**: Structured delegation for business continuity
- **Reduced Administration**: Automated user provisioning and role assignment
- **Compliance Ready**: Comprehensive audit logging and security controls

### Document Focus

This LLD focuses on five core areas of the authentication, authorization, and delegation system:

1. **Authentication Architecture**: SAML SSO and JWT token management
2. **Authorization Framework**: Role-based access with profile contexts
3. **Delegation Framework**: Hierarchical delegation with approval workflows
4. **Microsoft Graph API Integration**: Real-time user data synchronization
5. **Data Model and Best Practices**: Foundational design recommendations

## Table of Contents

1. [Overview](#1-overview)

2. [Authentication Architecture](#2-authentication-architecture)

3. [Authorization Framework](#3-authorization-framework)

4. [Delegation Framework](#4-delegation-framework)

5. [Microsoft Graph API Integration](#5-microsoft-graph-api-integration)

6. [Data Model](#6-data-model)

7. [Conclusion](#7-conclusion)

## 1. Overview

### 1.1 Purpose and Scope

#### Purpose

This document serves as a technical blueprint for implementing the authentication, authorization, and delegation system. It provides detailed specifications for:

1. **Authentication Mechanisms**

   - SAML-based Single Sign-On integration with Azure AD
   - JWT token management for session handling
   - Token refresh and synchronization processes

2. **Authorization Framework**

   - Role-based access control implementation
   - Permission and scope management
   - Profile-based authorization system

3. **Delegation System**
   - Hierarchical delegation rules
   - Temporary access management
   - profile Delegation with restrictions

#### Scope

The system encompasses:

- User authentication and session management
- Role and permission management
- Profile-based access control
- Delegation workflows
- Integration with Microsoft Graph API
- Security implementations
- Operational considerations

### 1.2 Key Components

#### Core Services

1. **Authentication Service**

   - SAML SSO processing
   - JWT token issuance and validation
   - Session management
   - Token refresh handling

2. **Authorization Service**

   - Permission evaluation
   - Role management
   - Scope validation
   - Profile context handling

3. **Delegation Service**

   - Delegation request processing
   - Authority transfer management
   - Temporary access control

4. **Profile Service**

   - Profile lifecycle management
   - Context switching
   - Profile synchronization
   - Role assignment management

5. **Sync Service**
   - Graph API integration
   - User data synchronization
   - Group membership management
   - Delta query handling

## 2. Authentication Architecture

### 2.1 SAML-based Single Sign-On

The SAML-based Single Sign-On (SSO) implementation provides secure authentication using Microsoft Entra ID as the Identity Provider (IdP). This section details the core components, authentication flows, and technical specifications for the Connected Workers platform.

#### 2.1.1 Core Components

1. **Identity Provider (IdP) - Microsoft Entra ID**

   - Central authentication authority for the Connected Workers platform
   - Manages user credentials and verification processes
   - Generates and cryptographically signs SAML assertions
   - Provides multi-factor authentication capabilities
   - Manages user sessions at the IdP level
   - Generates claims based on user attributes and group memberships
   - Maintains certificate for signing SAML responses (valid until February 18, 2028)

2. **Service Provider (SP) - Connected Workers Application**

   - Manages protected resources and access control
   - Generates and signs SAML authentication requests
   - Validates and processes SAML responses
   - Establishes and manages user sessions
   - Issues JWT tokens for authenticated sessions
   - Manages certificate and key pairs for secure communication
   - Implements certificate rotation and expiration monitoring

3. **Assertion Consumer Service (ACS)**
   - Dedicated endpoint (`/api/v1/auth/acs`) for processing SAML responses
   - Validates response signatures using Entra ID's certificate
   - Extracts user attributes and identity information
   - Generates session tokens (JWT) for authenticated users
   - Establishes security context for the user session
   - Implements comprehensive error handling and security logging
   - Supports both development and production environments with flexible configuration

#### 2.1.2 SP-Initiated Flow

The SP-initiated flow is triggered when a user attempts to access the application directly:

```mermaid
sequenceDiagram
    participant User
    participant SP as Service Provider
    participant IdP as Entra ID

    User->>SP: 1. Access Protected Resource
    Note over SP: 2. Check for valid session
    SP->>SP: 3. Generate SAML Request with ID, issuer, ACS URL
    SP->>User: 4. Redirect to Entra ID with SAML Request
    User->>IdP: 5. Forward SAML Request
    Note over IdP: 6. Validate Request (issuer, signature)
    alt User Not Authenticated
        IdP->>User: 7a. Present Authentication UI
        User->>IdP: 8a. Provide Credentials
        Note over IdP: 9a. Validate Credentials
    else User Has Active Session
        Note over IdP: 7b. Identify User from Session
    end
    Note over IdP: 10. Generate SAML Response
    Note over IdP: 11. Sign Response & Assertions
    IdP->>User: 12. Redirect to SP's ACS URL with SAML Response
    User->>SP: 13. Forward SAML Response
    Note over SP: 14. Validate Response (signature, conditions, audience)
    Note over SP: 15. Extract User Attributes & Create Session
    SP->>User: 16. Grant Access & Set Session Cookie/JWT
```

**Implementation Details:**

1. **Initial Application Access**

   - When a user navigates to a protected resource, the application checks for valid JWT tokens
   - If no valid token exists, the authentication flow is initiated
   - The application's authentication guard redirects to the login endpoint

2. **SAML Request Generation**

   - The `SamlStrategy` class generates a SAML AuthnRequest with:
     - Unique request ID for tracking and security
     - Issuer identifier configured in the application
     - ACS URL where Entra ID should send the response
     - Signature using the service provider's private key
   - The request is configured with SHA-256 signature and digest algorithms

3. **Redirection to Identity Provider**

   - The application redirects the user to Entra ID's SSO endpoint
   - The request includes RelayState to preserve the original URL
   - The SAML request is compressed and encoded for transmission

4. **Response Processing**

   - The ACS endpoint receives the SAML response via HTTP POST
   - The `validate` method in `SamlStrategy` processes the response:
     - Verifies the digital signature using Entra ID's certificate
     - Validates the issuer matches the expected value
     - Extracts user information (email, name, groups, etc.)
   - The `findOrCreateUserFromSaml` method in `AuthService` creates or updates the user record

5. **Session Establishment**
   - The `generateTokens` method creates JWT access and refresh tokens
   - Tokens include user identity, roles, and standard JWT claims
   - The access token has a short expiration (typically 15 minutes)
   - The refresh token has a longer expiration (typically 7 days)
   - Secure HTTP-only cookies are set with appropriate security flags

#### 2.1.3 IdP-Initiated Flow

The IdP-initiated flow starts when users access the application through the Microsoft Entra ID portal:

```mermaid
sequenceDiagram
    participant User
    participant IdP as Entra ID
    participant SP as Service Provider

    User->>IdP: 1. Access Entra ID Portal/MyApps
    IdP->>User: 2. Display Available Applications
    User->>IdP: 3. Select Application Tile
    Note over IdP: 4. Check User Authentication
    alt User Not Authenticated
        IdP->>User: 5a. Present Authentication UI
        User->>IdP: 6a. Provide Credentials
        Note over IdP: 7a. Validate Credentials
    end
    Note over IdP: 8. Generate SAML Response
    Note over IdP: 9. Include App-specific Attributes & Roles
    IdP->>User: 10. Redirect to SP with SAML Response
    User->>SP: 11. Submit SAML Response
    Note over SP: 12. Validate Response
    Note over SP: 13. Extract User Identity & Attributes
    Note over SP: 14. Create User Session
    SP->>User: 15. Redirect to Default Landing Page
```

**Implementation Details:**

1. **Portal Access and Application Selection**

   - User logs into Microsoft Entra ID portal (myapplications.microsoft.com)
   - Selects the Connected Workers application tile
   - Entra ID initiates the SAML flow without a prior request

2. **SAML Response Processing**

   - The application's ACS endpoint receives an unsolicited SAML response
   - The `SamlStrategy` validates the response with additional security checks
   - The system extracts user attributes including:
     - Email address (nameID)
     - Display name
     - First and last name
     - Object ID (unique identifier in Azure AD)
     - Group memberships for role mapping

3. **User Session Creation**
   - The application creates a user session with appropriate permissions
   - JWT tokens are generated with the same process as SP-initiated flow
   - The user is redirected to the application's default landing page
   - The session includes all necessary context for authorization

#### 2.1.4 SAML Message Structure

1. **SAML Request Structure**

```xml
   <samlp:AuthnRequest
     xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
     xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
     ID="${requestID}"
     Version="2.0"
     IssueInstant="${timestamp}"
     Destination="${ssoEndpoint}"
     AssertionConsumerServiceURL="${acsUrl}"
     ProtocolBinding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST">
     <saml:Issuer>${spEntityID}</saml:Issuer>
     <samlp:NameIDPolicy
       Format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
       AllowCreate="true" />
   </samlp:AuthnRequest>
```

2. **SAML Response Structure**

   Based on the EMEA-EDS-Connected Workers System XML file, the SAML response follows this structure:

```xml
   <samlp:Response
     xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
     xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
     ID="${responseID}"
     Version="2.0"
     IssueInstant="${timestamp}"
     Destination="${acsUrl}">
     <saml:Issuer>https://sts.windows.net/6b1311e5-123f-49db-acdf-8847c2d00bed/</saml:Issuer>
     <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
       <!-- Signature details with SHA-256 algorithm -->
     </Signature>
     <samlp:Status>
       <samlp:StatusCode Value="urn:oasis:names:tc:SAML:2.0:status:Success" />
     </samlp:Status>
     <saml:Assertion>
       <saml:Issuer>https://sts.windows.net/6b1311e5-123f-49db-acdf-8847c2d00bed/</saml:Issuer>
       <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
         <!-- Assertion signature -->
       </Signature>
       <saml:Subject>
         <saml:NameID Format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"><EMAIL></saml:NameID>
         <saml:SubjectConfirmation Method="urn:oasis:names:tc:SAML:2.0:cm:bearer">
           <saml:SubjectConfirmationData
             NotOnOrAfter="${expiryTime}"
             Recipient="${acsUrl}" />
         </saml:SubjectConfirmation>
       </saml:Subject>
       <saml:Conditions NotBefore="${notBefore}" NotOnOrAfter="${notOnOrAfter}">
         <saml:AudienceRestriction>
           <saml:Audience>${spEntityID}</saml:Audience>
         </saml:AudienceRestriction>
       </saml:Conditions>
       <saml:AuthnStatement AuthnInstant="${authnInstant}" SessionIndex="${sessionIndex}">
         <saml:AuthnContext>
           <saml:AuthnContextClassRef>urn:oasis:names:tc:SAML:2.0:ac:classes:Password</saml:AuthnContextClassRef>
         </saml:AuthnContext>
       </saml:AuthnStatement>
       <saml:AttributeStatement>
         <saml:Attribute Name="http://schemas.microsoft.com/identity/claims/displayname">
           <saml:AttributeValue>John Doe</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.microsoft.com/identity/claims/objectidentifier">
           <saml:AttributeValue>a1b2c3d4-e5f6-7890-abcd-ef1234567890</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname">
           <saml:AttributeValue>John</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname">
           <saml:AttributeValue>Doe</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress">
           <saml:AttributeValue><EMAIL></saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups">
           <saml:AttributeValue>Plant Managers</saml:AttributeValue>
           <saml:AttributeValue>Connected Workers Users</saml:AttributeValue>
         </saml:Attribute>
       </saml:AttributeStatement>
     </saml:Assertion>
   </samlp:Response>
```

### 2.2 JWT Authentication

After the initial SAML authentication, the system uses JSON Web Tokens (JWT) for session management and ongoing authentication. JWTs provide a stateless, secure method for maintaining user sessions and authorization information.

#### 2.2.1 Token Types and Structure

The authentication system implements two types of tokens with distinct purposes and structures:

**Token Types Comparison:**

| Characteristic       | Access Token                                   | Refresh Token                            |
| -------------------- | ---------------------------------------------- | ---------------------------------------- |
| **Purpose**          | API authorization and resource access          | Obtaining new access tokens              |
| **Lifetime**         | Short-lived (15-60 minutes)                    | Longer-lived (hours to days)             |
| **Storage Location** | Application memory                             | HTTP-only secure cookie                  |
| **Claim Richness**   | Contains complete user profile and permissions | Contains minimal identifying information |
| **Usage Frequency**  | Every API call                                 | Only during token refresh                |
| **Exposure**         | Limited to application frontend                | Server-side only                         |
| **Revocation**       | Self-expires quickly                           | Can be invalidated server-side           |

**Access Token Structure:**

```
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "[certificate identifier]"
  },
  "payload": {
    "sub": "<EMAIL>",
    "name": "John Doe",
    "given_name": "John",
    "family_name": "Doe",
    "email": "<EMAIL>",
    "roles": ["User", "ProjectManager"],
    "department": "Engineering",
    "location": "Paris",
    "employeeId": "EMP123456",
    "profile_id": "profile_12345",
    "iat": 1675091348,
    "exp": 1675094948,
    "aud": "connected-workers-api",
    "iss": "connected-workers-auth"
  }
}
```

**Refresh Token Structure:**

```
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "[certificate identifier]"
  },
  "payload": {
    "sub": "<EMAIL>",
    "jti": "unique-token-id-12345",
    "iat": 1675091348,
    "exp": 1675177748,
    "iss": "connected-workers-auth"
  }
}
```

**Token Claim Descriptions:**

| Claim         | Description                          | Access Token | Refresh Token |
| ------------- | ------------------------------------ | :----------: | :-----------: |
| `sub`         | Subject identifier (typically email) |      ✓       |       ✓       |
| `name`        | User's full name                     |      ✓       |       ✗       |
| `given_name`  | User's first name                    |      ✓       |       ✗       |
| `family_name` | User's last name                     |      ✓       |       ✗       |
| `email`       | User's email address                 |      ✓       |       ✗       |
| `roles`       | User's assigned roles                |      ✓       |       ✗       |
| `department`  | User's department                    |      ✓       |       ✗       |
| `employeeId`  | Employee identifier                  |      ✓       |       ✗       |
| `profile_id`  | Active profile context               |      ✓       |       ✗       |
| `iat`         | Issued at timestamp                  |      ✓       |       ✓       |
| `exp`         | Expiration timestamp                 |      ✓       |       ✓       |
| `aud`         | Intended audience                    |      ✓       |       ✗       |
| `iss`         | Token issuer                         |      ✓       |       ✓       |
| `jti`         | JWT ID (unique identifier)           |      ✗       |       ✓       |

#### 2.2.2 Token Management

Token management encompasses the complete lifecycle of JWTs within the system, including generation, validation, renewal, and revocation.

**Token Generation Security Considerations:**

| Consideration             | Implementation                                  |
| ------------------------- | ----------------------------------------------- |
| **Signing Algorithm**     | RS256 (asymmetric) with 2048-bit key            |
| **Key Rotation**          | 90-day rotation schedule with overlap period    |
| **Entropy Source**        | Hardware-based random number generation         |
| **Claim Minimization**    | Include only necessary claims per token type    |
| **Clock Synchronization** | NTP with maximum 30-second clock skew tolerance |
| **Identifier Uniqueness** | UUIDv4 for token identifiers (jti claim)        |

**Token Validation Process:**

1. **Signature Verification**:

   - Verify token is properly signed with the correct key
   - Check signing algorithm matches expected algorithm

2. **Standard Claims Validation**:

   - Verify token has not expired (`exp` claim)
   - Verify token was issued at a reasonable time (`iat` claim)
   - Verify issuer matches expected value (`iss` claim)
   - Verify audience is correct (`aud` claim)

3. **Application-Specific Validation**:
   - Check for required claims based on resource access
   - Validate role claims for authorization
   - Verify profile context is valid

#### 2.2.3 Token Storage Strategy

Proper token storage is critical for security. The system implements a defense-in-depth approach to protect tokens from various attack vectors:

**Token Storage Locations:**

| Token Type              | Storage Location  | Technical Implementation                | Security Considerations               |
| ----------------------- | ----------------- | --------------------------------------- | ------------------------------------- |
| **Access Token**        | Memory (frontend) | JavaScript variable in auth state       | Not persisted across page refreshes   |
| **Access Token Backup** | SessionStorage    | Encrypted, used only for page refreshes | Cleared when browser closes           |
| **Refresh Token**       | HTTP-only Cookie  | Secure, SameSite=Strict                 | Not accessible via JavaScript         |
| **Token Metadata**      | localStorage      | Expiry time only (no actual tokens)     | Used to detect when refresh is needed |

**Security Measures:**

| Threat               | Mitigation                                                       |
| -------------------- | ---------------------------------------------------------------- |
| XSS Attack           | Memory-only access tokens, HTTP-only cookies for refresh tokens  |
| CSRF Attack          | SameSite cookie policy, CSRF tokens for authentication endpoints |
| Token Leakage        | No tokens in localStorage, no tokens in URLs                     |
| Session Hijacking    | Short token lifetimes, secure cookie flags                       |
| Local Storage Access | Only non-sensitive metadata stored                               |

### 2.3 Token Refresh Implementation

The token refresh mechanism enables continuous user sessions without requiring re-authentication while simultaneously keeping user data synchronized with Microsoft Entra ID.

#### 2.3.1 Refresh Process

Token refresh is initiated automatically when an access token approaches expiration:

**Refresh Flow:**

```mermaid
sequenceDiagram
 participant Client
 participant API as Auth API
 participant Graph as Microsoft Graph API
 participant DB as Database

 Note over Client: Detect access token<br/>approaching expiration
 Client->>API: Request token refresh
 Note right of Client: Refresh token included<br/>in HTTP-only cookie
 API->>API: Validate refresh token

 alt Valid Refresh Token
     API->>Graph: Fetch latest user data
     Graph->>API: Return user profile & groups
     API->>DB: Update user data
     API->>API: Generate new tokens
     API->>Client: Return new access token
     Note right of API: Set new refresh token<br/>in HTTP-only cookie
 else Invalid Refresh Token
     API->>Client: Return 401 Unauthorized
     Client->>Client: Redirect to login
 end
```

**Expiration Detection Strategy:**

| Strategy Component        | Implementation                                      |
| ------------------------- | --------------------------------------------------- |
| **Client-Side Detection** | Track token expiry time in memory                   |
| **Refresh Timing**        | Initiate at 75% of token lifetime                   |
| **Background Refresh**    | Refresh happens in background without UI disruption |
| **Proactive Detection**   | Check before critical operations                    |
| **Fallback Detection**    | Handle 401 responses from API calls                 |

**Refresh Endpoint Security:**

| Security Feature                  | Implementation                                   |
| --------------------------------- | ------------------------------------------------ |
| **Rate Limiting**                 | Maximum 10 refresh attempts per minute per user  |
| **Jitter**                        | Random delay (1-3s) to prevent timing attacks    |
| **IP Validation**                 | Optional validation against previous request IPs |
| **Refresh Token Rotation**        | New refresh token with each successful refresh   |
| **Suspicious Activity Detection** | Block after multiple rapid refresh attempts      |

#### 2.3.2 User Data Synchronization

The Connected Workers platform implements a comprehensive user data synchronization strategy that ensures continuous alignment between Microsoft Entra ID and the application's user context. This synchronization occurs during each token refresh operation, maintaining data consistency and security across the entire system.

**Attribute Synchronization Framework**

The synchronization process encompasses multiple categories of user attributes, each serving distinct purposes within the application. At the foundation, basic profile information synchronization ensures accurate user identification and presentation throughout the interface. This includes essential identifiers such as display names, email addresses, organization and contact information, which are crucial for user recognition and communication.

Security and authorization data synchronization focuses on role assignments and group memberships. This critical security layer ensures that user permissions accurately reflect their current organizational responsibilities and access requirements. The system continuously updates these assignments to maintain precise access control and security boundaries.

**Role Authorization Framework**

The Connected Workers platform implements a sophisticated role-based access control system that translates Azure AD group memberships into application-specific roles and permissions. This mapping creates a clear, hierarchical authorization structure that aligns with organizational responsibilities while maintaining security and operational efficiency.

The system maintains a baseline access level through the Connected Workers Users group, ensuring all authorized users have access to essential platform functionality. This foundational access layer provides basic system interaction capabilities while maintaining security boundaries.

This role mapping framework is dynamically maintained during each token refresh operation, ensuring that user permissions always reflect current Azure AD group memberships. The system's ability to maintain this synchronization in real-time ensures that access control remains current and accurate, while the hierarchical structure supports clear lines of authority and responsibility within the organization.

#### 2.3.3 Error Handling

Robust error handling ensures the system can gracefully recover from authentication failures:

**Common Error Scenarios:**

| Error Scenario            | Detection Method             | Handling Strategy                       | User Experience                           |
| ------------------------- | ---------------------------- | --------------------------------------- | ----------------------------------------- |
| **Expired Refresh Token** | JWT expiration validation    | Redirect to login                       | Clear authentication message              |
| **Invalid Signature**     | Signature validation failure | Security alert, login redirect          | Security message                          |
| **User Not Found**        | Database lookup failure      | JIT provisioning attempt                | Transparent retry                         |
| **Graph API Unavailable** | API timeout/error            | Use cached data, retry later            | Notification of potentially outdated data |
| **Role Mapping Failure**  | Exception in mapping process | Log issue, use default/previous roles   | Limited functionality notice              |
| **Database Connectivity** | Database exception           | Retry with backoff, use cached JWT data | Transaction-specific error                |

**Recovery Strategies:**

```mermaid
flowchart TD
    A[Detect Refresh Error] --> B{Error Type}
    B -->|Token Expired/Invalid| C[Redirect to Login]
    B -->|Graph API Unavailable| D[Use Cached User Data]
    B -->|Database Error| E[Retry with Exponential Backoff]
    B -->|Network Error| F[Queue Refresh for Later]

    D --> G[Flag Data as Potentially Outdated]
    D --> H[Schedule Background Retry]
    E --> I{Retry Successful?}
    I -->|Yes| J[Resume Normal Operation]
    I -->|No, Max Retries| K[Degrade Gracefully]
    F --> L[Monitor Connectivity]
    L --> M{Connection Restored?}
    M -->|Yes| N[Execute Queued Operations]
    M -->|No| O[Notify User]
```

## 3. Authorization Framework

This section outlines the comprehensive authorization framework for managing access control within the application, leveraging JWT authentication with SAML SSO and Microsoft Graph API synchronization.

### 3.1 Core Authorization Concepts

#### 3.1.1 Role Management

Roles are collections of permissions derived from Azure AD group memberships, providing a direct mapping between organizational structure and system permissions.

##### Role Types

| Role Type      | Description                            | Examples                                 |
| -------------- | -------------------------------------- | ---------------------------------------- |
| Organizational | Based on position in company hierarchy | PLANT_MANAGER, SHIFT_LEADER, TEAM_LEADER |
| Functional     | Based on specific job functions        | QUALITY_INSPECTOR, MAINTENANCE_TECH      |
| Administrative | System administration capabilities     | SYSTEM_ADMIN, USER_MANAGER               |
| Specialized    | Temporary or special-purpose access    | EMERGENCY_RESPONDER, AUDITOR             |

##### Role Hierarchy

```mermaid
graph TD
    SA[SUPER_ADMIN] --> PM[PLANT_MANAGER]
    PM --> SL[SHIFT_LEADER]
    SL --> TL[TEAM_LEADER]
    TL --> OP[OPERATOR]
    SA --> TR[TRAINING_RESPONSIBLE]
    TR --> T[TRAINER]
    SA --> QI[QUALITY_INSPECTOR]
    SA --> MT[MAINTENANCE_TECH]
```

##### Hierarchical Role Structure

Roles have parent-child relationships that define an organization's management structure. Each role has:

- A hierarchical level (1 being the highest, increasing numbers for lower levels)
- Parent role references (which roles are above in the hierarchy)
- Child role references (which roles are below in the hierarchy)

This hierarchy structure enables:

1. **Direct Permission Assignment**: Each role has explicitly assigned permissions based on its level
2. **Delegation Control**: Delegation can be restricted to follow the hierarchy
3. **Organizational Alignment**: Authorization reflects organizational structure
4. **Validation Rules**: Hierarchical authorization checks (e.g., a role can only manage users with roles below it)

##### Role Definition with Hierarchy Support

```json
{
  "role": {
    "id": "SHIFT_LEADER",
    "name": "Shift Leader",
    "description": "Manages production shift and team leaders",
    "hierarchyLevel": 3,
    "parentRoles": ["PLANT_MANAGER"],
    "childRoles": ["TEAM_LEADER"],
    "azureAdGroups": ["Shift Leaders"]
  }
}
```

#### 3.1.2 Permission Framework

Permissions follow a simple resource:action format that clearly defines what operations a user can perform on specific resources.

##### Permission Structure

| Component | Description                   | Example                                     |
| --------- | ----------------------------- | ------------------------------------------- |
| Resource  | The entity being accessed     | `team`, `schedule`, `training` , `operator` |
| Action    | The operation being performed | `create`, `read`, `update`, `delete`        |

##### Permission Examples

| Permission          | Description                                |
| ------------------- | ------------------------------------------ |
| `team:manage`       | Manage team information and membership     |
| `operator:manage`   | Manage operator information and membership |
| `schedule:view`     | View schedules                             |
| `training:assign`   | Assign training to users                   |
| `equipment:control` | Control equipment and machinery            |
| `shift:manage`      | Manage shift schedules and assignments     |
| `quality:inspect`   | Perform quality inspection tasks           |
| `quality:approve`   | Approve quality-related documentation      |

#### 3.1.3 Scope Management

Scopes define the boundaries within which permissions can be exercised, allowing for fine-grained access control.

##### Scope Types

| Scope Type     | Description                 | Examples                     |
| -------------- | --------------------------- | ---------------------------- |
| Organizational | Based on company structure  | Site, Department, Team       |
| Temporal       | Based on time periods       | Shift, Date Range            |
| Functional     | Based on functional areas   | Process Area, Equipment Type |
| Resource       | Based on specific resources | Machine ID, Material Type    |

##### Scope Implementation

Scopes are implemented as attributes on user profiles, role assignments, and delegations. They can be derived from:

1. **Azure AD Attributes**: Department, location, job title
2. **Application Metadata**: Team assignments, shift assignments
3. **Delegation Restrictions**: Explicitly defined in delegation requests

### 3.2 Role-Based Access Control

#### 3.2.1 Role Types and Hierarchy

The RBAC system implements a hierarchical role structure that reflects organizational management levels and functional specializations.

##### Hierarchical Role Relationships

Role hierarchy is a fundamental aspect of the authorization system, providing clear lines of authority and permission inheritance:

1. **Vertical Relationships**: Define reporting lines (Manager → Supervisor → Team Lead → Operator)
2. **Horizontal Relationships**: Define peer relationships within the same hierarchy level
3. **Functional Relationships**: Define specialized cross-cutting concerns (Quality, Safety, Maintenance)

##### Role Hierarchy Configuration

```json
{
  "roleHierarchyConfig": {
    "enforceHierarchicalRestrictions": true,
    "allowCrossLevelOperations": false,
    "maxLevelSkipForOperations": 1,
    "inheritPermissionsFromParent": false,
    "permissionOverrideRules": {
      "allowChildOverride": false,
      "allowScopedOverride": true
    }
  }
}
```

#### 3.2.2 Permission Management

Permissions are managed at multiple levels to provide flexible yet secure access control.

##### Permission Assignment Model

The system uses a layered permission assignment model:

1. **Role-Based Permissions**: Core permissions assigned to roles
2. **Context-Based Permissions**: Permissions that vary based on context (e.g., location, time)
3. **Attribute-Based Rules**: Dynamic permissions based on user or resource attributes
4. **Policy-Based Overrides**: Global security policies that can override standard permissions

##### Permission Resolution Process

```mermaid
flowchart TD
    A[Permission Check] --> B[Collect Role Permissions]
    B --> C[Apply Context Constraints]
    C --> D[Apply Attribute Rules]
    D --> E[Apply Policy Overrides]
    E --> F{Permission Granted?}
    F -->|Yes| G[Allow Operation]
    F -->|No| H[Deny Operation]
```

##### Permission Conflict Resolution

| Conflict Type                    | Resolution Strategy                                         |
| -------------------------------- | ----------------------------------------------------------- |
| Role vs. Context                 | Context restrictions override role permissions              |
| Multiple Roles                   | Most permissive access granted (union of permissions)       |
| Hierarchical Conflicts           | Explicit permissions override inherited permissions         |
| Delegation vs. Direct Assignment | More restrictive permission applies                         |
| Policy vs. Role                  | Security policies always override role-based permissions    |
| Cross-functional Role Conflicts  | Domain-specific permissions take precedence in their domain |

### 3.3 Group-Based Authorization

#### 3.3.1 Dynamic Groups

Dynamic groups in Azure AD provide flexible, attribute-based group membership that automatically updates based on user properties.

##### Dynamic Group Rules

| Rule Type        | Description                        | Example Rule                                                   |
| ---------------- | ---------------------------------- | -------------------------------------------------------------- |
| Department-Based | Users from specific department     | `user.department -eq "Manufacturing"`                          |
| Location-Based   | Users at specific locations        | `user.physicalDeliveryOfficeName -eq "Plant A"`                |
| Title-Based      | Users with specific job titles     | `user.jobTitle -contains "Shift Leader"`                       |
| Combined Rules   | Multiple conditions for membership | `(user.department -eq "Quality") -and (user.country -eq "US")` |
| Exclusion Rules  | Exclude specific users from groups | `NOT(user.employeeId -in ["1234", "5678"])`                    |

##### Benefits of Dynamic Groups

1. **Automatic Membership Management**: Group membership automatically updated as user attributes change
2. **Reduced Administrative Overhead**: No manual group membership management required
3. **Consistent Access Control**: Users with same attributes have same access
4. **Simplified Onboarding/Offboarding**: Access granted/removed automatically based on user attributes

#### 3.3.2 Group to Role Mapping

Azure AD security groups are mapped directly to application roles, creating a seamless integration between identity management and authorization.

##### Configuration Example

| Azure AD Group          | Application Role  | Auto-Assign | Scope Attributes |
| ----------------------- | ----------------- | ----------- | ---------------- |
| Plant Managers          | PLANT_MANAGER     | Yes         | site             |
| Shift Leaders           | SHIFT_LEADER      | Yes         | site, shift      |
| Team Leaders            | TEAM_LEADER       | Yes         | team             |
| Quality Inspectors      | QUALITY_INSPECTOR | Yes         | department, site |
| Maintenance Technicians | MAINTENANCE_TECH  | Yes         | site             |

##### Mapping Process Flow

```mermaid
sequenceDiagram
participant User
participant App as Application
participant API as API Backend
participant AD as Azure AD
participant Graph as Microsoft Graph API
participant DB as Database

User->>App: Authenticate with SAML
App->>API: Process SAML Response
API->>Graph: Query User Group Memberships
Graph->>API: Return Group Memberships
API->>DB: Lookup Group-Role Mappings
API->>API: Apply Role Assignments
API->>App: Return JWT with Roles
App->>User: Authorized Session
```

##### Mapping Configuration

```json
{
  "groupMappings": [
    {
      "azureAdGroupId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "displayName": "Plant Managers",
      "applicationRole": "PLANT_MANAGER",
      "autoAssign": true,
      "scopeAttributes": ["site"]
    },
    {
      "azureAdGroupId": "b2c3d4e5-f6a7-8901-bcde-f12345678901",
      "displayName": "Quality Inspectors",
      "applicationRole": "QUALITY_INSPECTOR",
      "autoAssign": true,
      "scopeAttributes": ["department", "site"]
    }
  ]
}
```

This configuration is loaded when the microservice starts and is used during user synchronization with Microsoft Graph API to assign appropriate roles based on Azure AD group memberships.

### 3.4 Profile-Based Authorization

#### 3.4.1 Profile Types and Lifecycle

Profiles provide contextualized role containers that users can switch between.

##### Profile Types

| Profile Type | Source    | Description                                  | Creation                            |
| ------------ | --------- | -------------------------------------------- | ----------------------------------- |
| Direct       | Assigned  | Based on user's Azure AD group memberships   | Created during first authentication |
| Delegated    | Temporary | Created through delegation from another user | Created during delegation process   |
| System       | Automatic | Generated for special purposes               | Created for specific scenarios      |

##### Profile Attributes

Each profile contains:

- Unique identifier
- User reference
- Name and description
- Profile type
- Active status
- Validity period (for delegated profiles)
- Associated roles and restrictions
- Contextual metadata

##### Profile Lifecycle States

```mermaid
stateDiagram-v2
    [*] --> Inactive : Create
    Inactive --> Active : Activate
    Active --> Inactive : Deactivate
    Inactive --> [*] : Delete/Expire
    Active --> [*] : Delete/Expire
```

#### 3.4.2 Profile Switching

Users can switch between available profiles to change their operating context.

##### Profile Switching Process

```mermaid
sequenceDiagram
participant User
participant App as Application
participant API as API Backend
participant DB as Database

User->>App: Request Profile Switch
App->>API: Switch Profile Request
API->>DB: Validate Profile Access
API->>DB: Update Active Profile
API->>API: Generate New JWT with Profile Context
API->>App: Return New JWT
App->>User: Updated Authorization Context
```

##### Profile Switch Implementation

The profile switching mechanism follows these steps:

1. **Profile Selection**: User selects target profile from available profiles
2. **Backend Validation**: API validates user can access the requested profile
3. **Context Switch**: Profile is marked active, previous active profile is deactivated
4. **Token Refresh**: New JWT tokens are issued with updated profile context
5. **Client Update**: Application updates authorization context with new token

##### Profile Switch Security Controls

| Control                     | Implementation                                                |
| --------------------------- | ------------------------------------------------------------- |
| Switch Validation           | Verify user owns the profile before switching                 |
| Automatic Expiry            | Enforce automatic expiry for temporary profiles               |
| Privilege Escalation Check  | Prevent switching to profiles with excessive privileges       |
| Anomaly Detection           | Track unusual profile switching patterns                      |
| Context-Aware Restrictions  | Limit profile switching based on location, time, or device    |
| Mandatory Re-Authentication | Require re-authentication for switching to sensitive profiles |

#### 3.4.3 Profile Context in Tokens

JWT tokens serve as the carrier for profile context and authorization information.

##### Profile Context in JWT

Each JWT contains:

- User identity information
- Active profile identifier and type
- Roles associated with active profile
- Permissions derived from those roles
- Scope restrictions
- Delegation reference (if applicable)

##### Example Token Payload

```json
{
  "sub": "<EMAIL>",
  "name": "John Doe",
  "email": "<EMAIL>",
  "department": "Production",
  "jobTitle": "Team Leader",
  "roles": ["TEAM_LEADER", "TRAINER"],
  "permissions": ["team:manage", "schedule:view", "training:assign"],
  "activeProfileId": "profile123",
  "profileType": "DELEGATED",
  "delegationId": "delegation456",
  "scope": {
    "sites": ["SITE_A"],
    "teams": ["TEAM_X", "TEAM_Y"]
  },
  "restrictions": {
    "excluded": ["budget:manage"]
  },
  "iat": 1675091348,
  "exp": 1675094948
}
```

**Profile Context Security Considerations:**

| Consideration              | Implementation                                                       |
| -------------------------- | -------------------------------------------------------------------- |
| **Profile Validation**     | Verify profile exists and is active before honoring claims           |
| **Temporal Checks**        | Validate profile has not expired (especially for delegated profiles) |
| **Scope Enforcement**      | Always apply scope restrictions from active profile                  |
| **Restriction Precedence** | Profile restrictions override role permissions                       |
| **Delegation Validation**  | For delegated profiles, verify delegation is still valid             |

## 4. Delegation Framework

The delegation framework enables secure, controlled transfer of authority between users while maintaining clear lines of responsibility and accountability. This system supports business continuity, operational flexibility, and allows for efficient handling of absence scenarios.

### 4.1 Delegation Process

Delegation enables temporary transfer of authority from one user to another through a streamlined process that includes approval mechanisms and clear documentation.

#### 4.1.1 Delegation Creation Flow

```mermaid
sequenceDiagram
    participant Delegator as Delegator
    participant N1Delegator as Delegator's N+1
    participant API as API Backend
    participant DB as Database
    participant N1Delegate as Delegate's N+1
    participant Delegate as Delegate

    Delegator->>API: Create Delegation Request
    API->>DB: Validate Delegator Authority
    API->>N1Delegator: Request Approval
    N1Delegator->>API: Approve Delegation
    API->>N1Delegate: Request Approval
    N1Delegate->>API: Approve Delegation
    API->>DB: Create Delegation Record
    API->>DB: Create Delegated Profile
    API->>Delegate: Notify of New Delegation
    API->>Delegator: Confirm Creation
```

#### 4.1.2 Delegation Request Structure

The delegation request captures all information necessary to define the delegation scope, duration, and justification:

```json
{
  "delegatorId": "user123",
  "delegateId": "user456",
  "roleId": "SHIFT_LEADER",
  "validFrom": "2025-03-20T00:00:00Z",
  "validUntil": "2025-03-27T23:59:59Z",
  "profileName": "Shift Leader Coverage",
  "reason": "Annual leave coverage",
  "permissions": {
    "included": ["shift:manage", "incidents:handle"],
    "excluded": ["budget:manage", "performance:review"]
  },
  "restrictions": {
    "scope": {
      "teams": ["TEAM_A", "TEAM_B"],
      "shifts": ["MORNING_SHIFT"]
    }
  },
  "requiresApproval": true,
  "approvers": {
    "delegatorSupervisorId": "manager789",
    "delegateeSupervisorId": "manager012"
  },
  "metadata": {
    "businessUnit": "Manufacturing",
    "requestTimestamp": "2025-03-15T09:30:00Z"
  }
}
```

#### 4.1.3 Approval Workflow

The delegation process includes a structured approval workflow to ensure proper oversight:

| Approval Stage     | Approver        | Responsibility                              | Fallback Mechanism                        |
| ------------------ | --------------- | ------------------------------------------- | ----------------------------------------- |
| Initial Review     | Delegator's N+1 | Validate business need and delegation scope | Auto-approval after 24h if critical       |
| Secondary Review   | Delegate's N+1  | Confirm delegate capacity and suitability   | Auto-approval after 24h if critical       |
| Emergency Override | Security Admin  | Bypass approval in emergency situations     | Limited to predefined emergency scenarios |

For time-sensitive delegations, a streamlined approval workflow can be configured with appropriate controls:

### 4.2 Delegation Restrictions

Restrictions limit the scope and capabilities of delegated authority, ensuring the principle of least privilege is maintained throughout the delegation lifecycle.

#### 4.2.1 Restriction Types

| Restriction Type          | Description                      | Example                                                   | Implementation                                                 |
| ------------------------- | -------------------------------- | --------------------------------------------------------- | -------------------------------------------------------------- |
| **Scope Limitations**     | Where authority can be exercised | `{"teams": ["TEAM_A", "TEAM_B"]}`                         | Limit access to specific organizational units                  |
| **Permission Inclusions** | Explicitly allowed operations    | `["shift:manage", "incidents:handle"]`                    | Only specific permissions from the role are delegated          |
| **Permission Exclusions** | Explicitly forbidden operations  | `["budget:manage", "performance:review"]`                 | Specific high-sensitivity permissions excluded from delegation |
| **Time Constraints**      | When delegation is active        | `{"validFrom": "2025-03-01", "validUntil": "2025-03-15"}` | Enforce strict temporal boundaries                             |
| **Approval Requirements** | Approval workflow configuration  | `{"requireN1Approval": true}`                             | Additional verification for sensitive delegations              |
| **Quantity Limits**       | Numeric limits on operations     | `{"maxDailyOperations": 20}`                              | Prevent abuse of delegated authority                           |

#### 4.2.2 Non-Delegable Permissions

Certain permissions can be configured as non-delegable to protect sensitive operations:

| Permission           | Rationale                             | Configuration                      |
| -------------------- | ------------------------------------- | ---------------------------------- |
| `performance:review` | Requires direct knowledge of employee | System-defined non-delegable       |
| `budget:approve`     | Financial control requirement         | Organization-defined non-delegable |
| `hr:terminate`       | Critical HR function                  | Organization-defined non-delegable |
| `security:admin`     | Security risk                         | System-defined non-delegable       |

Non-delegable permissions are configured at the system level by administrators:

```json
{
  "nonDelegablePermissions": {
    "systemDefined": ["security:admin", "performance:review"],
    "organizationDefined": ["budget:approve", "hr:terminate"],
    "override": {
      "allowed": false,
      "requiredApprovals": ["SECURITY_ADMIN", "IT_DIRECTOR"]
    }
  }
}
```

#### 4.2.3 Partial Role Delegation

The system supports delegation of a subset of permissions within a role, allowing for granular authority transfer:

```mermaid
flowchart LR
    A[Full Role] --> B{Delegation Filter}
    B -->|Include| C[Permission 1]
    B -->|Include| D[Permission 2]
    B -->|Exclude| E[Permission 3]
    B -->|Include| F[Permission 4]
    C --> G[Delegated Profile]
    D --> G
    F --> G
```

Example of partial role delegation configuration:

```json
{
  "roleId": "SHIFT_LEADER",
  "fullPermissions": [
    "shift:manage",
    "team:view",
    "incidents:handle",
    "reports:generate",
    "performance:review",
    "budget:view"
  ],
  "delegatedPermissions": {
    "included": ["shift:manage", "team:view", "incidents:handle"],
    "excluded": ["performance:review", "budget:view", "reports:generate"]
  }
}
```

### 4.3 Delegation Lifecycle

Delegations follow a defined lifecycle with clear state transitions and governance controls.

#### 4.3.1 Delegation States

```mermaid
stateDiagram-v2
    [*] --> Draft: Create
    Draft --> PendingApproval: Submit
    PendingApproval --> Approved: Approve
    PendingApproval --> Rejected: Reject
    Approved --> Active: Start Date Reached
    Active --> Expired: End Date Reached
    Active --> Revoked: Manual Revoke
    Draft --> Cancelled: Cancel
    PendingApproval --> Cancelled: Cancel
    Expired --> [*]: Cleanup
    Revoked --> [*]: Cleanup
    Rejected --> [*]: Cleanup
    Cancelled --> [*]: Cleanup
```

#### 4.3.2 State Transitions

| From                  | To              | Trigger                | Action                    | Notification                    |
| --------------------- | --------------- | ---------------------- | ------------------------- | ------------------------------- |
| -                     | Draft           | Initial creation       | Create draft record       | None                            |
| Draft                 | PendingApproval | Submission             | Send to approvers         | Approvers notified              |
| PendingApproval       | Approved        | All approvals received | Update status             | Delegator and delegate notified |
| PendingApproval       | Rejected        | Rejection by approver  | Update status with reason | Delegator notified              |
| Approved              | Active          | Start date reached     | Create delegated profile  | Delegate notified               |
| Active                | Expired         | End date reached       | Deactivate profile        | Both parties notified           |
| Active                | Revoked         | Manual revocation      | Deactivate with reason    | Both parties notified           |
| Draft/PendingApproval | Cancelled       | Cancellation request   | Update status             | Relevant parties notified       |

#### 4.3.3 Automatic Delegation

For predefined scenarios, the system supports automatic delegation when specific conditions are met:

```mermaid
flowchart TD
    A[Detect User Absence] --> B{Has Predefined Delegation?}
    B -->|Yes| C[Activate Predefined Delegation]
    B -->|No| D{N+1 Available?}
    D -->|Yes| E[Create Auto-Delegation to N+1]
    D -->|No| F[Escalate to System Admin]
    C --> G[Notify Delegate of Activation]
    E --> G
```

Automatic delegation configuration:

```json
{
  "automaticDelegations": [
    {
      "userId": "teamlead1",
      "absenceType": "ANY",
      "defaultDelegateId": "shiftlead1",
      "roleId": "TEAM_LEADER",
      "permissions": "ALL",
      "maxDuration": "7D",
      "requiresConfirmation": false
    }
  ]
}
```

When a Team Leader (TL) is absent, their responsibilities automatically delegate to their Shift Leader (SL). The SL can either:

- Execute the delegated TL role directly
- Further delegate the TL role to another qualified TL

### 4.4 Hierarchical Delegation Rules

Delegation within hierarchical organizational structures follows specific rules to maintain management chain integrity and ensure proper authorization.

#### 4.4.1 Hierarchical Delegation Principles

1. **Delegation Flow Direction**: Delegations typically flow downward in the hierarchy
2. **Level Restrictions**: Delegations are usually limited to adjacent levels
3. **Scope Limitations**: Delegated authority cannot exceed the delegator's scope
4. **Contextual Validation**: Delegation may require validation against organizational context

#### 4.4.2 Hierarchical Delegation Configuration

```json
{
  "delegationRules": {
    "hierarchyValidation": true,
    "allowSkipLevel": false,
    "maxSkipLevels": 0,
    "validateHierarchicalScope": true,
    "allowDelegationChains": false,
    "validateOrganizationalContext": true
  }
}
```

#### 4.4.3 Delegation Rules Matrix

| Delegator Role    | Delegate Role | Permitted | Reason                           | Restrictions                           |
| ----------------- | ------------- | --------- | -------------------------------- | -------------------------------------- |
| PLANT_MANAGER     | SHIFT_LEADER  | Yes       | Direct hierarchical relationship | Limited to delegator's site scope      |
| SHIFT_LEADER      | TEAM_LEADER   | Yes       | Direct hierarchical relationship | Limited to delegator's shift and teams |
| PLANT_MANAGER     | TEAM_LEADER   | No        | Skip level (configurable)        | N/A                                    |
| TEAM_LEADER       | SHIFT_LEADER  | No        | Against hierarchy direction      | N/A                                    |
| QUALITY_INSPECTOR | TEAM_LEADER   | No        | No hierarchical relationship     | N/A                                    |

### 4.5 Delegation Authorization Model

The delegation authorization model defines how delegation permissions are evaluated against delegation requests, ensuring proper authorization throughout the delegation lifecycle.

#### 4.5.1 Permission Evaluation Process

```mermaid
flowchart TD
    A[Delegation Request] --> B{Has Required Permission?}
    B -->|No| C[Deny: Insufficient Permission]
    B -->|Yes| D{Validate Hierarchy Rules}
    D -->|Fail| E[Deny: Hierarchy Violation]
    D -->|Pass| F{Validate Scope Rules}
    F -->|Fail| G[Deny: Scope Violation]
    F -->|Pass| H{Validate Context Rules}
    H -->|Fail| I[Deny: Context Violation]
    H -->|Pass| J{Validate Permission Rules}
    J -->|Fail| K[Deny: Permission Violation]
    J -->|Pass| L[Approve Delegation]
```

#### 4.5.2 Authorization Decision Factors

1. **Permission Check**: Verify the delegator has the appropriate delegation permission
2. **Hierarchy Validation**: Ensure the delegation follows hierarchical constraints
3. **Scope Validation**: Confirm the delegation is within appropriate scope boundaries
4. **Context Validation**: Check business context rules (e.g., qualifications, conflicts)
5. **Permission Validation**: Verify no non-delegable permissions are included

#### 4.5.3 Delegation Permission Structure

Delegation permissions follow the format `delegate:{direction}:{role}`, where:

- `delegate` is the resource
- `{direction}` defines the delegation direction relative to the hierarchy (same, down, up, manage)
- `{role}` specifies the target role for delegation (or `any` for multiple roles)

| Permission                    | Description                                     | Example                      |
| ----------------------------- | ----------------------------------------------- | ---------------------------- |
| `delegate:same:any`           | Delegate own role to someone with the same role | Team Leader to Team Leader   |
| `delegate:down:TEAM_LEADER`   | Delegate own role to a TEAM_LEADER              | Shift Leader to Team Leader  |
| `delegate:same:SHIFT_LEADER`  | Delegate own role to another SHIFT_LEADER       | Shift Leader to Shift Leader |
| `delegate:manage:TEAM_LEADER` | Manage delegations between TEAM_LEADER roles    | For supervisors              |
| `delegate:temporary:any`      | Create temporary delegations (limited duration) | For vacation coverage        |
| `delegate:partial:any`        | Create partial role delegations                 | For specific task coverage   |

#### 4.5.4 Permission Mapping Examples

| Scenario                                               | Required Permission            | Additional Validation                               |
| ------------------------------------------------------ | ------------------------------ | --------------------------------------------------- |
| Shift Leader delegates to another Shift Leader         | `delegate:same:SHIFT_LEADER`   | Validate shift scope                                |
| Shift Leader delegates to Team Leader                  | `delegate:down:TEAM_LEADER`    | Validate hierarchical relationship and shared scope |
| Plant Manager manages delegation between Shift Leaders | `delegate:manage:SHIFT_LEADER` | Validate supervisory relationship and shared scope  |
| Team Leader creates partial delegation to Team Leader  | `delegate:partial:TEAM_LEADER` | Validate partial permission set                     |

#### 4.5.5 Delegation Permission Assignment

Delegation permissions are assigned to roles based on organizational policies:

| Role                | Delegation Permissions                                                                                              |
| ------------------- | ------------------------------------------------------------------------------------------------------------------- |
| PLANT_MANAGER       | `delegate:same:PLANT_MANAGER`, `delegate:down:SHIFT_LEADER`, `delegate:manage:SHIFT_LEADER`, `delegate:partial:any` |
| SHIFT_LEADER        | `delegate:same:SHIFT_LEADER`, `delegate:down:TEAM_LEADER`, `delegate:manage:TEAM_LEADER`, `delegate:partial:any`    |
| TEAM_LEADER         | `delegate:same:TEAM_LEADER` (within shift), `delegate:temporary:OPERATOR`, `delegate:partial:TEAM_LEADER`           |
| TRAINER_RESPONSIBLE | `delegate:same:TRAINER_RESPONSIBLE`, `delegate:down:TRAINER`, `delegate:manage:TRAINER`, `delegate:partial:any`     |
| TRAINER             | `delegate:same:TRAINER` (within specialization), `delegate:partial:TRAINER`                                         |

### 4.6 Cross-Level and Same-Level Delegation

The framework supports both cross-level (hierarchical) and same-level (peer) delegations with appropriate controls.

#### 4.6.1 Same-Level Delegation (Peer Delegation)

Same-level delegation allows users with the same role to delegate authority to each other, typically within the same organizational scope:

```json
{
  "sameLevelDelegationRules": {
    "enforceSharedScope": true,
    "allowedScopeDifferences": ["team"],
    "requireExplicitPermission": true,
    "maxDuration": "30D",
    "requireApproval": true,
    "approvalRequiredFrom": ["delegatorSupervisor", "delegateeSupervisor"],
    "reasonRequired": true
  }
}
```

**Example: Team Leader to Team Leader**

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Coverage during scheduled training",
    "approvals": {
      "delegatorSupervisor": {
        "userId": "<EMAIL>",
        "status": "PENDING"
      },
      "delegateeSupervisor": {
        "userId": "<EMAIL>",
        "status": "PENDING"
      }
    },
    "restrictions": {
      "scope": {
        "site": "SITE_A",
        "department": "PRODUCTION",
        "shift": "MORNING",
        "team": "ASSEMBLY_1"
      },
      "permissions": {
        "included": ["team:view", "operator:view", "schedule:view"],
        "excluded": ["team:manage", "performance:review"]
      }
    }
  }
}
```

#### 4.6.2 Cross-Level Delegation (Hierarchical Delegation)

Cross-level delegation follows the organizational hierarchy, allowing delegation to flow downward with appropriate permissions:

```json
{
  "crossLevelDelegationRules": {
    "allowDirection": "downward",
    "maxLevelDistance": 1,
    "requireExplicitPermission": true,
    "enforceStrictScopeValidation": true,
    "maxDuration": "90D",
    "requireApproval": true,
    "approvalRequiredFrom": ["delegatorSupervisor", "delegateeSupervisor"],
    "reasonRequired": true
  }
}
```

**Example: Shift Leader to Team Leader**

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "SHIFT_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Leadership development opportunity",
    "approvals": {
      "delegatorSupervisor": {
        "userId": "<EMAIL>",
        "status": "APPROVED"
      },
      "delegateeSupervisor": {
        "userId": "<EMAIL>",
        "status": "APPROVED"
      }
    },
    "restrictions": {
      "scope": {
        "site": "SITE_A",
        "department": "PRODUCTION",
        "shift": "MORNING"
      },
      "permissions": {
        "included": ["team:manage", "schedule:view", "incidents:handle"],
        "excluded": ["performance:review", "budget:manage"]
      }
    }
  }
}
```

#### 4.6.3 Supervisor-Managed Delegation

Supervisors can manage delegations between their direct reports with the appropriate delegation management permission:

```json
{
  "managedDelegationRules": {
    "requireDirectSupervisor": true,
    "requireSharedScope": true,
    "allowDelegationOutsideTeam": false,
    "requireExplicitPermission": true,
    "notifyParticipants": true,
    "reasonRequired": true
  }
}
```

**Example: Shift Leader Managing Team Leader Delegations**

```json
{
  "managedDelegationRequest": {
    "managerId": "<EMAIL>",
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Coverage during scheduled training",
    "restrictions": {
      "scope": {
        "team": "ASSEMBLY_1"
      },
      "permissions": {
        "included": ["team:view", "schedule:view"],
        "excluded": ["performance:review"]
      }
    }
  }
}
```

#### 4.6.4 Partial Role Delegation

The system supports delegation of a subset of permissions within a role, allowing for fine-grained responsibility transfer:

```json
{
  "partialDelegationRules": {
    "allowPartialDelegation": true,
    "requireExplicitPermission": true,
    "minimumPermissionsRequired": 1,
    "restrictedPermissions": [
      "budget:approve",
      "performance:finalize",
      "admin:access"
    ],
    "organizationConfigurableRestrictions": true,
    "reasonRequired": true,
    "requireApproval": true
  }
}
```

**Example: Partial Team Leader Delegation**

```json
{
  "partialDelegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Need assistance with schedule management while focusing on quality issues",
    "approvals": {
      "delegatorSupervisor": {
        "userId": "<EMAIL>",
        "status": "APPROVED"
      }
    },
    "restrictions": {
      "scope": {
        "team": "ASSEMBLY_1"
      },
      "permissions": {
        "included": ["schedule:view", "schedule:manage", "schedule:approve"],
        "excluded": ["team:manage", "performance:review", "budget:request"]
      }
    }
  }
}
```

### 4.7 Advanced Delegation Scenarios

#### 4.7.1 Emergency Access

Emergency situations may require expedited access to critical functions, bypassing standard delegation procedures.

```mermaid
sequenceDiagram
    participant Requester
    participant API as API Backend
    participant Approver
    participant Delegate

    Requester->>API: Create Emergency Delegation Request
    API->>Approver: Send Approval Request (urgent)
    Approver->>API: Approve Emergency Request
    API->>API: Create Emergency Delegation & Profile
    API->>Delegate: Notify of Emergency Access
    API->>Requester: Confirm Emergency Delegation
```

**Emergency Delegation Configuration**

```json
{
  "emergencyDelegationRules": {
    "allowEmergencyDelegation": true,
    "allowedRequesters": ["PLANT_MANAGER", "SHIFT_LEADER", "SYSTEM_ADMIN"],
    "expeditedApproval": true,
    "maxDuration": "24H",
    "autoExpire": true,
    "enhancedLogging": true,
    "bypassScopeValidation": true,
    "requirePostIncidentReview": true,
    "notifySecurity": true
  }
}
```

**Emergency Access Controls**

1. Pre-defined emergency profiles with appropriate permissions
2. Short duration (typically 8-24 hours)
3. Enhanced monitoring and logging
4. Automatic notifications to security team
5. Post-incident review required
6. Clearly documented reason for emergency access

#### 4.7.2 Role Conflict Management

Some role combinations may create conflicts of interest and should be managed through the delegation framework.

**Conflict Resolution Strategies**

1. **Profile Separation**: Keep conflicting roles in separate profiles
2. **Scope Restrictions**: Limit scope where conflicting roles apply
3. **Operational Exclusions**: Remove specific permissions that create conflicts
4. **Approval Requirements**: Require additional approval for conflicting actions
5. **Enhanced Logging**: Increase audit detail for potential conflict scenarios

**Conflict Management Configuration**

```json
{
  "roleConflictRules": {
    "conflictingRolePairs": [
      {
        "roles": ["QUALITY_INSPECTOR", "PRODUCTION_MANAGER"],
        "resolutionStrategy": "SEPARATION"
      },
      {
        "roles": ["FINANCIAL_APPROVER", "BUDGET_REQUESTER"],
        "resolutionStrategy": "APPROVAL_REQUIRED"
      },
      {
        "roles": ["SYSTEM_ADMIN", "AUDITOR"],
        "resolutionStrategy": "OPERATIONAL_EXCLUSION"
      }
    ],
    "delegationBehavior": {
      "preventConflictingDelegations": true,
      "additionalApprovalRequired": true,
      "enhancedLoggingForConflicts": true
    }
  }
}
```

#### 4.7.3 Temporary Access Management

Short-term access needs can be handled through streamlined delegation with appropriate controls.

```mermaid
sequenceDiagram
    participant Manager
    participant API as API Backend
    participant Temporary as Temporary User

    Manager->>API: Create Temporary Access Request
    API->>API: Validate Manager Authority
    API->>API: Create Time-Limited Delegation
    API->>API: Create Restricted Profile
    API->>Temporary: Notify of Temporary Access
    API->>Manager: Confirm Temporary Access Creation
```

**Temporary Access Configuration**

```json
{
  "temporaryAccessRules": {
    "maxDuration": "30D",
    "requireJustification": true,
    "enforceRestrictions": true,
    "requireApproval": true,
    "automaticExpiration": true,
    "allowRenewal": true,
    "maxRenewals": 2,
    "enhancedLogging": true
  }
}
```

#### 4.7.4 Automatic Delegation (N+1 Substitution)

The system supports automatic delegation to a supervisor (N+1) when an employee (N) is absent, ensuring business continuity.

**Automatic Delegation Flow**

```mermaid
sequenceDiagram
    participant HRIS as HR System
    participant System as Delegation System
    participant Supervisor as N+1 Supervisor
    participant Alternate as Alternate Delegate

    HRIS->>System: Employee Absence Notification
    System->>System: Check Automatic Delegation Policy
    System->>System: Create Automatic Delegation to N+1
    System->>Supervisor: Notify of Automatic Delegation
    Note over Supervisor: Two options
    Supervisor->>System: Accept Delegation
    Supervisor->>System: Redirect Delegation
    System->>Alternate: Notify of Redirected Delegation
    Alternate->>System: Accept Delegation
    System->>System: Update Delegation Records
```

**Automatic Delegation Configuration**

```json
{
  "automaticDelegationRules": {
    "enabledForRoles": ["TEAM_LEADER", "SHIFT_LEADER", "QUALITY_INSPECTOR"],
    "triggerEvents": ["ABSENCE", "VACATION", "SICK_LEAVE"],
    "defaultDelegateLevel": "DIRECT_SUPERVISOR",
    "allowRedirection": true,
    "requireRedirectionApproval": false,
    "notifyHierarchy": true,
    "scope": "SAME_AS_ABSENT_USER",
    "permissions": {
      "strategy": "FULL_ROLE",
      "excludeRestrictedPermissions": true
    },
    "automaticExpiration": {
      "onUserReturn": true,
      "gracePeriod": "8H"
    }
  }
}
```

**Example: Automatic Delegation When Team Leader Is Absent**

```json
{
  "automaticDelegation": {
    "absentUserId": "<EMAIL>",
    "absentUserRole": "TEAM_LEADER",
    "delegateId": "<EMAIL>",
    "delegateRole": "SHIFT_LEADER",
    "delegationType": "AUTOMATIC",
    "reason": "Sick leave (automatic)",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": null,
    "expirationTrigger": "USER_RETURN",
    "scope": {
      "site": "SITE_A",
      "department": "PRODUCTION",
      "shift": "MORNING",
      "team": "ASSEMBLY_1"
    },
    "redirectionStatus": {
      "redirected": true,
      "redirectedTo": "<EMAIL>",
      "redirectionReason": "Higher priority tasks",
      "redirectionTime": "2025-04-01T08:30:00Z"
    }
  }
}
```

### 4.8 Approval Workflow

The delegation framework includes a comprehensive approval process for delegation requests, ensuring proper oversight and control.

#### 4.8.2 Required Approvers

The system supports multiple approval scenarios based on organizational requirements:

| Approval Type             | Description                                     | Required For                                |
| ------------------------- | ----------------------------------------------- | ------------------------------------------- |
| Delegator's Supervisor    | Direct manager of the delegating user           | Most delegation types                       |
| Delegatee's Supervisor    | Direct manager of the user receiving delegation | Cross-department delegations                |
| Department Head           | Head of affected department                     | Delegations affecting critical functions    |
| Role Administrator        | Administrator of the role being delegated       | High-security or critical roles             |
| Security Officer          | Security team member                            | Emergency access or sensitive system access |
| Multiple Level Management | Multiple levels of management                   | Executive role delegations                  |

#### 4.8.3 Approval Configuration

```json
{
  "approvalConfiguration": {
    "defaultRequiredApprovers": ["delegatorSupervisor"],
    "roleSpecificApprovals": {
      "PLANT_MANAGER": ["delegatorSupervisor", "securityOfficer"],
      "FINANCIAL_APPROVER": [
        "delegatorSupervisor",
        "financeHead",
        "delegateeSupervisor"
      ]
    },
    "crossDepartmentApprovals": [
      "delegatorSupervisor",
      "delegateeSupervisor",
      "departmentHead"
    ],
    "emergencyApprovals": ["securityOfficer"],
    "approvalTimeouts": {
      "standard": "72H",
      "emergency": "1H",
      "escalation": {
        "enabled": true,
        "escalateAfter": "24H",
        "escalateTo": "nextLevelManager"
      }
    },
    "delegationStartBehavior": {
      "requireAllApprovalsBeforeStart": true,
      "allowScheduledStartWithApprovals": true
    }
  }
}
```

#### 4.8.4 Approval Records

Each delegation retains detailed approval records to maintain audit trails:

```json
{
  "delegationApprovals": {
    "delegationId": "delegation-123",
    "status": "APPROVED",
    "approvalSteps": [
      {
        "approverType": "delegatorSupervisor",
        "approverId": "<EMAIL>",
        "status": "APPROVED",
        "timestamp": "2025-03-25T14:30:00Z",
        "comments": "Approved for training coverage"
      },
      {
        "approverType": "delegateeSupervisor",
        "approverId": "<EMAIL>",
        "status": "APPROVED",
        "timestamp": "2025-03-25T16:45:00Z",
        "comments": "Delegatee has required qualifications"
      }
    ],
    "finalApprovalDate": "2025-03-25T16:45:00Z",
    "delegationExecutionDate": "2025-04-01T00:00:00Z"
  }
}
```

### 4.9 Organization-Configurable Restrictions

The system allows organizations to define global delegation restrictions and configure which permissions can or cannot be delegated.

#### 4.9.1 Global Delegation Configuration

```json
{
  "organizationDelegationConfiguration": {
    "allowDelegation": true,
    "maxDelegationDuration": "90D",
    "enablePartialDelegation": true,
    "enableAutomaticDelegation": true,
    "enableEmergencyDelegation": true,
    "requireApprovals": true,
    "requireReason": true,
    "neverDelegatePermissions": [
      "user:delete",
      "role:create",
      "system:configure",
      "security:override"
    ],
    "restrictedDelegationPermissions": [
      "budget:approve:above:10000",
      "performance:finalize",
      "disciplinary:initiate"
    ],
    "delegationCreationRoles": [
      "SUPER_ADMIN",
      "PLANT_MANAGER",
      "SHIFT_LEADER",
      "TEAM_LEADER"
    ],
    "hierarchyRules": {
      "respectHierarchy": true,
      "maxLevelSkip": 1,
      "allowCrossDepartment": false,
      "allowCrossSite": false
    },
    "notificationRules": {
      "notifyDelegator": true,
      "notifyDelegatee": true,
      "notifyDelegatorSupervisor": true,
      "notifyDelegateeSupervisor": true,
      "notifySecurityTeam": ["EMERGENCY", "SENSITIVE_ROLE"]
    }
  }
}
```

#### 4.9.2 Role-Specific Delegation Restrictions

```json
{
  "roleDelegationRestrictions": {
    "PLANT_MANAGER": {
      "canBeDelegated": true,
      "requiresApproval": true,
      "requiredApprovers": ["superAdmin", "securityOfficer"],
      "maxDelegationDuration": "30D",
      "nonDelegablePermissions": [
        "budget:approve:above:50000",
        "employee:terminate",
        "facility:decommission"
      ],
      "allowedDelegateeRoles": ["PLANT_MANAGER", "ASSISTANT_PLANT_MANAGER"]
    },
    "FINANCIAL_APPROVER": {
      "canBeDelegated": true,
      "requiresApproval": true,
      "requiredApprovers": ["financeHead", "complianceOfficer"],
      "maxDelegationDuration": "14D",
      "nonDelegablePermissions": [
        "budget:approve:above:10000",
        "payment:authorize:above:5000"
      ],
      "allowedDelegateeRoles": ["FINANCIAL_APPROVER", "FINANCE_MANAGER"]
    }
  }
}
```

#### 4.9.3 Permission Delegation Matrix

The organization can define a detailed matrix of which permissions can be delegated by which roles:

| Permission             | SUPER_ADMIN | PLANT_MANAGER | SHIFT_LEADER | TEAM_LEADER | QUALITY_INSPECTOR |
| ---------------------- | :---------: | :-----------: | :----------: | :---------: | :---------------: |
| **Core Operations**    |             |               |              |             |                   |
| team:view              |  Delegate   |   Delegate    |   Delegate   |  Delegate   |     Delegate      |
| team:manage            |  Delegate   |   Delegate    |   Delegate   | No Delegate |    No Delegate    |
| schedule:view          |  Delegate   |   Delegate    |   Delegate   |  Delegate   |     Delegate      |
| schedule:manage        |  Delegate   |   Delegate    |   Delegate   |  Delegate   |    No Delegate    |
| **Critical Functions** |             |               |              |             |                   |
| budget:view            |  Delegate   |   Delegate    |   Delegate   | No Delegate |    No Delegate    |
| budget:approve         |  Delegate   |    Partial    | No Delegate  | No Delegate |    No Delegate    |
| performance:review     |  Delegate   |   Delegate    |   Partial    | No Delegate |    No Delegate    |
| **Administrative**     |             |               |              |             |                   |
| user:create            |   Partial   |  No Delegate  | No Delegate  | No Delegate |    No Delegate    |
| role:assign            |   Partial   |  No Delegate  | No Delegate  | No Delegate |    No Delegate    |
| system:configure       | No Delegate |  No Delegate  | No Delegate  | No Delegate |    No Delegate    |

Legend:

- **Delegate**: Can be fully delegated
- **Partial**: Can be delegated with restrictions
- **No Delegate**: Cannot be delegated

## 5. Microsoft Graph API Integration

This section details the integration between our authentication system and Microsoft Graph API, which enables real-time user provisioning, profile synchronization, and group-based role management with Microsoft Entra ID (formerly Azure AD).

### 5.1. Service Principal Configuration

The integration with Microsoft Graph API requires a properly configured service principal in Microsoft Entra ID with appropriate permissions and security settings.

#### 5.1.1. Required Permissions

| Permission           | Type        | Description                | Justification                                                                 |
| -------------------- | ----------- | -------------------------- | ----------------------------------------------------------------------------- |
| User.Read.All        | Application | Read all user profiles     | Required to fetch comprehensive user data during JIT provisioning and refresh |
| GroupMember.Read.All | Application | Read all group memberships | Required to map Azure AD groups to application roles                          |
| Directory.Read.All   | Application | Read directory data        | Required for advanced directory queries and delta operations                  |

#### 5.1.2. Authentication Methods

Two primary authentication methods are supported for the service principal:

```mermaid
graph TD
    A[Service Principal Authentication] --> B[Client Credentials Flow]
    A --> C[Certificate-Based Authentication]
    B --> D[Uses Client ID and Secret]
    C --> E[Uses Client ID and Certificate]
    D --> F[Lower Setup Complexity]
    E --> G[Higher Security]
```

**Client Credentials Implementation:**

```json
{
  "tenant_id": "your-tenant-id",
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "scope": "https://graph.microsoft.com/.default"
}
```

**Certificate-Based Implementation:**

```json
{
  "tenant_id": "your-tenant-id",
  "client_id": "your-client-id",
  "certificate_thumbprint": "certificate-thumbprint",
  "private_key": "reference-to-private-key",
  "scope": "https://graph.microsoft.com/.default"
}
```

#### 5.1.3. Security Best Practices

| Best Practice       | Implementation                                                          |
| ------------------- | ----------------------------------------------------------------------- |
| Least Privilege     | Assign only required permissions; avoid global admin roles              |
| Credential Rotation | Implement 90-day rotation for client secrets                            |
| Secure Storage      | Store credentials in a secure vault (Azure Key Vault, HashiCorp Vault)  |
| Access Auditing     | Enable audit logs for service principal activity                        |
| IP Restrictions     | Apply conditional access policies to restrict access to known IP ranges |

### 5.2. User Data Management

This section details how user profile data is retrieved, processed, and synchronized between Microsoft Entra ID and the application database.

#### 5.2.1. User Profile Retrieval

**Graph API Endpoint:**

```
GET https://graph.microsoft.com/v1.0/users/{userPrincipalName or id}
```

**Requested User Attributes:**

| Attribute         | Description         | Usage                            |
| ----------------- | ------------------- | -------------------------------- |
| displayName       | User's full name    | User profile display             |
| givenName         | First name          | Personalization                  |
| surname           | Last name           | User identification              |
| mail              | Primary email       | Communication, unique identifier |
| userPrincipalName | UPN                 | Alternative identifier           |
| jobTitle          | Position            | Role-based access control        |
| department        | Organizational unit | Departmental permissions         |
| officeLocation    | Physical location   | Regional settings                |
| employeeId        | HR identifier       | Integration with other systems   |
| id                | Object ID           | Graph API reference              |

**Synchronization Flow:**

```mermaid
sequenceDiagram
    participant App as Application
    participant Graph as Microsoft Graph API
    participant DB as Application Database

    App->>Graph: Request user profile data
    Graph->>App: Return user attributes
    App->>App: Transform data to application model
    App->>DB: Store/update user profile
    Note over App,DB: During each token refresh
```

#### 5.2.2. Just-In-Time (JIT) User Provisioning

The system implements Just-In-Time user provisioning to create user accounts automatically upon first login:

1. User authenticates with SAML SSO
2. System checks if user exists in the application database
3. If not found, retrieves comprehensive user profile from Graph API
4. Creates new user record with complete profile information
5. Maps Azure AD groups to application roles
6. Issues JWT tokens with user profile and role information

#### 5.2.3. Profile Synchronization Strategy

| Event                   | Synchronization Action                    |
| ----------------------- | ----------------------------------------- |
| First Authentication    | Full profile creation from Graph API      |
| Token Refresh           | Differential update of profile attributes |
| Group Membership Change | Update of roles and permissions           |
| Periodic Background Job | Full synchronization for inactive users   |

### 5.3. Group Membership Management

This section details how Azure AD group memberships are retrieved and mapped to application roles.

#### 5.3.1. Group Membership Retrieval

**Graph API Endpoint:**

```
GET https://graph.microsoft.com/v1.0/users/{id}/memberOf
```

**Optional Filtering:**

```
GET https://graph.microsoft.com/v1.0/users/{id}/memberOf?$filter=securityEnabled eq true
```

**Implementation Considerations:**

| Consideration    | Approach                                                 |
| ---------------- | -------------------------------------------------------- |
| Large Group Sets | Implement pagination handling for users with many groups |
| Nested Groups    | Process transitive memberships when required             |
| Response Caching | Cache group memberships with appropriate TTL             |
| Error Handling   | Implement fallback for group retrieval failures          |

#### 5.3.2. Group-to-Role Mapping Configuration

The system maintains a configuration mapping between Azure AD groups and application roles:

```json
{
  "group-mappings": [
    {
      "group-id": "11b0131d-43c9-4734-9673-f2bc7f14f5e2",
      "group-name": "Application Administrators",
      "roles": ["admin", "user"],
      "permissions": ["full_access"]
    },
    {
      "group-id": "a8294692-bda8-41d5-91c8-58e3c54c9a2b",
      "group-name": "Finance Department",
      "roles": ["finance", "user"],
      "permissions": ["finance_read", "finance_write"]
    },
    {
      "group-id": "d4c46c76-7a5e-4aed-9a1f-ab72d7a39aa9",
      "group-name": "HR Managers",
      "roles": ["hr_manager", "user"],
      "permissions": ["employee_read", "employee_write"]
    }
  ]
}
```

#### 5.3.3. Dynamic Role Assignment

The system dynamically assigns roles based on group membership during:

1. Initial JIT provisioning
2. Each token refresh operation
3. Periodic background synchronization

### 5.4. Delta Query Implementation

This section details how the system uses Microsoft Graph API's delta query functionality to efficiently track and apply changes from Microsoft Entra ID.

#### 5.4.1. Delta Query Fundamentals

The delta query feature allows the system to request only changes to users and groups since the last synchronization, significantly reducing bandwidth and processing requirements.

**Initial Delta Query:**

```
GET https://graph.microsoft.com/v1.0/users/delta
```

**Subsequent Delta Query using Delta Link:**

```
GET {deltaLink-from-previous-response}
```

#### 5.4.2. Tracking Changes

The system maintains delta tokens to track the state of synchronization:

| Entity Type   | Tracked Changes    | Delta Token Storage           |
| ------------- | ------------------ | ----------------------------- |
| Users         | Profile attributes | Database table with timestamp |
| Groups        | Membership changes | Database table with timestamp |
| Group members | Role assignments   | Database table with timestamp |

#### 5.4.3. Synchronization Process

```mermaid
sequenceDiagram
    participant Scheduler as Sync Scheduler
    participant Delta as Delta Service
    participant Graph as Microsoft Graph API
    participant DB as Database

    Scheduler->>Delta: Initiate sync cycle
    Delta->>DB: Retrieve latest delta token
    Delta->>Graph: Request changes using delta token
    Graph->>Delta: Return changed entities
    Delta->>DB: Process and apply changes
    Delta->>DB: Store new delta token
    Note over Delta,DB: Repeat on schedule or event
```

#### 5.4.4. Implementation Optimizations

| Optimization          | Description                                    | Benefit                             |
| --------------------- | ---------------------------------------------- | ----------------------------------- |
| Background Processing | Execute delta sync in background workers       | Improves application responsiveness |
| Selective Application | Apply only relevant changes                    | Reduces database load               |
| Change Batching       | Process changes in batches                     | Improves efficiency                 |
| Conflict Resolution   | Implement resolution for conflicting changes   | Maintains data integrity            |
| Webhook Triggers      | Trigger sync on directory change notifications | Ensures near real-time updates      |

#### 5.4.5. Error Handling and Resilience

| Error Scenario               | Handling Strategy                                     |
| ---------------------------- | ----------------------------------------------------- |
| Delta token expired          | Fall back to full synchronization                     |
| Temporary API unavailability | Implement exponential backoff retry                   |
| Rate limiting                | Adaptive throttling with prioritization               |
| Conflicting changes          | Apply business rules for resolution                   |
| Service degradation          | Circuit breaker pattern to prevent cascading failures |

## 6. Data Model

This section defines the data model for the authentication, authorization, and delegation microservice. The model is designed for simplicity, performance, and maintainability while supporting all required functionality.

### 6.1. Core Entities

The data model consists of essential entities that support the integrated authentication, authorization, and delegation framework:

The data model consists of the following core entities and their relationships:

1. **User**: Represents an authenticated user in the system with attributes synchronized from Microsoft Entra ID.

   - Key attributes: id, email, displayName, givenName, surname, jobTitle, department, employeeId, officeLocation, lastSyncTime, isActive

2. **Role**: Defines a set of permissions and position in the organizational hierarchy.

   - Key attributes: id, name, description, hierarchyLevel, parentRoles, childRoles, isSystem

3. **Permission**: Represents a specific action that can be performed on a resource.

   - Key attributes: id, resource, action, description

4. **Profile**: A container for role assignments that provides context for a user's permissions.

   - Key attributes: id, userId, name, description, type, isActive, validFrom, validUntil, metadata

5. **RoleAssignment**: Links profiles to roles with specific restrictions and source information.

   - Key attributes: id, profileId, roleId, source, delegationId, restrictions, assignedAt

6. **Delegation**: Records the temporary transfer of authority from one user to another.
   - Key attributes: id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil, restrictions, reason, approverIds, approvalStatus, createdAt, lastModifiedAt

Key entity relationships include:

- One User can have multiple Profiles
- Each Profile can contain multiple RoleAssignments
- Each RoleAssignment links to exactly one Role
- Roles can have parent-child relationships with other Roles
- Roles grant multiple Permissions
- Delegations connect a delegator User to a delegate User
- Each Delegation creates exactly one Profile

The model also includes several enumeration types:

- ProfileType (DIRECT, DELEGATED, SYSTEM)
- RoleSource (DIRECT_ASSIGNMENT, AZURE_AD_GROUP, DELEGATION, SYSTEM)
- DelegationStatus (PENDING, ACTIVE, EXPIRED, REVOKED)
- DelegationApprovalStatus (PENDING, APPROVED, REJECTED)

### 6.2. Database Tables

The microservice uses the following database tables to store authentication, authorization, and delegation data:

| Table Name      | Primary Purpose              | Key Fields                                                                                                                                                  |
| --------------- | ---------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Users           | Store user information       | id, email, displayName, givenName, surname, jobTitle, department, employeeId, lastSyncTime                                                                  |
| Roles           | Define available roles       | id, name, description, hierarchyLevel, parentRoles, childRoles, isSystem                                                                                    |
| Permissions     | Define available permissions | id, resource, action, description                                                                                                                           |
| RolePermissions | Map roles to permissions     | roleId, permissionId                                                                                                                                        |
| Profiles        | Store user profiles          | id, userId, name, description, type, isActive, validFrom, validUntil, metadata                                                                              |
| RoleAssignments | Assign roles to profiles     | id, profileId, roleId, source, delegationId, restrictions, assignedAt                                                                                       |
| Delegations     | Track delegations            | id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil, restrictions, reason, approverIds, approvalStatus, createdAt, lastModifiedAt |

#### Table Schemas

**Users Table**

```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    given_name VARCHAR(255),
    surname VARCHAR(255),
    job_title VARCHAR(255),
    department VARCHAR(255),
    employee_id VARCHAR(50),
    office_location VARCHAR(255),
    last_sync_time TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Roles Table**

```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    hierarchy_level INT NOT NULL,
    parent_roles JSON,
    child_roles JSON,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Permissions Table**

```sql
CREATE TABLE permissions (
    id VARCHAR(36) PRIMARY KEY,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(resource, action)
);
```

**RolePermissions Table**

```sql
CREATE TABLE role_permissions (
    role_id VARCHAR(36) NOT NULL,
    permission_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);
```

**Profiles Table**

```sql
CREATE TABLE profiles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- DIRECT, DELEGATED, SYSTEM
    is_active BOOLEAN DEFAULT FALSE,
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**RoleAssignments Table**

```sql
CREATE TABLE role_assignments (
    id VARCHAR(36) PRIMARY KEY,
    profile_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    source VARCHAR(20) NOT NULL, -- DIRECT_ASSIGNMENT, AZURE_AD_GROUP, DELEGATION, SYSTEM
    delegation_id VARCHAR(36),
    restrictions JSON,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (delegation_id) REFERENCES delegations(id) ON DELETE SET NULL
);
```

**Delegations Table**

```sql
CREATE TABLE delegations (
    id VARCHAR(36) PRIMARY KEY,
    delegator_id VARCHAR(36) NOT NULL,
    delegate_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    profile_id VARCHAR(36),
    status VARCHAR(20) NOT NULL, -- PENDING, ACTIVE, EXPIRED, REVOKED
    valid_from TIMESTAMP NOT NULL,
    valid_until TIMESTAMP NOT NULL,
    restrictions JSON,
    reason TEXT NOT NULL,
    approver_ids JSON,
    approval_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delegator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (delegate_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);
```

### 6.3. Key Relationships

The data model is structured around these core relationships:

1. **User → Profiles**: One user can have multiple profiles (direct, delegated, or system)

   - Enables context switching between different role sets
   - Supports multiple delegated profiles from different delegators

2. **Profile → RoleAssignments**: Each profile contains multiple role assignments

   - Clearly separates roles in different contexts
   - Supports different sources for role assignments (direct, Azure AD group-derived, delegation)

3. **RoleAssignment → Role**: Role assignments link profiles to specific roles

   - Includes source tracking for auditing
   - Contains scope and restriction information

4. **Role → Permissions**: Roles aggregate multiple permissions

   - Many-to-many relationship via RolePermissions table
   - Enables flexible permission management

5. **Role → Role**: Roles maintain hierarchical relationships

   - Parent-child relationships stored as JSON arrays
   - Supports organizational hierarchy alignment

6. **Delegation → User**: Tracks both delegator and delegate

   - Records the user who delegated authority
   - Records the user receiving delegated authority

7. **Delegation → Profile**: Delegations create delegated profiles
   - One-to-one relationship between delegation and profile
   - Profile inherits restrictions from delegation

**Microservice Data Flow**

```mermaid
flowchart TD
    A[Authentication\nRequest] --> B[JWT\nToken Service]
    B --> C{User\nExists?}
    C -->|No| D[Create\nUser]
    C -->|Yes| E[Get User\nProfiles]
    D --> E
    E --> F[Load Active\nProfile]
    F --> G[Include Role\nAssignments]
    G --> H[Generate\nJWT]
    H --> I[Response with\nToken]

    J[Delegation\nRequest] --> K[Delegation\nService]
    K --> L{Validate\nPermissions}
    L -->|Invalid| M[Reject\nRequest]
    L -->|Valid| N[Create\nDelegation]
    N --> O[Create Delegated\nProfile]
    O --> P[Add Role\nAssignments]
    P --> Q[Notify\nDelegate]
```

**Group-to-Role Mapping (in Configuration)**

Rather than storing Azure AD group mappings in the database, the microservice uses a configuration file that maps Azure AD groups to application roles:

```json
{
  "groupMappings": [
    {
      "azureAdGroupId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "displayName": "Plant Managers",
      "applicationRole": "PLANT_MANAGER",
      "autoAssign": true,
      "scopeAttributes": ["site"]
    },
    {
      "azureAdGroupId": "b2c3d4e5-f6a7-8901-bcde-f12345678901",
      "displayName": "Quality Inspectors",
      "applicationRole": "QUALITY_INSPECTOR",
      "autoAssign": true,
      "scopeAttributes": ["department", "site"]
    }
  ]
}
```

This configuration is loaded when the microservice starts and is used during user synchronization with Microsoft Graph API to assign appropriate roles based on Azure AD group memberships.

## 7. Conclusion

This Authentication, Authorization, and Delegation Guide provides a comprehensive framework for implementing a secure, scalable, and flexible identity and access management solution for enterprise applications. By integrating SAML-based Single Sign-On with JWT authentication and Microsoft Graph API synchronization, the system delivers a robust foundation for modern applications with complex organizational structures.

### 7.1. Key Architecture Benefits

The integrated approach described in this document offers several significant advantages:

1. **Seamless User Experience**

   - Single authentication event with persistent session management
   - Profile-based context switching without re-authentication
   - Reduced authentication redirects and interruptions

2. **Enterprise-Grade Security**

   - Centralized identity management through Microsoft Entra ID
   - Secure token handling with appropriate storage strategies
   - Comprehensive certificate and key management
   - Fine-grained permission control with role-based access

3. **Operational Flexibility**

   - Just-In-Time user provisioning from authoritative source
   - Real-time synchronization of user attributes and group memberships
   - Delegation framework supporting business continuity
   - Support for complex organizational hierarchies

4. **Reduced Administrative Overhead**

   - Automated user provisioning and deprovisioning
   - Group-based role assignment through Azure AD
   - Self-service delegation with appropriate controls
   - Streamlined profile and role management

5. **Technical Resilience**
   - Stateless architecture supporting horizontal scaling
   - Fault tolerance through token-based authentication
   - Optimized Graph API integration with delta queries
   - Comprehensive error handling and recovery strategies

### 7.2. Implementation Strategy

To successfully implement this architecture, we recommend a phased approach:

1. **Foundation Phase**

   - Configure Azure AD for SAML authentication
   - Implement JWT token service with refresh capabilities
   - Establish basic role and permission framework
   - Create core database schema

2. **Integration Phase**

   - Implement Microsoft Graph API synchronization
   - Develop group-to-role mapping functionality
   - Build profile management capabilities
   - Create basic authorization middleware

3. **Advanced Features Phase**

   - Implement delegation framework
   - Add profile switching functionality
   - Develop hierarchical role management
   - Create audit and monitoring systems

4. **Optimization Phase**
   - Implement performance optimizations
   - Enhance security features
   - Add advanced error handling
   - Create comprehensive monitoring and alerting

### 7.3. Success Criteria

The implementation will be considered successful when it achieves:

1. **Security Objectives**

   - Zero unauthorized access events
   - Complete audit trail of all authentication and authorization events
   - Secure handling of all credentials and tokens

2. **Performance Targets**

   - Authentication response time under 500ms
   - Token refresh operations under 200ms
   - Authorization checks under 50ms
   - Profile switching under 300ms

3. **User Experience Goals**

   - Seamless authentication flow
   - Intuitive delegation interface
   - Simple profile management
   - Transparent authorization behavior

4. **Business Outcomes**
   - Reduced administrative overhead
   - Improved security posture
   - Enhanced operational flexibility
   - Simplified compliance management

By following this guide, organizations can implement a modern, secure, and user-friendly authentication and authorization system that meets the complex needs of enterprise applications while maintaining seamless integration with Microsoft identity platforms.
