# Low-Level Design Document: [Architecture for the Transport Management ]

## Document Information

**Version:** [1.1.0]  
**Last Updated:** [02/05/2025]  
**Status:** [In Progress]  
**Authors: <AUTHORS>

## Executive Summary

### Key Features
-  Microservices-based architecture with three core services
- Event-driven communication using Azure Service Bus
- Configurable business rules and validation
- Multi-country support with country-specific variations
- Real-time data synchronization and monitoring
- Role-based access control and security

### Business Benefits
-  Scalable and maintainable transport management system
- Flexible configuration for different country requirements
- Improved operational efficiency through automation
- Enhanced data security and compliance
- Real-time monitoring and reporting capabilities

### Document Focus
- This document provides detailed technical specifications for the Transport Management module, including architecture, domain models, database design.

## Table of Contents
1. [Overview](#1-overview)
   
2. [Architecture Overview](#2-system-architecture-overview)
  
3. [Core components](#3-core-components)
   1. [Domain Model Design](#3-domain-model-design)
   - [Bounded Contexts](#31-bounded-contexts)
   - [Aggregates and Entities](#32-aggregates-entities-and-value-objects)
      - [Pickup Station Context](#321-pickup-station-context)
      - [Transport Planning Context](#322-transport-planning-context)
      - [Bus Boarding Context](#313-bus-boarding-context)
4. [Architecture .Net](#4-architecture)
      - [Architecture en .NET](#41-architecture-en-net)
      - [Solution Overview](#42-solution-overview)
      - [Detailed Description of Solution](#43-detailed-description-of-solution)
      - [Key Features of the Solution](#44-key-features-of-the-solution)
      - [How the Solution Works](#45-how-the-solution-works)
      - [Design Patterns Used](#46-design-patterns-used)
     
   3. [Database Design](#5-database-design)
   4. [Event-Driven Architecture](#6-event-driven-architecture)
   5. [Security Design .Net 9](#7-security-design-net-9)
      - [Authentication and Authorization](#71-authentication-and-authorization)  
      - [Secure Communication](#72-secure-communication)
      - [Security Design Diagram](#73-security-design-diagram)
5. [Morocco VS EMEA/NA:](#8-morocco-vs-emea-na) 
     
6.  [Conclusion](#9-conclusion)
# 1. Overview

## 1.1 Purpose and Scope

This low-level design document outlines the detailed architecture for the Transport Management module based on Domain-Driven Design (DDD) principles and microservices architecture. The design prioritizes configurability, flexibility.

## 1.2 Key Components

The system consists of three primary microservices:

1. Pickup Station Service
2. Transport Planning Service
3. Bus Boarding Service

Each service is supported by:

- Azure CosmosDB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Configuration Service for centralized settings

## 2. Architecture Overview

## 2.1 High-Level Architecture
The system follows a microservices architecture with the following components:

```mermaid
flowchart TD
    ApiGateway[Azure API Management] --> PickupStation[Pickup Station Service]
    ApiGateway --> TransportPlanning[Transport Planning Service]
    ApiGateway --> BusBoarding[Bus Boarding Service]

    PickupStation --> StationDB[(Pickup Station CosmosDB)]
    TransportPlanning --> PlanningDB[(Transport Planning CosmosDB)]
    BusBoarding --> BoardingDB[(Bus Boarding CosmosDB)]

    PickupStation <--> EventBus[Azure Service Bus]
    TransportPlanning <--> EventBus
    BusBoarding <--> EventBus

    EventBus -->|StationCreated, StationUpdated| TransportPlanning
    EventBus -->|PlanCreated, PlanUpdated| BusBoarding

    style ApiGateway fill:#b3d9ff,stroke:#333,stroke-width:2px
    style EventBus fill:#ffcccb,stroke:#333,stroke-width:2px
    style PickupStation fill:#d5f5e3,stroke:#333,stroke-width:2px
    style TransportPlanning fill:#d5f5e3,stroke:#333,stroke-width:2px
    style BusBoarding fill:#d5f5e3,stroke:#333,stroke-width:2px
    style StationDB fill:#f9e79f,stroke:#333,stroke-width:2px
    style PlanningDB fill:#f9e79f,stroke:#333,stroke-width:2px
    style BoardingDB fill:#f9e79f,stroke:#333,stroke-width:2px
```
    
#### 2.1.1 Explanation of components

1. **API Gateway (Azure API Management)** :
         - Serves as a single point of entry for customers.
         - Manages routing, authentication, and rate limiting.

2. **Pickup Station Service** :
         - Manages the collection stations.
         -Stores data in **Pickup Station CosmosDB**.
         - Publishes and consumes events via **Azure Service Bus**.

3. **Transport Planning Service** :
   - Plans routes and assignments.
   - Stores data in **Transport Planning CosmosDB**.
   - Publishes and consumes events via **Azure Service Bus**.

4. **Bus Boarding Service** :
   - Tracks employee boardings.
   - Stores data in **Bus Boarding CosmosDB**.
   - Publishes and consumes events via **Azure Service Bus**.

5. **Azure Service Bus** :
   - Facilitates asynchronous communication between microservices.
   -Manages events like station updates, schedule changes, etc..

6. **Bases de données CosmosDB** :
   - Each microservice has its own database to respect the principle of data separation.
   - CosmosDB is used for its scalability, low latency, and multi-region support.

## 2.1.2 Description of possible actions for each microservice
 Here is a description of the possible actions for each microservice, based on the data provided. This will help to better structure the **High-Level Architecture**.

---

### ********* Pickup Station Service**
#### **Responsibilities :**
- Manage collection stations (creation, update, deletion).
- Fournir les informations sur les stations disponibles.
- Publier des événements lorsqu'une station est ajoutée ou modifiée.

#### **Exemples d'actions :**
- **API REST** :
  - `GET /stations`: Récupérer la liste des stations.
  - `POST /stations`: Ajouter une nouvelle station.
  - `PUT /stations/{id}`: Mettre à jour une station existante.
  - `DELETE /stations/{id}`: Supprimer une station.
- **Événements publiés** :
  - `StationCreated`
  - `StationUpdated`
  - `StationDeleted`

---

### ********* Transport Planning Service**
#### **Responsabilités :**
- Planifier les itinéraires et les affectations des véhicules.
- Optimiser les trajets en fonction des données des stations et des besoins.
- Publier des événements liés aux changements de planification.

#### **Exemples d'actions :**
- **API REST** :
  - `GET /plans`: Récupérer les plans de transport.
  - `POST /plans`: Créer un nouveau plan de transport.
  - `PUT /plans/{id}`: Mettre à jour un plan existant.
  - `DELETE /plans/{id}`: Supprimer un plan.
- **Événements publiés** :
  - `PlanCreated`
  - `PlanUpdated`
  - `PlanDeleted`

---

### **2.1.2.3 Bus Boarding Service**
#### **Responsabilités :**
- Suivre les embarquements des employés dans les bus.
- Gérer les validations des embarquements (par exemple, via des QR codes ou des badges).
- Publier des événements liés aux embarquements.

#### **Exemples d'actions :**
- **API REST** :
  - `GET /boardings`: Récupérer les données d'embarquement.
  - `POST /boardings`: Enregistrer un nouvel embarquement.
  - `PUT /boardings/{id}`: Mettre à jour les informations d'un embarquement.
  - `DELETE /boardings/{id}`: Supprimer un enregistrement d'embarquement.
- **Événements publiés** :
  - `BoardingRegistered`
  - `BoardingUpdated`
  - `BoardingDeleted`

### **2.1.3 Communication entre les microservices**
- **Pickup Station Service** publie des événements (`StationCreated`, `StationUpdated`) consommés par **Transport Planning Service** pour mettre à jour les plans.
- **Transport Planning Service** publie des événements (`PlanCreated`, `PlanUpdated`) consommés par **Bus Boarding Service** pour ajuster les embarquements.
- Tous les microservices utilisent **Azure Service Bus** pour la communication asynchrone.

## 2.2 Key Technical Components

1. **.Net 9 best platform for Microservices**: Core business logic implementation
2. **CosmosDB**: NoSQL database with multi-region support
3. **Azure Service Bus**: Event-driven communication between services
4. **Change Feed Processor**: Real-time data synchronization
5. **Azure API Management**: API Gateway that provides a unified entry point for all client applications, handling routing, authentication, and rate limiting
6. **Configuration Service**: Centralized configuration management for client-specific settings
7. **Design Patterns**:
Design Patterns are proven solutions to common software design problems. Design Patterns help you write code that is more flexible, adaptable, and easier.
- **Mediator** :
The Mediator pattern defines an object that encapsulates how a set of objects interact. This pattern promotes loose coupling by keeping objects from referring to each other explicitly, and it lets you vary their interaction independently.
- **Singleton** :
The Singleton pattern ensures that a class has only one instance and provides a global point of access to it.
- **CQRS** :
(Command Query Responsibility Segregation) separates read and write operations to improve performance
- **Dependency injection** :
is a design pattern, not just a programming technique ,that makes a class independent of its dependencies.

## 3. Domain Model Design

## 3.1 Bounded Contexts

The system is divided into three primary bounded contexts:

#### 3.1.1 Pickup Station Management Context

- Core domain responsible for station creation, management, and assignment
- Handles address change requests from Team Leaders

#### 3.1.2 Transport Planning Context

- Manages shift assignments and working plans
- Handles minibus allocation and route planning

#### 3.1.3 Bus Boarding Context

- Manages the bus boarding process
- Tracks employee presence and status changes

## 3.2 Aggregates, Entities, and Value Objects

#### 3.2.1 Pickup Station Context

**Class Diagram:**  

```mermaid
graph TD

    Utilisateur["Utilisateur<br>+id: int<br>+nom: string<br>+role: string"]
    Demande["Demande<br>+id: int<br>+typeDemande: string<br>+status: string<br>+dateCreation: datetime"]
    Notification["Notification<br>+id: int<br>+message: string<br>+date: datetime"]
    Role["Role<br>+id: int<br>+nomRole: string<br>+description: string"]
    Station["Station<br>+id: int<br>+nom: string<br>+type: string<br>+localisation: string"]
    Adresse["Adresse<br>+id: int<br>+adresse: string<br>+historiqueModifications: string"]

    Utilisateur -->|crée ou valide| Demande
    Demande -->|assigne| Station
    Demande -->|concerne| Adresse
    Adresse -->|modifie| Station
    Utilisateur -->|reçoit| Notification
    Notification -->|envoyée à| Utilisateur
    Utilisateur -->|a| Role
```
**Entites**

    class Utilisateur {
        +id: int
        +nom: string
        +role: string
    }

    class Demande {
        +id: int
        +typeDemande: string
        +status: string
        +dateCreation: datetime
    }

    class Notification {
        +id: int
        +message: string
        +date: datetime
    }

    class Role {
        +id: int
        +nomRole: string
        +description: string
    }

    class Station {
        +id: int
        +nom: string
        +type: string
        +localisation: string
    }

    class Adresse {
        +id: int
        +adresse: string
        +historiqueModifications: string
    }

    Utilisateur "1" --> "0..*" Demande : "crée ou valide"
    Demande "1" --> "1" Station : "assigne"
    Demande "1" --> "1" Adresse : "concerne"
    Adresse "1" --> "1" Station : "modifie"
    Utilisateur "1" --> "0..*" Notification : "reçoit"
    Notification "1" --> "1" Utilisateur : "envoyée à"
    Utilisateur "1" --> "1" Role : "a"


#### Explications des relations :
**Utilisateur** :
- Can create or validate multiple requests.
- Can receive multiple notifications.
- Is associated with a single Role.
**Demande** :
- A Request may concern the creation or modification of a Station or the modification of an Address.

**Adresse** :
- Can modify a Station, (Transport Agent) can assign a Station to another.

**Notification** :
- Is sent to a User after changes are made to Stations or Addresses.

**Role** :
- Defines the roles associated with a User (Team Leader, Transport Agent, etc.) determine the actions a User can perform.

**Aggregates** :

 An Aggregate is a logical grouping of entities and Value Objects that form a coherent unit of data and business rules..

- **Utilisateur Aggregate** :

Root Entity : **Utilisateur**
Entities :
**Role**,
**Notification**

**Description** :
The User Aggregate manages user information, their roles, and the notifications they receive.
Aggregate Request:

Root Entity : **Demande**

Entities :
**Station**,
**Adresse**,
**Description** : The Request aggregate handles requests related to the creation or modification of stations and addresses.

- **Station Aggregate** :

Root Entity : **Station**

**Entities :**

**Adresse**

Description : L'aggregate Station gère les informations des stations et leurs adresses associées.
**Entities**
Une Entity est un objet métier avec une identité unique.

**Utilisateur** :

**Attributs** :
id: int
nom: string
role: string
Description : Représente un utilisateur du système.

**Role** :

**Attributs** :
id: int
nomRole: string
description: string
Description : Définit les rôles associés à un utilisateur (par exemple, Team Leader, Transport Agent).

**Demande** :  
   **Attributs** :\
   id: int\
   typeDemande: string\
   status: string\
   dateCreation: datetime\
   Description : Représente une demande de création ou modification d'une station ou d'une adresse.

**Station** :

**Attributs** :
id: int
nom: string
type: string
localisation: string
Description : Représente une station de ramassage.
**Notification** :

**Attributs** :
id: int
message: string
date: datetime
Description : Représente une notification envoyée à un utilisateur.
**Adresse** :

**Attributs** :
id: int
adresse: string
historiqueModifications: string
Description : Represents an address associated with a station.
**Value Objects**
A Value Object is an object without its own identity, used to encapsulate business concepts.

**Localisation (dans Station)** :

**Attributs** :
latitude: float
longitude: float
Description : Represents the geographic location of a station.
**HistoriqueModifications (dans Adresse)** :

**Attributs** :
modifications: List<string>
Description : List of changes made to an address.
### **sequence Diagram Route Creation Flow**-

```mermaid
sequenceDiagram
    participant Utilisateur
    participant Demande
    participant Station
    participant Adresse
    participant Notification
    participant Role

    Utilisateur->>Demande: Crée ou valide une demande
    Demande->>Station: Associe une station
    Demande->>Adresse: Concerne une adresse
    Adresse->>Station: Modifie une station
    Utilisateur->>Notification: Reçoit une notification
    Notification->>Utilisateur: Envoyée à l'utilisateur
    Utilisateur->>Role: Est associé à un rôle
```

#### **3.2.2 Transport Planning Context**


**Class Diagram:**

```mermaid
flowchart TD
    WorkPlan[Work Plan<br>+id: String<br>+date: Date<br>+shift: String<br>+status: String<br>+employees: List<Employee>]
    User[User<br>+id: String<br>+name: String<br>+role: String<br>+email: String]
    Shift[Shift<br>+id: String<br>+type: String<br>+startTime: Time<br>+endTime: Time<br>+employees: List<Employee>]
    PickupStation[Pickup Station<br>+id: String<br>+code: String<br>+name: String<br>+zone: String<br>+schedule: String]
    Employee[Employee<br>+id: String<br>+registrationNumber: String<br>+name: String<br>+status: String<br>+shift: Shift]
    Transport[Transport<br>+id: String<br>+minibusId: String<br>+route: Route<br>+departureTime: Time<br>+capacity: int]
    Route[Route<br>+id: String<br>+startStation: Pickup Station<br>+endStation: Pickup Station<br>+schedule: String<br>+minibusId: String]
    TransportProvider[Transport Provider<br>+id: String<br>+name: String<br>+contact: String<br>+routeSheet: File]
    TourTracking[Tour Tracking<br>+id: String<br>+busId: String<br>+employeeStatus: String<br>+geolocation: String<br>+arrivalTime: Time]
    EmployeeStatus[Employee Status<br>+id: String<br>+statusType: String<br>+employeeId: String<br>+updatedBy: User<br>+timestamp: DateTime]

    WorkPlan -->|Includes| Employee
    WorkPlan -->|Associated with| Shift
    Employee -->|Assigned to| Shift
    Employee -->|Assigned to| PickupStation
    Transport -->|Follows| Route
    Transport -->|Managed by| TransportProvider
    Route -->|Contains| PickupStation
    TourTracking -->|Tracks| Transport
    TourTracking -->|Monitors| EmployeeStatus
    EmployeeStatus -->|Belongs to| Employee
    EmployeeStatus -->|Updated by| User
```
**Entites**

classDiagram

    class WorkPlan {
        +id: String
        +date: Date
        +shift: String
        +status: String
        +employees: List<Employee>
    }

    class User {
        +id: String
        +name: String
        +role: String
        +email: String
    }

    class Shift {
        +id: String
        +type: String
        +startTime: Time
        +endTime: Time
        +employees: List<Employee>
    }

    class PickupStation {
        +id: String
        +code: String
        +name: String
        +zone: String
        +schedule: String
    }

    class Employee {
        +id: String
        +registrationNumber: String
        +name: String
        +status: String
        +shift: Shift
    }

    class Transport {
        +id: String
        +minibusId: String
        +route: Route
        +departureTime: Time
        +capacity: int
    }

    class Route {
        +id: String
        +startStation: PickupStation
        +endStation: PickupStation
        +schedule: String
        +minibusId: String
    }

    class TransportProvider {
        +id: String
        +name: String
        +contact: String
        +routeSheet: File
    }

    class TourTracking {
        +id: String
        +busId: String
        +employeeStatus: String
        +geolocation: String
        +arrivalTime: Time
    }

    class EmployeeStatus {
        +id: String
        +statusType: String
        +employeeId: String
        +updatedBy: User
        +timestamp: DateTime
    }

    WorkPlan "1" --> "0..*" Employee : "includes"
    WorkPlan "1" --> "1..*" Shift : "associated with"
    Employee "1" --> "1" Shift : "assigned to"
    Employee "1" --> "1" PickupStation : "assigned to"
    Transport "1" --> "1" Route : "follows"
    Transport "1" --> "1" TransportProvider : "managed by"
    Route "1" --> "1..*" PickupStation : "contains"
    TourTracking "1" --> "1" Transport : "tracks"
    TourTracking "1" --> "1" EmployeeStatus : "monitors"
    EmployeeStatus "1" --> "1" Employee : "belongs to"
    EmployeeStatus "1" --> "1" User : "updated by"


#### Explications des relations :
•	Plan de Travail (Work Plan) est associé à Employés (les employés affectés à chaque plan) et à des Quarts de Travail (Shift).
•	Employé est assigné à un Shift et une Station de Ramassage.
•	Transport est lié à un Itinéraire et à un Fournisseur (Transport Provider) qui gère le bus.
•	Itinéraire contient plusieurs Stations de Ramassage et est suivi par le Transport Agent.
•	Statut des Employés est mis à jour par le Team Leader et Transport Agent en fonction des informations en temps réel (par exemple, si un employé est présent, absent, ou non planifié).
•	Suivi des Tours de Transport (Tour Tracking) est lié aux Transport (minibus) et suit l'état en temps réel des Employés (statut de présence).

**Aggregates** :

 An Aggregate is a logical grouping of entities and Value Objects that form a coherent unit of data and business rules..

- **Utilisateur Aggregate** :
Voici une proposition des **Aggregates** basés sur les entités et relations décrites dans votre document :


### **Aggregates**

### ***1. User Aggregate***
- **Root Entity**: `User`
**Entities:**
- `Role`
- `Notification`
- **Description**:
The `User` aggregate manages user information, their roles, and the notifications they receive.

### ***2. Request Aggregate***
- **Root Entity**: `Request`
**Entities:**
- `Station`
- `Address`
- **Description**:
The `Request` aggregate handles requests related to the creation or modification of stations and addresses.

### ***3. Station Aggregate***
- **Root Entity**: `Station`
**Entities:**
- `Address`
- **Description**:
The `Station` aggregate manages station information and their associated addresses.

### ***4. Transport Aggregate***
- **Root Entity**: `Transport`
**Entities:**
- `Route`
- `TransportProvider`
- **Description**:
The `Transport` aggregate manages routes, transport providers, and related trip information.

### ***5. WorkPlan Aggregate***
- **Root Entity**: `WorkPlan`
**Entities:**
- `Shift`
- `Employee`
- **Description**:
The `WorkPlan` aggregate manages work plans, shifts, and assigned employees.

Summary of Aggregates

| **Aggregate** | **Root Entity** | **Entities**            | **Description**                                                        |
|---------------|-----------------|--------------------------|------------------------------------------------------------------------|
| **User**      | `User`          | `Role`, `Notification`  | Manages users, their roles, and notifications.                        |
| **Request**   | `Request`       | `Station`, `Address`    | Handles requests for creating or modifying stations and addresses.    |
| **Station**   | `Station`       | `Address`               | Manages station information and their associated addresses.           |
| **Transport** | `Transport`     | `Route`, `TransportProvider` | Manages routes and transport providers.                               |
| **WorkPlan**  | `WorkPlan`      | `Shift`, `Employee`     | Manages work plans, shifts, and assigned employees.                   |


**Explanation of Relationships**

**WorkPlan**:  
Associated with employees (assigned to each plan) and shifts.  
**Employee**:  
Assigned to a shift and a pickup station.  
**Transport**:  
Linked to a route and a transport provider managing the bus.  
**Route**:  
Contains multiple pickup stations and is monitored by the transport agent.  
**Employee Status**:  
Updated in real-time by the team leader and transport agent (e.g., present, absent, or unplanned).  
**Tour Tracking**:  
Tracks transport (minibus) and monitors the real-time status of employees.
If you need further details or modifications, feel free to ask!
### **Sequence Diagram Route Transport Planning**

```mermaid
sequenceDiagram
    participant User
    participant Role
    participant Notification
    participant Request
    participant Station
    participant Address
    participant WorkPlan
    participant Shift
    participant Employee
    participant Transport
    participant Route
    participant TransportProvider
    participant TourTracking
    participant EmployeeStatus

    User->>Role: Assigns a role to the user
    User->>Notification: Receives notifications
    Request->>Station: Creates or modifies a station
    Request->>Address: Creates or modifies an address
    Station->>Address: Associates an address with a station
    WorkPlan->>Employee: Assigns employees to the work plan
    WorkPlan->>Shift: Associates shifts with the work plan
    Employee->>Shift: Assigned to a shift
    Employee->>Station: Assigned to a pickup station
    Transport->>Route: Linked to a route
    Transport->>TransportProvider: Managed by a transport provider
    Route->>Station: Contains multiple pickup stations
    TourTracking->>Transport: Tracks the minibus
    TourTracking->>EmployeeStatus: Monitors the real-time status of employees
    EmployeeStatus->>User: Updated by the team leader or transport agent
```

## 3.1.3 Bus Boarding Context

**Class Diagram:**

```mermaid
classDiagram
    class Operateur {
        -id: String
        -nomComplet: String
        -badgePerdu: Boolean
        -planifie: Boolean
    }

    class Embarquement {
        -dateHeure: DateTime
        -statut: String
        -typeAcces: String
    }

    class Terminal {
        -id: String
        -emplacement: String
    }

    class Statut {
        -typeStatut: String
        -horodatage: DateTime
    }

    class Bus {
        -id: String
        -capaciteMax: int
        -gpsPosition: String
    }

    class Trajet {
        -id: String
        -heureDepart: DateTime
        -heureArrivee: DateTime
        -placesLibres: int
        -itineraire: String
    }

    class Responsable {
        -id: String
        -nom: String
    }

    class OptimisationService {
        +calculerItineraire(): void
        +optimiserPlaces(): void
    }

    class NotificationSystem {
        +envoyerNotification(): void
    }

    Operateur "1" --> "0..*" Embarquement
    Embarquement "1" --> "1" Terminal
    Embarquement "1" --> "1" Statut
    Embarquement "1" --> "1" Bus
    Trajet "1" --> "1..*" Bus
    Trajet "1" --> "1" Responsable
    Trajet "1" --> "1" OptimisationService : "optimisé par"
    NotificationSystem "1" --> "1" Statut : "utilise"
```    

---
**Enities**  
classDiagram
    class Operator {
        -id: String
        -fullName: String
        -badgeLost: Boolean
        -scheduled: Boolean
    }

    class Boarding {
        -dateTime: DateTime
        -status: String
        -accessType: String
    }

    class Terminal {
        -id: String
        -location: String
    }

    class Status {
        -statusType: String
        -timestamp: DateTime
    }

    class Bus {
        -id: String
        -maxCapacity: int
        -gpsPosition: String
    }

    class Route {
        -id: String
        -departureTime: DateTime
        -arrivalTime: DateTime
        -availableSeats: int
        -itinerary: String
    }

    class Responsible {
        -id: String
        -name: String
    }

    class OptimizationService {
        +calculateItinerary(): void
        +optimizeSeats(): void
    }

    class NotificationSystem {
        +sendNotification(): void
    }

    Operator "1" --> "0..*" Boarding
    Boarding "1" --> "1" Terminal
    Boarding "1" --> "1" Status
    Boarding "1" --> "1" Bus
    Route "1" --> "1..*" Bus
    Route "1" --> "1" Responsible
    Route "1" --> "1" OptimizationService : "optimized by"
    NotificationSystem "1" --> "1" Status : "uses"

### Explanation of Relationships:
1. **Operateur**:
   - Can manage multiple **Embarquements**.
2. **Embarquement**:
   - Is associated with a **Terminal**, a **Statut**, and a **Bus**.
3. **Trajet**:
   - Includes multiple **Buses** and is managed by a **Responsable**.
   - Is optimized by the **OptimisationService**.
4. **NotificationSystem**:
   - Sends notifications based on the **Statut**.

**Aggregates** :
Here is the requested **Aggregate** description 

---

### **1. User Aggregate**
- **Root Entity**: `User`
- **Entities**:
  - `Role`
  - `Notification`
- **Description**:  
  The `User` aggregate manages user information, their roles, and the notifications they receive.

---

### **2. Request Aggregate**
- **Root Entity**: `Request`
- **Entities**:
  - `Station`
  - `Address`
- **Description**:  
  The `Request` aggregate handles requests related to the creation or modification of stations and addresses.

---

### **3. Station Aggregate**
- **Root Entity**: `Station`
- **Entities**:
  - `Address`
- **Description**:  
  The `Station` aggregate manages station information and their associated addresses.

---

### **4. Transport Aggregate**
- **Root Entity**: `Transport`
- **Entities**:
  - `Route`
  - `TransportProvider`
- **Description**:  
  The `Transport` aggregate manages routes, transport providers, and related trip information.

---

### **5. WorkPlan Aggregate**
- **Root Entity**: `WorkPlan`
- **Entities**:
  - `Shift`
  - `Employee`
- **Description**:  
  The `WorkPlan` aggregate manages work plans, shifts, and assigned employees.

---

Here is the **Summary of Aggregates** in a structured table format:

---

### **Summary of Aggregates**

| **Aggregate**       | **Root Entity** | **Entities**                     | **Description**                                                        |
|----------------------|-----------------|-----------------------------------|------------------------------------------------------------------------|
| **User**            | `User`          | `Role`, `Notification`           | Manages user information, their roles, and the notifications they receive. |
| **Request**         | `Request`       | `Station`, `Address`             | Handles requests for creating or modifying stations and addresses.     |
| **Station**         | `Station`       | `Address`                        | Manages station information and their associated addresses.            |
| **Transport**       | `Transport`     | `Route`, `TransportProvider`     | Manages routes, transport providers, and related trip information.     |
| **WorkPlan**        | `WorkPlan`      | `Shift`, `Employee`              | Manages work plans, shifts, and assigned employees.                    |

### **Sequence Diagram Route Bus Boarding**
```mermaid
sequenceDiagram
    participant Operator
    participant Boarding
    participant Terminal
    participant Status
    participant Bus
    participant Route
    participant Responsible
    participant OptimizationService
    participant NotificationSystem

    Operator->>Boarding: Plans a boarding
    Boarding->>Terminal: Associates a terminal
    Boarding->>Status: Updates the status
    Boarding->>Bus: Assigns a bus
    Route->>Bus: Adds a bus to the route
    Route->>Responsible: Assigns a responsible person
    Route->>OptimizationService: Optimizes the itinerary
    NotificationSystem->>Status: Sends a notification based on the status
```



## 4. Architecture 
1.	API Gateway (Azure API . Management):
- Acts as a single-entry point for all client requests.
- Handles routing, authentication, and rate limiting. 
2.	 Pickup Station Service:
-  Manages collection stations (creation, update, deletion).
-  Publishes events like StationCreated and StationUpdated. 
3.	Transport Planning Service
-	Plans route and assigns vehicles.
-	Consumes station-related events and publishes PlanCreated and PlanUpdated. 
4.	Bus Boarding Service:
-	Tracks employee boardings and manages validations.
-	Consumes transport plan events. 
5.	Azure Service Bus:
- Facilitates asynchronous communication between microservices.
- Manages events like StationCreated, PlanUpdated, etc.
6.	 CosmosDB:
-	Each microservice has its own database for data separation.
-	Provides scalability, low latency, and multi-region suppor


## 4.3 clean Architecture of Each microservice 
for the implementation of microservices we will use clean architecture
Key Principles of Clean Architecture
1.	Independence:
o	The business logic (core) should not depend on external frameworks, databases, or UI.
o	Changes in external systems should not affect the core logic.
2.	Separation of Concerns:
o	Divide the system into layers, each with a specific responsibility.
3.	Dependency Rule:
o	Dependencies should always point inward, toward the core business logic.
4.	Testability:
o	The architecture makes it easy to test the core business logic without relying on external systems.
**Sequence**
```mermaid
sequenceDiagram
    participant Controller as Controller (Interface Adapter)
    participant UseCase as Use Case
    participant Entity as Entity
    participant Repository as Repository (Framework/Driver)

    Controller->>UseCase: Receives request and forwards it
    UseCase->>Entity: Applies business rules
    UseCase->>Repository: Fetches or updates data
    Repository->>UseCase: Returns data
    UseCase->>Controller: Returns response
```

### 4.4. Key Features of the Solution 
##
 1. Microservices Architecture:
    - Each service is independent and manages its own domain.
    - Services communicate asynchronously using Azure Service Bus.
2. Domain-Driven Design (DDD):

   - The solution follows DDD principles, with clear separation of concerns:
     - Domain Layer: Contains core business logic and entities.
    - Application Layer: Handles use cases and orchestrates business logic.
    - Infrastructure Layer: Manages data persistence and external integrations.
3. Event-Driven Communication:
    - Services publish and consume events via Azure Service Bus to ensure loose coupling.
4. Scalability:
    - Each service can be scaled independently based on load.
5. CosmosDB Integration:
    - Each service has its own CosmosDB database for data separation and scalability.
   ##

### 4.5. **How the Solution Works**
1. Client Interaction:
   - The client sends requests to the API Gateway.
   - The gateway routes the requests to the appropriate microservice.
2. Pickup Station Management:
   - The Pickup Station Service handles CRUD operations for pickup stations.
   - Publishes events like StationCreated to notify other services.
3. Transport Planning:
   - The Transport Planning Service consumes station-related events and updates transport plans.
   - Publishes events like PlanCreated for downstream services.
4. Bus Boarding:
   - The Bus Boarding Service tracks employee boardings and validates them.
   - Consumes transport plan events to adjust boarding processes.
   ##
### 4.6. Design Patterns Used
1. Repository Pattern:
   - Used in the PickupStationRepository to abstract data access logic.
2. Dependency Injection:
  - Configured in Program.cs to manage service dependencies.
3. CQRS (Command Query Responsibility Segregation):
  - Likely used to separate read and write operations for better scalability.
4. Event Sourcing:
  - Events like StationCreated and PlanCreated are published to maintain a history of changes.
  ## 5. Database Design
 ### 5.1 CosmosDB Container Design
- Each microservice has its own Cosmos DB database with containers specific to its domain. The system incorporates all necessary data within these containers, including data that would typically come from external systems.
## 6. Event-Driven Architecture

### 6.1 Event Schema

```typescript
interface DomainEvent {
  id: string;
  type: string;
  timestamp: Date;
  correlationId: string;
  data: any;
  metadata: {
    producer: string;
    version: string;
  };
}
```

### 6.2 Key Events and Event Flow

1. **Station Management Events:**

   - `StationCreated`
   - `StationUpdated`
   - `StationAssigned`
   - `AddressChangeRequested`
   - `AddressChangeApproved`

2. **Transport Planning Events:**

   - `TransportPlanCreated`
   - `TransportPlanUpdated`
   - `TransportPlanPublished`
   - `ShiftAssignmentAdded`
   - `ShiftAssignmentRemoved`
   - `MinibusAllocationUpdated`

3. **Bus Boarding Events:**
   - `PassengerBoarded`
   - `BoardingStatusChanged`

### 6.3 Event Flow Diagram

```mermaid
flowchart LR
    PickupService[Pickup Station Service] --> |produces| Events1[StationCreated\nStationUpdated\nStationAssigned\nAddressChangeRequested\nAddressChangeApproved]
    TransportService[Transport Planning Service] --> |produces| Events2[TransportPlanCreated\nTransportPlanPublished\nShiftAssignmentAdded\nMinibusAllocationUpdated]
    BoardingService[Bus Boarding Service] --> |produces| Events3[PassengerBoarded\nBoardingStatusChanged]

    Events1 --> ServiceBus{Azure Service Bus}
    Events2 --> ServiceBus
    Events3 --> ServiceBus

    ServiceBus --> |consumes| PickupService
    ServiceBus --> |consumes| TransportService
    ServiceBus --> |consumes| BoardingService

    subgraph "Event Topics"
        ServiceBus
    end
```

**6.4 Change Feed**
A Change Feed is a change diffusion mechanism that allows you to react in real time to data insertions, updates, or deletions in a database. It is very useful for microservices architectures because it allows you to efficiently synchronize or propagate events between services
Key Features of Change Feed
1.	Real-Time Data Processing:
o	Automatically captures changes (inserts and updates) in the order they occur.
o	Enables downstream systems to react to changes in near real-time.
2.	Event-Driven Architecture:
o	Acts as a source of truth for triggering events based on data changes.
3.	Scalability:
o	Works seamlessly with Azure Functions, Azure Event Hubs, or custom consumers for distributed processing.
4.	Use Cases:
o	Data Synchronization: Sync data between Cosmos DB and other systems (e.g., SQL, Blob Storage).
o	Event Sourcing: Trigger business logic when data changes.
o	Analytics: Stream data to analytics platforms for real-time insights.
**Sequence Diagram for Change Feed**
```mermaid
sequenceDiagram
    participant CosmosDB as Azure Cosmos DB
    participant ChangeFeed as Change Feed
    participant Processor as Change Feed Processor
    participant Downstream as Downstream System

    Note over CosmosDB: Data is inserted or updated
    CosmosDB->>ChangeFeed: Capture changes (inserts/updates)
    ChangeFeed->>Processor: Push changes to the processor
    Processor->>Downstream: Process and forward changes
    Downstream->>Processor: Acknowledge processing
```
**5.5 localization geospatial et GeoJSON dans Azure Cosmos DB**
Azure Cosmos DB for NoSQL supports geospatial data and the GeoJSON format, allowing you to store, query, and analyze location data directly in the database. This is particularly useful for applications that require location-aware capabilities, such as real-time tracking, route planning, or finding points of interest

1. What is GeoJSON?
GeoJSON is a standard format based on JSON for representing geospatial data. It is used to describe geographic objects such as:

**Points**: Represent a single coordinate (latitude, longitude).
**LineStrings**: Represent paths or routes.
**Polygons***: Represent areas or regions.
```c#
Example of GeoJSON:

{
  "type": "Point",
  "coordinates": [45.123, -93.456]
}

2. Geospatial Features in Azure Cosmos DB
Azure Cosmos DB supports geospatial data through GeoJSON types and provides features to query this data.

Supported Geospatial Data Types:
Point: A single coordinate (latitude, longitude).
LineString: A series of connected points representing a path.
Polygon: An area defined by multiple connected points.
Example of a GeoJSON Document in Cosmos DB:

{
  "id": "1",
  "name": "Pickup Station",
  "location": {
    "type": "Point",
    "coordinates": [45.123, -93.456]
  }
}

3. Geospatial Indexing
Azure Cosmos DB automatically indexes geospatial data, enabling fast and efficient queries on this data.

Example of Geospatial Indexing:
Indexing is enabled by default for properties containing GeoJSON data. You can configure indexing in the indexing policy file.

4. Geospatial Queries
Azure Cosmos DB supports advanced geospatial queries to retrieve location-based data.

Examples of Geospatial Queries:
1. Find points near a location:

SELECT * FROM c
WHERE ST_DISTANCE(c.location, { "type": "Point", "coordinates": [45.123, -93.456] }) < 1000

-This query finds all points within 1,000 meters of a given location.

2. Find points within an area (polygon):
```javascript
SELECT * FROM c
WHERE ST_WITHIN(c.location, {
    "type": "Polygon",
    "coordinates": [
        [[45.0, -93.0], [46.0, -93.0], [46.0, -94.0], [45.0, -94.0], [45.0, -93.0]]
    ]
})
---

- This query finds all points within a defined polygon.
3. Calculate the distance between two points:

```javascript
SELECT ST_DISTANCE(c.location, { "type": "Point", "coordinates": [45.123, -93.456] }) AS distance
FROM c

- This query calculates the distance between a stored point and a given location.
5. Use Cases
 1. Real-Time Tracking:

- Track vehicles or employees in real-time by storing their GPS coordinates in Cosmos DB.
2. Route Planning:

- Use geospatial data to optimize routes based on pickup stations and destinations.
3. Proximity Search:

- Find the nearest pickup stations or points of interest to a given location.
4. Geospatial Analysis:

- Identify areas with high activity density or optimize station locations.
6. Benefits of Using Cosmos DB for Geospatial Data
1. High Performance:

 - Geospatial queries are fast due to automatic indexing.
2. Scalability:

- Cosmos DB can handle large volumes of geospatial data with low latency.
3. Multi-Region Availability:

- Data is available across multiple regions, making it ideal for global applications.
4. Easy Integration:

- Cosmos DB integrates seamlessly with other Azure services like Azure Maps, Azure Functions, and Azure Event Hubs.

7. Changes related to GPS tracking
7.1 Integration of GPS coordinates:
o	Add a field to store real-time GPS coordinates.
Exemple 

o	Exemple 
 {
  "incidentReport": {
    "date": "2025-05-01",
    "description": "Panne moteur",
    "resolved": false
  }
}
---

o		Real-time update:
-	Configure a mechanism to receive and update the bus's GPS k in the database (e.g., via Azure Cosmos DB).

7.2. Changes related to the itinerary
o	Change of route:
	Allow the route assigned to the bus to be modified in the event of a change in planning.
	Example d'API REST :
PUT /buses/{id}/route : Update the bus route
o	Adding intermediate stations:
	Add or remove pickup stations on the route.


7.3. Optimization-related changes
•	Optimisation des trajets :
o	Intégrer un service d'optimisation pour ajuster les itinéraires en fonction des conditions de trafic ou des besoins.
•	Calcul de la capacité :
o	Ajouter un algorithme pour optimiser l'utilisation des sièges disponibles.
7.4. Modifications dans les APIs
•	Nouvelles APIs à implémenter :
o	GET /buses/{id} : Retrieve information
Detailed information about a bus
o	PUT /buses/{id} : Update bus information.

o	GET /buses/{id}/gps : Retrieve real-time GPS position



## 6. Security Design .Net 9
The Transport Management Solution incorporates a robust security design to ensure data protection, secure communication, and controlled access across all microservices.   
## 6.1. Authentication and Authorization
**Authorization**
  - OAuth 2.0 and OpenID Connect:
    - The solution uses OAuth 2.0 for secure authentication.
    - Azure Active Directory (AAD) is integrated to manage user identities and provide Single Sign-On (SSO).
    - Tokens (e.g., JWT) are issued to clients after successful authentication.
**Authorization**
  - Role-Based Access Control (RBAC):
    - Each user is assigned a role (e.g., Admin, Transport Agent, Team Leader).
    - Access to APIs and resources is restricted based on roles.
    - Roles are managed centrally in Azure Active Directory.
  - Claims-Based Authorization:
    - Claims in the JWT token are used to enforce fine-grained access control.
    - Example: A Team Leader can create or modify pickup stations, while a Transport Agent can only view them.
**Permissions** 
    1. Team Leader
        •	Pickup Station Management:
        	Create, update, and delete pickup stations.
        	Assign employees to pickup stations.
        •	Work Plan Management:
        o	Create and update work plans.
        o	Assign shifts to employees.
        •	Employee Status:
        o	Update employee statuses (e.g., present, absent, unplanned).
        •	Notifications:
        o	Receive notifications related to station updates and employee statuses.
    2. Transport Agent
        •	Pickup Station Management:
        o	View pickup stations.
        •	Work Plan Management:
        o	View work plans and assigned shifts.
        •	Employee Status:
        o	View employee statuses.
        •	Boarding Management:
        o	Validate employee boardings (e.g., via QR codes or badges).

    3. Admin
        •	Global Management:
        o	Full access to all system features.
        o	Manage users, roles, and permissions.
        •	Pickup Station Management:
        o	Create, update, delete, and view all pickup stations.
        •	Transport Planning:
        o	Create, update, delete, and view transport plans.
        •	Boarding Management:
        o	View and manage all boarding records.
        •	System Configuration:
        o	Manage system settings and configurations.

    4. HR Manager
        •	Employee Management:
        o	Add, update, and delete employee records.
        o	Assign employees to shifts and work plans.
        •	Workday Integration:
        o	Synchronize employee data with Workday.
        •	Notifications:
        o	Receive notifications related to employee updates.
    5. Transport Provider
        •	Transport Management:
        o	View assigned routes and schedules.
        o	Update transport availability and capacity.
        •	Notifications:
        o	Receive notifications related to route changes or updates.

    6. System User (Read-Only)
        •	View Access:
        o	View pickup stations, transport plans, and boarding records.
        o	No permissions to create, update, or delete data.

### 7.2. Secure Communication
**Transport Layer Security (TLS)**
  - All communication between clients and the API Gateway, as well as between microservices, is encrypted using TLS 1.2/1.3.
  - Ensures data confidentiality and integrity during transmission.

**API Gateway Security**
  - The Azure API Management gateway enforces:
    - HTTPS-only traffic.
    - IP whitelisting to restrict access to trusted networks.
    - Rate limiting to prevent abuse and Distributed Denial of Service (DDoS) attacks.
### 7.3. Security Design Diagram
```mermaid
flowchart TD
    Client[Client Application] -->|OAuth 2.0| ApiGateway[Azure API Management]
    ApiGateway -->|JWT Validation| PickupStation[Pickup Station Service]
    ApiGateway -->|JWT Validation| TransportPlanning[Transport Planning Service]
    ApiGateway -->|JWT Validation| BusBoarding[Bus Boarding Service]

    PickupStation -->|Managed Identity| StationDB[(Pickup Station CosmosDB)]
    TransportPlanning -->|Managed Identity| PlanningDB[(Transport Planning CosmosDB)]
    BusBoarding -->|Managed Identity| BoardingDB[(Bus Boarding CosmosDB)]

    ApiGateway --> WAF[Web Application Firewall]
    WAF --> DDoSProtection[Azure DDoS Protection]

    subgraph Security
        WAF
        DDoSProtection
        AzureAD[Azure Active Directory]
        ServiceBus[Azure Service Bus]
    end
```
## 8. Morocco VS EMEA NA

8.1. **Address / Pickup Point Update**

Impacted User Stories:

US201: Weekly planning based on addresses.
US205: Communication of the pickup station.

| **Country**         | **Current Process**                                      | **Impact on the System**                                                                 |
|----------------------|----------------------------------------------------------|------------------------------------------------------------------------------------------|
| 🇷🇸 **Serbia**       | Transport manager manually prepares the plan from the HR database. | - Limited automation. <br> - Requires a simple planning system based on static data.     |
| 🇹🇳 **Tunisia**      | Data collected via email; manual consolidation process.  | - The system must allow centralized data entry. <br> - Reduce dependency on email/Excel. |
| 🇹🇷 **Turkey**       | Semi-automated system with AI + human validation.        | - Possible alignment with dynamic optimization user stories. <br> - Advanced use case to prioritize in implementation. |
|  **North America** | Transporters provide occupancy rates; fixed schedules.   | - Less flexibility. <br> - The system should primarily be used for reporting and occupancy tracking rather than dynamic planning. |

 North America	Address managed in Workday, which is the source of truth.	- The system must integrate Workday as the primary source for addresses. <br> - Dependency on an external database.

 8.2. **Generation and Validation of the Transport Plan**
Impacted User Stories:

US201: Generation of the plan.
US204: Calculation of the number of minibuses.
US206: Route optimization.

| **Country**         | **Current Process**                                      | **Impact on the System**                                                                 |
|----------------------|----------------------------------------------------------|------------------------------------------------------------------------------------------|
| 🇷🇸 **Serbia**       | Transport manager manually prepares the plan from the HR database. | - Limited automation. <br> - Requires a simple planning system based on static data.     |
| 🇹🇳 **Tunisia**      | Data collected via email; manual consolidation process.  | - The system must allow centralized data entry. <br> - Reduce dependency on email/Excel. |
| 🇹🇷 **Turkey**       | Semi-automated system with AI + human validation.        | - Possible alignment with dynamic optimization user stories. <br> - Advanced use case to prioritize in implementation. |
|  **North America** | Transporters provide occupancy rates; fixed schedules.   | - Less flexibility. <br> - The system should primarily be used for reporting and occupancy tracking rather than dynamic planning. |

8.3. **Boarding and Bus Tracking**
Impacted User Stories:

US202: GPS data / real-time tracking.
US203: Who is on board?
US101 – US108: Boarding process.

| **Country**         | **Current Process**                                      | **Impact on the System**                                                                 |
|----------------------|----------------------------------------------------------|------------------------------------------------------------------------------------------|
| 🇹🇷 **Turkey**       | QR code boarding system under development.               | - The system must integrate QR scanning with the bus and the list of planned operators. <br> - Ensure mobile/terminal compatibility. |
| **Other Countries** (Serbia, Tunisia, NA) | No real-time tracking or scanning system in place.         | - High added value to deploy a common boarding system (badge/QR code). <br> - Enable real-time presence tracking. |

8.4. **Architecture and Integration**

| **Country**         | **Current Process**                                      | **Impact on System**                                                                 |
|----------------------|----------------------------------------------------------|--------------------------------------------------------------------------------------|
| 🇷🇸 **Serbia** & 🇹🇳 **Tunisia** | Heavy reliance on paper/Excel processes.                     | - Need to unify workflows into a single application.                                |
| 🇹🇷 **Turkey**       | Advanced local architecture (MEMOT, AI).                 | - Ensure interoperability with MEMOT.                                               |
|  **North America** | Workday as the central HR system.                        | - System must synchronize with Workday instead of replacing it.                     |


System Impact Summary

| **Country**         | **Address Update**       | **Planning**       | **Optimization** | **Boarding (QR/Scan)** | **Integration**       |
|----------------------|--------------------------|---------------------|-------------------|------------------------|-----------------------|
| 🇷🇸 **Serbia**       | Manual + ID card         | HR + Transport      | Basic             | No                    | HR → Transport       |
| 🇹🇳 **Tunisia**      | Document + validation    | Emails + Excel      | Basic             | No                    | HR → Transport       |
| 🇹🇷 **Turkey**       | Automated + AI           | AI + MEMOT          | Advanced          | In progress (QR)      | MEMOT local          |
| 🌎 **North America** | Workday central          | Supplier            | None (fixed)      | No                    | Workday     
         |
**Sequence Diagram**

```mermaid
sequenceDiagram
    participant SerbiaTunisia as 🇷🇸 Serbia & 🇹🇳 Tunisia
    participant Turkey as 🇹🇷 Turkey
    participant NorthAmerica as 🌎 North America
    participant System as Transport Management System

    SerbiaTunisia->>System: Submit manual workflows (paper/Excel)
    System->>SerbiaTunisia: Unify workflows into a single application

    Turkey->>System: Use MEMOT and AI for local architecture
    System->>Turkey: Ensure interoperability with MEMOT

    NorthAmerica->>System: Provide HR data from Workday
    System->>NorthAmerica: Synchronize with Workday as the source of truth
```
## 9 **Conclusion**

This low-level design document provides a comprehensive blueprint for implementing the Transport Management Module using Domain-Driven Design (DDD) principles and a microservices architecture. The design emphasizes the following key aspects:

1. **Modularity**: 
    - Clear separation of concerns across bounded contexts ensures that each microservice is focused on a specific domain, making the system easier to understand and maintain.
2. **Configurability**:
    - The architecture supports dynamic configurations to meet country-specific requirements, ensuring flexibility for diverse client needs.
3. **Scalability** : 
    - The event-driven architecture, combined with independent scaling of microservices, ensures that the system can handle varying loads efficiently.
4. **Maintainability** : 
    - Clean domain models, encapsulated business logic, and adherence to design patterns like CQRS and Event Sourcing make the system easier to maintain and extend.
5. **Adaptability** : 
    - The extensible design allows for the seamless addition of new features and services to accommodate future requirements.

6. **Security** : 
    - Robust security measures, including OAuth 2.0, RBAC, TLS encryption, and Azure DDoS Protection, ensure data protection and secure communication across all microservices.
6. **Reliability**:
    - The use of Azure Service Bus for asynchronous communication and CosmosDB for scalable data storage ensures high availability and fault tolerance.

**Final Thoughts** :
   - This design aligns with industry best practices for cloud-native applications, ensuring reliability, performance, and security. It provides a flexible and scalable foundation to meet the specific requirements of clients across different regions while maintaining a focus on maintainability and adaptability for future growth

Explications des relations
Serbia & Tunisia :

Processus actuel : Forte dépendance aux processus papier/Excel.
Impact : Besoin d'unifier les flux de travail dans une application unique.
Turkey :

Processus actuel : Architecture locale avancée (MEMOT, IA).
Impact : Assurer l'interopérabilité avec MEMOT.
North America :

Processus actuel : Workday est le système central RH.
Impact : Le système doit se synchroniser avec Workday au lieu de le remplacer.