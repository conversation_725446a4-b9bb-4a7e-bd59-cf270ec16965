# Low-Level Design Document: [Architecture for the OverTime Management ]

## Document Information

**Version:** [1.1.0]  
**Last Updated:** [09/05/2025]  
**Status:** [In Progress]  
**Authors: <AUTHORS>

## Executive Summary

### Key Features
- Microservices-based architecture with three core services
- Event-driven communication using Azure Service Bus
- Configurable business rules and validation
- Multi-country support with country-specific variations
- Real-time data synchronization and monitoring
- Role-based access control and security

### Business Benefits
- Scalable and maintainable Overtime management system
- Flexible configuration for different country requirements
- Improved operational efficiency through automation
- Enhanced data security and compliance
- Real-time monitoring and reporting capabilities

### Document Focus
- This document provides detailed technical specifications for the OverTime Management module, including architecture, domain models, database design.

## Table of Contents
- [Low-Level Design Document: \[Architecture for the OverTime Management \]](#low-level-design-document-architecture-for-the-overtime-management-)
  - [Document Information](#document-information)
  - [Executive Summary](#executive-summary)
    - [Key Features](#key-features)
    - [Business Benefits](#business-benefits)
    - [Document Focus](#document-focus)
  - [Table of Contents](#table-of-contents)
- [1. Overview](#1-overview)
  - [1.1 Purpose and Scope](#11-purpose-and-scope)
  - [1.2 Functional architecture](#12-functional-architecture)
  - [1.2.1 Key modules](#121-key-modules)
  - [1.2.2 Explanation of the Diagram](#122-explanation-of-the-diagram)
  - [1.2.3 Actors and roles](#123-actors-and-roles)
  - [1.3 Key Components](#13-key-components)
  - [2. System Architecture Overview](#2-system-architecture-overview)
  - [2.1 High-Level Architecture](#21-high-level-architecture)
  - [2.1.1 Explanation of the Diagram](#211-explanation-of-the-diagram)
  - [2.1.2 Description of possible actions for each microservice](#212-description-of-possible-actions-for-each-microservice)
    - [\*\******** OvertimeRequest Service \*\*](#2121-overtimerequest-service-)
      - [**Responsabilités :**](#responsabilités-)
    - [**Examples of Actions**](#examples-of-actions)
      - [**API Actions**](#api-actions)
    - [**Event-Driven Actions**](#event-driven-actions)
    - [**Validation and Business Logic**](#validation-and-business-logic)
    - [**Use Cases**](#use-cases)
    - [********* Approval Workflow Service**](#2122-approval-workflow-service)
      - [**Responsabilités :**](#responsabilités--1)
    - [**Examples of Actions**](#examples-of-actions-1)
      - [**API Actions**](#api-actions-1)
    - [**Event-Driven Actions**](#event-driven-actions-1)
      - [**Validation and Business Logic**](#validation-and-business-logic-1)
    - [**Use Cases**](#use-cases-1)
    - [******* Shift Management Service](#2123-shift-management-service)
      - [**Responsibilities:**](#responsibilities)
    - [\***API Actions**](#api-actions-2)
    - [**Event-Driven Actions**](#event-driven-actions-2)
    - [**Validation and Business Logic**](#validation-and-business-logic-2)
    - [**Use Cases**](#use-cases-2)
    - [********* TKS Integration Service**](#2124-tks-integration-service)
      - [**Responsibilities:**](#responsibilities-1)
      - [**API Actions**](#api-actions-3)
    - [**Event-Driven Actions**](#event-driven-actions-3)
    - [**Validation and Business Logic**](#validation-and-business-logic-3)
    - [**Use Cases**](#use-cases-3)
  - [3. Domain Model Design](#3-domain-model-design)
  - [3.1 Bounded Contexts](#31-bounded-contexts)
    - [**3.1.1 Overtime Request Context**](#311-overtime-request-context)
    - [\*\* Sequence Diagram\*\*](#-sequence-diagram)
    - [**Explanation of the Sequence**](#explanation-of-the-sequence)
    - [**3.1.2 Approval Workflow Context**](#312-approval-workflow-context)
    - [**3.1.3 Shift Management Context**](#313-shift-management-context)
    - [- **Responsibilities:**](#--responsibilities)
    - [\*\* Sequence Diagram\*\*](#-sequence-diagram-1)
    - [**Explanation of the Sequence**](#explanation-of-the-sequence-1)
    - [**3.1.4 TKS Integration Context**](#314-tks-integration-context)
    - [Summary of Bounded Contexts](#summary-of-bounded-contexts)
    - [**3.1.4 Notification Service:**](#314-notification-service)
    - [**Class Diagram**](#class-diagram)
    - [\*\* Sequence Diagram\*\*](#-sequence-diagram-2)
    - [**3.1.5 TKS Integration Service**](#315-tks-integration-service)
    - [\*\* Sequence Diagram\*\*](#-sequence-diagram-3)
    - [**Benefits of Bounded Contexts**](#benefits-of-bounded-contexts)
    - [**Benefits of Bounded Contexts**](#benefits-of-bounded-contexts-1)
  - [4. Architecture](#4-architecture)
    - [**Ends points**](#ends-points)
# 1. Overview

## 1.1 Purpose and Scope

This low-level design details the technical architecture and functional components of the overtime management module.based on Domain-Driven Design (DDD) principles and microservices architecture.
The design prioritizes configurability, flexibility. 
The system aims to implement complete digitalization via TKS, the TL tablet, and Optitime. It covers all types of personnel: DH, IH, IS.

## 1.2 Functional architecture
## 1.2.1 Key modules

```mermaid
flowchart TD
    Soumission["Soumission OT Planifié<br>Input of overtime requests with employee selection, project, activity, etc."]
    Validation["Validation OT<br>Hierarchical validation chain (Coordinator → Department Manager → Project Manager) with possible modifications"]
    Execution["Exécution OT<br>Marking and closing of shifts with indicators (e.g., X, Y, W)"]
    CrossCheck["Cross-check OT<br>Comparison between planned and executed hours"]
    Reporting["Reporting & Historique<br>Generation of reports (PDF/Excel) and complete audit history"]

    Soumission --> Validation
    Validation --> Execution
    Execution --> CrossCheck
    CrossCheck --> Reporting
```

## 1.2.2 Explanation of the Diagram

1. Soumission OT Planifié:

    - Input of overtime requests with employee selection, project, activity, etc.
2. Validation OT:

- Hierarchical validation chain (Coordinator → Department Manager → Project Manager) with possible modifications.
3. Exécution OT:

- Marking and closing of shifts with indicators (e.g., X, Y, W).
4. Cross-check OT:

- Comparison between planned and executed hours.
5. Reporting & Historique:

- Generation of reports (PDF/Excel) and complete audit history.
## 1.2.3 Actors and roles
```mermaid
flowchart TD
    TeamLeader[Team Leader<br>Saisit les demandes DH, clôture les shifts]
    Clerk[Clerk<br>Saisie OT IH/IS, mise à jour du système]
    ShiftLeader[Shift Leader<br>Valide OT exécuté]
    Coordinator[Coordinator<br>Valide/modifie les OT]
    DepartmentManager[Department Manager<br>Modère les demandes avant envoi au PM]
    PlantManager[Plant Manager<br>Dernier niveau de validation]
    TKSAgent[TKS Agent<br>Contrôle des données et rapprochement plan/exécuté]

    TeamLeader --> Clerk
    Clerk --> ShiftLeader
    ShiftLeader --> Coordinator
    Coordinator --> DepartmentManager
    DepartmentManager --> PlantManager
    PlantManager --> TKSAgent
```
## 1.3 Key Components

The system consists of three primary microservices:

1. OvertimeRequest Service
2. ApprovalWorkflow Service
3. ShiftManagement Service
4. Notification Services
5. TKSIntegration Services

Each service is supported by:

- Azure CosmosDB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Configuration Service for centralized settings

## 2. System Architecture Overview

## 2.1 High-Level Architecture
The system follows a microservices architecture with the following components:

```mermaid
flowchart TD
    ApiGateway[Azure API Management] --> OvertimeRequestService[Overtime Request Service]
    ApiGateway --> ApprovalWorkflowService[Approval Workflow Service]
    ApiGateway --> ShiftManagementService[Shift Management Service]
    ApiGateway --> NotificationService[Notification Service]
    ApiGateway --> TKSIntegrationService[TKS Integration Service]

    OvertimeRequestService --> OvertimeDB[(Overtime Request CosmosDB)]
    ApprovalWorkflowService --> WorkflowDB[(Approval Workflow CosmosDB)]
    ShiftManagementService --> ShiftDB[(Shift Management CosmosDB)]
    NotificationService --> NotificationDB[(Notification CosmosDB)]
    TKSIntegrationService --> TKSDB[(TKS Integration CosmosDB)]

```
## 2.1.1 Explanation of the Diagram
1. **API Gateway (Azure API Management):**

- Acts as the single entry point for all client requests.
- Routes requests to the appropriate microservices.

2. **Overtime Request Service:**

- Handles the creation, modification, and management of overtime requests (planned/unplanned).
- Stores data in Overtime Request CosmosDB.
3. **Approval Workflow Service:**

- Manages the multi-level approval process (Coordinator → Department Manager → Project Manager).
- Stores data in Approval Workflow CosmosDB.
4. **Shift Management Service:**

- Handles shift closures and assigns statuses (X/Y/W).
- Stores data in Shift Management CosmosDB.
5. **Notification Service:**

- Sends notifications to users via SignalR, email, or other channels.
- Stores data in Notification CosmosDB.
6. **TKS Integration Service:**

- Performs cross-checks between planned and executed overtime.
- Synchronizes data with TKS.
- Stores data in TKS Integration CosmosDB.
7. **Azure Service Bus** :
   - Facilitates asynchronous communication between microservices.
   -Manages events like station updates, schedule changes, etc..

8. **Bases de données CosmosDB** :
   - Each microservice has its own database to respect the principle of data separation.
   - CosmosDB is used for its scalability, low latency, and multi-region support.

## 2.1.2 Description of possible actions for each microservice
 Here is a description of the possible actions for each microservice, based on the data provided. This will help to better structure the **High-Level Architecture**.

### ********* OvertimeRequest Service **
#### **Responsabilités :**

- Responsibilities
Manage overtime requests (creation, modification, deletion).
- Handle both planned and unplanned overtime requests.
- Validate input data (e.g., employee selection, project, activity).
- Publish events for downstream services (e.g., approval workflows).
### **Examples of Actions**
#### **API Actions**
1. **Create a new overtime request:**
- Endpoint: POST /overtime-requests
- Description: Allows users to create a new overtime request with details like employee, project, activity, and hours.
2. **Retrieve a list of overtime requests:**
- Endpoint: GET /overtime-requests
- Description: Fetches all overtime requests, optionally filtered by employee, project, or status.
3. **Retrieve details of a specific overtime request:**
-Endpoint: GET /overtime-requests/{id}
- Description: Fetches detailed information about a specific overtime request.
4. **Update an existing overtime request:**
- Endpoint: PUT /overtime-requests/{id}
- Description: Updates the details of an existing overtime request (e.g., hours, project, or activity).
5. **Delete an overtime request:**
- Endpoint: DELETE /overtime-requests/{id}
- Description: Deletes an overtime request that is no longer needed.
### **Event-Driven Actions**
1. **Publish OvertimeRequestCreated event:**
- Triggered when a new overtime request is created.
- Consumed by the Approval Workflow Service to initiate the approval process.
2. **Publish OvertimeRequestUpdated event:**
- Triggered when an overtime request is updated.
- Consumed by downstream services to reflect changes in the request.
3. **Publish OvertimeRequestDeleted event:**
- Triggered when an overtime request is deleted.
- Consumed by downstream services to remove the request from workflows.
### **Validation and Business Logic**
1. **Validate employee eligibility:**
- Ensure the selected employee is eligible for overtime based on company policies.
2. **Check project/activity validity:**
- Verify that the selected project or activity is valid and active.
3. **Enforce business rules:**
- Ensure overtime hours do not exceed predefined limits.
- Validate that the request aligns with company policies.
### **Use Cases**
1. **Planned Overtime:**
- Create and manage overtime requests for scheduled activities.
2. **Unplanned Overtime:**
- Handle urgent overtime requests for unexpected tasks.
3. **Audit and Reporting:**
- Provide a history of overtime requests for auditing and reporting purposes.
### ********* Approval Workflow Service**
#### **Responsabilités :**
- Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
- Allow modifications to requests during the approval process.
- Publish events for downstream services (e.g., notifications, shift management).
- Ensure compliance with business rules for approvals.

### **Examples of Actions**
#### **API Actions**
1. **Approve an overtime request:**
- **Endpoint**: POST /approvals/{id}/approve
- **Description**: Approves an overtime request at the current level (Coordinator, Department Manager, or Project Manager).
2. **Reject an overtime request:**
- **Endpoint**: POST /approvals/{id}/reject
- **Description**: Rejects an overtime request and provides a reason for rejection.
3. **Retrieve pending approvals:**
- **Endpoint**: GET /approvals/pending
- **Description**: Fetches a list of overtime requests awaiting approval for the current user.
4. **Retrieve approval history:**
- **Endpoint**: GET /approvals/history
- **Description**: Fetches the approval history for a specific overtime request.
5. **Modify an overtime request during approval:**
- **Endpoint**: PUT /approvals/{id}
- **Description**: Allows approvers to modify details of an overtime request (e.g., hours, project, or activity) before approving.

### **Event-Driven Actions**
1. **Publish** ApprovalStarted event:
- Triggered when an overtime request enters the approval workflow.
- Consumed by the Notification Service to notify the first approver.
2. **Publish** ApprovalCompleted event:
- Triggered when an overtime request is fully approved.
- Consumed by the Shift Management Service to update shift statuses.
4. **Publish** ApprovalRejected event:
- Triggered when an overtime request is rejected.
- Consumed by the Notification Service to notify the requester.
5. **Publish** ApprovalModified event:
- Triggered when an approver modifies an overtime request during the approval process.
- Consumed by downstream services to reflect the changes.

#### **Validation and Business Logic**
1. **Validate approver permissions:**
- Ensure the current user has the appropriate role (Coordinator, Department Manager, or Project Manager) to approve or reject the request.
2. **Enforce approval hierarchy:**
- Ensure that requests follow the defined approval chain (Coordinator → Department Manager → Project Manager).
3. **Check for conflicts:**
- Validate that the overtime request does not conflict with existing approvals or policies.
4. **Audit trail:**
- Maintain a complete history of all actions taken during the approval process for auditing purposes.

### **Use Cases**
1. **Multi-Level Approval:**
- Manage the approval process across multiple levels (Coordinator, Department Manager, Project Manager).
2. **Request Modifications:**
- Allow approvers to modify overtime requests during the approval process.
3. **Notifications:**
- Notify approvers and requesters about the status of overtime requests.
4. **Audit and Reporting:**
- Provide a detailed history of approvals for compliance and reporting purposes.

### ******* Shift Management Service
#### **Responsibilities:**
- Manage the closure of shifts and assign statuses (X/Y/W).
- Handle planned and unplanned shift updates.
- Publish events for downstream services (e.g., TKS Integration, Notification Service).
- Ensure compliance with business rules for shift management.

### ***API Actions**
1. **Close a shift:**
- **Endpoint**: POST /shifts/{id}/close
- **Description** : Closes a shift and assigns a status (e.g., X, Y, W).
2. **Retrieve a list of shifts:**
- **Endpoint**: GET /shifts
- **Description**: Fetches all shifts, optionally filtered by date, status, or employee.
3. **Retrieve details of a specific shift:**
- **Endpoint**: GET /shifts/{id}
- **Description**: Fetches detailed information about a specific shift.
4. **Update a shift:**
- **Endpoint**: PUT /shifts/{id}
- **Description**: Updates the details of an existing shift (e.g., start time, end time, or assigned employees).
5. **Delete a shift:**
- **Endpoint**: DELETE /shifts/{id}
- **Description**: Deletes a shift that is no longer needed.
### **Event-Driven Actions**
1. **Publish ShiftClosed event:**
- Triggered when a shift is closed.
- Consumed by the TKS Integration Service to reconcile planned vs. executed shifts.
2. **Publish ShiftUpdated event:**
- Triggered when a shift is updated.
- Consumed by downstream services to reflect changes in the shift.
3. **Publish ShiftDeleted event:**
- Triggered when a shift is deleted.
- Consumed by downstream services to remove the shift from workflows.

### **Validation and Business Logic**
1. **Validate shift closure:**
- Ensure all required tasks are completed before closing a shift.
2. **Check employee assignments:**
- Validate that employees assigned to the shift are eligible and available.
3. **Enforce business rules:**
- Ensure that shift statuses (X/Y/W) comply with company policies.
4. **Audit trail:**
- Maintain a complete history of all actions taken during shift management for auditing purposes.

### **Use Cases**
1. **Planned Shifts:**
- Manage and close shifts that are part of the planned schedule.
2. **Unplanned Shifts:**
- Handle urgent or unexpected shifts.
3. **Audit and Reporting:**
- Provide a history of shift closures and statuses for auditing and reporting purposes.
4. **Integration with TKS:**
- Publish events to synchronize planned vs. executed shifts with the TKS system.

### ********* TKS Integration Service**
#### **Responsibilities:**
- Perform cross-checks between planned and executed overtime.
- Synchronize data with the TKS system.
- Publish events for downstream services (e.g., notifications, reporting).
- Ensure data consistency between the OverTime Management system and TKS.

#### **API Actions**
1. **Synchronize planned vs. executed overtime:**
- Endpoint: POST /tks/synchronize
- Description: Synchronizes planned overtime data with executed data in the TKS system.
2. **Retrieve synchronization status:**
- Endpoint: GET /tks/synchronization-status
- Description: Fetches the status of the last synchronization process.
3. **Retrieve discrepancies:**
-Endpoint: GET /tks/discrepancies
-Description: Retrieves a list of discrepancies between planned and executed overtime.
4. **Force a manual synchronization:**
- Endpoint: POST /tks/manual-sync
- Description: Allows administrators to trigger a manual synchronization with TKS.
5. **Retrieve TKS integration logs:**
- Endpoint: GET /tks/logs
- Description: Fetches logs related to TKS integration for auditing and debugging purposes.
### **Event-Driven Actions**
1. **Publish TKSDataSynchronized event:**
- Triggered when data is successfully synchronized with TKS.
- Consumed by downstream services to confirm synchronization.
2. **Publish TKSDiscrepancyDetected event:**
- Triggered when discrepancies are found between planned and executed overtime.
- Consumed by the Notification Service to alert relevant users.
3. **Publish TKSManualSyncTriggered event:**
- Triggered when a manual synchronization is initiated.
Consumed by downstream services to log the action.

### **Validation and Business Logic**
1. **Validate synchronization data:**
- Ensure that the data being synchronized is complete and accurate.
2. **Check for discrepancies:**
- Identify and log any mismatches between planned and executed overtime.
3. **Enforce synchronization rules:**
- Ensure that synchronization processes comply with company policies and TKS requirements.
4. **Audit trail:**
- Maintain a complete history of synchronization actions and results for auditing purposes.

### **Use Cases**
1. **Planned vs. Executed Reconciliation:**
- Compare planned overtime with executed overtime and resolve discrepancies.
2. **Real-Time Synchronization:**
- Automatically synchronize data with TKS in real-time or on a scheduled basis.
3. **Manual Synchronization:**
- Allow administrators to trigger manual synchronization processes when needed.
4. **Audit and Reporting:**
- Provide a history of synchronization actions and discrepancies for auditing and reporting purposes.
--------------------------------------------------
## 3. Domain Model Design
The OverTime Management system is divided into multiple **Bounded Contexts** to ensure clear separation of concerns and maintainability. Each bounded context represents a specific domain with its own responsibilities, entities, and aggregates.

## 3.1 Bounded Contexts

The system is divided into three primary **bounded contexts:**

### **3.1.1 Overtime Request Context**
1. **Responsibilities:**
- Manage the creation, modification, and deletion of overtime requests.
- Handle both planned and unplanned overtime requests.
- Validate input data (e.g., employee selection, project, activity).
- Publish events for downstream services (e.g., approval workflows).
2. **Key Entities:**
- OvertimeRequest: Represents an overtime request with details like employee, project, activity, and hours.
- Employee: Represents the employee associated with the overtime request.
3. **Aggregates:**

- **OvertimeRequest Aggregate:**
  - **Root Entity**: OvertimeRequest
  - **Entities**: Employee
  - **Description**: Manages overtime request details and associated employees.

4. **Class Diagram**

```mermaid
classDiagram
    class OvertimeRequest {
        +String id
        +String employeeId
        +String projectId
        +String activityId
        +int hours
        +String status
        +createRequest()
        +updateRequest()
        +deleteRequest()
    }

    class Employee {
        +String id
        +String name
        +String department
        +String role
    }

    OvertimeRequest --> Employee : "Associated with"
```

**Explanation of the Diagram**
1. **OvertimeRequest:**
- Represents an overtime request with attributes like employee, project, activity, hours, and status.
- Actions:
    - createRequest(): Creates a new overtime request.
    - updateRequest(): Updates an existing overtime request.
    - deleteRequest(): Deletes an overtime request.
2. **Employee:**
    - Represents the employee associated with the overtime request.
    - Attributes include id, name, department, and role.
3. **Relationship:**
    - The OvertimeRequest class is associated with the Employee class, indicating that each overtime request is linked to a specific employee.
### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant OvertimeRequestService as Overtime Request Service
    participant ApprovalWorkflowService as Approval Workflow Service
    participant NotificationService as Notification Service
    participant Database as Overtime Request CosmosDB

    User->>OvertimeRequestService: Create Overtime Request (POST /overtime-requests)
    OvertimeRequestService->>Database: Save Overtime Request
    OvertimeRequestService->>ApprovalWorkflowService: Publish OvertimeRequestCreated Event
    ApprovalWorkflowService->>NotificationService: Notify Approver (Coordinator)
    NotificationService->>User: Send Notification (Request Submitted)
    ApprovalWorkflowService->>User: Approve/Reject Request (POST /approvals/{id}/approve or reject)
    ApprovalWorkflowService->>OvertimeRequestService: Update Request Status
    OvertimeRequestService->>Database: Update Request in Database
    OvertimeRequestService->>NotificationService: Notify Requester (Approval/Rejection Status)
    NotificationService->>User: Send Notification (Approval/Rejection)
```
### **Explanation of the Sequence**
1. **User Action:**
    - The user submits a new overtime request via the Overtime Request Service.
2. **Overtime Request Service:**
    - Saves the request in the Overtime Request CosmosDB.
    - Publishes an OvertimeRequestCreated event to the Approval Workflow Service.
3. **Approval Workflow Service:**
    - Notifies the first approver (e.g., Coordinator) via the Notification Service.
    - Handles the approval or rejection of the request.
4. **Notification Service:**
    - Sends notifications to the requester and approvers about the status of the request.
5. **Database Updates:**
    - The Overtime Request Service updates the request status in the database after approval or rejection.

### **3.1.2 Approval Workflow Context**
- **Responsibilities:**
   - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
   - **Allow** modifications to requests during the approval process.
   - **Publish** events for downstream services (e.g., notifications, shift management).
- **Key Entities:**
   - **Approval**: Represents the  - approval process for an overtime request.
  - **Approver**: Represents the individual responsible for approving or rejecting a request.
- **Aggregates:**
  - **Approval** Aggregate:
    - **Root Entity**: Approval
    - **Entities**: Approver
    - **Description**: Manages the approval process and tracks the status of overtime requests.
### **3.1.3 Shift Management Context**
### - **Responsibilities:**
- Manage the closure of shifts and assign statuses (X/Y/W).
- Handle planned and unplanned shift updates.
- Publish events for downstream services (e.g., TKS Integration, Notification Service).
- **Key Entities:**
   - **Shift**: Represents a specific shift with start and end times.
   - **EmployeeStatus**: Tracks the status of employees (e.g., present, absent).
- **Aggregates:**
  - **Shift Aggregate:**
    - **Root Entity**: Shift
    - **Entities**: EmployeeStatus
    - **Description**: Manages shift details and tracks employee statuses.

    4. **Class Diagram**

```mermaid
classDiagram
    class Shift {
        +String id
        +String shiftDate
        +String status
        +closeShift()
        +updateShift()
    }

    class EmployeeStatus {
        +String employeeId
        +String shiftId
        +String status
        +updateStatus()
    }

    class Notification {
        +String id
        +String message
        +String recipient
        +sendNotification()
    }

    Shift --> EmployeeStatus : "Tracks"
    Shift --> Notification : "Notifies"
```

Explanation of the Class Diagram
Shift:

Represents a shift with attributes like id, shiftDate, and status (e.g., X, Y, W).
Actions:
closeShift(): Closes a shift.
updateShift(): Updates shift details.
EmployeeStatus:

Tracks the status of employees for a specific shift (e.g., present, absent).
Attributes include employeeId, shiftId, and status.
Actions:
updateStatus(): Updates the status of an employee for a shift.
Notification:

Represents notifications sent to users about shift closures or updates.
Attributes include id, message, and recipient.
Actions:
sendNotification(): Sends a notification to the recipient.
Relationships:

Shift → EmployeeStatus: A shift tracks the status of employees.
Shift → Notification: A shift triggers notifications to users.

### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant ShiftManagementService as Shift Management Service
    participant NotificationService as Notification Service
    participant Database as Shift Management CosmosDB
    participant TKSIntegrationService as TKS Integration Service

    User->>ShiftManagementService: Close Shift (POST /shifts/{id}/close)
    ShiftManagementService->>Database: Save Shift Closure and Status (X/Y/W)
    ShiftManagementService->>NotificationService: Notify Users (Shift Closed)
    NotificationService->>User: Send Notification (Shift Closure Confirmation)
    ShiftManagementService->>TKSIntegrationService: Publish ShiftClosed Event
    TKSIntegrationService->>Database: Synchronize Planned vs Executed Shifts
    TKSIntegrationService->>NotificationService: Notify Admin (Synchronization Status)
```
### **Explanation of the Sequence**

1. **User Action:**
    - The user (e.g., Team Leader or Shift Leader) closes a shift via the **Shift Management Service.**
2. **Shift Management Service:**
    - Saves the shift closure and assigns a status (e.g., X, Y, W) in the **Shift Management CosmosDB**.
    - Publishes a ShiftClosed event to the TKS Integration Service.
3. **Notification Service:**
    - Sends notifications to relevant users (e.g., confirmation of shift closure).
4. **TKS Integration Service:**
    - Synchronizes planned vs. executed shifts in the database.
    - Notifies administrators about the synchronization status.
### **3.1.4 TKS Integration Context**
- **Responsibilities:**
 - Perform cross-checks between planned and executed overtime.
 - Synchronize data with the TKS system.
 - Publish events for downstream services (e.g., notifications, reporting).
- **Key Entities:**
 - **TKSData**: Represents the data synchronized with the TKS system.
 - **Discrepancy**: Represents any mismatch between planned and executed overtime.
- **Aggregates:**
  - **TKS Integration Aggregate:**
    - **Root Entity**: TKSData
    - **Entities**: Discrepancy
    - **Description**: Manages synchronization and reconciliation with the TKS system.


### Summary of Bounded Contexts

| **Bounded Context**   | **Responsibilities**                                                   | **Key Entities**           | **Key Actions**                                                                 |
|------------------------|-----------------------------------------------------------------------|-----------------------------|---------------------------------------------------------------------------------|
| **Overtime Request**   | Manage overtime requests (creation, modification, deletion).          | OvertimeRequest, Employee   | Create, update, delete requests; publish request-related events.               |
| **Approval Workflow**  | Manage multi-level approval processes for overtime requests.          | Approval, Approver          | Approve, reject, modify requests; publish approval-related events.             |
| **Shift Management**   | Manage shift closures and assign statuses (X/Y/W).                   | Shift, EmployeeStatus       | Close, update, delete shifts; publish shift-related events.                    |
| **TKS Integration**    | Synchronize planned vs. executed overtime and resolve discrepancies. | TKSData, Discrepancy        | Synchronize data, resolve discrepancies; publish synchronization-related events.|

---
### **3.1.4 Notification Service:**
- **Key Entities:**
  1. **Notification:**
- Represents a notification sent to users.
- Attributes:
    - id: Unique identifier for the notification.
    - message: The content of the notification.
    - recipient: The user receiving the notification.
    - timestamp: The time the notification was created.
    - status: The status of the notification (e.g., sent, pending).
2. **User:**
- Represents the recipient of the notification.
- Attributes:
    - id: Unique identifier for the user.
    - name: Name of the user.
    - email: Email address of the user.
    - role: Role of the user in the system.
3. NotificationRepository:
    - Handles the persistence of notifications.
    -   Key Methods:
        - save(notification: Notification): Saves a notification.
        - findById(id: String): Retrieves a notification by its ID.
        - findAllByRecipient(recipient: String): Retrieves all notifications for a specific recipient.
4. **NotificationSender:**
- Handles the delivery of notifications.
- Key Methods:
    - sendEmail(notification: Notification): Sends a notification via email.
    - sendSignalR(notification: Notification): Sends a notification via SignalR.
-  **Aggregates:**
  1. **Notification Aggregate:**
- **Root Entity**: Notification
- **Entities**: User
- **Description**: Manages the lifecycle of notifications, including creation, delivery, and status updates.
- **Key Actions:**
    - Create a new notification.
    - Update the status of a notification (e.g., sent, pending).
    - Associate a notification with a recipient (User).
2.**Notification Repository Aggregate:**
    - **Root Entity:** NotificationRepository
    - **Description**: Handles the persistence and retrieval of notifications.
    - **Key Actions:**
        - Save a notification.
        - Retrieve a notification by its ID.
        - Retrieve all notifications for a specific recipient.
3. **Notification Sender Aggregate:**
    - **Root Entity:** NotificationSender
    - **Description**: Manages the delivery of notifications through various channels (e.g., email, SignalR
    - **Key Actions:**
        - Send a notification via email.
        - Send a notification via SignalR.
### **Class Diagram**
```mermaid
classDiagram
    class NotificationService {
        +sendNotification(notification: Notification): void
        +scheduleNotification(notification: Notification, time: Date): void
    }

    class Notification {
        +String id
        +String message
        +String recipient
        +Date timestamp
        +String status
        +markAsSent(): void
    }

    class User {
        +String id
        +String name
        +String email
        +String role
    }

    class NotificationRepository {
        +save(notification: Notification): void
        +findById(id: String): Notification
        +findAllByRecipient(recipient: String): List<Notification>
    }

    class NotificationSender {
        +sendEmail(notification: Notification): void
        +sendSignalR(notification: Notification): void
    }

    NotificationService --> Notification : "Manages"
    NotificationService --> NotificationRepository : "Uses"
    NotificationService --> NotificationSender : "Delegates"
    Notification --> User : "Sent to"
```

### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant NotificationService as Notification Service
    participant NotificationRepository as Notification Repository
    participant NotificationSender as Notification Sender

    User->>NotificationService: Create Notification (e.g., Shift Closed)
    NotificationService->>NotificationRepository: Save Notification
    NotificationRepository-->>NotificationService: Notification Saved
    NotificationService->>NotificationSender: Send Notification (Email/SignalR)
    NotificationSender-->>NotificationService: Notification Sent
    NotificationService->>User: Confirm Notification Sent
```



### **3.1.5 TKS Integration Service**
- **Key Entities:**
  1. **TKSData:**
    - Represents the data synchronized with the TKS system.
    - **Attributes:**
        - id: Unique identifier for the TKS data.
        - plannedHours: Total planned overtime hours.
        - executedHours: Total executed overtime hours.
        - synchronizationStatus: Status of the synchronization (e.g., success, failed).
        - lastSyncTimestamp: Timestamp of the last synchronization.
2. **Discrepancy:**
    - Represents any mismatch between planned and executed overtime.
    - **Attributes:**
        - id: Unique identifier for the discrepancy.
        - plannedHours: Planned overtime hours.
        - executedHours: Executed overtime hours.
    - description: Description of the discrepancy.
    - status: Status of the discrepancy (e.g., resolved, unresolved).
3. **TKSLog:**
    - Represents logs related to TKS synchronization for auditing and debugging purposes.
    - **Attributes:**
        - id: Unique identifier for the log entry.
        - timestamp: Timestamp of the log entry.
        - action: Action performed (e.g., synchronization, discrepancy detection).
        - details: Additional details about the action.


-  **Aggregates:**
  . TKS Integration Aggregate
        - Root Entity: TKSData
        - Entities: Discrepancy
        - Description: Manages synchronization and reconciliation with the TKS system.
        - Key Actions:
            - Synchronize planned vs. executed overtime.
            - Detect and resolve discrepancies.
            - Log synchronization actions for auditing.
-  ### **Class Diagram**
```mermaid
  classDiagram
    class TKSIntegrationService {
        +synchronizeData(): void
        +detectDiscrepancies(): List<Discrepancy>
        +logSynchronizationAction(action: String, details: String): void
    }

    class TKSData {
        +String id
        +int plannedHours
        +int executedHours
        +String synchronizationStatus
        +Date lastSyncTimestamp
    }

    class Discrepancy {
        +String id
        +int plannedHours
        +int executedHours
        +String description
        +String status
    }

    class TKSLog {
        +String id
        +Date timestamp
        +String action
        +String details
    }

    TKSIntegrationService --> TKSData : "Manages"
    TKSIntegrationService --> Discrepancy : "Detects"
    TKSIntegrationService --> TKSLog : "Logs"
```

### ** Sequence Diagram**

```mermaid
  sequenceDiagram
    participant User as User
    participant TKSIntegrationService as TKS Integration Service
    participant TKSDB as TKS Integration CosmosDB
    participant NotificationService as Notification Service

    User->>TKSIntegrationService: Trigger Synchronization (POST /tks/synchronize)
    TKSIntegrationService->>TKSDB: Fetch Planned and Executed Data
    TKSDB-->>TKSIntegrationService: Return Data
    TKSIntegrationService->>TKSIntegrationService: Detect Discrepancies
    TKSIntegrationService->>TKSDB: Save Discrepancy Details
    TKSIntegrationService->>NotificationService: Notify Admin (Synchronization Status)
    NotificationService->>User: Send Notification (Synchronization Completed)
```

### **Benefits of Bounded Contexts**

1. **Separation of Concerns**:
   - Each context focuses on a specific domain, reducing complexity.
2. **Scalability**:
   - Each context can be scaled independently based on its workload.
3. **Maintainability**:
   - Clear boundaries make the system easier to understand and maintain.
4. **Flexibility**:
   - New features can be added to a specific context without affecting others.



### **Benefits of Bounded Contexts**
1. **Separation of Concerns:**
- Each context focuses on a specific domain, reducing complexity.
2.**Scalability:**
- Each context can be scaled independently based on its workload.
3. **Maintainability:**
Clear boundaries make the system easier to understand and maintain.
4. **Flexibility:**
New features can be added to a specific context without affecting others.

## 4. Architecture 
Architecture Description
1. **OvertimeRequest Service**
    -**Responsibilities:**
        - Manages the creation, modification, and deletion of overtime requests.
        - Handles both planned and unplanned overtime requests.
        - Validates input data (e.g., employee selection, project, activity).
        - Publishes events for downstream services (e.g., approval workflows).
    - **Key Components:**
        - **Database:** Overtime Request CosmosDB for storing overtime request data.
        - **API Endpoints:**
            - Create, update, delete, and retrieve overtime requests.
        - **Event-Driven Actions:**
            - Publishes events like OvertimeRequestCreated, OvertimeRequestUpdated, and OvertimeRequestDeleted.

2. ApprovalWorkflow Service
Responsibilities:
Manages the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
Allows modifications to requests during the approval process.
Publishes events for downstream services (e.g., notifications, shift management).
Key Components:
Database: Approval Workflow CosmosDB for storing approval data.
API Endpoints:
Approve, reject, and modify overtime requests.
Event-Driven Actions:
Publishes events like ApprovalStarted, ApprovalCompleted, and ApprovalRejected.

4. **Notification Services**
    - **Responsibilities:**
        - Sends notifications to users via SignalR, email, or other channels.
        - Manages the lifecycle of notifications, including creation, delivery, and status updates.
    - **Key Components:**
        - **Database:** Notification CosmosDB for storing notification data.
        - **API Endpoints:**
            - Create and retrieve notifications.
    - **Event-Driven Actions:**
            - Sends notifications triggered by events from other services (e.g., shift closures, approvals).

5. TKSIntegration Services
    - **Responsibilities:**
        - Performs cross-checks between planned and executed overtime.
        - Synchronizes data with the TKS system.
        - Detects and resolves discrepancies.
    - **Key Components:**
        - **Database:** TKS Integration CosmosDB for storing synchronized data and discrepancies.
        - **API Endpoints:**
            - Synchronize data, retrieve discrepancies, and fetch synchronization logs.
    - **Event-Driven Actions:**
        - Publishes events like TKSDataSynchronized and TKSDiscrepancyDetected.

**Common Architectural Features**
    - **API Gateway:**
        - Azure API Management acts as the single entry point for all client requests and routes them to the appropriate microservices.
    - **Event-Driven Communication:**
        - Azure Service Bus facilitates asynchronous communication between microservices.
    - **Database:**
        - Each service uses its own Azure CosmosDB instance to ensure data separation and scalability.
    - **Scalability:**
        - Each microservice can be scaled independently based on its workload.

### **Ends points**

description approfondie des responsabilités, composants clés, actions possibles, et événements pour chaque microservice :

1. **OvertimeRequest Service**
**Responsabilités :**
    - Manage the creation, modification, and deletion of overtime requests.
    - Handle both planned and unplanned overtime requests.
    - Validate input data (e.g., employee selection, project, activity).
    - Publish events for downstream services (e.g., approval workflows).
**Key Components:**
    - **Database:** Overtime Request CosmosDB.
    - **API Endpoints:**
        - POST /overtime-requests: Create a new overtime request.
        - GET /overtime-requests: Retrieve all overtime requests.
        - PUT /overtime-requests/{id}: Update an existing overtime request.
        - DELETE /overtime-requests/{id}: Delete an overtime request.
    - **Event-Driven Actions:**
        - Publish OvertimeRequestCreated, OvertimeRequestUpdated, OvertimeRequestDeleted.
2. **ApprovalWorkflow Service**
**Responsibilities:**
    - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
    - Allow modifications to requests during the approval process.
    - Publish events for downstream services (e.g., notifications, shift management).
**Key Components:**
    - **Database:** Approval Workflow CosmosDB.
    - **API Endpoints:**
        - POST /approvals/{id}/approve: Approve an overtime request.
        - POST /approvals/{id}/reject: Reject an overtime request.
        - GET /approvals/pending: Retrieve pending approvals.
        - GET /approvals/history: Retrieve approval history.
    - **Event-Driven Actions:**
        - Publish ApprovalStarted, ApprovalCompleted, ApprovalRejected.
3. **ShiftManagement Service**
**Responsibilities:**
    - Manage the closure of shifts and assign statuses (X/Y/W).
    - Handle planned and unplanned shift updates.
    - Publish events for downstream services (e.g., TKS Integration, Notification Service).
**Key Components:**
    - **Database:** Shift Management CosmosDB.
    - **API Endpoints:**
        - POST /shifts/{id}/close: Close a shift.
        - GET /shifts: Retrieve all shifts.
        - PUT /shifts/{id}: Update a shift.
        - DELETE /shifts/{id}: Delete a shift.
    - **Event-Driven Actions:**
        - Publish ShiftClosed, ShiftUpdated, ShiftDeleted.
        - 
4. **Notification Service**
Responsibilities:
Send notifications to users via SignalR, email, or other channels.
Manage the lifecycle of notifications, including creation, delivery, and status updates.
Key Components:
Database: Notification CosmosDB.
API Endpoints:
POST /notifications: Create a notification.
GET /notifications: Retrieve notifications.
Event-Driven Actions:
Send notifications triggered by events from other services (e.g., shift closures, approvals).
1. TKSIntegration Service
Responsibilities:
Perform cross-checks between planned and executed overtime.
Synchronize data with the TKS system.
Detect and resolve discrepancies.
Key Components:
Database: TKS Integration CosmosDB.
API Endpoints:
POST /tks/synchronize: Synchronize planned and executed overtime data.
GET /tks/discrepancies: Retrieve detected discrepancies.
GET /tks/logs: Retrieve TKS integration logs.
Event-Driven Actions:
Publish TKSDataSynchronized, TKSDiscrepancyDetected.
Summary of Microservices
Microservice	Responsibilities	Database	Event-Driven Actions
OvertimeRequest Service	Manage overtime requests (creation, modification, deletion).	Overtime Request CosmosDB	OvertimeRequestCreated, OvertimeRequestUpdated, OvertimeRequestDeleted
ApprovalWorkflow Service	Manage multi-level approval processes.	Approval Workflow CosmosDB	ApprovalStarted, ApprovalCompleted, ApprovalRejected
ShiftManagement Service	Manage shift closures and updates.	Shift Management CosmosDB	ShiftClosed, ShiftUpdated, ShiftDeleted
Notification Service	Send notifications to users.	Notification CosmosDB	Notifications triggered by events.
TKSIntegration Service	Synchronize data with TKS and detect discrepancies.	TKS Integration CosmosDB	TKSDataSynchronized, TKSDiscrepancyDetected
Common Architectural Features
API Gateway:
Azure API Management acts as the single entry point for all client requests and routes them to the appropriate microservices.
Event-Driven Communication:
Azure Service Bus facilitates asynchronous communication between microservices.
Database:
Each service uses its own Azure CosmosDB instance to ensure data separation and scalability.
Scalability:
Each microservice can be scaled independently based on its workload.