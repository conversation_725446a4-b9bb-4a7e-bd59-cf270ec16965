# Versatility Matrix - Low-Level Design (LLD)

## Document Information

**Version:** 1.1.0
**Last Updated:** 2025-04-22
**Status:** Draft
**Authors: <AUTHORS>

## Executive Summary

The Versatility Matrix microservice is a critical component of the Connected Workers (CW) application, providing a dynamic visualization and management system for tracking operator skills, certifications, and workstation experience across the Assembling and Cutting & Lead Prep departments. It enables Team Leaders and other stakeholders to quickly identify qualified operators for workstation replacements while maintaining compliance with training requirements.

### Key Features

- Dual-table matrix system for the Assembling department (Qualifications and Polyvalence)
- Single-table qualification tracking for the Cutting & Lead Prep department
- Role-based access control with differentiated permissions
- Real-time qualification status updates from the Training Process
- Team Leader-managed polyvalence level tracking
- Dynamic filtering, sorting, and searching capabilities
- Criticality-based operator coverage tracking for workstations
- Comprehensive audit logging and compliance reporting
- Custom saved views for frequent access patterns
- Real-time alerts for certification expiry and coverage gaps
- Export capabilities for offline analysis and reporting

### Key Integration Points

- **Training Process**: Consumes qualification status updates via Service Bus events (`TrainingStatusChangedEvent`, `FamiliarizationCompletedEvent`, `QualificationRemovedEvent`)
- **Crew Management**: Consumes operator data changes via Change Feed
- **Replacement Process**: Provides qualified operator suggestions for absent workers via API and Change Feed
- **Manufacturing Engineering**: Consumes workstation definitions and criticality settings
- **APRISO (MES)**: Potential indirect data source for polyvalence level suggestions
- **User Management/HR System**: Source for operator core data synchronization
- **Centralized Logging Service**: For comprehensive audit logging

## Table of Contents

1. [Overview](#1-overview)
2. [Architecture](#2-architecture)
   1. [Architectural Patterns](#21-architectural-patterns)
   2. [High-Level Component Diagram](#22-high-level-component-diagram)
   3. [Technology Stack](#23-technology-stack)
   4. [CQRS Implementation](#24-cqrs-implementation)
   5. [Microservices Breakdown](#25-microservices-breakdown)
3. [Detailed Component Design](#3-detailed-component-design)
4. [Data Models](#4-data-models)
5. [API Specification](#5-api-specification)
6. [Event Communication](#6-event-communication)

## 5.2.4 Versatility Matrix

### 1. Overview

The Versatility Matrix microservice implements a critical visualization and management tool for tracking operator skills and workstation proficiency across manufacturing departments. Based on the functional design specifications, this microservice:

- Maintains real-time qualification status of operators based on updates from the Training Process
- Enables Team Leaders to update polyvalence levels (experience) for operators at specific workstations
- Calculates and displays operator coverage for workstations based on criticality
- Provides filtering, searching, and export capabilities for various stakeholders
- Integrates with multiple data sources through event-driven mechanisms
- Supports compliance and audit requirements through comprehensive logging

The microservice follows cloud-native principles, implementing a CQRS (Command Query Responsibility Segregation) pattern to separate read and write operations, with event-driven integration to maintain loose coupling with other systems.

### 2. Architecture

#### 2.1 Architectural Patterns

The Versatility Matrix implements the following architectural patterns:

1. **Microservices Architecture**:

   - Deployed as an independent service within the Connected Workers ecosystem
   - Well-defined boundaries with specific responsibilities related to operator skills and workstation experience
   - Self-contained with its own data store and processing capabilities
   - Exposes standardized API for other services to consume

2. **CQRS (Command Query Responsibility Segregation)**:

   - **Commands**: Focused on updates to system state
     - `UpdatePolyvalenceCommand`: When Team Leaders update experience levels
     - `SyncQualificationStatusCommand`: When processing qualification events from Training Process
     - `RecalculateWorkstationCoverageCommand`: To update workstation coverage metrics
   - **Queries**: Optimized for different view requirements
     - Matrix views with various filtering and pagination options
     - Workstation coverage and gap analysis
     - Operator detail views with qualification history
   - **Write Model**: Normalized data structure optimized for updates
   - **Read Model**: Denormalized projections optimized for quick retrieval

3. **Event-Driven Architecture**:

   - **Event Consumption**:
     - Consumes events from Training Process for qualification updates via Azure Service Bus
     - Processes Change Feed events from Crew Management for operator data changes
   - **Event Publication**:
     - Exposes its own state changes via Cosmos DB Change Feed
     - Publishes internal domain events for audit logging and real-time UI updates
     - Emits notification events for alerts (e.g., expiring certifications)

4. **Domain-Driven Design**:

   - **Bounded Context**: Versatility Matrix owns the polyvalence domain and consumes qualification status
   - **Aggregates**: Clear aggregate boundaries (Qualification, Polyvalence, Operator)
   - **Value Objects**: Immutable objects like PolyvalenceLevel, QualificationStatus
   - **Domain Services**: Encapsulates complex business logic like workstation coverage calculation

5. **Repository Pattern**:
   - Abstracts data access logic from business logic
   - Enforces aggregate consistency boundaries
   - Provides specialized query methods for read models

#### 2.2 High-Level Component Diagram

```mermaid
graph TB
    subgraph "External Systems"
        HR["HR System"]
        TP["Training Process"]
        MES["Manufacturing Execution System<br>(APRISO)"]
        ME["Manufacturing Engineering"]
    end

    subgraph "Versatility Matrix Service"
        API["API Layer"]
        QS["Qualification Service"]
        PV["Polyvalence Service"]
        AZ["Authorization Service"]
        AS["Analytics Service"]
        REPO["Repository Layer"]

        API --> QS
        API --> PV
        API --> AZ
        API --> AS

        QS --> REPO
        PV --> REPO
        AS --> REPO
    end

    subgraph "Storage"
        DB[(Cosmos DB)]
    end

    subgraph "Event Infrastructure"
        SB["Azure Service Bus"]
        CF["Change Feed Processor"]
        EG["Event Grid"]
    end

    subgraph "Client Applications"
        UI["Web UI"]
        MUI["Mobile UI"]
    end

    %% External System Connections
    HR -- "Operator Data" --> API
    TP -- "Qualification Updates" --> SB
    MES -. "Optional Workstation Time Data" .-> API
    ME -- "Workstation Definitions" --> API

    %% Service Bus Integration
    SB --> API

    %% Data Storage
    REPO --> DB

    %% Change Notification
    DB --> CF
    CF --> EG

    %% Client Connections
    EG --> UI
    EG --> MUI
    UI -- "API Calls" --> API
    MUI -- "API Calls" --> API

    %% Integration with other Connected Workers modules
    RP["Replacement Process"] -- "Query Qualified Operators" --> API

    classDef external fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#bfb,stroke:#333,stroke-width:1px;
    classDef events fill:#fbb,stroke:#333,stroke-width:1px;
    classDef client fill:#bbf,stroke:#333,stroke-width:1px;

    class HR,TP,MES,ME,RP external;
    class API,QS,PV,AZ,AS,REPO service;
    class DB storage;
    class SB,CF,EG events;
    class UI,MUI client;
```

This component diagram illustrates the architecture of the Versatility Matrix service:

1. **External Systems**:

   - HR System provides operator master data
   - Training Process sends qualification updates via events
   - Manufacturing Execution System (APRISO) can optionally provide workstation time data
   - Manufacturing Engineering provides workstation definitions and criticality levels

2. **Versatility Matrix Service**:

   - API Layer handles incoming requests and events
   - Qualification Service manages operator training certifications
   - Polyvalence Service handles experience levels at workstations
   - Authorization Service enforces role-based access control
   - Analytics Service provides coverage analysis and reporting
   - Repository Layer abstracts data access operations

3. **Storage**:

   - Cosmos DB stores all matrix data with document model flexibility

4. **Event Infrastructure**:

   - Azure Service Bus receives external events
   - Change Feed Processor monitors database changes
   - Event Grid distributes real-time updates to clients

5. **Client Applications**:

   - Web UI for desktop access
   - Mobile UI for shop floor access

6. **Integration**:
   - Replacement Process queries the API for finding qualified operators

The architecture follows event-driven principles to ensure real-time data flow and provides multiple integration points for external systems while maintaining a clear separation of concerns within the service.

#### 2.3 Technology Stack

- **Backend Framework**: NestJS with TypeScript
- **Data Storage**: Azure Cosmos DB with SQL API
- **Message Broker**: Azure Service Bus (for consuming Training events)
- **Change Feed**: Azure Cosmos DB Change Feed + Azure Functions
- **Authentication/Authorization**: Azure AD with Role-Based Access Control (RBAC)
- **API Documentation**: OpenAPI/Swagger
- **Monitoring**: Application Insights
- **Deployment**: Azure Kubernetes Service (AKS) or Azure App Service
- **CI/CD**: Azure DevOps Pipelines
- **Secret Management**: Azure Key Vault

#### 2.4 CQRS Implementation

The Versatility Matrix implements a practical CQRS pattern to separate write and read concerns:

##### Command Side (Write Model):

**Commands:**

- `UpdatePolyvalenceCommand`: Issued when a Team Leader updates an operator's polyvalence level

  ```typescript
  interface UpdatePolyvalenceCommand {
    operatorId: string;
    workstationId: string;
    newLevel: "X" | "1" | "2" | "3" | "4" | null;
    notes?: string;
    updatedBy: string; // Team Leader's ID
  }
  ```

- `SyncQualificationStatusCommand`: Issued when processing qualification events from Training Process

  ```typescript
  interface SyncQualificationStatusCommand {
    operatorId: string;
    skillCode: string;
    newStatus: "O" | "LC" | "V" | "C" | "R" | "F" | null;
    certificationDate?: Date;
    expiryDate?: Date;
    sourceEventId: string;
  }
  ```

- `RecalculateWorkstationCoverageCommand`: Issued when qualifications or polyvalence data changes
  ```typescript
  interface RecalculateWorkstationCoverageCommand {
    workstationIds?: string[]; // Optional list of specific workstations, null for all
    departmentId?: string; // Optional department filter
  }
  ```

**Command Handlers:**

- Process commands by accessing and updating the write model
- Apply business rules and validations (e.g., ensure Team Leader can only update polyvalence for their team)
- Publish domain events on successful updates

**Domain Events:**

- `PolyvalenceUpdatedEvent`: When an operator's experience level at a workstation changes
- `QualificationStatusSyncedEvent`: When a qualification status is updated from a Training event
- `WorkstationCoverageChangedEvent`: When the calculated coverage for workstations changes

**Event Flow:**

```
Command → Validation → Command Handler → Write Model Update → Domain Event → Read Model Update
```

##### Query Side (Read Model):

**Queries:**

- `GetMatrixQuery`: Retrieve the matrix view with filtering options

  ```typescript
  interface GetMatrixQuery {
    departmentId: string;
    areaId?: string;
    qualificationStatus?: string[];
    polyvalenceLevel?: string[];
    skillCode?: string[];
    workstationId?: string[];
    operatorId?: string;
    searchTerm?: string;
    page: number;
    pageSize: number;
  }
  ```

- `GetWorkstationCoverageQuery`: Retrieve workstation coverage metrics

  ```typescript
  interface GetWorkstationCoverageQuery {
    departmentId: string;
    areaId?: string;
    criticalityLevel?: string[];
    gapThreshold?: number;
  }
  ```

- `GetOperatorDetailsQuery`: Retrieve detailed information for a specific operator
  ```typescript
  interface GetOperatorDetailsQuery {
    operatorId: string;
    includeHistory?: boolean;
  }
  ```

**Query Handlers:**

- Process queries against optimized read models
- Apply filtering, sorting, and pagination logic
- Map results to DTOs for API responses

**Read Model Projections:**

- Denormalized views stored in dedicated containers
- Optimized for specific query patterns
- Updated asynchronously when write model changes

**Projection Updates:**

- Triggered by domain events or Change Feed processors
- Performed asynchronously to avoid blocking the command side
- Maintains eventual consistency between write and read models

##### Benefits of CQRS for Versatility Matrix:

1. **Performance Optimization**: Read models can be denormalized and optimized for complex queries like matrix views
2. **Scalability**: Read and write sides can scale independently based on workload
3. **Query Flexibility**: Supports complex queries needed for filtering, reporting, and analysis
4. **Business Logic Isolation**: Command handlers enforce business rules without impacting query performance
5. **Event Sourcing Compatibility**: Facilitates audit logging and historical analysis

#### 2.5 Microservices Breakdown

The Versatility Matrix functions as a single microservice with clear boundaries:

1. **Responsibilities**:

   - Manage and display qualification status (sourced from Training Process)
   - Manage and display polyvalence levels (managed by Team Leaders)
   - Calculate and display workstation coverage metrics
   - Provide filterable, searchable matrix views
   - Maintain audit logs for compliance

2. **Dependencies**:

   - **Inbound**: Consumes events from Training Process
   - **Outbound**: Exposes data via API and Change Feed for consumers

3. **Isolation**:
   - Clear domain boundaries
   - Independent deployment
   - Autonomous scaling
   - Dedicated data storage

---

### 3. Detailed Component Design

The Versatility Matrix microservice is implemented using NestJS with TypeScript, following a modular architecture.

#### 3.1 API Layer

The API layer exposes RESTful endpoints through NestJS controllers.

##### 3.1.1 Controllers

```typescript
// Matrix controller handles matrix data retrieval with filtering options
@Controller("api/matrix")
export class MatrixController {
  @Get()
  async getMatrix(
    @Query() queryParams: GetMatrixQueryDto
  ): Promise<MatrixViewDto> {
    // Implementation leveraging the query handler
  }

  @Get("export")
  @Header("Content-Type", "application/octet-stream")
  async exportMatrix(
    @Query() queryParams: ExportMatrixQueryDto
  ): Promise<StreamableFile> {
    // Implementation for data export
  }
}

// Polyvalence controller handles polyvalence updates by Team Leaders
@Controller("api/polyvalence")
export class PolyvalenceController {
  @Put(":operatorId/:workstationId")
  @Roles(Role.TEAM_LEADER)
  async updatePolyvalence(
    @Param("operatorId") operatorId: string,
    @Param("workstationId") workstationId: string,
    @Body() updateDto: UpdatePolyvalenceDto,
    @CurrentUser() user: UserInfo
  ): Promise<PolyvalenceDto> {
    // Implementation leveraging the command handler
  }

  @Get("history/:operatorId")
  async getPolyvalenceHistory(
    @Param("operatorId") operatorId: string
  ): Promise<PolyvalenceHistoryDto> {
    // Implementation for history retrieval
  }
}

// Additional controllers for qualification, workstation, and reporting functionality
```

##### 3.1.2 NestJS Middleware and Guards

```typescript
// Authorization guard for RBAC
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Implementation checking user roles against required roles
  }
}

// Audit logging interceptor
@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Implementation for logging API calls
  }
}
```

#### 3.2 Domain Layer

The domain layer implements the core business logic using NestJS services and CQRS pattern.

##### 3.2.1 Services

```typescript
// Core service implementations
@Injectable()
export class PolyvalenceService {
  constructor(
    @InjectRepository(PolyvalenceRepository)
    private polyvalenceRepository: PolyvalenceRepository,
    private eventPublisher: EventPublisher
  ) {}

  async updatePolyvalence(
    command: UpdatePolyvalenceCommand
  ): Promise<Polyvalence> {
    // Business logic for updating polyvalence
    // Publish domain events
  }
}

@Injectable()
export class QualificationService {
  constructor(
    @InjectRepository(QualificationRepository)
    private qualificationRepository: QualificationRepository,
    private eventPublisher: EventPublisher
  ) {}

  async syncQualificationStatus(
    command: SyncQualificationStatusCommand
  ): Promise<Qualification> {
    // Business logic for syncing qualification status
    // Publish domain events
  }
}

@Injectable()
export class WorkstationCoverageService {
  constructor(
    @InjectRepository(WorkstationRepository)
    private workstationRepository: WorkstationRepository,
    @InjectRepository(QualificationRepository)
    private qualificationRepository: QualificationRepository,
    @InjectRepository(PolyvalenceRepository)
    private polyvalenceRepository: PolyvalenceRepository
  ) {}

  async calculateCoverage(workstationId: string): Promise<WorkstationCoverage> {
    // Implementation of coverage calculation logic
  }
}
```

##### 3.2.2 Command Handlers

```typescript
// CQRS command handlers
@CommandHandler(UpdatePolyvalenceCommand)
export class UpdatePolyvalenceCommandHandler
  implements ICommandHandler<UpdatePolyvalenceCommand>
{
  constructor(private polyvalenceService: PolyvalenceService) {}

  async execute(command: UpdatePolyvalenceCommand): Promise<Polyvalence> {
    // Command validation and execution
    return this.polyvalenceService.updatePolyvalence(command);
  }
}

@CommandHandler(SyncQualificationStatusCommand)
export class SyncQualificationStatusCommandHandler
  implements ICommandHandler<SyncQualificationStatusCommand>
{
  constructor(private qualificationService: QualificationService) {}

  async execute(
    command: SyncQualificationStatusCommand
  ): Promise<Qualification> {
    // Command validation and execution
    return this.qualificationService.syncQualificationStatus(command);
  }
}
```

##### 3.2.3 Query Handlers

```typescript
// CQRS query handlers
@QueryHandler(GetMatrixQuery)
export class GetMatrixQueryHandler implements IQueryHandler<GetMatrixQuery> {
  constructor(
    private readonly matrixProjectionRepository: MatrixProjectionRepository
  ) {}

  async execute(query: GetMatrixQuery): Promise<MatrixView> {
    // Implementation for retrieving and filtering matrix data
  }
}

@QueryHandler(GetWorkstationCoverageQuery)
export class GetWorkstationCoverageQueryHandler
  implements IQueryHandler<GetWorkstationCoverageQuery>
{
  constructor(
    private readonly workstationCoverageProjectionRepository: WorkstationCoverageProjectionRepository
  ) {}

  async execute(
    query: GetWorkstationCoverageQuery
  ): Promise<WorkstationCoverageView> {
    // Implementation for retrieving workstation coverage metrics
  }
}
```

##### 3.2.4 Domain Events

```typescript
// Domain events using NestJS event publisher
export class PolyvalenceUpdatedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly workstationId: string,
    public readonly oldLevel: PolyvalenceLevel | null,
    public readonly newLevel: PolyvalenceLevel | null,
    public readonly updatedBy: string,
    public readonly timestamp: Date
  ) {}
}

export class QualificationStatusUpdatedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly skillCode: string,
    public readonly oldStatus: QualificationStatus | null,
    public readonly newStatus: QualificationStatus | null,
    public readonly certificationDate: Date | null,
    public readonly expiryDate: Date | null,
    public readonly sourceEventId: string | null,
    public readonly timestamp: Date
  ) {}
}
```

#### 3.3 Infrastructure Layer

The infrastructure layer handles external integrations and technical concerns using NestJS providers.

##### 3.3.1 Event Handlers

```typescript
// Azure Service Bus handlers
@Injectable()
export class TrainingStatusChangedEventHandler {
  constructor(private commandBus: CommandBus) {}

  @ServiceBusListener("training-status-events", "TrainingStatusChanged")
  async handleTrainingStatusChanged(
    message: TrainingStatusChangedEventDto
  ): Promise<void> {
    // Transform event to command and dispatch
    const command = new SyncQualificationStatusCommand(
      message.operatorId,
      message.processId,
      message.newStatus,
      message.certificationDate,
      message.certificationExpiry,
      message.eventId
    );
    await this.commandBus.execute(command);
  }
}
```

##### 3.3.2 Cosmos DB Repositories

```typescript
// TypeORM repositories for Cosmos DB
@Injectable()
export class QualificationRepository {
  constructor(
    @InjectConnection()
    private connection: Connection
  ) {}

  async findByOperatorAndSkill(
    operatorId: string,
    skillCode: string
  ): Promise<Qualification | undefined> {
    // Implementation using Cosmos DB SQL API
  }

  async save(qualification: Qualification): Promise<Qualification> {
    // Implementation for saving to Cosmos DB
  }

  // Additional repository methods
}

@Injectable()
export class PolyvalenceRepository {
  constructor(
    @InjectConnection()
    private connection: Connection
  ) {}

  async findByOperatorAndWorkstation(
    operatorId: string,
    workstationId: string
  ): Promise<Polyvalence | undefined> {
    // Implementation using Cosmos DB SQL API
  }

  async save(polyvalence: Polyvalence): Promise<Polyvalence> {
    // Implementation for saving to Cosmos DB
  }

  // Additional repository methods
}
```

##### 3.3.3 Change Feed Processors

```typescript
// Azure Function implementation for Cosmos DB Change Feed processing
export class MatrixChangeFeedProcessor {
  constructor(
    private cosmosClient: CosmosClient,
    private serviceBusClient: ServiceBusClient
  ) {}

  @Function("ProcessQualificationChanges")
  async processQualificationChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "CosmosDBConnection",
      databaseName: "VersatilityMatrix",
      containerName: "qualifications",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Implementation for processing qualification changes
    // Publish events to relevant consumers
  }

  @Function("ProcessPolyvalenceChanges")
  async processPolyvalenceChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "CosmosDBConnection",
      databaseName: "VersatilityMatrix",
      containerName: "polyvalence",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Implementation for processing polyvalence changes
    // Publish events to relevant consumers
  }
}
```

#### 3.4 NestJS Module Structure

The microservice is organized into NestJS modules for better code organization and dependency management:

```typescript
// Main module
@Module({
  imports: [
    ConfigModule.forRoot(),
    CosmosDbModule.forRoot({
      /* Cosmos DB connection config */
    }),
    ServiceBusModule.forRoot({
      /* Service Bus connection config */
    }),
    CqrsModule,
    OperatorsModule,
    QualificationsModule,
    PolyvalenceModule,
    WorkstationsModule,
    ReportsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

// Feature module example
@Module({
  imports: [CqrsModule, CosmosDbModule],
  controllers: [PolyvalenceController],
  providers: [
    PolyvalenceService,
    PolyvalenceRepository,
    UpdatePolyvalenceCommandHandler,
    GetPolyvalenceHistoryQueryHandler,
    // Event handlers, etc.
  ],
  exports: [PolyvalenceService],
})
export class PolyvalenceModule {}
```

---

### 4. Data Models

The Versatility Matrix implements a domain-driven data model with clear separation between write models and read models.

#### 4.1 Write Models (Command Side)

##### 4.1.1 Operator

```typescript
interface Operator {
  id: string; // Unique operator ID
  employeeNumber?: string; // Optional employee number from HR system
  name: string; // Full name of the operator
  hiringDate: Date; // Date when operator was hired
  areaId: string; // ID of the area/team the operator belongs to
  departmentId: string; // ID of the department (Assembling, Cutting & Lead Prep)
  isActive: boolean; // Whether the operator is active
  lastUpdated: Date; // Last update timestamp
}
```

##### 4.1.2 Area

```typescript
interface Area {
  id: string; // Unique area/team ID
  name: string; // Name of the area/team
  departmentId: string; // ID of the department this area belongs to
  description?: string; // Optional description
}
```

##### 4.1.3 Department

```typescript
interface Department {
  id: string; // Unique department ID
  name: string; // Name of the department (Assembling, Cutting & Lead Prep)
  description?: string; // Optional description
}
```

##### 4.1.4 Skill

```typescript
interface Skill {
  code: string; // Unique skill code (e.g., USW, TQ)
  name: string; // Readable name of the skill
  description?: string; // Optional detailed description
  complianceFlag?: string; // Optional industry standard reference (e.g., IPC/WHMA-A-620)
  departmentId: string; // ID of the department this skill belongs to
}
```

##### 4.1.5 Workstation

```typescript
interface Workstation {
  id: string; // Unique workstation ID
  name: string; // Name of the workstation
  departmentId: string; // ID of the department this workstation belongs to
  areaId?: string; // Optional ID of the area this workstation belongs to
  criticalityLevel: "Critical" | "Medium" | "Normal"; // Criticality level
  targetBackupOperators: number; // Target number of backup operators based on criticality
  requiredSkills: string[]; // List of required skill codes
}
```

##### 4.1.6 Qualification

```typescript
interface Qualification {
  id: string; // Unique ID for this qualification record (for change tracking)
  operatorId: string; // ID of the operator
  skillCode: string; // Code of the skill
  status: "O" | "LC" | "V" | "C" | "R" | "F" | null; // Current qualification status
  statusDescription: {
    // Human-readable descriptions for UI display
    O: "On Job Training";
    LC: "Learning Curve";
    V: "Validated";
    C: "Certified";
    R: "Re-certified";
    F: "Formé (Trained)";
  };
  certificationDate?: Date; // Date of certification (for C/R statuses)
  expiryDate?: Date; // Expiry date of certification (for C/R statuses)
  lastUpdated: Date; // Last update timestamp
  updatedBy: string; // ID of the user who updated this (or system ID for automated updates)
  sourceEventId?: string; // ID of the source event from Training Process (for traceability)
  historyEntries?: QualificationHistoryEntry[]; // Optional embedded history entries
  isActive: boolean; // Whether this qualification is currently active
}

interface QualificationHistoryEntry {
  id: string; // Unique ID for this history entry
  timestamp: Date; // When the change occurred
  previousStatus?: "O" | "LC" | "V" | "C" | "R" | "F" | null; // Previous status
  newStatus: "O" | "LC" | "V" | "C" | "R" | "F" | null; // New status
  updatedBy: string; // Who made the change
  sourceEventId?: string; // ID of the source event
  notes?: string; // Optional notes about the change
}
```

##### 4.1.7 Polyvalence

```typescript
interface Polyvalence {
  id: string; // Unique ID for this polyvalence record (for change tracking)
  operatorId: string; // ID of the operator
  workstationId: string; // ID of the workstation
  level: "X" | "1" | "2" | "3" | "4" | null; // Current polyvalence level
  levelDescription: {
    // Human-readable descriptions for UI display
    X: "Owner (Primary Operator)";
    "1": "Less than 1 month experience";
    "2": "1-2 months experience";
    "3": "2-6 months experience";
    "4": "More than 6 months experience";
  };
  lastUpdated: Date; // Last update timestamp
  updatedBy: string; // ID of the user who updated this (Team Leader)
  notes?: string; // Optional notes about the update
  historyEntries?: PolyvalenceHistoryEntry[]; // Optional embedded history entries
  isActive: boolean; // Whether this polyvalence assignment is active
}

interface PolyvalenceHistoryEntry {
  id: string; // Unique ID for this history entry
  timestamp: Date; // When the change occurred
  previousLevel?: "X" | "1" | "2" | "3" | "4" | null; // Previous level
  newLevel: "X" | "1" | "2" | "3" | "4" | null; // New level
  updatedBy: string; // Who made the change
  notes?: string; // Optional notes about the change
}
```

##### 4.1.8 AuditLog

```typescript
interface AuditLog {
  id: string; // Unique audit log ID
  timestamp: Date; // Timestamp of the action
  userId: string; // ID of the user who performed the action
  action: string; // Description of the action
  entityType: string; // Type of entity affected (Qualification, Polyvalence, etc.)
  entityId: string; // ID of the entity affected
  oldValue?: string; // Optional previous value
  newValue?: string; // Optional new value
  ipAddress?: string; // Optional IP address of the user
  userAgent?: string; // Optional user agent information
}
```

#### 4.2 Read Models (Query Side)

##### 4.2.1 MatrixView

```typescript
interface MatrixView {
  operators: OperatorView[]; // List of operators with their details
  skills: SkillView[]; // List of skills for qualification columns
  workstations: WorkstationView[]; // List of workstations for polyvalence columns
  qualifications: QualificationView[]; // Flattened list of all qualifications
  polyvalence: PolyvalenceView[]; // Flattened list of all polyvalence levels
  metadata: {
    departmentId: string; // Current department filter
    areaId?: string; // Optional area filter
    lastUpdated: Date; // Last update timestamp
  };
}

interface OperatorView {
  id: string; // Operator ID
  name: string; // Operator name
  hiringDate: Date; // Hiring date
  areaName: string; // Area/team name
}

interface SkillView {
  code: string; // Skill code
  name: string; // Skill name
  description?: string; // Optional description
}

interface WorkstationView {
  id: string; // Workstation ID
  name: string; // Workstation name
  criticalityLevel: string; // Criticality level
  targetBackupOperators: number; // Target number of backup operators
  actualBackupOperators: number; // Actual number of qualified backup operators
  requiredSkills: string[]; // List of required skill codes
}

interface QualificationView {
  operatorId: string; // Operator ID
  skillCode: string; // Skill code
  status: string; // Qualification status (O, LC, V, C, R, F)
  certificationDate?: Date; // Certification date
  expiryDate?: Date; // Expiry date
}

interface PolyvalenceView {
  operatorId: string; // Operator ID
  workstationId: string; // Workstation ID
  level: string; // Polyvalence level (X, 1, 2, 3, 4)
  lastUpdated: Date; // Last update timestamp
}
```

##### 4.2.2 WorkstationCoverageView

```typescript
interface WorkstationCoverageView {
  workstations: WorkstationCoverage[]; // List of workstations with coverage details
  metadata: {
    departmentId: string; // Current department filter
    areaId?: string; // Optional area filter
    lastUpdated: Date; // Last update timestamp
    totalWorkstations: number; // Total number of workstations
    criticalWorkstations: number; // Number of critical workstations
    workstationsWithGaps: number; // Number of workstations with coverage gaps
  };
}

interface WorkstationCoverage {
  id: string; // Workstation ID
  name: string; // Workstation name
  criticalityLevel: "Critical" | "Medium" | "Normal"; // Criticality level
  targetBackupOperators: number; // Target number of backup operators based on criticality (3, 1, or 0)
  actualBackupOperators: number; // Actual number of qualified backup operators
  gap: number; // Target - Actual (negative means excess, positive means deficit)
  coveragePercentage: number; // Percentage of target that's covered (100+ means fully covered)
  requiredSkills: {
    // Skills required for this workstation
    code: string;
    name: string;
  }[];
  owner?: OperatorBasic; // Primary operator (X level)
  qualifiedOperators: OperatorBasic[]; // List of qualified backup operators (C/R status with any polyvalence)

  /*
   * Calculation Logic (based on FDS 3.a.2 Criticality Targets & Actuals):
   * - Critical Workstations: Target = 3 backup operators (C/R qualified)
   * - Medium Criticality: Target = 1 backup operator (C/R qualified)
   * - Normal Criticality: Target = 0 (no additional operators needed)
   *
   * Actual Value: Count of operators who:
   * 1. Have C or R qualification status for ALL required skills
   * 2. Have ANY polyvalence level (1-4 or X) at this workstation
   * 3. If owner (X) has C/R qualification, they count; if not, they're excluded
   */
}

interface OperatorBasic {
  id: string; // Operator ID
  name: string; // Operator name
  polyvalenceLevel?: string; // Optional polyvalence level for this workstation
  qualificationStatus: string; // Qualification status for the required skill
}
```

##### 4.2.3 OperatorDetailView

```typescript
interface OperatorDetailView {
  id: string; // Operator ID
  employeeNumber?: string; // Optional employee number
  name: string; // Operator name
  hiringDate: Date; // Hiring date
  area: {
    id: string; // Area ID
    name: string; // Area name
    department: {
      id: string; // Department ID
      name: string; // Department name
    };
  };
  qualifications: QualificationDetail[]; // List of qualification details
  polyvalence: PolyvalenceDetail[]; // List of polyvalence details
  statistics: {
    totalQualifications: number; // Total number of qualifications
    certifiedQualifications: number; // Number of C/R qualifications
    ownedWorkstations: number; // Number of workstations with X level
    averagePolyvalenceLevel: number; // Average polyvalence level (excluding X)
  };
}

interface QualificationDetail {
  skillCode: string; // Skill code
  skillName: string; // Skill name
  status: string; // Qualification status
  certificationDate?: Date; // Certification date
  expiryDate?: Date; // Expiry date
  daysUntilExpiry?: number; // Days until expiry (if applicable)
}

interface PolyvalenceDetail {
  workstationId: string; // Workstation ID
  workstationName: string; // Workstation name
  level: string; // Polyvalence level
  criticalityLevel: string; // Workstation criticality level
  lastUpdated: Date; // Last update timestamp
  updatedBy: string; // Name of the user who last updated this
}
```

#### 4.3 Cosmos DB Container Structure

The Versatility Matrix microservice uses the following Azure Cosmos DB containers optimized for the specific query patterns and entity relationships:

```typescript
// Container definitions with partition keys
interface CosmosContainer {
  name: string;
  partitionKey: string;
  purpose: string;
  ttl?: boolean; // Time-to-live enabled
}

const containers: CosmosContainer[] = [
  {
    name: "operators",
    partitionKey: "/departmentId",
    purpose: "Stores operator data with embedded area information",
  },
  {
    name: "skills",
    partitionKey: "/departmentId",
    purpose: "Stores skill definitions including compliance information",
  },
  {
    name: "workstations",
    partitionKey: "/departmentId",
    purpose:
      "Stores workstation definitions with required skills and criticality",
  },
  {
    name: "qualifications",
    partitionKey: "/operatorId",
    purpose: "Stores qualification status records with certification dates",
  },
  {
    name: "qualification-history",
    partitionKey: "/operatorId",
    purpose: "Stores historical qualification status changes",
    ttl: true, // Optional TTL for older records
  },
  {
    name: "polyvalence",
    partitionKey: "/operatorId",
    purpose: "Stores polyvalence level records assigned by Team Leaders",
  },
  {
    name: "polyvalence-history",
    partitionKey: "/operatorId",
    purpose: "Stores historical polyvalence level changes",
    ttl: true, // Optional TTL for older records
  },
  {
    name: "workstation-coverage",
    partitionKey: "/departmentId",
    purpose: "Stores pre-computed workstation coverage metrics",
  },
  {
    name: "audit-logs",
    partitionKey: "/entityType",
    purpose: "Stores audit log entries for compliance tracking",
    ttl: true, // TTL based on retention policy
  },
];
```

#### 4.4 Entity Relationships

The domain entities are related as follows:

```typescript
// Entity relationship diagram (pseudocode)
interface EntityRelationships {
  Operator: {
    belongsTo: ["Department", "Area"];
    hasMany: ["Qualification", "Polyvalence"];
  };

  Department: {
    hasMany: ["Area", "Skill", "Workstation", "Operator"];
  };

  Area: {
    belongsTo: ["Department"];
    hasMany: ["Operator", "Workstation"];
  };

  Skill: {
    belongsTo: ["Department"];
    hasMany: ["Qualification"];
    requiredBy: ["Workstation"];
  };

  Workstation: {
    belongsTo: ["Department", "Area?"]; // Optional area
    requires: ["Skill"]; // Many-to-many
    hasMany: ["Polyvalence"];
    hasOne: ["WorkstationCoverage"];
  };

  Qualification: {
    belongsTo: ["Operator", "Skill"];
    hasMany: ["QualificationHistoryEntry"];
  };

  Polyvalence: {
    belongsTo: ["Operator", "Workstation"];
    hasMany: ["PolyvalenceHistoryEntry"];
  };
}
```

##### 4.4.1 Domain Model Class Diagram

The following class diagram visualizes the domain model and entity relationships:

```mermaid
classDiagram
    class Operator {
        +string id
        +string employeeNumber
        +string name
        +Date hiringDate
        +string areaId
        +string departmentId
        +boolean isActive
        +Date lastUpdated
    }

    class Department {
        +string id
        +string name
        +string description
    }

    class Area {
        +string id
        +string name
        +string departmentId
        +string description
    }

    class Skill {
        +string code
        +string name
        +string description
        +string complianceFlag
        +string departmentId
    }

    class Workstation {
        +string id
        +string name
        +string departmentId
        +string areaId
        +CriticalityLevel criticalityLevel
        +number targetBackupOperators
        +string[] requiredSkills
    }

    class Qualification {
        +string id
        +string operatorId
        +string skillCode
        +QualificationStatus status
        +Date certificationDate
        +Date expiryDate
        +Date lastUpdated
        +string updatedBy
        +string sourceEventId
        +boolean isActive
    }

    class QualificationHistoryEntry {
        +string id
        +Date timestamp
        +QualificationStatus previousStatus
        +QualificationStatus newStatus
        +string updatedBy
        +string sourceEventId
        +string notes
    }

    class Polyvalence {
        +string id
        +string operatorId
        +string workstationId
        +PolyvalenceLevel level
        +Date lastUpdated
        +string updatedBy
        +string notes
        +boolean isActive
    }

    class PolyvalenceHistoryEntry {
        +string id
        +Date timestamp
        +PolyvalenceLevel previousLevel
        +PolyvalenceLevel newLevel
        +string updatedBy
        +string notes
    }

    class WorkstationCoverage {
        +string workstationId
        +number targetBackupOperators
        +number actualBackupOperators
        +number gap
        +Date lastCalculated
    }

    class AuditLog {
        +string id
        +Date timestamp
        +string userId
        +string action
        +string entityType
        +string entityId
        +string oldValue
        +string newValue
    }

    %% Relationships
    Department "1" -- "many" Area : contains
    Department "1" -- "many" Operator : employs
    Department "1" -- "many" Skill : defines
    Department "1" -- "many" Workstation : defines

    Area "1" -- "many" Operator : groups
    Area "1" -- "many" Workstation : contains

    Operator "1" -- "many" Qualification : has
    Operator "1" -- "many" Polyvalence : has

    Skill "1" -- "many" Qualification : defines
    Skill "many" -- "many" Workstation : required by

    Workstation "1" -- "many" Polyvalence : assigned to
    Workstation "1" -- "1" WorkstationCoverage : tracks

    Qualification "1" -- "many" QualificationHistoryEntry : tracks
    Polyvalence "1" -- "many" PolyvalenceHistoryEntry : tracks

    %% Enums
    class QualificationStatus {
        <<enumeration>>
        O
        LC
        V
        C
        R
        F
    }

    class PolyvalenceLevel {
        <<enumeration>>
        X
        1
        2
        3
        4
    }

    class CriticalityLevel {
        <<enumeration>>
        Critical
        Medium
        Normal
    }
```

#### 4.5 Data Access Patterns

The microservice implements the following data access patterns to optimize performance with Cosmos DB:

```typescript
// Common query patterns and their implementation
interface DataAccessPattern {
  pattern: string;
  implementation: string;
  container: string;
  indexing: string;
}

const dataAccessPatterns: DataAccessPattern[] = [
  {
    pattern: "Get matrix view with filtering",
    implementation: "Query pre-computed projections with filter conditions",
    container: "workstation-coverage",
    indexing: "Composite indexes on departmentId, areaId, criticalityLevel",
  },
  {
    pattern: "Find qualification status for operator/skill",
    implementation:
      "Point read using partition key (operatorId) and id pattern (skillCode)",
    container: "qualifications",
    indexing: "Standard partition key indexing",
  },
  {
    pattern: "Update polyvalence level",
    implementation: "Upsert with optimistic concurrency via ETag",
    container: "polyvalence",
    indexing: "Standard partition key indexing",
  },
  {
    pattern: "Calculate workstation coverage",
    implementation: "Cross-container query with materialized view refresh",
    container: "qualifications, polyvalence, workstations",
    indexing: "Composite indexes on required skills and status",
  },
  {
    pattern: "Find qualified operators for workstation",
    implementation: "Query using required skills and status filters",
    container: "qualifications, polyvalence",
    indexing: "Composite indexes on skill, status, and workstationId",
  },
];
```

#### 4.6 Change Feed Processing Strategy

The Change Feed processing is implemented to maintain consistency across read models and integrate with external systems:

```typescript
// Change Feed processor definitions
interface ChangeFeedProcessor {
  sourceName: string; // Source container
  triggerName: string; // Azure Function trigger name
  purpose: string;
  targets: string[]; // Target systems/containers
}

const changeFeedProcessors: ChangeFeedProcessor[] = [
  {
    sourceName: "qualifications",
    triggerName: "ProcessQualificationChanges",
    purpose: "Update workstation coverage metrics when qualifications change",
    targets: ["workstation-coverage", "Training Process Read Models"],
  },
  {
    sourceName: "polyvalence",
    triggerName: "ProcessPolyvalenceChanges",
    purpose:
      "Update workstation coverage metrics when polyvalence levels change",
    targets: ["workstation-coverage", "Training Process Read Models"],
  },
  {
    sourceName: "workstations",
    triggerName: "ProcessWorkstationChanges",
    purpose: "Recalculate coverage when workstations or required skills change",
    targets: ["workstation-coverage"],
  },
  {
    sourceName: "operators",
    triggerName: "ProcessOperatorChanges",
    purpose: "Update operator information in projections",
    targets: ["workstation-coverage"],
  },
];
```

---

### 5. API Specification

#### 5.1 REST Endpoints

##### 5.1.1 Matrix API

```
GET /api/matrix
```

Retrieves the versatility matrix data with optional filtering.

**Query Parameters:**

- `departmentId` (required): Department ID to filter by
- `areaId` (optional): Area/team ID to filter by
- `skillCode` (optional): Skill code to filter by
- `workstationId` (optional): Workstation ID to filter by
- `qualificationStatus` (optional): Qualification status to filter by (O, LC, V, C, R, F)
- `polyvalenceLevel` (optional): Polyvalence level to filter by (X, 1, 2, 3, 4)
- `operatorId` (optional): Operator ID to filter by
- `searchTerm` (optional): Search term to filter by (matches operator name, workstation name, etc.)
- `page` (optional): Page number for pagination (default: 1)
- `pageSize` (optional): Page size for pagination (default: 50)

**Response:**

```json
{
  "data": {
    "operators": [
      {
        "id": "op-123",
        "name": "John Doe",
        "hiringDate": "2020-01-01T00:00:00Z",
        "areaName": "Team A"
      }
      // Additional operators...
    ],
    "skills": [
      {
        "code": "USW",
        "name": "Ultrasonic Splice",
        "description": "Joining wires via high-frequency vibrations"
      }
      // Additional skills...
    ],
    "workstations": [
      {
        "id": "WS-TQ01",
        "name": "Torque Station 1",
        "criticalityLevel": "Critical",
        "targetBackupOperators": 3,
        "actualBackupOperators": 2,
        "requiredSkills": ["TQ"]
      }
      // Additional workstations...
    ],
    "qualifications": [
      {
        "operatorId": "op-123",
        "skillCode": "USW",
        "status": "C",
        "certificationDate": "2022-05-15T00:00:00Z",
        "expiryDate": "2023-05-15T00:00:00Z"
      }
      // Additional qualifications...
    ],
    "polyvalence": [
      {
        "operatorId": "op-123",
        "workstationId": "WS-TQ01",
        "level": "3",
        "lastUpdated": "2023-01-15T10:30:00Z"
      }
      // Additional polyvalence entries...
    ]
  },
  "metadata": {
    "departmentId": "dept-assy",
    "areaId": "area-team-a",
    "totalOperators": 120,
    "displayedOperators": 50,
    "page": 1,
    "pageSize": 50,
    "totalPages": 3,
    "lastUpdated": "2023-08-15T14:30:00Z"
  }
}
```

```
GET /api/matrix/export
```

Exports the matrix data to Excel or CSV format.

**Query Parameters:**

- Same as `/api/matrix` plus:
- `format` (required): Export format (excel, csv)

**Response:**

- Binary file download with appropriate MIME type

##### 5.1.2 Polyvalence API

```
PUT /api/polyvalence/{operatorId}/{workstationId}
```

Updates the polyvalence level for an operator at a specific workstation.

**URL Parameters:**

- `operatorId` (required): Operator ID
- `workstationId` (required): Workstation ID

**Request Body:**

```json
{
  "level": "3", // X, 1, 2, 3, 4, or null (to remove)
  "notes": "Operator completed 3 months at this station" // Optional notes
}
```

**Response:**

```json
{
  "operatorId": "op-123",
  "workstationId": "WS-TQ01",
  "level": "3",
  "lastUpdated": "2023-08-15T14:35:00Z",
  "updatedBy": "tl-user-456"
}
```

```
GET /api/polyvalence/history/{operatorId}
```

Retrieves the polyvalence update history for an operator.

**URL Parameters:**

- `operatorId` (required): Operator ID

**Response:**

```json
{
  "operatorId": "op-123",
  "name": "John Doe",
  "history": [
    {
      "workstationId": "WS-TQ01",
      "workstationName": "Torque Station 1",
      "oldLevel": "2",
      "newLevel": "3",
      "updatedBy": "TL User Name",
      "timestamp": "2023-08-15T14:35:00Z"
    }
    // Additional history entries...
  ]
}
```

##### 5.1.3 Qualification API

```
GET /api/qualification/{operatorId}
```

Retrieves the qualifications for a specific operator.

**URL Parameters:**

- `operatorId` (required): Operator ID

**Response:**

```json
{
  "operatorId": "op-123",
  "name": "John Doe",
  "qualifications": [
    {
      "skillCode": "USW",
      "skillName": "Ultrasonic Splice",
      "status": "C",
      "certificationDate": "2022-05-15T00:00:00Z",
      "expiryDate": "2023-05-15T00:00:00Z",
      "daysUntilExpiry": 45
    }
    // Additional qualifications...
  ]
}
```

```
GET /api/qualification/expiring
```

Retrieves certifications that are nearing expiry.

**Query Parameters:**

- `departmentId` (required): Department ID
- `areaId` (optional): Area ID
- `daysThreshold` (optional): Days threshold for expiry (default: 30)

**Response:**

```json
{
  "expiringCertifications": [
    {
      "operatorId": "op-123",
      "operatorName": "John Doe",
      "skillCode": "USW",
      "skillName": "Ultrasonic Splice",
      "expiryDate": "2023-05-15T00:00:00Z",
      "daysUntilExpiry": 12
    }
    // Additional expiring certifications...
  ]
}
```

##### 5.1.4 Workstation API

```
GET /api/workstation/coverage
```

Retrieves coverage metrics for workstations.

**Query Parameters:**

- `departmentId` (required): Department ID
- `areaId` (optional): Area ID
- `criticalityLevel` (optional): Filter by criticality level (Critical, Medium, Normal)
- `gapThreshold` (optional): Filter by coverage gap (positive number)

**Response:**

```json
{
  "workstations": [
    {
      "id": "WS-TQ01",
      "name": "Torque Station 1",
      "criticalityLevel": "Critical",
      "targetBackupOperators": 3,
      "actualBackupOperators": 2,
      "gap": 1,
      "owner": {
        "id": "op-456",
        "name": "Jane Smith",
        "polyvalenceLevel": "X",
        "qualificationStatus": "C"
      },
      "qualifiedOperators": [
        {
          "id": "op-123",
          "name": "John Doe",
          "polyvalenceLevel": "3",
          "qualificationStatus": "C"
        },
        {
          "id": "op-789",
          "name": "Alice Johnson",
          "polyvalenceLevel": "2",
          "qualificationStatus": "R"
        }
      ]
    }
    // Additional workstations...
  ],
  "metadata": {
    "departmentId": "dept-assy",
    "areaId": "area-team-a",
    "totalWorkstations": 25,
    "criticalWorkstations": 8,
    "workstationsWithGaps": 3
  }
}
```

```
GET /api/workstation/{workstationId}/operators
```

Retrieves qualified operators for a specific workstation.

**URL Parameters:**

- `workstationId` (required): Workstation ID

**Response:**

```json
{
  "workstationId": "WS-TQ01",
  "workstationName": "Torque Station 1",
  "criticalityLevel": "Critical",
  "requiredSkills": ["TQ"],
  "operators": [
    {
      "id": "op-456",
      "name": "Jane Smith",
      "polyvalenceLevel": "X",
      "qualificationStatus": "C",
      "certificationExpiryDate": "2024-03-15T00:00:00Z"
    }
    // Additional operators...
  ]
}
```

##### 5.1.5 Report API

```
GET /api/reports/skill-certification
```

Generates a skill certification history report.

**Query Parameters:**

- `departmentId` (optional): Department ID
- `areaId` (optional): Area ID
- `operatorId` (optional): Operator ID
- `skillCode` (optional): Skill code
- `format` (optional): Report format (json, excel, csv) - default: json

**Response:**
Report data in the requested format.

```
GET /api/reports/workstation-coverage
```

Generates a workstation coverage report.

**Query Parameters:**

- `departmentId` (required): Department ID
- `areaId` (optional): Area ID
- `format` (optional): Report format (json, excel, csv) - default: json

**Response:**
Report data in the requested format.

#### 5.2 Authorization Requirements

The API enforces RBAC for all endpoints:

| Endpoint                                          | Center Trainer   | Team Leader                   | Shift Leader & Coordinator      | Quality Auditor  |
| ------------------------------------------------- | ---------------- | ----------------------------- | ------------------------------- | ---------------- |
| GET /api/matrix                                   | All areas        | Team area only                | Assigned area only              | Read-only access |
| GET /api/matrix/export                            | All areas        | Team area only                | Assigned area only              | Read-only access |
| PUT /api/polyvalence/{operatorId}/{workstationId} | Not allowed      | Allowed for team members only | Not allowed                     | Not allowed      |
| GET /api/polyvalence/history/{operatorId}         | All operators    | Team operators only           | Assigned area operators only    | Read-only access |
| GET /api/qualification/{operatorId}               | All operators    | Team operators only           | Assigned area operators only    | Read-only access |
| GET /api/qualification/expiring                   | All areas        | Team area only                | Assigned area only              | Read-only access |
| GET /api/workstation/coverage                     | All areas        | Team area only                | Assigned area only              | Read-only access |
| GET /api/workstation/{workstationId}/operators    | All workstations | Team workstations only        | Assigned area workstations only | Read-only access |
| GET /api/reports/\*                               | All areas        | Team area only                | Assigned area only              | Read-only access |

### 6. Event Communication

#### 6.1 Inbound Events

The Versatility Matrix continuously listens for and processes events from external systems to ensure it maintains an accurate reflection of the operational reality.

##### 6.1.1 Qualification Updates from Training Process - Sequence Diagram

The following sequence diagram illustrates the flow of qualification updates from the Training Process to the Versatility Matrix:

```mermaid
sequenceDiagram
    participant TP as Training Process
    participant SB as Azure Service Bus
    participant VMP as Versatility Matrix Processor
    participant VMDB as Versatility Matrix DB
    participant AH as Audit History

    TP->>TP: Qualification status change
    TP->>SB: Publish QualificationChangedEvent
    SB-->>VMP: Receive event

    VMP->>VMP: Validate event data

    alt Valid Qualification Update
        VMP->>VMDB: Find existing qualification

        alt Qualification Exists
            VMP->>VMDB: Update qualification status
            VMP->>AH: Log qualification change
            VMP->>VMDB: Update workstation coverage

            opt Certification Expiry Alert Check
                VMP->>VMP: Check if certification is expiring soon

                alt Expiring Soon
                    VMP->>SB: Publish CertificationExpiryAlertEvent
                end
            end

        else No Existing Qualification
            VMP->>VMDB: Create new qualification
            VMP->>AH: Log new qualification
        end

        VMP->>SB: Acknowledge event processed

    else Invalid Event Data
        VMP->>AH: Log error in processing
        VMP->>SB: Dead-letter the message
    end
```

This sequence diagram shows the complete flow of a qualification update event from the Training Process through the Versatility Matrix system. When a qualification status changes in the Training Process, an event is published to Azure Service Bus. The Versatility Matrix Processor consumes this event, validates it, and then either updates an existing qualification or creates a new one. The processor also logs audit history entries for compliance and maintains workstation coverage metrics. If the qualification includes certification that's expiring soon, the system will publish an alert event to notify relevant stakeholders.

##### 6.1.2 Operator Data Events

The Versatility Matrix processes operator data changes from the Crew Management service via Cosmos DB Change Feed.

#### 6.2 Outbound Events

##### 6.2.1 Internal Domain Events

The Versatility Matrix uses NestJS event publisher for internal domain events:

```typescript
// Domain event definitions
export class PolyvalenceUpdatedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly workstationId: string,
    public readonly oldLevel: PolyvalenceLevel | null,
    public readonly newLevel: PolyvalenceLevel | null,
    public readonly updatedBy: string,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class QualificationStatusSyncedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly skillCode: string,
    public readonly oldStatus: QualificationStatus | null,
    public readonly newStatus: QualificationStatus | null,
    public readonly certificationDate: Date | null,
    public readonly expiryDate: Date | null,
    public readonly sourceEventId: string | null,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class WorkstationCoverageChangedEvent {
  constructor(
    public readonly workstationId: string,
    public readonly oldCoverage: {
      targetBackupOperators: number;
      actualBackupOperators: number;
      gap: number;
    },
    public readonly newCoverage: {
      targetBackupOperators: number;
      actualBackupOperators: number;
      gap: number;
    },
    public readonly timestamp: Date = new Date()
  ) {}
}

// Event handler implementation with NestJS
@Injectable()
export class DomainEventsHandler {
  constructor(
    private readonly auditService: AuditService,
    private readonly workstationCoverageService: WorkstationCoverageService,
    private readonly projectionService: ProjectionService
  ) {}

  @EventsHandler(PolyvalenceUpdatedEvent)
  async handlePolyvalenceUpdated(event: PolyvalenceUpdatedEvent) {
    // 1. Create audit log entry
    await this.auditService.createAuditLog({
      entityType: "Polyvalence",
      entityId: `${event.operatorId}:${event.workstationId}`,
      action: "Update",
      oldValue: JSON.stringify(event.oldLevel),
      newValue: JSON.stringify(event.newLevel),
      userId: event.updatedBy,
      timestamp: event.timestamp,
    });

    // 2. Recalculate workstation coverage
    await this.workstationCoverageService.recalculateCoverage(
      event.workstationId
    );

    // 3. Update read model projections
    await this.projectionService.refreshWorkstationCoverageProjection(
      event.workstationId
    );
  }

  @EventsHandler(QualificationStatusSyncedEvent)
  async handleQualificationStatusSynced(event: QualificationStatusSyncedEvent) {
    // 1. Create audit log entry
    await this.auditService.createAuditLog({
      entityType: "Qualification",
      entityId: `${event.operatorId}:${event.skillCode}`,
      action: "Update",
      oldValue: JSON.stringify(event.oldStatus),
      newValue: JSON.stringify(event.newStatus),
      userId: "system", // System-triggered from Training Process
      timestamp: event.timestamp,
    });

    // 2. Find affected workstations that require this skill
    const affectedWorkstations =
      await this.workstationCoverageService.findWorkstationsRequiringSkill(
        event.skillCode
      );

    // 3. Recalculate coverage for affected workstations
    for (const workstationId of affectedWorkstations) {
      await this.workstationCoverageService.recalculateCoverage(workstationId);
      await this.projectionService.refreshWorkstationCoverageProjection(
        workstationId
      );
    }

    // 4. Check for certification expiry alerts
    if (event.newStatus === "C" || event.newStatus === "R") {
      if (event.expiryDate) {
        const daysUntilExpiry = Math.floor(
          (event.expiryDate.getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        );

        if (daysUntilExpiry <= 30) {
          // Trigger certification expiry alert event
          this.eventBus.publish(
            new CertificationExpiringAlertEvent(
              event.operatorId,
              event.skillCode,
              event.expiryDate,
              daysUntilExpiry
            )
          );
        }
      }
    }
  }

  @EventsHandler(WorkstationCoverageChangedEvent)
  async handleWorkstationCoverageChanged(
    event: WorkstationCoverageChangedEvent
  ) {
    // 1. Create audit log entry
    await this.auditService.createAuditLog({
      entityType: "WorkstationCoverage",
      entityId: event.workstationId,
      action: "Update",
      oldValue: JSON.stringify(event.oldCoverage),
      newValue: JSON.stringify(event.newCoverage),
      userId: "system",
      timestamp: event.timestamp,
    });

    // 2. Check for coverage gap alerts
    if (
      event.newCoverage.gap > 0 &&
      (event.oldCoverage.gap === 0 ||
        event.oldCoverage.gap < event.newCoverage.gap)
    ) {
      // Trigger coverage gap alert event
      this.eventBus.publish(
        new WorkstationCoverageGapAlertEvent(
          event.workstationId,
          event.newCoverage.targetBackupOperators,
          event.newCoverage.actualBackupOperators,
          event.newCoverage.gap
        )
      );
    }
  }
}
```

##### 6.2.2 Cosmos DB Change Feed for External Consumers

The Versatility Matrix exposes data changes to external systems via Cosmos DB Change Feed:

```typescript
// Optimize container for Change Feed consumption
@Injectable()
export class CosmosDbService {
  constructor(private readonly configService: ConfigService) {}

  async configureContainerForChangeFeed(containerName: string): Promise<void> {
    const client = new CosmosClient(
      this.configService.get<string>("COSMOS_DB_CONNECTION")
    );
    const database = client.database("VersatilityMatrix");
    const container = database.container(containerName);

    // Configure TTL and indexes optimally for change feed processing
    const indexingPolicy = {
      indexingMode: "consistent",
      includedPaths: [
        {
          path: "/*",
        },
      ],
      compositeIndexes: [
        [
          {
            path: "/_ts",
            order: "ascending",
          },
        ],
      ],
    };

    await container.replace({
      id: containerName,
      partitionKey: container.partitionKey,
      indexingPolicy,
    });
  }
}

// Change Feed processor for Training Process read model updates
export class QualificationChangeFeedProcessor {
  constructor(
    private readonly cosmosClient: CosmosClient,
    private readonly serviceBusClient: ServiceBusClient
  ) {}

  @Function("ProcessQualificationChanges")
  async processQualificationChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "COSMOS_DB_CONNECTION",
      databaseName: "VersatilityMatrix",
      containerName: "qualifications",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Create Service Bus client for publishing events
    const sender = this.serviceBusClient.createSender(
      "training-read-model-updates"
    );

    // Process each document
    for (const doc of documents) {
      try {
        // Create event from document
        const event = {
          eventType: "QualificationUpdated",
          operatorId: doc.operatorId,
          skillCode: doc.skillCode,
          status: doc.status,
          certificationDate: doc.certificationDate,
          expiryDate: doc.expiryDate,
          timestamp: new Date(doc._ts * 1000),
        };

        // Send event to Service Bus
        await sender.sendMessages({
          body: event,
          contentType: "application/json",
          messageId: `qual-${doc.id}-${doc._ts}`,
        });
      } catch (error) {
        // Log error and continue
      }
    }
  }
}

// Change Feed processor for polyvalence updates
export class PolyvalenceChangeFeedProcessor {
  constructor(
    private readonly cosmosClient: CosmosClient,
    private readonly serviceBusClient: ServiceBusClient
  ) {}

  @Function("ProcessPolyvalenceChanges")
  async processPolyvalenceChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "COSMOS_DB_CONNECTION",
      databaseName: "VersatilityMatrix",
      containerName: "polyvalence",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Create Service Bus client for publishing events
    const sender = this.serviceBusClient.createSender(
      "training-read-model-updates"
    );

    // Process each document
    for (const doc of documents) {
      try {
        // Create event from document
        const event = {
          eventType: "PolyvalenceUpdated",
          operatorId: doc.operatorId,
          workstationId: doc.workstationId,
          level: doc.level,
          updatedBy: doc.updatedBy,
          timestamp: new Date(doc._ts * 1000),
        };

        // Send event to Service Bus
        await sender.sendMessages({
          body: event,
          contentType: "application/json",
          messageId: `poly-${doc.id}-${doc._ts}`,
        });
      } catch (error) {
        // Log error and continue
      }
    }
  }
}

// Query handler for replacement process integration
@QueryHandler(FindQualifiedOperatorsQuery)
export class FindQualifiedOperatorsQueryHandler
  implements IQueryHandler<FindQualifiedOperatorsQuery>
{
  constructor(
    private readonly workstationRepository: WorkstationRepository,
    private readonly qualificationRepository: QualificationRepository,
    private readonly polyvalenceRepository: PolyvalenceRepository
  ) {}

  async execute(
    query: FindQualifiedOperatorsQuery
  ): Promise<QualifiedOperatorsResult> {
    // 1. Get workstation with required skills
    const workstation = await this.workstationRepository.findById(
      query.workstationId
    );
    if (!workstation) {
      throw new NotFoundException(
        `Workstation ${query.workstationId} not found`
      );
    }

    // 2. Find operators with C/R qualification for all required skills
    const requiredSkills = query.requiredSkills || workstation.requiredSkills;
    const qualifiedOperatorIds =
      await this.qualificationRepository.findOperatorsQualifiedForSkills(
        requiredSkills,
        ["C", "R"]
      );

    // 3. Get polyvalence levels for these operators at this workstation
    const polyvalenceLevels =
      await this.polyvalenceRepository.findPolyvalenceLevels(
        qualifiedOperatorIds,
        query.workstationId
      );

    // 4. Build result with operator details
    const qualifiedOperators = await this.buildQualifiedOperatorsResult(
      qualifiedOperatorIds,
      polyvalenceLevels,
      requiredSkills
    );

    // 5. Sort by polyvalence level (higher is better)
    const sortedOperators = this.sortOperatorsByPolyvalence(qualifiedOperators);

    return {
      workstationId: query.workstationId,
      requiredSkills,
      operators: sortedOperators,
    };
  }

  // Helper methods
  private async buildQualifiedOperatorsResult(/*...*/): Promise<
    QualifiedOperator[]
  > {
    // Implementation details
    return [];
  }

  private sortOperatorsByPolyvalence(
    operators: QualifiedOperator[]
  ): QualifiedOperator[] {
    // Sort by polyvalence level (4 > 3 > 2 > 1 > X) for replacement purposes
    const levelOrder = { "4": 5, "3": 4, "2": 3, "1": 2, X: 1, null: 0 };
    return operators.sort((a, b) => {
      return (
        levelOrder[b.polyvalenceLevel || null] -
        levelOrder[a.polyvalenceLevel || null]
      );
    });
  }
}
```

##### 6.2.1 Polyvalence Update Flow - Sequence Diagram

The following sequence diagram illustrates the flow when a Team Leader updates an operator's polyvalence rating through the Versatility Matrix UI:

```mermaid
sequenceDiagram
    autonumber
    participant TL as Team Leader
    participant UI as Versatility Matrix UI
    participant API as Versatility Matrix API
    participant Domain as Domain Layer
    participant DB as Database
    participant Audit as Audit History
    participant SB as Service Bus

    TL->>UI: Selects Operator & Workstation
    TL->>UI: Updates Rating (1-5)
    UI->>API: POST /api/polyvalence/update
    API->>Domain: PolyvalenceUpdateCommand
    Domain->>DB: Get Current Polyvalence

    alt Existing Polyvalence
        DB->>Domain: Return Current Polyvalence
        Domain->>DB: Update Polyvalence
    else No Existing Polyvalence
        DB->>Domain: Return Not Found
        Domain->>DB: Create New Polyvalence
    end

    Domain->>Audit: Log Polyvalence Change
    Domain->>Domain: Calculate Workstation Coverage Impact
    Domain->>DB: Update Workstation Coverage

    Domain->>SB: Publish PolyvalenceChangedEvent
    Domain->>API: Update Success
    API->>UI: Return Success
    UI->>TL: Display Success Notification
```

This sequence diagram visualizes the complete flow of updating an operator's polyvalence rating, from the Team Leader's action in the UI through the processing and persistence layers to the notification of other services via the Service Bus. The process includes important steps like logging the change for compliance and calculating the impact of the change on workstation coverage metrics.

##### 6.2.2 Qualification Update Flow - Sequence Diagram

The following sequence diagram illustrates the flow when a qualification update is received from the Training Process:

```mermaid
sequenceDiagram
    autonumber
    participant TP as Training Process
    participant SB as Service Bus
    participant CF as Change Feed Processor
    participant Domain as Domain Layer
    participant DB as Database
    participant Audit as Audit History
    participant VSM as Versatility Matrix UI

    TP->>SB: Publish TrainingCompletedEvent
    SB->>CF: Trigger Change Feed Processor
    CF->>Domain: QualificationUpdateCommand

    Domain->>DB: Get Operator Record
    DB->>Domain: Return Operator

    Domain->>DB: Get Existing Qualification

    alt Existing Qualification
        DB->>Domain: Return Qualification
        Domain->>Domain: Validate Training Certificate
        Domain->>DB: Update Qualification Status
    else No Existing Qualification
        Domain->>Domain: Create New Qualification
        Domain->>DB: Insert New Qualification
    end

    Domain->>Audit: Log Qualification Change
    Domain->>Domain: Calculate Affected Workstations
    Domain->>DB: Update Eligible Workstations

    Domain->>SB: Publish QualificationStatusSyncedEvent
    Domain->>SB: Publish WorkstationCoverageChangedEvent

    Domain->>CF: Process Complete

    VSM-->>SB: Subscribe to Events
    SB-->>VSM: Push Qualification Update
    VSM-->>VSM: Update UI (if open)
```

This sequence diagram illustrates how qualification updates originating from the Training Process are processed by the Versatility Matrix system. The flow begins when the Training Process publishes a `TrainingCompletedEvent` to the Service Bus, which is then picked up by the Change Feed Processor. The Domain Layer processes the qualification update, creates or updates qualification records in the database, logs the changes in the audit history, and calculates the impact on workstation eligibility. The system then publishes events to notify other systems of the changes, and the Versatility Matrix UI updates if it is currently open and subscribed to these events.

##### 6.2.3 Change Feed Processing Flow - Sequence Diagram

The following sequence diagram illustrates the general Change Feed Processing flow used throughout the system for event-driven integrations:

```mermaid
sequenceDiagram
    autonumber
    participant ES as External System
    participant CF as Cosmos DB Change Feed
    participant PR as Change Feed Processor
    participant SB as Service Bus
    participant Domain as Domain Layer
    participant DB as Database
    participant Audit as Audit History

    ES->>CF: Write Document to Change Feed
    CF->>PR: Trigger Processor (Document Change)
    PR->>PR: Extract Event Data
    PR->>Domain: Create Domain Command

    Domain->>Domain: Validate Command
    Domain->>DB: Query Required Data
    DB->>Domain: Return Data

    Domain->>Domain: Apply Business Rules
    Domain->>DB: Persist Changes
    Domain->>Audit: Log Action

    alt Process Success
        Domain->>SB: Publish Integration Events
        Domain->>PR: Return Success
        PR->>CF: Acknowledge Processing (Checkpoint)
    else Process Failure
        Domain->>PR: Return Failure with Error
        PR->>PR: Apply Retry Policy

        alt Retry Possible
            PR->>Domain: Retry Command (After Delay)
        else Max Retries Exceeded
            PR->>SB: Publish FailedProcessingEvent
            PR->>CF: Acknowledge with Failure (Checkpoint)
        end
    end
```

This sequence diagram depicts the general flow of the Change Feed processing mechanism used throughout the system. When an external system writes a document to Cosmos DB, the Change Feed detects the change and triggers the appropriate Change Feed Processor. The processor extracts the event data and creates a domain command, which is then validated and processed by the Domain Layer.

The Domain Layer applies business rules, persists changes to the database, and logs the action in the audit history. If the process is successful, integration events are published to the Service Bus, and the processor acknowledges the processing by creating a checkpoint in the Change Feed. If the process fails, a retry policy is applied. If retries are possible, the command is retried after a delay. If the maximum number of retries is exceeded, a FailedProcessingEvent is published to the Service Bus, and the processor acknowledges the failure by creating a checkpoint in the Change Feed.

This pattern is used consistently throughout the system for various integrations, including qualification updates, operator transfers, and other event-driven processes.

#### 6.3 Event Schema Definitions

The Versatility Matrix uses the following event schema definitions for domain events:

- **PolyvalenceUpdatedEvent**: This event is published when an operator's polyvalence level changes.
- **QualificationStatusSyncedEvent**: This event is published when a qualification status changes.
- **WorkstationCoverageChangedEvent**: This event is published when the coverage for a workstation changes.

#### 6.4 Domain Model - Class Diagram

The following class diagram illustrates the key entity relationships in the Versatility Matrix domain model:

```mermaid
classDiagram
    class VersatilityMatrix {
        +string id
        +string plantId
        +string departmentId
        +DateTime lastUpdated
        +List~WorkstationGroup~ workstationGroups
        +calculateEligibility()
        +updateQualification()
        +updatePolyvalence()
    }

    class WorkstationGroup {
        +string id
        +string name
        +List~Workstation~ workstations
        +List~OperatorQualification~ operatorQualifications
        +addWorkstation()
        +removeWorkstation()
        +calculateGroupPolyvalence()
    }

    class Workstation {
        +string id
        +string name
        +string description
        +WorkstationType type
        +int requiredOperators
        +List~Qualification~ qualifications
        +bool isActive
        +DateTime lastUpdated
        +calculateEligibility()
    }

    class Operator {
        +string id
        +string employeeId
        +string name
        +string teamId
        +bool isActive
        +WorkShift shift
        +DateTime startDate
        +DateTime? endDate
    }

    class OperatorQualification {
        +string operatorId
        +Map~string, QualificationLevel~ qualifications
        +DateTime lastUpdated
        +updateQualification()
        +calculateEligibility()
    }

    class Qualification {
        +string id
        +string name
        +string description
        +QualificationType type
        +bool isRequired
        +DateTime validUntil
        +DateTime lastUpdated
    }

    class AuditRecord {
        +string id
        +string entityId
        +string entityType
        +string action
        +string userId
        +string userName
        +DateTime timestamp
        +object previousState
        +object newState
        +string systemSource
    }

    class QualificationLevel {
        <<enumeration>>
        NONE
        IN_TRAINING
        QUALIFIED
        TRAINER
        EXPIRED
    }

    class QualificationType {
        <<enumeration>>
        SAFETY
        TECHNICAL
        QUALITY
        PROCESS
    }

    class WorkstationType {
        <<enumeration>>
        ASSEMBLY
        MACHINING
        TESTING
        PACKAGING
        LOGISTICS
    }

    VersatilityMatrix "1" *-- "many" WorkstationGroup
    WorkstationGroup "1" *-- "many" Workstation
    WorkstationGroup "1" *-- "many" OperatorQualification
    Workstation "1" *-- "many" Qualification
    OperatorQualification "1" -- "1" Operator
    OperatorQualification "many" -- "many" Qualification
    VersatilityMatrix "1" -- "many" AuditRecord
    OperatorQualification -- QualificationLevel
    Qualification -- QualificationType
    Workstation -- WorkstationType
```

This class diagram shows the core domain entities of the Versatility Matrix system. The VersatilityMatrix is the root aggregate that contains WorkstationGroups. Each WorkstationGroup contains Workstations and OperatorQualifications. The OperatorQualification represents the qualifications an Operator has for various workstations within a group. Qualifications are attributes of Workstations that Operators must possess to be eligible to work at those stations.

The AuditRecord entity is used to track changes to the system, capturing the previous and new states of entities, along with who made the change and when. The system uses enumeration types like QualificationLevel, QualificationType, and WorkstationType to categorize and define the properties of the entities.

This domain model is designed to support the calculation of polyvalence metrics and workstation eligibility, which are core functions of the Versatility Matrix system.

### 7. Conclusion

The Versatility Matrix microservice, as detailed in this Low-Level Design document, provides a robust and scalable solution for managing operator skills and workstation proficiency. By leveraging a CQRS architecture, event-driven integrations, and a comprehensive data model, the system effectively addresses the complex requirements of real-time tracking, reporting, and compliance. The defined API endpoints and event communication mechanisms ensure seamless integration with other components of the Connected Workers ecosystem, contributing to overall operational efficiency and informed decision-making. Future enhancements can build upon this solid foundation to incorporate advanced analytics, machine learning-based suggestions, and broader integration with other enterprise systems.
