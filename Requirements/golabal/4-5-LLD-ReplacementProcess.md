# Low-Level Design Document: Operator Replacement Process

## Document Information

**Version:** 1.2.0  
**Last Updated:** 2025-05-05  
**Status:** Draft  
**Authors: <AUTHORS>

## Table of Contents

<!-- ## 5.2.5 Replacement Management -->

5.2.5 [Replacement Management](#525-replacement-management)

1. [Overview](#1-overview)

2. [Architecture Overview](#2-architecture-overview)

   1. [Architectural Patterns](#21-architectural-patterns)
   2. [High-Level Component Diagram](#22-high-level-component-diagram)

3. [Data Models](#3-data-models)
   1. [Write Models](#31-write-models)
   2. [Read Models](#32-read-models)
4. [API Specification](#4-api-specification)

   1. [Replacement Service APIs](#41-replacement-service-apis)
   2. [Panier Management APIs](#42-panier-management-apis)
   3. [Evaluation Service APIs](#43-evaluation-service-apis)
   4. [Reporting Service APIs](#44-reporting-service-apis)

5. [Security and Permission Models](#5-security-and-permission-models)
   1. [Authorization Model](#51-authorization-model)
   2. [Security Implementation](#52-security-implementation)
6. [Conclusion](#6-conclusion)

## 5.2.5 Replacement Management

### 1. Overview

The Operator Replacement Process is a critical component within the Connected Workers (CW) system designed to manage operator absenteeism efficiently and effectively. This Low-Level Design document outlines the technical architecture, implementation patterns, and specifications needed to build a robust solution that addresses this challenge.

In manufacturing environments, unexpected operator absences can significantly impact production efficiency and quality. The Replacement Process system addresses this by providing structured workflows for handling absences, leveraging a multi-level approach based on absence severity, and ensuring optimal operator assignment through skills-based recommendations.

This document details a microservices-based architecture implementing CQRS (Command Query Responsibility Segregation) and event-driven patterns to provide a scalable, maintainable solution. The system is built on Azure cloud services, leveraging Cosmos DB for data storage and Service Bus for asynchronous messaging.

The design supports various stakeholders, including Team Leaders (TLs), ShiftLeaders (SLs), and management, and provides appropriate interfaces for each role based on their responsibilities in the replacement process. Real-time notifications, recommendation engines, and comprehensive reporting capabilities ensure that all participants have the information they need to make timely decisions.

#### 1.1 High-Level Flow

The Operator Replacement Process follows these key workflows:

1. **Absence Detection and Initiation**:

   - TL verifies operator presence
   - System identifies absences and initiates replacement process

2. **Replacement Handling (multi-level)**:

   - **Low Level**: TL-managed using backup structure or polyvalence matrix
   - **Medium Level**: SL-managed using SL Backup Panier
   - **High Level**: Department-level coordination using Department Panier
   - **Critical Level**: To be defined (out of scope for this version)

3. **Recommendation Decision Support**:

   - System suggests replacements based on qualifications, skills, and historical evaluations

4. **Notification and Confirmation**:

   - Real-time updates to relevant stakeholders
   - Confirmation workflows for replacement decisions

5. **Evaluation and Reporting**:
   - End-of-shift evaluations by TLs
   - Historical reporting for management

### 2. Architecture Overview

#### 2.1 Architectural Patterns

The Replacement Process implementation follows these key architectural patterns:

##### 2.1.1 CQRS (Command Query Responsibility Segregation)

- **Command Side**: Handles write operations and business rules validation within the Replacement Service
- **Query Side**: Optimized for read operations with specialized data projections within the same Replacement Service
- **Benefits**: While we maintain logical separation of concerns, the operations are consolidated in one service

##### 2.1.2 Event-Driven Architecture

- Commands produce domain events when state changes
- Services communicate asynchronously through events
- Provides decoupling between services and ensures eventual consistency

##### 2.1.3 Microservices

- Bounded contexts aligned with business capabilities
- Independent deployment and scaling
- Loose coupling through standardized APIs and event contracts

#### 2.2 High-Level Component Diagram

```mermaid
flowchart TB
    Client["Client Apps"]
    Gateway["API Gateway"]
    Identity["Identity Service"]

    Client --> Gateway
    Gateway --> Identity

    subgraph "Connected Workers System"
        ReplacementSvc["Replacement Service"]
        NotifSvc["Notification Service"]
        EvalSvc["Evaluation Service"]

        CosmosWrite[(Cosmos DB\nWrite Model)]
        CosmosRead[(Cosmos DB\nRead Models)]
        ServiceBus[("Service Bus")]

        ChangeFeed["Change Feed Processor"]

        ReportSvc["Reporting Service"]

        VersalityMatrix[(Versality Matrix\nData)]
        VersalityChangeFeed["Versality Matrix\nChange Feed"]

        ReplacementSvc --> CosmosWrite
        ReplacementSvc --> ServiceBus
        ReplacementSvc -.-> CosmosRead

        CosmosWrite --> ChangeFeed
        ChangeFeed --> CosmosRead

        ServiceBus --> NotifSvc
        ServiceBus --> EvalSvc

        VersalityMatrix --> VersalityChangeFeed
        VersalityChangeFeed --> ReplacementSvc

        EvalSvc --> ServiceBus
    end

    Gateway --> ReplacementSvc
    Gateway --> NotifSvc
    Gateway --> EvalSvc
    Gateway --> ReportSvc
```

### 3. Data Models

### 3.1 Write Models

```mermaid
classDiagram
    class BaseEntity {
        +String id
        +String type
        +String partitionKey
        +int version
        +String createdBy
        +DateTime createdAt
        +String modifiedBy
        +DateTime modifiedAt
    }

    class Replacement {
        +ValueStream valueStreamId
        +Team teamId
        +Department departmentId
        +AbsentOperator absentOperator
        +ReplacementOperator replacementOperator
        +String replacementLevel
        +String replacementType
        +String replacementOption
        +String replacementCase
        +String status
        +Escalation[] escalations
        +TimelineEvent[] timeline
        +int evaluationScore
        +DateTime evaluatedAt
        +String evaluatedBy
    }

    class AbsentOperator {
        +String id
        +String name
        +String workstation
        +String workstationName
        +String[] qualifications
        +String criticity
    }

    class ReplacementOperator {
        +String id
        +String name
        +String sourceType
        +String originalWorkstation
        +String[] qualifications
        +int polyvalenceLevel
    }

    class BackupStructure {
        +String valueStreamId
        +String teamId
        +String teamLeaderId
        +String date
        +String shift
        +BackupOperator[] operators
        +boolean transferredToSl
        +DateTime transferredAt
        +String slId
    }

    class BackupOperator {
        +String id
        +String name
        +String type
        +String[] qualifications
        +int polyvalenceLevel
        +String status
        +String replacementId
    }

    class Panier {
        +String panierType
        +String ownerId
        +String departmentId
        +String date
        +String shift
        +PanierOperator[] operators
        +boolean transferredToDepartment
    }

    class PanierOperator {
        +String id
        +String name
        +String sourceType
        +String sourceTlId
        +String type
        +String[] qualifications
        +int polyvalenceLevel
        +String status
        +String assignedToReplacementId
        +DateTime entryTimestamp
    }

    class Evaluation {
        +String replacementId
        +String evaluatorId
        +int score
        +String comments
        +String date
        +String shift
        +String operatorId
        +String workstationId
    }

    BaseEntity <|-- Replacement
    BaseEntity <|-- BackupStructure
    BaseEntity <|-- Panier
    BaseEntity <|-- Evaluation

    Replacement o-- AbsentOperator
    Replacement o-- ReplacementOperator
    BackupStructure o-- BackupOperator
    Panier o-- PanierOperator
```

### 3.2 Read Models

Read models are already described in detail in Section 4.3.1, including:

- TL Workspace Container
- SL Panier Container
- Department Panier Container
- Replacement History Container

### 4. API Specification

#### 4.1 Replacement Service APIs

##### 4.1.1 Initiate Replacement

**Endpoint**: `POST /api/replacement/initiate`

```mermaid
sequenceDiagram
    actor TL as Team Leader
    participant API as API Gateway
    participant RS as Replacement Service
    participant CosmosDB as Cosmos DB (Write)
    participant SB as Service Bus
    participant NS as Notification Service

    TL->>API: POST /api/replacement/initiate
    Note right of TL: Request payload:<br>{<br>  "valueStreamId": "VS_123",<br>  "teamId": "Team_456",<br>  "operatorId": "OP_123",<br>  "workstationId": "WS_123",<br>  "absenteeismLevel": "Low"<br>}

    API->>RS: Forward request

    RS->>RS: Validate request
    RS->>RS: Check TL authority
    RS->>RS: Verify operator absence

    RS->>CosmosDB: Create replacement document
    CosmosDB-->>RS: Confirmation

    RS->>SB: Publish ReplacementInitiated event
    SB-->>NS: Forward to notification service
    NS->>NS: Create notification

    RS-->>API: Return response
    API-->>TL: 201 Created
    Note right of TL: Response payload:<br>{<br>  "replacementId": "replacement-uuid",<br>  "status": "Initiated",<br>  "timestamp": "2025-05-15T08:30:00Z",<br>  "workstationDetails": {<br>    "id": "WS_123",<br>    "name": "Assembly Station 5",<br>    "criticity": "C",<br>    "requiredQualifications": ["Assembly", "Testing"]<br>  },<br>  "options": {<br>    "backupStructureAvailable": true,<br>    "polyvalenceMatrixAvailable": true<br>  }<br>}
```

##### 4.1.2 Assign Backup Operator

**Endpoint**: `POST /api/replacement/assign-backup`

```mermaid
sequenceDiagram
    actor TL as Team Leader
    participant API as API Gateway
    participant RS as Replacement Service
    participant CosmosDB as Cosmos DB (Write)
    participant SB as Service Bus
    participant NS as Notification Service

    TL->>API: POST /api/replacement/assign-backup
    Note right of TL: Request payload:<br>{<br>  "replacementId": "replacement-uuid",<br>  "backupOperatorId": "OP_789",<br>  "backupType": "BackupStructure",<br>  "optionCase": "Case1"<br>}

    API->>RS: Forward request

    RS->>CosmosDB: Get replacement document
    CosmosDB-->>RS: Return replacement

    RS->>RS: Validate assignment
    RS->>RS: Check operator qualification

    RS->>CosmosDB: Update replacement document
    CosmosDB-->>RS: Confirmation

    RS->>CosmosDB: Update backup structure
    CosmosDB-->>RS: Confirmation

    RS->>SB: Publish OperatorAssigned event
    SB-->>NS: Forward to notification service

    RS-->>API: Return response
    API-->>TL: 200 OK
    Note right of TL: Response payload:<br>{<br>  "replacementId": "replacement-uuid",<br>  "status": "Completed",<br>  "timestamp": "2025-05-15T08:35:00Z",<br>  "assignedOperator": {<br>    "id": "OP_789",<br>    "name": "Alex Johnson",<br>    "qualifications": ["Assembly:C", "Testing:C", "Rework:V"]<br>  }<br>}
```

##### 4.1.3 Get Replacement Suggestions

**Endpoint**: `GET /api/replacement/suggestions/{absenceId}`

**Parameters**:

- `absenceId`: Absence ID

**Response (200 OK)**:

```json
{
  "absenceId": "absence-uuid",
  "workstationId": "WS_123",
  "workstationName": "Assembly Station 5",
  "requiredQualifications": ["Assembly:C", "Testing:V"],
  "criticity": "C",
  "suggestions": [
    {
      "operatorId": "OP_789",
      "name": "Alex Johnson",
      "source": "BackupStructure",
      "qualifications": ["Assembly:C", "Testing:C", "Rework:V"],
      "polyvalenceLevel": 3,
      "score": 0.95,
      "matchReason": "Primary skill match"
    },
    {
      "operatorId": "OP_101",
      "name": "Sam Wilson",
      "source": "BackupStructure",
      "qualifications": ["Assembly:C", "Testing:V"],
      "polyvalenceLevel": 2,
      "score": 0.85,
      "matchReason": "Secondary skill match"
    }
  ]
}
```

##### 4.1.4 Get Workspace Data

**Endpoint**: `GET /api/workspace/{tlId}`

**Parameters**:

- `tlId`: Team Leader ID

**Response (200 OK)**:

```json
{
  "tlId": "TL_123",
  "valueStreams": [
    {
      "id": "VS_123",
      "name": "Final Assembly Line 1",
      "operators": [
        {
          "id": "OP_123",
          "name": "John Doe",
          "status": "Absent",
          "workstation": "WS_123",
          "workstationName": "Assembly Station 5",
          "replacementStatus": "Pending"
        },
        {
          "id": "OP_124",
          "name": "Jane Smith",
          "status": "Present",
          "workstation": "WS_124",
          "workstationName": "Assembly Station 6"
        }
      ],
      "backupStructure": {
        "totalOperators": 3,
        "availableOperators": 2,
        "usedOperators": 1,
        "operators": [
          {
            "id": "OP_789",
            "name": "Alex Johnson",
            "type": "Polyvalent",
            "status": "Available"
          },
          {
            "id": "OP_101",
            "name": "Sam Wilson",
            "type": "Rework",
            "status": "Available"
          }
        ]
      },
      "pendingReplacements": [
        {
          "replacementId": "replacement-uuid",
          "absentOperatorId": "OP_123",
          "absentOperatorName": "John Doe",
          "workstationId": "WS_123",
          "workstationName": "Assembly Station 5",
          "status": "Pending",
          "initiatedAt": "2025-05-15T08:30:00Z",
          "timeRemaining": 180 // Seconds remaining for 5-minute window
        }
      ]
    }
  ],
  "pendingEvaluations": 2,
  "notifications": [
    {
      "id": "notification-uuid",
      "type": "ReplacementConfirmation",
      "message": "SL has assigned operator Robin Chen as replacement",
      "timestamp": "2025-05-15T08:45:00Z",
      "read": false
    }
  ]
}
```

#### 4.2 Panier Management APIs

#### 4.2.1 Get SL Panier

**Endpoint**: `GET /api/panier/sl/{slId}`

**Parameters**:

- `slId`: Shift Leader ID

**Response (200 OK)**:

```json
{
  "slId": "SL_123",
  "panierId": "panier-uuid",
  "totalOperators": 3,
  "availableOperators": 1,
  "assignedOperators": 1,
  "ctnOperators": 1,
  "operators": [
    {
      "id": "OP_103",
      "name": "Robin Chen",
      "sourceType": "SLBackup",
      "type": "Support",
      "qualifications": ["Assembly:C", "Testing:C"],
      "polyvalenceLevel": 3,
      "status": "Available"
    },
    {
      "id": "OP_789",
      "name": "Alex Johnson",
      "sourceType": "TLBackup",
      "sourceTlId": "TL_123",
      "type": "Polyvalent",
      "qualifications": ["Assembly:C", "Testing:C", "Rework:V"],
      "status": "Assigned"
    },
    {
      "id": "OP_101",
      "name": "Sam Wilson",
      "sourceType": "TLBackup",
      "sourceTlId": "TL_123",
      "type": "Rework",
      "qualifications": ["Rework:C", "Assembly:V"],
      "status": "CTN"
    }
  ],
  "pendingRequests": [
    {
      "requestId": "request-uuid",
      "tlId": "TL_456",
      "tlName": "Michael Brown",
      "valueStreamId": "VS_456",
      "valueStreamName": "Final Assembly Line 2",
      "workstationId": "WS_456",
      "workstationName": "Testing Station 3",
      "requiredQualifications": ["Testing"],
      "criticity": "M",
      "requestTime": "2025-05-15T08:50:00Z",
      "timeRemaining": 180 // Seconds remaining for 5-minute window
    }
  ]
}
```

#### 4.2.2 Update Operator Status

**Endpoint**: `PATCH /api/panier/operator/{operatorId}/status`

**Request**:

```json
{
  "panierId": "panier-uuid",
  "status": "CTN"
}
```

**Response (200 OK)**:

```json
{
  "operatorId": "OP_101",
  "name": "Sam Wilson",
  "status": "CTN",
  "updatedAt": "2025-05-15T09:00:00Z"
}
```

#### 4.3 Evaluation Service APIs

##### 4.3.1 Submit Replacement Evaluation

**Endpoint**: `POST /api/evaluation/replacement/{replacementId}`

**Request**:

```json
{
  "score": 4,
  "comments": "Good performance, adapted quickly to the workstation."
}
```

**Response (200 OK)**:

```json
{
  "evaluationId": "evaluation-uuid",
  "replacementId": "replacement-uuid",
  "score": 4,
  "timestamp": "2025-05-15T16:30:00Z",
  "status": "Completed"
}
```

#### 4.4 Reporting Service APIs

##### 4.4.1 Get Replacement History Report

**Endpoint**: `GET /api/reports/replacement-history`

**Query Parameters**:

- `startDate`: "2025-05-01"
- `endDate`: "2025-05-15"
- `shift`: "Morning"
- `department`: "Dept_789"
- `valueStream`: "VS_123"

### 5. Security and Permission Models

#### 5.1 Authorization Model

##### 5.1.1 Authorization Components

```mermaid
graph TD
    subgraph "RBAC Components"
        User(User)
        Role(Role)
        Permission(Permission)
        Resource(Resource)
        Action(Action)
    end

    User -->|Assigned to| Role
    Role -->|Contains| Permission
    Permission -->|Combines| Resource
    Permission -->|Combines| Action
    Resource -->|Protected by| Permission
    Action -->|Performed on| Resource
```

- **Users**: Individual system users authenticated via Azure AD B2C
- **Roles**: Collections of permissions assigned to users
- **Permissions**: Defined as `resource:action` pairs (e.g., `workspace:view`)
- **Resources**: System objects or data that can be accessed or modified
- **Actions**: Operations that can be performed on resources

##### 5.1.2 Permission Format

Permissions follow the format `resource:action` where:

- `resource` is the protected entity (e.g., workspace, replacement, panier-sl)
- `action` is the allowed operation (e.g., view, manage, transfer)

##### 5.1.3 Core Resources

| Resource        | Description                                       |
| --------------- | ------------------------------------------------- |
| workspace       | TL workspace with operator presence data          |
| backupstructure | Backup Structure for low-level replacements       |
| panier-sl       | Shift Leader Panier for medium-level replacements |
| panier-dept     | Department Panier for high-level replacements     |
| replacement     | Replacement process entities                      |
| evaluation      | Replacement evaluations and feedback              |
| report          | Replacement history and analytics reports         |
| notification    | System notifications                              |

##### 5.1.4 Core Actions

| Action   | Description                          |
| -------- | ------------------------------------ |
| view     | Read-only access to a resource       |
| manage   | Create, update, or delete a resource |
| transfer | Move a resource to another owner     |
| initiate | Start a process                      |
| assign   | Assign operators to replacements     |
| complete | Mark a process as completed          |
| escalate | Elevate a process to a higher level  |
| submit   | Provide data for a process           |
| approve  | Authorize a process or change        |

##### 5.1.5 Role Permission Matrix

```mermaid
graph TD
    subgraph "Role Hierarchy"
        Operator
        TeamLeader
        ShiftLeader
        Coordinator
        ProductionManager
        DepartmentClerk
        PlantManager
        SystemAdmin
    end

    Operator --> TeamLeader
    TeamLeader --> ShiftLeader
    ShiftLeader --> Coordinator
    Coordinator --> ProductionManager
    ProductionManager --> PlantManager
    DepartmentClerk --> ProductionManager
    PlantManager --> SystemAdmin
```

| Role              | Permissions                                                                                                                                                                                                                                                                                 |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Operator          | notification:view                                                                                                                                                                                                                                                                           |
| TeamLeader        | workspace:view, workspace:manage, backupstructure:view, backupstructure:manage, backupstructure:transfer, replacement:initiate, replacement:assign, replacement:complete, replacement:escalate, evaluation:submit, evaluation:view, report:view-own, notification:view, notification:manage |
| ShiftLeader       | workspace:view, panier-sl:view, panier-sl:manage, panier-sl:transfer, panier-dept:view, replacement:assign, replacement:complete, replacement:escalate, report:view-team, notification:view, notification:manage                                                                            |
| Coordinator       | workspace:view, panier-sl:view, panier-dept:view, panier-dept:manage, report:view-department, notification:view                                                                                                                                                                             |
| ProductionManager | workspace:view, panier-sl:view, panier-dept:view, report:view-department, notification:view                                                                                                                                                                                                 |
| DepartmentClerk   | report:view-department, notification:view                                                                                                                                                                                                                                                   |
| PlantManager      | workspace:view, panier-sl:view, panier-dept:view, report:view-plant, notification:view                                                                                                                                                                                                      |
| SystemAdmin       | All permissions                                                                                                                                                                                                                                                                             |

#### 5.2 Security Implementation

##### 5.2.1 API Layer Security

- **API Gateway**: Centralized authentication and authorization
- **JWT Validation**: Signature verification and claims extraction
- **Claims Transformation**: Mapping identity claims to internal authorization context
- **Rate Limiting**: Tiered limits based on endpoint sensitivity and user role
- **Request Validation**: Schema validation for all incoming requests

##### 5.2.2 Service-to-Service Security

- **Mutual TLS**: Certificate-based authentication between services
- **Service Identities**: Managed identities for Azure resources
- **Least Privilege**: Scoped permissions for each service
- **Network Security**: Private endpoints for Cosmos DB and Service Bus

##### 5.2.3 Service Authorization

The Replacement Service implements authorization checks internally by:

1. **Validating JWT claims**: Extracting user identity and roles
2. **Checking resource ownership**: Validating that users can only access their authorized resources
3. **Enforcing action permissions**: Allowing only permitted actions based on role
4. **Contextual authorization**: Considering contextual factors like time constraints or process stage

For example, when processing a replacement request, the service validates:

- The user has the role of TeamLeader
- The value stream belongs to the user's assigned streams
- The replacement process is in a state that allows the requested action
- The time constraints allow the action

The Evaluation and Notification services implement similar authorization patterns specific to their domain.

##### 5.2.4 Data Protection

- **Data Classification**:

  - Public: Non-sensitive information (e.g., shift schedules)
  - Internal: Business-operational data (e.g., replacement records)
  - Confidential: Personally identifiable information (e.g., operator profiles)

- **Encryption**:

  - At rest: Cosmos DB encryption with customer-managed keys
  - In transit: TLS 1.3 for all communications
  - Application-level: Field-level encryption for sensitive attributes

- **Audit Logging**:
  - All authentication events
  - Authorization decisions (grants/denials)
  - Resource access and modifications
  - Administrative actions

##### 5.2.5 Security Monitoring

- **Threat Detection**:

  - Unusual access patterns
  - Authentication anomalies
  - Privilege escalation attempts

- **Vulnerability Management**:

  - Regular security scanning
  - Dependency vulnerability checks
  - Secure development practices

- **Incident Response**:
  - Security event alerting
  - Defined response procedures
  - Regular security tabletop exercises

### 6. Conclusion

The Operator Replacement Process design provides a solid foundation for a modern, flexible system that can efficiently manage the complex workflows required for operator absenteeism scenarios. The consolidated Replacement Service approach simplifies the architecture while maintaining the benefits of logical separation between command and query responsibilities.

By leveraging Azure cloud services, event-driven architecture, and a consolidated service approach, the system can deliver the performance, reliability, and scalability needed for production environments while minimizing operational complexity.

The implementation should proceed with an iterative approach, focusing on delivering core functionality first and gradually expanding to more advanced features. This will allow for early feedback and validation of the architectural decisions made in this design.
