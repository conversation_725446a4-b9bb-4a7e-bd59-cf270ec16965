# Connected Workers - Technical design document

## Document Information

**Version:** 1.0.0  
**Last Updated:** 2025-05-13  
**Status:** Final

## Executive Summary

### Key Features

## Table of Contents

1. [Overview](#1-overview)
2. [Authentication and Authorization](#2-authentication-and-authorization)

   1. [Overview](#1-overview)

   2. [Authentication Architecture](#2-authentication-architecture)

   3. [Core components](#3-core-components)

      3.1. [Authorization Framework](#31-authorization-framework)

      3.2. [Delegation Framework](#32-delegation-framework)

      3.3. [Microsoft Graph API Integration](#33-microsoft-graph-api-integration)

   4. [Data Model](#4-data-model)

   5. [Conclusion](#5-conclusion)

3. [Crew Management](#3-crew-management)

   1. [Overview](#1-overview)

   2. [Architecture Overview](#2-architecture-overview)

   3. [Core components](#3-core-components)

      - [Operator Management Service](#31-operator-management-service)
      - [Employee Assignment Service](#32-employee-assignment-service)
      - [Team Assignment Service](#33-team-assignment-service)
      - [DH Walk Service](#34-dh-walk-service)
      - [Workstation Service](#35-workstation-service)
      - [Direct Dependents Service](#36-direct-dependents-service)
      - [Operator Skills Service](#37-operator-skills-service)

   4. [Integration Patterns](#4-integration-patterns)

   5. [Conclusion](#5-conclusion)

4. [Module 1: Administrative Document Management](#4-module-1-administrative-document-management)
5. [Module 2: Absenteeism Management](#5-module-2-absenteeism-management)

   1. [Overview](#51-overview)

   2. [Core components](#52-core-components)

      2.1 [Skills Matrix](#521-skills-matrix)

      2.2 [Training Process](#522-training-process)

      2.3 [Document Validation](#523-document-validation)

      2.4 [Versatility Matrix](#524-versatility-matrix)

      2.5 [Replacement Management](#525-replacement-management)

      2.6 [Nursing Management](#526-nursing-management)

   3. [Conclusion](#53-conclusion)

6. [Module 3: Headcount Management and Timekeeping system](#6-module-3-headcount-management-and-timekeeping-system)
7. [Overtime planification](#7-overtime-planification)
   1. [Overview](#1-overview)
      
   2. [Architecture Overview](#2-system-architecture-overview)
     
   3. [Core components](#3-core-components)
       1. [OvertimeRequest Service](#31-overtimerequest-service)
       2. [ApprovalWorkflow Service](#32-approvalworkflow-service) 
       3. [Shift Management Service](#33-shift-management-service)
       4. [TKSIntegration Services](#34-tksintegration-services)
       5. [Ends points](#5-ends-points)
   4. [Conclusion](#4-conclusion)
8. [Module 4: Transport Management](#8-module-4-transport-management)
   1. [Overview](#1-overview)
      
   2. [Architecture Overview](#2-system-architecture-overview)
     
   3. [Core components](#3-core-components)
       1. [Pickup Station Service](#31-pickup-station-service)
       2. [Transport Planning Service](#32-transport-planning-service) 
       3. [Bus Boarding Service](#33-bus-boarding-service)
   4. [Morocco VS EMEA/NA:](#4-morocco-vs-emea-na) 
   5. [Localization geospatial](#5-localization-geospatial) 
   6. [Conclusion](#6-conclusion)

9.  [Conclusion](#9-conclusion)
   1. [Design Summary](#91-design-summary)

---

## 1. Overview

## 2. Authentication and Authorization

## 1. Overview

### 1.1 Purpose and Scope

#### Purpose

This document serves as a technical blueprint for implementing the authentication, authorization, and delegation system. It provides detailed specifications for:

1. **Authentication Mechanisms**

   - SAML-based Single Sign-On integration with Azure AD
   - JWT token management for session handling
   - Token refresh and synchronization processes

2. **Authorization Framework**

   - Role-based access control implementation
   - Permission and scope management
   - Profile-based authorization system

3. **Delegation System**
   - Hierarchical delegation rules
   - Temporary access management
   - profile Delegation with restrictions

#### Scope

The system encompasses:

- User authentication and session management
- Role and permission management
- Profile-based access control
- Delegation workflows
- Integration with Microsoft Graph API
- Security implementations
- Operational considerations

### 1.2 Key Components

#### Core Services

1. **Authentication Service**

   - SAML SSO processing
   - JWT token issuance and validation
   - Session management
   - Token refresh handling

2. **Authorization Service**

   - Permission evaluation
   - Role management
   - Scope validation
   - Profile context handling

3. **Delegation Service**

   - Delegation request processing
   - Authority transfer management
   - Temporary access control

4. **Profile Service**

   - Profile lifecycle management
   - Context switching
   - Profile synchronization
   - Role assignment management

5. **Sync Service**
   - Graph API integration
   - User data synchronization
   - Group membership management
   - Delta query handling

## 2. Authentication Architecture

### 2.1 SAML-based Single Sign-On

The SAML-based Single Sign-On (SSO) implementation provides secure authentication using Microsoft Entra ID as the Identity Provider (IdP). This section details the core components, authentication flows, and technical specifications for the Connected Workers platform.

#### 2.1.1 Core Components

1. **Identity Provider (IdP) - Microsoft Entra ID**

   - Central authentication authority for the Connected Workers platform
   - Manages user credentials and verification processes
   - Generates and cryptographically signs SAML assertions
   - Provides multi-factor authentication capabilities
   - Manages user sessions at the IdP level
   - Generates claims based on user attributes and group memberships
   - Maintains certificate for signing SAML responses (valid until February 18, 2028)

2. **Service Provider (SP) - Connected Workers Application**

   - Manages protected resources and access control
   - Generates and signs SAML authentication requests
   - Validates and processes SAML responses
   - Establishes and manages user sessions
   - Issues JWT tokens for authenticated sessions
   - Manages certificate and key pairs for secure communication
   - Implements certificate rotation and expiration monitoring

3. **Assertion Consumer Service (ACS)**
   - Dedicated endpoint (`/api/v1/auth/acs`) for processing SAML responses
   - Validates response signatures using Entra ID's certificate
   - Extracts user attributes and identity information
   - Generates session tokens (JWT) for authenticated users
   - Establishes security context for the user session
   - Implements comprehensive error handling and security logging
   - Supports both development and production environments with flexible configuration

#### 2.1.2 SP-Initiated Flow

The SP-initiated flow is triggered when a user attempts to access the application directly:

```mermaid
sequenceDiagram
    participant User
    participant SP as Service Provider
    participant IdP as Entra ID

    User->>SP: 1. Access Protected Resource
    Note over SP: 2. Check for valid session
    SP->>SP: 3. Generate SAML Request with ID, issuer, ACS URL
    SP->>User: 4. Redirect to Entra ID with SAML Request
    User->>IdP: 5. Forward SAML Request
    Note over IdP: 6. Validate Request (issuer, signature)
    alt User Not Authenticated
        IdP->>User: 7a. Present Authentication UI
        User->>IdP: 8a. Provide Credentials
        Note over IdP: 9a. Validate Credentials
    else User Has Active Session
        Note over IdP: 7b. Identify User from Session
    end
    Note over IdP: 10. Generate SAML Response
    Note over IdP: 11. Sign Response & Assertions
    IdP->>User: 12. Redirect to SP's ACS URL with SAML Response
    User->>SP: 13. Forward SAML Response
    Note over SP: 14. Validate Response (signature, conditions, audience)
    Note over SP: 15. Extract User Attributes & Create Session
    SP->>User: 16. Grant Access & Set Session Cookie/JWT
```

**Implementation Details:**

1. **Initial Application Access**

   - When a user navigates to a protected resource, the application checks for valid JWT tokens
   - If no valid token exists, the authentication flow is initiated
   - The application's authentication guard redirects to the login endpoint

2. **SAML Request Generation**

   - The `SamlStrategy` class generates a SAML AuthnRequest with:
     - Unique request ID for tracking and security
     - Issuer identifier configured in the application
     - ACS URL where Entra ID should send the response
     - Signature using the service provider's private key
   - The request is configured with SHA-256 signature and digest algorithms

3. **Redirection to Identity Provider**

   - The application redirects the user to Entra ID's SSO endpoint
   - The request includes RelayState to preserve the original URL
   - The SAML request is compressed and encoded for transmission

4. **Response Processing**

   - The ACS endpoint receives the SAML response via HTTP POST
   - The `validate` method in `SamlStrategy` processes the response:
     - Verifies the digital signature using Entra ID's certificate
     - Validates the issuer matches the expected value
     - Extracts user information (email, name, groups, etc.)
   - The `findOrCreateUserFromSaml` method in `AuthService` creates or updates the user record

5. **Session Establishment**
   - The `generateTokens` method creates JWT access and refresh tokens
   - Tokens include user identity, roles, and standard JWT claims
   - The access token has a short expiration (typically 15 minutes)
   - The refresh token has a longer expiration (typically 7 days)
   - Secure HTTP-only cookies are set with appropriate security flags

#### 2.1.3 IdP-Initiated Flow

The IdP-initiated flow starts when users access the application through the Microsoft Entra ID portal:

```mermaid
sequenceDiagram
    participant User
    participant IdP as Entra ID
    participant SP as Service Provider

    User->>IdP: 1. Access Entra ID Portal/MyApps
    IdP->>User: 2. Display Available Applications
    User->>IdP: 3. Select Application Tile
    Note over IdP: 4. Check User Authentication
    alt User Not Authenticated
        IdP->>User: 5a. Present Authentication UI
        User->>IdP: 6a. Provide Credentials
        Note over IdP: 7a. Validate Credentials
    end
    Note over IdP: 8. Generate SAML Response
    Note over IdP: 9. Include App-specific Attributes & Roles
    IdP->>User: 10. Redirect to SP with SAML Response
    User->>SP: 11. Submit SAML Response
    Note over SP: 12. Validate Response
    Note over SP: 13. Extract User Identity & Attributes
    Note over SP: 14. Create User Session
    SP->>User: 15. Redirect to Default Landing Page
```

**Implementation Details:**

1. **Portal Access and Application Selection**

   - User logs into Microsoft Entra ID portal (myapplications.microsoft.com)
   - Selects the Connected Workers application tile
   - Entra ID initiates the SAML flow without a prior request

2. **SAML Response Processing**

   - The application's ACS endpoint receives an unsolicited SAML response
   - The `SamlStrategy` validates the response with additional security checks
   - The system extracts user attributes including:
     - Email address (nameID)
     - Display name
     - First and last name
     - Object ID (unique identifier in Azure AD)
     - Group memberships for role mapping

3. **User Session Creation**
   - The application creates a user session with appropriate permissions
   - JWT tokens are generated with the same process as SP-initiated flow
   - The user is redirected to the application's default landing page
   - The session includes all necessary context for authorization

#### 2.1.4 SAML Message Structure

1. **SAML Request Structure**

```xml
   <samlp:AuthnRequest
     xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
     xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
     ID="${requestID}"
     Version="2.0"
     IssueInstant="${timestamp}"
     Destination="${ssoEndpoint}"
     AssertionConsumerServiceURL="${acsUrl}"
     ProtocolBinding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST">
     <saml:Issuer>${spEntityID}</saml:Issuer>
     <samlp:NameIDPolicy
       Format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
       AllowCreate="true" />
   </samlp:AuthnRequest>
```

2. **SAML Response Structure**

   Based on the EMEA-EDS-Connected Workers System XML file, the SAML response follows this structure:

```xml
   <samlp:Response
     xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
     xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
     ID="${responseID}"
     Version="2.0"
     IssueInstant="${timestamp}"
     Destination="${acsUrl}">
     <saml:Issuer>https://sts.windows.net/6b1311e5-123f-49db-acdf-8847c2d00bed/</saml:Issuer>
     <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
       <!-- Signature details with SHA-256 algorithm -->
     </Signature>
     <samlp:Status>
       <samlp:StatusCode Value="urn:oasis:names:tc:SAML:2.0:status:Success" />
     </samlp:Status>
     <saml:Assertion>
       <saml:Issuer>https://sts.windows.net/6b1311e5-123f-49db-acdf-8847c2d00bed/</saml:Issuer>
       <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
         <!-- Assertion signature -->
       </Signature>
       <saml:Subject>
         <saml:NameID Format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"><EMAIL></saml:NameID>
         <saml:SubjectConfirmation Method="urn:oasis:names:tc:SAML:2.0:cm:bearer">
           <saml:SubjectConfirmationData
             NotOnOrAfter="${expiryTime}"
             Recipient="${acsUrl}" />
         </saml:SubjectConfirmation>
       </saml:Subject>
       <saml:Conditions NotBefore="${notBefore}" NotOnOrAfter="${notOnOrAfter}">
         <saml:AudienceRestriction>
           <saml:Audience>${spEntityID}</saml:Audience>
         </saml:AudienceRestriction>
       </saml:Conditions>
       <saml:AuthnStatement AuthnInstant="${authnInstant}" SessionIndex="${sessionIndex}">
         <saml:AuthnContext>
           <saml:AuthnContextClassRef>urn:oasis:names:tc:SAML:2.0:ac:classes:Password</saml:AuthnContextClassRef>
         </saml:AuthnContext>
       </saml:AuthnStatement>
       <saml:AttributeStatement>
         <saml:Attribute Name="http://schemas.microsoft.com/identity/claims/displayname">
           <saml:AttributeValue>John Doe</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.microsoft.com/identity/claims/objectidentifier">
           <saml:AttributeValue>a1b2c3d4-e5f6-7890-abcd-ef1234567890</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname">
           <saml:AttributeValue>John</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname">
           <saml:AttributeValue>Doe</saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress">
           <saml:AttributeValue><EMAIL></saml:AttributeValue>
         </saml:Attribute>
         <saml:Attribute Name="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups">
           <saml:AttributeValue>Plant Managers</saml:AttributeValue>
           <saml:AttributeValue>Connected Workers Users</saml:AttributeValue>
         </saml:Attribute>
       </saml:AttributeStatement>
     </saml:Assertion>
   </samlp:Response>
```

### 2.2 JWT Authentication

After the initial SAML authentication, the system uses JSON Web Tokens (JWT) for session management and ongoing authentication. JWTs provide a stateless, secure method for maintaining user sessions and authorization information.

#### 2.2.1 Token Types and Structure

The authentication system implements two types of tokens with distinct purposes and structures:

**Token Types Comparison:**

| Characteristic       | Access Token                                   | Refresh Token                            |
| -------------------- | ---------------------------------------------- | ---------------------------------------- |
| **Purpose**          | API authorization and resource access          | Obtaining new access tokens              |
| **Lifetime**         | Short-lived (15-60 minutes)                    | Longer-lived (hours to days)             |
| **Storage Location** | Application memory                             | HTTP-only secure cookie                  |
| **Claim Richness**   | Contains complete user profile and permissions | Contains minimal identifying information |
| **Usage Frequency**  | Every API call                                 | Only during token refresh                |
| **Exposure**         | Limited to application frontend                | Server-side only                         |
| **Revocation**       | Self-expires quickly                           | Can be invalidated server-side           |

**Access Token Structure:**

```
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "[certificate identifier]"
  },
  "payload": {
    "sub": "<EMAIL>",
    "name": "John Doe",
    "given_name": "John",
    "family_name": "Doe",
    "email": "<EMAIL>",
    "roles": ["User", "ProjectManager"],
    "department": "Engineering",
    "location": "Paris",
    "employeeId": "EMP123456",
    "profile_id": "profile_12345",
    "iat": 1675091348,
    "exp": 1675094948,
    "aud": "connected-workers-api",
    "iss": "connected-workers-auth"
  }
}
```

**Refresh Token Structure:**

```
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "[certificate identifier]"
  },
  "payload": {
    "sub": "<EMAIL>",
    "jti": "unique-token-id-12345",
    "iat": 1675091348,
    "exp": 1675177748,
    "iss": "connected-workers-auth"
  }
}
```

**Token Claim Descriptions:**

| Claim         | Description                          | Access Token | Refresh Token |
| ------------- | ------------------------------------ | :----------: | :-----------: |
| `sub`         | Subject identifier (typically email) |      ✓       |       ✓       |
| `name`        | User's full name                     |      ✓       |       ✗       |
| `given_name`  | User's first name                    |      ✓       |       ✗       |
| `family_name` | User's last name                     |      ✓       |       ✗       |
| `email`       | User's email address                 |      ✓       |       ✗       |
| `roles`       | User's assigned roles                |      ✓       |       ✗       |
| `department`  | User's department                    |      ✓       |       ✗       |
| `employeeId`  | Employee identifier                  |      ✓       |       ✗       |
| `profile_id`  | Active profile context               |      ✓       |       ✗       |
| `iat`         | Issued at timestamp                  |      ✓       |       ✓       |
| `exp`         | Expiration timestamp                 |      ✓       |       ✓       |
| `aud`         | Intended audience                    |      ✓       |       ✗       |
| `iss`         | Token issuer                         |      ✓       |       ✓       |
| `jti`         | JWT ID (unique identifier)           |      ✗       |       ✓       |

#### 2.2.2 Token Management

Token management encompasses the complete lifecycle of JWTs within the system, including generation, validation, renewal, and revocation.

**Token Generation Security Considerations:**

| Consideration             | Implementation                                  |
| ------------------------- | ----------------------------------------------- |
| **Signing Algorithm**     | RS256 (asymmetric) with 2048-bit key            |
| **Key Rotation**          | 90-day rotation schedule with overlap period    |
| **Entropy Source**        | Hardware-based random number generation         |
| **Claim Minimization**    | Include only necessary claims per token type    |
| **Clock Synchronization** | NTP with maximum 30-second clock skew tolerance |
| **Identifier Uniqueness** | UUIDv4 for token identifiers (jti claim)        |

**Token Validation Process:**

1. **Signature Verification**:

   - Verify token is properly signed with the correct key
   - Check signing algorithm matches expected algorithm

2. **Standard Claims Validation**:

   - Verify token has not expired (`exp` claim)
   - Verify token was issued at a reasonable time (`iat` claim)
   - Verify issuer matches expected value (`iss` claim)
   - Verify audience is correct (`aud` claim)

3. **Application-Specific Validation**:
   - Check for required claims based on resource access
   - Validate role claims for authorization
   - Verify profile context is valid

#### 2.2.3 Token Storage Strategy

Proper token storage is critical for security. The system implements a defense-in-depth approach to protect tokens from various attack vectors:

**Token Storage Locations:**

| Token Type              | Storage Location  | Technical Implementation                | Security Considerations               |
| ----------------------- | ----------------- | --------------------------------------- | ------------------------------------- |
| **Access Token**        | Memory (frontend) | JavaScript variable in auth state       | Not persisted across page refreshes   |
| **Access Token Backup** | SessionStorage    | Encrypted, used only for page refreshes | Cleared when browser closes           |
| **Refresh Token**       | HTTP-only Cookie  | Secure, SameSite=Strict                 | Not accessible via JavaScript         |
| **Token Metadata**      | localStorage      | Expiry time only (no actual tokens)     | Used to detect when refresh is needed |

**Security Measures:**

| Threat               | Mitigation                                                       |
| -------------------- | ---------------------------------------------------------------- |
| XSS Attack           | Memory-only access tokens, HTTP-only cookies for refresh tokens  |
| CSRF Attack          | SameSite cookie policy, CSRF tokens for authentication endpoints |
| Token Leakage        | No tokens in localStorage, no tokens in URLs                     |
| Session Hijacking    | Short token lifetimes, secure cookie flags                       |
| Local Storage Access | Only non-sensitive metadata stored                               |

### 2.3 Token Refresh Implementation

The token refresh mechanism enables continuous user sessions without requiring re-authentication while simultaneously keeping user data synchronized with Microsoft Entra ID.

#### 2.3.1 Refresh Process

Token refresh is initiated automatically when an access token approaches expiration:

**Refresh Flow:**

```mermaid
sequenceDiagram
 participant Client
 participant API as Auth API
 participant Graph as Microsoft Graph API
 participant DB as Database

 Note over Client: Detect access token<br/>approaching expiration
 Client->>API: Request token refresh
 Note right of Client: Refresh token included<br/>in HTTP-only cookie
 API->>API: Validate refresh token

 alt Valid Refresh Token
     API->>Graph: Fetch latest user data
     Graph->>API: Return user profile & groups
     API->>DB: Update user data
     API->>API: Generate new tokens
     API->>Client: Return new access token
     Note right of API: Set new refresh token<br/>in HTTP-only cookie
 else Invalid Refresh Token
     API->>Client: Return 401 Unauthorized
     Client->>Client: Redirect to login
 end
```

**Expiration Detection Strategy:**

| Strategy Component        | Implementation                                      |
| ------------------------- | --------------------------------------------------- |
| **Client-Side Detection** | Track token expiry time in memory                   |
| **Refresh Timing**        | Initiate at 75% of token lifetime                   |
| **Background Refresh**    | Refresh happens in background without UI disruption |
| **Proactive Detection**   | Check before critical operations                    |
| **Fallback Detection**    | Handle 401 responses from API calls                 |

**Refresh Endpoint Security:**

| Security Feature                  | Implementation                                   |
| --------------------------------- | ------------------------------------------------ |
| **Rate Limiting**                 | Maximum 10 refresh attempts per minute per user  |
| **Jitter**                        | Random delay (1-3s) to prevent timing attacks    |
| **IP Validation**                 | Optional validation against previous request IPs |
| **Refresh Token Rotation**        | New refresh token with each successful refresh   |
| **Suspicious Activity Detection** | Block after multiple rapid refresh attempts      |

#### 2.3.2 User Data Synchronization

The Connected Workers platform implements a comprehensive user data synchronization strategy that ensures continuous alignment between Microsoft Entra ID and the application's user context. This synchronization occurs during each token refresh operation, maintaining data consistency and security across the entire system.

**Attribute Synchronization Framework**

The synchronization process encompasses multiple categories of user attributes, each serving distinct purposes within the application. At the foundation, basic profile information synchronization ensures accurate user identification and presentation throughout the interface. This includes essential identifiers such as display names, email addresses, organization and contact information, which are crucial for user recognition and communication.

Security and authorization data synchronization focuses on role assignments and group memberships. This critical security layer ensures that user permissions accurately reflect their current organizational responsibilities and access requirements. The system continuously updates these assignments to maintain precise access control and security boundaries.

**Role Authorization Framework**

The Connected Workers platform implements a sophisticated role-based access control system that translates Azure AD group memberships into application-specific roles and permissions. This mapping creates a clear, hierarchical authorization structure that aligns with organizational responsibilities while maintaining security and operational efficiency.

The system maintains a baseline access level through the Connected Workers Users group, ensuring all authorized users have access to essential platform functionality. This foundational access layer provides basic system interaction capabilities while maintaining security boundaries.

This role mapping framework is dynamically maintained during each token refresh operation, ensuring that user permissions always reflect current Azure AD group memberships. The system's ability to maintain this synchronization in real-time ensures that access control remains current and accurate, while the hierarchical structure supports clear lines of authority and responsibility within the organization.

#### 2.3.3 Error Handling

Robust error handling ensures the system can gracefully recover from authentication failures:

**Common Error Scenarios:**

| Error Scenario            | Detection Method             | Handling Strategy                       | User Experience                           |
| ------------------------- | ---------------------------- | --------------------------------------- | ----------------------------------------- |
| **Expired Refresh Token** | JWT expiration validation    | Redirect to login                       | Clear authentication message              |
| **Invalid Signature**     | Signature validation failure | Security alert, login redirect          | Security message                          |
| **User Not Found**        | Database lookup failure      | JIT provisioning attempt                | Transparent retry                         |
| **Graph API Unavailable** | API timeout/error            | Use cached data, retry later            | Notification of potentially outdated data |
| **Role Mapping Failure**  | Exception in mapping process | Log issue, use default/previous roles   | Limited functionality notice              |
| **Database Connectivity** | Database exception           | Retry with backoff, use cached JWT data | Transaction-specific error                |

**Recovery Strategies:**

```mermaid
flowchart TD
    A[Detect Refresh Error] --> B{Error Type}
    B -->|Token Expired/Invalid| C[Redirect to Login]
    B -->|Graph API Unavailable| D[Use Cached User Data]
    B -->|Database Error| E[Retry with Exponential Backoff]
    B -->|Network Error| F[Queue Refresh for Later]

    D --> G[Flag Data as Potentially Outdated]
    D --> H[Schedule Background Retry]
    E --> I{Retry Successful?}
    I -->|Yes| J[Resume Normal Operation]
    I -->|No, Max Retries| K[Degrade Gracefully]
    F --> L[Monitor Connectivity]
    L --> M{Connection Restored?}
    M -->|Yes| N[Execute Queued Operations]
    M -->|No| O[Notify User]
```

## 3. Core components

### 3.1. Authorization Framework

This section outlines the comprehensive authorization framework for managing access control within the application, leveraging JWT authentication with SAML SSO and Microsoft Graph API synchronization.

#### 3.1.1. Role Management

Roles are collections of permissions derived from Azure AD group memberships, providing a direct mapping between organizational structure and system permissions.

##### *******. Role Types

| Role Type      | Description                            | Examples                                 |
| -------------- | -------------------------------------- | ---------------------------------------- |
| Organizational | Based on position in company hierarchy | PLANT_MANAGER, SHIFT_LEADER, TEAM_LEADER |
| Functional     | Based on specific job functions        | QUALITY_INSPECTOR, MAINTENANCE_TECH      |
| Administrative | System administration capabilities     | SYSTEM_ADMIN, USER_MANAGER               |
| Specialized    | Temporary or special-purpose access    | EMERGENCY_RESPONDER, AUDITOR             |

##### *******. Role Hierarchy

```mermaid
graph TD
    SA[SUPER_ADMIN] --> PM[PLANT_MANAGER]
    PM --> SL[SHIFT_LEADER]
    SL --> TL[TEAM_LEADER]
    TL --> OP[OPERATOR]
    SA --> TR[TRAINING_RESPONSIBLE]
    TR --> T[TRAINER]
    SA --> QI[QUALITY_INSPECTOR]
    SA --> MT[MAINTENANCE_TECH]
```

##### *******. Hierarchical Role Structure

Roles have parent-child relationships that define an organization's management structure. Each role has:

- A hierarchical level (1 being the highest, increasing numbers for lower levels)
- Parent role references (which roles are above in the hierarchy)
- Child role references (which roles are below in the hierarchy)

This hierarchy structure enables:

1. **Direct Permission Assignment**: Each role has explicitly assigned permissions based on its level
2. **Delegation Control**: Delegation can be restricted to follow the hierarchy
3. **Organizational Alignment**: Authorization reflects organizational structure
4. **Validation Rules**: Hierarchical authorization checks (e.g., a role can only manage users with roles below it)

##### *******. Role Definition with Hierarchy Support

```json
{
  "role": {
    "id": "SHIFT_LEADER",
    "name": "Shift Leader",
    "description": "Manages production shift and team leaders",
    "hierarchyLevel": 3,
    "parentRoles": ["PLANT_MANAGER"],
    "childRoles": ["TEAM_LEADER"],
    "azureAdGroups": ["Shift Leaders"]
  }
}
```

#### 3.1.2. Permission Framework

Permissions follow a simple resource:action format that clearly defines what operations a user can perform on specific resources.

##### *******. Permission Structure

| Component | Description                   | Example                                     |
| --------- | ----------------------------- | ------------------------------------------- |
| Resource  | The entity being accessed     | `team`, `schedule`, `training` , `operator` |
| Action    | The operation being performed | `create`, `read`, `update`, `delete`        |

##### *******. Permission Examples

| Permission          | Description                                |
| ------------------- | ------------------------------------------ |
| `team:manage`       | Manage team information and membership     |
| `operator:manage`   | Manage operator information and membership |
| `schedule:view`     | View schedules                             |
| `training:assign`   | Assign training to users                   |
| `equipment:control` | Control equipment and machinery            |
| `shift:manage`      | Manage shift schedules and assignments     |
| `quality:inspect`   | Perform quality inspection tasks           |
| `quality:approve`   | Approve quality-related documentation      |

#### 3.1.3. Scope Management

Scopes define the boundaries within which permissions can be exercised, allowing for fine-grained access control.

##### *******. Scope Types

| Scope Type     | Description                 | Examples                     |
| -------------- | --------------------------- | ---------------------------- |
| Organizational | Based on company structure  | Site, Department, Team       |
| Temporal       | Based on time periods       | Shift, Date Range            |
| Functional     | Based on functional areas   | Process Area, Equipment Type |
| Resource       | Based on specific resources | Machine ID, Material Type    |

##### *******. Scope Implementation

Scopes are implemented as attributes on user profiles, role assignments, and delegations. They can be derived from:

1. **Azure AD Attributes**: Department, location, job title
2. **Application Metadata**: Team assignments, shift assignments
3. **Delegation Restrictions**: Explicitly defined in delegation requests

#### 3.1.4. Role-Based Access Control

##### *******. Role Types and Hierarchy

The RBAC system implements a hierarchical role structure that reflects organizational management levels and functional specializations.

###### *******.1. Hierarchical Role Relationships

Role hierarchy is a fundamental aspect of the authorization system, providing clear lines of authority and permission inheritance:

1. **Vertical Relationships**: Define reporting lines (Manager → Supervisor → Team Lead → Operator)
2. **Horizontal Relationships**: Define peer relationships within the same hierarchy level
3. **Functional Relationships**: Define specialized cross-cutting concerns (Quality, Safety, Maintenance)

###### *******.2. Role Hierarchy Configuration

```json
{
  "roleHierarchyConfig": {
    "enforceHierarchicalRestrictions": true,
    "allowCrossLevelOperations": false,
    "maxLevelSkipForOperations": 1,
    "inheritPermissionsFromParent": false,
    "permissionOverrideRules": {
      "allowChildOverride": false,
      "allowScopedOverride": true
    }
  }
}
```

###### *******.3. Permission Management

Permissions are managed at multiple levels to provide flexible yet secure access control.

###### *******.4. Permission Assignment Model

The system uses a layered permission assignment model:

1. **Role-Based Permissions**: Core permissions assigned to roles
2. **Context-Based Permissions**: Permissions that vary based on context (e.g., location, time)
3. **Attribute-Based Rules**: Dynamic permissions based on user or resource attributes
4. **Policy-Based Overrides**: Global security policies that can override standard permissions

###### *******.5. Permission Resolution Process

```mermaid
flowchart TD
    A[Permission Check] --> B[Collect Role Permissions]
    B --> C[Apply Context Constraints]
    C --> D[Apply Attribute Rules]
    D --> E[Apply Policy Overrides]
    E --> F{Permission Granted?}
    F -->|Yes| G[Allow Operation]
    F -->|No| H[Deny Operation]
```

###### *******.6. Permission Conflict Resolution

| Conflict Type                    | Resolution Strategy                                         |
| -------------------------------- | ----------------------------------------------------------- |
| Role vs. Context                 | Context restrictions override role permissions              |
| Multiple Roles                   | Most permissive access granted (union of permissions)       |
| Hierarchical Conflicts           | Explicit permissions override inherited permissions         |
| Delegation vs. Direct Assignment | More restrictive permission applies                         |
| Policy vs. Role                  | Security policies always override role-based permissions    |
| Cross-functional Role Conflicts  | Domain-specific permissions take precedence in their domain |

#### 3.1.5. Group-Based Authorization

##### *******. Dynamic Groups

Dynamic groups in Azure AD provide flexible, attribute-based group membership that automatically updates based on user properties.

###### *******.1. Dynamic Group Rules

| Rule Type        | Description                        | Example Rule                                                   |
| ---------------- | ---------------------------------- | -------------------------------------------------------------- |
| Department-Based | Users from specific department     | `user.department -eq "Manufacturing"`                          |
| Location-Based   | Users at specific locations        | `user.physicalDeliveryOfficeName -eq "Plant A"`                |
| Title-Based      | Users with specific job titles     | `user.jobTitle -contains "Shift Leader"`                       |
| Combined Rules   | Multiple conditions for membership | `(user.department -eq "Quality") -and (user.country -eq "US")` |
| Exclusion Rules  | Exclude specific users from groups | `NOT(user.employeeId -in ["1234", "5678"])`                    |

###### *******.2. Benefits of Dynamic Groups

1. **Automatic Membership Management**: Group membership automatically updated as user attributes change
2. **Reduced Administrative Overhead**: No manual group membership management required
3. **Consistent Access Control**: Users with same attributes have same access
4. **Simplified Onboarding/Offboarding**: Access granted/removed automatically based on user attributes

##### *******. Group to Role Mapping

Azure AD security groups are mapped directly to application roles, creating a seamless integration between identity management and authorization.

###### *******.1. Configuration Example

| Azure AD Group          | Application Role  | Auto-Assign | Scope Attributes |
| ----------------------- | ----------------- | ----------- | ---------------- |
| Plant Managers          | PLANT_MANAGER     | Yes         | site             |
| Shift Leaders           | SHIFT_LEADER      | Yes         | site, shift      |
| Team Leaders            | TEAM_LEADER       | Yes         | team             |
| Quality Inspectors      | QUALITY_INSPECTOR | Yes         | department, site |
| Maintenance Technicians | MAINTENANCE_TECH  | Yes         | site             |

###### *******.2. Mapping Process Flow

```mermaid
sequenceDiagram
participant User
participant App as Application
participant API as API Backend
participant AD as Azure AD
participant Graph as Microsoft Graph API
participant DB as Database

User->>App: Authenticate with SAML
App->>API: Process SAML Response
API->>Graph: Query User Group Memberships
Graph->>API: Return Group Memberships
API->>DB: Lookup Group-Role Mappings
API->>API: Apply Role Assignments
API->>App: Return JWT with Roles
App->>User: Authorized Session
```

###### *******.3. Mapping Configuration

```json
{
  "groupMappings": [
    {
      "azureAdGroupId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "displayName": "Plant Managers",
      "applicationRole": "PLANT_MANAGER",
      "autoAssign": true,
      "scopeAttributes": ["site"]
    },
    {
      "azureAdGroupId": "b2c3d4e5-f6a7-8901-bcde-f12345678901",
      "displayName": "Quality Inspectors",
      "applicationRole": "QUALITY_INSPECTOR",
      "autoAssign": true,
      "scopeAttributes": ["department", "site"]
    }
  ]
}
```

This configuration is loaded when the microservice starts and is used during user synchronization with Microsoft Graph API to assign appropriate roles based on Azure AD group memberships.

### 3.2. Delegation Framework

The delegation framework enables secure, controlled transfer of authority between users while maintaining clear lines of responsibility and accountability. This system supports business continuity, operational flexibility, and allows for efficient handling of absence scenarios.

#### Delegation Process

Delegation enables temporary transfer of authority from one user to another through a streamlined process that includes approval mechanisms and clear documentation.

##### 3.2.1 Delegation Creation Flow

```mermaid
sequenceDiagram
    participant Delegator as Delegator
    participant N1Delegator as Delegator's N+1
    participant API as API Backend
    participant DB as Database
    participant N1Delegate as Delegate's N+1
    participant Delegate as Delegate

    Delegator->>API: Create Delegation Request
    API->>DB: Validate Delegator Authority
    API->>N1Delegator: Request Approval
    N1Delegator->>API: Approve Delegation
    API->>N1Delegate: Request Approval
    N1Delegate->>API: Approve Delegation
    API->>DB: Create Delegation Record
    API->>DB: Create Delegated Profile
    API->>Delegate: Notify of New Delegation
    API->>Delegator: Confirm Creation
```

##### 3.2.2 Delegation Request Structure

The delegation request captures all information necessary to define the delegation scope, duration, and justification:

```json
{
  "delegatorId": "user123",
  "delegateId": "user456",
  "roleId": "SHIFT_LEADER",
  "validFrom": "2025-03-20T00:00:00Z",
  "validUntil": "2025-03-27T23:59:59Z",
  "profileName": "Shift Leader Coverage",
  "reason": "Annual leave coverage",
  "permissions": {
    "included": ["shift:manage", "incidents:handle"],
    "excluded": ["budget:manage", "performance:review"]
  },
  "restrictions": {
    "scope": {
      "teams": ["TEAM_A", "TEAM_B"],
      "shifts": ["MORNING_SHIFT"]
    }
  },
  "requiresApproval": true,
  "approvers": {
    "delegatorSupervisorId": "manager789",
    "delegateeSupervisorId": "manager012"
  },
  "metadata": {
    "businessUnit": "Manufacturing",
    "requestTimestamp": "2025-03-15T09:30:00Z"
  }
}
```

##### 3.2.3 Approval Workflow

The delegation process includes a structured approval workflow to ensure proper oversight:

| Approval Stage     | Approver        | Responsibility                              | Fallback Mechanism                        |
| ------------------ | --------------- | ------------------------------------------- | ----------------------------------------- |
| Initial Review     | Delegator's N+1 | Validate business need and delegation scope | Auto-approval after 24h if critical       |
| Secondary Review   | Delegate's N+1  | Confirm delegate capacity and suitability   | Auto-approval after 24h if critical       |
| Emergency Override | Security Admin  | Bypass approval in emergency situations     | Limited to predefined emergency scenarios |

For time-sensitive delegations, a streamlined approval workflow can be configured with appropriate controls:

#### Delegation Restrictions

Restrictions limit the scope and capabilities of delegated authority, ensuring the principle of least privilege is maintained throughout the delegation lifecycle.

##### 4.2.1 Restriction Types

| Restriction Type          | Description                      | Example                                                   | Implementation                                                 |
| ------------------------- | -------------------------------- | --------------------------------------------------------- | -------------------------------------------------------------- |
| **Scope Limitations**     | Where authority can be exercised | `{"teams": ["TEAM_A", "TEAM_B"]}`                         | Limit access to specific organizational units                  |
| **Permission Inclusions** | Explicitly allowed operations    | `["shift:manage", "incidents:handle"]`                    | Only specific permissions from the role are delegated          |
| **Permission Exclusions** | Explicitly forbidden operations  | `["budget:manage", "performance:review"]`                 | Specific high-sensitivity permissions excluded from delegation |
| **Time Constraints**      | When delegation is active        | `{"validFrom": "2025-03-01", "validUntil": "2025-03-15"}` | Enforce strict temporal boundaries                             |
| **Approval Requirements** | Approval workflow configuration  | `{"requireN1Approval": true}`                             | Additional verification for sensitive delegations              |
| **Quantity Limits**       | Numeric limits on operations     | `{"maxDailyOperations": 20}`                              | Prevent abuse of delegated authority                           |

##### 4.2.2 Non-Delegable Permissions

Certain permissions can be configured as non-delegable to protect sensitive operations:

| Permission           | Rationale                             | Configuration                      |
| -------------------- | ------------------------------------- | ---------------------------------- |
| `performance:review` | Requires direct knowledge of employee | System-defined non-delegable       |
| `budget:approve`     | Financial control requirement         | Organization-defined non-delegable |
| `hr:terminate`       | Critical HR function                  | Organization-defined non-delegable |
| `security:admin`     | Security risk                         | System-defined non-delegable       |

Non-delegable permissions are configured at the system level by administrators:

```json
{
  "nonDelegablePermissions": {
    "systemDefined": ["security:admin", "performance:review"],
    "organizationDefined": ["budget:approve", "hr:terminate"],
    "override": {
      "allowed": false,
      "requiredApprovals": ["SECURITY_ADMIN", "IT_DIRECTOR"]
    }
  }
}
```

##### 4.2.3 Partial Role Delegation

The system supports delegation of a subset of permissions within a role, allowing for granular authority transfer:

```mermaid
flowchart LR
    A[Full Role] --> B{Delegation Filter}
    B -->|Include| C[Permission 1]
    B -->|Include| D[Permission 2]
    B -->|Exclude| E[Permission 3]
    B -->|Include| F[Permission 4]
    C --> G[Delegated Profile]
    D --> G
    F --> G
```

Example of partial role delegation configuration:

```json
{
  "roleId": "SHIFT_LEADER",
  "fullPermissions": [
    "shift:manage",
    "team:view",
    "incidents:handle",
    "reports:generate",
    "performance:review",
    "budget:view"
  ],
  "delegatedPermissions": {
    "included": ["shift:manage", "team:view", "incidents:handle"],
    "excluded": ["performance:review", "budget:view", "reports:generate"]
  }
}
```

#### Delegation Lifecycle

Delegations follow a defined lifecycle with clear state transitions and governance controls.

##### 4.3.1 Delegation States

```mermaid
stateDiagram-v2
    [*] --> Draft: Create
    Draft --> PendingApproval: Submit
    PendingApproval --> Approved: Approve
    PendingApproval --> Rejected: Reject
    Approved --> Active: Start Date Reached
    Active --> Expired: End Date Reached
    Active --> Revoked: Manual Revoke
    Draft --> Cancelled: Cancel
    PendingApproval --> Cancelled: Cancel
    Expired --> [*]: Cleanup
    Revoked --> [*]: Cleanup
    Rejected --> [*]: Cleanup
    Cancelled --> [*]: Cleanup
```

##### 4.3.2 State Transitions

| From                  | To              | Trigger                | Action                    | Notification                    |
| --------------------- | --------------- | ---------------------- | ------------------------- | ------------------------------- |
| -                     | Draft           | Initial creation       | Create draft record       | None                            |
| Draft                 | PendingApproval | Submission             | Send to approvers         | Approvers notified              |
| PendingApproval       | Approved        | All approvals received | Update status             | Delegator and delegate notified |
| PendingApproval       | Rejected        | Rejection by approver  | Update status with reason | Delegator notified              |
| Approved              | Active          | Start date reached     | Create delegated profile  | Delegate notified               |
| Active                | Expired         | End date reached       | Deactivate profile        | Both parties notified           |
| Active                | Revoked         | Manual revocation      | Deactivate with reason    | Both parties notified           |
| Draft/PendingApproval | Cancelled       | Cancellation request   | Update status             | Relevant parties notified       |

##### 4.3.3 Automatic Delegation

For predefined scenarios, the system supports automatic delegation when specific conditions are met:

```mermaid
flowchart TD
    A[Detect User Absence] --> B{Has Predefined Delegation?}
    B -->|Yes| C[Activate Predefined Delegation]
    B -->|No| D{N+1 Available?}
    D -->|Yes| E[Create Auto-Delegation to N+1]
    D -->|No| F[Escalate to System Admin]
    C --> G[Notify Delegate of Activation]
    E --> G
```

Automatic delegation configuration:

```json
{
  "automaticDelegations": [
    {
      "userId": "teamlead1",
      "absenceType": "ANY",
      "defaultDelegateId": "shiftlead1",
      "roleId": "TEAM_LEADER",
      "permissions": "ALL",
      "maxDuration": "7D",
      "requiresConfirmation": false
    }
  ]
}
```

When a Team Leader (TL) is absent, their responsibilities automatically delegate to their Shift Leader (SL). The SL can either:

- Execute the delegated TL role directly
- Further delegate the TL role to another qualified TL

#### Hierarchical Delegation Rules

Delegation within hierarchical organizational structures follows specific rules to maintain management chain integrity and ensure proper authorization.

##### 4.4.1 Hierarchical Delegation Principles

1. **Delegation Flow Direction**: Delegations typically flow downward in the hierarchy
2. **Level Restrictions**: Delegations are usually limited to adjacent levels
3. **Scope Limitations**: Delegated authority cannot exceed the delegator's scope
4. **Contextual Validation**: Delegation may require validation against organizational context

##### 4.4.2 Hierarchical Delegation Configuration

```json
{
  "delegationRules": {
    "hierarchyValidation": true,
    "allowSkipLevel": false,
    "maxSkipLevels": 0,
    "validateHierarchicalScope": true,
    "allowDelegationChains": false,
    "validateOrganizationalContext": true
  }
}
```

##### 4.4.3 Delegation Rules Matrix

| Delegator Role    | Delegate Role | Permitted | Reason                           | Restrictions                           |
| ----------------- | ------------- | --------- | -------------------------------- | -------------------------------------- |
| PLANT_MANAGER     | SHIFT_LEADER  | Yes       | Direct hierarchical relationship | Limited to delegator's site scope      |
| SHIFT_LEADER      | TEAM_LEADER   | Yes       | Direct hierarchical relationship | Limited to delegator's shift and teams |
| PLANT_MANAGER     | TEAM_LEADER   | No        | Skip level (configurable)        | N/A                                    |
| TEAM_LEADER       | SHIFT_LEADER  | No        | Against hierarchy direction      | N/A                                    |
| QUALITY_INSPECTOR | TEAM_LEADER   | No        | No hierarchical relationship     | N/A                                    |

#### Delegation Authorization Model

The delegation authorization model defines how delegation permissions are evaluated against delegation requests, ensuring proper authorization throughout the delegation lifecycle.

##### 4.5.1 Permission Evaluation Process

```mermaid
flowchart TD
    A[Delegation Request] --> B{Has Required Permission?}
    B -->|No| C[Deny: Insufficient Permission]
    B -->|Yes| D{Validate Hierarchy Rules}
    D -->|Fail| E[Deny: Hierarchy Violation]
    D -->|Pass| F{Validate Scope Rules}
    F -->|Fail| G[Deny: Scope Violation]
    F -->|Pass| H{Validate Context Rules}
    H -->|Fail| I[Deny: Context Violation]
    H -->|Pass| J{Validate Permission Rules}
    J -->|Fail| K[Deny: Permission Violation]
    J -->|Pass| L[Approve Delegation]
```

##### 4.5.2 Authorization Decision Factors

1. **Permission Check**: Verify the delegator has the appropriate delegation permission
2. **Hierarchy Validation**: Ensure the delegation follows hierarchical constraints
3. **Scope Validation**: Confirm the delegation is within appropriate scope boundaries
4. **Context Validation**: Check business context rules (e.g., qualifications, conflicts)
5. **Permission Validation**: Verify no non-delegable permissions are included

##### 4.5.3 Delegation Permission Structure

Delegation permissions follow the format `delegate:{direction}:{role}`, where:

- `delegate` is the resource
- `{direction}` defines the delegation direction relative to the hierarchy (same, down, up, manage)
- `{role}` specifies the target role for delegation (or `any` for multiple roles)

| Permission                    | Description                                     | Example                      |
| ----------------------------- | ----------------------------------------------- | ---------------------------- |
| `delegate:same:any`           | Delegate own role to someone with the same role | Team Leader to Team Leader   |
| `delegate:down:TEAM_LEADER`   | Delegate own role to a TEAM_LEADER              | Shift Leader to Team Leader  |
| `delegate:same:SHIFT_LEADER`  | Delegate own role to another SHIFT_LEADER       | Shift Leader to Shift Leader |
| `delegate:manage:TEAM_LEADER` | Manage delegations between TEAM_LEADER roles    | For supervisors              |
| `delegate:temporary:any`      | Create temporary delegations (limited duration) | For vacation coverage        |
| `delegate:partial:any`        | Create partial role delegations                 | For specific task coverage   |

##### 4.5.4 Permission Mapping Examples

| Scenario                                               | Required Permission            | Additional Validation                               |
| ------------------------------------------------------ | ------------------------------ | --------------------------------------------------- |
| Shift Leader delegates to another Shift Leader         | `delegate:same:SHIFT_LEADER`   | Validate shift scope                                |
| Shift Leader delegates to Team Leader                  | `delegate:down:TEAM_LEADER`    | Validate hierarchical relationship and shared scope |
| Plant Manager manages delegation between Shift Leaders | `delegate:manage:SHIFT_LEADER` | Validate supervisory relationship and shared scope  |
| Team Leader creates partial delegation to Team Leader  | `delegate:partial:TEAM_LEADER` | Validate partial permission set                     |

##### 4.5.5 Delegation Permission Assignment

Delegation permissions are assigned to roles based on organizational policies:

| Role                | Delegation Permissions                                                                                              |
| ------------------- | ------------------------------------------------------------------------------------------------------------------- |
| PLANT_MANAGER       | `delegate:same:PLANT_MANAGER`, `delegate:down:SHIFT_LEADER`, `delegate:manage:SHIFT_LEADER`, `delegate:partial:any` |
| SHIFT_LEADER        | `delegate:same:SHIFT_LEADER`, `delegate:down:TEAM_LEADER`, `delegate:manage:TEAM_LEADER`, `delegate:partial:any`    |
| TEAM_LEADER         | `delegate:same:TEAM_LEADER` (within shift), `delegate:temporary:OPERATOR`, `delegate:partial:TEAM_LEADER`           |
| TRAINER_RESPONSIBLE | `delegate:same:TRAINER_RESPONSIBLE`, `delegate:down:TRAINER`, `delegate:manage:TRAINER`, `delegate:partial:any`     |
| TRAINER             | `delegate:same:TRAINER` (within specialization), `delegate:partial:TRAINER`                                         |

#### Cross-Level and Same-Level Delegation

The framework supports both cross-level (hierarchical) and same-level (peer) delegations with appropriate controls.

##### 4.6.1 Same-Level Delegation (Peer Delegation)

Same-level delegation allows users with the same role to delegate authority to each other, typically within the same organizational scope:

```json
{
  "sameLevelDelegationRules": {
    "enforceSharedScope": true,
    "allowedScopeDifferences": ["team"],
    "requireExplicitPermission": true,
    "maxDuration": "30D",
    "requireApproval": true,
    "approvalRequiredFrom": ["delegatorSupervisor", "delegateeSupervisor"],
    "reasonRequired": true
  }
}
```

**Example: Team Leader to Team Leader**

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Coverage during scheduled training",
    "approvals": {
      "delegatorSupervisor": {
        "userId": "<EMAIL>",
        "status": "PENDING"
      },
      "delegateeSupervisor": {
        "userId": "<EMAIL>",
        "status": "PENDING"
      }
    },
    "restrictions": {
      "scope": {
        "site": "SITE_A",
        "department": "PRODUCTION",
        "shift": "MORNING",
        "team": "ASSEMBLY_1"
      },
      "permissions": {
        "included": ["team:view", "operator:view", "schedule:view"],
        "excluded": ["team:manage", "performance:review"]
      }
    }
  }
}
```

##### 4.6.2 Cross-Level Delegation (Hierarchical Delegation)

Cross-level delegation follows the organizational hierarchy, allowing delegation to flow downward with appropriate permissions:

```json
{
  "crossLevelDelegationRules": {
    "allowDirection": "downward",
    "maxLevelDistance": 1,
    "requireExplicitPermission": true,
    "enforceStrictScopeValidation": true,
    "maxDuration": "90D",
    "requireApproval": true,
    "approvalRequiredFrom": ["delegatorSupervisor", "delegateeSupervisor"],
    "reasonRequired": true
  }
}
```

**Example: Shift Leader to Team Leader**

```json
{
  "delegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "SHIFT_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Leadership development opportunity",
    "approvals": {
      "delegatorSupervisor": {
        "userId": "<EMAIL>",
        "status": "APPROVED"
      },
      "delegateeSupervisor": {
        "userId": "<EMAIL>",
        "status": "APPROVED"
      }
    },
    "restrictions": {
      "scope": {
        "site": "SITE_A",
        "department": "PRODUCTION",
        "shift": "MORNING"
      },
      "permissions": {
        "included": ["team:manage", "schedule:view", "incidents:handle"],
        "excluded": ["performance:review", "budget:manage"]
      }
    }
  }
}
```

##### 4.6.3 Supervisor-Managed Delegation

Supervisors can manage delegations between their direct reports with the appropriate delegation management permission:

```json
{
  "managedDelegationRules": {
    "requireDirectSupervisor": true,
    "requireSharedScope": true,
    "allowDelegationOutsideTeam": false,
    "requireExplicitPermission": true,
    "notifyParticipants": true,
    "reasonRequired": true
  }
}
```

**Example: Shift Leader Managing Team Leader Delegations**

```json
{
  "managedDelegationRequest": {
    "managerId": "<EMAIL>",
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Coverage during scheduled training",
    "restrictions": {
      "scope": {
        "team": "ASSEMBLY_1"
      },
      "permissions": {
        "included": ["team:view", "schedule:view"],
        "excluded": ["performance:review"]
      }
    }
  }
}
```

##### 4.6.4 Partial Role Delegation

The system supports delegation of a subset of permissions within a role, allowing for fine-grained responsibility transfer:

```json
{
  "partialDelegationRules": {
    "allowPartialDelegation": true,
    "requireExplicitPermission": true,
    "minimumPermissionsRequired": 1,
    "restrictedPermissions": [
      "budget:approve",
      "performance:finalize",
      "admin:access"
    ],
    "organizationConfigurableRestrictions": true,
    "reasonRequired": true,
    "requireApproval": true
  }
}
```

**Example: Partial Team Leader Delegation**

```json
{
  "partialDelegationRequest": {
    "delegatorId": "<EMAIL>",
    "delegateId": "<EMAIL>",
    "roleId": "TEAM_LEADER",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": "2025-04-14T23:59:59Z",
    "reason": "Need assistance with schedule management while focusing on quality issues",
    "approvals": {
      "delegatorSupervisor": {
        "userId": "<EMAIL>",
        "status": "APPROVED"
      }
    },
    "restrictions": {
      "scope": {
        "team": "ASSEMBLY_1"
      },
      "permissions": {
        "included": ["schedule:view", "schedule:manage", "schedule:approve"],
        "excluded": ["team:manage", "performance:review", "budget:request"]
      }
    }
  }
}
```

#### Advanced Delegation Scenarios

##### 4.7.1 Emergency Access

Emergency situations may require expedited access to critical functions, bypassing standard delegation procedures.

```mermaid
sequenceDiagram
    participant Requester
    participant API as API Backend
    participant Approver
    participant Delegate

    Requester->>API: Create Emergency Delegation Request
    API->>Approver: Send Approval Request (urgent)
    Approver->>API: Approve Emergency Request
    API->>API: Create Emergency Delegation & Profile
    API->>Delegate: Notify of Emergency Access
    API->>Requester: Confirm Emergency Delegation
```

**Emergency Delegation Configuration**

```json
{
  "emergencyDelegationRules": {
    "allowEmergencyDelegation": true,
    "allowedRequesters": ["PLANT_MANAGER", "SHIFT_LEADER", "SYSTEM_ADMIN"],
    "expeditedApproval": true,
    "maxDuration": "24H",
    "autoExpire": true,
    "enhancedLogging": true,
    "bypassScopeValidation": true,
    "requirePostIncidentReview": true,
    "notifySecurity": true
  }
}
```

**Emergency Access Controls**

1. Pre-defined emergency profiles with appropriate permissions
2. Short duration (typically 8-24 hours)
3. Enhanced monitoring and logging
4. Automatic notifications to security team
5. Post-incident review required
6. Clearly documented reason for emergency access

##### 4.7.2 Role Conflict Management

Some role combinations may create conflicts of interest and should be managed through the delegation framework.

**Conflict Resolution Strategies**

1. **Profile Separation**: Keep conflicting roles in separate profiles
2. **Scope Restrictions**: Limit scope where conflicting roles apply
3. **Operational Exclusions**: Remove specific permissions that create conflicts
4. **Approval Requirements**: Require additional approval for conflicting actions
5. **Enhanced Logging**: Increase audit detail for potential conflict scenarios

**Conflict Management Configuration**

```json
{
  "roleConflictRules": {
    "conflictingRolePairs": [
      {
        "roles": ["QUALITY_INSPECTOR", "PRODUCTION_MANAGER"],
        "resolutionStrategy": "SEPARATION"
      },
      {
        "roles": ["FINANCIAL_APPROVER", "BUDGET_REQUESTER"],
        "resolutionStrategy": "APPROVAL_REQUIRED"
      },
      {
        "roles": ["SYSTEM_ADMIN", "AUDITOR"],
        "resolutionStrategy": "OPERATIONAL_EXCLUSION"
      }
    ],
    "delegationBehavior": {
      "preventConflictingDelegations": true,
      "additionalApprovalRequired": true,
      "enhancedLoggingForConflicts": true
    }
  }
}
```

##### 4.7.3 Temporary Access Management

Short-term access needs can be handled through streamlined delegation with appropriate controls.

```mermaid
sequenceDiagram
    participant Manager
    participant API as API Backend
    participant Temporary as Temporary User

    Manager->>API: Create Temporary Access Request
    API->>API: Validate Manager Authority
    API->>API: Create Time-Limited Delegation
    API->>API: Create Restricted Profile
    API->>Temporary: Notify of Temporary Access
    API->>Manager: Confirm Temporary Access Creation
```

**Temporary Access Configuration**

```json
{
  "temporaryAccessRules": {
    "maxDuration": "30D",
    "requireJustification": true,
    "enforceRestrictions": true,
    "requireApproval": true,
    "automaticExpiration": true,
    "allowRenewal": true,
    "maxRenewals": 2,
    "enhancedLogging": true
  }
}
```

##### 4.7.4 Automatic Delegation (N+1 Substitution)

The system supports automatic delegation to a supervisor (N+1) when an employee (N) is absent, ensuring business continuity.

**Automatic Delegation Flow**

```mermaid
sequenceDiagram
    participant HRIS as HR System
    participant System as Delegation System
    participant Supervisor as N+1 Supervisor
    participant Alternate as Alternate Delegate

    HRIS->>System: Employee Absence Notification
    System->>System: Check Automatic Delegation Policy
    System->>System: Create Automatic Delegation to N+1
    System->>Supervisor: Notify of Automatic Delegation
    Note over Supervisor: Two options
    Supervisor->>System: Accept Delegation
    Supervisor->>System: Redirect Delegation
    System->>Alternate: Notify of Redirected Delegation
    Alternate->>System: Accept Delegation
    System->>System: Update Delegation Records
```

**Automatic Delegation Configuration**

```json
{
  "automaticDelegationRules": {
    "enabledForRoles": ["TEAM_LEADER", "SHIFT_LEADER", "QUALITY_INSPECTOR"],
    "triggerEvents": ["ABSENCE", "VACATION", "SICK_LEAVE"],
    "defaultDelegateLevel": "DIRECT_SUPERVISOR",
    "allowRedirection": true,
    "requireRedirectionApproval": false,
    "notifyHierarchy": true,
    "scope": "SAME_AS_ABSENT_USER",
    "permissions": {
      "strategy": "FULL_ROLE",
      "excludeRestrictedPermissions": true
    },
    "automaticExpiration": {
      "onUserReturn": true,
      "gracePeriod": "8H"
    }
  }
}
```

**Example: Automatic Delegation When Team Leader Is Absent**

```json
{
  "automaticDelegation": {
    "absentUserId": "<EMAIL>",
    "absentUserRole": "TEAM_LEADER",
    "delegateId": "<EMAIL>",
    "delegateRole": "SHIFT_LEADER",
    "delegationType": "AUTOMATIC",
    "reason": "Sick leave (automatic)",
    "validFrom": "2025-04-01T00:00:00Z",
    "validUntil": null,
    "expirationTrigger": "USER_RETURN",
    "scope": {
      "site": "SITE_A",
      "department": "PRODUCTION",
      "shift": "MORNING",
      "team": "ASSEMBLY_1"
    },
    "redirectionStatus": {
      "redirected": true,
      "redirectedTo": "<EMAIL>",
      "redirectionReason": "Higher priority tasks",
      "redirectionTime": "2025-04-01T08:30:00Z"
    }
  }
}
```

#### Approval Workflow

The delegation framework includes a comprehensive approval process for delegation requests, ensuring proper oversight and control.

##### 4.8.2 Required Approvers

The system supports multiple approval scenarios based on organizational requirements:

| Approval Type             | Description                                     | Required For                                |
| ------------------------- | ----------------------------------------------- | ------------------------------------------- |
| Delegator's Supervisor    | Direct manager of the delegating user           | Most delegation types                       |
| Delegatee's Supervisor    | Direct manager of the user receiving delegation | Cross-department delegations                |
| Department Head           | Head of affected department                     | Delegations affecting critical functions    |
| Role Administrator        | Administrator of the role being delegated       | High-security or critical roles             |
| Security Officer          | Security team member                            | Emergency access or sensitive system access |
| Multiple Level Management | Multiple levels of management                   | Executive role delegations                  |

##### 4.8.3 Approval Configuration

```json
{
  "approvalConfiguration": {
    "defaultRequiredApprovers": ["delegatorSupervisor"],
    "roleSpecificApprovals": {
      "PLANT_MANAGER": ["delegatorSupervisor", "securityOfficer"],
      "FINANCIAL_APPROVER": [
        "delegatorSupervisor",
        "financeHead",
        "delegateeSupervisor"
      ]
    },
    "crossDepartmentApprovals": [
      "delegatorSupervisor",
      "delegateeSupervisor",
      "departmentHead"
    ],
    "emergencyApprovals": ["securityOfficer"],
    "approvalTimeouts": {
      "standard": "72H",
      "emergency": "1H",
      "escalation": {
        "enabled": true,
        "escalateAfter": "24H",
        "escalateTo": "nextLevelManager"
      }
    },
    "delegationStartBehavior": {
      "requireAllApprovalsBeforeStart": true,
      "allowScheduledStartWithApprovals": true
    }
  }
}
```

##### 4.8.4 Approval Records

Each delegation retains detailed approval records to maintain audit trails:

```json
{
  "delegationApprovals": {
    "delegationId": "delegation-123",
    "status": "APPROVED",
    "approvalSteps": [
      {
        "approverType": "delegatorSupervisor",
        "approverId": "<EMAIL>",
        "status": "APPROVED",
        "timestamp": "2025-03-25T14:30:00Z",
        "comments": "Approved for training coverage"
      },
      {
        "approverType": "delegateeSupervisor",
        "approverId": "<EMAIL>",
        "status": "APPROVED",
        "timestamp": "2025-03-25T16:45:00Z",
        "comments": "Delegatee has required qualifications"
      }
    ],
    "finalApprovalDate": "2025-03-25T16:45:00Z",
    "delegationExecutionDate": "2025-04-01T00:00:00Z"
  }
}
```

#### Organization-Configurable Restrictions

The system allows organizations to define global delegation restrictions and configure which permissions can or cannot be delegated.

##### 4.9.1 Global Delegation Configuration

```json
{
  "organizationDelegationConfiguration": {
    "allowDelegation": true,
    "maxDelegationDuration": "90D",
    "enablePartialDelegation": true,
    "enableAutomaticDelegation": true,
    "enableEmergencyDelegation": true,
    "requireApprovals": true,
    "requireReason": true,
    "neverDelegatePermissions": [
      "user:delete",
      "role:create",
      "system:configure",
      "security:override"
    ],
    "restrictedDelegationPermissions": [
      "budget:approve:above:10000",
      "performance:finalize",
      "disciplinary:initiate"
    ],
    "delegationCreationRoles": [
      "SUPER_ADMIN",
      "PLANT_MANAGER",
      "SHIFT_LEADER",
      "TEAM_LEADER"
    ],
    "hierarchyRules": {
      "respectHierarchy": true,
      "maxLevelSkip": 1,
      "allowCrossDepartment": false,
      "allowCrossSite": false
    },
    "notificationRules": {
      "notifyDelegator": true,
      "notifyDelegatee": true,
      "notifyDelegatorSupervisor": true,
      "notifyDelegateeSupervisor": true,
      "notifySecurityTeam": ["EMERGENCY", "SENSITIVE_ROLE"]
    }
  }
}
```

##### 4.9.2 Role-Specific Delegation Restrictions

```json
{
  "roleDelegationRestrictions": {
    "PLANT_MANAGER": {
      "canBeDelegated": true,
      "requiresApproval": true,
      "requiredApprovers": ["superAdmin", "securityOfficer"],
      "maxDelegationDuration": "30D",
      "nonDelegablePermissions": [
        "budget:approve:above:50000",
        "employee:terminate",
        "facility:decommission"
      ],
      "allowedDelegateeRoles": ["PLANT_MANAGER", "ASSISTANT_PLANT_MANAGER"]
    },
    "FINANCIAL_APPROVER": {
      "canBeDelegated": true,
      "requiresApproval": true,
      "requiredApprovers": ["financeHead", "complianceOfficer"],
      "maxDelegationDuration": "14D",
      "nonDelegablePermissions": [
        "budget:approve:above:10000",
        "payment:authorize:above:5000"
      ],
      "allowedDelegateeRoles": ["FINANCIAL_APPROVER", "FINANCE_MANAGER"]
    }
  }
}
```

##### 4.9.3 Permission Delegation Matrix

The organization can define a detailed matrix of which permissions can be delegated by which roles:

| Permission             | SUPER_ADMIN | PLANT_MANAGER | SHIFT_LEADER | TEAM_LEADER | QUALITY_INSPECTOR |
| ---------------------- | :---------: | :-----------: | :----------: | :---------: | :---------------: |
| **Core Operations**    |             |               |              |             |                   |
| team:view              |  Delegate   |   Delegate    |   Delegate   |  Delegate   |     Delegate      |
| team:manage            |  Delegate   |   Delegate    |   Delegate   | No Delegate |    No Delegate    |
| schedule:view          |  Delegate   |   Delegate    |   Delegate   |  Delegate   |     Delegate      |
| schedule:manage        |  Delegate   |   Delegate    |   Delegate   |  Delegate   |    No Delegate    |
| **Critical Functions** |             |               |              |             |                   |
| budget:view            |  Delegate   |   Delegate    |   Delegate   | No Delegate |    No Delegate    |
| budget:approve         |  Delegate   |    Partial    | No Delegate  | No Delegate |    No Delegate    |
| performance:review     |  Delegate   |   Delegate    |   Partial    | No Delegate |    No Delegate    |
| **Administrative**     |             |               |              |             |                   |
| user:create            |   Partial   |  No Delegate  | No Delegate  | No Delegate |    No Delegate    |
| role:assign            |   Partial   |  No Delegate  | No Delegate  | No Delegate |    No Delegate    |
| system:configure       | No Delegate |  No Delegate  | No Delegate  | No Delegate |    No Delegate    |

Legend:

- **Delegate**: Can be fully delegated
- **Partial**: Can be delegated with restrictions
- **No Delegate**: Cannot be delegated

### 3.3. Microsoft Graph API Integration

This section details the integration between our authentication system and Microsoft Graph API, which enables real-time user provisioning, profile synchronization, and group-based role management with Microsoft Entra ID (formerly Azure AD).

#### 3.3.1. Service Principal Configuration

The integration with Microsoft Graph API requires a properly configured service principal in Microsoft Entra ID with appropriate permissions and security settings.

##### *******. Required Permissions

| Permission           | Type        | Description                | Justification                                                                 |
| -------------------- | ----------- | -------------------------- | ----------------------------------------------------------------------------- |
| User.Read.All        | Application | Read all user profiles     | Required to fetch comprehensive user data during JIT provisioning and refresh |
| GroupMember.Read.All | Application | Read all group memberships | Required to map Azure AD groups to application roles                          |
| Directory.Read.All   | Application | Read directory data        | Required for advanced directory queries and delta operations                  |

##### *******. Authentication Methods

Two primary authentication methods are supported for the service principal:

```mermaid
graph TD
    A[Service Principal Authentication] --> B[Client Credentials Flow]
    A --> C[Certificate-Based Authentication]
    B --> D[Uses Client ID and Secret]
    C --> E[Uses Client ID and Certificate]
    D --> F[Lower Setup Complexity]
    E --> G[Higher Security]
```

**Client Credentials Implementation:**

```json
{
  "tenant_id": "your-tenant-id",
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "scope": "https://graph.microsoft.com/.default"
}
```

**Certificate-Based Implementation:**

```json
{
  "tenant_id": "your-tenant-id",
  "client_id": "your-client-id",
  "certificate_thumbprint": "certificate-thumbprint",
  "private_key": "reference-to-private-key",
  "scope": "https://graph.microsoft.com/.default"
}
```

##### *******. Security Best Practices

| Best Practice       | Implementation                                                          |
| ------------------- | ----------------------------------------------------------------------- |
| Least Privilege     | Assign only required permissions; avoid global admin roles              |
| Credential Rotation | Implement 90-day rotation for client secrets                            |
| Secure Storage      | Store credentials in a secure vault (Azure Key Vault, HashiCorp Vault)  |
| Access Auditing     | Enable audit logs for service principal activity                        |
| IP Restrictions     | Apply conditional access policies to restrict access to known IP ranges |

#### 3.3.2. User Data Management

This section details how user profile data is retrieved, processed, and synchronized between Microsoft Entra ID and the application database.

##### *******. User Profile Retrieval

**Graph API Endpoint:**

```
GET https://graph.microsoft.com/v1.0/users/{userPrincipalName or id}
```

**Requested User Attributes:**

| Attribute         | Description         | Usage                            |
| ----------------- | ------------------- | -------------------------------- |
| displayName       | User's full name    | User profile display             |
| givenName         | First name          | Personalization                  |
| surname           | Last name           | User identification              |
| mail              | Primary email       | Communication, unique identifier |
| userPrincipalName | UPN                 | Alternative identifier           |
| jobTitle          | Position            | Role-based access control        |
| department        | Organizational unit | Departmental permissions         |
| officeLocation    | Physical location   | Regional settings                |
| employeeId        | HR identifier       | Integration with other systems   |
| id                | Object ID           | Graph API reference              |

**Synchronization Flow:**

```mermaid
sequenceDiagram
    participant App as Application
    participant Graph as Microsoft Graph API
    participant DB as Application Database

    App->>Graph: Request user profile data
    Graph->>App: Return user attributes
    App->>App: Transform data to application model
    App->>DB: Store/update user profile
    Note over App,DB: During each token refresh
```

##### *******. Just-In-Time (JIT) User Provisioning

The system implements Just-In-Time user provisioning to create user accounts automatically upon first login:

1. User authenticates with SAML SSO
2. System checks if user exists in the application database
3. If not found, retrieves comprehensive user profile from Graph API
4. Creates new user record with complete profile information
5. Maps Azure AD groups to application roles
6. Issues JWT tokens with user profile and role information

##### *******. Profile Synchronization Strategy

| Event                   | Synchronization Action                    |
| ----------------------- | ----------------------------------------- |
| First Authentication    | Full profile creation from Graph API      |
| Token Refresh           | Differential update of profile attributes |
| Group Membership Change | Update of roles and permissions           |
| Periodic Background Job | Full synchronization for inactive users   |

#### 3.3.3. Group Membership Management

This section details how Azure AD group memberships are retrieved and mapped to application roles.

##### *******. Group Membership Retrieval

**Graph API Endpoint:**

```
GET https://graph.microsoft.com/v1.0/users/{id}/memberOf
```

**Optional Filtering:**

```
GET https://graph.microsoft.com/v1.0/users/{id}/memberOf?$filter=securityEnabled eq true
```

**Implementation Considerations:**

| Consideration    | Approach                                                 |
| ---------------- | -------------------------------------------------------- |
| Large Group Sets | Implement pagination handling for users with many groups |
| Nested Groups    | Process transitive memberships when required             |
| Response Caching | Cache group memberships with appropriate TTL             |
| Error Handling   | Implement fallback for group retrieval failures          |

##### *******. Group-to-Role Mapping Configuration

The system maintains a configuration mapping between Azure AD groups and application roles:

```json
{
  "group-mappings": [
    {
      "group-id": "11b0131d-43c9-4734-9673-f2bc7f14f5e2",
      "group-name": "Application Administrators",
      "roles": ["admin", "user"],
      "permissions": ["full_access"]
    },
    {
      "group-id": "a8294692-bda8-41d5-91c8-58e3c54c9a2b",
      "group-name": "Finance Department",
      "roles": ["finance", "user"],
      "permissions": ["finance_read", "finance_write"]
    },
    {
      "group-id": "d4c46c76-7a5e-4aed-9a1f-ab72d7a39aa9",
      "group-name": "HR Managers",
      "roles": ["hr_manager", "user"],
      "permissions": ["employee_read", "employee_write"]
    }
  ]
}
```

##### *******. Dynamic Role Assignment

The system dynamically assigns roles based on group membership during:

1. Initial JIT provisioning
2. Each token refresh operation
3. Periodic background synchronization

#### 3.3.4. Delta Query Implementation

This section details how the system uses Microsoft Graph API's delta query functionality to efficiently track and apply changes from Microsoft Entra ID.

##### *******. Delta Query Fundamentals

The delta query feature allows the system to request only changes to users and groups since the last synchronization, significantly reducing bandwidth and processing requirements.

**Initial Delta Query:**

```
GET https://graph.microsoft.com/v1.0/users/delta
```

**Subsequent Delta Query using Delta Link:**

```
GET {deltaLink-from-previous-response}
```

##### *******. Tracking Changes

The system maintains delta tokens to track the state of synchronization:

| Entity Type   | Tracked Changes    | Delta Token Storage           |
| ------------- | ------------------ | ----------------------------- |
| Users         | Profile attributes | Database table with timestamp |
| Groups        | Membership changes | Database table with timestamp |
| Group members | Role assignments   | Database table with timestamp |

##### *******. Synchronization Process

```mermaid
sequenceDiagram
    participant Scheduler as Sync Scheduler
    participant Delta as Delta Service
    participant Graph as Microsoft Graph API
    participant DB as Database

    Scheduler->>Delta: Initiate sync cycle
    Delta->>DB: Retrieve latest delta token
    Delta->>Graph: Request changes using delta token
    Graph->>Delta: Return changed entities
    Delta->>DB: Process and apply changes
    Delta->>DB: Store new delta token
    Note over Delta,DB: Repeat on schedule or event
```

##### *******. Implementation Optimizations

| Optimization          | Description                                    | Benefit                             |
| --------------------- | ---------------------------------------------- | ----------------------------------- |
| Background Processing | Execute delta sync in background workers       | Improves application responsiveness |
| Selective Application | Apply only relevant changes                    | Reduces database load               |
| Change Batching       | Process changes in batches                     | Improves efficiency                 |
| Conflict Resolution   | Implement resolution for conflicting changes   | Maintains data integrity            |
| Webhook Triggers      | Trigger sync on directory change notifications | Ensures near real-time updates      |

##### *******. Error Handling and Resilience

| Error Scenario               | Handling Strategy                                     |
| ---------------------------- | ----------------------------------------------------- |
| Delta token expired          | Fall back to full synchronization                     |
| Temporary API unavailability | Implement exponential backoff retry                   |
| Rate limiting                | Adaptive throttling with prioritization               |
| Conflicting changes          | Apply business rules for resolution                   |
| Service degradation          | Circuit breaker pattern to prevent cascading failures |

## 4. Data Model

### 4.1 Core Entities

The data model consists of essential entities that support the integrated authentication, authorization, and delegation framework:

#### 4.1.1 Primary Entities

1. **User**: Represents an authenticated user in the system with attributes synchronized from Microsoft Entra ID.

   - Key attributes: id, email, displayName, givenName, surname, jobTitle, department, employeeId, officeLocation, lastSyncTime, isActive

2. **Role**: Defines a set of permissions and position in the organizational hierarchy.

   - Key attributes: id, name, description, hierarchyLevel, parentRoles, childRoles, isSystem

3. **Permission**: Represents a specific action that can be performed on a resource.

   - Key attributes: id, resource, action, description

4. **Profile**: A container for role assignments that provides context for a user's permissions.

   - Key attributes: id, userId, name, description, type, isActive, validFrom, validUntil, metadata

5. **RoleAssignment**: Links profiles to roles with specific restrictions and source information.

   - Key attributes: id, profileId, roleId, source, delegationId, restrictions, assignedAt

6. **Delegation**: Records the temporary transfer of authority from one user to another.
   - Key attributes: id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil, restrictions, reason, approverIds, approvalStatus, createdAt, lastModifiedAt

#### 4.1.2 Entity Relationships

- One User can have multiple Profiles
- Each Profile can contain multiple RoleAssignments
- Each RoleAssignment links to exactly one Role
- Roles can have parent-child relationships with other Roles
- Roles grant multiple Permissions
- Delegations connect a delegator User to a delegate User
- Each Delegation creates exactly one Profile

#### 4.1.3 Enumeration Types

- ProfileType (DIRECT, DELEGATED, SYSTEM)
- RoleSource (DIRECT_ASSIGNMENT, AZURE_AD_GROUP, DELEGATION, SYSTEM)
- DelegationStatus (PENDING, ACTIVE, EXPIRED, REVOKED)
- DelegationApprovalStatus (PENDING, APPROVED, REJECTED)

### 4.2 Database Schema

#### 4.2.1 Table Overview

| Table Name      | Primary Purpose              | Key Fields                                                                                                                                                  |
| --------------- | ---------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Users           | Store user information       | id, email, displayName, givenName, surname, jobTitle, department, employeeId, lastSyncTime                                                                  |
| Roles           | Define available roles       | id, name, description, hierarchyLevel, parentRoles, childRoles, isSystem                                                                                    |
| Permissions     | Define available permissions | id, resource, action, description                                                                                                                           |
| RolePermissions | Map roles to permissions     | roleId, permissionId                                                                                                                                        |
| Profiles        | Store user profiles          | id, userId, name, description, type, isActive, validFrom, validUntil, metadata                                                                              |
| RoleAssignments | Assign roles to profiles     | id, profileId, roleId, source, delegationId, restrictions, assignedAt                                                                                       |
| Delegations     | Track delegations            | id, delegatorId, delegateId, roleId, profileId, status, validFrom, validUntil, restrictions, reason, approverIds, approvalStatus, createdAt, lastModifiedAt |

#### 4.2.2 Table Schemas

**Users Table**

```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    given_name VARCHAR(255),
    surname VARCHAR(255),
    job_title VARCHAR(255),
    department VARCHAR(255),
    employee_id VARCHAR(50),
    office_location VARCHAR(255),
    last_sync_time TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Roles Table**

```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    hierarchy_level INT NOT NULL,
    parent_roles JSON,
    child_roles JSON,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Permissions Table**

```sql
CREATE TABLE permissions (
    id VARCHAR(36) PRIMARY KEY,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(resource, action)
);
```

**RolePermissions Table**

```sql
CREATE TABLE role_permissions (
    role_id VARCHAR(36) NOT NULL,
    permission_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);
```

**Profiles Table**

```sql
CREATE TABLE profiles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- DIRECT, DELEGATED, SYSTEM
    is_active BOOLEAN DEFAULT FALSE,
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**RoleAssignments Table**

```sql
CREATE TABLE role_assignments (
    id VARCHAR(36) PRIMARY KEY,
    profile_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    source VARCHAR(20) NOT NULL, -- DIRECT_ASSIGNMENT, AZURE_AD_GROUP, DELEGATION, SYSTEM
    delegation_id VARCHAR(36),
    restrictions JSON,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (delegation_id) REFERENCES delegations(id) ON DELETE SET NULL
);
```

**Delegations Table**

```sql
CREATE TABLE delegations (
    id VARCHAR(36) PRIMARY KEY,
    delegator_id VARCHAR(36) NOT NULL,
    delegate_id VARCHAR(36) NOT NULL,
    role_id VARCHAR(36) NOT NULL,
    profile_id VARCHAR(36),
    status VARCHAR(20) NOT NULL, -- PENDING, ACTIVE, EXPIRED, REVOKED
    valid_from TIMESTAMP NOT NULL,
    valid_until TIMESTAMP NOT NULL,
    restrictions JSON,
    reason TEXT NOT NULL,
    approver_ids JSON,
    approval_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (delegator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (delegate_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE SET NULL
);
```

### 4.3 System Architecture

#### 4.3.1 Key Relationships

1. **User → Profiles**: One user can have multiple profiles (direct, delegated, or system)

   - Enables context switching between different role sets
   - Supports multiple delegated profiles from different delegators

2. **Profile → RoleAssignments**: Each profile contains multiple role assignments

   - Clearly separates roles in different contexts
   - Supports different sources for role assignments (direct, Azure AD group-derived, delegation)

3. **RoleAssignment → Role**: Role assignments link profiles to specific roles

   - Includes source tracking for auditing
   - Contains scope and restriction information

4. **Role → Permissions**: Roles aggregate multiple permissions

   - Many-to-many relationship via RolePermissions table
   - Enables flexible permission management

5. **Role → Role**: Roles maintain hierarchical relationships

   - Parent-child relationships stored as JSON arrays
   - Supports organizational hierarchy alignment

6. **Delegation → User**: Tracks both delegator and delegate

   - Records the user who delegated authority
   - Records the user receiving delegated authority

7. **Delegation → Profile**: Delegations create delegated profiles
   - One-to-one relationship between delegation and profile
   - Profile inherits restrictions from delegation

#### 4.3.2 Microservice Data Flow

```mermaid
flowchart TD
    A[Authentication\nRequest] --> B[JWT\nToken Service]
    B --> C{User\nExists?}
    C -->|No| D[Create\nUser]
    C -->|Yes| E[Get User\nProfiles]
    D --> E
    E --> F[Load Active\nProfile]
    F --> G[Include Role\nAssignments]
    G --> H[Generate\nJWT]
    H --> I[Response with\nToken]

    J[Delegation\nRequest] --> K[Delegation\nService]
    K --> L{Validate\nPermissions}
    L -->|Invalid| M[Reject\nRequest]
    L -->|Valid| N[Create\nDelegation]
    N --> O[Create Delegated\nProfile]
    O --> P[Add Role\nAssignments]
    P --> Q[Notify\nDelegate]
```

#### 4.3.3 Azure AD Integration

Group-to-Role Mapping Configuration:

```json
{
  "groupMappings": [
    {
      "azureAdGroupId": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "displayName": "Plant Managers",
      "applicationRole": "PLANT_MANAGER",
      "autoAssign": true,
      "scopeAttributes": ["site"]
    },
    {
      "azureAdGroupId": "b2c3d4e5-f6a7-8901-bcde-f12345678901",
      "displayName": "Quality Inspectors",
      "applicationRole": "QUALITY_INSPECTOR",
      "autoAssign": true,
      "scopeAttributes": ["department", "site"]
    }
  ]
}
```

## 5. Conclusion

This Authentication, Authorization, and Delegation Guide provides a comprehensive framework for implementing a secure, scalable, and flexible identity and access management solution for enterprise applications. By integrating SAML-based Single Sign-On with JWT authentication and Microsoft Graph API synchronization, the system delivers a robust foundation for modern applications with complex organizational structures.

### 5.1. Key Architecture Benefits

The integrated approach described in this document offers several significant advantages:

1. **Seamless User Experience**

   - Single authentication event with persistent session management
   - Profile-based context switching without re-authentication
   - Reduced authentication redirects and interruptions

2. **Enterprise-Grade Security**

   - Centralized identity management through Microsoft Entra ID
   - Secure token handling with appropriate storage strategies
   - Comprehensive certificate and key management
   - Fine-grained permission control with role-based access

3. **Operational Flexibility**

   - Just-In-Time user provisioning from authoritative source
   - Real-time synchronization of user attributes and group memberships
   - Delegation framework supporting business continuity
   - Support for complex organizational hierarchies

4. **Reduced Administrative Overhead**

   - Automated user provisioning and deprovisioning
   - Group-based role assignment through Azure AD
   - Self-service delegation with appropriate controls
   - Streamlined profile and role management

5. **Technical Resilience**
   - Stateless architecture supporting horizontal scaling
   - Fault tolerance through token-based authentication
   - Optimized Graph API integration with delta queries
   - Comprehensive error handling and recovery strategies

### 5.2. Implementation Strategy

To successfully implement this architecture, we recommend a phased approach:

1. **Foundation Phase**

   - Configure Azure AD for SAML authentication
   - Implement JWT token service with refresh capabilities
   - Establish basic role and permission framework
   - Create core database schema

2. **Integration Phase**

   - Implement Microsoft Graph API synchronization
   - Develop group-to-role mapping functionality
   - Build profile management capabilities
   - Create basic authorization middleware

3. **Advanced Features Phase**

   - Implement delegation framework
   - Add profile switching functionality
   - Develop hierarchical role management
   - Create audit and monitoring systems

4. **Optimization Phase**
   - Implement performance optimizations
   - Enhance security features
   - Add advanced error handling
   - Create comprehensive monitoring and alerting

## 3. Crew Management

## 1. Overview

### 1.1 Purpose and Scope

The Crew Management module provides a comprehensive solution for managing manufacturing workforce operations. It implements a microservices architecture following Domain-Driven Design principles to ensure flexibility, scalability, and maintainability. The module enables manufacturing facilities to efficiently manage their workforce, track skills and certifications, maintain organizational structures, and optimize workstation assignments.

### 1.2 Key Components

The system consists of seven primary microservices:

1. Operator Management Service
2. Employee Assignment Service
3. Team Assignment Service
4. DH Walk Service
5. Workstation Service
6. Direct Dependents Service
7. Operator Skills Service

Each service is supported by:

- Azure Cosmos DB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Azure AD B2C for authentication and authorization

## 2. Architecture Overview

### 2.1 High-Level Architecture

```mermaid
graph TD
    Client[Client Applications] --> APIM[Azure API Management]
    APIM --> OM[Operator Management Service]
    APIM --> EA[Employee Assignment Service]
    APIM --> TA[Team Assignment Service]
    APIM --> DH[DH Walk Service]
    APIM --> WS[Workstation Service]
    APIM --> DD[Direct Dependents Service]
    APIM --> OS[Operator Skills Service]

    OM --> ES[Event Store]
    EA --> ES
    TA --> ES
    DH --> ES
    WS --> ES
    DD --> ES
    OS --> ES

    ES --> WServ[Write Service]

    WServ --> CDB[Cosmos DB Read Models]
    WServ --> SQL[SQL Read Models]

    WS --> CDB
    WS --> SQL
    DD --> CDB
    DD --> SQL
```

### 2.2 Technical Components

1. **NestJS Microservices**: Each service implemented as a NestJS application
2. **Event Store**: Cosmos DB containers for storing command-side events
3. **Service Bus**: Message broker for reliable event processing
4. **Write Service**: Dedicated service for updating read models
5. **Read Models**: Optimized for specific query needs in either SQL or Cosmos DB
6. **Azure API Management**: Unified entry point with authentication and routing
7. **Event Sourcing**: Captures all state changes as events

### 2.3 CQRS Implementation

The Connected Workers Platform implements a complete data isolation strategy between microservices:

1. **No Direct Communication**: Microservices do not communicate directly with each other, not even through asynchronous messaging systems like Service Bus.

2. **Event Storage and Processing**:

   - Events are stored in Cosmos DB command-side containers
   - Events are published to Azure Service Bus
   - A dedicated Write Service consumes events and updates read models
   - Complete separation of write and read operations

3. **Dual-Purpose Data Structure**:

   - Each microservice contains dedicated containers for write operations (events/commands)
   - Each microservice also maintains optimized read models for queries
   - The separation allows optimization for both write and read operations

4. **Read Models**:

   - SQL Tables: For relational data and complex queries
   - Cosmos DB Containers: For document-based queries and hierarchical data
   - Optimized for specific query patterns of each microservice

5. **Data Duplication Strategy**: When a microservice needs data owned by another microservice:

   - Data is duplicated into read models within the consuming microservice
   - This eliminates service-to-service dependencies and improves resilience

6. **Azure Change Feed Mechanism**:

   - Detects document changes in write model containers
   - Triggers functions to update corresponding read models
   - Enables eventual consistency between write and read models
   - Facilitates data duplication across microservice boundaries

7. **Read Model Projections**:
   - Specifically designed for query efficiency
   - Denormalized to avoid complex joins
   - May include redundant data to optimize specific query patterns
   - Updated asynchronously via Change Feed

This architecture provides several advantages:

- **Resilience**: Services can function independently even if other services are unavailable
- **Performance**: Data access is optimized for each service's specific needs
- **Scalability**: Services can scale independently based on their specific load patterns
- **Evolution**: Services can evolve independently with minimal impact on each other

## 3. Core Components

### 3.1 Operator Management Service

This document describes the low-level technical design for the **Operator Management** microservice (MS1), focusing on an **event-sourced** approach. The key responsibilities are:

1. **Create new operators** (with optional Excel batch upload).
2. **Assign a site / workstation** to each new operator.
3. **Determine the site responsible** (n+1) automatically for new operators.
4. **Store command/write data in Cosmos DB** (event-sourced).
5. **Provide read data** via a **separate database** fed by the **change feed** (handled by data team).

#### Domain Model & Entity

An **Operator** within this bounded context has attributes:

- `operatorId` (unique ID, generated on creation)
- `fullName`
- `siteId` or `siteName` (assigned site for the operator)
- `siteResponsibleId` (the "n+1" for that site, determined automatically on creation)
- **Other**: `createdDate`, `source` (e.g., `CW` vs. `WORKDAY`), etc.

#### Event Sourcing Model

##### Write Model in Cosmos DB

- A **Cosmos DB collection** (e.g., `operatorEvents`) holds **events** for each operator.
- **Partition Key**: Typically `operatorId`.
- **Event Documents**: Each event document has:
  - `id`: a unique GUID for the event
  - `operatorId`: the aggregate ID
  - `type`: e.g., `OperatorCreated`, `OperatorUpdated`
  - `timestamp`: date/time the event was produced
  - `payload`: JSON containing the event's data (e.g., operator name, site, etc.)

An example event document:

```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "operatorId": "operator-123",
  "type": "OperatorCreated",
  "timestamp": "2025-02-12T10:00:00Z",
  "payload": {
    "fullName": "John Doe",
    "siteId": "siteA",
    "siteResponsibleId": "manager-789",
    "source": "CW"
  }
}
```

##### Read Model in a Separate DB

- Data Team sets up a materialized view in, say, SQL or another NoSQL.
- They only store the latest operator info (flattened).

##### Change Feed for Projections

- Cosmos DB Change Feed can detect new event documents.
- The data team (or an internal process) subscribes to the feed:
  - Reads each new event.
  - Applies the event logic to the "read model" in the separate DB.
  - Upserts or modifies the row representing the latest state.

#### Commands, Events, and Workflows

##### CreateOperator Command

Purpose: User or system requests to create a new operator.

Input: `fullName`, `siteId` (optional if the user picks it), or it's derived from the Excel data.

Steps:

1. Validate that `fullName` is present.
2. Lookup `siteResponsibleId` for the given `siteId`.
3. Generate `operatorId` (GUID).
4. Produce `OperatorCreated` event in Cosmos DB.

Resulting Event: `OperatorCreated`

```json
{
  "operatorId": "operator-123",
  "fullName": "John Doe",
  "siteId": "siteA",
  "siteResponsibleId": "manager-789",
  "source": "CW",
  "timestamp": "..."
}
```

##### UpdateOperator Command

Purpose: If an operator's name, site, or other metadata changes.

Input: `operatorId`, updated fields.

Steps:

1. Check if `operatorId` exists (by replaying events or using a quick query).
2. Validate fields (e.g., new site requires new siteResponsible lookup?).
3. Produce `OperatorUpdated` event with the delta.

Resulting Event: `OperatorUpdated`

```json
{
  "operatorId": "operator-123",
  "changes": {
    "fullName": "Johnathan Doe",
    "siteId": "siteB",
    "siteResponsibleId": "manager-999"
  },
  "timestamp": "..."
}
```

#### Excel Upload Flow

1. User uploads an Excel file with one or more operators (name, site, etc.).
2. Parsing: The service reads the rows (e.g., using a Nest.js + xlsx library).
3. For each row:
   - Construct a CreateOperator command object.
   - Validate data.
   - Execute command → produces an OperatorCreated event in Cosmos DB.
4. Async or batch process: The read model is eventually updated once the events appear on the change feed.

#### API Endpoints (Write-Side)

Below are potential Nest.js endpoints for the command (write) side.

##### POST /operators

Body:

```json
{
  "fullName": "John Doe",
  "siteId": "siteA"
}
```

Behavior:

- Executes CreateOperator command.
- Returns operatorId or success status.

##### PUT /operators/:operatorId

Body:

```json
{
  "fullName": "Johnathan Doe",
  "siteId": "siteB"
}
```

Behavior:

- Executes UpdateOperator command.
- Produces an OperatorUpdated event.

##### POST /operators/upload-excel (batch creation)

- Consumes: .xlsx file.
- Behavior:
  - Parses each row → triggers CreateOperator for each.
  - Returns a summary of created IDs or error rows.

#### Roles & Permissions

##### Role Definitions

1. **TKS**

   - Can create and update operator basic information
   - Can view operator profiles across all sites
   - Cannot assign workstations
   - Can upload Excel files for batch operator creation
   - Limited access to skill information

2. **Team Leader**
   - Can view operators within their team/site
   - Can update workstation
   - Can view operator skills and workstation requirements
   - Cannot create or modify operator basic information

##### Permission Matrix

| Operation             | TKS | Team Leader |
| --------------------- | --- | ----------- |
| Create Operator       | Yes | No          |
| Update Operator Info  | Yes | No          |
| View Operator Profile | Yes | team Only   |
| Assign Workstation    | No  | Yes         |
| Upload Excel Batch    | Yes | No          |
| View Skills           | No  | team only   |
| Configure Skills      | No  | No          |
| Audit Events          | No  | No          |

#### Use Case Scenarios

##### New Operator Onboarding

1. **HR Initiates Onboarding**

   ```mermaid
   sequenceDiagram
       participant TKS
       participant API
       participant CommandHandler
       participant CosmosDB
       participant EventBus

       TKS->>API: CreateOperator Command
       API->>CommandHandler: Validate & Process
       CommandHandler->>CosmosDB: Store OperatorCreated Event
       CosmosDB->>EventBus: Publish Event
       EventBus->>ReadDB: Update Projections
       API->>TKS: Return Success
   ```

2. **Team Leader Assigns Workstation**
   - Checks operator skills
   - Validates against workstation requirements
   - Creates WorkstationAssigned event
   - Notifies relevant systems

##### Batch Upload Scenario

1. **Excel Upload Process**

   - HR uploads Excel file
   - System validates format
   - Processes rows in batches
   - Reports success/failures

2. **Error Handling**
   ```mermaid
   flowchart TD
       A[Upload Excel] --> B{Validate Format}
       B -->|Invalid| C[Return Error]
       B -->|Valid| D[Process Batch]
       D --> E{Row Validation}
       E -->|Failed| F[Add to Error Report]
       E -->|Success| G[Create Operator]
       G --> H[Next Row]
   ```

### 3.2 Employee Assignment Service

(This section would contain content from the LLD-employee-assignment-lld.md, but since the file content wasn't provided in the context, I'm leaving this as a placeholder. The structure would match the format of the other sections, preserving all original details from the LLD file.)

### 3.3 Team Assignment Service

#### 1. Overview

The **Team Assignment Service** is responsible for managing operator assignments to teams within the Connected Workers Platform. This service implements event sourcing and CQRS patterns to handle the assignment and unassignment of operators to teams.

##### Key Capabilities

1. **Team Assignment Management**

   - Assign operators to teams
   - Unassign operators from teams
   - Bulk assignment capabilities
   - Role-based assignment validation

2. **Assignment Validation**

   - Department-based validation
   - Workday status verification
   - Team leader authority validation
   - Site-based restrictions

3. **Query Capabilities**
   - View team assignments
   - Search operators by team
   - View unassigned operators
   - Filter by department and site

##### Technical Architecture

The service follows these architectural principles:

1. **Event Sourcing**

   - Assignment events stored in Cosmos DB
   - Complete audit trail of assignments
   - Event-based state reconstruction
   - Historical assignment view

2. **CQRS Pattern**
   - Separate command and query responsibilities
   - Optimized read models for queries
   - Eventual consistency
   - High performance for reads

##### Business Rules

1. **Assignment Rules**

   - Only team leaders can assign operators to their teams
   - Operators must belong to the same department as the team
   - Operators must be in active workday status
   - Assignments must be within the same site

2. **Validation Rules**
   - Team leader authority validation
   - Department membership validation
   - Workday status verification
   - Site boundary validation

#### 2. Domain Model & Events

##### 2.1 Event Types

```typescript
enum TeamAssignmentEventType {
  OPERATOR_ASSIGNED_TO_TEAM = "OPERATOR_ASSIGNED_TO_TEAM",
  OPERATOR_UNASSIGNED_FROM_TEAM = "OPERATOR_UNASSIGNED_FROM_TEAM",
}

interface BaseTeamEvent {
  id: string;
  type: TeamAssignmentEventType;
  timestamp: Date;
  site: string;
  metadata: {
    correlationId: string;
    userId: string;
    version: string;
  };
}

interface OperatorTeamAssignmentEvent extends BaseTeamEvent {
  type: TeamAssignmentEventType;
  payload: {
    assigner: {
      legacySiteId: string;
      role: string;
      firstName: string;
      lastName: string;
      department: string;
    };
    team: {
      teamId: string;
      teamName: string;
      legacyTeamLeaderId: string;
    };
    assignee: {
      legacySiteId: string;
      role: string;
      firstName: string;
      lastName: string;
      department: string;
    };
  };
}
```

##### 2.2 Event Examples

```json
// Assignment Event Example
{
  "id": "evt-123",
  "type": "OPERATOR_ASSIGNED_TO_TEAM",
  "timestamp": "2025-03-15T10:30:00Z",
  "site": "Site A",
  "metadata": {
    "correlationId": "corr-456",
    "userId": "user-789",
    "version": "1.0"
  },
  "payload": {
    "assigner": {
      "legacySiteId": "TL_123",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Assembly"
    },
    "team": {
      "teamId": "team-456",
      "teamName": "Assembly Team A",
      "legacyTeamLeaderId": "TL_123"
    },
    "assignee": {
      "legacySiteId": "OP_789",
      "role": "OPERATOR",
      "firstName": "Jane",
      "lastName": "Smith",
      "department": "Assembly"
    }
  }
}

// Unassignment Event Example
{
  "id": "evt-124",
  "type": "OPERATOR_UNASSIGNED_FROM_TEAM",
  "timestamp": "2025-03-15T14:30:00Z",
  "site": "Site A",
  "metadata": {
    "correlationId": "corr-457",
    "userId": "user-789",
    "version": "1.0"
  },
  "payload": {
    "assigner": {
      "legacySiteId": "TL_123",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Assembly"
    },
    "team": {
      "teamId": "team-456",
      "teamName": "Assembly Team A",
      "legacyTeamLeaderId": "TL_123"
    },
    "assignee": {
      "legacySiteId": "OP_789",
      "role": "OPERATOR",
      "firstName": "Jane",
      "lastName": "Smith",
      "department": "Assembly"
    }
  }
}
```

#### 3. Assignment Rules

```typescript
interface AssignmentRule {
  id: string;
  assignerRole: string; // Must be TEAM_LEADER
  assignToRole: string; // Must be TEAM
  assigneeRole: string; // Must be OPERATOR
  assignmentType: {
    assignToEmployee: boolean;
    assignToTeam: boolean;
    assignToWorkstation: boolean;
  };
  validationRules: {
    scope: [department | site | subordinate];
    requiresWorkday: boolean;
  };
  isActive: boolean;
  country: string;
}
```

##### 3.1 Example Assignment Rule

```json
{
  "id": "rule-001",
  "assignerRole": "TEAM_LEADER",
  "assignToRole": "TEAM",
  "assigneeRole": "OPERATOR",
  "assignmentType": {
    "assignToEmployee": true,
    "assignToTeam": false,
    "assignToWorkstation": false
  },
  "validationRules": {
    "requiresSameDepartment": true,
    "requiresSameSite": true,
    "requiresWorkday": true
  },
  "isActive": true,
  "country": "MOROCCO"
}
```

#### 4. Commands

##### 4.1 AssignToTeam Command

```typescript
interface AssignToTeamCommand {
  site: string;
  assigner: {
    id: string;
    role: string; // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignees: Array<{
    id: string;
    role: string; // Must be OPERATOR
  }>;
}
```

##### 4.2 UnassignFromTeam Command

```typescript
interface UnassignFromTeamCommand {
  site: string;
  assigner: {
    id: string;
    role: string; // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignee: {
    id: string;
    role: string; // Must be OPERATOR
  };
}
```

#### 5. Read Models

##### 5.1 Team Assignment Read Model

```json
{
  "teamleader_legacy_site": "E12345_MAR Moroocco 3",
  "teamleader_name": "cxxxxx",
  "teams": "team 1",
  "first_name": "xxx 1",
  "last_name": "xxx 1",
  "department": "E12345_MAR Moroocco 3",
  "operator_legacy_site": "cxxxxx",
  "role": "team 1",
  "role_status": "xxx 1",
  "site": "ssssss"
}
```

#### 6. API Endpoints

```typescript
// Assignment endpoint
POST /api/v1/assignments/assign-to-team
Request: {
  site: string;
  assigner: {
    id: string;
    role: string;  // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignees: Array<{
    id: string;
    role: string;  // Must be OPERATOR
  }>;
}
Response: {
  success: boolean;
  errors: string[];

}

// Unassignment endpoint
POST /api/v1/assignments/unassign-from-team
Request: {
  site: string;
  assigner: {
    id: string;
    role: string;  // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignee: {
    id: string;
    role: string;  // Must be OPERATOR
  };

}
Response: {
  success: boolean;
  errors: string[];

}

// Query endpoints
GET /api/v1/teams/{teamId}/operators
GET /api/v1/teams/unassigned-operators?department={dept}&site={site}
```

#### 7. Assignment Flow

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Management
    participant AS as Assignment Service
    participant RDB as Read DB
    participant ES as Event Store

    TL->>API: POST /assignments/assign-to-team
    API->>AS: Process Assignment Request

    AS->>RDB: Validate Team Leader
    RDB-->>AS: Team Leader Valid

    AS->>RDB: Validate Team
    RDB-->>AS: Team Valid

    AS->>RDB: Validate Operators
    RDB-->>AS: Operators Valid

    AS->>AS: Validate Business Rules
    Note over AS: - Same Department<br>- Same Site<br>- Active Workday

    alt Validation Failed
        AS-->>API: Return Error
        API-->>TL: 400 Bad Request
    else Validation Passed
        AS->>ES: Store Assignment Event
        ES-->>AS: Event Stored
        AS-->>API: Success Response
        API-->>TL: 200 OK
    end
```

### 3.4 DH Walk Service

#### 1. Overview

The **DH Walk Microservice** is a critical component within the Connected Workers Platform that manages the organizational hierarchy and team structure of the manufacturing environment. This service enables Department Clerks to:

1. Create, update, and delete teams across the manufacturing organization
2. Assign and reassign Indirect Headcount (IH) roles (Coordinators, Shift Leaders, and Team Leaders) to teams
3. Maintain the hierarchical relationship between projects, families, value streams, areas, and teams

The DH Walk Microservice implements an event-sourcing architecture to provide full audit capabilities and history tracking of all organizational changes. By capturing all state changes as events, the system maintains a complete record of team structures and role assignments over time, enabling historical analysis and state reconstruction.

The service leverages the Command Query Responsibility Segregation (CQRS) pattern to separate write operations (team creation, role assignments) from read operations (viewing team structures and assignments). This approach enables optimized query performance while maintaining data integrity for state-changing operations.

#### 2. Business Requirements

The DH Walk Microservice addresses the following key business requirements:

1. **Organizational Structure Management**

   - Define and maintain manufacturing hierarchies from project down to team level
   - Track relationships between projects, families, value streams, areas, and teams
   - Support dynamic reorganization as manufacturing needs evolve

2. **Role Assignment Management**

   - Assign appropriate Coordinators, Shift Leaders, and Team Leaders to teams
   - Support reassignment as personnel changes occur
   - Maintain historical record of role assignments and changes

3. **Department Clerk Capabilities**

   - Enable Department Clerks to efficiently create and manage teams
   - Provide Department Clerks with assignment capabilities for all IH roles
   - Support bulk operations for efficient management of large organizations

4. **Visibility and Filtering**

   - Support filtering by project, family, value stream, and area
   - Provide search capabilities to quickly find specific teams
   - Enable sorting by various attributes (e.g., date, name)

5. **Integration with Connected Workers Platform**
   - Share team and role assignment data with other microservices
   - Consume ME definition data (projects, families, value streams, areas)
   - Support downstream processes that depend on team structures

#### 3. Core Domain Models

##### 3.1 Zoning Document

The Zoning Document represents the assignment of IH roles to a specific team within the manufacturing structure hierarchy.

```typescript
interface ZoningDocument {
  id: string; // Unique identifier
  project: string; // Project name
  family: string; // Family name
  value_stream: string; // Value stream
  area: string; // Area
  coordinator_legacy_site_id: string; // Unique ID of the coordinator
  coordinator_fullname: string; // Coordinator's full nam
  shift_leader_legacy_site_id: string;
  shift_leader_fullname: string; // Shift leader's full name                     //
  team_leader_legacy_site_id: string; // Unique ID of the team leader
  team_leader_fullname: string; // Team leader's full name
  team_name: string; // Name of the team
  version: number; // Document version for concurrency control
  created_at: string; // Creation timestamp (ISO format)
  updated_at: string; // Last update timestamp (ISO format)
  created_by: string; // User ID of creator
  updated_by: string; // User ID of last updater
}
```

##### 3.2 Team Document

The Team Document represents a team entity within the manufacturing structure hierarchy.

```typescript
interface TeamDocument {
  id: string;                      // Unique identifier
  team_name: string;               // Name of the team
  team_leader_fullname string;                    // Can be null if not assigned
  is_active: boolean;              // Team active status
  headcount: number;               // Number of operators in the team
  version: number;                 // Document version for concurrency control
  created_at: string;              // Creation timestamp (ISO format)
  updated_at: string;              // Last update timestamp (ISO format)
  created_by: string;              // User ID of creator
  updated_by: string;              // User ID of last updater
}
```

#### 4. Event Model

##### 4.1 Event Types

```typescript
enum DHWalkEventType {
  CREATE_TEAM = "CreateTeam",
  UPDATE_TEAM = "UpdateTeam",
  DELETE_TEAM = "DeleteTeam",
  ASSIGN_TO_TEAM = "AssignedToTeam",
  UNASSIGN_FROM_TEAM = "UnassignedFromTeam",
}

enum IHRole {
  COORDINATOR = "COORDINATOR",
  SHIFT_LEADER = "SHIFT_LEADER",
  TEAM_LEADER = "TEAM_LEADER",
}
```

##### 4.2 Event Structure

```typescript
interface BaseEvent {
  id: string; // Unique event identifier
  createdBy: string; // User ID who initiated the event
  eventType: DHWalkEventType; // Type of event
  timestamp: string; // Timestamp when event occurred (ISO format)
  version: string; // Event schema version
  correlationId?: string; // Optional correlation ID for tracing
}
```

##### 4.3 Event Examples

###### Create Team Event

```typescript
interface CreateTeamEvent extends BaseEvent {
  eventType: DHWalkEventType.CREATE_TEAM;
  payload: {
    team_name: string; // Name of the new team
    TL_legacy_site_id: string; // Team Leader's legacy site ID
    TL_fullname: string; // Team Leader's full name
  };
}
```

Example:

```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "CreateTeam",
  "timestamp": "2025-02-12T10:00:00Z",
  "version": "1.0",
  "payload": {
    "TL_legacy_site_id": "TL_12345",
    "TL_fullname": "John Smith",
    "team_name": "Assembly Team 1"
  }
}
```

###### Assign To Team Event

```typescript
interface AssignToTeamEvent extends BaseEvent {
  eventType: DHWalkEventType.ASSIGN_TO_TEAM;
  payload: {
    project: string; // Project name
    family: string; // Family name
    value_stream: string; // Value stream
    area: string; // Area
    Coor_legacy_site_id: string; // Coordinator's legacy site ID
    Coor_fullname: string; // Coordinator's full name
    SL_legacy_site_id: string; // Shift Leader's legacy site ID
    SL_fullname: string; // Shift Leader's full name
    TL_legacy_site_id: string; // Team Leader's legacy site ID
    TL_fullname: string; // Team Leader's full name
    team_name: string; // Team name
  };
}
```

Example:

```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToTeam",
  "timestamp": "2025-02-12T10:00:00Z",
  "version": "1.0",
  "payload": {
    "project": "V426A",
    "family": "V426-L&R-RearDoor",
    "value_stream": "Line 1",
    "area": "SUB",
    "Coor_legacy_site_id": "CO_12345",
    "Coor_fullname": "Said Ousrhir",
    "SL_legacy_site_id": "SL_67890",
    "SL_fullname": "Bendissa Hibbou",
    "TL_legacy_site_id": "TL_45678",
    "TL_fullname": "WARDA HARDALA",
    "team_name": "E209C"
  }
}
```

#### 5. Commands

##### 5.1 Command Types

```typescript
// Create Team Command
interface CreateTeamCommand {
  team_name: string; // Team name
  team_leader_id?: string; // Optional Team Leader ID (can be assigned later)
}

// Assign Role Command
interface AssignRoleCommand {
  project: string; // Project name
  family: string; // Family name
  value_stream: string; // Value stream
  area: string; // Area
  team_name: string; // Team name
  role: IHRole; // COORDINATOR, SHIFT_LEADER, or TEAM_LEADER
  employee_id: string; // Legacy site ID of assignee
}

// Unassign Role Command
interface UnassignRoleCommand {
  project: string; // Project name
  family: string; // Family name
  value_stream: string; // Value stream
  area: string; // Area
  team_name: string; // Team name
  role: IHRole; // COORDINATOR, SHIFT_LEADER, or TEAM_LEADER
}

// Update Team Command
interface UpdateTeamCommand {
  team_id: string; // Team ID
  team_name?: string; // New team name (optional)
  is_active?: boolean; // New active status (optional)
}

// Delete Team Command
interface DeleteTeamCommand {
  team_id: string; // Team ID
}
```

##### 5.2 Command Validation

Commands are validated based on:

1. **Data Integrity** - Ensure all required fields are present and valid
2. **Business Rules** - Enforce rules such as:
   - Team names must be unique within a project-family-value_stream-area combination
   - Users can only be assigned to one role at a time
   - Department Clerks can only manage teams within their department
3. **Concurrency** - Handle concurrent modification using optimistic concurrency control

#### 6. Permissions and Authorization

##### 6.1 Role-Based Access Control

| Role                   | Create Team | Delete Team | Update Team | Assign Coordinator | Assign Shift Leader | Assign Team Leader | View Teams |
| ---------------------- | ----------- | ----------- | ----------- | ------------------ | ------------------- | ------------------ | ---------- |
| Department Clerk       | ✓           | ✓           | ✓           | ✓                  | ✓                   | ✓                  | ✓          |
| Coordinator            |             |             |             |                    |                     |                    | ✓          |
| Shift Leader           |             |             |             |                    |                     |                    | ✓          |
| Team Leader            |             |             |             |                    |                     |                    | ✓          |
| Manufacturing Engineer |             |             |             |                    |                     |                    | ✓          |

##### 6.2 Authorization Rules

1. Department Clerks can only manage teams within their assigned departments
2. Users can view teams and assignments according to their hierarchical position
3. Special roles (e.g., Quality Supervisors) may have cross-departmental view permissions
4. Audit trail of all changes is maintained for accountability

#### 7. API Endpoints

##### 7.1 Team Management

```typescript
// Create a new team
POST /api/v1/teams
Request: {
  team_name: string;
  team_leader_id?: string;  // Optional
}
Response: {
  id: string;  // Newly created team ID
  success: boolean;
  errors?: string[];
}

// Update an existing team
PUT /api/v1/teams/{teamId}
Request: {
  team_name?: string;
  is_active?: boolean;
}
Response: {
  success: boolean;
  errors?: string[];
}

// Delete a team
DELETE /api/v1/teams/{teamId}
Response: {
  success: boolean;
  errors?: string[];
}
```

##### 7.2 Assignment Management

```typescript
// Assign role to team
POST /api/v1/assignments
Request: {
  project: string;
  family: string;
  value_stream: string;
  area: string;
  team_name: string;
  role: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER";
  employee_id: string;
}
Response: {
  success: boolean;
  errors?: string[];
}

// Unassign role from team
DELETE /api/v1/assignments
Request: {
  project: string;
  family: string;
  value_stream: string;
  area: string;
  team_name: string;
  role: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER";
}
Response: {
  success: boolean;
  errors?: string[];
}
```

##### 7.3 Query Endpoints

```typescript
// Get all teams with optional filtering
GET /api/v1/teams
Query Parameters:
  - project?: string
  - family?: string
  - value_stream?: string
  - area?: string
  - team_name?: string
  - has_coordinator?: boolean
  - has_shift_leader?: boolean
  - has_team_leader?: boolean
  - is_active?: boolean
  - sort_by?: string
  - page?: number
  - page_size?: number
Response: {
  teams: ZoningDocument[];
  total_count: number;
  page: number;
  page_size: number;
}

// Get specific team by ID
GET /api/v1/teams/{teamId}
Response: {
  team: ZoningDocument;
}

// Get assignment history
GET /api/v1/teams/{teamId}/history
Query Parameters:
  - role?: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER"
  - from_date?: string  // ISO format
  - to_date?: string    // ISO format
  - page?: number
  - page_size?: number
Response: {
  history: {
    event_type: string;
    timestamp: string;
    role: string;
    employee_id?: string;
    employee_name?: string;
    performed_by: string;
  }[];
  total_count: number
}
```

#### 8. Workflows

##### 8.1 Team Creation

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB

    DC->>API: Create Team Request
    API->>API: Validate Request

    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store CreateTeam Event
        ES->>RP: Process Event
        RP->>DB: Create Team Document
        RP->>DB: Create Initial Zoning Document
        API-->>DC: Return Success
    end
```

##### 8.2 Role Assignment

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Assign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments

    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Event
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
```

##### 8.3 Role Reassignment

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Reassign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments

    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store UnassignedFromTeam Event
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Events
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
```

#### 9. Data Persistence

##### 9.1 Document Structure

The DH Walk microservice uses a document database (Cosmos DB) to store:

1. **Events** - All system events in an append-only event store
2. **Teams** - Team documents containing team details
3. **Zonings** - Zoning documents representing the assignment structure
4. **Projection Models** - Optimized read models for UI display

##### 9.2 Event Storage

Events are stored in an append-only event store with the following structure:

```typescript
interface StoredEvent {
  id: string; // Event ID
  type: string; // Event type
  payload: any; // Event payload (serialized JSON)
  metadata: {
    timestamp: string; // When the event occurred
    user_id: string; // Who created the event
    correlation_id?: string; // For tracking related events
    causation_id?: string; // What caused this event
    version: string; // Schema version
  };
  sequence_number: number; // For ordering events
  stream_id: string; // Aggregate ID this event belongs to
}
```

##### 9.3 Read Model

The read model is optimized for the UI and contains denormalized data for efficient querying:

```typescript
interface TeamProjectionModel {
  id: string; // Unique identifier
  project: string; // Project name
  family: string; // Family name
  value_stream: string; // Value stream
  area: string; // Area
  team_name: string; // Team name
  coordinator_id?: string; // Coordinator ID (if assigned)
  coordinator_name?: string; // Coordinator name (if assigned)
  shift_leader_id?: string; // Shift Leader ID (if assigned)
  shift_leader_name?: string; // Shift Leader name (if assigned)
  team_leader_id?: string; // Team Leader ID (if assigned)
  team_leader_name?: string; // Team Leader name (if assigned)
  is_active: boolean; // Active status
  headcount: number; // Number of operators
  last_updated: string; // Last update timestamp
}
```

#### 10. Integration Patterns

##### 10.1 Event Publication

After storing events in the event store, the DH Walk microservice publishes events to Azure Service Bus for consumption by other microservices:

```typescript
interface IntegrationEvent {
  id: string; // Event ID
  type: string; // Event type
  payload: any; // Event payload
  timestamp: string; // When the event occurred
  source: string; // Source system ("dh-walk")
  version: string; // Schema version
}
```

##### 10.2 External System Integration

The DH Walk microservice integrates with:

1. **ME Definition Service** - To retrieve project, family, value stream, and area data
2. **Identity Service** - To retrieve employee information
3. **Workstation Service** - To notify of Team Leader changes
4. **TKS Service** - To notify of team structure changes

#### 11. UI Components

##### 11.1 Project List View

The main UI view shows a table of projects with their associated families, value streams, areas, and assigned roles:

1. **Filtering Controls** - Filter by project, family, value stream, area
2. **Search Box** - Quick search across all fields
3. **Sort Controls** - Sort by various columns
4. **Assignment Status** - Visual indicators for filled/unfilled positions
5. **Assignment Buttons** - Buttons for assigning unassigned roles

##### 11.2 Assignment Dialogs

When assigning roles, modal dialogs provide:

1. **Employee Selection** - Search and select from eligible employees
2. **Role Details** - Information about the role being assigned
3. **Validation Feedback** - Real-time validation and error messages
4. **Confirmation** - Confirmation of assignment action

#### 12. Performance Considerations

Performance optimization strategies for the CQRS architecture include:

- **Database Indexing**: Strategic indexes on common query patterns
- **Read/Write Separation**: CQRS pattern for high-throughput operations
- **Dedicated Write Service**: Specialized service for read model updates
- **Asynchronous Processing**: Background processing for non-critical operations
- **Pagination**: All list endpoints support pagination for large datasets
- **Rate Limiting**: API rate limiting to prevent abuse
- **Error Handling**: Robust retry strategies and dead-letter queues
- **Caching**: Strategic caching of frequently accessed read models
- **Query Optimization**: Read models designed for specific query patterns
- **Independent Scaling**: Command and query sides can scale independently
- **Optimized Event Processing**: Batch processing for higher throughput

#### 13. Security Considerations

1. **Authentication** - Azure AD integration with role-based permissions
2. **Authorization** - Granular permission checks based on role and department
3. **Audit Logging** - Complete audit trail of all actions
4. **Data Protection** - Encryption in transit and at rest
5. **Input Validation** - Defense against injection attacks

#### 14. Deployment Strategy

1. **Containerization** - Docker containers for consistent deployment
2. **Kubernetes** - For orchestration and scaling
3. **CI/CD Pipeline** - Automated testing and deployment
4. **Blue/Green Deployments** - Zero-downtime updates
5. **Environment Separation** - Dev, Test, Staging, Production

### 3.5 Workstation Service

(This section would contain content from the LLD-workstation.md, but since the file content wasn't provided in the context in a usable format, I'm leaving this as a placeholder. The structure would match the format of the other sections, preserving all original details from the LLD file.)

### 3.6 Direct Dependents Service

#### 1. Introduction

The Direct Dependents microservice provides a hierarchical view of the organizational structure for Aptiv Connected Workers. It enables visibility of reporting relationships from Plant Manager to operator level.

#### 2. Architecture Overview

- **Read-Only Service**: The microservice is designed to perform read operations from a database.
- **Data Source**: Reads hierarchical data and unassigned workers from an existing cosmosdb.
- **API**: Exposes endpoints to fetch organizational charts and unassigned employees based on user roles.

#### 3. Domain Model & Entities

##### 3.1 UserRoleHierarchy

```csharp
public class UserRoleHierarchy
{
    public string LecacySiteId { get; set; } // employee_id
    public string FullName { get; set; }
    public string Department { get; set; }
    public string Site { get; set; }
    public string Role { get; set; }
    public string ManagerLecacySiteId { get; set; }
    public List<SubordinateCrew> SubordinateCrew { get; set; }
}

public class SubordinateCrew
{
    public string SubLecacySiteId { get; set; }
    public string SubFullName { get; set; }
    public string SubRole { get; set; }
    public string SubRoleStatus { get; set; }
    public bool InWorkday { get; set; }
    public List<string> skills { get; set; }
    public string category { get; set; }
    public string contract_type { get; set; }
    public List<SubordinateCrew> Subordinates { get; set; }
}
```

##### 3.2 TeamLeaderTeamCrew

```csharp
public class TeamLeaderTeamCrew
{
    public string TeamLeaderLegacySite { get; set; }
    public string TeamLeaderName { get; set; }
    public string Teams { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Department { get; set; }
    public string OperatorLegacySite { get; set; }
    public string Role { get; set; }
    public string RoleStatus { get; set; }
}
```

##### 3.3 AssignmentRule

```csharp
public class AssignmentRule
{
    public string Id { get; set; }
    public string AssignerRole { get; set; }
    public string AssignToRole { get; set; }
    public string AssigneeRole { get; set; }
    public ValidationRules ValidationRules { get; set; }
    public bool IsActive { get; set; }
    public string Country { get; set; }
    public string Scope { get; set; }
}

public class ValidationRules
{
    public bool RequiresDirectHierarchy { get; set; }
    public bool RequiresSameDepartment { get; set; }
    public bool RequiresWorkday { get; set; }
}
```

#### 4. Business Logic

##### 4.1 OrganisationChartService

- **GetOrganisationChart**: Determines the role of the employee and fetches the organization chart based on the role and parameters.
- **GetUnassignedEmployees**: Fetches unassigned employees for the given user based on their role.

#### 5. API Endpoints

##### 5.1 Get /OrganisationChart

- **Endpoint**: `/api/organisation-chart`
- **Method**: `GET`
- **Query Parameters**: `legacySiteId` (string)

##### 5.2 Get /UnassignedEmployees

- **Endpoint**: `/api/unassigned-employees`
- **Method**: `GET`
- **Query Parameters**: `legacySiteId` (string)

#### 6. Database Schema

##### 6.1 UserRoleHierarchy Table

- **Columns**: `LecacySiteId`, `FullName`, `Department`, `Site`, `Role`, `ManagerLecacySiteId`, `SubordinateCrew`

##### 6.2 TeamLeaderTeamCrew Table

- **Columns**: `TeamLeaderLegacySite`, `TeamLeaderName`, `Teams`, `FirstName`, `LastName`, `Department`, `OperatorLegacySite`, `Role`, `RoleStatus`

##### 6.3 AssignmentRule Table

- **Columns**: `Id`, `AssignerRole`, `AssignToRole`, `AssigneeRole`, `ValidationRules`, `IsActive`, `Country`, `Scope`

#### 7. Error Handling

- **Business Errors**: Invalid role, missing data.
- **Technical Errors**: Database connectivity issues, API failures.

#### 8. Summary

The Direct Dependents microservice provides a flexible and generic way to fetch organizational data and unassigned employees based on employee roles and parameters defined in the `AssignmentRule` table. The service can be extended to handle additional cases by updating the parameter table and adding corresponding logic in the service layer.

### 3.7 Operator Skills Service

#### 1. Overview of Architecture

The Operator Skills service adopts a straightforward event-driven architecture:

##### Event Handling:

- The service subscribes to a Service Bus topic to receive events.
- Events contain operator skills data (operator ID and skills).
- The service processes these events and stores the data in a database.

##### Database:

- A relational database (e.g., SQL Server) is used to store operator skills data.
- The database schema is designed to efficiently query operator skills by employee ID and site.

#### 2. Domain Model & Entity

##### 2.1 Operator Skills

An Operator Skills entity within this bounded context has attributes:

- `employee_id` (unique ID for the employee)
- `site` (employee's site)
- `certifications` (employee's certifications)
- `skills` (family or job type within the project)

#### 3. Event Handling Model

##### 3.1 Event Subscription

The service subscribes to a Service Bus topic to receive events. Each event contains the following data:

- `event_name`: Name of the event.
- `operator_id`: Unique ID of the operator.
- `skills`: List of skills for the operator.

##### 3.2 Event Processing

Upon receiving an event, the service:

1. Parses the event data.
2. Validates the data.
3. Stores the data in the database.

#### 4. Database Schema

##### 4.1 Operator Skills Table

| Column Name      | Data Type | Description                           |
| ---------------- | --------- | ------------------------------------- |
| `employee_id`    | STRING    | Unique employee ID                    |
| `site`           | STRING    | Employee's site                       |
| `certifications` | STRING    | Employee's certifications             |
| `skills`         | STRING    | Family or job type within the project |

- **Clustered Index on `site`** → Because queries often filter by site.
- **Non-Clustered Index on `employee_id`** → Because queries often look up employees by ID.

#### Example SQL schema:

```sql
CREATE TABLE OperatorSkills (
    employee_id VARCHAR(50) PRIMARY KEY,
    site VARCHAR(50),
    certifications VARCHAR(255),
    skills VARCHAR(255)
);

CREATE INDEX idx_site ON OperatorSkills(site);
CREATE INDEX idx_employee_id ON OperatorSkills(employee_id);
```

#### 5. API Endpoints

##### 5.1 `POST /operator-skills`

Endpoint to receive operator skills data.

#### Body:

```json
{
  "event_name": "OperatorSkillsUpdated",
  "operator_id": "operator-123",
  "site": "siteA",
  "certifications": "cert1, cert2",
  "skills": "skill1, skill2"
}
```

#### Behavior:

1. Parses the event data.
2. Validates the data.
3. Stores the data in the database.

#### 6. Implementation Notes

- **Service Bus SDK**: Use the official Azure Service Bus SDK to subscribe to the topic and receive events.
- **Database Access**: Use an ORM (e.g., Entity Framework) or direct SQL queries to interact with the database.
- **Error Handling**: Implement robust error handling to manage invalid data and connection issues.
- **Logging**: Use a logging framework to log important events and errors.

#### 7. Roles & Permissions

##### 7.1 Role Definitions

**Admin:**

- Can view and manage all operator skills data.
- Can configure the service (e.g., change Service Bus topic subscription).

**User:**

- Can view operator skills data.
- Cannot modify or configure the service.

##### 7.2 Permission Matrix

| Operation              | Admin | User |
| ---------------------- | ----- | ---- |
| View Operator Skills   | Yes   | Yes  |
| Manage Operator Skills | Yes   | No   |
| Configure Service      | Yes   | No   |

#### 8. Use Case Scenarios

##### 8.1 Receive and Store Operator Skills

```mermaid
sequenceDiagram
    participant ServiceBus
    participant MS3
    participant Database

    ServiceBus->>MS3: Send OperatorSkillsUpdated Event
    MS3->>MS3: Parse and Validate Event
    MS3->>Database: Store Operator Skills Data
    MS3->>ServiceBus: Acknowledge Event
```

##### 8.2 Query Operator Skills by Site

```mermaid
sequenceDiagram
    participant User
    participant MS3
    participant Database

    User->>MS3: GET /operator-skills?site=siteA
    MS3->>Database: Query Operator Skills by Site
    Database->>MS3: Return Operator Skills Data
    MS3->>User: Return Operator Skills Data
```

#### 9. Summary

The Operator Skills microservice (MS3) is designed to receive operator skills data from a Service Bus topic and store it in a database. The service ensures efficient querying of operator skills by employee ID and site, supporting the overall goal of maintaining detailed operator skill and certification data.

This design supports scalable event handling, robust data storage, and efficient querying, ensuring that operator skills data is always up-to-date and easily accessible.

## 4. Integration Patterns

### 4.1 Event-Driven Communication

Services publish domain events to Azure Service Bus topics when significant state changes occur. Other services subscribe to relevant events and react accordingly.

```mermaid
sequenceDiagram
    participant OM as Operator Management
    participant Bus as Azure Service Bus
    participant EA as Employee Assignment
    participant OS as Operator Skills

    OM->>Bus: Publish OperatorStatusChanged
    Bus->>EA: Notify of status change
    EA->>EA: Update assignments
    Bus->>OS: Notify of status change
    OS->>OS: Check skill implications
```

### 4.2 Synchronous API Calls

For immediate data needs, services make direct HTTP calls to other services' APIs.

### 4.3 Database Integration

For complex reporting needs, services may share read-only database views or use Cosmos DB Change Feed for real-time updates.

## 5. Conclusion

This document provides a comprehensive overview of the Crew Management microservices, including their architecture, core components, integration patterns, and conclusion.

## 4. Module 1: Administrative Document Management

## 5. Module 2: Absenteeism Management

## 5.1 Overview

The Absenteeism Management module is a comprehensive system within the Connected Workers platform designed to efficiently manage workforce availability and skill requirements in manufacturing environments, particularly focused on automobile cabling/wiring production lines. This integrated solution addresses the complex challenges of maintaining operational efficiency while handling employee absences and ensuring proper skill coverage across all workstations.

The module operates through six interconnected components that work together to provide a complete workforce management solution:

1. **Skills Matrix**: Serves as the foundation for defining and managing workstation-specific skills across different project phases (Prototype, Pre-Series, Series). It involves multiple departments:

   - Manufacturing Engineering (ME) for workstation and skill requirement definition
   - Training Department for maintaining the Skills Master Table
   - Quality Department for managing workstation criticality levels

2. **Training Process**: Orchestrates operator progression through various training phases, utilizing event-driven patterns to maintain synchronization with other modules while operating independently.

3. **Document Validation**: Manages evaluation templates and scoring systems for consistent assessment of operator skills during validation, certification, and performance evaluation processes.

4. **Versatility Matrix**: Provides real-time visualization and management of operator qualifications and workstation proficiency, enabling:

   - Live tracking of operator qualification status
   - Team Leader updates for polyvalence levels
   - Workstation coverage calculations based on criticality
   - Comprehensive filtering and reporting capabilities

5. **Replacement Management**: Implements structured workflows for handling operator absences through:

   - Multi-level severity-based approach
   - Skills-based recommendation system
   - Real-time notifications
   - Role-specific interfaces for Team Leaders and Shift Leaders

6. **Nursing Management**: Centralizes health-related data and processes, including:
   - Illness period tracking
   - Work accident management
   - Pregnancy accommodations
   - Work capability assessments
   - Regional compliance management

The module employs modern architectural patterns including microservices, CQRS, and event-driven design, built on Azure cloud services with Cosmos DB for data storage and Service Bus for messaging. This architecture ensures scalability, maintainability, and efficient cross-component communication while maintaining data privacy and regional compliance requirements.

## 5.2 Core components

## 5.2.1 Skills Matrix

### 1. Overview

The Skills Matrix module is a critical component of the broader Absenteeism and Replacement Management capability within the Connected Workers platform. Its primary purpose is to define, manage, and communicate the specific skills required to operate each workstation within a manufacturing environment, particularly tailored for automobile cabling/wiring production lines.

The module addresses the need for a dynamic and reliable system to track skill requirements across the lifecycle of a project (Prototype, Pre-Series, Series). It provides distinct functionalities managed by different departments:

- **Manufacturing Engineering (ME)**: Defines workstations, identifies necessary skills (Basic, Key, Specific/Non-Critical), links skills to workstations, and manages initial setup.
- **Training Department**: Manages the central **Skills Master Table**, defining all available skills, their codes, descriptions, and categories. This table functions on an overwrite basis without version control within CW.
- **Quality Department**: Manages the **Work Center Criticality Table**, assigning criticality levels (C, M, N) to workstations.

Data can be sourced initially or updated via imports (e.g., Excel sheets potentially originating from MES/PLM systems like Apriso, as detailed in the FDS), but the canonical definitions within CW reside in the Skills Master and Work Center Criticality tables.

The Skills Matrix serves as a foundational data source for other modules, notably the **Versatility Matrix** (tracking operator qualifications against these requirements) and the **Training Process** (defining training needs based on required skills). It ensures consistency in skill definitions and requirements across the platform, supporting efficient operations, training planning, and workforce management.

### 2. Architecture

#### 2.1 Architectural Patterns

The Skills Matrix module implements several key architectural patterns to ensure scalability, maintainability, and performance:

1. **Command Query Responsibility Segregation (CQRS)** - Separates write operations (commands) from read operations (queries) with distinct models, enabling optimized performance for each concern.
2. **Event-Driven Architecture** - Leverages asynchronous event communication for inter-service coordination, ensuring loose coupling and resilience.
3. **Microservices Architecture** - Decomposes the Skills Matrix functionality into discrete, independently deployable services with specific responsibilities.
4. **Change Feed Pattern** - Utilizes Cosmos DB's change feed to maintain consistency between write and read models, enabling efficient data synchronization.
5. **Domain-Driven Design** - Organizes functionality around business domains and implements a bounded context for the Skills Matrix module.

#### 2.2 High-Level Component Diagram

The Skills Matrix module consists of several key components that work together to implement the CQRS pattern and event-driven architecture:

```mermaid
flowchart TD
    User((User)) --> SCAPI[Skills Command API]
    User --> SQAPI[Skills Query API]

    subgraph Write Path
        SCAPI --> VALIDATE[Validation Layer]
        VALIDATE --> BLogic[Business Logic]
        BLogic --> CWRepo[Cosmos Write Repository]
        CWRepo --> WDB[(Cosmos DB\nWrite Container)]
        BLogic --> PUBLISHER[Event Publisher]
        PUBLISHER --> SB[(Azure Service Bus)]
    end

    subgraph Read Path
        SQAPI --> QHandlers[Query Handlers]
        QHandlers --> CRRepo[Cosmos Read Repository]
        CRRepo --> RDB[(Cosmos DB\nRead Containers)]
    end

    subgraph Synchronization
        CF[Change Feed Processor] --> WDB
        CF --> RDB
        CF --> SB
    end

    subgraph Event Consumers
        SB --> Sync[Sync Service]
        SB --> Notif[Notification Service]
        SB --> VM[Versatility Matrix]
        SB --> TP[Training Process]
    end

    classDef apiNodes fill:#90CAF9,stroke:#0D47A1,color:black
    classDef dbNodes fill:#AED581,stroke:#33691E,color:black
    classDef eventNodes fill:#FFD54F,stroke:#FF6F00,color:black
    classDef processorNodes fill:#CE93D8,stroke:#6A1B9A,color:black

    class SCAPI,SQAPI apiNodes
    class WDB,RDB dbNodes
    class SB,PUBLISHER eventNodes
    class CF,Sync,Notif,VM,TP processorNodes
```

This diagram illustrates the key components of the Skills Matrix module:

1. **Command Path (Write Operations)**:

   - **Skills Command API**: Exposes REST endpoints for write operations
   - **Validation Layer**: Validates incoming commands against business rules
   - **Business Logic**: Implements core domain logic
   - **Cosmos Write Repository**: Interacts with Cosmos DB write container
   - **Event Publisher**: Publishes domain events to Azure Service Bus

2. **Query Path (Read Operations)**:

   - **Skills Query API**: Exposes REST endpoints for read operations
   - **Query Handlers**: Processes read requests with optimized models
   - **Cosmos Read Repository**: Retrieves data from read-optimized containers

3. **Synchronization**:

   - **Change Feed Processor**: Detects changes in write container and synchronizes read models
   - Publishes events to Service Bus when relevant changes occur

4. **Event Consumers**:
   - **Sync Service**: Synchronizes data between write and read models
   - **Notification Service**: Delivers notifications to stakeholders
   - **Versatility Matrix**: Consumes skill changes to update operator qualifications
   - **Training Process**: Updates training requirements based on skill changes

#### 2.3 CQRS Implementation

The Command Query Responsibility Segregation (CQRS) pattern is a fundamental architectural principle in the Skills Matrix module, separating write and read operations to optimize for their different requirements.

```mermaid
flowchart LR
    subgraph "Command Side (Write Operations)"
        direction TB
        CMD[Commands]
        VLD[Validation]
        BL[Business Logic]
        WR[Write Repository]
        DB1[(Write Container)]
        EP[Event Publisher]

        CMD --> VLD
        VLD --> BL
        BL --> WR
        WR --> DB1
        BL --> EP

        classDef cmd fill:#FF8A65,stroke:#D84315,color:black
        class CMD,VLD,BL,WR,EP cmd
    end

    subgraph "Query Side (Read Operations)"
        direction TB
        QRY[Queries]
        QH[Query Handlers]
        RR[Read Repository]
        DB2[(Read Containers)]

        QRY --> QH
        QH --> RR
        RR --> DB2

        classDef qry fill:#81D4FA,stroke:#0288D1,color:black
        class QRY,QH,RR qry
    end

    subgraph "Synchronization"
        direction TB
        CF[Change Feed]
        TF[Model Transformer]
        UP[Read Model Updater]

        DB1 --> CF
        CF --> TF
        TF --> UP
        UP --> DB2

        classDef sync fill:#CE93D8,stroke:#8E24AA,color:black
        class CF,TF,UP sync
    end

    EP --> SB[(Service Bus)]
    SB --> ES[Event Subscribers]
    ES --> UP
```

##### 1. Command Side (Write Operations)

The command side is responsible for processing all data modifications:

- **Commands**: Represent intentions to change the system state (e.g., CreateSkill, UpdateWorkcenter)
- **Validation**: Validates commands against business rules before execution
- **Business Logic**: Implements domain logic and ensures data consistency
- **Write Repository**: Persists data in the normalized write model
- **Event Publisher**: Publishes domain events for successful operations

The write model is optimized for consistency and atomic updates, with normalized data structures that prevent duplication and maintain referential integrity.

##### 2. Query Side (Read Operations)

The query side is dedicated to efficient data retrieval:

- **Queries**: Represent requests for information without state changes
- **Query Handlers**: Process specific query types with optimized data access patterns
- **Read Repository**: Retrieves data from read-optimized models
- **Read Models**: Denormalized views designed for specific query scenarios

Read models are denormalized and structured for query performance, often containing pre-computed data that would otherwise require complex joins or aggregations.

##### 3. Synchronization

The synchronization process maintains consistency between write and read models:

- **Change Feed**: Detects changes in the write container
- **Model Transformer**: Converts write models to appropriate read model formats
- **Read Model Updater**: Updates read models with transformed data

This design provides several benefits:

- **Optimized Performance**: Each side is tuned for its specific operations
- **Scalability**: Read and write workloads can scale independently
- **Flexibility**: Read models can evolve without impacting the write model
- **Resilience**: Read operations remain available even during write-side issues

### 2.4 Change Feed and Synchronization

The Skills Matrix module leverages Azure Cosmos DB's change feed to implement data synchronization between write and read models, ensuring eventual consistency in the CQRS pattern.

```mermaid
flowchart TD
    WC[(Cosmos DB\nWrite Container)] --> |Changes| CF[Change Feed]

    subgraph "Change Feed Processors"
        CF --> SCFP[Skill Change Feed Processor]
        CF --> WCFP[Workcenter Change Feed Processor]
        CF --> MSFP[Mapping Change Feed Processor]
    end

    SCFP --> |Transform| SRM[Skill Read Model]
    WCFP --> |Transform| WRM[Workcenter Read Model]
    MSFP --> |Update Skill Relations| SRM
    MSFP --> |Update Workcenter Relations| WRM
    MSFP --> |Aggregate| CLRM[ChildLine Read Model]

    SRM --> RC1[(Cosmos DB\nSkills Read Container)]
    WRM --> RC2[(Cosmos DB\nWorkcenters Read Container)]
    CLRM --> RC3[(Cosmos DB\nChildLines Read Container)]

    subgraph "Leasing & Checkpointing"
        LC[(Lease Container)]
        SCFP <--> |Track Progress| LC
        WCFP <--> |Track Progress| LC
        MSFP <--> |Track Progress| LC
    end

    classDef container fill:#AED581,stroke:#33691E,color:black
    classDef processor fill:#CE93D8,stroke:#6A1B9A,color:black
    classDef model fill:#FFCC80,stroke:#E65100,color:black

    class WC,RC1,RC2,RC3,LC container
    class CF,SCFP,WCFP,MSFP processor
    class SRM,WRM,CLRM model
```

##### 1. Change Feed Processor

The change feed processor is the core component responsible for:

- **Monitoring Changes**: Continuously polls the write container for document changes (creates, updates, deletes)
- **Batched Processing**: Processes changes in configurable batches (typically 100 items) for efficiency
- **Idempotent Handling**: Ensures the same change is not applied multiple times to read models
- **Distributed Processing**: Uses lease mechanism to enable processing scale-out across multiple instances

##### 2. Synchronization Flow

When changes occur in the write container, the following process takes place:

1. **Change Detection**: The change feed processor identifies modified documents
2. **Document Categorization**: Each document is categorized based on its type (Skill, Workcenter, or Mapping)
3. **Model Transformation**: The write model is transformed to the appropriate read model format(s)
   - For skills: Updates the denormalized skill read model
   - For workcenters: Updates the workcenter read model
   - For mappings: Updates both skill and workcenter read models to maintain relationship integrity
4. **Read Model Update**: The transformed document is upserted into the appropriate read container(s)
5. **Event Publishing** (optional): Domain events are published to Service Bus for cross-service communication

##### 3. Deduplication Strategy

To prevent duplicate processing, the system implements:

- **Event ID Tracking**: Each event includes a unique event ID (UUID)
- **Processing History**: Services maintain a record of processed event IDs (with TTL-based expiration)
- **Idempotency Check**: Before processing, services verify if the event ID has been processed before
- **Safe Retry**: If an event was received but processing failed, safe retry logic allows reprocessing

##### 4. Resilience Mechanisms

The change feed architecture includes several resilience features:

- **Checkpoint-Based Progress Tracking**: Maintains progress via lease documents in a dedicated container
- **Automatic Retries**: Implements exponential backoff for transient failures
- **Partition Distribution**: Distributes processing across multiple instances for scalability
- **Monitor and Alert**: Provides metrics for processing lag and error rates
- **Circuit Breaking**: Prevents cascading failures by breaking circuits when downstream systems fail

Each change feed processor instance can process multiple partitions, and multiple instances can work together to process all partitions in the source container. The lease container manages coordination between processors, ensuring each change is processed exactly once even during scale-out operations.

### 2.5 Microservices Breakdown

The Skills Matrix functionality is decomposed into the following microservices:

1. **SkillsMatrix.CommandAPI**:

   - Responsible for handling all write operations
   - Exposes REST endpoints for creating/updating Skills, Workcenters, and their relationships
   - Implements validation logic and business rules
   - Writes to the source-of-truth write container
   - Publishes events for successful operations

2. **SkillsMatrix.QueryAPI**:

   - Handles all read operations from optimized read models
   - Supports various query patterns (by skill type, by workcenter, etc.)
   - Provides view-specific endpoints for UI consumption
   - Read-only access to data

3. **SkillsMatrix.SyncService**:

   - Hosts change feed processors
   - Manages synchronization between write and read models
   - Handles event processing and publishing
   - Implements the deduplication logic
   - Manages resilience and retry policies

4. **SkillsMatrix.NotificationService**:
   - Subscribes to relevant events
   - Generates and sends notifications to Team Leaders, Trainers, etc.
   - Maintains notification preferences and delivery tracking

### 3. Detailed Component Design

#### 3.1 SkillsMatrix.CommandAPI

**Responsibility**: Handles all write operations and maintains the source of truth for Skills Matrix data.

**Key Components**:

- **Controllers**: REST API endpoints for commands
- **Command Handlers**: Process commands and apply business logic
- **Validators**: Validate incoming commands against business rules
- **Domain Models**: Represent the core business entities
- **Repository**: Interacts with Cosmos DB write container
- **Event Publishers**: Publish domain events to Service Bus

**Main Endpoints**:

- `/api/skills` - Manage Skills Master data
- `/api/workcenters` - Manage Work Center data
- `/api/skill-workstation-mappings` - Manage relationships between skills and workstations

#### 3.2 SkillsMatrix.QueryAPI

**Responsibility**: Provides optimized read access to Skills Matrix data.

**Key Components**:

- **Controllers**: REST API endpoints for queries
- **Query Handlers**: Process queries and retrieve data
- **View Models**: Represent data optimized for specific query scenarios
- **Repository**: Interacts with Cosmos DB read containers
- **Caching Layer**: Improves performance for frequently accessed data

**Main Endpoints**:

- `/api/skills` - Query Skills Master data
- `/api/workcenters` - Query Work Center data
- `/api/workcenters/{id}/required-skills` - Get skills required for specific workcenters
- `/api/skills/{id}/workcenters` - Get workcenters requiring a specific skill

#### 3.3 SkillsMatrix.SyncService

**Responsibility**: Maintains consistency between write and read models.

**Key Components**:

- **Change Feed Processors**: Monitor write containers for changes
- **Model Transformers**: Convert between write and read models
- **Event Processors**: Process and publish domain events
- **Lease Management**: Track change feed processing progress
- **Retry Policies**: Handle transient failures

#### 3.4 SkillsMatrix.NotificationService

**Responsibility**: Manages notification workflows.

**Key Components**:

- **Event Subscribers**: Listen for domain events
- **Notification Templates**: Define message formats
- **Notification Dispatcher**: Send notifications via appropriate channels
- **Delivery Tracking**: Record notification statuses

### 4. Data Models

The Skills Matrix module implements CQRS with distinct write and read models. The write models are normalized for consistency and integrity, while read models are denormalized for query performance.

#### 4.0 Entity Relationship Diagram

The following diagram illustrates the relationships between key entities in the Skills Matrix module:

```mermaid
erDiagram
    Skill ||--o{ SkillWorkcenterMapping : "is required for"
    Workcenter ||--o{ SkillWorkcenterMapping : "requires"

    Skill {
        string id PK
        string skillId
        string skillDescription
        string shortDescription
        string skillCriticality
        array complianceRequirements
    }

    Workcenter {
        string id PK
        string childLine
        string workcenterId
        string workstationName
        string description
        int numberOfOperators
        string workstationCriticality
    }

    SkillWorkcenterMapping {
        string id PK
        string skillId FK
        string workcenterId FK
        string projectPhase
    }

    SkillReadModel {
        string id PK
        string skillId
        string skillDescription
        string shortDescription
        string skillCriticality
        array complianceRequirements
        array workcenters
    }

    WorkcenterReadModel {
        string id PK
        string childLine
        string workcenterId
        string workstationName
        string description
        int numberOfOperators
        string workstationCriticality
        array requiredSkills
    }

    ChildLineSkillsReadModel {
        string id PK
        string childLine
        array workcenters
    }
```

#### 4.1 Write Models

These models represent the source of truth for the Skills Matrix data.

##### Skill Entity

```json
{
  "id": "string", // Unique identifier (e.g., "S1")
  "type": "Skill", // Entity type discriminator
  "partitionKey": "Skill", // Partition key for Cosmos DB
  "skillId": "string", // Business identifier (e.g., "S1")
  "skillDescription": "string", // Full description (e.g., "ULTRASONIC SPLICE")
  "shortDescription": "string", // Code/abbreviation (e.g., "USW")
  "skillCriticality": "string", // "Basic Skills", "Critical Skills", "Specific/Non-Critical"
  "complianceRequirements": [
    // Associated standards (optional)
    {
      "standardName": "string", // e.g., "IPC/WHMA-A-620"
      "description": "string" // Details of the compliance requirement
    }
  ],
  "createdBy": "string", // User who created the record
  "createdDate": "string", // ISO 8601 date format
  "lastModifiedBy": "string", // User who last modified the record
  "lastModifiedDate": "string", // ISO 8601 date format
  "isDeleted": false, // Soft delete flag
  "etag": "string" // For optimistic concurrency
}
```

##### Workcenter Entity

```json
{
  "id": "string", // Unique identifier
  "type": "Workcenter", // Entity type discriminator
  "partitionKey": "Workcenter", // Partition key for Cosmos DB
  "childLine": "string", // Production line identifier (e.g., "200")
  "workcenterId": "string", // Business identifier (e.g., "200_SOP")
  "description": "string", // Description of the workcenter function
  "workstationName": "string", // User-friendly name (e.g., "Crimping Station")
  "numberOfOperators": 1, // Required operators for this workstation
  "workstationCriticality": "string", // "C" (Critical), "M" (Medium), "N" (Normal)
  "createdBy": "string", // User who created the record
  "createdDate": "string", // ISO 8601 date format
  "lastModifiedBy": "string", // User who last modified the record
  "lastModifiedDate": "string", // ISO 8601 date format
  "isDeleted": false, // Soft delete flag
  "etag": "string" // For optimistic concurrency
}
```

##### Skill-Workcenter Mapping Entity

```json
{
  "id": "string", // Unique identifier
  "type": "SkillWorkcenterMapping", // Entity type discriminator
  "partitionKey": "SkillWorkcenterMapping", // Partition key for Cosmos DB
  "skillId": "string", // Reference to Skill
  "workcenterId": "string", // Reference to Workcenter
  "projectPhase": "string", // "Prototype", "Pre-Series", or "Series"
  "createdBy": "string", // User who created the record
  "createdDate": "string", // ISO 8601 date format
  "lastModifiedBy": "string", // User who last modified the record
  "lastModifiedDate": "string", // ISO 8601 date format
  "isDeleted": false, // Soft delete flag
  "etag": "string" // For optimistic concurrency
}
```

#### 4.2 Read Models

These models are optimized for various query scenarios.

##### SkillReadModel

```json
{
  "id": "string", // Same as write model id
  "type": "SkillReadModel", // Entity type discriminator
  "partitionKey": "Skill", // Partition key for Cosmos DB
  "skillId": "string", // Business identifier
  "skillDescription": "string", // Full description
  "shortDescription": "string", // Code/abbreviation
  "skillCriticality": "string", // Criticality category
  "complianceRequirements": [
    // Associated standards (if any)
    {
      "standardName": "string",
      "description": "string"
    }
  ],
  "workcenters": [
    // Denormalized list of associated workcenters
    {
      "workcenterId": "string",
      "workstationName": "string",
      "childLine": "string",
      "projectPhase": "string"
    }
  ],
  "lastModifiedDate": "string" // For tracking freshness
}
```

##### WorkcenterReadModel

```json
{
  "id": "string", // Same as write model id
  "type": "WorkcenterReadModel", // Entity type discriminator
  "partitionKey": "Workcenter", // Partition key for Cosmos DB
  "childLine": "string", // Production line identifier
  "workcenterId": "string", // Business identifier
  "workstationName": "string", // User-friendly name
  "description": "string", // Description of workcenter function
  "numberOfOperators": 1, // Required operators
  "workstationCriticality": "string", // Criticality level
  "requiredSkills": [
    // Denormalized list of required skills
    {
      "skillId": "string",
      "skillDescription": "string",
      "shortDescription": "string",
      "skillCriticality": "string",
      "projectPhase": "string" // Phase when this skill is required
    }
  ],
  "lastModifiedDate": "string" // For tracking freshness
}
```

##### ChildLineSkillsReadModel

```json
{
  "id": "string", // Unique identifier (childLine value)
  "type": "ChildLineSkillsReadModel", // Entity type discriminator
  "partitionKey": "ChildLine", // Partition key for Cosmos DB
  "childLine": "string", // Production line identifier
  "workcenters": [
    // All workcenters in this childLine
    {
      "workcenterId": "string",
      "workstationName": "string",
      "workstationCriticality": "string",
      "numberOfOperators": 1,
      "requiredSkills": [
        // Skills required for this workcenter
        {
          "skillId": "string",
          "shortDescription": "string",
          "skillCriticality": "string"
        }
      ]
    }
  ],
  "lastModifiedDate": "string" // For tracking freshness
}
```

### 5. API Specification

### 5.1 Command API Endpoints

#### Create Skill

- **Endpoint**: `POST /api/skills`
- **Purpose**: Add a new skill to the Skills Master
- **Authorization**: Requires `skills:create` permission
- **Request Body**:
  ```json
  {
    "skillId": "S25",
    "skillDescription": "Terminal Crimping",
    "shortDescription": "CRMP",
    "skillCriticality": "Critical Skills",
    "complianceRequirements": [
      {
        "standardName": "IPC/WHMA-A-620",
        "description": "Class 3 High Performance Electronic Products"
      }
    ]
  }
  ```
- **Response**: 201 Created with the created resource

#### Update Skill

- **Endpoint**: `PUT /api/skills/{id}`
- **Purpose**: Update an existing skill
- **Authorization**: Requires `skills:update` permission
- **Request Body**: Same as Create with updated values
- **Response**: 200 OK with updated resource

#### Create Workcenter

- **Endpoint**: `POST /api/workcenters`
- **Purpose**: Add a new workcenter
- **Authorization**: Requires `workcenters:create` permission
- **Request Body**:
  ```json
  {
    "childLine": "200",
    "workcenterId": "200_P25",
    "workstationName": "Terminal Crimp Station 5",
    "description": "Crimping of terminals onto 18-22 AWG wires",
    "numberOfOperators": 2,
    "workstationCriticality": "C"
  }
  ```
- **Response**: 201 Created with the created resource

#### Map Skill to Workcenter

- **Endpoint**: `POST /api/skill-workstation-mappings`
- **Purpose**: Associate a skill with a workcenter
- **Authorization**: Requires `skillmapping:create` permission
- **Request Body**:
  ```json
  {
    "skillId": "S25",
    "workcenterId": "200_P25",
    "projectPhase": "Series"
  }
  ```
- **Response**: 201 Created with the created mapping

### 5.2 Query API Endpoints

#### Get All Skills

- **Endpoint**: `GET /api/skills`
- **Purpose**: Retrieve all skills with optional filtering
- **Authorization**: Requires `skills:read` permission
- **Query Parameters**:
  - `criticality`: Filter by skill criticality
  - `page`: Page number (default: 1)
  - `pageSize`: Items per page (default: 20)
- **Response**: 200 OK with paginated list of skills

#### Get Workcenter with Required Skills

- **Endpoint**: `GET /api/workcenters/{id}`
- **Purpose**: Get workcenter details including required skills
- **Authorization**: Requires `workcenters:read` permission
- **Query Parameters**:
  - `projectPhase`: Optional filter by project phase
- **Response**: 200 OK with workcenter and its skills

#### Get Skills for ChildLine

- **Endpoint**: `GET /api/childlines/{id}/skills`
- **Purpose**: Get all skills required for a production line
- **Authorization**: Requires `childlines:read` permission
- **Query Parameters**:
  - `projectPhase`: Optional filter by project phase
- **Response**: 200 OK with skills grouped by workcenter

### 6. Event Communication

The Skills Matrix module uses Azure Service Bus for reliable event-based communication.

#### 6.1 Event Schema

All events follow a common envelope format:

```json
{
  "id": "string", // Unique event identifier (UUID)
  "eventType": "string", // Type of event
  "eventTime": "string", // ISO 8601 timestamp
  "subject": "string", // Subject of the event (e.g., "Skill/S25")
  "dataVersion": "1.0", // Schema version
  "data": {
    // Event-specific payload
    // Varies by event type
  }
}
```

#### 6.2 Domain Events

#### SkillCreatedEvent

```json
{
  "id": "a0b1c2d3-e4f5-6789-abcd-ef0123456789",
  "eventType": "SkillCreated",
  "eventTime": "2023-05-15T14:30:00Z",
  "subject": "Skill/S25",
  "dataVersion": "1.0",
  "data": {
    "skillId": "S25",
    "skillDescription": "Terminal Crimping",
    "shortDescription": "CRMP",
    "skillCriticality": "Critical Skills",
    "complianceRequirements": [
      {
        "standardName": "IPC/WHMA-A-620",
        "description": "Class 3 High Performance Electronic Products"
      }
    ]
  }
}
```

#### SkillUpdatedEvent

```json
{
  "id": "b1c2d3e4-f5g6-7890-abcd-ef0123456789",
  "eventType": "SkillUpdated",
  "eventTime": "2023-05-16T09:45:00Z",
  "subject": "Skill/S25",
  "dataVersion": "1.0",
  "data": {
    "skillId": "S25",
    "skillDescription": "Terminal Crimping - Updated",
    "shortDescription": "CRMP",
    "skillCriticality": "Critical Skills",
    "complianceRequirements": [
      {
        "standardName": "IPC/WHMA-A-620",
        "description": "Class 3 High Performance Electronic Products"
      }
    ]
  }
}
```

#### WorkcenterCreatedEvent

```json
{
  "id": "c2d3e4f5-g6h7-8901-abcd-ef0123456789",
  "eventType": "WorkcenterCreated",
  "eventTime": "2023-05-17T11:15:00Z",
  "subject": "Workcenter/200_P25",
  "dataVersion": "1.0",
  "data": {
    "childLine": "200",
    "workcenterId": "200_P25",
    "workstationName": "Terminal Crimp Station 5",
    "description": "Crimping of terminals onto 18-22 AWG wires",
    "numberOfOperators": 2,
    "workstationCriticality": "C"
  }
}
```

#### SkillWorkcenterMappingCreatedEvent

```json
{
  "id": "d3e4f5g6-h7i8-9012-abcd-ef0123456789",
  "eventType": "SkillWorkcenterMappingCreated",
  "eventTime": "2023-05-18T13:30:00Z",
  "subject": "SkillWorkcenterMapping/S25_200_P25",
  "dataVersion": "1.0",
  "data": {
    "skillId": "S25",
    "workcenterId": "200_P25",
    "projectPhase": "Series"
  }
}
```

#### 6.3 Service Bus Topic Structure

The Skills Matrix module uses the following topics and subscriptions:

- **Topic**: `skillsmatrix-events`
  - **Subscription**: `sync-service` - Handles synchronization between write and read models
  - **Subscription**: `notification-service` - Processes events for notifications
  - **Subscription**: `versatility-matrix` - Updates the Versatility Matrix based on skill changes
  - **Subscription**: `training-process` - Updates training requirements based on skill changes

#### 6.4 Key Operations Sequence

The following sequence diagram illustrates the flow of a typical write operation (creating a new skill) through the system, showcasing the CQRS pattern and event-driven architecture:

```mermaid
sequenceDiagram
    participant Client
    participant CommandAPI as Skills Command API
    participant WriteRepo as Write Repository
    participant CDB as Cosmos DB Write Container
    participant EventPub as Event Publisher
    participant ASB as Azure Service Bus
    participant CFP as Change Feed Processor
    participant ReadRepo as Read Repository
    participant ReadDB as Cosmos DB Read Containers
    participant Subscribers as Event Subscribers

    Client->>CommandAPI: POST /api/skills
    CommandAPI->>CommandAPI: Validate Request
    CommandAPI->>WriteRepo: CreateSkill(skillData)
    WriteRepo->>CDB: Save Skill Document
    CDB-->>WriteRepo: Success
    WriteRepo-->>CommandAPI: Skill Created
    CommandAPI->>EventPub: Publish SkillCreatedEvent
    EventPub->>ASB: Send Event Message
    CommandAPI-->>Client: 201 Created Response

    Note over CDB,CFP: Asynchronous Processing

    CDB->>CFP: Change Feed Event
    CFP->>CFP: Transform Write Model to Read Model
    CFP->>ReadRepo: Update Read Models
    ReadRepo->>ReadDB: Upsert Read Documents

    ASB->>Subscribers: Process SkillCreatedEvent
    Subscribers->>Subscribers: Process Notifications
```

This diagram shows:

1. **Synchronous Path**:

   - Client submits a request to create a skill
   - Command API validates and processes the request
   - Write Repository persists the data in Cosmos DB
   - Event is published to Service Bus
   - Client receives success response

2. **Asynchronous Processing**:
   - Change Feed Processor detects the document change
   - Transforms the write model to appropriate read model(s)
   - Updates read containers with the new data
   - Event subscribers process the event and trigger notifications

This separation of synchronous and asynchronous processing is key to the CQRS implementation, allowing for immediate response to the client while background processes handle read model updates and downstream effects.

### 7. Azure Services Integration

The Skills Matrix module is deployed as a cloud-native solution on Azure, leveraging managed services for scalability, resilience, and operational efficiency.

#### 7.0 Azure Deployment Architecture

The following diagram illustrates how the Skills Matrix components are deployed on Azure:

```mermaid
flowchart TB
    subgraph "Azure App Service Plan"
        SCAPI[Skills Command API\nApp Service]
        SQAPI[Skills Query API\nApp Service]
        SSYNC[Skills Sync Service\nApp Service]
        SNOTIF[Skills Notification\nApp Service]
    end

    subgraph "Azure Cosmos DB Account"
        direction LR
        CDB[(Cosmos DB)]
        WC[Write Container]
        RC[Read Containers]
        LC[Lease Container]
        CDB --- WC
        CDB --- RC
        CDB --- LC
    end

    subgraph "Azure Service Bus"
        NS[Service Bus Namespace]
        T[skillsmatrix-events Topic]
        S1[sync-service Subscription]
        S2[notification-service Subscription]
        S3[versatility-matrix Subscription]
        S4[training-process Subscription]
        DLQ[Dead-Letter Queue]

        NS --- T
        T --- S1
        T --- S2
        T --- S3
        T --- S4
        S1 --- DLQ
        S2 --- DLQ
        S3 --- DLQ
        S4 --- DLQ
    end

    subgraph "Azure Key Vault"
        KV[Key Vault\nSecrets & Keys]
    end

    subgraph "Azure Monitor"
        AM[Application Insights]
        LOGS[Log Analytics]
        DASH[Dashboards]

        AM --- LOGS
        LOGS --- DASH
    end

    subgraph "Azure API Management"
        APIM[API Management]
    end

    SCAPI --> WC
    SCAPI --> T
    SSYNC --> WC
    SSYNC --> RC
    SSYNC --> LC
    SSYNC --> S1
    SQAPI --> RC
    SNOTIF --> S2

    APIM --> SCAPI
    APIM --> SQAPI

    SCAPI --> KV
    SQAPI --> KV
    SSYNC --> KV
    SNOTIF --> KV

    SCAPI --> AM
    SQAPI --> AM
    SSYNC --> AM
    SNOTIF --> AM

    classDef appService fill:#0078D7,stroke:#004E8C,color:white
    classDef cosmos fill:#3CA9F5,stroke:#0078D7,color:white
    classDef serviceBus fill:#8C4799,stroke:#5C2D62,color:white
    classDef keyVault fill:#4CAF50,stroke:#2E7D32,color:white
    classDef monitor fill:#FF8F00,stroke:#EF6C00,color:white
    classDef apim fill:#00BCD4,stroke:#00838F,color:white

    class SCAPI,SQAPI,SSYNC,SNOTIF appService
    class CDB,WC,RC,LC cosmos
    class NS,T,S1,S2,S3,S4,DLQ serviceBus
    class KV keyVault
    class AM,LOGS,DASH monitor
    class APIM apim
```

This architecture implements a cloud-native approach with:

- **Compute**: Azure App Services for hosting the microservices
- **Data Storage**: Azure Cosmos DB for document storage with separate containers for write and read models
- **Messaging**: Azure Service Bus for reliable, asynchronous event communication
- **Security**: Azure Key Vault for secret management and credential storage
- **Monitoring**: Application Insights and Log Analytics for telemetry and operational insights
- **API Gateway**: API Management for API exposure, documentation, and access control

#### 7.1 Cosmos DB Configuration

##### Containers and Partitioning

1. **Write Container**:

   - **Name**: `SkillsMatrixWrite`
   - **Partition Key**: `/partitionKey`
   - **Throughput**: 400 RU/s (autoscale enabled)
   - **Indexing**: Optimized for write operations

2. **Read Containers**:

   - **Name**: `SkillsMatrixReadSkills`
     - **Partition Key**: `/partitionKey`
     - **Throughput**: 400 RU/s (autoscale enabled)
     - **Indexing**: Optimized for skill queries
   - **Name**: `SkillsMatrixReadWorkcenters`
     - **Partition Key**: `/partitionKey`
     - **Throughput**: 400 RU/s (autoscale enabled)
     - **Indexing**: Optimized for workcenter queries
   - **Name**: `SkillsMatrixReadChildLines`
     - **Partition Key**: `/partitionKey`
     - **Throughput**: 400 RU/s (autoscale enabled)
     - **Indexing**: Optimized for production line queries

#### Change Feed Processors

1. **SkillChangeFeedProcessor**:

   - **Source**: Write container
   - **Target**: Skills read container
   - **Lease Container**: `SkillsMatrixLeases`
   - **Batch Size**: 100 items
   - **Polling Interval**: 5 seconds

2. **WorkcenterChangeFeedProcessor**:

   - **Source**: Write container
   - **Target**: Workcenters read container
   - **Lease Container**: `SkillsMatrixLeases`
   - **Batch Size**: 100 items
   - **Polling Interval**: 5 seconds

3. **SkillWorkcenterMappingChangeFeedProcessor**:
   - **Source**: Write container
   - **Target**: Updates both Skills and Workcenters read containers
   - **Lease Container**: `SkillsMatrixLeases`
   - **Batch Size**: 100 items
   - **Polling Interval**: 5 seconds

#### 7.2 Service Bus Configuration

#### Namespace and Topic

- **Namespace**: `connected-workers-servicebus`
- **Topic**: `skillsmatrix-events`
  - **Max Size**: 5 GB
  - **Message TTL**: 14 days
  - **Duplicate Detection**: Enabled, 10-minute window

#### Subscriptions

1. **sync-service**:

   - **Max Delivery Count**: 10
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 5 minutes

2. **notification-service**:

   - **Max Delivery Count**: 5
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 2 minutes

3. **versatility-matrix**:

   - **Max Delivery Count**: 8
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 3 minutes

4. **training-process**:
   - **Max Delivery Count**: 8
   - **Dead-Letter On Exception**: Enabled
   - **Session Support**: Disabled
   - **Lock Duration**: 3 minutes

### 8. Security and Permission Models

### 8.1 Authorization Model

The Skills Matrix module implements a resource-based permission model where:

- **Resources** are the entities in the system (skills, workcenters, mappings)
- **Actions** are operations that can be performed on resources (create, read, update, delete)
- **Permissions** are defined as `resource:action` pairs
- **Roles** are collections of permissions assigned to users

#### Resources

1. `skills` - Skills Master data
2. `workcenters` - Work Center data
3. `skillmapping` - Skill-to-Workcenter mappings
4. `childlines` - Production line data

#### Actions

1. `create` - Create a new resource
2. `read` - View resource data
3. `update` - Modify existing resource
4. `delete` - Remove a resource
5. `export` - Export resource data
6. `import` - Import resource data

#### Permissions

| Permission            | Description                      |
| --------------------- | -------------------------------- |
| `skills:create`       | Create new skills                |
| `skills:read`         | View skills data                 |
| `skills:update`       | Modify existing skills           |
| `skills:delete`       | Remove skills                    |
| `skills:export`       | Export skills data               |
| `skills:import`       | Import skills data               |
| `workcenters:create`  | Create new workcenters           |
| `workcenters:read`    | View workcenter data             |
| `workcenters:update`  | Modify existing workcenters      |
| `workcenters:delete`  | Remove workcenters               |
| `workcenters:export`  | Export workcenter data           |
| `workcenters:import`  | Import workcenter data           |
| `skillmapping:create` | Create skill-workcenter mappings |
| `skillmapping:read`   | View mapping data                |
| `skillmapping:update` | Modify existing mappings         |
| `skillmapping:delete` | Remove mappings                  |
| `childlines:read`     | View production line data        |
| `childlines:export`   | Export production line data      |

#### Roles

| Role                 | Description                         | Permissions                                                                                                                                                                                                     |
| -------------------- | ----------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **ME_Engineer**      | Manufacturing Engineering personnel | `skills:read`, `workcenters:create`, `workcenters:read`, `workcenters:update`, `skillmapping:create`, `skillmapping:read`, `skillmapping:update`, `skillmapping:delete`, `childlines:read`, `childlines:export` |
| **Training_Manager** | Training Department personnel       | `skills:create`, `skills:read`, `skills:update`, `skills:delete`, `skills:export`, `skills:import`, `workcenters:read`, `skillmapping:read`, `childlines:read`                                                  |
| **Quality_Engineer** | Quality Department personnel        | `workcenters:read`, `workcenters:update`, `skills:read`, `skillmapping:read`, `childlines:read`                                                                                                                 |
| **Team_Leader**      | Production Team Leaders             | `skills:read`, `workcenters:read`, `skillmapping:read`, `childlines:read`                                                                                                                                       |
| **HR_Manager**       | Human Resources personnel           | `skills:read`, `workcenters:read`, `skillmapping:read`, `childlines:read`, `childlines:export`                                                                                                                  |
| **System_Admin**     | IT system administrators            | All permissions                                                                                                                                                                                                 |

### 8.2 Security Implementation

1. **Authentication**:

   - Azure Active Directory (AAD) integration
   - JWT token-based authentication
   - Token validation middleware in API services

2. **Authorization**:

   - Permission checking middleware
   - Role-based access control (RBAC)
   - Claims-based authorization
   - Auditing of security-relevant events

3. **Data Protection**:
   - Azure Key Vault for secret management
   - Data encryption at rest and in transit
   - Appropriate retention policies
   - Data access auditing

### 9. Conclusion

The Skills Matrix module is designed as a robust, scalable system that leverages CQRS, event-driven architecture, and microservices principles to deliver a reliable and maintainable solution. Key design decisions include:

1. **Data Segregation**: Separate write and read models optimized for their respective purposes, improving performance and scalability.
2. **Eventual Consistency**: Change feed-based synchronization ensures read models reflect write model updates, with appropriate mechanisms for handling temporary inconsistencies.
3. **Loose Coupling**: Event-based communication enables independent development, deployment, and scaling of services.
4. **Resilience**: Comprehensive retry policies, error handling, and monitoring ensure system reliability even during partial failures.
5. **Security**: Fine-grained permissions and role-based access control protect data while enabling appropriate access.
6. **Performance**: Optimized data models, efficient query patterns, and appropriate scaling of Azure resources deliver responsive user experiences.

This architecture balances complexity with pragmatism, introducing the benefits of CQRS and event-driven design without unnecessary abstraction. The system is designed to be:

- **Maintainable**: Clear separation of concerns and modular components
- **Extensible**: Easy to add new features or modify existing functionality
- **Observable**: Comprehensive logging and monitoring
- **Scalable**: Ability to handle growing data and user loads
- **Reliable**: Resilient to partial failures and transient issues

## 5.2.2 Training Process

### 1. Overview

The Training Process microservice's primary responsibility is to orchestrate and track an operator's progression through training phases based on manual triggers. It integrates with other modules via Change Feed (consuming data) and event-driven patterns (publishing status, triggering workflows, receiving results), avoiding direct synchronous coupling where possible. It maintains a local, duplicated copy of necessary data from external sources (like operator details) for efficient operation.

**In Scope:**

- Recording and tracking operator training phase transitions (O, LC, V, C, R, F)
- Processing manually initiated training phase actions from Trainers and Team Leaders
- Consuming operator data from Crew Management via Change Feed and maintaining a local copy.
- Consuming process definitions from Skills Matrix via Change Feed and maintaining a local copy.
- Consuming data from Versatility Matrix via Change Feed for read-only purposes (e.g., enriching UI views). (_Specific data fields need confirmation_).
- Integrating with Validation Submodule for form templates and scoring
- **Managing internal approval workflows for certification, recertification, qualification removal, and IPE.**
- Publishing status updates (`TrainingStatusChanged` event) to Versatility Matrix
- **Publishing notification events to a central Notification Service for workflow steps (e.g., pending approvals).**

**Out of Scope:**

- Operator onboarding (handled by Crew Management)
- Skills/process definition management (handled by Skills Matrix)
- Form rendering and scoring logic (handled by Validation Submodule)
- ~~Workflow execution and approvals
- Versatility Matrix maintenance, calculations, and acting as the source of truth for workstation assignments.
- Automatic timelines, escalations, or retries
- Notification delivery (handled by central Notification Service)

---

### 2. Architecture

#### 2.1 Architectural Patterns

The Training Process microservice implements the following architectural patterns:

1. **Microservices Architecture**: Self-contained service focused on training process domain
2. **Event-Driven Architecture**: Async communication via Azure Service Bus
3. **CQRS Pattern**: Separation of command (write) and query (read) responsibilities
4. **Domain-Driven Design**: Focus on training domain concepts and bounded contexts
5. **Change Feed Pattern**: For integration with external data sources
6. **Event Sourcing** (partial): Training history as a sequence of events
7. **RESTful API**: For command and query operations

#### 2.2 High-Level Component Diagram

```mermaid
graph TD
    Client[Client Applications] -- API Calls / Approval Actions --> APIGw[Azure API Management]
    APIGw --> APILyr[API Layer]

    subgraph Training Process Microservice
        APILyr -- Commands / Approval Actions --> CMD[Command Handlers]
        APILyr -- Queries --> QRY[Query Handlers]

        CMD --> Domain[Domain Model / Internal Workflows]
        Domain --> REPO[Write Repository]

        QRY --> ReadRepo[Read Model Repository]

        REPO --> CosmosWrite[(Cosmos DB Write Model)]
        ReadRepo --> CosmosRead[(Cosmos DB Read Model)]

        Domain -- Domain Events / Notification Requests --> EVTPUB[Event Publisher]
        EVTPUB --> ServiceBusPub[Azure Service Bus - Publish]

        CFP[Change Feed Processors] -- Update Commands --> CMD
    end

    CrewMgtDB[(Crew Management DB)] -- Operator Data (Change Feed) --> CFP
    SkillsMatrixDB[(Skills Matrix DB)] -- Process Definitions (Change Feed) --> CFP
    VersatilityMatrixDB[(Versatility Matrix DB)] -- Versatility Data (Change Feed) --> CFP

    ServiceBusPub -- Training Status Events --> VersatilityMatrix[Versatility Matrix]
    ServiceBusPub -- Notification Events --> NotificationSvc[Notification Service]

    ValidationAPI[Validation Service API] <--> APILyr
```

#### 2.3 CQRS Implementation

The CQRS pattern separates the command and query responsibilities:

**Command Side (Write Model):**

- Handles all state-changing operations
- Rich domain model with business logic
- Optimized for consistency and correctness
- Publishes events upon successful state changes
- Uses a normalized data model in Cosmos DB

**Query Side (Read Model):**

- Handles all read operations
- Lightweight query handlers
- Optimized for query performance
- Reads from denormalized view models
- No business logic

**Initial Implementation:**

- Both models will share the same Cosmos DB container
- Separate read model projections to optimize for query scenarios
- Future option to physically separate read/write stores if needed

#### 2.4 Microservices Breakdown

The Training Process domain is divided into multiple focused microservices following DDD principles and NestJS modular architecture. Each microservice has its own bounded context, database containers, and clearly defined integration points.

##### 2.4.1 Core Training Orchestration Service

**Primary Responsibility:** Manages the overall operator training lifecycle, orchestrates state transitions, **and handles internal multi-step approval workflows.**

**Cosmos DB Containers:**

- `training-records` (Main container)
  - Partition Key: `/partitionKey`
- `internal-workflows` (Optional: Can also be embedded in training-records)
  - Partition Key: `/partitionKey` (e.g., workflow instance ID or operator ID)

**Communication:**

- **Publishes Events:** Training status events, notification request events
- **Consumes Change Feed:** From Crew Management, Skills Matrix, Versatility Matrix
- **REST API:** Exposes training lifecycle management and **workflow action** endpoints

**Main Responsibilities:**

- Operator training progression (O → LC → V → C → R)
- Qualification addition/management
- Integration with validation results
- **Initiating and managing internal workflows for certification, recertification, removal, IPE.**
- **Processing approval steps from authorized users.**
- Publishing status updates to Versatility Matrix
- **Publishing notification requests.**
- Managing new qualification requests (F) and qualification removal processes
- Publishing qualification status changes to Versatility Matrix

##### 2.4.2 Training Data Synchronization Service

**Primary Responsibility:** Consumes change feeds from other modules and updates local records.

**Cosmos DB Containers:**

- Uses `training-records` for writing
- Leases containers for change feed processing:
  - `crew-management-lease`
  - `skills-matrix-lease`
  - `versatility-matrix-lease`

**Communication:**

- **Consumes Change Feed:** Primary integration method
- **Publishes Commands:** Internal commands to Core Training service

**Main Responsibilities:**

- Monitors external module changes via Change Feed
- Transforms external data to internal domain models
- Maintains data consistency across bounded contexts
- Updates cached/duplicated data in training records
- Handles specific operator data from different sources (OPTITIME, Workday for North Africa)
- Processes operator assignment changes from Crew Management

##### 2.4.3 Internal Workflow Service

**Primary Responsibility:** Handles the management and execution of internal approval workflows.

**Cosmos DB Containers:**

- `internal-workflows` (Optional separate container for workflow state)
  - Partition Key: `/partitionKey` (workflowId)
- Alternatively, uses `training-records` for embedding workflow state

**Communication:**

- **Publishes Events:** Notification events to the Notification Service
- **REST API:** Exposes workflow action endpoints (approvals, rejections)

**Main Responsibilities:**

- Managing internal approval workflows for certification, recertification, qualification removal, etc.
- Processing approval/rejection decisions
- Updating workflow state and qualification status based on approvals
- Sending notification requests via the Notification Service
- Maintaining workflow history and audit trail

##### 2.4.4 Training Query Service

**Primary Responsibility:** Handles read operations and provides optimized query capabilities.

**Cosmos DB Containers:**

- `training-read-models` (Read model container)
  - Contains denormalized projections for different query scenarios
  - Optimized for query performance

**Communication:**

- **REST API:** Exposes query endpoints
- **Consumes Events:** From Core Training service for read model updates
- **Read Models:** Maintains denormalized projections

**Main Responsibilities:**

- Provides efficient read operations
- Maintains denormalized read models
- Offers various query options (by operator, department, status)
- Optimizes data for UI consumption

---

### 3. Detailed Component Design

#### 3.1 API Layer

The API layer provides RESTful endpoints for commands and queries:

- **Command Endpoints**: Accept requests that change system state
- **Query Endpoints**: Return data without side effects
- **Authentication**: Azure AD integration
- **Authorization**: Role-based authorization (Trainer, Team Leader, etc.)
- **Input Validation**: FluentValidation for DTO validation
- **Response Formatting**: Consistent REST conventions
- **Error Handling**: Problem Details standard (RFC 7807)

**Implementation Notes:**

- Commands/queries dispatched via MediatR
- Swagger/OpenAPI documentation
- Correlation IDs for request tracing

#### 3.2 Command Processing

The command processing flow:

1. API layer receives command request
2. Command is validated
3. MediatR dispatches command to appropriate handler
4. Handler loads domain entity/aggregate from repository
5. Domain logic is executed
6. Changes are persisted to Cosmos DB
7. Domain events are published to Service Bus
8. Response is returned to client

**Key Command Handlers:**

- `CompleteOjtTrainingHandler`
- `CompleteLearningCurveHandler`
- `InitiateValidationHandler`
- `ProcessValidationResultHandler` (Handles result from Validation Submodule callback/event)
- `InitiateCertificationHandler` // Initiates the internal certification workflow
- `InitiateRecertificationHandler` // Initiates the internal recertification workflow
- `InitiateNewQualificationHandler`
- `CompleteNewQualificationTrainingHandler`
- `InitiateQualificationRemovalHandler` // Initiates the internal removal workflow
- `InitiateIpeHandler` // Initiates the internal IPE workflow
- `ApproveWorkflowStepHandler` // Processes approval/rejection actions from users
- `SyncOperatorDataHandler` (Processes data from Crew Management Change Feed to update local record)
- `SyncProcessDefinitionHandler` (Processes data from Skills Matrix Change Feed to update local process info)
- `SyncVersatilityDataHandler` (Processes data from Versatility Matrix Change Feed to update local read model/cache - _Specific logic TBD_)

#### 3.3 Domain Model

The core domain model revolves around the `OperatorTrainingRecord` aggregate. This aggregate maintains the state related to an operator's training journey, **including the status of any ongoing internal approval workflows.** It includes locally cached (duplicated) operator details from Crew Management for operational efficiency.

```
// Pseudocode representation of OperatorTrainingRecord
class OperatorTrainingRecord {
    // Identity and basic data (Locally Cached from Crew Management)
    Id                 // Aggregate Root ID (e.g., Operator ID)
    OperatorId         // Referenced Operator ID
    OperatorName
    Department

    // Training State
    CurrentOverallPhase // Calculated or explicit overall phase

    // Collections
    Qualifications (List<Qualification>)
    TrainingHistory (List<TrainingHistoryEntry>)
    // InternalWorkflows (List<InternalWorkflow>) // Or embed workflow state in Qualification

    // Domain methods - Encapsulate business logic and raise events
    CompleteOjtTraining(processId, result, checklistUrl, userId) {
        // Rules: Check prerequisites, validate inputs
        // Find or create qualification entry
        // Update qualification status to "O"
        // Add history entry
        // Raise OjtCompletedEvent
        // Raise TrainingStatusChangedEvent
    }

    CompleteLearningCurve(processId, result, checklistUrl, userId) {
        // Rules: Must be in 'O' status for this process
        // Find qualification entry
        // Update qualification status to "LC"
        // Add history entry
        // Raise LcCompletedEvent
        // Raise TrainingStatusChangedEvent
    }

    InitiateValidation(processId, userId) {
        // Rules: Must be in 'LC' status
        // Find qualification entry
        // Get if process is key or basic (qualification.isKeyProcess)
        // If key process:
        //   Update qualification status to "V_KeyProcessPending"
        //   Create/update InternalWorkflow entity/state for Key Process Validation
        //   Define initial approval steps for key process validation
        // If basic process:
        //   Update qualification status to "V_Pending"
        //   Call ValidationService for form template if needed
        // Add history entry
        // Raise appropriate ValidationInitiatedEvent
        // Raise NotificationRequestedEvent for key process approvers if needed
    }

    RecordValidationResult(processId, score, isValidated, validationUserId) {
        // Rules: Must be in 'V_Pending' or 'V_KeyProcessPending' status
        // Find qualification entry
        // Check if key or basic process
        // Process result accordingly based on process type
        // If isValidated: Update status to "V"
        // Else: Revert status or update to appropriate failed status
        // Add history entry
        // Raise ValidationCompletedEvent
        // If isValidated: Raise TrainingStatusChangedEvent
    }

    SubmitKeyProcessEvaluation(processId, score, checklistUrl, comments, userId) {
        // Rules: Must be in key process validation status
        // Find qualification entry and verify it's a key process
        // Record score, checklist, and comments
        // Update qualification status based on score and criteria
        // Add history entry
        // Raise KeyProcessEvaluationCompletedEvent
        // If passed: Raise TrainingStatusChangedEvent
    }

    InitiateCertification(processId, userId) {
        // Rules: Must be in 'V' status
        // Find qualification entry
        // Check if key or basic process (qualification.isKeyProcess)
        // Update qualification status to appropriate pending status
        //   (e.g., "C_PendingApproval" or "C_KeyProcessPendingApproval")
        // Create/Update InternalWorkflow entity/state with appropriate steps
        //   based on process type (key or basic)
        // Add history entry
        // Raise appropriate certification event
        // Raise NotificationRequestedEvent for appropriate approvers
    }

    InitiateRecertification(processId, userId) {
        // Rules: Must be 'C' and recertification due
        // Find qualification entry
        // Check if key or basic process (qualification.isKeyProcess)
        // Update qualification status to appropriate pending status
        //   (e.g., "R_PendingApproval" or "R_KeyProcessPendingApproval")
        // Create/Update InternalWorkflow entity/state with appropriate steps
        //   based on process type (key or basic)
        // Add history entry
        // Raise RecertificationInitiatedEvent
        // Raise NotificationRequestedEvent for first approver
    }

    InitiateQualificationRemoval(processId, reason, removalType, evidence, userId) {
        // Find qualification entry
        // Update qualification status to "Removal_PendingApproval"
        // Create/Update InternalWorkflow entity/state for Removal
        // Define complex parallel approval steps (Production, Quality, HR)
        // Add history entry
        // Raise QualificationRemovalInitiatedEvent // Internal event if needed
        // Raise NotificationRequestedEvent (for initial approvers)
    }

    ApproveWorkflowStep(workflowId, processId, approverUserId, role, decision, comments) {
        // Find the relevant InternalWorkflow or Qualification
        // Verify approverUserId and role match the current step
        // Record the decision (Approved/Rejected)
        // If Approved:
        //   Check if more steps exist
        //   If yes: Update workflow to next step, Raise NotificationRequestedEvent (for next approver)
        //   If no (final approval): Update Qualification status (e.g., 'C', 'R', 'Removed'), Raise TrainingStatusChangedEvent / QualificationRemovedEvent
        // If Rejected:
        //   Update Qualification status (e.g., back to 'V', 'C', or 'Removal_Rejected')
        //   Terminate workflow
        //   Raise NotificationRequestedEvent (to initiator/relevant parties)
        // Add history entry
        // Raise WorkflowStepCompleted event
    }

    // Similar methods for Recertification (R), New Qualification (F), Removal, IPE
    // ...

    // Method to update cached operator data
    UpdateOperatorDetails(name, department) {
        // Update local fields if changed
    }
}

// Value Object/Entity
class Qualification {
    ProcessId
    ProcessName // (Locally cached from Skills Matrix)
    IsKeyProcess // Boolean flag to differentiate key vs basic processes
    Status      // O, LC, V_Pending, V_KeyProcessPending, V, C_PendingApproval, C_KeyProcessPendingApproval, C, R_PendingApproval, R_KeyProcessPendingApproval, R, F, Removal_PendingApproval, Removed, *_Rejected
    LastCertificationDate
    LastRecertificationDate
    IsActive
    // Embedded Workflow State (Alternative to separate entity)
    CurrentWorkflowId?: string
    CurrentWorkflowStatus?: string // Pending, Approved, Rejected
    CurrentApproverRole?: string
    ApprovalHistory?: ApprovalStep[]
}

// Example separate Workflow Entity (Alternative)
class InternalWorkflow {
    WorkflowId
    Type // Certification, KeyProcessCertification, Recertification, KeyProcessRecertification, Removal, IPE
    OperatorId
    ProcessId
    IsKeyProcess // Boolean flag copied from qualification
    Status // Pending, InProgress, Approved, Rejected, Cancelled
    CurrentStepIndex // For sequential workflows
    ApprovalSteps: ApprovalStep[]
    InitiatedByUserId
    InitiationTimestamp
    CompletionTimestamp?
    KeyProcessData?: { // Additional data for key processes
        Score?: number,
        ChecklistUrl?: string,
        Comments?: string
    }
}

class ApprovalStep {
    StepIndex // Or identifier for parallel steps
    Role // QualityAuditor, Trainer, ShiftLeader, ProdCoordinator, QualSupervisor, QualCoordinator, HRManager
    Status // Pending, Approved, Rejected
    DecisionByUserId?
    DecisionTimestamp?
    Comments?
}

// Value Object
class TrainingHistoryEntry {
    EventId
    EventType
    Phase       // O, LC, V, V_KeyProcess, C, C_KeyProcess, R, R_KeyProcess, F, etc.
    ProcessId
    Timestamp
    Result      // Score, OK/NOK, Status
    Details     // (e.g., Checklist URL, Comments)
    UserId      // User initiating/completing the action
    WorkflowId?: string
    ApprovalRole?: string // Role performing the action
}
```

#### 3.4 Repository Pattern

The repository abstracts data access for the domain model:

```
// Pseudocode interface
interface OperatorTrainingRepository {
    GetById(operatorId)
    Save(record)
    Exists(operatorId)
}

// Implementation for Cosmos DB
class CosmosDbOperatorTrainingRepository implements OperatorTrainingRepository {
    // Implementation methods
}
```

#### 3.5 Read Model

The read model is optimized for query scenarios:

```
// Pseudocode interface
interface OperatorTrainingQueries {
    GetSummary(operatorId)
    GetDetails(operatorId)
    GetByDepartment(department)
    GetByStatus(status)
    GetQualifications(operatorId)
    GetTrainingHistory(operatorId)
}

// Implementation for Cosmos DB
class CosmosDbOperatorTrainingQueries implements OperatorTrainingQueries {
    // Implementation methods with optimized queries
}
```

#### 3.6 Internal Workflow Management

The Training Process microservice implements a robust internal workflow engine to manage complex approval flows required for certification, recertification, qualification removal, and IPE processes. This approach eliminates external dependencies and provides full control over workflow progression.

##### 3.6.1 Certification/Recertification Workflow

1.  **Initiation**: Team Leader initiates certification/recertification via API (`/api/training/{operatorId}/certification` or `/recertification`).
2.  **Internal Workflow Start**: The command handler updates the `Qualification` status (e.g., to `C_PendingApproval` or `R_PendingApproval`) and creates/updates an internal workflow state (either embedded or in a separate `InternalWorkflow` entity).
3.  **First Approval Step**: The workflow identifies the first approver (e.g., Quality Auditor). A `NotificationRequested` event is published for this user.
4.  **Approver Action**: The Quality Auditor uses an API endpoint (e.g., `POST /api/training/workflows/{workflowId}/approve`) to submit their decision.
5.  **Subsequent Steps**: The `ApproveWorkflowStepHandler` processes the decision.
    - If approved and more steps exist (e.g., Trainer approval), the workflow state is updated to the next step, and a `NotificationRequested` event is published for the next approver.
    - If approved and it's the final step, the `Qualification` status is updated to `C` or `R`, the workflow is marked completed, and a `TrainingStatusChanged` event is published.
    - If rejected at any step, the workflow is terminated, the `Qualification` status is updated (e.g., `C_Rejected` or back to `V`), and relevant notifications are sent.
6.  **History**: Each step and decision is recorded in the `TrainingHistory` or `InternalWorkflow` entity.

```
// Pseudocode internal event for notification
class NotificationRequestedEvent {
    eventId: string;
    recipientUserId: string; // Or Role/Group ID
    notificationType: "ApprovalPending" | "WorkflowCompleted" | "WorkflowRejected";
    context: { // Information for the notification message
        operatorId: string;
        operatorName: string;
        processId: string;
        processName: string;
        workflowType: string; // Certification, Removal, etc.
        workflowId: string;
        currentStep?: string; // e.g., "Quality Auditor Approval"
        initiatorName?: string;
        rejectionReason?: string;
    };
    correlationId: string;
    timestamp: Date;
}

// Pseudocode event structure for workflow completion
class CertificationCompletedEvent {
    eventId: string;
    workflowType: "Certification";
    operatorId: string;
    processId: string;
    outcome: "Approved" | "Rejected";
    approvalChain: [
        { role: "QualityAuditor", userId: string, decision: "Approved" | "Rejected", timestamp: Date },
        { role: "Trainer", userId: string, decision: "Approved" | "Rejected", timestamp: Date }
    ];
    completionDate: Date;
    qualityScore: number;
    rejectionReason?: string;
    correlationId: string;
    timestamp: Date;
}
```

#### 3.6.2 Qualification Removal Workflow

The qualification removal workflow is complex and involves multiple parallel approval paths:

1.  **Initiation**: Team Leader submits removal request via API (`/api/training/qualification-removal`).
2.  **Internal Workflow Start**: Command handler updates `Qualification` status to `Removal_PendingApproval` and creates an `InternalWorkflow` instance for removal, defining the complex parallel/sequential approval paths (Production, Quality, HR).
3.  **Parallel Approval Steps**: Notifications are sent to the first approvers in _each_ required path (e.g., Shift Leader and Quality Supervisor).
4.  **Approver Actions**: Approvers submit decisions via the API.
5.  **Workflow Progression**: The `ApproveWorkflowStepHandler` tracks the state of each path.
    - A path completes when its final approver (e.g., Production Coordinator, Quality Coordinator) approves.
    - The overall workflow requires all mandatory paths to be approved before proceeding to the final HR approval step.
    - If any mandatory approver rejects, the entire workflow is terminated, the status updated (e.g., `Removal_Rejected`), and notifications sent.
6.  **Final Approval (HR)**: Once Production and Quality paths are approved, a notification is sent to the HR Manager.
7.  **HR Decision**: HR Manager submits decision via API.
    - If approved, the `Qualification` status becomes `Removed`, the workflow completes, and a `QualificationRemovedEvent` (which triggers a `TrainingStatusChanged` message for Versatility) is published.
    - If rejected, workflow terminates, status becomes `Removal_Rejected`, and notifications sent.
8.  **History**: All decisions are logged.

```
// Pseudocode event structure for qualification removal completion
class QualificationRemovedEvent {
    eventId: string;
    operatorId: string;
    processId: string;
    status: "Removed";
    removalReason: string;
    removalType: "FTQ_ISSUE" | "CUSTOMER_COMPLAINT" | "OTHER";
    removalDate: Date;
    approvalChain: {
        production: [
            { role: "ShiftLeader", userId: string, decision: "Approved", timestamp: Date },
            { role: "ProductionCoordinator", userId: string, decision: "Approved", timestamp: Date }
        ],
        quality: [
            { role: "QualitySupervisor", userId: string, decision: "Approved", timestamp: Date },
            { role: "QualityCoordinator", userId: string, decision: "Approved", timestamp: Date }
        ],
        hr: { role: "HRManager", userId: string, decision: "Approved", timestamp: Date }
    };
    correlationId: string;
    timestamp: Date;
}
```

#### 3.6.3 New Qualification Workflow

The new qualification workflow is simpler, primarily requiring Trainer approval:

1.  **Initiation**: Team Leader requests via API (`/api/training/new-qualification/request`).
2.  **Notification**: `NotificationRequestedEvent` published for the designated Trainer.
3.  **Trainer Review**: Trainer uses an API endpoint (e.g., `/api/training/qualification-request/approval`) to approve/reject.
4.  **Handler Logic**: `ApproveQualificationRequestCommand` handler processes the decision.
    - If approved, `NewQualificationApprovedEvent` is published (internal signal), and training can proceed.
    - If rejected, appropriate notification sent.
5.  **Training & Familiarization**: Proceeds as before (Trainer inputs status 'F' via API).
6.  **Certification**: When ready, the Team Leader initiates the internal certification workflow described in 3.9.1.

```
// Pseudocode event structure for new qualification request notification
class NewQualificationRequestedEvent {
    eventId: string;
    operatorId: string;
    operatorName: string;
    processId: string;
    processName: string;
    department: string;
    teamLeaderId: string; // Who requested
    trainerId: string;    // Who needs to approve
    requestDate: Date;
    reason: string;
    correlationId: string;
    timestamp: Date;
}

// Pseudocode event structure for familiarization completion
class FamiliarizationCompletedEvent {
    eventId: string;
    operatorId: string;
    operatorName: string;
    processId: string;
    processName: string;
    department: string;
    status: "F";
    completedByUserId: string; // Trainer ID
    completionDate: Date;
    correlationId: string;
    timestamp: Date;
}
```

#### 3.6.4 Individual Performance Evaluation (IPE) Workflow

This workflow is customized for performance evaluation scenarios and follows a defined approval sequence:

1.  **Initiation**: Initiated via API (`/api/training/{operatorId}/ipe`).
2.  **Internal Workflow**: An internal workflow is created with a specific sequence of steps:
    - Team Leader completes initial evaluation form
    - Trainer reviews and provides input
    - Department Manager gives final approval
3.  **Notification Flow**: Each step triggers notifications to the appropriate role
4.  **Approval Actions**: Each approver submits their evaluation via dedicated API endpoints
5.  **Completion**: Upon final approval, evaluation results are recorded and IPE status is updated
6.  **History**: Complete history of the IPE process is maintained for audit and reference purposes

---

### 4. Data Models

#### 4.1 Cosmos DB Data Models

##### 4.1.1 Write Model (`OperatorTrainingRecord`)

This model represents the aggregate root persisted in the write store. It contains all data necessary for enforcing business rules and tracking training history. Includes duplicated data from other modules for efficiency.

**NestJS Entity Definition:**

```typescript
// operator-training-record.entity.ts
export class OperatorTrainingRecord {
  id: string; // Operator ID as document ID & partition key
  type: string = "OperatorTrainingRecord"; // Document type discriminator
  partitionKey: string; // Operator ID as partition key
  operatorId: string;
  // Duplicated from Crew Management (updated via Change Feed)
  operatorName: string;
  department: string;
  // ---
  currentOverallPhase: TrainingPhase; // Represents highest achieved status
  qualifications: Qualification[];
  trainingHistory: TrainingHistoryEntry[];

  // Domain methods (implemented in TypeScript)
  completeOjtTraining(params: CompleteOjtParams): void {
    /* ... */
  }
  completeLearningCurve(params: CompleteLcParams): void {
    /* ... */
  }
  initiateValidation(params: InitiateValidationParams): void {
    /* ... */
  }
  // ... additional methods
}

// qualification.entity.ts
export class Qualification {
  processId: string;
  processName: string; // Duplicated from Skills Matrix
  isKeyProcess: boolean; // Flag to identify key processes that need special handling
  status: QualificationStatus; // O, LC, V_Pending, V_KeyProcessPending, V, C_Pending, C_KeyProcessPending, etc.
  lastCertificationDate?: Date;
  lastRecertificationDate?: Date;
  isActive: boolean;
}

// training-history-entry.entity.ts
export class TrainingHistoryEntry {
  eventId: string;
  eventType: TrainingEventType;
  phase: TrainingPhase;
  processId: string;
  timestamp: Date;
  result?: string; // Score, OK/NOK, Status
  details?: Record<string, any>; // Checklist URL, Comments, etc.
  userId: string; // User initiating/completing action
}

// training.types.ts
export enum TrainingPhase {
  OJT = "O",
  LEARNING_CURVE = "LC",
  VALIDATION_PENDING = "V_Pending",
  VALIDATION_KEY_PROCESS_PENDING = "V_KeyProcessPending",
  VALIDATION = "V",
  CERTIFICATION_PENDING = "C_Pending",
  CERTIFICATION_KEY_PROCESS_PENDING = "C_KeyProcessPending",
  CERTIFICATION = "C",
  RECERTIFICATION_PENDING = "R_Pending",
  RECERTIFICATION_KEY_PROCESS_PENDING = "R_KeyProcessPending",
  RECERTIFICATION = "R",
  FAMILIARIZATION = "F",
}

export enum QualificationStatus {
  OJT = "O",
  LEARNING_CURVE = "LC",
  VALIDATION_PENDING = "V_Pending",
  VALIDATION_KEY_PROCESS_PENDING = "V_KeyProcessPending",
  VALIDATION = "V",
  CERTIFICATION_PENDING = "C_Pending",
  CERTIFICATION_KEY_PROCESS_PENDING = "C_KeyProcessPending",
  CERTIFICATION = "C",
  RECERTIFICATION_PENDING = "R_Pending",
  RECERTIFICATION_KEY_PROCESS_PENDING = "R_KeyProcessPending",
  RECERTIFICATION = "R",
  FAMILIARIZATION = "F",
  REMOVED = "Removed",
}

export enum TrainingEventType {
  OJT_COMPLETED = "OjtCompleted",
  LC_COMPLETED = "LcCompleted",
  VALIDATION_INITIATED = "ValidationInitiated",
  KEY_PROCESS_VALIDATION_INITIATED = "KeyProcessValidationInitiated",
  VALIDATION_COMPLETED = "ValidationCompleted",
  KEY_PROCESS_EVALUATION_COMPLETED = "KeyProcessEvaluationCompleted",
  CERTIFICATION_INITIATED = "CertificationInitiated",
  KEY_PROCESS_CERTIFICATION_INITIATED = "KeyProcessCertificationInitiated",
  CERTIFICATION_COMPLETED = "CertificationCompleted",
  KEY_PROCESS_CERTIFICATION_COMPLETED = "KeyProcessCertificationCompleted",
  // ... other event types
}
```

**Cosmos DB Document Example:**

```json
{
  "id": "op-123",
  "type": "OperatorTrainingRecord",
  "partitionKey": "op-123",
  "operatorId": "op-123",
  "operatorName": "John Doe",
  "surname": "Doe",
  "hiringDate": "2023-05-15T00:00:00Z",
  "department": "Assembling",
  "category": "Operator",
  "function": "Assembler",
  "trainerId": "trainer-456",
  "teamLeaderId": "tl-789",
  "shiftLeaderId": "sl-123",
  "teamId": "team-45",
  "currentOverallPhase": "C",
  "qualifications": [
    {
      "processId": "PROC-ASY-001",
      "processName": "Wire Harness Assembly",
      "isKeyProcess": true,
      "status": "C",
      "lastCertificationDate": "2024-03-15T00:00:00Z",
      "lastRecertificationDate": "2024-03-15T00:00:00Z",
      "isActive": true,
      "department": "Assembling",
      "polyvalenceLevel": "3",
      "removalInfo": null
    },
    {
      "processId": "PROC-ASY-002",
      "processName": "Terminal Crimping",
      "isKeyProcess": false,
      "status": "F",
      "lastCertificationDate": null,
      "lastRecertificationDate": null,
      "isActive": true,
      "department": "Assembling",
      "polyvalenceLevel": "X",
      "familiarizationDate": "2024-04-10T00:00:00Z",
      "familiarizedByUserId": "trainer-456"
    },
    {
      "processId": "PROC-ASY-003",
      "processName": "Connector Assembly",
      "isKeyProcess": false,
      "status": "Removed",
      "lastCertificationDate": "2023-10-22T00:00:00Z",
      "lastRecertificationDate": null,
      "isActive": false,
      "department": "Assembling",
      "polyvalenceLevel": "2",
      "removalInfo": {
        "removalDate": "2024-02-20T00:00:00Z",
        "removalReason": "Recurring quality issues with connector assembly",
        "removalType": "FTQ_ISSUE",
        "removedByWorkflow": "wf-rem-789",
        "approvalChain": [
          { "role": "TeamLeader", "userId": "tl-123", "decision": "Approved", "timestamp": "2024-02-15T09:00:00Z" },
          { "role": "ShiftLeader", "userId": "sl-456", "decision": "Approved", "timestamp": "2024-02-16T14:30:00Z" },
          { "role": "ProductionCoordinator", "userId": "pc-789", "decision": "Approved", "timestamp": "2024-02-17T11:15:00Z" },
          { "role": "QualitySupervisor", "userId": "qs-123", "decision": "Approved", "timestamp": "2024-02-16T10:45:00Z" },
          { "role": "QualityCoordinator", "userId": "qc-456", "decision": "Approved", "timestamp": "2024-02-17T16:20:00Z" },
          { "role": "HRManager", "userId": "hr-789", "decision": "Approved", "timestamp": "2024-02-19T09:30:00Z" }
        ]
      }
    }
  ],
  "trainingHistory": [
    {
      "eventId": "evt-001",
      "eventType": "OjtCompleted",
      "phase": "O",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-01-15T10:30:00Z",
      "result": "85%",
      "details": {
        "checklistUrl": "/attachments/checklist-123.pdf",
        "comments": "Performed well on basic tasks",
        "uploadedBy": "trainer-456"
      },
      "userId": "trainer-456"
    },
    {
      "eventId": "evt-020",
      "eventType": "KeyProcessEvaluationCompleted",
      "phase": "V_KeyProcessPending",
      "processId": "PROC-ASY-001",
      "timestamp": "2024-02-10T14:30:00Z",
      "result": "92%",
      "details": {
        "checklistUrl": "/attachments/key-process-checklist-456.pdf",
        "comments": "Excellent performance on key process evaluation",
        "uploadedBy": "trainer-456"
      },
      "userId": "trainer-456"
    },
    {
      "eventId": "evt-007",
      "eventType": "NewQualificationRequested",
      "phase": "F_Requested",
      "processId": "PROC-ASY-002",
      "timestamp": "2024-04-01T09:00:00Z",
      "result": null,
      "details": {
        "requestReason": "Need for additional versatility in terminal crimping process"
      },
      "userId": "tl-789"
    }
  ],
  "_etag": ""...",
  "_ts": 1618847477
}
```

##### 4.1.2 Read Model Projections

**NestJS Projection Definition:**

```typescript
// operator-summary.projection.ts
export class OperatorTrainingSummary {
  id: string;
  type: string = "OperatorTrainingSummary";
  partitionKey: string;
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingPhase: string;
  qualificationCount: number;
  activeQualifications: QualificationSummary[];
  lastUpdated: Date;
}

// qualification-summary.projection.ts
export class QualificationSummary {
  processId: string;
  processName: string;
  status: string;
}
```

**Cosmos DB Document Examples:**

**Operator Training Summary Projection:**

```json
{
  "id": "op-123",
  "type": "OperatorTrainingSummary",
  "partitionKey": "op-123",
  "operatorId": "op-123",
  "operatorName": "John Doe",
  "department": "Assembling",
  "currentTrainingPhase": "LC",
  "qualificationCount": 2,
  "activeQualifications": [
    {
      "processId": "PROC-ASY-001",
      "processName": "Wire Harness Assembly",
      "status": "C"
    },
    {
      "processId": "PROC-ASY-005",
      "processName": "Advanced Soldering",
      "status": "LC"
    }
  ],
  "lastUpdated": "2024-02-10T14:15:00Z"
}
```

**Department Summary Projection:**

```json
{
  "id": "dept-Assembling",
  "type": "DepartmentSummary",
  "partitionKey": "Assembling",
  "department": "Assembling",
  "operatorCount": 50,
  "operatorsByPhase": {
    "O": 10,
    "LC": 15,
    "V": 5,
    "C": 20,
    "R": 0,
    "F": 0
  },
  "lastUpdated": "2024-02-10T14:15:00Z"
}
```

#### 4.2 Command DTOs

**NestJS DTO Definitions:**

```typescript
// complete-ojt.dto.ts
export class CompleteOjtRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsString()
  result: string;

  @IsOptional()
  @IsString()
  checklistUrl?: string;

  @IsOptional()
  @IsString()
  comments?: string;
}

// complete-learning-curve.dto.ts
export class CompleteLearningCurveRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsString()
  @IsIn(["OK", "NOK"])
  result: string;

  @IsOptional()
  @IsString()
  checklistUrl?: string;
}

// initiate-validation.dto.ts
export class InitiateValidationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
}

// initiate-certification.dto.ts
export class InitiateCertificationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
  // No callbackUrl needed
}

// initiate-recertification.dto.ts (Assuming similar structure)
export class InitiateRecertificationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
}

// remove-qualification.dto.ts
export class RemoveQualificationRequestDto {
  @IsString()
  @IsNotEmpty()
  operatorId: string;

  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsString()
  @IsNotEmpty()
  reason: string;

  @IsEnum(["FTQ_ISSUE", "CUSTOMER_COMPLAINT", "OTHER"])
  removalType: string;

  @IsObject()
  @IsOptional()
  evidenceDetails?: Record<string, any>;
  // No callbackUrl or explicit approval paths needed in request DTO
  // Workflow configuration is internal logic
}

// initiate-ipe.dto.ts (Assuming structure)
export class InitiateIpeRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string; // Or other relevant IPE context
}

// approve-workflow-step.dto.ts - NEW DTO
export class ApproveWorkflowStepDto {
  @IsString()
  @IsNotEmpty()
  workflowId: string; // Identifier for the specific workflow instance

  @IsString()
  @IsIn(["Approved", "Rejected"])
  decision: string;

  @IsString()
  @IsOptional()
  comments?: string;
}

// --- DTOs for receiving results from external systems are removed ---
// Removed: ValidationResultCallbackDto (Still needed for Validation Submodule)
// Removed: WorkflowResultCallbackDto

// --- Existing DTOs remain relevant ---
// assign-trainer.dto.ts
// approve-qualification-request.dto.ts // Used by Trainer for New Qual request
// new-qualification-request.dto.ts
// familiarization-completion.dto.ts

// New DTO for key process evaluation
export class SubmitKeyProcessEvaluationDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  @IsNumber()
  @Min(0)
  @Max(100)
  score: number;

  @IsString()
  @IsUrl()
  checklistUrl: string;

  @IsOptional()
  @IsString()
  comments?: string;
}

// Modified initiate-validation.dto.ts
export class InitiateValidationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;

  // No additional fields needed - isKeyProcess is determined internally
  // based on the process definition
}

// Modified initiate-certification.dto.ts
export class InitiateCertificationRequestDto {
  @IsString()
  @IsNotEmpty()
  processId: string;
  // No additional fields needed - isKeyProcess is determined internally
}

// ... Other existing DTOs
```

#### 4.3 Query DTOs

**NestJS DTO Definitions:**

```typescript
// operator-training-summary.dto.ts
export class OperatorTrainingSummaryDto {
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingPhase: string;
  qualifications: QualificationSummaryDto[];
}

// qualification-summary.dto.ts
export class QualificationSummaryDto {
  processId: string;
  processName: string;
  status: string;
}

// operator-training-details.dto.ts
export class OperatorTrainingDetailsDto {
  operatorId: string;
  operatorName: string;
  department: string;
  currentTrainingPhase: string;
  qualifications: QualificationDto[];
  trainingHistory: TrainingHistoryEntryDto[];
}

// training-history-entry.dto.ts
export class TrainingHistoryEntryDto {
  eventType: string;
  phase: string;
  processId: string;
  processName: string;
  timestamp: Date;
  result: string;
  userId: string;
  details: Record<string, any>;
}

// recertification-due.dto.ts
export class RecertificationDueDto {
  operatorId: string;
  operatorName: string;
  processId: string;
  processName: string;
  lastCertificationDate: Date;
  dueDate: Date; // Calculated based on certification date
}

// qualification-removal-history.dto.ts
export class QualificationRemovalHistoryDto {
  operatorId: string;
  operatorName: string;
  processId: string;
  processName: string;
  removalDate: Date;
  removalReason: string;
  removalType: string;
  approvalChain: {
    role: string;
    userId: string;
    userName: string;
    decision: string;
    timestamp: Date;
  }[];
}
```

#### 4.4 Event Schemas

**NestJS Event Definitions:**

```typescript
// ojt-completed.event.ts
export class OjtCompletedEvent {
  eventId: string;
  operatorId: string;
  operatorName: string;
  department: string;
  processId: string;
  processName: string;
  result: string;
  userId: string;
  details: Record<string, any>;
  correlationId: string;
  timestamp: Date;
}

// training-status-changed.event.ts (Remains important for Versatility Matrix)
export class TrainingStatusChangedEvent {
  eventId: string;
  operatorId: string;
  processId: string;
  processName: string;
  oldStatus: string;
  newStatus: string; // O, LC, V, C, R, F, Removed, etc.
  statusChangeDate: Date;
  correlationId: string;
  timestamp: Date;
}

// qualification-removed.event.ts (Published internally upon successful removal workflow)
export class QualificationRemovedEvent {
  eventId: string;
  operatorId: string;
  processId: string;
  status: string = "Removed";
  removalReason: string;
  removalType: string;
  removalDate: Date;
  correlationId: string;
  timestamp: Date;
}

// notification-requested.event.ts - NEW Generic Event for Notifications
export class NotificationRequestedEvent {
  eventId: string;
  recipientUserIds: string[]; // Can be one or many
  recipientRole?: string; // Alternative targeting
  notificationType: string; // e.g., "ApprovalPending", "Info", "Reminder"
  templateKey: string; // Identifier for message template in Notification Service
  context: Record<string, any>; // Data for the template
  correlationId: string;
  timestamp: Date;
}

// --- Events for triggering/completing EXTERNAL workflows removed ---
// Removed: CertificationWorkflowRequestedEvent
// Removed: CertificationWorkflowCompletedEvent
// Removed: RecertificationWorkflowRequestedEvent
// Removed: RecertificationWorkflowCompletedEvent
// Removed: QualificationRemovalWorkflowRequestedEvent
// Removed: IpeWorkflowRequestedEvent
// Removed: IpeWorkflowCompletedEvent

// --- Other existing events remain relevant ---
// LearningCurveCompletedNotificationEvent (Could potentially be replaced by generic NotificationRequestedEvent)
// NewQualificationRequestedEvent (Could be replaced by generic NotificationRequestedEvent)
// NewQualificationApprovedEvent (Internal signal)
// FamiliarizationCompletedEvent (Important for Versatility Matrix)

// New events for key process handling
export class KeyProcessEvaluationCompletedEvent {
  eventId: string;
  operatorId: string;
  operatorName: string;
  department: string;
  processId: string;
  processName: string;
  score: number;
  passed: boolean;
  checklistUrl: string;
  comments?: string;
  userId: string;
  correlationId: string;
  timestamp: Date;
}

export class KeyProcessCertificationCompletedEvent {
  eventId: string;
  operatorId: string;
  processId: string;
  processName: string;
  outcome: "Approved" | "Rejected";
  score: number;
  checklistUrl: string;
  approvalChain: Array<{
    role: string;
    userId: string;
    decision: string;
    timestamp: Date;
  }>;
  completionDate: Date;
  correlationId: string;
  timestamp: Date;
}
```

**Service Bus Message Examples:**

// Remove examples for \*WorkflowRequested events
// Keep/Update examples for TrainingStatusChangedEvent, QualificationRemovedEvent
// Add example for NotificationRequestedEvent

**NotificationRequestedEvent Example:**

```json
{
  "eventId": "evt-notify-123",
  "recipientUserIds": ["qa-555"],
  "recipientRole": "QualityAuditor",
  "notificationType": "ApprovalPending",
  "templateKey": "CERTIFICATION_APPROVAL_REQUIRED",
  "context": {
    "operatorId": "op-123",
    "operatorName": "John Doe",
    "processName": "Wire Harness Assembly",
    "workflowId": "wf-cert-987",
    "initiatorName": "Jane Smith (TL)",
    "submissionDate": "2024-03-01T10:00:00Z"
  },
  "correlationId": "corr-7890",
  "timestamp": "2024-03-01T10:01:00Z"
}
```

### 5. API Specification

#### 5.1 Command Endpoints

**Training Process API:**

```typescript
// training.controller.ts
@Controller("api/training")
export class TrainingController {
  constructor(private commandBus: CommandBus) {}

  @Post(":operatorId/ojt")
  @HttpCode(202)
  async completeOjt(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteOjtRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteOjtCommand(operatorId, dto, req.user.id)
    );
  }

  @Post(":operatorId/learning-curve")
  @HttpCode(202)
  async completeLearningCurve(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteLearningCurveRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteLearningCurveCommand(operatorId, dto, req.user.id)
    );
  }

  @Post(":operatorId/validation")
  @HttpCode(202)
  async initiateValidation(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateValidationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new InitiateValidationCommand(operatorId, dto.processId, req.user.id)
    );
  }

  @Post(":operatorId/certification")
  @HttpCode(202)
  async initiateCertification(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateCertificationRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateCertificationCommand(operatorId, dto.processId, req.user.id)
    );
  }

  @Post(":operatorId/recertification")
  @HttpCode(202)
  async initiateRecertification(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateRecertificationRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateRecertificationCommand(operatorId, dto.processId, req.user.id)
    );
  }

  @Post(":operatorId/new-qualification")
  @HttpCode(202)
  async initiateNewQualification(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateNewQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new InitiateNewQualificationCommand(operatorId, dto, req.user.id)
    );
  }

  @Post(":operatorId/qualification-completion")
  @HttpCode(202)
  async completeNewQualification(
    @Param("operatorId") operatorId: string,
    @Body() dto: CompleteNewQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteNewQualificationCommand(operatorId, dto, req.user.id)
    );
  }

  @Post("qualification-removal")
  @HttpCode(202)
  async removeQualification(
    @Body() dto: RemoveQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateQualificationRemovalCommand(dto, req.user.id) // Renamed command
    );
  }

  @Post(":operatorId/ipe")
  @HttpCode(202)
  async initiateIpe(
    @Param("operatorId") operatorId: string,
    @Body() dto: InitiateIpeRequestDto,
    @Request() req
  ): Promise<void> {
    // Returns 202 Accepted, starts internal workflow
    await this.commandBus.execute(
      new InitiateIpeCommand(operatorId, dto, req.user.id)
    );
  }

  @Post("trainer-assignment")
  @HttpCode(202)
  async assignTrainer(
    @Body() dto: AssignTrainerRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(new AssignTrainerCommand(dto, req.user.id));
  }

  @Post("qualification-request/approval")
  @HttpCode(202)
  async approveQualificationRequest(
    @Body() dto: ApproveQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new ApproveQualificationRequestCommand(dto, req.user.id)
    );
  }

  @Post("new-qualification/request")
  @HttpCode(202)
  async requestNewQualification(
    @Body() dto: NewQualificationRequestDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new RequestNewQualificationCommand(dto, req.user.id)
    );
  }

  @Post("familiarization/complete")
  @HttpCode(202)
  async completeFamiliarization(
    @Body() dto: FamiliarizationCompletionDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new CompleteFamiliarizationCommand(dto, req.user.id)
    );
  }

  @Post(":operatorId/key-process-evaluation")
  @HttpCode(202)
  async submitKeyProcessEvaluation(
    @Param("operatorId") operatorId: string,
    @Body() dto: SubmitKeyProcessEvaluationDto,
    @Request() req
  ): Promise<void> {
    await this.commandBus.execute(
      new SubmitKeyProcessEvaluationCommand(operatorId, dto, req.user.id)
    );
  }

  @Post("key-process/checklist-upload")
  @HttpCode(200)
  @UseInterceptors(FileInterceptor("file"))
  async uploadKeyProcessChecklist(
    @UploadedFile() file: Express.Multer.File,
    @Body("operatorId") operatorId: string,
    @Body("processId") processId: string,
    @Request() req
  ): Promise<{ checklistUrl: string }> {
    const checklistUrl = await this.keyProcessService.storeChecklist(
      file,
      operatorId,
      processId
    );
    return { checklistUrl };
  }
}

// workflow.controller.ts - NEW Controller for workflow actions
@Controller("api/training/workflows")
export class WorkflowController {
  constructor(private commandBus: CommandBus) {}

  @Post(":workflowId/approve") // Or a more general '/decision' endpoint
  @HttpCode(200) // Or 202 if action is async
  async approveWorkflowStep(
    @Param("workflowId") workflowId: string,
    @Body() dto: ApproveWorkflowStepDto, // Contains decision (Approve/Reject) & comments
    @Request() req // Get approver user ID
  ): Promise<void> {
    await this.commandBus.execute(
      new ApproveWorkflowStepCommand(workflowId, dto, req.user.id)
    );
  }
}

// Removed: callback.controller.ts
```

**API Swagger Documentation Summary:**

| Endpoint                                              | Method | Description                                                       | Request                            | Response        |
| ----------------------------------------------------- | ------ | ----------------------------------------------------------------- | ---------------------------------- | --------------- |
| `/api/training/{operatorId}/ojt`                      | POST   | Completes On Job Training (O) phase                               | CompleteOjtRequestDto              | 202 Accepted    |
| `/api/training/{operatorId}/learning-curve`           | POST   | Completes Learning Curve (LC) phase                               | CompleteLearningCurveRequestDto    | 202 Accepted    |
| `/api/training/{operatorId}/validation`               | POST   | Initiates Validation (V) phase                                    | InitiateValidationRequestDto       | 202 Accepted    |
| `/api/training/{operatorId}/certification`            | POST   | Initiates **Internal** Certification (C) workflow                 | InitiateCertificationRequestDto    | 202 Accepted    |
| `/api/training/{operatorId}/recertification`          | POST   | Initiates **Internal** Recertification (R) workflow               | InitiateRecertificationRequestDto  | 202 Accepted    |
| `/api/training/{operatorId}/new-qualification`        | POST   | Initiates New Qualification (F) training                          | InitiateNewQualificationRequestDto | 202 Accepted    |
| `/api/training/{operatorId}/qualification-completion` | POST   | Completes New Qualification training                              | CompleteNewQualificationRequestDto | 202 Accepted    |
| `/api/training/qualification-removal`                 | POST   | Initiates **Internal** qualification removal workflow             | RemoveQualificationRequestDto      | 202 Accepted    |
| `/api/training/{operatorId}/ipe`                      | POST   | Initiates **Internal** IPE workflow                               | InitiateIpeRequestDto              | 202 Accepted    |
| `/api/training/trainer-assignment`                    | POST   | Assigns a trainer to an operator                                  | AssignTrainerRequestDto            | 202 Accepted    |
| `/api/training/qualification-request/approval`        | POST   | Approves or rejects a new qualification request (by Trainer)      | ApproveQualificationRequestDto     | 202 Accepted    |
| `/api/training/new-qualification/request`             | POST   | Requests a new qualification for an operator (for Trainer review) | NewQualificationRequestDto         | 202 Accepted    |
| `/api/training/familiarization/complete`              | POST   | Completes familiarization training for a qualification            | FamiliarizationCompletionDto       | 202 Accepted    |
| `/api/training/workflows/{workflowId}/approve`        | POST   | Submits an approval/rejection decision for a workflow step        | ApproveWorkflowStepDto             | 200 OK          |
| `/api/training/{operatorId}/key-process-evaluation`   | POST   | Submits evaluation for a key process                              | SubmitKeyProcessEvaluationDto      | 202 Accepted    |
| `/api/training/key-process/checklist-upload`          | POST   | Uploads checklist document for key process                        | Multipart form data                | 200 OK with URL |

#### 5.2 Query Endpoints

**Query API:**

```typescript
// query.controller.ts
@Controller("api/training")
export class QueryController {
  constructor(private queryBus: QueryBus) {}

  @Get("qualification-removal/history/{operatorId}")
  async getQualificationRemovalHistory(
    @Param("operatorId") operatorId: string,
    @Query("processId") processId?: string
  ): Promise<QualificationRemovalHistoryDto[]> {
    return this.queryBus.execute(
      new GetQualificationRemovalHistoryQuery(operatorId, processId)
    );
  }
}
```

**API Swagger Documentation Summary:**

| Endpoint                                                   | Method | Description                                        | Query Params         | Response                         |
| ---------------------------------------------------------- | ------ | -------------------------------------------------- | -------------------- | -------------------------------- |
| `/api/training/qualification-removal/history/{operatorId}` | GET    | Gets qualification removal history for an operator | processId (optional) | QualificationRemovalHistoryDto[] |

### 6. Event Communication

#### 6.1 Published Events

##### 6.1.1 Training Status & Related Events (Topic: "training-status-events" or specific topics)

- `OjtCompleted`
- `LearningCurveCompleted`
- `ValidationInitiated`
- `ValidationCompleted`
- `CertificationInitiated` // Internal event, might not need external publishing
- `CertificationCompleted` // Signals completion of internal workflow
- `RecertificationInitiated` // Internal event
- `RecertificationCompleted` // Signals completion of internal workflow
- `NewQualificationInitiated` // Internal event
- `NewQualificationCompleted`
- `TrainingStatusChanged` (Key event for Versatility Matrix)
- `NewQualificationApproved` // Internal signal
- `FamiliarizationCompleted` (Key event for Versatility Matrix status "F")
- `QualificationRemovalInitiated` // Internal event
- `QualificationRemoved` (Key event for Versatility Matrix status "Removed")
- `WorkflowStepCompleted` // Internal event

// Removed: Workflow Trigger Events (Topic: "workflow-trigger-events")
// - CertificationWorkflowRequested
// - RecertificationWorkflowRequested
// - QualificationRemovalWorkflowRequested
// - IpeWorkflowRequested

##### 6.1.2 Notification Events (Topic: "notification-events")

- `NotificationRequested` (Generic event to trigger user notifications via Notification Service for pending approvals, workflow outcomes etc.)
  // Removed: Specific notification events like NewQualificationRequestedNotification, QualificationRemovedNotification

### 7. Azure Services Integration

#### 7.1 Azure Cosmos DB

- **Container Name**: `training-process`
- **Partition Key**: `/partitionKey` (operator ID)
- **Throughput**: Autoscale (1000-10000 RU/s)
- **Indexing Policy**: Optimized for common queries
- **TTL**: Not enabled (permanent data)
- **Change Feed**: Used for read model synchronization

#### 7.2 Azure Service Bus

- **Namespace**: `connected-workers-sb`
- **Topics**:

  - `training-status-events`: For training phase/status updates (Consumed by Versatility Matrix, potentially read models).
  - `notification-events`: For sending notification requests to the Notification Service.
  - `training-internal-events`: Optional topic for internal communication (e.g., read model updates if not using Change Feed directly).
    // Removed: workflow-trigger-events
    // Removed: workflow-completion-events

- **Subscriptions**:
  // Removed: training-process-workflow-results subscription
  // Add subscriptions needed by internal components (e.g., read model updater if using internal events topic)

#### 7.3 Azure API Management

- **API**: `training-process-api`
- **Operations**: Command and query endpoints
- **Policies**:
  - JWT validation
  - Rate limiting
  - Caching (for queries)
  - Correlation ID generation/propagation

### 8. Conclusion

The Training Process microservice provides a comprehensive solution for managing and tracking operator training progression and qualifications. By leveraging a microservices architecture, CQRS, and event-driven patterns, it offers a decoupled and scalable system. Key functionalities include orchestrating training phase transitions, managing complex internal approval workflows for certifications, recertifications, and qualification removals, and integrating with other modules like Crew Management and Skills Matrix via change feeds. The service ensures data consistency through local data duplication and publishes status updates and notification requests to maintain system-wide awareness. With a well-defined API for commands and queries, and clear integration with Azure services like Cosmos DB and Service Bus, the Training Process microservice is a robust and essential component for effective workforce management.

## 5.2.3 Document Validation

### 1. Overview

The Template Validation microservice manages evaluation templates for assessing operator skills during validation, certification, and performance evaluation processes. It provides configurable templates, scoring algorithms, and business rules that ensure consistent evaluation across departments.

**In Scope:**

- Template management (CRUD operations for templates, categories, criteria)
- Dynamic form generation based on template configurations
- Multi-evaluator support for training assessment
- Automated calculation of evaluation scores
- Enforcement of business rules (minimum thresholds, retraining flags)
- Storage of evaluation results and audit history
- Integration with Training Process microservice

**Key Design Constraints:**

- All templates use the RATING criterion type (1-4 scale)
- The scoring method used is WEIGHTED_AVERAGE for all templates

**Out of Scope:**

- User management and authentication (uses central identity service)
- Workflow orchestration (handled by Training Process microservice)
- Notification delivery (handled by central Notification Service)
- Operator data management (handled by Crew Management)

#### 1.1 Domain Context Diagram

```mermaid
graph TD
    subgraph Template Validation Microservice
        direction LR
        TV_Core[Template Core Logic]
        TV_Calc[Calculation Engine]
        TV_DB[(Database)]
        TV_API[API Layer]

        TV_API -- Commands --> TV_Core
        TV_Core -- Store/Retrieve --> TV_DB
        TV_Core -- Calculate Scores --> TV_Calc
        TV_DB -- Read Templates --> TV_API
    end

    TrainingProcess[Training Process Service] -- Request Templates/Score --> TV_API
    TV_API -- Templates/Scoring Results --> TrainingProcess

    Admin[Admin Users] -- Template Management --> TV_API
    Evaluators[Team Leaders/Trainers] -- Submit Evaluations --> TV_API

    IdentityService[Identity Service] -- Authentication --> TV_API
```

#### 1.2 Key Capabilities

1. **Template Management**: Create, update, and version control evaluation templates
2. **Dynamic Form Generation**: Generate UI-friendly template definitions for rendering forms
3. **Multi-Evaluator Support**: Enable separate evaluations from Team Leader and Trainer
4. **Calculation Engine**: Compute scores based on template-defined algorithms
5. **Business Rules Enforcement**: Apply validation rules and thresholds
6. **Results Storage**: Maintain evaluation history and audit trail
7. **API Integration**: Provide templates and scoring to Training Process microservice

---

### 2. Architecture

#### 2.1 Architectural Patterns

The Template Validation microservice implements the following architectural patterns:

1. **Domain-Driven Design**: Focuses on the core domain concepts of templates and evaluations
2. **RESTful Architecture**: Exposes HTTP-based API for template and evaluation operations
3. **Repository Pattern**: Abstracts data access for domain entities
4. **Factory Pattern**: For template and form generation
5. **Strategy Pattern**: For applying different business rules based on template type

#### 2.2 High-Level Component Diagram

```mermaid
graph TD
    Client[Client Applications] --> APILyr[API Layer]

    subgraph Template Validation Microservice
        APILyr --> TemplateController[Template Controller]
        APILyr --> EvaluationController[Evaluation Controller]
        APILyr --> ScoringController[Scoring Controller]

        TemplateController --> TemplateSvc[Template Service]
        EvaluationController --> EvaluationSvc[Evaluation Service]
        ScoringController --> ScoringSvc[Scoring Service]

        TemplateSvc --> TemplateRepo[Template Repository]
        EvaluationSvc --> EvaluationRepo[Evaluation Repository]
        ScoringSvc --> CalcEngine[Calculation Engine]

        TemplateRepo --> DB[(Database)]
        EvaluationRepo --> DB

        CalcEngine --> RuleEngine[Business Rule Engine]
    end

    TrainingSvc[Training Process Service] --> APILyr
```

#### 2.3 Component Details

##### 2.3.1 Template Module

**Responsibility**: Manages template definitions, categories, criteria, and versioning.

**Key Components**:

- Template Repository: CRUD operations for templates
- Template Factory: Creates template definitions from configurations
- Template Validator: Ensures templates meet requirements (completeness, structure, etc.)

  ```typescript
  // Example validation rules for template structure
  function validateTemplateStructure(
    template: CreateTemplateDto
  ): ValidationResult {
    // Ensure scoringMethod is WEIGHTED_AVERAGE
    if (template.scoringMethod !== "WEIGHTED_AVERAGE") {
      return {
        isValid: false,
        errors: ["Templates must use WEIGHTED_AVERAGE scoring method"],
      };
    }

    // Ensure all categories have at least one criterion
    for (const category of template.categories) {
      if (!category.criteria || category.criteria.length === 0) {
        return {
          isValid: false,
          errors: [
            `Category '${category.name}' must have at least one criterion`,
          ],
        };
      }

      // Ensure all criteria have complete rating descriptions
      for (const criterion of category.criteria) {
        if (!criterion.ratingDescriptions) {
          return {
            isValid: false,
            errors: [
              `Criterion '${criterion.description}' must have rating descriptions for all levels (1-4)`,
            ],
          };
        }

        // Check that all rating levels are defined
        const levels = ["1", "2", "3", "4"];
        for (const level of levels) {
          if (!criterion.ratingDescriptions[level]) {
            return {
              isValid: false,
              errors: [
                `Criterion '${criterion.description}' missing description for rating level ${level}`,
              ],
            };
          }
        }
      }
    }

    return { isValid: true };
  }
  ```

**Key Interfaces**:

- `ITemplateRepository`: Data access for templates
- `ITemplateService`: Business logic for template management
- `ITemplateFactory`: Template creation and transformation

##### 2.3.2 Evaluation Module

**Responsibility**: Handles evaluation submissions, validations, and storage.

**Key Components**:

- Evaluation Repository: Stores evaluation results
- Evaluation Validator: Validates evaluation submissions
- Multi-Evaluator Reconciler: Handles multiple evaluator inputs

**Key Interfaces**:

- `IEvaluationRepository`: Data access for evaluations
- `IEvaluationService`: Business logic for evaluation submission
- `IMultiEvaluatorService`: Reconciliation of multiple evaluator inputs

##### 2.3.3 Calculation Engine

**Responsibility**: Performs score calculations and applies business rules.

**Key Components**:

- Score Calculator: Implements the weighted average scoring method for rating-based templates (1-4 scale)
- Business Rule Engine: Applies thresholds and special rules
- Result Processor: Generates final evaluation results

**Key Interfaces**:

- `ICalculationService`: Score calculation logic
- `IBusinessRuleEngine`: Rule application and enforcement
- `IResultProcessor`: Final evaluation result generation

##### 2.3.4 API Layer

**Responsibility**: Exposes RESTful endpoints for template and evaluation operations.

**Key Controllers**:

- Template Controller: Template CRUD operations
- Evaluation Controller: Submission and retrieval of evaluations
- Scoring Controller: On-demand scoring and rule checking

**Middleware**:

- Authentication: JWT validation
- Authorization: Role-based access control
- Validation: Request payload validation
- Error Handling: Consistent error responses
- Logging: Request/response logging

---

### 3. Data Models

#### 3.1 Cosmos DB

##### 3.1.1 Templates Container

Stores template definitions with categories, criteria, and rules.

```typescript
// Template
interface Template {
  _id: ObjectId; // Unique identifier
  name: string; // Template name
  description: string; // Template description
  type: "VALIDATION" | "CERTIFICATION" | "IPE"; // Template type
  version: string; // Semantic version
  isActive: boolean; // Whether template is active
  department: string; // Department this template applies to
  processes: string[]; // Processes this template evaluates
  skillType: "BASIC" | "KEY"; // Whether this is a basic or key skill
  scoringMethod: "WEIGHTED_AVERAGE"; // Method used for scoring
  categories: Category[]; // Categories in this template
  minThreshold: number; // Minimum score threshold (e.g., 80 for validation)
  maxPossibleScore: number; // Maximum possible score
  createdBy: string; // User who created the template
  createdAt: Date; // Creation timestamp
  updatedBy: string; // User who last updated the template
  updatedAt: Date; // Last update timestamp
}

// Category
interface Category {
  id: string; // Unique identifier within template
  name: string; // Category name
  order: number; // Display order
  criteria: Criterion[]; // Criteria in this category
}

// Criterion
interface Criterion {
  id: string; // Unique identifier within category
  description: string; // Criterion description
  order: number; // Display order
  ratingDescriptions: {
    // Descriptions for each rating level
    "1": string;
    "2": string;
    "3": string;
    "4": string;
  };
  weight: number; // Weight of this criterion in scoring (default: 1)
  applicableTo: ("VALIDATION" | "CERTIFICATION" | "IPE")[]; // Which evaluation types this applies to
}
```

Example Document:

```json
{
  "_id": "60a12c5e2f7d8b1c98765432",
  "name": "Assembly Line Validation Template",
  "description": "Template for validating operators on the assembly line",
  "type": "VALIDATION",
  "version": "1.0.0",
  "isActive": true,
  "department": "Assembling",
  "processes": ["Wire Harness Assembly"],
  "skillType": "BASIC",
  "scoringMethod": "WEIGHTED_AVERAGE",
  "categories": [
    {
      "id": "cat-001",
      "name": "Santé et Sécurité",
      "order": 1,
      "criteria": [
        {
          "id": "crit-001",
          "description": "Utilisation correcte des outils de connexion",
          "order": 1,
          "ratingDescriptions": {
            "1": "N'utilise pas correctement les outils, risque de blessure",
            "2": "Utilise les outils correctement mais avec hésitation",
            "3": "Utilise les outils correctement et avec assurance",
            "4": "Maîtrise parfaitement l'utilisation des outils et peut former d'autres"
          },
          "weight": 1,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        }
      ]
    },
    {
      "id": "cat-002",
      "name": "Qualité",
      "order": 2,
      "criteria": [
        {
          "id": "crit-002",
          "description": "Inspection visuelle des connexions",
          "order": 1,
          "ratingDescriptions": {
            "1": "Ne vérifie pas visuellement les connexions",
            "2": "Vérifie occasionnellement les connexions",
            "3": "Vérifie systématiquement les connexions",
            "4": "Vérifie systématiquement et peut détecter des défauts subtils"
          },
          "weight": 1,
          "applicableTo": ["VALIDATION", "CERTIFICATION", "IPE"]
        }
      ]
    }
  ],
  "minThreshold": 80,
  "maxPossibleScore": 48,
  "createdBy": "admin-user-001",
  "createdAt": "2023-01-15T10:30:00Z",
  "updatedBy": "admin-user-001",
  "updatedAt": "2023-01-15T10:30:00Z"
}
```

##### 3.1.2 Evaluations Container

Stores evaluation submissions and results.

```typescript
// Evaluation
interface Evaluation {
  _id: ObjectId; // Unique identifier
  templateId: ObjectId; // Reference to template used
  templateVersion: string; // Version of template used
  templateType: "VALIDATION" | "CERTIFICATION" | "IPE"; // Type of evaluation
  operatorId: string; // ID of operator being evaluated
  operatorName: string; // Name of operator
  department: string; // Department
  process: string; // Process being evaluated
  evaluations: EvaluatorInput[]; // Inputs from evaluators
  finalScore: number; // Calculated final score
  percentage: number; // Score as percentage
  passed: boolean; // Whether operator passed
  hasRatingOne: boolean; // Whether any criteria received a '1' rating
  retrainingNeeded: boolean; // Whether retraining is needed (failed or has '1' rating)
  retrainingCriteria: string[]; // IDs of criteria needing retraining
  status: "DRAFT" | "SUBMITTED" | "APPROVED" | "REJECTED"; // Status
  submittedBy: string; // User who submitted evaluation
  submittedAt: Date; // Submission timestamp
  approvedBy: string; // User who approved evaluation
  approvedAt: Date; // Approval timestamp
  comments: string; // General comments
}

// Evaluator Input
interface EvaluatorInput {
  evaluatorId: string; // ID of evaluator
  evaluatorName: string; // Name of evaluator
  evaluatorRole: "TEAM_LEADER" | "TRAINER"; // Role of evaluator
  ratings: Rating[]; // Ratings provided by this evaluator
  comments: string; // Comments from this evaluator
  timestamp: Date; // When evaluation was provided
}

// Rating
interface Rating {
  criterionId: string; // ID of criterion being rated
  numericValue: 1 | 2 | 3 | 4; // Rating value
  comments: string; // Optional comments for this rating
}
```

Example Document:

```json
{
  "_id": "60b23d6f3a8d9c2e87654321",
  "templateId": "60a12c5e2f7d8b1c98765432",
  "templateVersion": "1.0.0",
  "templateType": "VALIDATION",
  "operatorId": "OP-12345",
  "operatorName": "John Doe",
  "department": "Assembling",
  "process": "Wire Harness Assembly",
  "evaluations": [
    {
      "evaluatorId": "TL-001",
      "evaluatorName": "Jane Smith",
      "evaluatorRole": "TEAM_LEADER",
      "ratings": [
        {
          "criterionId": "crit-001",
          "numericValue": 3,
          "comments": "Good technique with crimping tool"
        },
        {
          "criterionId": "crit-002",
          "numericValue": 2,
          "comments": "Needs to be more consistent with inspection"
        }
      ],
      "comments": "Shows potential but needs more practice on quality checks",
      "timestamp": "2023-02-10T14:30:00Z"
    },
    {
      "evaluatorId": "TR-001",
      "evaluatorName": "Bob Johnson",
      "evaluatorRole": "TRAINER",
      "ratings": [
        {
          "criterionId": "crit-001",
          "numericValue": 3,
          "comments": "Handles tools safely and effectively"
        },
        {
          "criterionId": "crit-002",
          "numericValue": 3,
          "comments": "Good visual inspection habits"
        }
      ],
      "comments": "Good overall performance",
      "timestamp": "2023-02-10T15:45:00Z"
    }
  ],
  "finalScore": 42,
  "percentage": 87.5,
  "passed": true,
  "hasRatingOne": false,
  "retrainingNeeded": false,
  "retrainingCriteria": [],
  "status": "APPROVED",
  "submittedBy": "TL-001",
  "submittedAt": "2023-02-10T16:00:00Z",
  "approvedBy": "TR-001",
  "approvedAt": "2023-02-11T09:15:00Z",
  "comments": "Operator meets validation requirements"
}
```

##### 3.1.3 TemplateHistory Container

Tracks changes to templates for audit purposes.

```typescript
// Template History
interface TemplateHistory {
  _id: ObjectId; // Unique identifier
  templateId: ObjectId; // Reference to template
  version: string; // Version snapshot
  changeType: "CREATE" | "UPDATE" | "DELETE" | "ACTIVATE" | "DEACTIVATE"; // Type of change
  changedBy: string; // User who made the change
  timestamp: Date; // When change occurred
  previousState: any; // Previous state (for updates)
  newState: any; // New state
  comments: string; // Reason for change
}
```

#### 3.2 Data Transfer Objects (DTOs)

##### 3.2.1 Template DTOs

```typescript
// Create Template DTO
interface CreateTemplateDto {
  name: string; // Template name
  description: string; // Template description
  type: "VALIDATION" | "CERTIFICATION" | "IPE"; // Template type
  department: string; // Department
  processes: string[]; // Processes
  skillType: "BASIC" | "KEY"; // Whether this is a basic or key skill
  scoringMethod: "WEIGHTED_AVERAGE"; // Method used for scoring
  categories: CategoryDto[]; // Categories
  minThreshold: number; // Minimum threshold
}

// Category DTO
interface CategoryDto {
  name: string; // Category name
  order: number; // Display order
  criteria: CriterionDto[]; // Criteria
}

// Criterion DTO
interface CriterionDto {
  description: string; // Criterion description
  order: number; // Display order
  ratingDescriptions: {
    // Rating descriptions
    "1": string;
    "2": string;
    "3": string;
    "4": string;
  };
  weight: number; // Weight of this criterion in scoring (default: 1)
  applicableTo: ("VALIDATION" | "CERTIFICATION" | "IPE")[]; // Applicable to
}

// Template Response DTO
interface TemplateResponseDto {
  id: string; // Template ID
  name: string; // Template name
  description: string; // Template description
  type: string; // Template type
  version: string; // Template version
  isActive: boolean; // Is active
  department: string; // Department
  processes: string[]; // Processes
  skillType: "BASIC" | "KEY"; // Whether this is a basic or key skill
  scoringMethod: "WEIGHTED_AVERAGE"; // Method used for scoring
  categories: CategoryResponseDto[]; // Categories
  minThreshold: number; // Minimum threshold
  maxPossibleScore: number; // Maximum possible score
  createdAt: string; // Creation date
  updatedAt: string; // Last update date
}

// Template List Item DTO
interface TemplateListItemDto {
  id: string; // Template ID
  name: string; // Template name
  type: string; // Template type
  version: string; // Template version
  isActive: boolean; // Is active
  department: string; // Department
  processes: string[]; // Processes
  updatedAt: string; // Last update date
}
```

##### 3.2.2 Evaluation DTOs

```typescript
// Submit Evaluation DTO
interface SubmitEvaluationDto {
  templateId: string; // Template ID
  operatorId: string; // Operator ID
  operatorName: string; // Operator name
  ratings: RatingDto[]; // Ratings
  evaluatorRole: "TEAM_LEADER" | "TRAINER"; // Evaluator role
  comments: string; // Comments
}

// Rating DTO
interface RatingDto {
  criterionId: string; // Criterion ID
  numericValue: 1 | 2 | 3 | 4; // Rating value
  comments?: string; // Optional comments
}

// Evaluation Response DTO
interface EvaluationResponseDto {
  id: string; // Evaluation ID
  templateInfo: {
    // Template info
    id: string;
    name: string;
    type: string;
    version: string;
  };
  operatorInfo: {
    // Operator info
    id: string;
    name: string;
  };
  process: string; // Process
  department: string; // Department
  evaluators: {
    // Evaluators
    id: string;
    name: string;
    role: string;
  }[];
  score: {
    // Score info
    raw: number;
    percentage: number;
    passed: boolean;
    hasRatingOne: boolean;
    retrainingNeeded: boolean;
  };
  status: string; // Status
  submittedAt: string; // Submission date
  comments: string; // Comments
}

// Score Summary DTO
interface ScoreSummaryDto {
  raw: number; // Raw score
  percentage: number; // Percentage
  passed: boolean; // Passed flag
  hasRatingOne: boolean; // Has rating of 1
  retrainingNeeded: boolean; // Retraining needed
  retrainingCriteria: {
    // Criteria needing retraining
    id: string;
    description: string;
    rating: number;
  }[];
  breakdown: {
    // Score breakdown
    category: string;
    averageScore: number;
    ratings: {
      criterionId: string;
      description: string;
      rating: number;
    }[];
  }[];
}
```

##### 3.2.3 Scoring DTOs

```typescript
// Calculate Score DTO
interface CalculateScoreDto {
  templateId: string; // Template ID
  ratings: RatingDto[]; // Ratings
}

// Score Response DTO
interface ScoreResponseDto {
  rawScore: number; // Raw score
  maxPossibleScore: number; // Maximum possible score
  percentage: number; // Percentage
  passed: boolean; // Passed flag
  minThreshold: number; // Minimum threshold
  hasRatingOne: boolean; // Has rating of 1
  retrainingNeeded: boolean; // Retraining needed
  retrainingCriteria: {
    // Criteria needing retraining
    id: string;
    description: string;
  }[];
}
```

---

### 4. API Specification

#### 4.1 Template Management API

##### 4.1.1 Create Template

Creates a new template with rating-based criteria.

- **URL**: `/api/templates`
- **Method**: `POST`
- **Auth Required**: Yes (Admin)
- **Request Body**: `CreateTemplateDto`
- **Success Response**:
  - **Code**: 201 CREATED
  - **Content**: `TemplateResponseDto`
- **Error Response**:
  - **Code**: 400 BAD REQUEST
  - **Content**:
  ```json
  {
    "error": "Validation failed",
    "details": [
      "Templates must use WEIGHTED_AVERAGE scoring method",
      "Criterion 'Assembly technique' missing description for rating level 4"
    ]
  }
  ```

##### 4.1.2 Get Templates

Retrieves a list of templates with filtering options.

- **URL**: `/api/templates`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `type`: Template type (VALIDATION, CERTIFICATION, IPE)
  - `department`: Department
  - `process`: Process name (will match any template that includes this process)
  - `isActive`: Active status (true/false)
- **Success Response**:
  - **Code**: 200 OK
  - **Content**: `TemplateListItemDto[]`

##### 4.1.3 Get Template by ID

Retrieves a template by ID.

- **URL**: `/api/templates/{templateId}`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**:
  - `templateId`: ID of the template
- **Success Response**:
  - **Code**: 200 OK
  - **Content**: `TemplateResponseDto`

---

### 5. Business Logic

#### 5.1 Template Management

##### 5.1.1 Template Creation and Validation

1. Template definitions are validated for required fields, structure, and consistency
2. Categories must have at least one criterion
3. Each criterion must have descriptions for all four rating levels
4. All templates use the WEIGHTED_AVERAGE scoring method
5. The template type determines which criteria are included in scoring (validation vs. certification)
6. Template versioning follows semantic versioning (major.minor.patch)
7. When a template is updated, a new version is created and history is tracked

##### 5.1.2 Template Version Management

1. Multiple versions of templates can exist, but only one version can be active at a time
2. Templates can be activated or deactivated
3. Templates can be deprecated but not deleted to maintain historical data integrity
4. Major version changes require administrator approval

#### 5.2 Evaluation Processing

##### 5.2.1 Multi-Evaluator Handling

1. Evaluations can be submitted by Team Leaders or Trainers
2. Multiple evaluations for the same operator/template will be reconciled using a standard method:
   - Use average scores across evaluators for all criteria
   - In case of significant discrepancies (difference > 1 point), flag for review
3. Conflicting evaluations (large discrepancies) are flagged for review

##### 5.2.2 Evaluation Workflow

1. Team Leader submits initial evaluation
2. Trainer reviews and can submit their own evaluation
3. Final score is calculated using the standard averaging method
4. Evaluation is approved or rejected by authorized users
5. Approved evaluations are considered final

#### 5.3 Score Calculation

##### 5.3.1 Standard Calculation Algorithm

##### Weighted Average Method

1. When multiple evaluators are involved:
   - For each criterion, calculate the average rating across all evaluators
   - Flag any criterion with a discrepancy > 1 between evaluators for review
2. Calculate the score using the weighted average method:
   - Count the total number of each rating value (how many 1s, 2s, 3s, and 4s)
   - Multiply each count by its rating value (count of 1s × 1, count of 2s × 2, etc.)
   - Multiply by each criterion's weight (default is 1)
   - Sum these products to get the raw score
   - Divide by the total number of criteria rated to get the average score
3. Calculate the percentage:
   - Divide the raw score by the maximum possible score (total criteria × 4) and multiply by 100
4. Compare against the minimum threshold to determine pass/fail

##### 5.3.2 Special Rule Handling

1. Any criterion rated as '1' triggers a retraining flag, regardless of overall score
2. For validation, the minimum threshold is 80%
3. For certification, the minimum threshold is 85%
4. For IPE, the minimum threshold is 90%
5. If certain criteria are not applicable for a template type, they are excluded from the calculation

#### 5.4 Business Rules Enforcement

1. Validate that all required criteria have ratings
2. Check for invalid rating values (must be 1-4)
3. Enforce department-specific rules based on template configuration
4. For "Flexibilité" and "Productivité" categories:
   - Include in Certification evaluations
   - Exclude from Validation evaluations
5. Mandatory comments for any criteria rated as '1' or '2'

---

### 6. Integration Patterns

#### 6.1 Training Process Integration

The Template Validation microservice integrates with the Training Process microservice to provide templates and scoring for validation/certification processes.

#### 6.2 Versatility Matrix Integration

The Template Validation microservice integrates with the Versatility Matrix to receive status updates based on validation/certification outcomes.

#### 6.3 User Management Integration

The Template Validation microservice integrates with the User Management microservice to authenticate and authorize users (Team Leaders, Trainers, Admins).

---

### 7. Conclusion

The Document Validation microservice is crucial for maintaining the integrity and objectivity of the operator training lifecycle detailed in this document. Its primary responsibilities include managing and providing standardized form templates, executing the necessary scoring logic based on submitted validation data, and efficiently communicating these validation outcomes (such as scores and validation status) back to the Training Process microservice. This interaction enables the Training Process microservice to accurately track an operator's progress and make informed decisions regarding their phase transitions and qualifications. Essentially, within the ecosystem described, the Document Validation microservice functions as a specialized and indispensable partner, handling the critical, document-centric validation and scoring tasks that underpin the overall training program.

## 5.2.4 Versatility Matrix

### 1. Overview

The Versatility Matrix microservice implements a critical visualization and management tool for tracking operator skills and workstation proficiency across manufacturing departments. Based on the functional design specifications, this microservice:

- Maintains real-time qualification status of operators based on updates from the Training Process
- Enables Team Leaders to update polyvalence levels (experience) for operators at specific workstations
- Calculates and displays operator coverage for workstations based on criticality
- Provides filtering, searching, and export capabilities for various stakeholders
- Integrates with multiple data sources through event-driven mechanisms
- Supports compliance and audit requirements through comprehensive logging

The microservice follows cloud-native principles, implementing a CQRS (Command Query Responsibility Segregation) pattern to separate read and write operations, with event-driven integration to maintain loose coupling with other systems.

### 2. Architecture

#### 2.1 Architectural Patterns

The Versatility Matrix implements the following architectural patterns:

1. **Microservices Architecture**:

   - Deployed as an independent service within the Connected Workers ecosystem
   - Well-defined boundaries with specific responsibilities related to operator skills and workstation experience
   - Self-contained with its own data store and processing capabilities
   - Exposes standardized API for other services to consume

2. **CQRS (Command Query Responsibility Segregation)**:

   - **Commands**: Focused on updates to system state
     - `UpdatePolyvalenceCommand`: When Team Leaders update experience levels
     - `SyncQualificationStatusCommand`: When processing qualification events from Training Process
     - `RecalculateWorkstationCoverageCommand`: To update workstation coverage metrics
   - **Queries**: Optimized for different view requirements
     - Matrix views with various filtering and pagination options
     - Workstation coverage and gap analysis
     - Operator detail views with qualification history
   - **Write Model**: Normalized data structure optimized for updates
   - **Read Model**: Denormalized projections optimized for quick retrieval

3. **Event-Driven Architecture**:

   - **Event Consumption**:
     - Consumes events from Training Process for qualification updates via Azure Service Bus
     - Processes Change Feed events from Crew Management for operator data changes
   - **Event Publication**:
     - Exposes its own state changes via Cosmos DB Change Feed
     - Publishes internal domain events for audit logging and real-time UI updates
     - Emits notification events for alerts (e.g., expiring certifications)

4. **Domain-Driven Design**:

   - **Bounded Context**: Versatility Matrix owns the polyvalence domain and consumes qualification status
   - **Aggregates**: Clear aggregate boundaries (Qualification, Polyvalence, Operator)
   - **Value Objects**: Immutable objects like PolyvalenceLevel, QualificationStatus
   - **Domain Services**: Encapsulates complex business logic like workstation coverage calculation

5. **Repository Pattern**:
   - Abstracts data access logic from business logic
   - Enforces aggregate consistency boundaries
   - Provides specialized query methods for read models

#### 2.2 High-Level Component Diagram

```mermaid
graph TB
    subgraph "External Systems"
        HR["HR System"]
        TP["Training Process"]
        MES["Manufacturing Execution System<br>(APRISO)"]
        ME["Manufacturing Engineering"]
    end

    subgraph "Versatility Matrix Service"
        API["API Layer"]
        QS["Qualification Service"]
        PV["Polyvalence Service"]
        AZ["Authorization Service"]
        AS["Analytics Service"]
        REPO["Repository Layer"]

        API --> QS
        API --> PV
        API --> AZ
        API --> AS

        QS --> REPO
        PV --> REPO
        AS --> REPO
    end

    subgraph "Storage"
        DB[(Cosmos DB)]
    end

    subgraph "Event Infrastructure"
        SB["Azure Service Bus"]
        CF["Change Feed Processor"]
        EG["Event Grid"]
    end

    subgraph "Client Applications"
        UI["Web UI"]
        MUI["Mobile UI"]
    end

    %% External System Connections
    HR -- "Operator Data" --> API
    TP -- "Qualification Updates" --> SB
    MES -. "Optional Workstation Time Data" .-> API
    ME -- "Workstation Definitions" --> API

    %% Service Bus Integration
    SB --> API

    %% Data Storage
    REPO --> DB

    %% Change Notification
    DB --> CF
    CF --> EG

    %% Client Connections
    EG --> UI
    EG --> MUI
    UI -- "API Calls" --> API
    MUI -- "API Calls" --> API

    %% Integration with other Connected Workers modules
    RP["Replacement Process"] -- "Query Qualified Operators" --> API

    classDef external fill:#f9f,stroke:#333,stroke-width:2px;
    classDef service fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#bfb,stroke:#333,stroke-width:1px;
    classDef events fill:#fbb,stroke:#333,stroke-width:1px;
    classDef client fill:#bbf,stroke:#333,stroke-width:1px;

    class HR,TP,MES,ME,RP external;
    class API,QS,PV,AZ,AS,REPO service;
    class DB storage;
    class SB,CF,EG events;
    class UI,MUI client;
```

This component diagram illustrates the architecture of the Versatility Matrix service:

1. **External Systems**:

   - HR System provides operator master data
   - Training Process sends qualification updates via events
   - Manufacturing Execution System (APRISO) can optionally provide workstation time data
   - Manufacturing Engineering provides workstation definitions and criticality levels

2. **Versatility Matrix Service**:

   - API Layer handles incoming requests and events
   - Qualification Service manages operator training certifications
   - Polyvalence Service handles experience levels at workstations
   - Authorization Service enforces role-based access control
   - Analytics Service provides coverage analysis and reporting
   - Repository Layer abstracts data access operations

3. **Storage**:

   - Cosmos DB stores all matrix data with document model flexibility

4. **Event Infrastructure**:

   - Azure Service Bus receives external events
   - Change Feed Processor monitors database changes
   - Event Grid distributes real-time updates to clients

5. **Client Applications**:

   - Web UI for desktop access
   - Mobile UI for shop floor access

6. **Integration**:
   - Replacement Process queries the API for finding qualified operators

The architecture follows event-driven principles to ensure real-time data flow and provides multiple integration points for external systems while maintaining a clear separation of concerns within the service.

#### 2.3 Technology Stack

- **Backend Framework**: NestJS with TypeScript
- **Data Storage**: Azure Cosmos DB with SQL API
- **Message Broker**: Azure Service Bus (for consuming Training events)
- **Change Feed**: Azure Cosmos DB Change Feed + Azure Functions
- **Authentication/Authorization**: Azure AD with Role-Based Access Control (RBAC)
- **API Documentation**: OpenAPI/Swagger
- **Monitoring**: Application Insights
- **Deployment**: Azure Kubernetes Service (AKS) or Azure App Service
- **CI/CD**: Azure DevOps Pipelines
- **Secret Management**: Azure Key Vault

#### 2.4 CQRS Implementation

The Versatility Matrix implements a practical CQRS pattern to separate write and read concerns:

##### Command Side (Write Model):

**Commands:**

- `UpdatePolyvalenceCommand`: Issued when a Team Leader updates an operator's polyvalence level

  ```typescript
  interface UpdatePolyvalenceCommand {
    operatorId: string;
    workstationId: string;
    newLevel: "X" | "1" | "2" | "3" | "4" | null;
    notes?: string;
    updatedBy: string; // Team Leader's ID
  }
  ```

- `SyncQualificationStatusCommand`: Issued when processing qualification events from Training Process

  ```typescript
  interface SyncQualificationStatusCommand {
    operatorId: string;
    skillCode: string;
    newStatus: "O" | "LC" | "V" | "C" | "R" | "F" | null;
    certificationDate?: Date;
    expiryDate?: Date;
    sourceEventId: string;
  }
  ```

- `RecalculateWorkstationCoverageCommand`: Issued when qualifications or polyvalence data changes
  ```typescript
  interface RecalculateWorkstationCoverageCommand {
    workstationIds?: string[]; // Optional list of specific workstations, null for all
    departmentId?: string; // Optional department filter
  }
  ```

**Command Handlers:**

- Process commands by accessing and updating the write model
- Apply business rules and validations (e.g., ensure Team Leader can only update polyvalence for their team)
- Publish domain events on successful updates

**Domain Events:**

- `PolyvalenceUpdatedEvent`: When an operator's experience level at a workstation changes
- `QualificationStatusSyncedEvent`: When a qualification status is updated from a Training event
- `WorkstationCoverageChangedEvent`: When the calculated coverage for workstations changes

**Event Flow:**

```
Command → Validation → Command Handler → Write Model Update → Domain Event → Read Model Update
```

##### Query Side (Read Model):

**Queries:**

- `GetMatrixQuery`: Retrieve the matrix view with filtering options

  ```typescript
  interface GetMatrixQuery {
    departmentId: string;
    areaId?: string;
    qualificationStatus?: string[];
    polyvalenceLevel?: string[];
    skillCode?: string[];
    workstationId?: string[];
    operatorId?: string;
    searchTerm?: string;
    page: number;
    pageSize: number;
  }
  ```

- `GetWorkstationCoverageQuery`: Retrieve workstation coverage metrics

  ```typescript
  interface GetWorkstationCoverageQuery {
    departmentId: string;
    areaId?: string;
    criticalityLevel?: string[];
    gapThreshold?: number;
  }
  ```

- `GetOperatorDetailsQuery`: Retrieve detailed information for a specific operator
  ```typescript
  interface GetOperatorDetailsQuery {
    operatorId: string;
    includeHistory?: boolean;
  }
  ```

**Query Handlers:**

- Process queries against optimized read models
- Apply filtering, sorting, and pagination logic
- Map results to DTOs for API responses

**Read Model Projections:**

- Denormalized views stored in dedicated containers
- Optimized for specific query patterns
- Updated asynchronously when write model changes

**Projection Updates:**

- Triggered by domain events or Change Feed processors
- Performed asynchronously to avoid blocking the command side
- Maintains eventual consistency between write and read models

##### Benefits of CQRS for Versatility Matrix:

1. **Performance Optimization**: Read models can be denormalized and optimized for complex queries like matrix views
2. **Scalability**: Read and write sides can scale independently based on workload
3. **Query Flexibility**: Supports complex queries needed for filtering, reporting, and analysis
4. **Business Logic Isolation**: Command handlers enforce business rules without impacting query performance
5. **Event Sourcing Compatibility**: Facilitates audit logging and historical analysis

#### 2.5 Microservices Breakdown

The Versatility Matrix functions as a single microservice with clear boundaries:

1. **Responsibilities**:

   - Manage and display qualification status (sourced from Training Process)
   - Manage and display polyvalence levels (managed by Team Leaders)
   - Calculate and display workstation coverage metrics
   - Provide filterable, searchable matrix views
   - Maintain audit logs for compliance

2. **Dependencies**:

   - **Inbound**: Consumes events from Training Process
   - **Outbound**: Exposes data via API and Change Feed for consumers

3. **Isolation**:
   - Clear domain boundaries
   - Independent deployment
   - Autonomous scaling
   - Dedicated data storage

---

### 3. Detailed Component Design

The Versatility Matrix microservice is implemented using NestJS with TypeScript, following a modular architecture.

#### 3.1 API Layer

The API layer exposes RESTful endpoints through NestJS controllers.

##### 3.1.1 Controllers

```typescript
// Matrix controller handles matrix data retrieval with filtering options
@Controller("api/matrix")
export class MatrixController {
  @Get()
  async getMatrix(
    @Query() queryParams: GetMatrixQueryDto
  ): Promise<MatrixViewDto> {
    // Implementation leveraging the query handler
  }

  @Get("export")
  @Header("Content-Type", "application/octet-stream")
  async exportMatrix(
    @Query() queryParams: ExportMatrixQueryDto
  ): Promise<StreamableFile> {
    // Implementation for data export
  }
}

// Polyvalence controller handles polyvalence updates by Team Leaders
@Controller("api/polyvalence")
export class PolyvalenceController {
  @Put(":operatorId/:workstationId")
  @Roles(Role.TEAM_LEADER)
  async updatePolyvalence(
    @Param("operatorId") operatorId: string,
    @Param("workstationId") workstationId: string,
    @Body() updateDto: UpdatePolyvalenceDto,
    @CurrentUser() user: UserInfo
  ): Promise<PolyvalenceDto> {
    // Implementation leveraging the command handler
  }

  @Get("history/:operatorId")
  async getPolyvalenceHistory(
    @Param("operatorId") operatorId: string
  ): Promise<PolyvalenceHistoryDto> {
    // Implementation for history retrieval
  }
}

// Additional controllers for qualification, workstation, and reporting functionality
```

##### 3.1.2 NestJS Middleware and Guards

```typescript
// Authorization guard for RBAC
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Implementation checking user roles against required roles
  }
}

// Audit logging interceptor
@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Implementation for logging API calls
  }
}
```

#### 3.2 Domain Layer

The domain layer implements the core business logic using NestJS services and CQRS pattern.

##### 3.2.1 Services

```typescript
// Core service implementations
@Injectable()
export class PolyvalenceService {
  constructor(
    @InjectRepository(PolyvalenceRepository)
    private polyvalenceRepository: PolyvalenceRepository,
    private eventPublisher: EventPublisher
  ) {}

  async updatePolyvalence(
    command: UpdatePolyvalenceCommand
  ): Promise<Polyvalence> {
    // Business logic for updating polyvalence
    // Publish domain events
  }
}

@Injectable()
export class QualificationService {
  constructor(
    @InjectRepository(QualificationRepository)
    private qualificationRepository: QualificationRepository,
    private eventPublisher: EventPublisher
  ) {}

  async syncQualificationStatus(
    command: SyncQualificationStatusCommand
  ): Promise<Qualification> {
    // Business logic for syncing qualification status
    // Publish domain events
  }
}

@Injectable()
export class WorkstationCoverageService {
  constructor(
    @InjectRepository(WorkstationRepository)
    private workstationRepository: WorkstationRepository,
    @InjectRepository(QualificationRepository)
    private qualificationRepository: QualificationRepository,
    @InjectRepository(PolyvalenceRepository)
    private polyvalenceRepository: PolyvalenceRepository
  ) {}

  async calculateCoverage(workstationId: string): Promise<WorkstationCoverage> {
    // Implementation of coverage calculation logic
  }
}
```

##### 3.2.2 Command Handlers

```typescript
// CQRS command handlers
@CommandHandler(UpdatePolyvalenceCommand)
export class UpdatePolyvalenceCommandHandler
  implements ICommandHandler<UpdatePolyvalenceCommand>
{
  constructor(private polyvalenceService: PolyvalenceService) {}

  async execute(command: UpdatePolyvalenceCommand): Promise<Polyvalence> {
    // Command validation and execution
    return this.polyvalenceService.updatePolyvalence(command);
  }
}

@CommandHandler(SyncQualificationStatusCommand)
export class SyncQualificationStatusCommandHandler
  implements ICommandHandler<SyncQualificationStatusCommand>
{
  constructor(private qualificationService: QualificationService) {}

  async execute(
    command: SyncQualificationStatusCommand
  ): Promise<Qualification> {
    // Command validation and execution
    return this.qualificationService.syncQualificationStatus(command);
  }
}
```

##### 3.2.3 Query Handlers

```typescript
// CQRS query handlers
@QueryHandler(GetMatrixQuery)
export class GetMatrixQueryHandler implements IQueryHandler<GetMatrixQuery> {
  constructor(
    private readonly matrixProjectionRepository: MatrixProjectionRepository
  ) {}

  async execute(query: GetMatrixQuery): Promise<MatrixView> {
    // Implementation for retrieving and filtering matrix data
  }
}

@QueryHandler(GetWorkstationCoverageQuery)
export class GetWorkstationCoverageQueryHandler
  implements IQueryHandler<GetWorkstationCoverageQuery>
{
  constructor(
    private readonly workstationCoverageProjectionRepository: WorkstationCoverageProjectionRepository
  ) {}

  async execute(
    query: GetWorkstationCoverageQuery
  ): Promise<WorkstationCoverageView> {
    // Implementation for retrieving workstation coverage metrics
  }
}
```

##### 3.2.4 Domain Events

```typescript
// Domain events using NestJS event publisher
export class PolyvalenceUpdatedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly workstationId: string,
    public readonly oldLevel: PolyvalenceLevel | null,
    public readonly newLevel: PolyvalenceLevel | null,
    public readonly updatedBy: string,
    public readonly timestamp: Date
  ) {}
}

export class QualificationStatusUpdatedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly skillCode: string,
    public readonly oldStatus: QualificationStatus | null,
    public readonly newStatus: QualificationStatus | null,
    public readonly certificationDate: Date | null,
    public readonly expiryDate: Date | null,
    public readonly sourceEventId: string | null,
    public readonly timestamp: Date
  ) {}
}
```

#### 3.3 Infrastructure Layer

The infrastructure layer handles external integrations and technical concerns using NestJS providers.

##### 3.3.1 Event Handlers

```typescript
// Azure Service Bus handlers
@Injectable()
export class TrainingStatusChangedEventHandler {
  constructor(private commandBus: CommandBus) {}

  @ServiceBusListener("training-status-events", "TrainingStatusChanged")
  async handleTrainingStatusChanged(
    message: TrainingStatusChangedEventDto
  ): Promise<void> {
    // Transform event to command and dispatch
    const command = new SyncQualificationStatusCommand(
      message.operatorId,
      message.processId,
      message.newStatus,
      message.certificationDate,
      message.certificationExpiry,
      message.eventId
    );
    await this.commandBus.execute(command);
  }
}
```

##### 3.3.2 Cosmos DB Repositories

```typescript
// TypeORM repositories for Cosmos DB
@Injectable()
export class QualificationRepository {
  constructor(
    @InjectConnection()
    private connection: Connection
  ) {}

  async findByOperatorAndSkill(
    operatorId: string,
    skillCode: string
  ): Promise<Qualification | undefined> {
    // Implementation using Cosmos DB SQL API
  }

  async save(qualification: Qualification): Promise<Qualification> {
    // Implementation for saving to Cosmos DB
  }

  // Additional repository methods
}

@Injectable()
export class PolyvalenceRepository {
  constructor(
    @InjectConnection()
    private connection: Connection
  ) {}

  async findByOperatorAndWorkstation(
    operatorId: string,
    workstationId: string
  ): Promise<Polyvalence | undefined> {
    // Implementation using Cosmos DB SQL API
  }

  async save(polyvalence: Polyvalence): Promise<Polyvalence> {
    // Implementation for saving to Cosmos DB
  }

  // Additional repository methods
}
```

##### 3.3.3 Change Feed Processors

```typescript
// Azure Function implementation for Cosmos DB Change Feed processing
export class MatrixChangeFeedProcessor {
  constructor(
    private cosmosClient: CosmosClient,
    private serviceBusClient: ServiceBusClient
  ) {}

  @Function("ProcessQualificationChanges")
  async processQualificationChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "CosmosDBConnection",
      databaseName: "VersatilityMatrix",
      containerName: "qualifications",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Implementation for processing qualification changes
    // Publish events to relevant consumers
  }

  @Function("ProcessPolyvalenceChanges")
  async processPolyvalenceChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "CosmosDBConnection",
      databaseName: "VersatilityMatrix",
      containerName: "polyvalence",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Implementation for processing polyvalence changes
    // Publish events to relevant consumers
  }
}
```

#### 3.4 NestJS Module Structure

The microservice is organized into NestJS modules for better code organization and dependency management:

```typescript
// Main module
@Module({
  imports: [
    ConfigModule.forRoot(),
    CosmosDbModule.forRoot({
      /* Cosmos DB connection config */
    }),
    ServiceBusModule.forRoot({
      /* Service Bus connection config */
    }),
    CqrsModule,
    OperatorsModule,
    QualificationsModule,
    PolyvalenceModule,
    WorkstationsModule,
    ReportsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

// Feature module example
@Module({
  imports: [CqrsModule, CosmosDbModule],
  controllers: [PolyvalenceController],
  providers: [
    PolyvalenceService,
    PolyvalenceRepository,
    UpdatePolyvalenceCommandHandler,
    GetPolyvalenceHistoryQueryHandler,
    // Event handlers, etc.
  ],
  exports: [PolyvalenceService],
})
export class PolyvalenceModule {}
```

---

### 4. Data Models

The Versatility Matrix implements a domain-driven data model with clear separation between write models and read models.

#### 4.1 Write Models (Command Side)

##### 4.1.1 Operator

```typescript
interface Operator {
  id: string; // Unique operator ID
  employeeNumber?: string; // Optional employee number from HR system
  name: string; // Full name of the operator
  hiringDate: Date; // Date when operator was hired
  areaId: string; // ID of the area/team the operator belongs to
  departmentId: string; // ID of the department (Assembling, Cutting & Lead Prep)
  isActive: boolean; // Whether the operator is active
  lastUpdated: Date; // Last update timestamp
}
```

##### 4.1.2 Area

```typescript
interface Area {
  id: string; // Unique area/team ID
  name: string; // Name of the area/team
  departmentId: string; // ID of the department this area belongs to
  description?: string; // Optional description
}
```

##### 4.1.3 Department

```typescript
interface Department {
  id: string; // Unique department ID
  name: string; // Name of the department (Assembling, Cutting & Lead Prep)
  description?: string; // Optional description
}
```

##### 4.1.4 Skill

```typescript
interface Skill {
  code: string; // Unique skill code (e.g., USW, TQ)
  name: string; // Readable name of the skill
  description?: string; // Optional detailed description
  complianceFlag?: string; // Optional industry standard reference (e.g., IPC/WHMA-A-620)
  departmentId: string; // ID of the department this skill belongs to
}
```

##### 4.1.5 Workstation

```typescript
interface Workstation {
  id: string; // Unique workstation ID
  name: string; // Name of the workstation
  departmentId: string; // ID of the department this workstation belongs to
  areaId?: string; // Optional ID of the area this workstation belongs to
  criticalityLevel: "Critical" | "Medium" | "Normal"; // Criticality level
  targetBackupOperators: number; // Target number of backup operators based on criticality
  requiredSkills: string[]; // List of required skill codes
}
```

##### 4.1.6 Qualification

```typescript
interface Qualification {
  id: string; // Unique ID for this qualification record (for change tracking)
  operatorId: string; // ID of the operator
  skillCode: string; // Code of the skill
  status: "O" | "LC" | "V" | "C" | "R" | "F" | null; // Current qualification status
  statusDescription: {
    // Human-readable descriptions for UI display
    O: "On Job Training";
    LC: "Learning Curve";
    V: "Validated";
    C: "Certified";
    R: "Re-certified";
    F: "Formé (Trained)";
  };
  certificationDate?: Date; // Date of certification (for C/R statuses)
  expiryDate?: Date; // Expiry date of certification (for C/R statuses)
  lastUpdated: Date; // Last update timestamp
  updatedBy: string; // ID of the user who updated this (or system ID for automated updates)
  sourceEventId?: string; // ID of the source event from Training Process (for traceability)
  historyEntries?: QualificationHistoryEntry[]; // Optional embedded history entries
  isActive: boolean; // Whether this qualification is currently active
}

interface QualificationHistoryEntry {
  id: string; // Unique ID for this history entry
  timestamp: Date; // When the change occurred
  previousStatus?: "O" | "LC" | "V" | "C" | "R" | "F" | null; // Previous status
  newStatus: "O" | "LC" | "V" | "C" | "R" | "F" | null; // New status
  updatedBy: string; // Who made the change
  sourceEventId?: string; // ID of the source event
  notes?: string; // Optional notes about the change
}
```

##### 4.1.7 Polyvalence

```typescript
interface Polyvalence {
  id: string; // Unique ID for this polyvalence record (for change tracking)
  operatorId: string; // ID of the operator
  workstationId: string; // ID of the workstation
  level: "X" | "1" | "2" | "3" | "4" | null; // Current polyvalence level
  levelDescription: {
    // Human-readable descriptions for UI display
    X: "Owner (Primary Operator)";
    "1": "Less than 1 month experience";
    "2": "1-2 months experience";
    "3": "2-6 months experience";
    "4": "More than 6 months experience";
  };
  lastUpdated: Date; // Last update timestamp
  updatedBy: string; // ID of the user who updated this (Team Leader)
  notes?: string; // Optional notes about the update
  historyEntries?: PolyvalenceHistoryEntry[]; // Optional embedded history entries
  isActive: boolean; // Whether this polyvalence assignment is active
}

interface PolyvalenceHistoryEntry {
  id: string; // Unique ID for this history entry
  timestamp: Date; // When the change occurred
  previousLevel?: "X" | "1" | "2" | "3" | "4" | null; // Previous level
  newLevel: "X" | "1" | "2" | "3" | "4" | null; // New level
  updatedBy: string; // Who made the change
  notes?: string; // Optional notes about the change
}
```

##### 4.1.8 AuditLog

```typescript
interface AuditLog {
  id: string; // Unique audit log ID
  timestamp: Date; // Timestamp of the action
  userId: string; // ID of the user who performed the action
  action: string; // Description of the action
  entityType: string; // Type of entity affected (Qualification, Polyvalence, etc.)
  entityId: string; // ID of the entity affected
  oldValue?: string; // Optional previous value
  newValue?: string; // Optional new value
  ipAddress?: string; // Optional IP address of the user
  userAgent?: string; // Optional user agent information
}
```

#### 4.2 Read Models (Query Side)

##### 4.2.1 MatrixView

```typescript
interface MatrixView {
  operators: OperatorView[]; // List of operators with their details
  skills: SkillView[]; // List of skills for qualification columns
  workstations: WorkstationView[]; // List of workstations for polyvalence columns
  qualifications: QualificationView[]; // Flattened list of all qualifications
  polyvalence: PolyvalenceView[]; // Flattened list of all polyvalence levels
  metadata: {
    departmentId: string; // Current department filter
    areaId?: string; // Optional area filter
    lastUpdated: Date; // Last update timestamp
  };
}

interface OperatorView {
  id: string; // Operator ID
  name: string; // Operator name
  hiringDate: Date; // Hiring date
  areaName: string; // Area/team name
}

interface SkillView {
  code: string; // Skill code
  name: string; // Skill name
  description?: string; // Optional description
}

interface WorkstationView {
  id: string; // Workstation ID
  name: string; // Workstation name
  criticalityLevel: string; // Criticality level
  targetBackupOperators: number; // Target number of backup operators
  actualBackupOperators: number; // Actual number of qualified backup operators
  requiredSkills: string[]; // List of required skill codes
}

interface QualificationView {
  operatorId: string; // Operator ID
  skillCode: string; // Skill code
  status: string; // Qualification status (O, LC, V, C, R, F)
  certificationDate?: Date; // Certification date
  expiryDate?: Date; // Expiry date
}

interface PolyvalenceView {
  operatorId: string; // Operator ID
  workstationId: string; // Workstation ID
  level: string; // Polyvalence level (X, 1, 2, 3, 4)
  lastUpdated: Date; // Last update timestamp
}
```

##### 4.2.2 WorkstationCoverageView

```typescript
interface WorkstationCoverageView {
  workstations: WorkstationCoverage[]; // List of workstations with coverage details
  metadata: {
    departmentId: string; // Current department filter
    areaId?: string; // Optional area filter
    lastUpdated: Date; // Last update timestamp
    totalWorkstations: number; // Total number of workstations
    criticalWorkstations: number; // Number of critical workstations
    workstationsWithGaps: number; // Number of workstations with coverage gaps
  };
}

interface WorkstationCoverage {
  id: string; // Workstation ID
  name: string; // Workstation name
  criticalityLevel: "Critical" | "Medium" | "Normal"; // Criticality level
  targetBackupOperators: number; // Target number of backup operators based on criticality (3, 1, or 0)
  actualBackupOperators: number; // Actual number of qualified backup operators
  gap: number; // Target - Actual (negative means excess, positive means deficit)
  coveragePercentage: number; // Percentage of target that's covered (100+ means fully covered)
  requiredSkills: {
    // Skills required for this workstation
    code: string;
    name: string;
  }[];
  owner?: OperatorBasic; // Primary operator (X level)
  qualifiedOperators: OperatorBasic[]; // List of qualified backup operators (C/R status with any polyvalence)

  /*
   * Calculation Logic (based on FDS 3.a.2 Criticality Targets & Actuals):
   * - Critical Workstations: Target = 3 backup operators (C/R qualified)
   * - Medium Criticality: Target = 1 backup operator (C/R qualified)
   * - Normal Criticality: Target = 0 (no additional operators needed)
   *
   * Actual Value: Count of operators who:
   * 1. Have C or R qualification status for ALL required skills
   * 2. Have ANY polyvalence level (1-4 or X) at this workstation
   * 3. If owner (X) has C/R qualification, they count; if not, they're excluded
   */
}

interface OperatorBasic {
  id: string; // Operator ID
  name: string; // Operator name
  polyvalenceLevel?: string; // Optional polyvalence level for this workstation
  qualificationStatus: string; // Qualification status for the required skill
}
```

##### 4.2.3 OperatorDetailView

```typescript
interface OperatorDetailView {
  id: string; // Operator ID
  employeeNumber?: string; // Optional employee number
  name: string; // Operator name
  hiringDate: Date; // Hiring date
  area: {
    id: string; // Area ID
    name: string; // Area name
    department: {
      id: string; // Department ID
      name: string; // Department name
    };
  };
  qualifications: QualificationDetail[]; // List of qualification details
  polyvalence: PolyvalenceDetail[]; // List of polyvalence details
  statistics: {
    totalQualifications: number; // Total number of qualifications
    certifiedQualifications: number; // Number of C/R qualifications
    ownedWorkstations: number; // Number of workstations with X level
    averagePolyvalenceLevel: number; // Average polyvalence level (excluding X)
  };
}

interface QualificationDetail {
  skillCode: string; // Skill code
  skillName: string; // Skill name
  status: string; // Qualification status
  certificationDate?: Date; // Certification date
  expiryDate?: Date; // Expiry date
  daysUntilExpiry?: number; // Days until expiry (if applicable)
}

interface PolyvalenceDetail {
  workstationId: string; // Workstation ID
  workstationName: string; // Workstation name
  level: string; // Polyvalence level
  criticalityLevel: string; // Workstation criticality level
  lastUpdated: Date; // Last update timestamp
  updatedBy: string; // Name of the user who last updated this
}
```

#### 4.3 Cosmos DB Container Structure

The Versatility Matrix microservice uses the following Azure Cosmos DB containers optimized for the specific query patterns and entity relationships:

```typescript
// Container definitions with partition keys
interface CosmosContainer {
  name: string;
  partitionKey: string;
  purpose: string;
  ttl?: boolean; // Time-to-live enabled
}

const containers: CosmosContainer[] = [
  {
    name: "operators",
    partitionKey: "/departmentId",
    purpose: "Stores operator data with embedded area information",
  },
  {
    name: "skills",
    partitionKey: "/departmentId",
    purpose: "Stores skill definitions including compliance information",
  },
  {
    name: "workstations",
    partitionKey: "/departmentId",
    purpose:
      "Stores workstation definitions with required skills and criticality",
  },
  {
    name: "qualifications",
    partitionKey: "/operatorId",
    purpose: "Stores qualification status records with certification dates",
  },
  {
    name: "qualification-history",
    partitionKey: "/operatorId",
    purpose: "Stores historical qualification status changes",
    ttl: true, // Optional TTL for older records
  },
  {
    name: "polyvalence",
    partitionKey: "/operatorId",
    purpose: "Stores polyvalence level records assigned by Team Leaders",
  },
  {
    name: "polyvalence-history",
    partitionKey: "/operatorId",
    purpose: "Stores historical polyvalence level changes",
    ttl: true, // Optional TTL for older records
  },
  {
    name: "workstation-coverage",
    partitionKey: "/departmentId",
    purpose: "Stores pre-computed workstation coverage metrics",
  },
  {
    name: "audit-logs",
    partitionKey: "/entityType",
    purpose: "Stores audit log entries for compliance tracking",
    ttl: true, // TTL based on retention policy
  },
];
```

#### 4.4 Entity Relationships

The domain entities are related as follows:

```typescript
// Entity relationship diagram (pseudocode)
interface EntityRelationships {
  Operator: {
    belongsTo: ["Department", "Area"];
    hasMany: ["Qualification", "Polyvalence"];
  };

  Department: {
    hasMany: ["Area", "Skill", "Workstation", "Operator"];
  };

  Area: {
    belongsTo: ["Department"];
    hasMany: ["Operator", "Workstation"];
  };

  Skill: {
    belongsTo: ["Department"];
    hasMany: ["Qualification"];
    requiredBy: ["Workstation"];
  };

  Workstation: {
    belongsTo: ["Department", "Area?"]; // Optional area
    requires: ["Skill"]; // Many-to-many
    hasMany: ["Polyvalence"];
    hasOne: ["WorkstationCoverage"];
  };

  Qualification: {
    belongsTo: ["Operator", "Skill"];
    hasMany: ["QualificationHistoryEntry"];
  };

  Polyvalence: {
    belongsTo: ["Operator", "Workstation"];
    hasMany: ["PolyvalenceHistoryEntry"];
  };
}
```

##### 4.4.1 Domain Model Class Diagram

The following class diagram visualizes the domain model and entity relationships:

```mermaid
classDiagram
    class Operator {
        +string id
        +string employeeNumber
        +string name
        +Date hiringDate
        +string areaId
        +string departmentId
        +boolean isActive
        +Date lastUpdated
    }

    class Department {
        +string id
        +string name
        +string description
    }

    class Area {
        +string id
        +string name
        +string departmentId
        +string description
    }

    class Skill {
        +string code
        +string name
        +string description
        +string complianceFlag
        +string departmentId
    }

    class Workstation {
        +string id
        +string name
        +string departmentId
        +string areaId
        +CriticalityLevel criticalityLevel
        +number targetBackupOperators
        +string[] requiredSkills
    }

    class Qualification {
        +string id
        +string operatorId
        +string skillCode
        +QualificationStatus status
        +Date certificationDate
        +Date expiryDate
        +Date lastUpdated
        +string updatedBy
        +string sourceEventId
        +boolean isActive
    }

    class QualificationHistoryEntry {
        +string id
        +Date timestamp
        +QualificationStatus previousStatus
        +QualificationStatus newStatus
        +string updatedBy
        +string sourceEventId
        +string notes
    }

    class Polyvalence {
        +string id
        +string operatorId
        +string workstationId
        +PolyvalenceLevel level
        +Date lastUpdated
        +string updatedBy
        +string notes
        +boolean isActive
    }

    class PolyvalenceHistoryEntry {
        +string id
        +Date timestamp
        +PolyvalenceLevel previousLevel
        +PolyvalenceLevel newLevel
        +string updatedBy
        +string notes
    }

    class WorkstationCoverage {
        +string workstationId
        +number targetBackupOperators
        +number actualBackupOperators
        +number gap
        +Date lastCalculated
    }

    class AuditLog {
        +string id
        +Date timestamp
        +string userId
        +string action
        +string entityType
        +string entityId
        +string oldValue
        +string newValue
    }

    %% Relationships
    Department "1" -- "many" Area : contains
    Department "1" -- "many" Operator : employs
    Department "1" -- "many" Skill : defines
    Department "1" -- "many" Workstation : defines

    Area "1" -- "many" Operator : groups
    Area "1" -- "many" Workstation : contains

    Operator "1" -- "many" Qualification : has
    Operator "1" -- "many" Polyvalence : has

    Skill "1" -- "many" Qualification : defines
    Skill "many" -- "many" Workstation : required by

    Workstation "1" -- "many" Polyvalence : assigned to
    Workstation "1" -- "1" WorkstationCoverage : tracks

    Qualification "1" -- "many" QualificationHistoryEntry : tracks
    Polyvalence "1" -- "many" PolyvalenceHistoryEntry : tracks

    %% Enums
    class QualificationStatus {
        <<enumeration>>
        O
        LC
        V
        C
        R
        F
    }

    class PolyvalenceLevel {
        <<enumeration>>
        X
        1
        2
        3
        4
    }

    class CriticalityLevel {
        <<enumeration>>
        Critical
        Medium
        Normal
    }
```

#### 4.5 Data Access Patterns

The microservice implements the following data access patterns to optimize performance with Cosmos DB:

```typescript
// Common query patterns and their implementation
interface DataAccessPattern {
  pattern: string;
  implementation: string;
  container: string;
  indexing: string;
}

const dataAccessPatterns: DataAccessPattern[] = [
  {
    pattern: "Get matrix view with filtering",
    implementation: "Query pre-computed projections with filter conditions",
    container: "workstation-coverage",
    indexing: "Composite indexes on departmentId, areaId, criticalityLevel",
  },
  {
    pattern: "Find qualification status for operator/skill",
    implementation:
      "Point read using partition key (operatorId) and id pattern (skillCode)",
    container: "qualifications",
    indexing: "Standard partition key indexing",
  },
  {
    pattern: "Update polyvalence level",
    implementation: "Upsert with optimistic concurrency via ETag",
    container: "polyvalence",
    indexing: "Standard partition key indexing",
  },
  {
    pattern: "Calculate workstation coverage",
    implementation: "Cross-container query with materialized view refresh",
    container: "qualifications, polyvalence, workstations",
    indexing: "Composite indexes on required skills and status",
  },
  {
    pattern: "Find qualified operators for workstation",
    implementation: "Query using required skills and status filters",
    container: "qualifications, polyvalence",
    indexing: "Composite indexes on skill, status, and workstationId",
  },
];
```

#### 4.6 Change Feed Processing Strategy

The Change Feed processing is implemented to maintain consistency across read models and integrate with external systems:

```typescript
// Change Feed processor definitions
interface ChangeFeedProcessor {
  sourceName: string; // Source container
  triggerName: string; // Azure Function trigger name
  purpose: string;
  targets: string[]; // Target systems/containers
}

const changeFeedProcessors: ChangeFeedProcessor[] = [
  {
    sourceName: "qualifications",
    triggerName: "ProcessQualificationChanges",
    purpose: "Update workstation coverage metrics when qualifications change",
    targets: ["workstation-coverage", "Training Process Read Models"],
  },
  {
    sourceName: "polyvalence",
    triggerName: "ProcessPolyvalenceChanges",
    purpose:
      "Update workstation coverage metrics when polyvalence levels change",
    targets: ["workstation-coverage", "Training Process Read Models"],
  },
  {
    sourceName: "workstations",
    triggerName: "ProcessWorkstationChanges",
    purpose: "Recalculate coverage when workstations or required skills change",
    targets: ["workstation-coverage"],
  },
  {
    sourceName: "operators",
    triggerName: "ProcessOperatorChanges",
    purpose: "Update operator information in projections",
    targets: ["workstation-coverage"],
  },
];
```

---

### 5. API Specification

#### 5.1 REST Endpoints

##### 5.1.1 Matrix API

```
GET /api/matrix
```

Retrieves the versatility matrix data with optional filtering.

**Query Parameters:**

- `departmentId` (required): Department ID to filter by
- `areaId` (optional): Area/team ID to filter by
- `skillCode` (optional): Skill code to filter by
- `workstationId` (optional): Workstation ID to filter by
- `qualificationStatus` (optional): Qualification status to filter by (O, LC, V, C, R, F)
- `polyvalenceLevel` (optional): Polyvalence level to filter by (X, 1, 2, 3, 4)
- `operatorId` (optional): Operator ID to filter by
- `searchTerm` (optional): Search term to filter by (matches operator name, workstation name, etc.)
- `page` (optional): Page number for pagination (default: 1)
- `pageSize` (optional): Page size for pagination (default: 50)

**Response:**

```json
{
  "data": {
    "operators": [
      {
        "id": "op-123",
        "name": "John Doe",
        "hiringDate": "2020-01-01T00:00:00Z",
        "areaName": "Team A"
      }
      // Additional operators...
    ],
    "skills": [
      {
        "code": "USW",
        "name": "Ultrasonic Splice",
        "description": "Joining wires via high-frequency vibrations"
      }
      // Additional skills...
    ],
    "workstations": [
      {
        "id": "WS-TQ01",
        "name": "Torque Station 1",
        "criticalityLevel": "Critical",
        "targetBackupOperators": 3,
        "actualBackupOperators": 2,
        "requiredSkills": ["TQ"]
      }
      // Additional workstations...
    ],
    "qualifications": [
      {
        "operatorId": "op-123",
        "skillCode": "USW",
        "status": "C",
        "certificationDate": "2022-05-15T00:00:00Z",
        "expiryDate": "2023-05-15T00:00:00Z"
      }
      // Additional qualifications...
    ],
    "polyvalence": [
      {
        "operatorId": "op-123",
        "workstationId": "WS-TQ01",
        "level": "3",
        "lastUpdated": "2023-01-15T10:30:00Z"
      }
      // Additional polyvalence entries...
    ]
  },
  "metadata": {
    "departmentId": "dept-assy",
    "areaId": "area-team-a",
    "totalOperators": 120,
    "displayedOperators": 50,
    "page": 1,
    "pageSize": 50,
    "totalPages": 3,
    "lastUpdated": "2023-08-15T14:30:00Z"
  }
}
```

```
GET /api/matrix/export
```

Exports the matrix data to Excel or CSV format.

**Query Parameters:**

- Same as `/api/matrix` plus:
- `format` (required): Export format (excel, csv)

**Response:**

- Binary file download with appropriate MIME type

##### 5.1.2 Polyvalence API

```
PUT /api/polyvalence/{operatorId}/{workstationId}
```

Updates the polyvalence level for an operator at a specific workstation.

**URL Parameters:**

- `operatorId` (required): Operator ID
- `workstationId` (required): Workstation ID

**Request Body:**

```json
{
  "level": "3", // X, 1, 2, 3, 4, or null (to remove)
  "notes": "Operator completed 3 months at this station" // Optional notes
}
```

**Response:**

```json
{
  "operatorId": "op-123",
  "workstationId": "WS-TQ01",
  "level": "3",
  "lastUpdated": "2023-08-15T14:35:00Z",
  "updatedBy": "tl-user-456"
}
```

```
GET /api/polyvalence/history/{operatorId}
```

Retrieves the polyvalence update history for an operator.

**URL Parameters:**

- `operatorId` (required): Operator ID

**Response:**

```json
{
  "operatorId": "op-123",
  "name": "John Doe",
  "history": [
    {
      "workstationId": "WS-TQ01",
      "workstationName": "Torque Station 1",
      "oldLevel": "2",
      "newLevel": "3",
      "updatedBy": "TL User Name",
      "timestamp": "2023-08-15T14:35:00Z"
    }
    // Additional history entries...
  ]
}
```

##### 5.1.3 Qualification API

```
GET /api/qualification/{operatorId}
```

Retrieves the qualifications for a specific operator.

**URL Parameters:**

- `operatorId` (required): Operator ID

**Response:**

```json
{
  "operatorId": "op-123",
  "name": "John Doe",
  "qualifications": [
    {
      "skillCode": "USW",
      "skillName": "Ultrasonic Splice",
      "status": "C",
      "certificationDate": "2022-05-15T00:00:00Z",
      "expiryDate": "2023-05-15T00:00:00Z",
      "daysUntilExpiry": 45
    }
    // Additional qualifications...
  ]
}
```

```
GET /api/qualification/expiring
```

Retrieves certifications that are nearing expiry.

**Query Parameters:**

- `departmentId` (required): Department ID
- `areaId` (optional): Area ID
- `daysThreshold` (optional): Days threshold for expiry (default: 30)

**Response:**

```json
{
  "expiringCertifications": [
    {
      "operatorId": "op-123",
      "operatorName": "John Doe",
      "skillCode": "USW",
      "skillName": "Ultrasonic Splice",
      "expiryDate": "2023-05-15T00:00:00Z",
      "daysUntilExpiry": 12
    }
    // Additional expiring certifications...
  ]
}
```

##### 5.1.4 Workstation API

```
GET /api/workstation/coverage
```

Retrieves coverage metrics for workstations.

**Query Parameters:**

- `departmentId` (required): Department ID
- `areaId` (optional): Area ID
- `criticalityLevel` (optional): Filter by criticality level (Critical, Medium, Normal)
- `gapThreshold` (optional): Filter by coverage gap (positive number)

**Response:**

```json
{
  "workstations": [
    {
      "id": "WS-TQ01",
      "name": "Torque Station 1",
      "criticalityLevel": "Critical",
      "targetBackupOperators": 3,
      "actualBackupOperators": 2,
      "gap": 1,
      "owner": {
        "id": "op-456",
        "name": "Jane Smith",
        "polyvalenceLevel": "X",
        "qualificationStatus": "C"
      },
      "qualifiedOperators": [
        {
          "id": "op-123",
          "name": "John Doe",
          "polyvalenceLevel": "3",
          "qualificationStatus": "C"
        },
        {
          "id": "op-789",
          "name": "Alice Johnson",
          "polyvalenceLevel": "2",
          "qualificationStatus": "R"
        }
      ]
    }
    // Additional workstations...
  ],
  "metadata": {
    "departmentId": "dept-assy",
    "areaId": "area-team-a",
    "totalWorkstations": 25,
    "criticalWorkstations": 8,
    "workstationsWithGaps": 3
  }
}
```

```
GET /api/workstation/{workstationId}/operators
```

Retrieves qualified operators for a specific workstation.

**URL Parameters:**

- `workstationId` (required): Workstation ID

**Response:**

```json
{
  "workstationId": "WS-TQ01",
  "workstationName": "Torque Station 1",
  "criticalityLevel": "Critical",
  "requiredSkills": ["TQ"],
  "operators": [
    {
      "id": "op-456",
      "name": "Jane Smith",
      "polyvalenceLevel": "X",
      "qualificationStatus": "C",
      "certificationExpiryDate": "2024-03-15T00:00:00Z"
    }
    // Additional operators...
  ]
}
```

##### 5.1.5 Report API

```
GET /api/reports/skill-certification
```

Generates a skill certification history report.

**Query Parameters:**

- `departmentId` (optional): Department ID
- `areaId` (optional): Area ID
- `operatorId` (optional): Operator ID
- `skillCode` (optional): Skill code
- `format` (optional): Report format (json, excel, csv) - default: json

**Response:**
Report data in the requested format.

```
GET /api/reports/workstation-coverage
```

Generates a workstation coverage report.

**Query Parameters:**

- `departmentId` (required): Department ID
- `areaId` (optional): Area ID
- `format` (optional): Report format (json, excel, csv) - default: json

**Response:**
Report data in the requested format.

#### 5.2 Authorization Requirements

The API enforces RBAC for all endpoints:

| Endpoint                                          | Center Trainer   | Team Leader                   | Shift Leader & Coordinator      | Quality Auditor  |
| ------------------------------------------------- | ---------------- | ----------------------------- | ------------------------------- | ---------------- |
| GET /api/matrix                                   | All areas        | Team area only                | Assigned area only              | Read-only access |
| GET /api/matrix/export                            | All areas        | Team area only                | Assigned area only              | Read-only access |
| PUT /api/polyvalence/{operatorId}/{workstationId} | Not allowed      | Allowed for team members only | Not allowed                     | Not allowed      |
| GET /api/polyvalence/history/{operatorId}         | All operators    | Team operators only           | Assigned area operators only    | Read-only access |
| GET /api/qualification/{operatorId}               | All operators    | Team operators only           | Assigned area operators only    | Read-only access |
| GET /api/qualification/expiring                   | All areas        | Team area only                | Assigned area only              | Read-only access |
| GET /api/workstation/coverage                     | All areas        | Team area only                | Assigned area only              | Read-only access |
| GET /api/workstation/{workstationId}/operators    | All workstations | Team workstations only        | Assigned area workstations only | Read-only access |
| GET /api/reports/\*                               | All areas        | Team area only                | Assigned area only              | Read-only access |

### 6. Event Communication

#### 6.1 Inbound Events

The Versatility Matrix continuously listens for and processes events from external systems to ensure it maintains an accurate reflection of the operational reality.

##### 6.1.1 Qualification Updates from Training Process - Sequence Diagram

The following sequence diagram illustrates the flow of qualification updates from the Training Process to the Versatility Matrix:

```mermaid
sequenceDiagram
    participant TP as Training Process
    participant SB as Azure Service Bus
    participant VMP as Versatility Matrix Processor
    participant VMDB as Versatility Matrix DB
    participant AH as Audit History

    TP->>TP: Qualification status change
    TP->>SB: Publish QualificationChangedEvent
    SB-->>VMP: Receive event

    VMP->>VMP: Validate event data

    alt Valid Qualification Update
        VMP->>VMDB: Find existing qualification

        alt Qualification Exists
            VMP->>VMDB: Update qualification status
            VMP->>AH: Log qualification change
            VMP->>VMDB: Update workstation coverage

            opt Certification Expiry Alert Check
                VMP->>VMP: Check if certification is expiring soon

                alt Expiring Soon
                    VMP->>SB: Publish CertificationExpiryAlertEvent
                end
            end

        else No Existing Qualification
            VMP->>VMDB: Create new qualification
            VMP->>AH: Log new qualification
        end

        VMP->>SB: Acknowledge event processed

    else Invalid Event Data
        VMP->>AH: Log error in processing
        VMP->>SB: Dead-letter the message
    end
```

This sequence diagram shows the complete flow of a qualification update event from the Training Process through the Versatility Matrix system. When a qualification status changes in the Training Process, an event is published to Azure Service Bus. The Versatility Matrix Processor consumes this event, validates it, and then either updates an existing qualification or creates a new one. The processor also logs audit history entries for compliance and maintains workstation coverage metrics. If the qualification includes certification that's expiring soon, the system will publish an alert event to notify relevant stakeholders.

##### 6.1.2 Operator Data Events

The Versatility Matrix processes operator data changes from the Crew Management service via Cosmos DB Change Feed.

#### 6.2 Outbound Events

##### 6.2.1 Internal Domain Events

The Versatility Matrix uses NestJS event publisher for internal domain events:

```typescript
// Domain event definitions
export class PolyvalenceUpdatedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly workstationId: string,
    public readonly oldLevel: PolyvalenceLevel | null,
    public readonly newLevel: PolyvalenceLevel | null,
    public readonly updatedBy: string,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class QualificationStatusSyncedEvent {
  constructor(
    public readonly operatorId: string,
    public readonly skillCode: string,
    public readonly oldStatus: QualificationStatus | null,
    public readonly newStatus: QualificationStatus | null,
    public readonly certificationDate: Date | null,
    public readonly expiryDate: Date | null,
    public readonly sourceEventId: string | null,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class WorkstationCoverageChangedEvent {
  constructor(
    public readonly workstationId: string,
    public readonly oldCoverage: {
      targetBackupOperators: number;
      actualBackupOperators: number;
      gap: number;
    },
    public readonly newCoverage: {
      targetBackupOperators: number;
      actualBackupOperators: number;
      gap: number;
    },
    public readonly timestamp: Date = new Date()
  ) {}
}

// Event handler implementation with NestJS
@Injectable()
export class DomainEventsHandler {
  constructor(
    private readonly auditService: AuditService,
    private readonly workstationCoverageService: WorkstationCoverageService,
    private readonly projectionService: ProjectionService
  ) {}

  @EventsHandler(PolyvalenceUpdatedEvent)
  async handlePolyvalenceUpdated(event: PolyvalenceUpdatedEvent) {
    // 1. Create audit log entry
    await this.auditService.createAuditLog({
      entityType: "Polyvalence",
      entityId: `${event.operatorId}:${event.workstationId}`,
      action: "Update",
      oldValue: JSON.stringify(event.oldLevel),
      newValue: JSON.stringify(event.newLevel),
      userId: event.updatedBy,
      timestamp: event.timestamp,
    });

    // 2. Recalculate workstation coverage
    await this.workstationCoverageService.recalculateCoverage(
      event.workstationId
    );

    // 3. Update read model projections
    await this.projectionService.refreshWorkstationCoverageProjection(
      event.workstationId
    );
  }

  @EventsHandler(QualificationStatusSyncedEvent)
  async handleQualificationStatusSynced(event: QualificationStatusSyncedEvent) {
    // 1. Create audit log entry
    await this.auditService.createAuditLog({
      entityType: "Qualification",
      entityId: `${event.operatorId}:${event.skillCode}`,
      action: "Update",
      oldValue: JSON.stringify(event.oldStatus),
      newValue: JSON.stringify(event.newStatus),
      userId: "system", // System-triggered from Training Process
      timestamp: event.timestamp,
    });

    // 2. Find affected workstations that require this skill
    const affectedWorkstations =
      await this.workstationCoverageService.findWorkstationsRequiringSkill(
        event.skillCode
      );

    // 3. Recalculate coverage for affected workstations
    for (const workstationId of affectedWorkstations) {
      await this.workstationCoverageService.recalculateCoverage(workstationId);
      await this.projectionService.refreshWorkstationCoverageProjection(
        workstationId
      );
    }

    // 4. Check for certification expiry alerts
    if (event.newStatus === "C" || event.newStatus === "R") {
      if (event.expiryDate) {
        const daysUntilExpiry = Math.floor(
          (event.expiryDate.getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        );

        if (daysUntilExpiry <= 30) {
          // Trigger certification expiry alert event
          this.eventBus.publish(
            new CertificationExpiringAlertEvent(
              event.operatorId,
              event.skillCode,
              event.expiryDate,
              daysUntilExpiry
            )
          );
        }
      }
    }
  }

  @EventsHandler(WorkstationCoverageChangedEvent)
  async handleWorkstationCoverageChanged(
    event: WorkstationCoverageChangedEvent
  ) {
    // 1. Create audit log entry
    await this.auditService.createAuditLog({
      entityType: "WorkstationCoverage",
      entityId: event.workstationId,
      action: "Update",
      oldValue: JSON.stringify(event.oldCoverage),
      newValue: JSON.stringify(event.newCoverage),
      userId: "system",
      timestamp: event.timestamp,
    });

    // 2. Check for coverage gap alerts
    if (
      event.newCoverage.gap > 0 &&
      (event.oldCoverage.gap === 0 ||
        event.oldCoverage.gap < event.newCoverage.gap)
    ) {
      // Trigger coverage gap alert event
      this.eventBus.publish(
        new WorkstationCoverageGapAlertEvent(
          event.workstationId,
          event.newCoverage.targetBackupOperators,
          event.newCoverage.actualBackupOperators,
          event.newCoverage.gap
        )
      );
    }
  }
}
```

##### 6.2.2 Cosmos DB Change Feed for External Consumers

The Versatility Matrix exposes data changes to external systems via Cosmos DB Change Feed:

```typescript
// Optimize container for Change Feed consumption
@Injectable()
export class CosmosDbService {
  constructor(private readonly configService: ConfigService) {}

  async configureContainerForChangeFeed(containerName: string): Promise<void> {
    const client = new CosmosClient(
      this.configService.get<string>("COSMOS_DB_CONNECTION")
    );
    const database = client.database("VersatilityMatrix");
    const container = database.container(containerName);

    // Configure TTL and indexes optimally for change feed processing
    const indexingPolicy = {
      indexingMode: "consistent",
      includedPaths: [
        {
          path: "/*",
        },
      ],
      compositeIndexes: [
        [
          {
            path: "/_ts",
            order: "ascending",
          },
        ],
      ],
    };

    await container.replace({
      id: containerName,
      partitionKey: container.partitionKey,
      indexingPolicy,
    });
  }
}

// Change Feed processor for Training Process read model updates
export class QualificationChangeFeedProcessor {
  constructor(
    private readonly cosmosClient: CosmosClient,
    private readonly serviceBusClient: ServiceBusClient
  ) {}

  @Function("ProcessQualificationChanges")
  async processQualificationChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "COSMOS_DB_CONNECTION",
      databaseName: "VersatilityMatrix",
      containerName: "qualifications",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Create Service Bus client for publishing events
    const sender = this.serviceBusClient.createSender(
      "training-read-model-updates"
    );

    // Process each document
    for (const doc of documents) {
      try {
        // Create event from document
        const event = {
          eventType: "QualificationUpdated",
          operatorId: doc.operatorId,
          skillCode: doc.skillCode,
          status: doc.status,
          certificationDate: doc.certificationDate,
          expiryDate: doc.expiryDate,
          timestamp: new Date(doc._ts * 1000),
        };

        // Send event to Service Bus
        await sender.sendMessages({
          body: event,
          contentType: "application/json",
          messageId: `qual-${doc.id}-${doc._ts}`,
        });
      } catch (error) {
        // Log error and continue
      }
    }
  }
}

// Change Feed processor for polyvalence updates
export class PolyvalenceChangeFeedProcessor {
  constructor(
    private readonly cosmosClient: CosmosClient,
    private readonly serviceBusClient: ServiceBusClient
  ) {}

  @Function("ProcessPolyvalenceChanges")
  async processPolyvalenceChanges(
    @CosmosDBTrigger({
      connectionStringSetting: "COSMOS_DB_CONNECTION",
      databaseName: "VersatilityMatrix",
      containerName: "polyvalence",
      leaseContainerName: "leases",
      createLeaseContainerIfNotExists: true,
    })
    documents: any[]
  ): Promise<void> {
    // Create Service Bus client for publishing events
    const sender = this.serviceBusClient.createSender(
      "training-read-model-updates"
    );

    // Process each document
    for (const doc of documents) {
      try {
        // Create event from document
        const event = {
          eventType: "PolyvalenceUpdated",
          operatorId: doc.operatorId,
          workstationId: doc.workstationId,
          level: doc.level,
          updatedBy: doc.updatedBy,
          timestamp: new Date(doc._ts * 1000),
        };

        // Send event to Service Bus
        await sender.sendMessages({
          body: event,
          contentType: "application/json",
          messageId: `poly-${doc.id}-${doc._ts}`,
        });
      } catch (error) {
        // Log error and continue
      }
    }
  }
}

// Query handler for replacement process integration
@QueryHandler(FindQualifiedOperatorsQuery)
export class FindQualifiedOperatorsQueryHandler
  implements IQueryHandler<FindQualifiedOperatorsQuery>
{
  constructor(
    private readonly workstationRepository: WorkstationRepository,
    private readonly qualificationRepository: QualificationRepository,
    private readonly polyvalenceRepository: PolyvalenceRepository
  ) {}

  async execute(
    query: FindQualifiedOperatorsQuery
  ): Promise<QualifiedOperatorsResult> {
    // 1. Get workstation with required skills
    const workstation = await this.workstationRepository.findById(
      query.workstationId
    );
    if (!workstation) {
      throw new NotFoundException(
        `Workstation ${query.workstationId} not found`
      );
    }

    // 2. Find operators with C/R qualification for all required skills
    const requiredSkills = query.requiredSkills || workstation.requiredSkills;
    const qualifiedOperatorIds =
      await this.qualificationRepository.findOperatorsQualifiedForSkills(
        requiredSkills,
        ["C", "R"]
      );

    // 3. Get polyvalence levels for these operators at this workstation
    const polyvalenceLevels =
      await this.polyvalenceRepository.findPolyvalenceLevels(
        qualifiedOperatorIds,
        query.workstationId
      );

    // 4. Build result with operator details
    const qualifiedOperators = await this.buildQualifiedOperatorsResult(
      qualifiedOperatorIds,
      polyvalenceLevels,
      requiredSkills
    );

    // 5. Sort by polyvalence level (higher is better)
    const sortedOperators = this.sortOperatorsByPolyvalence(qualifiedOperators);

    return {
      workstationId: query.workstationId,
      requiredSkills,
      operators: sortedOperators,
    };
  }

  // Helper methods
  private async buildQualifiedOperatorsResult(/*...*/): Promise<
    QualifiedOperator[]
  > {
    // Implementation details
    return [];
  }

  private sortOperatorsByPolyvalence(
    operators: QualifiedOperator[]
  ): QualifiedOperator[] {
    // Sort by polyvalence level (4 > 3 > 2 > 1 > X) for replacement purposes
    const levelOrder = { "4": 5, "3": 4, "2": 3, "1": 2, X: 1, null: 0 };
    return operators.sort((a, b) => {
      return (
        levelOrder[b.polyvalenceLevel || null] -
        levelOrder[a.polyvalenceLevel || null]
      );
    });
  }
}
```

##### 6.2.1 Polyvalence Update Flow - Sequence Diagram

The following sequence diagram illustrates the flow when a Team Leader updates an operator's polyvalence rating through the Versatility Matrix UI:

```mermaid
sequenceDiagram
    autonumber
    participant TL as Team Leader
    participant UI as Versatility Matrix UI
    participant API as Versatility Matrix API
    participant Domain as Domain Layer
    participant DB as Database
    participant Audit as Audit History
    participant SB as Service Bus

    TL->>UI: Selects Operator & Workstation
    TL->>UI: Updates Rating (1-5)
    UI->>API: POST /api/polyvalence/update
    API->>Domain: PolyvalenceUpdateCommand
    Domain->>DB: Get Current Polyvalence

    alt Existing Polyvalence
        DB->>Domain: Return Current Polyvalence
        Domain->>DB: Update Polyvalence
    else No Existing Polyvalence
        DB->>Domain: Return Not Found
        Domain->>DB: Create New Polyvalence
    end

    Domain->>Audit: Log Polyvalence Change
    Domain->>Domain: Calculate Workstation Coverage Impact
    Domain->>DB: Update Workstation Coverage

    Domain->>SB: Publish PolyvalenceChangedEvent
    Domain->>API: Update Success
    API->>UI: Return Success
    UI->>TL: Display Success Notification
```

This sequence diagram visualizes the complete flow of updating an operator's polyvalence rating, from the Team Leader's action in the UI through the processing and persistence layers to the notification of other services via the Service Bus. The process includes important steps like logging the change for compliance and calculating the impact of the change on workstation coverage metrics.

##### 6.2.2 Qualification Update Flow - Sequence Diagram

The following sequence diagram illustrates the flow when a qualification update is received from the Training Process:

```mermaid
sequenceDiagram
    autonumber
    participant TP as Training Process
    participant SB as Service Bus
    participant CF as Change Feed Processor
    participant Domain as Domain Layer
    participant DB as Database
    participant Audit as Audit History
    participant VSM as Versatility Matrix UI

    TP->>SB: Publish TrainingCompletedEvent
    SB->>CF: Trigger Change Feed Processor
    CF->>Domain: QualificationUpdateCommand

    Domain->>DB: Get Operator Record
    DB->>Domain: Return Operator

    Domain->>DB: Get Existing Qualification

    alt Existing Qualification
        DB->>Domain: Return Qualification
        Domain->>Domain: Validate Training Certificate
        Domain->>DB: Update Qualification Status
    else No Existing Qualification
        Domain->>Domain: Create New Qualification
        Domain->>DB: Insert New Qualification
    end

    Domain->>Audit: Log Qualification Change
    Domain->>Domain: Calculate Affected Workstations
    Domain->>DB: Update Eligible Workstations

    Domain->>SB: Publish QualificationStatusSyncedEvent
    Domain->>SB: Publish WorkstationCoverageChangedEvent

    Domain->>CF: Process Complete

    VSM-->>SB: Subscribe to Events
    SB-->>VSM: Push Qualification Update
    VSM-->>VSM: Update UI (if open)
```

This sequence diagram illustrates how qualification updates originating from the Training Process are processed by the Versatility Matrix system. The flow begins when the Training Process publishes a `TrainingCompletedEvent` to the Service Bus, which is then picked up by the Change Feed Processor. The Domain Layer processes the qualification update, creates or updates qualification records in the database, logs the changes in the audit history, and calculates the impact on workstation eligibility. The system then publishes events to notify other systems of the changes, and the Versatility Matrix UI updates if it is currently open and subscribed to these events.

##### 6.2.3 Change Feed Processing Flow - Sequence Diagram

The following sequence diagram illustrates the general Change Feed Processing flow used throughout the system for event-driven integrations:

```mermaid
sequenceDiagram
    autonumber
    participant ES as External System
    participant CF as Cosmos DB Change Feed
    participant PR as Change Feed Processor
    participant SB as Service Bus
    participant Domain as Domain Layer
    participant DB as Database
    participant Audit as Audit History

    ES->>CF: Write Document to Change Feed
    CF->>PR: Trigger Processor (Document Change)
    PR->>PR: Extract Event Data
    PR->>Domain: Create Domain Command

    Domain->>Domain: Validate Command
    Domain->>DB: Query Required Data
    DB->>Domain: Return Data

    Domain->>Domain: Apply Business Rules
    Domain->>DB: Persist Changes
    Domain->>Audit: Log Action

    alt Process Success
        Domain->>SB: Publish Integration Events
        Domain->>PR: Return Success
        PR->>CF: Acknowledge Processing (Checkpoint)
    else Process Failure
        Domain->>PR: Return Failure with Error
        PR->>PR: Apply Retry Policy

        alt Retry Possible
            PR->>Domain: Retry Command (After Delay)
        else Max Retries Exceeded
            PR->>SB: Publish FailedProcessingEvent
            PR->>CF: Acknowledge with Failure (Checkpoint)
        end
    end
```

This sequence diagram depicts the general flow of the Change Feed processing mechanism used throughout the system. When an external system writes a document to Cosmos DB, the Change Feed detects the change and triggers the appropriate Change Feed Processor. The processor extracts the event data and creates a domain command, which is then validated and processed by the Domain Layer.

The Domain Layer applies business rules, persists changes to the database, and logs the action in the audit history. If the process is successful, integration events are published to the Service Bus, and the processor acknowledges the processing by creating a checkpoint in the Change Feed. If the process fails, a retry policy is applied. If retries are possible, the command is retried after a delay. If the maximum number of retries is exceeded, a FailedProcessingEvent is published to the Service Bus, and the processor acknowledges the failure by creating a checkpoint in the Change Feed.

This pattern is used consistently throughout the system for various integrations, including qualification updates, operator transfers, and other event-driven processes.

#### 6.3 Event Schema Definitions

The Versatility Matrix uses the following event schema definitions for domain events:

- **PolyvalenceUpdatedEvent**: This event is published when an operator's polyvalence level changes.
- **QualificationStatusSyncedEvent**: This event is published when a qualification status changes.
- **WorkstationCoverageChangedEvent**: This event is published when the coverage for a workstation changes.

#### 6.4 Domain Model - Class Diagram

The following class diagram illustrates the key entity relationships in the Versatility Matrix domain model:

```mermaid
classDiagram
    class VersatilityMatrix {
        +string id
        +string plantId
        +string departmentId
        +DateTime lastUpdated
        +List~WorkstationGroup~ workstationGroups
        +calculateEligibility()
        +updateQualification()
        +updatePolyvalence()
    }

    class WorkstationGroup {
        +string id
        +string name
        +List~Workstation~ workstations
        +List~OperatorQualification~ operatorQualifications
        +addWorkstation()
        +removeWorkstation()
        +calculateGroupPolyvalence()
    }

    class Workstation {
        +string id
        +string name
        +string description
        +WorkstationType type
        +int requiredOperators
        +List~Qualification~ qualifications
        +bool isActive
        +DateTime lastUpdated
        +calculateEligibility()
    }

    class Operator {
        +string id
        +string employeeId
        +string name
        +string teamId
        +bool isActive
        +WorkShift shift
        +DateTime startDate
        +DateTime? endDate
    }

    class OperatorQualification {
        +string operatorId
        +Map~string, QualificationLevel~ qualifications
        +DateTime lastUpdated
        +updateQualification()
        +calculateEligibility()
    }

    class Qualification {
        +string id
        +string name
        +string description
        +QualificationType type
        +bool isRequired
        +DateTime validUntil
        +DateTime lastUpdated
    }

    class AuditRecord {
        +string id
        +string entityId
        +string entityType
        +string action
        +string userId
        +string userName
        +DateTime timestamp
        +object previousState
        +object newState
        +string systemSource
    }

    class QualificationLevel {
        <<enumeration>>
        NONE
        IN_TRAINING
        QUALIFIED
        TRAINER
        EXPIRED
    }

    class QualificationType {
        <<enumeration>>
        SAFETY
        TECHNICAL
        QUALITY
        PROCESS
    }

    class WorkstationType {
        <<enumeration>>
        ASSEMBLY
        MACHINING
        TESTING
        PACKAGING
        LOGISTICS
    }

    VersatilityMatrix "1" *-- "many" WorkstationGroup
    WorkstationGroup "1" *-- "many" Workstation
    WorkstationGroup "1" *-- "many" OperatorQualification
    Workstation "1" *-- "many" Qualification
    OperatorQualification "1" -- "1" Operator
    OperatorQualification "many" -- "many" Qualification
    VersatilityMatrix "1" -- "many" AuditRecord
    OperatorQualification -- QualificationLevel
    Qualification -- QualificationType
    Workstation -- WorkstationType
```

This class diagram shows the core domain entities of the Versatility Matrix system. The VersatilityMatrix is the root aggregate that contains WorkstationGroups. Each WorkstationGroup contains Workstations and OperatorQualifications. The OperatorQualification represents the qualifications an Operator has for various workstations within a group. Qualifications are attributes of Workstations that Operators must possess to be eligible to work at those stations.

The AuditRecord entity is used to track changes to the system, capturing the previous and new states of entities, along with who made the change and when. The system uses enumeration types like QualificationLevel, QualificationType, and WorkstationType to categorize and define the properties of the entities.

This domain model is designed to support the calculation of polyvalence metrics and workstation eligibility, which are core functions of the Versatility Matrix system.

### 7. Conclusion

The Versatility Matrix microservice, as detailed in this Low-Level Design document, provides a robust and scalable solution for managing operator skills and workstation proficiency. By leveraging a CQRS architecture, event-driven integrations, and a comprehensive data model, the system effectively addresses the complex requirements of real-time tracking, reporting, and compliance. The defined API endpoints and event communication mechanisms ensure seamless integration with other components of the Connected Workers ecosystem, contributing to overall operational efficiency and informed decision-making. Future enhancements can build upon this solid foundation to incorporate advanced analytics, machine learning-based suggestions, and broader integration with other enterprise systems.

## 5.2.5 Replacement Management

### 1. Overview

The Operator Replacement Process is a critical component within the Connected Workers (CW) system designed to manage operator absenteeism efficiently and effectively. This Low-Level Design document outlines the technical architecture, implementation patterns, and specifications needed to build a robust solution that addresses this challenge.

In manufacturing environments, unexpected operator absences can significantly impact production efficiency and quality. The Replacement Process system addresses this by providing structured workflows for handling absences, leveraging a multi-level approach based on absence severity, and ensuring optimal operator assignment through skills-based recommendations.

This document details a microservices-based architecture implementing CQRS (Command Query Responsibility Segregation) and event-driven patterns to provide a scalable, maintainable solution. The system is built on Azure cloud services, leveraging Cosmos DB for data storage and Service Bus for asynchronous messaging.

The design supports various stakeholders, including Team Leaders (TLs), ShiftLeaders (SLs), and management, and provides appropriate interfaces for each role based on their responsibilities in the replacement process. Real-time notifications, recommendation engines, and comprehensive reporting capabilities ensure that all participants have the information they need to make timely decisions.

#### 1.1 High-Level Flow

The Operator Replacement Process follows these key workflows:

1. **Absence Detection and Initiation**:

   - TL verifies operator presence
   - System identifies absences and initiates replacement process

2. **Replacement Handling (multi-level)**:

   - **Low Level**: TL-managed using backup structure or polyvalence matrix
   - **Medium Level**: SL-managed using SL Backup Panier
   - **High Level**: Department-level coordination using Department Panier
   - **Critical Level**: To be defined (out of scope for this version)

3. **Recommendation Decision Support**:

   - System suggests replacements based on qualifications, skills, and historical evaluations

4. **Notification and Confirmation**:

   - Real-time updates to relevant stakeholders
   - Confirmation workflows for replacement decisions

5. **Evaluation and Reporting**:
   - End-of-shift evaluations by TLs
   - Historical reporting for management

### 2. Architecture Overview

#### 2.1 Architectural Patterns

The Replacement Process implementation follows these key architectural patterns:

##### 2.1.1 CQRS (Command Query Responsibility Segregation)

- **Command Side**: Handles write operations and business rules validation within the Replacement Service
- **Query Side**: Optimized for read operations with specialized data projections within the same Replacement Service
- **Benefits**: While we maintain logical separation of concerns, the operations are consolidated in one service

##### 2.1.2 Event-Driven Architecture

- Commands produce domain events when state changes
- Services communicate asynchronously through events
- Provides decoupling between services and ensures eventual consistency

##### 2.1.3 Microservices

- Bounded contexts aligned with business capabilities
- Independent deployment and scaling
- Loose coupling through standardized APIs and event contracts

#### 2.2 High-Level Component Diagram

```mermaid
flowchart TB
    Client["Client Apps"]
    Gateway["API Gateway"]
    Identity["Identity Service"]

    Client --> Gateway
    Gateway --> Identity

    subgraph "Connected Workers System"
        ReplacementSvc["Replacement Service"]
        NotifSvc["Notification Service"]
        EvalSvc["Evaluation Service"]

        CosmosWrite[(Cosmos DB\nWrite Model)]
        CosmosRead[(Cosmos DB\nRead Models)]
        ServiceBus[("Service Bus")]

        ChangeFeed["Change Feed Processor"]

        ReportSvc["Reporting Service"]

        VersalityMatrix[(Versality Matrix\nData)]
        VersalityChangeFeed["Versality Matrix\nChange Feed"]

        ReplacementSvc --> CosmosWrite
        ReplacementSvc --> ServiceBus
        ReplacementSvc -.-> CosmosRead

        CosmosWrite --> ChangeFeed
        ChangeFeed --> CosmosRead

        ServiceBus --> NotifSvc
        ServiceBus --> EvalSvc

        VersalityMatrix --> VersalityChangeFeed
        VersalityChangeFeed --> ReplacementSvc

        EvalSvc --> ServiceBus
    end

    Gateway --> ReplacementSvc
    Gateway --> NotifSvc
    Gateway --> EvalSvc
    Gateway --> ReportSvc
```

### 3. Data Models

### 3.1 Write Models

```mermaid
classDiagram
    class BaseEntity {
        +String id
        +String type
        +String partitionKey
        +int version
        +String createdBy
        +DateTime createdAt
        +String modifiedBy
        +DateTime modifiedAt
    }

    class Replacement {
        +ValueStream valueStreamId
        +Team teamId
        +Department departmentId
        +AbsentOperator absentOperator
        +ReplacementOperator replacementOperator
        +String replacementLevel
        +String replacementType
        +String replacementOption
        +String replacementCase
        +String status
        +Escalation[] escalations
        +TimelineEvent[] timeline
        +int evaluationScore
        +DateTime evaluatedAt
        +String evaluatedBy
    }

    class AbsentOperator {
        +String id
        +String name
        +String workstation
        +String workstationName
        +String[] qualifications
        +String criticity
    }

    class ReplacementOperator {
        +String id
        +String name
        +String sourceType
        +String originalWorkstation
        +String[] qualifications
        +int polyvalenceLevel
    }

    class BackupStructure {
        +String valueStreamId
        +String teamId
        +String teamLeaderId
        +String date
        +String shift
        +BackupOperator[] operators
        +boolean transferredToSl
        +DateTime transferredAt
        +String slId
    }

    class BackupOperator {
        +String id
        +String name
        +String type
        +String[] qualifications
        +int polyvalenceLevel
        +String status
        +String replacementId
    }

    class Panier {
        +String panierType
        +String ownerId
        +String departmentId
        +String date
        +String shift
        +PanierOperator[] operators
        +boolean transferredToDepartment
    }

    class PanierOperator {
        +String id
        +String name
        +String sourceType
        +String sourceTlId
        +String type
        +String[] qualifications
        +int polyvalenceLevel
        +String status
        +String assignedToReplacementId
        +DateTime entryTimestamp
    }

    class Evaluation {
        +String replacementId
        +String evaluatorId
        +int score
        +String comments
        +String date
        +String shift
        +String operatorId
        +String workstationId
    }

    BaseEntity <|-- Replacement
    BaseEntity <|-- BackupStructure
    BaseEntity <|-- Panier
    BaseEntity <|-- Evaluation

    Replacement o-- AbsentOperator
    Replacement o-- ReplacementOperator
    BackupStructure o-- BackupOperator
    Panier o-- PanierOperator
```

### 3.2 Read Models

Read models are already described in detail in Section 4.3.1, including:

- TL Workspace Container
- SL Panier Container
- Department Panier Container
- Replacement History Container

### 4. API Specification

#### 4.1 Replacement Service APIs

##### 4.1.1 Initiate Replacement

**Endpoint**: `POST /api/replacement/initiate`

```mermaid
sequenceDiagram
    actor TL as Team Leader
    participant API as API Gateway
    participant RS as Replacement Service
    participant CosmosDB as Cosmos DB (Write)
    participant SB as Service Bus
    participant NS as Notification Service

    TL->>API: POST /api/replacement/initiate
    Note right of TL: Request payload:<br>{<br>  "valueStreamId": "VS_123",<br>  "teamId": "Team_456",<br>  "operatorId": "OP_123",<br>  "workstationId": "WS_123",<br>  "absenteeismLevel": "Low"<br>}

    API->>RS: Forward request

    RS->>RS: Validate request
    RS->>RS: Check TL authority
    RS->>RS: Verify operator absence

    RS->>CosmosDB: Create replacement document
    CosmosDB-->>RS: Confirmation

    RS->>SB: Publish ReplacementInitiated event
    SB-->>NS: Forward to notification service
    NS->>NS: Create notification

    RS-->>API: Return response
    API-->>TL: 201 Created
    Note right of TL: Response payload:<br>{<br>  "replacementId": "replacement-uuid",<br>  "status": "Initiated",<br>  "timestamp": "2025-05-15T08:30:00Z",<br>  "workstationDetails": {<br>    "id": "WS_123",<br>    "name": "Assembly Station 5",<br>    "criticity": "C",<br>    "requiredQualifications": ["Assembly", "Testing"]<br>  },<br>  "options": {<br>    "backupStructureAvailable": true,<br>    "polyvalenceMatrixAvailable": true<br>  }<br>}
```

##### 4.1.2 Assign Backup Operator

**Endpoint**: `POST /api/replacement/assign-backup`

```mermaid
sequenceDiagram
    actor TL as Team Leader
    participant API as API Gateway
    participant RS as Replacement Service
    participant CosmosDB as Cosmos DB (Write)
    participant SB as Service Bus
    participant NS as Notification Service

    TL->>API: POST /api/replacement/assign-backup
    Note right of TL: Request payload:<br>{<br>  "replacementId": "replacement-uuid",<br>  "backupOperatorId": "OP_789",<br>  "backupType": "BackupStructure",<br>  "optionCase": "Case1"<br>}

    API->>RS: Forward request

    RS->>CosmosDB: Get replacement document
    CosmosDB-->>RS: Return replacement

    RS->>RS: Validate assignment
    RS->>RS: Check operator qualification

    RS->>CosmosDB: Update replacement document
    CosmosDB-->>RS: Confirmation

    RS->>CosmosDB: Update backup structure
    CosmosDB-->>RS: Confirmation

    RS->>SB: Publish OperatorAssigned event
    SB-->>NS: Forward to notification service

    RS-->>API: Return response
    API-->>TL: 200 OK
    Note right of TL: Response payload:<br>{<br>  "replacementId": "replacement-uuid",<br>  "status": "Completed",<br>  "timestamp": "2025-05-15T08:35:00Z",<br>  "assignedOperator": {<br>    "id": "OP_789",<br>    "name": "Alex Johnson",<br>    "qualifications": ["Assembly:C", "Testing:C", "Rework:V"]<br>  }<br>}
```

##### 4.1.3 Get Replacement Suggestions

**Endpoint**: `GET /api/replacement/suggestions/{absenceId}`

**Parameters**:

- `absenceId`: Absence ID

**Response (200 OK)**:

```json
{
  "absenceId": "absence-uuid",
  "workstationId": "WS_123",
  "workstationName": "Assembly Station 5",
  "requiredQualifications": ["Assembly:C", "Testing:V"],
  "criticity": "C",
  "suggestions": [
    {
      "operatorId": "OP_789",
      "name": "Alex Johnson",
      "source": "BackupStructure",
      "qualifications": ["Assembly:C", "Testing:C", "Rework:V"],
      "polyvalenceLevel": 3,
      "score": 0.95,
      "matchReason": "Primary skill match"
    },
    {
      "operatorId": "OP_101",
      "name": "Sam Wilson",
      "source": "BackupStructure",
      "qualifications": ["Assembly:C", "Testing:V"],
      "polyvalenceLevel": 2,
      "score": 0.85,
      "matchReason": "Secondary skill match"
    }
  ]
}
```

##### 4.1.4 Get Workspace Data

**Endpoint**: `GET /api/workspace/{tlId}`

**Parameters**:

- `tlId`: Team Leader ID

**Response (200 OK)**:

```json
{
  "tlId": "TL_123",
  "valueStreams": [
    {
      "id": "VS_123",
      "name": "Final Assembly Line 1",
      "operators": [
        {
          "id": "OP_123",
          "name": "John Doe",
          "status": "Absent",
          "workstation": "WS_123",
          "workstationName": "Assembly Station 5",
          "replacementStatus": "Pending"
        },
        {
          "id": "OP_124",
          "name": "Jane Smith",
          "status": "Present",
          "workstation": "WS_124",
          "workstationName": "Assembly Station 6"
        }
      ],
      "backupStructure": {
        "totalOperators": 3,
        "availableOperators": 2,
        "usedOperators": 1,
        "operators": [
          {
            "id": "OP_789",
            "name": "Alex Johnson",
            "type": "Polyvalent",
            "status": "Available"
          },
          {
            "id": "OP_101",
            "name": "Sam Wilson",
            "type": "Rework",
            "status": "Available"
          }
        ]
      },
      "pendingReplacements": [
        {
          "replacementId": "replacement-uuid",
          "absentOperatorId": "OP_123",
          "absentOperatorName": "John Doe",
          "workstationId": "WS_123",
          "workstationName": "Assembly Station 5",
          "status": "Pending",
          "initiatedAt": "2025-05-15T08:30:00Z",
          "timeRemaining": 180 // Seconds remaining for 5-minute window
        }
      ]
    }
  ],
  "pendingEvaluations": 2,
  "notifications": [
    {
      "id": "notification-uuid",
      "type": "ReplacementConfirmation",
      "message": "SL has assigned operator Robin Chen as replacement",
      "timestamp": "2025-05-15T08:45:00Z",
      "read": false
    }
  ]
}
```

#### 4.2 Panier Management APIs

#### 4.2.1 Get SL Panier

**Endpoint**: `GET /api/panier/sl/{slId}`

**Parameters**:

- `slId`: Shift Leader ID

**Response (200 OK)**:

```json
{
  "slId": "SL_123",
  "panierId": "panier-uuid",
  "totalOperators": 3,
  "availableOperators": 1,
  "assignedOperators": 1,
  "ctnOperators": 1,
  "operators": [
    {
      "id": "OP_103",
      "name": "Robin Chen",
      "sourceType": "SLBackup",
      "type": "Support",
      "qualifications": ["Assembly:C", "Testing:C"],
      "polyvalenceLevel": 3,
      "status": "Available"
    },
    {
      "id": "OP_789",
      "name": "Alex Johnson",
      "sourceType": "TLBackup",
      "sourceTlId": "TL_123",
      "type": "Polyvalent",
      "qualifications": ["Assembly:C", "Testing:C", "Rework:V"],
      "status": "Assigned"
    },
    {
      "id": "OP_101",
      "name": "Sam Wilson",
      "sourceType": "TLBackup",
      "sourceTlId": "TL_123",
      "type": "Rework",
      "qualifications": ["Rework:C", "Assembly:V"],
      "status": "CTN"
    }
  ],
  "pendingRequests": [
    {
      "requestId": "request-uuid",
      "tlId": "TL_456",
      "tlName": "Michael Brown",
      "valueStreamId": "VS_456",
      "valueStreamName": "Final Assembly Line 2",
      "workstationId": "WS_456",
      "workstationName": "Testing Station 3",
      "requiredQualifications": ["Testing"],
      "criticity": "M",
      "requestTime": "2025-05-15T08:50:00Z",
      "timeRemaining": 180 // Seconds remaining for 5-minute window
    }
  ]
}
```

#### 4.2.2 Update Operator Status

**Endpoint**: `PATCH /api/panier/operator/{operatorId}/status`

**Request**:

```json
{
  "panierId": "panier-uuid",
  "status": "CTN"
}
```

**Response (200 OK)**:

```json
{
  "operatorId": "OP_101",
  "name": "Sam Wilson",
  "status": "CTN",
  "updatedAt": "2025-05-15T09:00:00Z"
}
```

#### 4.3 Evaluation Service APIs

##### 4.3.1 Submit Replacement Evaluation

**Endpoint**: `POST /api/evaluation/replacement/{replacementId}`

**Request**:

```json
{
  "score": 4,
  "comments": "Good performance, adapted quickly to the workstation."
}
```

**Response (200 OK)**:

```json
{
  "evaluationId": "evaluation-uuid",
  "replacementId": "replacement-uuid",
  "score": 4,
  "timestamp": "2025-05-15T16:30:00Z",
  "status": "Completed"
}
```

#### 4.4 Reporting Service APIs

##### 4.4.1 Get Replacement History Report

**Endpoint**: `GET /api/reports/replacement-history`

**Query Parameters**:

- `startDate`: "2025-05-01"
- `endDate`: "2025-05-15"
- `shift`: "Morning"
- `department`: "Dept_789"
- `valueStream`: "VS_123"

### 5. Security and Permission Models

#### 5.1 Authorization Model

##### 5.1.1 Authorization Components

```mermaid
graph TD
    subgraph "RBAC Components"
        User(User)
        Role(Role)
        Permission(Permission)
        Resource(Resource)
        Action(Action)
    end

    User -->|Assigned to| Role
    Role -->|Contains| Permission
    Permission -->|Combines| Resource
    Permission -->|Combines| Action
    Resource -->|Protected by| Permission
    Action -->|Performed on| Resource
```

- **Users**: Individual system users authenticated via Azure AD B2C
- **Roles**: Collections of permissions assigned to users
- **Permissions**: Defined as `resource:action` pairs (e.g., `workspace:view`)
- **Resources**: System objects or data that can be accessed or modified
- **Actions**: Operations that can be performed on resources

##### 5.1.2 Permission Format

Permissions follow the format `resource:action` where:

- `resource` is the protected entity (e.g., workspace, replacement, panier-sl)
- `action` is the allowed operation (e.g., view, manage, transfer)

##### 5.1.3 Core Resources

| Resource        | Description                                       |
| --------------- | ------------------------------------------------- |
| workspace       | TL workspace with operator presence data          |
| backupstructure | Backup Structure for low-level replacements       |
| panier-sl       | Shift Leader Panier for medium-level replacements |
| panier-dept     | Department Panier for high-level replacements     |
| replacement     | Replacement process entities                      |
| evaluation      | Replacement evaluations and feedback              |
| report          | Replacement history and analytics reports         |
| notification    | System notifications                              |

##### 5.1.4 Core Actions

| Action   | Description                          |
| -------- | ------------------------------------ |
| view     | Read-only access to a resource       |
| manage   | Create, update, or delete a resource |
| transfer | Move a resource to another owner     |
| initiate | Start a process                      |
| assign   | Assign operators to replacements     |
| complete | Mark a process as completed          |
| escalate | Elevate a process to a higher level  |
| submit   | Provide data for a process           |
| approve  | Authorize a process or change        |

##### 5.1.5 Role Permission Matrix

```mermaid
graph TD
    subgraph "Role Hierarchy"
        Operator
        TeamLeader
        ShiftLeader
        Coordinator
        ProductionManager
        DepartmentClerk
        PlantManager
        SystemAdmin
    end

    Operator --> TeamLeader
    TeamLeader --> ShiftLeader
    ShiftLeader --> Coordinator
    Coordinator --> ProductionManager
    ProductionManager --> PlantManager
    DepartmentClerk --> ProductionManager
    PlantManager --> SystemAdmin
```

| Role              | Permissions                                                                                                                                                                                                                                                                                 |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Operator          | notification:view                                                                                                                                                                                                                                                                           |
| TeamLeader        | workspace:view, workspace:manage, backupstructure:view, backupstructure:manage, backupstructure:transfer, replacement:initiate, replacement:assign, replacement:complete, replacement:escalate, evaluation:submit, evaluation:view, report:view-own, notification:view, notification:manage |
| ShiftLeader       | workspace:view, panier-sl:view, panier-sl:manage, panier-sl:transfer, panier-dept:view, replacement:assign, replacement:complete, replacement:escalate, report:view-team, notification:view, notification:manage                                                                            |
| Coordinator       | workspace:view, panier-sl:view, panier-dept:view, panier-dept:manage, report:view-department, notification:view                                                                                                                                                                             |
| ProductionManager | workspace:view, panier-sl:view, panier-dept:view, report:view-department, notification:view                                                                                                                                                                                                 |
| DepartmentClerk   | report:view-department, notification:view                                                                                                                                                                                                                                                   |
| PlantManager      | workspace:view, panier-sl:view, panier-dept:view, report:view-plant, notification:view                                                                                                                                                                                                      |
| SystemAdmin       | All permissions                                                                                                                                                                                                                                                                             |

#### 5.2 Security Implementation

##### 5.2.1 API Layer Security

- **API Gateway**: Centralized authentication and authorization
- **JWT Validation**: Signature verification and claims extraction
- **Claims Transformation**: Mapping identity claims to internal authorization context
- **Rate Limiting**: Tiered limits based on endpoint sensitivity and user role
- **Request Validation**: Schema validation for all incoming requests

##### 5.2.2 Service-to-Service Security

- **Mutual TLS**: Certificate-based authentication between services
- **Service Identities**: Managed identities for Azure resources
- **Least Privilege**: Scoped permissions for each service
- **Network Security**: Private endpoints for Cosmos DB and Service Bus

##### 5.2.3 Service Authorization

The Replacement Service implements authorization checks internally by:

1. **Validating JWT claims**: Extracting user identity and roles
2. **Checking resource ownership**: Validating that users can only access their authorized resources
3. **Enforcing action permissions**: Allowing only permitted actions based on role
4. **Contextual authorization**: Considering contextual factors like time constraints or process stage

For example, when processing a replacement request, the service validates:

- The user has the role of TeamLeader
- The value stream belongs to the user's assigned streams
- The replacement process is in a state that allows the requested action
- The time constraints allow the action

The Evaluation and Notification services implement similar authorization patterns specific to their domain.

##### 5.2.4 Data Protection

- **Data Classification**:

  - Public: Non-sensitive information (e.g., shift schedules)
  - Internal: Business-operational data (e.g., replacement records)
  - Confidential: Personally identifiable information (e.g., operator profiles)

- **Encryption**:

  - At rest: Cosmos DB encryption with customer-managed keys
  - In transit: TLS 1.3 for all communications
  - Application-level: Field-level encryption for sensitive attributes

- **Audit Logging**:
  - All authentication events
  - Authorization decisions (grants/denials)
  - Resource access and modifications
  - Administrative actions

##### 5.2.5 Security Monitoring

- **Threat Detection**:

  - Unusual access patterns
  - Authentication anomalies
  - Privilege escalation attempts

- **Vulnerability Management**:

  - Regular security scanning
  - Dependency vulnerability checks
  - Secure development practices

- **Incident Response**:
  - Security event alerting
  - Defined response procedures
  - Regular security tabletop exercises

### 6. Conclusion

The Operator Replacement Process design provides a solid foundation for a modern, flexible system that can efficiently manage the complex workflows required for operator absenteeism scenarios. The consolidated Replacement Service approach simplifies the architecture while maintaining the benefits of logical separation between command and query responsibilities.

By leveraging Azure cloud services, event-driven architecture, and a consolidated service approach, the system can deliver the performance, reliability, and scalability needed for production environments while minimizing operational complexity.

The implementation should proceed with an iterative approach, focusing on delivering core functionality first and gradually expanding to more advanced features. This will allow for early feedback and validation of the architectural decisions made in this design.

## 5.2.6 Nursing Management

## 5.3 Conclusion

## 6. Module 3: Headcount Management and Timekeeping system

### 7. Overtime planification
# 1. Overview

1. OvertimeRequest Service
2. ApprovalWorkflow Service
3. ShiftManagement Service
4. Notification Services
5. TKSIntegration Services
6. 
## 1.1 Purpose and Scope

This low-level design details the technical architecture and functional components of the overtime management module.based on Domain-Driven Design (DDD) principles and microservices architecture.
The design prioritizes configurability, flexibility. 
The system aims to implement complete digitalization via TKS, the TL tablet, and Optitime. It covers all types of personnel: DH, IH, IS.

## 1.2 Functional architecture
## 1.2.1 Key modules

```mermaid
flowchart TD
    Soumission["Soumission OT Planifié<br>Input of overtime requests with employee selection, project, activity, etc."]
    Validation["Validation OT<br>Hierarchical validation chain (Coordinator → Department Manager → Project Manager) with possible modifications"]
    Execution["Exécution OT<br>Marking and closing of shifts with indicators (e.g., X, Y, W)"]
    CrossCheck["Cross-check OT<br>Comparison between planned and executed hours"]
    Reporting["Reporting & Historique<br>Generation of reports (PDF/Excel) and complete audit history"]

    Soumission --> Validation
    Validation --> Execution
    Execution --> CrossCheck
    CrossCheck --> Reporting
```

## 1.2.2 Explanation of the Diagram

1. Soumission OT Planifié:

    - Input of overtime requests with employee selection, project, activity, etc.
2. Validation OT:

- Hierarchical validation chain (Coordinator → Department Manager → Project Manager) with possible modifications.
3. Exécution OT:

- Marking and closing of shifts with indicators (e.g., X, Y, W).
4. Cross-check OT:

- Comparison between planned and executed hours.
5. Reporting & Historique:

- Generation of reports (PDF/Excel) and complete audit history.
## 1.2.3 Actors and roles
```mermaid
flowchart TD
    TeamLeader[Team Leader<br>Saisit les demandes DH, clôture les shifts]
    Clerk[Clerk<br>Saisie OT IH/IS, mise à jour du système]
    ShiftLeader[Shift Leader<br>Valide OT exécuté]
    Coordinator[Coordinator<br>Valide/modifie les OT]
    DepartmentManager[Department Manager<br>Modère les demandes avant envoi au PM]
    PlantManager[Plant Manager<br>Dernier niveau de validation]
    TKSAgent[TKS Agent<br>Contrôle des données et rapprochement plan/exécuté]

    TeamLeader --> Clerk
    Clerk --> ShiftLeader
    ShiftLeader --> Coordinator
    Coordinator --> DepartmentManager
    DepartmentManager --> PlantManager
    PlantManager --> TKSAgent
```
## 1.3 Key Components

The system consists of three primary microservices:

1. OvertimeRequest Service
2. ApprovalWorkflow Service
3. ShiftManagement Service
4. Notification Services
5. TKSIntegration Services

Each service is supported by:

- Azure CosmosDB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Configuration Service for centralized settings

## 2. System Architecture Overview

## 2.1 High-Level Architecture
The system follows a microservices architecture with the following components:

```mermaid
flowchart TD
    ApiGateway[Azure API Management] --> OvertimeRequestService[Overtime Request Service]
    ApiGateway --> ApprovalWorkflowService[Approval Workflow Service]
    ApiGateway --> ShiftManagementService[Shift Management Service]
    ApiGateway --> NotificationService[Notification Service]
    ApiGateway --> TKSIntegrationService[TKS Integration Service]

    OvertimeRequestService --> OvertimeDB[(Overtime Request CosmosDB)]
    ApprovalWorkflowService --> WorkflowDB[(Approval Workflow CosmosDB)]
    ShiftManagementService --> ShiftDB[(Shift Management CosmosDB)]
    NotificationService --> NotificationDB[(Notification CosmosDB)]
    TKSIntegrationService --> TKSDB[(TKS Integration CosmosDB)]

```
## 2.1.1 Explanation of the Diagram
1. **API Gateway (Azure API Management):**

- Acts as the single entry point for all client requests.
- Routes requests to the appropriate microservices.

2. **Overtime Request Service:**

- Handles the creation, modification, and management of overtime requests (planned/unplanned).
- Stores data in Overtime Request CosmosDB.
3. **Approval Workflow Service:**

- Manages the multi-level approval process (Coordinator → Department Manager → Project Manager).
- Stores data in Approval Workflow CosmosDB.
4. **Shift Management Service:**

- Handles shift closures and assigns statuses (X/Y/W).
- Stores data in Shift Management CosmosDB.
5. **Notification Service:**

- Sends notifications to users via SignalR, email, or other channels.
- Stores data in Notification CosmosDB.
6. **TKS Integration Service:**

- Performs cross-checks between planned and executed overtime.
- Synchronizes data with TKS.
- Stores data in TKS Integration CosmosDB.
7. **Azure Service Bus** :
   - Facilitates asynchronous communication between microservices.
   -Manages events like station updates, schedule changes, etc..

8. **Bases de données CosmosDB** :
   - Each microservice has its own database to respect the principle of data separation.
   - CosmosDB is used for its scalability, low latency, and multi-region support.
  
## 3. Core components

Here is a description of the possible actions for each microservice, based on the data provided. This will help to better structure the **High-Level Architecture**.

### **3.1 OvertimeRequest Service**
1. **Responsibilities:**
- Manage the creation, modification, and deletion of overtime requests.
- Handle both planned and unplanned overtime requests.
- Validate input data (e.g., employee selection, project, activity).
- Publish events for downstream services (e.g., approval workflows).
2. **Key Entities:**
- OvertimeRequest: Represents an overtime request with details like employee, project, activity, and hours.
- Employee: Represents the employee associated with the overtime request.
3. **Aggregates:**

- **OvertimeRequest Aggregate:**
  - **Root Entity**: OvertimeRequest
  - **Entities**: Employee
  - **Description**: Manages overtime request details and associated employees.

4. **Class Diagram**

```mermaid
classDiagram
    class OvertimeRequest {
        +String id
        +String employeeId
        +String projectId
        +String activityId
        +int hours
        +String status
        +createRequest()
        +updateRequest()
        +deleteRequest()
    }

    class Employee {
        +String id
        +String name
        +String department
        +String role
    }

    OvertimeRequest --> Employee : "Associated with"
```

**Explanation of the Diagram**
1. **OvertimeRequest:**
- Represents an overtime request with attributes like employee, project, activity, hours, and status.
- Actions:
    - createRequest(): Creates a new overtime request.
    - updateRequest(): Updates an existing overtime request.
    - deleteRequest(): Deletes an overtime request.
2. **Employee:**
    - Represents the employee associated with the overtime request.
    - Attributes include id, name, department, and role.
3. **Relationship:**
    - The OvertimeRequest class is associated with the Employee class, indicating that each overtime request is linked to a specific employee.
### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant OvertimeRequestService as Overtime Request Service
    participant ApprovalWorkflowService as Approval Workflow Service
    participant NotificationService as Notification Service
    participant Database as Overtime Request CosmosDB

    User->>OvertimeRequestService: Create Overtime Request (POST /overtime-requests)
    OvertimeRequestService->>Database: Save Overtime Request
    OvertimeRequestService->>ApprovalWorkflowService: Publish OvertimeRequestCreated Event
    ApprovalWorkflowService->>NotificationService: Notify Approver (Coordinator)
    NotificationService->>User: Send Notification (Request Submitted)
    ApprovalWorkflowService->>User: Approve/Reject Request (POST /approvals/{id}/approve or reject)
    ApprovalWorkflowService->>OvertimeRequestService: Update Request Status
    OvertimeRequestService->>Database: Update Request in Database
    OvertimeRequestService->>NotificationService: Notify Requester (Approval/Rejection Status)
    NotificationService->>User: Send Notification (Approval/Rejection)
```
### **Explanation of the Sequence**
1. **User Action:**
    - The user submits a new overtime request via the Overtime Request Service.
2. **Overtime Request Service:**
    - Saves the request in the Overtime Request CosmosDB.
    - Publishes an OvertimeRequestCreated event to the Approval Workflow Service.
3. **Approval Workflow Service:**
    - Notifies the first approver (e.g., Coordinator) via the Notification Service.
    - Handles the approval or rejection of the request.
4. **Notification Service:**
    - Sends notifications to the requester and approvers about the status of the request.
5. **Database Updates:**
    - The Overtime Request Service updates the request status in the database after approval or rejection.
  

### **Validation and Business Logic**
1. **Validate employee eligibility:**
- Ensure the selected employee is eligible for overtime based on company policies.
2. **Check project/activity validity:**
- Verify that the selected project or activity is valid and active.
3. **Enforce business rules:**
- Ensure overtime hours do not exceed predefined limits.
- Validate that the request aligns with company policies.
### **Use Cases**
1. **Planned Overtime:**
- Create and manage overtime requests for scheduled activities.
2. **Unplanned Overtime:**
- Handle urgent overtime requests for unexpected tasks.
3. **Audit and Reporting:**
- Provide a history of overtime requests for auditing and reporting purposes.
  
### **3.2 ApprovalWorkflow Service**

- **Responsibilities:**
   - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
   - **Allow** modifications to requests during the approval process.
   - **Publish** events for downstream services (e.g., notifications, shift management).
- **Key Entities:**
   - **Approval**: Represents the  - approval process for an overtime request.
  - **Approver**: Represents the individual responsible for approving or rejecting a request.
- **Aggregates:**
  - **Approval** Aggregate:
    - **Root Entity**: Approval
    - **Entities**: Approver
    - **Description**: Manages the approval process and tracks the status of overtime requests.


### **3.3 Shift Management Service**
### - **Responsibilities:**
- Manage the closure of shifts and assign statuses (X/Y/W).
- Handle planned and unplanned shift updates.
- Publish events for downstream services (e.g., TKS Integration, Notification Service).
- **Key Entities:**
   - **Shift**: Represents a specific shift with start and end times.
   - **EmployeeStatus**: Tracks the status of employees (e.g., present, absent).
- **Aggregates:**
  - **Shift Aggregate:**
    - **Root Entity**: Shift
    - **Entities**: EmployeeStatus
    - **Description**: Manages shift details and tracks employee statuses.

    4. **Class Diagram**

```mermaid
classDiagram
    class Shift {
        +String id
        +String shiftDate
        +String status
        +closeShift()
        +updateShift()
    }

    class EmployeeStatus {
        +String employeeId
        +String shiftId
        +String status
        +updateStatus()
    }

    class Notification {
        +String id
        +String message
        +String recipient
        +sendNotification()
    }

    Shift --> EmployeeStatus : "Tracks"
    Shift --> Notification : "Notifies"
```

Explanation of the Class Diagram
Shift:

Represents a shift with attributes like id, shiftDate, and status (e.g., X, Y, W).
Actions:
closeShift(): Closes a shift.
updateShift(): Updates shift details.
EmployeeStatus:

Tracks the status of employees for a specific shift (e.g., present, absent).
Attributes include employeeId, shiftId, and status.
Actions:
updateStatus(): Updates the status of an employee for a shift.
Notification:

Represents notifications sent to users about shift closures or updates.
Attributes include id, message, and recipient.
Actions:
sendNotification(): Sends a notification to the recipient.
Relationships:

Shift → EmployeeStatus: A shift tracks the status of employees.
Shift → Notification: A shift triggers notifications to users.

### ** Sequence Diagram**
```mermaid
sequenceDiagram
    participant User as User
    participant ShiftManagementService as Shift Management Service
    participant NotificationService as Notification Service
    participant Database as Shift Management CosmosDB
    participant TKSIntegrationService as TKS Integration Service

    User->>ShiftManagementService: Close Shift (POST /shifts/{id}/close)
    ShiftManagementService->>Database: Save Shift Closure and Status (X/Y/W)
    ShiftManagementService->>NotificationService: Notify Users (Shift Closed)
    NotificationService->>User: Send Notification (Shift Closure Confirmation)
    ShiftManagementService->>TKSIntegrationService: Publish ShiftClosed Event
    TKSIntegrationService->>Database: Synchronize Planned vs Executed Shifts
    TKSIntegrationService->>NotificationService: Notify Admin (Synchronization Status)
```
### **Explanation of the Sequence**

1. **User Action:**
    - The user (e.g., Team Leader or Shift Leader) closes a shift via the **Shift Management Service.**
2. **Shift Management Service:**
    - Saves the shift closure and assigns a status (e.g., X, Y, W) in the **Shift Management CosmosDB**.
    - Publishes a ShiftClosed event to the TKS Integration Service.
3. **Notification Service:**
    - Sends notifications to relevant users (e.g., confirmation of shift closure).
4. **TKS Integration Service:**
    - Synchronizes planned vs. executed shifts in the database.
    - Notifies administrators about the synchronization status.
  

### **3.4. TKSIntegration Services**
- **Responsibilities:**
 - Perform cross-checks between planned and executed overtime.
 - Synchronize data with the TKS system.
 - Publish events for downstream services (e.g., notifications, reporting).
- **Key Entities:**
 - **TKSData**: Represents the data synchronized with the TKS system.
 - **Discrepancy**: Represents any mismatch between planned and executed overtime.
- **Aggregates:**
  - **TKS Integration Aggregate:**
    - **Root Entity**: TKSData
    - **Entities**: Discrepancy
    - **Description**: Manages synchronization and reconciliation with the TKS system.


### Summary of Bounded Contexts

| **Bounded Context**   | **Responsibilities**                                                   | **Key Entities**           | **Key Actions**                                                                 |
|------------------------|-----------------------------------------------------------------------|-----------------------------|---------------------------------------------------------------------------------|
| **Overtime Request**   | Manage overtime requests (creation, modification, deletion).          | OvertimeRequest, Employee   | Create, update, delete requests; publish request-related events.               |
| **Approval Workflow**  | Manage multi-level approval processes for overtime requests.          | Approval, Approver          | Approve, reject, modify requests; publish approval-related events.             |
| **Shift Management**   | Manage shift closures and assign statuses (X/Y/W).                   | Shift, EmployeeStatus       | Close, update, delete shifts; publish shift-related events.                    |
| **TKS Integration**    | Synchronize planned vs. executed overtime and resolve discrepancies. | TKSData, Discrepancy        | Synchronize data, resolve discrepancies; publish synchronization-related events.|

---


### 3.4. Shift Management Service
#### **Responsibilities:**
- Manage the closure of shifts and assign statuses (X/Y/W).
- Handle planned and unplanned shift updates.
- Publish events for downstream services (e.g., TKS Integration, Notification Service).
- Ensure compliance with business rules for shift management.

### ***API Actions**
1. **Close a shift:**
- **Endpoint**: POST /shifts/{id}/close
- **Description** : Closes a shift and assigns a status (e.g., X, Y, W).
2. **Retrieve a list of shifts:**
- **Endpoint**: GET /shifts
- **Description**: Fetches all shifts, optionally filtered by date, status, or employee.
3. **Retrieve details of a specific shift:**
- **Endpoint**: GET /shifts/{id}
- **Description**: Fetches detailed information about a specific shift.
4. **Update a shift:**
- **Endpoint**: PUT /shifts/{id}
- **Description**: Updates the details of an existing shift (e.g., start time, end time, or assigned employees).
5. **Delete a shift:**
- **Endpoint**: DELETE /shifts/{id}
- **Description**: Deletes a shift that is no longer needed.
### **Event-Driven Actions**
1. **Publish ShiftClosed event:**
- Triggered when a shift is closed.
- Consumed by the TKS Integration Service to reconcile planned vs. executed shifts.
2. **Publish ShiftUpdated event:**
- Triggered when a shift is updated.
- Consumed by downstream services to reflect changes in the shift.
3. **Publish ShiftDeleted event:**
- Triggered when a shift is deleted.
- Consumed by downstream services to remove the shift from workflows.

### **Validation and Business Logic**
1. **Validate shift closure:**
- Ensure all required tasks are completed before closing a shift.
2. **Check employee assignments:**
- Validate that employees assigned to the shift are eligible and available.
3. **Enforce business rules:**
- Ensure that shift statuses (X/Y/W) comply with company policies.
4. **Audit trail:**
- Maintain a complete history of all actions taken during shift management for auditing purposes.

### **Use Cases**
1. **Planned Shifts:**
- Manage and close shifts that are part of the planned schedule.
2. **Unplanned Shifts:**
- Handle urgent or unexpected shifts.
3. **Audit and Reporting:**
- Provide a history of shift closures and statuses for auditing and reporting purposes.
4. **Integration with TKS:**
- Publish events to synchronize planned vs. executed shifts with the TKS system.

### ********* TKS Integration Service**
#### **Responsibilities:**
- Perform cross-checks between planned and executed overtime.
- Synchronize data with the TKS system.
- Publish events for downstream services (e.g., notifications, reporting).
- Ensure data consistency between the OverTime Management system and TKS.

#### **API Actions**
1. **Synchronize planned vs. executed overtime:**
- Endpoint: POST /tks/synchronize
- Description: Synchronizes planned overtime data with executed data in the TKS system.
2. **Retrieve synchronization status:**
- Endpoint: GET /tks/synchronization-status
- Description: Fetches the status of the last synchronization process.
3. **Retrieve discrepancies:**
-Endpoint: GET /tks/discrepancies
-Description: Retrieves a list of discrepancies between planned and executed overtime.
4. **Force a manual synchronization:**
- Endpoint: POST /tks/manual-sync
- Description: Allows administrators to trigger a manual synchronization with TKS.
5. **Retrieve TKS integration logs:**
- Endpoint: GET /tks/logs
- Description: Fetches logs related to TKS integration for auditing and debugging purposes.
### **Event-Driven Actions**
1. **Publish TKSDataSynchronized event:**
- Triggered when data is successfully synchronized with TKS.
- Consumed by downstream services to confirm synchronization.
2. **Publish TKSDiscrepancyDetected event:**
- Triggered when discrepancies are found between planned and executed overtime.
- Consumed by the Notification Service to alert relevant users.
3. **Publish TKSManualSyncTriggered event:**
- Triggered when a manual synchronization is initiated.
Consumed by downstream services to log the action.

### **Validation and Business Logic**
1. **Validate synchronization data:**
- Ensure that the data being synchronized is complete and accurate.
2. **Check for discrepancies:**
- Identify and log any mismatches between planned and executed overtime.
3. **Enforce synchronization rules:**
- Ensure that synchronization processes comply with company policies and TKS requirements.
4. **Audit trail:**
- Maintain a complete history of synchronization actions and results for auditing purposes.

### **Use Cases**
1. **Planned vs. Executed Reconciliation:**
- Compare planned overtime with executed overtime and resolve discrepancies.
2. **Real-Time Synchronization:**
- Automatically synchronize data with TKS in real-time or on a scheduled basis.
3. **Manual Synchronization:**
- Allow administrators to trigger manual synchronization processes when needed.
4. **Audit and Reporting:**
- Provide a history of synchronization actions and discrepancies for auditing and reporting purposes.


2. **ApprovalWorkflow Service**
**Responsibilities:**
    - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
    - Allow modifications to requests during the approval process.
    - Publish events for downstream services (e.g., notifications, shift management).
**Key Components:**
    - **Database:** Approval Workflow CosmosDB.
    - **API Endpoints:**
        - POST /approvals/{id}/approve: Approve an overtime request.
        - POST /approvals/{id}/reject: Reject an overtime request.
        - GET /approvals/pending: Retrieve pending approvals.
        - GET /approvals/history: Retrieve approval history.
    - **Event-Driven Actions:**
        - Publish ApprovalStarted, ApprovalCompleted, ApprovalRejected.
3. **ShiftManagement Service**
**Responsibilities:**
    - Manage the closure of shifts and assign statuses (X/Y/W).
    - Handle planned and unplanned shift updates.
    - Publish events for downstream services (e.g., TKS Integration, Notification Service).
**Key Components:**
    - **Database:** Shift Management CosmosDB.
    - **API Endpoints:**
        - POST /shifts/{id}/close: Close a shift.
        - GET /shifts: Retrieve all shifts.
        - PUT /shifts/{id}: Update a shift.
        - DELETE /shifts/{id}: Delete a shift.
    - **Event-Driven Actions:**
        - Publish ShiftClosed, ShiftUpdated, ShiftDeleted.

### 5. **Ends points**

description approfondie des responsabilités, composants clés, actions possibles, et événements pour chaque microservice :

1. **OvertimeRequest Service**
**Responsabilités :**
    - Manage the creation, modification, and deletion of overtime requests.
    - Handle both planned and unplanned overtime requests.
    - Validate input data (e.g., employee selection, project, activity).
    - Publish events for downstream services (e.g., approval workflows).
**Key Components:**
    - **Database:** Overtime Request CosmosDB.
    - **API Endpoints:**
        - POST /overtime-requests: Create a new overtime request.
        - GET /overtime-requests: Retrieve all overtime requests.
        - PUT /overtime-requests/{id}: Update an existing overtime request.
        - DELETE /overtime-requests/{id}: Delete an overtime request.
    - **Event-Driven Actions:**
        - Publish OvertimeRequestCreated, OvertimeRequestUpdated, OvertimeRequestDeleted.
2. **ApprovalWorkflow Service**
**Responsibilities:**
    - Manage the multi-level approval process for overtime requests (Coordinator → Department Manager → Project Manager).
    - Allow modifications to requests during the approval process.
    - Publish events for downstream services (e.g., notifications, shift management).
**Key Components:**
    - **Database:** Approval Workflow CosmosDB.
    - **API Endpoints:**
        - POST /approvals/{id}/approve: Approve an overtime request.
        - POST /approvals/{id}/reject: Reject an overtime request.
        - GET /approvals/pending: Retrieve pending approvals.
        - GET /approvals/history: Retrieve approval history.
    - **Event-Driven Actions:**
        - Publish ApprovalStarted, ApprovalCompleted, ApprovalRejected.
3. **ShiftManagement Service**
**Responsibilities:**
    - Manage the closure of shifts and assign statuses (X/Y/W).
    - Handle planned and unplanned shift updates.
    - Publish events for downstream services (e.g., TKS Integration, Notification Service).
**Key Components:**
    - **Database:** Shift Management CosmosDB.
    - **API Endpoints:**
        - POST /shifts/{id}/close: Close a shift.
        - GET /shifts: Retrieve all shifts.
        - PUT /shifts/{id}: Update a shift.
        - DELETE /shifts/{id}: Delete a shift.
    - **Event-Driven Actions:**
        - Publish ShiftClosed, ShiftUpdated, ShiftDeleted.
  
  Certainly! Here is a concise conclusion for your **OverTime Management System** LLD:

---

## 4. **Conclusion**

The **OverTime Management** System is designed as a robust, scalable, and maintainable solution leveraging a **microservices architecture**. Each core services OvertimeRequest, ApprovalWorkflow, ShiftManagement, and TKSIntegration—operates independently, ensuring clear separation of concerns and facilitating easier maintenance and future enhancements.

**Key technical components** such as Azure CosmosDB, Azure Service Bus, and Azure API Management provide reliable data storage, event-driven communication, and secure API management. The system supports complex business processes, including multi-level approvals, real-time shift management, and automated reconciliation with external systems like TKS.

By adopting **domain-driven design** and **clean architecture** principles, the solution ensures flexibility, configurability, and testability. This approach enables the organization to efficiently manage overtime processes, comply with business rules, and provide comprehensive audit and reporting capabilities, ultimately improving operational efficiency and supporting digital transformation goals.

# 1. Overview

## 1.1 Purpose and Scope

This low-level design document outlines the detailed architecture for the Transport Management module based on Domain-Driven Design (DDD) principles and microservices architecture. The design prioritizes configurability, flexibility.

## 1.2 Key Components

The system consists of three primary microservices:

1. Pickup Station Service
2. Transport Planning Service
3. Bus Boarding Service

Each service is supported by:

- Azure CosmosDB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Configuration Service for centralized settings
## 1.2.1 Architecture Overview

## High-Level Architecture
The system follows a microservices architecture with the following components:

```mermaid
flowchart TD
    ApiGateway[Azure API Management] --> PickupStation[Pickup Station Service]
    ApiGateway --> TransportPlanning[Transport Planning Service]
    ApiGateway --> BusBoarding[Bus Boarding Service]

    PickupStation --> StationDB[(Pickup Station CosmosDB)]
    TransportPlanning --> PlanningDB[(Transport Planning CosmosDB)]
    BusBoarding --> BoardingDB[(Bus Boarding CosmosDB)]

    PickupStation <--> EventBus[Azure Service Bus]
    TransportPlanning <--> EventBus
    BusBoarding <--> EventBus

    EventBus -->|StationCreated, StationUpdated| TransportPlanning
    EventBus -->|PlanCreated, PlanUpdated| BusBoarding

    style ApiGateway fill:#b3d9ff,stroke:#333,stroke-width:2px
    style EventBus fill:#ffcccb,stroke:#333,stroke-width:2px
    style PickupStation fill:#d5f5e3,stroke:#333,stroke-width:2px
    style TransportPlanning fill:#d5f5e3,stroke:#333,stroke-width:2px
    style BusBoarding fill:#d5f5e3,stroke:#333,stroke-width:2px
    style StationDB fill:#f9e79f,stroke:#333,stroke-width:2px
    style PlanningDB fill:#f9e79f,stroke:#333,stroke-width:2px
    style BoardingDB fill:#f9e79f,stroke:#333,stroke-width:2px
```
    
#### Explanation of components

1. **API Gateway (Azure API Management)** :
         - Serves as a single point of entry for customers.
         - Manages routing, authentication, and rate limiting.

2. **Pickup Station Service** :
         - Manages the collection stations.
         -Stores data in **Pickup Station CosmosDB**.
         - Publishes and consumes events via **Azure Service Bus**.

3. **Transport Planning Service** :
   - Plans routes and assignments.
   - Stores data in **Transport Planning CosmosDB**.
   - Publishes and consumes events via **Azure Service Bus**.

4. **Bus Boarding Service** :
   - Tracks employee boardings.
   - Stores data in **Bus Boarding CosmosDB**.
   - Publishes and consumes events via **Azure Service Bus**.

5. **Azure Service Bus** :
   - Facilitates asynchronous communication between microservices.
   -Manages events like station updates, schedule changes, etc..

6. **Bases de données CosmosDB** :
   - Each microservice has its own database to respect the principle of data separation.
   - CosmosDB is used for its scalability, low latency, and multi-region support.

7. **Change Feed Processor**: Real-time data synchronization

8. **Configuration Service**: 
   - Centralized configuration management for client-specific settings
9. **Design Patterns**:  
    Design Patterns are proven solutions to common software design problems. 
    Design Patterns help you write code that is more flexible, adaptable, and easier.
    - **Mediator** :  
            The Mediator pattern defines an object that encapsulates how a set of objects interact. This pattern promotes loose coupling by keeping objects from referring to each other explicitly, and it lets you vary their interaction independently.  
    - **Singleton** :  
            The Singleton pattern ensures that a class has only one instance and provides a global point of access to it.
    - **CQRS** :  
           (Command Query Responsibility Segregation) separates read and write operations to improve performance  
    - **Dependency injection** :  
          is a design pattern, not just a programming technique ,that makes a class independent of its dependencies.
## 3. Core components
### **3.1. Pickup Station Service**
#### **Responsibilities :**
- Manage collection stations (creation, update, deletion).
- Fournir les informations sur les stations disponibles.
- Publier des événements lorsqu'une station est ajoutée ou modifiée.
#### Domain Model & Entity
**Class Diagram:**  

```mermaid
graph TD

    Utilisateur["Utilisateur<br>+id: int<br>+nom: string<br>+role: string"]
    Demande["Demande<br>+id: int<br>+typeDemande: string<br>+status: string<br>+dateCreation: datetime"]
    Notification["Notification<br>+id: int<br>+message: string<br>+date: datetime"]
    Role["Role<br>+id: int<br>+nomRole: string<br>+description: string"]
    Station["Station<br>+id: int<br>+nom: string<br>+type: string<br>+localisation: string"]
    Adresse["Adresse<br>+id: int<br>+adresse: string<br>+historiqueModifications: string"]

    Utilisateur -->|crée ou valide| Demande
    Demande -->|assigne| Station
    Demande -->|concerne| Adresse
    Adresse -->|modifie| Station
    Utilisateur -->|reçoit| Notification
    Notification -->|envoyée à| Utilisateur
    Utilisateur -->|a| Role
```
**Entites**

    class Utilisateur {
        +id: int
        +nom: string
        +role: string
    }

    class Demande {
        +id: int
        +typeDemande: string
        +status: string
        +dateCreation: datetime
    }

    class Notification {
        +id: int
        +message: string
        +date: datetime
    }

    class Role {
        +id: int
        +nomRole: string
        +description: string
    }

    class Station {
        +id: int
        +nom: string
        +type: string
        +localisation: string
    }

    class Adresse {
        +id: int
        +adresse: string
        +historiqueModifications: string
    }

    Utilisateur "1" --> "0..*" Demande : "crée ou valide"
    Demande "1" --> "1" Station : "assigne"
    Demande "1" --> "1" Adresse : "concerne"
    Adresse "1" --> "1" Station : "modifie"
    Utilisateur "1" --> "0..*" Notification : "reçoit"
    Notification "1" --> "1" Utilisateur : "envoyée à"
    Utilisateur "1" --> "1" Role : "a"


#### Explications des relations :
**Utilisateur** :
- Can create or validate multiple requests.
- Can receive multiple notifications.
- Is associated with a single Role.
**Demande** :
- A Request may concern the creation or modification of a Station or the modification of an Address.

**Adresse** :
- Can modify a Station, (Transport Agent) can assign a Station to another.

**Notification** :
- Is sent to a User after changes are made to Stations or Addresses.

**Role** :
- Defines the roles associated with a User (Team Leader, Transport Agent, etc.) determine the actions a User can perform.

**Aggregates** :

 An Aggregate is a logical grouping of entities and Value Objects that form a coherent unit of data and business rules..

- **Utilisateur Aggregate** :

Root Entity : **Utilisateur**
Entities :
**Role**,
**Notification**

**Description** :
The User Aggregate manages user information, their roles, and the notifications they receive.
Aggregate Request:

Root Entity : **Demande**

Entities :
**Station**,
**Adresse**,
**Description** : The Request aggregate handles requests related to the creation or modification of stations and addresses.

- **Station Aggregate** :

Root Entity : **Station**

**Entities :**

**Adresse**

Description : L'aggregate Station gère les informations des stations et leurs adresses associées.
**Entities**
Une Entity est un objet métier avec une identité unique.

**Utilisateur** :

**Attributs** :
id: int
nom: string
role: string
Description : Représente un utilisateur du système.

**Role** :

**Attributs** :
id: int
nomRole: string
description: string
Description : Définit les rôles associés à un utilisateur (par exemple, Team Leader, Transport Agent).

**Demande** :  
   **Attributs** :\
   id: int\
   typeDemande: string\
   status: string\
   dateCreation: datetime\
   Description : Représente une demande de création ou modification d'une station ou d'une adresse.

**Station** :

**Attributs** :
id: int
nom: string
type: string
localisation: string
Description : Représente une station de ramassage.
**Notification** :

**Attributs** :
id: int
message: string
date: datetime
Description : Représente une notification envoyée à un utilisateur.
**Adresse** :

**Attributs** :
id: int
adresse: string
historiqueModifications: string
Description : Represents an address associated with a station.
**Value Objects**
A Value Object is an object without its own identity, used to encapsulate business concepts.

**Localisation (dans Station)** :

**Attributs** :
latitude: float
longitude: float
Description : Represents the geographic location of a station.
**HistoriqueModifications (dans Adresse)** :

**Attributs** :
modifications: List<string>
Description : List of changes made to an address.
### **sequence Diagram Route Creation Flow**-

```mermaid
sequenceDiagram
    participant Utilisateur
    participant Demande
    participant Station
    participant Adresse
    participant Notification
    participant Role

    Utilisateur->>Demande: Crée ou valide une demande
    Demande->>Station: Associe une station
    Demande->>Adresse: Concerne une adresse
    Adresse->>Station: Modifie une station
    Utilisateur->>Notification: Reçoit une notification
    Notification->>Utilisateur: Envoyée à l'utilisateur
    Utilisateur->>Role: Est associé à un rôle
```


#### **Exemples d'actions :**
- **API REST** :
  - `GET /stations`: Récupérer la liste des stations.
  - `POST /stations`: Ajouter une nouvelle station.
  - `PUT /stations/{id}`: Mettre à jour une station existante.
  - `DELETE /stations/{id}`: Supprimer une station.
- **Événements publiés** :
  - `StationCreated`
  - `StationUpdated`
  - `StationDeleted`

---

### **3.2 Transport Planning Service**
#### **Responsabilités :**
- Planifier les itinéraires et les affectations des véhicules.
- Optimiser les trajets en fonction des données des stations et des besoins.
- Publier des événements liés aux changements de planification.
#### Domain Model & Entity
**Class Diagram:**

```mermaid
flowchart TD
    WorkPlan[Work Plan<br>+id: String<br>+date: Date<br>+shift: String<br>+status: String<br>+employees: List<Employee>]
    User[User<br>+id: String<br>+name: String<br>+role: String<br>+email: String]
    Shift[Shift<br>+id: String<br>+type: String<br>+startTime: Time<br>+endTime: Time<br>+employees: List<Employee>]
    PickupStation[Pickup Station<br>+id: String<br>+code: String<br>+name: String<br>+zone: String<br>+schedule: String]
    Employee[Employee<br>+id: String<br>+registrationNumber: String<br>+name: String<br>+status: String<br>+shift: Shift]
    Transport[Transport<br>+id: String<br>+minibusId: String<br>+route: Route<br>+departureTime: Time<br>+capacity: int]
    Route[Route<br>+id: String<br>+startStation: Pickup Station<br>+endStation: Pickup Station<br>+schedule: String<br>+minibusId: String]
    TransportProvider[Transport Provider<br>+id: String<br>+name: String<br>+contact: String<br>+routeSheet: File]
    TourTracking[Tour Tracking<br>+id: String<br>+busId: String<br>+employeeStatus: String<br>+geolocation: String<br>+arrivalTime: Time]
    EmployeeStatus[Employee Status<br>+id: String<br>+statusType: String<br>+employeeId: String<br>+updatedBy: User<br>+timestamp: DateTime]

    WorkPlan -->|Includes| Employee
    WorkPlan -->|Associated with| Shift
    Employee -->|Assigned to| Shift
    Employee -->|Assigned to| PickupStation
    Transport -->|Follows| Route
    Transport -->|Managed by| TransportProvider
    Route -->|Contains| PickupStation
    TourTracking -->|Tracks| Transport
    TourTracking -->|Monitors| EmployeeStatus
    EmployeeStatus -->|Belongs to| Employee
    EmployeeStatus -->|Updated by| User
```
**Entites**

classDiagram

    class WorkPlan {
        +id: String
        +date: Date
        +shift: String
        +status: String
        +employees: List<Employee>
    }

    class User {
        +id: String
        +name: String
        +role: String
        +email: String
    }

    class Shift {
        +id: String
        +type: String
        +startTime: Time
        +endTime: Time
        +employees: List<Employee>
    }

    class PickupStation {
        +id: String
        +code: String
        +name: String
        +zone: String
        +schedule: String
    }

    class Employee {
        +id: String
        +registrationNumber: String
        +name: String
        +status: String
        +shift: Shift
    }

    class Transport {
        +id: String
        +minibusId: String
        +route: Route
        +departureTime: Time
        +capacity: int
    }

    class Route {
        +id: String
        +startStation: PickupStation
        +endStation: PickupStation
        +schedule: String
        +minibusId: String
    }

    class TransportProvider {
        +id: String
        +name: String
        +contact: String
        +routeSheet: File
    }

    class TourTracking {
        +id: String
        +busId: String
        +employeeStatus: String
        +geolocation: String
        +arrivalTime: Time
    }

    class EmployeeStatus {
        +id: String
        +statusType: String
        +employeeId: String
        +updatedBy: User
        +timestamp: DateTime
    }

    WorkPlan "1" --> "0..*" Employee : "includes"
    WorkPlan "1" --> "1..*" Shift : "associated with"
    Employee "1" --> "1" Shift : "assigned to"
    Employee "1" --> "1" PickupStation : "assigned to"
    Transport "1" --> "1" Route : "follows"
    Transport "1" --> "1" TransportProvider : "managed by"
    Route "1" --> "1..*" PickupStation : "contains"
    TourTracking "1" --> "1" Transport : "tracks"
    TourTracking "1" --> "1" EmployeeStatus : "monitors"
    EmployeeStatus "1" --> "1" Employee : "belongs to"
    EmployeeStatus "1" --> "1" User : "updated by"


#### **Explications des relations :**
  - Plan de Travail (Work Plan) est associé à Employés (les employés affectés à chaque plan) et à des Quarts de Travail (Shift).
  
  - Employé est assigné à un Shift et une Station de Ramassage.
  
  - Transport est lié à un Itinéraire et à un Fournisseur (Transport Provider) qui gère le bus.
  
  - Itinéraire contient plusieurs Stations de Ramassage et est suivi par le Transport Agent.
  
  - Statut des Employés est mis à jour par le Team Leader et Transport Agent en fonction des informations en temps réel (par exemple, si un employé est présent, absent, ou non planifié).
  
  - Suivi des Tours de Transport (Tour Tracking) est lié aux Transport (minibus) et suit l'état en temps réel des Employés (statut de présence).

### **Aggregates**

 An Aggregate is a logical grouping of entities and Value Objects that form a coherent unit of data and business rules..

- **Utilisateur Aggregate** :
Voici une proposition des **Aggregates** basés sur les entités et relations décrites dans votre document :


### ***1. User Aggregate***
- **Root Entity**: `User`
**Entities:**
- `Role`
- `Notification`
- **Description**:
The `User` aggregate manages user information, their roles, and the notifications they receive.

### ***2. Request Aggregate***
- **Root Entity**: `Request`
**Entities:**
- `Station`
- `Address`
- **Description**:
The `Request` aggregate handles requests related to the creation or modification of stations and addresses.

### ***3. Station Aggregate***
- **Root Entity**: `Station`
**Entities:**
- `Address`
- **Description**:
The `Station` aggregate manages station information and their associated addresses.

### ***4. Transport Aggregate***
- **Root Entity**: `Transport`
**Entities:**
- `Route`
- `TransportProvider`
- **Description**:
The `Transport` aggregate manages routes, transport providers, and related trip information.

### ***5. WorkPlan Aggregate***
- **Root Entity**: `WorkPlan`
**Entities:**
- `Shift`
- `Employee`
- **Description**:
The `WorkPlan` aggregate manages work plans, shifts, and assigned employees.

Summary of Aggregates

| **Aggregate** | **Root Entity** | **Entities**            | **Description**                                                        |
|---------------|-----------------|--------------------------|------------------------------------------------------------------------|
| **User**      | `User`          | `Role`, `Notification`  | Manages users, their roles, and notifications.                        |
| **Request**   | `Request`       | `Station`, `Address`    | Handles requests for creating or modifying stations and addresses.    |
| **Station**   | `Station`       | `Address`               | Manages station information and their associated addresses.           |
| **Transport** | `Transport`     | `Route`, `TransportProvider` | Manages routes and transport providers.                               |
| **WorkPlan**  | `WorkPlan`      | `Shift`, `Employee`     | Manages work plans, shifts, and assigned employees.                   |


**Explanation of Relationships**

**WorkPlan**:  
Associated with employees (assigned to each plan) and shifts.  
**Employee**:  
Assigned to a shift and a pickup station.  
**Transport**:  
Linked to a route and a transport provider managing the bus.  
**Route**:  
Contains multiple pickup stations and is monitored by the transport agent.  
**Employee Status**:  
Updated in real-time by the team leader and transport agent (e.g., present, absent, or unplanned).  
**Tour Tracking**:  
Tracks transport (minibus) and monitors the real-time status of employees.
If you need further details or modifications, feel free to ask!
### **Sequence Diagram Route Transport Planning**

```mermaid
sequenceDiagram
    participant User
    participant Role
    participant Notification
    participant Request
    participant Station
    participant Address
    participant WorkPlan
    participant Shift
    participant Employee
    participant Transport
    participant Route
    participant TransportProvider
    participant TourTracking
    participant EmployeeStatus

    User->>Role: Assigns a role to the user
    User->>Notification: Receives notifications
    Request->>Station: Creates or modifies a station
    Request->>Address: Creates or modifies an address
    Station->>Address: Associates an address with a station
    WorkPlan->>Employee: Assigns employees to the work plan
    WorkPlan->>Shift: Associates shifts with the work plan
    Employee->>Shift: Assigned to a shift
    Employee->>Station: Assigned to a pickup station
    Transport->>Route: Linked to a route
    Transport->>TransportProvider: Managed by a transport provider
    Route->>Station: Contains multiple pickup stations
    TourTracking->>Transport: Tracks the minibus
    TourTracking->>EmployeeStatus: Monitors the real-time status of employees
    EmployeeStatus->>User: Updated by the team leader or transport agent
```

#### **Exemples d'actions :**
- **API REST** :
  - `GET /plans`: Récupérer les plans de transport.
  - `POST /plans`: Créer un nouveau plan de transport.
  - `PUT /plans/{id}`: Mettre à jour un plan existant.
  - `DELETE /plans/{id}`: Supprimer un plan.
- **Événements publiés** :
  - `PlanCreated`
  - `PlanUpdated`
  - `PlanDeleted`

---

### **3.3 Bus Boarding Service**
#### **Responsabilités :**
- Suivre les embarquements des employés dans les bus.
- Gérer les validations des embarquements (par exemple, via des QR codes ou des badges).
- Publier des événements liés aux embarquements.

#### Domain Model & Entity
**Class Diagram:**

```mermaid
classDiagram
    class Operateur {
        -id: String
        -nomComplet: String
        -badgePerdu: Boolean
        -planifie: Boolean
    }

    class Embarquement {
        -dateHeure: DateTime
        -statut: String
        -typeAcces: String
    }

    class Terminal {
        -id: String
        -emplacement: String
    }

    class Statut {
        -typeStatut: String
        -horodatage: DateTime
    }

    class Bus {
        -id: String
        -capaciteMax: int
        -gpsPosition: String
    }

    class Trajet {
        -id: String
        -heureDepart: DateTime
        -heureArrivee: DateTime
        -placesLibres: int
        -itineraire: String
    }

    class Responsable {
        -id: String
        -nom: String
    }

    class OptimisationService {
        +calculerItineraire(): void
        +optimiserPlaces(): void
    }

    class NotificationSystem {
        +envoyerNotification(): void
    }

    Operateur "1" --> "0..*" Embarquement
    Embarquement "1" --> "1" Terminal
    Embarquement "1" --> "1" Statut
    Embarquement "1" --> "1" Bus
    Trajet "1" --> "1..*" Bus
    Trajet "1" --> "1" Responsable
    Trajet "1" --> "1" OptimisationService : "optimisé par"
    NotificationSystem "1" --> "1" Statut : "utilise"
```    

---
**Enities**  
classDiagram
    class Operator {
        -id: String
        -fullName: String
        -badgeLost: Boolean
        -scheduled: Boolean
    }

    class Boarding {
        -dateTime: DateTime
        -status: String
        -accessType: String
    }

    class Terminal {
        -id: String
        -location: String
    }

    class Status {
        -statusType: String
        -timestamp: DateTime
    }

    class Bus {
        -id: String
        -maxCapacity: int
        -gpsPosition: String
    }

    class Route {
        -id: String
        -departureTime: DateTime
        -arrivalTime: DateTime
        -availableSeats: int
        -itinerary: String
    }

    class Responsible {
        -id: String
        -name: String
    }

    class OptimizationService {
        +calculateItinerary(): void
        +optimizeSeats(): void
    }

    class NotificationSystem {
        +sendNotification(): void
    }

    Operator "1" --> "0..*" Boarding
    Boarding "1" --> "1" Terminal
    Boarding "1" --> "1" Status
    Boarding "1" --> "1" Bus
    Route "1" --> "1..*" Bus
    Route "1" --> "1" Responsible
    Route "1" --> "1" OptimizationService : "optimized by"
    NotificationSystem "1" --> "1" Status : "uses"

### Explanation of Relationships:
1. **Operateur**:
   - Can manage multiple **Embarquements**.
2. **Embarquement**:
   - Is associated with a **Terminal**, a **Statut**, and a **Bus**.
3. **Trajet**:
   - Includes multiple **Buses** and is managed by a **Responsable**.
   - Is optimized by the **OptimisationService**.
4. **NotificationSystem**:
   - Sends notifications based on the **Statut**.

**Aggregates** :
Here is the requested **Aggregate** description 

---

### **1. User Aggregate**
- **Root Entity**: `User`
- **Entities**:
  - `Role`
  - `Notification`
- **Description**:  
  The `User` aggregate manages user information, their roles, and the notifications they receive.

---

### **2. Request Aggregate**
- **Root Entity**: `Request`
- **Entities**:
  - `Station`
  - `Address`
- **Description**:  
  The `Request` aggregate handles requests related to the creation or modification of stations and addresses.

---

### **3. Station Aggregate**
- **Root Entity**: `Station`
- **Entities**:
  - `Address`
- **Description**:  
  The `Station` aggregate manages station information and their associated addresses.

---

### **4. Transport Aggregate**
- **Root Entity**: `Transport`
- **Entities**:
  - `Route`
  - `TransportProvider`
- **Description**:  
  The `Transport` aggregate manages routes, transport providers, and related trip information.

---

### **5. WorkPlan Aggregate**
- **Root Entity**: `WorkPlan`
- **Entities**:
  - `Shift`
  - `Employee`
- **Description**:  
  The `WorkPlan` aggregate manages work plans, shifts, and assigned employees.

---

Here is the **Summary of Aggregates** in a structured table format:

---

### **Summary of Aggregates**

| **Aggregate**       | **Root Entity** | **Entities**                     | **Description**                                                        |
|----------------------|-----------------|-----------------------------------|------------------------------------------------------------------------|
| **User**            | `User`          | `Role`, `Notification`           | Manages user information, their roles, and the notifications they receive. |
| **Request**         | `Request`       | `Station`, `Address`             | Handles requests for creating or modifying stations and addresses.     |
| **Station**         | `Station`       | `Address`                        | Manages station information and their associated addresses.            |
| **Transport**       | `Transport`     | `Route`, `TransportProvider`     | Manages routes, transport providers, and related trip information.     |
| **WorkPlan**        | `WorkPlan`      | `Shift`, `Employee`              | Manages work plans, shifts, and assigned employees.                    |

### **Sequence Diagram Route Bus Boarding**
```mermaid
sequenceDiagram
    participant Operator
    participant Boarding
    participant Terminal
    participant Status
    participant Bus
    participant Route
    participant Responsible
    participant OptimizationService
    participant NotificationSystem

    Operator->>Boarding: Plans a boarding
    Boarding->>Terminal: Associates a terminal
    Boarding->>Status: Updates the status
    Boarding->>Bus: Assigns a bus
    Route->>Bus: Adds a bus to the route
    Route->>Responsible: Assigns a responsible person
    Route->>OptimizationService: Optimizes the itinerary
    NotificationSystem->>Status: Sends a notification based on the status
```


#### **Exemples d'actions :**
- **API REST** :
  - `GET /boardings`: Récupérer les données d'embarquement.
  - `POST /boardings`: Enregistrer un nouvel embarquement.
  - `PUT /boardings/{id}`: Mettre à jour les informations d'un embarquement.
  - `DELETE /boardings/{id}`: Supprimer un enregistrement d'embarquement.
- **Événements publiés** :
  - `BoardingRegistered`
  - `BoardingUpdated`
  - `BoardingDeleted`

### **3.2. Communication entre les microservices**
- **Pickup Station Service** publie des événements (`StationCreated`, `StationUpdated`) consommés par **Transport Planning Service** pour mettre à jour les plans.
- **Transport Planning Service** publie des événements (`PlanCreated`, `PlanUpdated`) consommés par **Bus Boarding Service** pour ajuster les embarquements.
- Tous les microservices utilisent **Azure Service Bus** pour la communication asynchrone.

## 3.4 clean Architecture of Each microservice 
for the implementation of microservices we will use clean architecture
Key Principles of Clean Architecture
1.	Independence:
o	The business logic (core) should not depend on external frameworks, databases, or UI.
o	Changes in external systems should not affect the core logic.
2.	Separation of Concerns:
o	Divide the system into layers, each with a specific responsibility.
3.	Dependency Rule:
o	Dependencies should always point inward, toward the core business logic.
4.	Testability:
o	The architecture makes it easy to test the core business logic without relying on external systems.
**Sequence**
```mermaid
sequenceDiagram
    participant Controller as Controller (Interface Adapter)
    participant UseCase as Use Case
    participant Entity as Entity
    participant Repository as Repository (Framework/Driver)

    Controller->>UseCase: Receives request and forwards it
    UseCase->>Entity: Applies business rules
    UseCase->>Repository: Fetches or updates data
    Repository->>UseCase: Returns data
    UseCase->>Controller: Returns response
```

### 3.5. Key Features of the Solution 
##
 1. Microservices Architecture:
    - Each service is independent and manages its own domain.
    - Services communicate asynchronously using Azure Service Bus.
2. Domain-Driven Design (DDD):

   - The solution follows DDD principles, with clear separation of concerns:
     - Domain Layer: Contains core business logic and entities.
    - Application Layer: Handles use cases and orchestrates business logic.
    - Infrastructure Layer: Manages data persistence and external integrations.
3. Event-Driven Communication:
    - Services publish and consume events via Azure Service Bus to ensure loose coupling.
4. Scalability:
    - Each service can be scaled independently based on load.
5. CosmosDB Integration:
    - Each service has its own CosmosDB database for data separation and scalability.
   ##

### 3.6. **How the Solution Works**
1. Client Interaction:
   - The client sends requests to the API Gateway.
   - The gateway routes the requests to the appropriate microservice.
2. Pickup Station Management:
   - The Pickup Station Service handles CRUD operations for pickup stations.
   - Publishes events like StationCreated to notify other services.
3. Transport Planning:
   - The Transport Planning Service consumes station-related events and updates transport plans.
   - Publishes events like PlanCreated for downstream services.
4. Bus Boarding:
   - The Bus Boarding Service tracks employee boardings and validates them.
   - Consumes transport plan events to adjust boarding processes.
## 3.7. Event-Driven Architecture

### 3.7.1 Event Schema

```typescript
interface DomainEvent {
  id: string;
  type: string;
  timestamp: Date;
  correlationId: string;
  data: any;
  metadata: {
    producer: string;
    version: string;
  };
}
```

### 3.7.2 Key Events and Event Flow

1. **Station Management Events:**

   - `StationCreated`
   - `StationUpdated`
   - `StationAssigned`
   - `AddressChangeRequested`
   - `AddressChangeApproved`

2. **Transport Planning Events:**

   - `TransportPlanCreated`
   - `TransportPlanUpdated`
   - `TransportPlanPublished`
   - `ShiftAssignmentAdded`
   - `ShiftAssignmentRemoved`
   - `MinibusAllocationUpdated`

3. **Bus Boarding Events:**
   - `PassengerBoarded`
   - `BoardingStatusChanged`

### 3.7.3 Event Flow Diagram

```mermaid
flowchart LR
    PickupService[Pickup Station Service] --> |produces| Events1[StationCreated\nStationUpdated\nStationAssigned\nAddressChangeRequested\nAddressChangeApproved]
    TransportService[Transport Planning Service] --> |produces| Events2[TransportPlanCreated\nTransportPlanPublished\nShiftAssignmentAdded\nMinibusAllocationUpdated]
    BoardingService[Bus Boarding Service] --> |produces| Events3[PassengerBoarded\nBoardingStatusChanged]

    Events1 --> ServiceBus{Azure Service Bus}
    Events2 --> ServiceBus
    Events3 --> ServiceBus

    ServiceBus --> |consumes| PickupService
    ServiceBus --> |consumes| TransportService
    ServiceBus --> |consumes| BoardingService

    subgraph "Event Topics"
        ServiceBus
    end
```
**3.7.4 Change Feed**
A Change Feed is a change diffusion mechanism that allows you to react in real time to data insertions, updates, or deletions in a database. It is very useful for microservices architectures because it allows you to efficiently synchronize or propagate events between services

**Sequence Diagram for Change Feed**
```mermaid
sequenceDiagram
    participant CosmosDB as Azure Cosmos DB
    participant ChangeFeed as Change Feed
    participant Processor as Change Feed Processor
    participant Downstream as Downstream System

    Note over CosmosDB: Data is inserted or updated
    CosmosDB->>ChangeFeed: Capture changes (inserts/updates)
    ChangeFeed->>Processor: Push changes to the processor
    Processor->>Downstream: Process and forward changes
    Downstream->>Processor: Acknowledge processing
```
### 4. Morocco VS EMEA/NA:
## 4.1 **Address / Pickup Point Update**

Impacted User Stories:

1: Weekly planning based on addresses.
2: Communication of the pickup station.

| **Country**         | **Current Process**                                      | **Impact on the System**                                                                 |
|----------------------|----------------------------------------------------------|------------------------------------------------------------------------------------------|
| 🇷🇸 **Serbia**       | Transport manager manually prepares the plan from the HR database. | - Limited automation. <br> - Requires a simple planning system based on static data.     |
| 🇹🇳 **Tunisia**      | Data collected via email; manual consolidation process.  | - The system must allow centralized data entry. <br> - Reduce dependency on email/Excel. |
| 🇹🇷 **Turkey**       | Semi-automated system with AI + human validation.        | - Possible alignment with dynamic optimization user stories. <br> - Advanced use case to prioritize in implementation. |
|  **North America** | Transporters provide occupancy rates; fixed schedules.   | - Less flexibility. <br> - The system should primarily be used for reporting and occupancy tracking rather than dynamic planning. |

 North America	Address managed in Workday, which is the source of truth.	- The system must integrate Workday as the primary source for addresses. <br> - Dependency on an external database.

 ## 4.2 **Generation and Validation of the Transport Plan**
Impacted User Stories:

US201: Generation of the plan.
US204: Calculation of the number of minibuses.
US206: Route optimization.

| **Country**         | **Current Process**                                      | **Impact on the System**                                                                 |
|----------------------|----------------------------------------------------------|------------------------------------------------------------------------------------------|
| 🇷🇸 **Serbia**       | Transport manager manually prepares the plan from the HR database. | - Limited automation. <br> - Requires a simple planning system based on static data.     |
| 🇹🇳 **Tunisia**      | Data collected via email; manual consolidation process.  | - The system must allow centralized data entry. <br> - Reduce dependency on email/Excel. |
| 🇹🇷 **Turkey**       | Semi-automated system with AI + human validation.        | - Possible alignment with dynamic optimization user stories. <br> - Advanced use case to prioritize in implementation. |
|  **North America** | Transporters provide occupancy rates; fixed schedules.   | - Less flexibility. <br> - The system should primarily be used for reporting and occupancy tracking rather than dynamic planning. |

## 4.3 **Boarding and Bus Tracking**
Impacted User Stories:

US202: GPS data / real-time tracking.
US203: Who is on board?
US101 – US108: Boarding process.

| **Country**         | **Current Process**                                      | **Impact on the System**                                                                 |
|----------------------|----------------------------------------------------------|------------------------------------------------------------------------------------------|
| 🇹🇷 **Turkey**       | QR code boarding system under development.               | - The system must integrate QR scanning with the bus and the list of planned operators. <br> - Ensure mobile/terminal compatibility. |
| **Other Countries** (Serbia, Tunisia, NA) | No real-time tracking or scanning system in place.         | - High added value to deploy a common boarding system (badge/QR code). <br> - Enable real-time presence tracking. |


### 5. Localization geospatial

**localization geospatial et GeoJSON dans Azure Cosmos DB**    
Azure Cosmos DB for NoSQL supports geospatial data and the GeoJSON format, allowing you to store, query, and analyze location data directly in the database. This is particularly useful for applications that require location-aware capabilities, such as real-time tracking, route planning, or finding points of interest

1. **What is GeoJSON?**
GeoJSON is a standard format based on JSON for representing geospatial data. It is used to describe geographic objects such as:

**Points**: Represent a single coordinate (latitude, longitude).
**LineStrings**: Represent paths or routes.
**Polygons***: Represent areas or regions.
```c#
Example of GeoJSON:

{
  "type": "Point",
  "coordinates": [45.123, -93.456]
}
```
2. **Geospatial Features in Azure Cosmos DB**
Azure Cosmos DB supports geospatial data through GeoJSON types and provides features to query this data.

Supported Geospatial Data Types:
Point: A single coordinate (latitude, longitude).
LineString: A series of connected points representing a path.
Polygon: An area defined by multiple connected points.
**Example of a GeoJSON** Document in Cosmos DB:
```javascript
{
  "id": "1",
  "name": "Pickup Station",
  "location": {
    "type": "Point",
    "coordinates": [45.123, -93.456]
  }
}
```
3. **Geospatial Indexing**
Azure Cosmos DB automatically indexes geospatial data, enabling fast and efficient queries on this data.

Example of Geospatial Indexing:
Indexing is enabled by default for properties containing GeoJSON data. You can configure indexing in the indexing policy file.

4. **Geospatial Queries**
Azure Cosmos DB supports advanced geospatial queries to retrieve location-based data.

**Examples of Geospatial Queries:**
1. Find points near a location:
```javascript
SELECT * FROM c
WHERE ST_DISTANCE(c.location, { "type": "Point", "coordinates": [45.123, -93.456] }) < 1000
```
-This query finds all points within 1,000 meters of a given location.

2. **Find points within an area (polygon):**
```javascript
SELECT * FROM c
WHERE ST_WITHIN(c.location, {
    "type": "Polygon",
    "coordinates": [
        [[45.0, -93.0], [46.0, -93.0], [46.0, -94.0], [45.0, -94.0], [45.0, -93.0]]
    ]
})
```
- This query finds all points within a defined polygon.
3. **Calculate the distance between two points:**
```javascript
SELECT ST_DISTANCE(c.location, { "type": "Point", "coordinates": [45.123, -93.456] }) AS distance
FROM c

```
- This query calculates the distance between a stored point and a given location.
5. **Use Cases**
    1. Real-Time Tracking:
       - **Track** vehicles or employees in real-time by storing their GPS coordinates in Cosmos DB.
    2. **Route Planning:**
       - Use geospatial data to optimize routes based on pickup stations and destinations.
    3. **Proximity Search:**
       - Find the nearest pickup stations or points of interest to a given location.
    4. **Geospatial Analysis:**
       - Identify areas with high activity density or optimize station locations.
6. **Benefits** 
        of Using Cosmos DB for Geospatial Data
7.  **High Performance:**
    - Geospatial queries are fast due to automatic indexing.
8.  **Scalability:**  
   - Cosmos DB can handle large volumes of geospatial data with low latency.
9.  Multi-Region Availability:
   - Data is available across multiple regions, making it ideal for global applications.
10. **Easy Integration:**  
   - Cosmos DB integrates seamlessly with other Azure services like Azure Maps, Azure Functions, and Azure Event Hubs.
11. **Changes related to GPS tracking**
    
4. **Integration of GPS coordinates:**
    Add a field to store real-time GPS coordinates.
**Exemple**

```javascript
 {
  "incidentReport": {
    "date": "2025-05-01",
    "description": "Panne moteur",
    "resolved": false
  }
}
---

6. ** Conclusion**
This low-level design document provides a comprehensive blueprint for implementing the Transport Management Module using Domain-Driven Design (DDD) principles and a microservices architecture. The design emphasizes the following key aspects:

1. **Modularity**: 
    - Clear separation of concerns across bounded contexts ensures that each microservice is focused on a specific domain, making the system easier to understand and maintain.
2. **Configurability**:
    - The architecture supports dynamic configurations to meet country-specific requirements, ensuring flexibility for diverse client needs.
3. **Scalability** : 
    - The event-driven architecture, combined with independent scaling of microservices, ensures that the system can handle varying loads efficiently.
4. **Maintainability** : 
    - Clean domain models, encapsulated business logic, and adherence to design patterns like CQRS and Event Sourcing make the system easier to maintain and extend.
5. **Adaptability** : 
    - The extensible design allows for the seamless addition of new features and services to accommodate future requirements.

6. **Security** : 
    - Robust security measures, including OAuth 2.0, RBAC, TLS encryption, and Azure DDoS Protection, ensure data protection and secure communication across all microservices.
6. **Reliability**:
    - The use of Azure Service Bus for asynchronous communication and CosmosDB for scalable data storage ensures high availability and fault tolerance.

**Final Thoughts** :
   - This design aligns with industry best practices for cloud-native applications, ensuring reliability, performance, and security. It provides a flexible and scalable foundation to meet the specific requirements of clients across different regions while maintaining a focus on maintainability and adaptability for future growth
