# MCP Tools for Teams Chat Integration

## Table of Contents

1. [Introduction](#introduction)
2. [Request Service Tools: Module 1](#request-service-tools--module-1)
   - [Request Management Tools](#request-management-tools)
   - [Request Information Tools](#request-information-tools)
   - [Request Status Tracking](#request-status-tracking)
   - [Multi-Operator Request Tools](#multi-operator-request-tools)
   - [Workflow Integration Tools](#workflow-integration-tools)
   - [Notifications and Alerts](#notifications-and-alerts)
   - [Dynamic Request Fields By Type](#dynamic-request-fields-by-type)
3. [Crew Management Tools: Module 2](#crew-management-tools--module-2)
   - [Organizational Structure Tools](#organizational-structure-tools)
   - [Member Management Tools](#member-management-tools)
   - [Crew Administration Tools](#crew-administration-tools)
   - [Workflow and Notification Tools](#workflow-and-notification-tools)
4. [Example Chatbot Interactions](#example-chatbot-interactions)
   - [Request Service Examples](#request-service-examples)
   - [Crew Management Examples](#crew-management-examples)

## Introduction

This document outlines the comprehensive set of MCP tools available for integration with Microsoft Teams chat. These tools enable users to manage requests, track workflows, and administer crew structures directly through conversational interfaces in Teams.

The tools are organized into functional modules:

- **Module 1: Request Service Tools** - For creating and managing various types of requests
- **Module 2: Crew Management Tools** - For managing organizational structure and team assignments

Each tool includes detailed descriptions, capabilities, role-based access information, and example interactions to demonstrate functionality.

## Request Service Tools : Module 1

### Request Management Tools

1. **Create Personal Request**

   - **Description**: Create a new request for yourself
   - **Capabilities**:
     - Submit different request types:
       - Absence Authorization
       - Holiday Leave
       - Movement Requests
       - Overtime
       - Payroll Claims
       - Clocking Corrections
     - Specify start and end dates, reason, and other type-specific details
     - Automatically starts approval workflow
   - **Role-Based Access**:
     - Regular employees can create personal requests for themselves
     - Specific request types may be limited based on employee role and department
   - **Request Type Examples**:
     - **Absence Authorization**:
       - Required fields: Start date/time, end date/time, reason
       - Optional fields: Supporting documentation, substitute operator
       - Approval flow: Direct manager → Department head (if > 3 days)
     - **Holiday Leave**:
       - Required fields: Start date, end date, leave type
       - Optional fields: Notes, handover information
       - Approval flow: Direct manager → HR (validation)
     - **Overtime**:
       - Required fields: Date, hours, justification
       - Optional fields: Project code, department code
       - Approval flow: Direct manager → Finance (if > 5 hours)

2. **Create On-Behalf Request**

   - **Description**: Team Leaders can create requests on behalf of one or multiple operators
   - **Capabilities**:
     - Select one or multiple target operators
     - Choose request type
     - Provide request details specific to the selected type
     - Create a single request associated with multiple operators
   - **Role-Based Access**:
     - Team Leaders/Managers can create requests for their direct reports
     - Department Heads can create requests for any employee in their department
     - HR can create certain request types for any employee
   - **Multi-Operator Examples**:
     - **Team Overtime**:
       - Required fields: Date, hours, reason, list of operators
       - Optional fields: Project code
       - Approval flow: Department head → Finance (single approval for all operators)
     - **Group Movement Request**:
       - Required fields: Current location, new location, date effective, list of operators
       - Optional fields: Reason, equipment needs
       - Approval flow: Department heads of both locations

3. **View Request Details**

   - **Description**: Retrieve detailed information about a specific request
   - **Capabilities**:
     - View request status, type, creation date, and other metadata
     - See request-specific data (dates, reasons, hours, etc.)
     - Check workflow instance associated with the request
     - View approval chain and current approval stage
     - Access any attached documents or supporting materials
   - **Role-Based Access**:
     - Requestors can view full details of their own requests
     - Approvers can view requests pending their approval
     - HR/Admin can view all requests

4. **List My Requests**

   - **Description**: List all requests created by or targeted at the current user
   - **Capabilities**:
     - Filter by request type
     - Filter by status (Pending, Approved, Rejected)
     - Sort by creation date or update date
     - Paginate through results
     - View as requester or approver
     - Set date range for historical requests
   - **Example Filters**:
     - "Show my pending holiday requests"
     - "Show all requests I need to approve today"
     - "Show all overtime requests from last month"
     - "Show rejected absence requests from my team"

5. **Update Request Status**
   - **Description**: Update the status of an existing request
   - **Capabilities**:
     - Approve or reject requests
     - Add comments to explain decision
     - Trigger appropriate workflow transitions
     - Request additional information before decision
     - Delegate approval to another authorized person
   - **Role-Based Access**:
     - Only designated approvers in the workflow can update status
     - Auto-approvals based on defined rules
     - Escalation path if approver doesn't respond within timeframe

### Request Information Tools

6. **Get Request Requirements**

   - **Description**: Learn about requirements for specific request types before creating them
   - **Capabilities**:
     - View required and optional fields for any request type
     - See approval flow for specific request types
     - Check eligibility criteria (e.g., holiday leave balance)
     - Learn about documentation requirements
     - Understand timeframes and deadlines
   - **Examples**:
     - "What information do I need for a holiday leave request?"
     - "Who approves overtime requests?"
     - "What documents do I need for a clocking correction?"

7. **Check Request Availability**
   - **Description**: Check if you're eligible to make a specific type of request
   - **Capabilities**:
     - Check leave balance for holiday requests
     - Verify if similar requests are already pending
     - Check blackout dates or restricted periods
     - Verify if prerequisites have been met
   - **Examples**:
     - "Am I eligible for holiday leave next month?"
     - "How many sick days do I have left this year?"
     - "Can my team request overtime this weekend?"

### Request Status Tracking

8. **Check Request Status**

   - **Description**: Quickly check the current status of a request
   - **Capabilities**:
     - Get real-time status updates
     - View approval progress
     - See status change timestamps
     - Identify current approver/stage in the workflow
     - Get estimated time to completion based on similar requests
   - **Status Types**:
     - Pending Submission
     - Submitted
     - In Review
     - Pending Approval (with current approver)
     - Additional Information Requested
     - Approved
     - Partially Approved
     - Rejected
     - Cancelled
     - Implementation In Progress
     - Completed

9. **Get Request History**
   - **Description**: View the complete history of a request
   - **Capabilities**:
     - Timeline of status changes
     - List of approvers and their decisions
     - Comments associated with each status change
     - Audit trail of all modifications
     - Time spent at each stage
   - **History Details**:
     - Created: Who created the request and when
     - Modifications: Any changes to the request after submission
     - Approval Steps: Each approval/rejection with timestamp and comments
     - Implementation: When the request was executed
     - Completion: Final resolution of the request

### Multi-Operator Request Tools

10. **Manage Team Requests**
    - **Description**: Team Leaders can view and manage requests for their team
    - **Capabilities**:
      - View all requests created for team members
      - Check which operators are associated with each request
      - Filter requests by operator, type, or status
      - Create bulk requests for multiple team members
      - Review team leave calendar/overtime schedule
      - Check for conflicts or overlapping requests
    - **Team Analytics**:
      - View team absence patterns
      - Check remaining leave balances for all team members
      - See upcoming approved time off
      - Monitor overtime distribution

### Workflow Integration Tools

11. **Explore Workflow Templates**

    - **Description**: View available workflow templates for different request types
    - **Capabilities**:
      - See visual representation of workflow steps
      - Understand approval paths and conditions
      - Check expected timelines for different workflows
      - Identify key approvers and decision points
    - **Examples**:
      - "Show me the workflow for holiday leave requests"
      - "What's the approval process for overtime exceeding 5 hours?"
      - "Who needs to approve my movement request?"

12. **Monitor Workflow Progress**

    - **Description**: Track where a request is in its workflow process
    - **Capabilities**:
      - See visual representation of current stage in workflow
      - Identify completed steps and remaining steps
      - View estimated time to completion
      - Check for bottlenecks or delays
      - Receive notifications about stage transitions
    - **Examples**:
      - "Where is my absence request in the approval process?"
      - "Who is currently reviewing my overtime request?"
      - "Why is my holiday leave request delayed?"

13. **Request Workflow Intervention**
    - **Description**: Request manual intervention in a workflow process
    - **Capabilities**:
      - Request expedited processing
      - Ask for approval delegation if approver is unavailable
      - Request workflow restart if errors occurred
      - Escalate stalled requests to higher management
    - **Intervention Types**:
      - Expedite: Request faster processing of an urgent request
      - Delegate: Request approval assignment to an alternate approver
      - Escalate: Move request to higher-level management for resolution
      - Cancel: Withdraw a submitted request

### Notifications and Alerts

14. **Request Notifications**

    - **Description**: Get real-time notifications about request updates
    - **Capabilities**:
      - Notifications for status changes
      - Alerts for new requests requiring approval
      - Reminders for pending requests
      - Escalation alerts for time-sensitive requests
      - Notifications for requests approaching deadlines
    - **Notification Settings**:
      - Configure notification frequency and channels
      - Set priority levels for different notification types
      - Enable/disable specific notification categories
      - Schedule daily/weekly summaries

15. **Schedule Reminders**
    - **Description**: Set up reminders for important request deadlines
    - **Capabilities**:
      - Remind about pending approvals
      - Alert before leave/absence starts
      - Notify about requests approaching SLA deadlines
      - Remind about required actions or documentation
    - **Reminder Types**:
      - Pre-approval reminders
      - Deadline approach warnings
      - Return-to-work reminders
      - Incomplete request notifications

## Dynamic Request Fields By Type

### Absence Authorization Fields

- **Required Fields**:
  - Start Date/Time
  - End Date/Time
  - Reason Category (Medical, Personal, Family, Other)
  - Detailed Reason
- **Optional Fields**:
  - Supporting Documentation
  - Contact During Absence
  - Substitute Operator
  - Return Date Confirmation

### Holiday Leave Fields

- **Required Fields**:
  - Start Date
  - End Date
  - Leave Type (Annual, Sick, Maternity/Paternity, Unpaid)
  - Total Days
- **Optional Fields**:
  - Contact During Leave
  - Handover Notes
  - Return Date Confirmation

### Movement Request Fields

- **Required Fields**:
  - Current Location/Department
  - New Location/Department
  - Effective Date
  - Duration (Temporary/Permanent)
- **Optional Fields**:
  - Reason for Movement
  - Skills/Qualifications
  - Equipment Requirements
  - Training Needs

### Overtime Fields

- **Required Fields**:
  - Date(s)
  - Start Time
  - End Time
  - Total Hours
  - Justification
- **Optional Fields**:
  - Project Code
  - Department Code
  - Manager Pre-approval Reference
  - Cost Center

### Payroll Claims Fields

- **Required Fields**:
  - Claim Type
  - Amount
  - Date Incurred
  - Justification
- **Optional Fields**:
  - Receipt/Documentation
  - Cost Center
  - Project Code
  - Payment Method Preference

### Clocking Corrections Fields

- **Required Fields**:
  - Date
  - Incorrect Time
  - Correct Time
  - Reason for Correction
- **Optional Fields**:
  - Supporting Evidence
  - Supervisor Witness
  - System Error Reference

## Example Chatbot Interactions

### Request Service Examples

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👤</div>
    <div class="message-content">
      <div class="sender">User</div>
      <div class="text">Create a new absence request for next Monday from 9 AM to 5 PM for a doctor's appointment</div>
    </div>
    <div class="timestamp">10:23 AM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        I've created an Absence Authorization request for Monday, November 6th from 9 AM to 5 PM with the reason 'doctor's appointment'.
        <br><br>
        <span class="highlight">Request ID: REQ-123</span>
        <br>
        <span class="status pending">Status: Pending approval from Jane Smith (Manager)</span>
        <br><br>
        You'll receive a notification when your request has been reviewed.
      </div>
    </div>
    <div class="timestamp">10:23 AM</div>
  </div>
</div>

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👥</div>
    <div class="message-content">
      <div class="sender">Team Leader</div>
      <div class="text">Create an overtime request for John Smith and Sarah Johnson for next Friday from 5 PM to 7 PM due to project deadline</div>
    </div>
    <div class="timestamp">11:45 AM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        I've submitted an Overtime request for John Smith and Sarah Johnson for Friday, November 3rd from 5 PM to 7 PM with the reason 'project deadline'.
        <br><br>
        <span class="highlight">Request ID: REQ-456</span><br>
        <span class="status pending">Status: Pending approval from Michael Brown (Department Head)</span>
        <br><br>
        <span class="note">Note: A single request has been created for both team members.</span>
        <br><br>
        Both team members and you will be notified when there's an update.
      </div>
    </div>
    <div class="timestamp">11:46 AM</div>
  </div>
</div>

### Checking Status and Information

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👤</div>
    <div class="message-content">
      <div class="sender">User</div>
      <div class="text">Show me the status of my holiday leave request from last week</div>
    </div>
    <div class="timestamp">9:15 AM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        <span class="highlight">Holiday Leave Request (REQ-789)</span>
        <br>
        <span class="status approved">Status: Approved ✓</span>
        <br><br>
        <strong>Request Details:</strong>
        <ul>
          <li>Submitted: October 24th, 2023</li>
          <li>Leave Period: November 15-19, 2023 (5 days)</li>
          <li>Approved by: Jane Doe (Manager) on October 25th</li>
          <li>Comments: "Enjoy your time off!"</li>
        </ul>
        <br>
        Your calendar has been updated with this approved leave.
      </div>
    </div>
    <div class="timestamp">9:16 AM</div>
  </div>
</div>

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👤</div>
    <div class="message-content">
      <div class="sender">User</div>
      <div class="text">What information do I need for a movement request?</div>
    </div>
    <div class="timestamp">2:30 PM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        <strong>Movement Request Requirements:</strong>
        <br><br>
        <span class="section-title">Required Information:</span>
        <ol>
          <li>Your current location/department</li>
          <li>The new location/department you're requesting to move to</li>
          <li>Effective date for the movement</li>
          <li>Whether this is temporary or permanent</li>
        </ol>
        <br>
        <span class="section-title">Optional Information:</span>
        <ul>
          <li>Reason for the movement</li>
          <li>Any special skills or qualifications relevant to the new position</li>
          <li>Equipment requirements</li>
          <li>Training needs</li>
        </ul>
        <br>
        <span class="section-title">Approval Workflow:</span>
        <p>The request will need approval from your current manager, the manager of the destination department, and final processing by HR.</p>
        <br>
        <span class="note">Would you like me to start a movement request for you now?</span>
      </div>
    </div>
    <div class="timestamp">2:31 PM</div>
  </div>
</div>

### Managing Team Requests

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👥</div>
    <div class="message-content">
      <div class="sender">Team Leader</div>
      <div class="text">Show me all pending leave requests for my team in November</div>
    </div>
    <div class="timestamp">3:45 PM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        <strong>Team Leave Requests for November:</strong>
        <br><br>
        <div class="request-card pending-approval">
          <div class="request-header">
            <span class="employee">John Smith</span>
            <span class="status">Pending Your Approval</span>
          </div>
          <div class="request-body">
            <span class="type">Annual Leave</span>
            <span class="date">Nov 1-3 (3 days)</span>
            <span class="id">REQ-788</span>
          </div>
          <div class="request-actions">
            <span class="action approve">Approve</span>
            <span class="action reject">Reject</span>
            <span class="action details">View Details</span>
          </div>
        </div>
        <br>
        <div class="request-card approved">
          <div class="request-header">
            <span class="employee">Sarah Johnson</span>
            <span class="status">Approved</span>
          </div>
          <div class="request-body">
            <span class="type">Sick Leave</span>
            <span class="date">Nov 8 (1 day)</span>
            <span class="id">REQ-801</span>
          </div>
          <div class="request-actions">
            <span class="action details">View Details</span>
          </div>
        </div>
        <br>
        <div class="request-card pending-hr">
          <div class="request-header">
            <span class="employee">Michael Brown</span>
            <span class="status">Pending HR Verification</span>
          </div>
          <div class="request-body">
            <span class="type">Holiday Leave</span>
            <span class="date">Nov 20-24 (5 days)</span>
            <span class="id">REQ-812</span>
          </div>
          <div class="request-actions">
            <span class="action details">View Details</span>
          </div>
        </div>
        <br>
        <span class="note">Would you like to approve John's request now or see more details about any of these requests?</span>
      </div>
    </div>
    <div class="timestamp">3:46 PM</div>
  </div>
</div>

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👥</div>
    <div class="message-content">
      <div class="sender">Team Leader</div>
      <div class="text">Create a movement request for my entire team from Building A to Building B effective December 1st</div>
    </div>
    <div class="timestamp">10:15 AM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        I'll help you create a multi-operator movement request.
        <br><br>
        <div class="team-selection">
          <span class="section-title">Team Members Identified (5):</span>
          <ul>
            <li>✓ John Smith</li>
            <li>✓ Sarah Johnson</li>
            <li>✓ Michael Brown</li>
            <li>✓ Emma Davis</li>
            <li>✓ Robert Wilson</li>
          </ul>
        </div>
        <br>
        <strong>Request Details:</strong>
        <ul>
          <li>Current Location: Building A</li>
          <li>New Location: Building B</li>
          <li>Effective Date: December 1, 2023</li>
          <li>Duration: Permanent (default)</li>
        </ul>
        <br>
        <span class="note">Do you want to proceed with this request for all 5 team members? You can also specify which team members to include or exclude.</span>
      </div>
    </div>
    <div class="timestamp">10:16 AM</div>
  </div>
</div>

### Workflow Information

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👤</div>
    <div class="message-content">
      <div class="sender">User</div>
      <div class="text">Who needs to approve my overtime request if it's for 10 hours?</div>
    </div>
    <div class="timestamp">4:20 PM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        <strong>Overtime Request Approval Workflow (10 hours)</strong>
        <br><br>
        <div class="workflow-diagram">
          <div class="workflow-step current">
            <div class="step-number">1</div>
            <div class="step-details">
              <div class="step-name">Direct Manager</div>
              <div class="step-assignee">Jane Smith</div>
            </div>
          </div>
          <div class="workflow-arrow">→</div>
          <div class="workflow-step">
            <div class="step-number">2</div>
            <div class="step-details">
              <div class="step-name">Department Head</div>
              <div class="step-assignee">Michael Brown</div>
            </div>
          </div>
          <div class="workflow-arrow">→</div>
          <div class="workflow-step">
            <div class="step-number">3</div>
            <div class="step-details">
              <div class="step-name">Finance Verification</div>
              <div class="step-assignee">Finance Department</div>
            </div>
          </div>
        </div>
        <br>
        <span class="note">Since your request is for 10 hours, it will follow this complete workflow. The typical approval time for similar requests is 2-3 business days.</span>
      </div>
    </div>
    <div class="timestamp">4:21 PM</div>
  </div>
</div>

## Crew Management Tools : Module 2

### Organizational Structure Tools

1. **View Organizational Chart**

   - **Description**: View the hierarchical structure of crews and team members
   - **Capabilities**:
     - See complete organizational hierarchy from Plant Manager to Operators
     - Filter view by department, shift, or location
     - Visualize reporting relationships and team structures
     - Identify crew leaders and their team members
   - **Role-Based Access**:
     - All employees can view the organizational chart
     - View depth limited by user's position in hierarchy
     - Plant Managers and Shift Leaders can view entire organization
     - Team Leaders can view their teams and adjacent teams
   - **Example Views**:
     - Department view showing all crews within a department
     - Team view showing all members of a specific crew
     - Hierarchy view showing management chain

2. **Search Crew Members**

   - **Description**: Find specific employees or teams within the organization
   - **Capabilities**:
     - Search by name, employee ID, role, or department
     - Filter results by shift, location, or qualification status
     - View detailed employee information
     - See team assignments and reporting relationships
   - **Search Options**:
     - Basic search: Simple name or ID lookup
     - Advanced search: Multiple criteria with filters
     - Faceted search: Drill down through categories
   - **Example Queries**:
     - "Find all operators in Assembly department on Shift A"
     - "Search for employee John Smith"
     - "Show all team members reporting to Sarah Johnson"

3. **View Crew Details**
   - **Description**: Get detailed information about a specific crew
   - **Capabilities**:
     - See crew name, manager, and metadata (shift, department, etc.)
     - View complete list of crew members with roles
     - Check qualification status of team members
     - View temporary assignments and training status
   - **Role-Based Access**:
     - Crew managers can view full details of their crews
     - Higher management can view all crew details
     - Team members can view basic information about their own crew
   - **Detail Categories**:
     - Basic Info: Name, ID, department, shift
     - Personnel: List of members with roles and status
     - Metrics: Team size, qualification distribution
     - History: Recent changes to crew composition

### Member Management Tools

4. **Assign Team Member**

   - **Description**: Assign an operator to a crew or change their assignment
   - **Capabilities**:
     - Assign new operators to crews
     - Transfer operators between crews
     - Set assignment type (permanent or temporary)
     - Specify assignment duration for temporary moves
   - **Role-Based Access**:
     - Shift Leaders can assign operators to any crew
     - Team Leaders can request transfers for their team members
     - TKS Responsible can create initial assignments
   - **Assignment Types**:
     - Permanent Assignment: Long-term crew placement
     - Temporary Assignment: Fixed duration placement
     - Training Assignment: Placement during qualification period
   - **Example Workflow**:
     - Select operator to assign
     - Choose destination crew
     - Specify assignment type and duration
     - Provide reason for assignment
     - Submit for appropriate approvals

5. **Manage Training Status**
   - **Description**: Track and update operator training and qualification status
   - **Capabilities**:
     - Start training for new operators
     - Assign trainers to operators
     - Update qualification status
     - Complete training and mark as qualified
   - **Role-Based Access**:
     - Training Responsible can assign trainers
     - Trainers can update training status
     - TKS Responsible can initiate training
   - **Training Statuses**:
     - Unqualified: Initial state for new operators
     - In Training: Assigned to trainer, training in progress
     - Qualified: Successfully completed training
   - **Example Interactions**:
     - "Start training for new operator Michael Brown"
     - "Assign Emma Davis as trainer for John Smith"
     - "Mark Sarah Johnson's training as completed with qualification 'O'"
     - "Check training status for all operators in Assembly department"

### Crew Administration Tools

6. **Create New Crew**

   - **Description**: Create a new crew within the organizational structure
   - **Capabilities**:
     - Define crew name and attributes
     - Assign crew manager
     - Set department, shift, and other metadata
     - Link to Workday references
   - **Role-Based Access**:
     - Super Admins can create any crew
     - Plant Managers can create crews within their plant
     - Shift Leaders can request new crews
   - **Required Information**:
     - Crew name
     - Manager assignment
     - Hierarchy level
     - Department and shift
   - **Example Interaction**:
     - "Create new Assembly Team 5 crew with Jane Smith as Team Leader in Building A"

7. **Archive Crew**

   - **Description**: Archive a crew that is no longer active
   - **Capabilities**:
     - Mark crew as archived
     - Record archival reason and effective date
     - Handle reassignment of crew members
     - Maintain historical records
   - **Role-Based Access**:
     - Super Admins can archive any crew
     - Plant Managers can archive crews within their plant
   - **Archival Process**:
     - Confirm all members have new assignments
     - Specify archival reason
     - Set effective date
     - Archive crew while preserving history
   - **Example Interaction**:
     - "Archive Assembly Team 3 effective December 31st due to reorganization"

8. **Import OPTITIME Data**
   - **Description**: Import operator data from OPTITIME Excel files
   - **Capabilities**:
     - Upload and parse OPTITIME Excel files
     - Create or update operator records
     - Map OPTITIME fields to system fields
     - Validate and process bulk imports
   - **Role-Based Access**:
     - TKS Responsible can import OPTITIME data
     - Super Admins can manage import configurations
   - **Import Options**:
     - New operator import
     - Update existing operators
     - Qualification status updates
     - Shift and team assignments
   - **Example Interaction**:
     - "Import OPTITIME data from this Excel file for Assembly department"

### Workflow and Notification Tools

9. **Initiate Onboarding Workflow**

   - **Description**: Start the onboarding process for new operators
   - **Capabilities**:
     - Create new operator records
     - Initiate training assignment workflow
     - Notify Training Responsible
     - Track onboarding progress
   - **Role-Based Access**:
     - TKS Responsible can initiate onboarding
     - Training Responsible can manage training assignments
     - Trainers can update training status
   - **Workflow Steps**:
     - Create operator record
     - Assign to Training Responsible
     - Assign to Trainer
     - Complete OJT training
     - Temporary team assignment
     - Final team assignment
   - **Example Interaction**:
     - "Start onboarding for new operator John Smith in Assembly department"

10. **Monitor Team Analytics**
    - **Description**: View analytics and metrics for crews and teams
    - **Capabilities**:
      - See team composition and size
      - View qualification distribution
      - Track training progress
      - Monitor temporary assignments
    - **Role-Based Access**:
      - Team Leaders can view their team analytics
      - Shift Leaders can view all teams in their shift
      - Plant Managers can view plant-wide analytics
    - **Analytics Categories**:
      - Team Composition: Roles, experience levels, skills
      - Training Status: Progress, completion rates
      - Assignment Metrics: Permanent vs. temporary ratios
      - Historical Trends: Changes over time
    - **Example Interaction**:
      - "Show me qualification status distribution for Assembly Team 5"
      - "What percentage of operators in Building A are fully qualified?"

### Example Chatbot Interactions

### Crew Management Examples

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👥</div>
    <div class="message-content">
      <div class="sender">Shift Leader</div>
      <div class="text">Show me the organizational chart for Assembly department</div>
    </div>
    <div class="timestamp">9:30 AM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        <strong>Assembly Department Organizational Chart:</strong>
        <br><br>
        <div class="org-chart">
          <div class="org-level">
            <div class="org-node manager">
              <div class="org-title">Department Head</div>
              <div class="org-name">Robert Wilson</div>
            </div>
          </div>
          <div class="org-level">
            <div class="org-node shift-leader">
              <div class="org-title">Shift Leader A</div>
              <div class="org-name">Jennifer Lee</div>
            </div>
            <div class="org-node shift-leader">
              <div class="org-title">Shift Leader B</div>
              <div class="org-name">Michael Chen</div>
            </div>
          </div>
          <div class="org-level">
            <div class="org-node team-leader">
              <div class="org-title">Team Leader</div>
              <div class="org-name">Sarah Johnson</div>
              <div class="org-meta">Team A1 (8 operators)</div>
            </div>
            <div class="org-node team-leader">
              <div class="org-title">Team Leader</div>
              <div class="org-name">David Miller</div>
              <div class="org-meta">Team A2 (7 operators)</div>
            </div>
            <div class="org-node team-leader">
              <div class="org-title">Team Leader</div>
              <div class="org-name">Emma Davis</div>
              <div class="org-meta">Team B1 (9 operators)</div>
            </div>
            <div class="org-node team-leader">
              <div class="org-title">Team Leader</div>
              <div class="org-name">James Wilson</div>
              <div class="org-meta">Team B2 (6 operators)</div>
            </div>
          </div>
        </div>
        <br>
        <span class="note">Click on any team to view detailed member information.</span>
      </div>
    </div>
    <div class="timestamp">9:31 AM</div>
  </div>
</div>

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👤</div>
    <div class="message-content">
      <div class="sender">TKS Responsible</div>
      <div class="text">Start onboarding for new operator Thomas Anderson in Assembly department</div>
    </div>
    <div class="timestamp">11:15 AM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        I've initiated the onboarding process for Thomas Anderson in the Assembly department.
        <br><br>
        <div class="workflow-diagram">
          <div class="workflow-step current">
            <div class="step-number">1</div>
            <div class="step-details">
              <div class="step-name">Create Operator Record</div>
              <div class="step-assignee">Completed</div>
            </div>
          </div>
          <div class="workflow-arrow">→</div>
          <div class="workflow-step">
            <div class="step-number">2</div>
            <div class="step-details">
              <div class="step-name">Training Assignment</div>
              <div class="step-assignee">Pending</div>
            </div>
          </div>
          <div class="workflow-arrow">→</div>
          <div class="workflow-step">
            <div class="step-number">3</div>
            <div class="step-details">
              <div class="step-name">OJT Training</div>
              <div class="step-assignee">Not Started</div>
            </div>
          </div>
        </div>
        <br>
        <strong>Operator Details:</strong>
        <ul>
          <li>Name: Thomas Anderson</li>
          <li>Department: Assembly</li>
          <li>Status: Onboarding</li>
          <li>Employee ID: EMP-2023-089</li>
        </ul>
        <br>
        <span class="note">Lisa Chen (Training Responsible) has been notified to assign a trainer for Thomas.</span>
      </div>
    </div>
    <div class="timestamp">11:16 AM</div>
  </div>
</div>

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👥</div>
    <div class="message-content">
      <div class="sender">Development Specialist</div>
      <div class="text">Create a movement notice to promote Sarah Johnson from Team Leader to Shift Leader effective January 15th</div>
    </div>
    <div class="timestamp">2:45 PM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        I'll help you create a movement notice for Sarah Johnson's promotion.
        <br><br>
        <div class="form-preview">
          <div class="form-header">
            <span class="form-title">Movement Notice</span>
            <span class="form-id">MN-2023-042</span>
          </div>
          <div class="form-section">
            <div class="form-field">
              <div class="field-label">Employee:</div>
              <div class="field-value">Sarah Johnson (EMP-2019-156)</div>
            </div>
            <div class="form-field">
              <div class="field-label">Current Role:</div>
              <div class="field-value">Team Leader - Assembly Team A1</div>
            </div>
            <div class="form-field">
              <div class="field-label">New Role:</div>
              <div class="field-value">Shift Leader</div>
            </div>
            <div class="form-field">
              <div class="field-label">Effective Date:</div>
              <div class="field-value">January 15, 2024</div>
            </div>
            <div class="form-field">
              <div class="field-label">Reason:</div>
              <div class="field-value">Promotion</div>
            </div>
          </div>
        </div>
        <br>
        <strong>Approval Workflow:</strong>
        <ol>
          <li>Department Head (Robert Wilson)</li>
          <li>Plant Manager (Elizabeth Taylor)</li>
          <li>HR Verification</li>
        </ol>
        <br>
        <span class="note">Would you like to add any additional details or comments to this movement notice before submitting?</span>
      </div>
    </div>
    <div class="timestamp">2:47 PM</div>
  </div>
</div>

<div class="chat-container">
  <div class="chat-message user-message">
    <div class="avatar">👤</div>
    <div class="message-content">
      <div class="sender">Trainer</div>
      <div class="text">Mark John Smith's training as completed with qualification 'O'</div>
    </div>
    <div class="timestamp">4:10 PM</div>
  </div>
  
  <div class="chat-message bot-message">
    <div class="avatar">🤖</div>
    <div class="message-content">
      <div class="sender">MCP Assistant</div>
      <div class="text">
        I've updated John Smith's training status to completed with qualification 'O'.
        <br><br>
        <div class="status-card completed">
          <div class="status-header">
            <span class="status-title">Training Completed</span>
            <span class="status-date">October 30, 2023</span>
          </div>
          <div class="status-body">
            <div class="status-field">
              <div class="field-label">Operator:</div>
              <div class="field-value">John Smith</div>
            </div>
            <div class="status-field">
              <div class="field-label">Trainer:</div>
              <div class="field-value">You (Alex Thompson)</div>
            </div>
            <div class="status-field">
              <div class="field-label">Qualification:</div>
              <div class="field-value">O (Qualified Operator)</div>
            </div>
            <div class="status-field">
              <div class="field-label">Training Duration:</div>
              <div class="field-value">3 weeks</div>
            </div>
          </div>
        </div>
        <br>
        <strong>Next Steps:</strong>
        <ul>
          <li>Jennifer Lee (Shift Leader) has been notified to assign John to a permanent team</li>
          <li>Versatility Matrix has been updated with the new qualification</li>
          <li>Training records have been updated in the system</li>
        </ul>
        <br>
        <span class="note">Would you like to add any comments about John's performance or training experience?</span>
      </div>
    </div>
    <div class="timestamp">4:11 PM</div>
  </div>
</div>

<style>
/* Base styles */
:root {
  --primary-color: #0078d4;
  --success-color: #107c10;
  --warning-color: #ca5010;
  --error-color: #c4314b;
  --bg-light: #f5f5f5;
  --bg-white: #ffffff;
  --text-primary: #252525;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
  --radius-sm: 8px;
  --radius-md: 12px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
}

/* Modern chat container */
.chat-container {
  max-width: 800px;
  margin: var(--spacing-lg) auto;
  background: var(--bg-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

/* Message styling */
.chat-message {
  display: flex;
  padding: var(--spacing-md);
  gap: var(--spacing-md);
  position: relative;
  transition: background-color 0.2s ease;
}

.user-message {
  background-color: var(--bg-light);
}

.user-message:hover,
.bot-message:hover {
  background-color: #fafafa;
}

/* Avatar styling */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: var(--primary-color);
  color: white;
  flex-shrink: 0;
}

.user-message .avatar {
  background: #5c2d91;
}

/* Message content */
.message-content {
  flex: 1;
  min-width: 0;
}

.sender {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.text {
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 15px;
}

.timestamp {
  font-size: 12px;
  color: var(--text-secondary);
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-md);
}

/* Status indicators */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 13px;
  gap: var(--spacing-xs);
}

.status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.pending {
  background-color: #fff4ce;
  color: #805b10;
}

.pending::before {
  background-color: #ffd335;
}

.approved {
  background-color: #dff6dd;
  color: var(--success-color);
}

.approved::before {
  background-color: var(--success-color);
}

/* Request cards */
.request-card {
  background: var(--bg-white);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  margin: var(--spacing-sm) 0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.request-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.request-body {
  padding: var(--spacing-md);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-sm);
}

.request-actions {
  padding: var(--spacing-sm);
  background-color: var(--bg-light);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* Action buttons */
.action {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  border: 1px solid transparent;
}

.action:hover {
  transform: translateY(-1px);
}

.approve {
  background-color: var(--success-color);
  color: white;
}

.approve:hover {
  background-color: darken(var(--success-color), 10%);
}

.reject {
  background-color: var(--error-color);
  color: white;
}

.details {
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.details:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Workflow diagram */
.workflow-diagram {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-light);
  border-radius: var(--radius-md);
  margin: var(--spacing-md) 0;
  overflow-x: auto;
}

.workflow-step {
  background: var(--bg-white);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  min-width: 200px;
  box-shadow: var(--shadow-sm);
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.workflow-step.current {
  border: 2px solid var(--primary-color);
  background-color: #f0f7ff;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-details {
  flex: 1;
}

.step-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.step-assignee {
  font-size: 13px;
  color: var(--text-secondary);
}

.workflow-arrow {
  color: var(--text-secondary);
  font-size: 20px;
  flex-shrink: 0;
}

/* Information sections */
.info-section {
  background: var(--bg-light);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  margin: var(--spacing-sm) 0;
}

.section-title {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* Lists */
ul, ol {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

/* Highlights and notes */
.highlight {
  background-color: #fff4ce;
  color: #805b10;
  padding: 2px var(--spacing-xs);
  border-radius: 3px;
  font-weight: 500;
}

.note {
  display: block;
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: #f0f7ff;
  border-left: 4px solid var(--primary-color);
  color: var(--text-primary);
  font-size: 14px;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

/* Team selection */
.team-selection {
  background: var(--bg-light);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
}

.team-selection ul {
  list-style: none;
  padding: 0;
  margin: var(--spacing-sm) 0;
}

.team-selection li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
}

.team-selection li::before {
  content: '✓';
  color: var(--success-color);
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-container {
    margin: var(--spacing-sm);
    border-radius: var(--radius-sm);
  }

  .workflow-diagram {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .workflow-arrow {
    transform: rotate(90deg);
    text-align: center;
    padding: var(--spacing-xs) 0;
  }

  .request-body {
    grid-template-columns: 1fr;
  }

  .request-actions {
    flex-wrap: wrap;
  }

  .action {
    flex: 1;
    text-align: center;
    justify-content: center;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-message {
  animation: fadeIn 0.3s ease-out;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }
}

/* Print styles */
@media print {
  .chat-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .action {
    display: none;
  }
}

/* Organization Chart */
.org-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--bg-light);
  border-radius: var(--radius-md);
  overflow-x: auto;
}

.org-level {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
}

.org-node {
  background-color: var(--bg-white);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  min-width: 150px;
  box-shadow: var(--shadow-sm);
  text-align: center;
  position: relative;
}

.org-node::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 50%;
  width: 2px;
  height: 16px;
  background-color: var(--border-color);
  z-index: 1;
}

.org-level:last-child .org-node::after {
  display: none;
}

.org-title {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.org-name {
  font-weight: 600;
  color: var(--text-primary);
}

.org-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.org-node.manager {
  background-color: #f0f7ff;
  border: 1px solid var(--primary-color);
}

.org-node.shift-leader {
  background-color: #f5f0ff;
  border: 1px solid #6b46c1;
}

.org-node.team-leader {
  background-color: #f0fff4;
  border: 1px solid var(--success-color);
}

/* Form Preview */
.form-preview {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.form-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-title {
  font-weight: 600;
  font-size: 16px;
}

.form-id {
  font-size: 14px;
  opacity: 0.9;
}

.form-section {
  padding: var(--spacing-md);
  background-color: var(--bg-white);
}

.form-field {
  display: flex;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.form-field:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.field-label {
  font-weight: 600;
  width: 120px;
  color: var(--text-secondary);
}

.field-value {
  flex: 1;
  color: var(--text-primary);
}

/* Status Card */
.status-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.status-header {
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-title {
  font-weight: 600;
  font-size: 16px;
}

.status-date {
  font-size: 14px;
  color: var(--text-secondary);
}

.status-body {
  padding: var(--spacing-md);
  background-color: var(--bg-white);
}

.status-field {
  display: flex;
  margin-bottom: var(--spacing-sm);
}

.status-card.completed .status-header {
  background-color: var(--success-color);
  color: white;
}

.status-card.pending .status-header {
  background-color: var(--warning-color);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .org-level {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
  }
  
  .org-node::after {
    height: 0;
  }
  
  .org-node::before {
    content: '';
    position: absolute;
    top: -16px;
    left: 50%;
    width: 2px;
    height: 16px;
    background-color: var(--border-color);
    z-index: 1;
  }
  
  .org-level:first-child .org-node::before {
    display: none;
  }
}
</style>
