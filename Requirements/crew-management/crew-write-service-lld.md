# Low-Level Design: Crew Write Service

## 1. Overview

The Crew Write Service is a NestJS microservice responsible for maintaining data consistency across multiple databases through two main synchronization methods:

1. Service Bus Topic subscription
2. Cosmos DB Change Feed processing

```mermaid
graph TB
    subgraph "Event Sources"
        SB[Service Bus Topic] --> WS[Crew Write Service]
        CF[Change Feed] --> WS
    end

    subgraph "Crew Write Service"
        WS --> EP[Event Processor]
        WS --> CFP[Change Feed Processor]
        EP --> TM[Transaction Manager]
        CFP --> TM
    end

    subgraph "Target Databases"
        TM --> SQL[(SQL Tables)]
        TM --> COSMOS[(Cosmos DB)]
    end
```

## 2. Core Components

### 2.1 Write Containers (Source)

- `operator_updates`: Operator management events
- `employee-assignments`: Assignment-related events
- `dhwalk_changes`: DH Walk related events

### 2.2 Target Containers/Tables

```mermaid
graph LR
    WS[Crew Write Service] --> SQL[SQL Tables]
    WS --> COSMOS[Cosmos DB Containers]

    subgraph "SQL Tables"
        SQL1[aptiv_active_employee]
        SQL2[aptiv_new_hires]
        SQL3[operator_skills]
        SQL4[zoning]
    end

    subgraph "Cosmos DB Containers"
        C1[user_role_hierarchy]
        C2[subordinate_role_counts]
        C3[user_validation]
        C4[teamleader_team_crew]
        C5[teamleader_line_assignment]
        C6[workstation]
    end
```

## 3. Implementation Architecture

### 3.1 NestJS Module Structure

```
src/
├── processors/
│   └── change-feed.processor.ts
├── services/
│   └── write.service.ts
├── config/
│   └── cosmos.config.ts
├── interfaces/
│   └── event.interface.ts
└── write-service.module.ts
```

## 4. Event Processing Examples

### 4.1 AssignedToEmployee Event

```json
{
  "eventType": "AssignedToEmployee",
  "payload": {
    "assigner": {
      "legacySiteId": "123_MOROCCO",
      "role": "TRAINER_RESPONSIBLE"
    },
    "assignTo": {
      "legacySiteId": "456_MOROCCO",
      "role": "TEAM_LEADER"
    },
    "assignee": {
      "legacySiteId": "789_MOROCCO",
      "role": "OPERATOR"
    }
  }
}
```

Target Updates:

1. `user_role_hierarchy`: Update reporting relationships
2. `subordinate_role_counts`: Update role counts
3. `aptiv_active_employee`: Update employee assignments
4. `user_validation`: Update validation records
5. `teamleader_team_crew`: Update team structure
6. `teamleader_line_assignment`: Update line assignments

## 5. Key Implementation Details

### 5.1 Change Feed Processor Configuration

```typescript
@Injectable()
export class ChangeFeedProcessor implements OnModuleInit {
  private writeContainers = [
    "operator_updates",
    "employee-assignments",
    "dhwalk_changes",
  ];

  async onModuleInit() {
    await this.startChangeFeeds();
  }
}
```

### 5.2 Event Handler Mapping

```typescript
const EVENT_HANDLER_MAPPING = {
  AssignedToEmployee: {
    targetContainers: [
      "user_role_hierarchy",
      "subordinate_role_counts",
      "aptiv_active_employee",
      "user_validation",
      "teamleader_team_crew",
      "teamleader_line_assignment",
    ],
  },
  // ... other event mappings
};
```

## 6. Error Handling & Recovery

### 6.1 Error Handling Strategy

```mermaid
graph TD
    A[Error Detected] --> B{Error Type}
    B -->|Transient| C[Retry with Backoff]
    B -->|Permanent| D[Dead Letter Queue]
    C -->|Max Retries| D
    D --> E[Alert Operations]
```

### 6.2 Recovery Mechanisms

1. Lease Container Tracking
2. Event Checkpointing
3. Dead Letter Queue
4. Transaction Rollback
5. Alert System

## 7. Monitoring & Logging

### 7.1 Key Metrics

- Event processing latency
- Success/failure rates
- Processing backlog size
- Database operation latency
- Error rates by event type

### 7.2 Log Categories

1. Event Processing Logs
2. Error Logs
3. Transaction Logs
4. Performance Metrics

## 8. Configuration Parameters

```typescript
interface ServiceConfig {
  cosmos: {
    endpoint: string;
    key: string;
    database: string;
    leaseContainer: string;
  };
  serviceBus: {
    connectionString: string;
    topic: string;
    subscription: string;
  };
  retry: {
    maxAttempts: number;
    backoffMs: number;
  };
}
```

## 9. Deployment Considerations

### 9.1 Prerequisites

- Azure Cosmos DB account
- Azure Service Bus namespace
- SQL Server instance
- Required container/table permissions

### 9.2 Environment Variables

```env
COSMOS_DB_ENDPOINT=
COSMOS_DB_KEY=
COSMOS_DB_DATABASE=
SERVICE_BUS_CONNECTION_STRING=
SERVICE_BUS_TOPIC=
SERVICE_BUS_SUBSCRIPTION=
SQL_CONNECTION_STRING=
```

## 10. Performance Optimization

### 10.1 Batch Processing

- Group similar events
- Bulk database operations
- Parallel processing where possible

### 10.2 Resource Management

- Connection pooling
- Cache frequently accessed data
- Optimize query patterns
