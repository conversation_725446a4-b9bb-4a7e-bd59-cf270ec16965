# Low-Level Design: Team Assignment Service

## 1. Overview

The **Team Assignment Service** is responsible for managing operator assignments to teams within the Connected Workers Platform. This service implements event sourcing and CQRS patterns to handle the assignment and unassignment of operators to teams.

### Key Capabilities

1. **Team Assignment Management**
   - Assign operators to teams
   - Unassign operators from teams
   - Bulk assignment capabilities
   - Role-based assignment validation

2. **Assignment Validation**
   - Department-based validation
   - Workday status verification
   - Team leader authority validation
   - Site-based restrictions

3. **Query Capabilities**
   - View team assignments
   - Search operators by team
   - View unassigned operators
   - Filter by department and site

### Technical Architecture

The service follows these architectural principles:

1. **Event Sourcing**
   - Assignment events stored in Cosmos DB
   - Complete audit trail of assignments
   - Event-based state reconstruction
   - Historical assignment view

2. **CQRS Pattern**
   - Separate command and query responsibilities
   - Optimized read models for queries
   - Eventual consistency
   - High performance for reads

### Business Rules

1. **Assignment Rules**
   - Only team leaders can assign operators to their teams
   - Operators must belong to the same department as the team
   - Operators must be in active workday status
   - Assignments must be within the same site

2. **Validation Rules**
   - Team leader authority validation
   - Department membership validation
   - Workday status verification
   - Site boundary validation

## 2. Domain Model & Events

### 2.1 Event Types

```typescript
enum TeamAssignmentEventType {
  OPERATOR_ASSIGNED_TO_TEAM = "OPERATOR_ASSIGNED_TO_TEAM",
  OPERATOR_UNASSIGNED_FROM_TEAM = "OPERATOR_UNASSIGNED_FROM_TEAM"
}

interface BaseTeamEvent {
  id: string;
  type: TeamAssignmentEventType;
  timestamp: Date;
  site: string;
  metadata: {
    correlationId: string;
    userId: string;
    version: string;
  };
}

interface OperatorTeamAssignmentEvent extends BaseTeamEvent {
  type: TeamAssignmentEventType;
  payload: {
    assigner: {
      legacySiteId: string;
      role: string;
      firstName: string;
      lastName: string;
      department: string;
    };
    team: {
      teamId: string;
      teamName: string;
      legacyTeamLeaderId: string;
    };
    assignee: {
      legacySiteId: string;
      role: string;
      firstName: string;
      lastName: string;
      department: string;
    };
  };
}
```

### 2.2 Event Examples

```json
// Assignment Event Example
{
  "id": "evt-123",
  "type": "OPERATOR_ASSIGNED_TO_TEAM",
  "timestamp": "2025-03-15T10:30:00Z",
  "site": "Site A",
  "metadata": {
    "correlationId": "corr-456",
    "userId": "user-789",
    "version": "1.0"
  },
  "payload": {
    "assigner": {
      "legacySiteId": "TL_123",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Assembly"
    },
    "team": {
      "teamId": "team-456",
      "teamName": "Assembly Team A",
      "legacyTeamLeaderId": "TL_123"
    },
    "assignee": {
      "legacySiteId": "OP_789",
      "role": "OPERATOR",
      "firstName": "Jane",
      "lastName": "Smith",
      "department": "Assembly"
    }
  }
}

// Unassignment Event Example
{
  "id": "evt-124",
  "type": "OPERATOR_UNASSIGNED_FROM_TEAM",
  "timestamp": "2025-03-15T14:30:00Z",
  "site": "Site A",
  "metadata": {
    "correlationId": "corr-457",
    "userId": "user-789",
    "version": "1.0"
  },
  "payload": {
    "assigner": {
      "legacySiteId": "TL_123",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Assembly"
    },
    "team": {
      "teamId": "team-456",
      "teamName": "Assembly Team A",
      "legacyTeamLeaderId": "TL_123"
    },
    "assignee": {
      "legacySiteId": "OP_789",
      "role": "OPERATOR",
      "firstName": "Jane",
      "lastName": "Smith",
      "department": "Assembly"
    }
  }
}
```

## 3. Assignment Rules

```typescript
interface AssignmentRule {
  id: string;
  assignerRole: string;  // Must be TEAM_LEADER
  assignToRole: string;  // Must be TEAM
  assigneeRole: string;  // Must be OPERATOR
  assignmentType: {
      assignToEmployee: boolean,
      assignToTeam: boolean,
      assignToWorkstation: boolean
    },
  validationRules: {
   scope: [department|site|subordinate]
    requiresWorkday: boolean;
  };
  isActive: boolean;
  country: string;
}
```

### 3.1 Example Assignment Rule

```json
{
  "id": "rule-001",
  "assignerRole": "TEAM_LEADER",
  "assignToRole": "TEAM",
  "assigneeRole": "OPERATOR",
  "assignmentType": {
      "assignToEmployee": true,
      "assignToTeam": false,
      "assignToWorkstation": false
    },
  "validationRules": {
    "requiresSameDepartment": true,
    "requiresSameSite": true,
    "requiresWorkday": true
  },
  "isActive": true,
  "country": "MOROCCO"
}
```

## 4. Commands

### 4.1 AssignToTeam Command

```typescript
interface AssignToTeamCommand {
  site: string;
  assigner: { 
    id: string; 
    role: string;  // Must be TEAM_LEADER
  };
  team: { 
    id: string; 
    name: string; 
  };
  assignees: Array<{ 
    id: string; 
    role: string;  // Must be OPERATOR
  }>;
}
```

### 4.2 UnassignFromTeam Command

```typescript
interface UnassignFromTeamCommand {
  site: string;
  assigner: { 
    id: string; 
    role: string;  // Must be TEAM_LEADER
  };
  team: { 
    id: string; 
    name: string; 
  };
  assignee: { 
    id: string; 
    role: string;  // Must be OPERATOR
  };
}
```

## 5. Read Models

### 5.1 Team Assignment Read Model
```json
{
  "teamleader_legacy_site": "E12345_MAR Moroocco 3",
  "teamleader_name": "cxxxxx",
  "teams": "team 1",
  "first_name": "xxx 1",
  "last_name": "xxx 1",
  "department": "E12345_MAR Moroocco 3",
  "operator_legacy_site": "cxxxxx",
  "role": "team 1",
  "role_status": "xxx 1",
  "site": "ssssss"
}
```

## 6. API Endpoints

```typescript
// Assignment endpoint
POST /api/v1/assignments/assign-to-team
Request: {
  site: string;
  assigner: {
    id: string;
    role: string;  // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignees: Array<{
    id: string;
    role: string;  // Must be OPERATOR
  }>;
}
Response: {
  success: boolean;
  errors: string[];

}

// Unassignment endpoint
POST /api/v1/assignments/unassign-from-team
Request: {
  site: string;
  assigner: {
    id: string;
    role: string;  // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignee: {
    id: string;
    role: string;  // Must be OPERATOR
  };

}
Response: {
  success: boolean;
  errors: string[];

}

// Query endpoints
GET /api/v1/teams/{teamId}/operators
GET /api/v1/teams/unassigned-operators?department={dept}&site={site}
```

## 7. Assignment Flow

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Management
    participant AS as Assignment Service
    participant RDB as Read DB
    participant ES as Event Store

    TL->>API: POST /assignments/assign-to-team
    API->>AS: Process Assignment Request
    
    AS->>RDB: Validate Team Leader
    RDB-->>AS: Team Leader Valid
    
    AS->>RDB: Validate Team
    RDB-->>AS: Team Valid
    
    AS->>RDB: Validate Operators
    RDB-->>AS: Operators Valid
    
    AS->>AS: Validate Business Rules
    Note over AS: - Same Department<br>- Same Site<br>- Active Workday
    
    alt Validation Failed
        AS-->>API: Return Error
        API-->>TL: 400 Bad Request
    else Validation Passed
        AS->>ES: Store Assignment Event
        ES-->>AS: Event Stored
        AS-->>API: Success Response
        API-->>TL: 200 OK
    end
```