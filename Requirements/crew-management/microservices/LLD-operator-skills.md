# Low-Level Design: Operator Skills Microservice

This document describes the low-level technical design for the Operator Skills microservice. The key responsibilities are:

1. Receive events from Service Bus containing operator skills data.
2. Store the operator skills data in Cosmos DB.
3. Monitor changes in Cosmos DB using the Change Feed Processor.

---

## Table of Contents
1. [Overview of Architecture](#overview-of-architecture)
2. [Domain Model & Entity](#domain-model--entity)
3. [Event Handling Model](#event-handling-model)
4. [Database Schema](#database-schema)
5. [Change Feed Integration](#change-feed-integration)
6. [Implementation Notes](#implementation-notes)
7. [Roles & Permissions](#roles--permissions)
8. [Use Case Scenarios](#use-case-scenarios)
9. [Summary](#summary)

---

## 1. Overview of Architecture

The Operator Skills service adopts an **event-driven architecture** with the following key components:

### Event Handling:
- The service subscribes to a **Service Bus topic** to receive operator skills events.
- Events are processed and validated before being stored in **Cosmos DB**.

### Change Feed Integration:
- The service uses the **Cosmos DB Change Feed Processor** to monitor changes in the database.
- Changes are logged and can trigger downstream processing.

### Database:
- **Cosmos DB** is used as the primary database for storing operator skills data.
- The schema is designed to support flexible querying and scalability.

---

## 2. Domain Model & Entity

### 2.1 Operator Skills Entity
The `OperatorSkillsEntity` represents the core domain model:

- **Attributes**:
  - `Id`: Unique identifier for the record.
  - `CreatedBy`: User or system that created the record.
  - `EventType`: Type of event (e.g., `new_skills`).
  - `Timestamp`: Timestamp of the event.
  - `PayloadData`: Contains the operator's details, including:
    - `OperatorId`: Unique ID of the operator.
    - `Site`: Operator's site.
    - `Skills`: List of skills.
    - `Certifications` (if applicable).

---

## 3. Event Handling Model

### 3.1 Event Subscription
The service subscribes to a **Service Bus topic** to receive events. Each event contains the following data:

- `Subject`: Event type (e.g., `OperatorSkillsInApp`).
- `Body`: Contains the operator's details:
  - `OperatorId`
  - `Site`
  - `Skills`
  - `CreatedBy`
  - `CreatedAt`

### 3.2 Event Processing
Upon receiving an event, the service:
1. Deserializes the event into an `OperatorSkillsInAppEvent`.
2. Validates the operator's existence using the **SQL Validation Service**.
3. Stores the data in **Cosmos DB** using the **Cosmos DB Service**.

---

## 4. Database Schema

### 4.1 Cosmos DB Schema
The `OperatorSkillsEntity` is stored in Cosmos DB with the following structure:
```json
{
  "id": "unique-id",
  "createdBy": "user-id",
  "eventType": "new_skills",
  "timestamp": "2023-01-01T12:00:00Z",
  "payload": {
    "operatorId": "operator-123",
    "site": "siteA",
    "skills": ["skill1", "skill2"],
    "certifications": ["cert1", "cert2"]
  }
}
```
### 4.2 Partitioning
- **Partition Key**: `site`
- **Reason**: Queries often filter by `site`, and partitioning by `site` ensures efficient lookups.

---

## 5. Change Feed Integration

### 5.1 Change Feed Processor
The service uses the **Cosmos DB Change Feed Processor** to monitor changes in the `OperatorSkillsEntity` container.

### 5.2 Change Handling
When a change is detected:
1. The change is deserialized into an `OperatorSkillsEntity`.
2. The service logs the change details (e.g., `OperatorId`, `Site`, `Skills`).
3. Additional processing (e.g., triggering downstream systems) can be added.

---

## 6. Implementation Notes

### 6.1 Service Bus Integration
- **SDK**: Azure Service Bus SDK is used to subscribe to the topic and process messages.
- **Retry Mechanism**: A retry policy (using **Polly**) is applied to handle transient failures when starting/stopping the Service Bus processor.

### 6.2 Cosmos DB Integration
- **SDK**: Azure Cosmos DB SDK is used for database operations.
- **Change Feed**: The Change Feed Processor is configured to monitor changes in the container.

### 6.3 SQL Validation Service
- **Purpose**: Validates the existence of an operator in an external SQL database.
- **Implementation**: Uses a SQL Server connection with token-based authentication.

### 6.4 Logging
- **Framework**: Microsoft.Extensions.Logging is used for structured logging.
- **Details Logged**:
  - Event processing status.
  - Errors during message processing or database operations.
  - Change feed events.

---

## 7. Roles & Permissions

### 7.1 Role Definitions

**Admin**:
- Can view and manage all operator skills data.
- Can configure the service (e.g., change Service Bus topic subscription).

**User**:
- Can view operator skills data.
- Cannot modify or configure the service.

### 7.2 Permission Matrix

| Operation                | Admin | User |
|--------------------------|-------|------|
| View Operator Skills     | Yes   | Yes  |
| Manage Operator Skills   | Yes   | No   |
| Configure Service        | Yes   | No   |

---

## 8. Use Case Scenarios

### 8.1 Receive and Store Operator Skills

#### Sequence Diagram
```mermaid

sequenceDiagram
  participant ServiceBus
  participant OperatorSkillsWorker
  participant CosmosDB
  participant SQLValidationService

  ServiceBus->>OperatorSkillsWorker: Send OperatorSkillsInApp Event
  OperatorSkillsWorker->>OperatorSkillsWorker: Parse and Validate Event
  OperatorSkillsWorker->>SQLValidationService: Validate Operator Existence
  SQLValidationService-->>OperatorSkillsWorker: Validation Result
  OperatorSkillsWorker->>CosmosDB: Store Operator Skills Data
  OperatorSkillsWorker->>ServiceBus: Acknowledge Event

```
---

### 8.2 Monitor Changes in Operator Skills

#### Sequence Diagram
```mermaid
sequenceDiagram participant CosmosDB participant ChangeFeedService participant Logger
CosmosDB->>ChangeFeedService: Detect Change in Operator Skills
ChangeFeedService->>ChangeFeedService: Deserialize Change
ChangeFeedService->>Logger: Log Change Details
```

---

## 9. Summary

The updated Operator Skills microservice is designed to:
1. Process operator skills events from Service Bus and store them in Cosmos DB.
2. Monitor changes in the database using the Change Feed Processor.
3. Validate operator existence using an external SQL database.

This design ensures scalability, reliability, and real-time processing of operator skills data, aligning with the current implementation in your solution.

