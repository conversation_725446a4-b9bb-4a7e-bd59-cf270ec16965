# Workstation Microservice - Low-Level Design

## Table of Contents

- [Workstation Microservice - Low-Level Design](#workstation-microservice---low-level-design)
  - [Table of Contents](#table-of-contents)
  - [1. Overview](#1-overview)
    - [Key Capabilities](#key-capabilities)
    - [Technical Approach](#technical-approach)
  - [2. Assignment Rules](#2-assignment-rules)
    - [2.1 Assignment Rule Structure](#21-assignment-rule-structure)
    - [2.2 Example Assignment Rules](#22-example-assignment-rules)
    - [2.3 Assignment Rule Processing](#23-assignment-rule-processing)
  - [3. Core Document Models](#3-core-document-models)
    - [3.1 Workstation Document](#31-workstation-document)
      - [Example Workstation Document](#example-workstation-document)
    - [3.2 Teamleader Line assignment Document](#32-teamleader-line-assignment-document)
      - [Example Teamleader Line assignment Document](#example-teamleader-line-assignment-document)
  - [4. Document Relationships](#4-document-relationships)
    - [Relationship Diagram](#relationship-diagram)
  - [5. Events and Commands](#5-events-and-commands)
    - [5.1 Event Types](#51-event-types)
      - [Workstation Event Structure](#workstation-event-structure)
    - [5.2 Command Types](#52-command-types)
  - [6. Architecture](#6-architecture)
  - [7. Assignment Workflows](#7-assignment-workflows)
    - [7.1 Initial Assignment](#71-initial-assignment)
    - [7.2 Reassignment](#72-reassignment)
    - [7.3 Unassignment](#73-unassignment)
  - [8. Data Retrieval and Filtering](#8-data-retrieval-and-filtering)
    - [8.1 Scope-Based Filtering](#81-scope-based-filtering)
    - [8.2 Retrieval Strategies](#82-retrieval-strategies)
      - [Role-Based Backend Filtering](#role-based-backend-filtering)
      - [Line Assignment Retrieval](#line-assignment-retrieval)
      - [Example Flow for Role-Based Filtering](#example-flow-for-role-based-filtering)
  - [9. Read Models](#9-read-models)
    - [9.1 ReadWorkstationEntity](#91-readworkstationentity)
    - [9.2 ReadLineAssignmentEntity](#92-readlineassignmententity)
    - [9.3 ReadEmployeeEntity](#93-reademployeeentity)
  - [10. API Endpoints](#10-api-endpoints)
  - [11. Additional Considerations](#11-additional-considerations)
  - [12. Change Feed Processing](#12-change-feed-processing)
    - [12.1 Change Feed Processors](#121-change-feed-processors)
    - [12.2 Event Processing Flow](#122-event-processing-flow)
    - [12.3 Key Benefits of Change Feed Processing](#123-key-benefits-of-change-feed-processing)
    - [12.4 Checkpoint Management](#124-checkpoint-management)

## 1. Overview

The **Workstation Microservice** manages the assignment of operators to workstations within the manufacturing process. It enables specific users (e.g., TEAM_LEADER) to efficiently allocate their workforce to specific stations based on skills, availability, and other factors.

### Key Capabilities

1. **Workstation Management**

   - Define workstations with attributes (station ID, skills required, capacity)
   - Track workstation status and assignments
   - Maintain workstation configuration versions
   - Create and delete workstations

2. **Operator Assignment**

   - Assign operators to workstations
   - Reassign operators when needed
   - Track assignment history
   - Validate skill matches

### Technical Approach

The microservice follows these architectural principles:

1. **Event Sourcing**: All state changes are captured as immutable events
2. **CQRS**: Separate command and query responsibilities
3. **Document DB**: Cosmos DB for flexible document storage
4. **Change Feed**: Event-driven processing of changes
5. **Eventual Consistency**: Read models updated asynchronously
6. **NestJS Framework**: Modular structure with dependency injection

## 2. Assignment Rules

The Workstation Microservice implements a flexible rules-based system to control which roles can assign different types of operators to workstations. This approach allows for special cases such as Quality Supervisors managing Containment operators and Coordinators handling specialized manufacturing roles.

### 2.1 Assignment Rule Structure

```typescript
interface AssignmentRuleEntity {
  id: string;
  assignerRole: string; // Role that can perform the assignment (e.g., "TEAM_LEADER", "QUALITY_SUPERVISOR")
  assignToRole: string; // Type of entity being assigned to (e.g., "WORKSTATION")
  assigneeRoles: string[]; // Types of operators that can be assigned (e.g., ["OPERATOR", "CONTAINMENT"])
  assignmentType: {
    toEmployee: boolean;
    toTeam: boolean;
    toWorkstation: boolean;
  };
  validationRules: {
    employeeToEmployee: {
      requiresDirectHierarchyBetweenAssignerAndAssignTo: boolean;
      requiresDirectHierarchyBetweenAssignToAndAssignee: boolean;
      requiresAssignerAndAssignToSameDepartment: boolean;
      requiresAssignToAndAssigneeSameDepartment: boolean;
      requiresAssignerAndAssignToSameSite: boolean;
      requiresAssigneeRegistrationInWorkday: boolean;
      indirectEmployees: boolean;
      excludeFirstLevelNewEmployees: boolean;
    };
    employeeToTeam: {
      requiresDirectHierarchyBetweenAssignerAndAssignee: boolean;
      requiresAssignerAndAssigneeSameDepartment: boolean;
      requiresAssigneeRegistrationInWorkday: boolean;
    };
    employeeToWorkstation: {
      requiresDirectHierarchyBetweenAssignerAndAssignee: boolean;
      requiresAssignerAndAssigneeSameDepartment: boolean;
      requiresAssigneeRegistrationInWorkday: boolean;
      requiresAssigneeAndWorkstationSkillsMatching: boolean;
      allowedToAssignToMeStructureStations: boolean;
    };
  };
  lineAssignmentFilterConditions: {
    filterByAssignment: boolean;
    filterByDepartment: boolean;
    filterBySite: boolean;
    canManageContainmentOperators: boolean;
  };
  isActive: boolean;
  country: string;
}
```

### 2.2 Example Assignment Rules

```json
[
  {
    "id": "rule-001",
    "assignerRole": "TEAM_LEADER",
    "assignToRole": "WORKSTATION",
    "assigneeRoles": ["OPERATOR"],
    "assignmentType": {
      "toEmployee": false,
      "toTeam": false,
      "toWorkstation": true
    },
    "validationRules": {
      "employeeToWorkstation": {
        "requiresDirectHierarchyBetweenAssignerAndAssignee": true,
        "requiresAssignerAndAssigneeSameDepartment": true,
        "requiresAssigneeRegistrationInWorkday": true,
        "requiresAssigneeAndWorkstationSkillsMatching": true,
        "allowedToAssignToMeStructureStations": true
      }
    },
    "lineAssignmentFilterConditions": {
      "filterByAssignment": false,
      "filterByDepartment": true,
      "filterBySite": false,
      "canManageContainmentOperators": false
    },
    "isActive": true,
    "country": "MOROCCO"
  },
  {
    "id": "rule-002",
    "assignerRole": "QUALITY_SUPERVISOR",
    "assignToRole": "WORKSTATION",
    "assigneeRoles": ["CONTAINMENT"],
    "assignmentType": {
      "toEmployee": false,
      "toTeam": false,
      "toWorkstation": true
    },
    "validationRules": {
      "employeeToWorkstation": {
        "requiresDirectHierarchyBetweenAssignerAndAssignee": false,
        "requiresAssignerAndAssigneeSameDepartment": false,
        "requiresAssigneeRegistrationInWorkday": true,
        "requiresAssigneeAndWorkstationSkillsMatching": true,
        "allowedToAssignToMeStructureStations": false
      }
    },
    "lineAssignmentFilterConditions": {
      "filterByAssignment": false,
      "filterByDepartment": false,
      "filterBySite": true,
      "canManageContainmentOperators": true
    },
    "isActive": true,
    "country": "MOROCCO"
  }
]
```

### 2.3 Assignment Rule Processing

When processing an assignment request, the system:

1. Identifies all active rules that match the assigner's role
2. Filters rules based on operator type and workstation type
3. Sorts matching rules by priority (highest first)
4. Applies the highest priority rule's validation criteria
5. If no matching rule is found, the assignment is rejected

This approach provides:

- Flexibility to add new rules without code changes
- Support for special cases like cross-department assignments
- Clear permission boundaries for different roles
- Ability to handle country-specific variations

## 3. Core Document Models

### 3.1 Workstation Document

```typescript
interface ReadWorkstationEntity {
  id: string; // Unique identifier
  customer: string; // The customer name
  projet: string; // Project assigned to the station
  famille: string; // Family assigned to the station
  value_stream: string; // Value stream assigned to the station
  area: string; // Area assigned to the station
  station_id: string; // Station identifier
  station: string; // Station name
  site: string; // Employee's site
  department: string; // Department
  role_station: string; // Role of station
  skills: string[] | null; // Station skills
  criticity: string; // Station criticity
  capacity: number; // Station capacity
  effective_date: string; // Effective date (ISO format)
  me_definition_version: string; // Document version
  is_polyvalent: boolean; // Whether the station is polyvalent
  is_rework: boolean; // Whether the station is for rework
  is_containment: boolean; // Whether the station is for containment
  has_me_structure_role: boolean; // Whether the station has ME Structure role
  is_deleted: boolean; // Whether the station is deleted
}
```

#### Example Workstation Document

```json
{
  "id": "WS_001",
  "customer": "AUDI",
  "projet": "PROJECT_A",
  "famille": "FAMILY_1",
  "value_stream": "VS_001",
  "area": "AREA_1",
  "station_id": "STATION_001",
  "station": "Assembly Station 1",
  "site": "MOROCCO",
  "department": "Assembly",
  "role_station": "ME Structure",
  "skills": ["SOLDERING", "TESTING", "QUALITY_CHECK"],
  "criticity": "HIGH",
  "capacity": 2,
  "effective_date": "2024-03-15T00:00:00Z",
  "me_definition_version": "1.0",
  "is_polyvalent": false,
  "is_rework": false,
  "is_containment": false,
  "has_me_structure_role": true,
  "is_deleted": false
}
```

### 3.2 Teamleader Line assignment Document

```typescript
interface ReadLineAssignmentEntity {
  id: string; // Unique identifier
  operator_legacy_site_id: string; // Operator's legacy site ID
  operator_id: string; // Operator's ID
  operator_first_name: string; // Operator's first name
  operator_last_name: string; // Operator's last name
  operator_role: string; // Operator's role
  operator_skills: string[] | null; // Operator's skills
  operator_department: string; // Operator's department
  team: string; // Team name
  teamleader_legacy_site_id: string; // Team leader's legacy site ID
  teamleader_first_name: string; // Team leader's first name
  teamleader_last_name: string; // Team leader's last name
  teamleader_department: string; // Team leader's department
  id: string; // Workstation ID
  project: string; // Workstation project
  family: string; // Workstation family
  value_stream: string; // Workstation value stream
  area: string; // Workstation area
  station_id: string; // Workstation station ID
  station: string; // Workstation station name
  station_role: string; // Workstation station role
  site: string; // Site
  is_active: boolean; // Whether the assignment is active
}
```

#### Example Teamleader Line assignment Document

```json
{
  "id": "OLA_12345",
  "operator_legacy_site_id": "OP_67890",
  "operator_id": "OPERATOR_001",
  "operator_first_name": "Jane",
  "operator_last_name": "Smith",
  "operator_role": "OPERATOR",
  "operator_skills": ["SOLDERING", "TESTING"],
  "operator_department": "Assembly",
  "team": "TEAM_1",
  "teamleader_legacy_site_id": "TL_12345",
  "teamleader_first_name": "John",
  "teamleader_last_name": "Doe",
  "teamleader_department": "Assembly",
  "id": "WS_001",
  "project": "PROJECT_A",
  "family": "FAMILY_1",
  "value_stream": "VS_001",
  "area": "AREA_1",
  "station_id": "STATION_001",
  "station": "Assembly Station 1",
  "station_role": "ME Structure",
  "site": "MOROCCO",
  "is_active": true
}
```

## 4. Document Relationships

The relationship between `ReadWorkstationEntity` and `ReadLineAssignmentEntity` is essential to the assignment process:

1. **Workstation Independence**:

   - `ReadWorkstationEntity` exists independently of assignments
   - A workstation can exist without any operator assigned to it
   - Workstations are defined by ME definition and manufacturing structure

2. **Assignment Dependency**:

   - `ReadLineAssignmentEntity` always references a workstation via `workstation_id`
   - Assignment documents have a 1-to-1 relationship with operators
   - A workstation can have multiple assignments over time, but only one active assignment at a time (per capacity slot)

3. **Data Duplication Strategy**:
   - Key workstation attributes are duplicated in the assignment document for performance
   - This denormalization allows for faster reads without joins
   - The assignment document captures the state of the workstation at the time of assignment

### Relationship Diagram

```
┌──────────────────────┐      ┌──────────────────────────────┐
│  ReadWorkstationEntity│      │ ReadLineAssignmentEntity         │
│                      │      │                                │
│  - id                │◄─────┤ - workstation_id                 │
│  - station           │      │ - workstation_station              │
│  - skills            │      │ - operator_id                      │
│  - capacity          │      │ - is_active                        │
└──────────────────────┘      └──────────────────────────────┘
         0..*                               0..*
```

## 5. Events and Commands

### 5.1 Event Types

```typescript
enum WorkstationEventType {
  // Workstation Events
  WORKSTATION_CREATED = "WORKSTATION_CREATED",
  WORKSTATION_DELETED = "WORKSTATION_DELETED",
}
```

#### Workstation Event Structure

```typescript
// Base Event
interface WorkstationBaseEvent {
  id: string;
  type: WorkstationEventType;
  createdBy: string;
  site: string;
  timestamp: string;
}

// Create Workstation Event
interface CreateWorkstationEvent extends WorkstationBaseEvent {
  type: WorkstationEventType.WORKSTATION_CREATED;
  payload: {
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
    meDefinitionVersion: string;
    effectiveDate: string;
    stationId: string;
    station: string;
    stationRole: string;
    department: string;
    assignedOperator?: {
      legacySite: string;
      legacyId: number;
      first_name: string;
      last_name: string;
      role: string;
      skills: string[];
      department: string;
      team: string;
      teamLeaderLegacySite: string;
    };
  };
}

// Delete Workstation Event
interface DeleteWorkstationEvent extends WorkstationBaseEvent {
  type: WorkstationEventType.WORKSTATION_DELETED;
  payload: {
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
    meDefinitionVersion: string;
    effectiveDate: string;
    stationId: string;
    station: string;
    stationRole: string;
    department: string;
    unassigned?: {
      legacySite: string;
      firstName: string;
      lastName: string;
      role: string;
      department: string;
      team: string;
      teamLeaderLegacySite: string;
    };
  };
}
```

### 5.2 Command Types

```typescript
// Create Workstation Command
interface CreateWorkstationDto {
  customer: string;
  projet: string;
  famille: string;
  value_stream: string;
  area: string;
  me_definition_version: string;
  effective_date: string;
  teamLeader_legacy_site: string;
  msStationId: string;
  team_name: string;
  assignedLegacySite?: string;
}

// Delete Workstation Command
interface DeleteWorkstationDto {
  stationId: string;
  teamLeader_legacy_site: string;
  unassignedLegacySite?: string;
  team?: string;
}

// Unassigned Operator Query
interface UnassignedOperatorQueryDto {
  valueStreamId?: string;
  areaId?: string;
  teamName?: string;
  skills?: string[];
  page?: number;
  pageSize?: number;
}

// Get Workstation Assignment Query
interface GetWorkstationAssignmentQueryDto {
  departmentId?: string;
  valueStreamId?: string;
  areaId?: string;
  teamId?: string;
  criticity?: string;
  page?: number;
  pageSize?: number;
}
```

## 6. Architecture

```
┌────────────────────────────────────────────────────────────────┐
│                   Workstation Microservice                     │
│                                                                │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐     │
│  │ Controllers  │    │   Services   │    │ Repositories │     │
│  │              │◄───┤              │◄───┤              │     │
│  └──────────────┘    └──────────────┘    └──────────────┘     │
│          ▲                   ▲                   ▲            │
└──────────┼───────────────────┼───────────────────┼────────────┘
           │                   │                   │
┌──────────┼───────────────────┼───────────────────┼────────────┐
│          ▼                   │                   ▼            │
│    ┌──────────┐              │             ┌──────────────┐   │
│    │   API    │              │             │  Database    │   │
│    │  Gateway │              │             │  (Cosmos DB) │   │
│    └──────────┘              │             └──────────────┘   │
│                              │                    ▲           │
│                              │                    │           │
│                              ▼                    │           │
│                      ┌──────────────┐             │           │
│                      │Event Handlers│─────────────┘           │
│                      └──────────────┘                         │
│                              ▲                                │
│                              │                                │
│                              ▼                                │
│                    ┌───────────────────┐                      │
│                    │ Change Feed       │                      │
│                    │   Processors      │                      │
│                    └───────────────────┘                      │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

## 7. Assignment Workflows

### 7.1 Initial Assignment

The process for assigning an operator to a workstation:

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Gateway
    participant WS as Workstation Service
    participant VAL as Validator
    participant REP as Repository
    participant CFP as Change Feed Processor

    TL->>API: Assign Operator to Workstation
    API->>WS: CreateWorkstationDto

    WS->>WS: Generate ID
    WS->>REP: Store WORKSTATION_CREATED Event

    WS->>VAL: Validate Assignment
    Note over VAL: Check skills match,<br>workstation capacity,<br>operator availability

    alt Validation Failed
        VAL-->>WS: Validation Failed
        WS-->>API: Return Error
        API-->>TL: Show Error Message
    else Validation Passed
        VAL-->>WS: Validation Passed

        REP->>CFP: Trigger via Change Feed
        CFP->>CFP: Process WORKSTATION_CREATED Event
        CFP->>CFP: Create ReadLineAssignmentEntity

        WS-->>API: Return Success
        API-->>TL: Show Success Message
    end
```

### 7.2 Reassignment

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Gateway
    participant WS as Workstation Service
    participant VAL as Validator
    participant REP as Repository
    participant CFP as Change Feed Processor

    TL->>API: Get Operators for Reassignment
    API->>WS: GetOperatorsForReassignmentQueryDto
    WS->>REP: Query Operators
    REP-->>WS: Return Operators List
    WS-->>API: Return Operators for Reassignment
    API-->>TL: Display Operators List

    TL->>API: Select New Operator and Submit
    API->>WS: ReassignOperatorCommand

    WS->>VAL: Validate Reassignment
    Note over VAL: Check skills match,<br>operator availability

    alt Validation Failed
        VAL-->>WS: Validation Failed
        WS-->>API: Return Error
        API-->>TL: Show Error Message
    else Validation Passed
        VAL-->>WS: Validation Passed
        WS->>REP: Update Assignments

        REP->>CFP: Trigger via Change Feed
        CFP->>CFP: Process Assignment Update

        WS-->>API: Return Success
        API-->>TL: Show Success Message
    end
```

### 7.3 Unassignment

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Gateway
    participant WS as Workstation Service
    participant REP as Repository
    participant CFP as Change Feed Processor

    TL->>API: Delete Workstation
    API->>WS: DeleteWorkstationDto

    WS->>REP: Check if Workstation Exists
    REP-->>WS: Workstation Details

    alt Workstation Not Found
        WS-->>API: Return Error
        API-->>TL: Show Error Message
    else Workstation Found
        WS->>REP: Store WORKSTATION_DELETED Event

        REP->>CFP: Trigger via Change Feed
        CFP->>CFP: Process WORKSTATION_DELETED Event
        CFP->>CFP: Update Assignments to Inactive

        WS-->>API: Return Success
        API-->>TL: Show Success Message
    end
```

## 8. Data Retrieval and Filtering

### 8.1 Scope-Based Filtering

The lineAssignmentFilterConditions in assignment rules determines how data is filtered when retrieving workstation and operator information:

1. **Department Filtering**

   - When filterByDepartment is set to true, the system retrieves data related to the same department as the requesting user.
   - For workstation assignments, only operators and workstations within the user's department are visible.
   - Department information is derived from the operator's profile and the workstation's metadata.

2. **Site Filtering**

   - When filterBySite is set to true, the system retrieves data across departments but limited to the same site.
   - This allows roles like Quality Supervisors to view and manage operators across different departments within the same manufacturing site.
   - Site information is derived from the site code in both operator and workstation data.

3. **Assignment Filtering**
   - When filterByAssignment is set to true, the system retrieves data only for assignments made by the requesting user.
   - This provides users with focused views of only their assignments.

### 8.2 Retrieval Strategies

#### Role-Based Backend Filtering

The system uses a consolidated set of endpoints and implements role-based filtering in the backend:

1. The API client makes requests to generic endpoints.
2. The backend identifies the user's role and location (site, department) from the authentication token.
3. The system retrieves the applicable assignment rules for the user's role.
4. Based on the user's role and the associated assignment rules, appropriate filters are applied:
   - Team Leaders see and assign operators to ME Structure workstations only in the projects of customers that related to him.
   - Quality Supervisors see and can assign of containment operators to containment workstations within his site.
   - Coordinators see and can assign of specific Manufacturing Structure Stations only in the projects of customers that related to him.
   - Departement clerk see and can assign of operators to workstations within his department.

This approach:

- Simplifies the API surface by reducing the number of endpoints
- Centralizes filter logic in the backend
- Ensures consistent application of access control rules
- Makes the API more maintainable as new roles or rules are added

#### Line Assignment Retrieval

When retrieving workstation assignments, the system applies the following strategy:

1. Identify the active assignment rule for the requesting user's role.
2. Apply the scope filter based on the rule's lineAssignmentFilterConditions:
   - For filterByDepartment=true: Filter assignments to those within the same department as the requesting user.
   - For filterBySite=true: Filter assignments to those within the same site as the requesting user.
   - For filterByAssignment=true: Filter assignments related to the requesting user.
3. Apply additional filters based on query parameters (e.g., project, family, valueStream).
4. Return the filtered collection of assignments with appropriate pagination.

#### Example Flow for Role-Based Filtering

```mermaid
sequenceDiagram
    participant User as User (Quality Supervisor)
    participant Auth as Auth Middleware
    participant API as API Gateway
    participant WS as Workstation Service
    participant Rules as Rules Service
    participant DB as Database

    User->>API: GET /workstation/assignments
    API->>Auth: Validate Token
    Auth->>Auth: Extract User Role & ID
    Auth->>API: User Role = "QUALITY_SUPERVISOR"

    API->>WS: Request Assignments
    WS->>Rules: Get Active Rules for QUALITY_SUPERVISOR
    Rules-->>WS: Rule with filterBySite=true, canManageContainmentOperators=true

    WS->>WS: Get User's Site = "MOROCCO"
    WS->>DB: Query Assignments (site="MOROCCO", stationRole="CONTAINMENT")
    DB-->>WS: Filtered Assignments
    WS-->>API: Return Filtered Data
    API-->>User: Show Role-Appropriate Assignments
```

## 9. Read Models

The Workstation microservice uses several read model entities to represent the data needed for queries and UI display. These models are optimized for read operations and are kept updated through the change feed processing mechanism.

### 9.1 ReadWorkstationEntity

This entity represents a workstation in the system:

```typescript
export class ReadWorkstationEntity {
  readonly id: string;
  readonly customer: string;
  readonly projet: string;
  readonly famille: string;
  readonly value_stream: string;
  readonly area: string;
  readonly station_id: string;
  readonly station: string;
  readonly site: string;
  readonly department: string;
  readonly role_station: string;
  readonly skills: string[] | null;
  readonly criticity: string;
  readonly capacity: number;
  readonly effective_date: string;
  readonly me_definition_version: string;
  readonly is_polyvalent: boolean;
  readonly is_rework: boolean;
  readonly is_containment: boolean;
  readonly has_me_structure_role: boolean;
  readonly is_deleted: boolean = false;
}
```

### 9.2 ReadLineAssignmentEntity

This entity represents an assignment of an operator to a workstation:

```typescript
export class ReadLineAssignmentEntity {
  readonly id: string;
  readonly teamleader_legacy_site: string;
  readonly teamleader_fullname: string;
  readonly customer: string;
  readonly projet: string;
  readonly famille: string;
  readonly value_stream: string;
  readonly area: string;
  readonly team: string;
  readonly station_id: string;
  readonly station: string;
  readonly station_role: string;
  readonly operator_legacy_site: string;
  readonly operator_legacy_id: number;
  readonly operator_first_name: string;
  readonly operator_last_name: string;
  readonly operator_skills: string[] = [];
  readonly me_definition_version: string;
  readonly department: string;
  readonly site: string;
  readonly effective_date: string;
}
```

### 9.3 ReadEmployeeEntity

This entity represents an employee in the system:

```typescript
export class ReadEmployeeEntity {
  readonly id: string;
  readonly legacy_site_id: string;
  readonly first_name: string;
  readonly last_name: string;
  readonly legacy_id: number;
  readonly role: string;
  readonly site: string;
  readonly category: string;
  readonly department: string;
  readonly skills: string[] | null;
  readonly subordinate_id: string[] | null;
  readonly in_workday: boolean;
  readonly is_rework: boolean;
  readonly is_containment: boolean;
}
```

## 10. API Endpoints

```typescript
// Zoning Endpoints
GET    /zoning/options
Query Parameters:
    - level?: string
    - customer?: string
    - projet?: string
    - famille?: string
    - value_stream?: string
    - area?: string
    - team_name?: string
Returns: ZoningOptionResponseDto with hierarchical options

GET    /zoning/me-definition-versions
Query Parameters:
    - customer: string
    - projet: string
    - famille: string
    - value_stream: string
    - area: string
    - team_name: string
Returns: MeVersionResponseDto with versions and teamLeader_legacy_site

// Workstation Assignments and Operators
GET    /workstation/assignments
Query Parameters:
    - departmentId?: string
    - valueStreamId?: string
    - areaId?: string
    - teamId?: string
    - criticity?: string
    - page?: number
    - pageSize?: number

GET    /workstation/unassigned-operators
Query Parameters:
    - valueStreamId?: string
    - areaId?: string
    - teamName?: string
    - skills?: string[]
    - page?: number
    - pageSize?: number

GET    /workstation/operators-for-reassignment
Query Parameters:
    - valueStreamId?: string
    - areaId?: string
    - team?: string
    - skills?: string[]
    - page?: number
    - pageSize?: number

GET    /workstation/operator-reassign-ms-stations-creation
Query Parameters:
    - valueStreamId?: string
    - areaId?: string
    - skills?: string[]
    - page?: number
    - pageSize?: number

GET    /workstation/ms-stations
Query Parameters:
    - valueStreamId?: string
    - areaId?: string
    - departmentId?: string
    - stationRole?: string
    - page?: number
    - pageSize?: number

GET    /workstation/value-stream-skills
Query Parameters:
    - valueStreamId: string

// Workstation Management
POST   /workstation-management/create-workstation
Body: CreateWorkstationDto

POST   /workstation-management/delete-workstation
Body: DeleteWorkstationDto
```

## 11. Additional Considerations

1. **CQRS Pattern**: The design uses Command Query Responsibility Segregation with separate services for handling commands (WorkstationManagementService) and queries (WorkstationService, ReadLineAssignmentService).

2. **Event Sourcing**: All changes are captured as events in the workstation-changes container, allowing for full audit trail and rebuilding state.

3. **Change Feed Processing**: Using Cosmos DB change feed processors (WorkstationChangeFeedProcessor, WorkstationChangesFeedProcessor) to react to events and maintain read models.

4. **Role-based Access Control**: Different roles (Team Leader, Coordinator, Quality Supervisor) have different permissions based on assignment rules.

5. **Zoning Support**: The service includes zoning functionality to support geographical organization of workstations.

6. **Manufacturing Structure Stations**: The service integrates with manufacturing structure definitions to create workstations based on defined stations.

7. **Real-time Updates**: Change feed processors enable real-time updates to the read models when changes occur.

8. **Logging and Monitoring**: Comprehensive logging is implemented using the AppLogger for debugging and monitoring service operations.

9. **NestJS Structure**: The service follows NestJS best practices with controllers, services, and repositories clearly separated.

## 12. Change Feed Processing

The Workstation Microservice heavily relies on Cosmos DB Change Feed Processing to maintain eventual consistency and enable event-driven architecture:

### 12.1 Change Feed Processors

```typescript
@Injectable()
export class WorkstationChangeFeedProcessor
  implements OnModuleInit, OnApplicationShutdown
{
  private readonly logger = new Logger(WorkstationChangeFeedProcessor.name);
  private readonly sourceContainer: Container;
  private isProcessing = false;

  constructor(
    @Inject(COSMOS_CLIENT) client: CosmosClient,
    @Inject(DATABASE_ID) databaseId: string,
    private readonly changeFeedService: WorkstationChangeFeedService
  ) {
    this.sourceContainer = client
      .database(databaseId)
      .container("workstation-changes");
  }

  async onModuleInit(): Promise<void> {
    setImmediate(() => this.startChangeFeedProcessor());
  }

  private async startChangeFeedProcessor(): Promise<void> {
    // Monitors the workstation-changes container for new events
  }
}
```

### 12.2 Event Processing Flow

The Change Feed Processing system follows this workflow:

1. **Event Storage**: When a workstation is created or deleted, events are stored in the `workstation-changes` container:

   - `WORKSTATION_CREATED` event when creating a new workstation
   - `WORKSTATION_DELETED` event when deleting a workstation

2. **Change Feed Triggering**: The Change Feed Processor continuously monitors the `workstation-changes` container for new events

3. **Event Handling**: When an event is detected, it's processed according to its type:

   - For `WORKSTATION_CREATED`, a new read model document is created in the workstation and assignment collections
   - For `WORKSTATION_DELETED`, related documents are updated to mark them as deleted/inactive

4. **Read Model Updates**: The Change Feed Processor updates the read models to reflect the latest state:
   - Updates `ReadWorkstationEntity` documents when workstation is created or deleted.
   - Updates `ReadLineAssignmentEntity` documents when operators saved as assignment to a workstation.

### 12.3 Key Benefits of Change Feed Processing

1. **Real-time Updates**: Changes are propagated to read models almost immediately
2. **Resilience**: Failed processing can be retried without losing events
3. **Scalability**: Multiple processors can handle high event volumes
4. **Audit Trail**: All events are preserved for historical tracking and debugging
5. **Eventual Consistency**: System maintains consistency across distributed components

### 12.4 Checkpoint Management

The service includes a `ChangeFeedCheckpointService` that tracks the processing progress to ensure events are processed exactly once, even in failure scenarios:

```typescript
@Injectable()
export class ChangeFeedCheckpointService {
  constructor(
    @Inject(COSMOS_CLIENT) private readonly client: CosmosClient,
    @Inject(DATABASE_ID) private readonly databaseId: string
  ) {}

  async getLastProcessedCheckpoint(
    processorName: string
  ): Promise<string | undefined> {
    // Retrieves the last processed checkpoint for a processor
  }

  async updateCheckpoint(
    processorName: string,
    checkpoint: string
  ): Promise<void> {
    // Updates the checkpoint after successful processing
  }
}
```