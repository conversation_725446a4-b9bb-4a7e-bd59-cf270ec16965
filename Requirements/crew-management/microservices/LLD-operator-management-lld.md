# Low-Level Design: MS1 - Operator Management

This document describes the low-level technical design for the **Operator Management** microservice (MS1), focusing on an **event-sourced** approach. The key responsibilities are:

1. **Create new operators** (with optional Excel batch upload).  
2. **Assign a site / workstation** to each new operator.  
3. **Determine the site responsible** (n+1) automatically for new operators.  
4. **Store command/write data in Cosmos DB** (event-sourced).  
5. **Provide read data** via a **separate database** fed by the **change feed** (handled by data team).

---

## Table of Contents
1. [Overview of Architecture](#1-overview-of-architecture)  
2. [Domain Model & Entity](#2-domain-model--entity)  
3. [Event Sourcing Model](#3-event-sourcing-model)  
   - [3.1 Write Model in Cosmos DB](#31-write-model-in-cosmos-db)  
   - [3.2 Read Model in a Separate DB](#32-read-model-in-a-separate-db)  
   - [3.3 Change Feed for Projections](#33-change-feed-for-projections)  
4. [Commands, Events, and Workflows](#4-commands-events-and-workflows)  
   - [4.1 CreateOperator Command](#41-createoperator-command)  
   - [4.2 UpdateOperator Command](#42-updateoperator-command)  
5. [Excel Upload Flow](#5-excel-upload-flow)  
6. [API Endpoints (Write-Side)](#6-api-endpoints-write-side)  
7. [Schema Details](#7-schema-details)  
8. [Implementation Notes](#8-implementation-notes)  
9. [Roles & Permissions](#9-roles--permissions)  
10. [Use Case Scenarios](#10-use-case-scenarios)  
11. [Summary](#11-summary)  

---

## 1. Overview of Architecture

The **Operator Management** service adopts a **CQRS** and **event-sourcing** style:

- **Command/Write Side**  
  - Commands (`CreateOperator`, `UpdateOperator`) are processed by the microservice.  
  - **Cosmos DB** acts as an **event store**, storing each domain event in a "write" collection.  
  - Operators do not have an updatable "row" in Cosmos DB; instead, each change results in a new **event** document.

- **Read Side**  
  - A **separate database** (relational or another NoSQL) is maintained by the **data team**.  
  - They listen to **Cosmos DB change feed** (or events) to build a **materialized view** of operators, sites, and their n+1 relationships.  
  - Queries for operator info come from this read database (or from the data team's API).

> **Goal**: Clean separation of **write** (event store) and **read** (materialized, query-ready data). 

### 1.1 CQRS Architecture

The CQRS pattern implementation in the Operator Management service:

1. **Command Flow**:
   - Client sends commands through Web API
   - Commands are handled and validated
   - Domain logic generates events
   - Events are stored in Cosmos DB

2. **Event Publishing**:
   - Events are published to Service Bus/Event Hub
   - Other services can subscribe to relevant events

3. **Read Flow**:
   - Change Feed processes new events
   - Projections update read tables
   - Read API serves queries
   - Skills-related tables support workstation assignment validation

4. **Client Interaction**:
   - Commands go through command-side API
   - Queries are served by read-side API

---

## 2. Domain Model & Entity

### 2.1 Operator (Aggregate)

An **Operator** within this bounded context has attributes:
- `operatorId` (unique ID, generated on creation)  
- `fullName`  
- `siteId` or `siteName` (assigned site for the operator)  
- `siteResponsibleId` (the "n+1" for that site, determined automatically on creation)  
- **Other**: `createdDate`, `source` (e.g., `CW` vs. `WORKDAY`), etc.

### 2.2 Site / Site Responsible (Reference)

While sites and site managers might be managed in another domain or system, here we assume:
- The microservice, upon operator creation, **looks up** the site's responsible manager (n+1) from an internal reference or a config (could be a cached list or an API call to another service).  
- The chosen `siteResponsibleId` is appended to the newly created operator event data.

---

## 3. Event Sourcing Model

### 3.1 Write Model in Cosmos DB

- A **Cosmos DB collection** (e.g., `operatorEvents`) holds **events** for each operator.  
- **Partition Key**: Typically `operatorId`.  
- **Event Documents**: Each event document has:
  - `id`: a unique GUID for the event  
  - `operatorId`: the aggregate ID  
  - `type`: e.g., `OperatorCreated`, `OperatorUpdated`  
  - `timestamp`: date/time the event was produced  
  - `payload`: JSON containing the event's data (e.g., operator name, site, etc.)

An example event document:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "operatorId": "operator-123",
  "type": "OperatorCreated",
  "timestamp": "2025-02-12T10:00:00Z",
  "payload": {
    "fullName": "John Doe",
    "siteId": "siteA",
    "siteResponsibleId": "manager-789",
    "source": "CW"
  }
}
```

### 3.2 Read Model in a Separate DB
- Data Team sets up a materialized view in, say, SQL or another NoSQL.
- They only store the latest operator info (flattened). For example:

```sql
CREATE TABLE OperatorsRead (
  operatorId VARCHAR PRIMARY KEY,
  fullName VARCHAR,
  siteId VARCHAR,
  siteResponsibleId VARCHAR,
  source VARCHAR,
  lastModified DATETIME
  -- etc.
);
```

All queries for operator profiles come from OperatorsRead (or a data team–managed API).

### 3.3 Change Feed for Projections
- Cosmos DB Change Feed can detect new event documents.
- The data team (or an internal process) subscribes to the feed:
  - Reads each new event.
  - Applies the event logic to the "read model" in the separate DB.
  - Upserts or modifies the row representing the latest state.
- Note: In a pure event-sourcing approach, you'd also manage reconstructing state from events. Here, the data team's read model does that once per new event.

## 4. Commands, Events, and Workflows

### 4.1 CreateOperator Command
Purpose: User or system requests to create a new operator.

Input: `fullName`, `siteId` (optional if the user picks it), or it's derived from the Excel data.

Steps:
1. Validate that `fullName` is present.
2. Lookup `siteResponsibleId` for the given `siteId`.
3. Generate `operatorId` (GUID).
4. Produce `OperatorCreated` event in Cosmos DB.

Resulting Event: `OperatorCreated`
```json
{
  "operatorId": "operator-123",
  "fullName": "John Doe",
  "siteId": "siteA",
  "siteResponsibleId": "manager-789",
  "source": "CW",
  "timestamp": "..."
}
```

### 4.2 UpdateOperator Command
Purpose: If an operator's name, site, or other metadata changes.

Input: `operatorId`, updated fields.

Steps:
1. Check if `operatorId` exists (by replaying events or using a quick query).
2. Validate fields (e.g., new site requires new siteResponsible lookup?).
3. Produce `OperatorUpdated` event with the delta.

Resulting Event: `OperatorUpdated`
```json
{
  "operatorId": "operator-123",
  "changes": {
    "fullName": "Johnathan Doe",
    "siteId": "siteB",
    "siteResponsibleId": "manager-999"
  },
  "timestamp": "..."
}
```

Additional commands might handle special flows (e.g., "DeactivateOperator"), each producing its own domain event.

## 5. Excel Upload Flow
1. User uploads an Excel file with one or more operators (name, site, etc.).
2. Parsing: The service reads the rows (e.g., using a Nest.js + xlsx library).
3. For each row:
   - Construct a CreateOperator command object.
   - Validate data.
   - Execute command → produces an OperatorCreated event in Cosmos DB.
4. Async or batch process: The read model is eventually updated once the events appear on the change feed.

Note: The user might see a "batch creation successful" message, while the read DB is updated asynchronously.

## 6. API Endpoints (Write-Side)
Below are potential Nest.js endpoints for the command (write) side.

### POST /operators
Body:
```json
{
  "fullName": "John Doe",
  "siteId": "siteA"
}
```
Behavior:
- Executes CreateOperator command.
- Returns operatorId or success status.

### PUT /operators/:operatorId
Body:
```json
{
  "fullName": "Johnathan Doe",
  "siteId": "siteB"
}
```
Behavior:
- Executes UpdateOperator command.
- Produces an OperatorUpdated event.

### POST /operators/upload-excel (batch creation)
- Consumes: .xlsx file.
- Behavior:
  - Parses each row → triggers CreateOperator for each.
  - Returns a summary of created IDs or error rows.

Reads are not served directly here. The application or UI queries the read DB (exposed by the data team's read API or an internal read microservice).

## 7. Schema Details

### 7.1 Cosmos DB: operatorEvents Collection
| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique event ID (GUID). |
| operatorId | string | Partition key, grouping events by operator. |
| type | string | Event type (e.g., OperatorCreated, OperatorUpdated). |
| timestamp | datetime | Creation time of the event. |
| payload | object | Event-specific data (name, site, siteResponsibleId, etc.). |

Indexing:
- Partition key is operatorId.
- Additional indices could be on type, timestamp if needed for queries.

### 7.2 Read DB (OperatorsRead)
Data Team decides the structure. A typical table:

```sql
CREATE TABLE OperatorsRead (
    operatorId VARCHAR(50) PRIMARY KEY,
    fullName VARCHAR(200),
    siteId VARCHAR(50),
    siteResponsibleId VARCHAR(50),
    source VARCHAR(50),
    lastModified DATETIME
);
```

They may also add columns for a human-friendly site name or other derived attributes.

### 7.3 Skills-Related Tables (Read-Only)
The following tables are used for skills validation during workstation assignments:

```sql
CREATE TABLE OperatorSkills (
    operatorId VARCHAR(50),
    skillId VARCHAR(50),
    skillLevel INT,
    acquiredDate DATETIME,
    PRIMARY KEY (operatorId, skillId)
);

CREATE TABLE WorkstationSkills (
    workstationId VARCHAR(50),
    skillId VARCHAR(50),
    requiredLevel INT,
    PRIMARY KEY (workstationId, skillId)
);
```

These tables are used in read-only mode by this service to validate workstation assignments. Before updating an operator's workstation, the service:
1. Retrieves the operator's skills
2. Fetches the required skills for the target workstation
3. Validates that the operator possesses all required skills at the appropriate levels

### 7.4 Event Publishing
The service uses a pub/sub mechanism (e.g., Azure Service Bus or Event Hub) to notify other services about operator-related changes. Events published include:

- `OperatorCreated`
- `OperatorUpdated`
- `OperatorSkillsChanged`
- `WorkstationAssigned`

Example event payload for WorkstationAssigned:
```json
{
  "eventType": "WorkstationAssigned",
  "operatorId": "operator-123",
  "workstationId": "ws-456",
  "timestamp": "2025-02-12T10:00:00Z",
  "validatedSkills": [
    {
      "skillId": "skill-789",
      "operatorLevel": 3,
      "requiredLevel": 2
    }
  ]
}
```

Other services can subscribe to these events to maintain their own operator-related data or trigger dependent workflows.

## 8. Implementation Notes
- Cosmos DB SDK: Use the official Nest.js-compatible @azure/cosmos (or @azure/azure-cosmos) to insert event documents.
- Excel Parsing: Could use xlsx library or node-xlsx.
- Event Replay: For large event streams, you might implement an in-memory or snapshot approach to avoid replaying all events for every update.
- Performance:
  - Writes are mostly single docs to Cosmos. This is efficient.
  - The data team's read model receives updates from the Change Feed. This is near real-time but asynchronous.
- Validation:
  - On creation, query a reference table or service for siteResponsibleId given a siteId.
  - If it fails, the command can be rejected or queued for manual resolution.
- Skills Validation:
  - Before workstation assignment, validate operator skills against workstation requirements
  - Cache frequently accessed skills data to improve performance
  - Log skill validation results for audit purposes
- Event Publishing:
  - Use Azure Service Bus topics for reliable event delivery
  - Implement dead-letter queue handling for failed event deliveries
  - Monitor event delivery latency and success rates

## 9. Roles & Permissions

### 9.1 Role Definitions


2. **TKS**
   - Can create and update operator basic information
   - Can view operator profiles across all sites
   - Cannot assign workstations
   - Can upload Excel files for batch operator creation
   - Limited access to skill information

3. **Team Leader**
   - Can view operators within their team/site
   - Can update workstation
   - Can view operator skills and workstation requirements
   - Cannot create or modify operator basic information


### 9.2 Permission Matrix

| Operation                   | TKS        | Team Leader |
|-----------------------------|------------|-------------|
| Create Operator             |  Yes       | No          |          |
| Update Operator Info        |  Yes       | No          |
| View Operator Profile       |  Yes       | team Only   |
| Assign Workstation          |  No        | Yes         |
| Upload Excel Batch          |  Yes       | No          |
| View Skills                 |  No        | team only   |
| Configure Skills            |  No        | No          |
| Audit Events                |  No        | No          |

### 9.3 Implementation Details

```typescript
interface Permission {
  resource: string;  // e.g., 'operator', 'workstation', 'skill'
  action: string;    // e.g., 'create', 'read', 'update'
  scope: string;     // e.g., 'site', 'all', 'team'
}

interface Role {
  name: string;
  permissions: Permission[];
  restrictions?: {
    siteId?: string;
    teamId?: string;
  };
}
```

- Permissions are enforced at the API gateway level
- Additional fine-grained permissions are checked in command handlers
- Role assignments are stored in a separate authorization service
- JWT tokens contain role and scope information

## 10. Use Case Scenarios

### 10.1 New Operator Onboarding

1. **HR Initiates Onboarding**
   ```mermaid
   sequenceDiagram
       participant TKS
       participant API
       participant CommandHandler
       participant CosmosDB
       participant EventBus
       
       TKS->>API: CreateOperator Command
       API->>CommandHandler: Validate & Process
       CommandHandler->>CosmosDB: Store OperatorCreated Event
       CosmosDB->>EventBus: Publish Event
       EventBus->>ReadDB: Update Projections
       API->>TKS: Return Success
   ```

2. **Team Leader Assigns Workstation**
   - Checks operator skills
   - Validates against workstation requirements
   - Creates WorkstationAssigned event
   - Notifies relevant systems

### 10.2 Batch Upload Scenario

1. **Excel Upload Process**
   - HR uploads Excel file
   - System validates format
   - Processes rows in batches
   - Reports success/failures

2. **Error Handling**
   ```mermaid
   flowchart TD
       A[Upload Excel] --> B{Validate Format}
       B -->|Invalid| C[Return Error]
       B -->|Valid| D[Process Batch]
       D --> E{Row Validation}
       E -->|Failed| F[Add to Error Report]
       E -->|Success| G[Create Operator]
       G --> H[Next Row]
   ```

### 10.3 Workstation Assignment

1. **Skills Validation Flow**
   ```mermaid
   flowchart LR
       A[Get Operator Skills] --> B[Get Workstation Requirements]
       B --> C{Compare Skills}
       C -->|Match| D[Assign Workstation]
       C -->|No Match| E[Return Mismatch Details]
   ```

2. **Assignment Process**
   - Team Leader requests assignment
   - System validates skills
   - Site Admin approves
   - System creates events
   - Other services updated via pub/sub

### 10.4 Error Scenarios

1. **Duplicate Operator Creation**
   - System detects duplicate
   - Returns appropriate error
   - Logs attempt for audit

2. **Invalid Skill Requirements**
   - Validates skill levels
   - Returns detailed mismatch info
   - Suggests alternative workstations

3. **System Unavailability**
   - Command side: Returns 503
   - Read side: Uses cached data
   - Background sync when available

## 11. Summary
The Operator Management microservice's low-level design follows event sourcing and CQRS patterns:

1. All writes go to Cosmos DB, storing domain events (OperatorCreated, OperatorUpdated).
2. Reads are served by a separate DB managed by the data team, continuously updated via the Cosmos DB change feed.
3. On creation, the system automatically determines the operator's site responsible (n+1) and includes that in the OperatorCreated event.
4. Excel batch upload is handled by generating multiple CreateOperator commands, each producing new events in Cosmos DB.

This design supports scalable writes, easy auditing (every change is an event), and a flexible read model that the data team can optimize for analytics or query performance.
