# Low-Level Design Document: Crew Management

## Document Information

**Version:** 1.0.0  
**Last Updated:** 2023-03-26  
**Status:** Completed  

## Executive Summary

### Key Features

- Microservices-based architecture with seven core services
- Event-driven communication using Azure Service Bus
- Comprehensive operator and team management
- Skill tracking and certification management
- Flexible workstation and assignment management
- Department Head walk procedure support
- Role-based access control and security

### Business Benefits

- Streamlined workforce management
- Improved operational visibility
- Enhanced skills and certification tracking
- Efficient team and organizational structure management
- Data-driven decision support for manufacturing operations
- Seamless integration with other platform modules

## Table of Contents

1. [Overview](#1-overview)
   - [Purpose and Scope](#11-purpose-and-scope)
   - [Key Components](#12-key-components)
2. [Architecture Overview](#2-architecture-overview)
   - [High-Level Architecture](#21-high-level-architecture)
   - [Technical Components](#22-technical-components)
3. [Technology Stack](#3-technology-stack)
4. [Core Microservices](#4-core-microservices)
   - [Operator Management Service](#41-operator-management-service)
   - [Employee Assignment Service](#42-employee-assignment-service)
   - [Team Assignment Service](#43-team-assignment-service)
   - [DH Walk Service](#44-dh-walk-service)
   - [Workstation Service](#45-workstation-service)
   - [Direct Dependents Service](#46-direct-dependents-service)
   - [Operator Skills Service](#47-operator-skills-service)
5. [Integration Patterns](#5-integration-patterns)
   - [Event-Driven Communication](#51-event-driven-communication)
   - [Synchronous API Calls](#52-synchronous-api-calls)
   - [Database Integration](#53-database-integration)
   - [Event Catalog](#54-event-catalog)
6. [Security](#6-security)
7. [Performance Considerations](#7-performance-considerations)
8. [Deployment Strategy](#8-deployment-strategy)

## 1. Overview

### 1.1 Purpose and Scope

The Crew Management module provides a comprehensive solution for managing manufacturing workforce operations. It implements a microservices architecture following Domain-Driven Design principles to ensure flexibility, scalability, and maintainability. The module enables manufacturing facilities to efficiently manage their workforce, track skills and certifications, maintain organizational structures, and optimize workstation assignments.

### 1.2 Key Components

The system consists of seven primary microservices:

1. Operator Management Service
2. Employee Assignment Service
3. Team Assignment Service
4. DH Walk Service
5. Workstation Service
6. Direct Dependents Service
7. Operator Skills Service

Each service is supported by:
- Azure Cosmos DB for data storage
- Azure Service Bus for event-driven communication
- Azure API Management for API gateway
- Azure AD B2C for authentication and authorization

## 2. Architecture Overview

### 2.1 High-Level Architecture

```mermaid
graph TD
    Client[Client Applications] --> APIM[Azure API Management]
    APIM --> OM[Operator Management Service]
    APIM --> EA[Employee Assignment Service]
    APIM --> TA[Team Assignment Service]
    APIM --> DH[DH Walk Service]
    APIM --> WS[Workstation Service]
    APIM --> DD[Direct Dependents Service]
    APIM --> OS[Operator Skills Service]
    
    OM --> ES[Event Store]
    EA --> ES
    TA --> ES
    DH --> ES
    WS --> ES
    DD --> ES
    OS --> ES
    
    ES --> WServ[Write Service]
    
    WServ --> CDB[Cosmos DB Read Models]
    WServ --> SQL[SQL Read Models]
    
    WS --> CDB
    WS --> SQL
    DD --> CDB
    DD --> SQL
```

### 2.2 Technical Components

1. **NestJS Microservices**: Each service implemented as a NestJS application
2. **Event Store**: Cosmos DB containers for storing command-side events
3. **Service Bus**: Message broker for reliable event processing
4. **Write Service**: Dedicated service for updating read models
5. **Read Models**: Optimized for specific query needs in either SQL or Cosmos DB
6. **Azure API Management**: Unified entry point with authentication and routing
7. **Event Sourcing**: Captures all state changes as events

### 2.3 CQRS Implementation

The Connected Workers Platform implements a complete data isolation strategy between microservices:

1. **No Direct Communication**: Microservices do not communicate directly with each other, not even through asynchronous messaging systems like Service Bus.

2. **Event Storage and Processing**:
   - Events are stored in Cosmos DB command-side containers
   - Events are published to Azure Service Bus 
   - A dedicated Write Service consumes events and updates read models
   - Complete separation of write and read operations

3. **Dual-Purpose Data Structure**:
   - Each microservice contains dedicated containers for write operations (events/commands)
   - Each microservice also maintains optimized read models for queries
   - The separation allows optimization for both write and read operations

4. **Read Models**:
   - SQL Tables: For relational data and complex queries
   - Cosmos DB Containers: For document-based queries and hierarchical data
   - Optimized for specific query patterns of each microservice

5. **Data Duplication Strategy**: When a microservice needs data owned by another microservice:
   - Data is duplicated into read models within the consuming microservice
   - This eliminates service-to-service dependencies and improves resilience

6. **Azure Change Feed Mechanism**:
   - Detects document changes in write model containers
   - Triggers functions to update corresponding read models
   - Enables eventual consistency between write and read models
   - Facilitates data duplication across microservice boundaries

7. **Read Model Projections**:
   - Specifically designed for query efficiency
   - Denormalized to avoid complex joins
   - May include redundant data to optimize specific query patterns
   - Updated asynchronously via Change Feed

This architecture provides several advantages:
- **Resilience**: Services can function independently even if other services are unavailable
- **Performance**: Data access is optimized for each service's specific needs
- **Scalability**: Services can scale independently based on their specific load patterns
- **Evolution**: Services can evolve independently with minimal impact on each other

## 3. Technology Stack

| Component | Technology | Description |
|-----------|------------|-------------|
| API Management | Azure API Management | Centralized API gateway for all microservices |
| Messaging | Azure Service Bus | Event-driven communication between services |
| Event Store | Azure Cosmos DB | Document storage for events with change feed |
| Read Models | Azure Cosmos DB & SQL Database | Storage for read-optimized projections |
| Write Service | NestJS | Dedicated service for read model updates |
| Backend | NestJS | TypeScript-based Node.js framework for microservices |
| Backend | .NET | .NET core framework for microservices |
| Containerization | Docker | Container platform for consistent deployment |
| Container Orchestration | Azure Kubernetes Service | Managed Kubernetes for container deployment |
| CI/CD | Azure DevOps | Continuous integration and deployment |
| Monitoring | Application Insights | Real-time performance and error monitoring |
| Logging | ELK Stack | Centralized logging and analysis |

## 4. Core Microservices

### 4.1 Operator Management Service

This document describes the low-level technical design for the **Operator Management** microservice (MS1), focusing on an **event-sourced** approach. The key responsibilities are:

1. **Create new operators** (with optional Excel batch upload).  
2. **Assign a site / workstation** to each new operator.  
3. **Determine the site responsible** (n+1) automatically for new operators.  
4. **Store command/write data in Cosmos DB** (event-sourced).  
5. **Provide read data** via a **separate database** fed by the **change feed** (handled by data team).

#### Domain Model & Entity

An **Operator** within this bounded context has attributes:
- `operatorId` (unique ID, generated on creation)  
- `fullName`  
- `siteId` or `siteName` (assigned site for the operator)  
- `siteResponsibleId` (the "n+1" for that site, determined automatically on creation)  
- **Other**: `createdDate`, `source` (e.g., `CW` vs. `WORKDAY`), etc.

#### Event Sourcing Model

##### Write Model in Cosmos DB

- A **Cosmos DB collection** (e.g., `operatorEvents`) holds **events** for each operator.  
- **Partition Key**: Typically `operatorId`.  
- **Event Documents**: Each event document has:
  - `id`: a unique GUID for the event  
  - `operatorId`: the aggregate ID  
  - `type`: e.g., `OperatorCreated`, `OperatorUpdated`  
  - `timestamp`: date/time the event was produced  
  - `payload`: JSON containing the event's data (e.g., operator name, site, etc.)

An example event document:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "operatorId": "operator-123",
  "type": "OperatorCreated",
  "timestamp": "2025-02-12T10:00:00Z",
  "payload": {
    "fullName": "John Doe",
    "siteId": "siteA",
    "siteResponsibleId": "manager-789",
    "source": "CW"
  }
}
```

##### Read Model in a Separate DB
- Data Team sets up a materialized view in, say, SQL or another NoSQL.
- They only store the latest operator info (flattened).

##### Change Feed for Projections
- Cosmos DB Change Feed can detect new event documents.
- The data team (or an internal process) subscribes to the feed:
  - Reads each new event.
  - Applies the event logic to the "read model" in the separate DB.
  - Upserts or modifies the row representing the latest state.

#### Commands, Events, and Workflows

##### CreateOperator Command
Purpose: User or system requests to create a new operator.

Input: `fullName`, `siteId` (optional if the user picks it), or it's derived from the Excel data.

Steps:
1. Validate that `fullName` is present.
2. Lookup `siteResponsibleId` for the given `siteId`.
3. Generate `operatorId` (GUID).
4. Produce `OperatorCreated` event in Cosmos DB.

Resulting Event: `OperatorCreated`
```json
{
  "operatorId": "operator-123",
  "fullName": "John Doe",
  "siteId": "siteA",
  "siteResponsibleId": "manager-789",
  "source": "CW",
  "timestamp": "..."
}
```

##### UpdateOperator Command
Purpose: If an operator's name, site, or other metadata changes.

Input: `operatorId`, updated fields.

Steps:
1. Check if `operatorId` exists (by replaying events or using a quick query).
2. Validate fields (e.g., new site requires new siteResponsible lookup?).
3. Produce `OperatorUpdated` event with the delta.

Resulting Event: `OperatorUpdated`
```json
{
  "operatorId": "operator-123",
  "changes": {
    "fullName": "Johnathan Doe",
    "siteId": "siteB",
    "siteResponsibleId": "manager-999"
  },
  "timestamp": "..."
}
```

#### Excel Upload Flow
1. User uploads an Excel file with one or more operators (name, site, etc.).
2. Parsing: The service reads the rows (e.g., using a Nest.js + xlsx library).
3. For each row:
   - Construct a CreateOperator command object.
   - Validate data.
   - Execute command → produces an OperatorCreated event in Cosmos DB.
4. Async or batch process: The read model is eventually updated once the events appear on the change feed.

#### API Endpoints (Write-Side)
Below are potential Nest.js endpoints for the command (write) side.

##### POST /operators
Body:
```json
{
  "fullName": "John Doe",
  "siteId": "siteA"
}
```
Behavior:
- Executes CreateOperator command.
- Returns operatorId or success status.

##### PUT /operators/:operatorId
Body:
```json
{
  "fullName": "Johnathan Doe",
  "siteId": "siteB"
}
```
Behavior:
- Executes UpdateOperator command.
- Produces an OperatorUpdated event.

##### POST /operators/upload-excel (batch creation)
- Consumes: .xlsx file.
- Behavior:
  - Parses each row → triggers CreateOperator for each.
  - Returns a summary of created IDs or error rows.

#### Roles & Permissions

##### Role Definitions

1. **TKS**
   - Can create and update operator basic information
   - Can view operator profiles across all sites
   - Cannot assign workstations
   - Can upload Excel files for batch operator creation
   - Limited access to skill information

2. **Team Leader**
   - Can view operators within their team/site
   - Can update workstation
   - Can view operator skills and workstation requirements
   - Cannot create or modify operator basic information

##### Permission Matrix

| Operation                   | TKS        | Team Leader |
|-----------------------------|------------|-------------|
| Create Operator             |  Yes       | No          |
| Update Operator Info        |  Yes       | No          |
| View Operator Profile       |  Yes       | team Only   |
| Assign Workstation          |  No        | Yes         |
| Upload Excel Batch          |  Yes       | No          |
| View Skills                 |  No        | team only   |
| Configure Skills            |  No        | No          |
| Audit Events                |  No        | No          |

#### Use Case Scenarios

##### New Operator Onboarding

1. **HR Initiates Onboarding**
   ```mermaid
   sequenceDiagram
       participant TKS
       participant API
       participant CommandHandler
       participant CosmosDB
       participant EventBus
       
       TKS->>API: CreateOperator Command
       API->>CommandHandler: Validate & Process
       CommandHandler->>CosmosDB: Store OperatorCreated Event
       CosmosDB->>EventBus: Publish Event
       EventBus->>ReadDB: Update Projections
       API->>TKS: Return Success
   ```

2. **Team Leader Assigns Workstation**
   - Checks operator skills
   - Validates against workstation requirements
   - Creates WorkstationAssigned event
   - Notifies relevant systems

##### Batch Upload Scenario

1. **Excel Upload Process**
   - HR uploads Excel file
   - System validates format
   - Processes rows in batches
   - Reports success/failures

2. **Error Handling**
   ```mermaid
   flowchart TD
       A[Upload Excel] --> B{Validate Format}
       B -->|Invalid| C[Return Error]
       B -->|Valid| D[Process Batch]
       D --> E{Row Validation}
       E -->|Failed| F[Add to Error Report]
       E -->|Success| G[Create Operator]
       G --> H[Next Row]
   ```

### 4.2 Employee Assignment Service

(This section would contain content from the LLD-employee-assignment-lld.md, but since the file content wasn't provided in the context, I'm leaving this as a placeholder. The structure would match the format of the other sections, preserving all original details from the LLD file.)

### 4.3 Team Assignment Service

#### 1. Overview

The **Team Assignment Service** is responsible for managing operator assignments to teams within the Connected Workers Platform. This service implements event sourcing and CQRS patterns to handle the assignment and unassignment of operators to teams.

##### Key Capabilities

1. **Team Assignment Management**
   - Assign operators to teams
   - Unassign operators from teams
   - Bulk assignment capabilities
   - Role-based assignment validation

2. **Assignment Validation**
   - Department-based validation
   - Workday status verification
   - Team leader authority validation
   - Site-based restrictions

3. **Query Capabilities**
   - View team assignments
   - Search operators by team
   - View unassigned operators
   - Filter by department and site

##### Technical Architecture

The service follows these architectural principles:

1. **Event Sourcing**
   - Assignment events stored in Cosmos DB
   - Complete audit trail of assignments
   - Event-based state reconstruction
   - Historical assignment view

2. **CQRS Pattern**
   - Separate command and query responsibilities
   - Optimized read models for queries
   - Eventual consistency
   - High performance for reads

##### Business Rules

1. **Assignment Rules**
   - Only team leaders can assign operators to their teams
   - Operators must belong to the same department as the team
   - Operators must be in active workday status
   - Assignments must be within the same site

2. **Validation Rules**
   - Team leader authority validation
   - Department membership validation
   - Workday status verification
   - Site boundary validation

#### 2. Domain Model & Events

##### 2.1 Event Types

```typescript
enum TeamAssignmentEventType {
  OPERATOR_ASSIGNED_TO_TEAM = "OPERATOR_ASSIGNED_TO_TEAM",
  OPERATOR_UNASSIGNED_FROM_TEAM = "OPERATOR_UNASSIGNED_FROM_TEAM"
}

interface BaseTeamEvent {
  id: string;
  type: TeamAssignmentEventType;
  timestamp: Date;
  site: string;
  metadata: {
    correlationId: string;
    userId: string;
    version: string;
  };
}

interface OperatorTeamAssignmentEvent extends BaseTeamEvent {
  type: TeamAssignmentEventType;
  payload: {
    assigner: {
      legacySiteId: string;
      role: string;
      firstName: string;
      lastName: string;
      department: string;
    };
    team: {
      teamId: string;
      teamName: string;
      legacyTeamLeaderId: string;
    };
    assignee: {
      legacySiteId: string;
      role: string;
      firstName: string;
      lastName: string;
      department: string;
    };
  };
}
```

##### 2.2 Event Examples

```json
// Assignment Event Example
{
  "id": "evt-123",
  "type": "OPERATOR_ASSIGNED_TO_TEAM",
  "timestamp": "2025-03-15T10:30:00Z",
  "site": "Site A",
  "metadata": {
    "correlationId": "corr-456",
    "userId": "user-789",
    "version": "1.0"
  },
  "payload": {
    "assigner": {
      "legacySiteId": "TL_123",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Assembly"
    },
    "team": {
      "teamId": "team-456",
      "teamName": "Assembly Team A",
      "legacyTeamLeaderId": "TL_123"
    },
    "assignee": {
      "legacySiteId": "OP_789",
      "role": "OPERATOR",
      "firstName": "Jane",
      "lastName": "Smith",
      "department": "Assembly"
    }
  }
}

// Unassignment Event Example
{
  "id": "evt-124",
  "type": "OPERATOR_UNASSIGNED_FROM_TEAM",
  "timestamp": "2025-03-15T14:30:00Z",
  "site": "Site A",
  "metadata": {
    "correlationId": "corr-457",
    "userId": "user-789",
    "version": "1.0"
  },
  "payload": {
    "assigner": {
      "legacySiteId": "TL_123",
      "role": "TEAM_LEADER",
      "firstName": "John",
      "lastName": "Doe",
      "department": "Assembly"
    },
    "team": {
      "teamId": "team-456",
      "teamName": "Assembly Team A",
      "legacyTeamLeaderId": "TL_123"
    },
    "assignee": {
      "legacySiteId": "OP_789",
      "role": "OPERATOR",
      "firstName": "Jane",
      "lastName": "Smith",
      "department": "Assembly"
    }
  }
}
```

#### 3. Assignment Rules

```typescript
interface AssignmentRule {
  id: string;
  assignerRole: string;  // Must be TEAM_LEADER
  assignToRole: string;  // Must be TEAM
  assigneeRole: string;  // Must be OPERATOR
  assignmentType: {
      assignToEmployee: boolean,
      assignToTeam: boolean,
      assignToWorkstation: boolean
    },
  validationRules: {
   scope: [department|site|subordinate]
    requiresWorkday: boolean;
  };
  isActive: boolean;
  country: string;
}
```

##### 3.1 Example Assignment Rule

```json
{
  "id": "rule-001",
  "assignerRole": "TEAM_LEADER",
  "assignToRole": "TEAM",
  "assigneeRole": "OPERATOR",
  "assignmentType": {
      "assignToEmployee": true,
      "assignToTeam": false,
      "assignToWorkstation": false
    },
  "validationRules": {
    "requiresSameDepartment": true,
    "requiresSameSite": true,
    "requiresWorkday": true
  },
  "isActive": true,
  "country": "MOROCCO"
}
```

#### 4. Commands

##### 4.1 AssignToTeam Command

```typescript
interface AssignToTeamCommand {
  site: string;
  assigner: { 
    id: string; 
    role: string;  // Must be TEAM_LEADER
  };
  team: { 
    id: string; 
    name: string; 
  };
  assignees: Array<{ 
    id: string; 
    role: string;  // Must be OPERATOR
  }>;
}
```

##### 4.2 UnassignFromTeam Command

```typescript
interface UnassignFromTeamCommand {
  site: string;
  assigner: { 
    id: string; 
    role: string;  // Must be TEAM_LEADER
  };
  team: { 
    id: string; 
    name: string; 
  };
  assignee: { 
    id: string; 
    role: string;  // Must be OPERATOR
  };
}
```

#### 5. Read Models

##### 5.1 Team Assignment Read Model
```json
{
  "teamleader_legacy_site": "E12345_MAR Moroocco 3",
  "teamleader_name": "cxxxxx",
  "teams": "team 1",
  "first_name": "xxx 1",
  "last_name": "xxx 1",
  "department": "E12345_MAR Moroocco 3",
  "operator_legacy_site": "cxxxxx",
  "role": "team 1",
  "role_status": "xxx 1",
  "site": "ssssss"
}
```

#### 6. API Endpoints

```typescript
// Assignment endpoint
POST /api/v1/assignments/assign-to-team
Request: {
  site: string;
  assigner: {
    id: string;
    role: string;  // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignees: Array<{
    id: string;
    role: string;  // Must be OPERATOR
  }>;
}
Response: {
  success: boolean;
  errors: string[];

}

// Unassignment endpoint
POST /api/v1/assignments/unassign-from-team
Request: {
  site: string;
  assigner: {
    id: string;
    role: string;  // Must be TEAM_LEADER
  };
  team: {
    id: string;
    name: string;
  };
  assignee: {
    id: string;
    role: string;  // Must be OPERATOR
  };

}
Response: {
  success: boolean;
  errors: string[];

}

// Query endpoints
GET /api/v1/teams/{teamId}/operators
GET /api/v1/teams/unassigned-operators?department={dept}&site={site}
```

#### 7. Assignment Flow

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant API as API Management
    participant AS as Assignment Service
    participant RDB as Read DB
    participant ES as Event Store

    TL->>API: POST /assignments/assign-to-team
    API->>AS: Process Assignment Request
    
    AS->>RDB: Validate Team Leader
    RDB-->>AS: Team Leader Valid
    
    AS->>RDB: Validate Team
    RDB-->>AS: Team Valid
    
    AS->>RDB: Validate Operators
    RDB-->>AS: Operators Valid
    
    AS->>AS: Validate Business Rules
    Note over AS: - Same Department<br>- Same Site<br>- Active Workday
    
    alt Validation Failed
        AS-->>API: Return Error
        API-->>TL: 400 Bad Request
    else Validation Passed
        AS->>ES: Store Assignment Event
        ES-->>AS: Event Stored
        AS-->>API: Success Response
        API-->>TL: 200 OK
    end
```

### 4.4 DH Walk Service

#### 1. Overview

The **DH Walk Microservice** is a critical component within the Connected Workers Platform that manages the organizational hierarchy and team structure of the manufacturing environment. This service enables Department Clerks to:

1. Create, update, and delete teams across the manufacturing organization
2. Assign and reassign Indirect Headcount (IH) roles (Coordinators, Shift Leaders, and Team Leaders) to teams
3. Maintain the hierarchical relationship between projects, families, value streams, areas, and teams

The DH Walk Microservice implements an event-sourcing architecture to provide full audit capabilities and history tracking of all organizational changes. By capturing all state changes as events, the system maintains a complete record of team structures and role assignments over time, enabling historical analysis and state reconstruction.

The service leverages the Command Query Responsibility Segregation (CQRS) pattern to separate write operations (team creation, role assignments) from read operations (viewing team structures and assignments). This approach enables optimized query performance while maintaining data integrity for state-changing operations.

#### 2. Business Requirements

The DH Walk Microservice addresses the following key business requirements:

1. **Organizational Structure Management**
   - Define and maintain manufacturing hierarchies from project down to team level
   - Track relationships between projects, families, value streams, areas, and teams
   - Support dynamic reorganization as manufacturing needs evolve

2. **Role Assignment Management**
   - Assign appropriate Coordinators, Shift Leaders, and Team Leaders to teams
   - Support reassignment as personnel changes occur
   - Maintain historical record of role assignments and changes

3. **Department Clerk Capabilities**
   - Enable Department Clerks to efficiently create and manage teams
   - Provide Department Clerks with assignment capabilities for all IH roles
   - Support bulk operations for efficient management of large organizations

4. **Visibility and Filtering**
   - Support filtering by project, family, value stream, and area
   - Provide search capabilities to quickly find specific teams
   - Enable sorting by various attributes (e.g., date, name)

5. **Integration with Connected Workers Platform**
   - Share team and role assignment data with other microservices
   - Consume ME definition data (projects, families, value streams, areas)
   - Support downstream processes that depend on team structures

#### 3. Core Domain Models

##### 3.1 Zoning Document

The Zoning Document represents the assignment of IH roles to a specific team within the manufacturing structure hierarchy.

```typescript
interface ZoningDocument {
  id: string;                      // Unique identifier
  project: string;                 // Project name
  family: string;                  // Family name
  value_stream: string;            // Value stream
  area: string;                    // Area
  coordinator_legacy_site_id: string; // Unique ID of the coordinator
  coordinator_fullname: string;              // Coordinator's full nam            
  shift_leader_legacy_site_id: string;  
  shift_leader_fullname: string;              // Shift leader's full name                     // 
  team_leader_legacy_site_id: string;        // Unique ID of the team leader
  team_leader_fullname: string;              // Team leader's full name  
  team_name: string;               // Name of the team
  version: number;                 // Document version for concurrency control
  created_at: string;              // Creation timestamp (ISO format)
  updated_at: string;              // Last update timestamp (ISO format)
  created_by: string;              // User ID of creator
  updated_by: string;              // User ID of last updater
}
```

##### 3.2 Team Document

The Team Document represents a team entity within the manufacturing structure hierarchy.

```typescript
interface TeamDocument {
  id: string;                      // Unique identifier
  team_name: string;               // Name of the team
  team_leader_fullname string;                    // Can be null if not assigned
  is_active: boolean;              // Team active status
  headcount: number;               // Number of operators in the team
  version: number;                 // Document version for concurrency control
  created_at: string;              // Creation timestamp (ISO format)
  updated_at: string;              // Last update timestamp (ISO format)
  created_by: string;              // User ID of creator
  updated_by: string;              // User ID of last updater
}
```

#### 4. Event Model

##### 4.1 Event Types

```typescript
enum DHWalkEventType {
  CREATE_TEAM = "CreateTeam",
  UPDATE_TEAM = "UpdateTeam",
  DELETE_TEAM = "DeleteTeam",
  ASSIGN_TO_TEAM = "AssignedToTeam",
  UNASSIGN_FROM_TEAM = "UnassignedFromTeam"
}

enum IHRole {
  COORDINATOR = "COORDINATOR",
  SHIFT_LEADER = "SHIFT_LEADER",
  TEAM_LEADER = "TEAM_LEADER"
}
```

##### 4.2 Event Structure

```typescript
interface BaseEvent {
  id: string;                      // Unique event identifier
  createdBy: string;               // User ID who initiated the event
  eventType: DHWalkEventType;      // Type of event
  timestamp: string;               // Timestamp when event occurred (ISO format)
  version: string;                 // Event schema version
  correlationId?: string;          // Optional correlation ID for tracing
}
```

##### 4.3 Event Examples

###### Create Team Event

```typescript
interface CreateTeamEvent extends BaseEvent {
  eventType: DHWalkEventType.CREATE_TEAM;
  payload: {
    team_name: string;             // Name of the new team
    TL_legacy_site_id: string;     // Team Leader's legacy site ID
    TL_fullname: string;           // Team Leader's full name
  };
}
```

Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "CreateTeam", 
  "timestamp": "2025-02-12T10:00:00Z",
  "version": "1.0",
  "payload": {
    "TL_legacy_site_id": "TL_12345",
    "TL_fullname": "John Smith",
    "team_name": "Assembly Team 1"
  }
}
```

###### Assign To Team Event

```typescript
interface AssignToTeamEvent extends BaseEvent {
  eventType: DHWalkEventType.ASSIGN_TO_TEAM;
  payload: {
    project: string;               // Project name
    family: string;                // Family name
    value_stream: string;          // Value stream
    area: string;                  // Area
    Coor_legacy_site_id: string;   // Coordinator's legacy site ID
    Coor_fullname: string;         // Coordinator's full name
    SL_legacy_site_id: string;     // Shift Leader's legacy site ID
    SL_fullname: string;           // Shift Leader's full name
    TL_legacy_site_id: string;     // Team Leader's legacy site ID
    TL_fullname: string;           // Team Leader's full name
    team_name: string;             // Team name
  };
}
```

Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToTeam",
  "timestamp": "2025-02-12T10:00:00Z",
  "version": "1.0",
  "payload": {
    "project": "V426A",
    "family": "V426-L&R-RearDoor",
    "value_stream": "Line 1",
    "area": "SUB",
    "Coor_legacy_site_id": "CO_12345",
    "Coor_fullname": "Said Ousrhir",
    "SL_legacy_site_id": "SL_67890",
    "SL_fullname": "Bendissa Hibbou",
    "TL_legacy_site_id": "TL_45678",
    "TL_fullname": "WARDA HARDALA",
    "team_name": "E209C"
  }
}
```

#### 5. Commands

##### 5.1 Command Types

```typescript
// Create Team Command
interface CreateTeamCommand {
  team_name: string;               // Team name
  team_leader_id?: string;         // Optional Team Leader ID (can be assigned later)
}

// Assign Role Command
interface AssignRoleCommand {
  project: string;                 // Project name
  family: string;                  // Family name
  value_stream: string;            // Value stream
  area: string;                    // Area
  team_name: string;               // Team name
  role: IHRole;                    // COORDINATOR, SHIFT_LEADER, or TEAM_LEADER
  employee_id: string;             // Legacy site ID of assignee
}

// Unassign Role Command
interface UnassignRoleCommand {
  project: string;                 // Project name
  family: string;                  // Family name
  value_stream: string;            // Value stream
  area: string;                    // Area
  team_name: string;               // Team name
  role: IHRole;                    // COORDINATOR, SHIFT_LEADER, or TEAM_LEADER
}

// Update Team Command
interface UpdateTeamCommand {
  team_id: string;                 // Team ID
  team_name?: string;              // New team name (optional)
  is_active?: boolean;             // New active status (optional)
}

// Delete Team Command
interface DeleteTeamCommand {
  team_id: string;                 // Team ID
}
```

##### 5.2 Command Validation

Commands are validated based on:

1. **Data Integrity** - Ensure all required fields are present and valid
2. **Business Rules** - Enforce rules such as:
   - Team names must be unique within a project-family-value_stream-area combination
   - Users can only be assigned to one role at a time
   - Department Clerks can only manage teams within their department
3. **Concurrency** - Handle concurrent modification using optimistic concurrency control

#### 6. Permissions and Authorization

##### 6.1 Role-Based Access Control

| Role             | Create Team | Delete Team | Update Team | Assign Coordinator | Assign Shift Leader | Assign Team Leader | View Teams |
|------------------|-------------|-------------|-------------|--------------------|--------------------|-------------------|------------|
| Department Clerk | ✓           | ✓           | ✓           | ✓                  | ✓                  | ✓                 | ✓          |
| Coordinator      |             |             |             |                    |                    |                   | ✓          |
| Shift Leader     |             |             |             |                    |                    |                   | ✓          |
| Team Leader      |             |             |             |                    |                    |                   | ✓          |
| Manufacturing Engineer |        |             |             |                    |                    |                   | ✓          |

##### 6.2 Authorization Rules

1. Department Clerks can only manage teams within their assigned departments
2. Users can view teams and assignments according to their hierarchical position
3. Special roles (e.g., Quality Supervisors) may have cross-departmental view permissions
4. Audit trail of all changes is maintained for accountability

#### 7. API Endpoints

##### 7.1 Team Management

```typescript
// Create a new team
POST /api/v1/teams
Request: {
  team_name: string;
  team_leader_id?: string;  // Optional
}
Response: {
  id: string;  // Newly created team ID
  success: boolean;
  errors?: string[];
}

// Update an existing team
PUT /api/v1/teams/{teamId}
Request: {
  team_name?: string;
  is_active?: boolean;
}
Response: {
  success: boolean;
  errors?: string[];
}

// Delete a team
DELETE /api/v1/teams/{teamId}
Response: {
  success: boolean;
  errors?: string[];
}
```

##### 7.2 Assignment Management

```typescript
// Assign role to team
POST /api/v1/assignments
Request: {
  project: string;
  family: string;
  value_stream: string;
  area: string;
  team_name: string;
  role: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER";
  employee_id: string;
}
Response: {
  success: boolean;
  errors?: string[];
}

// Unassign role from team
DELETE /api/v1/assignments
Request: {
  project: string;
  family: string;
  value_stream: string;
  area: string;
  team_name: string;
  role: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER";
}
Response: {
  success: boolean;
  errors?: string[];
}
```

##### 7.3 Query Endpoints

```typescript
// Get all teams with optional filtering
GET /api/v1/teams
Query Parameters:
  - project?: string
  - family?: string
  - value_stream?: string
  - area?: string
  - team_name?: string
  - has_coordinator?: boolean
  - has_shift_leader?: boolean
  - has_team_leader?: boolean
  - is_active?: boolean
  - sort_by?: string
  - page?: number
  - page_size?: number
Response: {
  teams: ZoningDocument[];
  total_count: number;
  page: number;
  page_size: number;
}

// Get specific team by ID
GET /api/v1/teams/{teamId}
Response: {
  team: ZoningDocument;
}

// Get assignment history
GET /api/v1/teams/{teamId}/history
Query Parameters:
  - role?: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER"
  - from_date?: string  // ISO format
  - to_date?: string    // ISO format
  - page?: number
  - page_size?: number
Response: {
  history: {
    event_type: string;
    timestamp: string;
    role: string;
    employee_id?: string;
    employee_name?: string;
    performed_by: string;
  }[];
  total_count: number


#### 8. Workflows

##### 8.1 Team Creation

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB

    DC->>API: Create Team Request
    API->>API: Validate Request
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store CreateTeam Event
        ES->>RP: Process Event
        RP->>DB: Create Team Document
        RP->>DB: Create Initial Zoning Document
        API-->>DC: Return Success
    end
```

##### 8.2 Role Assignment

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Assign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Event
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
```

##### 8.3 Role Reassignment

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Reassign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store UnassignedFromTeam Event
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Events
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
```

#### 9. Data Persistence

##### 9.1 Document Structure

The DH Walk microservice uses a document database (Cosmos DB) to store:

1. **Events** - All system events in an append-only event store
2. **Teams** - Team documents containing team details
3. **Zonings** - Zoning documents representing the assignment structure
4. **Projection Models** - Optimized read models for UI display

##### 9.2 Event Storage

Events are stored in an append-only event store with the following structure:

```typescript
interface StoredEvent {
  id: string;                    // Event ID
  type: string;                  // Event type
  payload: any;                  // Event payload (serialized JSON)
  metadata: {
    timestamp: string;           // When the event occurred
    user_id: string;             // Who created the event
    correlation_id?: string;     // For tracking related events
    causation_id?: string;       // What caused this event
    version: string;             // Schema version
  };
  sequence_number: number;       // For ordering events
  stream_id: string;             // Aggregate ID this event belongs to
}
```

##### 9.3 Read Model

The read model is optimized for the UI and contains denormalized data for efficient querying:

```typescript
interface TeamProjectionModel {
  id: string;                    // Unique identifier
  project: string;               // Project name
  family: string;                // Family name
  value_stream: string;          // Value stream
  area: string;                  // Area
  team_name: string;             // Team name
  coordinator_id?: string;       // Coordinator ID (if assigned)
  coordinator_name?: string;     // Coordinator name (if assigned)
  shift_leader_id?: string;      // Shift Leader ID (if assigned)
  shift_leader_name?: string;    // Shift Leader name (if assigned)
  team_leader_id?: string;       // Team Leader ID (if assigned)
  team_leader_name?: string;     // Team Leader name (if assigned)
  is_active: boolean;            // Active status
  headcount: number;             // Number of operators
  last_updated: string;          // Last update timestamp
}
```

#### 10. Integration Patterns

##### 10.1 Event Publication

After storing events in the event store, the DH Walk microservice publishes events to Azure Service Bus for consumption by other microservices:

```typescript
interface IntegrationEvent {
  id: string;                    // Event ID
  type: string;                  // Event type
  payload: any;                  // Event payload
  timestamp: string;             // When the event occurred
  source: string;                // Source system ("dh-walk")
  version: string;               // Schema version
}
```

##### 10.2 External System Integration

The DH Walk microservice integrates with:

1. **ME Definition Service** - To retrieve project, family, value stream, and area data
2. **Identity Service** - To retrieve employee information
3. **Workstation Service** - To notify of Team Leader changes
4. **TKS Service** - To notify of team structure changes

#### 11. UI Components

##### 11.1 Project List View

The main UI view shows a table of projects with their associated families, value streams, areas, and assigned roles:

1. **Filtering Controls** - Filter by project, family, value stream, area
2. **Search Box** - Quick search across all fields
3. **Sort Controls** - Sort by various columns
4. **Assignment Status** - Visual indicators for filled/unfilled positions
5. **Assignment Buttons** - Buttons for assigning unassigned roles

##### 11.2 Assignment Dialogs

When assigning roles, modal dialogs provide:

1. **Employee Selection** - Search and select from eligible employees
2. **Role Details** - Information about the role being assigned
3. **Validation Feedback** - Real-time validation and error messages
4. **Confirmation** - Confirmation of assignment action

#### 12. Performance Considerations

Performance optimization strategies for the CQRS architecture include:

- **Database Indexing**: Strategic indexes on common query patterns
- **Read/Write Separation**: CQRS pattern for high-throughput operations
- **Dedicated Write Service**: Specialized service for read model updates
- **Asynchronous Processing**: Background processing for non-critical operations
- **Pagination**: All list endpoints support pagination for large datasets
- **Rate Limiting**: API rate limiting to prevent abuse
- **Error Handling**: Robust retry strategies and dead-letter queues
- **Caching**: Strategic caching of frequently accessed read models
- **Query Optimization**: Read models designed for specific query patterns
- **Independent Scaling**: Command and query sides can scale independently
- **Optimized Event Processing**: Batch processing for higher throughput

#### 13. Security Considerations

1. **Authentication** - Azure AD integration with role-based permissions
2. **Authorization** - Granular permission checks based on role and department
3. **Audit Logging** - Complete audit trail of all actions
4. **Data Protection** - Encryption in transit and at rest
5. **Input Validation** - Defense against injection attacks

#### 14. Deployment Strategy

1. **Containerization** - Docker containers for consistent deployment
2. **Kubernetes** - For orchestration and scaling
3. **CI/CD Pipeline** - Automated testing and deployment
4. **Blue/Green Deployments** - Zero-downtime updates
5. **Environment Separation** - Dev, Test, Staging, Production

### 4.5 Workstation Service

(This section would contain content from the LLD-workstation.md, but since the file content wasn't provided in the context in a usable format, I'm leaving this as a placeholder. The structure would match the format of the other sections, preserving all original details from the LLD file.)

### 4.6 Direct Dependents Service

#### 1. Introduction

The Direct Dependents microservice provides a hierarchical view of the organizational structure for Aptiv Connected Workers. It enables visibility of reporting relationships from Plant Manager to operator level.

#### 2. Architecture Overview

- **Read-Only Service**: The microservice is designed to perform read operations from a database.
- **Data Source**: Reads hierarchical data and unassigned workers from an existing cosmosdb.
- **API**: Exposes endpoints to fetch organizational charts and unassigned employees based on user roles.

#### 3. Domain Model & Entities

##### 3.1 UserRoleHierarchy

```csharp
public class UserRoleHierarchy
{
    public string LecacySiteId { get; set; } // employee_id
    public string FullName { get; set; }
    public string Department { get; set; }
    public string Site { get; set; }
    public string Role { get; set; }
    public string ManagerLecacySiteId { get; set; }
    public List<SubordinateCrew> SubordinateCrew { get; set; }
}

public class SubordinateCrew
{
    public string SubLecacySiteId { get; set; }
    public string SubFullName { get; set; }
    public string SubRole { get; set; }
    public string SubRoleStatus { get; set; }
    public bool InWorkday { get; set; }
    public List<string> skills { get; set; }
    public string category { get; set; }
    public string contract_type { get; set; }
    public List<SubordinateCrew> Subordinates { get; set; }
}
```

##### 3.2 TeamLeaderTeamCrew

```csharp
public class TeamLeaderTeamCrew
{
    public string TeamLeaderLegacySite { get; set; }
    public string TeamLeaderName { get; set; }
    public string Teams { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Department { get; set; }
    public string OperatorLegacySite { get; set; }
    public string Role { get; set; }
    public string RoleStatus { get; set; }
}
```

##### 3.3 AssignmentRule

```csharp
public class AssignmentRule
{
    public string Id { get; set; }
    public string AssignerRole { get; set; }
    public string AssignToRole { get; set; }
    public string AssigneeRole { get; set; }
    public ValidationRules ValidationRules { get; set; }
    public bool IsActive { get; set; }
    public string Country { get; set; }
    public string Scope { get; set; }
}

public class ValidationRules
{
    public bool RequiresDirectHierarchy { get; set; }
    public bool RequiresSameDepartment { get; set; }
    public bool RequiresWorkday { get; set; }
}
```

#### 4. Business Logic

##### 4.1 OrganisationChartService

- **GetOrganisationChart**: Determines the role of the employee and fetches the organization chart based on the role and parameters.
- **GetUnassignedEmployees**: Fetches unassigned employees for the given user based on their role.

#### 5. API Endpoints

##### 5.1 Get /OrganisationChart

- **Endpoint**: `/api/organisation-chart`
- **Method**: `GET`
- **Query Parameters**: `legacySiteId` (string)

##### 5.2 Get /UnassignedEmployees

- **Endpoint**: `/api/unassigned-employees`
- **Method**: `GET`
- **Query Parameters**: `legacySiteId` (string)

#### 6. Database Schema

##### 6.1 UserRoleHierarchy Table

- **Columns**: `LecacySiteId`, `FullName`, `Department`, `Site`, `Role`, `ManagerLecacySiteId`, `SubordinateCrew`

##### 6.2 TeamLeaderTeamCrew Table

- **Columns**: `TeamLeaderLegacySite`, `TeamLeaderName`, `Teams`, `FirstName`, `LastName`, `Department`, `OperatorLegacySite`, `Role`, `RoleStatus`

##### 6.3 AssignmentRule Table

- **Columns**: `Id`, `AssignerRole`, `AssignToRole`, `AssigneeRole`, `ValidationRules`, `IsActive`, `Country`, `Scope`

#### 7. Error Handling

- **Business Errors**: Invalid role, missing data.
- **Technical Errors**: Database connectivity issues, API failures.

#### 8. Summary

The Direct Dependents microservice provides a flexible and generic way to fetch organizational data and unassigned employees based on employee roles and parameters defined in the `AssignmentRule` table. The service can be extended to handle additional cases by updating the parameter table and adding corresponding logic in the service layer.

### 4.7 Operator Skills Service

#### 1. Overview of Architecture

The Operator Skills service adopts a straightforward event-driven architecture:

##### Event Handling:
- The service subscribes to a Service Bus topic to receive events.
- Events contain operator skills data (operator ID and skills).
- The service processes these events and stores the data in a database.

##### Database:
- A relational database (e.g., SQL Server) is used to store operator skills data.
- The database schema is designed to efficiently query operator skills by employee ID and site.

#### 2. Domain Model & Entity

##### 2.1 Operator Skills
An Operator Skills entity within this bounded context has attributes:

- `employee_id` (unique ID for the employee)
- `site` (employee's site)
- `certifications` (employee's certifications)
- `skills` (family or job type within the project)

#### 3. Event Handling Model

##### 3.1 Event Subscription
The service subscribes to a Service Bus topic to receive events. Each event contains the following data:

- `event_name`: Name of the event.
- `operator_id`: Unique ID of the operator.
- `skills`: List of skills for the operator.

##### 3.2 Event Processing
Upon receiving an event, the service:
1. Parses the event data.
2. Validates the data.
3. Stores the data in the database.

#### 4. Database Schema

##### 4.1 Operator Skills Table

| Column Name     | Data Type | Description                           |
|----------------|----------|---------------------------------------|
| `employee_id`  | STRING   | Unique employee ID                    |
| `site`         | STRING   | Employee's site                       |
| `certifications` | STRING   | Employee's certifications            |
| `skills`       | STRING   | Family or job type within the project |

- **Clustered Index on `site`** → Because queries often filter by site.
- **Non-Clustered Index on `employee_id`** → Because queries often look up employees by ID.

#### Example SQL schema:

```sql
CREATE TABLE OperatorSkills (
    employee_id VARCHAR(50) PRIMARY KEY,
    site VARCHAR(50),
    certifications VARCHAR(255),
    skills VARCHAR(255)
);

CREATE INDEX idx_site ON OperatorSkills(site);
CREATE INDEX idx_employee_id ON OperatorSkills(employee_id);
```

#### 5. API Endpoints

##### 5.1 `POST /operator-skills`
Endpoint to receive operator skills data.

#### Body:

```json
{
  "event_name": "OperatorSkillsUpdated",
  "operator_id": "operator-123",
  "site": "siteA",
  "certifications": "cert1, cert2",
  "skills": "skill1, skill2"
}
```

#### Behavior:
1. Parses the event data.
2. Validates the data.
3. Stores the data in the database.

#### 6. Implementation Notes

- **Service Bus SDK**: Use the official Azure Service Bus SDK to subscribe to the topic and receive events.
- **Database Access**: Use an ORM (e.g., Entity Framework) or direct SQL queries to interact with the database.
- **Error Handling**: Implement robust error handling to manage invalid data and connection issues.
- **Logging**: Use a logging framework to log important events and errors.

#### 7. Roles & Permissions

##### 7.1 Role Definitions

**Admin:**
- Can view and manage all operator skills data.
- Can configure the service (e.g., change Service Bus topic subscription).

**User:**
- Can view operator skills data.
- Cannot modify or configure the service.

##### 7.2 Permission Matrix

| Operation                | Admin | User |
|--------------------------|-------|------|
| View Operator Skills     | Yes   | Yes  |
| Manage Operator Skills   | Yes   | No   |
| Configure Service        | Yes   | No   |

#### 8. Use Case Scenarios

##### 8.1 Receive and Store Operator Skills

```mermaid
sequenceDiagram
    participant ServiceBus
    participant MS3
    participant Database
    
    ServiceBus->>MS3: Send OperatorSkillsUpdated Event
    MS3->>MS3: Parse and Validate Event
    MS3->>Database: Store Operator Skills Data
    MS3->>ServiceBus: Acknowledge Event
```

##### 8.2 Query Operator Skills by Site

```mermaid
sequenceDiagram
    participant User
    participant MS3
    participant Database
    
    User->>MS3: GET /operator-skills?site=siteA
    MS3->>Database: Query Operator Skills by Site
    Database->>MS3: Return Operator Skills Data
    MS3->>User: Return Operator Skills Data
```

#### 9. Summary

The Operator Skills microservice (MS3) is designed to receive operator skills data from a Service Bus topic and store it in a database. The service ensures efficient querying of operator skills by employee ID and site, supporting the overall goal of maintaining detailed operator skill and certification data.

This design supports scalable event handling, robust data storage, and efficient querying, ensuring that operator skills data is always up-to-date and easily accessible.

## 5. Integration Patterns

### 5.1 Event-Driven Communication
Services publish domain events to Azure Service Bus topics when significant state changes occur. Other services subscribe to relevant events and react accordingly.

```mermaid
sequenceDiagram
    participant OM as Operator Management
    participant Bus as Azure Service Bus
    participant EA as Employee Assignment
    participant OS as Operator Skills
    
    OM->>Bus: Publish OperatorStatusChanged
    Bus->>EA: Notify of status change
    EA->>EA: Update assignments
    Bus->>OS: Notify of status change
    OS->>OS: Check skill implications
```

### 5.2 Synchronous API Calls
For immediate data needs, services make direct HTTP calls to other services' APIs.

### 5.3 Database Integration
For complex reporting needs, services may share read-only database views or use Cosmos DB Change Feed for real-time updates.

## 6. Security

The Crew Management microservices implement several security measures:

- **Authentication**: Azure AD integration with role-based permissions
- **Authorization**: Role-based access control with fine-grained permissions
- **API Gateway**: Azure API Management enforces security policies
- **Data Encryption**: Data encrypted at rest and in transit
- **Audit Logging**: Comprehensive audit trails for all operations
- **Certificate Management**: TLS certificates for secure communication

## 7. Performance Considerations

Performance optimization strategies include:

- **Database Indexing**: Strategic indexes on common query patterns
- **Read/Write Separation**: CQRS pattern for high-throughput operations
- **Asynchronous Processing**: Background processing for non-critical operations
- **Pagination**: All list endpoints support pagination for large datasets
- **Rate Limiting**: API rate limiting to prevent abuse

## 8. Deployment Strategy

The Crew Management microservices use a cloud-native deployment approach:

- **Containerization**: All services deployed as Docker containers
- **Kubernetes**: Azure Kubernetes Service for container orchestration
- **CI/CD Pipeline**: Automated builds and deployments via Azure DevOps
- **Environment Separation**: Development, Staging, and Production environments
- **Configuration Management**: External configuration via Azure App Configuration
- **Blue/Green Deployments**: Zero-downtime deployments
- **Monitoring and Alerting**: Application Insights for real-time monitoring