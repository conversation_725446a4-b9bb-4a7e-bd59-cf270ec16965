
# DH Walk Microservice - Low-Level Design
## Table of Contents
1. [Overview](#1-overview)
2. [Business Requirements](#2-business-requirements)
3. [Core Domain Models](#3-core-domain-models)
   - [3.1 Zoning Document](#31-zoning-document)
   - [3.2 Team Document](#32-team-document)
4. [Event Model](#4-event-model)
   - [4.1 Event Types](#41-event-types)
   - [4.2 Event Structure](#42-event-structure)
   - [4.3 Event Examples](#43-event-examples)
5. [Commands](#5-commands)
   - [5.1 Command Types](#51-command-types)
   - [5.2 Command Validation](#52-command-validation)
6. [Permissions and Authorization](#6-permissions-and-authorization)
   - [6.1 Role-Based Access Control](#61-role-based-access-control)
   - [6.2 Authorization Rules](#62-authorization-rules)
7. [API Endpoints](#7-api-endpoints)
   - [7.1 Team Management](#71-team-management)
   - [7.2 Assignment Management](#72-assignment-management)
   - [7.3 Query Endpoints](#73-query-endpoints)
8. [Workflows](#8-workflows)
   - [8.1 Team Creation](#81-team-creation)
   - [8.2 Role Assignment](#82-role-assignment)
   - [8.3 Role Reassignment](#83-role-reassignment)
9. [Data Persistence](#9-data-persistence)
   - [9.1 Document Structure](#91-document-structure)
   - [9.2 Event Storage](#92-event-storage)
   - [9.3 Read Model](#93-read-model)
10. [Integration Patterns](#10-integration-patterns)
    - [10.1 Event Publication](#101-event-publication)
    - [10.2 External System Integration](#102-external-system-integration)
11. [UI Components](#11-ui-components)
    - [11.1 Project List View](#111-project-list-view)
    - [11.2 Assignment Dialogs](#112-assignment-dialogs)
12. [Performance Considerations](#12-performance-considerations)
    - [12.1 Query Optimization](#121-query-optimization)
    - [12.2 Caching Strategy](#122-caching-strategy)
13. [Security Considerations](#13-security-considerations)
14. [Deployment Strategy](#14-deployment-strategy)

## 1. Overview

The **DH Walk Microservice** is a critical component within the Connected Workers Platform that manages the organizational hierarchy and team structure of the manufacturing environment. This service enables Department Clerks to:

1. Create, update, and delete teams across the manufacturing organization
2. Assign and reassign Indirect Headcount (IH) roles (Coordinators, Shift Leaders, and Team Leaders) to teams
3. Maintain the hierarchical relationship between projects, families, value streams, areas, and teams

The DH Walk Microservice implements an event-sourcing architecture to provide full audit capabilities and history tracking of all organizational changes. By capturing all state changes as events, the system maintains a complete record of team structures and role assignments over time, enabling historical analysis and state reconstruction.

The service leverages the Command Query Responsibility Segregation (CQRS) pattern to separate write operations (team creation, role assignments) from read operations (viewing team structures and assignments). This approach enables optimized query performance while maintaining data integrity for state-changing operations.

## 2. Business Requirements

The DH Walk Microservice addresses the following key business requirements:

1. **Organizational Structure Management**
   - Define and maintain manufacturing hierarchies from project down to team level
   - Track relationships between projects, families, value streams, areas, and teams
   - Support dynamic reorganization as manufacturing needs evolve

2. **Role Assignment Management**
   - Assign appropriate Coordinators, Shift Leaders, and Team Leaders to teams
   - Support reassignment as personnel changes occur
   - Maintain historical record of role assignments and changes

3. **Department Clerk Capabilities**
   - Enable Department Clerks to efficiently create and manage teams
   - Provide Department Clerks with assignment capabilities for all IH roles
   - Support bulk operations for efficient management of large organizations

4. **Visibility and Filtering**
   - Support filtering by project, family, value stream, and area
   - Provide search capabilities to quickly find specific teams
   - Enable sorting by various attributes (e.g., date, name)

5. **Integration with Connected Workers Platform**
   - Share team and role assignment data with other microservices
   - Consume ME definition data (projects, families, value streams, areas)
   - Support downstream processes that depend on team structures

## 3. Core Domain Models

### 3.1 Zoning Document

The Zoning Document represents the assignment of IH roles to a specific team within the manufacturing structure hierarchy.

```typescript
interface ZoningDocument {
  id: string;                      // Unique identifier
  project: string;                 // Project name
  family: string;                  // Family name
  value_stream: string;            // Value stream
  area: string;                    // Area
  coordinator_legacy_site_id: string; // Unique ID of the coordinator
  coordinator_fullname: string;              // Coordinator's full nam            
  shift_leader_legacy_site_id: string;  
  shift_leader_fullname: string;              // Shift leader's full name                     // 
  team_leader_legacy_site_id: string;        // Unique ID of the team leader
  team_leader_fullname: string;              // Team leader's full name  
  team_name: string;               // Name of the team
  version: number;                 // Document version for concurrency control
  created_at: string;              // Creation timestamp (ISO format)
  updated_at: string;              // Last update timestamp (ISO format)
  created_by: string;              // User ID of creator
  updated_by: string;              // User ID of last updater
}
```

### 3.2 Team Document

The Team Document represents a team entity within the manufacturing structure hierarchy.

```typescript
interface TeamDocument {
  id: string;                      // Unique identifier
  team_name: string;               // Name of the team
  team_leader_fullname string;                    // Can be null if not assigned
  is_active: boolean;              // Team active status
  headcount: number;               // Number of operators in the team
  version: number;                 // Document version for concurrency control
  created_at: string;              // Creation timestamp (ISO format)
  updated_at: string;              // Last update timestamp (ISO format)
  created_by: string;              // User ID of creator
  updated_by: string;              // User ID of last updater
}
```

## 4. Event Model

### 4.1 Event Types

```typescript
enum DHWalkEventType {
  CREATE_TEAM = "CreateTeam",
  UPDATE_TEAM = "UpdateTeam",
  DELETE_TEAM = "DeleteTeam",
  ASSIGN_TO_TEAM = "AssignedToTeam",
  UNASSIGN_FROM_TEAM = "UnassignedFromTeam"
}

enum IHRole {
  COORDINATOR = "COORDINATOR",
  SHIFT_LEADER = "SHIFT_LEADER",
  TEAM_LEADER = "TEAM_LEADER"
}
```

### 4.2 Event Structure

```typescript
interface BaseEvent {
  id: string;                      // Unique event identifier
  createdBy: string;               // User ID who initiated the event
  eventType: DHWalkEventType;      // Type of event
  timestamp: string;               // Timestamp when event occurred (ISO format)
  version: string;                 // Event schema version
  correlationId?: string;          // Optional correlation ID for tracing
}
```

### 4.3 Event Examples

#### Create Team Event

```typescript
interface CreateTeamEvent extends BaseEvent {
  eventType: DHWalkEventType.CREATE_TEAM;
  payload: {
    team_name: string;             // Name of the new team
    TL_legacy_site_id: string;     // Team Leader's legacy site ID
    TL_fullname: string;           // Team Leader's full name
  };
}
```

Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "CreateTeam", 
  "timestamp": "2025-02-12T10:00:00Z",
  "version": "1.0",
  "payload": {
    "TL_legacy_site_id": "TL_12345",
    "TL_fullname": "John Smith",
    "team_name": "Assembly Team 1"
  }
}
```

#### Assign To Team Event

```typescript
interface AssignToTeamEvent extends BaseEvent {
  eventType: DHWalkEventType.ASSIGN_TO_TEAM;
  payload: {
    project: string;               // Project name
    family: string;                // Family name
    value_stream: string;          // Value stream
    area: string;                  // Area
    Coor_legacy_site_id: string;   // Coordinator's legacy site ID
    Coor_fullname: string;         // Coordinator's full name
    SL_legacy_site_id: string;     // Shift Leader's legacy site ID
    SL_fullname: string;           // Shift Leader's full name
    TL_legacy_site_id: string;     // Team Leader's legacy site ID
    TL_fullname: string;           // Team Leader's full name
    team_name: string;             // Team name
  };
}
```

Example:
```json
{
  "id": "be21e010-12ab-4ca0-8881-bc378429efc3",
  "createdBy": "user-123",
  "eventType": "AssignedToTeam",
  "timestamp": "2025-02-12T10:00:00Z",
  "version": "1.0",
  "payload": {
    "project": "V426A",
    "family": "V426-L&R-RearDoor",
    "value_stream": "Line 1",
    "area": "SUB",
    "Coor_legacy_site_id": "CO_12345",
    "Coor_fullname": "Said Ousrhir",
    "SL_legacy_site_id": "SL_67890",
    "SL_fullname": "Bendissa Hibbou",
    "TL_legacy_site_id": "TL_45678",
    "TL_fullname": "WARDA HARDALA",
    "team_name": "E209C"
  }
}
```

## 5. Commands

### 5.1 Command Types

```typescript
// Create Team Command
interface CreateTeamCommand {
  team_name: string;               // Team name
  team_leader_id?: string;         // Optional Team Leader ID (can be assigned later)
}

// Assign Role Command
interface AssignRoleCommand {
  project: string;                 // Project name
  family: string;                  // Family name
  value_stream: string;            // Value stream
  area: string;                    // Area
  team_name: string;               // Team name
  role: IHRole;                    // COORDINATOR, SHIFT_LEADER, or TEAM_LEADER
  employee_id: string;             // Legacy site ID of assignee
}

// Unassign Role Command
interface UnassignRoleCommand {
  project: string;                 // Project name
  family: string;                  // Family name
  value_stream: string;            // Value stream
  area: string;                    // Area
  team_name: string;               // Team name
  role: IHRole;                    // COORDINATOR, SHIFT_LEADER, or TEAM_LEADER
}

// Update Team Command
interface UpdateTeamCommand {
  team_id: string;                 // Team ID
  team_name?: string;              // New team name (optional)
  is_active?: boolean;             // New active status (optional)
}

// Delete Team Command
interface DeleteTeamCommand {
  team_id: string;                 // Team ID
}
```

### 5.2 Command Validation

Commands are validated based on:

1. **Data Integrity** - Ensure all required fields are present and valid
2. **Business Rules** - Enforce rules such as:
   - Team names must be unique within a project-family-value_stream-area combination
   - Users can only be assigned to one role at a time
   - Department Clerks can only manage teams within their department
3. **Concurrency** - Handle concurrent modification using optimistic concurrency control

## 6. Permissions and Authorization

### 6.1 Role-Based Access Control

| Role             | Create Team | Delete Team | Update Team | Assign Coordinator | Assign Shift Leader | Assign Team Leader | View Teams |
|------------------|-------------|-------------|-------------|--------------------|--------------------|-------------------|------------|
| Department Clerk | ✓           | ✓           | ✓           | ✓                  | ✓                  | ✓                 | ✓          |
| Coordinator      |             |             |             |                    |                    |                   | ✓          |
| Shift Leader     |             |             |             |                    |                    |                   | ✓          |
| Team Leader      |             |             |             |                    |                    |                   | ✓          |
| Manufacturing Engineer |        |             |             |                    |                    |                   | ✓          |

### 6.2 Authorization Rules

1. Department Clerks can only manage teams within their assigned departments
2. Users can view teams and assignments according to their hierarchical position
3. Special roles (e.g., Quality Supervisors) may have cross-departmental view permissions
4. Audit trail of all changes is maintained for accountability

## 7. API Endpoints

### 7.1 Team Management

```typescript
// Create a new team
POST /api/v1/teams
Request: {
  team_name: string;
  team_leader_id?: string;  // Optional
}
Response: {
  id: string;  // Newly created team ID
  success: boolean;
  errors?: string[];
}

// Update an existing team
PUT /api/v1/teams/{teamId}
Request: {
  team_name?: string;
  is_active?: boolean;
}
Response: {
  success: boolean;
  errors?: string[];
}

// Delete a team
DELETE /api/v1/teams/{teamId}
Response: {
  success: boolean;
  errors?: string[];
}
```

### 7.2 Assignment Management

```typescript
// Assign role to team
POST /api/v1/assignments
Request: {
  project: string;
  family: string;
  value_stream: string;
  area: string;
  team_name: string;
  role: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER";
  employee_id: string;
}
Response: {
  success: boolean;
  errors?: string[];
}

// Unassign role from team
DELETE /api/v1/assignments
Request: {
  project: string;
  family: string;
  value_stream: string;
  area: string;
  team_name: string;
  role: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER";
}
Response: {
  success: boolean;
  errors?: string[];
}
```

### 7.3 Query Endpoints

```typescript
// Get all teams with optional filtering
GET /api/v1/teams
Query Parameters:
  - project?: string
  - family?: string
  - value_stream?: string
  - area?: string
  - team_name?: string
  - has_coordinator?: boolean
  - has_shift_leader?: boolean
  - has_team_leader?: boolean
  - is_active?: boolean
  - sort_by?: string
  - page?: number
  - page_size?: number
Response: {
  teams: ZoningDocument[];
  total_count: number;
  page: number;
  page_size: number;
}

// Get specific team by ID
GET /api/v1/teams/{teamId}
Response: {
  team: ZoningDocument;
}

// Get assignment history
GET /api/v1/teams/{teamId}/history
Query Parameters:
  - role?: "COORDINATOR" | "SHIFT_LEADER" | "TEAM_LEADER"
  - from_date?: string  // ISO format
  - to_date?: string    // ISO format
  - page?: number
  - page_size?: number
Response: {
  history: {
    event_type: string;
    timestamp: string;
    role: string;
    employee_id?: string;
    employee_name?: string;
    performed_by: string;
  }[];
  total_count: number;
  page: number;
  page_size: number;
}
```

## 8. Workflows

### 8.1 Team Creation

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB

    DC->>API: Create Team Request
    API->>API: Validate Request
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store CreateTeam Event
        ES->>RP: Process Event
        RP->>DB: Create Team Document
        RP->>DB: Create Initial Zoning Document
        API-->>DC: Return Success
    end
```

### 8.2 Role Assignment

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Assign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Event
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
```

### 8.3 Role Reassignment

```mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Reassign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store UnassignedFromTeam Event
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Events
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
```

## 9. Data Persistence

### 9.1 Document Structure

The DH Walk microservice uses a document database (Cosmos DB) to store:

1. **Events** - All system events in an append-only event store
2. **Teams** - Team documents containing team details
3. **Zonings** - Zoning documents representing the assignment structure
4. **Projection Models** - Optimized read models for UI display

### 9.2 Event Storage

Events are stored in an append-only event store with the following structure:

```typescript
interface StoredEvent {
  id: string;                    // Event ID
  type: string;                  // Event type
  payload: any;                  // Event payload (serialized JSON)
  metadata: {
    timestamp: string;           // When the event occurred
    user_id: string;             // Who created the event
    correlation_id?: string;     // For tracking related events
    causation_id?: string;       // What caused this event
    version: string;             // Schema version
  };
  sequence_number: number;       // For ordering events
  stream_id: string;             // Aggregate ID this event belongs to
}
```

### 9.3 Read Model

The read model is optimized for the UI and contains denormalized data for efficient querying:

```typescript
interface TeamProjectionModel {
  id: string;                    // Unique identifier
  project: string;               // Project name
  family: string;                // Family name
  value_stream: string;          // Value stream
  area: string;                  // Area
  team_name: string;             // Team name
  coordinator_id?: string;       // Coordinator ID (if assigned)
  coordinator_name?: string;     // Coordinator name (if assigned)
  shift_leader_id?: string;      // Shift Leader ID (if assigned)
  shift_leader_name?: string;    // Shift Leader name (if assigned)
  team_leader_id?: string;       // Team Leader ID (if assigned)
  team_leader_name?: string;     // Team Leader name (if assigned)
  is_active: boolean;            // Active status
  headcount: number;             // Number of operators
  last_updated: string;          // Last update timestamp
}
```

## 10. Integration Patterns

### 10.1 Event Publication

After storing events in the event store, the DH Walk microservice publishes events to Azure Service Bus for consumption by other microservices:

```typescript
interface IntegrationEvent {
  id: string;                    // Event ID
  type: string;                  // Event type
  payload: any;                  // Event payload
  timestamp: string;             // When the event occurred
  source: string;                // Source system ("dh-walk")
  version: string;               // Schema version
}
```

### 10.2 External System Integration

The DH Walk microservice integrates with:

1. **ME Definition Service** - To retrieve project, family, value stream, and area data
2. **Identity Service** - To retrieve employee information
3. **Workstation Service** - To notify of Team Leader changes
4. **TKS Service** - To notify of team structure changes

## 11. UI Components

### 11.1 Project List View

The main UI view shows a table of projects with their associated families, value streams, areas, and assigned roles:

1. **Filtering Controls** - Filter by project, family, value stream, area
2. **Search Box** - Quick search across all fields
3. **Sort Controls** - Sort by various columns
4. **Assignment Status** - Visual indicators for filled/unfilled positions
5. **Assignment Buttons** - Buttons for assigning unassigned roles

### 11.2 Assignment Dialogs

When assigning roles, modal dialogs provide:

1. **Employee Selection** - Search and select from eligible employees
2. **Role Details** - Information about the role being assigned
3. **Validation Feedback** - Real-time validation and error messages
4. **Confirmation** - Confirmation of assignment action
