# Low-Level Design: Direct Dependents Microservice

## Table of Contents
1. [Introduction](#1-introduction)
2. [Architecture Overview](#2-architecture-overview)
3. [Domain Model & Entities](#3-domain-model--entities)
4. [Business Logic](#4-business-logic)
5. [API Endpoints](#5-api-endpoints)
6. [Database Schema](#6-database-schema)
7. [<PERSON><PERSON><PERSON>](#7-error-handling)


---

## 1. Introduction
This microservice fetches organization charts and unassigned workers from a Cosmos DB, applying assignment rules for roles.

## 2. Architecture Overview
- **.NET 9**, **C# 13.0**
- Read-only data fetching from **Cosmos DB**
- Services handle business logic:
  - `OrgChartAssignmentService`
  - `OrganizationChartService`
- Controllers expose REST endpoints for chart and worker assignments

## 3. Domain Model & Entities
- **UserRoleHierarchy**: Stores user info (`Id`, `LegacySiteId`, `Role`, `SubordinateCrew`…)
- **Subordinate**: Replaces `SubordinateCrew`, includes fields like `SubLegacySiteId`, `SubFullname`, etc.
- **AssignmentRule**:
  - `AssignmentType`: `toEmployee`, `toTeam`, `toWorkstation`
  - `ValidationRules`: `EmployeeToEmployee`, etc.
- **TeamleaderTeamCrew**: Holds team-related data
- **OrganizationChartAssignmentDto**: Represents mapped worker data

## 4. Business Logic
### OrgChartAssignmentService
- Retrieves assignment rules based on user role  
- Loads organizational charts for employees, teams, or indirect employees  
- Gets assigned/unassigned workers  

### OrganizationChartService
- Gets basic organization chart data for a user  
- Fetches subordinate counts  

## 5. API Endpoints

### `/api/organization-chart-Assignment`
- `GET /user/{legacySiteId}`
- `GET /workers/{legacySiteId}`
- `GET /unassigned-workers/{legacySiteId}`

### `/api/organization-chart`
- `GET /user/{legacySiteId}`

## 6. Database Schema
- **user_role_hierarchy**: Matches `UserRoleHierarchy` fields and subordinates
- **AssignmentRules**: Reflects the extended validation properties (`EmployeeToEmployee`, etc.)
- **TeamleaderTeamCrew**: Stores team and operator data

## 7. Error Handling
- `NOTFOUND` if user or data doesn’t exist
- Database errors logged as exceptions

