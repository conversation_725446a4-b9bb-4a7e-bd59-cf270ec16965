<!DOCTYPE html>
<html>
<head>
<title>crew-management_final.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="low-level-design-document-crew-management">Low-Level Design Document: Crew Management</h1>
<h2 id="document-information">Document Information</h2>
<p><strong>Version:</strong> 1.0.0<br>
<strong>Last Updated:</strong> 2023-03-26<br>
<strong>Status:</strong> Completed</p>
<h2 id="executive-summary">Executive Summary</h2>
<h3 id="key-features">Key Features</h3>
<ul>
<li>Microservices-based architecture with seven core services</li>
<li>Event-driven communication using Azure Service Bus</li>
<li>Comprehensive operator and team management</li>
<li>Skill tracking and certification management</li>
<li>Flexible workstation and assignment management</li>
<li>Department Head walk procedure support</li>
<li>Role-based access control and security</li>
</ul>
<h3 id="business-benefits">Business Benefits</h3>
<ul>
<li>Streamlined workforce management</li>
<li>Improved operational visibility</li>
<li>Enhanced skills and certification tracking</li>
<li>Efficient team and organizational structure management</li>
<li>Data-driven decision support for manufacturing operations</li>
<li>Seamless integration with other platform modules</li>
</ul>
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li><a href="#1-overview">Overview</a>
<ul>
<li><a href="#11-purpose-and-scope">Purpose and Scope</a></li>
<li><a href="#12-key-components">Key Components</a></li>
</ul>
</li>
<li><a href="#2-architecture-overview">Architecture Overview</a>
<ul>
<li><a href="#21-high-level-architecture">High-Level Architecture</a></li>
<li><a href="#22-technical-components">Technical Components</a></li>
</ul>
</li>
<li><a href="#3-technology-stack">Technology Stack</a></li>
<li><a href="#4-core-microservices">Core Microservices</a>
<ul>
<li><a href="#41-operator-management-service">Operator Management Service</a></li>
<li><a href="#42-employee-assignment-service">Employee Assignment Service</a></li>
<li><a href="#43-team-assignment-service">Team Assignment Service</a></li>
<li><a href="#44-dh-walk-service">DH Walk Service</a></li>
<li><a href="#45-workstation-service">Workstation Service</a></li>
<li><a href="#46-direct-dependents-service">Direct Dependents Service</a></li>
<li><a href="#47-operator-skills-service">Operator Skills Service</a></li>
</ul>
</li>
<li><a href="#5-integration-patterns">Integration Patterns</a>
<ul>
<li><a href="#51-event-driven-communication">Event-Driven Communication</a></li>
<li><a href="#52-synchronous-api-calls">Synchronous API Calls</a></li>
<li><a href="#53-database-integration">Database Integration</a></li>
</ul>
</li>
<li><a href="#6-security">Security</a></li>
<li><a href="#7-performance-considerations">Performance Considerations</a></li>
<li><a href="#8-deployment-strategy">Deployment Strategy</a></li>
</ol>
<h2 id="1-overview">1. Overview</h2>
<h3 id="11-purpose-and-scope">1.1 Purpose and Scope</h3>
<p>The Crew Management module provides a comprehensive solution for managing manufacturing workforce operations. It implements a microservices architecture following Domain-Driven Design principles to ensure flexibility, scalability, and maintainability. The module enables manufacturing facilities to efficiently manage their workforce, track skills and certifications, maintain organizational structures, and optimize workstation assignments.</p>
<h3 id="12-key-components">1.2 Key Components</h3>
<p>The system consists of seven primary microservices:</p>
<ol>
<li>Operator Management Service</li>
<li>Employee Assignment Service</li>
<li>Team Assignment Service</li>
<li>DH Walk Service</li>
<li>Workstation Service</li>
<li>Direct Dependents Service</li>
<li>Operator Skills Service</li>
</ol>
<p>Each service is supported by:</p>
<ul>
<li>Azure Cosmos DB for data storage</li>
<li>Azure Service Bus for event-driven communication</li>
<li>Azure API Management for API gateway</li>
<li>Azure AD B2C for authentication and authorization</li>
</ul>
<h2 id="2-architecture-overview">2. Architecture Overview</h2>
<h3 id="21-high-level-architecture">2.1 High-Level Architecture</h3>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    Client[Client Applications] --> APIM[Azure API Management]
    APIM --> OM[Operator Management Service]
    APIM --> EA[Employee Assignment Service]
    APIM --> TA[Team Assignment Service]
    APIM --> DH[DH Walk Service]
    APIM --> WS[Workstation Service]
    APIM --> DD[Direct Dependents Service]
    APIM --> OS[Operator Skills Service]
    
    subgraph Storage
        Cosmos[Cosmos DB]
    end
    
    %% Operator Management Service internal structure
    subgraph OM
        OM_W[Write Models] --> OM_CF[Change Feed]
        OM_CF --> OM_R[Read Models]
    end
    
    %% Employee Assignment Service internal structure
    subgraph EA
        EA_W[Write Models] --> EA_CF[Change Feed]
        EA_CF --> EA_R[Read Models]
    end
    
    %% Team Assignment Service internal structure
    subgraph TA
        TA_W[Write Models] --> TA_CF[Change Feed]
        TA_CF --> TA_R[Read Models]
    end
    
    %% DH Walk Service internal structure
    subgraph DH
        DH_W[Write Models] --> DH_CF[Change Feed]
        DH_CF --> DH_R[Read Models]
    end
    
    %% Workstation Service internal structure
    subgraph WS
        WS_W[Write Models] --> WS_CF[Change Feed]
        WS_CF --> WS_R[Read Models]
    end
    
    %% Direct Dependents Service internal structure
    subgraph DD
        DD_W[Write Models] --> DD_CF[Change Feed]
        DD_CF --> DD_R[Read Models]
    end
    
    %% Operator Skills Service internal structure
    subgraph OS
        OS_W[Write Models] --> OS_CF[Change Feed]
        OS_CF --> OS_R[Read Models]
    end
    
    OM --> Storage
    EA --> Storage
    TA --> Storage
    DH --> Storage
    WS --> Storage
    DD --> Storage
    OS --> Storage
</div></code></pre>
<h3 id="22-technical-components">2.2 Technical Components</h3>
<ol>
<li><strong>NestJS Microservices</strong>: Each service implemented as a NestJS application</li>
<li><strong>Cosmos DB</strong>: NoSQL database with flexible schema support for both write and read models</li>
<li><strong>Change Feed Processor</strong>: Propagates changes between write and read models</li>
<li><strong>Azure API Management</strong>: Unified entry point with authentication and routing</li>
<li><strong>Event Sourcing</strong>: Captures all state changes as events</li>
</ol>
<h3 id="23-data-duplication-and-cqrs-approach">2.3 Data Duplication and CQRS Approach</h3>
<p>The Connected Workers Platform implements a complete data isolation strategy between microservices:</p>
<ol>
<li>
<p><strong>No Direct Communication</strong>: Microservices do not communicate directly with each other, not even through asynchronous messaging systems like Service Bus.</p>
</li>
<li>
<p><strong>CQRS Implementation</strong>: Each microservice maintains:</p>
<ul>
<li><strong>Write Models</strong>: Optimized for recording state changes as events</li>
<li><strong>Read Models</strong>: Denormalized projections optimized for specific query patterns</li>
<li><strong>Change Feed Processor</strong>: Bridges the gap between write and read models</li>
</ul>
</li>
<li>
<p><strong>Dual-Purpose Data Structure</strong>:</p>
<ul>
<li>Each microservice contains dedicated containers for write operations (events/commands)</li>
<li>Each microservice also maintains optimized read models for queries</li>
<li>The separation allows optimization for both write and read operations</li>
</ul>
</li>
<li>
<p><strong>Event Sourcing</strong>: All state changes are recorded as immutable events in write models, providing:</p>
<ul>
<li>Complete audit history</li>
<li>Ability to reconstruct state at any point in time</li>
<li>Source of truth for the system</li>
</ul>
</li>
<li>
<p><strong>Data Duplication Strategy</strong>: When a microservice needs data owned by another microservice:</p>
<ul>
<li>Data is duplicated into read models within the consuming microservice</li>
<li>This eliminates service-to-service dependencies and improves resilience</li>
</ul>
</li>
<li>
<p><strong>Azure Change Feed Mechanism</strong>:</p>
<ul>
<li>Detects document changes in write model containers</li>
<li>Triggers functions to update corresponding read models</li>
<li>Enables eventual consistency between write and read models</li>
<li>Facilitates data duplication across microservice boundaries</li>
</ul>
</li>
<li>
<p><strong>Read Model Projections</strong>:</p>
<ul>
<li>Specifically designed for query efficiency</li>
<li>Denormalized to avoid complex joins</li>
<li>May include redundant data to optimize specific query patterns</li>
<li>Updated asynchronously via Change Feed</li>
</ul>
</li>
</ol>
<p>This architecture provides several advantages:</p>
<ul>
<li><strong>Resilience</strong>: Services can function independently even if other services are unavailable</li>
<li><strong>Performance</strong>: Data access is optimized for each service's specific needs</li>
<li><strong>Scalability</strong>: Services can scale independently based on their specific load patterns</li>
<li><strong>Evolution</strong>: Services can evolve independently with minimal impact on each other</li>
</ul>
<h2 id="3-technology-stack">3. Technology Stack</h2>
<table>
<thead>
<tr>
<th>Component</th>
<th>Technology</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>API Management</td>
<td>Azure API Management</td>
<td>Centralized API gateway for all microservices</td>
</tr>
<tr>
<td>Messaging</td>
<td>Azure Service Bus</td>
<td>Event-driven communication between microservices</td>
</tr>
<tr>
<td>NoSQL Database</td>
<td>Azure Cosmos DB</td>
<td>Document storage for flexible schemas and change feed</td>
</tr>
<tr>
<td>Relational Database</td>
<td>Azure SQL Database</td>
<td>Structured data storage with transaction support</td>
</tr>
<tr>
<td>Caching</td>
<td>Azure Redis Cache</td>
<td>High-performance caching for frequently accessed data</td>
</tr>
<tr>
<td>Authentication</td>
<td>Azure AD B2C</td>
<td>Identity management and authentication</td>
</tr>
<tr>
<td>Backend</td>
<td>NestJS</td>
<td>TypeScript-based Node.js framework for microservices</td>
</tr>
<tr>
<td>Containerization</td>
<td>Docker</td>
<td>Container platform for consistent deployment</td>
</tr>
<tr>
<td>Container Orchestration</td>
<td>Azure Kubernetes Service</td>
<td>Managed Kubernetes for container deployment</td>
</tr>
<tr>
<td>CI/CD</td>
<td>Azure DevOps</td>
<td>Continuous integration and deployment</td>
</tr>
<tr>
<td>Monitoring</td>
<td>Application Insights</td>
<td>Real-time performance and error monitoring</td>
</tr>
<tr>
<td>Logging</td>
<td>ELK Stack</td>
<td>Centralized logging and analysis</td>
</tr>
</tbody>
</table>
<h2 id="4-core-microservices">4. Core Microservices</h2>
<h3 id="41-operator-management-service">4.1 Operator Management Service</h3>
<p>This document describes the low-level technical design for the <strong>Operator Management</strong> microservice (MS1), focusing on an <strong>event-sourced</strong> approach. The key responsibilities are:</p>
<ol>
<li><strong>Create new operators</strong> (with optional Excel batch upload).</li>
<li><strong>Assign a site / workstation</strong> to each new operator.</li>
<li><strong>Determine the site responsible</strong> (n+1) automatically for new operators.</li>
<li><strong>Store command/write data in Cosmos DB</strong> (event-sourced).</li>
<li><strong>Provide read data</strong> via a <strong>separate database</strong> fed by the <strong>change feed</strong> (handled by data team).</li>
</ol>
<h4 id="domain-model--entity">Domain Model &amp; Entity</h4>
<p>An <strong>Operator</strong> within this bounded context has attributes:</p>
<ul>
<li><code>operatorId</code> (unique ID, generated on creation)</li>
<li><code>fullName</code></li>
<li><code>siteId</code> or <code>siteName</code> (assigned site for the operator)</li>
<li><code>siteResponsibleId</code> (the &quot;n+1&quot; for that site, determined automatically on creation)</li>
<li><strong>Other</strong>: <code>createdDate</code>, <code>source</code> (e.g., <code>CW</code> vs. <code>WORKDAY</code>), etc.</li>
</ul>
<h4 id="event-sourcing-model">Event Sourcing Model</h4>
<h5 id="write-model-in-cosmos-db">Write Model in Cosmos DB</h5>
<ul>
<li>A <strong>Cosmos DB collection</strong> (e.g., <code>operatorEvents</code>) holds <strong>events</strong> for each operator.</li>
<li><strong>Partition Key</strong>: Typically <code>operatorId</code>.</li>
<li><strong>Event Documents</strong>: Each event document has:
<ul>
<li><code>id</code>: a unique GUID for the event</li>
<li><code>operatorId</code>: the aggregate ID</li>
<li><code>type</code>: e.g., <code>OperatorCreated</code>, <code>OperatorUpdated</code></li>
<li><code>timestamp</code>: date/time the event was produced</li>
<li><code>payload</code>: JSON containing the event's data (e.g., operator name, site, etc.)</li>
</ul>
</li>
</ul>
<p>An example event document:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"be21e010-12ab-4ca0-8881-bc378429efc3"</span>,
  <span class="hljs-attr">"operatorId"</span>: <span class="hljs-string">"operator-123"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"OperatorCreated"</span>,
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-02-12T10:00:00Z"</span>,
  <span class="hljs-attr">"payload"</span>: {
    <span class="hljs-attr">"fullName"</span>: <span class="hljs-string">"John Doe"</span>,
    <span class="hljs-attr">"siteId"</span>: <span class="hljs-string">"siteA"</span>,
    <span class="hljs-attr">"siteResponsibleId"</span>: <span class="hljs-string">"manager-789"</span>,
    <span class="hljs-attr">"source"</span>: <span class="hljs-string">"CW"</span>
  }
}
</div></code></pre>
<h5 id="read-model-in-a-separate-db">Read Model in a Separate DB</h5>
<ul>
<li>Data Team sets up a materialized view in, say, SQL or another NoSQL.</li>
<li>They only store the latest operator info (flattened).</li>
</ul>
<h5 id="change-feed-for-projections">Change Feed for Projections</h5>
<ul>
<li>Cosmos DB Change Feed can detect new event documents.</li>
<li>The data team (or an internal process) subscribes to the feed:
<ul>
<li>Reads each new event.</li>
<li>Applies the event logic to the &quot;read model&quot; in the separate DB.</li>
<li>Upserts or modifies the row representing the latest state.</li>
</ul>
</li>
</ul>
<h4 id="commands-events-and-workflows">Commands, Events, and Workflows</h4>
<h5 id="createoperator-command">CreateOperator Command</h5>
<p>Purpose: User or system requests to create a new operator.</p>
<p>Input: <code>fullName</code>, <code>siteId</code> (optional if the user picks it), or it's derived from the Excel data.</p>
<p>Steps:</p>
<ol>
<li>Validate that <code>fullName</code> is present.</li>
<li>Lookup <code>siteResponsibleId</code> for the given <code>siteId</code>.</li>
<li>Generate <code>operatorId</code> (GUID).</li>
<li>Produce <code>OperatorCreated</code> event in Cosmos DB.</li>
</ol>
<p>Resulting Event: <code>OperatorCreated</code></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"operatorId"</span>: <span class="hljs-string">"operator-123"</span>,
  <span class="hljs-attr">"fullName"</span>: <span class="hljs-string">"John Doe"</span>,
  <span class="hljs-attr">"siteId"</span>: <span class="hljs-string">"siteA"</span>,
  <span class="hljs-attr">"siteResponsibleId"</span>: <span class="hljs-string">"manager-789"</span>,
  <span class="hljs-attr">"source"</span>: <span class="hljs-string">"CW"</span>,
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"..."</span>
}
</div></code></pre>
<h5 id="updateoperator-command">UpdateOperator Command</h5>
<p>Purpose: If an operator's name, site, or other metadata changes.</p>
<p>Input: <code>operatorId</code>, updated fields.</p>
<p>Steps:</p>
<ol>
<li>Check if <code>operatorId</code> exists (by replaying events or using a quick query).</li>
<li>Validate fields (e.g., new site requires new siteResponsible lookup?).</li>
<li>Produce <code>OperatorUpdated</code> event with the delta.</li>
</ol>
<p>Resulting Event: <code>OperatorUpdated</code></p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"operatorId"</span>: <span class="hljs-string">"operator-123"</span>,
  <span class="hljs-attr">"changes"</span>: {
    <span class="hljs-attr">"fullName"</span>: <span class="hljs-string">"Johnathan Doe"</span>,
    <span class="hljs-attr">"siteId"</span>: <span class="hljs-string">"siteB"</span>,
    <span class="hljs-attr">"siteResponsibleId"</span>: <span class="hljs-string">"manager-999"</span>
  },
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"..."</span>
}
</div></code></pre>
<h4 id="excel-upload-flow">Excel Upload Flow</h4>
<ol>
<li>User uploads an Excel file with one or more operators (name, site, etc.).</li>
<li>Parsing: The service reads the rows (e.g., using a Nest.js + xlsx library).</li>
<li>For each row:
<ul>
<li>Construct a CreateOperator command object.</li>
<li>Validate data.</li>
<li>Execute command → produces an OperatorCreated event in Cosmos DB.</li>
</ul>
</li>
<li>Async or batch process: The read model is eventually updated once the events appear on the change feed.</li>
</ol>
<h4 id="api-endpoints-write-side">API Endpoints (Write-Side)</h4>
<p>Below are potential Nest.js endpoints for the command (write) side.</p>
<h5 id="post-operators">POST /operators</h5>
<p>Body:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"fullName"</span>: <span class="hljs-string">"John Doe"</span>,
  <span class="hljs-attr">"siteId"</span>: <span class="hljs-string">"siteA"</span>
}
</div></code></pre>
<p>Behavior:</p>
<ul>
<li>Executes CreateOperator command.</li>
<li>Returns operatorId or success status.</li>
</ul>
<h5 id="put-operatorsoperatorid">PUT /operators/:operatorId</h5>
<p>Body:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"fullName"</span>: <span class="hljs-string">"Johnathan Doe"</span>,
  <span class="hljs-attr">"siteId"</span>: <span class="hljs-string">"siteB"</span>
}
</div></code></pre>
<p>Behavior:</p>
<ul>
<li>Executes UpdateOperator command.</li>
<li>Produces an OperatorUpdated event.</li>
</ul>
<h5 id="post-operatorsupload-excel-batch-creation">POST /operators/upload-excel (batch creation)</h5>
<ul>
<li>Consumes: .xlsx file.</li>
<li>Behavior:
<ul>
<li>Parses each row → triggers CreateOperator for each.</li>
<li>Returns a summary of created IDs or error rows.</li>
</ul>
</li>
</ul>
<h4 id="roles--permissions">Roles &amp; Permissions</h4>
<h5 id="role-definitions">Role Definitions</h5>
<ol>
<li>
<p><strong>TKS</strong></p>
<ul>
<li>Can create and update operator basic information</li>
<li>Can view operator profiles across all sites</li>
<li>Cannot assign workstations</li>
<li>Can upload Excel files for batch operator creation</li>
<li>Limited access to skill information</li>
</ul>
</li>
<li>
<p><strong>Team Leader</strong></p>
<ul>
<li>Can view operators within their team/site</li>
<li>Can update workstation</li>
<li>Can view operator skills and workstation requirements</li>
<li>Cannot create or modify operator basic information</li>
</ul>
</li>
</ol>
<h5 id="permission-matrix">Permission Matrix</h5>
<table>
<thead>
<tr>
<th>Operation</th>
<th>TKS</th>
<th>Team Leader</th>
</tr>
</thead>
<tbody>
<tr>
<td>Create Operator</td>
<td>Yes</td>
<td>No</td>
</tr>
<tr>
<td>Update Operator Info</td>
<td>Yes</td>
<td>No</td>
</tr>
<tr>
<td>View Operator Profile</td>
<td>Yes</td>
<td>team Only</td>
</tr>
<tr>
<td>Assign Workstation</td>
<td>No</td>
<td>Yes</td>
</tr>
<tr>
<td>Upload Excel Batch</td>
<td>Yes</td>
<td>No</td>
</tr>
<tr>
<td>View Skills</td>
<td>No</td>
<td>team only</td>
</tr>
<tr>
<td>Configure Skills</td>
<td>No</td>
<td>No</td>
</tr>
<tr>
<td>Audit Events</td>
<td>No</td>
<td>No</td>
</tr>
</tbody>
</table>
<h4 id="use-case-scenarios">Use Case Scenarios</h4>
<h5 id="new-operator-onboarding">New Operator Onboarding</h5>
<ol>
<li>
<p><strong>HR Initiates Onboarding</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant TKS
    participant API
    participant CommandHandler
    participant CosmosDB
    participant EventBus
    
    TKS->>API: CreateOperator Command
    API->>CommandHandler: Validate & Process
    CommandHandler->>CosmosDB: Store OperatorCreated Event
    CosmosDB->>EventBus: Publish Event
    EventBus->>ReadDB: Update Projections
    API->>TKS: Return Success
</div></code></pre>
</li>
<li>
<p><strong>Team Leader Assigns Workstation</strong></p>
<ul>
<li>Checks operator skills</li>
<li>Validates against workstation requirements</li>
<li>Creates WorkstationAssigned event</li>
<li>Notifies relevant systems</li>
</ul>
</li>
</ol>
<h5 id="batch-upload-scenario">Batch Upload Scenario</h5>
<ol>
<li>
<p><strong>Excel Upload Process</strong></p>
<ul>
<li>HR uploads Excel file</li>
<li>System validates format</li>
<li>Processes rows in batches</li>
<li>Reports success/failures</li>
</ul>
</li>
<li>
<p><strong>Error Handling</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">flowchart TD
    A[Upload Excel] --> B{Validate Format}
    B -->|Invalid| C[Return Error]
    B -->|Valid| D[Process Batch]
    D --> E{Row Validation}
    E -->|Failed| F[Add to Error Report]
    E -->|Success| G[Create Operator]
    G --> H[Next Row]
</div></code></pre>
</li>
</ol>
<h3 id="42-employee-assignment-service">4.2 Employee Assignment Service</h3>
<p>(This section would contain content from the LLD-employee-assignment-lld.md, but since the file content wasn't provided in the context, I'm leaving this as a placeholder. The structure would match the format of the other sections, preserving all original details from the LLD file.)</p>
<h3 id="43-team-assignment-service">4.3 Team Assignment Service</h3>
<h4 id="1-overview">1. Overview</h4>
<p>The <strong>Team Assignment Service</strong> is responsible for managing operator assignments to teams within the Connected Workers Platform. This service implements event sourcing and CQRS patterns to handle the assignment and unassignment of operators to teams.</p>
<h5 id="key-capabilities">Key Capabilities</h5>
<ol>
<li>
<p><strong>Team Assignment Management</strong></p>
<ul>
<li>Assign operators to teams</li>
<li>Unassign operators from teams</li>
<li>Bulk assignment capabilities</li>
<li>Role-based assignment validation</li>
</ul>
</li>
<li>
<p><strong>Assignment Validation</strong></p>
<ul>
<li>Department-based validation</li>
<li>Workday status verification</li>
<li>Team leader authority validation</li>
<li>Site-based restrictions</li>
</ul>
</li>
<li>
<p><strong>Query Capabilities</strong></p>
<ul>
<li>View team assignments</li>
<li>Search operators by team</li>
<li>View unassigned operators</li>
<li>Filter by department and site</li>
</ul>
</li>
</ol>
<h5 id="technical-architecture">Technical Architecture</h5>
<p>The service follows these architectural principles:</p>
<ol>
<li>
<p><strong>Event Sourcing</strong></p>
<ul>
<li>Assignment events stored in Cosmos DB</li>
<li>Complete audit trail of assignments</li>
<li>Event-based state reconstruction</li>
<li>Historical assignment view</li>
</ul>
</li>
<li>
<p><strong>CQRS Pattern</strong></p>
<ul>
<li>Separate command and query responsibilities</li>
<li>Optimized read models for queries</li>
<li>Eventual consistency</li>
<li>High performance for reads</li>
</ul>
</li>
</ol>
<h5 id="business-rules">Business Rules</h5>
<ol>
<li>
<p><strong>Assignment Rules</strong></p>
<ul>
<li>Only team leaders can assign operators to their teams</li>
<li>Operators must belong to the same department as the team</li>
<li>Operators must be in active workday status</li>
<li>Assignments must be within the same site</li>
</ul>
</li>
<li>
<p><strong>Validation Rules</strong></p>
<ul>
<li>Team leader authority validation</li>
<li>Department membership validation</li>
<li>Workday status verification</li>
<li>Site boundary validation</li>
</ul>
</li>
</ol>
<h4 id="2-domain-model--events">2. Domain Model &amp; Events</h4>
<h5 id="21-event-types">2.1 Event Types</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">enum</span> TeamAssignmentEventType {
  OPERATOR_ASSIGNED_TO_TEAM = <span class="hljs-string">"OPERATOR_ASSIGNED_TO_TEAM"</span>,
  OPERATOR_UNASSIGNED_FROM_TEAM = <span class="hljs-string">"OPERATOR_UNASSIGNED_FROM_TEAM"</span>
}

<span class="hljs-keyword">interface</span> BaseTeamEvent {
  id: <span class="hljs-built_in">string</span>;
  <span class="hljs-keyword">type</span>: TeamAssignmentEventType;
  timestamp: <span class="hljs-built_in">Date</span>;
  site: <span class="hljs-built_in">string</span>;
  metadata: {
    correlationId: <span class="hljs-built_in">string</span>;
    userId: <span class="hljs-built_in">string</span>;
    version: <span class="hljs-built_in">string</span>;
  };
}

<span class="hljs-keyword">interface</span> OperatorTeamAssignmentEvent <span class="hljs-keyword">extends</span> BaseTeamEvent {
  <span class="hljs-keyword">type</span>: TeamAssignmentEventType;
  payload: {
    assigner: {
      legacySiteId: <span class="hljs-built_in">string</span>;
      role: <span class="hljs-built_in">string</span>;
      firstName: <span class="hljs-built_in">string</span>;
      lastName: <span class="hljs-built_in">string</span>;
      department: <span class="hljs-built_in">string</span>;
    };
    team: {
      teamId: <span class="hljs-built_in">string</span>;
      teamName: <span class="hljs-built_in">string</span>;
      legacyTeamLeaderId: <span class="hljs-built_in">string</span>;
    };
    assignee: {
      legacySiteId: <span class="hljs-built_in">string</span>;
      role: <span class="hljs-built_in">string</span>;
      firstName: <span class="hljs-built_in">string</span>;
      lastName: <span class="hljs-built_in">string</span>;
      department: <span class="hljs-built_in">string</span>;
    };
  };
}
</div></code></pre>
<h5 id="22-event-examples">2.2 Event Examples</h5>
<pre class="hljs"><code><div><span class="hljs-comment">// Assignment Event Example</span>
{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"evt-123"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"OPERATOR_ASSIGNED_TO_TEAM"</span>,
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-03-15T10:30:00Z"</span>,
  <span class="hljs-attr">"site"</span>: <span class="hljs-string">"Site A"</span>,
  <span class="hljs-attr">"metadata"</span>: {
    <span class="hljs-attr">"correlationId"</span>: <span class="hljs-string">"corr-456"</span>,
    <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"user-789"</span>,
    <span class="hljs-attr">"version"</span>: <span class="hljs-string">"1.0"</span>
  },
  <span class="hljs-attr">"payload"</span>: {
    <span class="hljs-attr">"assigner"</span>: {
      <span class="hljs-attr">"legacySiteId"</span>: <span class="hljs-string">"TL_123"</span>,
      <span class="hljs-attr">"role"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
      <span class="hljs-attr">"firstName"</span>: <span class="hljs-string">"John"</span>,
      <span class="hljs-attr">"lastName"</span>: <span class="hljs-string">"Doe"</span>,
      <span class="hljs-attr">"department"</span>: <span class="hljs-string">"Assembly"</span>
    },
    <span class="hljs-attr">"team"</span>: {
      <span class="hljs-attr">"teamId"</span>: <span class="hljs-string">"team-456"</span>,
      <span class="hljs-attr">"teamName"</span>: <span class="hljs-string">"Assembly Team A"</span>,
      <span class="hljs-attr">"legacyTeamLeaderId"</span>: <span class="hljs-string">"TL_123"</span>
    },
    <span class="hljs-attr">"assignee"</span>: {
      <span class="hljs-attr">"legacySiteId"</span>: <span class="hljs-string">"OP_789"</span>,
      <span class="hljs-attr">"role"</span>: <span class="hljs-string">"OPERATOR"</span>,
      <span class="hljs-attr">"firstName"</span>: <span class="hljs-string">"Jane"</span>,
      <span class="hljs-attr">"lastName"</span>: <span class="hljs-string">"Smith"</span>,
      <span class="hljs-attr">"department"</span>: <span class="hljs-string">"Assembly"</span>
    }
  }
}

<span class="hljs-comment">// Unassignment Event Example</span>
{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"evt-124"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"OPERATOR_UNASSIGNED_FROM_TEAM"</span>,
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-03-15T14:30:00Z"</span>,
  <span class="hljs-attr">"site"</span>: <span class="hljs-string">"Site A"</span>,
  <span class="hljs-attr">"metadata"</span>: {
    <span class="hljs-attr">"correlationId"</span>: <span class="hljs-string">"corr-457"</span>,
    <span class="hljs-attr">"userId"</span>: <span class="hljs-string">"user-789"</span>,
    <span class="hljs-attr">"version"</span>: <span class="hljs-string">"1.0"</span>
  },
  <span class="hljs-attr">"payload"</span>: {
    <span class="hljs-attr">"assigner"</span>: {
      <span class="hljs-attr">"legacySiteId"</span>: <span class="hljs-string">"TL_123"</span>,
      <span class="hljs-attr">"role"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
      <span class="hljs-attr">"firstName"</span>: <span class="hljs-string">"John"</span>,
      <span class="hljs-attr">"lastName"</span>: <span class="hljs-string">"Doe"</span>,
      <span class="hljs-attr">"department"</span>: <span class="hljs-string">"Assembly"</span>
    },
    <span class="hljs-attr">"team"</span>: {
      <span class="hljs-attr">"teamId"</span>: <span class="hljs-string">"team-456"</span>,
      <span class="hljs-attr">"teamName"</span>: <span class="hljs-string">"Assembly Team A"</span>,
      <span class="hljs-attr">"legacyTeamLeaderId"</span>: <span class="hljs-string">"TL_123"</span>
    },
    <span class="hljs-attr">"assignee"</span>: {
      <span class="hljs-attr">"legacySiteId"</span>: <span class="hljs-string">"OP_789"</span>,
      <span class="hljs-attr">"role"</span>: <span class="hljs-string">"OPERATOR"</span>,
      <span class="hljs-attr">"firstName"</span>: <span class="hljs-string">"Jane"</span>,
      <span class="hljs-attr">"lastName"</span>: <span class="hljs-string">"Smith"</span>,
      <span class="hljs-attr">"department"</span>: <span class="hljs-string">"Assembly"</span>
    }
  }
}
</div></code></pre>
<h4 id="3-assignment-rules">3. Assignment Rules</h4>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> AssignmentRule {
  id: <span class="hljs-built_in">string</span>;
  assignerRole: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be TEAM_LEADER</span>
  assignToRole: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be TEAM</span>
  assigneeRole: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be OPERATOR</span>
  assignmentType: {
      assignToEmployee: <span class="hljs-built_in">boolean</span>,
      assignToTeam: <span class="hljs-built_in">boolean</span>,
      assignToWorkstation: <span class="hljs-built_in">boolean</span>
    },
  validationRules: {
   scope: [department|site|subordinate]
    requiresWorkday: <span class="hljs-built_in">boolean</span>;
  };
  isActive: <span class="hljs-built_in">boolean</span>;
  country: <span class="hljs-built_in">string</span>;
}
</div></code></pre>
<h5 id="31-example-assignment-rule">3.1 Example Assignment Rule</h5>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"rule-001"</span>,
  <span class="hljs-attr">"assignerRole"</span>: <span class="hljs-string">"TEAM_LEADER"</span>,
  <span class="hljs-attr">"assignToRole"</span>: <span class="hljs-string">"TEAM"</span>,
  <span class="hljs-attr">"assigneeRole"</span>: <span class="hljs-string">"OPERATOR"</span>,
  <span class="hljs-attr">"assignmentType"</span>: {
      <span class="hljs-attr">"assignToEmployee"</span>: <span class="hljs-literal">true</span>,
      <span class="hljs-attr">"assignToTeam"</span>: <span class="hljs-literal">false</span>,
      <span class="hljs-attr">"assignToWorkstation"</span>: <span class="hljs-literal">false</span>
    },
  <span class="hljs-attr">"validationRules"</span>: {
    <span class="hljs-attr">"requiresSameDepartment"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requiresSameSite"</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">"requiresWorkday"</span>: <span class="hljs-literal">true</span>
  },
  <span class="hljs-attr">"isActive"</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">"country"</span>: <span class="hljs-string">"MOROCCO"</span>
}
</div></code></pre>
<h4 id="4-commands">4. Commands</h4>
<h5 id="41-assigntoteam-command">4.1 AssignToTeam Command</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> AssignToTeamCommand {
  site: <span class="hljs-built_in">string</span>;
  assigner: { 
    id: <span class="hljs-built_in">string</span>; 
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be TEAM_LEADER</span>
  };
  team: { 
    id: <span class="hljs-built_in">string</span>; 
    name: <span class="hljs-built_in">string</span>; 
  };
  assignees: <span class="hljs-built_in">Array</span>&lt;{ 
    id: <span class="hljs-built_in">string</span>; 
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be OPERATOR</span>
  }&gt;;
}
</div></code></pre>
<h5 id="42-unassignfromteam-command">4.2 UnassignFromTeam Command</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> UnassignFromTeamCommand {
  site: <span class="hljs-built_in">string</span>;
  assigner: { 
    id: <span class="hljs-built_in">string</span>; 
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be TEAM_LEADER</span>
  };
  team: { 
    id: <span class="hljs-built_in">string</span>; 
    name: <span class="hljs-built_in">string</span>; 
  };
  assignee: { 
    id: <span class="hljs-built_in">string</span>; 
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be OPERATOR</span>
  };
}
</div></code></pre>
<h4 id="5-read-models">5. Read Models</h4>
<h5 id="51-team-assignment-read-model">5.1 Team Assignment Read Model</h5>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"teamleader_legacy_site"</span>: <span class="hljs-string">"E12345_MAR Moroocco 3"</span>,
  <span class="hljs-attr">"teamleader_name"</span>: <span class="hljs-string">"cxxxxx"</span>,
  <span class="hljs-attr">"teams"</span>: <span class="hljs-string">"team 1"</span>,
  <span class="hljs-attr">"first_name"</span>: <span class="hljs-string">"xxx 1"</span>,
  <span class="hljs-attr">"last_name"</span>: <span class="hljs-string">"xxx 1"</span>,
  <span class="hljs-attr">"department"</span>: <span class="hljs-string">"E12345_MAR Moroocco 3"</span>,
  <span class="hljs-attr">"operator_legacy_site"</span>: <span class="hljs-string">"cxxxxx"</span>,
  <span class="hljs-attr">"role"</span>: <span class="hljs-string">"team 1"</span>,
  <span class="hljs-attr">"role_status"</span>: <span class="hljs-string">"xxx 1"</span>,
  <span class="hljs-attr">"site"</span>: <span class="hljs-string">"ssssss"</span>
}
</div></code></pre>
<h4 id="6-api-endpoints">6. API Endpoints</h4>
<pre class="hljs"><code><div><span class="hljs-comment">// Assignment endpoint</span>
POST /api/v1/assignments/assign-to-team
Request: {
  site: <span class="hljs-built_in">string</span>;
  assigner: {
    id: <span class="hljs-built_in">string</span>;
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be TEAM_LEADER</span>
  };
  team: {
    id: <span class="hljs-built_in">string</span>;
    name: <span class="hljs-built_in">string</span>;
  };
  assignees: <span class="hljs-built_in">Array</span>&lt;{
    id: <span class="hljs-built_in">string</span>;
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be OPERATOR</span>
  }&gt;;
}
Response: {
  success: <span class="hljs-built_in">boolean</span>;
  errors: <span class="hljs-built_in">string</span>[];

}

<span class="hljs-comment">// Unassignment endpoint</span>
POST /api/v1/assignments/unassign-<span class="hljs-keyword">from</span>-team
Request: {
  site: <span class="hljs-built_in">string</span>;
  assigner: {
    id: <span class="hljs-built_in">string</span>;
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be TEAM_LEADER</span>
  };
  team: {
    id: <span class="hljs-built_in">string</span>;
    name: <span class="hljs-built_in">string</span>;
  };
  assignee: {
    id: <span class="hljs-built_in">string</span>;
    role: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Must be OPERATOR</span>
  };

}
Response: {
  success: <span class="hljs-built_in">boolean</span>;
  errors: <span class="hljs-built_in">string</span>[];

}

<span class="hljs-comment">// Query endpoints</span>
GET /api/v1/teams/{teamId}/operators
GET /api/v1/teams/unassigned-operators?department={dept}&amp;site={site}
</div></code></pre>
<h4 id="7-assignment-flow">7. Assignment Flow</h4>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant TL as Team Leader
    participant API as API Management
    participant AS as Assignment Service
    participant RDB as Read DB
    participant ES as Event Store

    TL->>API: POST /assignments/assign-to-team
    API->>AS: Process Assignment Request
    
    AS->>RDB: Validate Team Leader
    RDB-->>AS: Team Leader Valid
    
    AS->>RDB: Validate Team
    RDB-->>AS: Team Valid
    
    AS->>RDB: Validate Operators
    RDB-->>AS: Operators Valid
    
    AS->>AS: Validate Business Rules
    Note over AS: - Same Department<br>- Same Site<br>- Active Workday
    
    alt Validation Failed
        AS-->>API: Return Error
        API-->>TL: 400 Bad Request
    else Validation Passed
        AS->>ES: Store Assignment Event
        ES-->>AS: Event Stored
        AS-->>API: Success Response
        API-->>TL: 200 OK
    end
</div></code></pre>
<h3 id="44-dh-walk-service">4.4 DH Walk Service</h3>
<h4 id="1-overview">1. Overview</h4>
<p>The <strong>DH Walk Microservice</strong> is a critical component within the Connected Workers Platform that manages the organizational hierarchy and team structure of the manufacturing environment. This service enables Department Clerks to:</p>
<ol>
<li>Create, update, and delete teams across the manufacturing organization</li>
<li>Assign and reassign Indirect Headcount (IH) roles (Coordinators, Shift Leaders, and Team Leaders) to teams</li>
<li>Maintain the hierarchical relationship between projects, families, value streams, areas, and teams</li>
</ol>
<p>The DH Walk Microservice implements an event-sourcing architecture to provide full audit capabilities and history tracking of all organizational changes. By capturing all state changes as events, the system maintains a complete record of team structures and role assignments over time, enabling historical analysis and state reconstruction.</p>
<p>The service leverages the Command Query Responsibility Segregation (CQRS) pattern to separate write operations (team creation, role assignments) from read operations (viewing team structures and assignments). This approach enables optimized query performance while maintaining data integrity for state-changing operations.</p>
<h4 id="2-business-requirements">2. Business Requirements</h4>
<p>The DH Walk Microservice addresses the following key business requirements:</p>
<ol>
<li>
<p><strong>Organizational Structure Management</strong></p>
<ul>
<li>Define and maintain manufacturing hierarchies from project down to team level</li>
<li>Track relationships between projects, families, value streams, areas, and teams</li>
<li>Support dynamic reorganization as manufacturing needs evolve</li>
</ul>
</li>
<li>
<p><strong>Role Assignment Management</strong></p>
<ul>
<li>Assign appropriate Coordinators, Shift Leaders, and Team Leaders to teams</li>
<li>Support reassignment as personnel changes occur</li>
<li>Maintain historical record of role assignments and changes</li>
</ul>
</li>
<li>
<p><strong>Department Clerk Capabilities</strong></p>
<ul>
<li>Enable Department Clerks to efficiently create and manage teams</li>
<li>Provide Department Clerks with assignment capabilities for all IH roles</li>
<li>Support bulk operations for efficient management of large organizations</li>
</ul>
</li>
<li>
<p><strong>Visibility and Filtering</strong></p>
<ul>
<li>Support filtering by project, family, value stream, and area</li>
<li>Provide search capabilities to quickly find specific teams</li>
<li>Enable sorting by various attributes (e.g., date, name)</li>
</ul>
</li>
<li>
<p><strong>Integration with Connected Workers Platform</strong></p>
<ul>
<li>Share team and role assignment data with other microservices</li>
<li>Consume ME definition data (projects, families, value streams, areas)</li>
<li>Support downstream processes that depend on team structures</li>
</ul>
</li>
</ol>
<h4 id="3-core-domain-models">3. Core Domain Models</h4>
<h5 id="31-zoning-document">3.1 Zoning Document</h5>
<p>The Zoning Document represents the assignment of IH roles to a specific team within the manufacturing structure hierarchy.</p>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> ZoningDocument {
  id: <span class="hljs-built_in">string</span>;                      <span class="hljs-comment">// Unique identifier</span>
  project: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Project name</span>
  family: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Family name</span>
  value_stream: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Value stream</span>
  area: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Area</span>
  coordinator_legacy_site_id: <span class="hljs-built_in">string</span>; <span class="hljs-comment">// Unique ID of the coordinator</span>
  coordinator_fullname: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Coordinator's full nam            </span>
  shift_leader_legacy_site_id: <span class="hljs-built_in">string</span>;  
  shift_leader_fullname: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Shift leader's full name                     // </span>
  team_leader_legacy_site_id: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// Unique ID of the team leader</span>
  team_leader_fullname: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Team leader's full name  </span>
  team_name: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Name of the team</span>
  version: <span class="hljs-built_in">number</span>;                 <span class="hljs-comment">// Document version for concurrency control</span>
  created_at: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Creation timestamp (ISO format)</span>
  updated_at: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Last update timestamp (ISO format)</span>
  created_by: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// User ID of creator</span>
  updated_by: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// User ID of last updater</span>
}
</div></code></pre>
<h5 id="32-team-document">3.2 Team Document</h5>
<p>The Team Document represents a team entity within the manufacturing structure hierarchy.</p>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> TeamDocument {
  id: <span class="hljs-built_in">string</span>;                      <span class="hljs-comment">// Unique identifier</span>
  team_name: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Name of the team</span>
  team_leader_fullname <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Can be null if not assigned</span>
  is_active: <span class="hljs-built_in">boolean</span>;              <span class="hljs-comment">// Team active status</span>
  headcount: <span class="hljs-built_in">number</span>;               <span class="hljs-comment">// Number of operators in the team</span>
  version: <span class="hljs-built_in">number</span>;                 <span class="hljs-comment">// Document version for concurrency control</span>
  created_at: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Creation timestamp (ISO format)</span>
  updated_at: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Last update timestamp (ISO format)</span>
  created_by: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// User ID of creator</span>
  updated_by: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// User ID of last updater</span>
}
</div></code></pre>
<h4 id="4-event-model">4. Event Model</h4>
<h5 id="41-event-types">4.1 Event Types</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">enum</span> DHWalkEventType {
  CREATE_TEAM = <span class="hljs-string">"CreateTeam"</span>,
  UPDATE_TEAM = <span class="hljs-string">"UpdateTeam"</span>,
  DELETE_TEAM = <span class="hljs-string">"DeleteTeam"</span>,
  ASSIGN_TO_TEAM = <span class="hljs-string">"AssignedToTeam"</span>,
  UNASSIGN_FROM_TEAM = <span class="hljs-string">"UnassignedFromTeam"</span>
}

<span class="hljs-keyword">enum</span> IHRole {
  COORDINATOR = <span class="hljs-string">"COORDINATOR"</span>,
  SHIFT_LEADER = <span class="hljs-string">"SHIFT_LEADER"</span>,
  TEAM_LEADER = <span class="hljs-string">"TEAM_LEADER"</span>
}
</div></code></pre>
<h5 id="42-event-structure">4.2 Event Structure</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> BaseEvent {
  id: <span class="hljs-built_in">string</span>;                      <span class="hljs-comment">// Unique event identifier</span>
  createdBy: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// User ID who initiated the event</span>
  eventType: DHWalkEventType;      <span class="hljs-comment">// Type of event</span>
  timestamp: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Timestamp when event occurred (ISO format)</span>
  version: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Event schema version</span>
  correlationId?: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Optional correlation ID for tracing</span>
}
</div></code></pre>
<h5 id="43-event-examples">4.3 Event Examples</h5>
<h6 id="create-team-event">Create Team Event</h6>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> CreateTeamEvent <span class="hljs-keyword">extends</span> BaseEvent {
  eventType: DHWalkEventType.CREATE_TEAM;
  payload: {
    team_name: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Name of the new team</span>
    TL_legacy_site_id: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Team Leader's legacy site ID</span>
    TL_fullname: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Team Leader's full name</span>
  };
}
</div></code></pre>
<p>Example:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"be21e010-12ab-4ca0-8881-bc378429efc3"</span>,
  <span class="hljs-attr">"createdBy"</span>: <span class="hljs-string">"user-123"</span>,
  <span class="hljs-attr">"eventType"</span>: <span class="hljs-string">"CreateTeam"</span>, 
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-02-12T10:00:00Z"</span>,
  <span class="hljs-attr">"version"</span>: <span class="hljs-string">"1.0"</span>,
  <span class="hljs-attr">"payload"</span>: {
    <span class="hljs-attr">"TL_legacy_site_id"</span>: <span class="hljs-string">"TL_12345"</span>,
    <span class="hljs-attr">"TL_fullname"</span>: <span class="hljs-string">"John Smith"</span>,
    <span class="hljs-attr">"team_name"</span>: <span class="hljs-string">"Assembly Team 1"</span>
  }
}
</div></code></pre>
<h6 id="assign-to-team-event">Assign To Team Event</h6>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> AssignToTeamEvent <span class="hljs-keyword">extends</span> BaseEvent {
  eventType: DHWalkEventType.ASSIGN_TO_TEAM;
  payload: {
    project: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Project name</span>
    family: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Family name</span>
    value_stream: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Value stream</span>
    area: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Area</span>
    Coor_legacy_site_id: <span class="hljs-built_in">string</span>;   <span class="hljs-comment">// Coordinator's legacy site ID</span>
    Coor_fullname: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// Coordinator's full name</span>
    SL_legacy_site_id: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Shift Leader's legacy site ID</span>
    SL_fullname: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Shift Leader's full name</span>
    TL_legacy_site_id: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Team Leader's legacy site ID</span>
    TL_fullname: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Team Leader's full name</span>
    team_name: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Team name</span>
  };
}
</div></code></pre>
<p>Example:</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"be21e010-12ab-4ca0-8881-bc378429efc3"</span>,
  <span class="hljs-attr">"createdBy"</span>: <span class="hljs-string">"user-123"</span>,
  <span class="hljs-attr">"eventType"</span>: <span class="hljs-string">"AssignedToTeam"</span>,
  <span class="hljs-attr">"timestamp"</span>: <span class="hljs-string">"2025-02-12T10:00:00Z"</span>,
  <span class="hljs-attr">"version"</span>: <span class="hljs-string">"1.0"</span>,
  <span class="hljs-attr">"payload"</span>: {
    <span class="hljs-attr">"project"</span>: <span class="hljs-string">"V426A"</span>,
    <span class="hljs-attr">"family"</span>: <span class="hljs-string">"V426-L&amp;R-RearDoor"</span>,
    <span class="hljs-attr">"value_stream"</span>: <span class="hljs-string">"Line 1"</span>,
    <span class="hljs-attr">"area"</span>: <span class="hljs-string">"SUB"</span>,
    <span class="hljs-attr">"Coor_legacy_site_id"</span>: <span class="hljs-string">"CO_12345"</span>,
    <span class="hljs-attr">"Coor_fullname"</span>: <span class="hljs-string">"Said Ousrhir"</span>,
    <span class="hljs-attr">"SL_legacy_site_id"</span>: <span class="hljs-string">"SL_67890"</span>,
    <span class="hljs-attr">"SL_fullname"</span>: <span class="hljs-string">"Bendissa Hibbou"</span>,
    <span class="hljs-attr">"TL_legacy_site_id"</span>: <span class="hljs-string">"TL_45678"</span>,
    <span class="hljs-attr">"TL_fullname"</span>: <span class="hljs-string">"WARDA HARDALA"</span>,
    <span class="hljs-attr">"team_name"</span>: <span class="hljs-string">"E209C"</span>
  }
}
</div></code></pre>
<h4 id="5-commands">5. Commands</h4>
<h5 id="51-command-types">5.1 Command Types</h5>
<pre class="hljs"><code><div><span class="hljs-comment">// Create Team Command</span>
<span class="hljs-keyword">interface</span> CreateTeamCommand {
  team_name: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Team name</span>
  team_leader_id?: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// Optional Team Leader ID (can be assigned later)</span>
}

<span class="hljs-comment">// Assign Role Command</span>
<span class="hljs-keyword">interface</span> AssignRoleCommand {
  project: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Project name</span>
  family: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Family name</span>
  value_stream: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Value stream</span>
  area: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Area</span>
  team_name: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Team name</span>
  role: IHRole;                    <span class="hljs-comment">// COORDINATOR, SHIFT_LEADER, or TEAM_LEADER</span>
  employee_id: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Legacy site ID of assignee</span>
}

<span class="hljs-comment">// Unassign Role Command</span>
<span class="hljs-keyword">interface</span> UnassignRoleCommand {
  project: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Project name</span>
  family: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Family name</span>
  value_stream: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Value stream</span>
  area: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Area</span>
  team_name: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Team name</span>
  role: IHRole;                    <span class="hljs-comment">// COORDINATOR, SHIFT_LEADER, or TEAM_LEADER</span>
}

<span class="hljs-comment">// Update Team Command</span>
<span class="hljs-keyword">interface</span> UpdateTeamCommand {
  team_id: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Team ID</span>
  team_name?: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// New team name (optional)</span>
  is_active?: <span class="hljs-built_in">boolean</span>;             <span class="hljs-comment">// New active status (optional)</span>
}

<span class="hljs-comment">// Delete Team Command</span>
<span class="hljs-keyword">interface</span> DeleteTeamCommand {
  team_id: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Team ID</span>
}
</div></code></pre>
<h5 id="52-command-validation">5.2 Command Validation</h5>
<p>Commands are validated based on:</p>
<ol>
<li><strong>Data Integrity</strong> - Ensure all required fields are present and valid</li>
<li><strong>Business Rules</strong> - Enforce rules such as:
<ul>
<li>Team names must be unique within a project-family-value_stream-area combination</li>
<li>Users can only be assigned to one role at a time</li>
<li>Department Clerks can only manage teams within their department</li>
</ul>
</li>
<li><strong>Concurrency</strong> - Handle concurrent modification using optimistic concurrency control</li>
</ol>
<h4 id="6-permissions-and-authorization">6. Permissions and Authorization</h4>
<h5 id="61-role-based-access-control">6.1 Role-Based Access Control</h5>
<table>
<thead>
<tr>
<th>Role</th>
<th>Create Team</th>
<th>Delete Team</th>
<th>Update Team</th>
<th>Assign Coordinator</th>
<th>Assign Shift Leader</th>
<th>Assign Team Leader</th>
<th>View Teams</th>
</tr>
</thead>
<tbody>
<tr>
<td>Department Clerk</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
</tr>
<tr>
<td>Coordinator</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>✓</td>
</tr>
<tr>
<td>Shift Leader</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>✓</td>
</tr>
<tr>
<td>Team Leader</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>✓</td>
</tr>
<tr>
<td>Manufacturing Engineer</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>✓</td>
</tr>
</tbody>
</table>
<h5 id="62-authorization-rules">6.2 Authorization Rules</h5>
<ol>
<li>Department Clerks can only manage teams within their assigned departments</li>
<li>Users can view teams and assignments according to their hierarchical position</li>
<li>Special roles (e.g., Quality Supervisors) may have cross-departmental view permissions</li>
<li>Audit trail of all changes is maintained for accountability</li>
</ol>
<h4 id="7-api-endpoints">7. API Endpoints</h4>
<h5 id="71-team-management">7.1 Team Management</h5>
<pre class="hljs"><code><div><span class="hljs-comment">// Create a new team</span>
POST /api/v1/teams
Request: {
  team_name: <span class="hljs-built_in">string</span>;
  team_leader_id?: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Optional</span>
}
Response: {
  id: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Newly created team ID</span>
  success: <span class="hljs-built_in">boolean</span>;
  errors?: <span class="hljs-built_in">string</span>[];
}

<span class="hljs-comment">// Update an existing team</span>
PUT /api/v1/teams/{teamId}
Request: {
  team_name?: <span class="hljs-built_in">string</span>;
  is_active?: <span class="hljs-built_in">boolean</span>;
}
Response: {
  success: <span class="hljs-built_in">boolean</span>;
  errors?: <span class="hljs-built_in">string</span>[];
}

<span class="hljs-comment">// Delete a team</span>
DELETE /api/v1/teams/{teamId}
Response: {
  success: <span class="hljs-built_in">boolean</span>;
  errors?: <span class="hljs-built_in">string</span>[];
}
</div></code></pre>
<h5 id="72-assignment-management">7.2 Assignment Management</h5>
<pre class="hljs"><code><div><span class="hljs-comment">// Assign role to team</span>
POST /api/v1/assignments
Request: {
  project: <span class="hljs-built_in">string</span>;
  family: <span class="hljs-built_in">string</span>;
  value_stream: <span class="hljs-built_in">string</span>;
  area: <span class="hljs-built_in">string</span>;
  team_name: <span class="hljs-built_in">string</span>;
  role: <span class="hljs-string">"COORDINATOR"</span> | <span class="hljs-string">"SHIFT_LEADER"</span> | <span class="hljs-string">"TEAM_LEADER"</span>;
  employee_id: <span class="hljs-built_in">string</span>;
}
Response: {
  success: <span class="hljs-built_in">boolean</span>;
  errors?: <span class="hljs-built_in">string</span>[];
}

<span class="hljs-comment">// Unassign role from team</span>
DELETE /api/v1/assignments
Request: {
  project: <span class="hljs-built_in">string</span>;
  family: <span class="hljs-built_in">string</span>;
  value_stream: <span class="hljs-built_in">string</span>;
  area: <span class="hljs-built_in">string</span>;
  team_name: <span class="hljs-built_in">string</span>;
  role: <span class="hljs-string">"COORDINATOR"</span> | <span class="hljs-string">"SHIFT_LEADER"</span> | <span class="hljs-string">"TEAM_LEADER"</span>;
}
Response: {
  success: <span class="hljs-built_in">boolean</span>;
  errors?: <span class="hljs-built_in">string</span>[];
}
</div></code></pre>
<h5 id="73-query-endpoints">7.3 Query Endpoints</h5>
<pre class="hljs"><code><div><span class="hljs-comment">// Get all teams with optional filtering</span>
GET /api/v1/teams
Query Parameters:
  - project?: <span class="hljs-built_in">string</span>
  - family?: <span class="hljs-built_in">string</span>
  - value_stream?: <span class="hljs-built_in">string</span>
  - area?: <span class="hljs-built_in">string</span>
  - team_name?: <span class="hljs-built_in">string</span>
  - has_coordinator?: <span class="hljs-built_in">boolean</span>
  - has_shift_leader?: <span class="hljs-built_in">boolean</span>
  - has_team_leader?: <span class="hljs-built_in">boolean</span>
  - is_active?: <span class="hljs-built_in">boolean</span>
  - sort_by?: <span class="hljs-built_in">string</span>
  - page?: <span class="hljs-built_in">number</span>
  - page_size?: <span class="hljs-built_in">number</span>
Response: {
  teams: ZoningDocument[];
  total_count: <span class="hljs-built_in">number</span>;
  page: <span class="hljs-built_in">number</span>;
  page_size: <span class="hljs-built_in">number</span>;
}

<span class="hljs-comment">// Get specific team by ID</span>
GET /api/v1/teams/{teamId}
Response: {
  team: ZoningDocument;
}

<span class="hljs-comment">// Get assignment history</span>
GET /api/v1/teams/{teamId}/history
Query Parameters:
  - role?: <span class="hljs-string">"COORDINATOR"</span> | <span class="hljs-string">"SHIFT_LEADER"</span> | <span class="hljs-string">"TEAM_LEADER"</span>
  - from_date?: <span class="hljs-built_in">string</span>  <span class="hljs-comment">// ISO format</span>
  - to_date?: <span class="hljs-built_in">string</span>    <span class="hljs-comment">// ISO format</span>
  - page?: <span class="hljs-built_in">number</span>
  - page_size?: <span class="hljs-built_in">number</span>
Response: {
  history: {
    event_type: <span class="hljs-built_in">string</span>;
    timestamp: <span class="hljs-built_in">string</span>;
    role: <span class="hljs-built_in">string</span>;
    employee_id?: <span class="hljs-built_in">string</span>;
    employee_name?: <span class="hljs-built_in">string</span>;
    performed_by: <span class="hljs-built_in">string</span>;
  }[];
  total_count: <span class="hljs-built_in">number</span>


#### <span class="hljs-number">8.</span> Workflows

##### <span class="hljs-number">8.1</span> Team Creation

<span class="hljs-string">``</span><span class="hljs-string">`mermaid
sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB

    DC-&gt;&gt;API: Create Team Request
    API-&gt;&gt;API: Validate Request
    
    alt Validation Failed
        API--&gt;&gt;DC: Return Error
    else Validation Passed
        API-&gt;&gt;ES: Store CreateTeam Event
        ES-&gt;&gt;RP: Process Event
        RP-&gt;&gt;DB: Create Team Document
        RP-&gt;&gt;DB: Create Initial Zoning Document
        API--&gt;&gt;DC: Return Success
    end
</span></div></code></pre>
<h5 id="82-role-assignment">8.2 Role Assignment</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Assign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Event
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
</div></code></pre>
<h5 id="83-role-reassignment">8.3 Role Reassignment</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant DC as Department Clerk
    participant API as DH Walk API
    participant ES as Event Store
    participant RP as Read Model Processor
    participant DB as CosmosDB
    participant WS as Workstation Service

    DC->>API: Reassign Role Request
    API->>API: Validate Request
    API->>DB: Check Current Assignments
    
    alt Validation Failed
        API-->>DC: Return Error
    else Validation Passed
        API->>ES: Store UnassignedFromTeam Event
        API->>ES: Store AssignedToTeam Event
        ES->>RP: Process Events
        RP->>DB: Update Zoning Document
        RP->>WS: Notify Workstation Service (if Team Leader)
        API-->>DC: Return Success
    end
</div></code></pre>
<h4 id="9-data-persistence">9. Data Persistence</h4>
<h5 id="91-document-structure">9.1 Document Structure</h5>
<p>The DH Walk microservice uses a document database (Cosmos DB) to store:</p>
<ol>
<li><strong>Events</strong> - All system events in an append-only event store</li>
<li><strong>Teams</strong> - Team documents containing team details</li>
<li><strong>Zonings</strong> - Zoning documents representing the assignment structure</li>
<li><strong>Projection Models</strong> - Optimized read models for UI display</li>
</ol>
<h5 id="92-event-storage">9.2 Event Storage</h5>
<p>Events are stored in an append-only event store with the following structure:</p>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> StoredEvent {
  id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Event ID</span>
  <span class="hljs-keyword">type</span>: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Event type</span>
  payload: <span class="hljs-built_in">any</span>;                  <span class="hljs-comment">// Event payload (serialized JSON)</span>
  metadata: {
    timestamp: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// When the event occurred</span>
    user_id: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Who created the event</span>
    correlation_id?: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// For tracking related events</span>
    causation_id?: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// What caused this event</span>
    version: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Schema version</span>
  };
  sequence_number: <span class="hljs-built_in">number</span>;       <span class="hljs-comment">// For ordering events</span>
  stream_id: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Aggregate ID this event belongs to</span>
}
</div></code></pre>
<h5 id="93-read-model">9.3 Read Model</h5>
<p>The read model is optimized for the UI and contains denormalized data for efficient querying:</p>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> TeamProjectionModel {
  id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
  project: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Project name</span>
  family: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Family name</span>
  value_stream: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Value stream</span>
  area: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Area</span>
  team_name: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Team name</span>
  coordinator_id?: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// Coordinator ID (if assigned)</span>
  coordinator_name?: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Coordinator name (if assigned)</span>
  shift_leader_id?: <span class="hljs-built_in">string</span>;      <span class="hljs-comment">// Shift Leader ID (if assigned)</span>
  shift_leader_name?: <span class="hljs-built_in">string</span>;    <span class="hljs-comment">// Shift Leader name (if assigned)</span>
  team_leader_id?: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// Team Leader ID (if assigned)</span>
  team_leader_name?: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Team Leader name (if assigned)</span>
  is_active: <span class="hljs-built_in">boolean</span>;            <span class="hljs-comment">// Active status</span>
  headcount: <span class="hljs-built_in">number</span>;             <span class="hljs-comment">// Number of operators</span>
  last_updated: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Last update timestamp</span>
}
</div></code></pre>
<h4 id="10-integration-patterns">10. Integration Patterns</h4>
<h5 id="101-event-publication">10.1 Event Publication</h5>
<p>After storing events in the event store, the DH Walk microservice publishes events to Azure Service Bus for consumption by other microservices:</p>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> IntegrationEvent {
  id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Event ID</span>
  <span class="hljs-keyword">type</span>: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// Event type</span>
  payload: <span class="hljs-built_in">any</span>;                  <span class="hljs-comment">// Event payload</span>
  timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// When the event occurred</span>
  source: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Source system ("dh-walk")</span>
  version: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Schema version</span>
}
</div></code></pre>
<h5 id="102-external-system-integration">10.2 External System Integration</h5>
<p>The DH Walk microservice integrates with:</p>
<ol>
<li><strong>ME Definition Service</strong> - To retrieve project, family, value stream, and area data</li>
<li><strong>Identity Service</strong> - To retrieve employee information</li>
<li><strong>Workstation Service</strong> - To notify of Team Leader changes</li>
<li><strong>TKS Service</strong> - To notify of team structure changes</li>
</ol>
<h4 id="11-ui-components">11. UI Components</h4>
<h5 id="111-project-list-view">11.1 Project List View</h5>
<p>The main UI view shows a table of projects with their associated families, value streams, areas, and assigned roles:</p>
<ol>
<li><strong>Filtering Controls</strong> - Filter by project, family, value stream, area</li>
<li><strong>Search Box</strong> - Quick search across all fields</li>
<li><strong>Sort Controls</strong> - Sort by various columns</li>
<li><strong>Assignment Status</strong> - Visual indicators for filled/unfilled positions</li>
<li><strong>Assignment Buttons</strong> - Buttons for assigning unassigned roles</li>
</ol>
<h5 id="112-assignment-dialogs">11.2 Assignment Dialogs</h5>
<p>When assigning roles, modal dialogs provide:</p>
<ol>
<li><strong>Employee Selection</strong> - Search and select from eligible employees</li>
<li><strong>Role Details</strong> - Information about the role being assigned</li>
<li><strong>Validation Feedback</strong> - Real-time validation and error messages</li>
<li><strong>Confirmation</strong> - Confirmation of assignment action</li>
</ol>
<h4 id="12-performance-considerations">12. Performance Considerations</h4>
<h5 id="121-query-optimization">12.1 Query Optimization</h5>
<p>The DH Walk microservice optimizes queries through:</p>
<ol>
<li><strong>Denormalized Read Models</strong> - For efficient filtering</li>
<li><strong>Pagination</strong> - For handling large result sets</li>
<li><strong>Caching</strong> - For frequently accessed data</li>
<li><strong>Indexed Fields</strong> - For optimal query performance</li>
</ol>
<h5 id="122-caching-strategy">12.2 Caching Strategy</h5>
<p>The system employs strategic caching:</p>
<ol>
<li><strong>Reference Data</strong> - Project, family, value stream, and area hierarchies</li>
<li><strong>Employee Lookup</strong> - Basic employee details</li>
<li><strong>Authorization Info</strong> - Department and role mappings</li>
<li><strong>Team Structures</strong> - Recent and frequently accessed teams</li>
</ol>
<h4 id="13-security-considerations">13. Security Considerations</h4>
<ol>
<li><strong>Authentication</strong> - Azure AD integration with role-based permissions</li>
<li><strong>Authorization</strong> - Granular permission checks based on role and department</li>
<li><strong>Audit Logging</strong> - Complete audit trail of all actions</li>
<li><strong>Data Protection</strong> - Encryption in transit and at rest</li>
<li><strong>Input Validation</strong> - Defense against injection attacks</li>
</ol>
<h4 id="14-deployment-strategy">14. Deployment Strategy</h4>
<ol>
<li><strong>Containerization</strong> - Docker containers for consistent deployment</li>
<li><strong>Kubernetes</strong> - For orchestration and scaling</li>
<li><strong>CI/CD Pipeline</strong> - Automated testing and deployment</li>
<li><strong>Blue/Green Deployments</strong> - Zero-downtime updates</li>
<li><strong>Environment Separation</strong> - Dev, Test, Staging, Production</li>
</ol>
<h3 id="45-workstation-service">4.5 Workstation Service</h3>
<p>(This section would contain content from the LLD-workstation.md, but since the file content wasn't provided in the context in a usable format, I'm leaving this as a placeholder. The structure would match the format of the other sections, preserving all original details from the LLD file.)</p>
<h3 id="46-direct-dependents-service">4.6 Direct Dependents Service</h3>
<h4 id="1-introduction">1. Introduction</h4>
<p>The Direct Dependents microservice provides a hierarchical view of the organizational structure for Aptiv Connected Workers. It enables visibility of reporting relationships from Plant Manager to operator level.</p>
<h4 id="2-architecture-overview">2. Architecture Overview</h4>
<ul>
<li><strong>Read-Only Service</strong>: The microservice is designed to perform read operations from a database.</li>
<li><strong>Data Source</strong>: Reads hierarchical data and unassigned workers from an existing cosmosdb.</li>
<li><strong>API</strong>: Exposes endpoints to fetch organizational charts and unassigned employees based on user roles.</li>
</ul>
<h4 id="3-domain-model--entities">3. Domain Model &amp; Entities</h4>
<h5 id="31-userrolehierarchy">3.1 UserRoleHierarchy</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">public</span> <span class="hljs-keyword">class</span> <span class="hljs-title">UserRoleHierarchy</span>
{
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> LecacySiteId { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; } <span class="hljs-comment">// employee_id</span>
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> FullName { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Department { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Site { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Role { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> ManagerLecacySiteId { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> List&lt;SubordinateCrew&gt; SubordinateCrew { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
}

<span class="hljs-keyword">public</span> <span class="hljs-keyword">class</span> <span class="hljs-title">SubordinateCrew</span>
{
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> SubLecacySiteId { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> SubFullName { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> SubRole { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> SubRoleStatus { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">bool</span> InWorkday { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> List&lt;<span class="hljs-keyword">string</span>&gt; skills { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> category { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> contract_type { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> List&lt;SubordinateCrew&gt; Subordinates { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
}
</div></code></pre>
<h5 id="32-teamleaderteamcrew">3.2 TeamLeaderTeamCrew</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">public</span> <span class="hljs-keyword">class</span> <span class="hljs-title">TeamLeaderTeamCrew</span>
{
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> TeamLeaderLegacySite { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> TeamLeaderName { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Teams { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> FirstName { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> LastName { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Department { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> OperatorLegacySite { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Role { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> RoleStatus { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
}
</div></code></pre>
<h5 id="33-assignmentrule">3.3 AssignmentRule</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">public</span> <span class="hljs-keyword">class</span> <span class="hljs-title">AssignmentRule</span>
{
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Id { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> AssignerRole { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> AssignToRole { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> AssigneeRole { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> ValidationRules ValidationRules { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">bool</span> IsActive { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Country { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">string</span> Scope { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
}

<span class="hljs-keyword">public</span> <span class="hljs-keyword">class</span> <span class="hljs-title">ValidationRules</span>
{
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">bool</span> RequiresDirectHierarchy { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">bool</span> RequiresSameDepartment { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
    <span class="hljs-keyword">public</span> <span class="hljs-keyword">bool</span> RequiresWorkday { <span class="hljs-keyword">get</span>; <span class="hljs-keyword">set</span>; }
}
</div></code></pre>
<h4 id="4-business-logic">4. Business Logic</h4>
<h5 id="41-organisationchartservice">4.1 OrganisationChartService</h5>
<ul>
<li><strong>GetOrganisationChart</strong>: Determines the role of the employee and fetches the organization chart based on the role and parameters.</li>
<li><strong>GetUnassignedEmployees</strong>: Fetches unassigned employees for the given user based on their role.</li>
</ul>
<h4 id="5-api-endpoints">5. API Endpoints</h4>
<h5 id="51-get-organisationchart">5.1 Get /OrganisationChart</h5>
<ul>
<li><strong>Endpoint</strong>: <code>/api/organisation-chart</code></li>
<li><strong>Method</strong>: <code>GET</code></li>
<li><strong>Query Parameters</strong>: <code>legacySiteId</code> (string)</li>
</ul>
<h5 id="52-get-unassignedemployees">5.2 Get /UnassignedEmployees</h5>
<ul>
<li><strong>Endpoint</strong>: <code>/api/unassigned-employees</code></li>
<li><strong>Method</strong>: <code>GET</code></li>
<li><strong>Query Parameters</strong>: <code>legacySiteId</code> (string)</li>
</ul>
<h4 id="6-database-schema">6. Database Schema</h4>
<h5 id="61-userrolehierarchy-table">6.1 UserRoleHierarchy Table</h5>
<ul>
<li><strong>Columns</strong>: <code>LecacySiteId</code>, <code>FullName</code>, <code>Department</code>, <code>Site</code>, <code>Role</code>, <code>ManagerLecacySiteId</code>, <code>SubordinateCrew</code></li>
</ul>
<h5 id="62-teamleaderteamcrew-table">6.2 TeamLeaderTeamCrew Table</h5>
<ul>
<li><strong>Columns</strong>: <code>TeamLeaderLegacySite</code>, <code>TeamLeaderName</code>, <code>Teams</code>, <code>FirstName</code>, <code>LastName</code>, <code>Department</code>, <code>OperatorLegacySite</code>, <code>Role</code>, <code>RoleStatus</code></li>
</ul>
<h5 id="63-assignmentrule-table">6.3 AssignmentRule Table</h5>
<ul>
<li><strong>Columns</strong>: <code>Id</code>, <code>AssignerRole</code>, <code>AssignToRole</code>, <code>AssigneeRole</code>, <code>ValidationRules</code>, <code>IsActive</code>, <code>Country</code>, <code>Scope</code></li>
</ul>
<h4 id="7-error-handling">7. Error Handling</h4>
<ul>
<li><strong>Business Errors</strong>: Invalid role, missing data.</li>
<li><strong>Technical Errors</strong>: Database connectivity issues, API failures.</li>
</ul>
<h4 id="8-summary">8. Summary</h4>
<p>The Direct Dependents microservice provides a flexible and generic way to fetch organizational data and unassigned employees based on employee roles and parameters defined in the <code>AssignmentRule</code> table. The service can be extended to handle additional cases by updating the parameter table and adding corresponding logic in the service layer.</p>
<h3 id="47-operator-skills-service">4.7 Operator Skills Service</h3>
<h4 id="1-overview-of-architecture">1. Overview of Architecture</h4>
<p>The Operator Skills service adopts a straightforward event-driven architecture:</p>
<h5 id="event-handling">Event Handling:</h5>
<ul>
<li>The service subscribes to a Service Bus topic to receive events.</li>
<li>Events contain operator skills data (operator ID and skills).</li>
<li>The service processes these events and stores the data in a database.</li>
</ul>
<h5 id="database">Database:</h5>
<ul>
<li>A relational database (e.g., SQL Server) is used to store operator skills data.</li>
<li>The database schema is designed to efficiently query operator skills by employee ID and site.</li>
</ul>
<h4 id="2-domain-model--entity">2. Domain Model &amp; Entity</h4>
<h5 id="21-operator-skills">2.1 Operator Skills</h5>
<p>An Operator Skills entity within this bounded context has attributes:</p>
<ul>
<li><code>employee_id</code> (unique ID for the employee)</li>
<li><code>site</code> (employee's site)</li>
<li><code>certifications</code> (employee's certifications)</li>
<li><code>skills</code> (family or job type within the project)</li>
</ul>
<h4 id="3-event-handling-model">3. Event Handling Model</h4>
<h5 id="31-event-subscription">3.1 Event Subscription</h5>
<p>The service subscribes to a Service Bus topic to receive events. Each event contains the following data:</p>
<ul>
<li><code>event_name</code>: Name of the event.</li>
<li><code>operator_id</code>: Unique ID of the operator.</li>
<li><code>skills</code>: List of skills for the operator.</li>
</ul>
<h5 id="32-event-processing">3.2 Event Processing</h5>
<p>Upon receiving an event, the service:</p>
<ol>
<li>Parses the event data.</li>
<li>Validates the data.</li>
<li>Stores the data in the database.</li>
</ol>
<h4 id="4-database-schema">4. Database Schema</h4>
<h5 id="41-operator-skills-table">4.1 Operator Skills Table</h5>
<table>
<thead>
<tr>
<th>Column Name</th>
<th>Data Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>employee_id</code></td>
<td>STRING</td>
<td>Unique employee ID</td>
</tr>
<tr>
<td><code>site</code></td>
<td>STRING</td>
<td>Employee's site</td>
</tr>
<tr>
<td><code>certifications</code></td>
<td>STRING</td>
<td>Employee's certifications</td>
</tr>
<tr>
<td><code>skills</code></td>
<td>STRING</td>
<td>Family or job type within the project</td>
</tr>
</tbody>
</table>
<ul>
<li><strong>Clustered Index on <code>site</code></strong> → Because queries often filter by site.</li>
<li><strong>Non-Clustered Index on <code>employee_id</code></strong> → Because queries often look up employees by ID.</li>
</ul>
<h4 id="example-sql-schema">Example SQL schema:</h4>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> OperatorSkills (
    employee_id <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">50</span>) PRIMARY <span class="hljs-keyword">KEY</span>,
    site <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">50</span>),
    certifications <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>),
    skills <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">255</span>)
);

<span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">INDEX</span> idx_site <span class="hljs-keyword">ON</span> OperatorSkills(site);
<span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">INDEX</span> idx_employee_id <span class="hljs-keyword">ON</span> OperatorSkills(employee_id);
</div></code></pre>
<h4 id="5-api-endpoints">5. API Endpoints</h4>
<h5 id="51-post-operator-skills">5.1 <code>POST /operator-skills</code></h5>
<p>Endpoint to receive operator skills data.</p>
<h4 id="body">Body:</h4>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"event_name"</span>: <span class="hljs-string">"OperatorSkillsUpdated"</span>,
  <span class="hljs-attr">"operator_id"</span>: <span class="hljs-string">"operator-123"</span>,
  <span class="hljs-attr">"site"</span>: <span class="hljs-string">"siteA"</span>,
  <span class="hljs-attr">"certifications"</span>: <span class="hljs-string">"cert1, cert2"</span>,
  <span class="hljs-attr">"skills"</span>: <span class="hljs-string">"skill1, skill2"</span>
}
</div></code></pre>
<h4 id="behavior">Behavior:</h4>
<ol>
<li>Parses the event data.</li>
<li>Validates the data.</li>
<li>Stores the data in the database.</li>
</ol>
<h4 id="6-implementation-notes">6. Implementation Notes</h4>
<ul>
<li><strong>Service Bus SDK</strong>: Use the official Azure Service Bus SDK to subscribe to the topic and receive events.</li>
<li><strong>Database Access</strong>: Use an ORM (e.g., Entity Framework) or direct SQL queries to interact with the database.</li>
<li><strong>Error Handling</strong>: Implement robust error handling to manage invalid data and connection issues.</li>
<li><strong>Logging</strong>: Use a logging framework to log important events and errors.</li>
</ul>
<h4 id="7-roles--permissions">7. Roles &amp; Permissions</h4>
<h5 id="71-role-definitions">7.1 Role Definitions</h5>
<p><strong>Admin:</strong></p>
<ul>
<li>Can view and manage all operator skills data.</li>
<li>Can configure the service (e.g., change Service Bus topic subscription).</li>
</ul>
<p><strong>User:</strong></p>
<ul>
<li>Can view operator skills data.</li>
<li>Cannot modify or configure the service.</li>
</ul>
<h5 id="72-permission-matrix">7.2 Permission Matrix</h5>
<table>
<thead>
<tr>
<th>Operation</th>
<th>Admin</th>
<th>User</th>
</tr>
</thead>
<tbody>
<tr>
<td>View Operator Skills</td>
<td>Yes</td>
<td>Yes</td>
</tr>
<tr>
<td>Manage Operator Skills</td>
<td>Yes</td>
<td>No</td>
</tr>
<tr>
<td>Configure Service</td>
<td>Yes</td>
<td>No</td>
</tr>
</tbody>
</table>
<h4 id="8-use-case-scenarios">8. Use Case Scenarios</h4>
<h5 id="81-receive-and-store-operator-skills">8.1 Receive and Store Operator Skills</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant ServiceBus
    participant MS3
    participant Database
    
    ServiceBus->>MS3: Send OperatorSkillsUpdated Event
    MS3->>MS3: Parse and Validate Event
    MS3->>Database: Store Operator Skills Data
    MS3->>ServiceBus: Acknowledge Event
</div></code></pre>
<h5 id="82-query-operator-skills-by-site">8.2 Query Operator Skills by Site</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant MS3
    participant Database
    
    User->>MS3: GET /operator-skills?site=siteA
    MS3->>Database: Query Operator Skills by Site
    Database->>MS3: Return Operator Skills Data
    MS3->>User: Return Operator Skills Data
</div></code></pre>
<h4 id="9-summary">9. Summary</h4>
<p>The Operator Skills microservice (MS3) is designed to receive operator skills data from a Service Bus topic and store it in a database. The service ensures efficient querying of operator skills by employee ID and site, supporting the overall goal of maintaining detailed operator skill and certification data.</p>
<p>This design supports scalable event handling, robust data storage, and efficient querying, ensuring that operator skills data is always up-to-date and easily accessible.</p>
<h2 id="5-integration-patterns">5. Integration Patterns</h2>
<h3 id="51-event-driven-communication">5.1 Event-Driven Communication</h3>
<p>Services publish domain events to Azure Service Bus topics when significant state changes occur. Other services subscribe to relevant events and react accordingly.</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant OM as Operator Management
    participant Bus as Azure Service Bus
    participant EA as Employee Assignment
    participant OS as Operator Skills
    
    OM->>Bus: Publish OperatorStatusChanged
    Bus->>EA: Notify of status change
    EA->>EA: Update assignments
    Bus->>OS: Notify of status change
    OS->>OS: Check skill implications
</div></code></pre>
<h3 id="52-synchronous-api-calls">5.2 Synchronous API Calls</h3>
<p>For immediate data needs, services make direct HTTP calls to other services' APIs.</p>
<h3 id="53-database-integration">5.3 Database Integration</h3>
<p>For complex reporting needs, services may share read-only database views or use Cosmos DB Change Feed for real-time updates.</p>
<h2 id="6-security">6. Security</h2>
<p>The Crew Management microservices implement several security measures:</p>
<ul>
<li><strong>Authentication</strong>: Azure AD integration with role-based permissions</li>
<li><strong>Authorization</strong>: Role-based access control with fine-grained permissions</li>
<li><strong>API Gateway</strong>: Azure API Management enforces security policies</li>
<li><strong>Data Encryption</strong>: Data encrypted at rest and in transit</li>
<li><strong>Audit Logging</strong>: Comprehensive audit trails for all operations</li>
<li><strong>Certificate Management</strong>: TLS certificates for secure communication</li>
</ul>
<h2 id="7-performance-considerations">7. Performance Considerations</h2>
<p>Performance optimization strategies include:</p>
<ul>
<li><strong>Database Indexing</strong>: Strategic indexes on common query patterns</li>
<li><strong>Read/Write Separation</strong>: CQRS pattern for high-throughput operations</li>
<li><strong>Asynchronous Processing</strong>: Background processing for non-critical operations</li>
<li><strong>Pagination</strong>: All list endpoints support pagination for large datasets</li>
<li><strong>Rate Limiting</strong>: API rate limiting to prevent abuse</li>
</ul>
<h2 id="8-deployment-strategy">8. Deployment Strategy</h2>
<p>The Crew Management microservices use a cloud-native deployment approach:</p>
<ul>
<li><strong>Containerization</strong>: All services deployed as Docker containers</li>
<li><strong>Kubernetes</strong>: Azure Kubernetes Service for container orchestration</li>
<li><strong>CI/CD Pipeline</strong>: Automated builds and deployments via Azure DevOps</li>
<li><strong>Environment Separation</strong>: Development, Staging, and Production environments</li>
<li><strong>Configuration Management</strong>: External configuration via Azure App Configuration</li>
<li><strong>Blue/Green Deployments</strong>: Zero-downtime deployments</li>
<li><strong>Monitoring and Alerting</strong>: Application Insights for real-time monitoring</li>
</ul>

</body>
</html>
