# Low-Level Design: CQRS Approach for Crew Management

## 1. Introduction

This document outlines the design for implementing Command Query Responsibility Segregation (CQRS) pattern within the Crew Management module of Connected Workers. The design replaces the current Databricks change feed implementation with a service-based event processing system, providing more direct control, improved performance, and tighter integration with business logic.

## 2. Architectural Overview

### 2.1 CQRS Architecture

The CQRS architecture separates write operations (commands) from read operations (queries):

- **Command Side**: Handles write operations, validates business rules, and produces events
- **Event Store**: Captures all state changes as events in a Cosmos DB container
- **Service Bus**: Acts as a reliable message broker between services
- **Write Service**: Consumes events and updates read models
- **Read Models**: Optimized projections for specific query needs
- **Query Side**: Reads from optimized models without affecting write performance

### 2.2 System Flow

```
1. User/System initiates a command (e.g., assign operator to workstation)
2. Command microservice validates the command and business rules
3. Upon validation, the command creates an event and stores it in Cosmos DB
4. The event is published to Service Bus
5. Write Service consumes the event from Service Bus
6. Write Service updates the appropriate read models (SQL or Cosmos DB)
7. Query microservices read from these optimized models
```


## 3. Microservices and Their Affected Tables

### 3.1 MS1: Operator Management
- **Write Containers**: 
  - `operator_updates` (Cosmos DB)
- **Read Tables/Containers Affected**:
  - `aptiv_active_employee` (SQL)
  - `aptiv_new_hires` (SQL)
  - `user_validation` (Cosmos DB)
- **Events Published**:
  - OperatorCreated
  - OperatorUpdated
  - OperatorDeleted

### 3.2 MS2: Direct Dependents (DH Walk)
- **Write Containers**: 
  - `create_team` (Cosmos DB)
  - `zoning_updates` (Cosmos DB)
- **Read Tables/Containers Affected**:
  - `zoning` (SQL) - Primary table affected
  - `teamleader_team_crew` (Cosmos DB)
  - `user_role_hierarchy` (Cosmos DB)
  - `subordinate_role_counts` (Cosmos DB)
- **Events Published**:
  - TeamFormationEvent
  - TeamUpdated
  - TeamDissolved
  - ZoningUpdated
  - ZoningRoleAssigned
  - ZoningRoleRemoved

### 3.3 MS3: Operator Skills
- **Write Containers**: 
  - `skills_validation` (Cosmos DB)
- **Read Tables/Containers Affected**:
  - `operator_skills` (SQL)
  - `user_validation` (Cosmos DB)
- **Events Published**:
  - SkillAdded
  - SkillUpdated
  - SkillRemoved
  - CertificationAdded
  - CertificationUpdated
  - CertificationExpired

### 3.4 MS4: Workstation
- **Read-Only Service**:
  - Consumes data from `workstation` (Cosmos DB)
  - Consumes data from `teamleader_line_assignment` (Cosmos DB)
  - Consumes data from `user_role_hierarchy` (Cosmos DB)
  - Consumes data from `teamleader_team_crew` (Cosmos DB)
  - Consumes data from `user_validation` (Cosmos DB)
  - Consumes data from `zoning` (SQL)

### 3.5 MS5: Operator Assignment
- **Write Containers**: 
  - `employee-assignments` (Cosmos DB)
- **Read Tables/Containers Affected**:
  - `user_role_hierarchy` (Cosmos DB)
  - `subordinate_role_counts` (Cosmos DB)
  - `aptiv_active_employee` (SQL)
  - `aptiv_new_hires` (SQL)
  - `user_validation` (Cosmos DB)
  - `teamleader_team_crew` (Cosmos DB)
  - `teamleader_line_assignment` (Cosmos DB)
- **Events Published**:
  - AssignedToEmployee
  - AssignedToTeam
  - AssignedToWorkstation
  - ReassignedToWorkstation
  - UnassignedFromWorkstation
  - TransferredBetweenTeams

## 4. Container Definitions and Relationships

### 4.1 Command-Side Containers (Cosmos DB)

| Container Name | Purpose | Partition Key | Key Relationships | Source Microservice |
|----------------|---------|---------------|-------------------|---------------------|
| operator_updates | Stores events related to operator creation or deletion | /legacySiteId | Referenced by user_validation | MS1: Operator Management |
| skills_validation | Stores events related to skill certification updates | /legacySiteId | Referenced by user_validation, operator_skills | MS3: Operator Skills |
| create_team | Stores team formation events | /TL_legacy_site_id | Referenced by teamleader_team_crew | MS2: Direct Dependents |
| zoning_updates | Stores zoning configuration changes | /project, /family | Referenced by zoning | MS2: Direct Dependents |
| employee-assignments | Stores all assignment events (to employee, team, workstation) | /site | Referenced by multiple read models | MS5: Operator Assignment |

### 4.2 Read-Side Containers/Tables

#### 4.2.1 SQL Tables

| Table Name | Purpose | Key Relationships | Updated By |
|------------|---------|-------------------|------------|
| aptiv_active_employee | Central employee information | Referenced by multiple tables | MS1: Operator Management, MS5: Operator Assignment |
| aptiv_new_hires | Tracks new employees not yet in Workday | Referenced by employee assignments | MS1: Operator Management, MS5: Operator Assignment |
| operator_skills | Tracks operator qualifications and certifications | References aptiv_active_employee | MS3: Operator Skills |
| zoning | Maintains zone assignments and hierarchy | References user_role_hierarchy | MS2: Direct Dependents (DH Walk) |

#### 4.2.2 Cosmos DB Read Containers

| Container Name | Purpose | Partition Key | Key Relationships | Updated By |
|----------------|---------|---------------|-------------------|------------|
| user_role_hierarchy | Maintains organizational hierarchy | /legacy_site_id | Referenced by multiple containers | MS2: Direct Dependents, MS5: Operator Assignment |
| subordinate_role_counts | Tracks count of subordinates by role | /legacy_site_id, /role | References user_role_hierarchy | MS2: Direct Dependents, MS5: Operator Assignment |
| user_validation | Validation data for users, skills, etc. | /site | References multiple containers | MS1: Operator Management, MS3: Operator Skills, MS5: Operator Assignment |
| teamleader_team_crew | Team structure under each team leader | /teamleader_legacy_site | References user_role_hierarchy | MS2: Direct Dependents, MS5: Operator Assignment |
| teamleader_line_assignment | Workstation assignments under team leaders | /teamleader_legacy_site, /projet, /famille | References workstation, user_role_hierarchy | MS5: Operator Assignment |
| workstation | Workstation configuration and requirements | /station | Referenced by teamleader_line_assignment | (Pre-loaded or separately managed) |

### 4.3 Microservice Architecture Overview

```mermaid
graph TD
    MS1[MS1: Operator Management] -->|Command| WC1[operator_updates]
    MS3[MS3: Operator Skills] -->|Command| WC2[skills_validation]
    MS2[MS2: Direct Dependents] -->|Command| WC3[create_team]
    MS2 -->|Command| WC4[zoning_updates]
    MS5[MS5: Operator Assignment] -->|Command| WC5[employee-assignments]
    
    WC1 -->|Change Feed| SB1[Service Bus]
    WC2 -->|Change Feed| SB1
    WC3 -->|Change Feed| SB1
    WC4 -->|Change Feed| SB1
    WC5 -->|Change Feed| SB1
    
    SB1 --> WS[Write Service]
    
    WS -->|Updates| RM1[SQL Tables]
    WS -->|Updates| RM2[Cosmos DB Read Models]
```

### 4.4 Data Flow Overview

```mermaid
graph TD
    MS[Microservices] --> EVENT[Event Store]
    EVENT --> SB[Service Bus]
    SB --> WRITE[Write Service]
    
    WRITE --> SQL[SQL Tables]
    WRITE --> COSMOS[Cosmos DB Read Models]
    
    SQL --- SQL_LIST["aptiv_active_employee, aptiv_new_hires, operator_skills, zoning"]
    COSMOS --- COSMOS_LIST["user_role_hierarchy, user_validation, teamleader_team_crew, teamleader_line_assignment, workstation, subordinate_role_counts"]
```

## 5. Event Catalog

### 5.1 Operator Management Events (MS1)

| Event Type | Description | Source Container | Target Containers/Tables |
|------------|-------------|------------------|--------------------------|
| OperatorCreated | New operator record created | operator_updates | aptiv_active_employee, aptiv_new_hires, user_validation |
| OperatorUpdated | Existing operator information updated | operator_updates | aptiv_active_employee, aptiv_new_hires, user_validation |
| OperatorDeleted | Operator marked as deleted/inactive | operator_updates | aptiv_active_employee, aptiv_new_hires, user_validation |

### 5.2 Skills Management Events (MS3)

| Event Type | Description | Source Container | Target Containers/Tables |
|------------|-------------|------------------|--------------------------|
| SkillAdded | New skill added to operator | skills_validation | operator_skills, user_validation |
| SkillUpdated | Existing skill updated (level change) | skills_validation | operator_skills, user_validation |
| SkillRemoved | Skill removed from operator | skills_validation | operator_skills, user_validation |
| CertificationAdded | New certification added to operator | skills_validation | operator_skills, user_validation |
| CertificationUpdated | Certification status updated | skills_validation | operator_skills, user_validation |
| CertificationExpired | Certification marked as expired | skills_validation | operator_skills, user_validation |

### 5.3 Team Structure Events (MS2)

| Event Type | Description | Source Container | Target Containers/Tables |
|------------|-------------|------------------|--------------------------|
| TeamFormationEvent | New team created | create_team | teamleader_team_crew |
| TeamUpdated | Team information updated | create_team | teamleader_team_crew |
| TeamDissolved | Team marked as inactive | create_team | teamleader_team_crew |

### 5.4 Zoning Events (MS2 - DH Walk)

| Event Type | Description | Source Container | Target Containers/Tables |
|------------|-------------|------------------|--------------------------|
| ZoningUpdated | Zone configuration updated | zoning_updates | zoning |
| ZoningRoleAssigned | Role assigned to a zone | zoning_updates | zoning |
| ZoningRoleRemoved | Role removed from a zone | zoning_updates | zoning |

### 5.5 Assignment Events (MS5)

| Event Type | Description | Source Container | Target Containers/Tables |
|------------|-------------|------------------|--------------------------|
| AssignedToEmployee | Employee assigned to another employee | employee-assignments | user_role_hierarchy, subordinate_role_counts, aptiv_active_employee, user_validation, aptiv_new_hires |
| AssignedToTeam | Employee assigned to a team | employee-assignments | teamleader_team_crew |
| AssignedToWorkstation | Employee assigned to a workstation | employee-assignments | user_role_hierarchy, teamleader_line_assignment |
| ReassignedToWorkstation | Employee reassigned from one workstation to another | employee-assignments | user_role_hierarchy, subordinate_role_counts, teamleader_line_assignment, teamleader_team_crew |
| UnassignedFromWorkstation | Employee removed from workstation | employee-assignments | user_role_hierarchy, teamleader_line_assignment |
| TransferredBetweenTeams | Employee transferred between teams | employee-assignments | teamleader_team_crew, user_role_hierarchy |

## 6. Event Schema Details

### 6.1 Base Event Structure

All events share a common structure with the following properties:

- **id**: Unique identifier for the event
- **createdBy**: User or service that created the event
- **eventType**: Type of event from the catalog
- **timestamp**: When the event occurred
- **site**: Site where the event occurred
- **payload**: Event-specific data (varies by event type)

### 6.2 Key Event Payload Structures

#### 6.2.1 Operator Events

Payload for **OperatorCreated**:
- firstName, lastName
- legacyId, legacySiteId
- department, site
- hiringDate
- categories
- operatorFunction
- source (e.g., "CW", "Workday")

#### 6.2.2 Assignment Events

Payload for **AssignedToWorkstation**:
- assigner (legacySiteId, role, name)
- workstation (customer, project, family, valueStream, station, etc.)
- assigned (legacySiteId, role, team details)

Payload for **ReassignedToWorkstation**:
- assigner (as above)
- workstation (as above)
- assigned (as above)
- unassigned (details of person being replaced)

## 7. Write Service Design

### 7.1 Service Components

- **Event Listener**: Subscribes to Service Bus topics
- **Event Deserializer**: Converts messages to event objects
- **Event Router**: Routes events to appropriate handlers
- **Event Handlers**: Type-specific handlers for each event
- **Transaction Manager**: Ensures atomic updates across tables
- **Repository Layer**: Interfaces with read databases
- **Error Handler**: Manages retries and dead-letter scenarios

### 7.2 Event Processing Flow

1. Event Listener receives message from Service Bus
2. Message is deserialized into event object
3. Event Router determines appropriate handler
4. Event Handler processes the event, calling repositories
5. Transaction Manager ensures atomic updates
6. Success/failure status returned to Event Listener
7. Message completed or abandoned based on result

## 8. Error Handling and Recovery

### 8.1 Retry Strategies

- **Immediate Retry**: For transient errors
- **Delayed Retry**: With exponential backoff
- **Dead-Letter**: After max retry attempts
- **Circuit Breaker**: For system-wide issues

### 8.2 Consistency Recovery

- **Reconciliation Jobs**: Periodic comparison of event store vs. read models
- **Manual Intervention Tools**: Admin interface for fixing inconsistencies
- **Event Replay**: Ability to replay events from a specific point

## 9. Advantages of CQRS Approach

- **Performance**: Read and write operations optimized independently
- **Scalability**: Read and write services can scale independently
- **Flexibility**: Read models tailored to specific query needs
- **Audit Trail**: Complete history of changes in event store
- **Business Logic**: Domain rules enforced at source
- **Resilience**: Robust error handling and recovery
- **Maintainability**: Clear separation of concerns

## 10. Implementation Considerations

- **Event Versioning**: Strategy for handling schema evolution
- **Eventual Consistency**: Managing expectations for read model updates
- **Idempotency**: Ensuring events can be processed multiple times safely
- **Monitoring**: Comprehensive metrics for system health
- **Testing**: Strategies for testing event handlers