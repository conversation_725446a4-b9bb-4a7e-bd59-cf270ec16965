# Change Feed Mechanism - Employee Assignment Microservice

## 1. Overview

This document specifies the change feed implementation for the Employee Assignment microservice. The change feed mechanism listens for events in the employee-assignments container and updates related data models based on the event type and payload content.

## 2. Assignment Events

| Event Type              | Description                                        | Source Container     | Target Containers/Tables to update                                                                                                                                                                                                                                               |
| ----------------------- | -------------------------------------------------- | -------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| AssignedToEmployee      | Employee assigned from one supervisor to another   | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |
| ReassignedToEmployee    | Employee reassigned from one supervisor to another | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |
| AssignedToTeam          | Employee assigned to a team                        | employee-assignments | `teamleader_team_crew_by_teamleader_legacy_site`, `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`                                                                                                                                                            |
| ReassignedToTeam        | Employee reassigned from one team to another       | employee-assignments | `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site`                                                                                                                                   |
| AssignedToWorkstation   | Employee assigned to a workstation                 | employee-assignments | `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`                                                                                                                                   |
| ReassignedToWorkstation | Employee reassigned to a different workstation     | employee-assignments | `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`                                                                                                                                   |

## 3. Event Schema

### 3.1 Base Event Schema

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "createdBy": "user-id",
  "eventType": "OperationEventName",
  "timestamp": "2023-01-01T00:00:00Z",
  "site": "site1",
  "payload": {}
}
```

### 3.2 Event Payloads

#### AssignedToEmployee & ReassignedToEmployee

```json
"payload": {
  "assigner": {
    "legacySiteId": "45_MOROCCO MAR 1",
    "legacyId": 45,
    "role": "shift leader",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly"
  },
  "assignTo": {
    "legacySiteId": "46_MOROCCO MAR 1",
    "legacyId": 46,
    "role": "shift leader",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly"
  },
  "assignee": {
    "legacySiteId": "47_MOROCCO MAR 1",
    "legacyId": 47,
    "role": "operator",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly",
    "category": "direct hourly",
    "skills": ["Soudage", "Ultrasonic"],
    "inWorkday": true,
  }
}
```

#### AssignedToTeam

```json
"payload": {
  "assigner": {
    "legacySiteId": "45_MOROCCO MAR 1",
    "legacyId": 45,
    "role": "team leader",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly"
  },
  "assignee": {
    "legacySiteId": "46_MOROCCO MAR 1",
    "legacyId": 46,
    "role": "operator",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly",
    "category": "direct hourly",
    "skills": ["Soudage", "Ultrasonic"],
    "inWorkday": true,
  },
  "team": {
    "teamName": "Team 1",
    "teamLeaderLegacySiteId": "46_MOROCCO MAR 1"
  }
}
```

#### ReassignedToTeam

```json
"payload": {
  "assigner": {
    "legacySiteId": "45_MOROCCO MAR 1",
    "legacyId": 45,
    "role": "team leader",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly"
  },
  "assignee": {
    "legacySiteId": "46_MOROCCO MAR 1",
    "legacyId": 46,
    "role": "operator",
    "firstName": "John",
    "lastName": "Doe",
    "department": "assembly",
    "category": "direct hourly",
    "skills": ["Soudage", "Ultrasonic"],
    "inWorkday": true,
  },
  "team": {
    "teamName": "Team 1",
    "teamLeaderLegacySiteId": "46_MOROCCO MAR 1"
  },
  "oldTeam": {
    "teamName": "Team 2",
    "teamLeaderLegacySiteId": "46_MOROCCO MAR 1"
  }
}
```

## 4. Cosmos DB Read Models

### 4.1 user_role_hierarchy

Containers: `user_role_hierarchy_by_legacy_site`, `user_role_hierarchy_by_role`

Stores information about a user and their hierarchy, including subordinates (N-1) under their management.

```typescript
{
  id: string,
  legacy_site_id: string,
  fullname: string,
  department: string,
  site: string,
  role: string,
  manager_legacy_site_id: string,
  subordinate_crew: [
    {
      sub_legacy_site_id: string,
      sub_legacy_id: number,
      sub_fullname: string,
      sub_role: string,
      sub_role_status: string,
      sub_department: string,
      in_workday: boolean,
      category: string,
      contract_type: string,
      skills: [string],
    }
  ]
}
```

### 4.2 subordinate_role_counts

Container: `subordinate_role_counts_by_legacy_site`

Contains information about each user, the roles they supervise, and the count of subordinates in each role.

```typescript
{
  id: string,
  legacy_site_id: string,
  role: string,
  site: string,
  subordinate_roles_counts: [
    {
      subordinate_role: string,
      subordinate_role_counts: number
    }
  ]
}
```

### 4.3 user_validation

Containers: `user_validation_by_legacy_site`, `user_validation_by_site`

Contains information about each employee across all branches.

```typescript
{
  id: string,
  legacy_site_id: string,
  legacy_id: number,
  first_name: string,
  last_name: string,
  subordinate_id: [string],
  skills: [string],
  category: string,
  department: string,
  role: string,
  site: string,
  in_workday: boolean,
  is_rework: boolean,
  is_containment: boolean,
}
```

### 4.4 teamleader_team_crew

Container: `teamleader_team_crew_by_teamleader_legacy_site`

Contains information about a team leader, their team, and operators belonging to this team.

```typescript
{
  id: string,
  teamleader_legacy_site: string,
  teamleader_fullname: string,
  team: string,
  department: string,
  department_type: string,
  site: string,
  operators: [
    {
      legacy_site: string,
      legacy_id: number,
      first_name: string,
      last_name: string,
      role: string,
      departement: string,
      skills: string[],
      is_assigned_to_workstation: boolean,
    }
  ]
}
```

### 4.5 teamleader_line_assignment

Containers: `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site`

Contains information about operator assignments to workstations, including product and location details.

```typescript
{
  id: string,
  teamleader_legacy_site: string,
  teamleader_fullname: string,
  customer: string,
  projet: string,
  famille: string,
  value_stream: string,
  area: string,
  team: string,
  station: string,
  station_role: string,
  site: string,
  operator_legacy_site: string,
  operator_legacy_id: number,
  operator_first_name: string,
  operator_last_name: string,
  operator_skills: string[],
  me_definition_version: string,
  department: string,
  effective_date: string,
  station_id: string,
}
```

### 4.6 workstation

Containers: `workstation_by_customer`, `workstation_by_station_id`

Contains information about each workstation.

```typescript
{
  id: string,
  customer: string,
  projet: string,
  famille: string,
  value_stream: string,
  area: string,
  station: string,
  site: string,
  department: string,
  role_station: string,
  skills: string[],
  criticity: string,
  capacity: number,
  is_polyvalent: boolean,
  is_rework: boolean,
  is_containment: boolean,
  effective_date: string,
  me_definition_version: string,
  station_id: string,
  is_deleted: boolean,
}
```

## 5. Change Feed Implementation Logic

The change feed mechanism continuously monitors the `employee-assignments` container. When a new record is saved, it processes updates to target containers based on the event type and payload.

### 5.1 AssignedToEmployee

When an employee is assigned from one supervisor to another, the following updates are made:

1. In `user_role_hierarchy_by_legacy_site` and `user_role_hierarchy_by_role`:

   - Remove the assignee from the crew of the assigner
   - Add the assignee to the crew of assignTo

2. In `user_validation_by_legacy_site` and `user_validation_by_site`:

   - Remove the assignee's legacy site ID from the assigner's subordinate_id array
   - Add the assignee's legacy site ID to the assignTo's subordinate_id array

3. In `teamleader_team_crew_by_teamleader_legacy_site`:

   - Check if the assignee belongs to a team, if yes, remove them

4. In `teamleader_line_assignment_by_teamleader_legacy_site` and `teamleader_line_assignment_by_site`:
   - Check if the assignee has workstation assignments, if yes, remove them

### 5.2 ReassignedToEmployee

Similar to AssignedToEmployee, but with an unassignFrom parameter indicating the current supervisor:

1. In `user_role_hierarchy_by_legacy_site` and `user_role_hierarchy_by_role`:

   - Remove the assignee from the crew of unassignFrom
   - Add the assignee to the crew of assignTo

2. In `user_validation_by_legacy_site` and `user_validation_by_site`:

   - Remove the assignee's legacy site ID from unassignFrom's subordinate_id array
   - Add the assignee's legacy site ID to assignTo's subordinate_id array

3. In `teamleader_team_crew_by_teamleader_legacy_site`:

   - Check if the assignee belongs to a team, if yes, remove them

4. In `teamleader_line_assignment_by_teamleader_legacy_site` and `teamleader_line_assignment_by_site`:
   - Check if the assignee has workstation assignments, if yes, remove them

### 5.3 AssignedToTeam

When an employee is assigned to a team:

1. In `teamleader_team_crew_by_teamleader_legacy_site`:

   - Add the assignee to the specified team using the team name and teamleader_legacy_site_id

2. In `user_role_hierarchy_by_legacy_site` and `user_role_hierarchy_by_role`:
   - Update the sub_role_status of the assignee to "assigned to team" in the assigner's subordinate_crew

### 5.4 ReassignedToTeam

When an employee is reassigned from one team to another:

1. In `teamleader_team_crew_by_teamleader_legacy_site`:

   - Remove the assignee from the old team using old team name and team leader legacy site ID
   - Add the assignee to the new team using new team name and team leader legacy site ID

2. In `teamleader_line_assignment_by_teamleader_legacy_site` and `teamleader_line_assignment_by_site`:
   - Check if the assignee has workstation assignments, if yes, remove them

### 5.5 AssignedToWorkstation

When an employee is assigned to a workstation:

1. In `teamleader_line_assignment_by_teamleader_legacy_site` and `teamleader_line_assignment_by_site`:

   - Add a new record with the workstation assignment details

2. In `teamleader_team_crew_by_teamleader_legacy_site`:
   - If isContainment is false, update the assignee's is_assigned_to_workstation field to true

### 5.6 ReassignedToWorkstation

When an employee is reassigned to a different workstation:

1. In `teamleader_line_assignment_by_teamleader_legacy_site` and `teamleader_line_assignment_by_site`:

   - Add a new record for the new assignee
   - Remove the record for the previous assignee (unassigned)

2. In `teamleader_team_crew_by_teamleader_legacy_site`:
   - If the unassigned employee is no longer assigned to any workstation and isContainment is false, set their is_assigned_to_workstation field to false
