# Low-Level Design: CQRS Approach for Crew Management

## 1. Introduction

This document outlines the design for implementing Command Query Responsibility Segregation (CQRS) pattern within the Crew Management module of Connected Workers. The design replaces the current Databricks change feed implementation with a service-based event processing system, providing more direct control, improved performance, and tighter integration with business logic.

## 2. Architectural Overview

### 2.1 CQRS Architecture

The CQRS architecture separates write operations (commands) from read operations (queries):

- **Command Side**: Handles write operations, validates business rules, and produces events.
- **Event Store**: Captures all state changes as events in a Cosmos DB container.
- **Service Bus**: Acts as a reliable message broker between services.
- **Write Service**: Consumes events and updates read models.
- **Read Models**: Optimized projections for specific query needs.
- **Query Side**: Reads from optimized models without affecting write performance.
- **change feed**: change feed to update the read models when events are stored in the event store.

### 2.2 System Flow

```
1. User/System initiates a command (e.g., assign operator to workstation).
2. Command microservice validates the command and business rules.
3. Upon validation, the command creates an event and stores it in Cosmos DB.
4. The event is published to Service Bus.
5. Write Service consumes the event from Service Bus.
6. Write Service updates the appropriate read models (SQL or Cosmos DB).
7. Query microservices read from these optimized models.
```

## 3. Microservices and Their Affected Tables

### 3.1 MS1: Operator Management

- **Read Tables/Containers**:

  - `aptiv_active_employee` (SQL)
  - `aptiv_new_hires` (SQL)

- **Write Containers (event store)**:

  - `operator_updates` (Cosmos DB)

- **Events stored in the event store**:

  - OperatorCreated
  - OperatorDeleted

- **Read Tables/Containers affected**:
  - `aptiv_new_hires` (SQL)

### 3.2 MS2: Direct Dependents

- **Read-Only Service**:

  - Consumes data from `user_role_hierarchy_by_legacy_site` (Cosmos DB)
  - Consumes data from `user_validation_by_legacy_site` (Cosmos DB)
  - Consumes data from `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)
  - Consumes data from `assignment_rules` (Cosmos DB) [only one shared between other services]

### 3.3 MS3: Operator Skills

- **Read Tables/Containers**:
  - `skills_validation` (Cosmos DB)
- **Write table**:
  - `operator_skills` (SQL)
- **Events Received**:
  - OperatorSkillsUpdated

### 3.4 MS4: Workstation

- **Read Tables/Containers**:

  - Consumes data from `workstation_by_customer` (Cosmos DB)
  - Consumes data from `workstation_by_station_id` (Cosmos DB)
  - Consumes data from `teamleader_line_assignment_by_teamleader_legacy_site` (Cosmos DB)
  - Consumes data from `teamleader_line_assignment_by_site` (Cosmos DB)
  - Consumes data from `user_validation_by_legacy_site` (Cosmos DB)
  - Consumes data from `user_validation_by_site` (Cosmos DB)
  - Consumes data from `user_role_hierarchy_by_role` (Cosmos DB)
  - Consumes data from `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)
  - Consumes data from `ms_stations_by_department` (Cosmos DB) [only using by Workstation service]
  - Consumes data from `zoning` (SQL)
  - Consumes data from `assignment_rules` (Cosmos DB) [only one shared between other services]

- **Write Containers**:

  - `workstation-changes` (Cosmos DB)

- **Events stored in the event store**:

  - WorkstationCreated
  - WorkstationDeleted

- **Read Tables/Containers affected**:
  - `workstation_by_customer` (Cosmos DB)
  - `workstation_by_station_id` (Cosmos DB)
  - `teamleader_line_assignment_by_site` (Cosmos DB)
  - `teamleader_line_assignment_by_teamleader_legacy_site` (Cosmos DB)
  - `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)

### 3.5 MS5: Employee Assignment

- **Read Tables/Containers** :

  - `workstation_by_customer` (Cosmos DB)
  - `workstation_by_station_id` (Cosmos DB)
  - `user_validation_by_legacy_site` (Cosmos DB)
  - `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)
  - `assignment_rules` (Cosmos DB) [only one shared between other services]

- **Write Containers**:

  - `employee-assignments` (Cosmos DB)

- **Events stored in the event store**:

  - AssignedToEmployee
  - ReassignedToEmployee
  - AssignedToTeam
  - ReassignedToTeam
  - AssignedToWorkstation
  - ReassignedToWorkstations

- **Read Tables/Containers affected**:

  - `user_role_hierarchy_by_role` (Cosmos DB)
  - `user_role_hierarchy_by_legacy_site` (Cosmos DB)
  - `user_validation_by_legacy_site` (Cosmos DB)
  - `user_validation_by_site` (Cosmos DB)
  - `teamleader_line_assignment_by_teamleader_legacy_site` (Cosmos DB)
  - `teamleader_line_assignment_by_site` (Cosmos DB)
  - `subordinate_role_counts_by_legacy_site` (Cosmos DB)
  - `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)
  - `aptiv_active_employee` (SQL)
  - `aptiv_new_hires` (SQL)

  ### 3.6 MS6: DH Walk

- **Read Tables/Containers** :

  - `zoning` (SQL)
  - `user_role_hierarchy_by_legacy_site` (Cosmos DB)
  - `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)
  - `user_validation_by_legacy_site` (Cosmos DB)
  - `assignment_rules` (Cosmos DB) [only one shared between other services]

- **Write Containers**:

  - `dhwalk_changes` (Cosmos DB)

- **Events stored in the event store**:

  - CreateTeam
  - UpdateTeam
  - DeleteTeam
  - AssignedToTeam
  - UnassignedFromTeam

- **Read Tables/Containers affected**:

  - `zoning` (SQL)
  - `teamleader_team_crew_by_teamleader_legacy_site` (Cosmos DB)

## 4. Container and table Definition: purpose and partitions keys

### 4.1 Command-Side Containers (Cosmos DB)

| Container Name       | Purpose                                                       | Partition Key | Source Microservice      |
| -------------------- | ------------------------------------------------------------- | ------------- | ------------------------ |
| operator_updates     | Stores events related to operator creation or deletion        | /id           | MS1: Operator Management |
| employee-assignments | Stores all assignment events (to employee, team, workstation) | /id           | MS5: Employee Assignment |
| workstation-changes  | Stores events related to workstation creation or deletion     | /id           | MS4: Workstation         |
| dhwalk_changes       | Stores all DH Walk events                                     | /id           | MS6: DH Walk             |
| operator_skills      | Stores events related to operator skills creation or deletion | /id           | MS3: Operator Skills     |

### 4.2 Read-Side Containers/Tables

#### 4.2.1 SQL Tables

| Table Name            | Purpose                                           | primary key | Source Microservice      |
| --------------------- | ------------------------------------------------- | ----------- | ------------------------ |
| aptiv_active_employee | Central employee information                      | id (uuid)   | MS1: Operator Management |
| aptiv_new_hires       | Tracks new employees not yet in Workday           | id (uuid)   | MS1: Operator Management |
| operator_skills       | Tracks operator qualifications and certifications | id (uuid)   | MS3: Operator Skills     |
| zoning                | Maintains zone assignments and hierarchy          | id (uuid)   | MS6: DH Walk             |

#### 4.2.2 Cosmos DB Read Containers

| Container Name                                       | Purpose                                                          | Partition Key           |
| ---------------------------------------------------- | ---------------------------------------------------------------- | ----------------------- |
| user_role_hierarchy_by_legacy_site                   | Maintains organizational hierarchy                               | /legacy_site_id         |
| user_role_hierarchy_by_role                          | Maintains organizational hierarchy                               | /role                   |
| user_validation_by_legacy_site                       | Validation data for users, skills, etc.                          | /legacy_site_id         |
| user_validation_by_site                              | Validation data for users, skills, etc.                          | /site                   |
| subordinate_role_counts_by_legacy_site               | Tracks count of subordinates by role                             | /legacy_site_id         |
| teamleader_team_crew_by_teamleader_legacy_site       | Team structure under each team leader                            | /teamleader_legacy_site |
| teamleader_line_assignment_by_teamleader_legacy_site | Workstation assignments under team leaders                       | /teamleader_legacy_site |
| teamleader_line_assignment_by_site                   | Workstation assignments under team leaders                       | /site                   |
| workstation_by_customer                              | contains information about each workstation and its requirements | /customer               |
| workstation_by_station_id                            | contains information about each workstation and its requirements | /station_id             |
| ms_stations_by_department                            | contain information about each Manufacturing Station             | /department             |
| assignment_rules                                     | configuration for assignment rules                               | /country                |

### 4.3 Microservice Architecture Overview

```mermaid
graph TD
    MS1[MS1: Operator Management] -->|Command| WC1[operator_updates]
    MS3[MS3: Operator Skills] -->|Command| WC2[skills_validation]
    MS2[MS2: Direct Dependents] -->|Command| WC3[create_team]
    MS2 -->|Command| WC4[zoning_updates]
    MS5[MS5: Operator Assignment] -->|Command| WC5[employee-assignments]

    WC1 -->|Change Feed| SB1[Service Bus]
    WC2 -->|Change Feed| SB1
    WC3 -->|Change Feed| SB1
    WC4 -->|Change Feed| SB1
    WC5 -->|Change Feed| SB1

    SB1 --> WS[Write Service]

    WS -->|Updates| RM1[SQL Tables]
    WS -->|Updates| RM2[Cosmos DB Read Models]
```

### 4.4 Data Flow Overview

```mermaid
graph TD
    MS[Microservices] --> EVENT[Event Store]
    EVENT --> SB[Service Bus]
    SB --> WRITE[Write Service]

    WRITE --> SQL[SQL Tables]
    WRITE --> COSMOS[Cosmos DB Read Models]

    SQL --- SQL_LIST["aptiv_active_employee, aptiv_new_hires, operator_skills, zoning"]
    COSMOS --- COSMOS_LIST["user_role_hierarchy, user_validation, teamleader_team_crew, teamleader_line_assignment, workstation, subordinate_role_counts"]
```

## 5. Read Models

### 5.1 SQL Read Models

#### 5.1.1 aptiv_active_employee

| Column Name       | Data Type | Description                                               |
| ----------------- | --------- | --------------------------------------------------------- |
| id                | STRING    | uuid identifier                                           |
| legacy_site       | STRING    | unique employee identifier to be used in mapping purposes |
| workday_id        | STRING    | Workday identifier (nullable for non-Workday records)     |
| legacy_id         | INTEGER   | matricule of employee                                     |
| site              | STRING    | Employee’s site (e.g., "MAR Morocco 3")                   |
| siteResponsibleId | STRING    | id of the trainer responsible at the site                 |
| department        | STRING    | Department name                                           |
| role              | STRING    | Derived role (e.g., Hr. manager, Team Leader, Operator)   |
| firstname         | STRING    | First name                                                |
| lastname          | STRING    | Last name                                                 |
| email             | STRING    | employee's email                                          |
| business_title    | STRING    | Full job title                                            |
| category          | STRING    | Employee category (Salaried, Direct/Indirect Hourly)      |
| hire_date         | DATE      | Hire date                                                 |

#### 5.1.2 aptiv_new_hires

| Column Name | Data Type | Description                                               |
| ----------- | --------- | --------------------------------------------------------- |
| id          | STRING    | uuid identifier                                           |
| legacy_site | STRING    | unique employee identifier to be used in mapping purposes |
| new_hire_id | INTEGER   | Matricula of the new hire; equivalent to legacy ID        |
| first_name  | STRING    | First name                                                |
| last_name   | STRING    | Surname                                                   |
| hiring_date | DATE      | Hiring date                                               |
| category    | STRING    | Employee category                                         |
| department  | STRING    | Department code or name                                   |
| function    | STRING    | Employee role                                             |
| Site        | STRING    | New hire’s site                                           |
| in_workday  | STRING    | Flag indicating if the new hire is in Workday             |

#### 5.1.3 operator_skills

| Column Name        | Data Type | Description                                               |
| ------------------ | --------- | --------------------------------------------------------- |
| id                 | STRING    | uuid identifier                                           |
| legacy_site        | STRING    | unique employee identifier to be used in mapping purposes |
| workday_id         | STRING    | Workday identifier (nullable for non-Workday records)     |
| legacy_id          | INTEGER   | matricule of employee                                     |
| name               | STRING    | employee fullname                                         |
| Site               | STRING    | Employee's site                                           |
| qualification_type | STRING    | Employee's qualification_type                             |
| skill              | STRING    | skill type within project                                 |
| process_type       | STRING    | Employee's process_type                                   |

#### 5.1.4 zoning

| Column Name         | Data Type | Description                  |
| ------------------- | --------- | ---------------------------- |
| uuid                | STRING    | uuid                         |
| customer            | STRING    | customer name                |
| projet              | STRING    | project name                 |
| famille             | STRING    | family name                  |
| value_stream        | STRING    | value_stream                 |
| area                | STRING    | Area                         |
| Coor_legacy_site_id | STRING    | unique ID of the coordinator |
| Coor_fullname       | STRING    | the coordinator fullname     |
| SL_legacy_site_id   | STRING    | unique ID of the shiftleader |
| SL_fullname         | STRING    | the shiftleader fullname     |
| TL_legacy_site_id   | STRING    | unique ID of the teamleader  |
| TL_fullname         | STRING    | the teamleader fullname      |
| team_name           | STRING    | the name of the team         |
| department_Coor     | STRING    | the name of the department   |
| department_SL       | STRING    | the name of the department   |
| department_TL       | STRING    | the name of the department   |
| site                | STRING    | the name of the site         |

### 5.2 Cosmos DB Read Models

#### 5.2.1 user_role_hierarchy [user_role_hierarchy_by_legacy_site, user_role_hierarchy_by_role]

```typescript
{
  id: string,
  legacy_site_id: string,
  fullname: string,
  department: string,
  site: string,
  role: string,
  manager_legacy_site_id: string,
  subordinate_crew: [
        {
          sub_legacy_site_id: string,
          sub_legacy_id: number,
          sub_fullname: string,
          sub_role: string,
          sub_role_status: string,
          sub_department: string,
          in_workday: boolean,
          category: string,
          contract_type: string,
          skills: [string],
        }
      ]
}
```

#### 5.2.2 subordinate_role_counts [subordinate_role_counts_by_legacy_site]

```typescript
{
  id: string,
  legacy_site_id: string,
  role: string,
  site: string,
  subordinate_roles_counts: [
    {
      subordinate_role: string,
      subordinate_role_counts: number
    }
  ]
}

```

#### 5.2.3 user_validation [user_validation_by_legacy_site, user_validation_by_site]

```typescript
{
  id: string,
  legacy_site_id: string,
  legacy_id: number,
  first_name: string,
  last_name: string,
  subordinate_id: [string],
  skills: [string],
  category: string,
  department: string,
  role: string,
  site: string,
  in_workday: boolean,
  is_rework: boolean,
  is_containment: boolean, // check if the employee has role "containment operator"
}
```

#### 5.2.4 teamleader_team_crew [teamleader_team_crew_by_teamleader_legacy_site]

```typescript
{
  id: string,
  teamleader_legacy_site: string,
  teamleader_fullname: string,
  team: string,
  department: string,
  department_type: string,
  site: string,
  operators : [
    {
      legacy_site: string,
      legacy_id: number,
      first_name: string,
      last_name: string,
      role: string,
      departement: string,
      skills: string[],
      is_assigned_to_workstation: boolean,
    }
  ]
}
```

#### 5.2.5 teamleader_line_assignment [teamleader_line_assignment_by_teamleader_legacy_site, teamleader_line_assignment_by_site]

```typescript
{
  id: string,
  teamleader_legacy_site: string,
  teamleader_fullname: string,
  customer: string,
  projet: string,
  famille: string,
  value_stream: string,
  area: string,
  team: string,
  station: string,
  station_role: string,
  site : string,
  operator_legacy_site: string,
  operator_legacy_id: number,
  operator_first_name: string,
  operator_last_name: string,
  operator_skills: string[],
  me_definition_version: string,
  department: string,
  effective_date: string,
  station_id: string,
}
```

#### 5.2.6 workstation [workstation_by_customer, workstation_by_station_id]

```typescript
{
  id: string, // workstation id - cosmos db id
  customer: string,
  projet: string,
  famille: string,
  value_stream: string,
  area: string,
  station: string,
  site: string,
  department: string,
  role_station: string,
  skills: string[],
  criticity: string,
  capacity: number,
  is_polyvalent: boolean,
  is_rework: boolean,
  is_containment: boolean, // check if the Workstation name is "Containment"
  has_me_structure_role: boolean, // check if the Workstation has ME Structure
  effective_date: string,
  me_definition_version: string,
  station_id: string,
  is_deleted: boolean, // Soft Delete: For ME Structure Stations (derived from ME Skills), it is always false. For Manufacturing Structure Stations, it is false when created but can later be set to true if the responsible employee deletes it.
}
```

#### 5.2.7 ms_stations [ms_stations_by_department]

```typescript
{
  id: string,
  department: string,
  station_role: string,
  station: string,
  user_role: [string],
  is_polyvalent: boolean,
  is_rework: boolean,
  is_containment: boolean,
  country: string
}
```

#### 5.2.8 assignment_rules [assignment_rules]

```typescript
{
  id: string,
  assignerRole: string,
  assignToRole: string,
  assigneeRoles: string[],
  assignmentType: {
    toEmployee: boolean,
    toTeam: boolean,
    toWorkstation: boolean,
  },
  validationRules: {
    employeeToEmployee: {
      requiresDirectHierarchyBetweenAssignerAndAssignTo: boolean,
      requiresDirectHierarchyBetweenAssignToAndAssignee: boolean,
      requiresAssignerAndAssignToSameDepartment: boolean,
      requiresAssignToAndAssigneeSameDepartment: boolean,
      requiresAssignerAndAssignToSameSite: boolean,
      requiresAssigneeRegistrationInWorkday: boolean,
      indirectEmployees: boolean,
      excludeFirstLevelNewEmployees: boolean,
    },
    employeeToTeam: {
      requiresDirectHierarchyBetweenAssignerAndAssignee: boolean,
      requiresAssignerAndAssigneeSameDepartment: boolean,
      requiresAssigneeRegistrationInWorkday: boolean,
    },
    employeeToWorkstation: {
      requiresDirectHierarchyBetweenAssignerAndAssignee: boolean,
      requiresAssignerAndAssigneeSameDepartment: boolean,
      requiresAssigneeRegistrationInWorkday: boolean,
      requiresAssigneeAndWorkstationSkillsMatching: boolean,
      allowedToAssignToMeStructureStations: boolean,
    },
  },
  lineAssignmentFilterConditions: {
    filterByAssignment: boolean,
    filterByDepartment: boolean,
    filterBySite: boolean,
    canManageContainmentOperators: boolean,
  },
  isActive: boolean,
  country: string,
}
```

## 6. Event Catalog

### 6.1 Operator Management Events (MS1)

| Event Type      | Description                         | Source Container | Target Containers/Tables to update |
| --------------- | ----------------------------------- | ---------------- | ---------------------------------- |
| OperatorCreated | New operator record created         | operator_updates | `aptiv_new_hires`                  |
| OperatorDeleted | Operator marked as deleted/inactive | operator_updates | `aptiv_new_hires`                  |

### 6.2 Operator Skills Management Events (MS3 - Operator Skills)

| Event Type            | Description            | Source Container             | Target Containers/Tables to update                           |
| --------------------- | ---------------------- | ---------------------------- | ------------------------------------------------------------ |
| OperatorSkillsUpdated | update operator skills | a microservice from Module 2 | `operator_skills` (updating in microservice operator skills) |

### 6.3 Assignment Events (MS5 - Employee Assignment)

| Event Type           | Description                                              | Source Container     | Target Containers/Tables to update                                                                                                                                                                                                                                                                                                                                     |
| -------------------- | -------------------------------------------------------- | -------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| AssignedToEmployee   | Employee assigned from an employee to another employee   | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `subordinate_role_counts_by_legacy_site`, `aptiv_active_employee`, `aptiv_new_hires`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |
| ReassignedToEmployee | Employee reassigned from an employee to another employee | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `subordinate_role_counts_by_legacy_site`, `aptiv_active_employee`, `aptiv_new_hires`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |

| ReassignedBackupToShiftLeader | backup operator reassigned from a team leader to shift leader | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `subordinate_role_counts_by_legacy_site`, `aptiv_active_employee`, `aptiv_new_hires`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |
| AssignedToTeam | Employee assigned to a team | employee-assignments | `teamleader_team_crew_by_teamleader_legacy_site` |
| ReassignedToTeam | Employee reassigned from a team to another team | employee-assignments | `teamleader_team_crew_by_teamleader_legacy_site` |
| AssignedToWorkstation | Employee assigned to a workstation | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `subordinate_role_counts_by_legacy_site`, `aptiv_active_employee`, `aptiv_new_hires`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |
| ReassignedToWorkstation | Employee reassigned from one workstation to another | employee-assignments | `user_role_hierarchy_by_role`, `user_role_hierarchy_by_legacy_site`, `subordinate_role_counts_by_legacy_site`, `aptiv_active_employee`, `aptiv_new_hires`, `user_validation_by_legacy_site`, `user_validation_by_site`, `teamleader_team_crew_by_teamleader_legacy_site`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site` |

### 6.4 Workstation Events (MS4 - Workstation Management)

| Event Type         | Description          | Source Container    | Target Containers/Tables to update                                                                                                                                                                     |
| ------------------ | -------------------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| WorkstationCreated | create a workstation | workstation-changes | `workstation_by_customer`, `workstation_by_station_id`                                                                                                                                                 |
| WorkstationDeleted | delete a workstation | workstation-changes | `workstation_by_customer`, `workstation_by_station_id`, `teamleader_line_assignment_by_teamleader_legacy_site`, `teamleader_line_assignment_by_site`, `teamleader_team_crew_by_teamleader_legacy_site` |

### 6.5 Zoning Events (MS6 - DH Walk)

| Event Type         | Description     | Source Container | Target Containers/Tables to update               |
| ------------------ | --------------- | ---------------- | ------------------------------------------------ |
| createTeam         | create new team | dhwalk_changes   | `teamleader_team_crew_by_teamleader_legacy_site` |
| updateTeam         | update a team   | dhwalk_changes   | `teamleader_team_crew_by_teamleader_legacy_site` |
| deleteTeam         | delete a team   | dhwalk_changes   | `teamleader_team_crew_by_teamleader_legacy_site` |
| AssignedToTeam     |                 | dhwalk_changes   | `zoning`                                         |
| UnassignedFromTeam |                 | dhwalk_changes   | `zoning`                                         |

## 7. Event Schema Details

### 7.1 Base Event Schema

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "createdBy": "user-id",
  "eventType": "OperationEventName",
  "timestamp": "2023-01-01T00:00:00Z",
  "site": "site1",
  "payload": {} // Event-specific payload
}
```

### 7.2 Events Payload

#### Microservice 1: Operator Management

Payload for **OperatorCreated**:

```json
  "payload": {
    "firstName": "John",
    "lastName": "Doe",
    "siteId": "SITE-001",
    "department": "Manufacturing",
    "hiringDate": "2024-01-01T00:00:00Z",
    "legacyId": "LEG-001",
    "legacySiteId": "LSITE-001",
    "categories": "DH",
    "operatorFunction": "Senior Operator",
    "source": "CW"
  }
```

Payload for **OperatorDeleted**:

```json
  "payload":{
    "legacyId": "OP123",
    "siteId": "SITE-001",
    "department": "Manufacturing",
    "deletedAt": "2024-01-01T00:00:00Z",
    "deletedBy": "<EMAIL>",
    "reason": "User requested deletion",
    "lastKnownState": {
      "firstName": "John",
      "lastName": "Doe",
      "skills": ["Welding", "Assembly"],
      "source": "CW"}
  }
```

#### Microservice: Employee Assignment

Payload for **AssignedToEmployee** & **ReassignedToEmployee**

```json
  "payload": {
    "assigner": {
      "legacySiteId": "45_MOROCCO MAR 1",
      "legacyId": 45,
      "role": "shift leader",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly"
    },
    "assignTo": {
      "legacySiteId": "46_MOROCCO MAR 1",
      "legacyId": 46,
      "role": "shift leader",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly"
    },
    "assignee": {
      "legacySiteId": "47_MOROCCO MAR 1",
      "legacyId": 47,
      "role": "operator",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly",
      "category": "direct hourly",
      "skills": ["Soudage", "Ultrasonic"]
    }
  }
```

Payload for **ReassignedBackupToShiftLeader**:

```json
  "payload": {
    "assigner": {
      "legacySiteId": "126_MOROCCO MAR 3",
      "legacyId": 126,
      "role": "shift leader",
      "firstName": "Mohamed",
      "lastName": "Tazi",
      "department": "assembly"
    },
    "assignTo": {
      "legacySiteId": "126_MOROCCO MAR 3",
      "legacyId": 126,
      "role": "shift leader",
      "firstName": "Mohamed",
      "lastName": "Tazi",
      "department": "assembly"
    },
    "unassignedFrom": {
      "legacySiteId": "345_MOROCCO MAR 3",
      "legacyId": 345,
      "role": "team leader",
      "firstName": "Mohamed",
      "lastName": "Tazi",
      "department": "assembly"
    },
    "assignee": {
      "legacySiteId": "789_MOROCCO MAR 3",
      "legacyId": 789,
      "role": "operator",
      "firstName": "Fatima",
      "lastName": "Benani",
      "department": "assembly",
      "category": "direct hourly",
      "skills": ["Soudage", "Ultrasonic"],
      "roleStatus": "backup shiftleader"
    }
  }
```

Payload for **AssignedToTeam**:

```json
  "payload": {
    "assigner": {
      "legacySiteId": "45_MOROCCO MAR 1",
      "legacyId": 45,
      "role": "team leader",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly"
    },
    "assignee": {
      "legacySiteId": "46_MOROCCO MAR 1",
      "legacyId": 46,
      "role": "operator",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly",
      "category": "direct hourly",
      "skills": ["Soudage", "Ultrasonic"]
    },
    "team": {
      "teamName": "Team 1",
      "teamLeaderLegacySiteId": "46_MOROCCO MAR 1"
    }
  }
```

Payload for **ReassignedToTeam**:

```json
  "payload": {
    "assigner": {
      "legacySiteId": "45_MOROCCO MAR 1",
      "legacyId": 45,
      "role": "team leader",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly"
    },
    "assignee": {
      "legacySiteId": "46_MOROCCO MAR 1",
      "legacyId": 46,
      "role": "operator",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly",
      "category": "direct hourly",
      "skills": ["Soudage", "Ultrasonic"]
    },
    "team": {
      "teamName": "Team 1",
      "teamLeaderLegacySiteId": "46_MOROCCO MAR 1"
    },
    "oldTeam": {
      "teamName": "Team 2",
      "teamLeaderLegacySiteId": "46_MOROCCO MAR 1"
    }
  }
```

Payload for **AssignedToWorkstation**:

```json
  "payload": {
    "assigner": {
      "legacySiteId": "45_MOROCCO MAR 1",
      "legacyId": 526,
      "role": "team leader",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly"
    },
    "workstation": {
      "customer": "Customer A",
      "project": "Project A",
      "family": "Family A",
      "valueStream": "Value Stream A",
      "meDefinition": "version 1",
      "effective_date": "2025-03-23",
      "area": "Area A",
      "station": "CELL 1",
      "roleStation": "ME STRUCTURE",
      "department": "assembly",
      "stationId": "uuid"
    },
    "assigned": {
      "legacySiteId": "46_MOROCCO MAR 1",
      "legacyId": 526,
      "role": "operator",
      "firstName": "John",
      "lastName": "Smith",
      "department": "assembly",
      "category": "direct hourly",
      "skills": ["soudage", "ultrasonic"],
      "team": "Team 1",
      "teamLeaderLegacySite": "45_MOROCCO MAR 1",
      "teamLeaderRole": "team leader",
      "teamLeaderFirstName": "John",
      "teamLeaderLastName": "Doe",
      "teamLeaderDepartment": "assembly"
    }
  }
```

Payload for **ReassignedToWorkstation**:

```json
  "payload": {
    "assigner": {
      "legacySiteId": "45_MOROCCO MAR 1",
      "legacyId": 526,
      "role": "team leader",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly"
    },
    "workstation": {
      "customer": "Customer A",
      "project": "Project A",
      "family": "Family A",
      "valueStream": "Value Stream A",
      "meDefinition": "version 1",
      "effective_date": "2025-03-23",
      "area": "Area A",
      "station": "CELL 1",
      "roleStation": "ME STRUCTURE",
      "department": "assembly",
      "stationId": "uuid"
    },
    "assigned": {
      "legacySiteId": "46_MOROCCO MAR 1",
      "legacyId": 526,
      "role": "operator",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly",
      "category": "direct hourly",
      "skills": ["soudage", "ultrasonic"],
      "team": "team 1",
      "teamLeaderLegacySite": "45_MOROCCO MAR 1",
      "teamLeaderRole": "team leader",
      "teamLeaderFirstName": "John",
      "teamLeaderLastName": "Doe",
      "teamLeaderDepartment": "assembly"
    },
    "unassigned": {
      "legacySiteId": "46_MOROCCO MAR 1",
      "legacyId": 526,
      "role": "operator",
      "firstName": "John",
      "lastName": "Doe",
      "department": "assembly",
      "team": "team 1",
      "teamLeaderLegacySite": "45_MOROCCO MAR 1"
    }
  }
```

#### Microservice: Workstation

Payload for **WorkstationCreated**:

```json

  "payload": {
    "customer": "AUDI",
    "project": "Project X",
    "family": "Harness Assembly",
    "valueStream": "VS-1234",
    "area": "Production Area 5",
    "meDefinitionVersion": "2.1",
    "effectiveDate": "2023-08-01",
    "stationId": "aafb9213-8a37-436b-8ac9-24ee477f4b5c",
    "station": "Rework",
    "stationRole": "Manufacturing Structure",
    "department": "assembly",
    // assignedOperator can be `null` if the responsible didn't want to assign an operator to the workstation during the creation
    // if it is not null, we must create the workstation in the workstation containers, and add assigned to line assignment
    "assignedOperator": {
      "legacySite": "4455_MOROCCO MAR 1",
      "legacyId": 1234567890,
      "first_name": "John",
      "last_name": "Doe",
      "role": "Operator",
      "skills": ["Soudage", "Ultrasonic"],
      "department": "assembly",
      "team": "Team 1",
      "teamLeaderLegacySite": "96_MOROCCO MAR 1"
    }
  }

```

Payload for **WorkstationDeleted**:

```json
  "payload": {
    "customer": "AUDI",
    "project": "Project X",
    "family": "Harness Assembly",
    "valueStream": "VS-1234",
    "area": "Production Area 5",
    "meDefinitionVersion": "2.1",
    "effectiveDate": "2023-08-01",
    "stationId": "985d7f09-5363-476e-8773-ef77c13dd6cf",
    "station": "Polyvalent",
    "stationRole": "Manufacturing Structure",
    "department": "cutting",
    // unassigned can be `null` if the workstation was empty during the deletion, but if it is not null, it means that the workstation had an operator assigned to it
    "unassigned": {
      "legacySite": "4455_MOROCCO MAR 1",
      "firstName": "John",
      "lastName": "Doe",
      "role": "Operator",
      "department": "cutting",
      "team": "Team Alpha",
      "teamLeaderLegacySite": "96_MOROCCO MAR 1"
    }
  }
```

#### Microservice: DH Walk

Payload for **CreateTeam**:

```json
  "payload": {
    "TL_legacy_site_id":"xxxxx",
    "TL_fullname":"xxxxx",
    "team_name":"xxxxx",
  }
```

Payload for **UpdateTeam**:

```json
  "payload": {
    "TL_legacy_site_id":"xxxxx",
    "TL_fullname":"xxxxx",
    "team_name":"xxxxx",
  }
```

Payload for **DeleteTeam**:

```json
  "payload": {
    "TL_legacy_site_id":"xxxxx",
    "team_name":"xxxxx",
  }
```

Payload for **AssignedToTeam**:

```json
"payload":{
    "customer":"xxxxx",
    "project":"xxxxx",
    "family":"xxxxx",
    "value_stream":"xxxxx",
    "area":"xxxxx",
    "Coor_legacy_site_id":"xxxxx",
    "Coor_fullname":"xxxxx",
    "SL_legacy_site_id":"xxxxx",
    "SL_fullname":"xxxxx",
    "TL_legacy_site_id":"xxxxx",
    "TL_fullname":"xxxxx",
    "team_name":"xxxxx",
}
```

Payload for **UnassignedFromTeam**:

```json
  "payload": {
    "project": "xxxxx",
    "family": "xxxxx",
    "value_stream": "xxxxx",
    "area": "xxxxx",
    "team_name": "xxxxx",
    "role": "xxxxx",
    "employee_id": "xxxxx",
    "employee_name": "xxxxx"
  }
```

## 8. Write Service Design

### 8.1 Service Components

- **Event Listener**: Subscribes to Service Bus topics
- **Event Deserializer**: Converts messages to event objects
- **Event Router**: Routes events to appropriate handlers
- **Event Handlers**: Type-specific handlers for each event
- **Transaction Manager**: Ensures atomic updates across tables
- **Repository Layer**: Interfaces with read databases
- **Error Handler**: Manages retries and dead-letter scenarios

### 8.2 Event Processing Flow

1. Event Listener receives message from Service Bus
2. Message is deserialized into event object
3. Event Router determines appropriate handler
4. Event Handler processes the event, calling repositories
5. Transaction Manager ensures atomic updates
6. Success/failure status returned to Event Listener
7. Message completed or abandoned based on result

## 9. Error Handling and Recovery

### 9.1 Retry Strategies

- **Immediate Retry**: For transient errors
- **Delayed Retry**: With exponential backoff
- **Dead-Letter**: After max retry attempts
- **Circuit Breaker**: For system-wide issues

### 9.2 Consistency Recovery

- **Reconciliation Jobs**: Periodic comparison of event store vs. read models
- **Manual Intervention Tools**: Admin interface for fixing inconsistencies
- **Event Replay**: Ability to replay events from a specific point

## 10. Advantages of CQRS Approach

- **Performance**: Read and write operations optimized independently
- **Scalability**: Read and write services can scale independently
- **Flexibility**: Read models tailored to specific query needs
- **Audit Trail**: Complete history of changes in event store
- **Business Logic**: Domain rules enforced at source
- **Resilience**: Robust error handling and recovery
- **Maintainability**: Clear separation of concerns

## 11. Implementation Considerations

- **Event Versioning**: Strategy for handling schema evolution
- **Eventual Consistency**: Managing expectations for read model updates
- **Idempotency**: Ensuring events can be processed multiple times safely
- **Monitoring**: Comprehensive metrics for system health
- **Testing**: Strategies for testing event handlers
