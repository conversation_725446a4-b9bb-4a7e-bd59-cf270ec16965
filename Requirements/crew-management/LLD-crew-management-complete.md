# Connected Workers - Crew Management System

## 1. System Overview

### 1.1 Purpose
The Crew Management System provides a centralized platform for managing manufacturing facility operators, their skills, assignments, and organizational structure. It integrates with Workday for employee data and DHwolk Excel files for team assignments.

### 1.2 Core Domains

```mermaid
graph TD
    A[Crew Management] --> B[Operator Management]
    A --> C[Team Structure]
    A --> D[Skills & Training]
    B --> E[Onboarding]
    B --> F[Transfers]
    C --> G[Hierarchy]
    C --> H[Assignments]
    D --> I[Skill Tracking]
    D --> J[Certifications]
```

## 2. Business Workflows

### 2.1 New Operator Onboarding

```mermaid
sequenceDiagram
    participant TKS
    participant TR as Training Responsible
    participant T as Trainer
    participant SL as Shift Leader
    participant TL as Team Leader
    
    TKS->>TR: Create New Operator
    Note over TKS,TR: source='CW', isNew=true
    TR->>T: Assign to Trainer
    T->>T: Conduct Basic Training
    T->>T: Validate Initial Skills
    T->>SL: Request Assignment
    SL->>TL: Assign to Team Leader
    TL->>TL: Finalize Assignment
```

#### Process Steps:
1. **TKS Role Actions**
   - Create operator record manually or via Excel import
   - Set initial flags: source='CW', isNew=true
   - Capture basic information:
     - Employee ID
     - Name
     - Department
     - Business Title

2. **Training Responsible Actions**
   - Receive new operator notification
   - Review department assignment
   - Assign appropriate trainer
   - Initialize training plan

3. **Trainer Actions**
   - Conduct basic training (OJT)
   - Assess initial skills
   - Record skill validations
   - Request team assignment

4. **Shift Leader Actions**
   - Review operator qualifications
   - Determine appropriate team
   - Assign to Team Leader
   - Set initial workstation

### 2.2 Operator Transfer Workflows

#### Within Same Department
```mermaid
sequenceDiagram
    participant TL1 as Team Leader 1
    participant SL as Shift Leader
    participant TL2 as Team Leader 2
    participant Skills as Skills Service
    
    TL1->>SL: Request Transfer
    SL->>Skills: Validate Skills
    Skills-->>SL: Skill Validation
    SL->>TL2: Approve Transfer
    TL2->>TL2: Update Assignment
```

#### Cross-Department Transfer
```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant SL1 as Shift Leader 1
    participant SL2 as Shift Leader 2
    participant HR as HR Development
    
    TL->>SL1: Initiate Transfer
    SL1->>HR: Submit Movement Notice
    HR->>SL2: Request Approval
    SL2->>HR: Approve Transfer
    HR->>SL1: Confirm Transfer
    SL1->>TL: Update Assignment
```

## 3. Data Architecture

### 3.1 Core Data Models

#### Employee Schema
```typescript
interface Employee {
    employeeId: string;
    externalId?: string;  // Workday ID
    firstName: string;
    lastName: string;
    department: string;
    businessTitle: string;
    source: 'CW' | 'WORKDAY';
    isNew: boolean;
    status: 'ACTIVE' | 'INACTIVE' | 'TRANSFERRED';
    metadata: {
        createdAt: Date;
        updatedAt: Date;
        createdBy: string;
        lastModifiedBy: string;
    };
}
```

#### Team Structure Schema
```typescript
interface Team {
    teamId: string;
    name: string;
    valueStreamId: string;
    teamLeaderId: string;
    shiftLeaderId: string;
    status: 'ACTIVE' | 'INACTIVE';
    members: TeamMember[];
    workstations: Workstation[];
    metadata: {
        createdAt: Date;
        updatedAt: Date;
    };
}

interface TeamMember {
    employeeId: string;
    role: 'OPERATOR' | 'TEAM_LEAD' | 'SHIFT_LEAD';
    startDate: Date;
    endDate?: Date;
    isActive: boolean;
}

interface Workstation {
    workstationId: string;
    name: string;
    capacity: number;
    requiredSkills: Skill[];
    currentAssignments: Assignment[];
}
```

#### Skills Schema
```typescript
interface OperatorSkills {
    operatorId: string;
    skills: {
        skillId: string;
        name: string;
        level: number;  // 1-5
        validatedBy: string;
        validatedAt: Date;
        status: 'ACTIVE' | 'EXPIRED' | 'PENDING';
        history: SkillHistory[];
    }[];
    certifications: {
        certId: string;
        name: string;
        issuedDate: Date;
        expiryDate?: Date;
        issuedBy: string;
        status: 'ACTIVE' | 'EXPIRED';
    }[];
    training: {
        programId: string;
        status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
        progress: number;
        startDate: Date;
        completionDate?: Date;
    }[];
}
```

### 3.2 Database Schema

#### Relational Tables (Azure SQL)

```sql
-- Employees
CREATE TABLE Employees (
    EmployeeId UNIQUEIDENTIFIER PRIMARY KEY,
    ExternalId NVARCHAR(50),
    FirstName NVARCHAR(100),
    LastName NVARCHAR(100),
    Department NVARCHAR(100),
    BusinessTitle NVARCHAR(100),
    Source NVARCHAR(20),
    IsNew BIT,
    Status NVARCHAR(20),
    CreatedAt DATETIME2,
    UpdatedAt DATETIME2,
    CreatedBy NVARCHAR(100),
    LastModifiedBy NVARCHAR(100)
);

-- Teams
CREATE TABLE Teams (
    TeamId UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100),
    ValueStreamId UNIQUEIDENTIFIER,
    TeamLeaderId UNIQUEIDENTIFIER,
    ShiftLeaderId UNIQUEIDENTIFIER,
    Status NVARCHAR(20),
    CreatedAt DATETIME2,
    UpdatedAt DATETIME2,
    FOREIGN KEY (ValueStreamId) REFERENCES ValueStreams(ValueStreamId),
    FOREIGN KEY (TeamLeaderId) REFERENCES Employees(EmployeeId),
    FOREIGN KEY (ShiftLeaderId) REFERENCES Employees(EmployeeId)
);

-- Team Members
CREATE TABLE TeamMembers (
    TeamMemberId UNIQUEIDENTIFIER PRIMARY KEY,
    TeamId UNIQUEIDENTIFIER,
    EmployeeId UNIQUEIDENTIFIER,
    Role NVARCHAR(50),
    StartDate DATETIME2,
    EndDate DATETIME2,
    IsActive BIT,
    CreatedAt DATETIME2,
    UpdatedAt DATETIME2,
    FOREIGN KEY (TeamId) REFERENCES Teams(TeamId),
    FOREIGN KEY (EmployeeId) REFERENCES Employees(EmployeeId)
);

-- Workstations
CREATE TABLE Workstations (
    WorkstationId UNIQUEIDENTIFIER PRIMARY KEY,
    Name NVARCHAR(100),
    TeamId UNIQUEIDENTIFIER,
    Capacity INT,
    Status NVARCHAR(20),
    CreatedAt DATETIME2,
    UpdatedAt DATETIME2,
    FOREIGN KEY (TeamId) REFERENCES Teams(TeamId)
);

-- Assignments
CREATE TABLE Assignments (
    AssignmentId UNIQUEIDENTIFIER PRIMARY KEY,
    EmployeeId UNIQUEIDENTIFIER,
    WorkstationId UNIQUEIDENTIFIER,
    StartDate DATETIME2,
    EndDate DATETIME2,
    Status NVARCHAR(20),
    CreatedAt DATETIME2,
    UpdatedAt DATETIME2,
    FOREIGN KEY (EmployeeId) REFERENCES Employees(EmployeeId),
    FOREIGN KEY (WorkstationId) REFERENCES Workstations(WorkstationId)
);

-- Transfer Requests
CREATE TABLE TransferRequests (
    RequestId UNIQUEIDENTIFIER PRIMARY KEY,
    EmployeeId UNIQUEIDENTIFIER,
    FromTeamId UNIQUEIDENTIFIER,
    ToTeamId UNIQUEIDENTIFIER,
    RequestedBy UNIQUEIDENTIFIER,
    Status NVARCHAR(20),
    RequestDate DATETIME2,
    CompletionDate DATETIME2,
    Comments NVARCHAR(MAX),
    CreatedAt DATETIME2,
    UpdatedAt DATETIME2,
    FOREIGN KEY (EmployeeId) REFERENCES Employees(EmployeeId),
    FOREIGN KEY (FromTeamId) REFERENCES Teams(TeamId),
    FOREIGN KEY (ToTeamId) REFERENCES Teams(TeamId),
    FOREIGN KEY (RequestedBy) REFERENCES Employees(EmployeeId)
);
```

#### NoSQL Collections (Cosmos DB)

```typescript
// Operator Skills Document
{
    id: string;  // operatorId
    type: 'OPERATOR_SKILLS';
    employeeId: string;
    skills: [{
        skillId: string;
        name: string;
        level: number;
        validatedBy: string;
        validatedAt: string;
        status: string;
        history: [{
            date: string;
            level: number;
            changedBy: string;
            reason: string;
        }];
    }];
    certifications: [{
        id: string;
        name: string;
        issuedDate: string;
        expiryDate: string;
        issuedBy: string;
        status: string;
    }];
    training: [{
        programId: string;
        status: string;
        progress: number;
        startDate: string;
        completionDate: string;
    }];
    metadata: {
        createdAt: string;
        updatedAt: string;
        version: number;
    };
}
```

## 4. Service Architecture

### 4.1 Microservices Overview

```mermaid
graph TD
    A[API Gateway] --> B[Data Integration Service]
    A --> C[Crew Management Service]
    A --> D[Skills Service]
    B --> E[Azure Service Bus]
    C --> E
    D --> E
    B --> F[(Azure SQL)]
    C --> F
    D --> G[(Cosmos DB)]
```

### 4.2 Service Responsibilities

#### Data Integration Service
```typescript
interface IDataIntegrationService {
    // Workday Integration
    syncWorkdayData(): Promise<SyncResult>;
    validateWorkdayData(data: WorkdayData): ValidationResult;
    
    // DHwolk Processing
    processExcelFile(file: Buffer): Promise<ProcessingResult>;
    validateExcelFormat(file: Buffer): ValidationResult;
    
    // Reference Data
    updateReferenceData(type: ReferenceType, data: any): Promise<void>;
    getLatestReferenceData(type: ReferenceType): Promise<ReferenceData>;
}
```

#### Crew Management Service
```typescript
interface ICrewManagementService {
    // Operator Management
    createOperator(data: OperatorData): Promise<Operator>;
    updateOperator(id: string, data: OperatorData): Promise<Operator>;
    getOperator(id: string): Promise<Operator>;
    
    // Team Management
    createTeam(data: TeamData): Promise<Team>;
    assignOperator(operatorId: string, teamId: string): Promise<Assignment>;
    transferOperator(request: TransferRequest): Promise<TransferResult>;
    
    // Workstation Management
    assignToWorkstation(operatorId: string, workstationId: string): Promise<Assignment>;
    validateWorkstationCapacity(workstationId: string): Promise<ValidationResult>;
}
```

#### Skills Service
```typescript
interface ISkillsService {
    // Skill Management
    addSkill(operatorId: string, skill: SkillData): Promise<void>;
    updateSkillLevel(operatorId: string, skillId: string, level: number): Promise<void>;
    validateSkills(operatorId: string, workstationId: string): Promise<ValidationResult>;
    
    // Training Management
    startTraining(operatorId: string, programId: string): Promise<void>;
    updateTrainingProgress(operatorId: string, progress: TrainingProgress): Promise<void>;
    completeTraining(operatorId: string, programId: string): Promise<void>;
}
```

## 5. Integration Points

### 5.1 External Systems

#### Workday Integration
```typescript
interface WorkdayIntegration {
    // Employee Data
    getEmployeeUpdates(since: Date): Promise<EmployeeUpdate[]>;
    validateEmployeeData(data: EmployeeData): ValidationResult;
    
    // Organization Structure
    getDepartmentUpdates(): Promise<DepartmentUpdate[]>;
    getManagerialRelationships(): Promise<ManagerialRelationship[]>;
}
```

#### DHwolk Excel Integration
```typescript
interface DHwolkIntegration {
    // File Processing
    processTeamAssignments(file: Buffer): Promise<TeamAssignment[]>;
    processWorkstationMappings(file: Buffer): Promise<WorkstationMapping[]>;
    validateFileFormat(file: Buffer): ValidationResult;
}
```

### 5.2 Event Schema

```typescript
// Event Types
type EventType =
    | 'OPERATOR_CREATED'
    | 'OPERATOR_UPDATED'
    | 'TEAM_ASSIGNMENT_CHANGED'
    | 'TRANSFER_REQUESTED'
    | 'TRANSFER_COMPLETED'
    | 'SKILLS_UPDATED'
    | 'TRAINING_COMPLETED';

// Event Structure
interface Event {
    eventId: string;
    eventType: EventType;
    timestamp: string;
    payload: any;
    metadata: {
        correlationId: string;
        source: string;
        version: string;
    };
}
```

## 6. Security & Authorization

### 6.1 Role Definitions

```typescript
enum Role {
    TKS_RESPONSIBLE = 'TKS_RESPONSIBLE',
    TRAINING_RESPONSIBLE = 'TRAINING_RESPONSIBLE',
    SHIFT_LEADER = 'SHIFT_LEADER',
    TEAM_LEADER = 'TEAM_LEADER',
    TRAINER = 'TRAINER',
    OPERATOR = 'OPERATOR'
}

interface RolePermissions {
    [Role.TKS_RESPONSIBLE]: [
        'CREATE_OPERATOR',
        'VIEW_OPERATORS',
        'IMPORT_EXCEL'
    ];
    [Role.TRAINING_RESPONSIBLE]: [
        'ASSIGN_TRAINER',
        'VIEW_SKILLS',
        'UPDATE_TRAINING'
    ];
    [Role.SHIFT_LEADER]: [
        'MANAGE_TEAMS',
        'APPROVE_TRANSFERS',
        'VIEW_ALL_OPERATORS'
    ];
    [Role.TEAM_LEADER]: [
        'VIEW_TEAM',
        'REQUEST_TRANSFER',
        'UPDATE_WORKSTATION'
    ];
}
```
 