## 1. **Introduction**

This document consolidates the architectural decisions and specifications for **Module 3: Headcount Control & Time Keeping System (TKS)** within the **Connected Workers** program. It builds on the existing Module 1 microservices architecture, adding new services and workflows specifically for operator status tracking, overtime management, and shift-based planning.

**Key Objectives of Module 3**:
1. Manage **operator attendance** in real time (present, absent, in line, on bus, etc.).
2. Track **status changes** (planned/unplanned TLO, overtime, authorized leaves, accidents, etc.) and maintain a **historical log**.
3. Integrate with existing module 1 services (User Service, Notification Service, Workflow Engine, etc.) for **roles, approvals, and notifications**.
4. Provide a clear foundation for **low-level design** (database schemas, endpoint definitions, messaging topics, etc.).

---

## 2. **Existing Architecture (Module 1) Recap**

Below are the **core microservices** introduced in Module 1:

1. **User Service**  
   - Handles user data, authentication, role/permission checks, organizational hierarchy.

2. **Workflow Engine**  
   - Defines and executes workflow logic (approval chains, decision nodes, etc.).

3. **Workflow Service**  
   - Manages workflow instances; interacts with the Workflow Engine for routing tasks.

4. **Request Service**  
   - Handles the lifecycle of requests that move through approval workflows.

5. **Dynamic Form Service**  
   - Stores and validates dynamic forms, used by multiple modules (e.g., user input forms).

6. **Document Template Service**  
   - Manages document templates (e.g., .html or placeholders).

7. **Document Generation Service**  
   - Transforms templates into final documents (PDF, Word, etc.).

8. **Notification Service**  
   - Sends system notifications via email, SMS, or push messages.

**All communication** follows **synchronous HTTP** (REST) for direct queries/commands and **asynchronous** (e.g., Service Bus, Kafka, or Azure Queues) for event-based updates.

---

## 3. **New Roles & Responsibilities in TKS**

### 3.1 **Team Leader (TL)**
- **Submits** absence requests or overtime requests for operators in their team.
- **Performs** visual checks for presence (bus, plant, line).
- **Approves or rejects** certain immediate status changes (e.g., tardy arrivals).
- **Requests** end-of-shift unplanned overtime when needed.

### 3.2 **TKS Agent**
- **Validates** TKS data (attendance, overtime, leave).
- **Approves** exceptional or higher-level requests (e.g., large overtime hours).
- **Coordinates** with Team Leaders to ensure correct timekeeping.

### 3.3 **Nurse**
- **Updates** health-related statuses (maternity, accident at work, medical leave).
- **Uploads** supporting medical documents for relevant requests.
- **Coordinates** with TKS Agent or HR for validations.

### 3.4 **Operator**
- **Passive user** in terms of TKS; their statuses are updated by TLs/Nurses.
- **Receives** notifications about shift changes or approvals (overtime, leave).

### 3.5 **Guardhouse / Security**
- **Cross-checks** identity for badge issues.
- **Manages** manual overrides when an operator arrives without a badge.

---

## 4. **New Microservices in Module 3**

To complete the TKS requirements, we introduce (or extend) the following **microservices**:

### 4.1 **Operator Attendance & Status Service**
**Purpose**  
- Tracks and updates operator presence status (present, absent, delayed, etc.) in real time.  
- Maintains **status history** for auditing and reporting.

**Responsibilities**  
1. **Create/Update** attendance records based on badge scans, manual inputs, or bus device data.  
2. **Archive** every status change (e.g., present → TLO → absent).  
3. **Expose** REST APIs for retrieving operator attendance, possibly returning a timeline of statuses.  
4. **Publish** events to the Notification Service for urgent alerts (missing operator, unplanned TLO).

**Key Interactions**  
- **User Service**: Validate operator and Team Leader info.  
- **Notification Service**: Send alerts for absent or missing staff.  
- **Workflow Service**: For approvals on certain corrections (e.g., post-facto clock-in).

---

### 4.2 **Shift & Planning Service**
**Purpose**  
- Manages **shifts** (3 default per day) and aligns them with the **production calendar** (holidays, TLO days, etc.).  
- Integrates with existing **Working Plan** if available or extends it for advanced scheduling (e.g., weekly capacity planning).

**Responsibilities**  
1. **Define** or retrieve shift blocks (Day Shift, Night Shift, etc.) and link them to a calendar.  
2. **Assign** operators/teams to shifts, respecting skill sets or capacity.  
3. **Plan** or **adjust** any **overtime** within the shift scope (planned scenario).  
4. **Update** relevant microservices (Attendance, Overtime) with shift structures.

**Key Interactions**  
- **Operator Attendance & Status Service**: Validate if an operator is on the correct shift or if they are tardy.  
- **Notification Service**: Inform TLs/operators of shift changes or new planned overtime.

---

### 4.3 **Overtime Management Service**
**Purpose**  
- Controls the end-to-end lifecycle of **planned** and **unplanned** overtime (25%, 50%, 100%).  
- Implements approval workflows (Team Leader → TKS Agent → maybe HR).

**Responsibilities**  
1. **Create** or **cancel** overtime requests (Team Leader).  
2. **Approve** or **reject** requests (TKS Agent).  
3. **Integrate** with external payroll (e.g., "Optitime") after finalizing hours.  
4. Optionally **leverage** the existing Request Service + Workflow Service from Module 1 for multi-step approvals.

**Key Interactions**  
- **User Service**: Check roles and permissions.  
- **Notification Service**: Over time request updates (approved/rejected).  
- **Shift & Planning Service**: Mark planned overtime in the shift schedule.  
- **Operator Attendance & Status Service**: Reflect final overtime in daily attendance records.

---

### 4.4 **(Optional) TLO Management Service**
**Purpose**  
- Manages **Temporary Line Off** statuses (CTN = planned, CTP = unplanned).  
- Updates operators' statuses to TLO if lines are down.

**Responsibilities**  
1. **Record** TLO events from production environment or capacity study.  
2. **Notify** Attendance Service to set the relevant operators to TLO.  
3. **Archive** reasons for TLO (raw material shortage, maintenance, etc.).

**Key Interactions**  
- **Attendance & Status Service**: Switch operator statuses to TLO.  
- **Notification Service**: Alerts to TKS Agents, Team Leaders for line disruptions.  
- **Shift & Planning Service**: Possibly adjust shift planning if TLO spans multiple hours.

> **Note**: TLO logic can be merged into the **Attendance Service** if simpler.

---

## 5. **Communication and Responsibility Overview**

To maintain consistency with the existing architecture:

1. **HTTP/REST** used for:
   - Direct queries (operator attendance, shift info, overtime request details).
   - Synchronous commands (create request, update status, etc.).

2. **Asynchronous** (e.g., Azure Service Bus, RabbitMQ, Kafka) used for:
   - **Events** like "overtime.approved," "operator.absent," "unplannedTLO.created."
   - **Notifications** (publish event → Notification Service sends email/SMS).

3. **Data Storage**:
   - Each new TKS microservice **owns** its data (e.g., attendance logs, TLO records) in a separate schema or DB.
   - Large documents (scans, medical certificates) stored in **Blob Storage** (referenced by IDs).

---

## 6. **High-Level Diagrams**

### 6.1 **Extended Architecture Diagram**

```mermaid
flowchart LR
    subgraph Existing_Module_1_Services
        US[(User Service)]
        WFS[(Workflow Service)]
        RQS[(Request Service)]
        DFS[(Dynamic Form Service)]
        NTS[(Notification Service)]
        DGS[(Doc Gen Service)]
        DTS[(Doc Template Service)]
    end

    subgraph New_TKS_Services_Module_3
        TKS_A[Attendance & Status<br>Service]
        TKS_S[Shift & Planning<br>Service]
        TKS_O[Overtime Management<br>Service]
        TKS_T[Optional TLO Mgmt<br>Service]
    end

    %% Inter-Service Connections

    US -- "Check roles/identities" --> TKS_A
    US -- "Check roles/identities" --> TKS_O
    US -- "Check roles/identities" --> TKS_S
    US -- "Check roles/identities" --> TKS_T

    TKS_A -- "Push status changes" --> RQS
    TKS_A -- "Publish 'absent' event" --> NTS
    TKS_S -- "Shift data" --> TKS_A
    TKS_O -- "Overtime requests" --> RQS
    TKS_O -- "Publish approval events" --> NTS
    TKS_T -- "Line Off events" --> TKS_A

    RQS -- "Existing request workflow" --> WFS
    DFS -- "Optional forms" --> TKS_O
    DFS -- "Optional forms" --> TKS_T

    DGS -- "Doc generation" --> TKS_O
    DTS -- "Template mgmt" --> TKS_O

    %% Indicate usage of the same Notification, Workflow, etc.

```

### 6.2 **Example Flow: Unplanned Overtime Request**

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant TKS_O as Overtime Management Service
    participant WFS as Workflow Service
    participant RQS as Request Service
    participant NTS as Notification Service
    participant TKS_A as Attendance Service

    rect rgb(200,200,255)
    Note over TL,TKS_O: Synchronous REST
    TL->>TKS_O: POST /overtime-request (unplanned)
    TKS_O->>RQS: Create request record (if using Request Service)
    RQS->>WFS: Validate & Start Workflow
    end

    rect rgb(255,200,200)
    Note over TL,TKS_O: Asynchronous via queue
    WFS->>NTS: event: "overtime.approvalRequired"
    NTS->>TL: Send notification "Approval needed"
    TL->>WFS: Approves or Rejects (via UI)
    WFS->>NTS: event: "overtime.approved" or "overtime.rejected"
    NTS->>TL: Notification with final decision
    end

    alt Approved
        TKS_O->>TKS_A: Update attendance with extended shift
        TKS_O->>External Payroll?: Publish final OT hours
    end
```

## 7. **User Stories for Module 3**

### 7.1 **Attendance & Status**
- As a Team Leader, I want to mark an operator as absent in real time so that we can quickly identify missing personnel.
- As a Guardhouse Security, I want to override an operator's status when they forget their badge so that the system accurately reflects who is on site.

### 7.2 **Shift & Planning**
- As a Production Manager, I want to define weekly shifts for my operators so that they know when to report and we can track attendance properly.
- As a Team Leader, I want to view my assigned operators on each shift so that I can allocate resources efficiently.

### 7.3 **Overtime Management**
- As a Team Leader, I want to submit an unplanned overtime request before the end of the shift so that I can keep certain critical operators beyond normal hours.
- As a TKS Agent, I want to review and approve overtime requests so that the company remains compliant with labor rules.

### 7.4 **TLO Management (Optional)**
- As a Production Supervisor, I want to trigger a TLO event when a line fails due to material shortage so that the system updates operator statuses to TLO.
- As a TKS Agent, I want to view a TLO summary (start time, end time, reason) so that we can evaluate downtime impact on workforce metrics.

## 8. **Next Steps for Low-Level Design**

### Define Detailed API Contracts
- For each microservice, specify endpoints (URI, method, payload, response codes).
- Example: POST /attendance/records, PATCH /attendance/records/{id}, etc.

### Database Schemas or Models
- OperatorAttendance table, OvertimeRequests table, Shifts table, TLOEvents table, etc.
- Include references to user identities from the User Service (like employeeId or userId).

### Messaging Topics/Queues
- Identify exact event names (e.g., overtime.requested, overtime.approved, attendance.absent).
- Align with existing naming from Module 1 if using the same Service Bus or Kafka topics.

### Security & Role-Based Access
- Confirm how TKS roles (Team Leader, TKS Agent, Nurse) map to existing role-permission model in the User Service.

### Integration with Workflow/Request Services
- Decide if certain TKS requests (overtime, special leaves) become new "request types" in the existing Request Service.
- Configure the Workflow Engine with new states (e.g., "OvertimePendingApproval").

### Performance & Scalability
- Evaluate daily peak usage (especially for attendance or TLO updates).
- Plan for microservice autoscaling where necessary.

### Testing Strategy
- Unit tests for each microservice.
- End-to-end tests to ensure the entire flow (Team Leader → Overtime → Workflow → Notification) works seamlessly.

## 9. **Conclusion**

Module 3 (TKS) extends the Connected Workers platform with dedicated microservices for timekeeping, shift planning, and operator status tracking. By leveraging the existing microservices from Module 1 (User, Workflow, Notification, etc.) and introducing new TKS-specific ones, we achieve a modular, scalable, and maintainable solution.

The architecture outlined above ensures:

Clear boundaries for each new microservice (Attendance, Overtime, Shift, TLO).
Alignment with existing communication patterns (REST + asynchronous events).
Proper role-based control for Team Leaders, TKS Agents, Nurses, and Operators.
A straightforward path to low-level design by detailing endpoints, data schemas, and integration flows.