Word to Markdown
Results of converting SFD M3.docx
Markdown
**Document de Spécifications Fonctionnelles**

**1\. Introduction**

**Objective**

The purpose of this document is to provide a detailed description of the functional specifications for Module 3, titled "Headcount Control and Time Keeping System," as part of the Connected Workers project for the client APTIV. This document aims to offer the development teams a clear and comprehensive understanding of the features and mechanisms associated with this module to ensure an implementation aligned with the client’s business requirements and strategic objectives.

**Context**

Module 3 plays a key role in workforce management and time tracking within the Connected Workers project. It has been designed to meet APTIV’s needs for defining and managing the statuses of employees and operators (e.g., present, absent, on break). This module specifies the moments when these statuses are triggered or modified, as well as the underlying mechanisms (manual, automated, or through integrations with third-party systems). The solution is intended to optimize human resources management while ensuring accurate, efficient, and automated time tracking.

**Scope**

- **Included:**
  - Definition and management of employee and operator statuses (e.g., present, absent, on break).
  - Identification of triggers for status changes (manual actions, automated processes, or external integrations).
  - Management of data related to time and attendance tracking.
  - Integration with other modules of the Connected Workers project.
- **Excluded:**
  - Payroll calculation and salary management.
  - Specific integrations not defined within the scope of this module.
  - Monitoring individual employee performance.

**2\. General Description**

**Overview**

The "Headcount Control and Time Keeping System" (TKS) module is a key component of APTIV’s initiative to digitize administrative processes. This module is specifically designed to track the presence and absence of operators by managing and consolidating various employee statuses originating from different sources. For example:

- If an operator is absent, the module identifies the specific type of absence (e.g., sick leave, personal leave) based on requests submitted by the operator's team leader.
- In the case of maternity leave, the "Maternity" status is triggered by the company nurse, following the necessary conditions and approvals.

These statuses are processed within the TKS to enable agents to reconcile employee presence records with entry and exit times captured through badge scans at turnstiles. While the module provides critical input impacting payroll (e.g., tracking worked hours or absences), payroll management itself is outside the scope of this solution.

**Target Users**

The solution is designed for various user groups within APTIV, each interacting with the system according to their roles:

- **Team Leaders:** Responsible for submitting absence requests or updating specific statuses related to their team members.
- **Nurses:** Responsible for triggering health-related statuses (e.g., maternity leave, medical absences).
- **TKS Agents:** Use the system to reconcile operator statuses with badge records to ensure accurate timekeeping.
- **Operators:** Passive users whose presence and absence data are recorded in the system.

**Constraints**

The implementation of this module is subject to the following constraints:

- **Data Accuracy:** The system’s reliability depends on the timely and accurate input of information by team leaders, nurses, and other stakeholders.
- **Integration with Turnstiles:** The system must seamlessly integrate with the existing badge and turnstile infrastructure to retrieve precise entry and exit time data.
- **Limited Scope on Payroll:** The module does not handle salary calculations, which are managed by a separate system. Clear delineation of responsibilities between TKS and payroll systems is essential.
- **Process Dependencies:** The triggering of specific statuses (e.g., maternity leave) relies on manual inputs or approvals from designated roles, which may introduce delays if not managed efficiently.

**3\. Main Features**

1. **Annual Calendar.**
2. **Working Plan.**
3. **Crew Management.**
4. **Headcount control and TKS**
5. **Annual Calendar**

The annual calendar shown in the image appears to be a structured tool used to manage and plan working days and holidays for a company, in this case, APTIV. Here's a detailed description of its main components:

1. **Monthly View:**
    - Each month of 2025 is represented with a detailed calendar.
    - Weeks are numbered (e.g., "w01" for the first week of January).
    - Different colors indicate specific types of days:
        - Red: National holidays.
        - Yellow: Religious holidays.
        - Green: "Unpaid public holidays."
        - Blue: TLO days (likely linked to specific company policies or activity reductions).
    - Weekends, such as Saturdays and Sundays, are also highlighted.
2. **Monthly Summary:**
    - On the right side, a table summarizes the totals for each type of day per month:
        - Potential month days (total days in the month).
        - Sundays, Saturdays, vacation days, and holidays.
        - A column for "worked days at the plant," calculated based on non-working days.
3. **Legend:**
    - At the bottom, a legend explains the meaning of the colors used in the calendar.
    - Specific religious (e.g., Ramadan, Aid El Fitr, Aid El Kebir) and national holidays (e.g., Fatih Moharram) are listed with their respective periods.
4. **Annual Totals:**
    - Totals for various types of days (e.g., Sundays, holidays, worked days) are calculated and displayed in a summary table.
    - The total working days for 2025 is calculated as 268 days.
5. **Purpose:**
    - The calendar is designed to:
        - Facilitate production planning.
        - Account for legal, religious, and company-specific holidays.
        - Optimize resource management with a clear overview of the year.
6. **Working Plan**

The Working Plan at APTIV is a key tool for weekly production planning and management. It consolidates essential information to coordinate tasks, optimize human resources, and track production progress.

### **Main Purpose:**

To enable efficient task planning and optimal resource utilization (workforce and capacity) for each product manufactured.

### **Table Structure:**

![](data:image/png;base64,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)

1. **Main Columns:**
    - Product: List of products or modules to be produced.
    - Task Description: Specific tasks related to each product.
    - Polyvalent Ctg (Categories): Worker categories or skill sets required for each task.
    - MSP (Manpower Standard Planning): Standard number of hours or human resources required to complete a task.
    - CU (Capacity Utilization): Utilization rate of available capacities (human and material) for a task.
    - Weekly Data (Weeks 1-5): Weekly data on production targets, volumes to be produced, or estimated time for each task.

### **Functional Usage:**

1. **Planning:**
    - Evaluate workforce and resource needs for each week.
    - Assign the right skills to appropriate tasks.
2. **Production Tracking:**
    - Measure weekly progress.
    - Identify bottlenecks or discrepancies in production targets.
3. **Resource Optimization:**
    - Maximize the utilization of available capacities.
    - Reduce idle times or overload on teams.
4. **Crew Management**

At APTIV, crew management is a critical process to ensure efficient production that meets client demands, particularly in the automotive wiring sector.

### **What is Crew Management?**

Crew management involves organizing, planning, and optimizing production teams to:

1. Meet production requirements (volume, deadlines).
2. Maximize workforce efficiency.
3. Balance workload and available capacity.

### **Crew Management at APTIV:**

1. **Team Division (Shifts):**
    - Workers are divided into teams and shifts (e.g., day, night shifts).
    - Each shift is assigned to a specific task or production line.
    - Schedules are adapted to production needs (e.g., three shifts per day with rotations).
2. **Task Allocation:**
    - Employees are assigned to positions or tasks based on their skills (Polyvalence) and production requirements.
    - Critical roles (e.g., assembling electronic modules or quality control) are assigned to trained or experienced workers.
3. **Human Resource Planning:**
    - MSP (Manpower Standard Planning) is used to determine the optimal number of workers required for a specific task.
    - Teams are adjusted according to production peaks or downtimes:
        - Example: Increasing teams during high-demand periods.
        - Reducing or consolidating teams during slow periods.
4. **Efficiency Tracking:**
    - Each team’s efficiency is measured as a percentage.
    - Production targets (output) are compared to actual results to evaluate performance.
    - Gaps (underperformance or overload) are identified to adjust team management.
5. **Absence and Replacement Management:**
    - Absences (sick leave, vacations) are accounted for to keep teams operational.
    - Versatile workers (multi-skilled) are often trained to cover multiple roles.
6. **Installed Capacity and Workload:**
    - Teams are aligned with the installed capacity to prevent overload or underutilization.
    - Direct Labor refers to the staff directly required for ongoing production.
7. **Weekly Objectives Management:**
    - Each week, teams are briefed on production goals based on client orders and capacity simulations.

**Visual Check**

- **Description:** A feature allowing team leaders to perform a real-time presence check of employees at the plant or on the bus or absent. Visual check statuses will be visible in the dashboard to track attendance and identify missing personnel.
- **Priority:** High
- **Acceptance Criteria:**
  - The dashboard displays real-time presence statuses (e.g., Present in Plant, Present in Bus, Absent).
  - The system triggers alerts for missing personnel and consolidates data by the end of the day.

**Overtime (25%, 50%, 100%)**

- **Description:** A system to calculate and categorize overtime based on predefined percentages (25%, 50%, 100%), ensuring compliance with legal and company policies**. T**he system supports planned and exceptional overtime. For planned overtime, once the Capacity Study is approved, the Working Plan is updated, and employees' presence statuses are adjusted accordingly. For exceptional overtime, the Team Leader (TL) submits an overtime request via the tablet for approval, following the predefined approval matrix. Upon approval, notifications are sent to the TL and TKS Responsible. After the overtime hours are executed, Optitime is updated with the completed overtime details.
- **Priority:** High
- **Acceptance Criteria:**
  - TLs can submit overtime requests via the tablet, and the approval workflow adheres to the approval matrix.
  - Notifications are triggered upon approval for TL and TKS Responsible.
  - Executed overtime hours are reflected in Optitime.
  - Overtime is logged and categorized correctly based on hours worked beyond standard shifts.
  - The consolidated data is sent to TKS for payroll processing.

**Legal Authorization**

- **Description:** Tracks employees’ legal authorization statuses, beginning with a temporary "T" status until the required evidence is provided. Once evidence is submitted, the Team Leader (TL) updates the status to "TL" (Authorized) on the tablet, which is synchronized with the Optitime system for real-time accuracy.
- **Priority:** Medium
- **Acceptance Criteria:**
  - The system allows TLs to update authorization statuses efficiently.
  - Notifications are sent for pending or expired authorizations.
  - Optitime reflects updated statuses immediately.

**Forgetting or Loss of Badge**

- **Description:** Enables a contingency process for employees who forget or lose their badge. Employees manually enter their ID on the Bus Reader Device for initial verification. Upon arrival at the plant, they report to the Guardhouse, where their identity is cross-checked based on a notification from the Team Leader. The TL then updates the employee’s status on the tablet to ensure accurate attendance records.
- **Priority:** High
- **Acceptance Criteria:**
  - The system records manual ID entries on the Bus Reader Device.
  - Notifications from TLs enable the Guardhouse to cross-check identities.
  - Status updates by TLs are reflected in real-time.

**Time Off**

- **Description**: Manages time-off requests and approvals, ensuring accurate tracking and synchronization with the TKS system.
- **Priority:** High
- **Acceptance Criteria:**
  - Approved time-off statuses are displayed on the dashboard.
  - The system prevents scheduling conflicts and consolidates data daily.

**Ordre de Déplacement (DI ou DN)**

- **Description:** Tracks orders for employee displacements (DI: short-term; DN: long-term) with a clear record of purpose and duration.
- **Priority:** Medium
- **Acceptance Criteria:**
  - Displacement orders are logged and categorized as DI or DN.
  - End-of-day data is consolidated and sent to TKS.

**Travail à l’Extérieur (TE)**

- **Description** :Tracks employees working outside the plant, ensuring their time is accounted for in the system.
- **Priority:** Medium
- **Acceptance Criteria:**
  - External work hours are logged accurately.
  - Consolidated data is transmitted for payroll claims.

**In Line**

- **Description:** Captures employees who are actively working on production lines, ensuring their presence and activity status is tracked.
- **Priority:** High
- **Acceptance Criteria:**
  - Real-time updates reflect line presence statuses.
  - Data is consolidated daily for verification.

**Present in Bus**

- **Description:** Tracks employees’ presence on company-provided transportation, ensuring attendance records are accurate from the start of the shift.
- **Priority:** Medium
- **Acceptance Criteria:**
  - Bus presence is logged at checkpoints.
  - Data is updated in real-time and included in the end-of-day report.

**Present in Plant**

- **Description:** Tracks employees who have checked in at the plant, ensuring accurate attendance and presence tracking.
- **Priority:** High
- **Acceptance Criteria:**
  - Real-time plant presence statuses are displayed on the dashboard.
  - Data is consolidated and transmitted to TKS by the end of the day.

**Consolidation and Tracking Requirements**

- **Statuses to Include:**
  - Present in Plant
  - Present in Bus
  - Absent
  - In Line
  - External Work (TE)
  - Time Off
  - Displacement Orders (DI/DN)
  - Badge Issues
  - Overtime (25%, 50%, 100%)
  - Legal Authorization
- **Daily Consolidation:** All statuses will be consolidated and sent to the TKS system or relevant stakeholders for verification and payroll claims.

**Absence Authorization Management**

**Description:**

This feature enables the system to manage operators' absence authorization requests based on various statuses. Requests are initiated by the team leader (TL) via a tablet. Once submitted, the request follows an approval process that includes:

- Updating the operator's status (e.g., "T").
- Notifying and involving the TKS agent to validate whether the information should be transmitted to Optitime (a third-party system) manually or automatically.

This feature ensures that absence-related information is accurately documented, approved, and shared with the appropriate systems for precise time tracking.

**Priority: Critical**

This feature is essential to ensure continuity and reliability in managing absences and their impact on attendance and time tracking processes.

**Acceptance Criteria:**

1. Absence requests can be initiated by the team leader via a tablet.
2. Each request follows a clear approval process, with automatic or manual updates to the operator's status.
3. A notification is sent to the TKS agent when the status is updated for validation and transmission to Optitime.
4. Users (team leaders and TKS agents) can view the history of absence authorizations for auditing or follow-up purposes.

#### **Planned TLO Management (CTN)**

#### **Description:**

This feature enables the proactive planning of Temporary Line Off (TLO) based on production capacity and identified needs. The process involves:

1. A **Capacity Study (CS)** to evaluate available resources and production requirements.
2. The creation of a **Working Plan (WP)** for the upcoming period, incorporating the necessary TLOs.
3. Distribution of the working plan to team leaders (TL) via a dedicated interface, including:
    - Schedules.
    - A list of team members assigned to TLOs for the N+1 period.

This feature ensures proactive planning of TLOs, enabling optimal resource management and smooth communication among teams.

####

#### **Purpose (Value for the User or System):**

- **For Team Leaders (TL)**: Provides a clear view of assignments and planned interruptions, enabling effective operator management.
- **For the Company**: Optimizes productivity by anticipating interruptions and balancing resource allocation efficiently.

#### **Priority: Important**

Planned TLOs are critical to ensuring smooth operations and minimizing disruptions to production.

#### **Associated Use Cases:**

- Capacity study (CS) performed by production managers.
- Automatic generation of the working plan (WP) in the system.
- Distribution of the WP to TLs, including CTN assignments for the N+1 period.

**Management of Unplanned TLO (CTP)**

**Description:**

This feature manages unplanned TLOs (Temporary Line Off) caused by raw material shortages or technical issues. The status change (CTP) is initiated by the team leader (TL) via their tablet and subsequently updates the operator's status in the system. The process involves:

- Recording the reason for the unplanned TLO (e.g., raw material shortage, technical failure).
- Updating the operator's status to "CTP" (Current Technical Problem).
- Notification to the TKS agent to confirm the updated status in the TKS interface for tracking purposes.

This feature ensures that unplanned TLOs are documented systematically and linked to relevant statuses for accurate tracking and analysis.

**Priority: Critical**

This feature is crucial for maintaining transparency and traceability of production interruptions and their impact on operator activity.

**Acceptance Criteria:**

1. The team leader can initiate the unplanned TLO status change (CTP) via the tablet.
2. The system records the reason for the TLO and updates the operator's status accordingly.
3. The TKS agent receives a notification and can review and validate the updated status in the TKS interface.
4. The process ensures accurate and real-time status updates, which can be referenced for operational analysis.
5. The historical data for unplanned TLOs is accessible for reporting or auditing purposes.

**Delay Management (R)**

**Description:**

This feature handles delays for operators scheduled for a shift, ensuring proper tracking and status updates based on the situation. The process involves two main cases:

1. **First Case**:
    - The operator is delayed due to transportation issues (e.g., stuck on the bus).
    - Upon arrival at the production line, their status is updated to "Present" (P) after the team leader requests a correction of the clock-in time.
2. **Second Case**:
    - If the operator notifies the team leader informally (e.g., via WhatsApp) about the delay, their status is updated to "Delayed" (R = Retard).
    - Once they arrive on-site, their status is updated to reflect the delay duration (e.g., R(De 06h10) + P).
    - If the operator fails to show up, the status is marked as R and the system triggers **Replacement Management** to find a substitute, at the end of the shift the status goes from R to "Absent" (AB),

This feature ensures that delays are documented and appropriately handled, while allowing team leaders to request clock-in corrections for legitimate delays.

**Priority: Critical**

This feature is essential for maintaining operational efficiency and ensuring accurate tracking of delays and absences.

**Acceptance Criteria:**

1. Operators scheduled for a shift are visually checked for their presence on the production line.
2. The system updates the operator’s status based on their situation:
    - **On the bus**: Marked as delayed but corrected to "Present" (P) after arrival.
    - **Informed delay**: Marked as "Retard" (R), updated with the arrival time and duration of the delay.
    - **Uninformed delay**: Marked as "Retard" (R), triggering replacement management, and “Absent” (AB) at the end of the shift
3. Team leaders can request corrections to clock-in times via their tablets.
4. Replacement management is automatically initiated when an operator is not present at the beginning of the shift.
5. Status updates are logged and accessible for review or operational analysis.

**Training Attendance Management (F)**

**Description:**

This feature manages the assignment and attendance tracking of operators scheduled for training sessions. The process starts with the working plan (WP) for the upcoming week (Week N-1), where operators are assigned specific days or hours for training. During the training week (Week N):

1. At the start of the shift, operators' attendance for the training is verified.
2. For the Team Leader, the operator is considered as “Present” (P).
3. The responsibility for marking attendance (Team Leader or Trainer) is clarified in the process.
4. Once attendance is confirmed, the operator's status is updated to "F" (Formation = Training) in the Versatility Matrix.

This feature ensures that training assignments are tracked accurately and integrated into the overall attendance system.

**Priority: High**

This feature is essential for ensuring proper planning and attendance tracking of operator training sessions, which are critical for skill development and compliance.

**Acceptance Criteria:**

1. The working plan (WP) includes training assignments for operators for the following week (Week N).
2. Operators are assigned specific days or hours for training.
3. The system verifies attendance during the training week (Week N) at the start of the shift.
4. The responsible party (Team Leader or Trainer) for marking attendance is clearly defined and integrated into the workflow.
5. Attendance is recorded, and the operator’s status is updated to "F" (Training) upon confirmation.
6. The system maintains a record of training attendance for auditing or reporting purposes.

**Workplace Accident Management (AT)**

**Description:**

This feature handles workplace accidents that occur during production and ensures that the operator's status is updated accordingly. The process includes two primary cases:

1. **First Case: Minor Consequences**
    - The operator receives on-site care by the nurse for minor injuries.
    - The operator’s status is updated to **AT (Accident de Travail)** to reflect the incident.
    - Once the operator returns to their workspace, the status is updated to **P (Present)**.
2. **Second Case: Moderate Consequences**
    - The operator is transferred to a hospital for further care due to moderate injuries.
    - The operator’s status is updated to **AT (Accident de Travail)** for the duration of their absence.

The feature ensures proper tracking of workplace accidents and integrates roles (Team Leader, Nurse, HR) to handle updates efficiently.

**Priority: Critical**

This feature is crucial for ensuring the safety and proper documentation of workplace accidents and their impact on operations.

**Acceptance Criteria:**

1. The system allows the team leader, nurse, or HR to initiate and update the operator’s status based on the severity of the accident.
2. In case of minor consequences:
    - The nurse updates the status to **AT (Accident de Travail)** during on-site care.
    - The status is reverted to **P (Present)** once the operator resumes work.
3. In case of moderate consequences:
    - The operator’s status is updated to **AT (Accident de Travail)** when they are transferred to a hospital.
    - The system maintains the status until the operator’s return.
4. The system logs all status changes and maintains records for reporting and compliance purposes.
5. The responsible roles for updating the status (Team Leader, Nurse, HR) are clearly identified in the workflow.

**Maternity Leave Management (MT)**

**Description:**

This feature handles the process of managing maternity leave for operators, ensuring proper documentation and status updates in the system. The workflow involves the following steps:

1. **Submission of the Maternity Certificate**:
    - The operator provides the maternity certificate to the Team Leader (TL).
2. **Team Leader Actions**:
    - The TL proceeds to the kiosk and performs the following:
        1. Selects "Send a document."
        2. Chooses the option for "birth document."
        3. Selects the concerned operator.
        4. Inputs the start and end dates of the maternity leave.
        5. Scans and uploads the document.
3. **TKS Agent Actions**:
    - The TKS agent is notified of the submission.
    - The agent verifies the information and inputs the official start date of the leave into the TKS system.
4. **System Update**:
    - The system updates the operator’s status to **MT (Maternity)**.
    - The TL is notified of the updated status.

This process ensures that maternity leave is handled transparently and accurately, with clear responsibilities for each stakeholder.

**Priority: High**

This feature is essential for ensuring proper management of maternity leave, compliance with labor regulations, and accurate record-keeping.

**Acceptance Criteria:**

1. Operators can submit their maternity certificate to the Team Leader.
2. The TL can input the required data and upload the document via the kiosk interface.
3. The TKS agent receives a notification, verifies the information, and updates the system with the leave start date.
4. The system updates the operator’s status to **MT (Maternity)** and notifies the TL of the change.
5. All steps are logged in the system for auditing purposes

### **4\. Use Cases**

#### **Title: Absence Authorization Management**

- **Actors**: Team Leader (TL), TKS Agent.
- **Scenario**:
  - The TL identifies an operator’s absence and initiates a request via the kiosk.
  - The TL inputs details such as the absence reason (e.g., personal leave, sick leave) and dates.
  - The TKS agent receives a notification for validation.
  - The TKS agent verifies the request and updates the operator’s status in the system.
- **Expected Outcome**:
  - The operator’s status is updated accurately as absent with the appropriate reason, and the system logs this status for further tracking.

#### **Title: Unplanned TLO (CTP) Management**

- **Actors**: Team Leader (TL), TKS Agent.
- **Scenario**:
  - The TL identifies an unplanned TLO caused by resource shortages or technical reasons.
  - The TL updates the operator’s status to **CTP** using the kiosk.
  - The TKS agent reviews and validates the status in the system.
- **Expected Outcome**:
  - The operator’s status is recorded as **CTP**, ensuring proper tracking and analysis of resource shortages.

#### **Title: Delay Management**

- **Actors**: Operator, Team Leader (TL), TKS Agent.
- **Scenario**:
  - An operator is late for their shift.
  - If informed, the TL updates the status to **R (Retard)** and notes the duration of the delay.
  - If not informed, the operator is marked as absent (**AB**) and replacement management is triggered.
  - The TKS agent validates the status update and finalizes it in the system.
- **Expected Outcome**:
  - Delays and absences are accurately documented, and replacements are triggered as needed.

#### **Title: Training Attendance Management**

- **Actors**: Operator, Team Leader (TL), Trainer.
- **Scenario**:
  - The operator is assigned to training during the planning stage (Week N-1).
  - At the start of the shift, the trainer or TL marks the operator’s attendance.
  - The system updates the status to **F (Formation)** upon attendance confirmation.
- **Expected Outcome**:
  - Training attendance is logged accurately, and the system updates the operator’s status for the training duration.

#### **Title: Workplace Accident Management (AT)**

- **Actors**: Operator, Nurse, TKS Agent, Team Leader (TL).
- **Scenario**:
  - The operator suffers a workplace accident during production.
  - For minor injuries, the nurse provides on-site care and updates the status to **AT (Accident de Travail)**.
  - For moderate injuries, the operator is transferred to a hospital, and the nurse or TL updates the status to **AT**.
  - The TKS agent validates the status and ensures proper documentation.
- **Expected Outcome**:
  - Workplace accidents are tracked with appropriate statuses for further reporting and compliance.

#### **Title: Maternity Leave Management**

- **Actors**: Operator, Team Leader (TL), TKS Agent.
- **Scenario**:
  - The operator submits a maternity certificate to the TL.
  - The TL uses the kiosk to upload the document, input start and end dates, and notify the TKS agent.
  - The TKS agent verifies the information and updates the operator’s status to **MT (Maternity)**.
  - The system notifies the TL of the update.
- **Expected Outcome**:
  - Maternity leave is properly documented, and the operator’s status reflects the leave period.

**5\. Interfaces Utilisateur**

- Décrire rapidement les écrans ou fonctionnalités visibles.
- Ajouter des schémas ou maquettes si possible.
Rendered
Document de Spécifications Fonctionnelles

1. Introduction

Objective

The purpose of this document is to provide a detailed description of the functional specifications for Module 3, titled "Headcount Control and Time Keeping System," as part of the Connected Workers project for the client APTIV. This document aims to offer the development teams a clear and comprehensive understanding of the features and mechanisms associated with this module to ensure an implementation aligned with the client’s business requirements and strategic objectives.

Context

Module 3 plays a key role in workforce management and time tracking within the Connected Workers project. It has been designed to meet APTIV’s needs for defining and managing the statuses of employees and operators (e.g., present, absent, on break). This module specifies the moments when these statuses are triggered or modified, as well as the underlying mechanisms (manual, automated, or through integrations with third-party systems). The solution is intended to optimize human resources management while ensuring accurate, efficient, and automated time tracking.

Scope

Included:
Definition and management of employee and operator statuses (e.g., present, absent, on break).
Identification of triggers for status changes (manual actions, automated processes, or external integrations).
Management of data related to time and attendance tracking.
Integration with other modules of the Connected Workers project.
Excluded:
Payroll calculation and salary management.
Specific integrations not defined within the scope of this module.
Monitoring individual employee performance.
2. General Description

Overview

The "Headcount Control and Time Keeping System" (TKS) module is a key component of APTIV’s initiative to digitize administrative processes. This module is specifically designed to track the presence and absence of operators by managing and consolidating various employee statuses originating from different sources. For example:

If an operator is absent, the module identifies the specific type of absence (e.g., sick leave, personal leave) based on requests submitted by the operator's team leader.
In the case of maternity leave, the "Maternity" status is triggered by the company nurse, following the necessary conditions and approvals.
These statuses are processed within the TKS to enable agents to reconcile employee presence records with entry and exit times captured through badge scans at turnstiles. While the module provides critical input impacting payroll (e.g., tracking worked hours or absences), payroll management itself is outside the scope of this solution.

Target Users

The solution is designed for various user groups within APTIV, each interacting with the system according to their roles:

Team Leaders: Responsible for submitting absence requests or updating specific statuses related to their team members.
Nurses: Responsible for triggering health-related statuses (e.g., maternity leave, medical absences).
TKS Agents: Use the system to reconcile operator statuses with badge records to ensure accurate timekeeping.
Operators: Passive users whose presence and absence data are recorded in the system.
Constraints

The implementation of this module is subject to the following constraints:

Data Accuracy: The system’s reliability depends on the timely and accurate input of information by team leaders, nurses, and other stakeholders.
Integration with Turnstiles: The system must seamlessly integrate with the existing badge and turnstile infrastructure to retrieve precise entry and exit time data.
Limited Scope on Payroll: The module does not handle salary calculations, which are managed by a separate system. Clear delineation of responsibilities between TKS and payroll systems is essential.
Process Dependencies: The triggering of specific statuses (e.g., maternity leave) relies on manual inputs or approvals from designated roles, which may introduce delays if not managed efficiently.
3. Main Features

Annual Calendar.
Working Plan.
Crew Management.
Headcount control and TKS
Annual Calendar
The annual calendar shown in the image appears to be a structured tool used to manage and plan working days and holidays for a company, in this case, APTIV. Here's a detailed description of its main components:

Monthly View:
Each month of 2025 is represented with a detailed calendar.
Weeks are numbered (e.g., "w01" for the first week of January).
Different colors indicate specific types of days:
Red: National holidays.
Yellow: Religious holidays.
Green: "Unpaid public holidays."
Blue: TLO days (likely linked to specific company policies or activity reductions).
Weekends, such as Saturdays and Sundays, are also highlighted.
Monthly Summary:
On the right side, a table summarizes the totals for each type of day per month:
Potential month days (total days in the month).
Sundays, Saturdays, vacation days, and holidays.
A column for "worked days at the plant," calculated based on non-working days.
Legend:
At the bottom, a legend explains the meaning of the colors used in the calendar.
Specific religious (e.g., Ramadan, Aid El Fitr, Aid El Kebir) and national holidays (e.g., Fatih Moharram) are listed with their respective periods.
Annual Totals:
Totals for various types of days (e.g., Sundays, holidays, worked days) are calculated and displayed in a summary table.
The total working days for 2025 is calculated as 268 days.
Purpose:
The calendar is designed to:
Facilitate production planning.
Account for legal, religious, and company-specific holidays.
Optimize resource management with a clear overview of the year.
Working Plan
The Working Plan at APTIV is a key tool for weekly production planning and management. It consolidates essential information to coordinate tasks, optimize human resources, and track production progress.

Main Purpose:
To enable efficient task planning and optimal resource utilization (workforce and capacity) for each product manufactured.

Table Structure:


Main Columns:
Product: List of products or modules to be produced.
Task Description: Specific tasks related to each product.
Polyvalent Ctg (Categories): Worker categories or skill sets required for each task.
MSP (Manpower Standard Planning): Standard number of hours or human resources required to complete a task.
CU (Capacity Utilization): Utilization rate of available capacities (human and material) for a task.
Weekly Data (Weeks 1-5): Weekly data on production targets, volumes to be produced, or estimated time for each task.
Functional Usage:
Planning:
Evaluate workforce and resource needs for each week.
Assign the right skills to appropriate tasks.
Production Tracking:
Measure weekly progress.
Identify bottlenecks or discrepancies in production targets.
Resource Optimization:
Maximize the utilization of available capacities.
Reduce idle times or overload on teams.
Crew Management
At APTIV, crew management is a critical process to ensure efficient production that meets client demands, particularly in the automotive wiring sector.

What is Crew Management?
Crew management involves organizing, planning, and optimizing production teams to:

Meet production requirements (volume, deadlines).
Maximize workforce efficiency.
Balance workload and available capacity.
Crew Management at APTIV:
Team Division (Shifts):
Workers are divided into teams and shifts (e.g., day, night shifts).
Each shift is assigned to a specific task or production line.
Schedules are adapted to production needs (e.g., three shifts per day with rotations).
Task Allocation:
Employees are assigned to positions or tasks based on their skills (Polyvalence) and production requirements.
Critical roles (e.g., assembling electronic modules or quality control) are assigned to trained or experienced workers.
Human Resource Planning:
MSP (Manpower Standard Planning) is used to determine the optimal number of workers required for a specific task.
Teams are adjusted according to production peaks or downtimes:
Example: Increasing teams during high-demand periods.
Reducing or consolidating teams during slow periods.
Efficiency Tracking:
Each team’s efficiency is measured as a percentage.
Production targets (output) are compared to actual results to evaluate performance.
Gaps (underperformance or overload) are identified to adjust team management.
Absence and Replacement Management:
Absences (sick leave, vacations) are accounted for to keep teams operational.
Versatile workers (multi-skilled) are often trained to cover multiple roles.
Installed Capacity and Workload:
Teams are aligned with the installed capacity to prevent overload or underutilization.
Direct Labor refers to the staff directly required for ongoing production.
Weekly Objectives Management:
Each week, teams are briefed on production goals based on client orders and capacity simulations.
Visual Check

Description: A feature allowing team leaders to perform a real-time presence check of employees at the plant or on the bus or absent. Visual check statuses will be visible in the dashboard to track attendance and identify missing personnel.
Priority: High
Acceptance Criteria:
The dashboard displays real-time presence statuses (e.g., Present in Plant, Present in Bus, Absent).
The system triggers alerts for missing personnel and consolidates data by the end of the day.
Overtime (25%, 50%, 100%)

Description: A system to calculate and categorize overtime based on predefined percentages (25%, 50%, 100%), ensuring compliance with legal and company policies**. T**he system supports planned and exceptional overtime. For planned overtime, once the Capacity Study is approved, the Working Plan is updated, and employees' presence statuses are adjusted accordingly. For exceptional overtime, the Team Leader (TL) submits an overtime request via the tablet for approval, following the predefined approval matrix. Upon approval, notifications are sent to the TL and TKS Responsible. After the overtime hours are executed, Optitime is updated with the completed overtime details.
Priority: High
Acceptance Criteria:
TLs can submit overtime requests via the tablet, and the approval workflow adheres to the approval matrix.
Notifications are triggered upon approval for TL and TKS Responsible.
Executed overtime hours are reflected in Optitime.
Overtime is logged and categorized correctly based on hours worked beyond standard shifts.
The consolidated data is sent to TKS for payroll processing.
Legal Authorization

Description: Tracks employees’ legal authorization statuses, beginning with a temporary "T" status until the required evidence is provided. Once evidence is submitted, the Team Leader (TL) updates the status to "TL" (Authorized) on the tablet, which is synchronized with the Optitime system for real-time accuracy.
Priority: Medium
Acceptance Criteria:
The system allows TLs to update authorization statuses efficiently.
Notifications are sent for pending or expired authorizations.
Optitime reflects updated statuses immediately.
Forgetting or Loss of Badge

Description: Enables a contingency process for employees who forget or lose their badge. Employees manually enter their ID on the Bus Reader Device for initial verification. Upon arrival at the plant, they report to the Guardhouse, where their identity is cross-checked based on a notification from the Team Leader. The TL then updates the employee’s status on the tablet to ensure accurate attendance records.
Priority: High
Acceptance Criteria:
The system records manual ID entries on the Bus Reader Device.
Notifications from TLs enable the Guardhouse to cross-check identities.
Status updates by TLs are reflected in real-time.
Time Off

Description: Manages time-off requests and approvals, ensuring accurate tracking and synchronization with the TKS system.
Priority: High
Acceptance Criteria:
Approved time-off statuses are displayed on the dashboard.
The system prevents scheduling conflicts and consolidates data daily.
Ordre de Déplacement (DI ou DN)

Description: Tracks orders for employee displacements (DI: short-term; DN: long-term) with a clear record of purpose and duration.
Priority: Medium
Acceptance Criteria:
Displacement orders are logged and categorized as DI or DN.
End-of-day data is consolidated and sent to TKS.
Travail à l’Extérieur (TE)

Description :Tracks employees working outside the plant, ensuring their time is accounted for in the system.
Priority: Medium
Acceptance Criteria:
External work hours are logged accurately.
Consolidated data is transmitted for payroll claims.
In Line

Description: Captures employees who are actively working on production lines, ensuring their presence and activity status is tracked.
Priority: High
Acceptance Criteria:
Real-time updates reflect line presence statuses.
Data is consolidated daily for verification.
Present in Bus

Description: Tracks employees’ presence on company-provided transportation, ensuring attendance records are accurate from the start of the shift.
Priority: Medium
Acceptance Criteria:
Bus presence is logged at checkpoints.
Data is updated in real-time and included in the end-of-day report.
Present in Plant

Description: Tracks employees who have checked in at the plant, ensuring accurate attendance and presence tracking.
Priority: High
Acceptance Criteria:
Real-time plant presence statuses are displayed on the dashboard.
Data is consolidated and transmitted to TKS by the end of the day.
Consolidation and Tracking Requirements

Statuses to Include:
Present in Plant
Present in Bus
Absent
In Line
External Work (TE)
Time Off
Displacement Orders (DI/DN)
Badge Issues
Overtime (25%, 50%, 100%)
Legal Authorization
Daily Consolidation: All statuses will be consolidated and sent to the TKS system or relevant stakeholders for verification and payroll claims.
Absence Authorization Management

Description:

This feature enables the system to manage operators' absence authorization requests based on various statuses. Requests are initiated by the team leader (TL) via a tablet. Once submitted, the request follows an approval process that includes:

Updating the operator's status (e.g., "T").
Notifying and involving the TKS agent to validate whether the information should be transmitted to Optitime (a third-party system) manually or automatically.
This feature ensures that absence-related information is accurately documented, approved, and shared with the appropriate systems for precise time tracking.

Priority: Critical

This feature is essential to ensure continuity and reliability in managing absences and their impact on attendance and time tracking processes.

Acceptance Criteria:

Absence requests can be initiated by the team leader via a tablet.
Each request follows a clear approval process, with automatic or manual updates to the operator's status.
A notification is sent to the TKS agent when the status is updated for validation and transmission to Optitime.
Users (team leaders and TKS agents) can view the history of absence authorizations for auditing or follow-up purposes.
Planned TLO Management (CTN)
Description:
This feature enables the proactive planning of Temporary Line Off (TLO) based on production capacity and identified needs. The process involves:

A Capacity Study (CS) to evaluate available resources and production requirements.
The creation of a Working Plan (WP) for the upcoming period, incorporating the necessary TLOs.
Distribution of the working plan to team leaders (TL) via a dedicated interface, including:
Schedules.
A list of team members assigned to TLOs for the N+1 period.
This feature ensures proactive planning of TLOs, enabling optimal resource management and smooth communication among teams.

Purpose (Value for the User or System):
For Team Leaders (TL): Provides a clear view of assignments and planned interruptions, enabling effective operator management.
For the Company: Optimizes productivity by anticipating interruptions and balancing resource allocation efficiently.
Priority: Important
Planned TLOs are critical to ensuring smooth operations and minimizing disruptions to production.

Associated Use Cases:
Capacity study (CS) performed by production managers.
Automatic generation of the working plan (WP) in the system.
Distribution of the WP to TLs, including CTN assignments for the N+1 period.
Management of Unplanned TLO (CTP)

Description:

This feature manages unplanned TLOs (Temporary Line Off) caused by raw material shortages or technical issues. The status change (CTP) is initiated by the team leader (TL) via their tablet and subsequently updates the operator's status in the system. The process involves:

Recording the reason for the unplanned TLO (e.g., raw material shortage, technical failure).
Updating the operator's status to "CTP" (Current Technical Problem).
Notification to the TKS agent to confirm the updated status in the TKS interface for tracking purposes.
This feature ensures that unplanned TLOs are documented systematically and linked to relevant statuses for accurate tracking and analysis.

Priority: Critical

This feature is crucial for maintaining transparency and traceability of production interruptions and their impact on operator activity.

Acceptance Criteria:

The team leader can initiate the unplanned TLO status change (CTP) via the tablet.
The system records the reason for the TLO and updates the operator's status accordingly.
The TKS agent receives a notification and can review and validate the updated status in the TKS interface.
The process ensures accurate and real-time status updates, which can be referenced for operational analysis.
The historical data for unplanned TLOs is accessible for reporting or auditing purposes.
Delay Management (R)

Description:

This feature handles delays for operators scheduled for a shift, ensuring proper tracking and status updates based on the situation. The process involves two main cases:

First Case:
The operator is delayed due to transportation issues (e.g., stuck on the bus).
Upon arrival at the production line, their status is updated to "Present" (P) after the team leader requests a correction of the clock-in time.
Second Case:
If the operator notifies the team leader informally (e.g., via WhatsApp) about the delay, their status is updated to "Delayed" (R = Retard).
Once they arrive on-site, their status is updated to reflect the delay duration (e.g., R(De 06h10) + P).
If the operator fails to show up, the status is marked as R and the system triggers Replacement Management to find a substitute, at the end of the shift the status goes from R to "Absent" (AB),
This feature ensures that delays are documented and appropriately handled, while allowing team leaders to request clock-in corrections for legitimate delays.

Priority: Critical

This feature is essential for maintaining operational efficiency and ensuring accurate tracking of delays and absences.

Acceptance Criteria:

Operators scheduled for a shift are visually checked for their presence on the production line.
The system updates the operator’s status based on their situation:
On the bus: Marked as delayed but corrected to "Present" (P) after arrival.
Informed delay: Marked as "Retard" (R), updated with the arrival time and duration of the delay.
Uninformed delay: Marked as "Retard" (R), triggering replacement management, and “Absent” (AB) at the end of the shift
Team leaders can request corrections to clock-in times via their tablets.
Replacement management is automatically initiated when an operator is not present at the beginning of the shift.
Status updates are logged and accessible for review or operational analysis.
Training Attendance Management (F)

Description:

This feature manages the assignment and attendance tracking of operators scheduled for training sessions. The process starts with the working plan (WP) for the upcoming week (Week N-1), where operators are assigned specific days or hours for training. During the training week (Week N):

At the start of the shift, operators' attendance for the training is verified.
For the Team Leader, the operator is considered as “Present” (P).
The responsibility for marking attendance (Team Leader or Trainer) is clarified in the process.
Once attendance is confirmed, the operator's status is updated to "F" (Formation = Training) in the Versatility Matrix.
This feature ensures that training assignments are tracked accurately and integrated into the overall attendance system.

Priority: High

This feature is essential for ensuring proper planning and attendance tracking of operator training sessions, which are critical for skill development and compliance.

Acceptance Criteria:

The working plan (WP) includes training assignments for operators for the following week (Week N).
Operators are assigned specific days or hours for training.
The system verifies attendance during the training week (Week N) at the start of the shift.
The responsible party (Team Leader or Trainer) for marking attendance is clearly defined and integrated into the workflow.
Attendance is recorded, and the operator’s status is updated to "F" (Training) upon confirmation.
The system maintains a record of training attendance for auditing or reporting purposes.
Workplace Accident Management (AT)

Description:

This feature handles workplace accidents that occur during production and ensures that the operator's status is updated accordingly. The process includes two primary cases:

First Case: Minor Consequences
The operator receives on-site care by the nurse for minor injuries.
The operator’s status is updated to AT (Accident de Travail) to reflect the incident.
Once the operator returns to their workspace, the status is updated to P (Present).
Second Case: Moderate Consequences
The operator is transferred to a hospital for further care due to moderate injuries.
The operator’s status is updated to AT (Accident de Travail) for the duration of their absence.
The feature ensures proper tracking of workplace accidents and integrates roles (Team Leader, Nurse, HR) to handle updates efficiently.

Priority: Critical

This feature is crucial for ensuring the safety and proper documentation of workplace accidents and their impact on operations.

Acceptance Criteria:

The system allows the team leader, nurse, or HR to initiate and update the operator’s status based on the severity of the accident.
In case of minor consequences:
The nurse updates the status to AT (Accident de Travail) during on-site care.
The status is reverted to P (Present) once the operator resumes work.
In case of moderate consequences:
The operator’s status is updated to AT (Accident de Travail) when they are transferred to a hospital.
The system maintains the status until the operator’s return.
The system logs all status changes and maintains records for reporting and compliance purposes.
The responsible roles for updating the status (Team Leader, Nurse, HR) are clearly identified in the workflow.
Maternity Leave Management (MT)

Description:

This feature handles the process of managing maternity leave for operators, ensuring proper documentation and status updates in the system. The workflow involves the following steps:

Submission of the Maternity Certificate:
The operator provides the maternity certificate to the Team Leader (TL).
Team Leader Actions:
The TL proceeds to the kiosk and performs the following:
Selects "Send a document."
Chooses the option for "birth document."
Selects the concerned operator.
Inputs the start and end dates of the maternity leave.
Scans and uploads the document.
TKS Agent Actions:
The TKS agent is notified of the submission.
The agent verifies the information and inputs the official start date of the leave into the TKS system.
System Update:
The system updates the operator’s status to MT (Maternity).
The TL is notified of the updated status.
This process ensures that maternity leave is handled transparently and accurately, with clear responsibilities for each stakeholder.

Priority: High

This feature is essential for ensuring proper management of maternity leave, compliance with labor regulations, and accurate record-keeping.

Acceptance Criteria:

Operators can submit their maternity certificate to the Team Leader.
The TL can input the required data and upload the document via the kiosk interface.
The TKS agent receives a notification, verifies the information, and updates the system with the leave start date.
The system updates the operator’s status to MT (Maternity) and notifies the TL of the change.
All steps are logged in the system for auditing purposes
4. Use Cases
Title: Absence Authorization Management
Actors: Team Leader (TL), TKS Agent.
Scenario:
The TL identifies an operator’s absence and initiates a request via the kiosk.
The TL inputs details such as the absence reason (e.g., personal leave, sick leave) and dates.
The TKS agent receives a notification for validation.
The TKS agent verifies the request and updates the operator’s status in the system.
Expected Outcome:
The operator’s status is updated accurately as absent with the appropriate reason, and the system logs this status for further tracking.
Title: Unplanned TLO (CTP) Management
Actors: Team Leader (TL), TKS Agent.
Scenario:
The TL identifies an unplanned TLO caused by resource shortages or technical reasons.
The TL updates the operator’s status to CTP using the kiosk.
The TKS agent reviews and validates the status in the system.
Expected Outcome:
The operator’s status is recorded as CTP, ensuring proper tracking and analysis of resource shortages.
Title: Delay Management
Actors: Operator, Team Leader (TL), TKS Agent.
Scenario:
An operator is late for their shift.
If informed, the TL updates the status to R (Retard) and notes the duration of the delay.
If not informed, the operator is marked as absent (AB) and replacement management is triggered.
The TKS agent validates the status update and finalizes it in the system.
Expected Outcome:
Delays and absences are accurately documented, and replacements are triggered as needed.
Title: Training Attendance Management
Actors: Operator, Team Leader (TL), Trainer.
Scenario:
The operator is assigned to training during the planning stage (Week N-1).
At the start of the shift, the trainer or TL marks the operator’s attendance.
The system updates the status to F (Formation) upon attendance confirmation.
Expected Outcome:
Training attendance is logged accurately, and the system updates the operator’s status for the training duration.
Title: Workplace Accident Management (AT)
Actors: Operator, Nurse, TKS Agent, Team Leader (TL).
Scenario:
The operator suffers a workplace accident during production.
For minor injuries, the nurse provides on-site care and updates the status to AT (Accident de Travail).
For moderate injuries, the operator is transferred to a hospital, and the nurse or TL updates the status to AT.
The TKS agent validates the status and ensures proper documentation.
Expected Outcome:
Workplace accidents are tracked with appropriate statuses for further reporting and compliance.
Title: Maternity Leave Management
Actors: Operator, Team Leader (TL), TKS Agent.
Scenario:
The operator submits a maternity certificate to the TL.
The TL uses the kiosk to upload the document, input start and end dates, and notify the TKS agent.
The TKS agent verifies the information and updates the operator’s status to MT (Maternity).
The system notifies the TL of the update.
Expected Outcome:
Maternity leave is properly documented, and the operator’s status reflects the leave period.
5. Interfaces Utilisateur

Décrire rapidement les écrans ou fonctionnalités visibles.
Ajouter des schémas ou maquettes si possible.
Feedback
Source
Donate
Terms
Privacy
@benbalter