
## System Analysis & Design

From your notes, I see this is a shift management system that needs to:
1. Track operator/employee presence
2. Handle various status changes triggered by different microservices
3. Distinguish between shift status and operator status
4. Support scheduled status changes with future effective dates
5. Implement CQRS with CosmosDB for event sourcing and read models

### Key Components

#### Status Management System

Your system needs to differentiate between:
- **Shift Status**: Immediate presence states (Present, Absent, Delayed)
- **Operator Status**: Extended states that may affect shift status (Leave, Authorization, etc.)

Let's design a solution that handles both while maintaining their relationship.

## CosmosDB Architecture with CQRS

### Event Sourcing Containers
1. **StatusChangeEvents**: All status changes are stored as immutable events
2. **ScheduledStatusEvents**: Future status changes with effective dates

### Read Model Containers
1. **OperatorStatus**: Current status of each operator
2. **ShiftTracker**: Current shift status for operators
3. **ShiftSummary**: Aggregated view for dashboards

## Detailed Design

### Status Types and Triggering Microservices

Here's a comprehensive table outlining all statuses, the microservices that trigger them, and the corresponding actions:



# Status Management System

## Status Types and Triggering Microservices

| Status Code | Status Name | Type | Description | Triggering Microservice | Action/Event | Impact on Shift Status |
|-------------|-------------|------|-------------|------------------------|--------------|------------------------|
| P | Present | Shift | Operator is present for shift | Shift Management | Visual check by Team Leader / Badge scan | Primary status |
| A | Absent | Shift | Operator is absent without authorization | Shift Management | Visual check by Team Leader | Primary status |
| R | Delayed | Shift | Operator is late but approved | Request | "Autorisation d'entrée" request approval | Temporary status until arrival |
| TL | Legal Authorization | Operator | Legal authorization (e.g., Breastfeeding) | Request | TL raises legal authorization request | Absent with authorization |
| CR | Leave Holiday | Operator | Planned vacation/leave | Request | Leave Request (REQ 07) | Absent with authorization |
| DI | Travel Order (Day) | Operator | Travel during day shift | Request | Travel Order Request (REQ 03) | Absent with authorization |
| DN | Travel Order (Night) | Operator | Travel during night shift | Request | Travel Order Request (REQ 03) | Absent with authorization |
| TE | External Work | Operator | Working outside regular location | Request | External Work Request (REQ 02) | Present but at different location |
| CTP | Planned TLO | Operator | Planned Technical Layoff | Working Plan | Assignment by Shift Leader | Absent with authorization |
| CTN | Unplanned TLO | Operator | Unplanned Technical Layoff | Request | Unplanned TLO Request | Absent with authorization |
| AT | Workplace Accident | Operator | Injury at workplace | Request | Work accident request by nurse | Absent with authorization |
| MT | Maternity Leave | Operator | Maternity leave | Request | Maternity Leave request | Absent with authorization (scheduled) |
| AP | Suspension | Operator | Disciplinary suspension | Request | Suspension request by Employee Relations | Absent with authorization |
| AE | Exceptional Leave | Operator | Special circumstances (e.g., COVID) | Request | Exceptional leave request | Absent with authorization |


## Status Change Workflow

| Actor | Action | System Response | Notification |
|-------|--------|----------------|--------------|
| Team Leader | Start Shift | Activates shift management | N/A |
| Team Leader | Visual Check | Updates operator status | N/A |
| Team Leader | Forget to Start Shift | N/A | Alert to Shift Leader |
| Team Leader | Close Shift | Finalizes status recording | Notification to Shift Leader |
| Team Leader | Leave status empty | Warning popup | N/A |
| Shift Leader | Validate daily records | Approves status recordings | Notification to TKS |
| Request MS | Status change approval | Updates operator status | Notification to relevant parties |
| System | Effective date reached | Automatic status transition | Update in dashboard |


### CQRS System Architecture

Let's design the CQRS architecture for your system:

```markdown
# CQRS Architecture for Status Management System

## Core Components

### Command Side
- **Commands**: Instructions to change status (StartShift, MarkPresent, RequestLeave, etc.)
- **Command Handlers**: Process commands and emit events
- **Domain Models**: Business logic for status changes and validations

### Event Side
- **Events**: Immutable records of status changes (StatusChanged, ShiftStarted, LeaveApproved, etc.)
- **Event Handlers**: Process events and update read models
- **Event Store**: CosmosDB container storing all events in chronological order

### Query Side
- **Read Models**: Optimized views of data in CosmosDB containers
- **Query Handlers**: Process queries against read models
- **Projections**: Build and maintain read models from events

## CosmosDB Structure

### Event Store Containers
1. **StatusChangeEvents**
   - PartitionKey: OperatorId
   - Properties:
     - EventId (GUID)
     - EventType (string)
     - OperatorId (string)
     - OldStatus (string)
     - NewStatus (string)
     - EffectiveDate (datetime)
     - ExpiryDate (datetime, nullable)
     - Reason (string)
     - SourceMicroservice (string)
     - RequestId (string, nullable)
     - Timestamp (datetime)
     - Actor (string)

2. **ScheduledStatusEvents**
   - PartitionKey: EffectiveDate
   - Properties:
     - EventId (GUID)
     - OperatorId (string)
     - StatusCode (string)
     - EffectiveDate (datetime)
     - ExpiryDate (datetime, nullable)
     - Reason (string)
     - RequestId (string, nullable)
     - CreatedBy (string)
     - CreatedTimestamp (datetime)

### Read Model Containers
1. **OperatorStatus**
   - PartitionKey: DepartmentId
   - Properties:
     - OperatorId (string)
     - Name (string)
     - DepartmentId (string)
     - TeamLeaderId (string)
     - CurrentStatusCode (string)
     - CurrentStatusName (string)
     - StatusType (string) - "Shift" or "Operator"
     - EffectiveFrom (datetime)
     - EffectiveTo (datetime, nullable)
     - LastUpdateTimestamp (datetime)
     - SourceEventId (string)
     - RequestId (string, nullable)
     - IsContainment (boolean)

2. **ShiftTracker**
   - PartitionKey: ShiftDate
   - Properties:
     - ShiftId (string)
     - ShiftDate (date)
     - ShiftType (string) - "Morning", "Evening", "Night"
     - OperatorId (string)
     - OperatorName (string)
     - ShiftStatus (string) - "Present", "Absent", "Delayed"
     - OperatorStatus (string) - Extended status code
     - TeamLeaderId (string)
     - ShiftLeaderId (string)
     - ShiftStarted (boolean)
     - ShiftClosed (boolean)
     - StartTime (datetime, nullable)
     - EndTime (datetime, nullable)
     - ValidationStatus (string) - "Pending", "Validated", "Forwarded"

3. **ShiftLeaderDashboard**
   - PartitionKey: ShiftDate
   - Properties:
     - ShiftDate (date)
     - ShiftType (string)
     - TeamLeaderId (string)
     - TeamLeaderName (string)
     - ShiftStarted (boolean)
     - ShiftStartTime (datetime, nullable)
     - ShiftClosed (boolean)
     - ShiftCloseTime (datetime, nullable)
     - TotalOperators (int)
     - PresentCount (int)
     - AbsentCount (int)
     - DelayedCount (int)
     - AuthorizedAbsenceCount (int)
     - ValidationStatus (string)

4. **ScheduledStatusChanges**
   - PartitionKey: OperatorId
   - Properties:
     - ScheduleId (string)
     - OperatorId (string)
     - OperatorName (string)
     - FutureStatusCode (string)
     - FutureStatusName (string)
     - EffectiveFrom (datetime)
     - EffectiveTo (datetime, nullable)
     - RequestId (string, nullable)
     - SourceMicroservice (string)
     - CreatedTimestamp (datetime)

```

### Workflow Processes

Now let's outline the core workflows and how they'll be implemented:

```markdown
# Status Management Workflows

## 1. Shift Status Management

### Start Shift Process
1. Team Leader clicks "Start Shift" button
2. System:
   - Creates `ShiftStarted` event
   - Updates ShiftTracker records for all operators in team
   - Marks shift as active in ShiftLeaderDashboard
   - Loads current operator statuses from OperatorStatus
3. If Team Leader doesn't click "Start Shift":
   - System sends alert to Shift Leader 1 hour after shift start

### Visual Check Process
1. Team Leader performs visual check of operators
2. For each operator, Team Leader:
   - Confirms presence/absence
   - System creates `ShiftStatusUpdated` event
   - Updates ShiftTracker and OperatorStatus
3. Containment operators appear grayed out (loaded from Containment Management microservice)

### Close Shift Process
1. Team Leader clicks "Close My Shift"
2. System:
   - Validates all operators have status assigned
   - If incomplete, shows warning popup
   - Creates `ShiftClosed` event
   - Updates ShiftLeaderDashboard
   - Sends notification to Shift Leader
3. Shift Leader validates daily records
4. System forwards to TKS upon validation

## 2. Operator Status Management

### Status Change Request
1. Request microservice sends StatusChangeRequest:
   ```json
   {
     "requestId": "REQ123",
     "operatorId": "OP456",
     "statusCode": "CR",
     "reason": "Annual Leave",
     "effectiveFrom": "2025-05-01T00:00:00Z",
     "effectiveTo": "2025-05-07T23:59:59Z",
     "requestType": "LeaveRequest",
     "requester": "TL789"
   }
   ```
2. System:
   - Validates request
   - Creates `StatusChangeRequested` event
   - If effectiveFrom is in future:
     - Adds to ScheduledStatusEvents
     - Updates ScheduledStatusChanges read model
   - If effectiveFrom is now:
     - Creates `StatusChanged` event
     - Updates OperatorStatus
     - Updates ShiftTracker if applicable

### Scheduled Status Activation
1. Time-triggered function runs every 15 minutes
2. For each ScheduledStatusEvent where effectiveDate <= now:
   - Creates `ScheduledStatusActivated` event
   - Updates OperatorStatus
   - Updates current ShiftTracker if during active shift
   - Removes from ScheduledStatusEvents
   - Updates ScheduledStatusChanges read model

### Status Expiration
1. Time-triggered function runs daily
2. For each OperatorStatus where effectiveTo <= now:
   - Creates `StatusExpired` event
   - Resets status to default (depends on context)
   - Updates OperatorStatus and relevant read models

## 3. Status Mapping Logic

### Operator Status to Shift Status Mapping
1. When operator status changes:
   ```csharp
   // Pseudocode
   string DetermineShiftStatus(string operatorStatusCode)
   {
       switch(operatorStatusCode)
       {
           case "P": return "Present";
           case "R": return "Delayed";
           case "TL":
           case "CR": 
           case "DI":
           case "DN":
           case "CTP":
           case "CTN":
           case "AT":
           case "MT":
           case "AP":
           case "AE": return "Absent"; // Authorized absence
           case "TE": return "Present"; // Present but at different location
           default: return "Absent";
       }
   }
   ```

2. System automatically updates ShiftTracker with mapped status

### Status Correction Process
1. Team Leader can modify operator status until D+1 7:00 AM for night shift
2. After deadline:
   - Team Leader submits Correction of Clocking request
   - Request microservice processes approval workflow
   - Upon approval, creates `StatusCorrected` event
   - Updates all relevant read models

## 4. Integration with Other Microservices

### Request Microservice Integration
1. Request microservice sends status change requests via message bus
2. Status management service processes requests based on type:
   - Leave requests (CR)
   - Legal authorizations (TL)
   - Travel orders (DI/DN)
   - External work (TE)
   - Unplanned TLO (CTN)
   - Workplace accidents (AT)
   - Maternity leave (MT)
   - Suspensions (AP)
   - Exceptional leave (AE)

### Working Plan Microservice Integration
1. Working Plan microservice sends:
   - Planned TLO assignments (CTP)
   - Shift schedule changes
2. Status management service updates ScheduledStatusEvents

### Containment Management Integration
1. Containment Management microservice provides containment status
2. Status service marks operators as containment in UI (grayed out)
3. Still requires Team Leader to record actual presence

```

### Data Flow and Change Feed Implementation

```markdown
# CosmosDB Change Feed Implementation

## Overview
The Change Feed in CosmosDB acts as the backbone of our CQRS implementation, propagating events to read models and ensuring consistency across the system.

## Implementation Details

### 1. Event Store to Read Models

```csharp
// StatusChangeEvents Change Feed Processor
public class StatusChangeEventProcessor : IChangeFeedProcessor
{
    private readonly CosmosClient _cosmosClient;
    private readonly ILogger _logger;
    
    public StatusChangeEventProcessor(CosmosClient cosmosClient, ILogger logger)
    {
        _cosmosClient = cosmosClient;
        _logger = logger;
    }
    
    public async Task ProcessChangesAsync(IReadOnlyCollection<StatusChangeEvent> changes, CancellationToken cancellationToken)
    {
        foreach (var change in changes)
        {
            try
            {
                // Update OperatorStatus read model
                await UpdateOperatorStatusAsync(change);
                
                // Update ShiftTracker if status change affects current shift
                await UpdateShiftTrackerAsync(change);
                
                // Update ShiftLeaderDashboard aggregated metrics
                await UpdateShiftLeaderDashboardAsync(change);
                
                _logger.LogInformation($"Processed status change event {change.EventId} for operator {change.OperatorId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing status change event {change.EventId}");
                // Implement retry or error handling strategy
            }
        }
    }
    
    private async Task UpdateOperatorStatusAsync(StatusChangeEvent change)
    {
        var container = _cosmosClient.GetContainer("StatusManagement", "OperatorStatus");
        
        try
        {
            // Try to get existing record
            var response = await container.ReadItemAsync<OperatorStatusModel>(
                change.OperatorId, 
                new PartitionKey(GetDepartmentId(change.OperatorId)));
                
            var status = response.Resource;
            
            // Update existing status
            status.CurrentStatusCode = change.NewStatus;
            status.CurrentStatusName = GetStatusName(change.NewStatus);
            status.StatusType = DetermineStatusType(change.NewStatus);
            status.EffectiveFrom = change.EffectiveDate;
            status.EffectiveTo = change.ExpiryDate;
            status.LastUpdateTimestamp = DateTime.UtcNow;
            status.SourceEventId = change.EventId;
            status.RequestId = change.RequestId;
            
            await container.ReplaceItemAsync(status, status.OperatorId, new PartitionKey(status.DepartmentId));
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            // Create new status record if it doesn't exist
            var newStatus = new OperatorStatusModel
            {
                OperatorId = change.OperatorId,
                Name = await GetOperatorNameAsync(change.OperatorId),
                DepartmentId = GetDepartmentId(change.OperatorId),
                TeamLeaderId = await GetTeamLeaderIdAsync(change.OperatorId),
                CurrentStatusCode = change.NewStatus,
                CurrentStatusName = GetStatusName(change.NewStatus),
                StatusType = DetermineStatusType(change.NewStatus),
                EffectiveFrom = change.EffectiveDate,
                EffectiveTo = change.ExpiryDate,
                LastUpdateTimestamp = DateTime.UtcNow,
                SourceEventId = change.EventId,
                RequestId = change.RequestId,
                IsContainment = await IsContainmentOperatorAsync(change.OperatorId)
            };
            
            await container.CreateItemAsync(newStatus, new PartitionKey(newStatus.DepartmentId));
        }
    }
    
    // Additional implementation methods for UpdateShiftTrackerAsync and UpdateShiftLeaderDashboardAsync
    // ...
}

// ScheduledStatusEvents Change Feed Processor
public class ScheduledStatusEventProcessor : IChangeFeedProcessor
{
    // Similar implementation for processing scheduled status events
    // and updating the ScheduledStatusChanges read model
    // ...
}
```

### 2. Time-Triggered Functions for Scheduled Status Changes

```csharp
public class ScheduledStatusActivationFunction
{
    private readonly CosmosClient _cosmosClient;
    private readonly ILogger _logger;
    
    [FunctionName("ActivateScheduledStatuses")]
    public async Task Run([TimerTrigger("0 */15 * * * *")] TimerInfo myTimer)
    {
        var eventsContainer = _cosmosClient.GetContainer("StatusManagement", "ScheduledStatusEvents");
        
        // Query for events that need activation
        var currentTime = DateTime.UtcNow;
        var queryDefinition = new QueryDefinition(
            "SELECT * FROM c WHERE c.EffectiveDate <= @currentTime")
            .WithParameter("@currentTime", currentTime);
            
        var query = eventsContainer.GetItemQueryIterator<ScheduledStatusEvent>(queryDefinition);
        
        while (query.HasMoreResults)
        {
            var results = await query.ReadNextAsync();
            
            foreach (var scheduledEvent in results)
            {
                try
                {
                    // Create status change event
                    var statusChangeEvent = new StatusChangeEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        EventType = "ScheduledStatusActivated",
                        OperatorId = scheduledEvent.OperatorId,
                        OldStatus = await GetCurrentOperatorStatusAsync(scheduledEvent.OperatorId),
                        NewStatus = scheduledEvent.StatusCode,
                        EffectiveDate = scheduledEvent.EffectiveDate,
                        ExpiryDate = scheduledEvent.ExpiryDate,
                        Reason = scheduledEvent.Reason,
                        SourceMicroservice = "StatusManagement",
                        RequestId = scheduledEvent.RequestId,
                        Timestamp = currentTime,
                        Actor = "System"
                    };
                    
                    // Store the status change event
                    var eventContainer = _cosmosClient.GetContainer("StatusManagement", "StatusChangeEvents");
                    await eventContainer.CreateItemAsync(statusChangeEvent, new PartitionKey(statusChangeEvent.OperatorId));
                    
                    // Delete the scheduled event
                    await eventsContainer.DeleteItemAsync<ScheduledStatusEvent>(
                        scheduledEvent.EventId, 
                        new PartitionKey(scheduledEvent.EffectiveDate.ToString("yyyy-MM-dd")));
                        
                    _logger.LogInformation($"Activated scheduled status {scheduledEvent.EventId} for operator {scheduledEvent.OperatorId}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error activating scheduled status {scheduledEvent.EventId}");
                    // Implement retry or error handling strategy
                }
            }
        }
    }
    
    // Helper methods
    // ...
}
```

### 3. Status Expiration Function

```csharp
public class StatusExpirationFunction
{
    private readonly CosmosClient _cosmosClient;
    private readonly ILogger _logger;
    
    [FunctionName("ProcessStatusExpirations")]
    public async Task Run([TimerTrigger("0 0 0 * * *")] TimerInfo myTimer) // Run daily at midnight
    {
        var operatorStatusContainer = _cosmosClient.GetContainer("StatusManagement", "OperatorStatus");
        
        // Query for statuses that have expired
        var currentTime = DateTime.UtcNow;
        var queryDefinition = new QueryDefinition(
            "SELECT * FROM c WHERE c.EffectiveTo <= @currentTime AND c.EffectiveTo != null")
            .WithParameter("@currentTime", currentTime);
            
        var query = operatorStatusContainer.GetItemQueryIterator<OperatorStatusModel>(queryDefinition);
        
        while (query.HasMoreResults)
        {
            var results = await query.ReadNextAsync();
            
            foreach (var status in results)
            {
                try
                {
                    // Create status expiration event
                    var statusExpirationEvent = new StatusChangeEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        EventType = "StatusExpired",
                        OperatorId = status.OperatorId,
                        OldStatus = status.CurrentStatusCode,
                        NewStatus = "P", // Default to Present, but logic may vary
                        EffectiveDate = currentTime,
                        ExpiryDate = null,
                        Reason = "Status expiration",
                        SourceMicroservice = "StatusManagement",
                        RequestId = null,
                        Timestamp = currentTime,
                        Actor = "System"
                    };
                    
                    // Store the status change event
                    var eventContainer = _cosmosClient.GetContainer("StatusManagement", "StatusChangeEvents");
                    await eventContainer.CreateItemAsync(statusExpirationEvent, new PartitionKey(statusExpirationEvent.OperatorId));
                    
                    _logger.LogInformation($"Processed status expiration for operator {status.OperatorId}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing status expiration for operator {status.OperatorId}");
                    // Implement retry or error handling strategy
                }
            }
        }
    }
}
```

```

### System Architecture Diagram

```mermaid
flowchart TD
    subgraph UI[User Interface]
        TLInterface[Team Leader Interface]
        SLInterface[Shift Leader Interface]
        TKSInterface[TKS Interface]
    end
    
    subgraph CommandSide[Command Side]
        API[Status Management API]
        CommandHandlers[Command Handlers]
        DomainModels[Domain Models]
    end
    
    subgraph Integration[Integration Layer]
        MessageBus[Message Bus]
        IntegrationEvents[Integration Events]
    end
    
    subgraph EventStore[Event Store]
        StatusChangeEvents[(StatusChangeEvents)]
        ScheduledStatusEvents[(ScheduledStatusEvents)]
    end
    
    subgraph ReadModels[Read Models]
        OperatorStatus[(OperatorStatus)]
        ShiftTracker[(ShiftTracker)]
        ShiftLeaderDashboard[(ShiftLeaderDashboard)]
        ScheduledStatusChanges[(ScheduledStatusChanges)]
    end
    
    subgraph Functions[Azure Functions]
        ChangeFeedProcessors[Change Feed Processors]
        ScheduledActivation[Scheduled Status Activation]
        ExpirationProcessor[Status Expiration Processor]
    end
    
    subgraph ExternalMS[External Microservices]
        RequestMS[Request MS]
        WorkingPlanMS[Working Plan MS]
        ContainmentMS[Containment MS]
    end
    
    TLInterface --> API
    SLInterface --> API
    TKSInterface --> API
    
    API --> CommandHandlers
    CommandHandlers --> DomainModels
    DomainModels --> StatusChangeEvents
    DomainModels --> ScheduledStatusEvents
    
    StatusChangeEvents --> ChangeFeedProcessors
    ScheduledStatusEvents --> ChangeFeedProcessors
    ChangeFeedProcessors --> OperatorStatus
    ChangeFeedProcessors --> ShiftTracker
    ChangeFeedProcessors --> ShiftLeaderDashboard
    ChangeFeedProcessors --> ScheduledStatusChanges
    
    ScheduledActivation --> ScheduledStatusEvents
    ScheduledActivation --> StatusChangeEvents
    ExpirationProcessor --> OperatorStatus
    ExpirationProcessor --> StatusChangeEvents
    
    RequestMS --> MessageBus
    WorkingPlanMS --> MessageBus
    ContainmentMS --> MessageBus
    MessageBus --> IntegrationEvents
    IntegrationEvents --> CommandHandlers
    
    OperatorStatus --> TLInterface
    ShiftTracker --> TLInterface
    ShiftLeaderDashboard --> SLInterface
    ScheduledStatusChanges --> TLInterface

```

### Status Mapping and Relations

```markdown
# Status Mapping and Relations

## Shift Status vs. Operator Status

### Conceptual Distinction

1. **Shift Status**:
   - Represents immediate presence in a specific shift
   - Has three core states: Present, Absent, Delayed
   - Recorded daily by Team Leaders
   - Directly observable during visual checks

2. **Operator Status**:
   - Represents an extended state that may affect multiple shifts
   - Has many possible states (CR, TL, MT, etc.)
   - Often triggered by requests/approvals
   - May have effective dates spanning multiple shifts

### Status Hierarchy and Mapping

The system needs to establish a clear relationship between these two status types:

```
Operator Status (Extended)
    |
    ├─> Affects Shift Status (Daily Presence)
    |
    └─> Attributes additional context to the Shift Status
```

## Implementation Approach

### 1. Status Type Classification

Each status code will be classified into one of these categories:
- **Shift Status**: P (Present), A (Absent), R (Delayed)
- **Operator Status**: All other status codes (CR, TL, MT, etc.)

### 2. Data Structure for Status Mapping

```csharp
public class StatusMapping
{
    public string StatusCode { get; set; }
    public string StatusName { get; set; }
    public string StatusType { get; set; } // "Shift" or "Operator"
    public string DefaultShiftStatus { get; set; } // Mapped shift status
    public bool RequiresEffectiveDate { get; set; }
    public bool CanOverrideShiftStatus { get; set; }
    public int DisplayOrder { get; set; }
}

// Example mappings
var statusMappings = new List<StatusMapping>
{
    new StatusMapping { StatusCode = "P", StatusName = "Present", StatusType = "Shift", DefaultShiftStatus = "Present", RequiresEffectiveDate = false, CanOverrideShiftStatus = true, DisplayOrder = 1 },
    new StatusMapping { StatusCode = "A", StatusName = "Absent", StatusType = "Shift", DefaultShiftStatus = "Absent", RequiresEffectiveDate = false, CanOverrideShiftStatus = true, DisplayOrder = 2 },
    new StatusMapping { StatusCode = "R", StatusName = "Delayed", StatusType = "Shift", DefaultShiftStatus = "Delayed", RequiresEffectiveDate = false, CanOverrideShiftStatus = true, DisplayOrder = 3 },
    new StatusMapping { StatusCode = "TL", StatusName = "Legal Authorization", StatusType = "Operator", DefaultShiftStatus = "Absent", RequiresEffectiveDate = true, CanOverrideShiftStatus = false, DisplayOrder = 4 },
    new StatusMapping { StatusCode = "CR", StatusName = "Leave Holiday", StatusType = "Operator", DefaultShiftStatus = "Absent", RequiresEffectiveDate = true, CanOverrideShiftStatus = false, DisplayOrder = 5 },
    // Additional mappings...
};
```

### 3. Status Resolution Logic

When determining an operator's status for a specific shift:

```csharp
public class StatusResolver
{
    private readonly IRepository<OperatorStatusModel> _operatorStatusRepository;
    private readonly IRepository<StatusMapping> _statusMappingRepository;
    
    public async Task<ShiftStatusResult> ResolveShiftStatus(string operatorId, DateTime shiftDate, string shiftType)
    {
        // Get operator's current extended status
        var operatorStatus = await _operatorStatusRepository.GetByIdAsync(operatorId);
        
        // Check if status is effective for this shift
        bool isStatusEffective = IsStatusEffectiveForShift(operatorStatus, shiftDate);
        
        // Get mapping for this status
        var statusMapping = await _statusMappingRepository.GetByIdAsync(operatorStatus.CurrentStatusCode);
        
        if (isStatusEffective && statusMapping.StatusType == "Operator")
        {
            // Operator status is effective, use its default shift status mapping
            return new ShiftStatusResult
            {
                ShiftStatus = statusMapping.DefaultShiftStatus,
                OperatorStatus = operatorStatus.CurrentStatusCode,
                IsOverridable = statusMapping.CanOverrideShiftStatus,
                Reason = operatorStatus.Reason
            };
        }
        
        // Default to normal shift status rules (visual check)
        return new ShiftStatusResult
        {
            ShiftStatus = "Unknown", // Requires visual check
            OperatorStatus = null,
            IsOverridable = true,
            Reason = null
        };
    }
    
    private bool IsStatusEffectiveForShift(OperatorStatusModel status, DateTime shiftDate)
    {
        // Check if status is effective for this date
        if (status.EffectiveFrom.Date <= shiftDate.Date && 
            (status.EffectiveTo == null || status.EffectiveTo.Value.Date >= shiftDate.Date))
        {
            return true;
        }
        return false;
    }
}
```

### 4. Handling Effective Dates and Scheduled Status Changes

For statuses with future effective dates (like Maternity Leave):

1. Store in ScheduledStatusEvents container with future effective date
2. ScheduledActivation function processes these events when the date arrives
3. At activation time:
   - Create StatusChangeEvent
   - Update OperatorStatus
```