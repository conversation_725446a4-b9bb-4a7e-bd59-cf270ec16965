# Simplified Annual Calendar Design

## Table of Contents
1. [Overview](#1-overview)
2. [Core Architecture](#2-core-architecture)
3. [Data Models](#3-data-models)
4. [API Endpoints](#4-api-endpoints)
5. [Business Rules](#5-business-rules)
6. [Frontend Implementation](#6-frontend-implementation)
7. [Technology Stack](#7-technology-stack)

## 1. Overview

The Annual Calendar module provides a straightforward way to manage yearly calendars, holidays, and working days. The design focuses on simplicity while maintaining flexibility for different regional requirements.

### Key Features
- Year calendar initialization and management
- Holiday definition and management
- Working day calculations
- Week and month summaries
- Calendar visualization

## 2. Core Architecture

```mermaid
graph TD
    subgraph Backend
        API[REST API Layer]
        BS[Business Services]
        DAL[Data Access Layer]
        DB[(Database)]
    end
    
    subgraph Frontend
        RC[React Components]
        RS[React State Management]
        RL[React Libraries]
    end
    
    Frontend --> API
    API --> BS
    BS --> DAL
    DAL --> DB
```

### Core Components
1. **REST API Layer**: Provides endpoints for calendar operations
2. **Business Services**: Implements calendar logic and validations
3. **Data Access Layer**: Handles database interactions
4. **React Frontend**: Calendar visualization and management interface

## 3. Data Models

We'll use a simplified SQL database schema with just three core tables:

```sql
-- Year configuration
CREATE TABLE Years (
    YearId INT PRIMARY KEY,
    Status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    DefaultWorkingDays VARCHAR(20) NOT NULL DEFAULT '1,2,3,4,5',
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Holiday definition
CREATE TABLE Holidays (
    HolidayId INT IDENTITY(1,1) PRIMARY KEY,
    Date DATE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    HolidayType VARCHAR(20) NOT NULL,
    Color VARCHAR(20) NOT NULL,
    IsWorkingDay BIT NOT NULL DEFAULT 0,
    IsRecurring BIT NOT NULL DEFAULT 0,
    RecurringMonth TINYINT NULL,
    RecurringDay TINYINT NULL,
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_holiday_date (Date)
);

-- Month summary for quick lookups
CREATE TABLE MonthSummaries (
    YearMonth VARCHAR(7) PRIMARY KEY, -- Format: YYYY-MM
    Year INT NOT NULL,
    Month TINYINT NOT NULL,
    TotalDays TINYINT NOT NULL,
    WorkingDays TINYINT NOT NULL,
    HolidayCount TINYINT NOT NULL DEFAULT 0,
    WeekendCount TINYINT NOT NULL,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## 4. API Endpoints

### Calendar Management
- `GET /api/calendar/{year}` - Get full year calendar
- `POST /api/calendar/{year}` - Initialize year calendar
- `GET /api/calendar/{year}/{month}` - Get month details

### Holiday Management
- `GET /api/holidays?year={year}&month={month}` - Get holidays for period
- `POST /api/holidays` - Create new holiday
- `PUT /api/holidays/{id}` - Update existing holiday
- `DELETE /api/holidays/{id}` - Delete holiday

### Summary Endpoints
- `GET /api/summaries/year/{year}` - Get year summary
- `GET /api/summaries/month/{year}/{month}` - Get month summary

## 5. Business Rules

### Holiday Types

| Type | Color | Working Day | Description |
|------|-------|-------------|-------------|
| NATIONAL | RED | No | National holidays |
| RELIGIOUS | GREEN | No | Religious holidays |
| TLO | BLUE | No | Temporary Layoff days |
| FOURTH_SATURDAY | ORANGE | No | Fourth Saturday of month |
| PLANT_HOLIDAY | YELLOW | No | Plant-wide holiday |

### Calendar Calculations
- Working days = Total days - Weekends - Holidays
- Fourth Saturday automatically calculated
- Regional variation handled through configuration
- Religious holidays can be updated yearly

## 6. Frontend Implementation

### 6.1 React Libraries for Calendar Management

| Library | Purpose | Link |
|---------|---------|------|
| **React Big Calendar** | Full-featured calendar component | [https://github.com/jquense/react-big-calendar](https://github.com/jquense/react-big-calendar) |
| **React Calendar** | Simple calendar component | [https://github.com/wojtekmaj/react-calendar](https://github.com/wojtekmaj/react-calendar) |
| **React-DatePicker** | Date selection component | [https://github.com/Hacker0x01/react-datepicker](https://github.com/Hacker0x01/react-datepicker) |
| **Day.js** | Date manipulation library | [https://day.js.org/](https://day.js.org/) |
| **date-fns** | Date utility library | [https://date-fns.org/](https://date-fns.org/) |

### 6.2 UI Component Libraries

| Library | Purpose | Link |
|---------|---------|------|
| **MUI (Material-UI)** | React UI framework with calendar components | [https://mui.com/](https://mui.com/) |
| **Ant Design** | UI library with calendar components | [https://ant.design/](https://ant.design/) |
| **Chakra UI** | Accessible component library | [https://chakra-ui.com/](https://chakra-ui.com/) |
| **TanStack Table** | Table component for data display | [https://tanstack.com/table/v8](https://tanstack.com/table/v8) |
| **React-DnD** | Drag and drop for interactive calendars | [https://react-dnd.github.io/react-dnd/](https://react-dnd.github.io/react-dnd/) |

### 6.3 State Management

| Library | Purpose | Link |
|---------|---------|------|
| **Redux Toolkit** | State management with reducers | [https://redux-toolkit.js.org/](https://redux-toolkit.js.org/) |
| **React Query** | Data fetching and caching | [https://tanstack.com/query/latest](https://tanstack.com/query/latest) |
| **Zustand** | Lightweight state management | [https://github.com/pmndrs/zustand](https://github.com/pmndrs/zustand) |

### 6.4 Sample Calendar Component

```jsx
import React, { useState } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import moment from 'moment';
import { useQuery } from 'react-query';
import api from '../services/api';

const localizer = momentLocalizer(moment);

// Custom event component to show holidays
const HolidayEvent = ({ event }) => (
  <div style={{ 
    background: event.color || '#3174ad',
    padding: '2px 5px',
    borderRadius: '5px',
    color: 'white'
  }}>
    {event.title}
  </div>
);

const AnnualCalendarView = ({ year }) => {
  const [view, setView] = useState('month');
  
  // Fetch calendar data
  const { data, isLoading } = useQuery(['calendar', year], () => 
    api.get(`/api/calendar/${year}`)
  );
  
  // Transform holidays into events format for react-big-calendar
  const events = data ? data.holidays.map(holiday => ({
    id: holiday.id,
    title: holiday.name,
    start: new Date(holiday.date),
    end: new Date(holiday.date),
    allDay: true,
    color: holiday.color,
    type: holiday.holidayType
  })) : [];

  if (isLoading) return <div>Loading calendar...</div>;

  return (
    <div style={{ height: 700 }}>
      <Calendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        defaultView={view}
        onView={setView}
        views={['month', 'week', 'agenda']}
        components={{
          event: HolidayEvent
        }}
      />
    </div>
  );
};

export default AnnualCalendarView;
```

## 7. Technology Stack

### Backend
- **Language**: Node.js with TypeScript
- **Framework**: NestJS or Express
- **Database**: MySQL or PostgreSQL
- **ORM**: TypeORM or Sequelize
- **API Documentation**: Swagger/OpenAPI

### Frontend
- **Framework**: React
- **State Management**: React Query + Context API
- **UI Components**: Material-UI or Ant Design
- **Calendar Library**: React Big Calendar or React Calendar
- **Date Utilities**: date-fns or Day.js
- **HTTP Client**: Axios or fetch API

This simplified design focuses on core functionality while providing flexibility for extension. The React frontend recommendations give multiple options to implement a robust calendar UI.
