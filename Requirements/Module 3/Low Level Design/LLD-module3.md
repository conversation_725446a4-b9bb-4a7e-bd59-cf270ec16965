
# Low-Level Design Document:  Module 3 Microservices Architecture

## Document Information

**Version:** 1.0.0  
**Last Updated:** 2023-03-26  
**Status:** Completed  

## Executive Summary

### Key Features

- Microservices-based architecture with three core services
- Event-driven communication with CQRS pattern
- Comprehensive annual calendar management
- Advanced headcount tracking and badge management
- Flexible working plan and shift management
- Document database with optimized query patterns
- Integration with production planning systems

### Business Benefits

- Streamlined workforce planning and scheduling
- Improved visibility into operator availability and status
- Enhanced production planning with calendar integration
- Efficient shift and activity management
- Real-time status tracking of operators
- Data-driven decision support for manufacturing operations
- Seamless integration with other platform modules

## Table of Contents

1. [Overview](#1-overview)
   - [Purpose and Scope](#11-purpose-and-scope)
   - [Key Components](#12-key-components)
2. [Architecture Overview](#2-architecture-overview)
   - [High-Level Architecture](#21-high-level-architecture)
   - [Technical Components](#22-technical-components)
   - [Data Duplication and CQRS Approach](#23-data-duplication-and-cqrs-approach)
3. [Technology Stack](#3-technology-stack)
4. [Core Microservices](#4-core-microservices)
   - [Annual Calendar Service](#41-annual-calendar-service)
   - [Headcount Service](#42-headcount-service)
   - [Working Plan Service](#43-working-plan-service)
5. [Integration Patterns](#5-integration-patterns)
   - [Event-Driven Communication](#51-event-driven-communication)
   - [Cosmos DB Change Feed](#52-cosmos-db-change-feed)
   - [Read Model Projections](#53-read-model-projections)
6. [Security](#6-security)
7. [Performance Considerations](#7-performance-considerations)
8. [Deployment Strategy](#8-deployment-strategy)

## 1. Overview

### 1.1 Purpose and Scope

Module 3 provides a comprehensive solution for managing manufacturing workforce planning, calendar operations, and headcount tracking. It implements a microservices architecture following Domain-Driven Design principles to ensure flexibility, scalability, and maintainability. The module enables manufacturing facilities to efficiently manage their annual calendar, track operator status and attendance, and create detailed working plans for production needs.

### 1.2 Key Components

The system consists of three primary microservices:

1. Annual Calendar Service
2. Headcount Service
3. Working Plan Service

Each service is supported by:
- Document database (Cosmos DB) for data storage
- Change Feed for event-driven communication
- CQRS pattern for read/write separation
- Role-based access control for security

## 2. Architecture Overview

### 2.1 High-Level Architecture

```mermaid
graph TD
    Client[Client Applications] --> APIM[Azure API Management]
    APIM --> ACS[Annual Calendar Service]
    APIM --> HS[Headcount Service]
    APIM --> WPS[Working Plan Service]
    
    subgraph Storage
        Cosmos[Cosmos DB]
    end
    
    %% Annual Calendar Service internal structure
    subgraph ACS
        ACS_W[Write Models] --> ACS_CF[Change Feed]
        ACS_CF --> ACS_R[Read Models]
    end
    
    %% Headcount Service internal structure
    subgraph HS
        HS_W[Write Models] --> HS_CF[Change Feed]
        HS_CF --> HS_R[Read Models]
    end
    
    %% Working Plan Service internal structure
    subgraph WPS
        WPS_W[Write Models] --> WPS_CF[Change Feed]
        WPS_CF --> WPS_R[Read Models]
    end
    
    ACS --> Storage
    HS --> Storage
    WPS --> Storage
```

### 2.2 Technical Components

1. **NestJS Microservices**: Each service implemented as a NestJS application
2. **Cosmos DB**: NoSQL database with flexible schema support for both write and read models
3. **Change Feed Processor**: Propagates changes between write and read models
4. **Azure API Management**: Unified entry point with authentication and routing
5. **Event Sourcing**: Captures all state changes as events

### 2.3 Data Duplication and CQRS Approach

The Connected Workers Platform Module 3 implements a complete data isolation strategy between microservices:

1. **No Direct Communication**: Microservices do not communicate directly with each other, not even through asynchronous messaging systems like Service Bus.

2. **CQRS Implementation**: Each microservice maintains:
   - **Write Models**: Optimized for recording state changes as events
   - **Read Models**: Denormalized projections optimized for specific query patterns
   - **Change Feed Processor**: Bridges the gap between write and read models

3. **Dual-Purpose Data Structure**:
   - Each microservice contains dedicated containers for write operations (events/commands)
   - Each microservice also maintains optimized read models for queries
   - The separation allows optimization for both write and read operations

4. **Event Sourcing**: All state changes are recorded as immutable events in write models, providing:
   - Complete audit history
   - Ability to reconstruct state at any point in time
   - Source of truth for the system

5. **Data Duplication Strategy**: When a microservice needs data owned by another microservice:
   - Data is duplicated into read models within the consuming microservice
   - This eliminates service-to-service dependencies and improves resilience

6. **Cosmos DB Change Feed Mechanism**:
   - Detects document changes in write model containers
   - Triggers functions to update corresponding read models
   - Enables eventual consistency between write and read models
   - Facilitates data duplication across microservice boundaries

This architecture provides several advantages:
- **Resilience**: Services can function independently even if other services are unavailable
- **Performance**: Data access is optimized for each service's specific needs
- **Scalability**: Services can scale independently based on their specific load patterns
- **Evolution**: Services can evolve independently with minimal impact on each other

## 3. Technology Stack

| Component | Technology | Description |
|-----------|------------|-------------|
| API Management | Azure API Management | Centralized API gateway for all microservices |
| NoSQL Database | Azure Cosmos DB | Document storage with change feed capabilities |
| Relational Database | Azure SQL Database | Used by Annual Calendar for structured data |
| Authentication | Azure AD B2C | Identity management and authentication |
| Backend | NestJS | TypeScript-based Node.js framework for microservices |
| Containerization | Docker | Container platform for consistent deployment |
| Container Orchestration | Azure Kubernetes Service | Managed Kubernetes for container deployment |
| CI/CD | Azure DevOps | Continuous integration and deployment |
| Monitoring | Application Insights | Real-time performance and error monitoring |
| Logging | ELK Stack | Centralized logging and analysis |

## 4. Core Microservices

### 4.1 Annual Calendar Service

#### 1. Overview

The Annual Calendar microservice is designed to manage and provide calendar-related data, including holidays, working days, and monthly summaries. It is built using NestJS and leverages a SQL database for data storage, along with a calendar library that dynamically generates calendar data. This approach eliminates the need to store individual days in the database, focusing instead on storing only holidays and special working days.

#### 2. Domain Model & Entities

##### 2.1 Year Configuration
```typescript
interface YearConfiguration {
    yearId: number;                  // Primary key (e.g., 2025)
    status: YearStatus;              // DRAFT, ACTIVE, ARCHIVED
    weekStartsOn: number;            // Day when week starts (1-7)
    weekEndsOn: number;              // Day when week ends (1-7)
    defaultWorkingDays: number[];    // Default working days of week
    metadata: {
        createdBy: string;
        createdAt: string;
        updatedAt: string;
        updatedBy: string;
    };
}

enum YearStatus {
    DRAFT = "DRAFT",
    ACTIVE = "ACTIVE",
    ARCHIVED = "ARCHIVED"
}
```

##### 2.2 Holiday
```typescript
interface Holiday {
    holidayId: string;               // UUID
    date: string;                    // ISO date format
    name: string;                    // Holiday name
    holidayType: HolidayType;        // Type of holiday
    color: string;                   // Display color
    isWorkingDay: boolean;           // Whether it's a working day
    isRecurring: boolean;            // Whether it recurs annually
    recurringType?: string;          // How it recurs (if applicable)
    recurringMonth?: number;         // Month of recurrence (if applicable)
    recurringDay?: number;           // Day of recurrence (if applicable)
    metadata: {
        createdBy: string;
        createdAt: string;
        updatedAt: string;
        updatedBy: string;
    };
}

enum HolidayType {
    NATIONAL = "NATIONAL",
    RELIGIOUS = "RELIGIOUS",
    UNPAID_PUBLIC = "UNPAID_PUBLIC",
    TLO = "TLO",
    FOURTH_SATURDAY = "FOURTH_SATURDAY",
    PLANT_HOLIDAY = "PLANT_HOLIDAY"
}
```

##### 2.3 Week & Month Summaries
```typescript
interface WeekSummary {
    weekId: string;                  // UUID
    yearId: number;                  // Reference to YearConfiguration
    weekNumber: number;              // ISO week number
    startDate: string;               // Week start date (ISO format)
    endDate: string;                 // Week end date (ISO format)
    workingDays: number;             // Number of working days
    totalHours: number;              // Total working hours
    status: string;                  // DRAFT, APPROVED, ARCHIVED
    metadata: {
        createdAt: string;
        updatedAt: string;
    };
}

interface MonthConfiguration {
    monthId: string;                 // UUID
    yearId: number;                  // Reference to YearConfiguration
    monthNumber: number;             // Month number (1-12)
    name: string;                    // Month name
    totalDays: number;               // Total days in month
    workingDays: number;             // Number of working days
    fourthSaturday?: string;         // Date of fourth Saturday (if applicable)
    status: string;                  // DRAFT, APPROVED, ARCHIVED
    metadata: {
        createdAt: string;
        updatedAt: string;
    };
}
```

#### 3. Event Model

##### 3.1 Event Types
```typescript
enum CalendarEventType {
    YEAR_INITIALIZED = 'calendar.year.initialized',
    YEAR_STATUS_CHANGED = 'calendar.year.status.changed',
    HOLIDAY_CREATED = 'calendar.holiday.created',
    HOLIDAY_UPDATED = 'calendar.holiday.updated',
    HOLIDAY_DELETED = 'calendar.holiday.deleted',
    WEEK_SUMMARY_CALCULATED = 'calendar.week.summary.calculated',
    MONTH_SUMMARY_CALCULATED = 'calendar.month.summary.calculated',
    FOURTH_SATURDAY_ASSIGNED = 'calendar.fourth.saturday.assigned'
}
```

##### 3.2 Base Event Structure
```typescript
interface CalendarEvent {
    id: string;                    // UUID
    type: CalendarEventType;       // Event type
    version: string;               // Schema version
    timestamp: string;             // ISO timestamp
    metadata: {
        userId: string;            // User who triggered the event
        correlationId: string;     // For tracking related events
        source: string;            // Source system identifier
    };
    data: any;                     // Event-specific payload
}
```

##### 3.3 Event Payload Examples
```typescript
// Year Initialized Event
interface YearInitializedEvent extends CalendarEvent {
    type: CalendarEventType.YEAR_INITIALIZED;
    data: {
        yearId: number;
        weekStartsOn: number;
        weekEndsOn: number;
        defaultWorkingDays: number[];
        status: string;
    };
}

// Holiday Created Event
interface HolidayCreatedEvent extends CalendarEvent {
    type: CalendarEventType.HOLIDAY_CREATED;
    data: {
        holidayId: string;
        date: string;              // ISO date format
        name: string;
        holidayType: string;       // NATIONAL, RELIGIOUS, etc.
        color: string;
        isWorkingDay: boolean;
        isRecurring: boolean;
        recurringPattern?: {
            type: string;
            month?: number;
            day?: number;
        };
    };
}
```

#### 4. Business Rules

1. **Holiday Type Classification**
   - National holidays are non-working days
   - Religious holidays are typically non-working days
   - TLO days are non-working days but may be rescheduled
   - Fourth Saturdays are non-working days

2. **Working Day Calculation Rules**
   - Standard working days: Monday through Friday
   - Exceptions:
     - National and religious holidays
     - Fourth Saturdays
     - TLO days
     - Special working days on weekends for production needs

3. **Monthly Summary Logic**
   - Automatic month summary calculation based on days and holidays
   - Fourth Saturday calculated as the fourth Saturday of each month
   - Working days calculated as (total days - weekends - holidays)

#### 5. API Endpoints

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/calendar/initialize/{year}` | POST | Initialize year configuration | Year configuration details | 201 Created |
| `/calendar/{year}/holidays` | POST | Add holiday | Holiday details | 201 Created |
| `/calendar/holidays/recurring` | POST | Add recurring holiday | Recurring holiday details | 201 Created |
| `/calendar/{year}/weeks` | GET | Get all week summaries for year | N/A | 200 OK with week data |
| `/calendar/{year}/week/{weekNumber}` | GET | Get specific week data | N/A | 200 OK with week details |

#### 6. Data Flow & Processes

##### 6.1 Calendar Generation Flow

```mermaid
sequenceDiagram
    participant HR as HR Manager
    participant API as Calendar API
    participant LIB as Calendar Library
    participant GEN as Generation Service
    participant DB as SQL Database

    HR->>API: Generate Year Calendar
    API->>GEN: InitializeYear(2025)
    GEN->>LIB: GenerateCalendarBase(2025)
    LIB-->>GEN: Return Calendar Structure
    GEN->>DB: Save Year Configuration
    GEN->>DB: Generate Month Summaries
    GEN->>DB: Generate Week Summaries
    GEN->>HR: Return Calendar for Review
    HR->>API: Add Holidays
    API->>DB: Store Holidays
    API->>GEN: RecalculateSummaries()
    GEN->>DB: Update Summaries
```

##### 6.2 Event Publication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as API Gateway
    participant CH as Command Handler
    participant VS as Validation Service
    participant DS as Domain Service
    participant DB as SQL Database
    participant EP as Event Publisher
    participant CF as Change Feed
    
    Client->>API: POST /calendar/initialize/{year}
    API->>CH: InitializeYearCommand
    CH->>VS: Validate Command
    VS-->>CH: Validation Result
    
    alt Validation Failed
        CH-->>API: Return Validation Errors
        API-->>Client: 422 Unprocessable Entity
    else Validation Passed
        CH->>DS: Initialize Year
        DS->>DB: Create Year Configuration
        DB-->>DS: Confirmation
        
        DS->>DS: Generate Summaries
        DS->>DB: Save Month & Week Summaries
        DB-->>DS: Confirmation
        
        DS->>EP: Publish YearInitializedEvent
        EP->>CF: Write to Change Feed
        
        DS-->>CH: Return Result
        CH-->>API: Return Success
        API-->>Client: 201 Created
    end
```

### 4.2 Headcount Service

#### 1. Overview

The Headcount Service is responsible for tracking operator status, managing badge events, and maintaining accurate headcount information for manufacturing operations. This service implements a multi-tenant architecture with event sourcing and CQRS patterns to handle real-time operator status changes, badge events, and long-term status management.

#### 2. Domain Model & Entities

##### 2.1 Operator
```typescript
interface Operator {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_teamId"
    badgeId: string;               // Badge identifier
    firstName: string;             // First name
    lastName: string;              // Last name
    teamId: string;                // Team identifier
    teamLeaderId: string;          // Team leader identifier
    department: string;            // Department
    category: string;              // Operator category
    isActive: boolean;             // Active status
    country: string;               // For hierarchical partitioning
    metadata: {
        createdAt: string;
        updatedAt: string;
        createdBy: string;
        updatedBy: string;
    };
}
```

##### 2.2 Status-Related Entities
```typescript
interface LongTermStatus {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_operatorId"
    operatorId: string;            // Operator identifier
    type: LongTermStatusType;      // Type of long-term status
    startDate: string;             // ISO date format
    endDate: string;               // ISO date format
    reason: string;                // Reason for status
    approvedBy: string;            // Who approved the status
    approvalRequestId: string;     // Reference to approval request
    metadata: {
        createdAt: string;
        updatedAt: string;
        country: string;           // For filtering by country
    };
}

interface ShiftStatus {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_shiftId"
    operatorId: string;            // Operator identifier
    shiftId: string;               // Shift identifier
    date: string;                  // ISO date format
    shiftCode: string;             // Shift code (e.g., M, A, N)
    status: ShiftStatusType;       // Type of shift status
    location: LocationType;        // Location (Bus, Plant, etc.)
    timestamp: string;             // ISO timestamp
    reason: string;                // Reason for status (if applicable)
    updatedBy: string;             // Who updated the status
    metadata: {
        country: string;           // For filtering by country
    };
}

enum LongTermStatusType {
    ACTIVE = 'ACTIVE',
    EXTENDED_SICK_LEAVE = 'EXTENDED_SICK_LEAVE',
    MATERNITY_LEAVE = 'MATERNITY_LEAVE',
    SABBATICAL = 'SABBATICAL',
    SUSPENSION = 'SUSPENSION',
    TERMINATION = 'TERMINATION'
}

enum ShiftStatusType {
    EXPECTED = 'EXPECTED',
    PRESENT_IN_BUS = 'PRESENT_IN_BUS',
    PRESENT_IN_PLANT = 'PRESENT_IN_PLANT',
    ABSENT = 'ABSENT',
    LEAVE = 'LEAVE',
    SICK_LEAVE = 'SICK_LEAVE',
    TLO = 'TLO',
    DELAY = 'DELAY',
    EXTERNAL_WORK = 'EXTERNAL_WORK',
    TRAINING = 'TRAINING'
}
```

##### 2.3 Badge-Related Entities
```typescript
interface BadgeEvent {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_operatorId"
    operatorId: string;            // Operator identifier
    badgeId: string;               // Badge identifier
    eventType: BadgeEventType;     // Type of badge event
    timestamp: string;             // ISO timestamp
    location: {
        type: LocationType;        // Location type
        deviceId: string;          // Device identifier
        coordinates?: {
            latitude: number;
            longitude: number;
        };
    };
    metadata: {
        country: string;           // For filtering by country
    };
}

interface BadgeException {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_operatorId"
    operatorId: string;            // Operator identifier
    shiftId: string;               // Shift identifier
    exceptionType: BadgeExceptionType; // Type of exception
    reportedBy: string;            // Who reported the exception
    timestamp: string;             // ISO timestamp
    resolution: string;            // How it was resolved
    tempBadgeId: string;           // Temporary badge ID (if applicable)
    status: string;                // Status of exception
    metadata: {
        country: string;           // For filtering by country
    };
}

enum BadgeEventType {
    BUS_ENTRY = 'BUS_ENTRY',
    PLANT_ENTRY = 'PLANT_ENTRY',
    PLANT_EXIT = 'PLANT_EXIT'
}

enum LocationType {
    BUS = 'BUS',
    PLANT = 'PLANT',
    WORKSTATION = 'WORKSTATION',
    EXTERNAL = 'EXTERNAL'
}

enum BadgeExceptionType {
    FORGOTTEN = 'FORGOTTEN',
    LOST = 'LOST',
    DAMAGED = 'DAMAGED',
    TEMPORARY = 'TEMPORARY'
}
```

##### 2.4 Visual Check Entities
```typescript
interface VisualCheckSession {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_shiftId"
    shiftId: string;               // Shift identifier
    initiatedBy: string;           // Who initiated the session
    startTime: string;             // ISO timestamp
    endTime: string;               // ISO timestamp
    status: string;                // Session status
    operatorChecks: OperatorVisualCheck[]; // Array of operator checks
    metadata: {
        country: string;           // For filtering by country
    };
}

interface OperatorVisualCheck {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_sessionId"
    sessionId: string;             // Session identifier
    operatorId: string;            // Operator identifier
    expectedStatus: ShiftStatusType; // Expected status
    actualStatus: ShiftStatusType; // Actual status observed
    comments: string;              // Comments
    checkedBy: string;             // Who performed the check
    timestamp: string;             // ISO timestamp
    metadata: {
        country: string;           // For filtering by country
    };
}
```

##### 2.5 Shift Entity
```typescript
interface Shift {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_teamId_date"
    date: string;                  // ISO date format
    shiftCode: string;             // Shift code (e.g., M, A, N)
    teamId: string;                // Team identifier
    teamLeaderId: string;          // Team leader identifier
    status: ShiftStatusEnum;       // NOT_STARTED, STARTED, ENDED
    startTime: string;             // ISO timestamp
    endTime: string;               // ISO timestamp
    expectedOperatorCount: number; // Expected number of operators
    actualOperatorCount: number;   // Actual number of operators
    activatedAt: string;           // When shift was started
    activatedBy: string;           // Who started the shift
    metadata: {
        country: string;           // For filtering by country
    };
}

enum ShiftStatusEnum {
    NOT_STARTED = 'NOT_STARTED',
    STARTED = 'STARTED',
    ENDED = 'ENDED'
}
```

#### 3. Event Model

##### 3.1 Common Event Interface
```typescript
interface Event {
    id: string;                    // Unique identifier
    partitionKey: string;          // Format: "country_eventType"
    eventType: EventType;          // Type of event
    timestamp: string;             // ISO timestamp
    operatorId?: string;           // Optional based on event type
    data: any;                     // Event-specific data
    metadata: {
        correlationId: string;     // For tracking related events
        source: string;            // Source system
        country: string;           // For filtering by country
    };
}

enum EventType {
    BADGE_SCAN = 'BADGE_SCAN',
    STATUS_CHANGE = 'STATUS_CHANGE',
    VISUAL_CHECK = 'VISUAL_CHECK',
    SHIFT_STARTED = 'SHIFT_STARTED',
    SHIFT_ENDED = 'SHIFT_ENDED',
    BADGE_EXCEPTION = 'BADGE_EXCEPTION',
    LONG_TERM_STATUS_CHANGE = 'LONG_TERM_STATUS_CHANGE',
    WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED'
}
```

##### 3.2 Event Data Examples
```typescript
interface BadgeScanEventData {
    badgeId: string;               // Badge identifier
    location: {
        type: LocationType;        // Location type
        deviceId: string;          // Device identifier
        coordinates?: {
            latitude: number;
            longitude: number;
        };
    };
}

interface StatusChangeEventData {
    statusType: "LONG_TERM" | "SHIFT"; // Type of status change
    previousStatus: string;       // Previous status
    newStatus: string;            // New status
    reason: string;               // Reason for change
    location: {
        type: LocationType;       // Location type
        deviceId?: string;        // Device identifier (if applicable)
    };
    shiftId: string;              // Shift identifier
    initiatedBy: string;          // Who initiated the change
}
```

#### 4. Business Logic & Processes

##### 4.1 Badge Scan Status Change Flow

```mermaid
sequenceDiagram
    participant BR as Badge Reader
    participant APIM as API Management
    participant EP as Event Processor
    participant SP as Status Processor
    participant RE as Rule Engine
    participant DB as Cosmos DB
    participant CF as Change Feed
    participant SR as SignalR Hub
    participant OT as Optitime System

    alt Bus Badge Reader
        BR->>APIM: Badge Scan Event (Bus)
        APIM->>EP: Process Badge Event
    else Plant Badge Reader (via Optitime)
        OT->>APIM: Badge Scan Event (Plant)
        APIM->>EP: Process Badge Event
    end
    
    EP->>SP: Determine Status Change
    SP->>RE: Validate Status Transition
    RE-->>SP: Validation Result
    
    SP->>DB: Store Badge Event
    SP->>DB: Update ShiftStatus
    
    DB->>CF: Trigger Change Feed
    CF->>SR: Push Real-time Updates
```

##### 4.2 Status Resolution Logic

When determining an operator's effective status:

1. **Check Long-Term Status first:**
   - If operator has an active long-term status (e.g., EXTENDED_SICK_LEAVE), this is their primary status.
   - Long-term status is stored in a separate container for clearer separation of concerns.
   - The UI should display the long-term status prominently, with shift-based status as secondary information.

2. **Special Case Handling:**
   - For operators with long-term status who badge in (e.g., delivering documents):
     - Record the badge event and shift status normally
     - The system will maintain both statuses (long-term and shift)
     - UI will prioritize displaying long-term status but also show "Present in Plant" as secondary information

3. **Status Resolution Logic:**
```typescript
function resolveStatus(operatorId, date, country) {
    longTermStatus = getLongTermStatus(operatorId, date, country)
    shiftStatus = getShiftStatus(operatorId, date, country)
    
    if (longTermStatus && longTermStatus.type !== 'ACTIVE') {
        primaryStatus = longTermStatus
        secondaryStatus = shiftStatus
    } else {
        primaryStatus = shiftStatus
        secondaryStatus = null
    }
    
    return { primaryStatus, secondaryStatus }
}
```

#### 5. API Endpoints

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/operators/{operatorId}/status` | GET | Get operator's current status | N/A | 200 OK with status details |
| `/badges/scan` | POST | Record badge scan event | Badge scan details | 201 Created |
| `/shifts/{shiftId}/start` | POST | Start a shift | Shift start details | 200 OK with shift details |
| `/visual-checks` | POST | Initiate visual check session | Visual check details | 201 Created |
| `/operators/{operatorId}/long-term-status` | POST | Set long-term status | Status details | 201 Created |

#### 6. Database Design

The Headcount Service uses a carefully designed Cosmos DB partitioning strategy:

1. **Primary Partition Key:**
   - Country-based partitioning as the primary strategy to isolate data by tenant
   - Combined with entity-specific identifiers for better RU utilization
   
2. **Hierarchical Partitioning:**
   - Added a country field to all entities for filtering within partitions
   - This supports both country-level and global queries efficiently

3. **Partition Key Formats:**
   - Operator: "country_teamId" - Groups operators by teams within countries
   - LongTermStatus: "country_operatorId" - Groups an operator's statuses together
   - ShiftStatus: "country_shiftId" - Groups all statuses for a shift 
   - Shift: "country_teamId_date" - Optimal for querying shifts by team and date
   - Events: "country_eventType" - Enables efficient event processing by type

### 4.3 Working Plan Service

#### 1. Overview

The Working Plan Microservice manages weekly work plans, shift assignments, and operator scheduling. It is built using a document-based approach with Cosmos DB and CQRS patterns for integration with Calendar and Operator Status information. The system enables planners to create weekly schedules, assign operators to activities, and ensures that only available operators are selected by verifying their long-term status through event-driven integration.

#### 2. Domain Model & Entities

##### 2.1 Working Plan Document
```typescript
interface WorkingPlanDocument {
    id: string;                    // "WP_2025_09"
    type: "WorkingPlan";          
    partitionKey: string;          // "WP_2025"
    weekNumber: number;            // 9
    year: number;                  // 2025
    startDate: string;             // "2025-02-24"
    endDate: string;               // "2025-03-02"
    status: WorkingPlanStatus;     // "Draft" | "Published" | "Archived"
    days: DayInfo[];               // Embedded days information
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}

interface DayInfo {
    date: string;                  // "2025-02-24"
    dayOfWeek: string;             // "Monday"
    dayType: DayType;              // "WorkingDay" | "Holiday" | "Weekend"
    holidayName?: string;          // Only for Holiday days
}

enum WorkingPlanStatus {
    DRAFT = "Draft",
    PUBLISHED = "Published",
    ARCHIVED = "Archived"
}

enum DayType {
    WORKING_DAY = "WorkingDay",
    HOLIDAY = "Holiday",
    WEEKEND = "Weekend"
}
```

##### 2.2 Shift Document
```typescript
interface ShiftDocument {
    id: string;                    // "SHIFT_2025_09_MON_M"
    type: "Shift";
    partitionKey: string;          // "WP_2025_09"
    workingPlanId: string;         // "WP_2025_09"
    date: string;                  // "2025-02-24"
    shiftType: ShiftType;          // "Morning" | "Afternoon" | "Night"
    code: string;                  // "M", "A", "N"
    startTime: string;             // "06:00"
    endTime: string;               // "14:00"
    activities: ShiftActivityInfo[]; // Embedded activities
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}

interface ShiftActivityInfo {
    id: string;                    // "SA_001" 
    familyZoneId: string;          // Reference to FamilyZone
    activityType: ActivityType;    // "WP" | "SA"
    description: string;           // "Front Door Assembly"
    teamId: string;                // Reference to Team
    teamLeaderId: string;          // Reference to TeamLeader
    status: ActivityStatus;        // "Planned" | "InProgress" | "Completed"
}

enum ShiftType {
    MORNING = "Morning",
    AFTERNOON = "Afternoon",
    NIGHT = "Night"
}

enum ActivityType {
    WORKING_PRODUCTION = "WP",
    SPECIAL_ASSIGNMENT = "SA"
}

enum ActivityStatus {
    PLANNED = "Planned",
    IN_PROGRESS = "InProgress",
    COMPLETED = "Completed"
}
```

##### 2.3 Operator Assignment Document
```typescript
interface OperatorAssignmentDocument {
    id: string;                    // "OA_2025_09_SHIFT_M_SA001"
    type: "OperatorAssignment";
    partitionKey: string;          // "WP_2025_09"
    workingPlanId: string;         // "WP_2025_09"
    shiftId: string;               // "SHIFT_2025_09_MON_M"
    activityId: string;            // "SA_001"
    operators: OperatorInfo[];     // Array of assigned operators
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}

interface OperatorInfo {
    operatorId: string;            // "OP001"
    name: string;                  // "John Doe"
    status: OperatorAssignmentStatus; // "Active" | "TLO"
    assignedAt: string;            // Timestamp
    assignedBy: string;            // User who made assignment
}

enum OperatorAssignmentStatus {
    ACTIVE = "Active",
    TECHNICAL_LAYOFF = "TLO"
}
```

##### 2.4 Projection Models
```typescript
interface CalendarProjectionDocument {
    id: string;                    // "CAL_2025_09"
    type: "CalendarProjection";
    partitionKey: string;          // "CALENDAR_2025"
    year: number;                  // 2025
    weekNumber: number;            // 9
    startDate: string;             // "2025-02-24"
    endDate: string;               // "2025-03-02"
    workingDays: string[];         // ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
    holidays: Holiday[];           // Holiday information
    eventId: string;               // Reference to event that updated this
    lastUpdated: string;           // Timestamp
}

interface OperatorStatusProjectionDocument {
    id: string;                    // "OPS_OP001"
    type: "OperatorStatusProjection";
    partitionKey: string;          // "OPERATORS_STATUS"
    operatorId: string;            // "OP001"
    name: string;                  // "John Doe"
    currentStatus: string;         // "ACTIVE", "SICK_LEAVE", etc.
    longTermStatus: LongTermStatus; // Information about long-term status
    eventId: string;               // Reference to event that updated this
    lastUpdated: string;           // Timestamp
}
```

#### 3. Business Processes

##### 3.1 Create Working Plan

```mermaid
sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant WPS as Working Plan Service
    participant CPS as Calendar Projection Service
    participant CDB as Cosmos DB
    
    Client->>WPMS: POST /api/v1/workingplans
    Note over Client, WPMS: {year: 2025, weekNumber: 9}
    
    WPMS->>WPS: createPlan(2025, 9)
    WPS->>CDB: Check if plan exists
    CDB-->>WPS: Return result (not found)
    
    WPS->>CPS: getCalendarForWeek(2025, 9)
    CPS->>CDB: Query calendar projection
    CDB-->>CPS: Return calendar data
    CPS-->>WPS: Return calendar week
    
    WPS->>WPS: Create WorkingPlan document
    WPS->>WPS: Create days based on calendar
    WPS->>CDB: Save working plan document
    CDB-->>WPS: Confirm save
    
    WPS->>WPS: Generate default shifts
    WPS->>CDB: Save shift documents
    CDB-->>WPS: Confirm save
        
    WPS-->>WPMS: Return completed plan
    WPMS-->>Client: 201 Created with plan details
```

##### 3.2 Assign Operators with Status Check

```mermaid
sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant OAS as Operator Assignment Service
    participant OSP as Operator Status Projection
    participant CDB as Cosmos DB
    
    Client->>WPMS: POST /api/v1/shifts/{shiftId}/activities/{activityId}/operators
    Note over Client, WPMS: {operatorIds: ["OP001", "OP002", "OP003"]}
    
    WPMS->>OAS: assignOperators(shiftId, activityId, operatorIds)
    OAS->>WPMS: getShiftById(shiftId)

<think>I need to continue generating the LLD-Module3.md document, picking up from where I left off. I was in the middle of describing the "Assign Operators with Status Check" workflow diagram in the Working Plan Service section. I should continue with completing this diagram and finish the rest of the document, including:

1. Complete the workflow diagram
2. Complete the Working Plan Service section
3. Add the Integration Patterns section
4. Add Security section
5. Add Performance Considerations
6. Add Deployment Strategy

I'll continue in the same style and format as the initial part of the document.</think>Here's the continuation of the LLD-Module3.md document:

```markdown
    WPMS->>CDB: Query shift
    CDB-->>WPMS: Return shift
    
    OAS->>OSP: checkOperatorsAvailability(operatorIds, shift.date)
    OSP->>CDB: Query operator status projections
    CDB-->>OSP: Return operator statuses
    
    Note over OSP: Filter out operators on long-term leave
    OSP-->>OAS: Return available operators
    
    alt Some operators unavailable
        OAS-->>WPMS: Return partial success with unavailable operators
        WPMS-->>Client: 207 Multi-Status with available/unavailable operators
    else All operators available
        OAS->>CDB: Create/Update operator assignment document
        CDB-->>OAS: Confirm save
        OAS-->>WPMS: Return assignment document
        WPMS-->>Client: 200 OK with assignment details
    end
```

##### 3.3 Publish Working Plan

```mermaid
sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant WPS as Working Plan Service
    participant VS as Validation Service
    participant CDB as Cosmos DB
    
    Client->>WPMS: PUT /api/v1/workingplans/WP_2025_09/status
    Note over Client, WPMS: {status: "Published"}
    
    WPMS->>WPS: updatePlanStatus("WP_2025_09", "Published")
    WPS->>CDB: Query plan
    CDB-->>WPS: Return plan
    
    alt Plan not found
        WPS-->>WPMS: Throw EntityNotFoundError
        WPMS-->>Client: HTTP 404 Not Found
    else Plan found
        WPS->>VS: validatePlanForPublication(plan)
        VS->>CDB: Query related entities
        CDB-->>VS: Return entities
        VS->>VS: Perform validation rules
        
        alt Validation failed
            VS-->>WPMS: Return validation errors
            WPMS-->>Client: HTTP 422 Unprocessable Entity
        else Validation passed
            WPS->>CDB: Update plan status
            CDB-->>WPS: Confirmation
            
            WPS-->>WPMS: Return updated plan
            WPMS-->>Client: HTTP 200 OK with updated plan
        end
    end
```

#### 4. API Endpoints

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/workingplans` | GET | Get list of working plans | N/A | 200 - List of plans |
| `/api/v1/workingplans/current` | GET | Get current week's plan | N/A | 200 - Current plan |
| `/api/v1/workingplans/{year}/{week}` | GET | Get plan by year and week | N/A | 200 - Plan details |
| `/api/v1/workingplans` | POST | Create new plan | Year, week number | 201 - Created plan |
| `/api/v1/workingplans/{id}/status` | PUT | Update plan status | New status | 200 - Updated plan |
| `/api/v1/shifts/{shiftId}/activities/{activityId}/operators` | POST | Assign operators to activity | List of operator IDs | 200/207 - Assignment results |
| `/api/v1/operators/available` | GET | Get available operators for a date | date (query param) | 200 - List of available operators |

#### 5. Database Design

The Working Plan Service utilizes Cosmos DB with carefully optimized containers:

1. **workingplans**: Stores working plan documents with partition key on year
2. **shifts**: Stores shift documents with partition key on working plan ID
3. **operatorAssignments**: Stores operator assignment documents with partition key on working plan ID
4. **projections**: Stores projection data from other systems (calendar, operator status)

This design optimizes for common query patterns:
- Retrieving all shifts for a specific working plan
- Looking up assignments for a specific shift and activity
- Finding available operators for a specific date
- Checking current operator status before assignment

## 5. Integration Patterns

### 5.1 Event-Driven Communication

In the Module 3 architecture, microservices do not communicate directly with each other. Instead, they detect changes through the Cosmos DB Change Feed and maintain their own optimized read models.

```mermaid
graph TD
    subgraph "Annual Calendar Service"
        ACS_W[Write Models]
        ACS_CF[Change Feed]
        ACS_R[Read Models]
        
        ACS_W --> ACS_CF
        ACS_CF --> ACS_R
    end
    
    subgraph "Working Plan Service"
        WPS_W[Write Models]
        WPS_CF[Change Feed]
        WPS_R[Read Models]
        WPS_CP[Calendar Projection]
        
        WPS_W --> WPS_CF
        WPS_CF --> WPS_R
        ACS_CF -.-> WPS_CP
    end
    
    subgraph "Headcount Service"
        HS_W[Write Models]
        HS_CF[Change Feed]
        HS_R[Read Models]
        HS_CP[Working Plan Projection]
        
        HS_W --> HS_CF
        HS_CF --> HS_R
        WPS_CF -.-> HS_CP
    end
```

This pattern offers several advantages:
- Loose coupling between services
- Resilience to service outages 
- Independent evolution of each service
- Optimized data models for specific service needs

### 5.2 Cosmos DB Change Feed

The Change Feed pattern is central to the system's integration approach:

1. **Change Feed Processors**:
   - Each service implements Change Feed processors to detect document changes
   - Processors are configured with lease containers to track their position
   - Resilient design handles retries and failures

2. **Document Transformations**:
   - Raw document changes are transformed into projections
   - Only relevant data is extracted and stored
   - Denormalization optimizes for query performance

3. **Consistency Model**:
   - Eventually consistent data propagation
   - Services process changes at their own pace
   - Optimistic concurrency control for conflict resolution

```typescript
// Example Change Feed Processor Configuration
const changeFeedProcessorConfig = {
  hostName: `host-${serviceName}-${instanceId}`,
  leaseContainerName: "leases",
  targetContainerName: "events",
  createLeaseContainerIfNotExists: true,
  startFromBeginning: false,
  processorOptions: {
    maxItemCount: 100,
    startContinuationToken: undefined
  },
  onError: (err) => logger.error(`Change feed error: ${err.message}`),
  onChanges: async (changes) => {
    for (const doc of changes) {
      await processDocumentChange(doc);
    }
  }
};
```

### 5.3 Read Model Projections

Each service maintains projections from other services to support its own business logic:

1. **Calendar Projections in Working Plan Service**:
   - Calendar events are processed to create optimized calendar projections
   - Working days, holidays, and week structures are extracted
   - Changes trigger recalculation of affected plans

2. **Operator Status Projections in Working Plan Service**:
   - Operator status changes are tracked in dedicated projections
   - Long-term status information is used to determine availability
   - Ensures operators on leave aren't assigned to activities

3. **Working Plan Projections in Headcount Service**:
   - Working Plan changes create projections for expected attendance
   - Used to compare actual vs. planned attendance
   - Supports reporting on attendance trends

## 6. Security

The Module 3 microservices implement several security measures:

- **Authentication**: Azure AD B2C handles identity management
- **Authorization**: Role-based access control with fine-grained permissions
- **API Gateway**: Azure API Management enforces security policies
- **Data Encryption**: Data encrypted at rest and in transit
- **Audit Logging**: Comprehensive audit trails for all operations
- **Certificate Management**: TLS certificates for secure communication

### Role-Based Access Control

```typescript
enum Role {
    ADMIN = "ADMIN",
    HR_MANAGER = "HR_MANAGER",
    PLANNER = "PLANNER",
    TEAM_LEADER = "TEAM_LEADER",
    SHIFT_MANAGER = "SHIFT_MANAGER",
    VIEWER = "VIEWER"
}

const rolePermissions = {
    [Role.ADMIN]: ["*"],
    [Role.HR_MANAGER]: ["calendar.*", "headcount.read", "workingplan.read"],
    [Role.PLANNER]: ["calendar.read", "headcount.read", "workingplan.*"],
    [Role.TEAM_LEADER]: ["calendar.read", "headcount.manage", "workingplan.read"],
    [Role.SHIFT_MANAGER]: ["calendar.read", "headcount.read", "workingplan.publish"],
    [Role.VIEWER]: ["calendar.read", "headcount.read", "workingplan.read"]
};
```

## 7. Performance Considerations

Performance optimization strategies include:

- **Database Indexing**: Strategic indexes on common query patterns
- **Partition Key Design**: Optimized partition keys for efficient data access
- **Read/Write Separation**: CQRS pattern for high-throughput operations
- **Asynchronous Processing**: Background processing for non-critical operations
- **Pagination**: All list endpoints support pagination for large datasets
- **Rate Limiting**: API rate limiting to prevent abuse

### Cosmos DB Optimization

```typescript
// Example indexing policy for a Cosmos DB container
const indexingPolicy = {
  indexingMode: "consistent",
  includedPaths: [
    { path: "/type/?" },
    { path: "/partitionKey/?" },
    { path: "/year/?" },
    { path: "/weekNumber/?" },
    { path: "/status/?" },
    { path: "/date/?" },
    { path: "/operatorId/?" }
  ],
  excludedPaths: [
    { path: "/metadata/*" },
    { path: "/*" }
  ]
};
```


## 8. Deployment Strategy

The Module 3 microservices use a cloud-native deployment approach:

- **Containerization**: All services deployed as Docker containers
- **Kubernetes**: Azure Kubernetes Service for container orchestration
- **CI/CD Pipeline**: Automated builds and deployments via Azure DevOps
- **Environment Separation**: Development, Staging, and Production environments
- **Configuration Management**: External configuration via Azure App Configuration
- **Blue/Green Deployments**: Zero-downtime deployments
- **Monitoring and Alerting**: Application Insights for real-time monitoring

### Deployment Architecture


```mermaid
graph TD
    subgraph "Azure DevOps"
        GIT[Git Repository]
        BUILD[Build Pipeline]
        RELEASE[Release Pipeline]
    end
    
    subgraph "Azure Resources"
        ACR[Container Registry]
        AKS[Kubernetes Service]
        APIM[API Management]
        COSMOS[Cosmos DB]
        SQL[SQL Database]
        AAD[Azure AD B2C]
        INSIGHTS[Application Insights]
    end
    
    GIT --> BUILD
    BUILD --> ACR
    ACR --> RELEASE
    RELEASE --> AKS
    
    AKS --> APIM
    APIM --> AAD
    AKS --> COSMOS
    AKS --> SQL
    AKS --> INSIGHTS
```

This deployment approach ensures:
1. **Consistent Environments**: Same configuration across all environments
2. **Automated Deployments**: Reduces manual errors and deployment time
3. **Scalability**: Independent scaling of each microservice
4. **High Availability**: Multiple replicas and availability zones
5. **Disaster Recovery**: Geo-redundant data storage
6. **Monitoring**: Real-time metrics and alerts
```
