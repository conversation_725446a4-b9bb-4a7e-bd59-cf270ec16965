<!DOCTYPE html>
<html>
<head>
<title>LLD-module3.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="low-level-design-document-module-3-microservices-architecture">Low-Level Design Document:  Module 3 Microservices Architecture</h1>
<h2 id="document-information">Document Information</h2>
<p><strong>Version:</strong> 1.0.0<br>
<strong>Last Updated:</strong> 2023-03-26<br>
<strong>Status:</strong> Completed</p>
<h2 id="executive-summary">Executive Summary</h2>
<h3 id="key-features">Key Features</h3>
<ul>
<li>Microservices-based architecture with three core services</li>
<li>Event-driven communication with CQRS pattern</li>
<li>Comprehensive annual calendar management</li>
<li>Advanced headcount tracking and badge management</li>
<li>Flexible working plan and shift management</li>
<li>Document database with optimized query patterns</li>
<li>Integration with production planning systems</li>
</ul>
<h3 id="business-benefits">Business Benefits</h3>
<ul>
<li>Streamlined workforce planning and scheduling</li>
<li>Improved visibility into operator availability and status</li>
<li>Enhanced production planning with calendar integration</li>
<li>Efficient shift and activity management</li>
<li>Real-time status tracking of operators</li>
<li>Data-driven decision support for manufacturing operations</li>
<li>Seamless integration with other platform modules</li>
</ul>
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li><a href="#1-overview">Overview</a>
<ul>
<li><a href="#11-purpose-and-scope">Purpose and Scope</a></li>
<li><a href="#12-key-components">Key Components</a></li>
</ul>
</li>
<li><a href="#2-architecture-overview">Architecture Overview</a>
<ul>
<li><a href="#21-high-level-architecture">High-Level Architecture</a></li>
<li><a href="#22-technical-components">Technical Components</a></li>
<li><a href="#23-data-duplication-and-cqrs-approach">Data Duplication and CQRS Approach</a></li>
</ul>
</li>
<li><a href="#3-technology-stack">Technology Stack</a></li>
<li><a href="#4-core-microservices">Core Microservices</a>
<ul>
<li><a href="#41-annual-calendar-service">Annual Calendar Service</a></li>
<li><a href="#42-headcount-service">Headcount Service</a></li>
<li><a href="#43-working-plan-service">Working Plan Service</a></li>
</ul>
</li>
<li><a href="#5-integration-patterns">Integration Patterns</a>
<ul>
<li><a href="#51-event-driven-communication">Event-Driven Communication</a></li>
<li><a href="#52-cosmos-db-change-feed">Cosmos DB Change Feed</a></li>
<li><a href="#53-read-model-projections">Read Model Projections</a></li>
</ul>
</li>
<li><a href="#6-security">Security</a></li>
<li><a href="#7-performance-considerations">Performance Considerations</a></li>
<li><a href="#8-deployment-strategy">Deployment Strategy</a></li>
</ol>
<h2 id="1-overview">1. Overview</h2>
<h3 id="11-purpose-and-scope">1.1 Purpose and Scope</h3>
<p>Module 3 provides a comprehensive solution for managing manufacturing workforce planning, calendar operations, and headcount tracking. It implements a microservices architecture following Domain-Driven Design principles to ensure flexibility, scalability, and maintainability. The module enables manufacturing facilities to efficiently manage their annual calendar, track operator status and attendance, and create detailed working plans for production needs.</p>
<h3 id="12-key-components">1.2 Key Components</h3>
<p>The system consists of three primary microservices:</p>
<ol>
<li>Annual Calendar Service</li>
<li>Headcount Service</li>
<li>Working Plan Service</li>
</ol>
<p>Each service is supported by:</p>
<ul>
<li>Document database (Cosmos DB) for data storage</li>
<li>Change Feed for event-driven communication</li>
<li>CQRS pattern for read/write separation</li>
<li>Role-based access control for security</li>
</ul>
<h2 id="2-architecture-overview">2. Architecture Overview</h2>
<h3 id="21-high-level-architecture">2.1 High-Level Architecture</h3>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    Client[Client Applications] --> APIM[Azure API Management]
    APIM --> ACS[Annual Calendar Service]
    APIM --> HS[Headcount Service]
    APIM --> WPS[Working Plan Service]
    
    subgraph Storage
        Cosmos[Cosmos DB]
    end
    
    %% Annual Calendar Service internal structure
    subgraph ACS
        ACS_W[Write Models] --> ACS_CF[Change Feed]
        ACS_CF --> ACS_R[Read Models]
    end
    
    %% Headcount Service internal structure
    subgraph HS
        HS_W[Write Models] --> HS_CF[Change Feed]
        HS_CF --> HS_R[Read Models]
    end
    
    %% Working Plan Service internal structure
    subgraph WPS
        WPS_W[Write Models] --> WPS_CF[Change Feed]
        WPS_CF --> WPS_R[Read Models]
    end
    
    ACS --> Storage
    HS --> Storage
    WPS --> Storage
</div></code></pre>
<h3 id="22-technical-components">2.2 Technical Components</h3>
<ol>
<li><strong>NestJS Microservices</strong>: Each service implemented as a NestJS application</li>
<li><strong>Cosmos DB</strong>: NoSQL database with flexible schema support for both write and read models</li>
<li><strong>Change Feed Processor</strong>: Propagates changes between write and read models</li>
<li><strong>Azure API Management</strong>: Unified entry point with authentication and routing</li>
<li><strong>Event Sourcing</strong>: Captures all state changes as events</li>
</ol>
<h3 id="23-data-duplication-and-cqrs-approach">2.3 Data Duplication and CQRS Approach</h3>
<p>The Connected Workers Platform Module 3 implements a complete data isolation strategy between microservices:</p>
<ol>
<li>
<p><strong>No Direct Communication</strong>: Microservices do not communicate directly with each other, not even through asynchronous messaging systems like Service Bus.</p>
</li>
<li>
<p><strong>CQRS Implementation</strong>: Each microservice maintains:</p>
<ul>
<li><strong>Write Models</strong>: Optimized for recording state changes as events</li>
<li><strong>Read Models</strong>: Denormalized projections optimized for specific query patterns</li>
<li><strong>Change Feed Processor</strong>: Bridges the gap between write and read models</li>
</ul>
</li>
<li>
<p><strong>Dual-Purpose Data Structure</strong>:</p>
<ul>
<li>Each microservice contains dedicated containers for write operations (events/commands)</li>
<li>Each microservice also maintains optimized read models for queries</li>
<li>The separation allows optimization for both write and read operations</li>
</ul>
</li>
<li>
<p><strong>Event Sourcing</strong>: All state changes are recorded as immutable events in write models, providing:</p>
<ul>
<li>Complete audit history</li>
<li>Ability to reconstruct state at any point in time</li>
<li>Source of truth for the system</li>
</ul>
</li>
<li>
<p><strong>Data Duplication Strategy</strong>: When a microservice needs data owned by another microservice:</p>
<ul>
<li>Data is duplicated into read models within the consuming microservice</li>
<li>This eliminates service-to-service dependencies and improves resilience</li>
</ul>
</li>
<li>
<p><strong>Cosmos DB Change Feed Mechanism</strong>:</p>
<ul>
<li>Detects document changes in write model containers</li>
<li>Triggers functions to update corresponding read models</li>
<li>Enables eventual consistency between write and read models</li>
<li>Facilitates data duplication across microservice boundaries</li>
</ul>
</li>
</ol>
<p>This architecture provides several advantages:</p>
<ul>
<li><strong>Resilience</strong>: Services can function independently even if other services are unavailable</li>
<li><strong>Performance</strong>: Data access is optimized for each service's specific needs</li>
<li><strong>Scalability</strong>: Services can scale independently based on their specific load patterns</li>
<li><strong>Evolution</strong>: Services can evolve independently with minimal impact on each other</li>
</ul>
<h2 id="3-technology-stack">3. Technology Stack</h2>
<table>
<thead>
<tr>
<th>Component</th>
<th>Technology</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td>API Management</td>
<td>Azure API Management</td>
<td>Centralized API gateway for all microservices</td>
</tr>
<tr>
<td>NoSQL Database</td>
<td>Azure Cosmos DB</td>
<td>Document storage with change feed capabilities</td>
</tr>
<tr>
<td>Relational Database</td>
<td>Azure SQL Database</td>
<td>Used by Annual Calendar for structured data</td>
</tr>
<tr>
<td>Authentication</td>
<td>Azure AD B2C</td>
<td>Identity management and authentication</td>
</tr>
<tr>
<td>Backend</td>
<td>NestJS</td>
<td>TypeScript-based Node.js framework for microservices</td>
</tr>
<tr>
<td>Containerization</td>
<td>Docker</td>
<td>Container platform for consistent deployment</td>
</tr>
<tr>
<td>Container Orchestration</td>
<td>Azure Kubernetes Service</td>
<td>Managed Kubernetes for container deployment</td>
</tr>
<tr>
<td>CI/CD</td>
<td>Azure DevOps</td>
<td>Continuous integration and deployment</td>
</tr>
<tr>
<td>Monitoring</td>
<td>Application Insights</td>
<td>Real-time performance and error monitoring</td>
</tr>
<tr>
<td>Logging</td>
<td>ELK Stack</td>
<td>Centralized logging and analysis</td>
</tr>
</tbody>
</table>
<h2 id="4-core-microservices">4. Core Microservices</h2>
<h3 id="41-annual-calendar-service">4.1 Annual Calendar Service</h3>
<h4 id="1-overview">1. Overview</h4>
<p>The Annual Calendar microservice is designed to manage and provide calendar-related data, including holidays, working days, and monthly summaries. It is built using NestJS and leverages a SQL database for data storage, along with a calendar library that dynamically generates calendar data. This approach eliminates the need to store individual days in the database, focusing instead on storing only holidays and special working days.</p>
<h4 id="2-domain-model--entities">2. Domain Model &amp; Entities</h4>
<h5 id="21-year-configuration">2.1 Year Configuration</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> YearConfiguration {
    yearId: <span class="hljs-built_in">number</span>;                  <span class="hljs-comment">// Primary key (e.g., 2025)</span>
    status: YearStatus;              <span class="hljs-comment">// DRAFT, ACTIVE, ARCHIVED</span>
    weekStartsOn: <span class="hljs-built_in">number</span>;            <span class="hljs-comment">// Day when week starts (1-7)</span>
    weekEndsOn: <span class="hljs-built_in">number</span>;              <span class="hljs-comment">// Day when week ends (1-7)</span>
    defaultWorkingDays: <span class="hljs-built_in">number</span>[];    <span class="hljs-comment">// Default working days of week</span>
    metadata: {
        createdBy: <span class="hljs-built_in">string</span>;
        createdAt: <span class="hljs-built_in">string</span>;
        updatedAt: <span class="hljs-built_in">string</span>;
        updatedBy: <span class="hljs-built_in">string</span>;
    };
}

<span class="hljs-keyword">enum</span> YearStatus {
    DRAFT = <span class="hljs-string">"DRAFT"</span>,
    ACTIVE = <span class="hljs-string">"ACTIVE"</span>,
    ARCHIVED = <span class="hljs-string">"ARCHIVED"</span>
}
</div></code></pre>
<h5 id="22-holiday">2.2 Holiday</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> Holiday {
    holidayId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// UUID</span>
    date: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// ISO date format</span>
    name: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Holiday name</span>
    holidayType: HolidayType;        <span class="hljs-comment">// Type of holiday</span>
    color: <span class="hljs-built_in">string</span>;                   <span class="hljs-comment">// Display color</span>
    isWorkingDay: <span class="hljs-built_in">boolean</span>;           <span class="hljs-comment">// Whether it's a working day</span>
    isRecurring: <span class="hljs-built_in">boolean</span>;            <span class="hljs-comment">// Whether it recurs annually</span>
    recurringType?: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// How it recurs (if applicable)</span>
    recurringMonth?: <span class="hljs-built_in">number</span>;         <span class="hljs-comment">// Month of recurrence (if applicable)</span>
    recurringDay?: <span class="hljs-built_in">number</span>;           <span class="hljs-comment">// Day of recurrence (if applicable)</span>
    metadata: {
        createdBy: <span class="hljs-built_in">string</span>;
        createdAt: <span class="hljs-built_in">string</span>;
        updatedAt: <span class="hljs-built_in">string</span>;
        updatedBy: <span class="hljs-built_in">string</span>;
    };
}

<span class="hljs-keyword">enum</span> HolidayType {
    NATIONAL = <span class="hljs-string">"NATIONAL"</span>,
    RELIGIOUS = <span class="hljs-string">"RELIGIOUS"</span>,
    UNPAID_PUBLIC = <span class="hljs-string">"UNPAID_PUBLIC"</span>,
    TLO = <span class="hljs-string">"TLO"</span>,
    FOURTH_SATURDAY = <span class="hljs-string">"FOURTH_SATURDAY"</span>,
    PLANT_HOLIDAY = <span class="hljs-string">"PLANT_HOLIDAY"</span>
}
</div></code></pre>
<h5 id="23-week--month-summaries">2.3 Week &amp; Month Summaries</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> WeekSummary {
    weekId: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// UUID</span>
    yearId: <span class="hljs-built_in">number</span>;                  <span class="hljs-comment">// Reference to YearConfiguration</span>
    weekNumber: <span class="hljs-built_in">number</span>;              <span class="hljs-comment">// ISO week number</span>
    startDate: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Week start date (ISO format)</span>
    endDate: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// Week end date (ISO format)</span>
    workingDays: <span class="hljs-built_in">number</span>;             <span class="hljs-comment">// Number of working days</span>
    totalHours: <span class="hljs-built_in">number</span>;              <span class="hljs-comment">// Total working hours</span>
    status: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// DRAFT, APPROVED, ARCHIVED</span>
    metadata: {
        createdAt: <span class="hljs-built_in">string</span>;
        updatedAt: <span class="hljs-built_in">string</span>;
    };
}

<span class="hljs-keyword">interface</span> MonthConfiguration {
    monthId: <span class="hljs-built_in">string</span>;                 <span class="hljs-comment">// UUID</span>
    yearId: <span class="hljs-built_in">number</span>;                  <span class="hljs-comment">// Reference to YearConfiguration</span>
    monthNumber: <span class="hljs-built_in">number</span>;             <span class="hljs-comment">// Month number (1-12)</span>
    name: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Month name</span>
    totalDays: <span class="hljs-built_in">number</span>;               <span class="hljs-comment">// Total days in month</span>
    workingDays: <span class="hljs-built_in">number</span>;             <span class="hljs-comment">// Number of working days</span>
    fourthSaturday?: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// Date of fourth Saturday (if applicable)</span>
    status: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// DRAFT, APPROVED, ARCHIVED</span>
    metadata: {
        createdAt: <span class="hljs-built_in">string</span>;
        updatedAt: <span class="hljs-built_in">string</span>;
    };
}
</div></code></pre>
<h4 id="3-event-model">3. Event Model</h4>
<h5 id="31-event-types">3.1 Event Types</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">enum</span> CalendarEventType {
    YEAR_INITIALIZED = <span class="hljs-string">'calendar.year.initialized'</span>,
    YEAR_STATUS_CHANGED = <span class="hljs-string">'calendar.year.status.changed'</span>,
    HOLIDAY_CREATED = <span class="hljs-string">'calendar.holiday.created'</span>,
    HOLIDAY_UPDATED = <span class="hljs-string">'calendar.holiday.updated'</span>,
    HOLIDAY_DELETED = <span class="hljs-string">'calendar.holiday.deleted'</span>,
    WEEK_SUMMARY_CALCULATED = <span class="hljs-string">'calendar.week.summary.calculated'</span>,
    MONTH_SUMMARY_CALCULATED = <span class="hljs-string">'calendar.month.summary.calculated'</span>,
    FOURTH_SATURDAY_ASSIGNED = <span class="hljs-string">'calendar.fourth.saturday.assigned'</span>
}
</div></code></pre>
<h5 id="32-base-event-structure">3.2 Base Event Structure</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> CalendarEvent {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// UUID</span>
    <span class="hljs-keyword">type</span>: CalendarEventType;       <span class="hljs-comment">// Event type</span>
    version: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Schema version</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    metadata: {
        userId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// User who triggered the event</span>
        correlationId: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// For tracking related events</span>
        source: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Source system identifier</span>
    };
    data: <span class="hljs-built_in">any</span>;                     <span class="hljs-comment">// Event-specific payload</span>
}
</div></code></pre>
<h5 id="33-event-payload-examples">3.3 Event Payload Examples</h5>
<pre class="hljs"><code><div><span class="hljs-comment">// Year Initialized Event</span>
<span class="hljs-keyword">interface</span> YearInitializedEvent <span class="hljs-keyword">extends</span> CalendarEvent {
    <span class="hljs-keyword">type</span>: CalendarEventType.YEAR_INITIALIZED;
    data: {
        yearId: <span class="hljs-built_in">number</span>;
        weekStartsOn: <span class="hljs-built_in">number</span>;
        weekEndsOn: <span class="hljs-built_in">number</span>;
        defaultWorkingDays: <span class="hljs-built_in">number</span>[];
        status: <span class="hljs-built_in">string</span>;
    };
}

<span class="hljs-comment">// Holiday Created Event</span>
<span class="hljs-keyword">interface</span> HolidayCreatedEvent <span class="hljs-keyword">extends</span> CalendarEvent {
    <span class="hljs-keyword">type</span>: CalendarEventType.HOLIDAY_CREATED;
    data: {
        holidayId: <span class="hljs-built_in">string</span>;
        date: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// ISO date format</span>
        name: <span class="hljs-built_in">string</span>;
        holidayType: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// NATIONAL, RELIGIOUS, etc.</span>
        color: <span class="hljs-built_in">string</span>;
        isWorkingDay: <span class="hljs-built_in">boolean</span>;
        isRecurring: <span class="hljs-built_in">boolean</span>;
        recurringPattern?: {
            <span class="hljs-keyword">type</span>: <span class="hljs-built_in">string</span>;
            month?: <span class="hljs-built_in">number</span>;
            day?: <span class="hljs-built_in">number</span>;
        };
    };
}
</div></code></pre>
<h4 id="4-business-rules">4. Business Rules</h4>
<ol>
<li>
<p><strong>Holiday Type Classification</strong></p>
<ul>
<li>National holidays are non-working days</li>
<li>Religious holidays are typically non-working days</li>
<li>TLO days are non-working days but may be rescheduled</li>
<li>Fourth Saturdays are non-working days</li>
</ul>
</li>
<li>
<p><strong>Working Day Calculation Rules</strong></p>
<ul>
<li>Standard working days: Monday through Friday</li>
<li>Exceptions:
<ul>
<li>National and religious holidays</li>
<li>Fourth Saturdays</li>
<li>TLO days</li>
<li>Special working days on weekends for production needs</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Monthly Summary Logic</strong></p>
<ul>
<li>Automatic month summary calculation based on days and holidays</li>
<li>Fourth Saturday calculated as the fourth Saturday of each month</li>
<li>Working days calculated as (total days - weekends - holidays)</li>
</ul>
</li>
</ol>
<h4 id="5-api-endpoints">5. API Endpoints</h4>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
<th>Request Body</th>
<th>Response</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/calendar/initialize/{year}</code></td>
<td>POST</td>
<td>Initialize year configuration</td>
<td>Year configuration details</td>
<td>201 Created</td>
</tr>
<tr>
<td><code>/calendar/{year}/holidays</code></td>
<td>POST</td>
<td>Add holiday</td>
<td>Holiday details</td>
<td>201 Created</td>
</tr>
<tr>
<td><code>/calendar/holidays/recurring</code></td>
<td>POST</td>
<td>Add recurring holiday</td>
<td>Recurring holiday details</td>
<td>201 Created</td>
</tr>
<tr>
<td><code>/calendar/{year}/weeks</code></td>
<td>GET</td>
<td>Get all week summaries for year</td>
<td>N/A</td>
<td>200 OK with week data</td>
</tr>
<tr>
<td><code>/calendar/{year}/week/{weekNumber}</code></td>
<td>GET</td>
<td>Get specific week data</td>
<td>N/A</td>
<td>200 OK with week details</td>
</tr>
</tbody>
</table>
<h4 id="6-data-flow--processes">6. Data Flow &amp; Processes</h4>
<h5 id="61-calendar-generation-flow">6.1 Calendar Generation Flow</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant HR as HR Manager
    participant API as Calendar API
    participant LIB as Calendar Library
    participant GEN as Generation Service
    participant DB as SQL Database

    HR->>API: Generate Year Calendar
    API->>GEN: InitializeYear(2025)
    GEN->>LIB: GenerateCalendarBase(2025)
    LIB-->>GEN: Return Calendar Structure
    GEN->>DB: Save Year Configuration
    GEN->>DB: Generate Month Summaries
    GEN->>DB: Generate Week Summaries
    GEN->>HR: Return Calendar for Review
    HR->>API: Add Holidays
    API->>DB: Store Holidays
    API->>GEN: RecalculateSummaries()
    GEN->>DB: Update Summaries
</div></code></pre>
<h5 id="62-event-publication-flow">6.2 Event Publication Flow</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Client
    participant API as API Gateway
    participant CH as Command Handler
    participant VS as Validation Service
    participant DS as Domain Service
    participant DB as SQL Database
    participant EP as Event Publisher
    participant CF as Change Feed
    
    Client->>API: POST /calendar/initialize/{year}
    API->>CH: InitializeYearCommand
    CH->>VS: Validate Command
    VS-->>CH: Validation Result
    
    alt Validation Failed
        CH-->>API: Return Validation Errors
        API-->>Client: 422 Unprocessable Entity
    else Validation Passed
        CH->>DS: Initialize Year
        DS->>DB: Create Year Configuration
        DB-->>DS: Confirmation
        
        DS->>DS: Generate Summaries
        DS->>DB: Save Month & Week Summaries
        DB-->>DS: Confirmation
        
        DS->>EP: Publish YearInitializedEvent
        EP->>CF: Write to Change Feed
        
        DS-->>CH: Return Result
        CH-->>API: Return Success
        API-->>Client: 201 Created
    end
</div></code></pre>
<h3 id="42-headcount-service">4.2 Headcount Service</h3>
<h4 id="1-overview">1. Overview</h4>
<p>The Headcount Service is responsible for tracking operator status, managing badge events, and maintaining accurate headcount information for manufacturing operations. This service implements a multi-tenant architecture with event sourcing and CQRS patterns to handle real-time operator status changes, badge events, and long-term status management.</p>
<h4 id="2-domain-model--entities">2. Domain Model &amp; Entities</h4>
<h5 id="21-operator">2.1 Operator</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> Operator {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_teamId"</span>
    badgeId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Badge identifier</span>
    firstName: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// First name</span>
    lastName: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Last name</span>
    teamId: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Team identifier</span>
    teamLeaderId: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Team leader identifier</span>
    department: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Department</span>
    category: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Operator category</span>
    isActive: <span class="hljs-built_in">boolean</span>;             <span class="hljs-comment">// Active status</span>
    country: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// For hierarchical partitioning</span>
    metadata: {
        createdAt: <span class="hljs-built_in">string</span>;
        updatedAt: <span class="hljs-built_in">string</span>;
        createdBy: <span class="hljs-built_in">string</span>;
        updatedBy: <span class="hljs-built_in">string</span>;
    };
}
</div></code></pre>
<h5 id="22-status-related-entities">2.2 Status-Related Entities</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> LongTermStatus {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_operatorId"</span>
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Operator identifier</span>
    <span class="hljs-keyword">type</span>: LongTermStatusType;      <span class="hljs-comment">// Type of long-term status</span>
    startDate: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO date format</span>
    endDate: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// ISO date format</span>
    reason: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Reason for status</span>
    approvedBy: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Who approved the status</span>
    approvalRequestId: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Reference to approval request</span>
    metadata: {
        createdAt: <span class="hljs-built_in">string</span>;
        updatedAt: <span class="hljs-built_in">string</span>;
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">interface</span> ShiftStatus {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_shiftId"</span>
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Operator identifier</span>
    shiftId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Shift identifier</span>
    date: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// ISO date format</span>
    shiftCode: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Shift code (e.g., M, A, N)</span>
    status: ShiftStatusType;       <span class="hljs-comment">// Type of shift status</span>
    location: LocationType;        <span class="hljs-comment">// Location (Bus, Plant, etc.)</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    reason: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Reason for status (if applicable)</span>
    updatedBy: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Who updated the status</span>
    metadata: {
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">enum</span> LongTermStatusType {
    ACTIVE = <span class="hljs-string">'ACTIVE'</span>,
    EXTENDED_SICK_LEAVE = <span class="hljs-string">'EXTENDED_SICK_LEAVE'</span>,
    MATERNITY_LEAVE = <span class="hljs-string">'MATERNITY_LEAVE'</span>,
    SABBATICAL = <span class="hljs-string">'SABBATICAL'</span>,
    SUSPENSION = <span class="hljs-string">'SUSPENSION'</span>,
    TERMINATION = <span class="hljs-string">'TERMINATION'</span>
}

<span class="hljs-keyword">enum</span> ShiftStatusType {
    EXPECTED = <span class="hljs-string">'EXPECTED'</span>,
    PRESENT_IN_BUS = <span class="hljs-string">'PRESENT_IN_BUS'</span>,
    PRESENT_IN_PLANT = <span class="hljs-string">'PRESENT_IN_PLANT'</span>,
    ABSENT = <span class="hljs-string">'ABSENT'</span>,
    LEAVE = <span class="hljs-string">'LEAVE'</span>,
    SICK_LEAVE = <span class="hljs-string">'SICK_LEAVE'</span>,
    TLO = <span class="hljs-string">'TLO'</span>,
    DELAY = <span class="hljs-string">'DELAY'</span>,
    EXTERNAL_WORK = <span class="hljs-string">'EXTERNAL_WORK'</span>,
    TRAINING = <span class="hljs-string">'TRAINING'</span>
}
</div></code></pre>
<h5 id="23-badge-related-entities">2.3 Badge-Related Entities</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> BadgeEvent {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_operatorId"</span>
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Operator identifier</span>
    badgeId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Badge identifier</span>
    eventType: BadgeEventType;     <span class="hljs-comment">// Type of badge event</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    location: {
        <span class="hljs-keyword">type</span>: LocationType;        <span class="hljs-comment">// Location type</span>
        deviceId: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Device identifier</span>
        coordinates?: {
            latitude: <span class="hljs-built_in">number</span>;
            longitude: <span class="hljs-built_in">number</span>;
        };
    };
    metadata: {
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">interface</span> BadgeException {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_operatorId"</span>
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Operator identifier</span>
    shiftId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Shift identifier</span>
    exceptionType: BadgeExceptionType; <span class="hljs-comment">// Type of exception</span>
    reportedBy: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Who reported the exception</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    resolution: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// How it was resolved</span>
    tempBadgeId: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Temporary badge ID (if applicable)</span>
    status: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Status of exception</span>
    metadata: {
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">enum</span> BadgeEventType {
    BUS_ENTRY = <span class="hljs-string">'BUS_ENTRY'</span>,
    PLANT_ENTRY = <span class="hljs-string">'PLANT_ENTRY'</span>,
    PLANT_EXIT = <span class="hljs-string">'PLANT_EXIT'</span>
}

<span class="hljs-keyword">enum</span> LocationType {
    BUS = <span class="hljs-string">'BUS'</span>,
    PLANT = <span class="hljs-string">'PLANT'</span>,
    WORKSTATION = <span class="hljs-string">'WORKSTATION'</span>,
    EXTERNAL = <span class="hljs-string">'EXTERNAL'</span>
}

<span class="hljs-keyword">enum</span> BadgeExceptionType {
    FORGOTTEN = <span class="hljs-string">'FORGOTTEN'</span>,
    LOST = <span class="hljs-string">'LOST'</span>,
    DAMAGED = <span class="hljs-string">'DAMAGED'</span>,
    TEMPORARY = <span class="hljs-string">'TEMPORARY'</span>
}
</div></code></pre>
<h5 id="24-visual-check-entities">2.4 Visual Check Entities</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> VisualCheckSession {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_shiftId"</span>
    shiftId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Shift identifier</span>
    initiatedBy: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Who initiated the session</span>
    startTime: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    endTime: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// ISO timestamp</span>
    status: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Session status</span>
    operatorChecks: OperatorVisualCheck[]; <span class="hljs-comment">// Array of operator checks</span>
    metadata: {
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">interface</span> OperatorVisualCheck {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_sessionId"</span>
    sessionId: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Session identifier</span>
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Operator identifier</span>
    expectedStatus: ShiftStatusType; <span class="hljs-comment">// Expected status</span>
    actualStatus: ShiftStatusType; <span class="hljs-comment">// Actual status observed</span>
    comments: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Comments</span>
    checkedBy: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Who performed the check</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    metadata: {
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}
</div></code></pre>
<h5 id="25-shift-entity">2.5 Shift Entity</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> Shift {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_teamId_date"</span>
    date: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// ISO date format</span>
    shiftCode: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// Shift code (e.g., M, A, N)</span>
    teamId: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Team identifier</span>
    teamLeaderId: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Team leader identifier</span>
    status: ShiftStatusEnum;       <span class="hljs-comment">// NOT_STARTED, STARTED, ENDED</span>
    startTime: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    endTime: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// ISO timestamp</span>
    expectedOperatorCount: <span class="hljs-built_in">number</span>; <span class="hljs-comment">// Expected number of operators</span>
    actualOperatorCount: <span class="hljs-built_in">number</span>;   <span class="hljs-comment">// Actual number of operators</span>
    activatedAt: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// When shift was started</span>
    activatedBy: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Who started the shift</span>
    metadata: {
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">enum</span> ShiftStatusEnum {
    NOT_STARTED = <span class="hljs-string">'NOT_STARTED'</span>,
    STARTED = <span class="hljs-string">'STARTED'</span>,
    ENDED = <span class="hljs-string">'ENDED'</span>
}
</div></code></pre>
<h4 id="3-event-model">3. Event Model</h4>
<h5 id="31-common-event-interface">3.1 Common Event Interface</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> Event {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// Unique identifier</span>
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Format: "country_eventType"</span>
    eventType: EventType;          <span class="hljs-comment">// Type of event</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// ISO timestamp</span>
    operatorId?: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Optional based on event type</span>
    data: <span class="hljs-built_in">any</span>;                     <span class="hljs-comment">// Event-specific data</span>
    metadata: {
        correlationId: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// For tracking related events</span>
        source: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Source system</span>
        country: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// For filtering by country</span>
    };
}

<span class="hljs-keyword">enum</span> EventType {
    BADGE_SCAN = <span class="hljs-string">'BADGE_SCAN'</span>,
    STATUS_CHANGE = <span class="hljs-string">'STATUS_CHANGE'</span>,
    VISUAL_CHECK = <span class="hljs-string">'VISUAL_CHECK'</span>,
    SHIFT_STARTED = <span class="hljs-string">'SHIFT_STARTED'</span>,
    SHIFT_ENDED = <span class="hljs-string">'SHIFT_ENDED'</span>,
    BADGE_EXCEPTION = <span class="hljs-string">'BADGE_EXCEPTION'</span>,
    LONG_TERM_STATUS_CHANGE = <span class="hljs-string">'LONG_TERM_STATUS_CHANGE'</span>,
    WORKFLOW_COMPLETED = <span class="hljs-string">'WORKFLOW_COMPLETED'</span>
}
</div></code></pre>
<h5 id="32-event-data-examples">3.2 Event Data Examples</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> BadgeScanEventData {
    badgeId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Badge identifier</span>
    location: {
        <span class="hljs-keyword">type</span>: LocationType;        <span class="hljs-comment">// Location type</span>
        deviceId: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Device identifier</span>
        coordinates?: {
            latitude: <span class="hljs-built_in">number</span>;
            longitude: <span class="hljs-built_in">number</span>;
        };
    };
}

<span class="hljs-keyword">interface</span> StatusChangeEventData {
    statusType: <span class="hljs-string">"LONG_TERM"</span> | <span class="hljs-string">"SHIFT"</span>; <span class="hljs-comment">// Type of status change</span>
    previousStatus: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// Previous status</span>
    newStatus: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// New status</span>
    reason: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Reason for change</span>
    location: {
        <span class="hljs-keyword">type</span>: LocationType;       <span class="hljs-comment">// Location type</span>
        deviceId?: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// Device identifier (if applicable)</span>
    };
    shiftId: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Shift identifier</span>
    initiatedBy: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Who initiated the change</span>
}
</div></code></pre>
<h4 id="4-business-logic--processes">4. Business Logic &amp; Processes</h4>
<h5 id="41-badge-scan-status-change-flow">4.1 Badge Scan Status Change Flow</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant BR as Badge Reader
    participant APIM as API Management
    participant EP as Event Processor
    participant SP as Status Processor
    participant RE as Rule Engine
    participant DB as Cosmos DB
    participant CF as Change Feed
    participant SR as SignalR Hub
    participant OT as Optitime System

    alt Bus Badge Reader
        BR->>APIM: Badge Scan Event (Bus)
        APIM->>EP: Process Badge Event
    else Plant Badge Reader (via Optitime)
        OT->>APIM: Badge Scan Event (Plant)
        APIM->>EP: Process Badge Event
    end
    
    EP->>SP: Determine Status Change
    SP->>RE: Validate Status Transition
    RE-->>SP: Validation Result
    
    SP->>DB: Store Badge Event
    SP->>DB: Update ShiftStatus
    
    DB->>CF: Trigger Change Feed
    CF->>SR: Push Real-time Updates
</div></code></pre>
<h5 id="42-status-resolution-logic">4.2 Status Resolution Logic</h5>
<p>When determining an operator's effective status:</p>
<ol>
<li>
<p><strong>Check Long-Term Status first:</strong></p>
<ul>
<li>If operator has an active long-term status (e.g., EXTENDED_SICK_LEAVE), this is their primary status.</li>
<li>Long-term status is stored in a separate container for clearer separation of concerns.</li>
<li>The UI should display the long-term status prominently, with shift-based status as secondary information.</li>
</ul>
</li>
<li>
<p><strong>Special Case Handling:</strong></p>
<ul>
<li>For operators with long-term status who badge in (e.g., delivering documents):
<ul>
<li>Record the badge event and shift status normally</li>
<li>The system will maintain both statuses (long-term and shift)</li>
<li>UI will prioritize displaying long-term status but also show &quot;Present in Plant&quot; as secondary information</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>Status Resolution Logic:</strong></p>
</li>
</ol>
<pre class="hljs"><code><div><span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">resolveStatus</span>(<span class="hljs-params">operatorId, date, country</span>) </span>{
    longTermStatus = getLongTermStatus(operatorId, date, country)
    shiftStatus = getShiftStatus(operatorId, date, country)
    
    <span class="hljs-keyword">if</span> (longTermStatus &amp;&amp; longTermStatus.type !== <span class="hljs-string">'ACTIVE'</span>) {
        primaryStatus = longTermStatus
        secondaryStatus = shiftStatus
    } <span class="hljs-keyword">else</span> {
        primaryStatus = shiftStatus
        secondaryStatus = <span class="hljs-literal">null</span>
    }
    
    <span class="hljs-keyword">return</span> { primaryStatus, secondaryStatus }
}
</div></code></pre>
<h4 id="5-api-endpoints">5. API Endpoints</h4>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
<th>Request Body</th>
<th>Response</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/operators/{operatorId}/status</code></td>
<td>GET</td>
<td>Get operator's current status</td>
<td>N/A</td>
<td>200 OK with status details</td>
</tr>
<tr>
<td><code>/badges/scan</code></td>
<td>POST</td>
<td>Record badge scan event</td>
<td>Badge scan details</td>
<td>201 Created</td>
</tr>
<tr>
<td><code>/shifts/{shiftId}/start</code></td>
<td>POST</td>
<td>Start a shift</td>
<td>Shift start details</td>
<td>200 OK with shift details</td>
</tr>
<tr>
<td><code>/visual-checks</code></td>
<td>POST</td>
<td>Initiate visual check session</td>
<td>Visual check details</td>
<td>201 Created</td>
</tr>
<tr>
<td><code>/operators/{operatorId}/long-term-status</code></td>
<td>POST</td>
<td>Set long-term status</td>
<td>Status details</td>
<td>201 Created</td>
</tr>
</tbody>
</table>
<h4 id="6-database-design">6. Database Design</h4>
<p>The Headcount Service uses a carefully designed Cosmos DB partitioning strategy:</p>
<ol>
<li>
<p><strong>Primary Partition Key:</strong></p>
<ul>
<li>Country-based partitioning as the primary strategy to isolate data by tenant</li>
<li>Combined with entity-specific identifiers for better RU utilization</li>
</ul>
</li>
<li>
<p><strong>Hierarchical Partitioning:</strong></p>
<ul>
<li>Added a country field to all entities for filtering within partitions</li>
<li>This supports both country-level and global queries efficiently</li>
</ul>
</li>
<li>
<p><strong>Partition Key Formats:</strong></p>
<ul>
<li>Operator: &quot;country_teamId&quot; - Groups operators by teams within countries</li>
<li>LongTermStatus: &quot;country_operatorId&quot; - Groups an operator's statuses together</li>
<li>ShiftStatus: &quot;country_shiftId&quot; - Groups all statuses for a shift</li>
<li>Shift: &quot;country_teamId_date&quot; - Optimal for querying shifts by team and date</li>
<li>Events: &quot;country_eventType&quot; - Enables efficient event processing by type</li>
</ul>
</li>
</ol>
<h3 id="43-working-plan-service">4.3 Working Plan Service</h3>
<h4 id="1-overview">1. Overview</h4>
<p>The Working Plan Microservice manages weekly work plans, shift assignments, and operator scheduling. It is built using a document-based approach with Cosmos DB and CQRS patterns for integration with Calendar and Operator Status information. The system enables planners to create weekly schedules, assign operators to activities, and ensures that only available operators are selected by verifying their long-term status through event-driven integration.</p>
<h4 id="2-domain-model--entities">2. Domain Model &amp; Entities</h4>
<h5 id="21-working-plan-document">2.1 Working Plan Document</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> WorkingPlanDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "WP_2025_09"</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"WorkingPlan"</span>;          
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// "WP_2025"</span>
    weekNumber: <span class="hljs-built_in">number</span>;            <span class="hljs-comment">// 9</span>
    year: <span class="hljs-built_in">number</span>;                  <span class="hljs-comment">// 2025</span>
    startDate: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// "2025-02-24"</span>
    endDate: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// "2025-03-02"</span>
    status: WorkingPlanStatus;     <span class="hljs-comment">// "Draft" | "Published" | "Archived"</span>
    days: DayInfo[];               <span class="hljs-comment">// Embedded days information</span>
    metadata: {
        createdBy: <span class="hljs-built_in">string</span>;
        createdAt: <span class="hljs-built_in">string</span>;
        lastModifiedBy: <span class="hljs-built_in">string</span>;
        lastModifiedAt: <span class="hljs-built_in">string</span>;
        version: <span class="hljs-built_in">number</span>;
    };
}

<span class="hljs-keyword">interface</span> DayInfo {
    date: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "2025-02-24"</span>
    dayOfWeek: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// "Monday"</span>
    dayType: DayType;              <span class="hljs-comment">// "WorkingDay" | "Holiday" | "Weekend"</span>
    holidayName?: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Only for Holiday days</span>
}

<span class="hljs-keyword">enum</span> WorkingPlanStatus {
    DRAFT = <span class="hljs-string">"Draft"</span>,
    PUBLISHED = <span class="hljs-string">"Published"</span>,
    ARCHIVED = <span class="hljs-string">"Archived"</span>
}

<span class="hljs-keyword">enum</span> DayType {
    WORKING_DAY = <span class="hljs-string">"WorkingDay"</span>,
    HOLIDAY = <span class="hljs-string">"Holiday"</span>,
    WEEKEND = <span class="hljs-string">"Weekend"</span>
}
</div></code></pre>
<h5 id="22-shift-document">2.2 Shift Document</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> ShiftDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "SHIFT_2025_09_MON_M"</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"Shift"</span>;
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// "WP_2025_09"</span>
    workingPlanId: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// "WP_2025_09"</span>
    date: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "2025-02-24"</span>
    shiftType: ShiftType;          <span class="hljs-comment">// "Morning" | "Afternoon" | "Night"</span>
    code: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "M", "A", "N"</span>
    startTime: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// "06:00"</span>
    endTime: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// "14:00"</span>
    activities: ShiftActivityInfo[]; <span class="hljs-comment">// Embedded activities</span>
    metadata: {
        createdBy: <span class="hljs-built_in">string</span>;
        createdAt: <span class="hljs-built_in">string</span>;
        lastModifiedBy: <span class="hljs-built_in">string</span>;
        lastModifiedAt: <span class="hljs-built_in">string</span>;
        version: <span class="hljs-built_in">number</span>;
    };
}

<span class="hljs-keyword">interface</span> ShiftActivityInfo {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "SA_001" </span>
    familyZoneId: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Reference to FamilyZone</span>
    activityType: ActivityType;    <span class="hljs-comment">// "WP" | "SA"</span>
    description: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// "Front Door Assembly"</span>
    teamId: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Reference to Team</span>
    teamLeaderId: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Reference to TeamLeader</span>
    status: ActivityStatus;        <span class="hljs-comment">// "Planned" | "InProgress" | "Completed"</span>
}

<span class="hljs-keyword">enum</span> ShiftType {
    MORNING = <span class="hljs-string">"Morning"</span>,
    AFTERNOON = <span class="hljs-string">"Afternoon"</span>,
    NIGHT = <span class="hljs-string">"Night"</span>
}

<span class="hljs-keyword">enum</span> ActivityType {
    WORKING_PRODUCTION = <span class="hljs-string">"WP"</span>,
    SPECIAL_ASSIGNMENT = <span class="hljs-string">"SA"</span>
}

<span class="hljs-keyword">enum</span> ActivityStatus {
    PLANNED = <span class="hljs-string">"Planned"</span>,
    IN_PROGRESS = <span class="hljs-string">"InProgress"</span>,
    COMPLETED = <span class="hljs-string">"Completed"</span>
}
</div></code></pre>
<h5 id="23-operator-assignment-document">2.3 Operator Assignment Document</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> OperatorAssignmentDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "OA_2025_09_SHIFT_M_SA001"</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"OperatorAssignment"</span>;
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// "WP_2025_09"</span>
    workingPlanId: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// "WP_2025_09"</span>
    shiftId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// "SHIFT_2025_09_MON_M"</span>
    activityId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// "SA_001"</span>
    operators: OperatorInfo[];     <span class="hljs-comment">// Array of assigned operators</span>
    metadata: {
        createdBy: <span class="hljs-built_in">string</span>;
        createdAt: <span class="hljs-built_in">string</span>;
        lastModifiedBy: <span class="hljs-built_in">string</span>;
        lastModifiedAt: <span class="hljs-built_in">string</span>;
        version: <span class="hljs-built_in">number</span>;
    };
}

<span class="hljs-keyword">interface</span> OperatorInfo {
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// "OP001"</span>
    name: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "John Doe"</span>
    status: OperatorAssignmentStatus; <span class="hljs-comment">// "Active" | "TLO"</span>
    assignedAt: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// Timestamp</span>
    assignedBy: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// User who made assignment</span>
}

<span class="hljs-keyword">enum</span> OperatorAssignmentStatus {
    ACTIVE = <span class="hljs-string">"Active"</span>,
    TECHNICAL_LAYOFF = <span class="hljs-string">"TLO"</span>
}
</div></code></pre>
<h5 id="24-projection-models">2.4 Projection Models</h5>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> CalendarProjectionDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "CAL_2025_09"</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"CalendarProjection"</span>;
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// "CALENDAR_2025"</span>
    year: <span class="hljs-built_in">number</span>;                  <span class="hljs-comment">// 2025</span>
    weekNumber: <span class="hljs-built_in">number</span>;            <span class="hljs-comment">// 9</span>
    startDate: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// "2025-02-24"</span>
    endDate: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// "2025-03-02"</span>
    workingDays: <span class="hljs-built_in">string</span>[];         <span class="hljs-comment">// ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]</span>
    holidays: Holiday[];           <span class="hljs-comment">// Holiday information</span>
    eventId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Reference to event that updated this</span>
    lastUpdated: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Timestamp</span>
}

<span class="hljs-keyword">interface</span> OperatorStatusProjectionDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "OPS_OP001"</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"OperatorStatusProjection"</span>;
    partitionKey: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// "OPERATORS_STATUS"</span>
    operatorId: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// "OP001"</span>
    name: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "John Doe"</span>
    currentStatus: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// "ACTIVE", "SICK_LEAVE", etc.</span>
    longTermStatus: LongTermStatus; <span class="hljs-comment">// Information about long-term status</span>
    eventId: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Reference to event that updated this</span>
    lastUpdated: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// Timestamp</span>
}
</div></code></pre>
<h4 id="3-business-processes">3. Business Processes</h4>
<h5 id="31-create-working-plan">3.1 Create Working Plan</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant WPS as Working Plan Service
    participant CPS as Calendar Projection Service
    participant CDB as Cosmos DB
    
    Client->>WPMS: POST /api/v1/workingplans
    Note over Client, WPMS: {year: 2025, weekNumber: 9}
    
    WPMS->>WPS: createPlan(2025, 9)
    WPS->>CDB: Check if plan exists
    CDB-->>WPS: Return result (not found)
    
    WPS->>CPS: getCalendarForWeek(2025, 9)
    CPS->>CDB: Query calendar projection
    CDB-->>CPS: Return calendar data
    CPS-->>WPS: Return calendar week
    
    WPS->>WPS: Create WorkingPlan document
    WPS->>WPS: Create days based on calendar
    WPS->>CDB: Save working plan document
    CDB-->>WPS: Confirm save
    
    WPS->>WPS: Generate default shifts
    WPS->>CDB: Save shift documents
    CDB-->>WPS: Confirm save
        
    WPS-->>WPMS: Return completed plan
    WPMS-->>Client: 201 Created with plan details
</div></code></pre>
<h5 id="32-assign-operators-with-status-check">3.2 Assign Operators with Status Check</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant OAS as Operator Assignment Service
    participant OSP as Operator Status Projection
    participant CDB as Cosmos DB
    
    Client->>WPMS: POST /api/v1/shifts/{shiftId}/activities/{activityId}/operators
    Note over Client, WPMS: {operatorIds: ["OP001", "OP002", "OP003"]}
    
    WPMS->>OAS: assignOperators(shiftId, activityId, operatorIds)
    OAS->>WPMS: getShiftById(shiftId)

<think>I need to continue generating the LLD-Module3.md document, picking up from where I left off. I was in the middle of describing the "Assign Operators with Status Check" workflow diagram in the Working Plan Service section. I should continue with completing this diagram and finish the rest of the document, including:

1. Complete the workflow diagram
2. Complete the Working Plan Service section
3. Add the Integration Patterns section
4. Add Security section
5. Add Performance Considerations
6. Add Deployment Strategy

I'll continue in the same style and format as the initial part of the document.</think>Here's the continuation of the LLD-Module3.md document:

```markdown
    WPMS->>CDB: Query shift
    CDB-->>WPMS: Return shift
    
    OAS->>OSP: checkOperatorsAvailability(operatorIds, shift.date)
    OSP->>CDB: Query operator status projections
    CDB-->>OSP: Return operator statuses
    
    Note over OSP: Filter out operators on long-term leave
    OSP-->>OAS: Return available operators
    
    alt Some operators unavailable
        OAS-->>WPMS: Return partial success with unavailable operators
        WPMS-->>Client: 207 Multi-Status with available/unavailable operators
    else All operators available
        OAS->>CDB: Create/Update operator assignment document
        CDB-->>OAS: Confirm save
        OAS-->>WPMS: Return assignment document
        WPMS-->>Client: 200 OK with assignment details
    end
</div></code></pre>
<h5 id="33-publish-working-plan">3.3 Publish Working Plan</h5>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant WPS as Working Plan Service
    participant VS as Validation Service
    participant CDB as Cosmos DB
    
    Client->>WPMS: PUT /api/v1/workingplans/WP_2025_09/status
    Note over Client, WPMS: {status: "Published"}
    
    WPMS->>WPS: updatePlanStatus("WP_2025_09", "Published")
    WPS->>CDB: Query plan
    CDB-->>WPS: Return plan
    
    alt Plan not found
        WPS-->>WPMS: Throw EntityNotFoundError
        WPMS-->>Client: HTTP 404 Not Found
    else Plan found
        WPS->>VS: validatePlanForPublication(plan)
        VS->>CDB: Query related entities
        CDB-->>VS: Return entities
        VS->>VS: Perform validation rules
        
        alt Validation failed
            VS-->>WPMS: Return validation errors
            WPMS-->>Client: HTTP 422 Unprocessable Entity
        else Validation passed
            WPS->>CDB: Update plan status
            CDB-->>WPS: Confirmation
            
            WPS-->>WPMS: Return updated plan
            WPMS-->>Client: HTTP 200 OK with updated plan
        end
    end
</div></code></pre>
<h4 id="4-api-endpoints">4. API Endpoints</h4>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
<th>Request Body</th>
<th>Response</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/api/v1/workingplans</code></td>
<td>GET</td>
<td>Get list of working plans</td>
<td>N/A</td>
<td>200 - List of plans</td>
</tr>
<tr>
<td><code>/api/v1/workingplans/current</code></td>
<td>GET</td>
<td>Get current week's plan</td>
<td>N/A</td>
<td>200 - Current plan</td>
</tr>
<tr>
<td><code>/api/v1/workingplans/{year}/{week}</code></td>
<td>GET</td>
<td>Get plan by year and week</td>
<td>N/A</td>
<td>200 - Plan details</td>
</tr>
<tr>
<td><code>/api/v1/workingplans</code></td>
<td>POST</td>
<td>Create new plan</td>
<td>Year, week number</td>
<td>201 - Created plan</td>
</tr>
<tr>
<td><code>/api/v1/workingplans/{id}/status</code></td>
<td>PUT</td>
<td>Update plan status</td>
<td>New status</td>
<td>200 - Updated plan</td>
</tr>
<tr>
<td><code>/api/v1/shifts/{shiftId}/activities/{activityId}/operators</code></td>
<td>POST</td>
<td>Assign operators to activity</td>
<td>List of operator IDs</td>
<td>200/207 - Assignment results</td>
</tr>
<tr>
<td><code>/api/v1/operators/available</code></td>
<td>GET</td>
<td>Get available operators for a date</td>
<td>date (query param)</td>
<td>200 - List of available operators</td>
</tr>
</tbody>
</table>
<h4 id="5-database-design">5. Database Design</h4>
<p>The Working Plan Service utilizes Cosmos DB with carefully optimized containers:</p>
<ol>
<li><strong>workingplans</strong>: Stores working plan documents with partition key on year</li>
<li><strong>shifts</strong>: Stores shift documents with partition key on working plan ID</li>
<li><strong>operatorAssignments</strong>: Stores operator assignment documents with partition key on working plan ID</li>
<li><strong>projections</strong>: Stores projection data from other systems (calendar, operator status)</li>
</ol>
<p>This design optimizes for common query patterns:</p>
<ul>
<li>Retrieving all shifts for a specific working plan</li>
<li>Looking up assignments for a specific shift and activity</li>
<li>Finding available operators for a specific date</li>
<li>Checking current operator status before assignment</li>
</ul>
<h2 id="5-integration-patterns">5. Integration Patterns</h2>
<h3 id="51-event-driven-communication">5.1 Event-Driven Communication</h3>
<p>In the Module 3 architecture, microservices do not communicate directly with each other. Instead, they detect changes through the Cosmos DB Change Feed and maintain their own optimized read models.</p>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    subgraph "Annual Calendar Service"
        ACS_W[Write Models]
        ACS_CF[Change Feed]
        ACS_R[Read Models]
        
        ACS_W --> ACS_CF
        ACS_CF --> ACS_R
    end
    
    subgraph "Working Plan Service"
        WPS_W[Write Models]
        WPS_CF[Change Feed]
        WPS_R[Read Models]
        WPS_CP[Calendar Projection]
        
        WPS_W --> WPS_CF
        WPS_CF --> WPS_R
        ACS_CF -.-> WPS_CP
    end
    
    subgraph "Headcount Service"
        HS_W[Write Models]
        HS_CF[Change Feed]
        HS_R[Read Models]
        HS_CP[Working Plan Projection]
        
        HS_W --> HS_CF
        HS_CF --> HS_R
        WPS_CF -.-> HS_CP
    end
</div></code></pre>
<p>This pattern offers several advantages:</p>
<ul>
<li>Loose coupling between services</li>
<li>Resilience to service outages</li>
<li>Independent evolution of each service</li>
<li>Optimized data models for specific service needs</li>
</ul>
<h3 id="52-cosmos-db-change-feed">5.2 Cosmos DB Change Feed</h3>
<p>The Change Feed pattern is central to the system's integration approach:</p>
<ol>
<li>
<p><strong>Change Feed Processors</strong>:</p>
<ul>
<li>Each service implements Change Feed processors to detect document changes</li>
<li>Processors are configured with lease containers to track their position</li>
<li>Resilient design handles retries and failures</li>
</ul>
</li>
<li>
<p><strong>Document Transformations</strong>:</p>
<ul>
<li>Raw document changes are transformed into projections</li>
<li>Only relevant data is extracted and stored</li>
<li>Denormalization optimizes for query performance</li>
</ul>
</li>
<li>
<p><strong>Consistency Model</strong>:</p>
<ul>
<li>Eventually consistent data propagation</li>
<li>Services process changes at their own pace</li>
<li>Optimistic concurrency control for conflict resolution</li>
</ul>
</li>
</ol>
<pre class="hljs"><code><div><span class="hljs-comment">// Example Change Feed Processor Configuration</span>
<span class="hljs-keyword">const</span> changeFeedProcessorConfig = {
  hostName: <span class="hljs-string">`host-<span class="hljs-subst">${serviceName}</span>-<span class="hljs-subst">${instanceId}</span>`</span>,
  leaseContainerName: <span class="hljs-string">"leases"</span>,
  targetContainerName: <span class="hljs-string">"events"</span>,
  createLeaseContainerIfNotExists: <span class="hljs-literal">true</span>,
  startFromBeginning: <span class="hljs-literal">false</span>,
  processorOptions: {
    maxItemCount: <span class="hljs-number">100</span>,
    startContinuationToken: <span class="hljs-literal">undefined</span>
  },
  onError: <span class="hljs-function">(<span class="hljs-params">err</span>) =&gt;</span> logger.error(<span class="hljs-string">`Change feed error: <span class="hljs-subst">${err.message}</span>`</span>),
  onChanges: <span class="hljs-keyword">async</span> (changes) =&gt; {
    <span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> doc of changes) {
      <span class="hljs-keyword">await</span> processDocumentChange(doc);
    }
  }
};
</div></code></pre>
<h3 id="53-read-model-projections">5.3 Read Model Projections</h3>
<p>Each service maintains projections from other services to support its own business logic:</p>
<ol>
<li>
<p><strong>Calendar Projections in Working Plan Service</strong>:</p>
<ul>
<li>Calendar events are processed to create optimized calendar projections</li>
<li>Working days, holidays, and week structures are extracted</li>
<li>Changes trigger recalculation of affected plans</li>
</ul>
</li>
<li>
<p><strong>Operator Status Projections in Working Plan Service</strong>:</p>
<ul>
<li>Operator status changes are tracked in dedicated projections</li>
<li>Long-term status information is used to determine availability</li>
<li>Ensures operators on leave aren't assigned to activities</li>
</ul>
</li>
<li>
<p><strong>Working Plan Projections in Headcount Service</strong>:</p>
<ul>
<li>Working Plan changes create projections for expected attendance</li>
<li>Used to compare actual vs. planned attendance</li>
<li>Supports reporting on attendance trends</li>
</ul>
</li>
</ol>
<h2 id="6-security">6. Security</h2>
<p>The Module 3 microservices implement several security measures:</p>
<ul>
<li><strong>Authentication</strong>: Azure AD B2C handles identity management</li>
<li><strong>Authorization</strong>: Role-based access control with fine-grained permissions</li>
<li><strong>API Gateway</strong>: Azure API Management enforces security policies</li>
<li><strong>Data Encryption</strong>: Data encrypted at rest and in transit</li>
<li><strong>Audit Logging</strong>: Comprehensive audit trails for all operations</li>
<li><strong>Certificate Management</strong>: TLS certificates for secure communication</li>
</ul>
<h3 id="role-based-access-control">Role-Based Access Control</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">enum</span> Role {
    ADMIN = <span class="hljs-string">"ADMIN"</span>,
    HR_MANAGER = <span class="hljs-string">"HR_MANAGER"</span>,
    PLANNER = <span class="hljs-string">"PLANNER"</span>,
    TEAM_LEADER = <span class="hljs-string">"TEAM_LEADER"</span>,
    SHIFT_MANAGER = <span class="hljs-string">"SHIFT_MANAGER"</span>,
    VIEWER = <span class="hljs-string">"VIEWER"</span>
}

<span class="hljs-keyword">const</span> rolePermissions = {
    [Role.ADMIN]: [<span class="hljs-string">"*"</span>],
    [Role.HR_MANAGER]: [<span class="hljs-string">"calendar.*"</span>, <span class="hljs-string">"headcount.read"</span>, <span class="hljs-string">"workingplan.read"</span>],
    [Role.PLANNER]: [<span class="hljs-string">"calendar.read"</span>, <span class="hljs-string">"headcount.read"</span>, <span class="hljs-string">"workingplan.*"</span>],
    [Role.TEAM_LEADER]: [<span class="hljs-string">"calendar.read"</span>, <span class="hljs-string">"headcount.manage"</span>, <span class="hljs-string">"workingplan.read"</span>],
    [Role.SHIFT_MANAGER]: [<span class="hljs-string">"calendar.read"</span>, <span class="hljs-string">"headcount.read"</span>, <span class="hljs-string">"workingplan.publish"</span>],
    [Role.VIEWER]: [<span class="hljs-string">"calendar.read"</span>, <span class="hljs-string">"headcount.read"</span>, <span class="hljs-string">"workingplan.read"</span>]
};
</div></code></pre>
<h2 id="7-performance-considerations">7. Performance Considerations</h2>
<p>Performance optimization strategies include:</p>
<ul>
<li><strong>Database Indexing</strong>: Strategic indexes on common query patterns</li>
<li><strong>Partition Key Design</strong>: Optimized partition keys for efficient data access</li>
<li><strong>Read/Write Separation</strong>: CQRS pattern for high-throughput operations</li>
<li><strong>Asynchronous Processing</strong>: Background processing for non-critical operations</li>
<li><strong>Pagination</strong>: All list endpoints support pagination for large datasets</li>
<li><strong>Rate Limiting</strong>: API rate limiting to prevent abuse</li>
</ul>
<h3 id="cosmos-db-optimization">Cosmos DB Optimization</h3>
<pre class="hljs"><code><div><span class="hljs-comment">// Example indexing policy for a Cosmos DB container</span>
<span class="hljs-keyword">const</span> indexingPolicy = {
  indexingMode: <span class="hljs-string">"consistent"</span>,
  includedPaths: [
    { path: <span class="hljs-string">"/type/?"</span> },
    { path: <span class="hljs-string">"/partitionKey/?"</span> },
    { path: <span class="hljs-string">"/year/?"</span> },
    { path: <span class="hljs-string">"/weekNumber/?"</span> },
    { path: <span class="hljs-string">"/status/?"</span> },
    { path: <span class="hljs-string">"/date/?"</span> },
    { path: <span class="hljs-string">"/operatorId/?"</span> }
  ],
  excludedPaths: [
    { path: <span class="hljs-string">"/metadata/*"</span> },
    { path: <span class="hljs-string">"/*"</span> }
  ]
};
</div></code></pre>
<h2 id="8-deployment-strategy">8. Deployment Strategy</h2>
<p>The Module 3 microservices use a cloud-native deployment approach:</p>
<ul>
<li><strong>Containerization</strong>: All services deployed as Docker containers</li>
<li><strong>Kubernetes</strong>: Azure Kubernetes Service for container orchestration</li>
<li><strong>CI/CD Pipeline</strong>: Automated builds and deployments via Azure DevOps</li>
<li><strong>Environment Separation</strong>: Development, Staging, and Production environments</li>
<li><strong>Configuration Management</strong>: External configuration via Azure App Configuration</li>
<li><strong>Blue/Green Deployments</strong>: Zero-downtime deployments</li>
<li><strong>Monitoring and Alerting</strong>: Application Insights for real-time monitoring</li>
</ul>
<h3 id="deployment-architecture">Deployment Architecture</h3>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    subgraph "Azure DevOps"
        GIT[Git Repository]
        BUILD[Build Pipeline]
        RELEASE[Release Pipeline]
    end
    
    subgraph "Azure Resources"
        ACR[Container Registry]
        AKS[Kubernetes Service]
        APIM[API Management]
        COSMOS[Cosmos DB]
        SQL[SQL Database]
        AAD[Azure AD B2C]
        INSIGHTS[Application Insights]
    end
    
    GIT --> BUILD
    BUILD --> ACR
    ACR --> RELEASE
    RELEASE --> AKS
    
    AKS --> APIM
    APIM --> AAD
    AKS --> COSMOS
    AKS --> SQL
    AKS --> INSIGHTS
</div></code></pre>
<p>This deployment approach ensures:</p>
<ol>
<li><strong>Consistent Environments</strong>: Same configuration across all environments</li>
<li><strong>Automated Deployments</strong>: Reduces manual errors and deployment time</li>
<li><strong>Scalability</strong>: Independent scaling of each microservice</li>
<li><strong>High Availability</strong>: Multiple replicas and availability zones</li>
<li><strong>Disaster Recovery</strong>: Geo-redundant data storage</li>
<li><strong>Monitoring</strong>: Real-time metrics and alerts</li>
</ol>
<pre class="hljs"><code><div></div></code></pre>

</body>
</html>
