# Annual Calendar Microservice - Low-Level Design Document

## Table of Contents
1. [Overview](#1-overview)
2. [Architecture Overview](#2-architecture-overview)
   1. [NestJS Service Structure](#21-nestjs-service-structure)
   2. [SQL Database Approach](#22-sql-database-approach)
   3. [Event-Driven Integration with CQRS](#23-event-driven-integration-with-cqrs)
   4. [Calendar Library Integration](#24-calendar-library-integration)
3. [System Boundaries](#3-system-boundaries)
4. [Data Models (SQL)](#4-data-models-sql)
   1. [Year Configuration Table](#41-year-configuration-table)
   2. [Holiday Table](#42-holiday-table)
   3. [Week Summary Table](#43-week-summary-table)
   4. [Month Configuration Table](#44-month-configuration-table)
   5. [Production Plan Read Models](#45-production-plan-read-models)
5. [CQRS Implementation Design](#5-cqrs-implementation-design)
   1. [Command Flow](#51-command-flow)
   2. [Event Flow](#52-event-flow)
   3. [Component Design](#53-component-design)
6. [Event Schema Definition](#6-event-schema-definition)
   1. [Base Event Structure](#61-base-event-structure)
   2. [Event Payload Schemas](#62-event-payload-schemas)
7. [Calendar Generation Service](#7-calendar-generation-service)
   1. [Calendar Generation Flow](#71-calendar-generation-flow)
   2. [Calendar Generation Rules](#72-calendar-generation-rules)
   3. [API Endpoints](#73-api-endpoints)
8. [Business Logic & Rules](#8-business-logic--rules)
   1. [Holiday Type Classification](#81-holiday-type-classification)
   2. [Working Day Calculation Rules](#82-working-day-calculation-rules)
   3. [Monthly Summary Calculation](#83-monthly-summary-calculation)
   4. [Religious Holiday Calculation](#84-religious-holiday-calculation)
9. [Role-Based Access Control](#9-role-based-access-control)
   1. [User Roles](#91-user-roles)
   2. [Permission Types](#92-permission-types)
10. [Performance & Scalability Considerations](#10-performance--scalability-considerations)
    1. [SQL Optimization](#101-sql-optimization)
    2. [Caching Strategy](#102-caching-strategy)
11. [Security Implementation](#11-security-implementation)
    1. [Authentication & Authorization](#111-authentication--authorization)
    2. [Data Protection](#112-data-protection)
    3. [Audit Logging](#113-audit-logging)
12. [Technology Stack](#12-technology-stack)
13. [Deployment Approach](#13-deployment-approach)
    1. [Containerization](#131-containerization)

## 1. Overview

The Annual Calendar microservice is designed to manage and provide calendar-related data, including holidays, working days, and monthly summaries. It is built using NestJS and leverages a SQL database for data storage, along with a calendar library that dynamically generates calendar data. This approach eliminates the need to store individual days in the database, focusing instead on storing only holidays and special working days. The microservice integrates with other systems using an event-driven architecture with CQRS pattern.

## 2. Architecture Overview

### 2.1 NestJS Service Structure

The Annual Calendar microservice will be built using NestJS with the following structure:

- **Modules**
  - `CalendarModule`: Core functionality for managing the annual calendar
  - `HolidayModule`: Specialized functionality for holiday management
  - `CalendarLibraryModule`: Integration with the calendar generation library

- **Controllers**
  - `CalendarController`: Handles calendar data retrieval and updates
  - `HolidayController`: Manages holiday-specific operations

- **Services**
  - `CalendarService`: Business logic for calendar operations
  - `HolidayService`: Business logic for holiday management
  - `WorkdayCalculationService`: Logic for calculating working days
  - `CalendarLibraryService`: Wrapper for the calendar library functions

- **Repositories**
  - `CalendarRepository`: Interfaces with SQL database for calendar data
  - `HolidayRepository`: Manages holiday data in SQL

### 2.2 SQL Database Approach

All data will be stored in SQL tables:

- **Normalized Data Model**: Using relational tables with appropriate foreign key relationships
- **Efficient Storage**: Only storing holidays, special working days, and summary data
- **Query Optimization**: Using indexes and stored procedures for efficient data access

### 2.3 Event-Driven Integration with CQRS

The Annual Calendar microservice leverages a CQRS (Command Query Responsibility Segregation) pattern with event-driven integration to propagate calendar data to other systems, particularly the Production Plan microservice:

- **Command Side**:
  - Handles all write operations (initialize year, add/update/delete holidays)
  - Performs validation and business rule enforcement
  - Persists data to SQL database
  - Generates domain events for all state changes

- **Query Side**:
  - Handles all read operations directly from SQL database
  - Optimized for query performance
  - Uses projections for specialized views

- **Event Publishing**:
  - All state changes generate domain events
  - Events are published to a message bus (e.g., Azure Service Bus)
  - Events follow a standardized schema with versioning
  - Event types include: YearInitialized, HolidayCreated, HolidayUpdated, etc.

- **Event Consumption**:
  - Production Plan microservice subscribes to calendar events
  - Maintains its own read-only copy of calendar data
  - Updates local data when events are received
  - No direct database access between microservices

This approach ensures:
1. Complete decoupling between Annual Calendar and Production Plan
2. Eventual consistency of calendar data across systems
3. Resilience to temporary outages or performance issues
4. Audit trail of all calendar changes via event stream

#### 2.3.1 Event Flow

```mermaid
graph LR
    subgraph "Annual Calendar"
        CMD[Commands]
        DB[(SQL Database)]
        EP[Event Publisher]
    end
    
    subgraph "Message Bus"
        EB[Event Topics]
    end
    
    subgraph "Production Plan"
        ES[Event Subscriber]
        PD[(Production DB)]
    end
    
    CMD -->|Execute| DB
    DB -->|Trigger| EP
    EP -->|Publish| EB
    EB -->|Consume| ES
    ES -->|Update| PD
```

#### 2.3.2 Event Schema Structure

All events follow a standardized schema to ensure consistency and forward compatibility:

```json
{
  "id": "uuid",
  "type": "calendar.holiday.created",
  "version": "1.0",
  "timestamp": "2024-03-14T15:30:00Z",
  "metadata": {
    "userId": "user-123",
    "correlationId": "request-456",
    "source": "annual-calendar-service"
  },
  "data": {
    // Event-specific payload
  }
}
```

### 2.4 Calendar Library Integration

The microservice will leverage a calendar library to generate day-by-day calendar data:

- **Library Selection**: Date-fns, Luxon, or Moment.js for date manipulation
- **Dynamic Generation**: Calendar days will be generated on-demand rather than stored
- **Customization**: Library will be extended to support business-specific calendar rules

## 3. System Boundaries

```mermaid
graph TD
    subgraph "Connected Workers Platform"
        subgraph "Annual Calendar Domain"
            CMD[Command API]
            QRY[Query API]
            CAL[(Calendar Data)]
        end
        
        subgraph "Event Bus"
            EB[Calendar Events]
        end
        
        subgraph "Production Plan Domain"
            PPS[Production Planning]
            PCAL[(Calendar Projection)]
        end
        
        subgraph "Other Domains"
            WP[Working Plan]
            HC[Headcount]
        end
    end
    
    CMD --> CAL
    CAL --> QRY
    CAL --> EB
    EB --> PCAL
    EB --> WP
    EB --> HC
    PCAL --> PPS
```

## 4. Data Models (SQL)

### 4.1 Year Configuration Table

```sql
CREATE TABLE YearConfigurations (
    YearId INT PRIMARY KEY,
    Status VARCHAR(20) NOT NULL CHECK (Status IN ('DRAFT', 'ACTIVE', 'ARCHIVED')),
    WeekStartsOn TINYINT NOT NULL DEFAULT 1,
    WeekEndsOn TINYINT NOT NULL DEFAULT 7,
    DefaultWorkingDays VARCHAR(20) NOT NULL DEFAULT '1,2,3,4,5', -- Stored as comma-separated list
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_year_status (Status)
);
```

### 4.2 Holiday Table

```sql
CREATE TABLE Holidays (
    HolidayId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Date DATE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    HolidayType VARCHAR(20) NOT NULL CHECK (HolidayType IN ('NATIONAL', 'RELIGIOUS', 'UNPAID_PUBLIC', 'TLO', 'FOURTH_SATURDAY', 'PLANT_HOLIDAY')),
    Color VARCHAR(20) NOT NULL,
    IsWorkingDay BIT NOT NULL DEFAULT 0,
    IsRecurring BIT NOT NULL DEFAULT 0,
    RecurringType VARCHAR(20) NULL,
    RecurringMonth TINYINT NULL,
    RecurringDay TINYINT NULL,
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_holiday_date (Date),
    INDEX idx_holiday_type (HolidayType),
    INDEX idx_holiday_year_month (YEAR(Date), MONTH(Date))
);
```

### 4.3 Week Summary Table

```sql
CREATE TABLE WeekSummaries (
    WeekId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    YearId INT NOT NULL,
    WeekNumber TINYINT NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    WorkingDays TINYINT NOT NULL,
    TotalHours DECIMAL(5,2) NOT NULL,
    Status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (Status IN ('DRAFT', 'APPROVED', 'ARCHIVED')),
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (YearId) REFERENCES YearConfigurations(YearId),
    UNIQUE (YearId, WeekNumber),
    INDEX idx_week_dates (StartDate, EndDate)
);
```

### 4.4 Month Configuration Table

```sql
CREATE TABLE MonthConfigurations (
    MonthId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    YearId INT NOT NULL,
    MonthNumber TINYINT NOT NULL,
    Name VARCHAR(20) NOT NULL,
    TotalDays TINYINT NOT NULL,
    WorkingDays TINYINT NOT NULL,
    FourthSaturday DATE NULL,
    Status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (Status IN ('DRAFT', 'APPROVED', 'ARCHIVED')),
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (YearId) REFERENCES YearConfigurations(YearId),
    UNIQUE (YearId, MonthNumber),
    INDEX idx_month_year (YearId, MonthNumber)
);
```

### 4.5 Production Plan Read Models

```sql
-- Production Plan Calendar Schema (Read Model)
CREATE TABLE ProductionCalendars (
    YearId INT PRIMARY KEY,
    Status VARCHAR(20) NOT NULL,
    WeekStartsOn TINYINT NOT NULL,
    WeekEndsOn TINYINT NOT NULL,
    DefaultWorkingDays VARCHAR(20) NOT NULL,
    LastUpdated DATETIME NOT NULL,
    EventId VARCHAR(36) NOT NULL,  -- Reference to last event that updated this record
    INDEX idx_status (Status)
);

CREATE TABLE ProductionHolidays (
    HolidayId VARCHAR(36) PRIMARY KEY,
    Date DATE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    HolidayType VARCHAR(20) NOT NULL,
    Color VARCHAR(20) NOT NULL,
    IsWorkingDay BIT NOT NULL,
    EventId VARCHAR(36) NOT NULL,  -- Reference to last event that updated this record
    INDEX idx_date (Date)
);

CREATE TABLE ProductionWeekSummaries (
    YearId INT NOT NULL,
    WeekNumber TINYINT NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    WorkingDays TINYINT NOT NULL,
    TotalHours DECIMAL(5,2) NOT NULL,
    EventId VARCHAR(36) NOT NULL,  -- Reference to last event that updated this record
    PRIMARY KEY (YearId, WeekNumber),
    INDEX idx_dates (StartDate, EndDate)
);
```

## 5. CQRS Implementation Design

### 5.1 Command Flow

```mermaid
sequenceDiagram
    participant Client
    participant API as API Gateway
    participant CH as Command Handler
    participant VS as Validation Service
    participant DS as Domain Service
    participant DB as SQL Database
    participant EP as Event Publisher
    participant EB as Event Bus
    
    Client->>API: POST /calendar/initialize/{year}
    API->>CH: InitializeYearCommand
    CH->>VS: Validate Command
    VS-->>CH: Validation Result
    
    alt Validation Failed
        CH-->>API: Return Validation Errors
        API-->>Client: 422 Unprocessable Entity
    else Validation Passed
        CH->>DS: Initialize Year
        DS->>DB: Create Year Configuration
        DB-->>DS: Confirmation
        
        DS->>DS: Generate Summaries
        DS->>DB: Save Month & Week Summaries
        DB-->>DS: Confirmation
        
        DS->>EP: Publish YearInitializedEvent
        EP->>EB: Send to Event Bus
        
        DS-->>CH: Return Result
        CH-->>API: Return Success
        API-->>Client: 201 Created
    end
```

### 5.2 Event Flow

```mermaid
sequenceDiagram
    participant ACS as Annual Calendar Service
    participant EP as Event Publisher
    participant EB as Event Bus
    participant ES as Event Subscriber
    participant PPS as Production Plan Service
    participant PPDB as Production Plan DB
    
    Note over ACS: Holiday Created
    ACS->>EP: Create HolidayCreatedEvent
    EP->>EB: Publish Event
    EB->>ES: Deliver Event
    ES->>PPS: Process HolidayCreatedEvent
    PPS->>PPDB: Create Production Holiday
    
    Note over ACS: Month Summary Calculated
    ACS->>EP: Create MonthSummaryCalculatedEvent
    EP->>EB: Publish Event
    EB->>ES: Deliver Event
    ES->>PPS: Process MonthSummaryCalculatedEvent
    PPS->>PPDB: Update Production Month Summary
```

### 5.3 Component Design

```mermaid
classDiagram
    class CalendarCommandService {
        +initializeYear(command)
        +addHoliday(command)
        +updateHoliday(command)
        +deleteHoliday(command)
        +calculateSummaries(command)
    }
    
    class CalendarQueryService {
        +getCalendarByYear(query)
        +getHolidaysByRange(query)
        +getMonthSummary(query)
        +getWeekSummary(query)
    }
    
    class CalendarEventPublisher {
        +publishEvent(event)
        -formatEvent(event)
    }
    
    class YearInitializedEvent
    class HolidayCreatedEvent
    class HolidayUpdatedEvent
    class SummaryCalculatedEvent
    
    class SQLDatabaseRepository {
        +save(entity)
        +find(criteria)
        +update(entity)
        +delete(id)
    }
    
    CalendarCommandService --> SQLDatabaseRepository
    CalendarCommandService --> CalendarEventPublisher
    CalendarQueryService --> SQLDatabaseRepository
    
    CalendarEventPublisher --> YearInitializedEvent
    CalendarEventPublisher --> HolidayCreatedEvent
    CalendarEventPublisher --> HolidayUpdatedEvent
    CalendarEventPublisher --> SummaryCalculatedEvent
```

## 6. Event Schema Definition

### 6.1 Base Event Structure

```typescript
// Base calendar event interface
interface CalendarEvent {
  id: string;                 // Unique event ID (UUID)
  type: CalendarEventType;    // Type of event
  version: string;            // Schema version for forward compatibility
  timestamp: string;          // ISO timestamp
  metadata: {
    userId: string;           // User who triggered the event
    correlationId: string;    // For tracking related events
    source: string;           // Source system identifier
  };
  data: any;                  // Event-specific payload
}

// Calendar event types
enum CalendarEventType {
  YEAR_INITIALIZED = 'calendar.year.initialized',
  YEAR_STATUS_CHANGED = 'calendar.year.status.changed',
  HOLIDAY_CREATED = 'calendar.holiday.created',
  HOLIDAY_UPDATED = 'calendar.holiday.updated',
  HOLIDAY_DELETED = 'calendar.holiday.deleted',
  WEEK_SUMMARY_CALCULATED = 'calendar.week.summary.calculated',
  MONTH_SUMMARY_CALCULATED = 'calendar.month.summary.calculated',
  FOURTH_SATURDAY_ASSIGNED = 'calendar.fourth.saturday.assigned'
}
```

### 6.2 Event Payload Schemas

```typescript
// Year Initialized Event
interface YearInitializedEvent extends CalendarEvent {
  type: CalendarEventType.YEAR_INITIALIZED;
  data: {
    yearId: number;
    weekStartsOn: number;
    weekEndsOn: number;
    defaultWorkingDays: number[];
    status: string;
  };
}

// Holiday Created Event
interface HolidayCreatedEvent extends CalendarEvent {
  type: CalendarEventType.HOLIDAY_CREATED;
  data: {
    holidayId: string;
    date: string;              // ISO date format
    name: string;
    holidayType: string;       // NATIONAL, RELIGIOUS, etc.
    color: string;
    isWorkingDay: boolean;
    isRecurring: boolean;
    recurringPattern?: {
      type: string;
      month?: number;
      day?: number;
    };
  };
}

// Week Summary Calculated Event
interface WeekSummaryCalculatedEvent extends CalendarEvent {
  type: CalendarEventType.WEEK_SUMMARY_CALCULATED;
  data: {
    yearId: number;
    weekNumber: number;
    startDate: string;         // ISO date format
    endDate: string;           // ISO date format
    workingDays: number;
    totalHours: number;
    holidays: Array<{
      date: string;            // ISO date format
      name: string;
      holidayType: string;
    }>;
  };
}
```

## 7. Calendar Generation Service

### 7.1 Calendar Generation Flow

```mermaid
sequenceDiagram
    participant HR as HR Manager
    participant API as Calendar API
    participant LIB as Calendar Library
    participant GEN as Generation Service
    participant DB as SQL Database

    HR->>API: Generate Year Calendar
    API->>GEN: InitializeYear(2025)
    GEN->>LIB: GenerateCalendarBase(2025)
    LIB-->>GEN: Return Calendar Structure
    GEN->>DB: Save Year Configuration
    GEN->>DB: Generate Month Summaries
    GEN->>DB: Generate Week Summaries
    GEN->>HR: Return Calendar for Review
    HR->>API: Add Holidays
    API->>DB: Store Holidays
    API->>GEN: RecalculateSummaries()
    GEN->>DB: Update Summaries
```

### 7.2 Calendar Generation Rules

1. Automatic Calendar Generation:
   - Calendar library generates day structure dynamically
   - Only holiday and special day data stored in database
   - Week numbers follow ISO-8601 standard
   - First week of year contains January 4th

2. Working Days Calculation:
   - Default: Monday to Friday (configurable)
   - Fourth Saturday calculated dynamically
   - Weekends determined by library
   - Holidays override working day status

3. Holiday Management:
   - HR adds holidays after initial configuration
   - Supports recurring and one-time holidays
   - Religious holidays can be added with variable dates

### 7.3 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/calendar/initialize/{year}` | POST | Initialize year configuration |
| `/calendar/{year}/weeks` | GET | Get all week summaries for year |
| `/calendar/{year}/week/{weekNumber}` | GET | Get specific week data |
| `/calendar/{year}/holidays` | POST | Add holiday |
| `/calendar/holidays/recurring` | POST | Add recurring holiday |

## 8. Business Logic & Rules

### 8.1 Holiday Type Classification

| Holiday Type | Color | Working Day | Description |
|--------------|-------|-------------|-------------|
| NATIONAL | RED | No | Government-declared holidays |
| RELIGIOUS | GREEN | No | Faith-based observances |
| UNPAID_PUBLIC | GREEN | Optional | Optional public holidays |
| TLO | BLUE | No | Temporary Layoff days |
| FOURTH_SATURDAY | ORANGE | No | Fourth Saturday of month |
| PLANT_HOLIDAY | YELLOW | No | Full plant closure |

### 8.2 Working Day Calculation Rules

- Standard working days: Monday through Friday
- Exceptions:
  - National and religious holidays are non-working days
  - Fourth Saturdays are non-working days
  - TLO days are non-working days but may be rescheduled
  - Special working days can be designated on weekends for production needs

### 8.3 Monthly Summary Calculation

Using the calendar library and SQL:

```typescript
async calculateMonthlySummary(year: number, month: number): Promise<MonthSummary> {
  // Get all days in month using calendar library
  const daysInMonth = this.calendarLibrary.getDaysInMonth(year, month);
  
  // Get all holidays for this month from database
  const holidays = await this.holidayRepository.findByYearAndMonth(year, month);
  
  // Calculate working days
  const weekendCount = daysInMonth.filter(day => 
    !this.isWorkingDay(day, holidays)
  ).length;
  
  const workingDays = daysInMonth.length - weekendCount - 
    holidays.filter(h => !h.isWorkingDay).length;
  
  // Calculate holiday counts by type
  const holidayCounts = this.countHolidaysByType(holidays);
  
  return {
    year,
    month,
    totalDays: daysInMonth.length,
    workingDays,
    holidayCounts,
    weekendCount
  };
}
```

### 8.4 Religious Holiday Calculation

- Religious holidays follow lunar calendars and may change yearly
- System integrates with external calendar APIs for accurate religious holiday dates
- Stored procedure updates these dates annually based on official calendars

## 9. Role-Based Access Control

### 9.1 User Roles

| Role | Description | Permissions |
|------|-------------|-------------|
| System Admin | Full system access | All permissions |
| HR Manager | Calendar management access | Create/update/delete holidays, Manage calendar |

### 9.2 Permission Types

| Permission | Description |
|------------|-------------|
| `calendar.read` | View calendar data |
| `calendar.write` | Modify calendar data |
| `holiday.read` | View holiday information |
| `holiday.write` | Modify holiday information |
| `user.read` | View user information |
| `user.write` | Modify user information |

## 10. Performance & Scalability Considerations

### 10.1 SQL Optimization

- **Indexing Strategy**:
  - Composite indexes on date ranges for holiday retrieval
  - Indexes on lookups by year, month, and week
  - Covering indexes for common query patterns

- **Query Optimization**:
  - Stored procedures for complex calculations
  - Parameterized queries to leverage query plan caching
  - Paging for large result sets

```sql
-- Example of an optimized stored procedure
CREATE PROCEDURE GetWorkingDaysBetweenDates
    @StartDate DATE,
    @EndDate DATE
AS
BEGIN
    DECLARE @WeekendDayCount INT;
    DECLARE @HolidayCount INT;
    
    -- Get weekend days using calendar calculation
    SELECT @WeekendDayCount = COUNT(*)
    FROM CalendarHelper.GetDateSeries(@StartDate, @EndDate)
    WHERE DATEPART(WEEKDAY, [Date]) IN (6, 7); -- Saturday and Sunday
    
    -- Get non-working holidays that fall on weekdays
    SELECT @HolidayCount = COUNT(*)
    FROM Holidays
    WHERE Date BETWEEN @StartDate AND @EndDate
      AND IsWorkingDay = 0
      AND DATEPART(WEEKDAY, Date) NOT IN (6, 7);
    
    -- Return working days
    SELECT DATEDIFF(DAY, @StartDate, @EndDate) + 1 - @WeekendDayCount - @HolidayCount;
END
```

### 10.2 Caching Strategy

- Redis caching for frequently accessed data:
  - Current year calendar structure (generated by library)
  - Holiday lists
  - Monthly and weekly summaries

- Cache invalidation via SQL dependency tracking:
  - Automatic invalidation when holiday data changes
  - Timed expiration for less frequently accessed data

## 11. Security Implementation

### 11.1 Authentication & Authorization

- JWT-based authentication
- NestJS Guards for role-based access control
- Middleware for request validation

### 11.2 Data Protection

- Parameterized queries to prevent SQL injection
- Input validation for all API endpoints
- Data encryption for sensitive information

### 11.3 Audit Logging

```sql
CREATE TABLE AuditLogs (
    LogId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId VARCHAR(50) NOT NULL,
    Action VARCHAR(50) NOT NULL,
    EntityType VARCHAR(50) NOT NULL,
    EntityId VARCHAR(50) NOT NULL,
    OldValue NVARCHAR(MAX) NULL,
    NewValue NVARCHAR(MAX) NULL,
    Timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    IPAddress VARCHAR(50) NULL,
    UserAgent NVARCHAR(500) NULL,
    INDEX idx_audit_entity (EntityType, EntityId),
    INDEX idx_audit_user (UserId),
    INDEX idx_audit_time (Timestamp)
);
```

## 12. Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| Backend Framework | NestJS | Application architecture |
| Database | Azure SQL Database | Data storage |
| Calendar Library | date-fns/Luxon | Calendar generation |
| ORM | TypeORM | Database access |
| Messaging | Azure Service Bus | Event processing |
| Authentication | JWT/Passport | Security |
| Containerization | Docker | Deployment |
| Orchestration | Kubernetes (AKS) | Scaling and management |
| Caching | Redis | Performance optimization |

## 13. Deployment Approach

### 13.1 Containerization

- Dockerized microservice
- Environment-specific configurations via environment variables
- Health check endpoints for monitoring
- Database migrations managed through TypeORM

```typescript
// Example of calendar library integration in NestJS
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Holiday } from './entities/holiday.entity';
import { addDays, eachDayOfInterval, getISOWeek, isSaturday, isSunday } from 'date-fns';

@Injectable()
export class CalendarLibraryService {
  constructor(
    @InjectRepository(Holiday)
    private holidayRepository: Repository<Holiday>,
  ) {}

  async generateCalendarDays(year: number): Promise<CalendarDay[]> {
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year, 11, 31);
    
    // Get all days in the year using date-fns
    const allDays = eachDayOfInterval({ start: startDate, end: endDate });
    
    // Get all holidays for this year
    const holidays = await this.holidayRepository.find({
      where: {
        date: Between(startDate, endDate)
      }
    });
    
    // Map each day with relevant information
    return allDays.map(date => {
      const holiday = holidays.find(h => 
        h.date.getFullYear() === date.getFullYear() && 
        h.date.getMonth() === date.getMonth() && 
        h.date.getDate() === date.getDate()
      );
      
      return {
        date,
        weekNumber: getISOWeek(date),
        isWeekend: isSaturday(date) || isSunday(date),
        isHoliday: !!holiday,
        holidayName: holiday?.name,
        holidayType: holiday?.type,
        isWorkingDay: this.isWorkingDay(date, !!holiday, holiday?.isWorkingDay)
      };
    });
  }

  private isWorkingDay(date: Date, isHoliday: boolean, holidayIsWorkingDay?: boolean): boolean {
    if (isHoliday && holidayIsWorkingDay !== undefined) {
      return holidayIsWorkingDay;
    }
    
    const dayOfWeek = date.getDay();
    // Default working days: Monday (1) to Friday (5)
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  }
}
```