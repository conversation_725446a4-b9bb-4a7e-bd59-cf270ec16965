
## Revised Data Model for Multi-Tenant Architecture

### Core Entities with Partitioning Strategy

#### 1. Operator
```
Operator {
    id: string                  // Unique identifier
    partitionKey: string        // Format: "country_teamId"
    badgeId: string
    firstName: string
    lastName: string
    teamId: string
    teamLeaderId: string        // Added as requested
    department: string
    category: string
    isActive: boolean
    country: string             // For hierarchical partitioning
}
```

#### 2. LongTermStatus
```
LongTermStatus {
    id: string
    partitionKey: string        // Format: "country_operatorId"
    operatorId: string
    type: LongTermStatusType
    startDate: date
    endDate: date
    reason: string
    approvedBy: string
    approvalRequestId: string   // Changed from approvalWorkflowId as requested
    createdAt: datetime
    updatedAt: datetime
    metadata: object
    country: string             // For hierarchical partitioning
}
```

#### 3. ShiftStatus
```
ShiftStatus {
    id: string
    partitionKey: string        // Format: "country_shiftId"
    operatorId: string
    shiftId: string
    date: date
    shiftCode: string
    status: ShiftStatusType
    location: LocationType
    timestamp: datetime
    reason: string
    updatedBy: string
    metadata: object
    country: string             // For hierarchical partitioning
}
```

#### 4. Shift
```
Shift {
    id: string
    partitionKey: string        // Format: "country_teamId_date"
    date: date
    shiftCode: string
    teamId: string
    teamLeaderId: string
    status: ShiftStatusEnum     // Updated to NotStarted/Started/Ended
    startTime: datetime
    endTime: datetime
    expectedOperatorCount: number
    actualOperatorCount: number
    activatedAt: datetime       // Kept for tracking when shift was started
    activatedBy: string
    country: string             // For hierarchical partitioning
}
```

#### 5. BadgeEvent
```
BadgeEvent {
    id: string
    partitionKey: string        // Format: "country_operatorId"
    operatorId: string
    badgeId: string
    eventType: BadgeEventType
    timestamp: datetime
    location: Location
    deviceId: string
    metadata: object
    country: string             // For hierarchical partitioning
}
```

#### 6. BadgeException
```
BadgeException {
    id: string
    partitionKey: string        // Format: "country_operatorId"
    operatorId: string
    shiftId: string
    exceptionType: BadgeExceptionType
    reportedBy: string
    timestamp: datetime
    resolution: string
    tempBadgeId: string
    status: string
    country: string             // For hierarchical partitioning
}
```

#### 7. VisualCheckSession
```
VisualCheckSession {
    id: string
    partitionKey: string        // Format: "country_shiftId"
    shiftId: string
    initiatedBy: string
    startTime: datetime
    endTime: datetime
    status: string
    operatorChecks: [OperatorVisualCheck]
    country: string             // For hierarchical partitioning
}
```

#### 8. OperatorVisualCheck
```
OperatorVisualCheck {
    id: string
    partitionKey: string        // Format: "country_sessionId"
    sessionId: string
    operatorId: string
    expectedStatus: ShiftStatusType
    actualStatus: ShiftStatusType
    comments: string
    checkedBy: string
    timestamp: datetime
    country: string             // For hierarchical partitioning
}
```

#### 9. WorkflowApproval
```
WorkflowApproval {
    id: string
    partitionKey: string        // Format: "country_requestId"
    requestId: string           // Changed from workflowRequestId
    approverId: string
    approvalTimestamp: datetime
    decision: ApprovalDecision
    comments: string
    level: number
    country: string             // For hierarchical partitioning
}
```

#### 10. ExpectedOperator
```
ExpectedOperator {
    id: string
    partitionKey: string        // Format: "country_shiftId"
    shiftId: string
    operatorId: string
    teamId: string
    expectedRole: string
    expectedStartTime: datetime
    expectedEndTime: datetime
    syncStatus: string
    country: string             // For hierarchical partitioning
}
```

### Updated Enums

#### LongTermStatusType
```
enum LongTermStatusType {
    ACTIVE = 'ACTIVE',
    EXTENDED_SICK_LEAVE = 'EXTENDED_SICK_LEAVE',
    MATERNITY_LEAVE = 'MATERNITY_LEAVE',
    SABBATICAL = 'SABBATICAL',
    SUSPENSION = 'SUSPENSION',
    TERMINATION = 'TERMINATION'
}
```

#### ShiftStatusType
```
enum ShiftStatusType {
    EXPECTED = 'EXPECTED',
    PRESENT_IN_BUS = 'PRESENT_IN_BUS',
    PRESENT_IN_PLANT = 'PRESENT_IN_PLANT',
    ABSENT = 'ABSENT',
    LEAVE = 'LEAVE',
    SICK_LEAVE = 'SICK_LEAVE',
    TLO = 'TLO', // Temporary Lay Off
    DELAY = 'DELAY',
    EXTERNAL_WORK = 'EXTERNAL_WORK',
    TRAINING = 'TRAINING'
}
```

#### ShiftStatusEnum
```
enum ShiftStatusEnum {
    NOT_STARTED = 'NOT_STARTED',
    STARTED = 'STARTED',
    ENDED = 'ENDED'
}
```

#### BadgeEventType
```
enum BadgeEventType {
    BUS_ENTRY = 'BUS_ENTRY',
    PLANT_ENTRY = 'PLANT_ENTRY',
    PLANT_EXIT = 'PLANT_EXIT'
}
```

#### LocationType
```
enum LocationType {
    BUS = 'BUS',
    PLANT = 'PLANT',
    WORKSTATION = 'WORKSTATION',
    EXTERNAL = 'EXTERNAL'
}
```

#### BadgeExceptionType
```
enum BadgeExceptionType {
    FORGOTTEN = 'FORGOTTEN',
    LOST = 'LOST',
    DAMAGED = 'DAMAGED',
    TEMPORARY = 'TEMPORARY'
}
```

#### ApprovalDecision
```
enum ApprovalDecision {
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    PENDING_INFO = 'PENDING_INFO'
}
```

## Unified Event Model

### Common Event Interface
```
Event {
    id: string
    partitionKey: string        // Format: "country_eventType"
    eventType: EventType
    timestamp: datetime
    operatorId: string          // Optional based on event type
    data: object                // Event-specific data
    metadata: {
        correlationId: string
        source: string
        country: string         // For filtering by country
    }
}
```

### EventType Enum
```
enum EventType {
    BADGE_SCAN = 'BADGE_SCAN',
    STATUS_CHANGE = 'STATUS_CHANGE',
    VISUAL_CHECK = 'VISUAL_CHECK',
    SHIFT_STARTED = 'SHIFT_STARTED',
    SHIFT_ENDED = 'SHIFT_ENDED',
    BADGE_EXCEPTION = 'BADGE_EXCEPTION',
    LONG_TERM_STATUS_CHANGE = 'LONG_TERM_STATUS_CHANGE',
    WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED'
}
```

### Event Type-Specific Data Structures

#### BadgeScanEventData
```
BadgeScanEventData {
    badgeId: string
    location: {
        type: LocationType
        deviceId: string
        coordinates: {
            latitude: number
            longitude: number
        }
    }
}
```

#### StatusChangeEventData
```
StatusChangeEventData {
    statusType: "LONG_TERM" | "SHIFT"
    previousStatus: string
    newStatus: string
    reason: string
    location: Location
    shiftId: string
    initiatedBy: string
}
```

#### VisualCheckEventData
```
VisualCheckEventData {
    sessionId: string
    expectedStatus: ShiftStatusType
    actualStatus: ShiftStatusType
    checkedBy: string
    comments: string
}
```

#### ShiftStartedEventData
```
ShiftStartedEventData {
    shiftId: string
    teamLeaderId: string
    teamId: string
    shiftCode: string
    expectedOperatorCount: number
}
```

#### BadgeExceptionEventData
```
BadgeExceptionEventData {
    exceptionType: BadgeExceptionType
    reportedBy: string
    resolution: string
    tempBadgeId: string
}
```

#### LongTermStatusChangeEventData
```
LongTermStatusChangeEventData {
    previousStatus: LongTermStatusType
    newStatus: LongTermStatusType
    startDate: date
    endDate: date
    reason: string
    approvedBy: string
    requestId: string
}
```

#### WorkflowCompletedEventData
```
WorkflowCompletedEventData {
    requestId: string
    requestType: string
    status: string
    details: {
        statusType: LongTermStatusType
        startDate: date
        endDate: date
        reason: string
    }
    approvedBy: string
}
```

## Updated Flow Diagrams

### 1. Badge Scan Status Change Flow

```mermaid
sequenceDiagram
    participant BR as Badge Reader
    participant APIM as API Management
    participant EP as Event Processor
    participant SP as Status Processor
    participant RE as Rule Engine
    participant DB as Cosmos DB
    participant CF as Change Feed
    participant SR as SignalR Hub
    participant OT as Optitime System

    alt Bus Badge Reader
        BR->>APIM: Badge Scan Event (Bus)
        APIM->>EP: Process Badge Event
    else Plant Badge Reader (via Optitime)
        OT->>APIM: Badge Scan Event (Plant)
        APIM->>EP: Process Badge Event
    end
    
    EP->>SP: Determine Status Change
    SP->>RE: Validate Status Transition
    RE-->>SP: Validation Result
    
    SP->>DB: Store Badge Event
    SP->>DB: Update ShiftStatus
    
    DB->>CF: Trigger Change Feed
    CF->>SR: Push Real-time Updates
```

### 2. Long-Term vs. Shift Status Resolution Flow

```mermaid
sequenceDiagram
    participant UI as User Interface
    participant APIM as API Management
    participant SR as Status Resolver
    participant LTS as Long-Term Status Repository
    participant SS as Shift Status Repository
    
    UI->>APIM: Get Operator Current Status
    APIM->>SR: Resolve Effective Status
    
    SR->>LTS: Get Long-Term Status
    LTS-->>SR: Long-Term Status Data
    
    SR->>SS: Get Shift Status
    SS-->>SR: Shift Status Data
    
    SR->>SR: Apply Status Resolution Rules
    
    alt Long-Term Status Takes Precedence
        SR-->>APIM: Return Long-Term Status
    else Shift Status Valid
        SR-->>APIM: Return Shift Status
    else Display Both
        SR-->>APIM: Return Combined Status
    end
    
    APIM-->>UI: Display Resolved Status
```

### 3. Visual Check Flow

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant APIM as API Management
    participant VS as Visual Check Service
    participant ESR as Expected Shift Repository
    participant SR as Status Resolver
    participant DB as Cosmos DB
    
    TL->>APIM: Request Visual Check
    APIM->>VS: Initialize Visual Check Session
    VS->>ESR: Fetch Expected Operators
    
    loop For Each Operator
        VS->>SR: Get Resolved Status
        SR-->>VS: Current Status
    end
    
    VS-->>APIM: Visual Check Data
    APIM-->>TL: Display Visual Check UI
    
    TL->>APIM: Submit Visual Checks
    APIM->>VS: Process Visual Checks
    
    loop For Each Changed Status
        VS->>DB: Store Visual Check Event
        VS->>DB: Update Shift Status
    end
    
    VS-->>APIM: Confirmation
    APIM-->>TL: Visual Check Complete
```

### 4. Event Sourcing & CQRS Flow

```mermaid
graph TD
    subgraph "Command Side"
        APIM[API Management]
        BC[Badge Command]
        VSC[Visual Check Command]
        SC[Status Command]
        ES[Event Store]
    end
    
    subgraph "Query Side"
        CF[Change Feed]
        PH[Projection Handler]
        SR[Status Resolver]
        OSR[Operator Status Read Model]
        SSR[Shift Status Read Model]
        LTSR[Long-Term Status Read Model]
    end
    
    subgraph "Integration"
        PP[Production Plan]
        WP[Working Plan]
        TKS[TKS System]
    end
    
    APIM --> BC
    APIM --> VSC
    APIM --> SC
    
    BC --> ES
    VSC --> ES
    SC --> ES
    
    ES --> CF
    
    CF --> PH
    PH --> OSR
    PH --> SSR
    PH --> LTSR
    
    SR --> OSR
    SR --> SSR
    SR --> LTSR
    
    PH --> PP
    PH --> TKS
    
    WP --> APIM
```

### 5. Shift Activation & Operator Status Flow

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant APIM as API Management
    participant SH as Shift Handler
    participant EO as Expected Operators Repository
    participant SR as Status Resolver
    participant DB as Cosmos DB
    
    TL->>APIM: Start Shift
    APIM->>SH: Process Shift Started
    SH->>EO: Fetch Expected Operators
    EO-->>SH: Expected Operator List
    
    SH->>DB: Update Shift Status to STARTED
    SH->>DB: Emit ShiftStartedEvent
    
    loop For Each Expected Operator
        SH->>SR: Check Long-Term Status
        SR-->>SH: Long-Term Status
        
        alt Not on Long-Term Leave
            SH->>DB: Create EXPECTED Shift Status
        else On Long-Term Leave
            SH->>DB: Tag as Long-Term Absent
        end
    end
    
    SH-->>APIM: Shift Started
    APIM-->>TL: Confirmation
```

### 6. Workflow Integration for Long-Term Status Change

```mermaid
sequenceDiagram
    participant TL as Team Leader
    participant UI as User Interface
    participant APIM as API Management
    participant WF as Workflow Service
    participant DB as Cosmos DB
    participant ES as Event Store
    participant PP as Production Plan Service
    
    TL->>UI: Submit Leave Request
    UI->>APIM: Create Request
    APIM->>WF: Start Workflow Process
    
    Note over WF: Approval Process
    
    WF->>WF: Process Approvals
    
    alt Request Approved
        WF->>ES: Emit WorkflowCompletedEvent (Approved)
        ES->>DB: Store Event
        DB->>DB: Trigger LongTermStatusChangeEvent
        
        DB->>PP: Notify Production Plan
        
        APIM-->>UI: Show Confirmation
        UI-->>TL: Request Approved
    else Request Rejected
        WF->>ES: Emit WorkflowCompletedEvent (Rejected)
        APIM-->>UI: Show Rejection
        UI-->>TL: Request Rejected
    end
```

## Status Resolution Strategy

When determining an operator's effective status:

1. **Check Long-Term Status first:**
   - If operator has an active long-term status (e.g., EXTENDED_SICK_LEAVE), this is their primary status.
   - Long-term status is stored in a separate container for clearer separation of concerns.
   - The UI should display the long-term status prominently, with shift-based status as secondary information.

2. **Special Case Handling:**
   - For operators with long-term status who badge in (e.g., delivering documents):
     - Record the badge event and shift status normally
     - The system will maintain both statuses (long-term and shift)
     - UI will prioritize displaying long-term status but also show "Present in Plant" as secondary information

3. **Status Resolution Logic:**
```
function resolveStatus(operatorId, date, country):
    longTermStatus = getLongTermStatus(operatorId, date, country)
    shiftStatus = getShiftStatus(operatorId, date, country)
    
    if longTermStatus exists && longTermStatus.type != ACTIVE:
        primaryStatus = longTermStatus
        secondaryStatus = shiftStatus
    else:
        primaryStatus = shiftStatus
        secondaryStatus = null
    
    return {primaryStatus, secondaryStatus}
```

## Cosmos DB Partitioning Strategy

For a multi-tenant application using Cosmos DB, I've implemented:

1. **Primary Partition Key:**
   - Country-based partitioning as the primary strategy to isolate data by tenant
   - Combined with entity-specific identifiers for better RU utilization
   
2. **Hierarchical Partitioning:**
   - Added a country field to all entities for filtering within partitions
   - This supports both country-level and global queries efficiently

3. **Partition Key Formats:**
   - Operator: "country_teamId" - Groups operators by teams within countries
   - LongTermStatus: "country_operatorId" - Groups an operator's statuses together
   - ShiftStatus: "country_shiftId" - Groups all statuses for a shift 
   - Shift: "country_teamId_date" - Optimal for querying shifts by team and date
   - Events: "country_eventType" - Enables efficient event processing by type

4. **Advantages of this approach:**
   - Improves query performance by reducing cross-partition queries
   - Better distribution of RUs across physical partitions
   - Supports country-specific operations without scanning entire database
   - Enables efficient team-based queries which are common in this domain

## Event Sourcing & CQRS Recommendations

1. **Unified Event Model:**
   - All events now follow the same structure with event-specific data in the "data" field
   - The "eventType" field determines how to interpret the data

2. **Event Store Design:**
   - Use a dedicated container for the event store with "country_eventType" as partition key
   - This enables efficient replay of events by type or country

3. **Change Feed Processing:**
   - Create separate projectors for different read models
   - Use lease containers to track processing position
   - Implement resilient processing with error handling and retries

4. **Integration Events:**
   - Use the Change Feed processor to transform domain events into integration events
   - Maintain a separate outbox container for reliable delivery to external systems

5. **Read Models:**
   - Create dedicated containers for each read model optimized for specific query patterns
   - Use materialized views to support complex aggregations and reporting requirements
