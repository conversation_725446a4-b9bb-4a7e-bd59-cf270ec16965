# Working Plan Microservice - Low-Level Design Document (Redesigned)

## Table of Contents
1. [Overview](#1-overview)
2. [System Architecture](#2-system-architecture)
   1. [High-Level Architecture](#21-high-level-architecture)
   2. [Domain Services](#22-domain-services)
   3. [CQRS Pattern Implementation](#23-cqrs-pattern-implementation)
3. [Data Model](#3-data-model)
   1. [Document Types (Cosmos DB)](#31-document-types-cosmos-db)
   2. [Document Schemas](#32-document-schemas)
   3. [Projection Models](#33-projection-models)
4. [Service Components](#4-service-components)
   1. [Core Services](#41-core-services)
   2. [Integration Services](#42-integration-services)
   3. [Repository Implementations](#43-repository-implementations)
5. [API Endpoints](#5-api-endpoints)
   1. [Working Plan Management](#51-working-plan-management)
   2. [Shift Management](#52-shift-management)
   3. [Activity Management](#53-activity-management)
   4. [Operator Assignment](#54-operator-assignment)
   5. [Reference Data](#55-reference-data)
6. [Event-Driven Architecture](#6-event-driven-architecture)
   1. [Event Schemas](#61-event-schemas)
   2. [Event Handling](#62-event-handling)
   3. [Subscription Management](#63-subscription-management)
7. [Business Processes](#7-business-processes)
   1. [Create Working Plan](#71-create-working-plan)
   2. [Assign Operators with Status Check](#72-assign-operators-with-status-check)
   3. [Publish Working Plan](#73-publish-working-plan)
8. [Security & Authorization](#8-security--authorization)
   1. [Role Definitions](#81-role-definitions)
   2. [Authorization Middleware](#82-authorization-middleware)
9. [Performance Considerations](#9-performance-considerations)
   1. [Cosmos DB Optimization](#91-cosmos-db-optimization)
   2. [Caching Strategy](#92-caching-strategy)
   3. [Query Optimization](#93-query-optimization)

## 1. Overview

The Working Plan Microservice manages weekly work plans, shift assignments, and operator scheduling. This revised design implements a document-based approach using Cosmos DB with CQRS patterns for integration with Calendar and Operator Status information. 

The system enables planners to create weekly schedules, assign operators to activities, and ensures that only available operators are selected by verifying their long-term status through event-driven integration with the Operator Service.

## 2. System Architecture

### 2.1 High-Level Architecture

```mermaid
graph TD
    Client[Client Applications]
    API[API Gateway]
    WPMS[Working Plan Microservice]
    SB[Service Bus]
    APS[Annual Plan Service]
    OPS[Operator Service]
    CDB[(Cosmos DB)]
    REDIS[(Redis Cache)]
    
    Client --> API
    API --> WPMS
    WPMS <--> CDB
    WPMS <--> REDIS
    
    APS --> SB
    OPS --> SB
    SB --> WPMS
    
    subgraph "CQRS Pattern"
        CMD[Command Side]
        QRY[Query Side]
        EVS[Event Store]
        PRJ[Projections]
    end
    
    WPMS --- CMD
    WPMS --- QRY
    CMD --> EVS
    EVS --> PRJ
    PRJ --> QRY
```

### 2.2 Domain Services

```mermaid
graph LR
    WPMS[Working Plan Microservice]
    
    subgraph "Core Domains"
        WPC[Working Plan Core]
        SHF[Shift Management]
        ACT[Activity Management]
        OPA[Operator Assignment]
    end
    
    subgraph "Integration Domains"
        CAL[Calendar Integration]
        OPR[Operator Status Integration]
    end
    
    subgraph "Event Processing"
        EVT[Event Processor]
        SUB[Subscription Manager]
    end
    
    WPMS --- WPC
    WPMS --- SHF
    WPMS --- ACT
    WPMS --- OPA
    WPMS --- CAL
    WPMS --- OPR
    WPMS --- EVT
    WPMS --- SUB
```

### 2.3 CQRS Pattern Implementation

```mermaid
flowchart TB
    subgraph "Annual Calendar Service"
        ACS[Command API]
        ACE[Event Publisher]
    end
    
    subgraph "Operator Service"
        OPS[Command API]
        OPE[Event Publisher]
    end
    
    subgraph "Service Bus"
        CT[Calendar Topic]
        OT[Operator Topic]
    end
    
    subgraph "Working Plan Service"
        subgraph "Event Handlers"
            CSH[Calendar Subscriber]
            OSH[Operator Subscriber]
        end
        
        subgraph "Projections"
            CP[Calendar Projector]
            OP[Operator Projector]
        end
        
        subgraph "Document Store"
            CPD[(Calendar Projections)]
            OPD[(Operator Projections)]
        end
    end
    
    ACE -- Calendar Events --> CT
    OPE -- Operator Events --> OT
    
    CT -- Subscribe --> CSH
    OT -- Subscribe --> OSH
    
    CSH -- Process Events --> CP
    OSH -- Process Events --> OP
    
    CP -- Project Data --> CPD
    OP -- Project Data --> OPD
```

## 3. Data Model

### 3.1 Document Types (Cosmos DB)

The microservice uses the following Cosmos DB containers:

- **workingplans**: Working plan documents
- **shifts**: Shift documents
- **operatorAssignments**: Operator assignment documents
- **referenceData**: Reference data including family zones
- **projections**: Projections from external events (calendar, operator status)

### 3.2 Document Schemas

#### WorkingPlan Document
```typescript
interface WorkingPlanDocument {
    id: string;                    // "WP_2025_09"
    type: "WorkingPlan";          
    partitionKey: string;          // "WP_2025"
    weekNumber: number;            // 9
    year: number;                  // 2025
    startDate: string;             // "2025-02-24"
    endDate: string;               // "2025-03-02"
    status: WorkingPlanStatus;     // "Draft" | "Published" | "Archived"
    days: DayInfo[];               // Embedded days information
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}

interface DayInfo {
    date: string;                  // "2025-02-24"
    dayOfWeek: string;             // "Monday"
    dayType: DayType;              // "WorkingDay" | "Holiday" | "Weekend"
    holidayName?: string;          // Only for Holiday days
}

enum WorkingPlanStatus {
    DRAFT = "Draft",
    PUBLISHED = "Published",
    ARCHIVED = "Archived"
}

enum DayType {
    WORKING_DAY = "WorkingDay",
    HOLIDAY = "Holiday",
    WEEKEND = "Weekend"
}
```

#### Shift Document
```typescript
interface ShiftDocument {
    id: string;                    // "SHIFT_2025_09_MON_M"
    type: "Shift";
    partitionKey: string;          // "WP_2025_09"
    workingPlanId: string;         // "WP_2025_09"
    date: string;                  // "2025-02-24"
    shiftType: ShiftType;          // "Morning" | "Afternoon" | "Night"
    code: string;                  // "M", "A", "N"
    startTime: string;             // "06:00"
    endTime: string;               // "14:00"
    activities: ShiftActivityInfo[]; // Embedded activities
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}

interface ShiftActivityInfo {
    id: string;                    // "SA_001" 
    familyZoneId: string;          // Reference to FamilyZone
    activityType: ActivityType;    // "WP" | "SA"
    description: string;           // "Front Door Assembly"
    teamId: string;                // Reference to Team
    teamLeaderId: string;          // Reference to TeamLeader
    status: ActivityStatus;        // "Planned" | "InProgress" | "Completed"
}

enum ShiftType {
    MORNING = "Morning",
    AFTERNOON = "Afternoon",
    NIGHT = "Night"
}

enum ActivityType {
    WORKING_PRODUCTION = "WP",
    SPECIAL_ASSIGNMENT = "SA"
}

enum ActivityStatus {
    PLANNED = "Planned",
    IN_PROGRESS = "InProgress",
    COMPLETED = "Completed"
}
```

#### OperatorAssignment Document
```typescript
interface OperatorAssignmentDocument {
    id: string;                    // "OA_2025_09_SHIFT_M_SA001"
    type: "OperatorAssignment";
    partitionKey: string;          // "WP_2025_09"
    workingPlanId: string;         // "WP_2025_09"
    shiftId: string;               // "SHIFT_2025_09_MON_M"
    activityId: string;            // "SA_001"
    operators: OperatorInfo[];     // Array of assigned operators
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}

interface OperatorInfo {
    operatorId: string;            // "OP001"
    name: string;                  // "John Doe"
    status: OperatorAssignmentStatus; // "Active" | "TLO"
    assignedAt: string;            // Timestamp
    assignedBy: string;            // User who made assignment
}

enum OperatorAssignmentStatus {
    ACTIVE = "Active",
    TECHNICAL_LAYOFF = "TLO"
}
```

#### FamilyZone Document
```typescript
interface FamilyZoneDocument {
    id: string;                    // "FRONTDOOR_V43X"
    type: "FamilyZone";
    partitionKey: string;          // "REFERENCE_DATA"
    name: string;                  // "Front Doors V43X"
    description: string;           // "Front door assembly for V43X model"
    status: string;                // "Active" | "Inactive"
    metadata: {
        createdBy: string;
        createdAt: string;
        lastModifiedBy: string;
        lastModifiedAt: string;
        version: number;
    };
}
```

### 3.3 Projection Models

#### Calendar Projection Document
```typescript
interface CalendarProjectionDocument {
    id: string;                    // "CAL_2025_09"
    type: "CalendarProjection";
    partitionKey: string;          // "CALENDAR_2025"
    year: number;                  // 2025
    weekNumber: number;            // 9
    startDate: string;             // "2025-02-24"
    endDate: string;               // "2025-03-02"
    workingDays: string[];         // ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
    holidays: Holiday[];           // Holiday information
    eventId: string;               // Reference to event that updated this
    lastUpdated: string;           // Timestamp
}

interface Holiday {
    date: string;                  // "2025-02-26"
    name: string;                  // "Company Holiday"
    type: string;                  // "NATIONAL", "COMPANY", etc.
}
```

#### Operator Status Projection Document
```typescript
interface OperatorStatusProjectionDocument {
    id: string;                    // "OPS_OP001"
    type: "OperatorStatusProjection";
    partitionKey: string;          // "OPERATORS_STATUS"
    operatorId: string;            // "OP001"
    name: string;                  // "John Doe"
    currentStatus: string;         // "ACTIVE", "SICK_LEAVE", etc.
    longTermStatus: LongTermStatus; // Information about long-term status
    eventId: string;               // Reference to event that updated this
    lastUpdated: string;           // Timestamp
}

interface LongTermStatus {
    type: string;                  // "SICK_LEAVE", "MATERNITY_LEAVE", etc.
    startDate: string;             // "2025-02-24"
    endDate: string;               // "2025-03-15"
    reason: string;                // "Medical leave"
    isAvailableForSelection: boolean; // Whether operator can be selected for assignments
}
```

## 4. Service Components

### 4.1 Core Services

#### Working Plan Service
```typescript
interface IWorkingPlanService {
    createPlan(year: number, weekNumber: number): Promise<WorkingPlanDocument>;
    getPlan(year: number, weekNumber: number): Promise<WorkingPlanDocument>;
    updatePlanStatus(planId: string, status: WorkingPlanStatus): Promise<WorkingPlanDocument>;
    deletePlan(planId: string): Promise<void>;
    getCurrentWeekPlan(): Promise<WorkingPlanDocument>;
    listPlans(filters: PlanFilters): Promise<WorkingPlanSummary[]>;
}
```

#### Shift Service
```typescript
interface IShiftService {
    getShiftsForPlan(planId: string): Promise<ShiftDocument[]>;
    getShiftById(shiftId: string): Promise<ShiftDocument>;
    updateShift(shiftId: string, shiftData: Partial<ShiftDocument>): Promise<ShiftDocument>;
    generateDefaultShifts(planId: string): Promise<ShiftDocument[]>;
    getShiftsForDate(date: string): Promise<ShiftDocument[]>;
}
```

#### Activity Service
```typescript
interface IActivityService {
    createActivity(shiftId: string, activityData: Partial<ShiftActivityInfo>): Promise<ShiftDocument>;
    getActivitiesForShift(shiftId: string): Promise<ShiftActivityInfo[]>;
    updateActivity(shiftId: string, activityId: string, activityData: Partial<ShiftActivityInfo>): Promise<ShiftDocument>;
    deleteActivity(shiftId: string, activityId: string): Promise<ShiftDocument>;
}
```

#### Operator Assignment Service
```typescript
interface IOperatorAssignmentService {
    assignOperators(shiftId: string, activityId: string, operatorIds: string[]): Promise<OperatorAssignmentDocument>;
    updateOperatorStatus(assignmentId: string, operatorId: string, status: OperatorAssignmentStatus): Promise<OperatorAssignmentDocument>;
    getAssignment(assignmentId: string): Promise<OperatorAssignmentDocument>;
    removeOperatorFromActivity(assignmentId: string, operatorId: string): Promise<OperatorAssignmentDocument>;
    getAvailableOperatorsForDate(date: string): Promise<OperatorInfo[]>;
}
```

#### Family Zone Service
```typescript
interface IFamilyZoneService {
    getAllFamilyZones(): Promise<FamilyZoneDocument[]>;
    getFamilyZoneById(zoneId: string): Promise<FamilyZoneDocument>;
    createFamilyZone(zoneData: Partial<FamilyZoneDocument>): Promise<FamilyZoneDocument>;
    updateFamilyZone(zoneId: string, zoneData: Partial<FamilyZoneDocument>): Promise<FamilyZoneDocument>;
}
```

### 4.2 Integration Services

#### Calendar Projection Service
```typescript
interface ICalendarProjectionService {
    getCalendarForWeek(year: number, weekNumber: number): Promise<CalendarProjectionDocument>;
    getWorkingDaysForWeek(year: number, weekNumber: number): Promise<string[]>;
    getHolidaysForWeek(year: number, weekNumber: number): Promise<Holiday[]>;
    isDayWorkingDay(date: string): Promise<boolean>;
}
```

#### Operator Status Projection Service
```typescript
interface IOperatorStatusProjectionService {
    getOperatorStatus(operatorId: string): Promise<OperatorStatusProjectionDocument>;
    isOperatorAvailable(operatorId: string, date: string): Promise<boolean>;
    getAvailableOperators(date: string): Promise<OperatorStatusProjectionDocument[]>;
    filterAvailableOperators(operatorIds: string[], date: string): Promise<string[]>;
}
```

#### Event Subscription Service
```typescript
interface IEventSubscriptionService {
    subscribeToCalendarEvents(): Promise<void>;
    subscribeToOperatorEvents(): Promise<void>;
    unsubscribeFromCalendarEvents(): Promise<void>;
    unsubscribeFromOperatorEvents(): Promise<void>;
    getSubscriptionStatus(): Promise<SubscriptionStatus>;
}
```

### 4.3 Repository Implementations

#### Working Plan Repository
```typescript
interface IWorkingPlanRepository {
    create(plan: WorkingPlanDocument): Promise<WorkingPlanDocument>;
    findById(id: string): Promise<WorkingPlanDocument>;
    findByYearAndWeek(year: number, weekNumber: number): Promise<WorkingPlanDocument>;
    update(id: string, planData: Partial<WorkingPlanDocument>): Promise<WorkingPlanDocument>;
    delete(id: string): Promise<void>;
    list(filters: PlanFilters): Promise<WorkingPlanSummary[]>;
}
```

#### Shift Repository
```typescript
interface IShiftRepository {
    create(shift: ShiftDocument): Promise<ShiftDocument>;
    findById(id: string): Promise<ShiftDocument>;
    findByWorkingPlanId(workingPlanId: string): Promise<ShiftDocument[]>;
    findByDate(date: string): Promise<ShiftDocument[]>;
    update(id: string, shiftData: Partial<ShiftDocument>): Promise<ShiftDocument>;
    delete(id: string): Promise<void>;
}
```

#### Operator Assignment Repository
```typescript
interface IOperatorAssignmentRepository {
    create(assignment: OperatorAssignmentDocument): Promise<OperatorAssignmentDocument>;
    findById(id: string): Promise<OperatorAssignmentDocument>;
    findByShiftAndActivity(shiftId: string, activityId: string): Promise<OperatorAssignmentDocument>;
    update(id: string, assignmentData: Partial<OperatorAssignmentDocument>): Promise<OperatorAssignmentDocument>;
    delete(id: string): Promise<void>;
}
```

#### Projection Repositories
```typescript
interface ICalendarProjectionRepository {
    upsert(calendar: CalendarProjectionDocument): Promise<CalendarProjectionDocument>;
    findByYearAndWeek(year: number, weekNumber: number): Promise<CalendarProjectionDocument>;
    findByDate(date: string): Promise<CalendarProjectionDocument[]>;
}

interface IOperatorStatusProjectionRepository {
    upsert(operatorStatus: OperatorStatusProjectionDocument): Promise<OperatorStatusProjectionDocument>;
    findById(operatorId: string): Promise<OperatorStatusProjectionDocument>;
    findAvailableOperators(date: string): Promise<OperatorStatusProjectionDocument[]>;
}
```

## 5. API Endpoints

### 5.1 Working Plan Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/workingplans` | GET | Get list of working plans | N/A | 200 - List of plans |
| `/api/v1/workingplans/current` | GET | Get current week's plan | N/A | 200 - Current plan |
| `/api/v1/workingplans/{year}/{week}` | GET | Get plan by year and week | N/A | 200 - Plan details |
| `/api/v1/workingplans` | POST | Create new plan | Year, week number | 201 - Created plan |
| `/api/v1/workingplans/{id}/status` | PUT | Update plan status | New status | 200 - Updated plan |
| `/api/v1/workingplans/{id}` | DELETE | Delete a plan | N/A | 204 - No content |

### 5.2 Shift Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/workingplans/{planId}/shifts` | GET | Get shifts for a plan | N/A | 200 - List of shifts |
| `/api/v1/shifts/{shiftId}` | GET | Get shift details | N/A | 200 - Shift details |
| `/api/v1/shifts/{shiftId}` | PUT | Update shift | Shift details | 200 - Updated shift |

### 5.3 Activity Management

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/shifts/{shiftId}/activities` | GET | Get all activities for a shift | N/A | 200 - List of activities |
| `/api/v1/shifts/{shiftId}/activities` | POST | Add a new activity to a shift | Activity details | 201 - Updated shift with new activity |
| `/api/v1/shifts/{shiftId}/activities/{activityId}` | PUT | Update an existing activity | Updated activity details | 200 - Updated shift |
| `/api/v1/shifts/{shiftId}/activities/{activityId}` | DELETE | Delete an activity | N/A | 200 - Updated shift |

### 5.4 Operator Assignment

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/shifts/{shiftId}/activities/{activityId}/operators` | GET | Get operators assigned to an activity | N/A | 200 - List of operator assignments |
| `/api/v1/shifts/{shiftId}/activities/{activityId}/operators` | POST | Assign operators to an activity | List of operator IDs | 200/207 - Assignment result with available/unavailable operators |
| `/api/v1/operators/available` | GET | Get available operators for a date | date (query param) | 200 - List of available operators |
| `/api/v1/assignments/{assignmentId}/operators/{operatorId}` | DELETE | Remove operator from activity | N/A | 200 - Updated assignment |

### 5.5 Reference Data

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/v1/family-zones` | GET | Get list of family zones | N/A | 200 - List of family zones |
| `/api/v1/family-zones/{id}` | GET | Get family zone details | N/A | 200 - Family zone details |
| `/api/v1/calendars/{year}/{weekNumber}` | GET | Get calendar for specific week | N/A | 200 - Week calendar |

## 6. Event-Driven Architecture

### 6.1 Event Schemas

#### Operator Status Event Schema
```json
{
  "id": "evt-123456",
  "type": "operator.longterm.started",
  "version": "1.0",
  "timestamp": "2025-02-24T10:00:00Z",
  "metadata": {
    "userId": "user123",
    "correlationId": "corr-abc123",
    "source": "operator-service"
  },
  "data": {
    "operatorId": "OP001",
    "name": "John Doe",
    "longTermStatus": {
      "type": "SICK_LEAVE",
      "startDate": "2025-02-24",
      "endDate": "2025-03-15",
      "reason": "Medical leave",
      "isAvailableForSelection": false
    }
  }
}
```

#### Calendar Event Schema
```json
{
  "id": "evt-789012",
  "type": "calendar.week.summary.calculated",
  "version": "1.0",
  "timestamp": "2025-01-15T14:30:00Z",
  "metadata": {
    "userId": "sys-calendar",
    "correlationId": "corr-def456",
    "source": "annual-calendar-service"
  },
  "data": {
    "yearId": 2025,
    "weekNumber": 9,
    "startDate": "2025-02-24",
    "endDate": "2025-03-02",
    "workingDays": ["2025-02-24", "2025-02-25", "2025-02-27", "2025-02-28"],
    "holidays": [
      {
        "date": "2025-02-26",
        "name": "Company Holiday",
        "type": "COMPANY"
      }
    ],
    "weekendDays": ["2025-03-01", "2025-03-02"]
  }
}
```

### 6.2 Event Handling

```typescript
// Calendar Event Types and Handler
enum CalendarEventType {
    YEAR_INITIALIZED = 'calendar.year.initialized',
    HOLIDAY_CREATED = 'calendar.holiday.created',
    HOLIDAY_UPDATED = 'calendar.holiday.updated',
    HOLIDAY_DELETED = 'calendar.holiday.deleted',
    WEEK_SUMMARY_CALCULATED = 'calendar.week.summary.calculated'
}

interface ICalendarEventHandler {
    handleYearInitialized(event: any): Promise<void>;
    handleHolidayCreated(event: any): Promise<void>;
    handleHolidayUpdated(event: any): Promise<void>;
    handleHolidayDeleted(event: any): Promise<void>;
    handleWeekSummaryCalculated(event: any): Promise<void>;
}

// Operator Status Event Types and Handler
enum OperatorStatusEventType {
    OPERATOR_CREATED = 'operator.created',
    OPERATOR_UPDATED = 'operator.updated',
    OPERATOR_STATUS_CHANGED = 'operator.status.changed',
    LONG_TERM_STATUS_STARTED = 'operator.longterm.started',
    LONG_TERM_STATUS_ENDED = 'operator.longterm.ended'
}

interface IOperatorStatusEventHandler {
    handleOperatorCreated(event: any): Promise<void>;
    handleOperatorUpdated(event: any): Promise<void>;
    handleOperatorStatusChanged(event: any): Promise<void>;
    handleLongTermStatusStarted(event: any): Promise<void>;
    handleLongTermStatusEnded(event: any): Promise<void>;
}
```

### 6.3 Subscription Management

```typescript
interface ISubscriptionManager {
    initialize(): Promise<void>;
    subscribeToTopic(topicName: string, subscription: string, messageHandler: (message: any) => Promise<void>): Promise<void>;
    unsubscribeFromTopic(topicName: string, subscription: string): Promise<void>;
    completeMessage(messageId: string, lockToken: string): Promise<void>;
    abandonMessage(messageId: string, lockToken: string): Promise<void>;
    deadLetterMessage(messageId: string, lockToken: string, reason: string): Promise<void>;
}
```

## 7. Business Processes

### 7.1 Create Working Plan

```mermaid
sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant WPS as Working Plan Service
    participant CPS as Calendar Projection Service
    participant CDB as Cosmos DB
    
    Client->>WPMS: POST /api/v1/workingplans
    Note over Client, WPMS: {year: 2025, weekNumber: 9}
    
    WPMS->>WPS: createPlan(2025, 9)
    WPS->>CDB: Check if plan exists
    CDB-->>WPS: Return result (not found)
    
    WPS->>CPS: getCalendarForWeek(2025, 9)
    CPS->>CDB: Query calendar projection
    CDB-->>CPS: Return calendar data
    CPS-->>WPS: Return calendar week
    
    WPS->>WPS: Create WorkingPlan document
    WPS->>WPS: Create days based on calendar
    WPS->>CDB: Save working plan document
    CDB-->>WPS: Confirm save
    
    WPS->>WPS: Generate default shifts
    WPS->>CDB: Save shift documents
    CDB-->>WPS: Confirm save
        
        WPS-->>WPMS: Return completed plan
    WPMS-->>Client: 201 Created with plan details
```

### 7.2 Assign Operators with Status Check

```mermaid
sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant OAS as Operator Assignment Service
    participant OSP as Operator Status Projection
    participant CDB as Cosmos DB
    
    Client->>WPMS: POST /api/v1/shifts/{shiftId}/activities/{activityId}/operators
    Note over Client, WPMS: {operatorIds: ["OP001", "OP002", "OP003"]}
    
    WPMS->>OAS: assignOperators(shiftId, activityId, operatorIds)
    OAS->>WPMS: getShiftById(shiftId)
    WPMS->>CDB: Query shift
    CDB-->>WPMS: Return shift
    
    OAS->>OSP: checkOperatorsAvailability(operatorIds, shift.date)
    OSP->>CDB: Query operator status projections
    CDB-->>OSP: Return operator statuses
    
    Note over OSP: Filter out operators on long-term leave
    OSP-->>OAS: Return available operators
    
    alt Some operators unavailable
        OAS-->>WPMS: Return partial success with unavailable operators
        WPMS-->>Client: 207 Multi-Status with available/unavailable operators
    else All operators available
        OAS->>CDB: Create/Update operator assignment document
        CDB-->>OAS: Confirm save
        OAS-->>WPMS: Return assignment document
        WPMS-->>Client: 200 OK with assignment details
    end
```

### 7.3 Publish Working Plan

```mermaid
sequenceDiagram
    participant Client
    participant WPMS as Working Plan MS
    participant WPS as Working Plan Service
    participant VS as Validation Service
    participant CDB as Cosmos DB
    
    Client->>WPMS: PUT /api/v1/workingplans/WP_2025_09/status
    Note over Client, WPMS: {status: "Published"}
    
    WPMS->>WPS: updatePlanStatus("WP_2025_09", "Published")
    WPS->>CDB: Query plan
    CDB-->>WPS: Return plan
    
    alt Plan not found
        WPS-->>WPMS: Throw EntityNotFoundError
        WPMS-->>Client: HTTP 404 Not Found
    else Plan found
        WPS->>VS: validatePlanForPublication(plan)
        VS->>CDB: Query related entities
        CDB-->>VS: Return entities
        VS->>VS: Perform validation rules
        
        alt Validation failed
            VS-->>WPMS: Return validation errors
            WPMS-->>Client: HTTP 422 Unprocessable Entity
        else Validation passed
            WPS->>CDB: Update plan status
            CDB-->>WPS: Confirmation
            
            WPS-->>WPMS: Return updated plan
            WPMS-->>Client: HTTP 200 OK with updated plan
        end
    end
```

## 8. Security & Authorization

### 8.1 Role Definitions

```typescript
enum Role {
    ADMIN = "ADMIN",
    PLANNER = "PLANNER",
    TEAM_LEADER = "TEAM_LEADER",
    SHIFT_MANAGER = "SHIFT_MANAGER",
    VIEWER = "VIEWER"
}

const rolePermissions = {
    [Role.ADMIN]: [
        "create:workingplan", "update:workingplan", "delete:workingplan", 
        "read:workingplan", "manage:familyzones"
    ],
    [Role.PLANNER]: [
        "create:workingplan", "update:workingplan", 
        "read:workingplan", "manage:activity", "manage:shifts"
    ],
    [Role.TEAM_LEADER]: [
        "read:workingplan", "manage:operators", "update:activity"
    ],
    [Role.SHIFT_MANAGER]: [
        "read:workingplan", "publish:workingplan", "approve:changes"
    ],
    [Role.VIEWER]: ["read:workingplan"]
};
```

### 8.2 Authorization Middleware

```typescript
function authorizeEndpoint(requiredPermissions: string[]) {
    return (req, res, next) => {
        const userPermissions = getUserPermissions(req.user);
        const hasPermission = requiredPermissions.every(perm => 
            userPermissions.includes(perm)
        );
        
        if (!hasPermission) {
            return res.status(403).json({
                error: "Forbidden",
                message: "Insufficient permissions"
            });
        }
        
        next();
    };
}
```

## 9. Performance Considerations

### 9.1 Cosmos DB Optimization

```typescript
// Cosmos DB container configuration
const containersConfig = [
    {
        id: 'workingplans',
        partitionKey: { paths: ['/partitionKey'] },
        indexingPolicy: {
            indexingMode: 'consistent',
            includedPaths: [
                { path: '/type/?' },
                { path: '/year/?' },
                { path: '/weekNumber/?' },
                { path: '/status/?' }
            ],
            excludedPaths: [{ path: '/*' }]
        }
    },
    {
        id: 'shifts',
        partitionKey: { paths: ['/partitionKey'] },
        indexingPolicy: {
            indexingMode: 'consistent',
            includedPaths: [
                { path: '/type/?' },
                { path: '/workingPlanId/?' },
                { path: '/date/?' },
                { path: '/shiftType/?' }
            ],
            excludedPaths: [{ path: '/*' }]
        }
    },
    {
        id: 'operatorAssignments',
        partitionKey: { paths: ['/partitionKey'] },
        indexingPolicy: {
            indexingMode: 'consistent',
            includedPaths: [
                { path: '/type/?' },
                { path: '/workingPlanId/?' },
                { path: '/shiftId/?' },
                { path: '/activityId/?' },
                { path: '/operators[]/operatorId/?' }
            ],
            excludedPaths: [{ path: '/*' }]
        }
    },
    {
        id: 'projections',
        partitionKey: { paths: ['/partitionKey'] },
        indexingPolicy: {
            indexingMode: 'consistent',
            includedPaths: [
                { path: '/type/?' },
                { path: '/year/?' },
                { path: '/weekNumber/?' },
                { path: '/operatorId/?' },
                { path: '/currentStatus/?' },
                { path: '/longTermStatus/startDate/?' },
                { path: '/longTermStatus/endDate/?' }
            ],
            excludedPaths: [{ path: '/*' }]
        }
    }
];
```

### 9.2 Caching Strategy

```typescript
// Cache configuration
const cacheConfig = {
    // Redis cache configuration
    options: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT),
        password: process.env.REDIS_PASSWORD,
        tls: process.env.REDIS_TLS === 'true'
    },
    // TTL values in seconds
    ttl: {
        workingPlan: 3600,        // 1 hour
        shifts: 3600,             // 1 hour
        calendarProjection: 86400, // 24 hours
        operatorProjection: 300    // 5 minutes (more frequent updates)
    }
};
```

### 9.3 Query Optimization

Key strategies for optimizing Cosmos DB query performance:

1. **Partition Key Design**
   - Working plans partitioned by year
   - Shifts and assignments partitioned by working plan ID
   - Projections partitioned by data type

2. **Indexing Strategies**
   - Selective indexing of frequently queried properties
   - Exclusion of rarely queried properties
   - Optimized composite indexes for common query patterns

3. **Query Patterns**
   - Use of point reads where possible
   - Minimized cross-partition queries
   - Pagination for large result sets
   - Projection queries to reduce document size

4. **Document Structure**
   - Embedded sub-documents for related entities
   - Denormalization for frequently accessed data
   - Optimized document size to reduce RU consumption
   - Strategic use of TTL for transient data