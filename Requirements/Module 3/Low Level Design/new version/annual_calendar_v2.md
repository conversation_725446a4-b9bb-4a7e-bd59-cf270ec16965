# Annual Calendar Microservice - LLD

## Overview
This microservice manages the annual calendar data for APTIV including holidays, overtime events, and working day statistics. The design uses an event-sourcing approach with Cosmos DB as the primary data store.

## Architecture
The system utilizes Azure Cosmos DB for event storage with a Change Feed mechanism to propagate changes to downstream SQL databases. This design ensures separation of concerns between microservices while maintaining data consistency.

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Calendar API    │────▶│ Cosmos DB       │────▶│ Change Feed     │
│                 │     │ (Event Store)   │     │ Processor       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                                                │
        │                                                ▼
┌───────┴───────┐       ┌─────────────────┐     ┌───────┴───────┐
│ Excel Import  │       │ Production Plan │     │ Calendar SQL  │
│ Service       │       │ SQL Database    │◀────│ Database      │
└───────────────┘       └─────────────────┘     └───────────────┘
```

## Data Models

### CalendarEvents
Stored in Cosmos DB, this contains individual calendar events (holidays, overtime, etc.)

```json
{
  "id": "holiday-2025-12-25-DE-NUREMBERG",
  "type": "HOLIDAY",
  "eventDate": "2025-12-25",
  "name": "Christmas Day",
  "description": "National holiday",
  "category": "NATIONAL",
  "color": "#FF0000",
  "isWorkingDay": false,
  "isRecurring": true,
  "recurringType": "YEARLY",
  "recurringMonth": 12,
  "recurringDay": 25,
  "hours": null,
  "department": null,
  "country": "DE",
  "site": "NUREMBERG",
  "createdBy": "john.doe",
  "createdAt": "2024-11-15T10:30:00Z",
  "updatedAt": "2024-11-15T10:30:00Z"
}
```

### YearSummary Container
Cosmos DB container holding yearly summaries with embedded monthly data. Each document represents a complete year summary for a specific country and site.

```json
{
  "id": "2025-DE-NUREMBERG",
  "year": 2025,
  "country": "DE",
  "site": "NUREMBERG",
  "status": "ACTIVE",
  "totalDays": 365,
  "sundays": 52,
  "saturdays": 52,
  "nationalHolidays": 9,
  "religiousHolidays": 4,
  "unpaidPublicHolidays": 3,
  "vacationDays": 18,
  "tlodays": 0,
  "fourthSaturdays": 12,
  "totalPlantHolidays": 0,
  "workedDaysPlant": 268,
  "lastCalculated": "2024-11-15T10:30:00Z",
  "createdBy": "john.doe",
  "createdAt": "2024-11-01T08:00:00Z",
  "updatedAt": "2024-11-15T10:30:00Z",
  "monthSummaries": [
    {
      "month": 1,
      "name": "January",
      "totalDays": 31,
      "sundays": 4,
      "saturdays": 4,
      "nationalHolidays": 1,
      "religiousHolidays": 0,
      "unpaidPublicHolidays": 0,
      "vacationDays": 0,
      "tlodays": 0,
      "fourthSaturdays": 1,
      "totalPlantHolidays": 0,
      "workedDaysPlant": 24
    },
    {
      "month": 2,
      "name": "February",
      "totalDays": 28,
      "sundays": 4,
      "saturdays": 4,
      "nationalHolidays": 0,
      "religiousHolidays": 0,
      "unpaidPublicHolidays": 0,
      "vacationDays": 0,
      "tlodays": 0,
      "fourthSaturdays": 1,
      "totalPlantHolidays": 0,
      "workedDaysPlant": 20
    }
    // Additional months...
  ]
}
```

## SQL Tables
While the primary data is stored in Cosmos DB, we maintain SQL replicas for reporting and integration:

```sql
CREATE TABLE CalendarEvents (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    Type VARCHAR(20) NOT NULL,
    EventDate DATE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(200) NULL,
    Category VARCHAR(30) NOT NULL,
    Color VARCHAR(20) NOT NULL,
    IsWorkingDay BIT NOT NULL,
    IsRecurring BIT NOT NULL,
    RecurringType VARCHAR(20) NULL,
    RecurringMonth TINYINT NULL,
    RecurringDay TINYINT NULL,
    Hours DECIMAL(5,2) NULL,
    Department VARCHAR(100) NULL,
    Country VARCHAR(2) NOT NULL,
    Site VARCHAR(50) NOT NULL,
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    EventId VARCHAR(36) NOT NULL,
    
    INDEX idx_event_date (EventDate),
    INDEX idx_country_site (Country, Site)
);
```

## Event Types

| Event Type | Description | Key Properties |
|------------|-------------|----------------|
| `HolidayCreated` | New holiday added | id, date, name, category, country, site |
| `HolidayUpdated` | Holiday modified | id, date, name, category, country, site |
| `HolidayDeleted` | Holiday removed | id |
| `OvertimeCreated` | New overtime added | id, date, hours, department, country, site |
| `YearInitialized` | Year configuration created | year, country, site, status |
| `YearStatusChanged` | Year status updated | year, country, site, oldStatus, newStatus |
| `YearSummaryCalculated` | Summary statistics updated | year, country, site, summaryData |
| `CalendarImported` | Bulk import completed | source, fileName, importedEvents |

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/calendar/years/{year}/countries/{country}/sites/{site}` | GET | Get year summary data |
| `/api/calendar/years/{year}/countries/{country}/sites/{site}` | POST | Initialize or update year |
| `/api/calendar/years/{year}/countries/{country}/sites/{site}/status` | PUT | Update year status |
| `/api/calendar/events` | GET | Get calendar events |
| `/api/calendar/events/holidays` | POST | Create holiday |
| `/api/calendar/events/holidays/{id}` | PUT | Update holiday |
| `/api/calendar/events/holidays/{id}` | DELETE | Delete holiday |
| `/api/calendar/events/overtime` | POST | Create overtime |
| `/api/calendar/import` | POST | Import calendar from Excel |

## Excel Import Feature

The microservice supports bulk import of calendar data through Excel files. The system validates the data, creates appropriate events, and updates the year summary automatically.

### Import Template
The Excel template includes these columns:
- Date
- Name
- Category
- Is Working Day
- Country
- Site
- Color
- Description

### Import Process Flow
1. User uploads Excel file
2. System validates file format and content
3. Valid entries are converted to events and stored
4. Year summary is recalculated
5. Import result with success/failure details is returned

## Frontend Calendar Libraries

Several React libraries are suitable for displaying the calendar data:

1. **FullCalendar**
   - Comprehensive solution with multiple views
   - Extensive customization options
   - Good documentation and community support

2. **React Big Calendar**
   - Flexible and intuitive
   - Easy integration with Redux
   - Customizable event rendering

3. **Ant Design Calendar**
   - Clean UI with material design principles
   - Part of a larger component ecosystem
   - Good for simple implementations

4. **DHTMLX Scheduler**
   - Enterprise-grade solution
   - Timeline and resource views
   - Advanced scheduling capabilities

## Security Considerations

- Role-based access control for API endpoints
- Country and site-based data segmentation
- Input validation for all commands
- Special validation for Excel imports
- Encryption for data at rest and in transit

## Performance Optimizations

- Cosmos DB indexing for common query patterns
- Caching for frequently accessed year summaries
- Batched processing in Change Feed
- Optimized bulk import with background processing
- Pre-calculation of summary statistics