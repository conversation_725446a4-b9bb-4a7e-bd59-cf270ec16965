# HeadCount Microservice Low-Level Design

## 1. Introduction

This Low-Level Design (LLD) document outlines the architecture and implementation details for a multi-tenant status management system designed for manufacturing environments. The system addresses the complex requirements of tracking operator availability across multiple production facilities while supporting various employee categories, shift patterns, and country-specific workflows.

The design employs a CQRS (Command Query Responsibility Segregation) architecture with event sourcing to ensure scalability, flexibility, and accurate historical tracking. It provides robust mechanisms for status changes, approval workflows, and integration with external systems while maintaining clear separation between different tenants in the database.

## 2. Core Data Model

### 2.1 Primary Entities

```
Operator {
    id: string                  
    partitionKey: string        // Format: "country_teamId"
    badgeId: string
    firstName: string
    lastName: string
    teamId: string
    teamLeaderId: string        
    department: string
    category: string            // "DH", "IS", "IH"
    isActive: boolean
    country: string             
}

LongTermStatus {
    id: string
    partitionKey: string        // Format: "country_operatorId"
    operatorId: string
    type: LongTermStatusType
    startDate: date
    endDate: date
    reason: string
    approvedBy: string
    approvalRequestId: string   
    createdAt: datetime
    updatedAt: datetime
    metadata: object
    country: string             
}

ShiftDocument {
    id: string //partitionKey
    date: date
    shiftCode: string           // "M" (Morning), "E" (Evening), "N" (Night)
    departmentId: string
    teamId: string
    workingPlanId: string       // Reference to source working plan
    operators: [
        {
            operatorlegacysiteid: string
            fullName: string     // Added operator's full name for easier UI display
            status: ShiftStatusType  // Actual status for the shift
            plannedStatus: string    // Pre-assigned status from working plan
            recordedBy: string   // Who recorded this status
            recordedAt: datetime // When the status was recorded
        }
    ]
    shiftStarted: boolean
    shiftStartTime: datetime
    shiftClosed: boolean
    shiftCloseTime: datetime
    validated: boolean
    validatedBy: string
    validatedAt: datetime
    country: string
    updatedAt: datetime
}
```

## 3. Status Types and Triggering Microservices

| Status Code | Status Name | Type | Description | Triggering Microservice/Process | Action/Event | Approval Workflow | Applicable Categories |
|-------------|-------------|------|-------------|--------------------------------|--------------|-------------------|----------------------|
| P | Present | Shift | employee is present for shift | Headount | Visual check / Badge scan | TL → SL → TKS | All |
| A | Absent | Shift | employee is absent without justification | Headount | Visual check by responsible person | TL → SL → TKS / Clerk → Dept Manager → TKS | All |
| R | Delayed | Shift | employee is late with approved entry authorization | Request | "Autorisation d'entrée" request (REQ 01) | Admin hours: TL → SL → Admin Coordinator / Non-admin: TL → SL | DH, IH, IS |
| AB | Absent | Shift | Delayed but didn't come to work | Headount | Visual check | TL → SL → TKS / Clerk → Dept Manager → TKS | All |
| T | Temporary Authorization | employee | Temporary status pending evidence | Request | Absence Authorization Request (REQ 01) | TL/Requester → N+1 → TKS | All |
| TL | Legal Authorization | employee | Legal authorization (e.g., Breastfeeding) | Request | Legal authorization request with reason "BREASTFEEDING" | TL → TKS | DH |
| CR | Leave Holiday | employee | Planned vacation/leave | Request | Leave Request (REQ 07) | Approval workflow in FDS Module 1 | All |
| DI | Day Travel Order | employee | Travel during day shift | Request | Travel Order Request (REQ 03) | Approval workflow in FDS Module 1 | All |
| DN | Night Travel Order | employee | Travel during night shift | Request | Travel Order Request (REQ 03) | Approval workflow in FDS Module 1 | All |
| TE | External Work | employee | Working outside regular location | Request | External Work Request (REQ 02) | Approval workflow in FDS Module 1 | All |
| CTP | Planned TLO | employee | Planned Technical Layoff | Working Plan | Assignment by Shift Leader based on Capacity Study | SL based on work plan | DH, IS, IH |
| CTN | Unplanned TLO | employee | Unplanned Technical Layoff | Request | Unplanned TLO Request | TL → SL → Coordinator → Manager | All |
| AT | Workplace Accident | employee | Injury at workplace | Request | Work accident request by nurse | Nurse → TL/N+1/Clerk → TKS | All |
| MT | Maternity Leave | employee | Maternity leave | Request | Maternity Leave request using "Annual Leave" template (REQ 07) | TL → SL → TKS | All |
| AP | Suspension | employee | Disciplinary suspension | Request | Suspension request by Employee Relations Supervisor | Employee Relations → TL/N+1 → TKS | All |
| AE | Exceptional Leave | employee | Special circumstances (e.g., COVID) | Request | Exceptional leave request | TL → SL → HR Manager → TKS | All |

## 4. Visual Check Responsibility Matrix

| Employee Category | Visual Check Responsibility | Validation Path | Timing |
|-------------------|----------------------------|----------------|--------|
| DH Operators | Team Leader | TL → SL → TKS | During shift |
| IS/IH Employees | Department Clerk | Clerk → Department Manager → TKS | D+1 |
| Team Leaders | Shift Leader | SL → TKS | During shift |
| Shift Leader Backup Structure | Shift Leader | SL → TKS | During shift |
| Backup Structure in Replacement Process | Team Leader | TL → SL → TKS | During shift |
| Team Leader Backups (not in replacement) | Shift Leader | SL → TKS | During shift |
| Shift Leader Backups (not in replacement) | Shift Leader | SL → TKS | During shift |
| Containment Operators | Team Leader + Quality Supervisor | TL → SL → TKS | During shift |

## 5. Time-Based Rules and Constraints

| Rule Type | Description | Timing | Action on Violation |
|-----------|-------------|--------|---------------------|
| Status Modification Window | Team Leaders can modify operator status | Until D+1 at 7:00 AM for night shift | Submit Correction of Clocking request |
| Shift Start Alert | Alert if Team Leader doesn't click "Start Shift" | Before end of shift | Notification to Shift Leader |
| Dashboard Update | Show Team Leaders who clicked/didn't click Start/Close Shift | Real-time | Visible to Shift Leader |
| Prolonged Absence | Alert for absence without justification | After 4 consecutive days | Notification to HR (potential job abandonment) |
| Status Closure | Team Leader must close all operator statuses | End of shift | Popup warning if statuses left empty |

## 6. CQRS Architecture for Status Management System

### 6.1 Core Components

#### 6.1.1 Command Side
- **Commands**: 
  - `StartShiftCommand` - Initiated by Team Leader
  - `RecordVisualCheckCommand` - Used for visual checks by TL/Clerk/SL
  - `CloseShiftCommand` - Initiated by Team Leader at end of shift
  - `RequestStatusChangeCommand` - For all request-based status changes
  - `ValidateShiftRecordsCommand` - For SL/Department Manager validation
  - `CorrectClockingCommand` - For corrections after deadline
  - `ScheduleStatusChangeCommand` - For future-dated status changes

- **Command Handlers**: 
  - Process business logic and validation rules
  - Apply category-specific approval workflows
  - Generate appropriate events

- **Domain Models**:
  - `Operator` - Core entity for DH category employees
  - `Employee` - Core entity for IS/IH category employees
  - `Team` - Group of operators under a Team Leader
  - `Shift` - Represents a work shift (Morning, Evening, Night)
  - `ShiftDocument` - Represents shift plan imported from Working Plan system

#### 6.1.2 Event Store Containers

1. **StatusChangeEvents**
   - PartitionKey: OperatorId/EmployeeId
   - Properties:
     - EventId (GUID)
     - EventType (string) - "ShiftStarted", "VisualCheckRecorded", "StatusChangeRequested", etc.
     - OperatorId/EmployeeId (string)
     - Category (string) - "DH", "IS", "IH"
     - OldStatus (string)
     - NewStatus (string)
     - StatusType (string) - "Shift" or "Operator"
     - EffectiveDate (datetime)
     - ExpiryDate (datetime, nullable)
     - Reason (string)
     - RequestId (string, nullable)
     - RequestType (string, nullable) - "REQ01", "REQ02", etc.
     - SourceMicroservice (string)
     - Timestamp (datetime)
     - Actor (string) - who initiated the change
     - ActorRole (string) - "TeamLeader", "ShiftLeader", "Clerk", "Nurse", etc.
     - ApprovalState (string) - "Initiated", "Approved", "Forwarded", "Completed"
     - ApprovalStep (int)
     - NextApprover (string, nullable)

2. **ScheduledStatusEvents**
   - PartitionKey: EffectiveDate
   - Properties:
     - EventId (GUID)
     - OperatorId/EmployeeId (string)
     - Category (string) - "DH", "IS", "IH"
     - StatusCode (string)
     - EffectiveDate (datetime)
     - ExpiryDate (datetime, nullable)
     - Reason (string)
     - RequestId (string, nullable)
     - RequestType (string, nullable)
     - CreatedBy (string)
     - CreatedTimestamp (datetime)
     - ApprovalState (string)
     - ApprovalWorkflow (string) - JSON representation of approval steps

3. **ApprovalEvents (triggered b the workflow microservice)**
   - PartitionKey: RequestId
   - Properties:
     - EventId (GUID)
     - RequestId (string)
     - ApprovalStep (int)
     - ApproverRole (string)
     - ApproverId (string)
     - Decision (string) - "Approved", "Rejected"
     - Comments (string)
     - Timestamp (datetime)

4. **ShiftDocumentEvents**
   - PartitionKey: ShiftDate
   - Properties:
     - EventId (GUID)
     - EventType (string) - "ShiftDocumentImported", "ShiftDocumentUpdated"
     - ShiftDocumentId (string)
     - ShiftDate (date)
     - ShiftCode (string)
     - DepartmentId (string)
     - TeamId (string)
     - WorkingPlanId (string)
     - OperatorStatusUpdates (array) - Operator status changes
     - ImportedBy (string)
     - ImportTimestamp (datetime)
     - Version (int)

#### 6.1.3 Read Model Containers

1. **ShiftDocumentView**
   - PartitionKey: ShiftDate
   - Properties:
     - DocumentId (string)
     - ShiftDate (date)
     - ShiftCode (string)
     - DepartmentId (string)
     - TeamId (string)
     - WorkingPlanId (string)
     - ExpectedOperators (array)
     - StatusBreakdown (JSON object)
     - ImportedAt (datetime)
     - LastUpdatedAt (datetime)
     - IsActive (boolean)
     - Version (int)

### 6.2 System Architecture Diagram

```mermaid
flowchart TD
    subgraph UI[User Interfaces]
        TLInterface[Team Leader Interface]
        SLInterface[Shift Leader Interface]
        ClerkInterface[Department Clerk Interface]
        TKSInterface[TKS Interface]
        NurseInterface[Nurse Interface]
        HRInterface[HR Interface]
    end
    
    subgraph CommandSide[Command Side]
        API[Status Management API]
        CommandHandlers[Command Handlers]
        ValidationService[Validation Service]
        DomainModels[Domain Models]
        WorkflowEngine[Approval Workflow Engine]
    end
    
    subgraph Integration[Integration Layer]
        MessageBus[Message Bus]
        IntegrationEvents[Integration Events]
        NotificationService[Notification Service]
    end
    
    subgraph EventStore[Event Store]
        StatusChangeEvents[(StatusChangeEvents)]
        ScheduledStatusEvents[(ScheduledStatusEvents)]
        ApprovalEvents[(ApprovalEvents)]
        ShiftDocumentEvents[(ShiftDocumentEvents)]
    end
    
    subgraph ReadModels[Read Models]
        OperatorStatus[(OperatorStatus)]
        ShiftSession[(ShiftSession)]
        ShiftRecords[(ShiftRecords)]
        ShiftLeaderDashboard[(ShiftLeaderDashboard)]
        StatusRequests[(StatusRequests)]
        NotificationQueue[(NotificationQueue)]
        ShiftDocumentView[(ShiftDocumentView)]
    end
    
    subgraph Functions[Azure Functions]
        ChangeFeedProcessors[Change Feed Processors]
        ScheduledActivation[Scheduled Status Activation]
        ExpirationProcessor[Status Expiration Processor]
        AlertMonitor[Alert & Notification Monitor]
        ReportGenerator[Report Generator]
        ShiftImporter[Shift Document Importer]
    end
    
    subgraph ExternalMS[External Microservices]
        RequestMS[Request MS]
        WorkingPlanMS[Working Plan MS]
        ContainmentMS[Containment MS]
        OptitimeIntegration[Optitime Integration]
        WorkdayIntegration[Workday Integration]
        CWIntegration[CW Integration]
    end
    
    TLInterface --> API
    SLInterface --> API
    ClerkInterface --> API
    NurseInterface --> API
    HRInterface --> API
    TKSInterface --> API
    
    API --> CommandHandlers
    CommandHandlers --> ValidationService
    CommandHandlers --> WorkflowEngine
    CommandHandlers --> DomainModels
    DomainModels --> StatusChangeEvents
    DomainModels --> ScheduledStatusEvents
    DomainModels --> ShiftDocumentEvents
    WorkflowEngine --> ApprovalEvents
    
    StatusChangeEvents --> ChangeFeedProcessors
    ScheduledStatusEvents --> ChangeFeedProcessors
    ApprovalEvents --> ChangeFeedProcessors
    ShiftDocumentEvents --> ChangeFeedProcessors
    ChangeFeedProcessors --> OperatorStatus
    ChangeFeedProcessors --> ShiftSession
    ChangeFeedProcessors --> ShiftRecords
    ChangeFeedProcessors --> ShiftLeaderDashboard
    ChangeFeedProcessors --> StatusRequests
    ChangeFeedProcessors --> NotificationQueue
    ChangeFeedProcessors --> ShiftDocumentView
    
    ScheduledActivation --> ScheduledStatusEvents
    ScheduledActivation --> StatusChangeEvents
    ExpirationProcessor --> OperatorStatus
    ExpirationProcessor --> StatusChangeEvents
    AlertMonitor --> ShiftSession
    AlertMonitor --> ShiftRecords
    AlertMonitor --> NotificationQueue
    ReportGenerator --> OperatorStatus
    ReportGenerator --> ShiftRecords
    ShiftImporter --> ShiftDocumentEvents
    
    RequestMS --> MessageBus
    WorkingPlanMS --> MessageBus
    WorkingPlanMS --> ShiftImporter
    ContainmentMS --> MessageBus
    MessageBus --> IntegrationEvents
    IntegrationEvents --> CommandHandlers
    
    CommandHandlers --> NotificationService
    WorkflowEngine --> NotificationService
    NotificationService --> NotificationQueue
    
    CommandHandlers --> OptitimeIntegration
    CommandHandlers --> WorkdayIntegration
    CommandHandlers --> CWIntegration
    
    OperatorStatus --> TLInterface
    ShiftSession --> TLInterface
    ShiftRecords --> TLInterface
    ShiftLeaderDashboard --> SLInterface
    StatusRequests --> TLInterface
    NotificationQueue --> TLInterface
    NotificationQueue --> SLInterface
    ShiftDocumentView --> SLInterface
    ShiftDocumentView --> TLInterface
    
    ShiftRecords --> ClerkInterface
    StatusRequests --> ClerkInterface
    NotificationQueue --> ClerkInterface
    
    StatusRequests --> TKSInterface
    NotificationQueue --> TKSInterface
    
    StatusRequests --> NurseInterface
    NotificationQueue --> NurseInterface
    
    StatusRequests --> HRInterface
    NotificationQueue --> HRInterface
```

## 7. Core System Workflows

### 7.1 Headount Workflows

#### 7.1.1 Start Shift Process
1. Team Leader clicks "Start Shift" button
2. System:
   - Creates `ShiftStarted` event
   - Creates a new ShiftSession record
   - Initializes ShiftRecords for all operators in team
   - Pre-loads any effective operator statuses
   - Imports ShiftDocument from Working Plan if available
   - Updates ShiftLeaderDashboard
3. If Team Leader doesn't click "Start Shift":
   - System automatically checks 1 hour after shift start time
   - Sends alert to Shift Leader
   - Adds notification to ShiftLeaderDashboard

#### 7.1.2 Visual Check Process
**For DH Category (Team Leader):**
1. Team Leader sees list of operators including:
   - Regular team members
   - Containment operators (grayed out)
   - Backup structure operators (if replacement process completed)
   - Pre-filled statuses from ShiftDocument if available
2. For each operator, Team Leader:
   - Verifies physical presence
   - Confirms or updates pre-filled status
   - System creates `VisualCheckRecorded` event
   - Updates ShiftRecords and OperatorStatus

**For IS/IH Category (Department Clerk):**
1. Department Clerk performs visual check on D+1
2. System follows same process but with different approval workflow
3. Final approval comes from Department Manager

**For Team Leaders (by Shift Leader):**
1. Shift Leader performs visual check for Team Leaders
2. Shift Leader also checks backup structure operators not in replacement process

#### 7.1.3 Close Shift Process
1. Team Leader clicks "Close My Shift"
2. System:
   - Validates all operators have status assigned
   - If incomplete, shows warning popup
   - Creates `ShiftClosed` event
   - Updates ShiftSession and ShiftLeaderDashboard
   - Sends notification to Shift Leader
3. Shift Leader validation process:
   - Reviews all team statuses
   - Can override/correct if needed
   - Approves with `ValidateShiftRecords` command
   - System creates `ShiftRecordsValidated` event
   - Sends notification to TKS
4. For IS/IH categories (Department Clerk):
   - Clerk closes on D+1
   - Sends notification to Department Manager
   - Department Manager validates
   - System forwards to TKS

### 7.2 ShiftDocument Integration with Working Plan

1. **Automatic Import Process**:
   - Working Plan microservice publishes ShiftDocument event
   - ShiftImporter function triggered to process incoming document
   - Creates `ShiftDocumentImported` event
   - Updates ShiftDocumentView read model
   - Flags any pre-assigned statuses (e.g., TLO, planned absences)

2. **Manual Import Process**:
   - Shift Leader can manually trigger import via UI
   - Validates against current shift data
   - Resolves conflicts with current operator statuses
   - Creates `ShiftDocumentUpdated` event

3. **Status Pre-loading**:
   - During shift start, system consults ShiftDocument
   - Pre-fills operator statuses based on planned data
   - Team Leader confirms or overrides during visual check
   - Differences between planned and actual are recorded for reporting

4. **Planned TLO (CTP) Processing**:
   - Working Plan includes TLO assignments from Capacity Study
   - System extracts TLO-assigned operators
   - Pre-fills status as "CTP" for those operators
   - Team Leader confirms during visual check
   - Statistics on planned vs. actual TLO usage tracked

### 7.3 Status Change Request flow

#### 7.3.1 Absence Authorization (T → TL) Workflow
1. Team Leader/Requester initiates absence authorization request (REQ 01)
   ```json
   {
     "requestId": "REQ01-123",
     "requestType": "REQ01",
     "operatorId": "OP456",
     "statusCode": "T",
     "reason": "Medical Appointment",
     "proofType": "Medical Certificate",
     "effectiveFrom": "2025-05-01T08:00:00Z",
     "effectiveTo": "2025-05-01T16:00:00Z",
     "requester": "TL789"
   }
   ```
2. System:
   - Creates `StatusChangeRequested` event
   - Adds to approval workflow based on:
     - Employee category (DH/IS/IH)
     - Time (administrative vs. non-administrative hours)
   - Updates StatusRequests read model

3. Approval Process:
   - For DH categories during administrative hours:
     - Team Leader → Shift Leader → Admin Coordinator
   - For DH categories during non-administrative hours:
     - Team Leader → Shift Leader
   - For IH categories during administrative hours:
     - Requester → N+1 → Admin Coordinator
   - For IH categories during non-administrative hours:
     - Requester → N+1
   - For IS categories:
     - Follow approval matrix in Module 1

4. After final approval:
   - System creates `StatusApproved` event
   - Updates operator status to "TL"
   - Sends notification to TKS
   - TKS validates proof (scanned at kiosk)
   - Status change reflected in Optitime system

5. Breastfeeding-specific process:
   - Request with reason "BREASTFEEDING"
   - Declaration of honor scanned at kiosk
   - TKS approval
   - Status updated to "TL"
   - For IS/IH category, notification sent to N+1
   - Status updated in clerk interface

#### 7.3.2 Leave Holiday (CR) flow
1. Team Leader initiates Leave Request (REQ 07)
2. System:
   - Creates `LeaveRequested` event
   - Follows approval flow from FDS Module 1
   - For IH category, requester raises request directly
3. Upon approval:
   - Creates `LeaveApproved` event
   - Updates OperatorStatus
   - If future-dated, adds to ScheduledStatusEvents
   - Updates connected to Workday for balance verification (M-1)

#### 7.3.3 Travel Orders (DI/DN) flow
1. Team Leader/Requester initiates Travel Order request (REQ 03)
2. System:
   - Creates `TravelOrderRequested` event
   - Follows approval workflow from SFD Module 1
3. Upon approval:
   - Creates `TravelOrderApproved` event
   - Updates status to "DI" (day) or "DN" (night)
   - Sends to TKS for validation

#### 7.3.4 External Work (TE) flow
1. Team Leader/Requester initiates External Work request (REQ 02)
2. System:
   - Creates `ExternalWorkRequested` event
   - Follows approval workflow from SFD Module 1
3. Upon approval:
   - Creates `ExternalWorkApproved` event
   - Updates status to "TE"
   - Sends to TKS for validation

#### 7.3.5 Unplanned TLO (CTN) flow
1. Team Leader initiates Unplanned TLO request
2. System:
   - Creates `UnplannedTLORequested` event
   - Captures reason (raw material shortage, technical issue)
3. Complex approval workflow:
   - Team Leader → Shift Leader → Coordinator → Manager
4. Upon approval:
   - Creates `UnplannedTLOApproved` event
   - Updates status to "CTN"
   - Notifies TKS agent for validation
   - Updates Optitime system

#### 7.3.6 Workplace Accident (AT) flow
1. Nurse submits workplace accident request
   - Includes operator ID, start date, end date
   - Option to extend absence period
   - Scans medical proof
2. System:
   - Creates `WorkplaceAccidentReported` event
   - For DH: Notifies TL, SL, TKS, Social Services, Safety responsible
   - For IH/IS: Notifies N+1, Clerk, TKS, Social Services, Safety responsible
3. Status automatically updated to "AT"
4. Extensions can be initiated by nurse, TL, N+1, clerk, or HR

#### 7.3.7 Maternity Leave (MT) Workflow
1. Team Leader submits request using "Annual Leave" template (REQ 07)
   - Specifies reason as "Maternity"
2. System:
   - Creates `MaternityLeaveRequested` event
   - For DH: Team Leader manages operators
   - For other employees: Department Clerk manages
3. Follows country-specific approval workflow
4. Upon approval:
   - Creates `MaternityLeaveApproved` event
   - TKS updates system with leave start date
   - Updates status to "MT"
   - Notifies relevant stakeholders

#### 7.3.8 Suspension (AP) flow
1. Employee Relations Supervisor submits suspension request
2. System:
   - Creates `SuspensionRequested` event
   - Automatically notifies TL, TKS agent, and Shift Leader
3. Status automatically updated to "AP"
4. No further approval needed (pre-authorized)

#### 7.3.9 Exceptional Leave (AE) flow
1. Team Leader submits exceptional leave request (e.g., COVID)
2. Approval workflow:
   - Shift Leader → HR Manager
3. System:
   - Creates `ExceptionalLeaveApproved` event upon final approval
   - Notifies TKS Agent
   - Updates status to "AE"

### 7.4 Integration with Working Plan for Planned TLO (CTP)

1. Capacity Study process:
   - Working Plan microservice conducts Capacity Study
   - Develops Production Plan with TLO requirements
   - Shares work plan with Shift Leaders

2. TLO Assignment process:
   - Shift Leader assigns operators to TLOs for N+1 period
   - Assignments recorded in Working Plan system
   - Exported to HeadCount as ShiftDocument
   - For DH: Shift Leader manages operators
   - For IS/IH: Department clerk handles planning
   - Follows work plan update rules

3. Status Update process:
   - ShiftDocument imported at shift start
   - Operators with TLO assignments pre-marked as "CTP"
   - Team Leader can modify during shift if needed
   - Changes synchronized with Optitime system

### 7.5 Special Case flow

#### 7.5.1 Badge Loss/Forgetting Process
1. If badge forgotten/lost:
   - Operator manually enters ID at bus terminal
   - If not scheduled on bus, access denied
   - Automatic email sent to guardhouse
   - Operator reports to guardhouse on arrival

2. For badge loss:
   - Team Leader raises Declaration of Loss of Badge request (REQ 08)
   - Includes sworn statement from operator
   - Information sent to TKS Responsible

#### 7.5.2 Delay Handling Process
1. If operator delayed due to transport/personal issue:
   - Status updated to "Absent" not "Delayed"
   - Updated status reflected in CW

2. "Delayed" status only applicable with approved entry authorization:
   - Triggered when operator exceeds 1 min for starting shift
   - Team Leader initiates "autorisation d'entrée" request
   - Once approved, guardhouse notified to grant access
   - Status updated to "R"

#### 7.5.3 Prolonged Absence Alert Process
1. System monitors consecutive absence days
2. If operator absent without justification for > 4 days:
   - Alert sent to HR department
   - Flagged as potential job abandonment
   - Country-specific legal regulations applied

#### 7.5.4 Status Correction Process
1. Team Leader can modify operator status until deadline:
   - D+1 at 7:00 AM for night shift
2. If deadline passed:
   - Team Leader submits Correction of Clocking request
   - Scans absence justification
   - Requires TKS approval
   - Status updated after approval

#### 7.5.5 Shift Change Request Process
1. Team Leader submits shift change request to:
   - Shift Leader
   - TKS supervisor
2. For IH category:
   - Requires approval from employee's N+1
   - Once approved, processed in TKS
   - Change reflected in Optitime system


## 9. Multi-Tenant Implementation

The system implements multi-tenancy through careful partitioning of data:

1. **Partition Key Strategy**:
   - All entities use partition keys that include country code
   - Format typically: "country_[entity-specific-id]"
   - Ensures data segregation and performance optimization

2. **Tenant Isolation**:
   - Each request includes tenant context (country, site)
   - Authentication middleware validates tenant access
   - Repository layer filters by tenant automatically

3. **Shared Business Logic**:
   - Core business logic shared across tenants
   - Country-specific rules implemented via strategy pattern
   - Configuration repository holds tenant-specific settings

4. **Event Sourcing Benefits**:
   - Events tagged with tenant information
   - Projections filtered by tenant
   - Historical data preserved separately by tenant


1. **Read Model Optimization**:
   - Denormalized views for UI queries
   - Cosmos DB indexing on common query paths
   - Caching for frequently accessed data

2. **Write Model Scaling**:
   - Event sourcing enables horizontal scaling
   - Command validation performed in-memory
   - Batched event processing for efficiency

3. **Expected Load Parameters**:
   - Peak: 500 concurrent users
   - Average: 2000 status changes per hour
   - Storage: 50M events per year

4. **Monitoring Strategy**:
   - Application Insights integration
   - Custom metrics for workflow completion times
   - Alerts for processing delays > 5 minutes

## 12. Deployment Architecture

1. **Service Topology**:
   - Microservice deployed as Azure Web App
   - Multiple instances for high availability
   - Geographical distribution by region

2. **Database Resources**:
   - Azure Cosmos DB with multi-region replication
   - Azure Blob Storage for document attachments
   - Redis Cache for session and application caching

3. **Integration Services**:
   - Azure Service Bus for messaging
   - Azure Event Grid for event distribution
   - Azure Functions for scheduled and event-triggered processes

4. **CI/CD Pipeline**:
   - Azure DevOps build and release pipelines
   - Automated testing at build time
   - Blue-green deployment strategy

5. **Environment Strategy**:
   - Development, Testing, Staging, Production
   - Synthetic load testing in Staging
   - Feature flags for incremental rollout

## 13. Future Enhancements

1. **Mobile Application**:
   - For Team Leader and Shift Leader use
   - Push notifications for urgent approvals
   - Mobile visual check capability

2. **Analytics Dashboard**:
   - Real-time status statistics
   - Absence pattern detection
   - Predictive analytics for staffing needs

3. **Integration Enhancements**:
   - Enhanced synchronization with Workday
   - Direct connection to badge systems
   - Bi-directional integration with Working Plan

4. **Automation Improvements**:
   - Machine learning for anomaly detection
   - Automated status recommendations
   - Optimization of approval flows

## 14. Appendix

### 14.1 Status Transition Rules

| From Status | To Status | Restrictions | Approvals Required |
|-------------|-----------|--------------|-------------------|
| P | A | None | TL → SL → TKS |
| P | T | Requires documentation | TL → SL → Admin |
| P | R | Must be on same day | TL → SL |
| A | T | Within 24h of absence | TL → SL → Admin |
| A | TL | Requires legal proof | TL → TKS |
| T | TL | After TKS validation | TKS |
| A | AT | Only via nurse | Nurse → TL/N+1 → TKS |
| P | CTP | Must be scheduled | From work plan |
| P | CTN | Unplanned TLO | TL → SL → Coord → Mgr |

### 14.2 API Endpoints

| Endpoint | Method | Description | Required Role |
|----------|--------|-------------|--------------|
| `/api/shift/start` | POST | Start shift process | TeamLeader, ShiftLeader |
| `/api/shift/close` | POST | Close shift process | TeamLeader, ShiftLeader |
| `/api/operator/status` | POST | Update operator status | TeamLeader, ShiftLeader |
| `/api/operator/status` | GET | Get operator status | All authenticated |
| `/api/dashboard` | GET | Get dashboard data | ShiftLeader, Manager |

## 8. Country-Specific Regulations and Workflows

### 8.1 Regional Matrix Configuration

The system implements country-specific workflow configurations through a parametrized approach:

| Feature | Morocco | Tunisia | Turkey | Portugal | Poland | Serbia | North Americas |
|---------|----------|----------|----------|-----------|---------|---------|----------------|
| Visual check | ✓ | ✓ | ✓ | ✓ | ✗ | ✗ | ✗ |
| TLO | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ | ✗ |
| Unplanned TLO | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ |
| Payroll claims | ✓ | ✓ | ✓ | ✓ | ✗ | ✓ | ✓ |
| Absence authorization | ✓ | ✓ | ✓ | ✓ | ✗ | ✓ | ✓ |
| Forfetting or loss of badge | ✓ | ✓ | ✓ | ✓ | ✗ | ✗ | ✓ |

### 8.2 Implementation Strategy

1. **Configuration Repository**:
   ```typescript
   interface CountryConfig {
     countryCode: string;
     features: {
       visualCheck: boolean;
       tlo: boolean;
       unplannedTlo: boolean;
       payrollClaims: boolean;
       absenceAuthorization: boolean;
       badgeLoss: boolean;
     };
     workflows: {
       visualCheckApproval: WorkflowDefinition;
       tloApproval: WorkflowDefinition;
       absenceApproval: WorkflowDefinition;
       // ... other workflow definitions
     };
   }
   ```

2. **Feature Toggles**:
   - Each country configuration stored in Cosmos DB
   - Feature flags control availability of functionality
   - Dynamic workflow routing based on country settings

3. **Workflow Engine Parameters**:
   - Country-specific approval chains
   - Different validation requirements per region
   - Custom notification rules
   - Region-specific documentation requirements

4. **UI Adaptations**:
   - Dynamic menu items based on country features
   - Country-specific form fields
   - Localized validation messages
   - Regional compliance warnings

### 8.3 Regional Specifics

1. **Mediterranean Region (Morocco, Tunisia, Turkey)**:
   - Full feature set enabled
   - Strict visual check requirements
   - Complete TLO management
   - Comprehensive absence tracking

2. **European Region (Portugal, Poland, Serbia)**:
   - Mixed feature availability
   - Varied TLO implementations
   - Country-specific labor laws
   - Different approval hierarchies

3. **North Americas**:
   - Limited feature set
   - Focus on payroll and absence
   - Simplified workflow approvals
   - Different badge management

### 8.4 Configuration Management

1. **Admin Interface**:
   - Country configuration dashboard
   - Feature toggle management
   - Workflow builder for each region
   - Audit logging of changes

2. **Validation Rules**:
   - Country-specific business rules
   - Regional compliance checks
   - Local holiday calendars
   - Time zone handling

3. **Integration Points**:
   - Region-specific HR systems
   - Local payroll processors
   - Country-specific reporting
   - Regional data retention rules

