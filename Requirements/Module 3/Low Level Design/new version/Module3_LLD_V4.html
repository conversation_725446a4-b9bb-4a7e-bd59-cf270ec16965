<!DOCTYPE html>
<html>
<head>
<title>Module3_LLD_V4.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="connected-workers-platform---module-3-microservices-architecture">Connected Workers Platform - Module 3 Microservices Architecture</h1>
<h2 id="table-of-contents">Table of Contents</h2>
<ol>
<li><a href="#overview">Overview</a></li>
<li><a href="#architecture-overview">Architecture Overview</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#core-microservices">Core Microservices</a>
<ul>
<li><a href="#annual-calendar-service">Annual Calendar Service</a>
<ul>
<li><a href="#overview-1">Overview</a></li>
<li><a href="#key-components">Key Components</a></li>
<li><a href="#architecture-diagram">Architecture Diagram</a></li>
<li><a href="#data-models">Data Models</a></li>
<li><a href="#event-types">Event Types</a></li>
<li><a href="#api-endpoints">API Endpoints</a></li>
<li><a href="#excel-import-feature">Excel Import Feature</a></li>
<li><a href="#performance-optimizations">Performance Optimizations</a></li>
</ul>
</li>
<li><a href="#working-plan-service">Working Plan Service</a>
<ul>
<li><a href="#overview-2">Overview</a></li>
<li><a href="#key-components-1">Key Components</a></li>
<li><a href="#architecture-enhancements">Architecture Enhancements</a></li>
<li><a href="#data-models-1">Data Models</a></li>
<li><a href="#cosmos-db-configuration">Cosmos DB Configuration</a></li>
<li><a href="#sample-implementation-flows">Sample Implementation Flows</a></li>
<li><a href="#api-endpoints-1">API Endpoints</a></li>
<li><a href="#integration-points">Integration Points</a></li>
<li><a href="#business-rules-and-validations">Business Rules and Validations</a></li>
</ul>
</li>
<li><a href="#headcount-service">HeadCount Service</a>
<ul>
<li><a href="#overview-3">Overview</a></li>
<li><a href="#key-components-2">Key Components</a></li>
<li><a href="#data-model">Data Model</a></li>
<li><a href="#status-types-and-workflows">Status Types and Workflows</a></li>
<li><a href="#visual-check-responsibility-matrix">Visual Check Responsibility Matrix</a></li>
<li><a href="#time-based-rules-and-constraints">Time-Based Rules and Constraints</a></li>
<li><a href="#cqrs-architecture-components">CQRS Architecture Components</a></li>
<li><a href="#core-system-workflows">Core System Workflows</a></li>
<li><a href="#country-specific-implementation-emeana">Country-Specific Implementation EMEA/NA</a></li>
<li><a href="#api-endpoints-2">API Endpoints</a></li>
</ul>
</li>
</ul>
</li>
</ol>
<h2 id="overview">Overview</h2>
<p>The Connected Workers Platform Module 3 implements a microservices architecture to handle workforce planning, scheduling, and tracking. Each microservice is designed with specific responsibilities and communicates with others through well-defined interfaces using Azure Service Bus and RESTful APIs.</p>
<h3 id="key-features">Key Features</h3>
<ul>
<li>Annual calendar management with holiday tracking</li>
<li>Working plan and shift management</li>
<li>Real-time operator status tracking and management</li>
<li>Multi-tenant support with country-specific configurations</li>
<li>Event-driven architecture with CQRS patterns</li>
</ul>
<h3 id="business-benefits">Business Benefits</h3>
<ul>
<li>Improved workforce planning efficiency</li>
<li>Better tracking of operator attendance and status</li>
<li>Enhanced integration between planning and execution</li>
<li>Robust error handling and monitoring</li>
<li>Flexible configuration for different regions</li>
</ul>
<h2 id="architecture-overview">Architecture Overview</h2>
<pre><code class="language-mermaid"><div class="mermaid">graph TD
    subgraph "Client Applications"
        UI[Web UI]
        Mobile[Mobile Apps]
    end

    subgraph "API Layer"
        APIM[API Management]
    end

    subgraph "Core Services"
        ACS[Annual Calendar Service]
        WPS[Working Plan Service]
        HCS[HeadCount Service]
        SB[Service Bus]
    end

    subgraph "Storage Layer"
        CDB[(Cosmos DB)]
        SQLDB[(SQL Database)]
        Redis[(Redis Cache)]
    end

    UI --> APIM
    Mobile --> APIM
    APIM --> ACS
    APIM --> WPS
    APIM --> HCS
    
    ACS --> SB
    WPS --> SB
    HCS --> SB
    
    ACS --> SQLDB
    ACS --> CDB
    WPS --> CDB
    HCS --> CDB
    
    ACS --> Redis
    WPS --> Redis
    HCS --> Redis
</div></code></pre>
<h2 id="technology-stack">Technology Stack</h2>
<h3 id="message-queues">Message Queues</h3>
<ul>
<li>Azure Service Bus for asynchronous communication</li>
<li>Event-driven integration between services</li>
<li>Support for pub/sub patterns and message sessions</li>
<li>Dead-letter queue handling and retry policies</li>
</ul>
<h3 id="databases">Databases</h3>
<ul>
<li>Cosmos DB for event sourcing and document storage
<ul>
<li>Multi-tenant support</li>
<li>Document-based storage</li>
<li>Change Feed for event processing</li>
<li>Optimized partition strategies</li>
</ul>
</li>
<li>SQL Database for relational data and reporting
<ul>
<li>Structured calendar data</li>
<li>Complex querying capabilities</li>
<li>Maintaining read models</li>
</ul>
</li>
<li>Redis Cache for performance optimization
<ul>
<li>Caching frequently accessed data</li>
<li>Distributed caching support</li>
</ul>
</li>
</ul>
<h3 id="restful-apis">RESTful APIs</h3>
<ul>
<li>Azure API Management for API gateway</li>
<li>Standardized error handling</li>
<li>Request/response validation</li>
<li>Rate limiting and throttling</li>
</ul>
<h3 id="api-gateway-with-authentication">API Gateway with Authentication</h3>
<ul>
<li>Azure AD integration</li>
<li>JWT token validation</li>
<li>Role-based access control</li>
<li>Multi-tenant security</li>
</ul>
<h1 id="annual-calendar-service">Annual Calendar Service</h1>
<h2 id="overview">Overview</h2>
<p>The Annual Calendar service manages holiday schedules, overtime events, and working day statistics across different regions. The design uses an event-sourcing approach with Cosmos DB as the primary data store and SQL read replicas for efficient querying.</p>
<h3 id="key-components">Key Components</h3>
<ul>
<li>Calendar API</li>
<li>Excel Import Service</li>
<li>Change Feed Processor</li>
<li>SQL Read Database</li>
</ul>
<h2 id="architecture-diagram">Architecture Diagram</h2>
<pre class="hljs"><code><div>┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Calendar API    │────▶│ Cosmos DB       │────▶│ Change Feed     │
│                 │     │ (Event Store)   │     │ Processor       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                                                │
        │                                                ▼
┌───────┴───────┐       ┌─────────────────┐     ┌───────┴───────┐
│ Excel Import  │       │ Production Plan │     │ Calendar SQL  │
│ Service       │       │ SQL Database    │◀────│ Database      │
└───────────────┘       └─────────────────┘     └───────────────┘
</div></code></pre>
<h2 id="data-models">Data Models</h2>
<h3 id="calendarevents-cosmos-db">CalendarEvents (Cosmos DB)</h3>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"holiday-2025-12-25-DE-NUREMBERG"</span>,
  <span class="hljs-attr">"type"</span>: <span class="hljs-string">"HOLIDAY"</span>,
  <span class="hljs-attr">"eventDate"</span>: <span class="hljs-string">"2025-12-25"</span>,
  <span class="hljs-attr">"name"</span>: <span class="hljs-string">"Christmas Day"</span>,
  <span class="hljs-attr">"description"</span>: <span class="hljs-string">"National holiday"</span>,
  <span class="hljs-attr">"category"</span>: <span class="hljs-string">"NATIONAL"</span>,
  <span class="hljs-attr">"color"</span>: <span class="hljs-string">"#FF0000"</span>,
  <span class="hljs-attr">"isWorkingDay"</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">"isRecurring"</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">"recurringType"</span>: <span class="hljs-string">"YEARLY"</span>,
  <span class="hljs-attr">"recurringMonth"</span>: <span class="hljs-number">12</span>,
  <span class="hljs-attr">"recurringDay"</span>: <span class="hljs-number">25</span>,
  <span class="hljs-attr">"hours"</span>: <span class="hljs-literal">null</span>,
  <span class="hljs-attr">"department"</span>: <span class="hljs-literal">null</span>,
  <span class="hljs-attr">"country"</span>: <span class="hljs-string">"DE"</span>,
  <span class="hljs-attr">"site"</span>: <span class="hljs-string">"NUREMBERG"</span>,
  <span class="hljs-attr">"createdBy"</span>: <span class="hljs-string">"john.doe"</span>,
  <span class="hljs-attr">"createdAt"</span>: <span class="hljs-string">"2024-11-15T10:30:00Z"</span>,
  <span class="hljs-attr">"updatedAt"</span>: <span class="hljs-string">"2024-11-15T10:30:00Z"</span>
}
</div></code></pre>
<h3 id="yearsummary-cosmos-db">YearSummary (Cosmos DB)</h3>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"id"</span>: <span class="hljs-string">"2025-DE-NUREMBERG"</span>,
  <span class="hljs-attr">"year"</span>: <span class="hljs-number">2025</span>,
  <span class="hljs-attr">"country"</span>: <span class="hljs-string">"DE"</span>,
  <span class="hljs-attr">"site"</span>: <span class="hljs-string">"NUREMBERG"</span>,
  <span class="hljs-attr">"status"</span>: <span class="hljs-string">"ACTIVE"</span>,
  <span class="hljs-attr">"totalDays"</span>: <span class="hljs-number">365</span>,
  <span class="hljs-attr">"sundays"</span>: <span class="hljs-number">52</span>,
  <span class="hljs-attr">"saturdays"</span>: <span class="hljs-number">52</span>,
  <span class="hljs-attr">"nationalHolidays"</span>: <span class="hljs-number">9</span>,
  <span class="hljs-attr">"religiousHolidays"</span>: <span class="hljs-number">4</span>,
  <span class="hljs-attr">"unpaidPublicHolidays"</span>: <span class="hljs-number">3</span>,
  <span class="hljs-attr">"vacationDays"</span>: <span class="hljs-number">18</span>,
  <span class="hljs-attr">"tlodays"</span>: <span class="hljs-number">0</span>,
  <span class="hljs-attr">"fourthSaturdays"</span>: <span class="hljs-number">12</span>,
  <span class="hljs-attr">"totalPlantHolidays"</span>: <span class="hljs-number">0</span>,
  <span class="hljs-attr">"workedDaysPlant"</span>: <span class="hljs-number">268</span>,
  <span class="hljs-attr">"lastCalculated"</span>: <span class="hljs-string">"2024-11-15T10:30:00Z"</span>,
  <span class="hljs-attr">"createdBy"</span>: <span class="hljs-string">"john.doe"</span>,
  <span class="hljs-attr">"createdAt"</span>: <span class="hljs-string">"2024-11-01T08:00:00Z"</span>,
  <span class="hljs-attr">"updatedAt"</span>: <span class="hljs-string">"2024-11-15T10:30:00Z"</span>,
  <span class="hljs-attr">"monthSummaries"</span>: [
    {
      <span class="hljs-attr">"month"</span>: <span class="hljs-number">1</span>,
      <span class="hljs-attr">"name"</span>: <span class="hljs-string">"January"</span>,
      <span class="hljs-attr">"totalDays"</span>: <span class="hljs-number">31</span>,
      <span class="hljs-attr">"sundays"</span>: <span class="hljs-number">4</span>,
      <span class="hljs-attr">"saturdays"</span>: <span class="hljs-number">4</span>,
      <span class="hljs-attr">"nationalHolidays"</span>: <span class="hljs-number">1</span>,
      <span class="hljs-attr">"religiousHolidays"</span>: <span class="hljs-number">0</span>,
      <span class="hljs-attr">"unpaidPublicHolidays"</span>: <span class="hljs-number">0</span>,
      <span class="hljs-attr">"vacationDays"</span>: <span class="hljs-number">0</span>,
      <span class="hljs-attr">"tlodays"</span>: <span class="hljs-number">0</span>,
      <span class="hljs-attr">"fourthSaturdays"</span>: <span class="hljs-number">1</span>,
      <span class="hljs-attr">"totalPlantHolidays"</span>: <span class="hljs-number">0</span>,
      <span class="hljs-attr">"workedDaysPlant"</span>: <span class="hljs-number">24</span>
    }
    <span class="hljs-comment">// Additional months...</span>
  ]
}
</div></code></pre>
<h3 id="sql-read-model">SQL Read Model</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">CREATE</span> <span class="hljs-keyword">TABLE</span> CalendarEvents (
    <span class="hljs-keyword">Id</span> UNIQUEIDENTIFIER PRIMARY <span class="hljs-keyword">KEY</span>,
    <span class="hljs-keyword">Type</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    EventDate <span class="hljs-built_in">DATE</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    <span class="hljs-keyword">Name</span> <span class="hljs-keyword">NVARCHAR</span>(<span class="hljs-number">100</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    Description <span class="hljs-keyword">NVARCHAR</span>(<span class="hljs-number">200</span>) <span class="hljs-literal">NULL</span>,
    <span class="hljs-keyword">Category</span> <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">30</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    Color <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    IsWorkingDay <span class="hljs-built_in">BIT</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    IsRecurring <span class="hljs-built_in">BIT</span> <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    RecurringType <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">20</span>) <span class="hljs-literal">NULL</span>,
    RecurringMonth <span class="hljs-built_in">TINYINT</span> <span class="hljs-literal">NULL</span>,
    RecurringDay <span class="hljs-built_in">TINYINT</span> <span class="hljs-literal">NULL</span>,
    <span class="hljs-keyword">Hours</span> <span class="hljs-built_in">DECIMAL</span>(<span class="hljs-number">5</span>,<span class="hljs-number">2</span>) <span class="hljs-literal">NULL</span>,
    Department <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">100</span>) <span class="hljs-literal">NULL</span>,
    Country <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">2</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    Site <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">50</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    CreatedBy <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">100</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    CreatedAt DATETIME <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    UpdatedAt DATETIME <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    EventId <span class="hljs-built_in">VARCHAR</span>(<span class="hljs-number">36</span>) <span class="hljs-keyword">NOT</span> <span class="hljs-literal">NULL</span>,
    
    <span class="hljs-keyword">INDEX</span> idx_event_date (EventDate),
    <span class="hljs-keyword">INDEX</span> idx_country_site (Country, Site)
);
</div></code></pre>
<h2 id="event-types">Event Types</h2>
<table>
<thead>
<tr>
<th>Event Type</th>
<th>Description</th>
<th>Key Properties</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>HolidayCreated</code></td>
<td>New holiday added</td>
<td>id, date, name, category, country, site</td>
</tr>
<tr>
<td><code>HolidayUpdated</code></td>
<td>Holiday modified</td>
<td>id, date, name, category, country, site</td>
</tr>
<tr>
<td><code>HolidayDeleted</code></td>
<td>Holiday removed</td>
<td>id</td>
</tr>
<tr>
<td><code>OvertimeCreated</code></td>
<td>New overtime added</td>
<td>id, date, hours, department, country, site</td>
</tr>
<tr>
<td><code>YearInitialized</code></td>
<td>Year configuration created</td>
<td>year, country, site, status</td>
</tr>
<tr>
<td><code>YearStatusChanged</code></td>
<td>Year status updated</td>
<td>year, country, site, oldStatus, newStatus</td>
</tr>
<tr>
<td><code>YearSummaryCalculated</code></td>
<td>Summary statistics updated</td>
<td>year, country, site, summaryData</td>
</tr>
<tr>
<td><code>CalendarImported</code></td>
<td>Bulk import completed</td>
<td>source, fileName, importedEvents</td>
</tr>
</tbody>
</table>
<h2 id="api-endpoints">API Endpoints</h2>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/api/calendar/years/{year}/countries/{country}/sites/{site}</code></td>
<td>GET</td>
<td>Get year summary data</td>
</tr>
<tr>
<td><code>/api/calendar/years/{year}/countries/{country}/sites/{site}</code></td>
<td>POST</td>
<td>Initialize or update year</td>
</tr>
<tr>
<td><code>/api/calendar/years/{year}/countries/{country}/sites/{site}/status</code></td>
<td>PUT</td>
<td>Update year status</td>
</tr>
<tr>
<td><code>/api/calendar/events</code></td>
<td>GET</td>
<td>Get calendar events</td>
</tr>
<tr>
<td><code>/api/calendar/events/holidays</code></td>
<td>POST</td>
<td>Create holiday</td>
</tr>
<tr>
<td><code>/api/calendar/events/holidays/{id}</code></td>
<td>PUT</td>
<td>Update holiday</td>
</tr>
<tr>
<td><code>/api/calendar/events/holidays/{id}</code></td>
<td>DELETE</td>
<td>Delete holiday</td>
</tr>
<tr>
<td><code>/api/calendar/events/overtime</code></td>
<td>POST</td>
<td>Create overtime</td>
</tr>
<tr>
<td><code>/api/calendar/import</code></td>
<td>POST</td>
<td>Import calendar from Excel</td>
</tr>
</tbody>
</table>
<h2 id="excel-import-feature">Excel Import Feature</h2>
<p>The microservice supports bulk import of calendar data through Excel files. The system validates the data, creates appropriate events, and updates the year summary automatically.</p>
<h3 id="import-template">Import Template</h3>
<p>The Excel template includes these columns:</p>
<ul>
<li>Date</li>
<li>Name</li>
<li>Category</li>
<li>Is Working Day</li>
<li>Country</li>
<li>Site</li>
<li>Color</li>
<li>Description</li>
</ul>
<h3 id="import-process-flow">Import Process Flow</h3>
<ol>
<li>User uploads Excel file</li>
<li>System validates file format and content</li>
<li>Valid entries are converted to events and stored</li>
<li>Year summary is recalculated</li>
<li>Import result with success/failure details is returned</li>
</ol>
<h2 id="performance-optimizations">Performance Optimizations</h2>
<ul>
<li>Cosmos DB indexing for common query patterns</li>
<li>Caching for frequently accessed year summaries</li>
<li>Batched processing in Change Feed</li>
<li>Optimized bulk import with background processing</li>
<li>Pre-calculation of summary statistics</li>
</ul>
<h1 id="working-plan-service">Working Plan Service</h1>
<h2 id="overview">Overview</h2>
<p>The Working Plan service manages weekly work plans, shift assignments, and operator scheduling across different manufacturing sites. It implements an optimized Sourced CQRS (SCQRS) pattern with advanced event-driven architecture to improve scalability, auditability, and performance.</p>
<h2 id="key-components">Key Components</h2>
<ul>
<li>Command API</li>
<li>Event Store</li>
<li>Specialized Read Models</li>
<li>Integration Framework</li>
<li>Notification Service</li>
</ul>
<h2 id="architecture-enhancements">Architecture Enhancements</h2>
<ul>
<li><strong>Multi-tenant Support</strong>: Added country and region support for global deployment</li>
<li><strong>Advanced Domain Modeling</strong>: Refined entities with clear boundaries and relationships</li>
<li><strong>Enhanced Event Sourcing</strong>: Optimized event store with efficient partition keys</li>
<li><strong>Improved Projections</strong>: Specialized read models for different query patterns</li>
<li><strong>Integration Framework</strong>: Connectors for HR systems, production planning, and transport planning</li>
<li><strong>Real-time Notifications</strong>: Event-driven notification system for stakeholders</li>
</ul>
<h2 id="data-models">Data Models</h2>
<h3 id="shift-document">Shift Document</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> ShiftDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// "SHIFT_FR_SITE1_2025_09_MON_M"</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"Shift"</span>;
    country: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// "FR", "DE", etc.</span>
    region: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// "EMEA", "APAC", etc.</span>
    site: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "SITE1"</span>
    week: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "2025_09"</span>
    date: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "2025-02-24"</span>
    dayOfWeek: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// "Monday"</span>
    shiftType: ShiftType;          <span class="hljs-comment">// "Morning" | "Afternoon" | "Night"</span>
    code: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// "M", "A", "N"</span>
    startTime: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// "06:00"</span>
    endTime: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// "14:00"</span>
    status: ShiftStatus;           <span class="hljs-comment">// "Draft" | "Planned" | "Published" | "Completed"</span>
    totalPlannedOperators: <span class="hljs-built_in">number</span>; <span class="hljs-comment">// Total operators across all teams</span>
    teams: {
        project: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// Project identifier</span>
        team_name: <span class="hljs-built_in">string</span>;
        family_zone: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// "FRONT DOOR 526", etc.</span>
        line: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Production line</span>
        activityType: <span class="hljs-built_in">string</span>;      <span class="hljs-comment">// "WP", "SA", "SA-S", "SA-P", "SA-B"</span>
        teamleader_id: <span class="hljs-built_in">string</span>;
        teamleader_name: <span class="hljs-built_in">string</span>;
        totalOperators: <span class="hljs-built_in">number</span>;    <span class="hljs-comment">// Total operators for this team</span>
        assignedOperators: {
            operator_id: <span class="hljs-built_in">string</span>;
            operator_name: <span class="hljs-built_in">string</span>;
            skills: <span class="hljs-built_in">string</span>[];
            status: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// "Active", "TLO", "OT", etc.</span>
        }[];
        unassignedOperators: {     <span class="hljs-comment">// Operators not assigned to any team (TLO)</span>
            operator_id: <span class="hljs-built_in">string</span>;
            operator_name: <span class="hljs-built_in">string</span>;
            skills: <span class="hljs-built_in">string</span>[];
            previousTeam: <span class="hljs-built_in">string</span>;  <span class="hljs-comment">// Last team assignment before unassignment</span>
            reasonCode: <span class="hljs-built_in">string</span>;    <span class="hljs-comment">// Why operator was unassigned</span>
        }[];
    }[];
}
</div></code></pre>
<h3 id="event-document">Event Document</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> EventDocument {
    id: <span class="hljs-built_in">string</span>;                    <span class="hljs-comment">// UUID</span>
    <span class="hljs-keyword">type</span>: <span class="hljs-string">"Event"</span>;
    eventType: EventType;          <span class="hljs-comment">// Specific event type</span>
    aggregateId: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// ID of the entity this event affects</span>
    aggregateType: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// "Shift", "Team", "Operator"</span>
    country: <span class="hljs-built_in">string</span>;               <span class="hljs-comment">// For partition key</span>
    site: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// For partition key</span>
    week: <span class="hljs-built_in">string</span>;                  <span class="hljs-comment">// For weekly grouping</span>
    timestamp: <span class="hljs-built_in">string</span>;             <span class="hljs-comment">// When the event occurred</span>
    userId: <span class="hljs-built_in">string</span>;                <span class="hljs-comment">// Who triggered this event</span>
    payload: <span class="hljs-built_in">any</span>;                  <span class="hljs-comment">// Event-specific data</span>
}
</div></code></pre>
<h2 id="cosmos-db-configuration">Cosmos DB Configuration</h2>
<table>
<thead>
<tr>
<th>Container Name</th>
<th>Purpose</th>
<th>Partition Key</th>
<th>Indexing Policy</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>events</code></td>
<td>Event store</td>
<td><code>/country,/site</code></td>
<td>Optimize for timestamp and aggregate queries</td>
</tr>
<tr>
<td><code>shifts</code></td>
<td>Shift read models</td>
<td><code>/country,/site</code></td>
<td>Optimize for date and week queries</td>
</tr>
<tr>
<td><code>teamLeader_team_crew</code></td>
<td>Team definitions</td>
<td><code>customer</code></td>
<td>Default</td>
</tr>
<tr>
<td><code>operators</code></td>
<td>Operator definitions</td>
<td><code>/country,/site</code></td>
<td>Optimize for skill queries</td>
</tr>
</tbody>
</table>
<h2 id="sample-implementation-flows">Sample Implementation Flows</h2>
<h3 id="dh-category-planning-flow">DH Category Planning Flow</h3>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    actor SL as Shift Leader
    participant UI as Web UI
    participant API as Command API
    participant ES as Event Store
    participant RM as Read Models
    participant NS as Notification Service
    
    SL->>UI: Access calendar interface
    UI->>API: GET /weeks/{weekId}/calendar
    API->>RM: Get calendar data
    RM-->>API: Return calendar
    API-->>UI: Display calendar
    
    SL->>UI: Select week and shift
    
    SL->>UI: Assign team leaders to zones
    UI->>API: POST /shifts/{id}/teams
    API->>ES: Save TeamAssigned event
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    UI->>API: POST /shifts/{id}/teams/{teamId}/teamleader
    API->>ES: Save TeamleaderAssigned event
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    SL->>UI: Assign operators to teams
    UI->>API: POST /shifts/{id}/teams/{teamId}/operators
    API->>ES: Save OperatorAssigned events
    ES-->>API: Confirm
    API-->>UI: Update UI with team members
    
    SL->>UI: Complete planning
    UI->>API: POST /shifts/{id}/validate
    API->>ES: Save ShiftValidated event
    ES->>RM: Update read models
    API->>NS: Trigger notifications
    NS-->>SL: Send email notification
    NS-->>+Team Leaders: Send notifications
    API-->>UI: Show confirmation
</div></code></pre>
<h2 id="api-endpoints">API Endpoints</h2>
<h3 id="command-api">Command API</h3>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
<th>Request Body Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/api/v1/shifts</code></td>
<td>POST</td>
<td>Create new shift</td>
<td><code>{ &quot;country&quot;: &quot;FR&quot;, &quot;site&quot;: &quot;SITE1&quot;, &quot;date&quot;: &quot;2025-05-06&quot;, &quot;shiftType&quot;: &quot;Morning&quot; }</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/teams</code></td>
<td>POST</td>
<td>Add team to shift</td>
<td><code>{ &quot;team_id&quot;: &quot;TEAM123&quot;, &quot;team_name&quot;: &quot;Assembly A&quot;, &quot;family_zone&quot;: &quot;FRONT DOOR 526&quot;, &quot;line&quot;: &quot;L1&quot;, &quot;activityType&quot;: &quot;WP&quot; }</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/teams/{teamId}/teamleader</code></td>
<td>POST</td>
<td>Set team leader</td>
<td><code>{ &quot;teamleader_id&quot;: &quot;EMP789&quot;, &quot;teamleader_name&quot;: &quot;John Smith&quot; }</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/teams/{teamId}/operators</code></td>
<td>POST</td>
<td>Add operator</td>
<td><code>{ &quot;operator_id&quot;: &quot;EMP456&quot;, &quot;operator_name&quot;: &quot;Jane Doe&quot;, &quot;skills&quot;: [&quot;cutting&quot;, &quot;assembly&quot;] }</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/operators/tlo</code></td>
<td>POST</td>
<td>Move operator to TLO</td>
<td><code>{ &quot;operator_id&quot;: &quot;EMP456&quot;, &quot;previousTeam&quot;: &quot;TEAM123&quot;, &quot;reasonCode&quot;: &quot;SKILL_MISMATCH&quot; }</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/validate</code></td>
<td>POST</td>
<td>Validate shift plan</td>
<td><code>{ &quot;validateAllocation&quot;: true }</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/emergency-update</code></td>
<td>POST</td>
<td>Emergency update</td>
<td><code>{ &quot;reason&quot;: &quot;Production line change&quot;, &quot;approvedBy&quot;: &quot;SUPERVISOR1&quot;, &quot;changes&quot;: [{&quot;field&quot;: &quot;team.line&quot;, &quot;value&quot;: &quot;L3&quot;}] }</code></td>
</tr>
</tbody>
</table>
<h3 id="query-api">Query API</h3>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
<th>Query Parameters</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/api/v1/sites/{site}/shifts</code></td>
<td>GET</td>
<td>Get shifts for site</td>
<td><code>country</code>, <code>week?</code>, <code>date?</code>, <code>category?</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}</code></td>
<td>GET</td>
<td>Get shift details</td>
<td>N/A</td>
</tr>
<tr>
<td><code>/api/v1/sites/{site}/calendar/{week}</code></td>
<td>GET</td>
<td>Get calendar view</td>
<td><code>country</code>, <code>category?</code></td>
</tr>
<tr>
<td><code>/api/v1/teams</code></td>
<td>GET</td>
<td>Get teams</td>
<td><code>country</code>, <code>site</code>, <code>family?</code></td>
</tr>
<tr>
<td><code>/api/v1/operators</code></td>
<td>GET</td>
<td>Get operators</td>
<td><code>country</code>, <code>site</code>, <code>status?</code>, <code>skill?</code></td>
</tr>
<tr>
<td><code>/api/v1/shifts/{id}/unassigned</code></td>
<td>GET</td>
<td>Get TLO operators</td>
<td>N/A</td>
</tr>
<tr>
<td><code>/api/v1/reports/utilization</code></td>
<td>GET</td>
<td>Get utilization report</td>
<td><code>country</code>, <code>site</code>, <code>week</code>, <code>department?</code></td>
</tr>
</tbody>
</table>
<h2 id="integration-points">Integration Points</h2>
<ol>
<li>HR System Integration</li>
<li>Production Planning Integration</li>
<li>Transport Planning Integration</li>
</ol>
<h2 id="business-rules-and-validations">Business Rules and Validations</h2>
<h3 id="planning-timeline-rules">Planning Timeline Rules</h3>
<table>
<thead>
<tr>
<th>Rule ID</th>
<th>Rule Description</th>
<th>Validation Logic</th>
</tr>
</thead>
<tbody>
<tr>
<td>TR-01</td>
<td>Initial planning deadline: Friday 5 PM</td>
<td><code>if (currentTime &lt; fridayDeadline &amp;&amp; shiftWeek == nextWeek) allow else deny</code></td>
</tr>
<tr>
<td>TR-02</td>
<td>Weekend planning deadline: Friday 5 PM</td>
<td><code>if (currentTime &lt; fridayDeadline &amp;&amp; isWeekendShift &amp;&amp; shiftWeek == nextWeek) allow else deny</code></td>
</tr>
<tr>
<td>TR-03</td>
<td>J-1 modification: before 5 PM</td>
<td><code>if (currentTime &lt; dailyDeadline &amp;&amp; shiftDate == tomorrow) allow else deny</code></td>
</tr>
<tr>
<td>TR-04</td>
<td>Evening shift same-day mod: 4h before</td>
<td><code>if (currentTime &lt; (shiftStart - 4h) &amp;&amp; shiftType == &quot;Evening&quot; &amp;&amp; shiftDate == today) allow else deny</code></td>
</tr>
<tr>
<td>TR-05</td>
<td>Night shift same-day mod: before 5 PM</td>
<td><code>if (currentTime &lt; dailyDeadline &amp;&amp; shiftType == &quot;Night&quot; &amp;&amp; shiftDate == today) allow else deny</code></td>
</tr>
<tr>
<td>TR-06</td>
<td>Overtime addition: before 11 AM</td>
<td><code>if (currentTime &lt; overtimeDeadline &amp;&amp; shiftDate == today) allow else deny</code></td>
</tr>
</tbody>
</table>
<h3 id="validation-rules">Validation Rules</h3>
<table>
<thead>
<tr>
<th>Rule ID</th>
<th>Rule Description</th>
<th>Validation Logic</th>
</tr>
</thead>
<tbody>
<tr>
<td>VR-01</td>
<td>Operator eligibility check</td>
<td><code>if (operator.status not in [&quot;SICK_LEAVE&quot;, &quot;DISCIPLINARY&quot;]) allow else deny</code></td>
</tr>
<tr>
<td>VR-02</td>
<td>Transport planning auth</td>
<td><code>if (user.role == &quot;TRANSPORT_RESPONSIBLE&quot;) allow else deny</code></td>
</tr>
<tr>
<td>VR-03</td>
<td>Team skill requirements</td>
<td><code>if (team.requiredSkills.every(skill =&gt; team.operators.some(op =&gt; op.skills.includes(skill)))) allow else deny</code></td>
</tr>
<tr>
<td>VR-04</td>
<td>Special activity authorization</td>
<td><code>if (activityType.startsWith(&quot;SA&quot;) &amp;&amp; user.hasPermission(&quot;SPECIAL_ACTIVITY&quot;)) allow else deny</code></td>
</tr>
<tr>
<td>VR-05</td>
<td>Min/max staffing levels</td>
<td><code>if (team.operatorCount &gt;= team.minOperators &amp;&amp; team.operatorCount &lt;= team.maxOperators) allow else deny</code></td>
</tr>
<tr>
<td>VR-06</td>
<td>No overlapping assignments</td>
<td><code>if (!hasOperatorOverlappingShift(operator, shift)) allow else deny</code></td>
</tr>
</tbody>
</table>
<h1 id="headcount-service">HeadCount Service</h1>
<h2 id="overview">Overview</h2>
<p>The HeadCount service handles real-time operator tracking and status management. It implements a multi-tenant architecture with comprehensive event processing and support for various employee categories, shift patterns, and country-specific workflows.</p>
<h2 id="key-components">Key Components</h2>
<ul>
<li>Command and Query APIs</li>
<li>Event-driven status management</li>
<li>Workflow approval engine</li>
<li>Integration with Working Plan</li>
<li>Multi-tenant configuration</li>
</ul>
<h2 id="data-model">Data Model</h2>
<h3 id="primary-entities">Primary Entities</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> Operator {
    id: <span class="hljs-built_in">string</span>;                  
    partitionKey: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// Format: "country_teamId"</span>
    badgeId: <span class="hljs-built_in">string</span>;
    firstName: <span class="hljs-built_in">string</span>;
    lastName: <span class="hljs-built_in">string</span>;
    teamId: <span class="hljs-built_in">string</span>;
    teamLeaderId: <span class="hljs-built_in">string</span>;        
    department: <span class="hljs-built_in">string</span>;
    category: <span class="hljs-built_in">string</span>;            <span class="hljs-comment">// "DH", "IS", "IH"</span>
    isActive: <span class="hljs-built_in">boolean</span>;
    country: <span class="hljs-built_in">string</span>;             
}

<span class="hljs-keyword">interface</span> LongTermStatus {
    id: <span class="hljs-built_in">string</span>;
    partitionKey: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// Format: "country_operatorId"</span>
    operatorId: <span class="hljs-built_in">string</span>;
    <span class="hljs-keyword">type</span>: LongTermStatusType;
    startDate: date;
    endDate: date;
    reason: <span class="hljs-built_in">string</span>;
    approvedBy: <span class="hljs-built_in">string</span>;
    approvalRequestId: <span class="hljs-built_in">string</span>;   
    createdAt: datetime;
    updatedAt: datetime;
    metadata: object;
    country: <span class="hljs-built_in">string</span>;             
}

<span class="hljs-keyword">interface</span> ShiftDocument {
    id: <span class="hljs-built_in">string</span>;
    partitionKey: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// Format: "country_date" </span>
    date: date;
    shiftCode: <span class="hljs-built_in">string</span>;           <span class="hljs-comment">// "M" (Morning), "E" (Evening), "N" (Night)</span>
    departmentId: <span class="hljs-built_in">string</span>;
    teamId: <span class="hljs-built_in">string</span>;
    workingPlanId: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// Reference to source working plan</span>
    operators: [
        {
            operatorId: <span class="hljs-built_in">string</span>;
            fullName: <span class="hljs-built_in">string</span>;     <span class="hljs-comment">// Added operator's full name for easier UI display</span>
            status: ShiftStatusType;  <span class="hljs-comment">// Actual status for the shift</span>
            plannedStatus: <span class="hljs-built_in">string</span>;    <span class="hljs-comment">// Pre-assigned status from working plan</span>
            isTLO: <span class="hljs-built_in">boolean</span>;       <span class="hljs-comment">// Technical layoff indicator</span>
            isContainment: <span class="hljs-built_in">boolean</span>;
            recordedBy: <span class="hljs-built_in">string</span>;   <span class="hljs-comment">// Who recorded this status</span>
            recordedAt: datetime; <span class="hljs-comment">// When the status was recorded</span>
        }
    ];
    shiftStarted: <span class="hljs-built_in">boolean</span>;
    shiftStartTime: datetime;
    shiftClosed: <span class="hljs-built_in">boolean</span>;
    shiftCloseTime: datetime;
    validated: <span class="hljs-built_in">boolean</span>;
    validatedBy: <span class="hljs-built_in">string</span>;
    validatedAt: datetime;
    country: <span class="hljs-built_in">string</span>;
    updatedAt: datetime;
}
</div></code></pre>
<h2 id="status-types-and-workflows">Status Types and Workflows</h2>
<table>
<thead>
<tr>
<th>Status Code</th>
<th>Status Name</th>
<th>Type</th>
<th>Description</th>
<th>Triggering Microservice/Process</th>
<th>Applicable Categories</th>
</tr>
</thead>
<tbody>
<tr>
<td>P</td>
<td>Present</td>
<td>Shift</td>
<td>Operator is present for shift</td>
<td>HeadCount</td>
<td>All</td>
</tr>
<tr>
<td>A</td>
<td>Absent</td>
<td>Shift</td>
<td>Operator is absent without justification</td>
<td>HeadCount</td>
<td>All</td>
</tr>
<tr>
<td>R</td>
<td>Delayed</td>
<td>Shift</td>
<td>Operator is late with approved entry authorization</td>
<td>Request</td>
<td>DH, IH, IS</td>
</tr>
<tr>
<td>T</td>
<td>Temporary Authorization</td>
<td>Operator</td>
<td>Temporary status pending evidence</td>
<td>Request</td>
<td>All</td>
</tr>
<tr>
<td>TL</td>
<td>Legal Authorization</td>
<td>Operator</td>
<td>Legal authorization (e.g., Breastfeeding)</td>
<td>Request</td>
<td>DH</td>
</tr>
<tr>
<td>CR</td>
<td>Leave Holiday</td>
<td>Operator</td>
<td>Planned vacation/leave</td>
<td>Request</td>
<td>All</td>
</tr>
<tr>
<td>CTP</td>
<td>Planned TLO</td>
<td>Operator</td>
<td>Planned Technical Layoff</td>
<td>Working Plan</td>
<td>DH, IS, IH</td>
</tr>
<tr>
<td>CTN</td>
<td>Unplanned TLO</td>
<td>Operator</td>
<td>Unplanned Technical Layoff</td>
<td>Request</td>
<td>All</td>
</tr>
</tbody>
</table>
<h2 id="visual-check-responsibility-matrix">Visual Check Responsibility Matrix</h2>
<table>
<thead>
<tr>
<th>Employee Category</th>
<th>Visual Check Responsibility</th>
<th>Validation Path</th>
<th>Timing</th>
</tr>
</thead>
<tbody>
<tr>
<td>DH Operators</td>
<td>Team Leader</td>
<td>TL → SL → TKS</td>
<td>During shift</td>
</tr>
<tr>
<td>IS/IH Employees</td>
<td>Department Clerk</td>
<td>Clerk → Department Manager → TKS</td>
<td>D+1</td>
</tr>
<tr>
<td>Team Leaders</td>
<td>Shift Leader</td>
<td>SL → TKS</td>
<td>During shift</td>
</tr>
<tr>
<td>Shift Leader Backup Structure</td>
<td>Shift Leader</td>
<td>SL → TKS</td>
<td>During shift</td>
</tr>
<tr>
<td>Backup Structure in Replacement Process</td>
<td>Team Leader</td>
<td>TL → SL → TKS</td>
<td>During shift</td>
</tr>
<tr>
<td>Team Leader Backups (not in replacement)</td>
<td>Shift Leader</td>
<td>SL → TKS</td>
<td>During shift</td>
</tr>
<tr>
<td>Shift Leader Backups (not in replacement)</td>
<td>Shift Leader</td>
<td>SL → TKS</td>
<td>During shift</td>
</tr>
<tr>
<td>Containment Operators</td>
<td>Team Leader + Quality Supervisor</td>
<td>TL → SL → TKS</td>
<td>During shift</td>
</tr>
</tbody>
</table>
<h2 id="time-based-rules-and-constraints">Time-Based Rules and Constraints</h2>
<table>
<thead>
<tr>
<th>Rule Type</th>
<th>Description</th>
<th>Timing</th>
<th>Action on Violation</th>
</tr>
</thead>
<tbody>
<tr>
<td>Status Modification Window</td>
<td>Team Leaders can modify operator status</td>
<td>Until D+1 at 7:00 AM for night shift</td>
<td>Submit Correction of Clocking request</td>
</tr>
<tr>
<td>Shift Start Alert</td>
<td>Alert if Team Leader doesn't click &quot;Start Shift&quot;</td>
<td>Before end of shift</td>
<td>Notification to Shift Leader</td>
</tr>
<tr>
<td>Dashboard Update</td>
<td>Show Team Leaders who clicked/didn't click Start/Close Shift</td>
<td>Real-time</td>
<td>Visible to Shift Leader</td>
</tr>
<tr>
<td>Prolonged Absence</td>
<td>Alert for absence without justification</td>
<td>After 4 consecutive days</td>
<td>Notification to HR (potential job abandonment)</td>
</tr>
<tr>
<td>Status Closure</td>
<td>Team Leader must close all operator statuses</td>
<td>End of shift</td>
<td>Popup warning if statuses left empty</td>
</tr>
</tbody>
</table>
<h2 id="cqrs-architecture-components">CQRS Architecture Components</h2>
<h3 id="command-side">Command Side</h3>
<ul>
<li>
<p><strong>Commands</strong>:</p>
<ul>
<li><code>StartShiftCommand</code> - Initiated by Team Leader</li>
<li><code>RecordVisualCheckCommand</code> - Used for visual checks by TL/Clerk/SL</li>
<li><code>CloseShiftCommand</code> - Initiated by Team Leader at end of shift</li>
<li><code>RequestStatusChangeCommand</code> - For all request-based status changes</li>
<li><code>ValidateShiftRecordsCommand</code> - For SL/Department Manager validation</li>
<li><code>CorrectClockingCommand</code> - For corrections after deadline</li>
<li><code>ScheduleStatusChangeCommand</code> - For future-dated status changes</li>
</ul>
</li>
<li>
<p><strong>Command Handlers</strong>:</p>
<ul>
<li>Process business logic and validation rules</li>
<li>Apply category-specific approval workflows</li>
<li>Generate appropriate events</li>
</ul>
</li>
<li>
<p><strong>Domain Models</strong>:</p>
<ul>
<li><code>Operator</code> - Core entity for DH category employees</li>
<li><code>Employee</code> - Core entity for IS/IH category employees</li>
<li><code>Team</code> - Group of operators under a Team Leader</li>
<li><code>Shift</code> - Represents a work shift (Morning, Evening, Night)</li>
<li><code>ShiftDocument</code> - Represents shift plan imported from Working Plan system</li>
</ul>
</li>
</ul>
<h3 id="event-store-containers">Event Store Containers</h3>
<ol>
<li>
<p><strong>StatusChangeEvents</strong></p>
<ul>
<li>PartitionKey: OperatorId/EmployeeId</li>
<li>Properties:
<ul>
<li>EventId (GUID)</li>
<li>EventType (string) - &quot;ShiftStarted&quot;, &quot;VisualCheckRecorded&quot;, &quot;StatusChangeRequested&quot;, etc.</li>
<li>OperatorId/EmployeeId (string)</li>
<li>Category (string) - &quot;DH&quot;, &quot;IS&quot;, &quot;IH&quot;</li>
<li>OldStatus (string)</li>
<li>NewStatus (string)</li>
<li>StatusType (string) - &quot;Shift&quot; or &quot;Operator&quot;</li>
<li>EffectiveDate (datetime)</li>
<li>ExpiryDate (datetime, nullable)</li>
<li>Reason (string)</li>
<li>RequestId (string, nullable)</li>
<li>RequestType (string, nullable) - &quot;REQ01&quot;, &quot;REQ02&quot;, etc.</li>
<li>SourceMicroservice (string)</li>
<li>Timestamp (datetime)</li>
<li>Actor (string) - who initiated the change</li>
<li>ActorRole (string) - &quot;TeamLeader&quot;, &quot;ShiftLeader&quot;, &quot;Clerk&quot;, &quot;Nurse&quot;, etc.</li>
<li>ApprovalState (string) - &quot;Initiated&quot;, &quot;Approved&quot;, &quot;Forwarded&quot;, &quot;Completed&quot;</li>
<li>ApprovalStep (int)</li>
<li>NextApprover (string, nullable)</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>ScheduledStatusEvents</strong></p>
<ul>
<li>PartitionKey: EffectiveDate</li>
<li>Properties:
<ul>
<li>EventId (GUID)</li>
<li>OperatorId/EmployeeId (string)</li>
<li>Category (string) - &quot;DH&quot;, &quot;IS&quot;, &quot;IH&quot;</li>
<li>StatusCode (string)</li>
<li>EffectiveDate (datetime)</li>
<li>ExpiryDate (datetime, nullable)</li>
<li>Reason (string)</li>
<li>RequestId (string, nullable)</li>
<li>RequestType (string, nullable)</li>
<li>CreatedBy (string)</li>
<li>CreatedTimestamp (datetime)</li>
<li>ApprovalState (string)</li>
<li>ApprovalWorkflow (string) - JSON representation of approval steps</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>ApprovalEvents (triggered by the workflow microservice)</strong></p>
<ul>
<li>PartitionKey: RequestId</li>
<li>Properties:
<ul>
<li>EventId (GUID)</li>
<li>RequestId (string)</li>
<li>ApprovalStep (int)</li>
<li>ApproverRole (string)</li>
<li>ApproverId (string)</li>
<li>Decision (string) - &quot;Approved&quot;, &quot;Rejected&quot;</li>
<li>Comments (string)</li>
<li>Timestamp (datetime)</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>ShiftDocumentEvents</strong></p>
<ul>
<li>PartitionKey: ShiftDate</li>
<li>Properties:
<ul>
<li>EventId (GUID)</li>
<li>EventType (string) - &quot;ShiftDocumentImported&quot;, &quot;ShiftDocumentUpdated&quot;</li>
<li>ShiftDocumentId (string)</li>
<li>ShiftDate (date)</li>
<li>ShiftCode (string)</li>
<li>DepartmentId (string)</li>
<li>TeamId (string)</li>
<li>WorkingPlanId (string)</li>
<li>OperatorStatusUpdates (array) - Operator status changes</li>
<li>ImportedBy (string)</li>
<li>ImportTimestamp (datetime)</li>
<li>Version (int)</li>
</ul>
</li>
</ul>
</li>
</ol>
<h3 id="read-model-containers">Read Model Containers</h3>
<ol>
<li>
<p><strong>OperatorStatus</strong></p>
<ul>
<li>PartitionKey: DepartmentId</li>
<li>Properties:
<ul>
<li>OperatorId/EmployeeId (string)</li>
<li>Name (string)</li>
<li>Category (string) - &quot;DH&quot;, &quot;IS&quot;, &quot;IH&quot;</li>
<li>DepartmentId (string)</li>
<li>TeamId (string, nullable)</li>
<li>TeamLeaderId (string, nullable)</li>
<li>ShiftLeaderId (string, nullable)</li>
<li>CurrentShiftStatus (string) - &quot;Present&quot;, &quot;Absent&quot;, &quot;Delayed&quot;</li>
<li>CurrentOperatorStatus (string) - Extended status code</li>
<li>StatusType (string) - &quot;Shift&quot; or &quot;Operator&quot;</li>
<li>EffectiveFrom (datetime)</li>
<li>EffectiveTo (datetime, nullable)</li>
<li>LastUpdateTimestamp (datetime)</li>
<li>SourceEventId (string)</li>
<li>RequestId (string, nullable)</li>
<li>IsContainment (boolean)</li>
<li>ResponsibleForVisualCheck (string) - PersonId of responsible checker</li>
<li>StatusModifiable (boolean) - Based on time window rules</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>ShiftSession</strong></p>
<ul>
<li>PartitionKey: ShiftDate</li>
<li>Properties:
<ul>
<li>SessionId (string)</li>
<li>TeamId (string)</li>
<li>TeamLeaderId (string)</li>
<li>ShiftDate (date)</li>
<li>ShiftType (string) - &quot;Morning&quot;, &quot;Evening&quot;, &quot;Night&quot;</li>
<li>SessionStarted (boolean)</li>
<li>SessionStartTime (datetime, nullable)</li>
<li>SessionClosed (boolean)</li>
<li>SessionCloseTime (datetime, nullable)</li>
<li>TotalOperators (int)</li>
<li>StatusBreakdown (JSON object) - Counts of each status</li>
<li>ValidationStatus (string) - &quot;Pending&quot;, &quot;Validated&quot;, &quot;Forwarded&quot;</li>
<li>ValidatedBy (string, nullable)</li>
<li>ValidatedTimestamp (datetime, nullable)</li>
</ul>
</li>
</ul>
</li>
<li>
<p><strong>ShiftDocumentView</strong></p>
<ul>
<li>PartitionKey: ShiftDate</li>
<li>Properties:
<ul>
<li>DocumentId (string)</li>
<li>ShiftDate (date)</li>
<li>ShiftCode (string)</li>
<li>DepartmentId (string)</li>
<li>TeamId (string)</li>
<li>WorkingPlanId (string)</li>
<li>ExpectedOperators (array)</li>
<li>StatusBreakdown (JSON object)</li>
<li>ImportedAt (datetime)</li>
<li>LastUpdatedAt (datetime)</li>
<li>IsActive (boolean)</li>
<li>Version (int)</li>
</ul>
</li>
</ul>
</li>
</ol>
<h2 id="core-system-workflows">Core System Workflows</h2>
<h3 id="start-shift-process">Start Shift Process</h3>
<ol>
<li>Team Leader clicks &quot;Start Shift&quot; button</li>
<li>System:
<ul>
<li>Creates <code>ShiftStarted</code> event</li>
<li>Creates a new ShiftSession record</li>
<li>Initializes ShiftRecords for all operators in team</li>
<li>Pre-loads any effective operator statuses</li>
<li>Imports ShiftDocument from Working Plan if available</li>
<li>Updates ShiftLeaderDashboard</li>
</ul>
</li>
<li>If Team Leader doesn't click &quot;Start Shift&quot;:
<ul>
<li>System automatically checks 1 hour after shift start time</li>
<li>Sends alert to Shift Leader</li>
<li>Adds notification to ShiftLeaderDashboard</li>
</ul>
</li>
</ol>
<h3 id="visual-check-process">Visual Check Process</h3>
<p><strong>For DH Category (Team Leader):</strong></p>
<ol>
<li>Team Leader sees list of operators including:
<ul>
<li>Regular team members</li>
<li>Containment operators (grayed out)</li>
<li>Backup structure operators (if replacement process completed)</li>
<li>Pre-filled statuses from ShiftDocument if available</li>
</ul>
</li>
<li>For each operator, Team Leader:
<ul>
<li>Verifies physical presence</li>
<li>Confirms or updates pre-filled status</li>
<li>System creates <code>VisualCheckRecorded</code> event</li>
<li>Updates ShiftRecords and OperatorStatus</li>
</ul>
</li>
</ol>
<p><strong>For IS/IH Category (Department Clerk):</strong></p>
<ol>
<li>Department Clerk performs visual check on D+1</li>
<li>System follows same process but with different approval workflow</li>
<li>Final approval comes from Department Manager</li>
</ol>
<p><strong>For Team Leaders (by Shift Leader):</strong></p>
<ol>
<li>Shift Leader performs visual check for Team Leaders</li>
<li>Shift Leader also checks backup structure operators not in replacement process</li>
</ol>
<h3 id="close-shift-process">Close Shift Process</h3>
<ol>
<li>Team Leader clicks &quot;Close My Shift&quot;</li>
<li>System:
<ul>
<li>Validates all operators have status assigned</li>
<li>If incomplete, shows warning popup</li>
<li>Creates <code>ShiftClosed</code> event</li>
<li>Updates ShiftSession and ShiftLeaderDashboard</li>
<li>Sends notification to Shift Leader</li>
</ul>
</li>
<li>Shift Leader validation process:
<ul>
<li>Reviews all team statuses</li>
<li>Can override/correct if needed</li>
<li>Approves with <code>ValidateShiftRecords</code> command</li>
<li>System creates <code>ShiftRecordsValidated</code> event</li>
<li>Sends notification to TKS</li>
</ul>
</li>
<li>For IS/IH categories (Department Clerk):
<ul>
<li>Clerk closes on D+1</li>
<li>Sends notification to Department Manager</li>
<li>Department Manager validates</li>
<li>System forwards to TKS</li>
</ul>
</li>
</ol>
<h3 id="shiftdocument-integration-with-working-plan">ShiftDocument Integration with Working Plan</h3>
<ol>
<li>
<p><strong>Automatic Import Process</strong>:</p>
<ul>
<li>Working Plan microservice publishes ShiftDocument event</li>
<li>ShiftImporter function triggered to process incoming document</li>
<li>Creates <code>ShiftDocumentImported</code> event</li>
<li>Updates ShiftDocumentView read model</li>
<li>Flags any pre-assigned statuses (e.g., TLO, planned absences)</li>
</ul>
</li>
<li>
<p><strong>Manual Import Process</strong>:</p>
<ul>
<li>Shift Leader can manually trigger import via UI</li>
<li>Validates against current shift data</li>
<li>Resolves conflicts with current operator statuses</li>
<li>Creates <code>ShiftDocumentUpdated</code> event</li>
</ul>
</li>
<li>
<p><strong>Status Pre-loading</strong>:</p>
<ul>
<li>During shift start, system consults ShiftDocument</li>
<li>Pre-fills operator statuses based on planned data</li>
<li>Team Leader confirms or overrides during visual check</li>
<li>Differences between planned and actual are recorded for reporting</li>
</ul>
</li>
<li>
<p><strong>Planned TLO (CTP) Processing</strong>:</p>
<ul>
<li>Working Plan includes TLO assignments from Capacity Study</li>
<li>System extracts TLO-assigned operators</li>
<li>Pre-fills status as &quot;CTP&quot; for those operators</li>
<li>Team Leader confirms during visual check</li>
<li>Statistics on planned vs. actual TLO usage tracked</li>
</ul>
</li>
</ol>
<h3 id="status-change-request-workflows">Status Change Request Workflows</h3>
<h4 id="absence-authorization-t-%E2%86%92-tl-workflow">Absence Authorization (T → TL) Workflow</h4>
<ol>
<li>
<p>Team Leader/Requester initiates absence authorization request (REQ 01)</p>
<pre class="hljs"><code><div>{
  <span class="hljs-attr">"requestId"</span>: <span class="hljs-string">"REQ01-123"</span>,
  <span class="hljs-attr">"requestType"</span>: <span class="hljs-string">"REQ01"</span>,
  <span class="hljs-attr">"operatorId"</span>: <span class="hljs-string">"OP456"</span>,
  <span class="hljs-attr">"statusCode"</span>: <span class="hljs-string">"T"</span>,
  <span class="hljs-attr">"reason"</span>: <span class="hljs-string">"Medical Appointment"</span>,
  <span class="hljs-attr">"proofType"</span>: <span class="hljs-string">"Medical Certificate"</span>,
  <span class="hljs-attr">"effectiveFrom"</span>: <span class="hljs-string">"2025-05-01T08:00:00Z"</span>,
  <span class="hljs-attr">"effectiveTo"</span>: <span class="hljs-string">"2025-05-01T16:00:00Z"</span>,
  <span class="hljs-attr">"requester"</span>: <span class="hljs-string">"TL789"</span>
}
</div></code></pre>
</li>
<li>
<p>System:</p>
<ul>
<li>Creates <code>StatusChangeRequested</code> event</li>
<li>Adds to approval workflow based on:
<ul>
<li>Employee category (DH/IS/IH)</li>
<li>Time (administrative vs. non-administrative hours)</li>
</ul>
</li>
<li>Updates StatusRequests read model</li>
</ul>
</li>
<li>
<p>Approval Process:</p>
<ul>
<li>For DH categories during administrative hours:
<ul>
<li>Team Leader → Shift Leader → Admin Coordinator</li>
</ul>
</li>
<li>For DH categories during non-administrative hours:
<ul>
<li>Team Leader → Shift Leader</li>
</ul>
</li>
<li>For IH categories during administrative hours:
<ul>
<li>Requester → N+1 → Admin Coordinator</li>
</ul>
</li>
<li>For IH categories during non-administrative hours:
<ul>
<li>Requester → N+1</li>
</ul>
</li>
<li>For IS categories:
<ul>
<li>Follow approval matrix in Module 1</li>
</ul>
</li>
</ul>
</li>
<li>
<p>After final approval:</p>
<ul>
<li>System creates <code>StatusApproved</code> event</li>
<li>Updates operator status to &quot;TL&quot;</li>
<li>Sends notification to TKS</li>
<li>TKS validates proof (scanned at kiosk)</li>
<li>Status change reflected in Optitime system</li>
</ul>
</li>
<li>
<p>Breastfeeding-specific process:</p>
<ul>
<li>Request with reason &quot;BREASTFEEDING&quot;</li>
<li>Declaration of honor scanned at kiosk</li>
<li>TKS approval</li>
<li>Status updated to &quot;TL&quot;</li>
<li>For IS/IH category, notification sent to N+1</li>
<li>Status updated in clerk interface</li>
</ul>
</li>
</ol>
<h4 id="leave-holiday-cr-workflow">Leave Holiday (CR) Workflow</h4>
<ol>
<li>Team Leader initiates Leave Request (REQ 07)</li>
<li>System:
<ul>
<li>Creates <code>LeaveRequested</code> event</li>
<li>Follows approval workflow from FDS Module 1</li>
<li>For IH category, requester raises request directly</li>
</ul>
</li>
<li>Upon approval:
<ul>
<li>Creates <code>LeaveApproved</code> event</li>
<li>Updates OperatorStatus</li>
<li>If future-dated, adds to ScheduledStatusEvents</li>
<li>Updates connected to Workday for balance verification (M-1)</li>
</ul>
</li>
</ol>
<h4 id="travel-orders-didn-workflow">Travel Orders (DI/DN) Workflow</h4>
<ol>
<li>Team Leader/Requester initiates Travel Order request (REQ 03)</li>
<li>System:
<ul>
<li>Creates <code>TravelOrderRequested</code> event</li>
<li>Follows approval workflow from SFD Module 1</li>
</ul>
</li>
<li>Upon approval:
<ul>
<li>Creates <code>TravelOrderApproved</code> event</li>
<li>Updates status to &quot;DI&quot; (day) or &quot;DN&quot; (night)</li>
<li>Sends to TKS for validation</li>
</ul>
</li>
</ol>
<h4 id="external-work-te-workflow">External Work (TE) Workflow</h4>
<ol>
<li>Team Leader/Requester initiates External Work request (REQ 02)</li>
<li>System:
<ul>
<li>Creates <code>ExternalWorkRequested</code> event</li>
<li>Follows approval workflow from SFD Module 1</li>
</ul>
</li>
<li>Upon approval:
<ul>
<li>Creates <code>ExternalWorkApproved</code> event</li>
<li>Updates status to &quot;TE&quot;</li>
<li>Sends to TKS for validation</li>
</ul>
</li>
</ol>
<h4 id="unplanned-tlo-ctn-workflow">Unplanned TLO (CTN) Workflow</h4>
<ol>
<li>Team Leader initiates Unplanned TLO request</li>
<li>System:
<ul>
<li>Creates <code>UnplannedTLORequested</code> event</li>
<li>Captures reason (raw material shortage, technical issue)</li>
</ul>
</li>
<li>Complex approval workflow:
<ul>
<li>Team Leader → Shift Leader → Coordinator → Manager</li>
</ul>
</li>
<li>Upon approval:
<ul>
<li>Creates <code>UnplannedTLOApproved</code> event</li>
<li>Updates status to &quot;CTN&quot;</li>
<li>Notifies TKS agent for validation</li>
<li>Updates Optitime system</li>
</ul>
</li>
</ol>
<h4 id="workplace-accident-at-workflow">Workplace Accident (AT) Workflow</h4>
<ol>
<li>Nurse submits workplace accident request
<ul>
<li>Includes operator ID, start date, end date</li>
<li>Option to extend absence period</li>
<li>Scans medical proof</li>
</ul>
</li>
<li>System:
<ul>
<li>Creates <code>WorkplaceAccidentReported</code> event</li>
<li>For DH: Notifies TL, SL, TKS, Social Services, Safety responsible</li>
<li>For IH/IS: Notifies N+1, Clerk, TKS, Social Services, Safety responsible</li>
</ul>
</li>
<li>Status automatically updated to &quot;AT&quot;</li>
<li>Extensions can be initiated by nurse, TL, N+1, clerk, or HR</li>
</ol>
<h4 id="maternity-leave-mt-workflow">Maternity Leave (MT) Workflow</h4>
<ol>
<li>Team Leader submits request using &quot;Annual Leave&quot; template (REQ 07)
<ul>
<li>Specifies reason as &quot;Maternity&quot;</li>
</ul>
</li>
<li>System:
<ul>
<li>Creates <code>MaternityLeaveRequested</code> event</li>
<li>For DH: Team Leader manages operators</li>
<li>For other employees: Department Clerk manages</li>
</ul>
</li>
<li>Follows country-specific approval workflow</li>
<li>Upon approval:
<ul>
<li>Creates <code>MaternityLeaveApproved</code> event</li>
<li>TKS updates system with leave start date</li>
<li>Updates status to &quot;MT&quot;</li>
<li>Notifies relevant stakeholders</li>
</ul>
</li>
</ol>
<h4 id="suspension-ap-workflow">Suspension (AP) Workflow</h4>
<ol>
<li>Employee Relations Supervisor submits suspension request</li>
<li>System:
<ul>
<li>Creates <code>SuspensionRequested</code> event</li>
<li>Automatically notifies TL, TKS agent, and Shift Leader</li>
</ul>
</li>
<li>Status automatically updated to &quot;AP&quot;</li>
<li>No further approval needed (pre-authorized)</li>
</ol>
<h4 id="exceptional-leave-ae-workflow">Exceptional Leave (AE) Workflow</h4>
<ol>
<li>Team Leader submits exceptional leave request (e.g., COVID)</li>
<li>Approval workflow:
<ul>
<li>Shift Leader → HR Manager</li>
</ul>
</li>
<li>System:
<ul>
<li>Creates <code>ExceptionalLeaveApproved</code> event upon final approval</li>
<li>Notifies TKS Agent</li>
<li>Updates status to &quot;AE&quot;</li>
</ul>
</li>
</ol>
<h3 id="integration-with-working-plan-for-planned-tlo-ctp">Integration with Working Plan for Planned TLO (CTP)</h3>
<ol>
<li>
<p>Capacity Study process:</p>
<ul>
<li>Working Plan microservice conducts Capacity Study</li>
<li>Develops Production Plan with TLO requirements</li>
<li>Shares work plan with Shift Leaders</li>
</ul>
</li>
<li>
<p>TLO Assignment process:</p>
<ul>
<li>Shift Leader assigns operators to TLOs for N+1 period</li>
<li>Assignments recorded in Working Plan system</li>
<li>Exported to HeadCount as ShiftDocument</li>
<li>For DH: Shift Leader manages operators</li>
<li>For IS/IH: Department clerk handles planning</li>
<li>Follows work plan update rules</li>
</ul>
</li>
<li>
<p>Status Update process:</p>
<ul>
<li>ShiftDocument imported at shift start</li>
<li>Operators with TLO assignments pre-marked as &quot;CTP&quot;</li>
<li>Team Leader can modify during shift if needed</li>
<li>Changes synchronized with Optitime system</li>
</ul>
</li>
</ol>
<h3 id="special-case-workflows">Special Case Workflows</h3>
<h4 id="badge-lossforgetting-process">Badge Loss/Forgetting Process</h4>
<ol>
<li>
<p>If badge forgotten/lost:</p>
<ul>
<li>Operator manually enters ID at bus terminal</li>
<li>If not scheduled on bus, access denied</li>
<li>Automatic email sent to guardhouse</li>
<li>Operator reports to guardhouse on arrival</li>
</ul>
</li>
<li>
<p>For badge loss:</p>
<ul>
<li>Team Leader raises Declaration of Loss of Badge request (REQ 08)</li>
<li>Includes sworn statement from operator</li>
<li>Information sent to TKS Responsible</li>
</ul>
</li>
</ol>
<h4 id="delay-handling-process">Delay Handling Process</h4>
<ol>
<li>
<p>If operator delayed due to transport/personal issue:</p>
<ul>
<li>Status updated to &quot;Absent&quot; not &quot;Delayed&quot;</li>
<li>Updated status reflected in CW</li>
</ul>
</li>
<li>
<p>&quot;Delayed&quot; status only applicable with approved entry authorization:</p>
<ul>
<li>Triggered when operator exceeds 1 min for starting shift</li>
<li>Team Leader initiates &quot;autorisation d'entrée&quot; request</li>
<li>Once approved, guardhouse notified to grant access</li>
<li>Status updated to &quot;R&quot;</li>
</ul>
</li>
</ol>
<h4 id="prolonged-absence-alert-process">Prolonged Absence Alert Process</h4>
<ol>
<li>System monitors consecutive absence days</li>
<li>If operator absent without justification for &gt; 4 days:
<ul>
<li>Alert sent to HR department</li>
<li>Flagged as potential job abandonment</li>
<li>Country-specific legal regulations applied</li>
</ul>
</li>
</ol>
<h4 id="status-correction-process">Status Correction Process</h4>
<ol>
<li>Team Leader can modify operator status until deadline:
<ul>
<li>D+1 at 7:00 AM for night shift</li>
</ul>
</li>
<li>If deadline passed:
<ul>
<li>Team Leader submits Correction of Clocking request</li>
<li>Scans absence justification</li>
<li>Requires TKS approval</li>
<li>Status updated after approval</li>
</ul>
</li>
</ol>
<h4 id="shift-change-request-process">Shift Change Request Process</h4>
<ol>
<li>Team Leader submits shift change request to:
<ul>
<li>Shift Leader</li>
<li>TKS supervisor</li>
</ul>
</li>
<li>For IH category:
<ul>
<li>Requires approval from employee's N+1</li>
<li>Once approved, processed in TKS</li>
<li>Change reflected in Optitime system</li>
</ul>
</li>
</ol>
<h2 id="country-specific-implementation-emeana">Country-Specific Implementation EMEA/NA</h2>
<h3 id="regional-matrix">Regional Matrix</h3>
<table>
<thead>
<tr>
<th>Feature</th>
<th>Morocco</th>
<th>Tunisia</th>
<th>Turkey</th>
<th>Portugal</th>
<th>Poland</th>
<th>Serbia</th>
<th>North Americas</th>
</tr>
</thead>
<tbody>
<tr>
<td>Visual check</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✗</td>
<td>✗</td>
<td>✗</td>
</tr>
<tr>
<td>TLO</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✗</td>
<td>✗</td>
</tr>
<tr>
<td>Unplanned TLO</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✗</td>
</tr>
<tr>
<td>Payroll claims</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✓</td>
<td>✗</td>
<td>✓</td>
<td>✓</td>
</tr>
</tbody>
</table>
<h3 id="implementation-strategy">Implementation Strategy</h3>
<ol>
<li>
<p><strong>Configuration Repository</strong>:</p>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> CountryConfig {
  countryCode: <span class="hljs-built_in">string</span>;
  features: {
    visualCheck: <span class="hljs-built_in">boolean</span>;
    tlo: <span class="hljs-built_in">boolean</span>;
    unplannedTlo: <span class="hljs-built_in">boolean</span>;
    payrollClaims: <span class="hljs-built_in">boolean</span>;
    absenceAuthorization: <span class="hljs-built_in">boolean</span>;
    badgeLoss: <span class="hljs-built_in">boolean</span>;
  };
  workflows: {
    visualCheckApproval: WorkflowDefinition;
    tloApproval: WorkflowDefinition;
    absenceApproval: WorkflowDefinition;
    <span class="hljs-comment">// ... other workflow definitions</span>
  };
}
</div></code></pre>
</li>
<li>
<p><strong>Feature Toggles</strong>:</p>
<ul>
<li>Each country configuration stored in Cosmos DB</li>
<li>Feature flags control availability of functionality</li>
<li>Dynamic workflow routing based on country settings</li>
</ul>
</li>
<li>
<p><strong>Workflow Engine Parameters</strong>:</p>
<ul>
<li>Country-specific approval chains</li>
<li>Different validation requirements per region</li>
<li>Custom notification rules</li>
<li>Region-specific documentation requirements</li>
</ul>
</li>
<li>
<p><strong>UI Adaptations</strong>:</p>
<ul>
<li>Dynamic menu items based on country features</li>
<li>Country-specific form fields</li>
<li>Localized validation messages</li>
<li>Regional compliance warnings</li>
</ul>
</li>
</ol>
<h2 id="api-endpoints">API Endpoints</h2>
<table>
<thead>
<tr>
<th>Endpoint</th>
<th>Method</th>
<th>Description</th>
<th>Required Role</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>/api/shift/start</code></td>
<td>POST</td>
<td>Start shift process</td>
<td>TeamLeader, ShiftLeader</td>
</tr>
<tr>
<td><code>/api/shift/close</code></td>
<td>POST</td>
<td>Close shift process</td>
<td>TeamLeader, ShiftLeader</td>
</tr>
<tr>
<td><code>/api/operator/status</code></td>
<td>POST</td>
<td>Update operator status</td>
<td>TeamLeader, ShiftLeader</td>
</tr>
<tr>
<td><code>/api/operator/status</code></td>
<td>GET</td>
<td>Get operator status</td>
<td>All authenticated</td>
</tr>
<tr>
<td><code>/api/dashboard</code></td>
<td>GET</td>
<td>Get dashboard data</td>
<td>ShiftLeader, Manager</td>
</tr>
</tbody>
</table>
<h2 id="integration-patterns">Integration Patterns</h2>
<h3 id="service-bus-topics">Service Bus Topics</h3>
<ol>
<li>
<p><strong>Calendar Events Topic</strong></p>
<ul>
<li>YearInitialized</li>
<li>HolidayCreated</li>
<li>WeekSummaryCalculated</li>
</ul>
</li>
<li>
<p><strong>Working Plan Events Topic</strong></p>
<ul>
<li>PlanCreated</li>
<li>ShiftAssigned</li>
<li>OperatorAssigned</li>
</ul>
</li>
<li>
<p><strong>HeadCount Events Topic</strong></p>
<ul>
<li>ShiftStarted</li>
<li>VisualCheckRecorded</li>
<li>StatusChangeRequested</li>
</ul>
</li>
</ol>
<h3 id="event-flow">Event Flow</h3>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant ACS as Annual Calendar Service
    participant WPS as Working Plan Service
    participant HCS as HeadCount Service
    participant SB as Service Bus
    participant DB as Databases

    ACS->>SB: Publish Calendar Events
    SB->>WPS: Consume Calendar Events
    WPS->>DB: Update Working Plans

    WPS->>SB: Publish Plan Events
    SB->>HCS: Consume Plan Events
    HCS->>DB: Update Operator Assignments

    HCS->>SB: Publish Status Events
    SB->>WPS: Consume Status Events
    WPS->>DB: Update Plan Status
</div></code></pre>
<h2 id="security--authorization">Security &amp; Authorization</h2>
<h3 id="role-based-access-control">Role-Based Access Control</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">enum</span> Role {
    ADMIN = <span class="hljs-string">"ADMIN"</span>,
    PLANNER = <span class="hljs-string">"PLANNER"</span>,
    TEAM_LEADER = <span class="hljs-string">"TEAM_LEADER"</span>,
    SHIFT_LEADER = <span class="hljs-string">"SHIFT_LEADER"</span>,
    DEPARTMENT_CLERK = <span class="hljs-string">"DEPARTMENT_CLERK"</span>,
    TKS_AGENT = <span class="hljs-string">"TKS_AGENT"</span>,
    VIEWER = <span class="hljs-string">"VIEWER"</span>
}

<span class="hljs-keyword">const</span> rolePermissions = {
    [Role.ADMIN]: [
        <span class="hljs-string">"manage:calendar"</span>,
        <span class="hljs-string">"manage:workingplan"</span>,
        <span class="hljs-string">"manage:headcount"</span>
    ],
    [Role.TEAM_LEADER]: [
        <span class="hljs-string">"manage:operators"</span>,
        <span class="hljs-string">"read:workingplan"</span>,
        <span class="hljs-string">"perform:visualcheck"</span>,
        <span class="hljs-string">"start:shift"</span>,
        <span class="hljs-string">"close:shift"</span>
    ],
    [Role.SHIFT_LEADER]: [
        <span class="hljs-string">"validate:shifts"</span>,
        <span class="hljs-string">"manage:teams"</span>,
        <span class="hljs-string">"emergency:update"</span>,
        <span class="hljs-string">"read:dashboard"</span>
    ]
};
</div></code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="database-optimization">Database Optimization</h3>
<ol>
<li>
<p><strong>Cosmos DB</strong></p>
<ul>
<li>Efficient partition keys</li>
<li>Optimized indexing policies</li>
<li>Change Feed processors</li>
<li>Multi-tenant partitioning strategies</li>
</ul>
</li>
<li>
<p><strong>SQL Database</strong></p>
<ul>
<li>Indexed views for common queries</li>
<li>Optimized stored procedures</li>
<li>Proper indexing strategy</li>
</ul>
</li>
</ol>
<h2 id="error-handling">Error Handling</h2>
<pre class="hljs"><code><div><span class="hljs-keyword">interface</span> ErrorResponse {
    code: <span class="hljs-built_in">string</span>;
    message: <span class="hljs-built_in">string</span>;
    details?: <span class="hljs-built_in">any</span>;
    correlationId: <span class="hljs-built_in">string</span>;
    timestamp: <span class="hljs-built_in">string</span>;
}

<span class="hljs-keyword">enum</span> ErrorCode {
    VALIDATION_ERROR = <span class="hljs-string">'VALIDATION_ERROR'</span>,
    NOT_FOUND = <span class="hljs-string">'NOT_FOUND'</span>,
    UNAUTHORIZED = <span class="hljs-string">'UNAUTHORIZED'</span>,
    CONFLICT = <span class="hljs-string">'CONFLICT'</span>,
    INTERNAL_ERROR = <span class="hljs-string">'INTERNAL_ERROR'</span>
}
</div></code></pre>
<h2 id="monitoring--logging">Monitoring &amp; Logging</h2>
<h3 id="metrics">Metrics</h3>
<ul>
<li>Service health metrics</li>
<li>API response times</li>
<li>Database performance</li>
<li>Event processing latency</li>
<li>Cache hit rates</li>
</ul>
<pre class="hljs"><code><div></div></code></pre>

</body>
</html>
