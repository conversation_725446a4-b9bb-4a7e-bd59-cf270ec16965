# Working Plan Microservice -  Architecture Documentation

## 1. Introduction

This document presents the architecture for the Working Plan Microservice, responsible for managing weekly work plans, shift assignments, and operator scheduling across different manufacturing sites. The redesign implements an optimized Sourced CQRS (SCQRS) pattern with advanced event-driven architecture to improve scalability, auditability, and performance.

## 2. Architecture Overview

The enhanced architecture builds upon the existing design while introducing several key improvements:

- **Multi-tenant Support**: Added country and region support for global deployment
- **Advanced Domain Modeling**: Refined entities with clear boundaries and relationships
- **Enhanced Event Sourcing**: Optimized event store with efficient partition keys
- **Improved Projections**: Specialized read models for different query patterns
- **Integration Framework**: Connectors for HR systems, production planning, and transport planning
- **Real-time Notifications**: Event-driven notification system for stakeholders

## 3. Enhanced Data Model

### 3.1. Core Domain Entity

#### Shift Document

```typescript
interface ShiftDocument {
    id: string;                    // "SHIFT_FR_SITE1_2025_09_MON_M"
    type: "Shift";
    country: string;               // "FR", "DE", etc.
    region: string;                // "EMEA", "APAC", etc.
    site: string;                  // "SITE1"
    week: string;                  // "2025_09"
    date: string;                  // "2025-02-24"
    dayOfWeek: string;             // "Monday"
    shiftType: ShiftType;          // "Morning" | "Afternoon" | "Night"
    code: string;                  // "M", "A", "N"
    startTime: string;             // "06:00"
    endTime: string;               // "14:00"
    status: ShiftStatus;           // "Draft" | "Planned" | "Published" | "Completed"
    totalPlannedOperators: number; // Total operators across all teams
    teams: {
        project: string;               // Project identifier
        team_name: string;
        family_zone: string;       // "FRONT DOOR 526", "UNDERBODY R 526", etc.
        line: string;              // Production line
        activityType: string;      // "WP", "SA", "SA-S", "SA-P", "SA-B"
        teamleader_id: string;
        teamleader_name: string;
        totalOperators: number;    // Total operators for this team
        assignedOperators: {
            operator_id: string;
            operator_name: string;
            skills: string[];
            status: string;        // "Active", "TLO", "OT", etc.
        }[];
        unassignedOperators: {         // Operators not assigned to any team (TLO)
        operator_id: string;
        operator_name: string;
        skills: string[];
        previousTeam: string;      // Last team assignment before unassignment
        reasonCode: string;        // Why operator was unassigned
    }[];
    }[];
}
```

### 3.2. Event Store Model

```typescript
interface EventDocument {
    id: string;                    // UUID
    type: "Event";
    eventType: EventType;          // Specific event type
    aggregateId: string;           // ID of the entity this event affects
    aggregateType: string;         // "Shift", "Team", "Operator"
    country: string;               // For partition key
    site: string;                  // For partition key
    week: string;                  // For weekly grouping
    timestamp: string;             // When the event occurred
    userId: string;                // Who triggered this event
    payload: any;                     // Event-specific data
}
```

## 4. Detailed Event Catalog

The table below details all possible events in the system, their triggers, effects, and complete payload structures:

| Event Type | Description | Trigger | Effect | Payload Structure |
|------------|-------------|---------|--------|-------------------|
| `SHIFT_CREATED` | New shift created | Auto-create based on calendar | Creates shift document | ```{ shiftId: string; country: string; site: string; week: string; date: string; dayOfWeek: string; shiftType: string; code: string; startTime: string; endTime: string; category: string; departmentType: string; project: string; status: "Draft"; }``` |
| `SHIFT_UPDATED` | Shift details modified | User modifies shift details | Updates shift document | ```{ shiftId: string; changedFields: { fieldName: string; oldValue: any; newValue: any; }[]; }``` |
| `SHIFT_STATUS_CHANGED` | Shift status changed | User/system changes status | Updates shift status | ```{ shiftId: string; oldStatus: string; newStatus: string; reason: string; }``` |
| `TEAM_ASSIGNED` | Team assigned to shift | User assigns team | Adds team to shift | ```{ shiftId: string; team: { team_id: string; team_name: string; family_zone: string; line: string; activityType: string; totalOperators: 0; }; }``` |
| `TEAM_REMOVED` | Team removed from shift | User removes team | Removes team from shift | ```{ shiftId: string; teamId: string; reason: string; }``` |
| `TEAM_UPDATED` | Team details updated | User modifies team details | Updates team info | ```{ shiftId: string; teamId: string; changedFields: { fieldName: string; oldValue: any; newValue: any; }[]; }``` |
| `TEAMLEADER_ASSIGNED` | Team leader assigned | User assigns team leader | Sets team leader | ```{ shiftId: string; teamId: string; teamleader: { teamleader_id: string; teamleader_name: string; }; }``` |
| `TEAMLEADER_REMOVED` | Team leader removed | User removes team leader | Unsets team leader | ```{ shiftId: string; teamId: string; teamleaderId: string; reason: string; }``` |
| `OPERATOR_ASSIGNED` | Operator assigned | User assigns operator | Adds operator to team | ```{ shiftId: string; teamId: string; operator: { operator_id: string; operator_name: string; skills: string[]; status: string; }; }``` |
| `OPERATOR_REMOVED` | Operator removed | User removes operator | Removes operator | ```{ shiftId: string; teamId: string; operatorId: string; reason: string; }``` |
| `OPERATOR_MOVED` | Operator moved teams | User moves operator | Updates team assignments | ```{ shiftId: string; operatorId: string; sourceTeamId: string; targetTeamId: string; }``` |
| `OPERATOR_TO_TLO` | Operator moved to TLO | User marks as unassigned | Moves to unassigned list | ```{ shiftId: string; operator: { operator_id: string; operator_name: string; skills: string[]; previousTeam: string; reasonCode: string; }; }``` |
| `OPERATOR_FROM_TLO` | Operator from TLO | User reassigns operator | Moves from unassigned | ```{ shiftId: string; teamId: string; operatorId: string; previousStatus: string; }``` |
| `ACTIVITY_TYPE_CHANGED` | Activity type changed | User changes activity | Updates team activity | ```{ shiftId: string; teamId: string; oldType: string; newType: string; }``` |
| `IMPORT_CALENDAR` | Calendar imported | HR system integration | Creates shifts for week | ```{ country: string; site: string; week: string; source: string; importedShifts: string[]; calendarMetadata: any; }``` |
| `IMPORT_OPERATORS` | Operators imported | HR system integration | Updates operator pool | ```{ country: string; site: string; source: string; operatorCount: number; importDate: string; }``` |
| `SHIFT_PUBLISHED` | Shift plan published | Plan validated after deadline | Notifies stakeholders | ```{ shiftId: string; publishedBy: string; notifiedStakeholders: string[]; timestamp: string; }``` |
| `EMERGENCY_PLAN_CHANGE` | Emergency change | Last-minute change | Updates plan with exception | ```{ shiftId: string; reason: string; approvedBy: string; changes: { field: string; oldValue: any; newValue: any; }[]; emergencyCode: string; }``` |
| `TRANSPORT_PLAN_UPDATED` | Transport plan updated | Transport update | Updates shift plans | ```{ transportPlanId: string; affectedShifts: string[]; changeType: string; updatedBy: string; }``` |
| `SHIFT_VALIDATED` | Shift plan validated | User validates plan | Finalizes plan | ```{ shiftId: string; validatedBy: string; timestamp: string; completeStatus: boolean; validationErrors: string[]; }``` |
| `WORK_PLAN_EXPORTED` | Work plan exported | User/system exports | Creates export | ```{ week: string; site: string; country: string; exportedBy: string; exportFormat: string; destinationSystem: string; shiftIds: string[]; }``` |


## 6. Cosmos DB Configuration

### 6.1. Containers and Partition Keys

| Container Name | Purpose | Partition Key |Indexing Policy |
|----------------|---------|---------------|-----|-----------------|
| `events` | Event store | `/country,/site` | Optimize for timestamp and aggregate queries |
| `shifts` | Shift read models | `/country,/site` |  Optimize for date and week queries |
| `teamLeader_team_crew` | Team definitions | `customer` |  Default |
| `operators` | Operator definitions | `/country,/site` | Optimize for skill queries |


### 6.2. Sql Read tables
CalendarEvents


## 7. Implementation Flows

### 7.1. DH Category Planning Flow

```mermaid
sequenceDiagram
    actor SL as Shift Leader
    participant UI as Web UI
    participant API as Command API
    participant ES as Event Store
    participant RM as Read Models
    participant NS as Notification Service
    
    SL->>UI: Access calendar interface
    UI->>API: GET /weeks/{weekId}/calendar
    API->>RM: Get calendar data
    RM-->>API: Return calendar
    API-->>UI: Display calendar
    
    SL->>UI: Select week and shift
    
    SL->>UI: Assign team leaders to zones
    UI->>API: POST /shifts/{id}/teams
    API->>ES: Save TeamAssigned event
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    UI->>API: POST /shifts/{id}/teams/{teamId}/teamleader
    API->>ES: Save TeamleaderAssigned event
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    SL->>UI: Assign operators to teams
    UI->>API: POST /shifts/{id}/teams/{teamId}/operators
    API->>ES: Save OperatorAssigned events
    ES-->>API: Confirm
    API-->>UI: Update UI with team members
    
    SL->>UI: Complete planning
    UI->>API: POST /shifts/{id}/validate
    API->>ES: Save ShiftValidated event
    ES->>RM: Update read models
    API->>NS: Trigger notifications
    NS-->>SL: Send email notification
    NS-->>+Team Leaders: Send notifications
    API-->>UI: Show confirmation
```

### 7.2. IH/IS Category Planning Flow

```mermaid
sequenceDiagram
    actor CL as Department Clerk
    participant UI as Web UI
    participant API as Command API
    participant ES as Event Store
    participant RM as Read Models
    participant NS as Notification Service
    
    CL->>UI: Access calendar interface
    UI->>API: GET /weeks/{weekId}/calendar?category=IS
    API->>RM: Get calendar data
    RM-->>API: Return calendar
    API-->>UI: Display calendar
    
    CL->>UI: Select week
    
    CL->>UI: Assign employees to shifts
    UI->>API: POST /shifts/{id}/operators
    API->>ES: Save OperatorAssigned events
    ES-->>API: Confirm
    API-->>UI: Update UI
    
    CL->>UI: Complete planning
    UI->>API: POST /shifts/{id}/validate
    API->>ES: Save ShiftValidated event
    ES->>RM: Update read models
    API->>NS: Trigger notifications
    NS-->>CL: Send email notification
    NS-->>+Managers: Send notifications
    API-->>UI: Show confirmation
```

### 7.3. Emergency Update Flow

```mermaid
sequenceDiagram
    actor SL as Shift Leader/Clerk
    participant UI as Web UI
    participant API as Command API
    participant V as Validators
    participant ES as Event Store
    participant RM as Read Models
    participant NS as Notification Service
    
    SL->>UI: Access shift plan
    UI->>API: GET /shifts/{id}
    API->>RM: Get shift data
    RM-->>API: Return shift
    API-->>UI: Display shift
    
    SL->>UI: Request emergency update
    UI->>API: POST /shifts/{id}/emergency-update
    API->>V: Validate against rules
    V-->>API: Validation result
    
    alt Valid Update
        API->>ES: Save EmergencyPlanChange event
        ES->>RM: Update read models
        API->>NS: Trigger emergency notifications
        NS-->>+Transport: Send urgent update
        API-->>UI: Show confirmation
    else Invalid Update
        API-->>UI: Show error with rules
    end
```

### 7.4. Import Calendar Flow

```mermaid
sequenceDiagram
    participant HR as HR System
    participant IG as Integration Gateway
    participant PS as Processing Service
    participant ES as Event Store
    participant RM as Read Models
    participant NS as Notification Service
    
    HR->>IG: Send calendar data
    IG->>PS: Process calendar import
    PS->>PS: Validate calendar format
    PS->>ES: Save ImportCalendar event
    
    loop For each shift
        PS->>ES: Save ShiftCreated event
    end
    
    ES->>RM: Update read models
    PS->>NS: Send import completed notification
    NS-->>+Shift Leaders: Notify calendar update
```

## 8. API Endpoints

### 8.1. Command API

| Endpoint | Method | Description | Request Body Example |
|----------|--------|-------------|-----------------|
| `/api/v1/shifts` | POST | Create new shift | `{ "country": "FR", "site": "SITE1", "date": "2025-05-06", "shiftType": "Morning" }` |
| `/api/v1/shifts/{id}/teams` | POST | Add team to shift | `{ "team_id": "TEAM123", "team_name": "Assembly A", "family_zone": "FRONT DOOR 526", "line": "L1", "activityType": "WP" }` |
| `/api/v1/shifts/{id}/teams/{teamId}` | PUT | Update team | `{ "activityType": "SA-P", "line": "L2" }` |
| `/api/v1/shifts/{id}/teams/{teamId}` | DELETE | Remove team | N/A |
| `/api/v1/shifts/{id}/teams/{teamId}/teamleader` | POST | Set team leader | `{ "teamleader_id": "EMP789", "teamleader_name": "John Smith" }` |
| `/api/v1/shifts/{id}/teams/{teamId}/operators` | POST | Add operator | `{ "operator_id": "EMP456", "operator_name": "Jane Doe", "skills": ["cutting", "assembly"] }` |
| `/api/v1/shifts/{id}/teams/{teamId}/operators/{operatorId}` | DELETE | Remove operator | N/A |
| `/api/v1/shifts/{id}/operators/tlo` | POST | Move operator to TLO | `{ "operator_id": "EMP456", "previousTeam": "TEAM123", "reasonCode": "SKILL_MISMATCH" }` |
| `/api/v1/shifts/{id}/validate` | POST | Validate shift plan | `{ "validateAllocation": true }` |
| `/api/v1/shifts/{id}/emergency-update` | POST | Emergency update | `{ "reason": "Production line change", "approvedBy": "SUPERVISOR1", "changes": [{"field": "team.line", "value": "L3"}] }` |
| `/api/v1/import/calendar` | POST | Import calendar | `{ "country": "FR", "site": "SITE1", "week": "2025_09", "source": "HR_SYSTEM" }` |

### 8.2. Query API

| Endpoint | Method | Description | Query Parameters |
|----------|--------|-------------|-----------------|
| `/api/v1/sites/{site}/shifts` | GET | Get shifts for site | `country`, `week?`, `date?`, `category?` |
| `/api/v1/shifts/{id}` | GET | Get shift details | N/A |
| `/api/v1/sites/{site}/calendar/{week}` | GET | Get calendar view | `country`, `category?` |
| `/api/v1/teams` | GET | Get teams | `country`, `site`, `family?` |
| `/api/v1/operators` | GET | Get operators | `country`, `site`, `status?`, `skill?` |
| `/api/v1/shifts/{id}/unassigned` | GET | Get TLO operators | N/A |
| `/api/v1/reports/utilization` | GET | Get utilization report | `country`, `site`, `week`, `department?` |

## 9. Integration Points

### 9.1. HR System Integration

#### 9.1.1. Import Calendar API

**Endpoint:** `/api/v1/integration/hr/calendar`  
**Method:** POST  
**Payload:**
```json
{
  "country": "FR",
  "site": "SITE1",
  "week": "2025_09",
  "shifts": [
    {
      "date": "2025-02-24",
      "shiftType": "Morning",
      "startTime": "06:00",
      "endTime": "14:00",
      "departmentType": "Final Assembly",
      "project": "P526"
    },
    // More shifts...
  ]
}
```

#### 9.1.2. Import Operators API

**Endpoint:** `/api/v1/integration/hr/operators`  
**Method:** POST  
**Payload:**
```json
{
  "country": "FR",
  "site": "SITE1",
  "operators": [
    {
      "operator_id": "EMP123",
      "operator_name": "John Smith",
      "skills": ["assembly", "testing"],
      "availability": {
        "week": "2025_09",
        "unavailableDates": ["2025-02-25", "2025-02-26"],
        "reasonCodes": ["TRAINING", "VACATION"]
      }
    },
    // More operators...
  ]
}
```

### 9.2. Production Planning Integration

#### 9.2.1. Production Schedule API

**Endpoint:** `/api/v1/integration/production/schedule`  
**Method:** POST  
**Payload:**
```json
{
  "country": "FR",
  "site": "SITE1",
  "week": "2025_09",
  "productionLines": [
    {
      "line": "L1",
      "family": "FRONT DOOR 526",
      "dailyRequirements": [
        {
          "date": "2025-02-24",
          "shifts": [
            {
              "shiftType": "Morning",
              "targetUnits": 450,
              "operatorsRequired": 12
            },
            // More shifts...
          ]
        },
        // More days...
      ]
    },
    // More lines...
  ]
}
```

### 9.3. Transport Planning Integration

#### 9.3.1. Export Work Plan API

**Endpoint:** `/api/v1/integration/transport/work-plan`  
**Method:** GET  
**Query Parameters:** `country`, `site`, `week`  
**Response:**
```json
{
  "country": "FR",
  "site": "SITE1",
  "week": "2025_09",
  "generatedAt": "2025-02-21T17:00:00Z",
  "shifts": [
    {
      "date": "2025-02-24",
      "shiftType": "Morning",
      "teams": [
        {
          "family_zone": "FRONT DOOR 526",
          "line": "L1",
          "operatorCount": 12,
          "teamleader": "John Smith"
        },
        // More teams...
      ]
    },
    // More shifts...
  ]
}
```

## 10. Business Rules and Validations

### 10.1. Planning Timeline Rules

| Rule ID | Rule Description | Validation Logic |
|---------|------------------|-----------------|
| TR-01 | Initial planning deadline: Friday 5 PM | `if (currentTime < fridayDeadline && shiftWeek == nextWeek) allow else deny` |
| TR-02 | Weekend planning deadline: Friday 5 PM | `if (currentTime < fridayDeadline && isWeekendShift && shiftWeek == nextWeek) allow else deny` |
| TR-03 | J-1 modification: before 5 PM | `if (currentTime < dailyDeadline && shiftDate == tomorrow) allow else deny` |
| TR-04 | Evening shift same-day mod: 4h before | `if (currentTime < (shiftStart - 4h) && shiftType == "Evening" && shiftDate == today) allow else deny` |
| TR-05 | Night shift same-day mod: before 5 PM | `if (currentTime < dailyDeadline && shiftType == "Night" && shiftDate == today) allow else deny` |
| TR-06 | Overtime addition: before 11 AM | `if (currentTime < overtimeDeadline && shiftDate == today) allow else deny` |

### 10.2. Validation Rules

| Rule ID | Rule Description | Validation Logic |
|---------|------------------|-----------------|
| VR-01 | Operator eligibility check | `if (operator.status not in ["SICK_LEAVE", "DISCIPLINARY"]) allow else deny` |
| VR-02 | Transport planning auth | `if (user.role == "TRANSPORT_RESPONSIBLE") allow else deny` |
| VR-03 | Team skill requirements | `if (team.requiredSkills.every(skill => team.operators.some(op => op.skills.includes(skill)))) allow else deny` |
| VR-04 | Special activity authorization | `if (activityType.startsWith("SA") && user.hasPermission("SPECIAL_ACTIVITY")) allow else deny` |
| VR-05 | Min/max staffing levels | `if (team.operatorCount >= team.minOperators && team.operatorCount <= team.maxOperators) allow else deny` |
| VR-06 | No overlapping assignments | `if (!hasOperatorOverlappingShift(operator, shift)) allow else deny` |

## 11. Performance Considerations

### 11.1. Event Store Optimization

- **Partition Strategy**: Use composite partition key (`country,site`) for better distribution
- **Index on Week**: Create secondary index on `week` for frequent weekly queries
- **Event Compression**: Compress event data for events older than 30 days
- **Change Feed Processing**: Use Cosmos DB change feed for event processing

