# Connected Workers Platform - Module 3 Low-Level Design

## Table of Contents
1. [Overview](#1-overview)
2. [System Architecture](#2-system-architecture)
3. [Core Microservices](#3-core-microservices)
   - [Annual Calendar Service](#31-annual-calendar-service)
   - [Working Plan Service](#32-working-plan-service)
   - [TKS Service](#33-tks-service)
   - [Operator Assignment Service](#34-operator-assignment-service)
4. [Integration Patterns](#4-integration-patterns)
5. [Data Models](#5-data-models)
6. [Event-Driven Architecture](#6-event-driven-architecture)
7. [Security & Authorization](#7-security--authorization)
8. [Performance Considerations](#8-performance-considerations)

## 1. Overview

Module 3 implements a microservices architecture focused on workforce planning, scheduling, tracking, and assignment management. The module consists of four core microservices:

1. **Annual Calendar Service**: Manages calendar data, holidays, and working days
2. **Working Plan Service**: Handles weekly work plans and shift assignments
3. **TKS Service**: Manages real-time operator tracking and status
4. **Operator Assignment Service**: Manages hierarchical operator assignments and reporting structures

### Key Features
- Comprehensive calendar management with holiday tracking
- Dynamic work plan generation and management
- Real-time operator status tracking
- Multi-tenant support with regional configurations
- Event-driven architecture with CQRS patterns
- Hierarchical operator assignment management

## 2. System Architecture

```mermaid
graph TD
    subgraph "Client Layer"
        UI[Web UI]
        Mobile[Mobile Apps]
    end

    subgraph "API Gateway"
        APIM[Azure API Management]
    end

    subgraph "Core Services"
        ACS[Annual Calendar Service]
        WPS[Working Plan Service]
        TKS[TKS Service]
        OAS[Operator Assignment Service]
        SB[Service Bus]
    end

    subgraph "Data Layer"
        SQLDB[(SQL Database)]
        CDB[(Cosmos DB)]
        Redis[(Redis Cache)]
    end

    UI --> APIM
    Mobile --> APIM
    APIM --> ACS
    APIM --> WPS
    APIM --> TKS
    APIM --> OAS
    
    ACS --> SB
    WPS --> SB
    TKS --> SB
    OAS --> SB
    
    ACS --> SQLDB
    WPS --> CDB
    TKS --> CDB
    OAS --> CDB
    
    ACS --> Redis
    WPS --> Redis
    TKS --> Redis
    OAS --> Redis
```

### Technology Stack

| Component | Technology | Purpose |
|-----------|------------|----------|
| API Gateway | Azure API Management | Request routing, authentication |
| Message Bus | Azure Service Bus | Event processing, service communication |
| Primary Database | Azure Cosmos DB | Document storage for Working Plan, TKS & Assignment |
| Secondary Database | Azure SQL Database | Relational data for Calendar Service |
| Cache | Azure Redis Cache | Performance optimization |
| Authentication | Azure AD | Identity management |
| Monitoring | Application Insights | Telemetry and monitoring |

[Previous sections remain unchanged...]

### 3.4 Operator Assignment Service

#### Overview
The Operator Assignment service manages the operator assignment lifecycle within the manufacturing facility using event sourcing and CQRS patterns.

#### Key Components
1. Assignment Command Handler
2. Role & Hierarchy Validator
3. Event Store Manager
4. Assignment Query Service

#### Data Model (Cosmos DB)

```typescript
enum Role {
  TRAINER_RESPONSIBLE = "TRAINER_RESPONSIBLE",
  TRAINER = "TRAINER",
  SHIFT_LEADER = "SHIFT_LEADER",
  TEAM_LEADER = "TEAM_LEADER",
}

enum AssignmentEventType {
  AssignedToTrainer = "AssignedToTrainer",
  AssignedToShiftLeader = "AssignedToShiftLeader",
  AssignedToTeamLeader = "AssignedToTeamLeader",
}

interface AssignmentEvent {
  id: string;
        createdBy: string;
  type: AssignmentEventType;
  timestamp: Date;
  payload: {
    site: string;
    assignFrom: {
      id: string;
      role: Role;
      firstName: string;
      lastName: string;
      department: string;
    };
    assignTo: {
      id: string;
      role: Role;
      firstName: string;
      lastName: string;
      department: string;
    };
    operator: {
      operatorId: string;
      firstName: string;
      lastName: string;
      department: string;
    };
    };
}
```

#### Assignment Service Implementation

```typescript
interface IAssignmentService {
    assignOperators(command: AssignOperatorsCommand): Promise<CommandResult>;
    validateAssignment(command: AssignOperatorsCommand): Promise<ValidationResult>;
    getAssignmentHistory(operatorId: string): Promise<AssignmentEvent[]>;
}

class AssignmentService implements IAssignmentService {
    constructor(
        private readonly eventStore: IEventStore,
        private readonly validator: IAssignmentValidator,
        private readonly publisher: IEventPublisher
    ) {}

    async assignOperators(command: AssignOperatorsCommand): Promise<CommandResult> {
        // Implementation
    }

    // Additional methods
}
```

#### API Endpoints

```typescript
// Assignment API Endpoints
interface AssignmentAPI {
    // Create new assignment
    POST /api/v1/assignments
    Request: {
        assignmentType: "TRAINER" | "SHIFT_LEADER" | "TEAM_LEADER",
        site: string,
        assignFromId: string,
        assignToId: string,
        operatorsIds: string[]
    }
    Response: {
        success: boolean,
        errors: string[]
    }

    // Get assignment history
    GET /api/v1/assignments/history/{operatorId}
    Response: AssignmentEvent[]
}
```

#### Role-Based Permissions

| Action / Role | Trainer Responsible | Trainer | Shift Leader | Team Leader |
|---------------|:------------------:|:--------:|:------------:|:-----------:|
| View Own Operators | ✓ | ✓ | ✓ | ✓ |
| View Trainer's Operators | ✓ | - | - | - |
| View Shift Leader's Operators | - | ✓ | - | - |
| View Team Leader's Operators | - | - | ✓ | - |
| Assign to Team Leader | - | - | ✓ | - |
| Assign to Shift Leader | - | ✓ | - | - |
| Assign to Trainer | ✓ | - | - | - |

#### Assignment Flow

```mermaid
sequenceDiagram
    actor User
    participant API as API Gateway
    participant AS as Assignment Service
    participant VAL as Validator
    participant DB as Cosmos DB

    User->>API: POST /assignments
    API->>AS: Process Assignment
    AS->>VAL: Validate Assignment
    VAL-->>AS: Validation Result
    
    alt Validation Failed
        AS-->>API: Return Error
        API-->>User: 400 Bad Request
    else Validation Passed
        AS->>DB: Store Assignment Event
        DB-->>AS: Confirm Storage
        AS-->>API: Success Response
        API-->>User: 200 OK
    end
```

[Continue with sections 4-8 as before...]

## 4. Integration Patterns

### Service Bus Topics

1. **Calendar Events Topic**
   - YearInitialized
   - HolidayCreated
   - WeekSummaryCalculated

2. **Working Plan Events Topic**
   - PlanCreated
   - ShiftAssigned
   - OperatorAssigned

3. **TKS Events Topic**
   - BadgeScan
   - StatusChange
   - VisualCheck

4. **Assignment Events Topic**
   - AssignedToTrainer
   - AssignedToShiftLeader
   - AssignedToTeamLeader

### Event Flow Diagram

```mermaid
sequenceDiagram
    participant ACS as Annual Calendar Service
    participant WPS as Working Plan Service
    participant TKS as TKS Service
    participant OAS as Operator Assignment Service
    participant SB as Service Bus
    participant DB as Databases

    ACS->>SB: Publish Calendar Events
    SB->>WPS: Consume Calendar Events
    WPS->>DB: Update Working Plans

    WPS->>SB: Publish Plan Events
    SB->>TKS: Consume Plan Events
    TKS->>DB: Update Operator Status

    TKS->>SB: Publish Status Events
    SB->>WPS: Consume Status Events
    WPS->>DB: Update Plan Status

    OAS->>SB: Publish Assignment Events
    SB->>TKS: Consume Assignment Events
    TKS->>DB: Update Operator Assignments
```

## 5. Data Models

### Event Schema

```typescript
interface BaseEvent {
    id: string;
    type: EventType;
    timestamp: string;
    metadata: {
        correlationId: string;
        source: string;
        version: string;
    };
}

interface CalendarEvent extends BaseEvent {
    data: {
        year: number;
        weekNumber?: number;
        date?: string;
        holidayInfo?: HolidayInfo;
    };
}

interface WorkingPlanEvent extends BaseEvent {
    data: {
        planId: string;
        weekNumber: number;
        year: number;
        shifts?: ShiftInfo[];
        operators?: OperatorAssignment[];
    };
}

interface StatusEvent extends BaseEvent {
    data: {
        operatorId: string;
        status: StatusType;
        location: LocationType;
        timestamp: string;
        reason?: string;
    };
}

interface AssignmentEvent extends BaseEvent {
    data: {
        site: string;
        assignFrom: UserInfo;
        assignTo: UserInfo;
        operator: OperatorInfo;
    };
}
```

### Database Schemas

#### SQL Database (Calendar Service)

```sql
CREATE TABLE YearConfigurations (
    YearId INT PRIMARY KEY,
    Status VARCHAR(20) NOT NULL,
    WeekStartsOn TINYINT NOT NULL,
    WeekEndsOn TINYINT NOT NULL,
    DefaultWorkingDays VARCHAR(20) NOT NULL,
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL
);

CREATE TABLE Holidays (
    HolidayId UNIQUEIDENTIFIER PRIMARY KEY,
    Date DATE NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    HolidayType VARCHAR(20) NOT NULL,
    IsWorkingDay BIT NOT NULL,
    CreatedBy VARCHAR(100) NOT NULL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL
);
```

#### Cosmos DB Collections

1. **WorkingPlan Collection**
```typescript
interface WorkingPlanDocument {
    id: string;
    type: "WorkingPlan";
    partitionKey: string;
    weekNumber: number;
    year: number;
    startDate: string;
    endDate: string;
    status: WorkingPlanStatus;
    shifts: ShiftInfo[];
    metadata: DocumentMetadata;
}
```

2. **Status Collection**
```typescript
interface StatusDocument {
    id: string;
    type: "Status";
    partitionKey: string;
    operatorId: string;
    currentStatus: StatusType;
    location: LocationType;
    timestamp: string;
    history: StatusHistory[];
    metadata: DocumentMetadata;
}
```

3. **Assignment Collection**
```typescript
interface AssignmentDocument {
    id: string;
    type: "Assignment";
    partitionKey: string;
    site: string;
    assignmentType: AssignmentEventType;
    assignFrom: UserInfo;
    assignTo: UserInfo;
    operator: OperatorInfo;
    timestamp: string;
    metadata: DocumentMetadata;
}
```

## 6. Event-Driven Architecture

### Event Processing Flow

1. **Calendar Events**
   - Event Generation: Calendar service generates events for year initialization, holiday creation
   - Consumers: Working Plan service updates its schedules based on calendar changes

2. **Working Plan Events**
   - Event Generation: Working Plan service generates events for plan creation, shift assignments
   - Consumers: TKS service updates operator assignments and status tracking

3. **Status Events**
   - Event Generation: TKS service generates events for operator status changes
   - Consumers: Working Plan service updates plan status, Assignment service validates assignments

4. **Assignment Events**
   - Event Generation: Assignment service generates events for operator assignments
   - Consumers: TKS service updates operator hierarchies, Working Plan service updates assignments

### Event Processing Implementation

```typescript
interface IEventProcessor {
    processEvent(event: BaseEvent): Promise<void>;
    validateEvent(event: BaseEvent): Promise<boolean>;
    persistEvent(event: BaseEvent): Promise<void>;
}

class EventProcessor implements IEventProcessor {
    constructor(
        private readonly validator: IEventValidator,
        private readonly repository: IEventRepository,
        private readonly publisher: IEventPublisher
    ) {}

    async processEvent(event: BaseEvent): Promise<void> {
        try {
            const isValid = await this.validateEvent(event);
            if (!isValid) {
                throw new Error('Invalid event');
            }

            await this.persistEvent(event);
            await this.publisher.publish(event);
        } catch (error) {
            // Handle error
        }
    }
}
```

## 7. Security & Authorization

### Role-Based Access Control

```typescript
enum Role {
    ADMIN = "ADMIN",
    TRAINER_RESPONSIBLE = "TRAINER_RESPONSIBLE",
    TRAINER = "TRAINER",
    SHIFT_LEADER = "SHIFT_LEADER",
    TEAM_LEADER = "TEAM_LEADER",
    OPERATOR = "OPERATOR"
}

interface Permission {
    action: string;
    resource: string;
}

const rolePermissions = {
    [Role.ADMIN]: [
        { action: "manage", resource: "*" }
    ],
    [Role.TRAINER_RESPONSIBLE]: [
        { action: "assign", resource: "trainer" },
        { action: "view", resource: "operators" }
    ],
    [Role.TRAINER]: [
        { action: "assign", resource: "shift_leader" },
        { action: "view", resource: "operators" }
    ]
    // ... other role permissions
};
```

### Security Implementation

```typescript
interface IAuthorizationService {
    validateAccess(userId: string, action: string, resource: string): Promise<boolean>;
    getUserRoles(userId: string): Promise<Role[]>;
    validateToken(token: string): Promise<TokenValidationResult>;
}

class AuthorizationService implements IAuthorizationService {
    constructor(
        private readonly roleRepository: IRoleRepository,
        private readonly tokenValidator: ITokenValidator
    ) {}

    async validateAccess(userId: string, action: string, resource: string): Promise<boolean> {
        const roles = await this.getUserRoles(userId);
        return roles.some(role => this.hasPermission(role, action, resource));
    }
}
```

## 8. Performance Considerations

### Caching Strategy

```typescript
interface ICacheConfig {
    options: {
        host: string;
        port: number;
        password: string;
        tls: boolean;
    };
    ttl: {
        calendar: number;    // 24 hours
        workingPlan: number; // 1 hour
        status: number;      // 5 minutes
        assignment: number;  // 1 hour
    };
}

interface ICacheService {
    get<T>(key: string): Promise<T | null>;
    set<T>(key: string, value: T, ttl?: number): Promise<void>;
    invalidate(key: string): Promise<void>;
}
```

### Database Optimization

1. **Cosmos DB**
   - Partition Key Strategy
     - Working Plans: `/year`
     - Status: `/operatorId`
     - Assignments: `/site`
   - Indexing Policy
     - Include paths for frequently queried fields
     - Exclude paths for unused fields
   - Change Feed Processing
     - Batch size optimization
     - Checkpoint frequency

2. **SQL Database**
   - Indexed Views
   - Optimized Stored Procedures
   - Query Performance Tuning

### Monitoring & Logging

```typescript
interface IMetrics {
    recordLatency(operation: string, duration: number): void;
    recordError(operation: string, error: Error): void;
    recordEvent(eventType: string, count: number): void;
}

interface ILogger {
    info(message: string, context?: object): void;
    error(message: string, error: Error, context?: object): void;
    warn(message: string, context?: object): void;
    debug(message: string, context?: object): void;
}
```

Key Metrics:
- API Response Times
- Event Processing Latency
- Database Query Performance
- Cache Hit Rates
- Error Rates by Service 