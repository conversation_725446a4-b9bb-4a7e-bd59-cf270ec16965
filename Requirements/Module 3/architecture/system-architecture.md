# System Architecture - Module 3

## High-Level Architecture

### Microservices Architecture

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│  Status Management  │     │    Time Tracking    │     │    Authorization    │
│      Service       │◄────►│      Service       │◄────►│      Service       │
└─────────────────────┘     └─────────────────────┘     └─────────────────────┘

```

## Service Descriptions

### 1. Status Management Service
- **Purpose**: Core service for managing employee statuses and state transitions
- **Key Responsibilities**:
  - Real-time status tracking
  - Status change validation
  - Status history maintenance
  - Status notification dispatch

### 2. Time Tracking Service
- **Purpose**: Handles all time-related operations and calculations
- **Key Responsibilities**:
  - Work hours tracking
  - Overtime calculation
  - Delay management
  - Attendance reconciliation

### 3. Authorization Service
- **Purpose**: Manages access control and permissions
- **Key Responsibilities**:
  - Badge management
  - Access level control
  - Authorization validation
  - Legal status tracking




### 6. Integration Service
- **Purpose**: Manages external system integrations
- **Key Responsibilities**:
  - Turnstile system integration
  - Optitime synchronization
  - Bus reader device integration
  - HR system integration

## Communication Patterns

### Synchronous Communication
- REST APIs for direct service-to-service communication
- gRPC for high-performance internal communication

### Asynchronous Communication
- Event-driven architecture using message queues
- Publish/Subscribe pattern for status updates
- Event sourcing for status history

## Data Flow

```
External Systems ──► Integration Service ──► Status Management ──► Time Tracking

```

## Security Architecture

### Authentication
- JWT-based authentication
- Role-based access control (RBAC)

## Scalability Considerations


### Data Partitioning
- Time-based partitioning
- Status-based partitioning 