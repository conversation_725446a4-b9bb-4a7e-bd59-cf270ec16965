# Business Processes and Workflows - Module 3

## Status Management Workflows

### 1. Daily Attendance Flow
```mermaid
stateDiagram-v2
    [*] --> PresentInBus: Board Bus
    PresentInBus --> PresentInPlant: <PERSON><PERSON>an at Plant
    PresentInPlant --> InLine: Line Assignment
    InLine --> Break: Break Time
    Break --> InLine: Return from Break
    InLine --> PresentInPlant: End of Line Work
    PresentInPlant --> [*]: End of Shift
```

### 2. Absence Management Flow
```mermaid
stateDiagram-v2
    [*] --> Delayed: Late Arrival
    Delayed --> Present: Arrival at Plant
    Delayed --> Absent: No Show
    [*] --> TimeOff: Planned Leave
    TimeOff --> [*]: Leave Period End
    [*] --> Absent: Unplanned Absence
    Absent --> Present: Return to Work
```

## Overtime Management Process

### 1. Planned Overtime
1. Capacity Study Approval
2. Working Plan Update
3. Employee Notification
4. Overtime Execution
5. TKS Update
6. Payroll Processing

### 2. Exceptional Overtime
1. Team Leader Request
2. Approval Matrix Review
3. TKS Responsible Notification
4. Overtime Execution
5. Optitime Update

## Legal Authorization Process

### 1. Initial Authorization
```mermaid
stateDiagram-v2
    [*] --> Temporary: New Employee
    Temporary --> Pending: Evidence Submitted
    Pending --> Authorized: Verification Complete
    Authorized --> [*]: Valid Authorization
```

### 2. Authorization Renewal
1. System Alert for Expiring Authorization
2. Evidence Request
3. Document Submission
4. Verification Process
5. Status Update

## Training Management Process

### 1. Training Schedule
1. Working Plan Review
2. Training Slot Allocation
3. Employee Assignment
4. Notification Distribution
5. Attendance Tracking
6. Status Update

### 2. Training Status Flow
```mermaid
stateDiagram-v2
    [*] --> Scheduled: Training Assignment
    Scheduled --> InProgress: Training Start
    InProgress --> Completed: Training End
    Scheduled --> Cancelled: Cancellation
    Scheduled --> Absent: No Show
```

## Incident Management Process

### 1. Minor Incident Flow
```mermaid
stateDiagram-v2
    [*] --> Reported: Incident Occurs
    Reported --> NurseCare: On-site Treatment
    NurseCare --> ReturnToWork: Minor Treatment
    ReturnToWork --> [*]: Status Update
```

### 2. Major Incident Flow
```mermaid
stateDiagram-v2
    [*] --> Reported: Incident Occurs
    Reported --> HospitalTransfer: Serious Injury
    HospitalTransfer --> MedicalLeave: Treatment Required
    MedicalLeave --> ReturnToWork: Recovery Complete
    ReturnToWork --> [*]: Status Update
```

## Maternity Leave Process

### 1. Leave Request Flow
```mermaid
stateDiagram-v2
    [*] --> Pending: Certificate Submission
    Pending --> Approved: Documentation Verified
    Approved --> Active: Leave Starts
    Active --> Completed: Return to Work
    Completed --> [*]: Status Update
```

### 2. Documentation Process
1. Certificate Submission to Team Leader
2. Document Upload at Kiosk
3. TKS Agent Verification
4. Status Update in System
5. Leave Period Tracking

## Integration Workflows

### 1. Turnstile Integration
```mermaid
stateDiagram-v2
    [*] --> BadgeScan: Employee Arrives
    BadgeScan --> ValidationCheck: System Check
    ValidationCheck --> AccessGranted: Valid Badge
    ValidationCheck --> AccessDenied: Invalid Badge
    AccessGranted --> StatusUpdate: System Update
    AccessDenied --> ManualVerification: Security Check
```

### 2. Optitime Synchronization
1. Daily Data Collection
2. Status Consolidation
3. Data Transformation
4. Optitime Upload
5. Verification Process
6. Error Resolution

## Status Transition Rules

### 1. Valid Status Transitions
- Present → Break
- Present → External Work
- Present → Training
- Present → Accident
- Delayed → Present
- Delayed → Absent

### 2. Invalid Status Transitions
- Training → Break
- Accident → Training
- Maternity → Present (without proper documentation)
- Absent → Present (without proper authorization)

## Approval Workflows

### 1. Overtime Approval Matrix
1. Team Leader Request
2. Department Head Review
3. HR Validation
4. Final Approval
5. System Update

### 2. Leave Approval Process
1. Request Submission
2. Team Leader Review
3. Resource Availability Check
4. Approval/Rejection
5. Status Update

## System Integration Points

### 1. Badge System Integration
- Real-time badge scan processing
- Access control validation
- Status update triggers
- Security alerts

### 2. HR System Integration
- Employee data synchronization
- Leave balance updates
- Authorization status checks
- Training record updates

### 3. Payroll System Integration
- Time record export
- Overtime calculation
- Absence reconciliation
- Special case handling 