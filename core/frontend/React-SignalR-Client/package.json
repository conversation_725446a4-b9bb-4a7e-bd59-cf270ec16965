{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@microsoft/signalr": "^8.0.7", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.13", "@types/node": "^20.14.8", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "react": "^18.2.0", "react-dom": "^18.3.1", "react-scripts": "^0.0.0", "typescript": "^4.9.5", "web-vitals": "^3.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}