import React, { useState, useEffect } from 'react';
import './AlertIcon.css';

interface AlertIconProps {
  hasNewAlert: boolean;
  alertMessage: string;
  alertCount: number;
  onAlertClick: () => void;
}

const AlertIcon: React.FC<AlertIconProps> = ({
  hasNewAlert,
  alertMessage,
  alertCount,
  onAlertClick
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  // Auto-hide tooltip after 5 seconds when new alert arrives
  useEffect(() => {
    if (hasNewAlert) {
      setShowTooltip(true);
      const timer = setTimeout(() => {
        setShowTooltip(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [hasNewAlert]);

  return (
    <div className="alert-container">
      <div 
        className={`alert-icon ${hasNewAlert ? 'has-alert' : ''}`}
        onClick={() => {
          onAlertClick();
          setShowTooltip(!showTooltip);
        }}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {/* Alert Triangle Icon SVG */}
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.29 3.86L1.82 18C1.64 18.37 1.64 18.8 1.82 19.17C2 19.54 2.37 19.78 2.79 19.78H21.21C21.63 19.78 22 19.54 22.18 19.17C22.36 18.8 22.36 18.37 22.18 18L13.71 3.86C13.53 3.49 13.16 3.25 12.74 3.25C12.32 3.25 11.95 3.49 11.77 3.86H10.29Z"
            stroke="currentColor"
            fill="currentColor"
            strokeWidth="1"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <line 
            x1="12" 
            y1="9" 
            x2="12" 
            y2="13" 
            stroke="white" 
            strokeWidth="2" 
            strokeLinecap="round"
          />
          <circle 
            cx="12" 
            cy="17" 
            r="1" 
            fill="white"
          />
        </svg>
        
        {/* Red alert dot with count */}
        {hasNewAlert && (
          <div className="alert-dot">
            {alertCount > 0 && alertCount < 100 ? alertCount : '99+'}
          </div>
        )}
      </div>

      {/* Tooltip popup */}
      {showTooltip && alertMessage && (
        <div className="alert-tooltip">
          <div className="alert-tooltip-content">
            <strong>⚠️ Alert</strong>
            <p>{alertMessage}</p>
            {alertCount > 1 && (
              <small>{alertCount} active alerts</small>
            )}
          </div>
          <div className="alert-tooltip-arrow"></div>
        </div>
      )}
    </div>
  );
};

export default AlertIcon;
