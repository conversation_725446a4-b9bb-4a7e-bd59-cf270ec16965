.notification-container {
  position: relative;
  display: inline-block;
}

.notification-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  border: 2px solid #e0e0e0;
}

.notification-icon:hover {
  background-color: #e8e8e8;
  transform: scale(1.1);
}

.notification-icon.has-notification {
  color: #333;
  background-color: #fff;
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.notification-icon.has-notification:hover {
  background-color: #f8f9fa;
}

.notification-dot {
  position: absolute;
  top: -5px;
  right: -5px;
  min-width: 20px;
  height: 20px;
  background-color: #dc3545;
  border-radius: 10px;
  border: 2px solid white;
  color: white;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
  padding: 0 4px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.notification-tooltip {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  background-color: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s ease;
  min-width: 200px;
  max-width: 300px;
  white-space: normal;
}

.tooltip-content {
  text-align: left;
}

.tooltip-content strong {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

.tooltip-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #333;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .notification-tooltip {
    position: fixed;
    top: 60px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
  }
  
  .tooltip-arrow {
    display: none;
  }
}
