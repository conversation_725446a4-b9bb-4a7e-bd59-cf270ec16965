import React, { useEffect, useState } from 'react';
import './App.css';
import Connector from './signalr-connection';
import NotificationIcon from './NotificationIcon';
import AlertIcon from './AlertIcon';

function App() {
  const { newMessage, events, alertEvents, alertMessageEvents, userId } = Connector();
  const [message, setMessage] = useState("initial value");
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const [notificationCount, setNotificationCount] = useState(0);
  
  // Alert states
  const [alertMessage, setAlertMessage] = useState("No alerts");
  const [hasNewAlert, setHasNewAlert] = useState(false);
  const [alertCount, setAlertCount] = useState(0);

  useEffect(() => {
    // Handle notifications - ensure only one increment per notification
    events((message) => {
      setMessage(message);
      setHasNewNotification(true);
      setNotificationCount(prev => prev + 1);
      console.log('Notification received, count should increment by 1');
    });

    // Handle alert events - ONLY use this one for counting
    alertEvents((message) => {
      setAlertMessage(message);
      setHasNewAlert(true);
      setAlertCount(prev => prev + 1);
      console.log('Alert received, count should increment by 1');
    });

    // DON'T use alertMessageEvents to avoid double counting
    // If you need message content, handle it in the backend to send via "NewAlert"
    
  }, []); // Empty dependency array to run only once

  const handleNotificationClick = () => {
    setHasNewNotification(false);
    console.log('Notification clicked:', message);
    // You can add more logic here, like opening a notification panel
  };

  const handleAlertClick = () => {
    setHasNewAlert(false);
    console.log('Alert clicked:', alertMessage);
    // You can add more logic here, like opening an alert panel
  };

  return (
    <div className="App">
      {/* Top bar with notification and alert icons */}
      <div style={{ display: 'flex', justifyContent: 'flex-end', padding: '60px 20px 20px 20px' }}>
        <NotificationIcon
          hasNewNotification={hasNewNotification}
          notificationMessage={message}
          notificationCount={notificationCount}
          onNotificationClick={handleNotificationClick}
        />
        <AlertIcon
          hasNewAlert={hasNewAlert}
          alertMessage={alertMessage}
          alertCount={alertCount}
          onAlertClick={handleAlertClick}
        />
      </div>
      
      {/* Center content */}
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center', 
        minHeight: '60vh',
        textAlign: 'center' 
      }}>
        <div style={{ marginBottom: '20px' }}>
          <span style={{ fontSize: '18px', fontWeight: 'bold' }}>User id: {userId}</span>
          <br />
          <span style={{ fontSize: '16px' }}>Notifications received: {notificationCount}</span>
          <br />
          <span style={{ fontSize: '16px' }}>Alerts received: {alertCount}</span>
        </div>
        
        <button 
          onClick={() => newMessage((new Date()).toISOString())}
          style={{
            padding: '10px 20px',
            fontSize: '16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Send Test Message
        </button>
      </div>
    </div>
  );
}
export default App;