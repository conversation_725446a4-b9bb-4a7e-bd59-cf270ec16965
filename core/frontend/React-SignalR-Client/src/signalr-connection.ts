import * as signalR from "@microsoft/signalr";

const URL = process.env.HUB_ADDRESS ?? "https://ingress.connected-workersdev.aptiv.com/notifications-signalr/hub/notification"; //or whatever your backend port is
class Connector {
    private connection: signalR.HubConnection;
    public events: (onMessageReceived: (message: string) => void) => void;
    public alertEvents: (onAlertReceived: (message: string) => void) => void;
    public alertMessageEvents: (onAlertMessageReceived: (message: string) => void) => void;
    static instance: Connector;
    public userId: string
    
    constructor() {

        this.userId = "user" + Math.floor(Math.random() * 50);

        this.connection = new signalR.HubConnectionBuilder()
            .withUrl(`${URL}?userId=${this.userId}`, {
                withCredentials: true, // Re-enabled for Azure SignalR
                transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
                skipNegotiation: false
            })
            .withAutomaticReconnect()
            .build();
        this.connection.start()
            .then(() => console.log(`SignalR connected as ${this.userId}`))
            .catch(err => {
                console.error("SignalR connection failed:", err);
                // Try to provide more helpful error message
                if (err.message.includes('ERR_CERT_AUTHORITY_INVALID')) {
                    console.error('SSL Certificate error - please accept the certificate by visiting the URL directly in your browser');
                }
                document.write(`Connection Error: ${err.message}`);
            });
        
        // Handle notification events (keeping your existing logic)
        this.events = (onMessageReceived) => {
            // Remove existing listener to prevent duplicates
            this.connection.off("NewNotification");
            this.connection.on("NewNotification",  () => {
                onMessageReceived("got your notification");
            });
        };

        // Handle alert events
        this.alertEvents = (onAlertReceived) => {
            // Remove existing listener to prevent duplicates
            this.connection.off("NewAlert");
            this.connection.on("NewAlert", () => {
                onAlertReceived("Got your alert");
            });
        };

        // Handle alert message events with content
        this.alertMessageEvents = (onAlertMessageReceived) => {
            // Remove existing listener to prevent duplicates
            this.connection.off("NewAlertMessage");
            this.connection.on("NewAlertMessage", (message) => {
                onAlertMessageReceived(message);
            });
        };
    }
    public newMessage = (messages: string) => {
        this.connection.send("newMessage", "foo", messages).then(x => console.log("sent"))
    }

    // Fetch alerts for the current user
    public fetchAlerts = async () => {
        try {
            const response = await fetch(`/api/Alert/active-alerts/${this.userId}`);
            if (response.ok) {
                return await response.json();
            } else {
                console.error("Failed to fetch alerts:", response.statusText);
                return [];
            }
        } catch (error) {
            console.error("Error fetching alerts:", error);
            return [];
        }
    }

    // Dismiss a specific alert
    public dismissAlert = async (alertId: string) => {
        try {
            const response = await fetch(`/api/Alert/dismiss-alert/${this.userId}/${alertId}`, {
                method: 'PUT'
            });
            return response.ok;
        } catch (error) {
            console.error("Error dismissing alert:", error);
            return false;
        }
    }

    // Dismiss all alerts for the user
    public dismissAllAlerts = async () => {
        try {
            const response = await fetch(`/api/Alert/dismiss-all-alerts/${this.userId}`, {
                method: 'PUT'
            });
            return response.ok;
        } catch (error) {
            console.error("Error dismissing all alerts:", error);
            return false;
        }
    }
    public static getInstance(): Connector {
        if (!Connector.instance)
            Connector.instance = new Connector();
        return Connector.instance;
    }
}

export default Connector.getInstance;