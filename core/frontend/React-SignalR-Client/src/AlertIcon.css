.alert-container {
  position: relative;
  display: inline-block;
  margin-left: 15px;
}

.alert-icon {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: #fff3cd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #856404;
  border: 2px solid #ffeaa7;
}

.alert-icon:hover {
  background-color: #fff3cd;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.alert-icon.has-alert {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
  animation: shake 0.5s ease-in-out;
}

.alert-icon.has-alert:hover {
  background-color: #f1b0b7;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.alert-dot {
  position: absolute;
  top: -5px;
  right: -5px;
  min-width: 20px;
  height: 20px;
  background-color: #dc3545;
  border-radius: 10px;
  border: 2px solid white;
  color: white;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: alertPulse 2s infinite;
  padding: 0 4px;
}

@keyframes alertPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.alert-tooltip {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  background-color: #dc3545;
  color: white;
  padding: 10px 14px;
  border-radius: 6px;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
  animation: alertFadeIn 0.3s ease;
  min-width: 220px;
  max-width: 320px;
  white-space: normal;
}

.alert-tooltip-content {
  text-align: left;
}

.alert-tooltip-content strong {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: bold;
}

.alert-tooltip-content p {
  margin: 0 0 4px 0;
  font-size: 12px;
  opacity: 0.95;
  line-height: 1.3;
}

.alert-tooltip-content small {
  font-size: 10px;
  opacity: 0.8;
  font-style: italic;
}

.alert-tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #dc3545;
}

@keyframes alertFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .alert-tooltip {
    position: fixed;
    top: 60px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
  }
  
  .alert-tooltip-arrow {
    display: none;
  }
}

/* High priority alert styles */
.alert-icon.critical {
  animation: criticalAlert 1s infinite;
}

@keyframes criticalAlert {
  0%, 100% { 
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }
  50% { 
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }
}
