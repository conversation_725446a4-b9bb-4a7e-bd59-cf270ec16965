"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { AUTH_ENDPOINTS, getRedirectPath } from "@/lib/axios";
import { useAuthStore } from "@/store/authStore";

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const searchParams = useSearchParams();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  // Handle redirect param if present
  useEffect(() => {
    const redirect = searchParams?.get("redirect");
    if (redirect) {
      // Save the redirect path for after login
      sessionStorage.setItem("redirectAfterLogin", redirect);
    }

    // If already authenticated, redirect to saved path or dashboard
    if (isAuthenticated) {
      window.location.href = getRedirectPath();
    }
  }, [searchParams, isAuthenticated]);

  // Handle login with redirect back to original page
  const handleSamlLogin = () => {
    setLoading(true);

    // Add the return URL that points to our callback page
    const returnUrl = `${window.location.origin}/auth/callback`;

    // Redirect to the login endpoint with callback URL
    window.location.href = `${AUTH_ENDPOINTS.login}?returnUrl=${encodeURIComponent(returnUrl)}`;
  };

  return (
    <div className="mx-auto w-full max-w-full space-y-8 p-6 lg:p-8">
      <div className="flex items-center justify-center p-4 lg:p-8">
        <div className="flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Sign In</h1>
            <p className="text-sm text-muted-foreground">
              Connect with your organizational account
            </p>
          </div>

          <button
            onClick={handleSamlLogin}
            disabled={loading}
            className="flex items-center justify-center w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? "Redirecting..." : "Sign in with Azure AD"}
          </button>
        </div>
      </div>
    </div>
  );
}
