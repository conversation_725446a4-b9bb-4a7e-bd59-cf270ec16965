"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { getRedirectPath } from "@/lib/axios";

// Define UI components to separate concerns
const LoadingUI = ({ onClick }: { onClick: () => void }) => (
  <div className="text-center p-8 max-w-md bg-white rounded-lg shadow">
    <h2 className="text-xl mb-4">Completing your sign-in...</h2>
    <div className="w-8 h-8 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
    <p className="text-sm text-gray-600">
      If you&apos;re not redirected soon, click the button below
    </p>
    <button
      onClick={onClick}
      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
    >
      Go to Dashboard
    </button>
  </div>
);

const ErrorUI = ({ error, onBack }: { error: string; onBack: () => void }) => (
  <div className="text-center p-8 max-w-md bg-white rounded-lg shadow">
    <h2 className="text-xl text-red-600 mb-4">Authentication Error</h2>
    <p className="mb-4">{error}</p>
    <p className="text-sm text-gray-600 mb-4">
      This might indicate your token is invalid or expired. Please try logging
      in again.
    </p>
    <button
      onClick={onBack}
      className="px-4 py-2 bg-blue-600 text-white rounded"
    >
      Back to Login
    </button>
  </div>
);

// Main component
export default function AuthCallbackPage() {
  const [error, setError] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const setTokens = useAuthStore((state) => state.setTokens);
  const router = useRouter();

  // Navigate to dashboard
  const goToDashboard = () => {
    router.replace(getRedirectPath());
  };

  // Navigate to login
  const goToLogin = () => {
    router.push("/login");
  };

  // Handle authentication
  useEffect(() => {
    let isMounted = true;

    const processAuth = async () => {
      try {
        // Check for access token or error in URL
        const accessToken = searchParams?.get("accessToken");
        const errorParam = searchParams?.get("error");

        if (errorParam && isMounted) {
          setError(decodeURIComponent(errorParam));
          return;
        }

        if (!accessToken && isMounted) {
          setError("No access token provided in callback URL");
          return;
        }

        // Process the token
        await setTokens(accessToken!);

        // Redirect to dashboard after successful login
        if (isMounted) {
          goToDashboard();
        }
      } catch (err) {
        if (isMounted) {
          console.error("Auth callback error:", err);
          setError(
            err instanceof Error ? err.message : "Authentication failed",
          );
        }
      }
    };

    // Process authentication
    processAuth();

    // Cleanup function
    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  // Render different UI based on state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ErrorUI error={error} onBack={goToLogin} />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <LoadingUI onClick={goToDashboard} />
    </div>
  );
}
