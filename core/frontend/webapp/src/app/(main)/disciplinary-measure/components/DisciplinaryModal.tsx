import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import FileUpload from "@/components/ui/file-upload";
import { addDays, isSunday } from "date-fns";
import { useDisciplinaryStore } from "../store/disciplinaryStore";
import {
  CreateDisciplinaryMeasureRequest,
  EmployeeSuggestion,
} from "../types/disciplinaryTypes";
import { formatDateToISO } from "../utils/dateUtils";
import CustomIcon from "@/components/common/CustomIcons";
import { useTranslations } from "next-intl";

interface DisciplinaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingId?: string | null;
}

const DisciplinaryModal: React.FC<DisciplinaryModalProps> = ({
  isOpen,
  onClose,
  editingId,
}) => {
  const t = useTranslations("disciplinary_measures");
  console.log("DisciplinaryModal rendering", editingId);

  // Store methods
  const {
    employees,
    measures,
    isCreating,
    isUpdating,
    searchEmployees,
    createDisciplinaryMeasure,
    updateDisciplinaryMeasure,
    isLoadingEmployees,
  } = useDisciplinaryStore();

  // State for form data
  const [formData, setFormData] = useState({
    EmployeeId: "",
    Id: "",
    firstName: "",
    lastName: "",
    Description: "",
    RequestedBy: "System",
    department: "",
    category: "",
    Reason: "",
    StartDate: new Date().toISOString().split("T")[0], // Default to today,
    numberOfDays: 0,
    EndDate: "",
    File: null as File | File[] | null,
    FileData: null as File | File[] | null,
  });

  // State for autocomplete suggestions
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  console.log("searchQuery", searchQuery);
  // Get current disciplinary measure if editing
  const currentMeasure = editingId
    ? measures.find((m) => m.Id === editingId)
    : null;
  const isEditing = !!editingId && !!currentMeasure;
  // Load data for editing
  useEffect(() => {
    if (isEditing && currentMeasure) {
      setFormData({
        EmployeeId: currentMeasure.EmployeeId,
        Id: currentMeasure.Id,
        RequestedBy: "System",
        firstName: currentMeasure.firstName,
        lastName: currentMeasure.lastName,
        department: currentMeasure.department,
        category: currentMeasure.category,
        Reason: currentMeasure.Reason,
        StartDate: new Date(currentMeasure.StartDate)
          .toISOString()
          .split("T")[0],
        numberOfDays: currentMeasure.days,
        EndDate: currentMeasure.EndDate,
        Description: currentMeasure.Description ?? "",
        File: currentMeasure.FileUrl
          ? [new File([], currentMeasure?.FileUrl || "attached-file.pdf")]
          : null, // Reset file for editing
        FileData: currentMeasure.FileData ?? null, // Reset file for editing
      });
    } else {
      // Reset form for new entry
      setFormData({
        EmployeeId: "",
        Id: "",
        RequestedBy: "System",
        firstName: "",
        lastName: "",
        department: "",
        category: "",
        Reason: "",
        StartDate: new Date().toISOString().split("T")[0], // Default to today,
        numberOfDays: 0,
        EndDate: "",
        Description: "",
        File: null,
        FileData: null, // Reset file for new entry
      });
    }
  }, [isEditing, currentMeasure, isOpen]);

  const handleChange = (
    field: string,
    value: string | File | File[] | null | number,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Handle employee search when typing in operatorId field
    if (field === "EmployeeId" && typeof value === "string") {
      setSearchQuery(value);
      if (value.trim().length > 0) {
        searchEmployees(value);
        setShowSuggestions(true);
      } else {
        setShowSuggestions(false);
      }
    }
  };
  const calculateEndDate = (
    StartDate: string,
    numberOfDays: number,
  ): string => {
    if (!StartDate || numberOfDays <= 0) return "";

    let currentDate = new Date(StartDate);
    let daysAdded = 0;

    while (daysAdded < numberOfDays) {
      currentDate = addDays(currentDate, 1);
      if (!isSunday(currentDate)) {
        daysAdded++;
      }
    }

    return currentDate.toISOString().split("T")[0];
  };

  // Handle selecting an employee from suggestions
  const handleSelectEmployee = (employee: EmployeeSuggestion) => {
    setFormData((prev) => ({
      ...prev,
      EmployeeId: employee.id,
      firstName: employee.firstName,
      lastName: employee.lastName,
      department: employee.department,
      category: employee.category || "",
      // Reset other fields if needed
    }));
    setShowSuggestions(false);
    setSearchQuery(employee.id);
  };

  // Auto-calculate end date when start date or number of days changes
  useEffect(() => {
    if (formData.StartDate && formData.numberOfDays > 0) {
      const calculatedEndDate = calculateEndDate(
        formData.StartDate,
        formData.numberOfDays,
      );
      setFormData((prev) => ({ ...prev, EndDate: calculatedEndDate }));
    }
  }, [formData.StartDate, formData.numberOfDays]);

  const handleSave = async () => {
    try {
      const apiData: CreateDisciplinaryMeasureRequest = {
        EmployeeId: formData.EmployeeId,
        Id: formData.Id,
        firstName: formData.firstName,
        lastName: formData.lastName,
        department: formData.department,
        category: formData.category,
        Reason: formData.Reason,
        StartDate: formatDateToISO(formData.StartDate),
        EndDate: formatDateToISO(formData.EndDate),
        days: formData.numberOfDays,
        File: formData.File || undefined,
        Description: formData.Reason,
        RequestedBy: formData.RequestedBy || "System",
        status: "AP", // Default status
      };

      let success = false;
      if (isEditing && editingId) {
        success = await updateDisciplinaryMeasure(editingId, apiData);
      } else {
        success = await createDisciplinaryMeasure(apiData);
      }

      if (success) {
        // Reset form after saving
        setFormData({
          EmployeeId: "",
          Id: "",
          firstName: "",
          lastName: "",
          RequestedBy: "System",
          department: "",
          category: "",
          Reason: "",
          StartDate: "",
          numberOfDays: 0,
          EndDate: "",
          Description: "",
          File: null,
          FileData: null, // Reset file after saving
        });
        onClose();
      }
    } catch (error) {
      console.error("Error saving disciplinary measure:", error);
    }
  };

  console.log("formData:", formData);
  console.log("file type:", formData.File);
  console.log("file type:", formData.FileData);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] mx-auto p-4 gap-4 overflow-y-auto">
        <DialogHeader className="relative pb-4 border-b border-gray-200">
          <DialogTitle className="text-lg font-semibold text-left text-gray-900">
            {isEditing ? t("edit_title") : t("submit_title")}
          </DialogTitle>
        </DialogHeader>
        {/* Centered Icon */}
        <div className="flex justify-center mb-4">
          <div className="flex items-center justify-center">
            <CustomIcon
              name="disciplinaryMeasuresIcon"
              className="w-10 h-10 text-white"
            />
          </div>
        </div>
        <div className="space-y-3">
          {/* Operator ID, First Name and Last Name in a row */}
          <div className="grid grid-cols-3 gap-3">
            {/* Operator ID with Search */}
            <div className="relative">
              <Label
                htmlFor="EmployeeId"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("operator_id")} :
              </Label>
              <Input
                id="EmployeeId"
                value={formData.EmployeeId}
                onChange={(e) => handleChange("EmployeeId", e.target.value)}
                className="h-8 text-sm"
                placeholder={t("operator_id_placeholder")}
                onFocus={() => {
                  if (formData.EmployeeId && employees.length > 0) {
                    setShowSuggestions(true);
                  }
                }}
                onBlur={() => {
                  setTimeout(() => setShowSuggestions(false), 200);
                }}
              />

              {/* Autocomplete Suggestions */}
              {showSuggestions && employees.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-auto">
                  {employees.map((employee) => (
                    <div
                      key={employee.EmployeeId}
                      className="px-3 py-2 cursor-pointer hover:bg-gray-100 flex flex-col"
                      onMouseDown={(e) => {
                        e.preventDefault();
                        handleSelectEmployee({
                          id: employee.EmployeeId,
                          firstName: employee.firstName,
                          lastName: employee.lastName,
                          department: employee.department,
                          category: employee.category,
                        });
                      }}
                    >
                      <span className="font-medium">{employee.EmployeeId}</span>
                      <span className="text-xs text-gray-500">
                        {employee.firstName} {employee.lastName} -{" "}
                        {employee.department}
                        {employee.category && (
                          <span className="text-xs text-gray-500">
                            {" "}
                            ({employee.category})
                          </span>
                        )}
                      </span>
                    </div>
                  ))}
                </div>
              )}
              {isLoadingEmployees && (
                <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md border border-gray-200 p-2">
                  <span className="text-sm text-gray-500">Searching...</span>
                </div>
              )}
            </div>
            <div>
              <Label
                htmlFor="firstName"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("first_name")} :
              </Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => handleChange("firstName", e.target.value)}
                className="h-8 text-sm"
                placeholder={t("first_name_placeholder")}
              />
            </div>
            <div>
              <Label
                htmlFor="lastName"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("last_name")} :
              </Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => handleChange("lastName", e.target.value)}
                className="h-8 text-sm"
                placeholder={t("last_name_placeholder")}
              />
            </div>
          </div>
          {/* Department and Disciplinary Type in a row */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label
                htmlFor="department"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("department")} :
              </Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => handleChange("department", e.target.value)}
                className="h-8 text-sm"
                placeholder={t("department_placeholder")}
              />
            </div>
            <div>
              <Label
                htmlFor="category"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("disciplinary_type")} :
              </Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => handleChange("category", e.target.value)}
                className="h-8 text-sm"
                placeholder="category"
              />
            </div>
          </div>
          {/* Start Date and Number of Days and End date in a row */}
          <div className="grid grid-cols-3 gap-3">
            {" "}
            <div>
              <Label
                htmlFor="startDate"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("start_date")} :
              </Label>
              <Input
                id="StartDate"
                type="date"
                value={formData.StartDate}
                onChange={(e) => handleChange("StartDate", e.target.value)}
                className="h-8 text-sm"
              />
            </div>
            <div>
              <Label
                htmlFor="numberOfDays"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("number_of_days")} :
              </Label>
              <Input
                id="numberOfDays"
                type="number"
                value={formData.numberOfDays || ""}
                onChange={(e) => {
                  const numberOfDays = parseInt(e.target.value, 10) || 0;
                  handleChange("numberOfDays", numberOfDays);
                }}
                className="h-8 text-sm"
                placeholder={t("number_of_days_placeholder")}
              />
            </div>{" "}
            <div>
              <Label
                htmlFor="endDate"
                className="text-xs text-gray-600 mb-1 block"
              >
                {t("end_date")} :
              </Label>
              <Input
                id="endDate"
                type="date"
                value={formData.EndDate}
                onChange={(e) => handleChange("endDate", e.target.value)}
                className="h-8 text-sm bg-gray-50 cursor-not-allowed"
                readOnly
                tabIndex={-1}
              />
            </div>
          </div>{" "}
          {/* Reason/Comment */}
          <div>
            <Label
              htmlFor="reason"
              className="text-xs text-gray-600 mb-1 block"
            >
              {t("reason")} :
            </Label>
            <Textarea
              id="Reason"
              value={formData.Reason}
              onChange={(e) => handleChange("Reason", e.target.value)}
              className="min-h-[90px] text-sm resize-none"
              placeholder={t("reason_placeholder")}
            />
          </div>
          {/* File Upload */}
          <div className="overflow-hidden">
            <FileUpload
              id="File"
              label={t("file_upload")}
              value={formData.File}
              onChange={(file) => handleChange("File", file)}
              accept=".jpeg,.jpg,.png,.docs,.pdf"
              multiple={true}
              maxFiles={3}
              translationNamespace="disciplinary_measures"
            />
          </div>
        </div>{" "}
        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <Button
            onClick={onClose}
            variant="outline"
            className="text-sm rounded-lg border-gray-300 text-gray-700 hover:bg-gray-50 p-4"
            disabled={isCreating || isUpdating}
          >
            {t("close")}
          </Button>
          <Button
            onClick={handleSave}
            className="text-sm bg-black text-white hover:bg-gray-800 rounded-lg p-4"
            disabled={isCreating || isUpdating}
          >
            {isCreating || isUpdating
              ? t("saving")
              : isEditing
                ? t("update")
                : t("save")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DisciplinaryModal;
