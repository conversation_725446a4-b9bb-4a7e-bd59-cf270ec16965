import React, { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/common/tables/DataTable";
import { MoreVertical, Edit, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { ToastAction } from "@/components/ui/toast";
import { useDisciplinaryStore } from "../store/disciplinaryStore";
import { DisciplinaryMeasureResponse } from "../types/disciplinaryTypes";
import { formatDateToDDMMYYYY } from "../utils/dateUtils";

interface EmployeeTableProps {
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const EmployeeTable: React.FC<EmployeeTableProps> = ({ onEdit, onDelete }) => {
  const t = useTranslations("disciplinary_measures");
  const { toast } = useToast();
  const {
    measures,
    isLoading,
    isDeleting,
    error,
    fetchAllDisciplinaryMeasures,
    deleteDisciplinaryMeasure,
  } = useDisciplinaryStore();

  // Fetch data on component mount
  useEffect(() => {
    fetchAllDisciplinaryMeasures();
  }, [fetchAllDisciplinaryMeasures]);

  const handleDelete = async (disciplinaryMeasureId: string) => {
    if (!disciplinaryMeasureId) return;

    // Show confirmation toast
    toast({
      title: t("confirm_delete_title"),
      description: t("confirm_delete_message"),
      variant: "destructive",
      action: (
        <ToastAction
          altText={t("confirm")}
          onClick={async () => {
            try {
              const success = await deleteDisciplinaryMeasure(
                disciplinaryMeasureId,
              );
              if (success) {
                toast({
                  title: t("delete_success"),
                  description: t("delete_success_message"),
                  variant: "default",
                });
                if (onDelete) {
                  onDelete(disciplinaryMeasureId);
                }
              } else {
                toast({
                  title: t("delete_error"),
                  description: t("delete_error_message"),
                  variant: "destructive",
                });
              }
            } catch (error) {
              console.log("Delete failed", error);
              toast({
                title: t("delete_error"),
                description: t("delete_error_message"),
                variant: "destructive",
              });
            }
          }}
        >
          {t("confirm")}
        </ToastAction>
      ),
    });
  };

  const columns: ColumnDef<DisciplinaryMeasureResponse>[] = [
    {
      accessorKey: "Id",
      header: t("id"),
      cell: ({ row }) => (
        <div className="font-medium text-gray-900">{row.getValue("Id")}</div>
      ),
    },
    {
      accessorKey: "firstName",
      header: t("first_name"),
      cell: ({ row }) => (
        <div className="text-gray-900">{row.getValue("firstName")}</div>
      ),
    },
    {
      accessorKey: "lastName",
      header: t("last_name"),
      cell: ({ row }) => (
        <div className="text-gray-900">{row.getValue("lastName")}</div>
      ),
    },
    {
      accessorKey: "function",
      header: t("function"),
      cell: ({ row }) => (
        <div className="text-gray-900">{row.getValue("function")}</div>
      ),
    },
    {
      accessorKey: "department",
      header: t("department"),
      cell: ({ row }) => (
        <div className="text-gray-900">{row.getValue("department")}</div>
      ),
    },
    {
      accessorKey: "category",
      header: t("category"),
      cell: ({ row }) => (
        <div className="text-gray-900">{row.getValue("category")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: t("status"),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge
            variant={status === "AP" ? "default" : "destructive"}
            className="bg-cyan-100 text-cyan-800 font-medium pointer-events-none"
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "StartDate",
      header: t("start_date"),
      cell: ({ row }) => (
        <div className="text-gray-900">
          {formatDateToDDMMYYYY(row.getValue("StartDate"))}
        </div>
      ),
    },
    {
      accessorKey: "EndDate",
      header: t("end_date"),
      cell: ({ row }) => (
        <div className="text-gray-900">
          {formatDateToDDMMYYYY(row.getValue("EndDate") ?? "")}
        </div>
      ),
    },
    {
      accessorKey: "days",
      header: t("days"),
      cell: ({ row }) => (
        <div className="text-gray-900">{row.getValue("days")}</div>
      ),
    },
    {
      id: "actions",
      header: t("actions"),
      cell: ({ row }) => {
        const Measure = row.original;

        return (
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  disabled={isDeleting}
                >
                  <span className="sr-only">{t("openMenu")}</span>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(Measure.Id)}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t("edit")}
                  </DropdownMenuItem>
                )}
                {onDelete && Measure.Id && (
                  <DropdownMenuItem
                    onClick={() => handleDelete(Measure.Id!)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    {t("delete")}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="text-red-600 text-center">
          {t("error")}: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <DataTable
        columns={columns}
        data={measures}
        showStatusFilter={false}
        id="disciplinary-measures-table"
        isLoading={isLoading}
      />
    </div>
  );
};

export default EmployeeTable;
