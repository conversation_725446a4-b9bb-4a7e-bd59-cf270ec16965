import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDisciplinaryStore } from "../store/disciplinaryStore";

interface ExportButtonProps {
  onExport?: () => void; // Optional callback for custom export logic
}

const ExportButton: React.FC<ExportButtonProps> = ({ onExport }) => {
  const t = useTranslations("disciplinary_measures");
  const tableData = useDisciplinaryStore((state) => state.getTableData());

  // Helper to convert table data to CSV
  const handleExport = () => {
    if (onExport) {
      onExport();
      return;
    }
    if (!tableData || tableData.length === 0) return;

    const headers = Object.keys(tableData[0]);
    const csvRows = [
      headers.join(","), // header row
      ...tableData.map((row) =>
        headers
          .map((field) => {
            const value = row[field as keyof typeof row];
            // Escape quotes and commas
            if (
              typeof value === "string" &&
              (value.includes(",") || value.includes('"'))
            ) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          })
          .join(","),
      ),
    ];
    const csvContent = csvRows.join("\r\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);

    // Create a temporary link to trigger download
    const a = document.createElement("a");
    a.href = url;
    a.download = "disciplinary_measures.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Button onClick={handleExport} className="ml-auto">
      <Download className="mr-2 h-4 w-4" /> {t("export")}
    </Button>
  );
};

export default ExportButton;
