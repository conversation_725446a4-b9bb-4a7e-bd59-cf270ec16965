import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Filter,
  ArrowUpDown,
  Download,
  Search,
  ChevronUp,
  ChevronDown,
  X,
} from "lucide-react";
import { useTranslations } from "next-intl";

interface FilterBarProps {
  onFilter: (query: string) => void;
  onSort: (criteria: string) => void;
  onExport: () => void;
  totalItems?: number;
  filteredItems?: number;
}

const FilterBar: React.FC<FilterBarProps> = ({
  onFilter,
  onSort,
  onExport,
  totalItems = 0,
  filteredItems = 0,
}) => {
  const t = useTranslations("disciplinary_measures");
  const [filterQuery, setFilterQuery] = React.useState("");
  const [activeFilters, setActiveFilters] = React.useState<string[]>([]);
  const [sortField, setSortField] = React.useState("date");
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc">(
    "desc",
  );
  const handleSort = (field: string) => {
    const newDirection =
      sortField === field && sortDirection === "desc" ? "asc" : "desc";
    setSortField(field);
    setSortDirection(newDirection);
    onSort(`${field}-${newDirection}`);
  };

  const clearFilter = () => {
    setFilterQuery("");
    onFilter("");
  };

  const removeActiveFilter = (filter: string) => {
    setActiveFilters((prev) => prev.filter((f) => f !== filter));
  };

  const isFiltered = filterQuery.length > 0 || activeFilters.length > 0;
  const showingResults = filteredItems !== totalItems;

  return (
    <div className="space-y-4">
      {/* Main Filter Bar */}
      <div className="flex justify-between items-center">
        {/* Search Input with Clear Button */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />{" "}
          <Input
            placeholder={t("search_placeholder")}
            value={filterQuery}
            onChange={(e) => {
              setFilterQuery(e.target.value);
              onFilter(e.target.value);
            }}
            className="pl-10 pr-10 bg-white border-gray-200 rounded-lg"
          />
          {filterQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilter}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
            >
              <X className="h-3 w-3 text-gray-400" />
            </Button>
          )}
        </div>

        {/* Filter, Sort, and Export buttons */}
        <div className="flex gap-3 ml-4">
          <Button
            variant="outline"
            className={`bg-white border-gray-200 text-gray-700 hover:bg-gray-50 ${
              activeFilters.length > 0
                ? "border-indigo-500 bg-indigo-50 text-indigo-700"
                : ""
            }`}
          >
            {" "}
            <Filter className="mr-2 h-4 w-4" />
            {t("filter")}
            {activeFilters.length > 0 && (
              <Badge
                variant="secondary"
                className="ml-2 bg-indigo-600 text-white text-xs px-1.5 py-0.5"
              >
                {activeFilters.length}
              </Badge>
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className={`bg-white border-gray-200 text-gray-700 hover:bg-gray-50 ${
                  sortField !== "date"
                    ? "border-indigo-500 bg-indigo-50 text-indigo-700"
                    : ""
                }`}
              >
                {" "}
                <ArrowUpDown className="mr-2 h-4 w-4" />
                {t("sort_by")}:{" "}
                {sortField.charAt(0).toUpperCase() + sortField.slice(1)}
                {sortDirection === "asc" ? (
                  <ChevronUp className="ml-1 h-3 w-3" />
                ) : (
                  <ChevronDown className="ml-1 h-3 w-3" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {" "}
              <DropdownMenuItem onClick={() => handleSort("date")}>
                <div className="flex items-center justify-between w-full">
                  {t("date")}
                  {sortField === "date" &&
                    (sortDirection === "asc" ? (
                      <ChevronUp className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    ))}
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort("name")}>
                <div className="flex items-center justify-between w-full">
                  {t("name")}
                  {sortField === "name" &&
                    (sortDirection === "asc" ? (
                      <ChevronUp className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    ))}
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleSort("department")}>
                <div className="flex items-center justify-between w-full">
                  {t("department")}
                  {sortField === "department" &&
                    (sortDirection === "asc" ? (
                      <ChevronUp className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    ))}
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>{" "}
          <Button
            onClick={onExport}
            className="bg-black text-white hover:bg-gray-800"
          >
            <Download className="mr-2 h-4 w-4" />
            {t("export")}
          </Button>
        </div>
      </div>

      {/* Active Filters and Results Count */}
      {(isFiltered || showingResults) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-wrap">
            {" "}
            {filterQuery && (
              <Badge
                variant="outline"
                className="bg-yellow-50 border-yellow-200 text-yellow-800"
              >
                {t("search")}: &quot;{filterQuery}&quot;
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilter}
                  className="ml-1 h-4 w-4 p-0 hover:bg-yellow-100"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
            {activeFilters.map((filter) => (
              <Badge
                key={filter}
                variant="outline"
                className="bg-blue-50 border-blue-200 text-blue-800"
              >
                {filter}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeActiveFilter(filter)}
                  className="ml-1 h-4 w-4 p-0 hover:bg-blue-100"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            ))}
          </div>{" "}
          <div className="text-sm text-gray-600">
            {showingResults ? (
              <span className="font-medium text-indigo-600">
                {t("showing_results", { filteredItems, totalItems })}
              </span>
            ) : (
              <span>{t("total_results", { totalItems })}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterBar;
