// Interface for file metadata (for storage)
export interface FileMetadata {
  name: string;
  size: number;
  type: string;
}

// Legacy interface - kept for backward compatibility
export interface DisciplinaryMeasure {
  id?: string; // Optional ID for tracking
  EmployeeId: string;
  Id?: string; // Optional ID for existing measures
  firstName: string;
  lastName: string;
  Reason: string;
  category: string; // e.g., "WARNING", "SUSPENSION", etc.
  department: string;
  StartDate: string;
  EndDate: string;
  Description: string;
  File?: File | File[] | FileMetadata | FileMetadata[] | null;
  numberOfDays: number;
  status?: string;
}

// API Request/Response interfaces
export interface CreateDisciplinaryMeasureRequest {
  EmployeeId: string;
  Id: string;
  category: string;
  firstName: string;
  lastName: string;
  department: string;
  function?: string;
  Reason: string;
  StartDate: string; // ISO format
  EndDate: string; // ISO format
  days: number;
  File?: File | File[];
  Description?: string;
  RequestedBy?: string;
  status?: string;
}

export interface DisciplinaryMeasureResponse {
  Id: string;
  EmployeeId: string;
  firstName: string;
  lastName: string;
  department: string;
  function?: string;
  category: string;
  Reason: string;
  Description?: string;
  StartDate: string;
  EndDate: string;
  days: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  FileData?: File | File[] | null; // Array of file metadat
  FileUrl?: string | undefined; // URL to access the file
  FileName?: string; // For single file uploads
}

export interface GetDisciplinaryMeasuresRequest {
  pageNumber?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  search?: string;
  department?: string;
  category?: string;
  Reason?: string;
  Description?: string;
  status?: string;
  StartDate?: string;
  EndDate?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface EmployeeSearchResponse {
  EmployeeId: string;
  firstName: string;
  lastName: string;
  department: string;
  category: string;
  function?: string; // Optional, can be used for filtering
}

export interface TableRowData {
  Id: string;
  id: string;
  firstName: string;
  lastName: string;
  function: string;
  department: string;
  category: string;
  status: string;
  startDate: string;
  endDate: string;
  days: string;
  // Add these for better API integration
  disciplinaryMeasureId?: string;
  disciplinaryType?: string;
  Reason?: string;
}

export interface EmployeeSuggestion {
  id: string;
  firstName: string;
  lastName: string;
  department: string;
  category?: string;
  // Optional, can be used for filtering
  function?: string; // Optional, can be used for filtering
  // add other fields if needed
}
