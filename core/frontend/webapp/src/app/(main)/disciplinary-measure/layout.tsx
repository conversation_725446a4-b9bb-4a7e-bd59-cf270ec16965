// import { RouteGuard } from "@/components/auth/RouteGuard";
// import { PermissionLevel } from "@/config/permissionsConfig";

export default function DisciplinaryMeasureLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    // <RouteGuard
    //   requiredPermissionLevel={PermissionLevel.Read}
    //   fallbackPath="/unauthorized"
    // >
    <div id="page-container" className="flex-1 overflow-y-auto md:flex-col">
      <main id="main-content">
        <div className="relative flex min-h-screen flex-col md:min-h-0">
          {children}
        </div>
      </main>
    </div>
    // </RouteGuard>
  );
}
