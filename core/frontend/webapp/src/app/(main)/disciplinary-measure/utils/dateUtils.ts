/**
 * Utility functions for date formatting in disciplinary measures
 */

/**
 * Formats a date to DD/MM/YYYY format
 * @param date - Date string or Date object
 * @returns Formatted date string in DD/MM/YYYY format
 */
export const formatDateToDDMMYYYY = (date: string | Date): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return "";
  }

  const day = dateObj.getDate().toString().padStart(2, "0");
  const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
  const year = dateObj.getFullYear();

  return `${day}/${month}/${year}`;
};

/**
 * Formats a date to YYYY-MM-DD format for internal storage and sorting
 * @param date - Date string or Date object
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateToISO = (date: string | Date): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return "";
  }

  return dateObj.toISOString().split("T")[0];
};
