import { create } from "zustand";
import api from "@/lib/axios";
import { persist } from "zustand/middleware";
import {
  DisciplinaryMeasure,
  CreateDisciplinaryMeasureRequest,
  DisciplinaryMeasureResponse,
  EmployeeSearchResponse,
} from "../types/disciplinaryTypes";
import { formatDateToISO } from "../utils/dateUtils";

// Helper to extract backend error message
interface ErrorDetails {
  error?: { message?: string };
}

interface ErrorResponseData {
  message?: string;
  details?: ErrorDetails;
}

interface AxiosLikeError {
  response?: { data?: ErrorResponseData };
  message?: string;
}

function getApiErrorMessage(error: unknown): string {
  if (!error || typeof error !== "object") return "An unknown error occurred";

  const axiosError = error as AxiosLikeError;

  // Try to get message from response data
  const responseMessage = axiosError.response?.data?.message;
  if (responseMessage) return responseMessage;

  // Try to get message from details
  const detailsMessage = axiosError.response?.data?.details?.error?.message;
  if (detailsMessage) return detailsMessage;

  // Fallback to error message
  return axiosError.message || "An unknown error occurred";
}

interface DisciplinaryStore {
  // Data
  measures: DisciplinaryMeasureResponse[];
  employees: EmployeeSearchResponse[]; // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isLoadingEmployees: boolean;

  // Filters
  filters: {
    search?: string;
    department?: string;
    disciplinaryType?: string;
    status?: string;
    StartDate?: string;
    EndDate?: string;
  };

  // Error handling
  error: string | null;

  // API Actions
  fetchAllDisciplinaryMeasures: () => Promise<void>;
  createDisciplinaryMeasure: (
    data: CreateDisciplinaryMeasureRequest,
  ) => Promise<boolean>;
  updateDisciplinaryMeasure: (
    id: string,
    data: Partial<CreateDisciplinaryMeasureRequest>,
  ) => Promise<boolean>;
  deleteDisciplinaryMeasure: (id: string) => Promise<boolean>;
  searchEmployees: (searchTerm: string) => Promise<void>;

  // UI Actions
  setFilters: (filters: Partial<DisciplinaryStore["filters"]>) => void;
  clearFilters: () => void;
  setError: (error: string | null) => void;
  refreshData: () => Promise<void>;

  // Legacy methods (for backward compatibility)
  addMeasure: (measure: DisciplinaryMeasure) => void;
  getMeasures: () => DisciplinaryMeasureResponse[];
  updateMeasure: (id: string, measure: Partial<DisciplinaryMeasure>) => void;
  clearMeasures: () => void;
  getEmployees: () => EmployeeSearchResponse[];
  getTableData: () => DisciplinaryMeasureResponse[];
}

export const useDisciplinaryStore = create<DisciplinaryStore>()(
  persist(
    (set, get) => ({
      // Initial state
      measures: [],
      employees: [],

      // Loading states
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isLoadingEmployees: false,

      // Filters
      filters: {},

      // Error handling
      error: null,

      // API Actions
      fetchAllDisciplinaryMeasures: async () => {
        set({ isLoading: true, error: null });

        try {
          const queryParams = {
            page: "createdAt",
            limit: "desc",
            ...get().filters,
            // No pagination parameters to get all records
          };

          const response = await api.get<DisciplinaryMeasureResponse[]>(
            "/disciplinary-measures/api/Suspension/GetAll",
            { params: queryParams },
          );

          const items = response.data;

          console.log("Fetched disciplinary measures:", items);

          set({
            measures: items,
            isLoading: false,
          });
        } catch (error) {
          const errorMessage = getApiErrorMessage(error);
          set({ error: errorMessage, isLoading: false });
          console.error("Failed to fetch all disciplinary measures:", error);
        }
      },

      createDisciplinaryMeasure: async (
        data: CreateDisciplinaryMeasureRequest,
      ) => {
        set({ isCreating: true, error: null });

        try {
          // Prepare form data for file upload
          const formData = new FormData();
          formData.append("EmployeeId", data.EmployeeId);
          formData.append("firstName", data.firstName);
          formData.append("lastName", data.lastName);
          formData.append("Description", data.Description || "");
          formData.append("department", data.department);
          formData.append("category", data.category);
          formData.append("reason", data.Reason);
          formData.append("startDate", data.StartDate);
          formData.append("endDate", data.EndDate);
          formData.append("requestedBy", "system"); // Assuming system user for now
          formData.append("days", data.days.toString());
          formData.append("status", data.status || "AP"); // Default to "AP"
          if (data.File) {
            // Only append the first file if it's an array, otherwise append the single file
            if (Array.isArray(data.File)) {
              if (data.File.length > 0) {
                formData.append("File", data.File[0]);
              }
            } else {
              formData.append("File", data.File);
            }
          }
          if (data.Description) {
            formData.append("comments", data.Description);
          }
          await api.post<DisciplinaryMeasureResponse>(
            `/disciplinary-measures/api/Suspension/Create`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            },
          );

          // Refresh the list after creation
          await get().fetchAllDisciplinaryMeasures();

          set({ isCreating: false });
          return true;
        } catch (error) {
          const errorMessage = getApiErrorMessage(error);
          set({ error: errorMessage, isCreating: false });
          console.error("Failed to create disciplinary measure:", error);
          return false;
        }
      },

      updateDisciplinaryMeasure: async (
        id: string,
        data: Partial<CreateDisciplinaryMeasureRequest>,
      ) => {
        set({ isUpdating: true, error: null });

        try {
          const formData = new FormData();

          Object.entries(data).forEach(([key, value]) => {
            if (value !== undefined && key !== "file") {
              formData.append(key, value.toString());
            }
          });
          // Handle file uploads
          if (data.File) {
            if (Array.isArray(data.File)) {
              if (data.File.length > 0) {
                formData.append("File", data.File[0]);
              }
            } else {
              formData.append("File", data.File);
            }
          }

          await api.put(
            `/disciplinary-measures/api/Suspension/Update/${id}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            },
          );

          // Refresh the list after update
          await get().fetchAllDisciplinaryMeasures();

          set({ isUpdating: false });
          return true;
        } catch (error) {
          const errorMessage = getApiErrorMessage(error);
          set({ error: errorMessage, isUpdating: false });
          console.error("Failed to update disciplinary measure:", error);
          return false;
        }
      },

      deleteDisciplinaryMeasure: async (id: string) => {
        set({ isDeleting: true, error: null });

        try {
          await api.delete(
            `/disciplinary-measures/api/Suspension/Delete/${id}`,
          );

          // Refresh the list after deletion
          await get().fetchAllDisciplinaryMeasures();

          set({ isDeleting: false });
          return true;
        } catch (error) {
          const errorMessage = getApiErrorMessage(error);
          set({ error: errorMessage, isDeleting: false });
          console.error("Failed to delete disciplinary measure:", error);
          return false;
        }
      },

      searchEmployees: async (searchTerm: string) => {
        if (!searchTerm.trim()) {
          set({ employees: [] });
          return;
        }

        set({ isLoadingEmployees: true, error: null });

        try {
          // Call the real API endpoint
          const response = await api.get<EmployeeSearchResponse[]>(
            "/disciplinary-measures/api/Operators/GetAll",
            {
              params: {
                search: searchTerm, // Pass search term as query parameter
              },
            },
          );

          const allEmployees = response.data;

          // Filter employees based on search term (client-side filtering as backup)
          const filteredEmployees = allEmployees.filter(
            (employee) =>
              employee.firstName
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
              employee.lastName
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
              employee.EmployeeId.includes(searchTerm) ||
              employee.department
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
              employee.category
                .toLowerCase()
                .includes(searchTerm.toLowerCase()),
          );

          set({ employees: filteredEmployees, isLoadingEmployees: false });
        } catch (error) {
          const errorMessage = getApiErrorMessage(error);
          set({
            error: errorMessage,
            isLoadingEmployees: false,
            employees: [],
          });
          console.error("Employee search error:", error);
        }
      },

      // UI Actions
      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
        }));
        // Auto-refresh data when filters change
        get().fetchAllDisciplinaryMeasures();
      },

      clearFilters: () => {
        set({ filters: {} });
        get().fetchAllDisciplinaryMeasures();
      },

      setError: (error) => {
        set({ error });
      },

      refreshData: async () => {
        await get().fetchAllDisciplinaryMeasures();
      },

      // Legacy methods for backward compatibility
      addMeasure: (measure: DisciplinaryMeasure) => {
        // Convert legacy format to API format and create
        const apiData: CreateDisciplinaryMeasureRequest = {
          EmployeeId: measure.EmployeeId,
          Id: measure.Id || "", // Use existing ID or empty string
          firstName: measure.firstName,
          lastName: measure.lastName,
          department: measure.department,
          category: measure.category,
          Reason: measure.Reason,
          StartDate: formatDateToISO(measure.StartDate),
          EndDate: formatDateToISO(measure.EndDate),
          days: measure.numberOfDays,
          File: measure.File as File | File[] | undefined,
          Description: measure.Description,
        };

        get().createDisciplinaryMeasure(apiData);
      },

      getMeasures: () => get().measures,

      updateMeasure: (
        id: string,
        updatedMeasure: Partial<DisciplinaryMeasure>,
      ) => {
        // Convert legacy format to API format and update
        const apiData: Partial<CreateDisciplinaryMeasureRequest> = {};

        if (updatedMeasure.Description)
          apiData.Reason = updatedMeasure.Description;
        if (updatedMeasure.StartDate)
          apiData.StartDate = formatDateToISO(updatedMeasure.StartDate);
        if (updatedMeasure.EndDate)
          apiData.EndDate = formatDateToISO(updatedMeasure.EndDate);
        if (updatedMeasure.numberOfDays)
          apiData.days = updatedMeasure.numberOfDays;
        if (updatedMeasure.File)
          apiData.File = updatedMeasure.File as File | File[];
        if (updatedMeasure.Description)
          apiData.Description = updatedMeasure.Description;

        get().updateDisciplinaryMeasure(id, apiData);
      },

      clearMeasures: () => {
        set({ measures: [], employees: [] });
      },
      getEmployees: () => get().employees,

      getTableData: () => get().measures,
    }),
    {
      name: "disciplinary-measures-storage",
      // Only persist non-sensitive data
      partialize: (state) => ({
        filters: state.filters,
      }),
    },
  ),
);
