"use client";

import React, { useState, useEffect } from "react";
import EmployeeTable from "./components/EmployeeTable";
import DisciplinaryModal from "./components/DisciplinaryModal";
import FilterBar from "./components/FilterBar";
import { useDisciplinaryStore } from "./store/disciplinaryStore";
// import { DisciplinaryMeasure } from "./types/disciplinaryTypes";
import CustomIcon from "@/components/common/CustomIcons";
import { Plus } from "lucide-react";
import { useTranslations } from "next-intl";

const DisciplinaryMeasurePage = () => {
  const t = useTranslations("disciplinary_measures");
  // const { toast } = useToast();

  // State for modal visibility
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);

  // Use the disciplinary store
  const { measures, error, fetchAllDisciplinaryMeasures, setFilters } =
    useDisciplinaryStore();

  // Initialize data on component mount
  useEffect(() => {
    // Try to fetch from API first, fallback to sample data for development
    fetchAllDisciplinaryMeasures().catch(() => {
      console.log("API not available, loading sample data");
    });
  }, [fetchAllDisciplinaryMeasures]);
  const handleFilter = (query: string) => {
    setFilters({ search: query });
  };

  const handleSort = () => {
    // const [field, direction] = criteria.split("-");
    fetchAllDisciplinaryMeasures();
  };

  const handleExport = () => {
    if (!measures || measures.length === 0) return;

    const headers = [
      "ID",
      "First Name",
      "Last Name",
      "Function",
      "Department",
      "Category",
      "Status",
      "Start Date",
      "End Date",
      "Days",
    ];

    const csvRows = [
      headers.join(","),
      ...measures.map((row) =>
        [
          row.Id,
          row.firstName,
          row.lastName,
          row.function,
          row.department,
          row.category,
          row.status,
          row.StartDate,
          row.EndDate,
          row.days,
        ]
          .map((value) => {
            if (
              typeof value === "string" &&
              (value.includes(",") || value.includes('"'))
            ) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          })
          .join(","),
      ),
    ];

    const csvContent = csvRows.join("\r\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `disciplinary-measures-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // const handleSave = async (formData: DisciplinaryMeasure) => {
  //   console.log("Saving disciplinary measure:", formData);

  //   try {
  //     await addMeasure(formData);

  //     toast({
  //       variant: "default",
  //       title: t("save_success_title"),
  //       description: t("save_success_message"),
  //     });

  //     setIsModalOpen(false);
  //     setEditingId(null);
  //   } catch (error) {
  //     console.error("Error saving disciplinary measure:", error);

  //     toast({
  //       variant: "destructive",
  //       title: t("save_error_title"),
  //       description: t("save_error_message"),
  //     });
  //   }
  // };

  const handleEdit = (id: string) => {
    console.log("Edit record with ID:", id);
    setEditingId(id);
    setIsModalOpen(true);
  };

  // const handleView = (id: string) => {
  //   console.log("View record with ID:", id);
  // };

  const handleDelete = (id: string) => {
    console.log("Deleted record with ID:", id);
    // Refresh data after successful deletion (handled by store)
  };

  const handleAddNew = () => {
    setEditingId(null);
    setIsModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header with centered icon and button */}
      <div className="flex flex-col items-center mb-8">
        {/* Icon */}
        <div className="flex items-center justify-center mb-4">
          <CustomIcon
            name="disciplinaryMeasuresIcon"
            className="w-10 h-10 text-white"
          />
        </div>

        {/* Submit Button */}
        <button
          className="bg-[#0F0061] hover:bg-[#0F0061] text-white px-6 py-2 rounded-md text-sm font-medium flex items-center gap-2 transition-colors"
          onClick={handleAddNew}
        >
          <span className="w-5 h-5 bg-white text-indigo-600 rounded-full flex items-center justify-center text-xs font-bold">
            <Plus className="w-3 h-3 text-indigo-600" />
          </span>
          {t("submit_title")}
        </button>
      </div>
      {/* Filter Bar and Export */}
      <div className="mb-6">
        <FilterBar
          onFilter={handleFilter}
          onSort={handleSort}
          onExport={handleExport}
          totalItems={measures.length}
          filteredItems={measures.length}
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Employee Table */}
      <EmployeeTable onEdit={handleEdit} onDelete={handleDelete} />

      {/* Modal */}
      <DisciplinaryModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingId(null);
        }}
        editingId={editingId}
      />
    </div>
  );
};
export default DisciplinaryMeasurePage;
