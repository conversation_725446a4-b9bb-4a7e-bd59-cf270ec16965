"use client";

import { useState, useEffect } from "react";
import { useAttendanceStore as useMyTeamStore } from "../store/myTeamStore";
import useReplacementStore from "../store/replacementStore";
import useMockAuthStore from "@/store/mockAuthStore";
import ReplacementPanel from "./ReplacementPanel";

export function ReplacementIndicators() {
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const { currentDate, shiftStartTime, shiftEndTime } = useMyTeamStore();
  const { backupMovements, fetchBackupMovements } = useReplacementStore();
  const { currentUser } = useMockAuthStore();

  useEffect(() => {
    if (currentUser?.id && currentDate) {
      // Generate shiftId from current date and shift times
      fetchBackupMovements(
        "3e2bac24-5866-4065-8a7b-914c2e077cf1",
        "b4bedff2-165e-4156-969f-d3b3cd025970",
      );
    }
  }, [
    currentUser?.id,
    currentDate,
    shiftStartTime,
    shiftEndTime,
    fetchBackupMovements,
  ]);

  // Use backup movements data if available, otherwise fall back to attendance store
  const incomingCount = backupMovements?.added?.length || 0;
  const outgoingCount = backupMovements?.taken?.length || 0;

  return (
    <>
      <div
        className="fixed right-0 top-1/2 transform -translate-y-1/2 z-40"
        style={{
          borderTopLeftRadius: "16px",
          borderBottomLeftRadius: "16px",
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
          overflow: "hidden",
        }}
      >
        <div className="bg-black flex flex-col w-16 shadow-lg">
          {/* Outgoing (top) */}
          <button
            onClick={() => setIsPanelOpen(true)}
            className="flex-1 flex items-center justify-center gap-1 py-2 focus:outline-none"
            aria-label="Show outgoing replacements"
            style={{ minHeight: "44px" }}
          >
            <span className="text-red-500 text-xl font-bold">
              {outgoingCount}
            </span>
            <span className="text-red-500 text-lg">
              <svg
                width="18"
                height="18"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10 15V5M10 5L6 9M10 5L14 9"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
          </button>
          {/* Divider */}
          <div className="w-full h-px bg-gray-400 opacity-40" />
          {/* Incoming (bottom) */}
          <button
            onClick={() => setIsPanelOpen(true)}
            className="flex-1 flex items-center justify-center gap-1 py-2 focus:outline-none"
            aria-label="Show incoming replacements"
            style={{ minHeight: "44px" }}
          >
            <span className="text-green-500 text-xl font-bold">
              {incomingCount}
            </span>
            <span className="text-green-500 text-lg">
              <svg
                width="18"
                height="18"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10 5v10M10 15l-4-4M10 15l4-4"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </span>
          </button>
        </div>
      </div>
      <ReplacementPanel
        isOpen={isPanelOpen}
        onClose={() => setIsPanelOpen(false)}
      />
    </>
  );
}
