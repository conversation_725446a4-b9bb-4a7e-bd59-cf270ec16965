"use client";
import { X } from "lucide-react";
import { useEffect } from "react";
import { useAttendanceStore } from "../attendance-sheet/store/attendanceStore";
import { useAttendanceStore as useMyTeamStore } from "../store/myTeamStore";
import useReplacementStore from "../store/replacementStore";
import useMockAuthStore from "@/store/mockAuthStore";
import CustomIcon from "@/components/common/CustomIcons";

interface ReplacementPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ReplacementPanel({
  isOpen = true,
  onClose = () => {},
}: ReplacementPanelProps) {
  const { replacements } = useAttendanceStore();
  const { currentDate, shiftStartTime, shiftEndTime } = useMyTeamStore();
  const {
    backupMovements,
    isLoadingBackupMovements,
    backupMovementsError,
    fetchBackupMovements,
  } = useReplacementStore();
  const { currentUser } = useMockAuthStore();

  useEffect(() => {
    if (isOpen && currentUser?.id && currentDate) {
      // Generate shiftId from current date and shift times
      fetchBackupMovements(
        "3e2bac24-5866-4065-8a7b-914c2e077cf1",
        "b4bedff2-165e-4156-969f-d3b3cd025970",
      );
    }
  }, [
    isOpen,
    currentUser?.id,
    currentDate,
    shiftStartTime,
    shiftEndTime,
    fetchBackupMovements,
  ]);

  if (!isOpen) return null;

  // Use backup movements data if available, otherwise fall back to attendance store
  const incomingMovements = backupMovements?.added || [];
  const outgoingMovements = backupMovements?.taken || [];

  // Fallback to old data structure if no backup movements
  const fallbackIncoming = !backupMovements ? replacements.incoming : [];
  const fallbackOutgoing = !backupMovements ? replacements.outgoing : [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end">
      <div className="bg-white w-[600px] h-full shadow-xl overflow-y-auto">
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 absolute top-4 right-4"
        >
          <X className="h-6 w-6" />
        </button>

        <div className="py-6 space-y-8">
          {/* Loading State */}
          {isLoadingBackupMovements && (
            <div className="flex justify-center items-center py-8">
              <div className="text-gray-500">Loading backup movements...</div>
            </div>
          )}

          {/* Error State */}
          {backupMovementsError && (
            <div className="flex justify-center items-center py-4">
              <div className="text-red-500 text-sm">{backupMovementsError}</div>
            </div>
          )}

          {/* Workers joining to cover */}
          <div className="space-y-4 space-x-6">
            <div className="flex items-center gap-3">
              <div
                className="bg-green-500 text-white w-20 h-12 flex items-center justify-center text-xl font-bold"
                style={{
                  borderBottomRightRadius: "40px",
                  borderTopRightRadius: "40px",
                }}
              >
                <CustomIcon
                  name="upload"
                  className="w-6 h-6"
                  style={{
                    width: "50%",
                    height: "50%",
                  }}
                />
                {incomingMovements.length}
              </div>
              <div className="text-base font-medium text-gray-800">
                Workers will join to cover for my absenteeism.
              </div>
            </div>

            {/* Render new data structure if available */}
            {incomingMovements.map((worker, index) => (
              <div
                key={index}
                className="bg-[#F5FBFF] p-4 rounded-lg border shadow-lg"
              >
                <div className="flex items-center justify-between">
                  {/* Replacement Workers (Left) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    {worker.remplacedBy && worker.remplacedBy.length > 0 ? (
                      worker.remplacedBy.map((replacement, replIndex) => (
                        <div key={replIndex} className="mb-4">
                          <CustomIcon
                            name="circleUser"
                            className="w-6 h-6 mx-auto mb-3"
                            style={{
                              width: "60px",
                              height: "60px",
                              fill: "#4CAF50",
                            }}
                          />
                          <div className="text-xs text-gray-500 mb-1">
                            ID: {replacement.replacementId}
                          </div>
                          <div className="font-semibold text-sm mb-1">
                            {replacement.replacementName}
                          </div>
                          <div className="text-green-600 text-sm font-medium">
                            {replacement.team}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="mb-4">
                        <CustomIcon
                          name="circleUser"
                          className="w-6 h-6 mx-auto mb-3"
                          style={{
                            width: "60px",
                            height: "60px",
                            fill: "grey",
                          }}
                        />
                        <div className="text-xs text-grey-500 mb-1">
                          Waiting for
                        </div>
                        <div className="font-semibold text-sm mb-1">
                          Replacement
                        </div>
                        <div className="text-orange-500 text-sm font-medium">
                          Pending ...
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Center - Will Replace */}
                  <div className="flex flex-col items-center text-center mx-8 flex-1">
                    <span className="text-sm text-green-600 font-medium">
                      Will Replace
                    </span>
                    <div className="flex items-center">
                      <div className="h-[0.2rem] w-32 bg-green-500"></div>
                      <div className="w-0 h-0 border-l-[8px] border-l-green-500 border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent"></div>
                    </div>
                    <div className="text-xs text-gray-700">
                      <div className="font-medium">Current Shift</div>
                      <div className="text-gray-600">
                        ({shiftStartTime} - {shiftEndTime})
                      </div>
                    </div>
                  </div>

                  {/* Absent Worker (Right) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    <div className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                      Absent
                    </div>
                    <CustomIcon
                      name="circleUser"
                      className="w-6 h-6 mx-auto mb-3"
                      style={{
                        width: "60px",
                        height: "60px",
                        fill: "#F84018",
                      }}
                    />
                    <div className="text-xs text-gray-500 mb-1">
                      ID: {worker.id}
                    </div>
                    <div className="font-semibold text-sm mb-1">
                      {worker.firstName} {worker.lastName}
                    </div>
                    <div className="text-red-600 text-sm font-medium">
                      {worker.teamName}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Fallback to old data structure */}
            {fallbackIncoming.map((replacement, index) => (
              <div
                key={`fallback-${index}`}
                className="bg-[#F5FBFF] p-4 rounded-lg border shadow-lg"
              >
                <div className="flex items-center justify-between">
                  {/* Replacement Worker (Left) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    <CustomIcon
                      name="circleUser"
                      className="w-6 h-6 mx-auto mb-3"
                      style={{
                        width: "60px",
                        height: "60px",
                        fill: "#4CAF50",
                      }}
                    />

                    <div className="text-xs text-gray-500 mb-1">
                      ID: {replacement.replacementWorker.id}
                    </div>
                    <div className="font-semibold text-sm mb-1">
                      {replacement.replacementWorker.name}
                    </div>
                    <div className="text-green-600 text-sm font-medium">
                      {replacement.replacementWorker.department}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {replacement.replacementWorker.project}
                    </div>
                  </div>

                  {/* Center - Will Replace */}
                  <div className="flex flex-col items-center text-center mx-8 flex-1">
                    <span className="text-sm text-green-600 font-medium">
                      Replaced
                    </span>
                    <div className="flex items-center">
                      <div className="h-[0.2rem] w-32 bg-green-500"></div>
                      <div className="w-0 h-0 border-l-[8px] border-l-green-500 border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent"></div>
                    </div>
                    <div className="text-xs text-gray-700">
                      <div className="font-medium">On: {replacement.date}</div>
                      <div className="text-gray-600">({replacement.shift})</div>
                    </div>
                  </div>

                  {/* Absent Worker (Right) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    <div className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full mb-2">
                      Absent
                    </div>
                    <CustomIcon
                      name="circleUser"
                      className="w-6 h-6 mx-auto mb-3"
                      style={{
                        width: "60px",
                        height: "60px",
                        fill: "#F84018",
                      }}
                    />
                    <div className="text-xs text-gray-500 mb-1">
                      ID: {replacement.absentWorker.id}
                    </div>
                    <div className="font-semibold text-sm mb-1">
                      {replacement.absentWorker.name}
                    </div>
                    <div className="text-red-600 text-sm font-medium">
                      {replacement.absentWorker.department}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {replacement.absentWorker.project}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {/* Separator Line */}
          <div className="border-t-4 border-gray-200"></div>
          {/* Workers being taken to replace others */}
          <div className="space-y-4 space-x-6 ">
            <div className="flex items-center gap-3">
              <div
                className="bg-red-500 text-white w-20 h-12 flex items-center justify-center text-xl font-bold"
                style={{
                  borderBottomRightRadius: "40px",
                  borderTopRightRadius: "40px",
                }}
              >
                <CustomIcon
                  name="upload"
                  className="w-6 h-6"
                  style={{
                    width: "50%",
                    height: "50%",
                    transform: "scaleY(-1)",
                  }}
                />{" "}
                {outgoingMovements.length}
              </div>
              <div className="text-base font-medium text-gray-800">
                Workers will be taken from my team to replace absenteeism in
                other teams.
              </div>
            </div>

            {/* Render new data structure if available */}
            {outgoingMovements.map((worker, index) => (
              <div
                key={index}
                className="bg-[#FFF5F5] p-4 rounded-lg border shadow-lg"
              >
                <div className="flex items-center justify-between">
                  {/* Taken Worker (Left) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    <CustomIcon
                      name="circleUser"
                      className="w-6 h-6 mx-auto mb-3"
                      style={{
                        width: "60px",
                        height: "60px",
                        fill: "#4CAF50",
                      }}
                    />

                    <div className="text-xs text-gray-500 mb-1">
                      ID: {worker.id}
                    </div>
                    <div className="font-semibold text-sm mb-1">
                      {worker.firstName} {worker.lastName}
                    </div>
                    <div className="text-green-600 text-sm font-medium">
                      {worker.teamName}
                    </div>
                    {/* <div className="text-xs text-gray-500 mt-1">
                      {worker.isSentToAnotherBackup ? 'Sent to backup' : 'Available'}
                    </div> */}
                  </div>

                  {/* Center - Will Replace */}
                  <div className="flex flex-col items-center text-center mx-8 flex-1">
                    <span className="text-sm text-green-600 font-medium">
                      Will Replace
                    </span>
                    <div className="flex items-center">
                      <div className="h-[0.2rem] w-32 bg-green-500"></div>
                      <div className="w-0 h-0 border-l-[8px] border-l-green-500 border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent"></div>
                    </div>
                    <div className="text-xs text-gray-700">
                      <div className="font-medium">Current Shift</div>
                      <div className="text-gray-600">
                        ({shiftStartTime} - {shiftEndTime})
                      </div>
                    </div>
                  </div>

                  {/* Workers to Replace (Right) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    {worker.willReplace && worker.willReplace.length > 0 ? (
                      worker.willReplace.map((replaceWorker, replIndex) => (
                        <div key={replIndex} className="mb-2">
                          <div className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full mb-2">
                            Replace at: {replaceWorker.workstation}
                          </div>
                          <CustomIcon
                            name="circleUser"
                            className="w-6 h-6 mx-auto mb-3"
                            style={{
                              width: "60px",
                              height: "60px",
                              fill: "#F84018",
                            }}
                          />
                          <div className="text-xs text-gray-500 mb-1">
                            ID: {replaceWorker.id}
                          </div>
                          <div className="font-semibold text-sm mb-1">
                            {replaceWorker.firstName} {replaceWorker.lastName}
                          </div>
                          <div className="text-red-600 text-sm font-medium">
                            {replaceWorker.teamName}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Executed by: {replaceWorker.executedBy}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="mb-2">
                        <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full mb-2">
                          No assignment yet
                        </div>
                        <CustomIcon
                          name="circleUser"
                          className="w-6 h-6 mx-auto mb-3"
                          style={{
                            width: "60px",
                            height: "60px",
                            fill: "grey",
                          }}
                        />
                        <div className="text-xs text-gray-500 mb-1">
                          Waiting for
                        </div>
                        <div className="font-semibold text-sm mb-1">
                          Assignment
                        </div>
                        <div className="text-gray-500 text-sm font-medium">
                          Pending ...
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* Fallback to old data structure */}
            {fallbackOutgoing.map((replacement, index) => (
              <div
                key={`fallback-${index}`}
                className="bg-[#FFF5F5] p-4 rounded-lg border shadow-lg"
              >
                <div className="flex items-center justify-between">
                  {/* Replacement Worker (Left) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    <CustomIcon
                      name="circleUser"
                      className="w-6 h-6 mx-auto mb-3"
                      style={{
                        width: "60px",
                        height: "60px",
                        fill: "#4CAF50",
                      }}
                    />

                    <div className="text-xs text-gray-500 mb-1">
                      ID: {replacement.replacementWorker.id}
                    </div>
                    <div className="font-semibold text-sm mb-1">
                      {replacement.replacementWorker.name}
                    </div>
                    <div className="text-green-600 text-sm font-medium">
                      {replacement.replacementWorker.department}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {replacement.replacementWorker.project}
                    </div>
                  </div>

                  {/* Center - Will Replace */}
                  <div className="flex flex-col items-center text-center mx-8 flex-1">
                    <span className="text-sm text-green-600 font-medium">
                      Replaced
                    </span>
                    <div className="flex items-center">
                      <div className="h-[0.2rem] w-32 bg-green-500"></div>
                      <div className="w-0 h-0 border-l-[8px] border-l-green-500 border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent"></div>
                    </div>
                    <div className="text-xs text-gray-700">
                      <div className="font-medium">On: {replacement.date}</div>
                      <div className="text-gray-600">({replacement.shift})</div>
                    </div>
                  </div>

                  {/* Absent Worker (Right) */}
                  <div className="flex flex-col items-center text-center flex-1">
                    <div className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full mb-2">
                      Absent
                    </div>
                    <CustomIcon
                      name="circleUser"
                      className="w-6 h-6 mx-auto mb-3"
                      style={{
                        width: "60px",
                        height: "60px",
                        fill: "#F84018",
                      }}
                    />
                    <div className="text-xs text-gray-500 mb-1">
                      ID: {replacement.absentWorker.id}
                    </div>
                    <div className="font-semibold text-sm mb-1">
                      {replacement.absentWorker.name}
                    </div>
                    <div className="text-red-600 text-sm font-medium">
                      {replacement.absentWorker.department}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {replacement.absentWorker.project}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
