"use client";
import React from "react";
import { MyTeamHeader } from "./components/MyTeamHeader";
import { ReplacementIndicators } from "./shared/ReplacementIndicators";
import { resolveUserRole } from "@/utils/userRoleHelper";
import useMockAuthStore from "@/store/mockAuthStore";
import { UserRole } from "@/enum/rolesEnum";
import CustomPageCard from "@/components/common/CustomPageCard";

const Page = () => {
  const { currentUser: user } = useMockAuthStore();
  const userRole = resolveUserRole(user);
  return (
    <CustomPageCard>
      <MyTeamHeader />
      {userRole === UserRole.TEAM_LEADER && <ReplacementIndicators />}
    </CustomPageCard>
  );
};

export default Page;
