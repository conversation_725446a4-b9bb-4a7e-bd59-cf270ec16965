# My Team Store Structure

This directory contains the state management stores for the My Team module, organized by role to improve maintainability and debugging.

## Store Files

### `myTeamStore.ts`

**Purpose**: General attendance management and shared functionality across all roles.

**Key Features**:

- Visual check timers and states
- Backup basket management
- Department clerk and shift leader specific functionality
- Team leader filter management (UI only - no API calls)
- General attendance marking and history
- Mock data for non-Team Leader roles

**Used By**: All roles (Team Leader, Shift Leader, Department Clerk, Quality Supervisor)

### `teamLeaderStore.ts` (NEW)

**Purpose**: Team Leader specific API calls and data management.

**Key Features**:

- Team data fetching (`/api/teams/{teamId}/shift_instant/history`)
- Current status management (`/api/teams/{teamId}/current`)
- Operator status updates (`/api/shift_instant/{shiftInstantId}/operators/{operatorId}/status`)
- Status history tracking
- Loading and error state management

**Used By**: Team Leader role only

### `replacementStore.ts`

**Purpose**: Replacement and backup functionality.

**Key Features**:

- Backup operator management
- Replacement requests
- Shift leader backup operations

**Used By**: Shift Leader and Team Leader roles

## API Endpoints for Team Leader

The `teamLeaderStore.ts` handles the following API endpoints:

1. **GET** `/api/teams/{teamId}/shift_instant/history`

   - Fetches team data and operator history
   - Parameters: `startDate`

2. **GET** `/api/teams/{teamId}/current`

   - Fetches current status for all operators in a team
   - Parameters: `date`

3. **PUT** `/api/shift_instant/{shiftInstantId}/operators/{operatorId}/status`
   - Updates operator status
   - Body: `{ status: AttendanceStatus }`

## Benefits of Store Separation

1. **Easier Debugging**: Each role has its own store, making it easier to trace issues
2. **Better Performance**: Only load data relevant to the current role
3. **Maintainability**: Clear separation of concerns
4. **Type Safety**: Role-specific types and interfaces
5. **Testing**: Easier to test individual store functionality

## Usage Examples

### Team Leader Store Usage

```typescript
import { useTeamLeaderStore } from "../store/teamLeaderStore";

// In a component
const {
  teamData,
  currentStatusData,
  isTeamDataLoading,
  fetchTeamData,
  updateOperatorStatus,
  getTeamOperators,
  getCurrentStatusForOperator,
} = useTeamLeaderStore();

// Fetch team data when team is selected
useEffect(() => {
  if (selectedTeam) {
    fetchTeamData(selectedTeam, currentDate);
  }
}, [selectedTeam, currentDate]);
```

### General Store Usage

```typescript
import { useAttendanceStore } from "../store/myTeamStore";

// In a component
const { workers, isVisualCheckActive, startVisualCheck, markAttendance } =
  useAttendanceStore();
```

## Migration Notes

When migrating from the old single store approach:

1. **Team Leader components** now use both `useAttendanceStore` (for general functionality) and `useTeamLeaderStore` (for team-specific data)
2. **Other roles** continue to use only `useAttendanceStore` (no changes needed)
3. **Team data** for Team Leader now comes from `teamLeaderStore` instead of `myTeamStore`
4. **Status updates** for Team Leader use the new API endpoints through `teamLeaderStore`
5. **No duplicate API calls** - team leader APIs have been completely removed from `myTeamStore`

## Testing

Each store has its own test file:

- `myTeamStore.test.ts`
- `teamLeaderStore.test.ts`
- `replacementStore.test.ts`

Run tests with: `npm test`
