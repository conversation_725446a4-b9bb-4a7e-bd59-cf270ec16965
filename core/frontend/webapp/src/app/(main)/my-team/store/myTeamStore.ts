import { create } from "zustand";
import { useMockAuthStore } from "@/store/mockAuthStore";
import { SimpleMockUser } from "@/data/mockUsers";
import {
  TeamLeaderFilterState,
  ZoningOptionsResponse,
  // StatusHistoryEntry,
  // TeamOperator,
  // TeamLeader
} from "../types/MyTeamTypes";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";

// Helper function to get current user ID
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID
  return "2443_MAR Morocco 3";
};

// Helper function to normalize level names (remove _id suffix)
const normalizeLevel = (level: string): string => {
  return level.replace(/_id$/, "");
};

// Helper to extract backend error message
interface ErrorDetails {
  error?: { message?: string };
}
interface ErrorResponseData {
  message?: string;
  details?: ErrorDetails;
}
interface AxiosLikeError {
  response?: { data?: ErrorResponseData };
  message?: string;
}
function getApiErrorMessage(error: unknown): string | undefined {
  if (!error) return undefined;
  if (typeof error === "object" && error !== null && "response" in error) {
    const errObj = error as AxiosLikeError;
    const data = errObj.response?.data;
    if (data) {
      if (data.details?.error?.message) return data.details.error.message;
      if (data.message) return data.message;
    }
    if (errObj.message) return errObj.message;
  }
  if (typeof error === "string") return error;
  return undefined;
}

export type AttendanceStatus = "P" | "CTN" | "MA" | "AB" | "REPLACE" | "";
export type ClockingValidationStatus =
  | "Not finished yet"
  | "Waiting validation..."
  | "Validated"
  | "Sent for review"
  | "Absent"
  | "";

// New interfaces for team API response
export interface TeamStatus {
  teamId: string;
  teamName: string;
  status: ClockingValidationStatus;
  workerCount: number;
  validatedCount: number;
  waitingCount: number;
  absentCount: number;
  notFinishedCount: number;
  reviewed?: boolean;
}

export interface Worker {
  id: string;
  mle: string;
  firstName: string;
  lastName: string;
  role: string;
  team?: string;
  line?: string;
  function?: string;
  category?: string;
  clockingValidation?: ClockingValidationStatus;
  clockingValidationStatus?: "Validated" | "Pending" | "Rejected";
  [key: string]: string | number | undefined;
}

export interface AttendanceRecord {
  workerId: string;
  date: string;
  status: AttendanceStatus;
}

export interface AttendanceHistoryEntry {
  id: string;
  workerId: string;
  date: string;
  timestamp: string;
  attendanceData: { [hour: number]: string };
  submittedBy: string;
}

export interface ReplacementHistory {
  id: string;
  replacedWorker: Worker;
  replacementWorker: Worker[];
  date: string;
  shift: string;
  feedbackRating?: "satisfied" | "medium" | "low" | null;
}

interface AttendanceState {
  workers: Worker[];
  attendanceRecords: AttendanceRecord[];
  attendanceHistory: AttendanceHistoryEntry[];
  isVisualCheckActive: boolean;
  visualCheckTimer: number;
  isMyBackupBasketActive: boolean;
  myBackupBasketTimer: number;
  isDepartmentBackupBasketActive: boolean;
  departmentBackupBasketTimer: number;
  filterMfgOnly: boolean;
  replacementHistory: ReplacementHistory[];
  currentDate: Date;
  shiftStartTime: string;
  shiftEndTime: string;
  teamStatuses: TeamStatus[];
  teamStatusesByWorker: { [workerId: string]: TeamStatus[] };
  clockingCategory: "IH" | "IS";
  attendanceSheetDetails: { [workerDateKey: string]: string[] };

  // New: selected team for shift leader validation view
  selectedValidationTeam: TeamStatus | null;
  setSelectedValidationTeam: (team: TeamStatus | null) => void;

  // Team leader filter state
  teamLeaderFilters: TeamLeaderFilterState;

  // Actions
  setWorkers: (workers: Worker[]) => void;
  markAttendance: (
    workerId: string,
    date: string,
    status: AttendanceStatus,
  ) => void;
  startVisualCheck: () => void;
  finishVisualCheck: () => void;
  setVisualCheckTimer: (time: number) => void;
  startMyBackupBasket: () => void;
  finishMyBackupBasket: () => void;
  setMyBackupBasketTimer: (time: number) => void;
  startDepartmentBackupBasket: () => void;
  finishDepartmentBackupBasket: () => void;
  setDepartmentBackupBasketTimer: (time: number) => void;
  toggleMfgFilter: () => void;
  setClockingCategory: (category: "IH" | "IS") => void;
  setCurrentDate: (date: Date) => void;
  getAttendanceForWorkerAndDate: (
    workerId: string,
    date: string,
  ) => AttendanceStatus;
  submitAttendanceSheet: (
    workerId: string,
    date: string,
    attendanceData: { [hour: number]: string },
    submittedBy: string,
  ) => void;
  getAttendanceHistory: (
    workerId: string,
    date: string,
  ) => AttendanceHistoryEntry[];
  switchUserAndReload: (user: SimpleMockUser | null) => void;
  getTeamStatuses: (workerId: string) => TeamStatus[];
  updateTeamStatus: (
    workerId: string,
    teamId: string,
    status: ClockingValidationStatus,
    reviewed?: boolean,
  ) => void;
  approveAttendanceSheet: () => void;
  getShiftLeaderTabCounts: () => {
    myTeamleaders: number;
    myOperators: number;
    myBackupBasket: number;
    departmentBackupBasket: number;
  };
  getDepartmentClerkTabCounts: () => {
    ih: number;
    is: number;
  };
  setAttendanceSheetDetails: (
    workerId: string,
    date: string,
    details: string[],
  ) => void;

  // Team leader filter actions
  fetchFilteredOptions: (filters: Record<string, string>) => Promise<void>;
  setSelectedCustomer: (value: string) => void;
  setSelectedProject: (value: string) => void;
  setSelectedFamily: (value: string) => void;
  setSelectedValueStream: (value: string) => void;
  setSelectedArea: (value: string) => void;
  setSelectedTeam: (value: string) => void;
  clearTeamLeaderFilters: () => void;
}

export const useAttendanceStore = create<AttendanceState>((set, get) => {
  const defaultTeamStatuses = [
    {
      teamId: "team-1",
      teamName: "TEAM 1",
      status: "Waiting validation..." as ClockingValidationStatus,
      workerCount: 8,
      validatedCount: 3,
      waitingCount: 4,
      absentCount: 1,
      notFinishedCount: 0,
    },
    {
      teamId: "team-2",
      teamName: "TEAM 2",
      status: "Validated" as ClockingValidationStatus,
      workerCount: 6,
      validatedCount: 6,
      waitingCount: 0,
      absentCount: 0,
      notFinishedCount: 0,
    },
    {
      teamId: "team-3",
      teamName: "TEAM 3",
      status: "Not finished yet" as ClockingValidationStatus,
      workerCount: 7,
      validatedCount: 2,
      waitingCount: 3,
      absentCount: 0,
      notFinishedCount: 2,
    },
    {
      teamId: "team-4",
      teamName: "TEAM 4",
      status: "Waiting validation..." as ClockingValidationStatus,
      workerCount: 5,
      validatedCount: 1,
      waitingCount: 3,
      absentCount: 1,
      notFinishedCount: 0,
    },
    {
      teamId: "team-5",
      teamName: "TEAM 5",
      status: "Absent" as ClockingValidationStatus,
      workerCount: 4,
      validatedCount: 0,
      waitingCount: 0,
      absentCount: 4,
      notFinishedCount: 0,
    },
  ];

  // Initialize teamStatusesByWorker for each worker
  const initialTeamStatusesByWorker: { [workerId: string]: TeamStatus[] } = {};
  const initialWorkers = [
    {
      id: "1001",
      mle: "aa005cbd-3e3e-4144-a833-84ce3fb0434e",
      firstName: "Charlie",
      lastName: "Brown",
      role: "ME STRUCTURE",
      function: "POLYVALENT",
      team: "TEAM 5",
      line: "BILLBOARD",
      category: "IH",
      clockingValidation: "Validated" as ClockingValidationStatus,
    },
    {
      id: "8129",
      mle: "6d786d7d-2b57-4229-b9f1-a740175803ad",
      firstName: "Salma",
      lastName: "Bennani",
      role: "ME STRUCTURE",
      function: "POLYVALENT",
      category: "IH",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "8972",
      mle: "a20d225b-40e9-4c6c-b4bb-1e137b2c62f6",
      firstName: "Yassir",
      lastName: "El Idrissi",
      role: "ME STRUCTURE",
      function: "OPERATOR",
      category: "IH",
      clockingValidation: "Validated" as ClockingValidationStatus,
    },
    {
      id: "8372",
      mle: "50aa9e9b-6615-4693-ad36-ad159d455a76",
      firstName: "Karim",
      lastName: "Tazi",
      role: "ME STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "3001",
      mle: "c4744ddb-0c07-4ec7-813c-2e3ac2b776ed",
      firstName: "Frank",
      lastName: "Black",
      role: "ME STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Validated" as ClockingValidationStatus,
    },
    {
      id: "1003",
      mle: "0ce6f429-74df-4787-aa7b-64a39969f253",
      firstName: "Michael",
      lastName: "Johnson",
      role: "MFG STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Sent for review" as ClockingValidationStatus,
    },
    {
      id: "2002",
      mle: "683d39f8-d575-4e9e-857a-7b02d51cfe8b",
      firstName: "Sarah",
      lastName: "Davis",
      role: "MFG STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "9370",
      mle: "6bf4e688-a5fb-428b-8e22-3745d19f9058",
      firstName: "MFG_Salma",
      lastName: "Bennani",
      role: "MFG STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Validated" as ClockingValidationStatus,
    },
    {
      id: "9656",
      mle: "d04ed28b-ad47-4cce-a623-f625ad4fa96a",
      firstName: "MFG_Yassir",
      lastName: "El Idrissi",
      role: "MFG STRUCTURE",
      function: "REPAIRER",
      category: "IS",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "9603",
      mle: "af2a6165-4893-495b-b4a4-ad3ff911a306",
      firstName: "MFG_Karim",
      lastName: "Tazi",
      role: "MFG STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "4003",
      mle: "a8f01d45-8021-4aa8-b1aa-0daf77ff49ee",
      firstName: "Robert",
      lastName: "Miller",
      role: "MFG STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "7890",
      mle: "7890",
      firstName: "Ahmed",
      lastName: "MEHDI",
      role: "ME STRUCTURE",
      function: "OPERATOR",
      team: "TEAM 3",
      line: "BILLBOARD",
      category: "IH",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "5002",
      mle: "5bb39774-e09d-4a39-94e2-5a428f7a3956",
      firstName: "Julia",
      lastName: "Ortiz",
      role: "MFG STRUCTURE",
      function: "OPERATOR",
      category: "IS",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    {
      id: "5678",
      mle: "5678",
      firstName: "Samir",
      lastName: "AZMI",
      role: "ME STRUCTURE",
      function: "OPERATOR",
      team: "TEAM 3",
      line: "BILLBOARD",
      category: "IH",
      clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    },
    // {
    //   id: "9012",
    //   mle: "9012",
    //   firstName: "Amine",
    //   lastName: "FASSI",
    //   role: "MFG STRUCTURE",
    //   function: "OPERATOR",
    //   category: "IS",
    //   clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    // },
    // {
    //   id: "3457",
    //   mle: "3457",
    //   firstName: "Imad",
    //   lastName: "HARRAK",
    //   role: "MFG STRUCTURE",
    //   function: "OPERATOR",
    //   category: "IS",
    //   clockingValidation: "Waiting validation..." as ClockingValidationStatus,
    // },
  ];

  initialWorkers.forEach((worker) => {
    initialTeamStatusesByWorker[worker.id] = defaultTeamStatuses.map(
      (team) => ({ ...team }),
    );
  });

  const replacementHistory: ReplacementHistory[] = initialWorkers.map(
    (worker) => ({
      id: `rep_${worker.id}`,
      replacedWorker: worker,
      replacementWorker: initialWorkers
        .filter((w) => w.id !== worker.id)
        .slice(0, 2),
      date: new Date().toISOString().split("T")[0],
      shift: "06:00 - 14:00",
    }),
  );

  return {
    workers: initialWorkers, // Keep mock workers for non-Team Leader roles
    attendanceRecords: [],
    replacementHistory: replacementHistory,
    teamLeaderFilters: {
      customerOptions: [],
      projectOptions: [],
      familyOptions: [],
      valueStreamOptions: [],
      areaOptions: [],
      teamOptions: [],
      selectedCustomer: "",
      selectedProject: "",
      selectedFamily: "",
      selectedValueStream: "",
      selectedArea: "",
      selectedTeam: "",
      isLoading: false,
    },
    attendanceHistory: [
      // Sample history entries for testing
      {
        id: "hist1",
        workerId: "7841",
        date: new Date().toISOString().split("T")[0],
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        attendanceData: {
          6: "P",
          7: "P",
          8: "P",
          9: "P",
          10: "P",
          11: "P",
          12: "AB",
          13: "AB",
        },
        submittedBy: "Team Leader A",
      },
      {
        id: "hist2",
        workerId: "7841",
        date: new Date().toISOString().split("T")[0],
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        attendanceData: {
          6: "P",
          7: "P",
          8: "CTN",
          9: "CTN",
          10: "P",
          11: "P",
          12: "P",
          13: "P",
        },
        submittedBy: "Supervisor B",
      },
    ],
    isVisualCheckActive: false,
    visualCheckTimer: 300, // 5 minutes in seconds
    isMyBackupBasketActive: false,
    myBackupBasketTimer: 300, // 5 minutes in seconds
    isDepartmentBackupBasketActive: false,
    departmentBackupBasketTimer: 300, // 5 minutes in seconds
    filterMfgOnly: false,
    currentDate: new Date(),
    shiftStartTime: "06:00",
    shiftEndTime: "14:00",
    teamStatuses: defaultTeamStatuses,
    teamStatusesByWorker: initialTeamStatusesByWorker,
    clockingCategory: "IH",
    attendanceSheetDetails: {},

    // New: selected team for shift leader validation view
    selectedValidationTeam: null,
    setSelectedValidationTeam: (team) => set({ selectedValidationTeam: team }),

    // New: Team data and status history
    teamData: null,
    statusHistory: {},
    isTeamDataLoading: false,
    teamDataError: null,

    // New: Current status data
    currentStatusData: null,
    isCurrentStatusLoading: false,
    currentStatusError: null,

    setWorkers: (workers) => set({ workers }),

    markAttendance: (workerId, date, status) => {
      set((state) => {
        const existingRecordIndex = state.attendanceRecords.findIndex(
          (rec) => rec.workerId === workerId && rec.date === date,
        );

        if (existingRecordIndex !== -1) {
          // Update existing record
          const updatedRecords = [...state.attendanceRecords];
          updatedRecords[existingRecordIndex] = {
            ...updatedRecords[existingRecordIndex],
            status,
          };
          return { attendanceRecords: updatedRecords };
        }
        // Add new record
        return {
          attendanceRecords: [
            ...state.attendanceRecords,
            { workerId, date, status },
          ],
        };
      });
    },

    startVisualCheck: () => set({ isVisualCheckActive: true }),

    finishVisualCheck: () => set({ isVisualCheckActive: false }),

    setVisualCheckTimer: (time) => set({ visualCheckTimer: time }),

    startMyBackupBasket: () => set({ isMyBackupBasketActive: true }),

    finishMyBackupBasket: () => set({ isMyBackupBasketActive: false }),

    setMyBackupBasketTimer: (time) => set({ myBackupBasketTimer: time }),

    startDepartmentBackupBasket: () =>
      set({ isDepartmentBackupBasketActive: true }),

    finishDepartmentBackupBasket: () =>
      set({ isDepartmentBackupBasketActive: false }),

    setDepartmentBackupBasketTimer: (time) =>
      set({ departmentBackupBasketTimer: time }),

    toggleMfgFilter: () =>
      set((state) => ({ filterMfgOnly: !state.filterMfgOnly })),

    setClockingCategory: (category) => set({ clockingCategory: category }),

    setCurrentDate: (date) => {
      set({ currentDate: date });
    },

    getAttendanceForWorkerAndDate: (workerId: string, date: string) => {
      // Check if there's a current attendance record
      const record = get().attendanceRecords.find(
        (rec) => rec.workerId === workerId && rec.date === date,
      );
      if (record) {
        return record.status;
      }

      return "" as AttendanceStatus;
    },

    submitAttendanceSheet: (workerId, date, attendanceData, submittedBy) => {
      set((state) => ({
        attendanceHistory: [
          ...state.attendanceHistory,
          {
            id: `hist_${Date.now()}`,
            workerId,
            date,
            timestamp: new Date().toISOString(),
            attendanceData,
            submittedBy,
          },
        ],
      }));
    },

    getAttendanceHistory: (workerId, date) => {
      return get().attendanceHistory.filter(
        (entry) => entry.workerId === workerId && entry.date === date,
      );
    },

    switchUserAndReload: (user: SimpleMockUser | null) => {
      useMockAuthStore.getState().setCurrentUser(user);
      window.location.reload();
    },

    getTeamStatuses: (workerId: string) => {
      const byWorker = get().teamStatusesByWorker;
      return byWorker[workerId] || [];
    },

    updateTeamStatus: (
      workerId: string,
      teamId: string,
      status: ClockingValidationStatus,
      reviewed?: boolean,
    ) =>
      set((state) => {
        const byWorker = { ...state.teamStatusesByWorker };
        const teams = byWorker[workerId] || [];
        byWorker[workerId] = teams.map((team) =>
          team.teamId === teamId ? { ...team, status, reviewed } : team,
        );
        return { teamStatusesByWorker: byWorker };
      }),

    approveAttendanceSheet: () => {
      // Implementation for approving attendance sheet
      console.log("Attendance sheet approved");
      // Here you would typically make an API call to approve the attendance sheet
      // For now, we'll just log the action
    },

    getShiftLeaderTabCounts: () => {
      const workers = get().workers;
      // Example logic, adjust as needed for your real data
      return {
        myTeamleaders: workers.filter((w) =>
          w.role?.toLowerCase().includes("teamleader"),
        ).length,
        myOperators: workers.filter((w) =>
          w.role?.toLowerCase().includes("operator"),
        ).length,
        myBackupBasket: workers.filter((w) =>
          w.role?.toLowerCase().includes("backup"),
        ).length,
        departmentBackupBasket: workers.filter((w) =>
          w.role?.toLowerCase().includes("department"),
        ).length,
      };
    },

    getDepartmentClerkTabCounts: () => {
      const workers = get().workers;
      return {
        ih: workers.filter((w) => w.category === "IH").length,
        is: workers.filter((w) => w.category === "IS").length,
      };
    },

    setAttendanceSheetDetails: (workerId, date, details) =>
      set((state) => ({
        attendanceSheetDetails: {
          ...state.attendanceSheetDetails,
          [`${workerId}_${date}`]: details,
        },
      })),

    // Team leader filter actions
    fetchFilteredOptions: async (filters: Record<string, string>) => {
      try {
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            isLoading: true,
          },
        }));

        const currentUserId = getCurrentUserId();

        // Extract the level from filters if provided
        const { level, ...otherFilters } = filters;

        const response = await api.get<ZoningOptionsResponse>(
          `/workstation/zoning/options/${currentUserId}`,
          {
            params: {
              level,
              ...otherFilters,
            },
          },
        );
        const { values, metadata } = response.data;

        // Update the appropriate options based on the level returned
        const responseLevel = metadata.level.toLowerCase();
        const formattedOptions = values.map((item) => ({
          id: item.id,
          label: item.value,
          value: item.value,
        }));

        // Map the level from API response to our store field names
        const levelMapping: Record<string, string> = {
          customer: "customerOptions",
          customer_id: "customerOptions",
          project: "projectOptions",
          project_id: "projectOptions",
          family: "familyOptions",
          family_id: "familyOptions",
          value_stream: "valueStreamOptions",
          value_stream_id: "valueStreamOptions",
          area: "areaOptions",
          area_id: "areaOptions",
          team: "teamOptions",
          team_id: "teamOptions",
          team_name: "teamOptions",
        };

        const optionsField = levelMapping[responseLevel];
        if (!optionsField) {
          console.error("Unknown level received from API:", responseLevel);
          return;
        }

        // Normalize the response level for consistent handling
        const normalizedResponseLevel = normalizeLevel(responseLevel);

        console.log(
          "Updating options for level:",
          responseLevel,
          "(normalized:",
          normalizedResponseLevel,
          ") with values:",
          formattedOptions,
        );

        set((state) => {
          const updates: Partial<TeamLeaderFilterState> = {
            [optionsField]: formattedOptions,
          };

          // Clear dependent fields if we're fetching a higher level
          if (normalizedResponseLevel === "customer") {
            updates.projectOptions = [];
            updates.familyOptions = [];
            updates.valueStreamOptions = [];
            updates.areaOptions = [];
            updates.teamOptions = [];
            updates.selectedProject = "";
            updates.selectedFamily = "";
            updates.selectedValueStream = "";
            updates.selectedArea = "";
            updates.selectedTeam = "";
          } else if (normalizedResponseLevel === "project") {
            updates.familyOptions = [];
            updates.valueStreamOptions = [];
            updates.areaOptions = [];
            updates.teamOptions = [];
            updates.selectedFamily = "";
            updates.selectedValueStream = "";
            updates.selectedArea = "";
            updates.selectedTeam = "";
          } else if (normalizedResponseLevel === "family") {
            updates.valueStreamOptions = [];
            updates.areaOptions = [];
            updates.teamOptions = [];
            updates.selectedValueStream = "";
            updates.selectedArea = "";
            updates.selectedTeam = "";
          } else if (normalizedResponseLevel === "value_stream") {
            updates.areaOptions = [];
            updates.teamOptions = [];
            updates.selectedArea = "";
            updates.selectedTeam = "";
          } else if (normalizedResponseLevel === "area") {
            updates.teamOptions = [];
            updates.selectedTeam = "";
          }

          return {
            teamLeaderFilters: {
              ...state.teamLeaderFilters,
              ...updates,
              isLoading: false,
            },
          };
        });
      } catch (error) {
        console.error("Error fetching filtered options:", error);
        const msg = getApiErrorMessage(error);
        if (msg) {
          toast({ title: "Error", description: msg, variant: "destructive" });
        }
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            isLoading: false,
          },
        }));
      }
    },

    setSelectedCustomer: async (value: string) => {
      set((state) => ({
        teamLeaderFilters: {
          ...state.teamLeaderFilters,
          selectedCustomer: value,
        },
      }));

      if (value) {
        await get().fetchFilteredOptions({ customer_id: value });
      } else {
        // Clear all dependent fields when customer is deselected
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            projectOptions: [],
            familyOptions: [],
            valueStreamOptions: [],
            areaOptions: [],
            teamOptions: [],
            selectedProject: "",
            selectedFamily: "",
            selectedValueStream: "",
            selectedArea: "",
            selectedTeam: "",
          },
        }));
      }
    },

    setSelectedProject: async (value: string) => {
      const { selectedCustomer } = get().teamLeaderFilters;

      set((state) => ({
        teamLeaderFilters: {
          ...state.teamLeaderFilters,
          selectedProject: value,
        },
      }));

      if (value && selectedCustomer) {
        await get().fetchFilteredOptions({
          customer_id: selectedCustomer,
          project_id: value,
        });
      } else {
        // Clear dependent fields when project is deselected
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            familyOptions: [],
            valueStreamOptions: [],
            areaOptions: [],
            teamOptions: [],
            selectedFamily: "",
            selectedValueStream: "",
            selectedArea: "",
            selectedTeam: "",
          },
        }));
      }
    },

    setSelectedFamily: async (value: string) => {
      const { selectedCustomer, selectedProject } = get().teamLeaderFilters;

      set((state) => ({
        teamLeaderFilters: {
          ...state.teamLeaderFilters,
          selectedFamily: value,
        },
      }));

      if (value && selectedCustomer && selectedProject) {
        await get().fetchFilteredOptions({
          customer_id: selectedCustomer,
          project_id: selectedProject,
          family_id: value,
        });
      } else {
        // Clear dependent fields when family is deselected
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            valueStreamOptions: [],
            areaOptions: [],
            teamOptions: [],
            selectedValueStream: "",
            selectedArea: "",
            selectedTeam: "",
          },
        }));
      }
    },

    setSelectedValueStream: async (value: string) => {
      const { selectedCustomer, selectedProject, selectedFamily } =
        get().teamLeaderFilters;

      set((state) => ({
        teamLeaderFilters: {
          ...state.teamLeaderFilters,
          selectedValueStream: value,
        },
      }));

      if (value && selectedCustomer && selectedProject && selectedFamily) {
        await get().fetchFilteredOptions({
          customer_id: selectedCustomer,
          project_id: selectedProject,
          family_id: selectedFamily,
          value_stream_id: value,
        });
      } else {
        // Clear dependent fields when value stream is deselected
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            areaOptions: [],
            teamOptions: [],
            selectedArea: "",
            selectedTeam: "",
          },
        }));
      }
    },

    setSelectedArea: async (value: string) => {
      const {
        selectedCustomer,
        selectedProject,
        selectedFamily,
        selectedValueStream,
      } = get().teamLeaderFilters;

      set((state) => ({
        teamLeaderFilters: {
          ...state.teamLeaderFilters,
          selectedArea: value,
        },
      }));

      if (
        value &&
        selectedCustomer &&
        selectedProject &&
        selectedFamily &&
        selectedValueStream
      ) {
        await get().fetchFilteredOptions({
          customer_id: selectedCustomer,
          project_id: selectedProject,
          family_id: selectedFamily,
          value_stream_id: selectedValueStream,
          area_id: value,
        });
      } else {
        // Clear dependent fields when area is deselected
        set((state) => ({
          teamLeaderFilters: {
            ...state.teamLeaderFilters,
            teamOptions: [],
            selectedTeam: "",
          },
        }));
      }
    },

    setSelectedTeam: async (value: string) => {
      console.log(`Setting selected team: ${value}`);

      set((state) => ({
        teamLeaderFilters: {
          ...state.teamLeaderFilters,
          selectedTeam: value,
        },
      }));
    },

    clearTeamLeaderFilters: () => {
      set(() => ({
        teamLeaderFilters: {
          customerOptions: [],
          projectOptions: [],
          familyOptions: [],
          valueStreamOptions: [],
          areaOptions: [],
          teamOptions: [],
          selectedCustomer: "",
          selectedProject: "",
          selectedFamily: "",
          selectedValueStream: "",
          selectedArea: "",
          selectedTeam: "",
          isLoading: false,
        },
      }));
    },
  };
});
