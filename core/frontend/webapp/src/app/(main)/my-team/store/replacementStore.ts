import { create } from "zustand";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";
// import { useMockAuthStore } from "@/store/mockAuthStore";

// Helper function to get current user ID
// const getCurrentUserId = (): string => {
//   const mockAuthState = useMockAuthStore.getState();
//   const currentUser = mockAuthState.currentUser;

//   // If mock user is selected, use their ID, otherwise use default
//   if (currentUser) {
//     return currentUser.id;
//   }

//   // Default fallback ID
//   return "2443_MAR Morocco 3";
// };

// Helper to extract backend error message
interface ErrorDetails {
  error?: { message?: string };
}
interface ErrorResponseData {
  message?: string;
  details?: ErrorDetails;
}
interface AxiosLikeError {
  response?: { data?: ErrorResponseData };
  message?: string;
}

function getApiErrorMessage(error: unknown): string | undefined {
  if (!error) return undefined;
  if (typeof error === "object" && error !== null && "response" in error) {
    const errObj = error as AxiosLikeError;
    const data = errObj.response?.data;
    if (data) {
      if (data.details?.error?.message) return data.details.error.message;
      if (data.message) return data.message;
    }
    if (errObj.message) return errObj.message;
  }
  if (typeof error === "string") return error;
  return undefined;
}

// Request interfaces
interface ReplacementSuggestionsRequest {
  absentEmployeeId: string;
  teamLeaderId: string;
  shiftId: string;
}

interface ReplacementOperator {
  id: string;
  leaveOwnStation: boolean;
}

interface SubmitReplacementRequest {
  replacementRequest: {
    absentEmployeeId: string;
    replacementOperator?: ReplacementOperator[];
    shiftId: string;
    workstationId: string;
    createdBy: string;
  };
}

interface SubmitReplacementResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    data: {
      absentDetails: string;
      replacement: ReplacementOperator[];
    };
  };
}

// Medium-level backup interfaces
interface MediumLevelBackupRequest {
  shiftleaderId: string;
  shiftId: string;
  skills: string[];
}

interface OperatorQualification {
  skillCode: string;
  skillLevel: string;
}

interface WorkstationChosenFor {
  id: string;
  name: string;
  requiredSkills: string[];
}

interface MediumLevelOperator {
  isValidChoice: boolean;
  operatorId: string;
  operatorLegacyId: number;
  operatorFirstName: string;
  operatorLastName: string;
  operatorQualifications: OperatorQualification[];
  isAvailable: boolean;
  workstationChosenFor: WorkstationChosenFor | null;
  operatorTeamName: string | null;
  projectHierarchy: string | null;
  teamleader: string | null;
}

type MediumLevelBackupResponse = MediumLevelOperator[];

// Medium-level requests interfaces
interface MediumLevelRequestsRequest {
  shiftLeaderId: string;
  shiftId: string;
  site: string;
}

interface RequestWorkstation {
  id: string;
  name: string;
  requiredSkills: string[];
  customer: string;
  project: string;
  family: string;
  valueStream: string;
  area: string;
  criticity: "NORMAL" | "MEDIUM" | "HIGH" | "CRITICAL";
}

interface RequestTeamLeader {
  id: string;
  legacyId?: number;
  firstName: string;
  lastName: string;
}

interface RequestReplacementOperator {
  id: string;
  legacyId: number;
  firstName: string;
  lastName: string;
  skills: string[];
}

interface MediumLevelRequest {
  requestId: string;
  workstation: RequestWorkstation;
  teamLeader: RequestTeamLeader;
  isCompleted: boolean;
  replacementOperator: RequestReplacementOperator | null;
}

type MediumLevelRequestsResponse = MediumLevelRequest[];

// Medium-level assign operators interfaces
interface OperatorAssignment {
  requestId: string;
  replacementOperatorId: string;
}

interface MediumLevelAssignOperatorsRequest {
  shiftLeaderId: string;
  shiftId: string;
  site: string;
  operatorAssignments: OperatorAssignment[];
}

interface MediumLevelAssignOperatorsResponse {
  message: string;
}

// Medium-level send backup to department interfaces
interface MediumLevelSendBackupToDepartmentRequest {
  operatorsIds: string[];
  shiftLeaderId: string;
  department: string;
  shiftId: string;
  site: string;
}

interface MediumLevelSendBackupToDepartmentResponse {
  message: string;
}

// Medium-level escalate interfaces
interface MediumLevelEscalateRequest {
  requestsIds: string[];
  site: string;
  shiftLeaderId: string;
}

interface MediumLevelEscalateResponse {
  message: string;
}

// High-level backup interfaces
interface HighLevelBackupRequest {
  department: string;
  shiftId: string;
  site: string;
  skills: string[];
}

interface HighLevelOperator {
  isValidChoice: boolean;
  operatorId: string;
  operatorLegacyId: number;
  operatorFirstName: string;
  operatorLastName: string;
  operatorQualifications: OperatorQualification[];
  isAvailable: boolean;
  shiftLeader: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
  };
  workstationChosenFor: WorkstationChosenFor | null;
  operatorTeamName: string | null;
  projectHierarchy: {
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
  } | null;
  teamleader: string | null;
}

type HighLevelBackupResponse = HighLevelOperator[];

// High-level requests interfaces
interface HighLevelRequestsRequest {
  shiftLeaderId: string;
  shiftId: string;
  site: string;
}

interface HighLevelRequest {
  requestId: string;
  workstation: RequestWorkstation;
  teamLeader: RequestTeamLeader;
  isCompleted: boolean;
  replacementOperator: RequestReplacementOperator | null;
}

type HighLevelRequestsResponse = HighLevelRequest[];

// High-level assign operators interfaces
interface HighLevelAssignOperatorsRequest {
  department: string;
  shiftId: string;
  site: string;
  operatorAssignments: OperatorAssignment[];
}

interface HighLevelAssignOperatorsResponse {
  message: string;
}

// High-level escalate interfaces
interface HighLevelEscalateRequest {
  requestsIds: string[];
  department: string;
  shiftId: string;
  site: string;
}

interface HighLevelEscalateResponse {
  message: string;
}

// Replacement History interfaces
interface HistoryAbsentOperator {
  id: string;
  firstName: string;
  lastName: string;
  legacyId: number;
}

interface HistoryReplacementOperator {
  id: string;
  firstName: string;
  lastName: string;
  legacyId: number;
  isMFG?: boolean;
  leaveOwnStation?: boolean;
}

interface HistoryTargetWorkstation {
  id: string;
  name: string;
  customer: string;
  project: string;
  family: string;
  valueStream: string;
  area: string;
  requiredSkills: string[];
  criticity: string;
}

interface HistoryShift {
  id: string;
  startDate: string;
  endDate: string;
}

interface ReplacementHistoryItem {
  id: string;
  partitionKey: string;
  createdAt: string;
  updatedAt: string;
  version: number;
  type: string;
  deleted: boolean;
  metadata: Record<string, string>;
  absentOperator: HistoryAbsentOperator;
  replacementOperator: HistoryReplacementOperator[];
  targetWorkstation: HistoryTargetWorkstation;
  replacementLevel: "LOW" | "MEDIUM" | "HIGH";
  shiftId: string;
  shiftStartDate: string;
  shiftEndDate: string;
  site: string;
  absentTeamId: string;
  absentTeamName: string;
  createdBy: string;
  _rid: string;
  _self: string;
  _etag: string;
  _attachments: string;
  _ts: number;
}

interface ReplacementHistoryResponse {
  success: boolean;
  message: string;
  data: ReplacementHistoryItem[];
}

interface ReplacementHistoryRequest {
  isEvaluated?: boolean;
  createdBy?: string;
  shiftId?: string;
}

// Evaluation interfaces
interface EvaluationData {
  score: "LOW" | "MEDIUM" | "HIGH";
  comment?: string;
}

interface EvaluateReplacementRequest {
  replacementHistoryId: string;
  evaluation: EvaluationData;
}

interface EvaluateReplacementResponse {
  success: boolean;
  message: string;
  data?: EvaluationData;
}

// Raise Request interfaces
interface RaiseRequestEscalation {
  absentOperatorId: string;
  shiftId: string;
  teamLeaderId: string;
  reason: string;
}

interface RaiseRequestRequest {
  escalation: RaiseRequestEscalation;
}

interface RaiseRequestResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    data: {
      absentOperatorId: string;
      escalatedBy: string;
      escalatedTo: string;
      reason: string;
      escalatedAt: string;
      status: string;
    };
  };
}

// Send Backup interfaces
interface SendBackupRequest {
  backUpOperators: string[];
  shiftId: string;
  teamLeaderId: string;
}

interface SendBackupResponse {
  success: boolean;
  message: string;
  data?: string; // We don't care about the response data much, just the success flag
}

// Reset Replacement interfaces
interface ResetReplacementRequest {
  shiftId: string;
  teamLeaderId: string;
}

interface ResetReplacementResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
  };
}

// MFG Operators interfaces
interface MFGOperatorsRequest {
  teamLeaderId: string;
  shiftId: string;
  isMFG?: boolean;
  isUsed?: boolean;
}

interface MFGOperatorsResponse {
  success: boolean;
  message: string;
  data: Team[];
}

// Backup Movements interfaces
interface BackupMovementsRequest {
  teamLeaderId: string;
  shiftId: string;
}

interface WillReplaceItem {
  id: string;
  firstName: string;
  lastName: string;
  teamName: string;
  workstation: string;
  executedBy: string;
}

interface TakenWorker {
  id: string;
  firstName: string;
  lastName: string;
  teamName: string;
  isSentToAnotherBackup: boolean;
  willReplace: WillReplaceItem[];
}

interface ReplacementDetail {
  team: string;
  replacementName: string;
  executedBy: string;
  replacementId: string;
}

interface AddedWorker {
  id: string;
  firstName: string;
  lastName: string;
  teamName: string;
  isRaisedToShiftLeader: boolean;
  remplacedBy: ReplacementDetail[];
}

interface BackupMovementsData {
  taken: TakenWorker[];
  added: AddedWorker[];
}

interface BackupMovementsResponse {
  success: boolean;
  message: string;
  data: BackupMovementsData;
}

// Response interfaces
interface Skill {
  id: string;
  name: string;
  skillCode: string;
  qualification: string;
}

interface Workstation {
  id: string;
  workstation: string;
  isOwner: boolean;
  criticity: string;
  requiredSkills: string[];
  experience: string;
}

interface Operator {
  id: string;
  firstName: string;
  lastName: string;
  legacyId: number;
  skills: Skill[];
  status: string;
  workstation: Workstation[];
  isMFG: boolean;
  department: string;
  customer: string;
  project: string;
  family: string;
  valueStream: string;
  area: string;
  isSentToAnotherBackup: boolean;
  isUsed: boolean;
  isReplaced?: boolean;
}

interface Team {
  teamId: string;
  teamName: string;
  operators: Operator[];
}

interface ReplacementWorkstation {
  id: string;
  workstation: string;
  isOwner: boolean;
  criticity: string;
  requiredSkills: string[];
  experience: string;
}

interface ReplacementSuggestionsResponse {
  workstation: ReplacementWorkstation;
  teams: Team[];
}

// Store state interface
interface ReplacementState {
  // Data
  replacementData: ReplacementSuggestionsResponse | null;
  replacementHistory: ReplacementHistoryItem[] | null;
  mfgOperators: Team[] | null;
  mediumLevelBackup: MediumLevelOperator[] | null;
  mediumLevelRequests: MediumLevelRequest[] | null;
  highLevelBackup: HighLevelOperator[] | null;
  highLevelRequests: HighLevelRequest[] | null;
  backupMovements: BackupMovementsData | null;

  // Request parameters
  absentEmployeeId: string;
  teamLeaderId: string;
  shiftId: string;

  // Loading states
  isLoading: boolean;
  isLoadingSuggestions: boolean;
  isSubmittingReplacement: boolean;
  isLoadingHistory: boolean;
  isEvaluatingReplacement: boolean;
  isRaisingRequest: boolean;
  isSendingBackup: boolean;
  isResettingReplacement: boolean;
  isLoadingMFGOperators: boolean;
  isLoadingMediumLevelBackup: boolean;
  isLoadingMediumLevelRequests: boolean;
  isAssigningMediumLevelOperators: boolean;
  isSendingBackupToDepartment: boolean;
  isEscalatingMediumLevel: boolean;
  isLoadingHighLevelBackup: boolean;
  isLoadingHighLevelRequests: boolean;
  isAssigningHighLevelOperators: boolean;
  isEscalatingHighLevel: boolean;
  isLoadingBackupMovements: boolean;

  // Error handling
  error: string | null;
  submitError: string | null;
  historyError: string | null;
  evaluationError: string | null;
  raiseRequestError: string | null;
  sendBackupError: string | null;
  resetReplacementError: string | null;
  mfgOperatorsError: string | null;
  mediumLevelBackupError: string | null;
  mediumLevelRequestsError: string | null;
  assignMediumLevelOperatorsError: string | null;
  sendBackupToDepartmentError: string | null;
  escalateMediumLevelError: string | null;
  highLevelBackupError: string | null;
  highLevelRequestsError: string | null;
  assignHighLevelOperatorsError: string | null;
  escalateHighLevelError: string | null;
  backupMovementsError: string | null;

  // Selected data
  selectedTeam: Team | null;
  selectedOperator: Operator | null;
  selectedOperators: string[];

  // UI state
  searchQuery: string;
  filteredTeams: Team[];

  // Pagination for teams/operators
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  // Sorting
  sorting: { id: string; desc: boolean }[];

  // Actions
  setAbsentEmployeeId: (id: string) => void;
  setTeamLeaderId: (id: string) => void;
  setShiftId: (id: string) => void;
  setSearchQuery: (query: string) => void;
  setSorting: (sorting: { id: string; desc: boolean }[]) => void;

  // API actions
  fetchReplacementSuggestions: () => Promise<void>;
  fetchOperatorById: (
    operatorId: string,
    teamLeaderId: string,
    shiftId: string,
  ) => Promise<Operator | undefined>;
  fetchReplacementHistory: (
    isEvaluated?: boolean,
    createdBy?: string,
    shiftId?: string,
  ) => Promise<void>;
  fetchMFGOperators: (
    teamLeaderId: string,
    shiftId: string,
    isMFG?: boolean,
    isUsed?: boolean,
  ) => Promise<void>;
  fetchBackupMovements: (
    teamLeaderId: string,
    shiftId: string,
  ) => Promise<void>;
  fetchMediumLevelBackup: (
    shiftleaderId: string,
    shiftId: string,
    skills: string[],
  ) => Promise<void>;
  fetchMediumLevelRequests: (
    shiftLeaderId: string,
    shiftId: string,
    site: string,
  ) => Promise<void>;
  assignMediumLevelOperators: (
    shiftLeaderId: string,
    shiftId: string,
    site: string,
    operatorAssignments: OperatorAssignment[],
  ) => Promise<MediumLevelAssignOperatorsResponse | undefined>;
  sendBackupToDepartment: (
    operatorsIds: string[],
    shiftLeaderId: string,
    department: string,
    shiftId: string,
    site: string,
  ) => Promise<MediumLevelSendBackupToDepartmentResponse | undefined>;
  escalateMediumLevel: (
    requestsIds: string[],
    site: string,
    shiftLeaderId: string,
  ) => Promise<MediumLevelEscalateResponse | undefined>;
  fetchHighLevelBackup: (
    department: string,
    shiftId: string,
    site: string,
    skills: string[],
  ) => Promise<void>;
  fetchHighLevelRequests: (
    shiftLeaderId: string,
    shiftId: string,
    site: string,
  ) => Promise<void>;
  assignHighLevelOperators: (
    department: string,
    shiftId: string,
    site: string,
    operatorAssignments: OperatorAssignment[],
  ) => Promise<HighLevelAssignOperatorsResponse | undefined>;
  escalateHighLevel: (
    requestsIds: string[],
    department: string,
    shiftId: string,
    site: string,
  ) => Promise<HighLevelEscalateResponse | undefined>;
  evaluateReplacement: (
    replacementHistoryId: string,
    evaluation: EvaluationData,
  ) => Promise<EvaluateReplacementResponse | undefined>;
  submitReplacementRequest: (
    workstationId: string,
    createdBy: string,
    replacementOperators?: ReplacementOperator[],
  ) => Promise<SubmitReplacementResponse | undefined>;
  raiseRequestToShiftLeader: (
    absentOperatorId: string,
    shiftId: string,
    teamLeaderId: string,
    reason: string,
  ) => Promise<RaiseRequestResponse | undefined>;
  sendBackupToShiftLeader: (
    backUpOperators: string[],
    shiftId: string,
    teamLeaderId: string,
  ) => Promise<SendBackupResponse | undefined>;
  resetReplacement: (
    shiftId: string,
    teamLeaderId: string,
  ) => Promise<ResetReplacementResponse | undefined>;

  // Selection actions
  setSelectedTeam: (team: Team | null) => void;
  setSelectedOperator: (operator: Operator | null) => void;
  toggleOperatorSelection: (operatorId: string) => void;
  selectAllOperators: () => void;
  clearOperatorSelection: () => void;
  setSelectedOperators: (operatorIds: string[]) => void;

  // Filter actions
  filterTeamsByQuery: () => void;
  getOperatorsByTeam: (teamId: string) => Operator[];
  getAvailableOperators: () => Operator[];
  getOperatorsBySkill: (skillCode: string) => Operator[];
  getMFGOperatorsByTeam: (teamId: string) => Operator[];
  getAllMFGOperators: () => Operator[];
  getAvailableMFGOperators: () => Operator[];
  getAvailableMediumLevelOperators: () => MediumLevelOperator[];
  getValidMediumLevelOperators: () => MediumLevelOperator[];
  getCompletedMediumLevelRequests: () => MediumLevelRequest[];
  getPendingMediumLevelRequests: () => MediumLevelRequest[];
  getMediumLevelRequestsByTeamLeader: (
    teamLeaderId: string,
  ) => MediumLevelRequest[];
  getAvailableHighLevelOperators: () => HighLevelOperator[];
  getValidHighLevelOperators: () => HighLevelOperator[];
  getCompletedHighLevelRequests: () => HighLevelRequest[];
  getPendingHighLevelRequests: () => HighLevelRequest[];
  getHighLevelRequestsByTeamLeader: (
    teamLeaderId: string,
  ) => HighLevelRequest[];

  // Utility actions
  clearError: () => void;
  clearSubmitError: () => void;
  clearHistoryError: () => void;
  clearEvaluationError: () => void;
  clearRaiseRequestError: () => void;
  clearSendBackupError: () => void;
  clearResetReplacementError: () => void;
  clearMFGOperatorsError: () => void;
  clearBackupMovementsError: () => void;
  clearMediumLevelBackupError: () => void;
  clearMediumLevelRequestsError: () => void;
  clearAssignMediumLevelOperatorsError: () => void;
  clearSendBackupToDepartmentError: () => void;
  clearEscalateMediumLevelError: () => void;
  clearHighLevelBackupError: () => void;
  clearHighLevelRequestsError: () => void;
  clearAssignHighLevelOperatorsError: () => void;
  clearEscalateHighLevelError: () => void;
  resetStore: () => void;

  // Pagination actions
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
}

const useReplacementStore = create<ReplacementState>((set, get) => ({
  // Initial data
  replacementData: null,
  replacementHistory: null,
  mfgOperators: null,
  mediumLevelBackup: null,
  mediumLevelRequests: null,
  highLevelBackup: null,
  highLevelRequests: null,
  backupMovements: null,

  // Request parameters
  absentEmployeeId: "",
  teamLeaderId: "",
  shiftId: "",

  // Loading states
  isLoading: false,
  isLoadingSuggestions: false,
  isSubmittingReplacement: false,
  isLoadingHistory: false,
  isEvaluatingReplacement: false,
  isRaisingRequest: false,
  isSendingBackup: false,
  isResettingReplacement: false,
  isLoadingMFGOperators: false,
  isLoadingMediumLevelBackup: false,
  isLoadingMediumLevelRequests: false,
  isAssigningMediumLevelOperators: false,
  isSendingBackupToDepartment: false,
  isEscalatingMediumLevel: false,
  isLoadingHighLevelBackup: false,
  isLoadingHighLevelRequests: false,
  isAssigningHighLevelOperators: false,
  isEscalatingHighLevel: false,
  isLoadingBackupMovements: false,

  // Error handling
  error: null,
  submitError: null,
  historyError: null,
  evaluationError: null,
  raiseRequestError: null,
  sendBackupError: null,
  resetReplacementError: null,
  mfgOperatorsError: null,
  mediumLevelBackupError: null,
  mediumLevelRequestsError: null,
  assignMediumLevelOperatorsError: null,
  sendBackupToDepartmentError: null,
  escalateMediumLevelError: null,
  highLevelBackupError: null,
  highLevelRequestsError: null,
  assignHighLevelOperatorsError: null,
  escalateHighLevelError: null,
  backupMovementsError: null,

  // Selected data
  selectedTeam: null,
  selectedOperator: null,
  selectedOperators: [],

  // UI state
  searchQuery: "",
  filteredTeams: [],

  // Pagination
  pagination: {
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },

  // Sorting
  sorting: [],

  // Setters for request parameters
  setAbsentEmployeeId: (id: string) => {
    set({ absentEmployeeId: id });
  },

  setTeamLeaderId: (id: string) => {
    set({ teamLeaderId: id });
  },

  setShiftId: (id: string) => {
    set({ shiftId: id });
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
    get().filterTeamsByQuery();
  },

  setSorting: (sorting: { id: string; desc: boolean }[]) => {
    set({ sorting });
  },

  // Main API action
  fetchReplacementSuggestions: async () => {
    const { absentEmployeeId, teamLeaderId, shiftId } = get();

    if (!absentEmployeeId || !teamLeaderId || !shiftId) {
      const errorMessage =
        "Missing required parameters: absentEmployeeId, teamLeaderId, or shiftId";
      set({ error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({ isLoadingSuggestions: true, error: null });

    try {
      const response = await api.get<ReplacementSuggestionsResponse>(
        "/replacement/replacement/suggestions",
        {
          params: {
            absentEmployeeId,
            teamLeaderId,
            shiftId,
          },
        },
      );

      const replacementData = response.data;

      set({
        replacementData,
        filteredTeams: replacementData.teams,
        pagination: {
          ...get().pagination,
          totalCount: replacementData.teams.length,
          totalPages: Math.ceil(
            replacementData.teams.length / get().pagination.pageSize,
          ),
        },
        isLoadingSuggestions: false,
        error: null,
      });

      toast({
        title: "Success",
        description: "Replacement suggestions loaded successfully",
        variant: "success",
      });
    } catch (error) {
      console.error("Failed to fetch replacement suggestions:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch replacement suggestions";

      set({
        error: errorMessage,
        isLoadingSuggestions: false,
        replacementData: null,
        filteredTeams: [],
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // Fetch operator by ID
  fetchOperatorById: async (
    operatorId: string,
    teamLeaderId: string,
    shiftId: string,
  ): Promise<Operator | undefined> => {
    if (!operatorId || !teamLeaderId || !shiftId) {
      console.error("Missing required parameters for fetchOperatorById");
      return undefined;
    }

    try {
      const response = await api.get(
        `/replacement/replacement/operators/${operatorId}`,
        {
          params: {
            teamLeaderId,
            shiftId,
          },
        },
      );

      // Extract operator from the response
      const operatorData = response.data?.data?.operator;

      if (operatorData) {
        return operatorData as Operator;
      }

      console.warn("No operator data found in response");
      return undefined;
    } catch (error) {
      console.error("Failed to fetch operator by ID:", error);
      return undefined;
    }
  },

  // Fetch replacement history
  fetchReplacementHistory: async (
    isEvaluated?: boolean,
    createdBy?: string,
    shiftId?: string,
  ) => {
    set({ isLoadingHistory: true, historyError: null });

    try {
      // Build query parameters
      const params: Record<string, boolean | string> = {};

      if (isEvaluated !== undefined) {
        params.isEvaluated = isEvaluated;
      }

      if (createdBy) {
        params.createdBy = createdBy;
      }

      if (shiftId) {
        params.shiftId = shiftId;
      }

      const response = await api.get<ReplacementHistoryResponse>(
        "/replacement/replacement/history",
        Object.keys(params).length > 0 ? { params } : undefined,
      );

      const historyData = response.data.data;

      set({
        replacementHistory: historyData,
        isLoadingHistory: false,
        historyError: null,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch replacement history";
      console.error("Failed to fetch replacement history:", error);
      set({
        historyError: errorMessage,
        isLoadingHistory: false,
        replacementHistory: null,
      });
    }
  },

  // Fetch MFG operators
  fetchMFGOperators: async (
    teamLeaderId: string,
    shiftId: string,
    isMFG: boolean = true,
    isUsed: boolean = false,
  ) => {
    if (!teamLeaderId || !shiftId) {
      set({
        mfgOperatorsError:
          "Missing required parameters: teamLeaderId or shiftId",
      });
      return;
    }

    set({ isLoadingMFGOperators: true, mfgOperatorsError: null });

    try {
      const response = await api.get<MFGOperatorsResponse>(
        "/replacement/operators",
        {
          params: {
            teamLeaderId,
            shiftId,
            isMFG,
            isUsed,
          },
        },
      );

      const operatorsData = response.data.data;

      set({
        mfgOperators: operatorsData,
        isLoadingMFGOperators: false,
        mfgOperatorsError: null,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch MFG operators";
      console.error("Failed to fetch MFG operators:", error);
      set({
        mfgOperatorsError: errorMessage,
        isLoadingMFGOperators: false,
        mfgOperators: null,
      });
    }
  },

  // Fetch backup movements
  fetchBackupMovements: async (teamLeaderId: string, shiftId: string) => {
    if (!teamLeaderId || !shiftId) {
      set({
        backupMovementsError:
          "Missing required parameters: teamLeaderId or shiftId",
      });
      return;
    }

    set({ isLoadingBackupMovements: true, backupMovementsError: null });

    try {
      const response = await api.get<BackupMovementsResponse>(
        "replacement/replacement/backup-movements/",
        {
          params: {
            teamLeaderId,
            shiftId,
          },
        },
      );

      const backupMovementsData = response.data.data;

      set({
        backupMovements: backupMovementsData,
        isLoadingBackupMovements: false,
        backupMovementsError: null,
      });

      toast({
        title: "Success",
        description: "Backup movements loaded successfully",
        variant: "success",
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch backup movements";
      console.error("Failed to fetch backup movements:", error);
      set({
        backupMovementsError: errorMessage,
        isLoadingBackupMovements: false,
        backupMovements: null,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // Fetch medium-level backup
  fetchMediumLevelBackup: async (
    shiftleaderId: string,
    shiftId: string,
    skills?: string[],
  ) => {
    if (!shiftleaderId || !shiftId) {
      set({
        mediumLevelBackupError:
          "Missing required parameters: shiftleaderId, shiftId, or skills",
      });
      return;
    }

    set({ isLoadingMediumLevelBackup: true, mediumLevelBackupError: null });

    try {
      // Build query parameters with multiple skills
      const params = new URLSearchParams();
      params.append("shiftleaderId", shiftleaderId);
      params.append("shiftId", shiftId);
      skills?.forEach((skill) => params.append("skills", skill));

      const response = await api.get<MediumLevelBackupResponse>(
        `replacement/medium-level/backup?${params.toString()}`,
      );

      const mediumLevelData = response.data;

      set({
        mediumLevelBackup: mediumLevelData,
        isLoadingMediumLevelBackup: false,
        mediumLevelBackupError: null,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch medium-level backup";
      console.error("Failed to fetch medium-level backup:", error);
      set({
        mediumLevelBackupError: errorMessage,
        isLoadingMediumLevelBackup: false,
        mediumLevelBackup: null,
      });
    }
  },

  // Fetch medium-level requests
  fetchMediumLevelRequests: async (
    shiftLeaderId: string,
    shiftId: string,
    site: string,
  ) => {
    if (!shiftLeaderId || !shiftId || !site) {
      set({
        mediumLevelRequestsError:
          "Missing required parameters: shiftLeaderId, shiftId, or site",
      });
      return;
    }

    set({ isLoadingMediumLevelRequests: true, mediumLevelRequestsError: null });

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("shiftLeaderId", shiftLeaderId);
      params.append("shiftId", shiftId);
      params.append("site", site);

      const response = await api.get<MediumLevelRequestsResponse>(
        `replacement/medium-level/requests?${params.toString()}`,
      );

      const requestsData = response.data;

      set({
        mediumLevelRequests: requestsData,
        isLoadingMediumLevelRequests: false,
        mediumLevelRequestsError: null,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch medium-level requests";
      console.error("Failed to fetch medium-level requests:", error);
      set({
        mediumLevelRequestsError: errorMessage,
        isLoadingMediumLevelRequests: false,
        mediumLevelRequests: null,
      });
    }
  },

  // Assign medium-level operators
  assignMediumLevelOperators: async (
    shiftLeaderId: string,
    shiftId: string,
    site: string,
    operatorAssignments: OperatorAssignment[],
  ) => {
    if (!shiftLeaderId || !shiftId || !site || !operatorAssignments.length) {
      const errorMessage =
        "Missing required parameters: shiftLeaderId, shiftId, site, or operatorAssignments";
      set({ assignMediumLevelOperatorsError: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({
      isAssigningMediumLevelOperators: true,
      assignMediumLevelOperatorsError: null,
    });

    try {
      const requestBody: MediumLevelAssignOperatorsRequest = {
        shiftLeaderId,
        shiftId,
        site,
        operatorAssignments,
      };

      const response = await api.post<MediumLevelAssignOperatorsResponse>(
        "replacement/medium-level/assign-operators",
        requestBody,
      );

      console.log(
        "Medium-level operators assigned successfully:",
        response.data,
      );

      set({
        isAssigningMediumLevelOperators: false,
        assignMediumLevelOperatorsError: null,
      });

      toast({
        title: "Success",
        description: `Successfully assigned ${operatorAssignments.length} operator(s)`,
        variant: "success",
      });

      return response.data;
    } catch (error) {
      console.error("Failed to assign medium-level operators:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to assign medium-level operators";

      set({
        assignMediumLevelOperatorsError: errorMessage,
        isAssigningMediumLevelOperators: false,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // Send backup to department
  sendBackupToDepartment: async (
    operatorsIds: string[],
    shiftLeaderId: string,
    department: string,
    shiftId: string,
    site: string,
  ) => {
    if (
      !operatorsIds.length ||
      !shiftLeaderId ||
      !department ||
      !shiftId ||
      !site
    ) {
      const errorMessage =
        "Missing required parameters: operatorsIds, shiftLeaderId, department, shiftId, or site";
      set({ sendBackupToDepartmentError: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({
      isSendingBackupToDepartment: true,
      sendBackupToDepartmentError: null,
    });

    try {
      const requestBody: MediumLevelSendBackupToDepartmentRequest = {
        operatorsIds,
        shiftLeaderId,
        department,
        shiftId,
        site,
      };

      const response =
        await api.post<MediumLevelSendBackupToDepartmentResponse>(
          "replacement/medium-level/send-backup-to-department",
          requestBody,
        );

      console.log("Backup sent to department successfully:", response.data);

      set({
        isSendingBackupToDepartment: false,
        sendBackupToDepartmentError: null,
      });

      toast({
        title: "Success",
        description: `Successfully sent ${operatorsIds.length} operator(s) to ${department}`,
        variant: "success",
      });

      return response.data;
    } catch (error) {
      console.error("Failed to send backup to department:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to send backup to department";

      set({
        sendBackupToDepartmentError: errorMessage,
        isSendingBackupToDepartment: false,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // Escalate medium-level requests
  escalateMediumLevel: async (
    requestsIds: string[],
    site: string,
    shiftLeaderId: string,
  ) => {
    if (!requestsIds.length || !site || !shiftLeaderId) {
      const errorMessage =
        "Missing required parameters: requestsIds, site, or shiftLeaderId";
      set({ escalateMediumLevelError: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({ isEscalatingMediumLevel: true, escalateMediumLevelError: null });

    try {
      const requestBody: MediumLevelEscalateRequest = {
        requestsIds,
        site,
        shiftLeaderId,
      };

      const response = await api.post<MediumLevelEscalateResponse>(
        "replacement/medium-level/escalate",
        requestBody,
      );

      console.log(
        "Medium-level requests escalated successfully:",
        response.data,
      );

      // Remove escalated requests from the list
      const { mediumLevelRequests } = get();
      if (mediumLevelRequests) {
        const updatedRequests = mediumLevelRequests.filter(
          (request) => !requestsIds.includes(request.requestId),
        );

        set({
          mediumLevelRequests: updatedRequests,
          isEscalatingMediumLevel: false,
          escalateMediumLevelError: null,
        });
      } else {
        set({
          isEscalatingMediumLevel: false,
          escalateMediumLevelError: null,
        });
      }

      toast({
        title: "Success",
        description: `Successfully escalated ${requestsIds.length} request(s)`,
        variant: "success",
      });

      return response.data;
    } catch (error) {
      console.error("Failed to escalate medium-level requests:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to escalate medium-level requests";

      set({
        escalateMediumLevelError: errorMessage,
        isEscalatingMediumLevel: false,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // Fetch high-level backup
  fetchHighLevelBackup: async (
    department: string,
    shiftId: string,
    site: string,
    skills?: string[],
  ) => {
    if (!department || !shiftId || !site) {
      const errorMessage =
        "Missing required parameters: department, shiftId, or site";
      set({ highLevelBackupError: errorMessage });
      // Don't show toast for fetch functions as they are called automatically
      return;
    }

    set({ isLoadingHighLevelBackup: true, highLevelBackupError: null });

    try {
      // Build query parameters with multiple skills
      const params = new URLSearchParams();
      params.append("department", department);
      params.append("shiftId", shiftId);
      params.append("site", site);
      skills?.forEach((skill) => params.append("skills", skill));

      const response = await api.get<HighLevelBackupResponse>(
        `replacement/high-level/backup?${params.toString()}`,
      );

      const highLevelData = response.data;

      set({
        highLevelBackup: highLevelData,
        isLoadingHighLevelBackup: false,
        highLevelBackupError: null,
      });
    } catch (error) {
      console.error("Failed to fetch high-level backup:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch high-level backup";

      set({
        highLevelBackupError: errorMessage,
        isLoadingHighLevelBackup: false,
        highLevelBackup: null,
      });

      // Only show toast for unexpected errors, not for normal API failures
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as { response?: { status?: number } };
        if (axiosError?.response?.status && axiosError.response.status >= 500) {
          toast({
            title: "Error",
            description: "Server error occurred while fetching backup data",
            variant: "destructive",
          });
        }
      }
    }
  },

  // Fetch high-level requests
  fetchHighLevelRequests: async (
    shiftLeaderId: string,
    shiftId: string,
    site: string,
  ) => {
    if (!shiftLeaderId || !shiftId || !site) {
      set({
        highLevelRequestsError:
          "Missing required parameters: shiftLeaderId, shiftId, or site",
      });
      return;
    }

    set({ isLoadingHighLevelRequests: true, highLevelRequestsError: null });

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append("shiftLeaderId", shiftLeaderId);
      params.append("shiftId", shiftId);
      params.append("site", site);

      const response = await api.get<HighLevelRequestsResponse>(
        `replacement/high-level/requests?${params.toString()}`,
      );

      const requestsData = response.data;

      set({
        highLevelRequests: requestsData,
        isLoadingHighLevelRequests: false,
        highLevelRequestsError: null,
      });
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to fetch high-level requests";
      console.error("Failed to fetch high-level requests:", error);
      set({
        highLevelRequestsError: errorMessage,
        isLoadingHighLevelRequests: false,
        highLevelRequests: null,
      });
    }
  },

  // Assign high-level operators
  assignHighLevelOperators: async (
    department: string,
    shiftId: string,
    site: string,
    operatorAssignments: OperatorAssignment[],
  ) => {
    if (!department || !shiftId || !site || !operatorAssignments.length) {
      const errorMessage =
        "Missing required parameters: department, shiftId, site, or operatorAssignments";
      set({ assignHighLevelOperatorsError: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({
      isAssigningHighLevelOperators: true,
      assignHighLevelOperatorsError: null,
    });

    try {
      const requestBody: HighLevelAssignOperatorsRequest = {
        department,
        shiftId,
        site,
        operatorAssignments,
      };

      const response = await api.post<HighLevelAssignOperatorsResponse>(
        "replacement/high-level/assign-operators",
        requestBody,
      );

      console.log("High-level operators assigned successfully:", response.data);

      set({
        isAssigningHighLevelOperators: false,
        assignHighLevelOperatorsError: null,
      });

      toast({
        title: "Success",
        description: `Successfully assigned ${operatorAssignments.length} operator(s)`,
        variant: "success",
      });

      return response.data;
    } catch (error) {
      console.error("Failed to assign high-level operators:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to assign high-level operators";

      set({
        assignHighLevelOperatorsError: errorMessage,
        isAssigningHighLevelOperators: false,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // Escalate high-level requests
  escalateHighLevel: async (
    requestsIds: string[],
    department: string,
    shiftId: string,
    site: string,
  ) => {
    if (!requestsIds.length || !department || !shiftId || !site) {
      const errorMessage =
        "Missing required parameters: requestsIds, department, shiftId, or site";
      set({ escalateHighLevelError: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({ isEscalatingHighLevel: true, escalateHighLevelError: null });

    try {
      const requestBody: HighLevelEscalateRequest = {
        requestsIds,
        department,
        shiftId,
        site,
      };

      const response = await api.post<HighLevelEscalateResponse>(
        "replacement/high-level/escalate",
        requestBody,
      );

      console.log("High-level requests escalated successfully:", response.data);

      // Remove escalated requests from the list
      const { highLevelRequests } = get();
      if (highLevelRequests) {
        const updatedRequests = highLevelRequests.filter(
          (request) => !requestsIds.includes(request.requestId),
        );

        set({
          highLevelRequests: updatedRequests,
          isEscalatingHighLevel: false,
          escalateHighLevelError: null,
        });
      } else {
        set({
          isEscalatingHighLevel: false,
          escalateHighLevelError: null,
        });
      }

      toast({
        title: "Success",
        description: `Successfully escalated ${requestsIds.length} request(s)`,
        variant: "success",
      });

      return response.data;
    } catch (error) {
      console.error("Failed to escalate high-level requests:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to escalate high-level requests";

      set({
        escalateHighLevelError: errorMessage,
        isEscalatingHighLevel: false,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // Evaluate replacement
  evaluateReplacement: async (
    replacementHistoryId: string,
    evaluation: EvaluationData,
  ) => {
    if (!replacementHistoryId || !evaluation) {
      set({
        evaluationError:
          "Missing required parameters: replacementHistoryId or evaluation",
      });
      return;
    }

    set({ isEvaluatingReplacement: true, evaluationError: null });

    try {
      const requestBody: EvaluateReplacementRequest = {
        replacementHistoryId,
        evaluation,
      };

      const response = await api.post<EvaluateReplacementResponse>(
        "/replacement/replacement/evaluate-replacement",
        requestBody,
      );

      console.log(
        "Replacement evaluation submitted successfully:",
        response.data,
      );

      set({
        isEvaluatingReplacement: false,
        evaluationError: null,
      });

      return response.data;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to submit replacement evaluation";
      console.error("Failed to submit replacement evaluation:", error);
      set({
        evaluationError: errorMessage,
        isEvaluatingReplacement: false,
      });
      throw error;
    }
  },

  // Submit replacement request
  submitReplacementRequest: async (
    workstationId: string,
    createdBy: string,
    replacementOperators: ReplacementOperator[] = [],
  ) => {
    const { absentEmployeeId, shiftId } = get();

    if (!absentEmployeeId || !shiftId || !workstationId || !createdBy) {
      const errorMessage =
        "Missing required parameters: absentEmployeeId, shiftId, workstationId, or createdBy";
      set({ submitError: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    set({ isSubmittingReplacement: true, submitError: null });

    try {
      const requestBody: SubmitReplacementRequest = {
        replacementRequest: {
          absentEmployeeId,
          replacementOperator: replacementOperators,
          shiftId,
          workstationId,
          createdBy,
        },
      };

      const response = await api.post<SubmitReplacementResponse>(
        "/replacement/replacement/execute-replacement",
        requestBody,
      );

      console.log("Replacement request submitted successfully:", response.data);

      set({
        isSubmittingReplacement: false,
        submitError: null,
      });

      toast({
        title: "Success",
        description: "Replacement request submitted successfully",
        variant: "success",
      });

      return response.data;
    } catch (error) {
      console.error("Failed to submit replacement request:", error);
      const errorMessage =
        getApiErrorMessage(error) || "Failed to submit replacement request";

      set({
        submitError: errorMessage,
        isSubmittingReplacement: false,
      });

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    }
  },

  // Raise request to shift leader
  raiseRequestToShiftLeader: async (
    absentOperatorId: string,
    shiftId: string,
    teamLeaderId: string,
    reason: string,
  ) => {
    if (!absentOperatorId || !shiftId || !teamLeaderId || !reason) {
      set({
        raiseRequestError:
          "Missing required parameters: absentOperatorId, shiftId, teamLeaderId, or reason",
      });
      return;
    }

    set({ isRaisingRequest: true, raiseRequestError: null });

    try {
      const requestBody: RaiseRequestRequest = {
        escalation: {
          absentOperatorId,
          shiftId,
          teamLeaderId,
          reason,
        },
      };

      const response = await api.post<RaiseRequestResponse>(
        "/replacement/replacement/raise-replacement-request",
        requestBody,
      );

      console.log("Replacement request raised successfully:", response.data);

      set({
        isRaisingRequest: false,
        raiseRequestError: null,
      });

      return response.data;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to raise replacement request";
      console.error("Failed to raise replacement request:", error);
      set({
        raiseRequestError: errorMessage,
        isRaisingRequest: false,
      });
      throw error;
    }
  },

  // Send backup to shift leader
  sendBackupToShiftLeader: async (
    backUpOperators: string[],
    shiftId: string,
    teamLeaderId: string,
  ) => {
    if (!backUpOperators.length || !shiftId || !teamLeaderId) {
      set({
        sendBackupError:
          "Missing required parameters: backUpOperators, shiftId, or teamLeaderId",
      });
      return;
    }

    set({ isSendingBackup: true, sendBackupError: null });

    try {
      const requestBody: SendBackupRequest = {
        backUpOperators,
        shiftId,
        teamLeaderId,
      };

      const response = await api.post<SendBackupResponse>(
        "/replacement/replacement/send-to-shift-leader",
        requestBody,
      );

      console.log("Backup sent to shift leader successfully:", response.data);

      set({
        isSendingBackup: false,
        sendBackupError: null,
      });

      return response.data;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to send backup to shift leader";
      console.error("Failed to send backup to shift leader:", error);
      set({
        sendBackupError: errorMessage,
        isSendingBackup: false,
      });
      throw error;
    }
  },

  // Reset replacement
  resetReplacement: async (shiftId: string, teamLeaderId: string) => {
    if (!shiftId || !teamLeaderId) {
      set({
        resetReplacementError:
          "Missing required parameters: shiftId or teamLeaderId",
      });
      return;
    }

    set({ isResettingReplacement: true, resetReplacementError: null });

    try {
      const requestBody: ResetReplacementRequest = {
        shiftId,
        teamLeaderId,
      };

      const response = await api.post<ResetReplacementResponse>(
        "/replacement/replacement/reset-replacement",
        requestBody,
      );

      console.log("Replacement reset successfully:", response.data);

      set({
        isResettingReplacement: false,
        resetReplacementError: null,
      });

      return response.data;
    } catch (error) {
      const errorMessage =
        getApiErrorMessage(error) || "Failed to reset replacement";
      console.error("Failed to reset replacement:", error);
      set({
        resetReplacementError: errorMessage,
        isResettingReplacement: false,
      });
      throw error;
    }
  },

  // Selection actions
  setSelectedTeam: (team: Team | null) => {
    set({ selectedTeam: team, selectedOperator: null });
  },

  setSelectedOperator: (operator: Operator | null) => {
    set({ selectedOperator: operator });
  },

  toggleOperatorSelection: (operatorId: string) => {
    const { selectedOperators } = get();
    const isSelected = selectedOperators.includes(operatorId);

    if (isSelected) {
      set({
        selectedOperators: selectedOperators.filter((id) => id !== operatorId),
      });
    } else {
      set({
        selectedOperators: [...selectedOperators, operatorId],
      });
    }
  },

  selectAllOperators: () => {
    const { replacementData } = get();
    if (!replacementData) return;

    const allOperatorIds = replacementData.teams.flatMap((team) =>
      team.operators.map((operator) => operator.id),
    );

    set({ selectedOperators: allOperatorIds });
  },

  clearOperatorSelection: () => {
    set({ selectedOperators: [] });
  },

  setSelectedOperators: (operatorIds: string[]) => {
    set({ selectedOperators: operatorIds });
  },

  // Filter actions
  filterTeamsByQuery: () => {
    const { replacementData, searchQuery } = get();
    if (!replacementData) return;

    if (!searchQuery.trim()) {
      set({ filteredTeams: replacementData.teams });
      return;
    }

    const filtered = replacementData.teams.filter(
      (team) =>
        team.teamName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        team.operators.some(
          (operator) =>
            `${operator.firstName} ${operator.lastName}`
              .toLowerCase()
              .includes(searchQuery.toLowerCase()) ||
            operator.department
              .toLowerCase()
              .includes(searchQuery.toLowerCase()) ||
            operator.skills.some(
              (skill) =>
                skill.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                skill.skillCode
                  .toLowerCase()
                  .includes(searchQuery.toLowerCase()),
            ),
        ),
    );

    set({
      filteredTeams: filtered,
      pagination: {
        ...get().pagination,
        totalCount: filtered.length,
        totalPages: Math.ceil(filtered.length / get().pagination.pageSize),
        currentPage: 1,
      },
    });
  },

  getOperatorsByTeam: (teamId: string) => {
    const { replacementData } = get();
    if (!replacementData) return [];

    const team = replacementData.teams.find((t) => t.teamId === teamId);
    return team ? team.operators : [];
  },

  getAvailableOperators: () => {
    const { replacementData } = get();
    if (!replacementData) return [];

    return replacementData.teams.flatMap((team) =>
      team.operators.filter(
        (operator) => !operator.isUsed && !operator.isSentToAnotherBackup,
      ),
    );
  },

  getOperatorsBySkill: (skillCode: string) => {
    const { replacementData } = get();
    if (!replacementData) return [];

    return replacementData.teams.flatMap((team) =>
      team.operators.filter((operator) =>
        operator.skills.some((skill) => skill.skillCode === skillCode),
      ),
    );
  },

  getMFGOperatorsByTeam: (teamId: string) => {
    const { mfgOperators } = get();
    if (!mfgOperators) return [];

    const team = mfgOperators.find((t) => t.teamId === teamId);
    return team ? team.operators : [];
  },

  getAllMFGOperators: () => {
    const { mfgOperators } = get();
    if (!mfgOperators) return [];

    return mfgOperators.flatMap((team) => team.operators);
  },

  getAvailableMFGOperators: () => {
    const { mfgOperators } = get();
    if (!mfgOperators) return [];

    return mfgOperators.flatMap((team) =>
      team.operators.filter(
        (operator) => !operator.isUsed && !operator.isSentToAnotherBackup,
      ),
    );
  },

  getAvailableMediumLevelOperators: () => {
    const { mediumLevelBackup } = get();
    if (!mediumLevelBackup) return [];

    return mediumLevelBackup.filter((operator) => operator.isAvailable);
  },

  getValidMediumLevelOperators: () => {
    const { mediumLevelBackup } = get();
    if (!mediumLevelBackup) return [];

    return mediumLevelBackup.filter((operator) => operator.isValidChoice);
  },

  getCompletedMediumLevelRequests: () => {
    const { mediumLevelRequests } = get();
    if (!mediumLevelRequests) return [];

    return mediumLevelRequests.filter((request) => request.isCompleted);
  },

  getPendingMediumLevelRequests: () => {
    const { mediumLevelRequests } = get();
    if (!mediumLevelRequests) return [];

    return mediumLevelRequests.filter((request) => !request.isCompleted);
  },

  getMediumLevelRequestsByTeamLeader: (teamLeaderId: string) => {
    const { mediumLevelRequests } = get();
    if (!mediumLevelRequests) return [];

    return mediumLevelRequests.filter(
      (request) => request.teamLeader.id === teamLeaderId,
    );
  },

  getAvailableHighLevelOperators: () => {
    const { highLevelBackup } = get();
    if (!highLevelBackup) return [];

    return highLevelBackup.filter((operator) => operator.isAvailable);
  },

  getValidHighLevelOperators: () => {
    const { highLevelBackup } = get();
    if (!highLevelBackup) return [];

    return highLevelBackup.filter((operator) => operator.isValidChoice);
  },

  getCompletedHighLevelRequests: () => {
    const { highLevelRequests } = get();
    if (!highLevelRequests) return [];

    return highLevelRequests.filter((request) => request.isCompleted);
  },

  getPendingHighLevelRequests: () => {
    const { highLevelRequests } = get();
    if (!highLevelRequests) return [];

    return highLevelRequests.filter((request) => !request.isCompleted);
  },

  getHighLevelRequestsByTeamLeader: (teamLeaderId: string) => {
    const { highLevelRequests } = get();
    if (!highLevelRequests) return [];

    return highLevelRequests.filter(
      (request) => request.teamLeader.id === teamLeaderId,
    );
  },

  // Utility actions
  clearError: () => {
    set({ error: null });
  },

  clearMediumLevelRequestsError: () => {
    set({ error: null });
  },

  clearAssignMediumLevelOperatorsError: () => {
    set({ assignMediumLevelOperatorsError: null });
  },

  clearSendBackupToDepartmentError: () => {
    set({ sendBackupToDepartmentError: null });
  },

  clearEscalateMediumLevelError: () => {
    set({ escalateMediumLevelError: null });
  },

  clearHighLevelBackupError: () => {
    set({ highLevelBackupError: null });
  },

  clearHighLevelRequestsError: () => {
    set({ highLevelRequestsError: null });
  },

  clearAssignHighLevelOperatorsError: () => {
    set({ assignHighLevelOperatorsError: null });
  },

  clearEscalateHighLevelError: () => {
    set({ escalateHighLevelError: null });
  },

  clearSubmitError: () => {
    set({ submitError: null });
  },

  clearHistoryError: () => {
    set({ historyError: null });
  },

  clearEvaluationError: () => {
    set({ evaluationError: null });
  },

  clearRaiseRequestError: () => {
    set({ raiseRequestError: null });
  },

  clearSendBackupError: () => {
    set({ sendBackupError: null });
  },

  clearResetReplacementError: () => {
    set({ resetReplacementError: null });
  },

  clearMFGOperatorsError: () => {
    set({ mfgOperatorsError: null });
  },

  clearBackupMovementsError: () => {
    set({ backupMovementsError: null });
  },

  clearMediumLevelBackupError: () => {
    set({ mediumLevelBackupError: null });
  },

  resetStore: () => {
    set({
      replacementData: null,
      replacementHistory: null,
      mfgOperators: null,
      mediumLevelBackup: null,
      mediumLevelRequests: null,
      highLevelBackup: null,
      highLevelRequests: null,
      absentEmployeeId: "",
      teamLeaderId: "",
      shiftId: "",
      selectedTeam: null,
      selectedOperator: null,
      selectedOperators: [],
      searchQuery: "",
      filteredTeams: [],
      error: null,
      isLoading: false,
      isLoadingSuggestions: false,
      isSubmittingReplacement: false,
      isLoadingHistory: false,
      isEvaluatingReplacement: false,
      isRaisingRequest: false,
      isSendingBackup: false,
      isResettingReplacement: false,
      isLoadingMFGOperators: false,
      isLoadingMediumLevelBackup: false,
      isLoadingMediumLevelRequests: false,
      isLoadingHighLevelBackup: false,
      isLoadingHighLevelRequests: false,
      isAssigningHighLevelOperators: false,
      isEscalatingHighLevel: false,
      submitError: null,
      historyError: null,
      evaluationError: null,
      raiseRequestError: null,
      sendBackupError: null,
      resetReplacementError: null,
      mfgOperatorsError: null,
      mediumLevelBackupError: null,
      mediumLevelRequestsError: null,
      highLevelBackupError: null,
      highLevelRequestsError: null,
      assignHighLevelOperatorsError: null,
      escalateHighLevelError: null,
      pagination: {
        currentPage: 1,
        pageSize: 10,
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      },
      sorting: [],
    });
  },

  // Pagination actions
  setCurrentPage: (page: number) => {
    const { pagination } = get();
    set({
      pagination: {
        ...pagination,
        currentPage: page,
        hasNextPage: page < pagination.totalPages,
        hasPreviousPage: page > 1,
      },
    });
  },

  setPageSize: (size: number) => {
    const { filteredTeams, pagination } = get();
    const totalPages = Math.ceil(filteredTeams.length / size);

    set({
      pagination: {
        ...pagination,
        pageSize: size,
        totalPages,
        currentPage: Math.min(pagination.currentPage, totalPages || 1),
        hasNextPage: pagination.currentPage < totalPages,
        hasPreviousPage: pagination.currentPage > 1,
      },
    });
  },
}));

export default useReplacementStore;

// Export types for use in components
export type {
  ReplacementSuggestionsRequest,
  ReplacementSuggestionsResponse,
  ReplacementWorkstation,
  Team,
  Operator,
  Skill,
  Workstation,
  ReplacementState,
  ReplacementOperator,
  SubmitReplacementRequest,
  SubmitReplacementResponse,
  ReplacementHistoryItem,
  ReplacementHistoryResponse,
  ReplacementHistoryRequest,
  HistoryAbsentOperator,
  HistoryReplacementOperator,
  HistoryTargetWorkstation,
  HistoryShift,
  EvaluationData,
  EvaluateReplacementRequest,
  EvaluateReplacementResponse,
  RaiseRequestEscalation,
  RaiseRequestRequest,
  RaiseRequestResponse,
  SendBackupRequest,
  SendBackupResponse,
  ResetReplacementRequest,
  ResetReplacementResponse,
  MFGOperatorsRequest,
  MFGOperatorsResponse,
  BackupMovementsRequest,
  BackupMovementsResponse,
  BackupMovementsData,
  TakenWorker,
  AddedWorker,
  WillReplaceItem,
  ReplacementDetail,
  MediumLevelBackupRequest,
  MediumLevelBackupResponse,
  MediumLevelOperator,
  MediumLevelRequest,
  MediumLevelRequestsRequest,
  MediumLevelRequestsResponse,
  MediumLevelAssignOperatorsRequest,
  MediumLevelAssignOperatorsResponse,
  MediumLevelSendBackupToDepartmentRequest,
  MediumLevelSendBackupToDepartmentResponse,
  MediumLevelEscalateRequest,
  MediumLevelEscalateResponse,
  OperatorAssignment,
  OperatorQualification,
  WorkstationChosenFor,
  RequestWorkstation,
  RequestTeamLeader,
  RequestReplacementOperator,
  HighLevelBackupRequest,
  HighLevelBackupResponse,
  HighLevelOperator,
  HighLevelRequest,
  HighLevelRequestsRequest,
  HighLevelRequestsResponse,
  HighLevelAssignOperatorsRequest,
  HighLevelAssignOperatorsResponse,
  HighLevelEscalateRequest,
  HighLevelEscalateResponse,
};
