import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, SlidersHorizontal, LayoutGrid } from "lucide-react";

export function SearchFilter() {
  return (
    <div className="flex items-center justify-end gap-2 p-4 ">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input className="pl-9 w-[200px] bg-white" placeholder="Search..." />
      </div>
      <Button variant="outline" size="icon" className="h-10 w-10">
        <SlidersHorizontal className="h-4 w-4" />
        <span className="sr-only">Filter</span>
      </Button>
      <Button variant="outline" size="icon" className="h-10 w-10">
        <LayoutGrid className="h-4 w-4" />
        <span className="sr-only">View</span>
      </Button>
    </div>
  );
}
