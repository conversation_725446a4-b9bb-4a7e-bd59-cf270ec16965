import { create } from "zustand";

export interface Worker {
  key: string;
  id: string;
  name: string;
  status: "Present" | "Absent" | "None";
  type?: "Line" | "Plant" | "Bus";
  absenceCode?: string;
  lineCode?: string;
  isBackupReplacement?: boolean;
  isFlagged?: boolean;
}

interface ReplacementWorker {
  id: string;
  name: string;
  department: string;
  project: string;
}

interface Replacement {
  replacementWorker: ReplacementWorker;
  absentWorker: ReplacementWorker;
  date: string;
  timeFrom: string;
  timeTo: string;
  shift: string;
}

interface Replacements {
  incoming: Replacement[];
  outgoing: Replacement[];
}

interface AttendanceState {
  workers: Worker[];
  replacements: Replacements;
  shiftStarted: boolean;
  startTime: number | null;
  elapsedTime: string;

  startShift: () => void;
  updateAttendance: (
    key: string,
    status: "Present" | "Absent" | "None",
    type?: string,
    absenceCode?: string,
  ) => void;
  updateElapsedTime: () => void;
  findReplacements: () => void;
}

// Mock replacement data
const mockReplacements: Replacements = {
  incoming: [
    {
      replacementWorker: {
        id: "87321",
        name: "<PERSON>ouad ZEDAL",
        department: "Welding",
        project: "Project: Renault x5, Zone A, Area 3",
      },
      absentWorker: {
        id: "123953",
        name: "Ahmed Malik",
        department: "Welding",
        project: "Project: TESLA 3, Zone B, Area 5",
      },
      date: "25/12/2024",
      timeFrom: "09:00",
      timeTo: "12:00",
      shift: "Morning Shift",
    },
  ],
  outgoing: [
    {
      replacementWorker: {
        id: "87321",
        name: "Fouad ZEDAL",
        department: "Welding",
        project: "Project: Renault x5, Zone A, Area 3",
      },
      absentWorker: {
        id: "123953",
        name: "Ahmed Malik",
        department: "Welding",
        project: "Project: TESLA 3, Zone B, Area 5",
      },
      date: "25/12/2024",
      timeFrom: "09:00",
      timeTo: "12:00",
      shift: "Morning Shift",
    },
    {
      replacementWorker: {
        id: "87321",
        name: "Fouad ZEDAL",
        department: "Welding",
        project: "Project: Renault x5, Zone A, Area 3",
      },
      absentWorker: {
        id: "123953",
        name: "Ahmed Malik",
        department: "Welding",
        project: "Project: TESLA 3, Zone B, Area 5",
      },
      date: "25/12/2024",
      timeFrom: "09:00",
      timeTo: "12:00",
      shift: "Morning Shift",
    },
  ],
};

// Mock initial data
const initialWorkers: Worker[] = [
  {
    key: "1",
    id: "123212",
    name: "Ahmed Al-Mansoori",
    status: "None",
    type: "Line",
    lineCode: "P",
  },
  {
    key: "2",
    id: "123212",
    name: "Hassan Al-Farsi",
    status: "None",
    absenceCode: "AB",
  },
  {
    key: "3",
    id: "123212",
    name: "Hassan Al-Farsi",
    status: "None",
    absenceCode: "AB",
  },
  {
    key: "4",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    absenceCode: "AB",
  },
  {
    key: "5",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "6",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "7",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "8",
    id: "123212",
    name: "Samir Al-Madari",
    status: "None",
    type: "Line",
    lineCode: "X",
  },
  {
    key: "9",
    id: "123212",
    name: "Samir Al-Madari",
    status: "None",
    type: "Line",
    lineCode: "X",
  },
  {
    key: "10",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "11",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "12",
    id: "123212",
    name: "Nabil Al-Sayed",
    status: "None",
    absenceCode: "CR",
  },
  {
    key: "13",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "14",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  { key: "15", id: "123212", name: "Ahmed Malik", status: "None", type: "Bus" },
  {
    key: "16",
    id: "123212",
    name: "Kamal Fatihi",
    status: "None",
    absenceCode: "MA",
  },
  {
    key: "17",
    id: "123212",
    name: "Idris Al-Mahdi",
    status: "None",
    type: "Line",
    lineCode: "P",
    isFlagged: true,
    isBackupReplacement: true,
  },
  {
    key: "18",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  { key: "19", id: "123212", name: "Ahmed Malik", status: "None", type: "Bus" },
  {
    key: "20",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
  {
    key: "21",
    id: "123212",
    name: "Ahmed Malik",
    status: "None",
    type: "Plant",
  },
];

export const useAttendanceStore = create<AttendanceState>((set, get) => ({
  workers: initialWorkers,
  replacements: mockReplacements,
  shiftStarted: false,
  startTime: null,
  elapsedTime: "00:00",

  startShift: () => {
    set({
      shiftStarted: true,
      startTime: Date.now(),
    });

    // Start the timer
    const updateTimer = () => {
      get().updateElapsedTime();
      if (get().shiftStarted) {
        setTimeout(updateTimer, 1000);
      }
    };

    updateTimer();
  },

  updateElapsedTime: () => {
    const { startTime } = get();
    if (!startTime) return;

    const elapsed = Math.floor((Date.now() - startTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;

    set({
      elapsedTime: `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`,
    });
  },

  updateAttendance: (key, status, type = "Line", absenceCode = "AB") => {
    set((state) => ({
      workers: state.workers.map((worker) => {
        if (worker.key === key) {
          return {
            ...worker,
            status,
            type:
              status === "Present"
                ? (type as "Line" | "Plant" | "Bus")
                : worker.type,
            absenceCode: status === "Absent" ? absenceCode : undefined,
          };
        }
        return worker;
      }),
    }));

    // Find replacements if needed
    if (status === "Absent") {
      get().findReplacements();
    }
  },

  findReplacements: () => {
    // Logic to find replacements for absent workers
    const { workers } = get();

    // Count absences by type
    const absencesByType: Record<string, number> = {};
    workers.forEach((worker) => {
      if (worker.status === "Absent") {
        const type = worker.type || "General";
        absencesByType[type] = (absencesByType[type] || 0) + 1;
      }
    });

    // Mark some workers as backup replacements (simplified logic)
    set((state) => ({
      workers: state.workers.map((worker) => {
        // Simple logic: mark a worker as backup if they're present and we have absences
        const shouldBeBackup =
          worker.status === "Present" &&
          !worker.isBackupReplacement &&
          Object.keys(absencesByType).length > 0 &&
          Math.random() > 0.9; // Random selection for demo purposes

        if (shouldBeBackup) {
          return { ...worker, isBackupReplacement: true };
        }

        return worker;
      }),
    }));
  },
}));
