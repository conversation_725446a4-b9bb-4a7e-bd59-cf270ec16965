"use client";

import { Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { SearchFilter } from "./SearchFilter";
import CustomSelect from "@/components/common/CustomSelect";

interface AttendanceHeaderProps {
  startShift: () => void;
  shiftStarted: boolean;
  elapsedTime: string;
}

export function AttendanceHeader({
  startShift,
  shiftStarted,
  elapsedTime,
}: AttendanceHeaderProps) {
  return (
    <div className="flex flex-col gap-4 p-4 border-b w-full">
      <div className="flex items-center gap-4 w-full overflow-x-auto flex-nowrap">
        <div className="flex items-center gap-3 flex-shrink-0 min-w-0">
          <div className="p-2 bg-blue-50 rounded-md flex-shrink-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-blue-600"
            >
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
              <rect width="8" height="4" x="8" y="2" rx="1" ry="1" />
              <path d="M12 11h4" />
              <path d="M12 16h4" />
              <path d="M8 11h.01" />
              <path d="M8 16h.01" />
            </svg>
          </div>
          <div className="min-w-0">
            <h1 className="text-lg font-medium text-gray-700 truncate">
              Attendance sheet :
            </h1>
            <p className="text-sm text-gray-500 truncate">
              Morning shift (06:00 AM to 14:00 AM)
            </p>
          </div>
        </div>

        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />

        <div className="flex items-center gap-2 bg-orange-50 text-orange-600 px-3 py-2 rounded-md flex-shrink-0">
          <Clock className="h-5 w-5 flex-shrink-0" />
          <span className="text-xl font-bold truncate">{elapsedTime}</span>
        </div>

        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />

        <Button
          className="bg-blue-600 hover:bg-blue-700 text-white flex-shrink-0"
          onClick={startShift}
          disabled={shiftStarted}
        >
          Start Shift
        </Button>

        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />

        <div className="flex-shrink-0">
          <CustomSelect
            options={[
              {
                label: "Morning Shift",
                value: "morning",
              },
              {
                label: "Evening Shift",
                value: "evening",
              },
            ]}
            onValueChange={() => {}}
            placeholder="Select :"
            value={""}
            className="w-[180px]"
          />
        </div>

        <div className="flex-shrink-0 w-auto ml-auto">
          <SearchFilter />
        </div>
      </div>
    </div>
  );
}
