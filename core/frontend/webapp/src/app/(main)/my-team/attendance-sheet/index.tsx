"use client";

import { useEffect } from "react";
import { useAttendanceStore } from "./store/attendanceStore";
import { AttendanceHeader } from "./components/AttendanceHeader";
import { AttendanceGrid } from "./components/AttendanceGrid";
import { ReplacementIndicators } from "../shared/ReplacementIndicators";

export default function AttendancePage() {
  const { startShift, shiftStarted, elapsedTime, updateElapsedTime } =
    useAttendanceStore();

  // Update the timer display when the component mounts
  useEffect(() => {
    const timer = setInterval(() => {
      if (shiftStarted) {
        updateElapsedTime();
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [shiftStarted, updateElapsedTime]);

  return (
    <div className="flex-1 w-full h-full overflow-hidden">
      <div className="h-full w-full p-6 lg:p-8">
        <div className="h-full w-full rounded-3xl border bg-white shadow-sm">
          <AttendanceHeader
            startShift={startShift}
            shiftStarted={shiftStarted}
            elapsedTime={elapsedTime}
          />
          <div className="bg-[#F5FBFF]">
            <AttendanceGrid />
            <ReplacementIndicators />
          </div>
        </div>
      </div>
    </div>
  );
}
