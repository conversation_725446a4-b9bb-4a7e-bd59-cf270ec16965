export type WorkerType = "Line" | "Plant" | "Bus";
export type WorkerStatus = "Present" | "Absent";

export interface Worker {
  key: string;
  id: string;
  name: string;
  status: WorkerStatus;
  type?: WorkerType;
  absenceCode?: string;
  lineCode?: string;
  isBackupReplacement?: boolean;
  isFlagged?: boolean;
}

export interface AttendanceUpdate {
  workerKey: string;
  status: WorkerStatus;
  type: WorkerType;
  absenceCode?: string;
}
