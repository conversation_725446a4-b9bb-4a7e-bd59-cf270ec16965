import { useAttendanceStore } from "../store/attendanceStore";
import { WorkerCard } from "./WorkersCard";

function chunkArray<T>(arr: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}

export function AttendanceGrid() {
  const { workers } = useAttendanceStore();

  // Split workers into columns of max 9
  const columns = chunkArray(workers, 9);

  return (
    <div className="w-full flex flex-row items-stretch gap-0 md:gap-4 p-2 md:p-4 overflow-x-auto min-w-0">
      {columns
        .map((col, idx) => (
          <div
            key={idx}
            className="space-y-2 min-w-[380px] max-w-[520px] h-full"
          >
            {col.map((worker) => (
              <WorkerCard key={worker.key} worker={worker} />
            ))}
          </div>
          // Add separator except after last column
        ))
        .reduce((acc, col, idx) => {
          acc.push(col);
          if (idx < columns.length - 1) {
            acc.push(
              <div
                key={`sep-${idx}`}
                className="self-stretch mx-4 w-px bg-gray-300 opacity-60 rounded-full"
              />,
            );
          }
          return acc;
        }, [] as React.ReactNode[])}
    </div>
  );
}
