"use client";

import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAttendanceStore, Worker } from "../store/attendanceStore";

interface WorkerCardProps {
  worker: Worker;
}

export function WorkerCard({ worker }: WorkerCardProps) {
  const { updateAttendance } = useAttendanceStore();

  const getStatusColor = () => {
    if (worker.status === "Present") {
      if (worker.type === "Line") return "text-[#4CAF50]";
      if (worker.type === "Plant") return "text-[#4762F1]";
      if (worker.type === "Bus") return "text-yellow-600";
      return "text-[#4762F1]"; // Default to Plant color
    }
    if (worker.status === "Absent") return "text-[#D60000]";
    return "text-[#4762F1]"; // Default to Plant color when no status
  };

  const getStatusIcon = () => {
    if (worker.status === "Present") {
      if (worker.type === "Line") return "🟢";
      if (worker.type === "Plant") return "📊";
      if (worker.type === "Bus") return "🚌";
      return "📊"; // Default to Plant icon
    }
    if (worker.status === "Absent") return "❌";
    return "📊"; // Default to Plant icon when no status
  };

  const getStatusText = () => {
    if (worker.status === "Present") {
      if (worker.type === "Line") {
        return `Present (Line) : ${worker.lineCode}`;
      }
      return `Present (${worker.type})`;
    }
    if (worker.status === "Absent") {
      return `Absent : AB`;
    }
    return "Present (Plant)"; // Default text when no status
  };

  const markAbsent = () => {
    // If already absent, toggle back to None
    if (worker.status === "Absent") {
      updateAttendance(worker.key, "None", "Plant");
    } else {
      // Mark as absent (show only red X)
      updateAttendance(worker.key, "Absent", "Plant", "AB");
    }
  };

  const markPresent = () => {
    // If already present, toggle back to None
    if (worker.status === "Present") {
      updateAttendance(worker.key, "None", "Plant");
    } else {
      // Mark as present (show only green check)
      updateAttendance(worker.key, "Present", "Line");
    }
  };

  // Determine which buttons to show based on status
  const showBothButtons = worker.status === "None";
  const showOnlyCheck = worker.status === "Present";
  const showOnlyX = worker.status === "Absent";

  return (
    <div
      className={cn(
        "relative flex items-center gap-3 p-2 border rounded-full mb-2 bg-white shadow-sm hover:shadow-md transition-shadow",
        worker.isBackupReplacement && "border-yellow-400",
        "min-h-[72px] max-w-[380px] min-w-[260px]",
      )}
    >
      {worker.isBackupReplacement && (
        <div className="absolute -top-2 right-4 bg-yellow-400 text-xs text-black px-2 py-0.5 rounded-full">
          Backup replacement
        </div>
      )}

      {worker.isFlagged && (
        <div className="absolute -top-2 left-4 bg-red-500 text-xs text-white px-2 py-0.5 rounded-full">
          F.Z. ⚠
        </div>
      )}

      <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center ml-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-gray-500"
        >
          <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
      </div>

      <div className="flex-1 min-w-0">
        <div className="font-bold text-sm text-gray-800 truncate">
          {worker.id}
        </div>
        <div className="text-xs text-gray-600 truncate">{worker.name}</div>
        <div
          className={cn("flex items-center gap-1 text-xs", getStatusColor())}
        >
          {getStatusIcon() && (
            <span className="text-xs">{getStatusIcon()}</span>
          )}
          {getStatusText() && (
            <span className="truncate">{getStatusText()}</span>
          )}
        </div>
      </div>

      <div className="flex gap-1 mr-1">
        {/* Show X button when: showing both buttons OR only showing X */}
        {(showBothButtons || showOnlyX) && (
          <button
            onClick={markAbsent}
            className={cn(
              "w-7 h-7 rounded-full border flex items-center justify-center transition-all duration-200",
              showOnlyX
                ? "bg-red-500 text-white border-red-500"
                : "border-gray-200 bg-[#D1D1D1] text-gray-400 hover:bg-red-50 hover:text-red-500 hover:border-red-200",
            )}
          >
            <X className="h-4 w-4" />
          </button>
        )}

        {/* Show Check button when: showing both buttons OR only showing check */}
        {(showBothButtons || showOnlyCheck) && (
          <button
            onClick={markPresent}
            className={cn(
              "w-7 h-7 rounded-full border flex items-center justify-center transition-all duration-200",
              showOnlyCheck
                ? "bg-green-500 text-white border-green-500"
                : "border-gray-200 bg-[#D1D1D1] text-gray-400 hover:bg-green-50 hover:text-green-500 hover:border-green-200",
            )}
          >
            <Check className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}
