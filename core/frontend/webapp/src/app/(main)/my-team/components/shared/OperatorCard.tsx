import React from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import CustomIcon from "@/components/common/CustomIcons";

type HighlightStatus =
  | "selected"
  | "pending"
  | "highlighted"
  | "unavailable"
  | "matched"
  | "normal";

interface BaseOperator {
  operatorId: string;
  operatorLegacyId: number;
  operatorFirstName: string;
  operatorLastName: string;
  operatorQualifications: Array<{ skillCode: string; skillLevel?: string }>;
  isAvailable: boolean;
  isValidChoice: boolean;
  operatorTeamName: string | null;
  shiftLeader?: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
  } | null;
  projectHierarchy: {
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
  } | null;
  workstationChosenFor?: {
    id: string;
    name: string;
    requiredSkills: string[];
  } | null;
}

interface OperatorCardProps {
  operator: BaseOperator;
  highlightStatus: HighlightStatus;
  isChecked: boolean;
  selectedOperators: string[];
  currentSelectionLabel: string;
  department: string;
  pendingAssignmentLabel?: string;
  onSelectOperator: (operatorId: string) => void;
  onCheckOperator: (operatorId: string) => void;
  getPendingAssignmentForOperator: (
    operatorId: string,
  ) => { label: string } | undefined;
  isOperatorMatched: (operatorId: string) => boolean;
}

export default function OperatorCard({
  operator,
  highlightStatus,
  isChecked,
  selectedOperators,
  currentSelectionLabel,
  onSelectOperator,
  onCheckOperator,
  getPendingAssignmentForOperator,
  isOperatorMatched,
  department,
}: OperatorCardProps) {
  const getCardClassName = () => {
    const baseClasses = "p-4 transition-all duration-200 rounded-3xl border";

    switch (highlightStatus) {
      case "selected":
        return `${baseClasses} border-blue-500 bg-blue-50`;
      case "pending":
        return `${baseClasses} border-gray-200 border-2 bg-[#F5FBFF]`;
      case "highlighted":
        return `${baseClasses} border-[#526BF1] border-2 bg-[#F5FBFF]`;
      case "unavailable":
        return `${baseClasses} border-gray-100 bg-gray-50 opacity-50`;
      // case "matched":
      //   return `${baseClasses} border-green-500 bg-green-50`;
      default:
        return `${baseClasses} border-gray-200 border-2 bg-[#F5FBFF]`;
    }
  };

  const renderButton = () => {
    console.log("Rendering button for operator:", highlightStatus);
    if (!isOperatorMatched(operator.operatorId) && operator.isAvailable) {
      return (
        <div className="flex items-center space-x-2">
          <Button
            disabled={
              highlightStatus === "normal" ||
              highlightStatus === "unavailable" ||
              highlightStatus === "matched" ||
              (getPendingAssignmentForOperator(operator.operatorId) &&
                getPendingAssignmentForOperator(operator.operatorId)?.label !==
                  currentSelectionLabel)
            }
            className={
              !selectedOperators.includes(operator.operatorId)
                ? "border-dashed border-2 border-gray-300 rounded-xl"
                : "bg-[#5B7291] text-white rounded-lg hover:bg-[#4A5B7A]"
            }
            variant={
              selectedOperators.includes(operator.operatorId)
                ? "default"
                : "outline"
            }
            size="lg"
            onClick={() => onSelectOperator(operator.operatorId)}
          >
            {selectedOperators.includes(operator.operatorId)
              ? `SELECTED (${currentSelectionLabel})`
              : getPendingAssignmentForOperator(operator.operatorId)
                ? `ASSIGNED (${getPendingAssignmentForOperator(operator.operatorId)?.label})`
                : "Select to match"}
          </Button>
        </div>
      );
    }

    // Matched operator display
    return (
      <div className="flex items-start justify-between mb-2 pb-2">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-[#A0D5A7] rounded-full flex items-center justify-center mb-3">
            <div className="w-5 h-5 bg-[#4CAF50] rounded-full flex items-center justify-center">
              <CustomIcon
                name="pin"
                style={{
                  width: "50%",
                  height: "50%",
                  fill: "#A0D5A7",
                }}
              />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="font-semibold text-[#4CAF50]">Workstation:</span>
              <span className="font-medium text-[#4CAF50] text-lg">
                {operator.workstationChosenFor?.name || "N/A"}
              </span>
            </div>
            <div className="text-sm text-gray-900">
              <span className="font-medium">Qualifs:</span>{" "}
              {operator.workstationChosenFor?.requiredSkills
                ?.slice(0, 3)
                .join(", ") || "N/A"}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const detailItems = [
    {
      label: "Customer",
      value: operator?.projectHierarchy?.customer || "N/A",
    },
    { label: "Project", value: operator?.projectHierarchy?.project || "N/A" },
    {
      label: "Family",
      value: operator?.projectHierarchy?.family || "N/A",
    },
    {
      label: "Value Stream",
      value: operator?.projectHierarchy?.valueStream || "N/A",
    },
    { label: "Area", value: operator?.projectHierarchy?.area || "N/A" },
    { label: "Team", value: operator.operatorFirstName || "N/A" },
  ];

  return (
    <Card className={getCardClassName()}>
      {/* Block 1: name / qualifs / TL / button */}
      <div className="flex items-start justify-between mb-2 border-b-2 pb-2">
        {/* Left: icon, name + qualifs + TL */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[#F5FBFF] rounded-full flex items-center justify-center">
            <CustomIcon
              name="circleUser"
              className="w-4 h-4"
              style={{
                fill: operator.isAvailable ? "#526BF1" : "#4CAF50",
              }}
            />
          </div>
          <div className="min-w-0 border-l-2 pl-4">
            <div className="flex items-baseline space-x-1">
              <span
                className={`font-semibold ${
                  !operator.isAvailable ? "text-[#4CAF50]" : "text-gray-900"
                } truncate`}
              >
                {operator.operatorLegacyId}
              </span>
              <span className="text-gray-400">-</span>
              <span
                className={`font-medium ${
                  !operator.isAvailable ? "text-[#4CAF50]" : "text-gray-900"
                } truncate`}
              >
                {`${operator.operatorFirstName} ${operator.operatorLastName}`}
              </span>
            </div>
            <div className="text-gray-400">
              <span className="font-medium">Qualifs:</span>{" "}
              {operator.operatorQualifications
                .slice(0, 3)
                .map((q) => q.skillCode)
                .join(", ")}
              {operator.operatorQualifications.length > 3 && (
                <div className="inline-block bg-[#ACB4BC] text-center rounded-full ml-1 px-2 py-1">
                  <span className="ml-1 text-xs text-white">
                    +{operator.operatorQualifications.length - 3}
                  </span>
                </div>
              )}
            </div>
            <div className="text-sm text-gray-400">
              <span className="font-medium">TL:</span>{" "}
              {operator.shiftLeader
                ? `${operator.shiftLeader.firstName} ${operator.shiftLeader.lastName}`
                : "N/A"}
            </div>
          </div>
        </div>
        {/* put a vertical border vetween them */}
        {!operator.isAvailable && (
          <div className="border-r-2 border-gray-200 h-12"></div>
        )}
        <div className="flex-shrink-0 ml-4">{renderButton()}</div>
      </div>

      {/* Block 2: checkbox + details */}
      <div className="flex flex-wrap items-start">
        {/* Checkbox - hide for department-backup-basket */}
        {operator.isAvailable && department !== "departement-backup-basket" ? (
          <div className="flex-shrink-0 mr-4 border-r-2 pr-[31px]">
            <input
              type="checkbox"
              className="rounded"
              checked={isChecked}
              onChange={() => onCheckOperator(operator.operatorId)}
            />
          </div>
        ) : (
          <div
            className={`w-6 h-6 bg-[#F5FBFF] rounded-full flex items-center justify-center border-2 border-[#5B7291] mr-4`}
          >
            <CustomIcon
              name="pin"
              style={{
                width: "80%",
                height: "80%",
                fill: "#5B7291",
              }}
            />
          </div>
        )}
        {/* Details list */}
        <div className="flex-1 grid grid-cols-6 md:grid-cols-6 lg:grid-cols-6 gap-4 text-xs text-gray-500 border-l-2">
          {detailItems.map((item, idx) => (
            <div key={idx} className="space-y-1 text-center">
              <div className="text-blue-500 font-medium">{item.label}</div>
              <div className="truncate">{item.value}</div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}
