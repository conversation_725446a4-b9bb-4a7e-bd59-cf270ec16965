"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { STATUS_MAP } from "../types/Types";

interface TimeSlotListItemProps {
  id: string;
  start: string;
  end: string;
  duration: string;
  status: string;
  onRemove: (id: string) => void;
}

export function TimeSlotListItem({
  id,
  start,
  end,
  duration,
  status,
  onRemove,
}: TimeSlotListItemProps) {
  const statusInfo = STATUS_MAP[status] || {
    name: status,
    color: "bg-gray-400",
    textColor: "text-white",
  };

  return (
    <div className="flex items-center justify-between rounded-md border p-3">
      <div className="flex items-center gap-3">
        <div
          className={`size-4 shrink-0 rounded-sm ${statusInfo.color}`}
          aria-hidden="true"
        />
        <span className="font-medium">
          {start} - {end}
        </span>
        <span className="text-sm text-muted-foreground">({duration})</span>
        {statusInfo.name && (
          <Badge
            className={`${statusInfo.color} ${statusInfo.textColor || "text-white"} px-2 py-0.5 text-xs`}
          >
            {statusInfo.name}
          </Badge>
        )}
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onRemove(id)}
        className="size-6"
        aria-label={`Remove time slot from ${start} to ${end}`}
      >
        <X className="size-4" />
      </Button>
    </div>
  );
}
