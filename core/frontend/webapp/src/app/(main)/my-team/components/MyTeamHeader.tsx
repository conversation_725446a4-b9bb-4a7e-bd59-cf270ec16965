"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useAttendanceStore } from "../store/myTeamStore";
import { useTeamLeaderStore } from "../store/teamLeaderStore";
import CustomSelect from "@/components/common/CustomSelect";
import CustomIcon from "@/components/common/CustomIcons";
import { DatePicker } from "@/components/ui/datePicker";
import { UserRole } from "@/enum/rolesEnum";
import { useMockAuthStore } from "@/store/mockAuthStore";
import { resolveUserRole } from "@/utils/userRoleHelper";
import ValidationDialog from "@/components/common/ValidationDialog";
import { IconButton, ReusableButton } from "@/components/common/CustomButtons";
import { ShiftLeaderTabs } from "./ShiftLeaderTabs";
import { DepartmentClerkTabs } from "./DepartmentClerkTabs";
import { MyTeamTable } from "./MyTeamTable";
import { SendForReviewDialog } from "./dialogs/SendForReviewDialog";
import { ReplacementFeedbackDialog } from "./dialogs/EvaluationDialog";
import { BulkRequestDialog } from "./dialogs/BulkRequestDialog";
import { RequestFormDialog } from "./dialogs/RequestFormDialog";
import MatchingInterface from "./ShiftLeaderBackup";
import useReplacementStore from "../store/replacementStore";
import DepartmentBackup from "./DepartmentBackup";

const createIcon = (
  name:
    | "fileText"
    | "user"
    | "error"
    | "bell"
    | "circleUser"
    | "reassign"
    | "project"
    | "family"
    | "lineChart"
    | "areaChart"
    | "team"
    | "customer"
    | "meDefinition"
    | "play"
    | "doubleCheck",
  fill: string,
) => (
  <span className="flex items-center">
    <CustomIcon
      name={name}
      className="mr-2"
      style={{
        width: "17px",
        height: "18px",
        fill,
      }}
    />
  </span>
);

const ProjectIcons = createIcon("project", "#000000");
const FamilyIcons = createIcon("family", "#9E7A00");
const LineChartIcons = createIcon("lineChart", "#4CAF50");
const AreaIcons = createIcon("areaChart", "#c59800");
const TeamIcons = createIcon("team", "#8804A1");
const CustomerIcons = createIcon("customer", "#ffb72f");
const PlayIcons = (
  <span className="flex items-center">
    <CustomIcon
      name="play"
      className="mr-2"
      style={{
        width: "25px",
        height: "25px",
        fill: "#ffffff",
      }}
    />
  </span>
);

const addIcon = (
  <CustomIcon
    name="circleAdd"
    style={{
      width: "20px",
      height: "18px",
      fill: "#ffffff",
    }}
  />
);

export function MyTeamHeader() {
  const [currentTime, setCurrentTime] = useState(new Date());

  const { currentUser: user } = useMockAuthStore();
  console.log(currentTime);
  // Get user role from auth store - moved before useState
  const userRole = resolveUserRole(user);

  const {
    isVisualCheckActive,
    visualCheckTimer,
    isMyBackupBasketActive,
    myBackupBasketTimer,
    isDepartmentBackupBasketActive,
    departmentBackupBasketTimer,
    filterMfgOnly,
    shiftStartTime,
    startVisualCheck,
    // finishVisualCheck,
    setVisualCheckTimer,
    startMyBackupBasket,
    setMyBackupBasketTimer,
    startDepartmentBackupBasket,
    setDepartmentBackupBasketTimer,
    toggleMfgFilter,
    approveAttendanceSheet,
    setClockingCategory,
    selectedValidationTeam,
    // Team leader filter state and actions
    teamLeaderFilters,
    fetchFilteredOptions,
    setSelectedCustomer,
    setSelectedProject,
    setSelectedFamily,
    setSelectedValueStream,
    setSelectedArea,
    setSelectedTeam,
    currentDate,
    setCurrentDate,
  } = useAttendanceStore();

  // Team Leader specific store
  const {
    fetchStatusMonitoring,
  } = useTeamLeaderStore();
  const [isValidationDialogOpen, setIsValidationDialogOpen] = useState(false);
  const [isSendForReviewDialogOpen, setIsSendForReviewDialogOpen] =
    useState(false);
  const getShiftLeaderTabCounts = useAttendanceStore(
    (s) => s.getShiftLeaderTabCounts,
  );
  const getDepartmentClerkTabCounts = useAttendanceStore(
    (s) => s.getDepartmentClerkTabCounts,
  );
  const tabCounts = useMemo(
    () => getShiftLeaderTabCounts(),
    [getShiftLeaderTabCounts],
  );
  const departmentClerkTabCounts = useMemo(
    () => getDepartmentClerkTabCounts(),
    [getDepartmentClerkTabCounts],
  );
  const [activeTab, setActiveTab] = useState(() => {
    // Set default tab based on user role
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      return "clocking-ih";
    }
    return "my-teamleaders";
  });
  const [departmentRefreshKey, setDepartmentRefreshKey] = useState(0);
  const [selectedWorkers, setSelectedWorkers] = useState<Set<string>>(
    new Set(),
  );

  const [isEvaluationDialogOpen, setIsEvaluationDialogOpen] = useState(false);

  // Bulk Request Dialog States
  const [isBulkRequestDialogOpen, setIsBulkRequestDialogOpen] = useState(false);
  const [isBulkRequestFormDialogOpen, setIsBulkRequestFormDialogOpen] =
    useState(false);
  const [selectedBulkRequestType, setSelectedBulkRequestType] = useState<
    | {
        id: string;
        title: string;
      }
    | undefined
  >(undefined);
  const [selectedBulkOperators, setSelectedBulkOperators] = useState<string[]>(
    [],
  );

  const [selectedAction, setSelectedAction] = useState<string>("");

  // Shift management state
  const [isShiftStarted, setIsShiftStarted] = useState(false);
  const [hasOperatorsSent, setHasOperatorsSent] = useState(false);

  // Replacement store for API calls
  const { sendBackupToShiftLeader, isSendingBackup, clearSendBackupError } =
    useReplacementStore();

  // Compute filteredWorkers here so we can pass its length
  const workers = useAttendanceStore((s) => s.workers);
  const filteredWorkers = useMemo(() => {
    let filtered = workers;
    if (filterMfgOnly) {
      filtered = filtered.filter(
        (worker) => worker.role.toLowerCase() === "mfg structure",
      );
    }
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      if (activeTab === "clocking-ih") {
        filtered = filtered.filter((worker) => worker.category === "IH");
      } else if (activeTab === "clocking-is") {
        filtered = filtered.filter((worker) => worker.category === "IS");
      }
    }
    return filtered;
  }, [workers, filterMfgOnly, userRole, activeTab]);

  // Fetch initial customer options on component mount for team leader view
  useEffect(() => {
    if (userRole === UserRole.TEAM_LEADER) {
      fetchFilteredOptions({});
    }
  }, [userRole, fetchFilteredOptions]);

  // Fetch team data when team is selected for team leader
  useEffect(() => {
    if (userRole === UserRole.TEAM_LEADER) {
      const endDate = currentDate.toISOString().split("T")[0];
      fetchStatusMonitoring(endDate);
    }
  }, [
    userRole,
    teamLeaderFilters.selectedTeam,
    currentDate,
    fetchStatusMonitoring,
  ]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isVisualCheckActive && visualCheckTimer > 0) {
      interval = setInterval(() => {
        setVisualCheckTimer(visualCheckTimer - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isVisualCheckActive, visualCheckTimer, setVisualCheckTimer]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isMyBackupBasketActive && myBackupBasketTimer > 0) {
      interval = setInterval(() => {
        setMyBackupBasketTimer(myBackupBasketTimer - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isMyBackupBasketActive, myBackupBasketTimer, setMyBackupBasketTimer]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isDepartmentBackupBasketActive && departmentBackupBasketTimer > 0) {
      interval = setInterval(() => {
        setDepartmentBackupBasketTimer(departmentBackupBasketTimer - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [
    isDepartmentBackupBasketActive,
    departmentBackupBasketTimer,
    setDepartmentBackupBasketTimer,
  ]);

  // Start appropriate timer if we're already on a backup basket tab when component mounts
  useEffect(() => {
    if (activeTab === "my-backup-basket" && !isMyBackupBasketActive) {
      startMyBackupBasket();
    } else if (
      activeTab === "department-backup-basket" &&
      !isDepartmentBackupBasketActive
    ) {
      startDepartmentBackupBasket();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]); // Only depend on activeTab to avoid infinite loops

  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const canStartVisualCheck = () => {
    const now = new Date();
    const [shiftHour, shiftMinute] = shiftStartTime.split(":").map(Number);
    const shiftStart = new Date();
    shiftStart.setHours(shiftHour, shiftMinute, 0, 0);
    return now >= shiftStart;
  };

  const handleApprove = () => {
    approveAttendanceSheet();
    setIsValidationDialogOpen(false);
  };

  const handleOpenSendForReviewDialog = () => {
    setIsSendForReviewDialogOpen(true);
  };

  const handleCloseSendForReviewDialog = () => {
    setIsSendForReviewDialogOpen(false);
  };

  const handleSendForReview = (comment: string) => {
    // Handle send for review logic
    console.log("Sending for review with comment:", comment);
    setIsSendForReviewDialogOpen(false);
  };

  // Bulk Request Handlers
  const handleOpenBulkRequestDialog = () => {
    setIsBulkRequestDialogOpen(true);
  };

  const handleCloseBulkRequestDialog = () => {
    setIsBulkRequestDialogOpen(false);
    setSelectedBulkRequestType(undefined);
    setSelectedBulkOperators([]);
  };

  const handleBulkRequestNext = (
    requestType: { id: string; title: string },
    operators: string[],
  ) => {
    setSelectedBulkRequestType(requestType);
    setSelectedBulkOperators(operators);
    setIsBulkRequestDialogOpen(false);
    setIsBulkRequestFormDialogOpen(true);
  };

  const handleCloseBulkRequestFormDialog = () => {
    setIsBulkRequestFormDialogOpen(false);
    setSelectedBulkRequestType(undefined);
    setSelectedBulkOperators([]);
  };

  const handleBackToBulkRequestList = () => {
    setIsBulkRequestFormDialogOpen(false);
    setIsBulkRequestDialogOpen(true);
  };

  const handleTabChange = (newTab: string | ((prev: string) => string)) => {
    const tabValue = typeof newTab === "function" ? newTab(activeTab) : newTab;
    setActiveTab(tabValue);

    // Start timer for my-backup-basket tab
    if (tabValue === "my-backup-basket") {
      startMyBackupBasket();
    }

    // Start timer for department-backup-basket tab
    if (tabValue === "department-backup-basket") {
      startDepartmentBackupBasket();
      setDepartmentRefreshKey((prev) => prev + 1);
    }

    // Update clocking category for Department Clerk and Department Manager
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      if (tabValue === "clocking-ih") {
        setClockingCategory("IH");
      } else if (tabValue === "clocking-is") {
        setClockingCategory("IS");
      }
    }
  };

  const handleSubmitAction = async () => {
    if (selectedAction === "escalate" && selectedWorkers.size > 0) {
      try {
        // Clear any previous errors
        clearSendBackupError();

        // Convert selectedWorkers Set to Array for the API call
        const selectedWorkerIds = Array.from(selectedWorkers);

        // Static values for demo - in real app these would come from context/props
        const teamLeaderId = "3e2bac24-5866-4065-8a7b-914c2e077cf1";
        const shiftId = "b4bedff2-165e-4156-969f-d3b3cd025970";

        await sendBackupToShiftLeader(selectedWorkerIds, shiftId, teamLeaderId);

        // Mark that operators have been sent
        setHasOperatorsSent(true);

        // Clear selections on success
        setSelectedWorkers(new Set());
        setSelectedAction("");
      } catch (error) {
        console.error("Error sending backup to shift leader:", error);
      }
    } else if (selectedAction === "present") {
      // Handle present action
      console.log("Marking workers as present:", Array.from(selectedWorkers));
      setSelectedWorkers(new Set());
      setSelectedAction("");
    } else if (selectedAction === "absent") {
      // Handle absent action
      console.log("Marking workers as absent:", Array.from(selectedWorkers));
      setSelectedWorkers(new Set());
      setSelectedAction("");
    }
  };

  const shouldShowApproveButton = () => {
    return (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.SHIFT_LEADER ||
      userRole === UserRole.QUALITY_SUPERVISOR
    );
  };

  return (
    <div className="flex flex-col gap-4 w-full relative">
      {/* Hide tabs if special shift leader validation view is active */}
      {!selectedValidationTeam &&
        (userRole === UserRole.DEPARTEMENT_CLERK ||
        userRole === UserRole.DEPARTEMENT_MANAGER ? (
          <>
            <DepartmentClerkTabs
              counts={departmentClerkTabCounts}
              activeTab={activeTab}
              setActiveTab={handleTabChange}
            />
            <div className="w-full overflow-x-auto">
              <div className="flex items-center justify-between min-w-max">
                <div className="flex items-center gap-3 flex-shrink-0 min-w-0">
                  <div className="p-1 rounded-md flex-shrink-0">
                    <CustomIcon
                      name="dayView"
                      style={{ width: "30px", height: "30px", fill: "#5B7291" }}
                    />
                  </div>
                  <div className="min-w-0">
                    <h1 className="text-lg font-medium text-gray-700 truncate">
                      Attendance sheet :
                    </h1>
                    <p className="text-sm text-gray-500 truncate">
                      Morning shift ({shiftStartTime} AM to 14:00 AM)
                    </p>
                  </div>
                </div>
                {userRole === UserRole.DEPARTEMENT_CLERK ? (
                  <div className="flex gap-3 ml-auto">
                    <DatePicker
                      label="Date"
                      placeholder="Select a date"
                      className="min-w-[200px] mt-[-20px]"
                      defaultDate={currentDate}
                      onChange={(date) => date && setCurrentDate(date)}
                    />
                    <Button
                      onClick={handleOpenBulkRequestDialog}
                      className="flex items-center gap-2 px-3 py-5 rounded-lg bg-black text-white font-semibold shadow hover:bg-gray-800 focus:outline-none"
                    >
                      Bulk Request
                    </Button>
                    <IconButton
                      icon={addIcon}
                      label="Approve"
                      onClick={() => setIsValidationDialogOpen(true)}
                      className="bg-black text-white rounded-lg px-3 py-5 flex items-center justify-center gap-1"
                    />
                  </div>
                ) : userRole === UserRole.DEPARTEMENT_MANAGER ? (
                  <div className="flex gap-3 ml-auto">
                    <DatePicker
                      label="Date"
                      placeholder="Select a date"
                      className="min-w-[200px] mt-[-20px]"
                      defaultDate={currentDate}
                      onChange={(date) => date && setCurrentDate(date)}
                    />
                    <Button
                      className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-green-600 bg-black text-white font-semibold shadow hover:bg-green-900 focus:outline-none"
                      style={{ borderColor: "#1DB954" }}
                      onClick={() => {}}
                    >
                      <svg
                        width="20"
                        height="20"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        className="text-green-400"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Confirm
                    </Button>
                    <Button
                      className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-red-600 bg-black text-white font-semibold shadow hover:bg-red-900 focus:outline-none"
                      style={{ borderColor: "#FF3B30" }}
                      onClick={handleOpenSendForReviewDialog}
                    >
                      <svg
                        width="20"
                        height="20"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        className="text-red-400"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                      Send for review
                    </Button>
                  </div>
                ) : null}
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <div className="flex flex-col gap-4 min-w-max">
                {/* First row - Filters and action buttons */}
                <div className="flex items-center gap-2">
                  {userRole === UserRole.SHIFT_LEADER ? (
                    <>
                      <ShiftLeaderTabs
                        counts={tabCounts}
                        activeTab={activeTab}
                        setActiveTab={handleTabChange}
                      />
                      {(activeTab === "my-backup-basket" ||
                        activeTab === "department-backup-basket") && (
                        <>
                          <div className="flex items-center gap-2 px-3 py-2 rounded-md flex-shrink-0">
                            <CustomIcon
                              name="stopWatch"
                              style={{
                                width: "32px",
                                height: "36px",
                                fill: "#F84018",
                              }}
                            />
                            <span className="text-xl font-bold truncate text-[#F84018]">
                              {isMyBackupBasketActive
                                ? formatTimer(myBackupBasketTimer)
                                : isDepartmentBackupBasketActive
                                  ? formatTimer(departmentBackupBasketTimer)
                                  : "00:00"}
                            </span>
                          </div>
                        </>
                      )}
                    </>
                  ) : userRole === UserRole.QUALITY_SUPERVISOR ? (
                    // Filters for Quality Supervisor
                    <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-grow">
                      <div className="flex items-center gap-2 flex-nowrap h-full">
                        <div className="flex-1">
                          <DatePicker
                            label="Date"
                            placeholder="Select a date"
                            defaultDate={currentDate}
                            onChange={(date) => date && setCurrentDate(date)}
                          />
                        </div>
                        <div className="flex-1">
                          <CustomSelect
                            options={[]}
                            onValueChange={(value) => console.log(value)}
                            placeholder="Select area"
                            label={
                              <span className="flex items-center">
                                {AreaIcons}
                                Area
                              </span>
                            }
                          />
                        </div>
                        <div className="flex-1">
                          <CustomSelect
                            options={[]}
                            onValueChange={(value) => console.log(value)}
                            placeholder="Select Team"
                            label={
                              <span className="flex items-center">
                                {TeamIcons}
                                Team
                              </span>
                            }
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Original filters for Team Leader and other roles
                    <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-grow">
                      <div className="flex items-center gap-2 flex-nowrap h-full">
                        <div className="flex-1 min-w-[200px]">
                          <DatePicker
                            label="Date"
                            placeholder="Select a date"
                            defaultDate={currentDate}
                            onChange={(date) => date && setCurrentDate(date)}
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.customerOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              }),
                            )}
                            onValueChange={setSelectedCustomer}
                            defaultValue={teamLeaderFilters.selectedCustomer}
                            placeholder="Select customer"
                            label={
                              <span className="flex items-center">
                                {CustomerIcons}
                                Customer
                              </span>
                            }
                            disabled={teamLeaderFilters.isLoading}
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.projectOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              }),
                            )}
                            onValueChange={setSelectedProject}
                            defaultValue={teamLeaderFilters.selectedProject}
                            placeholder="Select project"
                            label={
                              <span className="flex items-center">
                                {ProjectIcons}
                                Project
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedCustomer ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.projectOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.familyOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              }),
                            )}
                            onValueChange={setSelectedFamily}
                            defaultValue={teamLeaderFilters.selectedFamily}
                            placeholder="Select family"
                            label={
                              <span className="flex items-center">
                                {FamilyIcons}
                                Family
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedProject ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.familyOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.valueStreamOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              }),
                            )}
                            onValueChange={setSelectedValueStream}
                            defaultValue={teamLeaderFilters.selectedValueStream}
                            placeholder="Select value stream"
                            label={
                              <span className="flex items-center">
                                {LineChartIcons}
                                Value Stream
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedFamily ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.valueStreamOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.areaOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              }),
                            )}
                            onValueChange={setSelectedArea}
                            defaultValue={teamLeaderFilters.selectedArea}
                            placeholder="Select area"
                            label={
                              <span className="flex items-center">
                                {AreaIcons}
                                Area
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedValueStream ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.areaOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.teamOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              }),
                            )}
                            onValueChange={setSelectedTeam}
                            defaultValue={teamLeaderFilters.selectedTeam}
                            placeholder="Select Team"
                            label={
                              <span className="flex items-center">
                                {TeamIcons}
                                Team
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedArea ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.teamOptions.length === 0
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  <div className="flex-shrink-0 ml-auto">
                    {userRole !== UserRole.SHIFT_LEADER &&
                      (isVisualCheckActive ? (
                        isShiftStarted ? (
                          <Button
                            onClick={() => {
                              setIsEvaluationDialogOpen(true);
                              // Reset states after opening evaluation
                              // setIsShiftStarted(false);
                              // setHasOperatorsSent(false);
                            }}
                            className="flex items-center bg-black border-2 border-red-600 text-[#F84018] font-bold rounded-lg shadow-md disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200"
                            style={{
                              minWidth: "100px",
                              minHeight: "56px",
                              marginRight: "5px",
                            }}
                          >
                            {PlayIcons}
                            <span className="flex flex-col items-start text-left text-white leading-tight">
                              Close Shift
                            </span>
                          </Button>
                        ) : (
                          <Button
                            onClick={() => setIsShiftStarted(true)}
                            disabled={!hasOperatorsSent}
                            className="flex items-center bg-[#4762F1] border-2 border-[#005A04] text-white font-bold rounded-lg shadow-md hover:bg-[#3a56c5] disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200"
                            style={{
                              minWidth: "100px",
                              minHeight: "56px",
                              marginRight: "5px",
                            }}
                          >
                            {PlayIcons}
                            <span className="flex flex-col items-start text-left leading-tight">
                              Start Shift
                            </span>
                          </Button>
                        )
                      ) : (
                        <Button
                          onClick={startVisualCheck}
                          disabled={!canStartVisualCheck()}
                          className="flex items-center  bg-[#4762F1] border-2 border-[#005A04] text-white font-bold rounded-lg shadow-md hover:bg-[#3a56c5] disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200"
                          style={{
                            minWidth: "100px",
                            minHeight: "56px",
                            marginRight: "5px",
                          }}
                        >
                          {PlayIcons}
                          <span className="flex flex-col items-start text-left leading-tight">
                            Start Visual
                            <br />
                            Check
                          </span>
                        </Button>
                      ))}
                  </div>
                </div>

                {/* Second row - Attendance sheet info and other elements */}
                <div className="flex items-center gap-4">
                  {activeTab !== "my-backup-basket" &&
                    activeTab !== "department-backup-basket" && (
                      <div className="flex items-center gap-3 flex-shrink-0 min-w-0">
                        <div className="p-1 rounded-md flex-shrink-0">
                          <CustomIcon
                            name="dayView"
                            style={{
                              width: "30px",
                              height: "30px",
                              fill: "#5B7291",
                            }}
                          />
                        </div>
                        <div className="min-w-0">
                          <h1 className="text-lg font-medium text-gray-700 truncate">
                            Attendance sheet :
                          </h1>
                          <p className="text-sm text-gray-500 truncate">
                            Morning shift ({shiftStartTime} AM to 14:00 AM)
                          </p>
                        </div>
                      </div>
                    )}

                  {userRole !== UserRole.SHIFT_LEADER &&
                    userRole !== UserRole.QUALITY_SUPERVISOR && (
                      <>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />

                        {!isVisualCheckActive ? (
                          <>
                            <div className="flex items-center gap-2 bg-orange-50 px-3 py-2 rounded-md flex-shrink-0">
                              <CustomIcon
                                name="stopWatch"
                                style={{
                                  width: "32px",
                                  height: "36px",
                                  fill: "#F84018",
                                }}
                              />
                              <span className="text-xl font-bold truncate text-[#F84018]">
                                05:00
                              </span>
                            </div>
                          </>
                        ) : (
                          <div className="flex items-center gap-2 bg-orange-50 px-3 py-2 rounded-md flex-shrink-0">
                            <CustomIcon
                              name="stopWatch"
                              style={{
                                width: "32px",
                                height: "36px",
                                fill: "#F84018",
                              }}
                            />
                            <span className="text-xl font-bold truncate text-[#F84018]">
                              {formatTimer(visualCheckTimer)}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  {userRole === UserRole.TEAM_LEADER && isVisualCheckActive && (
                    <>
                      <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                      <div
                        className="flex flex-wrap items-center gap-3 md:gap-4 lg:gap-6 w-full max-w-full"
                        style={{ minWidth: 0 }}
                      >
                        {selectedWorkers.size > 0 && (
                          <>
                            <div className="flex-shrink-0 min-w-[180px] mb-2 md:mb-0">
                              <CustomSelect
                                options={[
                                  {
                                    label: "Present",
                                    value: "present",
                                  },
                                  {
                                    label: "Absent",
                                    value: "absent",
                                  },
                                  ...(filterMfgOnly
                                    ? [
                                        {
                                          label: "Send To ShiftLeader",
                                          value: "escalate",
                                        },
                                      ]
                                    : []),
                                ]}
                                onValueChange={(value) =>
                                  setSelectedAction(value)
                                }
                                value={selectedAction}
                                placeholder="Select Action"
                              />
                            </div>
                            <div className="flex flex-row flex-wrap gap-2 md:gap-3 mb-2 md:mb-0">
                              <ReusableButton
                                label={
                                  isSendingBackup ? "Submitting..." : "Submit"
                                }
                                onClick={handleSubmitAction}
                                disabled={isSendingBackup || !selectedAction}
                                className="bg-black text-white rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] disabled:bg-gray-400 disabled:cursor-not-allowed"
                              />
                              <ReusableButton
                                label="Cancel"
                                onClick={() => {
                                  setSelectedWorkers(new Set());
                                  setSelectedAction("");
                                }}
                                className="bg-white text-black hover:bg-gray-100 rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] border border-gray-300"
                              />
                            </div>
                            <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                          </>
                        )}

                        <div className="flex items-center gap-2 ml-0 md:ml-4">
                          <Switch
                            id="mfg-filter"
                            checked={filterMfgOnly}
                            onCheckedChange={toggleMfgFilter}
                          />
                          <Label htmlFor="mfg-filter" className="text-sm">
                            MFG Structure (Only)
                          </Label>
                        </div>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                        <div className="flex items-center b">
                          <Label htmlFor="mfg-filter" className="text-sm">
                            All Backup ({filteredWorkers.length})
                          </Label>
                        </div>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                        <div className="flex items-center">
                          <Label htmlFor="mfg-filter" className="text-sm">
                            Used Backup ({filteredWorkers.length})
                          </Label>
                        </div>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                        <div className="flex items-center  pr-3">
                          <Label htmlFor="mfg-filter" className="text-sm">
                            Unused Backup ({filteredWorkers.length})
                          </Label>
                        </div>
                      </div>
                    </>
                  )}
                  {/* Bulk Request Button for Team Leader */}
                  {userRole === UserRole.TEAM_LEADER && (
                    <div className="flex items-center gap-3 ml-auto">
                      <Button
                        onClick={handleOpenBulkRequestDialog}
                        className="flex items-center gap-2 px-3 py-5 rounded-lg bg-black text-white font-semibold shadow hover:bg-gray-800 focus:outline-none"
                      >
                        Bulk Request
                      </Button>
                    </div>
                  )}
                  {shouldShowApproveButton() &&
                    activeTab !== "my-backup-basket" &&
                    activeTab !== "department-backup-basket" && (
                      <div className="flex gap-3 ml-auto">
                        <Button
                          onClick={handleOpenBulkRequestDialog}
                          className="flex items-center gap-2 px-3 py-5 rounded-lg bg-black text-white font-semibold shadow hover:bg-gray-800 focus:outline-none"
                        >
                          Bulk Request
                        </Button>
                        <IconButton
                          icon={addIcon}
                          label="Approve"
                          onClick={() => setIsValidationDialogOpen(true)}
                          className="bg-black text-white rounded-lg px-3 py-5 flex items-center justify-center gap-1"
                        />
                      </div>
                    )}
                </div>
              </div>
            </div>
          </>
        ))}
      <ValidationDialog
        isDialogOpen={isValidationDialogOpen}
        setIsDialogOpen={setIsValidationDialogOpen}
        handleConfirm={handleApprove}
        labelCancel="Cancel"
        labelConfirm="Approve"
        isLoading={false}
      >
        Would you like to approve the presence sheet?
      </ValidationDialog>
      <SendForReviewDialog
        isOpen={isSendForReviewDialogOpen}
        onClose={handleCloseSendForReviewDialog}
        onSubmit={handleSendForReview}
        teamLeaderId="4857"
        teamLeaderName="Amine SIDKI"
        teamName="TEAM 1"
        shiftTime="Morning shift (06:00 AM to 14:00 AM)"
      />
      {activeTab !== "my-backup-basket" &&
        activeTab !== "department-backup-basket" && (
          <MyTeamTable
            activeTab={activeTab}
            selectedWorkers={selectedWorkers}
            setSelectedWorkers={setSelectedWorkers}
            filteredWorkersLength={filteredWorkers.length}
          />
        )}
      {activeTab === "my-backup-basket" && <MatchingInterface />}
      {activeTab === "department-backup-basket" && (
        <DepartmentBackup refreshKey={departmentRefreshKey} />
      )}
      <ReplacementFeedbackDialog
        isOpen={isEvaluationDialogOpen}
        onClose={() => setIsEvaluationDialogOpen(false)}
      />

      {/* Bulk Request Dialogs */}
      <BulkRequestDialog
        isOpen={isBulkRequestDialogOpen}
        onClose={handleCloseBulkRequestDialog}
        onNext={handleBulkRequestNext}
      />

      <RequestFormDialog
        isOpen={isBulkRequestFormDialogOpen}
        onClose={handleCloseBulkRequestFormDialog}
        onBack={handleBackToBulkRequestList}
        requestType={selectedBulkRequestType}
        selectedOperators={selectedBulkOperators}
        isBulkRequest={true}
      />
    </div>
  );
}
