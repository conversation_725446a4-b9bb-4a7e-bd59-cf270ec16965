"use client";

import React from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Button } from "@/components/ui/button";

interface ReplacementSuccessDialogProps {
  isOpen: boolean;
  onClose: () => void;
  actionType: "submit" | "no-replacement" | "send-to-shift-leader";
}

export function ReplacementSuccessDialog({
  isOpen,
  onClose,
  actionType,
}: ReplacementSuccessDialogProps) {
  return (
    <ReusableDialog isOpen={isOpen} onClose={onClose} title="" size="md">
      <div className="flex flex-col items-center text-center">
        {/* Success Icon */}
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6">
          <svg
            className="w-10 h-10 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={3}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>

        {/* Success Message */}
        <div className="mb-8">
          {actionType == "submit" && (
            <div className="text-lg font-medium text-gray-900 mb-2">
              The <span className="font-bold">replacement</span> has
              successfully been submitted.
            </div>
          )}
          {actionType == "no-replacement" && (
            <div className="text-lg font-medium text-gray-900 mb-2">
              Your choice to{" "}
              <span className="font-bold text-red-500">
                not make any replacement
              </span>{" "}
              has been confirmed.
            </div>
          )}
          {actionType == "send-to-shift-leader" && (
            <div className="text-lg font-medium text-gray-900 mb-2">
              Your request has successfully sent to your shift leader
            </div>
          )}
        </div>

        {/* Close Button */}
        <Button
          onClick={onClose}
          className="bg-transparent text-black border border-gray-300  px-8 py-2 rounded-xl"
          variant={"outline"}
        >
          Close
        </Button>
      </div>
    </ReusableDialog>
  );
}
