"use client";

import type React from "react";

import { useState, useRef } from "react";
import { formatMinutesToHHMM, parseTime, STATUS_MAP } from "../types/Types";

interface TimeSlot {
  id: string;
  date: string;
  start: string;
  end: string;
  status: string;
}

interface TimelineViewProps {
  timeSlots: TimeSlot[];
}

export function TimelineView({ timeSlots }: TimelineViewProps) {
  const totalMinutesInDay = 24 * 60; // 1440 minutes
  const [hoverTime, setHoverTime] = useState<string | null>(null);
  const [hoverPosition, setHoverPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = (x / rect.width) * 100;

    // Clamp percentage between 0 and 100
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    // Convert percentage to minutes
    const minutes = Math.round((clampedPercentage / 100) * totalMinutesInDay);

    // Convert minutes to HH:MM format
    const timeString = formatMinutesToHHMM(minutes);

    setHoverTime(timeString);
    setHoverPosition({ x: e.clientX, y: e.clientY });
  };

  const handleMouseLeave = () => {
    setHoverTime(null);
    setHoverPosition(null);
  };

  return (
    <>
      <div
        ref={timelineRef}
        className="relative h-16 w-full overflow-hidden rounded-lg border bg-muted/20 cursor-crosshair"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        {/* Timeline markers */}
        <div className="absolute inset-0 flex items-center justify-between px-2 text-xs text-muted-foreground pointer-events-none">
          <span className="absolute left-0 top-1/2 -translate-y-1/2">
            00:00
          </span>
          <span className="absolute left-[25%] top-1/2 -translate-x-1/2 -translate-y-1/2">
            06:00
          </span>
          <span className="absolute left-[50%] top-1/2 -translate-x-1/2 -translate-y-1/2">
            12:00
          </span>
          <span className="absolute left-[75%] top-1/2 -translate-x-1/2 -translate-y-1/2">
            18:00
          </span>
          <span className="absolute right-0 top-1/2 -translate-y-1/2">
            24:00
          </span>
        </div>

        {/* Vertical grid lines */}
        <div className="absolute inset-0 flex pointer-events-none">
          {Array.from({ length: 24 }).map((_, i) => (
            <div
              key={i}
              className="h-full border-l border-dashed border-gray-300/50"
              style={{ width: `${100 / 24}%` }}
            />
          ))}
        </div>

        {/* Time slot blocks */}
        <div className="absolute inset-0 pointer-events-none">
          {timeSlots.map((slot, index) => {
            const startMinutes = parseTime(slot.start);
            const endMinutes = parseTime(slot.end);

            // Calculate duration properly
            let durationMinutes = endMinutes - startMinutes;
            if (durationMinutes < 0) {
              durationMinutes += totalMinutesInDay; // Handle overnight within a single day's view
            }

            // Calculate position and width as percentages of the full day
            const leftPercentage = (startMinutes / totalMinutesInDay) * 100;
            const widthPercentage = (durationMinutes / totalMinutesInDay) * 100;

            const statusInfo = STATUS_MAP[slot.status] || {
              name: slot.status,
              color: "bg-gray-400",
            };

            // Ensure minimum width for very short durations (for visibility)
            const minWidthPercentage = 0.5; // 0.5% minimum width
            const finalWidthPercentage = Math.max(
              widthPercentage,
              minWidthPercentage,
            );

            return (
              <div
                key={slot.id}
                className={`absolute inset-y-0 rounded-sm px-2 flex items-center justify-center text-sm font-medium text-white ${statusInfo.color} ${index < timeSlots.length - 1 ? "border-r-2 border-orange-500" : ""} pointer-events-auto`}
                style={{
                  left: `${leftPercentage}%`,
                  width: `${finalWidthPercentage}%`,
                  zIndex: timeSlots.length - index, // Ensure later blocks are on top if overlapping
                }}
                title={`${slot.start} - ${slot.end} (${statusInfo.name}) - ${durationMinutes} minutes`}
              >
                <span className="truncate">{slot.status}</span>
              </div>
            );
          })}
        </div>

        {/* Hover indicator line */}
        {hoverTime && hoverPosition && (
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-red-500 pointer-events-none z-50"
            style={{
              left: `${(parseTime(hoverTime) / totalMinutesInDay) * 100}%`,
            }}
          />
        )}
      </div>

      {/* Hover tooltip */}
      {hoverTime && hoverPosition && (
        <div
          className="fixed z-50 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg pointer-events-none"
          style={{
            left: hoverPosition.x + 10,
            top: hoverPosition.y - 30,
          }}
        >
          {hoverTime}
        </div>
      )}
    </>
  );
}
