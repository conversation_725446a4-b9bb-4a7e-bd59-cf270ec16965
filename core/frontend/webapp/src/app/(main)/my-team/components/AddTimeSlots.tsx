"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type React from "react";
import { useState, useEffect } from "react";
import { Plus } from "lucide-react";
import { formatDuration } from "date-fns";
import { calculateDurationInMinutes, STATUS_MAP } from "../types/Types";
import TimeSelector from "@/components/common/TimeSelector";

interface AddTimeSlotFormProps {
  onAddTimeSlot: (
    start: string,
    end: string,
    status: string,
    duration: string,
  ) => void;
  selectedDate: Date;
  minStartTime: Date;
}

export function AddTimeSlotForm({ onAddTimeSlot }: AddTimeSlotFormProps) {
  const [startTime, setStartTime] = useState<string>("");
  const [endTime, setEndTime] = useState<string>("");
  const [status, setStatus] = useState("");
  const [duration, setDuration] = useState("0 min");

  useEffect(() => {
    if (startTime && endTime) {
      const minutes = calculateDurationInMinutes(startTime, endTime);
      setDuration(formatDuration({ minutes }));
    } else {
      setDuration("0 min");
    }
  }, [startTime, endTime]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (startTime && endTime && status) {
      onAddTimeSlot(startTime, endTime, status, duration);
      setStartTime("");
      setEndTime("");
      setStatus("");
      setDuration("0 min");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="grid gap-4 md:grid-cols-5">
      <div className="grid gap-2 pl-1">
        <TimeSelector
          label="Start *"
          value={startTime}
          onChange={setStartTime}
          placeholder="Select start time"
          format24Hour={true}
        />
      </div>
      <div className="grid gap-2">
        <TimeSelector
          label="End *"
          value={endTime}
          onChange={setEndTime}
          placeholder="Select end time"
          format24Hour={true}
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="status">Status *</Label>
        <Select value={status} onValueChange={setStatus} required>
          <SelectTrigger id="status">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(STATUS_MAP).map(([key, value]) => (
              <SelectItem key={key} value={key}>
                {value.name} ({key})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="grid gap-2">
        <Label htmlFor="duration">Duration :</Label>
        <div className="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm">
          {duration}
        </div>
      </div>
      <div className="flex items-end">
        <Button type="submit" className="w-full">
          <Plus className="mr-2 size-4" /> Add
        </Button>
      </div>
    </form>
  );
}
