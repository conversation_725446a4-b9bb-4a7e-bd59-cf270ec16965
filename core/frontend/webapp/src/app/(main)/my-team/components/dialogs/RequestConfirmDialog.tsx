"use client";

import { ReusableDialog } from "@/components/common/CustomDialog";
import { Button } from "@/components/ui/button";

interface RequestConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onSubmit: () => void;
  userInfo?: {
    name: string;
    id: string;
    status: string;
  };
  selectedOperators?: string[];
  isBulkRequest?: boolean;
  formData: {
    startDate?: Date;
    endDate?: Date;
    startTime: string;
    endTime: string;
    reason: string;
    paperProof: string;
    comment: string;
    duration?: string;
  };
}

function formatDate(date?: Date) {
  if (!date) return "-";
  return date.toLocaleDateString("en-GB");
}

function getDuration(start: string, end: string) {
  if (!start || !end) return "-";
  const parse = (t: string) => {
    const [time, meridian] = t.split(" ");
    const [h, m] = time.split(":").map(Number);
    let hour = h;
    if (meridian === "PM" && hour !== 12) hour += 12;
    if (meridian === "AM" && hour === 12) hour = 0;
    return hour * 60 + m;
  };
  const diff = parse(end) - parse(start);
  if (diff <= 0) return "-";
  const hours = Math.floor(diff / 60);
  const minutes = diff % 60;
  return `${hours > 0 ? hours + " Hour" + (hours > 1 ? "s" : "") : ""}${hours > 0 && minutes > 0 ? " " : ""}${minutes > 0 ? minutes + " min" : ""}`.trim();
}

export default function RequestConfirmDialog({
  isOpen,
  onClose,
  onBack,
  onSubmit,
  userInfo,
  selectedOperators,
  isBulkRequest = false,
  formData,
}: RequestConfirmDialogProps) {
  const duration = getDuration(formData.startTime, formData.endTime);
  return (
    <ReusableDialog isOpen={isOpen} onClose={onClose} title="" size="xl">
      <div className="w-full max-w-lg mx-auto min-h-0 flex flex-col space-y-6 p-1">
        <h2 className="text-center text-lg font-semibold text-[#4F5BD5] mt-2 mb-1">
          Confirm Your Request
        </h2>
        <p className="text-center text-gray-500 text-sm mb-2">
          Please review the request details and confirm or make changes as
          necessary.
        </p>
        <hr className="border-b border-gray-200 mb-2" />
        {/* 3 fields per line, then reason, then paper proof, each separated by a thin gray line */}
        <div className="space-y-0.5">
          <div className="grid grid-cols-3 gap-x-4 text-sm text-gray-700 py-2">
            <div>
              <span className="font-normal">Request for :</span>
              <div className="font-semibold">
                {isBulkRequest && selectedOperators ? (
                  <div>
                    <div>Bulk Request</div>
                    <div className="text-xs text-gray-500">
                      {selectedOperators.length} operator(s) selected
                    </div>
                  </div>
                ) : (
                  userInfo?.name || "-"
                )}
              </div>
            </div>
            <div>
              <span className="font-normal">Start Date :</span>
              <div>{formatDate(formData.startDate)}</div>
            </div>
            <div>
              <span className="font-normal">End Date :</span>
              <div>{formatDate(formData.endDate)}</div>
            </div>
          </div>
          <hr className="border-gray-200" />
          <div className="grid grid-cols-3 gap-x-4 text-sm text-gray-700 py-2">
            <div>
              <span className="font-normal">Duration :</span>
              <div>{duration}</div>
            </div>
            <div>
              <span className="font-normal">Start time :</span>
              <div>{formData.startTime}</div>
            </div>
            <div>
              <span className="font-normal">End time :</span>
              <div>{formData.endTime}</div>
            </div>
          </div>
          <hr className="border-gray-200" />
          <div className="py-2">
            <span className="font-normal">Reason for absence :</span>
            <div className="font-semibold">{formData.reason}</div>
          </div>
          <hr className="border-gray-200" />
          <div className="py-2">
            <span className="font-normal">Paper proof :</span>
            <div>{formData.paperProof === "yes" ? "Yes" : "No"}</div>
          </div>
          {formData.comment && (
            <>
              <hr className="border-gray-200" />
              <div className="py-2">
                <span className="font-normal">Comment :</span>
                <div>{formData.comment}</div>
              </div>
            </>
          )}
        </div>
        <div className="flex justify-between flex-shrink-0 mt-6">
          <Button variant="outline" onClick={onBack} className="px-6 py-2">
            Back
          </Button>
          <Button
            onClick={onSubmit}
            className="px-6 py-2 bg-black text-white hover:bg-gray-800"
          >
            Submit Request
          </Button>
        </div>
      </div>
    </ReusableDialog>
  );
}
