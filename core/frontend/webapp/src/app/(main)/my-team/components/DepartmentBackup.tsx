"use client";

import { useMemo, useEffect, useCallback } from "react";
import useReplacementStore from "../store/replacementStore";
import ReplacementInterface, {
  type ReplacementInterfaceConfig,
} from "./shared/ReplacementInterface";
import { PendingAssignment } from "../hooks/useReplacementLogic";

interface DepartmentBackupProps {
  refreshKey?: number;
}

export default function DepartmentBackup({
  refreshKey = 0,
}: DepartmentBackupProps) {
  // Store integration
  const {
    highLevelBackup,
    highLevelRequests,
    isLoadingHighLevelBackup,
    isLoadingHighLevelRequests,
    isAssigningHighLevelOperators,
    isEscalatingHighLevel,
    fetchHighLevelBackup,
    fetchHighLevelRequests,
    assignHighLevelOperators,
    escalateHighLevel,
    highLevelBackupError,
    highLevelRequestsError,
    assignHighLevelOperatorsError,
    escalateHighLevelError,
    clearHighLevelBackupError,
    clearHighLevelRequestsError,
    clearAssignHighLevelOperatorsError,
    clearEscalateHighLevelError,
  } = useReplacementStore();

  // Sample parameters - in real implementation, these would come from props or context
  const sampleParams = useMemo(
    () => ({
      shiftLeaderId: "d2dad5d4-d7d1-48e9-a3ed-e04387e28d40",
      shiftId: "b4bedff2-165e-4156-969f-d3b3cd025970",
      site: "89_MOROCCO MAR 1",
      department: "Assembly",
    }),
    [],
  );

  useEffect(() => {
    // Fetch requests if we don't have data, not currently loading, and no error
    if (!isLoadingHighLevelRequests && !highLevelRequestsError) {
      const fetchWithErrorHandling = async () => {
        try {
          await fetchHighLevelRequests(
            sampleParams.shiftLeaderId,
            sampleParams.shiftId,
            sampleParams.site,
          );
        } catch (error) {
          console.error(
            "Failed to fetch high-level requests, stopping retry:",
            error,
          );
          // Don't retry on API failures - error is handled by the store
        }
      };
      fetchWithErrorHandling();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sampleParams.shiftLeaderId, sampleParams.shiftId, sampleParams.site]);

  useEffect(() => {
    if (!isLoadingHighLevelBackup) {
      const fetchWithErrorHandling = async () => {
        try {
          await fetchHighLevelBackup(
            sampleParams.department,
            sampleParams.shiftId,
            sampleParams.site,
            [], // Empty skills array to get all operators
          );
        } catch (error) {
          console.error("Failed to fetch high-level backup:", error);
        }
      };
      fetchWithErrorHandling();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshKey]); // Only depends on refreshKey

  const handleFetchOperators = useCallback(
    async (skills: string[]) => {
      try {
        await fetchHighLevelBackup(
          sampleParams.department,
          sampleParams.shiftId,
          sampleParams.site,
          skills,
        );
      } catch (error) {
        console.error("Failed to fetch operators, stopping retry:", error);
        // Error is handled by the store, don't retry
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [sampleParams.department, sampleParams.shiftId, sampleParams.site],
  );

  const handleConfirmAssignments = useCallback(
    async (assignments: PendingAssignment[]) => {
      try {
        // Convert to the expected format for high-level assignments
        // Note: High-level only supports single operator per request
        const operatorAssignments = assignments.flatMap((assignment) =>
          assignment.operatorIds.map((operatorId: string) => ({
            requestId: assignment.requestId,
            replacementOperatorId: operatorId,
          })),
        );

        await assignHighLevelOperators(
          sampleParams.department,
          sampleParams.shiftId,
          sampleParams.site,
          operatorAssignments,
        );

        // Refresh the requests data to get updated completion status
        fetchHighLevelRequests(
          sampleParams.shiftLeaderId,
          sampleParams.shiftId,
          sampleParams.site,
        );

        // Refresh the operators data to reflect updated availability status
        fetchHighLevelBackup(
          sampleParams.department,
          sampleParams.shiftId,
          sampleParams.site,
          [], // Empty skills array to get all operators with updated status
        );
      } catch (error) {
        console.error("Failed to assign operators:", error);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      sampleParams.department,
      sampleParams.shiftLeaderId,
      sampleParams.shiftId,
      sampleParams.site,
    ],
  );

  const handleProcessOperators = useCallback(async (operatorIds: string[]) => {
    console.log("Processing department operators:", operatorIds);
    // Implement department operator processing logic here
  }, []);

  const handleEscalateRequests = useCallback(
    async (requestIds: string[]) => {
      try {
        await escalateHighLevel(
          requestIds,
          sampleParams.department,
          sampleParams.shiftId,
          sampleParams.site,
        );
        console.log("Requests escalated:", requestIds);
      } catch (error) {
        console.error("Failed to escalate department requests:", error);
        throw error; // Re-throw to be caught by the UI
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [sampleParams.department, sampleParams.shiftId, sampleParams.site],
  );

  const config: ReplacementInterfaceConfig = {
    department: "departement-backup-basket",
    operatorsTitle: "All Backup",
    requestsTitle: "All Requests",
    operators: (highLevelBackup || []).map((operator) => ({
      ...operator,
      projectHierarchy:
        typeof operator.projectHierarchy === "string"
          ? null
          : operator.projectHierarchy,
    })),
    requests: (highLevelRequests || []).map((request) => ({
      ...request,
      teamLeader: {
        ...request.teamLeader,
        legacyId: request.teamLeader.legacyId ?? 0,
      },
    })),
    isLoading:
      isLoadingHighLevelBackup ||
      isLoadingHighLevelRequests ||
      isAssigningHighLevelOperators ||
      isEscalatingHighLevel,
    errors: {
      operatorError: highLevelBackupError || undefined,
      requestError:
        highLevelRequestsError ||
        assignHighLevelOperatorsError ||
        escalateHighLevelError ||
        undefined,
    },
    onClearErrors: {
      clearOperatorError: clearHighLevelBackupError,
      clearRequestError: () => {
        clearHighLevelRequestsError();
        clearAssignHighLevelOperatorsError();
        clearEscalateHighLevelError();
      },
    },
    onFetchOperators: handleFetchOperators,
    onConfirmAssignments: handleConfirmAssignments,
    onProcessOperators: handleProcessOperators,
    onEscalateRequests: handleEscalateRequests,
    operatorActionLabel: "PROCESS DEPARTMENT REQUEST",
    requestActionLabel: "ESCALATE REQUESTS",
  };

  return <ReplacementInterface config={config} />;
}
