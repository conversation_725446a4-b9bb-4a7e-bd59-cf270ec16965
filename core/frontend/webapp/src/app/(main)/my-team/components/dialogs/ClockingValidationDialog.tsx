"use client";

import {
  useAttendanceStore,
  ClockingValidationStatus,
  TeamStatus,
} from "../../store/myTeamStore";
import { ReusableDialog } from "@/components/common/CustomDialog";
import CustomIcon from "@/components/common/CustomIcons";
import { Check, X, Clock, AlertCircle } from "lucide-react";
import { useState } from "react";
import { DatePicker } from "@/components/ui/datePicker";
import CustomSelect from "@/components/common/CustomSelect";
import { ChevronLeft } from "lucide-react";

interface ClockingValidationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  userInfo?: {
    name: string;
    id: string;
    status: string;
    statusType: "present" | "absent";
  };
  workerId?: string | null;
}

export function ClockingValidationDialog({
  isOpen,
  onClose,
  userInfo = {
    name: "<PERSON>",
    id: "123953",
    status: "Present (Line)",
    statusType: "present",
  },
  workerId,
}: ClockingValidationDialogProps) {
  const {
    getTeamStatuses,
    workers,
    currentDate,
    shiftStartTime,
    attendanceRecords,
    setSelectedValidationTeam,
    updateTeamStatus,
  } = useAttendanceStore();
  const teamStatuses = workerId ? getTeamStatuses(workerId) : [];

  // New state for enhanced dialog
  const [selectedTeam, setSelectedTeam] = useState<TeamStatus | null>(null);
  const [showTeamSheetView, setShowTeamSheetView] = useState(false);
  const [showSendForReviewDialog, setShowSendForReviewDialog] = useState(false);
  const [reviewComment, setReviewComment] = useState("");

  // Status icon for each team
  const getStatusIcon = (
    status: ClockingValidationStatus,
    reviewed?: boolean,
  ) => {
    if (status === "Validated")
      return <Check className="h-5 w-5 text-green-600" />;
    if (status === "Waiting validation..." && reviewed)
      return <Clock className="h-5 w-5 text-orange-600" />;
    if (status === "Waiting validation...")
      return <Clock className="h-5 w-5 text-orange-400" />;
    if (status === "Absent") return <X className="h-5 w-5 text-red-600" />;
    if (status === "Not finished yet")
      return <AlertCircle className="h-5 w-5 text-gray-400" />;
    return <AlertCircle className="h-5 w-5 text-gray-400" />;
  };

  // Card color for each team
  const getStatusClass = (
    status: ClockingValidationStatus,
    reviewed?: boolean,
  ) => {
    if (status === "Validated")
      return "bg-green-50 border-green-200 text-green-700";
    if (status === "Waiting validation..." && reviewed)
      return "bg-orange-50 border-orange-200 text-orange-700";
    if (status === "Waiting validation...")
      return "bg-yellow-50 border-yellow-200 text-yellow-700";
    if (status === "Absent") return "border-red-300 text-red-700 bg-white";
    if (status === "Not finished yet")
      return "bg-gray-50 border-gray-200 text-gray-500";
    return "bg-gray-50 border-gray-200 text-gray-500";
  };

  // Special text for status
  const getStatusText = (
    status: ClockingValidationStatus,
    reviewed?: boolean,
  ) => {
    if (status === "Waiting validation..." && reviewed)
      return (
        <span>
          Waiting validation...<span className="text-red-500">(Reviewed)</span>
        </span>
      );
    if (status === "Not finished yet") return "Not Finished yet";
    return status;
  };

  // For demo, mark Team 4 as reviewed
  const isReviewed = (team: TeamStatus) => !!team.reviewed;

  // Make teams with 'Waiting validation...' or 'Absent' status clickable, but not if reviewed
  const isClickable = (team: TeamStatus) =>
    (team.status === "Waiting validation..." && !team.reviewed) ||
    team.status === "Absent";

  // Handler for team click: open team sheet view
  const handleTeamClick = (team: TeamStatus) => {
    if (!isClickable(team) || !workerId) return;
    setSelectedValidationTeam(team);
    onClose();
  };

  // Handler for return button
  const handleReturn = () => {
    setShowTeamSheetView(false);
    setSelectedTeam(null);
    setShowSendForReviewDialog(false);
    setReviewComment("");
  };

  // Handler for confirm button
  const handleConfirm = () => {
    // For now, just return to shift leader view
    setShowTeamSheetView(false);
    setSelectedTeam(null);
  };

  // Handler for send for review button
  const handleSendForReview = () => {
    setShowSendForReviewDialog(true);
  };

  // Handler for send for review dialog confirm
  const handleReviewConfirm = () => {
    if (workerId && selectedTeam) {
      updateTeamStatus(
        workerId,
        selectedTeam.teamId,
        "Waiting validation...",
        true,
      );
    }
    setShowSendForReviewDialog(false);
    setShowTeamSheetView(false);
    setSelectedTeam(null);
    setReviewComment("");
  };

  // Handler for send for review dialog cancel
  const handleReviewCancel = () => {
    setShowSendForReviewDialog(false);
    setReviewComment("");
  };

  // Find team leader info (mocked for now)
  const teamLeader = { id: "4857", name: "Amine SIDKI" };
  const shiftTime = `Morning shift (${shiftStartTime} AM to 14:00 AM)`;

  // Read-only table rendering (same columns as Team Leader, no interactivity)
  const renderReadOnlyTable = () => {
    // For demo, filter workers by team if possible
    const filteredWorkers = workers.filter(
      (w) =>
        !selectedTeam ||
        !selectedTeam.teamName ||
        w.team === selectedTeam.teamName,
    );
    // Generate calendar days for current month
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const calendarDays = Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      const date = new Date(year, month, day);
      return {
        day,
        date: date.toISOString().split("T")[0],
        dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
      };
    });
    return (
      <div className="min-w-max">
        <table
          className="w-full border-collapse"
          style={{ borderColor: "#9D9D9D" }}
        >
          <thead className="sticky top-0 bg-white z-10">
            <tr>
              <th
                className="sticky left-0 z-30 border p-2 text-left min-w-[80px] text-sm font-medium"
                style={{ backgroundColor: "#BFD5F1", borderColor: "#9D9D9D" }}
              >
                Mle
              </th>
              <th
                className="sticky left-[80px] z-30 border p-2 text-left min-w-[100px] text-sm font-medium"
                style={{ backgroundColor: "#BFD5F1", borderColor: "#9D9D9D" }}
              >
                First Name
              </th>
              <th
                className="sticky left-[180px] z-30 border p-2 text-left min-w-[100px] text-sm font-medium"
                style={{ backgroundColor: "#BFD5F1", borderColor: "#9D9D9D" }}
              >
                Last Name
              </th>
              <th
                className="sticky left-[280px] z-30 border p-2 text-left min-w-[100px] text-sm font-medium"
                style={{ backgroundColor: "#BFD5F1", borderColor: "#9D9D9D" }}
              >
                Role
              </th>
              <th
                className="sticky left-[380px] z-30 border p-2 text-left min-w-[100px] text-sm font-medium"
                style={{ backgroundColor: "#BFD5F1", borderColor: "#9D9D9D" }}
              >
                Function
              </th>
              {calendarDays.map(({ day, dayName }) => (
                <th
                  key={day}
                  className="border p-1 text-center min-w-[60px] z-5"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    color: "#000000",
                  }}
                >
                  <div className="text-xs font-medium">
                    {day} {dayName}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredWorkers.map((worker) => (
              <tr key={worker.id}>
                <td
                  className="sticky left-0 border p-2 text-sm z-20"
                  style={{ backgroundColor: "#F5FBFF", borderColor: "#9D9D9D" }}
                >
                  {worker.id}
                </td>
                <td
                  className="sticky left-[80px] border p-2 text-sm z-20"
                  style={{ backgroundColor: "#F5FBFF", borderColor: "#9D9D9D" }}
                >
                  {worker.firstName}
                </td>
                <td
                  className="sticky left-[180px] border p-2 text-sm z-20"
                  style={{ backgroundColor: "#F5FBFF", borderColor: "#9D9D9D" }}
                >
                  {worker.lastName}
                </td>
                <td
                  className="sticky left-[280px] border p-2 text-xs z-20"
                  style={{ backgroundColor: "#F5FBFF", borderColor: "#9D9D9D" }}
                >
                  <div className="text-blue-600 font-medium">{worker.role}</div>
                </td>
                <td
                  className="sticky left-[380px] border p-2 text-sm z-20"
                  style={{ backgroundColor: "#F5FBFF", borderColor: "#9D9D9D" }}
                >
                  <div className="text-blue-500 font-medium">
                    {worker.function}
                  </div>
                </td>
                {calendarDays.map(({ day, date }) => {
                  // Read-only cell: just show the status, no click
                  const record = attendanceRecords.find(
                    (rec) => rec.workerId === worker.id && rec.date === date,
                  );
                  const status = record ? record.status : "";
                  return (
                    <td
                      key={day}
                      className="border p-0 relative z-10"
                      style={{ borderColor: "#9D9D9D" }}
                    >
                      <div className="w-full h-12 flex items-center justify-center text-xs font-medium">
                        {status}
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Header for team sheet view
  const renderTeamSheetHeader = () => (
    <div className="flex flex-col gap-2 border-b pb-2 mb-2">
      <div className="flex items-center gap-2">
        <button
          onClick={handleReturn}
          className="p-2 rounded hover:bg-gray-100 flex items-center"
        >
          <ChevronLeft className="w-5 h-5" /> Return
        </button>
        <div className="flex-1 flex gap-2">
          <DatePicker label="Date" placeholder="Select a date" />
          <CustomSelect
            options={[]}
            onValueChange={() => {}}
            placeholder="Select customer"
            label={<span>Customer</span>}
          />
          <CustomSelect
            options={[]}
            onValueChange={() => {}}
            placeholder="Select project"
            label={<span>Project</span>}
          />
          <CustomSelect
            options={[]}
            onValueChange={() => {}}
            placeholder="Select family"
            label={<span>Family</span>}
          />
          <CustomSelect
            options={[]}
            onValueChange={() => {}}
            placeholder="Select value stream"
            label={<span>Value Stream</span>}
          />
          <CustomSelect
            options={[]}
            onValueChange={() => {}}
            placeholder="Select area"
            label={<span>Area</span>}
          />
          <CustomSelect
            options={[]}
            onValueChange={() => {}}
            placeholder="Select Team"
            label={<span>Team</span>}
          />
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleConfirm}
            className="bg-black text-white px-4 py-2 rounded"
          >
            Confirm
          </button>
          <button
            onClick={handleSendForReview}
            className="bg-red-600 text-white px-4 py-2 rounded"
          >
            Send for review
          </button>
        </div>
      </div>
      <div className="flex items-center gap-4 mt-2">
        <div className="flex flex-col">
          <span className="font-semibold">Attendance sheet</span>
          <span className="text-xs text-gray-500">{shiftTime}</span>
        </div>
        <div className="flex flex-col">
          <span className="font-semibold">
            {teamLeader.id} - {teamLeader.name}
          </span>
          <span className="text-xs text-gray-500">
            {selectedTeam?.teamName}
          </span>
        </div>
      </div>
    </div>
  );

  // Send for review dialog
  const renderSendForReviewDialog = () => (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-2">
          Return Attendance sheet to Team Leader
        </h2>
        <div className="flex items-center gap-4 mb-4">
          <div className="flex flex-col flex-1">
            <span className="font-semibold">Attendance sheet</span>
            <span className="text-xs text-gray-500">{shiftTime}</span>
          </div>
          <div className="flex flex-col flex-1">
            <span className="font-semibold">
              {teamLeader.id} - {teamLeader.name}
            </span>
            <span className="text-xs text-gray-500">
              {selectedTeam?.teamName}
            </span>
          </div>
        </div>
        <textarea
          className="w-full border rounded p-2 mb-4 min-h-[80px]"
          placeholder="Add a comment here..."
          value={reviewComment}
          onChange={(e) => setReviewComment(e.target.value)}
        />
        <div className="flex justify-end gap-2">
          <button
            onClick={handleReviewCancel}
            className="px-4 py-2 border rounded"
          >
            Cancel
          </button>
          <button
            onClick={handleReviewConfirm}
            className="px-4 py-2 bg-black text-white rounded"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );

  // Main render
  if (!isOpen) return null;

  if (showTeamSheetView && selectedTeam) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
        <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-auto">
          {renderTeamSheetHeader()}
          {renderReadOnlyTable()}
          {showSendForReviewDialog && renderSendForReviewDialog()}
        </div>
      </div>
    );
  }

  // Default: team list view
  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={onClose}
      title="Select a team to access to there Attendance sheet"
      size="xl"
    >
      <div className="w-full max-w-lg mx-auto min-h-0 flex flex-col p-1">
        {/* User Info Section */}
        <div className="flex flex-col items-center mb-6 flex-shrink-0">
          <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mb-3">
            <CustomIcon
              name="circleUser"
              className="w-16 h-16 bg-white "
              style={{ width: "80px", height: "80px", fill: "#D1D1D1" }}
            />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">
            {userInfo.name}
          </h3>
          <p className="text-sm text-gray-500">ID : {userInfo.id}</p>
          <div className="flex items-center mt-2">
            {userInfo.statusType === "present" ? (
              <>
                <Check className="w-5 h-5 text-green-500 mr-1" />
                <span className="text-sm text-green-600">
                  {userInfo.status}
                </span>
              </>
            ) : (
              <>
                <X className="w-5 h-5 text-red-500 mr-1" />
                <span className="text-sm text-red-600">{userInfo.status}</span>
              </>
            )}
          </div>
        </div>
        {/* Team List */}
        <div className="flex flex-col gap-3 mb-2">
          {teamStatuses
            .filter((t) => t.status !== "Absent")
            .map((team) => (
              <div
                key={team.teamId}
                className={`flex items-center justify-between border rounded-lg px-4 py-3 ${getStatusClass(team.status, isReviewed(team))} ${isClickable(team) ? "cursor-pointer" : ""}`}
                onClick={() => handleTeamClick(team)}
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(team.status, isReviewed(team))}
                  <span className="font-medium">{team.teamName}</span>
                </div>
                <div className="text-right text-sm font-medium">
                  {getStatusText(team.status, isReviewed(team))}
                </div>
              </div>
            ))}
        </div>
        {/* Absent Card */}
        {(() => {
          const absentTeam = teamStatuses.find((t) => t.status === "Absent");
          if (!absentTeam) return null;
          return (
            <div
              className="flex items-center justify-between border-2 border-red-400 rounded-lg px-4 py-3 bg-white text-red-700 mb-2 cursor-pointer"
              onClick={() => handleTeamClick(absentTeam)}
            >
              <div className="flex items-center gap-3">
                <X className="h-5 w-5 text-red-600" />
                <span className="font-medium">Absent</span>
              </div>
            </div>
          );
        })()}
        {/* Close Button */}
        <div className="flex justify-center mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 min-w-[120px]"
          >
            Close
          </button>
        </div>
      </div>
    </ReusableDialog>
  );
}
