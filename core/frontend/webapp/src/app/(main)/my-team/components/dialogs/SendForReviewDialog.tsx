import React, { useState } from "react";
import CustomIcon from "@/components/common/CustomIcons";
import { ReusableDialog } from "@/components/common/CustomDialog";
import CustomInput from "@/components/common/CustomInput";
import { Button } from "@/components/ui/button";

interface SendForReviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (comment: string) => void;
  teamLeaderId: string;
  teamLeaderName: string;
  teamName: string;
  shiftTime: string;
}

export const SendForReviewDialog: React.FC<SendForReviewDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  teamLeaderId,
  teamLeaderName,
  teamName,
  shiftTime,
}) => {
  const [comment, setComment] = useState("");

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={onClose}
      title="Return Attendance sheet to Team Leader"
      size="lg"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex flex-col items-center flex-1">
          <CustomIcon
            className="mt-[-10px]"
            name="dayView"
            style={{ width: 48, height: 48, fill: "#5B7291" }}
          />
          <span className="font-bold ">Attendance sheet</span>
          <span className="text-xs text-gray-500">{shiftTime}</span>
        </div>
        <div className="flex flex-col items-center justify-center mx-4 min-w-[120px] mt-[-80px]">
          <span className="text-sm text-gray-700 mb-1">Return for review</span>
          <div className="flex items-center w-full">
            <div className="h-0.5 bg-gray-300 flex-1" />
            <span className="mx-1">
              <svg
                width="24"
                height="12"
                viewBox="0 0 24 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <line
                  x1="0"
                  y1="6"
                  x2="20"
                  y2="6"
                  stroke="#D1D5DB"
                  strokeWidth="2"
                />
                <polygon points="20,2 24,6 20,10" fill="#D1D5DB" />
              </svg>
            </span>
          </div>
        </div>
        <div className="flex-1 flex flex-col items-center">
          <CustomIcon
            name="circleUser"
            style={{ width: 48, height: 48, fill: "#4762F1" }}
          />
          <span className="font-bold mt-2">{teamLeaderId}</span>
          <span className="text-sm text-gray-700">{teamLeaderName}</span>
          <span className="text-sm text-gray-700">{"(Team Leader)"}</span>
          <span className="text-xs text-blue-600">{teamName}</span>
        </div>
      </div>
      <CustomInput
        className="w-full rounded p-2 mb-4 min-h-[80px]"
        placeholder="Add a comment here..."
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        multiline={true}
      />

      {/* Action Buttons */}
      <div className="flex justify-between flex-shrink-0 mt-4">
        <Button variant="outline" onClick={onClose} className="px-6 py-5">
          Cancel
        </Button>
        <Button
          onClick={() => {
            onSubmit(comment);
            setComment("");
          }}
          className="px-6 py-5 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Confirm
        </Button>
      </div>
    </ReusableDialog>
  );
};
