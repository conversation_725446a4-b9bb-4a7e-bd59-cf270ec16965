"use client";

import React, { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { DataTable } from "@/components/common/tables/DataTable";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
  CustomTabsContent,
} from "@/components/common/CustomTabs";
import { Button } from "@/components/ui/button";
import { HelpCircle } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import CustomIcon from "@/components/common/CustomIcons";
import useReplacementStore, {
  Operator,
  ReplacementOperator,
} from "../../store/replacementStore";
import { ReplacementSummaryDialog } from "./ReplacementSummaryDialog";
import { ReplacementSuccessDialog } from "./ReplacementSuccessDialog";

interface ReplaceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  absentEmployee?: Operator;
  teamLeaderId?: string;
  shiftId?: string;
}

export function ReplaceDialog({
  isOpen,
  onClose,
  absentEmployee,
  teamLeaderId,
  shiftId,
}: ReplaceDialogProps) {
  // Parent team tab state - will be set to first team ID when API data loads
  const [activeTeamTab, setActiveTeamTab] = useState<string>("");
  // Structure tab state
  const [activeStructureTab, setActiveStructureTab] = useState("mfg"); // "mfg", "me-structure"
  const [selectedWorkers, setSelectedWorkers] = useState<Operator[]>([]); // For both single and multi-select
  const [workerReplacementTypes, setWorkerReplacementTypes] = useState<
    Record<string, "move" | "both">
  >({}); // For individual worker types

  // Dialog states
  const [showSummaryDialog, setShowSummaryDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [summaryActionType, setSummaryActionType] = useState<
    "submit" | "no-replacement" | "send-to-shift-leader"
  >("submit");

  // Replacement store
  const {
    setAbsentEmployeeId,
    setTeamLeaderId,
    setShiftId,
    fetchReplacementSuggestions,
    submitReplacementRequest,
    raiseRequestToShiftLeader,
    isLoadingSuggestions,
    isSubmittingReplacement,
    isRaisingRequest,
    replacementData,
  } = useReplacementStore();

  // Set the first team as active when replacement data is loaded OR when dialog opens
  React.useEffect(() => {
    if (replacementData?.teams && replacementData.teams.length > 0) {
      //search for team with teamname Same Team and set it as active team
      const sameTeam = replacementData.teams.find(
        (team) => team.teamName === "Same Team",
      );
      if (sameTeam) {
        setActiveTeamTab(sameTeam.teamId);
      } else {
        setActiveTeamTab(replacementData.teams[0].teamId);
      }
    }
  }, [replacementData]);

  // Ensure active tab is set when dialog opens
  React.useEffect(() => {
    if (isOpen) {
      if (replacementData?.teams && replacementData.teams.length > 0) {
        setActiveTeamTab(replacementData.teams[0].teamId);
      } else {
        // Set fallback to "same" for placeholder tabs
        setActiveTeamTab("same");
      }
    }
  }, [isOpen, replacementData]);

  // Clear selections when changing tabs or structure
  React.useEffect(() => {
    setSelectedWorkers([]);
    setWorkerReplacementTypes({});
  }, [activeTeamTab, activeStructureTab, replacementData]);

  // Load replacement suggestions when dialog opens
  React.useEffect(() => {
    console.log(
      "Loading replacement suggestions for",
      absentEmployee?.id,
      teamLeaderId,
    );
    if (isOpen && absentEmployee?.id && teamLeaderId && shiftId) {
      // Set the required values
      setAbsentEmployeeId(absentEmployee.id);
      setTeamLeaderId(teamLeaderId);
      setShiftId(shiftId);

      // Fetch replacement suggestions
      fetchReplacementSuggestions();
    }
  }, [
    isOpen,
    absentEmployee?.id,
    teamLeaderId,
    shiftId,
    fetchReplacementSuggestions,
    setAbsentEmployeeId,
    setShiftId,
    setTeamLeaderId,
  ]);

  // Test function to fire the API (for debugging)

  // Get operators from the selected team
  const getSelectedTeamOperators = () => {
    if (!replacementData?.teams || !activeTeamTab) return [];

    const selectedTeam = replacementData.teams.find(
      (team) => team.teamId === activeTeamTab,
    );
    return selectedTeam?.operators || [];
  };

  // Check if multi-select should be enabled (ME Structure only)
  const isMultiSelectEnabled = () => {
    return activeStructureTab === "me-structure";
  };

  // Handle worker selection (single or multi)
  const handleWorkerSelection = (operator: Operator) => {
    const isSelected = selectedWorkers.some((w) => w.id === operator.id);

    if (isMultiSelectEnabled()) {
      // Multi-select mode
      if (isSelected) {
        setSelectedWorkers((prev) => prev.filter((w) => w.id !== operator.id));
        // Remove the replacement type for this worker
        setWorkerReplacementTypes((prev) => {
          const newTypes = { ...prev };
          delete newTypes[operator.id];
          return newTypes;
        });
      } else {
        setSelectedWorkers((prev) => [...prev, operator]);
        // Set default replacement type for this worker
        setWorkerReplacementTypes((prev) => ({
          ...prev,
          [operator.id]: "move",
        }));
      }
    } else {
      // Single select mode
      if (isSelected) {
        setSelectedWorkers([]);
        setWorkerReplacementTypes({});
      } else {
        setSelectedWorkers([operator]);
        setWorkerReplacementTypes({ [operator.id]: "move" });
      }
    }
  };

  // Check if a worker is selected
  const isWorkerSelected = (operator: Operator) => {
    return selectedWorkers.some((w) => w.id === operator.id);
  };

  // Filter operators by structure type (MFG vs ME)
  const selectedTeamOperators = getSelectedTeamOperators();
  const mfgOperators = selectedTeamOperators.filter(
    (operator) => operator.isMFG,
  );
  const meStructureOperators = selectedTeamOperators.filter(
    (operator) => !operator.isMFG,
  );

  // Table columns for MFG operators (simplified - no qualification/polyvalence, workstation renamed to function)
  const mfgOperatorColumns: ColumnDef<Operator>[] = [
    {
      accessorKey: "legacyId",
      header: "ID",
      cell: ({ row }) => (
        <div className="font-bold">{row.getValue("legacyId")}</div>
      ),
    },
    {
      accessorKey: "firstName",
      header: "First name",
      cell: ({ row }) => <div>{row.getValue("firstName")}</div>,
    },
    {
      accessorKey: "lastName",
      header: "Last name",
      cell: ({ row }) => <div>{row.getValue("lastName")}</div>,
    },
    {
      accessorKey: "workstation",
      header: "Function",
      cell: ({ row }) => {
        const workstations = row.getValue(
          "workstation",
        ) as Operator["workstation"];
        const primaryWorkstation =
          workstations?.find((w) => w.isOwner)?.workstation ||
          workstations?.[0]?.workstation ||
          "N/A";
        return <div>{primaryWorkstation}</div>;
      },
    },
    {
      accessorKey: "sss",
      header: "",
      cell: ({}) => {
        return <div></div>;
      },
    },
    {
      accessorKey: "ssss",
      header: "",
      cell: ({}) => {
        return <div></div>;
      },
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const operator = row.original;
        const isSelected = isWorkerSelected(operator);

        return (
          <div className="flex justify-end items-center w-full pr-4">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleWorkerSelection(operator)}
              className={
                isSelected
                  ? "border-transparent text-white bg-[#4CAF50]"
                  : "border-[#3B4A6B] text-[#3B4A6B] bg-[#ECF3FC]"
              }
            >
              {isSelected ? "Selected" : "Select"}
            </Button>
          </div>
        );
      },
    },
  ];

  // Table columns for ME Structure operators (full columns with qualification/polyvalence)
  const meOperatorColumns: ColumnDef<Operator>[] = [
    {
      accessorKey: "legacyId",
      header: "ID",
      cell: ({ row }) => (
        <div className="font-bold">{row.getValue("legacyId")}</div>
      ),
    },
    {
      accessorKey: "firstName",
      header: "First name",
      cell: ({ row }) => <div>{row.getValue("firstName")}</div>,
    },
    {
      accessorKey: "lastName",
      header: "Last name",
      cell: ({ row }) => <div>{row.getValue("lastName")}</div>,
    },
    {
      accessorKey: "workstation",
      header: "Workstation",
      cell: ({ row }) => {
        const workstations = row.getValue(
          "workstation",
        ) as Operator["workstation"];
        const primaryWorkstation =
          workstations?.find((w) => w.isOwner)?.workstation ||
          workstations?.[0]?.workstation ||
          "N/A";
        return <div>{primaryWorkstation}</div>;
      },
    },
    {
      accessorKey: "skills",
      header: "Qualification",
      cell: ({ row }) => {
        const skills = row.getValue("skills") as Operator["skills"];
        return (
          <div className="flex flex-wrap gap-1">
            {skills?.map((skill, index) => (
              <span
                key={index}
                className="bg-[#E9ECF6] text-[#3B4A6B] rounded px-2 py-0.5 text-xs font-semibold"
              >
                {skill.skillCode}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "Polyvalence",
      header: "Polyvalence",
      cell: ({ row }) => {
        const workstations = row.getValue(
          "workstation",
        ) as Operator["workstation"];
        return <div>{workstations?.length || 0}</div>;
      },
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const operator = row.original;
        const isSelected = isWorkerSelected(operator);

        return (
          <div className="flex justify-between items-center gap-4 w-full pr-4">
            {/* Reserve space for checkboxes */}
            <div className="flex gap-4 items-center">
              {isSelected &&
              !operator.isMFG &&
              activeStructureTab === "me-structure" ? (
                <>
                  <label className="flex items-center gap-2 text-sm text-gray-700">
                    <Checkbox
                      checked={
                        (workerReplacementTypes[operator.id] || "move") ===
                        "move"
                      }
                      onCheckedChange={() => {
                        setWorkerReplacementTypes((prev) => ({
                          ...prev,
                          [operator.id]: "move",
                        }));
                      }}
                    />
                    <span>Move</span>
                  </label>
                  <label className="flex items-center gap-1 text-sm text-gray-700">
                    <Checkbox
                      checked={
                        (workerReplacementTypes[operator.id] || "move") ===
                        "both"
                      }
                      onCheckedChange={() => {
                        setWorkerReplacementTypes((prev) => ({
                          ...prev,
                          [operator.id]: "both",
                        }));
                      }}
                    />
                    <span>Work on both workstations</span>
                  </label>
                </>
              ) : (
                <div className="h-5" />
              )}
            </div>

            {/* Button aligned right */}
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleWorkerSelection(operator)}
              className={
                isSelected
                  ? "border-transparent text-white bg-[#4CAF50]"
                  : "border-[#3B4A6B] text-[#3B4A6B] bg-[#ECF3FC]"
              }
            >
              {isSelected ? "Selected" : "Select"}
            </Button>
          </div>
        );
      },
    },
  ];

  // Helper function to get criticity color
  const getCriticityColor = (criticity: string) => {
    const lowerCriticity = criticity.toLowerCase();
    if (
      lowerCriticity.includes("high") ||
      lowerCriticity.includes("critical")
    ) {
      return "bg-red-100 text-red-800";
    } else if (
      lowerCriticity.includes("medium") ||
      lowerCriticity.includes("moderate")
    ) {
      return "bg-yellow-100 text-yellow-800";
    } else {
      return "bg-green-100 text-green-800";
    }
  };

  const handleConfirmReplacement = async () => {
    // Show summary dialog for sending to shift leader
    setSummaryActionType("send-to-shift-leader");
    setShowSummaryDialog(true);
  };

  const handleConfirmSendToShiftLeader = async () => {
    if (!absentEmployee?.id || !teamLeaderId || !shiftId) {
      console.error("Missing required parameters for raising request");
      return;
    }

    try {
      const reason =
        selectedWorkers.length > 0
          ? `Unable to find suitable replacement. Selected ${selectedWorkers.length} worker(s) but need escalation.`
          : "Unable to find a suitable replacement worker";

      await raiseRequestToShiftLeader(
        absentEmployee.id,
        shiftId,
        teamLeaderId,
        reason,
      );

      console.log("Request raised to shift leader successfully");
      setShowSummaryDialog(false); // Close summary dialog
      setShowSuccessDialog(true); // Show success dialog
    } catch (error) {
      console.error("Failed to raise request to shift leader:", error);
    }
  };

  const handleSubmit = async () => {
    // Show summary dialog for submitting replacement
    setSummaryActionType("submit");
    setShowSummaryDialog(true);
  };

  const handleConfirmSubmission = async () => {
    if (!teamLeaderId || !absentEmployee?.workstation?.[0]?.id) {
      console.error("Missing teamLeaderId or workstation ID");
      return;
    }

    try {
      let replacementOperators: ReplacementOperator[] = [];

      if (selectedWorkers.length > 0) {
        // Create replacement operators for all selected workers with their individual types
        replacementOperators = selectedWorkers.map((worker) => ({
          id: worker.id,
          leaveOwnStation:
            (workerReplacementTypes[worker.id] || "move") === "move",
        }));
      }

      await submitReplacementRequest(
        absentEmployee.workstation[0].id, // workstationId
        teamLeaderId, // createdBy (team leader)
        replacementOperators,
      );

      console.log("Replacement request submitted successfully");
      setShowSummaryDialog(false); // Close summary dialog
      setShowSuccessDialog(true); // Show success dialog
    } catch (error) {
      console.error("Failed to submit replacement request:", error);
    }
  };

  const handleNoReplacementNeeded = async () => {
    // Clear selected workers for "No Replacement Needed" scenario
    setSelectedWorkers([]);
    setWorkerReplacementTypes({});
    // Show summary dialog for no replacement
    setSummaryActionType("no-replacement");
    setShowSummaryDialog(true);
  };

  const handleConfirmNoReplacement = async () => {
    if (!teamLeaderId || !absentEmployee?.workstation?.[0]?.id) {
      console.error("Missing teamLeaderId or workstation ID");
      return;
    }

    try {
      // Send empty replacement operators array for "No Replacement Needed"
      await submitReplacementRequest(
        absentEmployee.workstation[0].id, // workstationId
        teamLeaderId, // createdBy (team leader)
        [], // empty array for no replacement
      );

      console.log("No replacement request submitted successfully");
      setShowSummaryDialog(false); // Close summary dialog
      setShowSuccessDialog(true); // Show success dialog
    } catch (error) {
      console.error("Failed to submit no replacement request:", error);
    }
  };

  // Handler for the summary dialog confirmation
  const handleSummaryConfirm = () => {
    if (summaryActionType === "send-to-shift-leader") {
      handleConfirmSendToShiftLeader();
    } else if (summaryActionType === "no-replacement") {
      handleConfirmNoReplacement();
    } else {
      handleConfirmSubmission();
    }
  };

  // Handler for closing all dialogs and resetting state
  const handleCloseAll = () => {
    setShowSummaryDialog(false);
    setShowSuccessDialog(false);
    onClose();
  };

  return (
    <>
      <ReusableDialog
        isOpen={isOpen}
        onClose={onClose}
        title="Replacement"
        size="7xl"
      >
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Summary/Info Section */}
          <div className="grid grid-cols-4 mb-4 ">
            {/* Workstation */}
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-[#ADB8C8] rounded-full flex items-center justify-center mb-3">
                <div className="w-8 h-8 bg-[#5B7291] rounded-full flex items-center justify-center">
                  <CustomIcon
                    name="pin"
                    style={{ width: "50%", height: "50%", fill: "#fff" }}
                  />
                </div>
              </div>
              <div className="text-sm text-gray-500 mb-1">Workstation</div>
              <div className="font-bold text-gray-900 text-lg">
                {absentEmployee?.workstation?.find((w) => w.isOwner)
                  ?.workstation ||
                  absentEmployee?.workstation?.[0]?.workstation ||
                  "CELL 3"}
              </div>
            </div>

            {/* Workstation qualification */}
            <div className="flex flex-col items-center text-center border-l-[2px] border-gray-200">
              <div className="w-16 h-16 bg-[#ADB8C8] rounded-full flex items-center justify-center mb-3">
                <div className="w-8 h-8 bg-[#5B7291] rounded-full flex items-center justify-center">
                  <CustomIcon
                    name="diplomaIcon"
                    style={{ width: "50%", height: "50%" }}
                  />
                </div>
              </div>
              <div className="text-sm text-gray-500 mb-2">
                Workstation qualification
              </div>
              <div className="flex flex-wrap gap-1 justify-center">
                {absentEmployee?.skills && absentEmployee.skills.length > 0 ? (
                  absentEmployee.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium"
                    >
                      {skill.skillCode}
                    </span>
                  ))
                ) : (
                  // Fallback to static data if no skills available
                  <>
                    <span className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium">
                      ROB
                    </span>
                    <span className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium">
                      UCAB
                    </span>
                    <span className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium">
                      USW
                    </span>
                    <span className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium">
                      TRT
                    </span>
                    <span className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium">
                      FUS
                    </span>
                  </>
                )}
              </div>
            </div>

            {/* Criticity */}
            <div className="flex flex-col items-center text-center border-l-[2px] border-gray-200">
              <div className="w-16 h-16 bg-[#ADB8C8] rounded-full flex items-center justify-center mb-3">
                <div className="w-8 h-8 bg-[#5B7291] rounded-full flex items-center justify-center">
                  <CustomIcon
                    name="stairs"
                    style={{ width: "40%", height: "40%" }}
                  />
                </div>
              </div>
              <div className="text-sm text-gray-500 mb-2">Criticity</div>
              <div
                className={`rounded px-3 py-1 text-xs font-medium ${getCriticityColor(
                  absentEmployee?.workstation?.find((w) => w.isOwner)
                    ?.criticity ||
                    absentEmployee?.workstation?.[0]?.criticity ||
                    "N : Normal",
                )}`}
              >
                {absentEmployee?.workstation?.find((w) => w.isOwner)
                  ?.criticity ||
                  absentEmployee?.workstation?.[0]?.criticity ||
                  "N : Normal"}
              </div>
            </div>

            {/* Workers if selectedworkers is not empty  make the color #A5D7A7*/}
            <div className="flex flex-col items-center text-center border-l-[2px]  border-gray-200">
              <div
                className={
                  selectedWorkers.length > 0
                    ? "w-16 h-16 bg-[#A5D7A7] rounded-full flex items-center justify-center mb-3"
                    : "w-16 h-16 bg-yellow-100  rounded-full flex items-center justify-center mb-3"
                }
              >
                {selectedWorkers.length > 0 ? (
                  <div className="w-8 h-8 bg-[#4CAF50] rounded-full flex items-center justify-center">
                    <CustomIcon
                      name="circleUser"
                      className="w-8 h-8 text-gray-500"
                      style={{ width: "60%", height: "60%", fill: "#fff" }}
                    />
                  </div>
                ) : (
                  <HelpCircle className="w-8 h-8 text-yellow-600" />
                )}
              </div>
              <div className="text-sm text-gray-500 mb-2">
                Workers ({selectedWorkers.length})
              </div>
              {/* Show selected worker name and id */}

              {isMultiSelectEnabled() && selectedWorkers.length > 0 ? (
                <div className="text-sm text-gray-800 font-medium max-h-20 overflow-y-auto">
                  {selectedWorkers.map((worker) => (
                    <div key={worker.id} className="mb-1">
                      ID: ({worker.legacyId}) - {worker.firstName}{" "}
                      {worker.lastName}
                    </div>
                  ))}
                </div>
              ) : selectedWorkers.length === 1 && !isMultiSelectEnabled() ? (
                <div className="text-sm text-gray-800 font-medium">
                  ID: ({selectedWorkers[0].legacyId}) -{" "}
                  {selectedWorkers[0].firstName} {selectedWorkers[0].lastName}
                  {selectedWorkers[0].isMFG ? (
                    ""
                  ) : (
                    <div className="text-xs text-blue-600 mt-1">
                      Action:{" "}
                      {(workerReplacementTypes[selectedWorkers[0].id] ||
                        "move") === "move"
                        ? "Move"
                        : "Work both"}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-yellow-600 font-medium">
                  {isMultiSelectEnabled()
                    ? "Select multiple workers from this team"
                    : "Select the best worker to replace the absenteeisme"}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Parent Team Tabs */}
        <div className="flex justify-between mb-4 w-full">
          {/* Show teams from API response if available, otherwise show placeholder */}
          {replacementData?.teams && replacementData.teams.length > 0
            ? (() => {
                // Sort teams to put "Same Team" first - assuming it's the team that contains the absent employee
                const sortedTeams = [...replacementData.teams].sort((a, b) => {
                  // You can customize this logic based on how you identify the "same team"
                  // For now, we'll put the first team as "Same Team"
                  if (
                    a.teamName.toLowerCase().includes("same") ||
                    a === replacementData.teams[0]
                  )
                    return -1;
                  if (
                    b.teamName.toLowerCase().includes("same") ||
                    b === replacementData.teams[0]
                  )
                    return 1;
                  return 0;
                });

                return sortedTeams.map((team, index, array) => (
                  <button
                    key={team.teamId}
                    className={`flex-1 px-6 py-2 border-t-2 border-b-2 font-semibold transition-colors duration-150
        ${
          activeTeamTab === team.teamId
            ? "border-transparent text-[#E6EAEE] bg-[#5B7291]"
            : "text-[#3B4A6B] bg-[#ECF3FC] hover:bg-[#F7F8FA]"
        }
        ${index !== array.length - 1 ? "border-l-2" : "border-r-2"}`}
                    onClick={() => setActiveTeamTab(team.teamId)}
                  >
                    {index === 0 ? "Same Team" : team.teamName}
                  </button>
                ));
              })()
            : // Fallback placeholder tabs when no API data is available
              [
                { key: "same", label: "Same Team" },
                { key: "a", label: "Team A" },
                { key: "b", label: "Team B" },
                { key: "c", label: "Team C" },
              ].map((tab, index, array) => (
                <button
                  key={tab.key}
                  className={`flex-1 px-6 py-2 border-t-2 border-b-2 font-semibold transition-colors duration-150
        ${
          activeTeamTab === tab.key
            ? "border-transparent text-[#E6EAEE] bg-[#5B7291]"
            : "text-[#3B4A6B] bg-[#ECF3FC] hover:bg-[#F7F8FA]"
        }
        ${index !== array.length - 1 ? "border-l-2" : "border-r-2"}`}
                  onClick={() => setActiveTeamTab(tab.key)}
                  disabled={true}
                >
                  {tab.label}
                </button>
              ))}
        </div>
        {/* Structure Tabs (child) */}
        <div className="mb-2 flex items-center">
          <CustomTabs
            value={activeStructureTab}
            onValueChange={setActiveStructureTab}
          >
            <CustomTabsList>
              <CustomTabsTrigger value="mfg">MFG Structure</CustomTabsTrigger>
              <CustomTabsTrigger value="me-structure">
                ME - Structure
              </CustomTabsTrigger>
            </CustomTabsList>
          </CustomTabs>
        </div>

        <CustomTabs
          value={activeStructureTab}
          onValueChange={setActiveStructureTab}
        >
          <CustomTabsContent value="mfg" className="mt-4">
            <div className="mb-2 text-sm font-semibold text-[#3B4A6B]">
              Operators list ({replacementData ? mfgOperators.length : 0})
            </div>
            {isLoadingSuggestions ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500">
                  Loading replacement suggestions...
                </div>
              </div>
            ) : replacementData ? (
              <DataTable
                columns={mfgOperatorColumns}
                data={mfgOperators}
                showSearchBar={true}
                id="mfg-replacement-table"
              />
            ) : (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500">
                  No data available. Please provide employee, team leader, and
                  shift IDs.
                </div>
              </div>
            )}
          </CustomTabsContent>

          <CustomTabsContent value="me-structure" className="mt-4">
            <div className="mb-2 text-sm font-semibold text-[#3B4A6B]">
              Operators list (
              {replacementData ? meStructureOperators.length : 0})
            </div>
            {isLoadingSuggestions ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500">
                  Loading replacement suggestions...
                </div>
              </div>
            ) : replacementData ? (
              <DataTable
                columns={meOperatorColumns}
                data={meStructureOperators}
                showSearchBar={true}
                id="me-structure-replacement-table"
              />
            ) : (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500">
                  No data available. Please provide employee, team leader, and
                  shift IDs.
                </div>
              </div>
            )}
          </CustomTabsContent>
        </CustomTabs>

        {/* Action Buttons */}
        <div className="flex justify-between items-center gap-3 pt-4 border-t mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {/* Right side buttons */}
          <div className="flex gap-3 ml-auto">
            <Button
              className="bg-[#5B7291] text-white hover:bg-[#6B8BAA]"
              onClick={handleConfirmReplacement}
              disabled={isRaisingRequest}
            >
              {isRaisingRequest
                ? "Sending request..."
                : "Send request to Shift Leader"}
            </Button>
            <Button
              className="bg-[#B26C11] text-white hover:bg-[#D69E2E]"
              onClick={handleNoReplacementNeeded}
              disabled={isSubmittingReplacement}
            >
              {isSubmittingReplacement
                ? "Submitting..."
                : "No Replacement Needed"}
            </Button>
            <Button
              className={
                selectedWorkers.length > 0 && !isSubmittingReplacement
                  ? "bg-black text-white"
                  : "bg-[#E5E7EB] text-[#A1A1AA] cursor-not-allowed"
              }
              onClick={handleSubmit}
              disabled={selectedWorkers.length === 0 || isSubmittingReplacement}
            >
              {isSubmittingReplacement ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </div>
      </ReusableDialog>

      {/* Replacement Summary Dialog */}
      <ReplacementSummaryDialog
        isOpen={showSummaryDialog}
        onClose={() => setShowSummaryDialog(false)}
        onConfirm={handleSummaryConfirm}
        absentEmployee={absentEmployee}
        selectedWorkers={selectedWorkers}
        workerReplacementTypes={workerReplacementTypes}
        isSubmitting={isSubmittingReplacement || isRaisingRequest}
        actionType={summaryActionType}
      />

      {/* Replacement Success Dialog */}
      <ReplacementSuccessDialog
        isOpen={showSuccessDialog}
        onClose={handleCloseAll}
        actionType={summaryActionType}
      />
    </>
  );
}
