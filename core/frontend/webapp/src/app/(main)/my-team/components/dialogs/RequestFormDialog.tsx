"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { DatePicker } from "@/components/ui/datePicker";
import TimeSelector from "@/components/common/TimeSelector";
import CustomSelect from "@/components/common/CustomSelect";
import CustomInput from "@/components/common/CustomInput";
import CustomIcon from "@/components/common/CustomIcons";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import RequestConfirmDialog from "./RequestConfirmDialog";

interface RequestFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  userInfo?: {
    name: string;
    id: string;
    status: string;
  };
  requestType?: {
    id: string;
    title: string;
  };
  selectedOperators?: string[]; // New prop for bulk requests
  isBulkRequest?: boolean; // New prop to identify bulk requests
}

const reasonOptions = [
  { value: "sick_leave", label: "Sick Leave" },
  { value: "personal_leave", label: "Personal Leave" },
  { value: "vacation", label: "Vacation" },
  { value: "emergency", label: "Emergency" },
  { value: "medical_appointment", label: "Medical Appointment" },
  { value: "family_emergency", label: "Family Emergency" },
  { value: "other", label: "Other" },
];

export function RequestFormDialog({
  isOpen,
  onClose,
  onBack,
  userInfo,
  requestType,
  selectedOperators,
  isBulkRequest = false,
}: RequestFormDialogProps) {
  const [formData, setFormData] = useState({
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    startTime: "",
    endTime: "",
    reason: "",
    paperProof: "no",
    comment: "",
  });
  const [showConfirm, setShowConfirm] = useState(false);

  const handleSubmit = () => {
    setShowConfirm(true);
  };

  const handleConfirmBack = () => {
    setShowConfirm(false);
  };

  const handleConfirmSubmit = () => {
    // Finalize request submission here
    // You can add your API call or logic
    setShowConfirm(false);
    onClose();
  };

  const handleDateChange = (
    field: "startDate" | "endDate",
    date: Date | undefined,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: date,
    }));
  };

  const handleTimeChange = (field: "startTime" | "endTime", time: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: time,
    }));
  };

  const handleReasonChange = (reason: string) => {
    setFormData((prev) => ({
      ...prev,
      reason,
    }));
  };
  const handlePaperProofChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      paperProof: value,
    }));
  };

  const handleCommentChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setFormData((prev) => ({
      ...prev,
      comment: event.target.value,
    }));
  };

  const isFormValid =
    formData.startDate &&
    formData.endDate &&
    formData.startTime &&
    formData.endTime &&
    formData.reason;
  return (
    <>
      <ReusableDialog
        isOpen={isOpen && !showConfirm}
        onClose={onClose}
        title=""
        size="xl"
      >
        <div className="w-full max-w-lg mx-auto min-h-0 flex flex-col space-y-6 p-1">
          {/* User Info Section */}
          {userInfo && !isBulkRequest && (
            <div className="flex flex-col items-center mb-1 flex-shrink-0">
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-3">
                <CustomIcon
                  name="circleUser"
                  className="w-6 h-6 text-gray-500"
                  style={{ width: "80px", height: "80px", fill: "#D1D1D1" }}
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {userInfo.name}
              </h3>
              <p className="text-sm text-gray-500">ID : {userInfo.id}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-green-600">
                  {userInfo.status}
                </span>
              </div>
            </div>
          )}

          {/* Bulk Request Info Section */}
          {isBulkRequest &&
            selectedOperators &&
            selectedOperators.length > 0 && (
              <div className="flex flex-col items-center mb-1 flex-shrink-0">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                  <CustomIcon
                    name="user"
                    className="w-6 h-6 text-blue-600"
                    style={{ width: "40px", height: "40px", fill: "#3B82F6" }}
                  />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Bulk Request
                </h3>
                <p className="text-sm text-gray-500 mb-2">
                  {selectedOperators.length} operator(s) selected
                </p>
                <div className="max-w-xs text-center">
                  <p className="text-xs text-gray-600">
                    Selected operators will be included in this request
                  </p>
                </div>
              </div>
            )}

          {/* Icon Row with Title (Responsive) */}
          <div className="flex items-center justify-between w-full mb-2 mt-2 gap-2 flex-wrap sm:flex-nowrap">
            <div
              className="flex items-center justify-center rounded-full flex-shrink-0"
              style={{
                background: "#99D6EE",
                border: "4px solid #D2F3FF",
                width: 56,
                height: 56,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginLeft: "auto",
                marginRight: "auto",
              }}
            >
              <span className="flex items-center justify-center w-full h-full">
                <CustomIcon
                  name="calendarPlus"
                  className="w-7 h-7 text-[#2B7CA7]"
                  style={{
                    width: 28,
                    height: 28,
                    display: "block",
                    margin: "auto",
                  }}
                />
              </span>
            </div>
            <div className="flex-1 min-w-0 flex justify-center items-center">
              {requestType && (
                <div className="px-4 py-2 rounded-lg text-center w-full max-w-xs bg-white/80">
                  <span className="text-sm font-medium truncate block">
                    {requestType.title}
                  </span>
                </div>
              )}
            </div>
            <div
              className="flex items-center justify-center rounded-full flex-shrink-0"
              style={{
                background: "#99DCBD",
                border: "4px solid #DCF8EB",
                width: 56,
                height: 56,
              }}
            >
              {/* Use a clock icon from lucide-react as a placeholder */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-7 h-7 text-[#2B7C6A]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="9"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
                <path
                  d="M12 7v5l3 3"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>

          {/* Form Fields */}
          <div className="flex-1 space-y-4 overflow-y-auto p-1">
            {" "}
            {/* Date Fields */}
            <div className="grid grid-cols-2 gap-4">
              <DatePicker
                label="Start Date"
                defaultDate={formData.startDate}
                onChange={(date) => handleDateChange("startDate", date)}
                placeholder="Select start date"
              />
              <DatePicker
                label="End Date"
                defaultDate={formData.endDate}
                onChange={(date) => handleDateChange("endDate", date)}
                placeholder="Select end date"
                minDate={formData.startDate}
              />
            </div>
            {/* Time Fields */}
            <div className="grid grid-cols-2 gap-4">
              <TimeSelector
                label="Start time"
                value={formData.startTime}
                onChange={(time) => handleTimeChange("startTime", time)}
                placeholder="Select start time"
              />
              <TimeSelector
                label="End time"
                value={formData.endTime}
                onChange={(time) => handleTimeChange("endTime", time)}
                placeholder="Select end time"
              />
            </div>
            {/* Reason Selection */}
            <CustomSelect
              label="Reason for Absence"
              options={reasonOptions}
              onValueChange={handleReasonChange}
              placeholder="Select the reason for Absence"
              className="w-full"
            />
            {/* Paper Proof Radio Buttons */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Paper proof
              </Label>
              <RadioGroup
                value={formData.paperProof}
                onValueChange={handlePaperProofChange}
                className="flex space-x-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="yes" />
                  <Label htmlFor="yes" className="text-sm">
                    Yes
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="no" color="#010101" />
                  <Label htmlFor="no" className="text-sm">
                    No
                  </Label>
                </div>
              </RadioGroup>{" "}
            </div>
            {/* Comment Field */}
            <CustomInput
              label="Comment"
              value={formData.comment}
              onChange={handleCommentChange}
              placeholder="Add any additional comments..."
              className="w-full"
              multiline={true}
              rows={4}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between flex-shrink-0 mt-6">
            <Button variant="outline" onClick={onBack} className="px-6 py-2">
              Back
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!isFormValid}
              className="px-6 py-2 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Submit Request
            </Button>
          </div>
        </div>
      </ReusableDialog>
      {showConfirm && (
        <RequestConfirmDialog
          isOpen={showConfirm}
          onClose={() => {
            setShowConfirm(false);
            onClose();
          }}
          onBack={handleConfirmBack}
          onSubmit={handleConfirmSubmit}
          userInfo={userInfo}
          selectedOperators={selectedOperators}
          isBulkRequest={isBulkRequest}
          formData={formData}
        />
      )}
    </>
  );
}
