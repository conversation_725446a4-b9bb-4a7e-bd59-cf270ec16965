import React, { useMemo } from "react";
import { Loader2 } from "lucide-react";
import {
  PendingAssignment,
  useReplacementLogic,
} from "../../hooks/useReplacementLogic";
import OperatorCard from "./OperatorCard";
import RequestCard from "./RequestCard";
import HeaderSection from "./HeaderSection";
import BottomNotification from "./BottomNotification";

interface BaseOperator {
  operatorId: string;
  operatorLegacyId: number;
  operatorFirstName: string;
  operatorLastName: string;
  operatorQualifications: Array<{ skillCode: string; skillLevel?: string }>;
  isAvailable: boolean;
  isValidChoice: boolean;
  operatorTeamName: string | null;
  shiftLeader?: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
  } | null;
  projectHierarchy: {
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
  } | null;
  workstationChosenFor?: {
    id: string;
    name: string;
    requiredSkills: string[];
  } | null;
}

interface BaseRequest {
  requestId: string;
  workstation: {
    id: string;
    name: string;
    requiredSkills: string[];
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
    criticity: "NORMAL" | "MEDIUM" | "HIGH" | "CRITICAL";
  };
  teamLeader?: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
  } | null;
  isCompleted: boolean;
  replacementOperator?: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
    skills?: string[];
  } | null;
}

export interface ReplacementInterfaceConfig {
  department: string;
  operatorsTitle: string;
  requestsTitle: string;
  operators: BaseOperator[];
  requests: BaseRequest[];
  isLoading: boolean;
  errors: {
    operatorError?: string;
    requestError?: string;
  };
  onClearErrors: {
    clearOperatorError?: () => void;
    clearRequestError?: () => void;
  };
  onFetchOperators: (skills: string[]) => void;
  onConfirmAssignments: (assignments: PendingAssignment[]) => Promise<void>;
  onProcessOperators?: (operatorIds: string[]) => Promise<void>;
  onEscalateRequests?: (requestIds: string[]) => Promise<void>;
  operatorActionLabel?: string;
  requestActionLabel?: string;
}

interface ReplacementInterfaceProps {
  config: ReplacementInterfaceConfig;
}

export default function ReplacementInterface({
  config,
}: ReplacementInterfaceProps) {
  const {
    department,
    operatorsTitle,
    requestsTitle,
    operators,
    requests,
    isLoading,
    onConfirmAssignments,
    onProcessOperators,
    onEscalateRequests,
    operatorActionLabel = "PROCESS OPERATORS",
    requestActionLabel = "ESCALATE REQUESTS",
  } = config;

  const {
    selectedWorkstation,
    setSelectedWorkstation,
    selectedOperators,
    setSelectedOperators,
    checkedOperators,
    setCheckedOperators,
    checkedRequests,
    setCheckedRequests,
    setConfirmedMatches,
    pendingAssignments,
    confirmedMatches,
    currentSelectionLabel,
    getPendingAssignmentForRequest,
    getPendingAssignmentForOperator,
    handleSelectOperator,
    handleCheckOperator,
    handleCheckRequest,
    isWorkstationHighlighted,
    isWorkstationMatched,
    isWorkstationInPendingAssignments,
    isOperatorMatched,
    clearAllSelections,
    removePendingAssignment,
  } = useReplacementLogic();

  // Compute enriched requests with local assignment data
  const enrichedRequests = useMemo(() => {
    return requests.map((request) => {
      // Check if this request has a confirmed match
      const match = confirmedMatches.find(
        (m) => m.workstationId === request.requestId,
      );
      if (match && !request.replacementOperator) {
        // Find the operator details
        const assignedOperator = operators.find(
          (op) => op.operatorId === match.operatorId,
        );
        if (assignedOperator) {
          // Create enriched request with operator info
          return {
            ...request,
            isCompleted: true,
            replacementOperator: {
              id: assignedOperator.operatorId,
              legacyId: assignedOperator.operatorLegacyId,
              firstName: assignedOperator.operatorFirstName,
              lastName: assignedOperator.operatorLastName,
              skills: assignedOperator.operatorQualifications.map(
                (q) => q.skillCode,
              ),
            },
          };
        }
      }
      return request;
    });
  }, [requests, confirmedMatches, operators]);

  // Compute enriched operators with local assignment data
  const enrichedOperators = useMemo(() => {
    return operators.map((operator) => {
      // Check if this operator has a confirmed match
      const match = confirmedMatches.find(
        (m) => m.operatorId === operator.operatorId,
      );
      if (match && (!operator.workstationChosenFor || operator.isAvailable)) {
        // Find the request details
        const assignedRequest = enrichedRequests.find(
          (req) => req.requestId === match.workstationId,
        );
        if (assignedRequest) {
          // Create enriched operator with workstation info
          return {
            ...operator,
            isAvailable: false, // Operator is no longer available
            workstationChosenFor: {
              id: assignedRequest.workstation.id,
              name: assignedRequest.workstation.name,
              requiredSkills: assignedRequest.workstation.requiredSkills,
            },
          };
        }
      }
      return operator;
    });
  }, [operators, confirmedMatches, enrichedRequests]);

  // Client-side function to check if operator matches request skills
  const operatorMatchesRequestSkills = (
    operator: BaseOperator,
    requiredSkills: string[],
  ) => {
    if (!requiredSkills || requiredSkills.length === 0) {
      console.log("No required skills, returning true");
      return true;
    }

    const operatorSkills = operator.operatorQualifications.map(
      (q) => q.skillCode,
    );
    const matches = requiredSkills.every((skill) =>
      operatorSkills.includes(skill),
    );

    console.log("Required skills:", requiredSkills);
    console.log("Operator skills:", operatorSkills);
    console.log("Matches all required skills:", matches);

    return matches;
  };

  const handleSelectToMatch = (workstationId: string) => {
    const existingAssignment = getPendingAssignmentForRequest(workstationId);

    if (existingAssignment) {
      // If there's already an assignment for this request, remove it (deselect)
      removePendingAssignment(workstationId);

      // Clear selections
      setSelectedWorkstation(null);
      setSelectedOperators([]);
    } else {
      // No existing assignment, so this is a new selection
      setSelectedWorkstation(workstationId);
      setSelectedOperators([]);
    }
    if (selectedWorkstation) {
      console.log(selectedWorkstation);
      setSelectedWorkstation(null);
    }
  };

  const handleConfirmChoice = async () => {
    if (pendingAssignments.length > 0) {
      try {
        await onConfirmAssignments(pendingAssignments);

        const newMatches = pendingAssignments.flatMap((assignment) =>
          assignment.operatorIds.map((operatorId) => ({
            workstationId: assignment.requestId,
            operatorId: operatorId,
          })),
        );
        setConfirmedMatches((prev) => [...prev, ...newMatches]);

        // Update the requests with the assigned operator information
        // This should be handled by the parent component through data refetching
        // But for immediate UI update, we can trigger a local state update

        clearAllSelections();
      } catch (error) {
        console.error("Failed to assign operators:", error);
      }
    }
  };

  const handleCheckAllOperators = () => {
    // Only select operators that are available
    const availableOperatorIds = enrichedOperators
      .filter((op) => op.isAvailable)
      .map((op) => op.operatorId);

    if (checkedOperators.length === availableOperatorIds.length) {
      setCheckedOperators([]);
    } else {
      setCheckedOperators(availableOperatorIds);
    }
  };

  const handleCheckAllRequests = () => {
    // Only select requests that are not completed
    const incompleteRequestIds = enrichedRequests
      .filter((req) => !req.isCompleted)
      .map((req) => req.requestId);

    if (checkedRequests.length === incompleteRequestIds.length) {
      setCheckedRequests([]);
    } else {
      setCheckedRequests(incompleteRequestIds);
    }
  };

  const getOperatorHighlightStatus = (operator: BaseOperator) => {
    // Check if operator is in pending assignments
    const pendingAssignment = getPendingAssignmentForOperator(
      operator.operatorId,
    );
    if (pendingAssignment) return "pending";

    // Check if operator is currently selected
    if (selectedOperators.includes(operator.operatorId)) return "selected";

    // If a workstation is selected, check for skill matching
    if (selectedWorkstation) {
      const currentRequest = enrichedRequests.find(
        (request) => request.requestId === selectedWorkstation,
      );

      if (currentRequest) {
        console.log(
          "Current request:",
          currentRequest.workstation.name,
          "Required skills:",
          currentRequest.workstation.requiredSkills,
        );
        console.log(
          "Operator:",
          operator.operatorFirstName,
          operator.operatorLastName,
          "Skills:",
          operator.operatorQualifications.map((q) => q.skillCode),
        );
        console.log(
          "isAvailable:",
          operator.isAvailable,
          "isValidChoice:",
          operator.isValidChoice,
        );

        // Check if operator skills match the required skills
        const hasMatchingSkills = operatorMatchesRequestSkills(
          operator,
          currentRequest.workstation.requiredSkills,
        );

        console.log("Has matching skills:", hasMatchingSkills);

        if (operator.isAvailable && hasMatchingSkills) {
          return "highlighted";
        }
        if (isOperatorMatched(operator.operatorId)) {
          return "unavailable";
        }
      }
      return "unavailable";
    }
    if (isOperatorMatched(operator.operatorId)) return "matched";

    return "normal";
  };

  // Computed values
  const isAllOperatorsChecked = useMemo(() => {
    // Only consider available operators for "select all" logic
    const availableOperatorIds = enrichedOperators
      .filter((op) => op.isAvailable)
      .map((op) => op.operatorId);
    return (
      availableOperatorIds.length > 0 &&
      checkedOperators.length === availableOperatorIds.length
    );
  }, [enrichedOperators, checkedOperators]);

  const isSomeOperatorsChecked = useMemo(() => {
    const availableOperatorIds = enrichedOperators
      .filter((op) => op.isAvailable)
      .map((op) => op.operatorId);
    return (
      checkedOperators.length > 0 &&
      checkedOperators.length < availableOperatorIds.length
    );
  }, [enrichedOperators, checkedOperators]);

  const isAllRequestsChecked = useMemo(() => {
    // Only consider incomplete requests for "select all" logic
    const incompleteRequestIds = enrichedRequests
      .filter((req) => !req.isCompleted)
      .map((req) => req.requestId);
    return (
      incompleteRequestIds.length > 0 &&
      checkedRequests.length === incompleteRequestIds.length
    );
  }, [enrichedRequests, checkedRequests]);

  const isSomeRequestsChecked = useMemo(() => {
    const incompleteRequestIds = enrichedRequests
      .filter((req) => !req.isCompleted)
      .map((req) => req.requestId);
    return (
      checkedRequests.length > 0 &&
      checkedRequests.length < incompleteRequestIds.length
    );
  }, [enrichedRequests, checkedRequests]);

  //   const isAllRequestsChecked = useMemo(() => {
  //     const availableRequestIds = requests
  //       .filter((request) => !request.isCompleted && !isWorkstationMatched(request.requestId))
  //       .map((request) => request.requestId);

  //     return availableRequestIds.length > 0 && checkedRequests.length === availableRequestIds.length;
  //   }, [requests, checkedRequests, isWorkstationMatched]);

  //   const isSomeRequestsChecked = useMemo(() => {
  //     const availableRequestIds = requests
  //       .filter((request) => !request.isCompleted && !isWorkstationMatched(request.requestId))
  //       .map((request) => request.requestId);

  //     return checkedRequests.length > 0 && checkedRequests.length < availableRequestIds.length;
  //   }, [requests, checkedRequests, isWorkstationMatched]);

  const getMatchedOperatorsCount = () => {
    return (
      enrichedOperators.length -
      enrichedOperators.filter((op) => op.isAvailable).length
    );
  };
  const getAvailableOperatorsCount = () =>
    enrichedOperators.filter((op) => op.isAvailable).length;
  const getMatchedRequestsCount = () => {
    // check the request.isCompleted count
    return enrichedRequests.filter((request) => request.isCompleted).length;
  };
  const getNotMatchedRequestsCount = () => {
    // get non matched
    return enrichedRequests.length - getMatchedRequestsCount();
  };

  return (
    <div className="w-full">
      <div className="grid grid-cols-2 gap-6">
        {/* Left Side - Operators */}
        <div className="space-y-4 border-r-4 border-[#D9E7F8]">
          <HeaderSection
            title={operatorsTitle}
            totalCount={enrichedOperators.length}
            matchedCount={getMatchedOperatorsCount()}
            availableCount={getAvailableOperatorsCount()}
            matchedLabel="Used Backup"
            availableLabel="Available Backup"
            showCheckbox
            isAllChecked={isAllOperatorsChecked}
            isSomeChecked={isSomeOperatorsChecked}
            onCheckAll={handleCheckAllOperators}
            department={department}
          />

          <div
            className="space-y-3 pr-2 h-full overflow-y-auto max-h-[70vh]"
            style={{ scrollbarWidth: "none" }}
          >
            {enrichedOperators.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                No operators available
              </div>
            ) : (
              enrichedOperators.map((operator) => (
                <OperatorCard
                  key={operator.operatorId}
                  operator={operator}
                  highlightStatus={getOperatorHighlightStatus(operator)}
                  isChecked={checkedOperators.includes(operator.operatorId)}
                  selectedOperators={selectedOperators}
                  currentSelectionLabel={currentSelectionLabel}
                  department={department}
                  onSelectOperator={handleSelectOperator}
                  onCheckOperator={handleCheckOperator}
                  getPendingAssignmentForOperator={
                    getPendingAssignmentForOperator
                  }
                  isOperatorMatched={isOperatorMatched}
                />
              ))
            )}
          </div>
        </div>

        {/* Right Side - Workstation Requests */}
        <div className="space-y-4 border-l-4 border-[#D9E7F8]">
          <HeaderSection
            title={requestsTitle}
            totalCount={enrichedRequests.length}
            matchedCount={getMatchedRequestsCount()}
            availableCount={getNotMatchedRequestsCount()}
            matchedLabel="Matched"
            availableLabel="Not Matched"
            department={department}
            showCheckbox={department !== "departement-backup-basket"}
            onCheckAll={handleCheckAllRequests}
            isAllChecked={isAllRequestsChecked}
            isSomeChecked={isSomeRequestsChecked}
          />

          <div
            className="space-y-3 pl-2 h-full overflow-y-auto max-h-[70vh]"
            style={{ scrollbarWidth: "none" }}
          >
            {isLoading ? (
              <div className="p-8 text-center text-gray-500">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                <div>Loading requests...</div>
              </div>
            ) : enrichedRequests.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                No requests available
              </div>
            ) : (
              enrichedRequests.map((request) => (
                <RequestCard
                  key={request.requestId}
                  request={request}
                  isHighlighted={isWorkstationHighlighted(request.requestId)}
                  isInPendingAssignments={isWorkstationInPendingAssignments(
                    request.requestId,
                  )}
                  isMatched={isWorkstationMatched(request.requestId)}
                  isChecked={checkedRequests.includes(request.requestId)}
                  selectedWorkstation={selectedWorkstation}
                  currentSelectionLabel={currentSelectionLabel}
                  onSelectToMatch={handleSelectToMatch}
                  onCheckRequest={handleCheckRequest}
                  getPendingAssignmentForRequest={
                    getPendingAssignmentForRequest
                  }
                  department={department}
                />
              ))
            )}
          </div>
        </div>
      </div>
      <div className="absolute bottom-0 left-0 right-0 z-10">
        {/* Bottom Notifications */}
        {selectedWorkstation != null && pendingAssignments.length === 0 && (
          <BottomNotification
            type="workstationSelection"
            label={
              "Select an operator from the left list to match him with the selected request, and confirm your choice, you can select multiple choices before confirme !"
            }
            onAction={handleConfirmChoice}
            actionLabel="CONFIRM ALL ASSIGNMENTS"
            actionColor="blue"
          />
        )}
        {pendingAssignments.length > 0 && (
          <BottomNotification
            type="pending"
            label="Select an operator from the left list to match him with the selected request, and confirm your choice, you can select multiple choices before confirme !"
            onAction={handleConfirmChoice}
            actionLabel="CONFIRM MY CHOICE"
            actionColor="blue"
          />
        )}

        {checkedOperators.length > 0 && onProcessOperators && (
          <BottomNotification
            type="checked-operators"
            label="Send selected operators to Departement Backup Basket ?"
            onAction={async () => {
              try {
                await onProcessOperators(checkedOperators);
                // Clear checked operators after successful processing
                setCheckedOperators([]);
              } catch (error) {
                console.error("Failed to process operators:", error);

                // Show user-friendly error message
                let errorMessage =
                  "Failed to process operators. Please try again.";
                if (error instanceof Error) {
                  if (
                    error.message.includes("503") ||
                    error.message.includes("Service Temporarily Unavailable")
                  ) {
                    errorMessage =
                      "Service is temporarily unavailable. Please try again later.";
                  } else {
                    errorMessage = error.message;
                  }
                }

                // You might want to show this error in a toast or alert
                alert(`Error: ${errorMessage}`);

                // Don't clear the selection on failure so user can retry manually
              }
            }}
            actionLabel={operatorActionLabel}
            actionColor="green"
          />
        )}

        {checkedRequests.length > 0 && onEscalateRequests && (
          <BottomNotification
            type="checked-requests"
            label="Escalate selected requests to the next level ?"
            onAction={async () => {
              try {
                if (onEscalateRequests) {
                  await onEscalateRequests(checkedRequests);
                  // Clear checked requests after successful escalation
                  setCheckedRequests([]);
                }
              } catch (error) {
                console.error("Failed to escalate requests:", error);

                // Show user-friendly error message
                let errorMessage =
                  "Failed to escalate requests. Please try again.";
                if (error instanceof Error) {
                  if (
                    error.message.includes("503") ||
                    error.message.includes("Service Temporarily Unavailable")
                  ) {
                    errorMessage =
                      "Service is temporarily unavailable. Please try again later.";
                  } else if (error.message.includes("not yet implemented")) {
                    errorMessage = error.message;
                  } else {
                    errorMessage = error.message;
                  }
                }

                // You might want to show this error in a toast or alert
                alert(`Error: ${errorMessage}`);

                // Don't clear the selection on failure so user can retry manually
              }
            }}
            actionLabel={requestActionLabel}
            actionColor="red"
          />
        )}
      </div>
    </div>
  );
}
