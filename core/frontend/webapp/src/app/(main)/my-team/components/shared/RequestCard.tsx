import React from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import CustomIcon from "@/components/common/CustomIcons";

interface BaseRequest {
  requestId: string;
  workstation: {
    id: string;
    name: string;
    requiredSkills: string[];
    customer: string;
    project: string;
    family: string;
    valueStream: string;
    area: string;
    criticity: "NORMAL" | "MEDIUM" | "HIGH" | "CRITICAL";
  };
  teamLeader?: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
  } | null;
  isCompleted: boolean;
  replacementOperator?: {
    id: string;
    legacyId: number;
    firstName: string;
    lastName: string;
    skills?: string[];
  } | null;
}

interface RequestCardProps {
  request: BaseRequest;
  isHighlighted: boolean;
  isInPendingAssignments: boolean;
  isMatched: boolean;
  isChecked: boolean;
  selectedWorkstation: string | null;
  currentSelectionLabel: string;
  pendingAssignmentLabel?: string;
  onSelectToMatch: (requestId: string) => void;
  onCheckRequest: (requestId: string) => void;
  getPendingAssignmentForRequest: (
    requestId: string,
  ) => { label: string } | undefined;
  department?: string;
}

export default function RequestCard({
  request,
  isHighlighted,
  isInPendingAssignments,
  isMatched,
  isChecked,
  selectedWorkstation,
  currentSelectionLabel,
  onSelectToMatch,
  onCheckRequest,
  getPendingAssignmentForRequest,
  department,
}: RequestCardProps) {
  const getCardClassName = () => {
    const baseClasses = "p-4 transition-all duration-200 rounded-3xl border";

    if (isHighlighted) {
      return `${baseClasses} border-gray-200 border-2 bg-[#F5FBFF]`;
    }
    if (isInPendingAssignments) {
      return `${baseClasses} border-orange-500 bg-orange-50`;
    }
    if (selectedWorkstation && selectedWorkstation !== request.requestId) {
      return `${baseClasses} border-gray-100 bg-gray-50 opacity-50`;
    }
    return `${baseClasses} border-gray-200 border-2 bg-[#F5FBFF]`;
  };

  const renderStatusIcon = () => {
    if (request.isCompleted) {
      return (
        <div className="w-10 h-10 bg-[#A0D5A7] rounded-full flex items-center justify-center mb-3">
          <div className="w-5 h-5 bg-[#4CAF50] rounded-full flex items-center justify-center">
            <CustomIcon
              name="pin"
              style={{
                width: "50%",
                height: "50%",
                fill: "#A0D5A7",
              }}
            />
          </div>
        </div>
      );
    }

    return (
      <div className="w-10 h-10 bg-[#F69D8B] rounded-full flex items-center justify-center mb-3">
        <div className="w-5 h-5 bg-[#F84018] rounded-full flex items-center justify-center">
          <CustomIcon
            name="pin"
            style={{
              width: "50%",
              height: "50%",
              fill: "#F69D8B",
            }}
          />
        </div>
      </div>
    );
  };

  const renderButton = () => {
    if (!isMatched && !request.isCompleted) {
      return (
        <div className="flex items-center space-x-2">
          <Button
            className={
              !isHighlighted && !isInPendingAssignments
                ? "border-dashed border-2 border-gray-300 rounded-xl"
                : "bg-[#5B7291] text-white rounded-xl hover:bg-[#4A5B7A]"
            }
            variant={
              isHighlighted || isInPendingAssignments ? "default" : "outline"
            }
            size="lg"
            onClick={() => onSelectToMatch(request.requestId)}
            disabled={Boolean(
              selectedWorkstation && selectedWorkstation !== request.requestId,
            )}
          >
            {isMatched
              ? "MATCHED"
              : isHighlighted
                ? `SELECTED (${currentSelectionLabel})`
                : isInPendingAssignments
                  ? `ASSIGNED (${getPendingAssignmentForRequest(request.requestId)?.label})`
                  : "Select to match"}
          </Button>
        </div>
      );
    }

    // Matched request display
    return (
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-[#F5FBFF] rounded-full flex items-center justify-center">
          <CustomIcon
            name="circleUser"
            className="w-4 h-4"
            style={{
              fill: "#4CAF50",
            }}
          />
        </div>
        <div className="min-w-0">
          <div className="flex items-baseline space-x-1">
            <span className="font-semibold text-[#4CAF50] truncate">
              {request.replacementOperator?.legacyId}
            </span>
            <span className="text-gray-400">-</span>
            <span className="font-medium text-[#4CAF50] truncate">
              {`${request.replacementOperator?.firstName} ${request.replacementOperator?.lastName}`}
            </span>
          </div>
          <div className="text-gray-600">
            <span className="font-medium">Qualifs:</span>{" "}
            {request.replacementOperator?.skills
              ?.slice(0, 3)
              .map((q) => q)
              .join(", ")}
            {(request.replacementOperator?.skills?.length ?? 0) > 3 && (
              // wrap it in a grey rounded container
              <div className="inline-block bg-[#ACB4BC] text-center rounded-full ml-1 px-2 py-1">
                <span className="ml-1 text-xs text-white">
                  +{(request.replacementOperator?.skills?.length ?? 0) - 3}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const detailItems = [
    {
      label: `By TL :${request?.teamLeader?.legacyId.toString() || "N/A"}`,
      value:
        `${request?.teamLeader?.firstName} ${request?.teamLeader?.lastName}` ||
        "N/A",
    },
    {
      label: "Customer",
      value: String(request.workstation.customer || "N/A"),
    },
    {
      label: "Project",
      value: String(request.workstation.project || "N/A"),
    },
    {
      label: "Family",
      value: String(request.workstation.family || "N/A"),
    },
    {
      label: "Value Stream",
      value: String(request.workstation.valueStream || "N/A"),
    },
    {
      label: "Area",
      value: String(request.workstation.area || "N/A"),
    },
  ];

  return (
    <Card className={getCardClassName()}>
      {/* Block 1: icon / name / up to 3 qualifs / TL + button */}
      <div className="flex items-start justify-between mb-2 border-b-2 pb-2">
        <div className="flex items-center space-x-3">
          {/* status icon */}
          {renderStatusIcon()}
          {/* text block */}
          <div className="flex-1 min-w-0 border-l-2 pl-4">
            <div className="flex items-center space-x-2">
              <span
                className={
                  request.isCompleted
                    ? "font-semibold text-[#4CAF50]"
                    : "font-semibold text-gray-900"
                }
              >
                Workstation:
              </span>
              <span
                className={
                  request.isCompleted
                    ? "font-medium text-[#4CAF50] text-lg"
                    : "font-medium text-gray-900 text-lg"
                }
              >
                {request.workstation.name}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">Qualifs:</span>{" "}
              {request.workstation.requiredSkills.slice(0, 3).join(", ")}
            </div>
          </div>
        </div>
        {request.isCompleted && (
          <div className="border-r-2 border-gray-200 h-12"></div>
        )}
        {/* select/match button */}
        <div className="flex-shrink-0 ml-4">{renderButton()}</div>
      </div>

      {/* Block 2: checkbox (if not matched) + details */}
      <div className="flex items-start">
        {!isMatched &&
        !request.isCompleted &&
        department !== "departement-backup-basket" ? (
          <div className="flex-shrink-0 mr-4 border-r-2 pr-9">
            <input
              type="checkbox"
              className="rounded"
              checked={isHighlighted || isChecked}
              onChange={() => {
                if (request.isCompleted) return;

                // If currently selected for workstation matching, handle that
                if (selectedWorkstation === request.requestId) {
                  onSelectToMatch(request.requestId);
                } else {
                  // Handle escalation selection
                  onCheckRequest(request.requestId);
                }
              }}
            />
          </div>
        ) : (
          <div className="w-7 h-7 bg-[#F5FBFF] rounded-full flex items-center justify-center mr-5">
            <CustomIcon
              name="circleUser"
              className="w-4 h-4"
              style={{
                fill: "#5B7291",
              }}
            />
          </div>
        )}
        <div className="flex-1">
          <div className="flex-1 grid grid-cols-6 md:grid-cols-4 lg:grid-cols-6 gap-4 text-xs text-gray-500 border-l-2">
            {detailItems.map((item, idx) => (
              <div key={idx} className="space-y-1 text-center">
                <div className="text-blue-500 font-medium">{item.label}</div>
                <div>{item.value}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}
