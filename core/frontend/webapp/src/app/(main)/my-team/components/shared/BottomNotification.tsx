import React from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface BottomNotificationProps {
  type:
    | "selection"
    | "pending"
    | "checked-operators"
    | "checked-requests"
    | "workstationSelection";
  count?: number;
  label?: string;
  onAction: () => void;
  actionLabel: string;
  actionColor?: "yellow" | "blue" | "green" | "red";
  disabled?: boolean;
  isLoading?: boolean;
}

export default function BottomNotification({
  type,
  count,
  label,
  onAction,
  actionLabel,
  disabled = false,
  isLoading = false,
}: BottomNotificationProps) {
  const getNotificationConfig = () => {
    switch (type) {
      case "workstationSelection":
        return {
          bgColor: "bg-[#FCF6DF]",
          borderColor: "border-gray-200",
          iconBg: "bg-gray-500",
          textColor: "text-gray-800",
          icon: "!",
        };
      case "selection":
        return {
          bgColor: "bg-[#FCF6DF]",
          borderColor: "border-gray-200",
          iconBg: "bg-gray-500",
          textColor: "text-gray-800",
          icon: "!",
        };
      case "pending":
        return {
          bgColor: "bg-[#FCF6DF]",
          borderColor: "border-gray-200",
          iconBg: "bg-gray-500",
          textColor: "text-gray-800",
          icon: "!",
        };
      case "checked-operators":
        return {
          bgColor: "bg-[#FCF6DF]",
          borderColor: "border-gray-200",
          iconBg: "bg-gray-500",
          textColor: "text-gray-800",
          icon: "!",
        };
      case "checked-requests":
        return {
          bgColor: "bg-[#FCF6DF]",
          borderColor: "border-gray-200",
          iconBg: "bg-gray-500",
          textColor: "text-gray-800",
          icon: "!",
        };
      default:
        return {
          bgColor: "bg-[#FCF6DF]",
          borderColor: "border-gray-200",
          iconBg: "bg-gray-500",
          textColor: "text-gray-800",
          icon: "!",
        };
    }
  };

  const config = getNotificationConfig();

  if (count === 0 && type !== "selection") {
    return null;
  }

  return (
    <div
      className={`mt-6 ${config.bgColor} border ${config.borderColor} rounded-b-xl p-4`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div
            className={`w-5 h-5 ${config.iconBg} rounded-full flex items-center justify-center`}
          >
            <span className="text-white text-xs font-bold">{config.icon}</span>
          </div>
          <span className={`text-sm ${config.textColor}`}>{label}</span>
        </div>
        {type != "workstationSelection" && (
          <Button
            onClick={onAction}
            disabled={disabled || isLoading}
            className={` "bg-gray-800 hover:bg-gray-900 text-white flex items-center space-x-2`}
            size={"lg"}
          >
            {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            <span>{actionLabel}</span>
          </Button>
        )}
      </div>
    </div>
  );
}
