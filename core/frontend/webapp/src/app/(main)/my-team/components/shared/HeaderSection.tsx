import React from "react";

interface HeaderSectionProps {
  title: string;
  totalCount: number;
  matchedCount: number;
  availableCount: number;
  matchedLabel?: string;
  availableLabel?: string;
  showCheckbox?: boolean;
  isAllChecked?: boolean;
  isSomeChecked?: boolean;
  onCheckAll?: () => void;
  department?: string;
}

export default function HeaderSection({
  title,
  totalCount,
  matchedCount,
  availableCount,
  matchedLabel = "Matched",
  availableLabel = "All",
  showCheckbox = false,
  isAllChecked = false,
  isSomeChecked = false,
  onCheckAll,
  department,
}: HeaderSectionProps) {
  return (
    <div className="bg-[#D9E7F8] border border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-6 text-sm text-gray-600">
          {showCheckbox &&
          onCheckAll &&
          department !== "departement-backup-basket" ? (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                className="rounded"
                checked={isAllChecked}
                ref={(el) => {
                  if (el) el.indeterminate = isSomeChecked;
                }}
                onChange={onCheckAll}
              />
              <span>
                <span className="font-medium text-gray-900">
                  {title} ({totalCount})
                </span>
              </span>
            </div>
          ) : (
            <span className="font-medium text-gray-900">
              {title} ({totalCount})
            </span>
          )}
          <span>
            <span className="font-medium text-green-600">
              {matchedLabel} ({matchedCount})
            </span>
          </span>
          <span>
            <span className="font-medium text-blue-600">
              {availableLabel} ({availableCount})
            </span>
          </span>
        </div>
      </div>
    </div>
  );
}
