import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ReusableDialog } from "@/components/common/CustomDialog";
import CustomIcon from "@/components/common/CustomIcons";
import useReplacementStore from "../../store/replacementStore";
import type {
  ReplacementHistoryItem,
  EvaluationData,
} from "../../store/replacementStore";

type FeedbackRating = "LOW" | "MEDIUM" | "HIGH" | null;

// Flattened structure for easier iteration
interface FlattenedHistoryItem {
  id: string;
  replacementHistoryId: string;
  replacedWorker: {
    id: string;
    firstName: string;
    lastName: string;
    function: string;
    line: string;
    team: string;
  };
  replacementWorker: {
    id: string;
    firstName: string;
    lastName: string;
    function: string;
    line: string;
    team: string;
  };
  date: string;
  shift: string;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

export const ReplacementFeedbackDialog: React.FC<Props> = ({
  isOpen,
  onClose,
}) => {
  const {
    replacementHistory,
    isLoadingHistory,
    historyError,
    fetchReplacementHistory,
    evaluateReplacement,
    isEvaluatingReplacement,
    evaluationError,
    clearHistoryError,
    clearEvaluationError,
    resetReplacement,
    isResettingReplacement,
    resetReplacementError,
    clearResetReplacementError,
  } = useReplacementStore();

  const [stepIndex, setStepIndex] = useState(0);
  const [selectedRating, setSelectedRating] = useState<FeedbackRating>(null);
  const [flattenedData, setFlattenedData] = useState<FlattenedHistoryItem[]>(
    [],
  );

  // Load history data when dialog opens
  useEffect(() => {
    if (isOpen) {
      // I don't want to give teamLeaderId, just isEvaluated and shiftId
      // This will fetch only unevaluated replacements
      fetchReplacementHistory(
        false, // isEvaluated
        undefined, // createdBy - skip this parameter
        "b4bedff2-165e-4156-969f-d3b3cd025970", // shiftId
      ); // Only fetch unevaluated replacements
      clearHistoryError();
      clearEvaluationError();
      clearResetReplacementError();
    }
  }, [
    isOpen,
    fetchReplacementHistory,
    clearHistoryError,
    clearEvaluationError,
    clearResetReplacementError,
  ]);

  // Process history data into flattened format
  useEffect(() => {
    if (replacementHistory && replacementHistory.length > 0) {
      const flattened = replacementHistory.flatMap(
        (entry: ReplacementHistoryItem) =>
          entry.replacementOperator.map((rw) => ({
            id: `${entry.id}-${rw.id}`,
            replacementHistoryId: entry.id,
            replacedWorker: {
              id: entry.absentOperator.legacyId.toString(),
              firstName: entry.absentOperator.firstName,
              lastName: entry.absentOperator.lastName,
              function: entry.targetWorkstation.name || "N/A",
              line: entry.targetWorkstation.area || "N/A",
              team: entry.absentTeamName,
            },
            replacementWorker: {
              id: rw.legacyId.toString(),
              firstName: rw.firstName,
              lastName: rw.lastName,
              function: entry.targetWorkstation.name || "N/A",
              line: "N/A",
              team: "N/A",
            },
            date: new Date(entry.shiftId).toLocaleDateString(),
            shift: `${new Date(entry.shiftStartDate).toLocaleTimeString()} - ${new Date(entry.shiftEndDate).toLocaleTimeString()}`,
          })),
      );
      setFlattenedData(flattened);
      setStepIndex(0);
      setSelectedRating(null);
    }
  }, [replacementHistory]);

  const totalSteps = flattenedData.length;
  const current = flattenedData[stepIndex];

  const handleSubmit = async () => {
    if (!selectedRating || !current) return;

    try {
      const evaluation: EvaluationData = {
        score: selectedRating,
        comment: `User feedback: ${selectedRating}`,
      };

      await evaluateReplacement(current.replacementHistoryId, evaluation);

      console.log("Submitted:", {
        replaced: current.replacedWorker.id,
        replacement: current.replacementWorker.id,
        rating: selectedRating,
      });

      if (stepIndex + 1 < totalSteps) {
        setStepIndex(stepIndex + 1);
        setSelectedRating(null);
      } else {
        // This is the last evaluation, call reset replacement
        try {
          await resetReplacement(
            "b4bedff2-165e-4156-969f-d3b3cd025970", // shiftId
            "3e2bac24-5866-4065-8a7b-914c2e077cf1", // teamLeaderId
          );
          console.log("Replacement reset successfully after final evaluation");
        } catch (resetError) {
          console.error("Failed to reset replacement:", resetError);
          // Continue with closing the dialog even if reset fails
        }

        onClose();
        setStepIndex(0);
        setSelectedRating(null);
      }
    } catch (error) {
      console.error("Failed to submit evaluation:", error);
    }
  };

  const getRatingEmoji = (rating: FeedbackRating) => {
    switch (rating) {
      case "HIGH":
        return <CustomIcon name="satisfied" className="w-4 h-4" />;
      case "MEDIUM":
        return <CustomIcon name="medium" className="w-4 h-4" />;
      case "LOW":
        return <CustomIcon name="low" className="w-4 h-4" />;
      default:
        return "";
    }
  };

  const getRatingLabel = (rating: "HIGH" | "MEDIUM" | "LOW") => {
    switch (rating) {
      case "HIGH":
        return "Satisfied";
      case "MEDIUM":
        return "Medium";
      case "LOW":
        return "Low";
      default:
        return "";
    }
  };

  const handleClose = () => {
    setStepIndex(0);
    setSelectedRating(null);
    onClose();
  };

  if (isLoadingHistory) {
    return (
      <ReusableDialog
        isOpen={isOpen}
        onClose={handleClose}
        title="Replacement Feedback"
        size="lg"
        id="replacement-feedback-dialog"
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading replacement history...</p>
          </div>
        </div>
      </ReusableDialog>
    );
  }

  if (historyError) {
    return (
      <ReusableDialog
        isOpen={isOpen}
        onClose={handleClose}
        title="Replacement Feedback"
        size="lg"
        id="replacement-feedback-dialog"
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <CustomIcon name="error" className="w-8 h-8 mx-auto" />
            </div>
            <p className="text-red-600 mb-4">{historyError}</p>
            <Button
              onClick={() =>
                fetchReplacementHistory(
                  false,
                  undefined,
                  "b4bedff2-165e-4156-969f-d3b3cd025970",
                )
              }
              className="bg-black text-white"
            >
              Retry
            </Button>
          </div>
        </div>
      </ReusableDialog>
    );
  }

  if (!flattenedData.length) {
    return (
      <ReusableDialog
        isOpen={isOpen}
        onClose={handleClose}
        title="Replacement Feedback"
        size="lg"
        id="replacement-feedback-dialog"
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-gray-500">
              No replacement available for evaluation.
            </p>
            <Button onClick={handleClose} className="mt-4 bg-black text-white">
              Close
            </Button>
          </div>
        </div>
      </ReusableDialog>
    );
  }

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={() => {
        setStepIndex(0);
        setSelectedRating(null);
        onClose();
      }}
      title="Replacement Feedback"
      size="lg"
      id="replacement-feedback-dialog"
    >
      <div className="border-t-2 border-gray-200 pt-2">
        <div className="text-center text-2xl font-bold text-gray-700 mb-4">
          {stepIndex + 1}/{totalSteps}
        </div>

        {/* Replacement overview */}
        <div className="flex items-center justify-between space-x-4 border-b-2 border-gray-200 py-4">
          <div className="flex-1 text-center">
            <CustomIcon
              name="circleUser"
              className="w-6 h-6 mx-auto mb-3"
              style={{ width: "60px", height: "60px", fill: "#4CAF50" }}
            />
            <p className="text-xs text-gray-500">
              ID: {current.replacementWorker.id}
            </p>
            <p className="font-semibold text-gray-900">
              {current.replacementWorker.firstName +
                " " +
                current.replacementWorker.lastName}
            </p>
            <p className="text-green-600 font-medium">
              {current.replacementWorker.function}
            </p>
            <p className="text-xs text-gray-400">
              P: {current.replacementWorker.line}
            </p>
            <p className="text-xs text-gray-400">
              {current.replacementWorker.team}
            </p>
          </div>

          <div className="flex flex-col items-center space-y-2 px-4">
            <span className="text-sm text-green-600 font-medium">Replaced</span>
            <div className="flex items-center">
              <div className="h-[0.2rem] w-32 bg-green-500"></div>
              <div className="w-0 h-0 border-l-[8px] border-l-green-500 border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent"></div>
            </div>
            <div className="text-xs text-gray-500 text-center">
              <p>On: {current.date}</p>
              <p>({current.shift})</p>
            </div>
          </div>

          <div className="flex-1 text-center">
            <CustomIcon
              name="circleUser"
              className="w-6 h-6 mx-auto mb-3"
              style={{ width: "60px", height: "60px", fill: "#F84018" }}
            />
            <p className="text-xs text-gray-500">
              ID: {current.replacedWorker.id}
            </p>
            <p className="font-semibold text-gray-900">
              {current.replacedWorker.firstName +
                " " +
                current.replacedWorker.lastName}
            </p>
            <p className="text-red-600 font-medium">
              {current.replacedWorker.function}
            </p>
            <p className="text-xs text-gray-400">
              P: {current.replacedWorker.line}
            </p>
            <p className="text-xs text-gray-400">
              {current.replacedWorker.team}
            </p>
          </div>
        </div>

        {/* Feedback prompt */}
        <div className="space-y-4">
          <p className="text-center font-medium text-gray-900">
            Give us your feedback about the replaced worker!
          </p>

          <div className="flex justify-center gap-[6rem]">
            {(["HIGH", "MEDIUM", "LOW"] as const).map((rating) => (
              <div key={rating} className="text-center">
                <button
                  onClick={() => setSelectedRating(rating)}
                  className={`w-12 h-12 rounded-full text-3xl mb-2 transition-all duration-200 ${
                    selectedRating === rating
                      ? "opacity-100"
                      : "opacity-60 hover:opacity-80"
                  }`}
                >
                  {getRatingEmoji(rating)}
                </button>
                <div className="flex items-center justify-center mb-1">
                  <div
                    className={`w-4 h-4 rounded-full border-2 ${
                      selectedRating === rating
                        ? "bg-black border-black"
                        : "border-gray-300"
                    }`}
                  />
                </div>
                <p
                  className={`text-sm font-medium ${
                    selectedRating === rating ? "text-black" : "text-gray-500"
                  }`}
                >
                  {getRatingLabel(rating)}
                </p>
              </div>
            ))}
          </div>
        </div>

        {(evaluationError || resetReplacementError) && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">
              {evaluationError || resetReplacementError}
            </p>
          </div>
        )}

        <div className="flex justify-between pt-4">
          <Button
            variant="outline"
            onClick={handleClose}
            className="px-8 bg-transparent"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              !selectedRating ||
              isEvaluatingReplacement ||
              isResettingReplacement
            }
            className="px-8 bg-black hover:bg-gray-800 text-white disabled:opacity-50"
          >
            {isEvaluatingReplacement
              ? "Submitting..."
              : isResettingReplacement
                ? "Finalizing..."
                : "Submit feedback"}
          </Button>
        </div>
      </div>
    </ReusableDialog>
  );
};
