"use client";

import React from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Button } from "@/components/ui/button";
import CustomIcon from "@/components/common/CustomIcons";
import { Operator } from "../../store/replacementStore";

interface ReplacementSummaryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  absentEmployee?: Operator;
  selectedWorkers: Operator[];
  workerReplacementTypes: Record<string, "move" | "both">;
  isSubmitting?: boolean;
  actionType?: "submit" | "no-replacement" | "send-to-shift-leader";
}

export function ReplacementSummaryDialog({
  isOpen,
  onClose,
  onConfirm,
  absentEmployee,
  selectedWorkers,
  workerReplacementTypes,
  isSubmitting = false,
  actionType = "submit",
}: ReplacementSummaryDialogProps) {
  // Get the primary workstation for the absent employee
  const primaryWorkstation =
    absentEmployee?.workstation?.find((w) => w.isOwner) ||
    absentEmployee?.workstation?.[0];

  // Get required skills from the workstation
  const requiredSkills = primaryWorkstation?.requiredSkills || [];

  // Get criticity level
  const criticity = primaryWorkstation?.criticity || "Normal";

  // Helper function to get criticity color (same as ReplaceDialog)
  const getCriticityColor = (criticity: string) => {
    const lowerCriticity = criticity.toLowerCase();
    if (
      lowerCriticity.includes("high") ||
      lowerCriticity.includes("critical")
    ) {
      return "bg-red-100 text-red-800";
    } else if (
      lowerCriticity.includes("medium") ||
      lowerCriticity.includes("moderate")
    ) {
      return "bg-yellow-100 text-yellow-800";
    } else {
      return "bg-green-100 text-green-800";
    }
  };

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={onClose}
      title="Replacement Summary"
      size="3xl"
    >
      <div className="space-y-6">
        {/* Header Section with Workstation, Qualifications, and Criticity */}
        <div className="grid grid-cols-3 gap-6 p-4 rounded-lg border-b-2 border-gray-200 ">
          {/* Workstation */}
          <div className="flex flex-col items-center text-center border-r-2 border-gray-200">
            <div className="w-16 h-16 bg-[#ADB8C8] rounded-full flex items-center justify-center mb-3">
              <div className="w-8 h-8 bg-[#5B7291] rounded-full flex items-center justify-center">
                <CustomIcon
                  name="pin"
                  style={{ width: "50%", height: "50%" }}
                />
              </div>
            </div>
            <div className="text-sm text-gray-500 mb-1">Workstation</div>
            <div className="font-bold text-gray-900 text-lg">
              {primaryWorkstation?.workstation || "N/A"}
            </div>
          </div>

          {/* Qualifications */}
          <div className="flex flex-col items-center text-center">
            <div className="w-16 h-16 bg-[#ADB8C8] rounded-full flex items-center justify-center mb-3">
              <div className="w-8 h-8 bg-[#5B7291] rounded-full flex items-center justify-center">
                <CustomIcon
                  name="diplomaIcon"
                  style={{ width: "50%", height: "50%" }}
                />
              </div>
            </div>
            <div className="text-sm text-gray-500 mb-2">Qualifications</div>
            <div className="flex flex-wrap gap-1 justify-center">
              {requiredSkills.length > 0 ? (
                requiredSkills.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 rounded px-2 py-1 text-xs font-medium"
                  >
                    {skill}
                  </span>
                ))
              ) : (
                <span className="text-gray-400 text-sm">
                  No specific qualifications
                </span>
              )}
            </div>
          </div>

          {/* Criticity */}
          <div className="flex flex-col items-center text-center border-l-2 border-gray-200">
            <div className="w-16 h-16 bg-[#ADB8C8] rounded-full flex items-center justify-center mb-3">
              <div className="w-8 h-8 bg-[#5B7291] rounded-full flex items-center justify-center">
                <CustomIcon
                  name="stairs"
                  style={{ width: "40%", height: "40%" }}
                />
              </div>
            </div>
            <div className="text-sm text-gray-500 mb-1">Criticity</div>
            <div
              className={`rounded px-3 py-1 text-xs font-medium ${getCriticityColor(criticity)}`}
            >
              {criticity || "N : Normal"}
            </div>
          </div>
        </div>

        {/* Replacement Assignments */}
        <div className="">
          {actionType === "send-to-shift-leader" ? (
            // Send to Shift Leader scenario
            <div className="text-center">
              <div className="w-20 h-20 bg-transparent rounded-full flex items-center justify-center mx-auto mb-4">
                <CustomIcon
                  name="replace"
                  style={{ width: "60%", height: "60%", fill: "#5B7291" }}
                />
              </div>
              <div className="text-lg font-medium text-[#5B7291] mb-2">
                You are about to send a request to the shift leader to
              </div>
              <div className="text-lg font-medium text-[#5B7291]">
                find the best operator to replace the current absentee.
              </div>
            </div>
          ) : actionType === "no-replacement" ||
            selectedWorkers.length === 0 ? (
            // No Replacement scenario
            <div className="text-center">
              <div className="w-20 h-20 bg-transparent rounded-full flex items-center justify-center mx-auto mb-4">
                <CustomIcon
                  name="error"
                  style={{ width: "70%", height: "70%", fill: "#EF4444" }}
                />
              </div>
              <div className="text-lg font-medium text-red-600 mb-2">
                You choosed to not replace the absentiesme
              </div>
              <div className="text-lg font-medium text-red-600">
                and to keep the Workstation empty
              </div>
            </div>
          ) : (
            // Regular replacement with selected workers
            selectedWorkers.map((worker, index) => {
              const replacementType =
                workerReplacementTypes[worker.id] || "move";
              const actionText =
                replacementType === "move"
                  ? `Move from Workstation ${worker.workstation?.[0]?.workstation || "N/A"} to Work on -> Workstation ${primaryWorkstation?.workstation || "N/A"}`
                  : `Work on Both Workstations (${worker.workstation?.[0]?.workstation || "N/A"} + ${primaryWorkstation?.workstation || "N/A"} SE OP !)`;

              return (
                <div
                  key={worker.id}
                  className="flex items-center p-4 bg-green-50 rounded-lg border border-green-200"
                >
                  <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-gray-900">
                      {index + 1} - {worker.legacyId} - {worker.firstName}{" "}
                      {worker.lastName}
                    </div>
                    <div className="text-green-600 text-sm mt-1">
                      {actionText}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="px-6"
          >
            Back
          </Button>

          <Button
            onClick={onConfirm}
            disabled={isSubmitting}
            className={
              actionType === "send-to-shift-leader"
                ? "bg-[#5B7291] text-white hover:bg-[#4B5B6D] px-8"
                : actionType === "no-replacement"
                  ? "bg-red-600 text-white hover:bg-red-700 px-8"
                  : "bg-black text-white hover:bg-gray-800 px-8"
            }
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {actionType === "send-to-shift-leader"
                  ? "Sending..."
                  : actionType === "no-replacement"
                    ? "Processing..."
                    : "Processing..."}
              </div>
            ) : actionType === "send-to-shift-leader" ? (
              "Confirm"
            ) : actionType === "no-replacement" ? (
              "Confirm"
            ) : (
              "Confirm & Replace"
            )}
          </Button>
        </div>
      </div>
    </ReusableDialog>
  );
}
