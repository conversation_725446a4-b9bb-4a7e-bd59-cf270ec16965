import { Dispatch, SetStateAction } from "react";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
} from "@/components/common/CustomTabs";

interface ShiftLeaderTabsProps {
  counts: {
    myTeamleaders: number;
    myOperators: number;
    myBackupBasket: number;
    departmentBackupBasket: number;
  };
  activeTab: string;
  setActiveTab: Dispatch<SetStateAction<string>>;
}

export function ShiftLeaderTabs({
  counts,
  activeTab,
  setActiveTab,
}: ShiftLeaderTabsProps) {
  return (
    <div className="w-full">
      <CustomTabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <CustomTabsList>
          <CustomTabsTrigger value="my-teamleaders">
            My Teamleaders ({counts.myTeamleaders})
          </CustomTabsTrigger>
          <CustomTabsTrigger value="my-operators">
            My Operators ({counts.myOperators})
          </CustomTabsTrigger>
          <CustomTabsTrigger value="my-backup-basket">
            My Backup Basket ({counts.myBackupBasket})
          </CustomTabsTrigger>
          <CustomTabsTrigger value="department-backup-basket">
            Department Backup Basket ({counts.departmentBackupBasket})
          </CustomTabsTrigger>
        </CustomTabsList>
      </CustomTabs>
    </div>
  );
}
