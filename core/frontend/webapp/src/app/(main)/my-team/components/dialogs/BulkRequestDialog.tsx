"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { DialogTable } from "@/components/common/tables/DialogTable";
import { useAttendanceStore } from "../../store/myTeamStore";
import CustomMultiSelect from "@/components/common/CustomMultiSelect";

interface RequestOption {
  id: string;
  title: string;
  description?: string;
  [key: string]: unknown;
}

interface BulkRequestDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onNext?: (
    requestType: { id: string; title: string },
    selectedOperators: string[],
  ) => void;
}

const requestOptions: RequestOption[] = [
  {
    id: "absence_authorizations",
    title: "Absence authorizations",
  },
  {
    id: "inter_site_movement",
    title: "Inter-site movement",
  },
  {
    id: "request_outside_work",
    title: "Request for outside work",
  },
  {
    id: "request_leave_holidays",
    title: "Request for leave (Holidays)",
  },
  {
    id: "declaration_loss_badge",
    title: "Declaration of loss of badge",
  },
  {
    id: "request_salary_domiciliation",
    title: "Request for salary domiciliation",
  },
  {
    id: "request_account_transfer",
    title: "Request of account transfer",
  },
  {
    id: "payment_request_salary_anticipation",
    title: "Payment request (Salary anticipation)",
  },
];

export function BulkRequestDialog({
  isOpen,
  onClose,
  onNext,
}: BulkRequestDialogProps) {
  const [selectedRequest, setSelectedRequest] = useState<RequestOption | null>(
    null,
  );
  const [selectedOperators, setSelectedOperators] = useState<string[]>([]);

  const { workers } = useAttendanceStore();

  // Create operator options for the multi-select
  const operatorOptions = workers.map((worker) => ({
    label: `${worker.firstName} ${worker.lastName} (${worker.id})`,
    value: worker.id,
  }));

  const handleRequestSelect = (request: RequestOption) => {
    setSelectedRequest(request);
  };

  const handleOperatorSelect = (operatorIds: string[]) => {
    setSelectedOperators(operatorIds);
  };

  const handleNext = () => {
    if (selectedRequest && selectedOperators.length > 0 && onNext) {
      // Pass the selected request and operators to the next dialog
      onNext(
        {
          id: selectedRequest.id,
          title: selectedRequest.title,
        },
        selectedOperators,
      );
    } else if (selectedRequest && selectedOperators.length > 0) {
      // Fallback behavior if onNext is not provided
      console.log("Selected request:", selectedRequest);
      console.log("Selected operators:", selectedOperators);
      onClose();
    }
  };

  const handleBack = () => {
    onClose();
  };

  return (
    <ReusableDialog isOpen={isOpen} onClose={onClose} title="" size="xl">
      <div className="w-full max-w-lg mx-auto min-h-0 flex flex-col p-1">
        {/* Operator Selection Section */}
        <div className="mb-6 flex-shrink-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            Select Operators
          </h3>
          <CustomMultiSelect
            key={selectedOperators.join(",")}
            options={operatorOptions}
            onValueChange={handleOperatorSelect}
            defaultValue={selectedOperators}
            placeholder="Select operators..."
            className="w-full"
          />
          {selectedOperators.length > 0 && (
            <p className="text-sm text-gray-500 mt-2">
              {selectedOperators.length} operator(s) selected
            </p>
          )}
        </div>

        {/* Request Options Table */}
        <DialogTable
          data={requestOptions}
          onSelect={handleRequestSelect}
          selectedId={selectedRequest?.id || null}
          searchPlaceholder="Search for a request or document..."
          emptyMessage="No requests found matching your search."
          responsive={true}
          autoHeight={false}
          maxHeight="max-h-80"
          minHeight="min-h-48"
          containerClassName="flex-1"
          listClassName="rounded-lg border border-gray-200"
        />

        {/* Action Buttons */}
        <div className="flex justify-between flex-shrink-0 mt-4">
          <Button variant="outline" onClick={handleBack} className="px-6 py-2">
            Back
          </Button>
          <Button
            onClick={handleNext}
            disabled={!selectedRequest || selectedOperators.length === 0}
            className="px-6 py-2 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Next
          </Button>
        </div>
      </div>
    </ReusableDialog>
  );
}
