import { Card, CardContent } from "@/components/ui/card";
import { formatDuration } from "../types/Types";

interface TimeSlotSummaryProps {
  summaryData: {
    status: string;
    totalDurationMinutes: number;
    count: number;
    color: string;
  }[];
}

export function TimeSlotSummary({ summaryData }: TimeSlotSummaryProps) {
  return (
    <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
      {summaryData.map((item, index) => (
        <Card key={index} className="flex items-center gap-2 p-3">
          <div
            className={`size-4 shrink-0 rounded-sm ${item.color}`}
            aria-hidden="true"
          />
          <CardContent className="flex flex-col p-0">
            <span className="text-sm font-medium">
              {formatDuration(item.totalDurationMinutes)}
            </span>
            <span className="text-xs text-muted-foreground">{item.status}</span>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
