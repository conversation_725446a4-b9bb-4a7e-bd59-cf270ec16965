"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { DialogTable } from "@/components/common/tables/DialogTable";
import CustomIcon from "@/components/common/CustomIcons";

interface RequestOption {
  id: string;
  title: string;
  description?: string;
  [key: string]: unknown;
}

interface RequestDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onNext?: (requestType: { id: string; title: string }) => void;
  userInfo?: {
    name: string;
    id: string;
    status: string;
  };
}

const requestOptions: RequestOption[] = [
  {
    id: "absence_authorizations",
    title: "Absence authorizations",
  },
  {
    id: "inter_site_movement",
    title: "Inter-site movement",
  },
  {
    id: "request_outside_work",
    title: "Request for outside work",
  },
  {
    id: "request_leave_holidays",
    title: "Request for leave (Holidays)",
  },
  {
    id: "declaration_loss_badge",
    title: "Declaration of loss of badge",
  },
  {
    id: "request_salary_domiciliation",
    title: "Request for salary domiciliation",
  },
  {
    id: "request_account_transfer",
    title: "Request of account transfer",
  },
  {
    id: "payment_request_salary_anticipation",
    title: "Payment request (Salary anticipation)",
  },
];

export function RequestDialog({
  isOpen,
  onClose,
  onNext,
  userInfo,
}: RequestDialogProps) {
  const [selectedRequest, setSelectedRequest] = useState<RequestOption | null>(
    null,
  );

  const handleRequestSelect = (request: RequestOption) => {
    setSelectedRequest(request);
  };
  const handleNext = () => {
    if (selectedRequest && onNext) {
      // Pass the selected request to the next dialog
      onNext({
        id: selectedRequest.id,
        title: selectedRequest.title,
      });
    } else if (selectedRequest) {
      // Fallback behavior if onNext is not provided
      console.log("Selected request:", selectedRequest);
      onClose();
    }
  };

  const handleBack = () => {
    onClose();
  };
  return (
    <ReusableDialog isOpen={isOpen} onClose={onClose} title="" size="xl">
      <div className="w-full max-w-lg mx-auto min-h-0 flex flex-col p-1">
        {/* User Info Section */}
        {userInfo && (
          <div className="flex flex-col items-center mb-6 flex-shrink-0">
            <div className="w-16 h-16 bg-fffff rounded-full flex items-center justify-center mb-3">
              <CustomIcon
                name="circleUser"
                className="w-6 h-6 text-gray-500"
                style={{ width: "80px", height: "80px", fill: "#D1D1D1" }}
              />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              {userInfo.name}
            </h3>
            <p className="text-sm text-gray-500">ID : {userInfo.id}</p>
            <div className="flex items-center mt-2">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-green-600">{userInfo.status}</span>
            </div>
          </div>
        )}{" "}
        {/* Request Options Table */}
        <DialogTable
          data={requestOptions}
          onSelect={handleRequestSelect}
          selectedId={selectedRequest?.id || null}
          searchPlaceholder="Search for a request or document..."
          emptyMessage="No requests found matching your search."
          responsive={true}
          autoHeight={false}
          maxHeight="max-h-80"
          minHeight="min-h-48"
          containerClassName="flex-1"
          listClassName="rounded-lg border border-gray-200"
        />{" "}
        {/* Action Buttons */}
        <div className="flex justify-between flex-shrink-0 mt-4">
          <Button variant="outline" onClick={handleBack} className="px-6 py-2">
            Back
          </Button>
          <Button
            onClick={handleNext}
            disabled={!selectedRequest}
            className="px-6 py-2 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Next
          </Button>
        </div>
      </div>
    </ReusableDialog>
  );
}
