"use client";

import { useMemo, useEffect, useCallback } from "react";
import useReplacementStore from "../store/replacementStore";
import ReplacementInterface, {
  type ReplacementInterfaceConfig,
} from "./shared/ReplacementInterface";
import { PendingAssignment } from "../hooks/useReplacementLogic";

export default function MatchingInterface() {
  // Store integration
  const {
    mediumLevelBackup,
    mediumLevelRequests,
    isLoadingMediumLevelBackup,
    isLoadingMediumLevelRequests,
    isAssigningMediumLevelOperators,
    isSendingBackupToDepartment,
    isEscalatingMediumLevel,
    fetchMediumLevelBackup,
    fetchMediumLevelRequests,
    assignMediumLevelOperators,
    sendBackupToDepartment,
    escalateMediumLevel,
    assignMediumLevelOperatorsError,
    sendBackupToDepartmentError,
    clearAssignMediumLevelOperatorsError,
    clearSendBackupToDepartmentError,
  } = useReplacementStore();

  // Sample parameters - in real implementation, these would come from props or context
  const sampleParams = useMemo(
    () => ({
      shiftLeaderId: "d2dad5d4-d7d1-48e9-a3ed-e04387e28d40",
      shiftId: "b4bedff2-165e-4156-969f-d3b3cd025970",
      site: "89_MOROCCO MAR 1",
      department: "Assembly",
    }),
    [],
  );

  // Load medium-level requests on component mount
  useEffect(() => {
    // Fetch requests if not currently loading
    if (!isLoadingMediumLevelRequests) {
      const fetchWithErrorHandling = async () => {
        try {
          await fetchMediumLevelRequests(
            sampleParams.shiftLeaderId,
            sampleParams.shiftId,
            sampleParams.site,
          );
        } catch (error) {
          console.error(
            "Failed to fetch medium-level requests, stopping retry:",
            error,
          );
          // Don't retry on API failures - error is handled by the store
        }
      };
      fetchWithErrorHandling();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sampleParams.shiftLeaderId, sampleParams.shiftId, sampleParams.site]);

  // Load initial operators on component mount (with empty skills to get all operators)
  useEffect(() => {
    if (!mediumLevelBackup?.length && !isLoadingMediumLevelBackup) {
      const fetchWithErrorHandling = async () => {
        try {
          await fetchMediumLevelBackup(
            sampleParams.shiftLeaderId,
            sampleParams.shiftId,
            [], // Empty skills array to get all operators
          );
        } catch (error) {
          console.error(
            "Failed to fetch medium-level backup, stopping retry:",
            error,
          );
          // Don't retry on API failures - error is handled by the store
        }
      };
      fetchWithErrorHandling();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sampleParams.shiftLeaderId, sampleParams.shiftId]);

  // const handleFetchRequests = useCallback(
  //   async () => {
  //     try {
  //       await fetchMediumLevelRequests(
  //         sampleParams.shiftLeaderId,
  //         sampleParams.shiftId,
  //         sampleParams.site,
  //       );
  //     } catch (error) {
  //       console.error("Failed to fetch requests, stopping retry:", error);
  //       // Error is handled by the store, don't retry
  //     }
  //   },
  //   [
  //     sampleParams.shiftLeaderId,
  //     sampleParams.shiftId,
  //     sampleParams.site,
  //   ],
  // );

  const handleConfirmAssignments = useCallback(
    async (assignments: PendingAssignment[]) => {
      try {
        // Convert to the expected format for medium-level assignments
        // Note: Medium-level only supports single operator per request
        const operatorAssignments = assignments.flatMap((assignment) =>
          assignment.operatorIds.map((operatorId: string) => ({
            requestId: assignment.requestId,
            replacementOperatorId: operatorId,
          })),
        );

        await assignMediumLevelOperators(
          sampleParams.shiftLeaderId,
          sampleParams.shiftId,
          sampleParams.site,
          operatorAssignments,
        );

        // Update local state directly with the assignment information
        // This provides instant UI feedback without needing to refetch data

        // The assignMediumLevelOperators store action should ideally handle this,
        // but for now we rely on the local confirmedMatches state in ReplacementInterface
        console.log("Assignment completed successfully");
      } catch (error) {
        console.error("Failed to assign operators:", error);
        throw error; // Re-throw so ReplacementInterface can handle the error
      }
    },
    [
      assignMediumLevelOperators,
      sampleParams.shiftLeaderId,
      sampleParams.shiftId,
      sampleParams.site,
    ],
  );

  const handleProcessOperators = useCallback(
    async (operatorIds: string[]) => {
      try {
        await sendBackupToDepartment(
          operatorIds,
          sampleParams.shiftLeaderId,
          sampleParams.department,
          sampleParams.shiftId,
          sampleParams.site,
        );
        console.log("Operators sent to department:", operatorIds);

        // Refresh the medium-level backup data to reflect that operators have been sent away
        await fetchMediumLevelBackup(
          sampleParams.shiftLeaderId,
          sampleParams.shiftId,
          [], // Empty skills array to get all remaining operators
        );
      } catch (error) {
        console.error("Failed to send operators to department:", error);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      sampleParams.shiftLeaderId,
      sampleParams.department,
      sampleParams.shiftId,
      sampleParams.site,
    ],
  );

  const handleEscalateRequests = useCallback(
    async (requestIds: string[]) => {
      try {
        await escalateMediumLevel(
          requestIds,
          sampleParams.site,
          sampleParams.shiftLeaderId,
        );
        console.log("Requests escalated:", requestIds);
      } catch (error) {
        console.error("Failed to escalate requests:", error);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [sampleParams.site, sampleParams.shiftLeaderId],
  );

  const config: ReplacementInterfaceConfig = {
    department: sampleParams.department,
    operatorsTitle: "All Backup",
    requestsTitle: "All Requests",
    operators: (mediumLevelBackup || []).map((operator) => ({
      ...operator,
      projectHierarchy:
        typeof operator.projectHierarchy === "string"
          ? null
          : operator.projectHierarchy,
    })),
    requests: (mediumLevelRequests || []).map((request) => ({
      ...request,
      teamLeader: {
        ...request.teamLeader,
        legacyId: request.teamLeader.legacyId ?? 0,
      },
    })),
    isLoading:
      isLoadingMediumLevelBackup ||
      isLoadingMediumLevelRequests ||
      isAssigningMediumLevelOperators ||
      isSendingBackupToDepartment ||
      isEscalatingMediumLevel,
    errors: {
      operatorError: sendBackupToDepartmentError || undefined,
      requestError: assignMediumLevelOperatorsError || undefined,
    },
    onClearErrors: {
      clearOperatorError: clearSendBackupToDepartmentError,
      clearRequestError: clearAssignMediumLevelOperatorsError,
    },
    onFetchOperators: () => {}, // No-op function since we handle highlighting client-side
    onConfirmAssignments: handleConfirmAssignments,
    onProcessOperators: handleProcessOperators,
    onEscalateRequests: handleEscalateRequests,
    operatorActionLabel: "SEND TO DEPARTMENT",
    requestActionLabel: "ESCALATE TO DEPARTMENT",
  };

  return <ReplacementInterface config={config} />;
}
