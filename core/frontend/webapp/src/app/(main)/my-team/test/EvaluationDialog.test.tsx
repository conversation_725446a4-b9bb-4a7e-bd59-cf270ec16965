// This test file is temporarily skipped due to commented-out content
describe.skip("EvaluationDialog", () => {
  it("should be implemented", () => {
    expect(true).toBe(true);
  });
});
//         </div>
//       );
//     // },
//   };
// });

// // Mock other UI components
// jest.mock('@/components/ui/button', () => ({
//   Button: ({ children, onClick, disabled, ...props }: any) => (
//     <button onClick={onClick} disabled={disabled} data-testid={props['data-testid'] || 'button'}>
//       {children}
//     </button>
//   ),
// }));

// jest.mock('@/components/custom-icon', () => ({
//   CustomIcon: ({ name, className }: any) => (
//     <div data-testid={`icon-${name}`} className={className}>
//       {name}
//     </div>
//   ),
// }));

// describe('ReplacementFeedbackDialog', () => {
//   const mockStore = {
//     replacementHistory: [
//       {
//         id: 'hist1',
//         absentOperator: {
//           legacyId: 123,
//           firstName: 'John',
//           lastName: 'Doe',
//         },
//         replacementOperator: [
//           {
//             id: 'rep1',
//             legacyId: 456,
//             firstName: 'Jane',
//             lastName: 'Smith',
//           },
//         ],
//         targetWorkstation: {
//           name: 'Workstation 1',
//           area: 'Area 1',
//         },
//         absentTeamName: 'Team A',
//         shiftId: 'shift1',
//         shiftStartDate: '2024-01-01T08:00:00Z',
//         shiftEndDate: '2024-01-01T16:00:00Z',
//       },
//     ],
//     isLoadingHistory: false,
//     historyError: null,
//     fetchReplacementHistory: jest.fn().mockResolvedValue(undefined),
//     evaluateReplacement: jest.fn().mockResolvedValue(undefined),
//     isEvaluatingReplacement: false,
//     evaluationError: null,
//     clearHistoryError: jest.fn(),
//     clearEvaluationError: jest.fn(),
//     resetReplacement: jest.fn().mockResolvedValue(undefined),
//     isResettingReplacement: false,
//     resetReplacementError: null,
//     clearResetReplacementError: jest.fn(),
//   };

//   const defaultProps = {
//     isOpen: true,
//     onClose: jest.fn(),
//   };

//   beforeEach(() => {
//     jest.clearAllMocks();
//     mockUseReplacementStore.mockReturnValue(mockStore);
//   });

//   it('renders when open', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByTestId('reusable-dialog')).toBeInTheDocument();
//     expect(screen.getByTestId('dialog-title')).toHaveTextContent('Replacement Feedback');
//   });

//   it('does not render when closed', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} isOpen={false} />);

//     expect(screen.queryByTestId('reusable-dialog')).not.toBeInTheDocument();
//   });

//   it('fetches replacement history on open', async () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     await waitFor(() => {
//       expect(mockStore.fetchReplacementHistory).toHaveBeenCalledWith(
//         false,
//         '3e2bac24-5866-4065-8a7b-914c2e077cf1',
//         'b4bedff2-165e-4156-969f-d3b3cd025970'
//       );
//     });

//     expect(mockStore.clearHistoryError).toHaveBeenCalled();
//     expect(mockStore.clearEvaluationError).toHaveBeenCalled();
//     expect(mockStore.clearResetReplacementError).toHaveBeenCalled();
//   });

//   it('displays loading state', () => {
//     mockUseReplacementStore.mockReturnValue({
//       ...mockStore,
//       isLoadingHistory: true,
//     });

//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText(/loading/i)).toBeInTheDocument();
//   });

//   it('displays error state', () => {
//     mockUseReplacementStore.mockReturnValue({
//       ...mockStore,
//       historyError: 'Failed to fetch history',
//     });

//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText(/failed to fetch history/i)).toBeInTheDocument();
//   });

//   it('displays no data message when no history available', () => {
//     mockUseReplacementStore.mockReturnValue({
//       ...mockStore,
//       replacementHistory: [],
//     });

//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText(/no replacements/i)).toBeInTheDocument();
//   });

//   it('displays replacement history data', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText('1/1')).toBeInTheDocument(); // Step indicator
//     expect(screen.getByText('John Doe')).toBeInTheDocument();
//     expect(screen.getByText('Jane Smith')).toBeInTheDocument();
//     expect(screen.getByText('Team A')).toBeInTheDocument();
//   });

//   it('handles rating selection', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     const highRatingButton = screen.getByTestId('rating-HIGH');
//     fireEvent.click(highRatingButton);

//     // The rating should be selected (would need to check component state)
//     expect(highRatingButton).toHaveClass('selected'); // Assuming this class is applied
//   });

//   it('submits evaluation and moves to next step', async () => {
//     const storeWithMultipleHistory = {
//       ...mockStore,
//       replacementHistory: [
//         ...mockStore.replacementHistory,
//         {
//           id: 'hist2',
//           absentOperator: {
//             legacyId: 789,
//             firstName: 'Bob',
//             lastName: 'Johnson',
//           },
//           replacementOperator: [
//             {
//               id: 'rep2',
//               legacyId: 101,
//               firstName: 'Alice',
//               lastName: 'Brown',
//             },
//           ],
//           targetWorkstation: {
//             name: 'Workstation 2',
//             area: 'Area 2',
//           },
//           absentTeamName: 'Team B',
//           shiftId: 'shift2',
//           shiftStartDate: '2024-01-02T08:00:00Z',
//           shiftEndDate: '2024-01-02T16:00:00Z',
//         },
//       ],
//     };

//     mockUseReplacementStore.mockReturnValue(storeWithMultipleHistory);
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     // Select a rating
//     const highRatingButton = screen.getByTestId('rating-HIGH');
//     fireEvent.click(highRatingButton);

//     // Submit
//     const submitButton = screen.getByTestId('submit-button');
//     fireEvent.click(submitButton);

//     await waitFor(() => {
//       expect(mockStore.evaluateReplacement).toHaveBeenCalledWith('hist1', {
//         score: 'HIGH',
//         comment: 'User feedback: HIGH',
//       });
//     });

//     // Should move to next step
//     expect(screen.getByText('2/2')).toBeInTheDocument();
//   });

//   it('submits final evaluation and calls reset replacement', async () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     // Select a rating
//     const mediumRatingButton = screen.getByTestId('rating-MEDIUM');
//     fireEvent.click(mediumRatingButton);

//     // Submit (this is the last/only evaluation)
//     const submitButton = screen.getByTestId('submit-button');
//     fireEvent.click(submitButton);

//     await waitFor(() => {
//       expect(mockStore.evaluateReplacement).toHaveBeenCalledWith('hist1', {
//         score: 'MEDIUM',
//         comment: 'User feedback: MEDIUM',
//       });
//     });

//     await waitFor(() => {
//       expect(mockStore.resetReplacement).toHaveBeenCalledWith(
//         'b4bedff2-165e-4156-969f-d3b3cd025970',
//         '3e2bac24-5866-4065-8a7b-914c2e077cf1'
//       );
//     });

//     expect(defaultProps.onClose).toHaveBeenCalled();
//   });

//   it('handles evaluation error', async () => {
//     const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
//     const errorStore = {
//       ...mockStore,
//       evaluateReplacement: jest.fn().mockRejectedValue(new Error('Evaluation failed')),
//     };

//     mockUseReplacementStore.mockReturnValue(errorStore);
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     const highRatingButton = screen.getByTestId('rating-HIGH');
//     fireEvent.click(highRatingButton);

//     const submitButton = screen.getByTestId('submit-button');
//     fireEvent.click(submitButton);

//     await waitFor(() => {
//       expect(consoleSpy).toHaveBeenCalledWith(
//         'Failed to submit evaluation:',
//         expect.any(Error)
//       );
//     });

//     consoleSpy.mockRestore();
//   });

//   it('handles reset replacement error gracefully', async () => {
//     const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
//     const errorStore = {
//       ...mockStore,
//       resetReplacement: jest.fn().mockRejectedValue(new Error('Reset failed')),
//     };

//     mockUseReplacementStore.mockReturnValue(errorStore);
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     const highRatingButton = screen.getByTestId('rating-HIGH');
//     fireEvent.click(highRatingButton);

//     const submitButton = screen.getByTestId('submit-button');
//     fireEvent.click(submitButton);

//     await waitFor(() => {
//       expect(consoleSpy).toHaveBeenCalledWith(
//         'Failed to reset replacement:',
//         expect.any(Error)
//       );
//     });

//     // Should still close the dialog even if reset fails
//     expect(defaultProps.onClose).toHaveBeenCalled();

//     consoleSpy.mockRestore();
//   });

//   it('displays evaluation error', () => {
//     mockUseReplacementStore.mockReturnValue({
//       ...mockStore,
//       evaluationError: 'Failed to submit evaluation',
//     });

//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText(/failed to submit evaluation/i)).toBeInTheDocument();
//   });

//   it('displays reset replacement error', () => {
//     mockUseReplacementStore.mockReturnValue({
//       ...mockStore,
//       resetReplacementError: 'Failed to reset replacement',
//     });

//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText(/failed to reset replacement/i)).toBeInTheDocument();
//   });

//   it('closes dialog and resets state when close button is clicked', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     const closeButton = screen.getByTestId('close-button');
//     fireEvent.click(closeButton);

//     expect(defaultProps.onClose).toHaveBeenCalled();
//   });

//   it('disables submit button when no rating is selected', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     const submitButton = screen.getByTestId('submit-button');
//     expect(submitButton).toBeDisabled();
//   });

//   it('enables submit button when rating is selected', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     const highRatingButton = screen.getByTestId('rating-HIGH');
//     fireEvent.click(highRatingButton);

//     const submitButton = screen.getByTestId('submit-button');
//     expect(submitButton).not.toBeDisabled();
//   });

//   it('displays correct rating labels and emojis', () => {
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByText('Satisfied')).toBeInTheDocument();
//     expect(screen.getByText('Medium')).toBeInTheDocument();
//     expect(screen.getByText('Low')).toBeInTheDocument();

//     expect(screen.getByTestId('icon-satisfied')).toBeInTheDocument();
//     expect(screen.getByTestId('icon-medium')).toBeInTheDocument();
//     expect(screen.getByTestId('icon-low')).toBeInTheDocument();
//   });

//   it('processes replacement history into flattened format correctly', () => {
//     const storeWithComplexHistory = {
//       ...mockStore,
//       replacementHistory: [
//         {
//           id: 'hist1',
//           absentOperator: {
//             legacyId: 123,
//             firstName: 'John',
//             lastName: 'Doe',
//           },
//           replacementOperator: [
//             {
//               id: 'rep1',
//               legacyId: 456,
//               firstName: 'Jane',
//               lastName: 'Smith',
//             },
//             {
//               id: 'rep2',
//               legacyId: 789,
//               firstName: 'Bob',
//               lastName: 'Johnson',
//             },
//           ],
//           targetWorkstation: {
//             name: 'Workstation 1',
//             area: 'Area 1',
//           },
//           absentTeamName: 'Team A',
//           shiftId: 'shift1',
//           shiftStartDate: '2024-01-01T08:00:00Z',
//           shiftEndDate: '2024-01-01T16:00:00Z',
//         },
//       ],
//     };

//     mockUseReplacementStore.mockReturnValue(storeWithComplexHistory);
//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     // Should show 1/2 because there are 2 replacement operators for 1 history entry
//     expect(screen.getByText('1/2')).toBeInTheDocument();
//   });

//   it('handles loading state for evaluation', () => {
//     mockUseReplacementStore.mockReturnValue({
//       ...mockStore,
//       isEvaluatingReplacement: true,
//     });

//     render(<ReplacementFeedbackDialog {...defaultProps} />);

//     expect(screen.getByTestId('submit-button')).toBeDisabled();
//   });
// });
