import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import ReplacementPanel from "../shared/ReplacementPanel";

// Type definitions for test data
interface ReplacementWorker {
  id: string;
  name: string;
  department: string;
  project: string;
}

interface Replacement {
  replacementWorker: ReplacementWorker;
  absentWorker: ReplacementWorker;
  date: string;
  timeFrom: string;
  timeTo: string;
  shift: string;
}

interface ReplacementDetail {
  team: string;
  replacementName: string;
  executedBy: string;
  replacementId: string;
}

interface WillReplaceItem {
  id: string;
  firstName: string;
  lastName: string;
  teamName: string;
  workstation: string;
  executedBy: string;
}

interface TakenWorker {
  id: string;
  firstName: string;
  lastName: string;
  teamName: string;
  isSentToAnotherBackup: boolean;
  willReplace: WillReplaceItem[] | undefined;
}

interface AddedWorker {
  id: string;
  firstName: string;
  lastName: string;
  teamName: string;
  isRaisedToShiftLeader: boolean;
  remplacedBy: ReplacementDetail[] | undefined;
}

interface BackupMovementsData {
  taken: TakenWorker[];
  added: AddedWorker[];
}

interface SimpleMockUser {
  id: string;
  name: string;
  role?: string;
}

// Mock the stores
const mockAttendanceStore = {
  replacements: {
    incoming: [] as Replacement[],
    outgoing: [] as Replacement[],
  },
};

const mockMyTeamStore = {
  currentDate: new Date("2024-01-01"),
  shiftStartTime: "08:00",
  shiftEndTime: "16:00",
};

const mockReplacementStore = {
  backupMovements: null as BackupMovementsData | null,
  isLoadingBackupMovements: false,
  backupMovementsError: null as string | null,
  fetchBackupMovements: jest.fn(),
};

const mockAuthStore = {
  currentUser: {
    id: "user-123",
    name: "Test User",
  } as SimpleMockUser | null,
};

// Mock the custom hooks
jest.mock("../attendance-sheet/store/attendanceStore", () => ({
  useAttendanceStore: () => mockAttendanceStore,
}));

jest.mock("../store/myTeamStore", () => ({
  useAttendanceStore: () => mockMyTeamStore,
}));

jest.mock("../store/replacementStore", () => ({
  __esModule: true,
  default: () => mockReplacementStore,
}));

jest.mock("@/store/mockAuthStore", () => ({
  __esModule: true,
  default: () => mockAuthStore,
}));

// Mock the CustomIcon component
jest.mock("@/components/common/CustomIcons", () => {
  return function CustomIcon({
    name,
    style,
    className,
  }: {
    name: string;
    style?: React.CSSProperties;
    className?: string;
  }) {
    return (
      <div data-testid={`icon-${name}`} className={className} style={style}>
        {name}
      </div>
    );
  };
});

// Mock lucide-react icons
jest.mock("lucide-react", () => ({
  X: ({ className }: { className?: string }) => (
    <div data-testid="close-icon" className={className}>
      X
    </div>
  ),
}));

describe("ReplacementPanel", () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockReplacementStore.backupMovements = null;
    mockReplacementStore.isLoadingBackupMovements = false;
    mockReplacementStore.backupMovementsError = null;
  });

  describe("Basic Rendering", () => {
    it("should render when open", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(
        screen.getByText("Workers will join to cover for my absenteeism."),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          "Workers will be taken from my team to replace absenteeism in other teams.",
        ),
      ).toBeInTheDocument();
    });

    it("should not render when closed", () => {
      render(<ReplacementPanel {...defaultProps} isOpen={false} />);

      expect(
        screen.queryByText("Workers will join to cover for my absenteeism."),
      ).not.toBeInTheDocument();
    });

    it("should call onClose when close button is clicked", () => {
      const onClose = jest.fn();
      render(<ReplacementPanel {...defaultProps} onClose={onClose} />);

      const closeButton = screen.getByTestId("close-icon").parentElement;
      fireEvent.click(closeButton!);

      expect(onClose).toHaveBeenCalled();
    });
  });

  describe("API Integration", () => {
    it("should fetch backup movements on mount when user is logged in", async () => {
      render(<ReplacementPanel {...defaultProps} />);

      await waitFor(() => {
        expect(mockReplacementStore.fetchBackupMovements).toHaveBeenCalledWith(
          "3e2bac24-5866-4065-8a7b-914c2e077cf1",
          "b4bedff2-165e-4156-969f-d3b3cd025970",
        );
      });
    });

    it("should not fetch backup movements when user is not logged in", () => {
      mockAuthStore.currentUser = null;
      render(<ReplacementPanel {...defaultProps} />);

      expect(mockReplacementStore.fetchBackupMovements).not.toHaveBeenCalled();
    });

    it("should not fetch backup movements when panel is closed", () => {
      render(<ReplacementPanel {...defaultProps} isOpen={false} />);

      expect(mockReplacementStore.fetchBackupMovements).not.toHaveBeenCalled();
    });
  });

  describe("Loading States", () => {
    it("should display loading state when fetching backup movements", () => {
      mockReplacementStore.isLoadingBackupMovements = true;
      render(<ReplacementPanel {...defaultProps} />);

      expect(
        screen.getByText("Loading backup movements..."),
      ).toBeInTheDocument();
    });

    it("should display error state when fetch fails", () => {
      mockReplacementStore.backupMovementsError = "Failed to fetch data";
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Failed to fetch data")).toBeInTheDocument();
    });
  });

  describe("Backup Movements Data Display", () => {
    const mockBackupMovements: BackupMovementsData = {
      added: [
        {
          id: "worker-1",
          firstName: "John",
          lastName: "Doe",
          teamName: "Team Alpha",
          isRaisedToShiftLeader: false,
          remplacedBy: [
            {
              replacementId: "repl-1",
              replacementName: "Jane Smith",
              team: "Team Beta",
              executedBy: "Manager 1",
            },
          ],
        },
        {
          id: "worker-2",
          firstName: "Bob",
          lastName: "Wilson",
          teamName: "Team Gamma",
          isRaisedToShiftLeader: true,
          remplacedBy: [], // Empty array to test waiting state
        },
      ],
      taken: [
        {
          id: "worker-3",
          firstName: "Alice",
          lastName: "Brown",
          teamName: "Team Delta",
          isSentToAnotherBackup: false,
          willReplace: [
            {
              id: "target-1",
              firstName: "Mike",
              lastName: "Johnson",
              teamName: "Team Echo",
              workstation: "Station A",
              executedBy: "Manager 2",
            },
          ],
        },
        {
          id: "worker-4",
          firstName: "Carol",
          lastName: "Davis",
          teamName: "Team Foxtrot",
          isSentToAnotherBackup: true,
          willReplace: [], // Empty array to test waiting state
        },
      ],
    };

    beforeEach(() => {
      mockReplacementStore.backupMovements = mockBackupMovements;
    });

    it("should display correct count for incoming movements", () => {
      render(<ReplacementPanel {...defaultProps} />);

      const counts = screen.getAllByText("2");
      expect(counts).toHaveLength(2); // Both incoming and outgoing show 2
    });

    it("should display correct count for outgoing movements", () => {
      render(<ReplacementPanel {...defaultProps} />);

      const counts = screen.getAllByText("2");
      expect(counts).toHaveLength(2); // Both incoming and outgoing show 2
    });

    it("should display worker information for incoming movements", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("John Doe")).toBeInTheDocument();
      expect(screen.getByText("Team Alpha")).toBeInTheDocument();
      expect(screen.getByText("Bob Wilson")).toBeInTheDocument();
      expect(screen.getByText("Team Gamma")).toBeInTheDocument();
    });

    it("should display replacement information when available", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Jane Smith")).toBeInTheDocument();
      expect(screen.getByText("Team Beta")).toBeInTheDocument();
      // Note: "Executed by" was removed from the component
    });

    it("should display waiting state when no replacement assigned", () => {
      render(<ReplacementPanel {...defaultProps} />);

      const waitingTexts = screen.getAllByText("Waiting for");
      expect(waitingTexts.length).toBeGreaterThan(0);
      expect(screen.getByText("Replacement")).toBeInTheDocument();
      const pendingTexts = screen.getAllByText("Pending ...");
      expect(pendingTexts.length).toBeGreaterThan(0);
    });

    it("should display absent status for workers", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getAllByText("Absent")).toHaveLength(2); // Both workers show "Absent"
    });

    it("should display outgoing worker information", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Alice Brown")).toBeInTheDocument();
      expect(screen.getByText("Team Delta")).toBeInTheDocument();
      expect(screen.getByText("Carol Davis")).toBeInTheDocument();
      expect(screen.getByText("Team Foxtrot")).toBeInTheDocument();
    });

    it("should display target replacement information", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Mike Johnson")).toBeInTheDocument();
      expect(screen.getByText("Team Echo")).toBeInTheDocument();
      expect(screen.getByText("Replace at: Station A")).toBeInTheDocument();
      // Note: "Executed by" was removed from the component
    });

    it("should display waiting state for unassigned workers", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("No assignment yet")).toBeInTheDocument();
      const waitingTexts = screen.getAllByText("Waiting for");
      expect(waitingTexts.length).toBeGreaterThan(0);
      expect(screen.getByText("Assignment")).toBeInTheDocument();
    });
    it.skip("should display backup status indicators", () => {
      render(<ReplacementPanel {...defaultProps} />);

      // Check that both status types are displayed based on the mock data
      expect(screen.getByText("Available")).toBeInTheDocument(); // worker-3 has isSentToAnotherBackup=false
      expect(screen.getByText("Sent to backup")).toBeInTheDocument(); // worker-4 has isSentToAnotherBackup=true
    });
  });

  describe("Fallback to Legacy Data", () => {
    const mockLegacyReplacements = {
      incoming: [
        {
          date: "2024-01-01",
          shift: "08:00-16:00",
          timeFrom: "08:00",
          timeTo: "16:00",
          replacementWorker: {
            id: "legacy-1",
            name: "Legacy Worker",
            department: "Legacy Dept",
            project: "Legacy Project",
          },
          absentWorker: {
            id: "absent-1",
            name: "Absent Legacy",
            department: "Absent Dept",
            project: "Absent Project",
          },
        },
      ],
      outgoing: [
        {
          date: "2024-01-01",
          shift: "08:00-16:00",
          timeFrom: "08:00",
          timeTo: "16:00",
          replacementWorker: {
            id: "legacy-2",
            name: "Outgoing Worker",
            department: "Outgoing Dept",
            project: "Outgoing Project",
          },
          absentWorker: {
            id: "absent-2",
            name: "Target Worker",
            department: "Target Dept",
            project: "Target Project",
          },
        },
      ],
    };

    beforeEach(() => {
      mockAttendanceStore.replacements = mockLegacyReplacements;
      mockReplacementStore.backupMovements = null; // No new data available
    });

    it("should display legacy data when backup movements are not available", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Legacy Worker")).toBeInTheDocument();
      expect(screen.getByText("Legacy Dept")).toBeInTheDocument();
      expect(screen.getByText("Absent Legacy")).toBeInTheDocument();
      expect(screen.getByText("Outgoing Worker")).toBeInTheDocument();
    });

    it("should display correct counts for legacy data", () => {
      render(<ReplacementPanel {...defaultProps} />);

      // Should show counts of 0 since we're using empty backup movements
      expect(screen.getAllByText("0")).toHaveLength(2);
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty backup movements gracefully", () => {
      mockReplacementStore.backupMovements = { added: [], taken: [] };
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getAllByText("0")).toHaveLength(2); // Both counts should be 0
    });

    it("should handle null backup movements", () => {
      mockReplacementStore.backupMovements = null;
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getAllByText("0")).toHaveLength(2); // Should fallback to empty arrays
    });

    it("should handle workers with undefined remplacedBy array", () => {
      mockReplacementStore.backupMovements = {
        added: [
          {
            id: "worker-1",
            firstName: "Test",
            lastName: "Worker",
            teamName: "Test Team",
            isRaisedToShiftLeader: false,
            remplacedBy: undefined,
          },
        ],
        taken: [],
      };

      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Waiting for")).toBeInTheDocument();
      expect(screen.getByText("Replacement")).toBeInTheDocument();
    });

    it("should handle workers with undefined willReplace array", () => {
      mockReplacementStore.backupMovements = {
        added: [],
        taken: [
          {
            id: "worker-1",
            firstName: "Test",
            lastName: "Worker",
            teamName: "Test Team",
            isSentToAnotherBackup: false,
            willReplace: undefined,
          },
        ],
      };

      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getByText("Waiting for")).toBeInTheDocument();
      expect(screen.getByText("Assignment")).toBeInTheDocument();
    });
  });

  describe("Visual Elements", () => {
    beforeEach(() => {
      mockReplacementStore.backupMovements = {
        added: [
          {
            id: "worker-1",
            firstName: "Test",
            lastName: "Worker",
            teamName: "Test Team",
            isRaisedToShiftLeader: false,
            remplacedBy: [],
          },
        ],
        taken: [
          {
            id: "worker-2",
            firstName: "Test",
            lastName: "Worker2",
            teamName: "Test Team2",
            isSentToAnotherBackup: false,
            willReplace: [],
          },
        ],
      };
    });

    it("should render correct icons for different states", () => {
      render(<ReplacementPanel {...defaultProps} />);

      expect(screen.getAllByTestId("icon-circleUser")).toHaveLength(4); // 2 for waiting states, 2 for workers
      expect(screen.getAllByTestId("icon-upload")).toHaveLength(2); // Header indicators
    });

    it("should apply correct styling classes", () => {
      render(<ReplacementPanel {...defaultProps} />);

      // Check for the parent container with the correct classes
      const incomingSection = screen.getByText(
        "Workers will join to cover for my absenteeism.",
      ).parentElement?.parentElement;
      expect(incomingSection).toHaveClass("space-y-4", "space-x-6");
    });

    it("should display shift time information", () => {
      render(<ReplacementPanel {...defaultProps} />);

      const timeElements = screen.getAllByText(/\(08:00 - 16:00\)/);
      expect(timeElements.length).toBeGreaterThan(0);
    });
  });
});
