import { useTeamLeaderStore } from "../../store/teamLeaderStore";

describe("Team Leader Store", () => {
  beforeEach(() => {
    // Reset store state before each test
    useTeamLeaderStore.setState({
      teamData: null,
      currentStatusData: null,
      statusHistory: {},
      isTeamDataLoading: false,
      isCurrentStatusLoading: false,
      isStatusHistoryLoading: false,
      isUpdatingStatus: false,
      teamDataError: null,
      currentStatusError: null,
      statusHistoryError: null,
      updateStatusError: null,
    });
  });

  it("should initialize with correct default state", () => {
    const state = useTeamLeaderStore.getState();

    expect(state.teamData).toBeNull();
    expect(state.currentStatusData).toBeNull();
    expect(state.statusHistory).toEqual({});
    expect(state.isTeamDataLoading).toBe(false);
    expect(state.isCurrentStatusLoading).toBe(false);
    expect(state.teamDataError).toBeNull();
    expect(state.currentStatusError).toBeNull();
  });

  it("should return empty array when no team operators", () => {
    const operators = useTeamLeaderStore.getState().getTeamOperators();
    expect(operators).toEqual([]);
  });

  it("should return empty string when no current status for operator", () => {
    const status = useTeamLeaderStore.getState().getCurrentStatusForOperator(1);
    expect(status).toBe("");
  });

  it("should return empty array when no status history for operator", () => {
    const history = useTeamLeaderStore
      .getState()
      .getStatusHistoryForOperator(1);
    expect(history).toEqual([]);
  });

  it("should clear team data correctly", () => {
    // Set some data first
    useTeamLeaderStore.setState({
      teamData: {
        teamId: "1",
        teamName: "Test",
        operators: [],
        shiftInstantId: "1",
        date: "2024-01-01",
      },
      teamDataError: "Some error",
    });

    useTeamLeaderStore.getState().clearTeamData();

    const state = useTeamLeaderStore.getState();
    expect(state.teamData).toBeNull();
    expect(state.teamDataError).toBeNull();
  });

  it("should clear current status correctly", () => {
    // Set some data first
    useTeamLeaderStore.setState({
      currentStatusData: { shiftInstantId: "1", teamId: "1", operators: [] },
      currentStatusError: "Some error",
    });

    useTeamLeaderStore.getState().clearCurrentStatus();

    const state = useTeamLeaderStore.getState();
    expect(state.currentStatusData).toBeNull();
    expect(state.currentStatusError).toBeNull();
  });

  it("should clear all errors correctly", () => {
    // Set some errors first
    useTeamLeaderStore.setState({
      teamDataError: "Team error",
      currentStatusError: "Status error",
      statusHistoryError: "History error",
      updateStatusError: "Update error",
    });

    useTeamLeaderStore.getState().clearErrors();

    const state = useTeamLeaderStore.getState();
    expect(state.teamDataError).toBeNull();
    expect(state.currentStatusError).toBeNull();
    expect(state.statusHistoryError).toBeNull();
    expect(state.updateStatusError).toBeNull();
  });
});
