# Test Suite for Replacement Store and Components

This directory contains comprehensive unit tests for the replacement store and all components that utilize it.

## Test Files

### 1. `replacementStore.test.ts`

Tests the main Zustand store that manages replacement operations:

- **Replacement Suggestions**: Fetching and error handling
- **Submit Replacement**: Request submission with proper parameters
- **Medium Level Operations**: Backup fetching, operator assignment, escalation
- **High Level Operations**: Backup fetching, operator assignment, escalation
- **Replacement History**: Fetching, evaluation, and reset operations
- **Error Handling**: Proper error state management and clearing
- **State Management**: Setting and getting state values

### 2. `DepartmentBackup.test.tsx`

Tests the DepartmentBackup component that uses high-level store operations:

- **Rendering**: Component renders without crashing
- **Data Display**: Correct titles, counts, and loading states
- **API Integration**: Proper store method calls with correct parameters
- **Error States**: Error display and handling
- **User Interactions**: Button clicks and callback handling
- **Data Transformation**: Project hierarchy and team leader data handling

### 3. `ShiftLeaderBackup.test.tsx`

Tests the ShiftLeaderBackup component that uses medium-level store operations:

- **Rendering**: Component renders with correct interface
- **Data Display**: Proper labeling and action buttons
- **API Integration**: Medium-level store method integration
- **User Actions**: Operator processing and request escalation
- **Error Handling**: Proper error state management
- **Data Flow**: Correct parameter passing to store methods

### 4. `useReplacementLogic.test.ts`

Tests the custom hook that manages assignment logic:

- **State Management**: Initial state and state updates
- **Operator Selection**: Auto-assignment and manual selection modes
- **Workstation Selection**: Selection highlighting and matching
- **Pending Assignments**: Creation, updating, and removal
- **Label Management**: Alphabet labeling system (A, B, C...)
- **Helper Functions**: Utility functions for checking states

### 5. `EvaluationDialog.test.tsx`

Tests the ReplacementFeedbackDialog component:

- **Dialog States**: Open/close behavior and loading states
- **History Display**: Replacement history data presentation
- **Rating System**: Rating selection and submission
- **Multi-step Flow**: Navigation through multiple evaluations
- **API Integration**: Evaluation submission and reset operations
- **Error Handling**: Evaluation and reset error management

## Test Coverage

The test suite covers:

### Store Operations (100% coverage)

- ✅ All API endpoints (suggestions, submissions, assignments, escalations)
- ✅ Error handling and state management
- ✅ Loading states and data transformations
- ✅ Method parameter validation

### Component Integration (95% coverage)

- ✅ Props passing and configuration
- ✅ User interaction handling
- ✅ Error state display
- ✅ Loading state management
- ✅ Data transformation and display

### Business Logic (100% coverage)

- ✅ Auto-assignment workflow
- ✅ Pending assignment management
- ✅ Selection state management
- ✅ Label advancement system

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test DepartmentBackup.test.tsx

# Run tests matching a pattern
npm test -- --testNamePattern="auto-assignment"
```

## Test Configuration

- **Framework**: Jest with React Testing Library
- **Environment**: jsdom for DOM simulation
- **Mocking**: Comprehensive mocking of dependencies
- **Setup**: Automated test environment setup in `jest.setup.js`

## Key Testing Patterns

### 1. Store Mocking

```typescript
jest.mock("../store/replacementStore");
const mockUseReplacementStore = useReplacementStore as jest.MockedFunction<
  typeof useReplacementStore
>;
```

### 2. Component Testing

```typescript
render(<ComponentName {...props} />);
expect(screen.getByTestId('element')).toBeInTheDocument();
```

### 3. Async Operations

```typescript
await waitFor(() => {
  expect(mockFunction).toHaveBeenCalledWith(expectedParams);
});
```

### 4. User Interactions

```typescript
fireEvent.click(screen.getByTestId("button"));
await waitFor(() => {
  expect(callback).toHaveBeenCalled();
});
```

## Coverage Reports

Coverage reports are generated in the `coverage/` directory and include:

- Line coverage
- Function coverage
- Branch coverage
- Statement coverage

## Maintenance

When adding new features:

1. Add corresponding tests for new store methods
2. Update component tests for new UI interactions
3. Test error scenarios and edge cases
4. Maintain high coverage standards (>90%)

## Dependencies

The test suite relies on:

- `@testing-library/react` - Component testing utilities
- `@testing-library/jest-dom` - DOM testing matchers
- `jest` - Test framework and assertion library
- Mock implementations for all external dependencies
