export type AttendanceStatus = "P" | "CTN" | "MA" | "AB" | "REPLACE" | "";
export type ClockingValidationStatus =
  | "Not finished yet"
  | "Waiting validation..."
  | "Validated"
  | "Sent for review"
  | "Absent"
  | "";

// New type for the API response values
export type ApiOptionValue = {
  id: string;
  value: string;
};

// New type for the API response metadata
export type ApiResponseMetadata = {
  level: string;
  filters: {
    customer_id?: string;
    projet_id?: string;
    famille_id?: string;
    value_stream_id?: string;
    area_id?: string;
  };
  timestamp: string;
};

// New type for the complete API response
export type ZoningOptionsResponse = {
  values: ApiOptionValue[];
  total: number;
  metadata: ApiResponseMetadata;
};
// New interfaces for team API response
export interface StatusHistoryEntry {
  label: "Present" | "Absent" | "CTN";
  date: string;
}

export interface TeamOperator {
  legacyId: number;
  firstName: string;
  lastName: string;
  role: string;
  statusHistory: StatusHistoryEntry[];
}

export interface TeamLeader {
  legacyId: number;
  fullName: string;
  category: string;
}

export interface TeamApiResponse {
  teamId: string;
  teamLeader: TeamLeader;
  operators: TeamOperator[];
}

// New interfaces for current status API
export interface CurrentStatusOperator {
  operatorId: number;
  name: string;
  currentStatus: AttendanceStatus; // Use the enum type
}

export interface CurrentStatusResponse {
  teamId: string;
  date: string;
  shiftInstantId: string;
  shiftStatus: string;
  startTime: string;
  shiftName: string;
  endTime: string;
  operators: CurrentStatusOperator[];
}

export interface UpdateStatusResponse {
  statusCode: string;
}

// Filter option interface for team leader filters
export interface FilterOption {
  id: string;
  label: string;
  value: string;
}

// Team leader filter state interface
export interface TeamLeaderFilterState {
  customerOptions: FilterOption[];
  projectOptions: FilterOption[];
  familyOptions: FilterOption[];
  valueStreamOptions: FilterOption[];
  areaOptions: FilterOption[];
  teamOptions: FilterOption[];

  selectedCustomer: string;
  selectedProject: string;
  selectedFamily: string;
  selectedValueStream: string;
  selectedArea: string;
  selectedTeam: string;

  isLoading: boolean;
}
