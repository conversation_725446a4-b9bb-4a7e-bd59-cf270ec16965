import { useState, useCallback } from "react";

export interface PendingAssignment {
  requestId: string;
  operatorIds: string[];
  label: string;
}

export interface ConfirmedMatch {
  workstationId: string;
  operatorId: string;
}

export function useReplacementLogic() {
  // Simplified state for single selections
  const [selectedWorkstation, setSelectedWorkstation] = useState<string | null>(
    null,
  );
  const [selectedOperator, setSelectedOperator] = useState<string | null>(null);
  const [checkedOperators, setCheckedOperators] = useState<string[]>([]);
  const [checkedRequests, setCheckedRequests] = useState<string[]>([]);
  const [confirmedMatches, setConfirmedMatches] = useState<ConfirmedMatch[]>(
    [],
  );

  // Create a simple pending assignment when both request and operator are selected
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const pendingAssignments =
    selectedWorkstation && selectedOperator
      ? [
          {
            requestId: selectedWorkstation,
            operatorIds: [selectedOperator],
            label: "A",
          },
        ]
      : [];

  const currentSelectionLabel = "A"; // Since we only have single selection

  // Helper functions for compatibility with existing interface
  const getPendingAssignmentForRequest = useCallback(
    (requestId: string) => {
      return pendingAssignments.find(
        (assignment) => assignment.requestId === requestId,
      );
    },
    [pendingAssignments],
  );

  const getPendingAssignmentForOperator = useCallback(
    (operatorId: string) => {
      return pendingAssignments.find((assignment) =>
        assignment.operatorIds.includes(operatorId),
      );
    },
    [pendingAssignments],
  );

  const handleSelectOperator = useCallback(
    (operatorId: string) => {
      if (!selectedWorkstation) {
        // If no workstation is selected, can't select operator
        return;
      }

      // Toggle operator selection
      if (selectedOperator === operatorId) {
        setSelectedOperator(null);
      } else {
        setSelectedOperator(operatorId);
      }
    },
    [selectedWorkstation, selectedOperator],
  );

  // Add pending assignment function (simplified - not used anymore)
  const handleAddToPendingAssignments = useCallback(() => {
    // Not needed with single selection, but kept for compatibility
  }, []);

  const handleCheckOperator = useCallback((operatorId: string) => {
    setCheckedOperators((prev) =>
      prev.includes(operatorId)
        ? prev.filter((id) => id !== operatorId)
        : [...prev, operatorId],
    );
  }, []);

  const handleCheckRequest = useCallback((requestId: string) => {
    setCheckedRequests((prev) =>
      prev.includes(requestId)
        ? prev.filter((id) => id !== requestId)
        : [...prev, requestId],
    );
  }, []);

  const isWorkstationHighlighted = useCallback(
    (workstationId: string) => {
      return selectedWorkstation === workstationId;
    },
    [selectedWorkstation],
  );

  const isWorkstationMatched = useCallback(
    (workstationId: string) => {
      return confirmedMatches.some(
        (match) => match.workstationId === workstationId,
      );
    },
    [confirmedMatches],
  );

  const isWorkstationInPendingAssignments = useCallback(
    (workstationId: string) => {
      return pendingAssignments.some(
        (assignment) => assignment.requestId === workstationId,
      );
    },
    [pendingAssignments],
  );

  const isOperatorMatched = useCallback(
    (operatorId: string) => {
      return confirmedMatches.some((match) => match.operatorId === operatorId);
    },
    [confirmedMatches],
  );

  const clearAllSelections = useCallback(() => {
    setSelectedWorkstation(null);
    setSelectedOperator(null);
  }, []);

  const removePendingAssignment = useCallback(
    (requestId: string) => {
      // Clear selections if removing the current pending assignment
      if (selectedWorkstation === requestId) {
        setSelectedWorkstation(null);
        setSelectedOperator(null);
      }
    },
    [selectedWorkstation],
  );

  return {
    // State
    selectedWorkstation,
    setSelectedWorkstation,
    selectedOperators: selectedOperator ? [selectedOperator] : [], // Compatibility with existing interface
    setSelectedOperators: (
      operators: string[] | ((prev: string[]) => string[]),
    ) => {
      // Compatibility function - take first operator if array provided
      if (typeof operators === "function") {
        const newOperators = operators(
          selectedOperator ? [selectedOperator] : [],
        );
        setSelectedOperator(newOperators[0] || null);
      } else {
        setSelectedOperator(operators[0] || null);
      }
    },
    checkedOperators,
    setCheckedOperators,
    checkedRequests,
    setCheckedRequests,
    confirmedMatches,
    setConfirmedMatches,
    pendingAssignments,
    setPendingAssignments: () => {}, // No-op for compatibility
    currentSelectionLabel,
    setCurrentSelectionLabel: () => {}, // No-op for compatibility

    // Helper functions
    getNextLabel: () => "A", // Simplified since we only have single selection
    getPendingAssignmentForRequest,
    getPendingAssignmentForOperator,
    handleSelectOperator,
    handleAddToPendingAssignments,
    handleCheckOperator,
    handleCheckRequest,
    isWorkstationHighlighted,
    isWorkstationMatched,
    isWorkstationInPendingAssignments,
    isOperatorMatched,
    clearAllSelections,
    removePendingAssignment,
  };
}
