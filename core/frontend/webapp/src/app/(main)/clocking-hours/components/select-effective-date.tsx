"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Save } from "lucide-react";
import type { DateRange } from "react-day-picker";
import { useTranslations } from "next-intl";
import { IconButton } from "@/components/common/CustomButtons";
import { useScheduleStore } from "../store/scheduleStore";

interface SelectEffectiveDateModalProps {
  open: boolean;
  onClose: () => void;
  selectedScheduleTypeId?: string;
}

export default function SelectEffectiveDateModal({
  open,
  onClose,
  selectedScheduleTypeId,
}: SelectEffectiveDateModalProps) {
  const t = useTranslations("clocking");
  const {
    availableScheduleTypes,
    setEffectiveDateRange,
    updateScheduleTypeEffectiveDates,
    isUpdatingScheduleType,
  } = useScheduleStore();

  const [range, setRange] = useState<DateRange | undefined>();

  useEffect(() => {
    if (open && selectedScheduleTypeId) {
      const selectedType = availableScheduleTypes.find(
        (type) => type.id === selectedScheduleTypeId,
      );
      if (selectedType?.effectiveStartDate && selectedType?.effectiveEndDate) {
        setRange({
          from: new Date(selectedType.effectiveStartDate),
          to: new Date(selectedType.effectiveEndDate),
        });
      } else {
        setRange(undefined);
      }
    } else if (!open) {
      setRange(undefined);
    }
  }, [open, selectedScheduleTypeId, availableScheduleTypes]);

  const handleSave = async () => {
    if (range?.from && range?.to && selectedScheduleTypeId) {
      try {
        // Call the new store action to update the schedule type's effective dates
        await updateScheduleTypeEffectiveDates(
          selectedScheduleTypeId,
          range.from,
          range.to,
        );
        setEffectiveDateRange(range.from, range.to); // Update local store state for effective date range
        onClose();
      } catch (error) {
        // Error handling is already in the store action, but can add more here if needed
        console.error("Error saving effective dates:", error);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] p-0 rounded-xl">
        <div className="flex flex-col items-center w-full">
          <DialogHeader className="w-full border-b px-6 pt-6 pb-2">
            <DialogTitle className="text-lg font-semibold text-left w-full">
              Select Effective Date
            </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center w-full px-6 pt-6">
            <div className="w-full flex flex-col items-center">
              <Calendar
                mode="range"
                selected={range}
                onSelect={(value) => setRange(value as DateRange)}
                numberOfMonths={2}
                className="border-none rounded-none w-full"
                classNames={{
                  months: "flex flex-row gap-8 justify-center w-full",
                  month: "w-full",
                  caption: "flex justify-center items-center mb-2",
                  nav: "flex items-center gap-2",
                  table: "w-full border-collapse",
                  head_row: "flex",
                  head_cell:
                    "text-muted-foreground w-8 font-medium text-xs py-2",
                  row: "flex w-full",
                  cell: "p-0 text-center text-sm w-8 h-8",
                  day: "h-8 w-8 p-0 font-medium rounded transition-colors duration-200",
                  day_range_start: "bg-black text-white font-bold shadow-md",
                  day_range_end: "bg-black text-white font-bold shadow-md",
                  day_selected: "",
                  day_range_middle: "bg-[#F2F2F3] text-black",
                  day_today: "border border-black",
                  day_outside: "text-gray-300",
                }}
              />
            </div>
          </div>
          <div className="flex justify-between items-center w-full px-6 pb-6 pt-4 mt-2">
            <Button
              variant="outline"
              onClick={onClose}
              className="border-gray-300 w-28 bg-transparent"
            >
              Cancel
            </Button>
            <IconButton
              icon={() => <Save className="mr-2 h-4 w-4" />}
              label={isUpdatingScheduleType ? "Saving..." : t("save")}
              color="default"
              type="button"
              variant="default"
              id="save-ramadan-dates"
              onClick={handleSave}
              className="border border-black flex items-center gap-2"
              disabled={
                !range?.from ||
                !range?.to ||
                !selectedScheduleTypeId ||
                isUpdatingScheduleType
              }
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
