"use client";

import type React from "react";
import { CreateScheduleModal } from "./CreateScheduleModal";
import CustomIcon from "@/components/common/CustomIcons";
import { IconButton } from "@/components/common/CustomButtons";
import { useTranslations } from "next-intl";
import { useScheduleStore } from "../store/scheduleStore";

export const WorkingHoursHeader = () => {
  const {
    openImportModal,
    isCreateScheduleModalOpen,
    openCreateScheduleModal,
    closeCreateScheduleModal,
    downloadCanvas,
    isDownloadingCanvas,
  } = useScheduleStore();
  const t = useTranslations("clocking");

  const handleDownloadCanvas = async () => {
    try {
      await downloadCanvas();
    } catch (error) {
      console.error("Error downloading canvas:", error);
    }
  };

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full flex items-center justify-center">
            <CustomIcon name="clockhourWeekIcon" />
          </div>
          <h1 className="text-xl font-semibold text-foreground">
            Working hours and TKS profile
          </h1>
        </div>
        <div className="flex gap-3">
          <IconButton
            icon={() => (
              <CustomIcon
                name="addEvent"
                style={{
                  width: "1rem",
                  height: "1rem",
                }}
              />
            )}
            label={t("addSchedule")}
            color="default"
            type="button"
            variant="outline"
            id="add-schedule-button"
            onClick={openCreateScheduleModal}
            className="border border-black flex items-center gap-2"
          />
          <IconButton
            icon={() => (
              <CustomIcon
                name="importRightIcon"
                style={{
                  width: "1rem",
                  height: "1rem",
                }}
              />
            )}
            label={t("import")}
            color="default"
            type="button"
            variant="outline"
            id="import-schedule-button"
            onClick={() => openImportModal()}
            className="border border-black bg-black text-white hover:bg-black hover:text-white flex items-center gap-2"
          />
          <IconButton
            icon={() => (
              <CustomIcon
                name="export"
                style={{
                  width: "1rem",
                  height: "1rem",
                }}
              />
            )}
            label={
              isDownloadingCanvas ? t("downloading") : t("downloadTemplate")
            }
            color="default"
            type="button"
            variant="outline"
            id="download-template-button"
            onClick={handleDownloadCanvas}
            disabled={isDownloadingCanvas}
            className="border border-gray-300 bg-green-500 text-white hover:bg-green-600 hover:text-white flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          />
        </div>
      </div>

      {/* Modal Component */}
      <CreateScheduleModal
        isOpen={isCreateScheduleModalOpen}
        onClose={closeCreateScheduleModal}
      />
    </>
  );
};
