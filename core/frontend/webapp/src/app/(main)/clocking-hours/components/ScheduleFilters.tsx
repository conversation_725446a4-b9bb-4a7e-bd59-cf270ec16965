"use client";

import type React from "react";
import { useState, useMemo, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CalendarDays } from "lucide-react";
import SelectEffectiveDateModal from "./select-effective-date";
import { IconButton } from "@/components/common/CustomButtons";
import { useTranslations } from "next-intl";
import { useScheduleStore } from "../store/scheduleStore"; // Import useScheduleStore
import CustomMultiSelect from "@/components/common/CustomMultiSelect";
import { WorkDayOfWeek } from "../types/clockingHoursTypes";

interface ScheduleFiltersProps {
  filterDaysOfWeek: WorkDayOfWeek[];
  setFilterDaysOfWeek: (days: WorkDayOfWeek[]) => void;
  filterWorkHours: number | undefined;
  setFilterWorkHours: (hours: number | undefined) => void;
  filterCode: string;
  setFilterCode: (code: string) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  activeTabId: string;
}

export const ScheduleFilters: React.FC<ScheduleFiltersProps> = ({
  filterDaysOfWeek,
  setFilterDaysOfWeek,
  filterWorkHours,
  setFilterWorkHours,
  filterCode,
  setFilterCode,
  onApplyFilters,
  onClearFilters,
  activeTabId,
}) => {
  const t = useTranslations("clocking");
  const [isRamadanModalOpen, setIsRamadanModalOpen] = useState(false);
  const [isCleared, setIsCleared] = useState(false);

  const { workingHoursActivated } = useScheduleStore();

  // Use ref to store the latest callback without causing re-renders
  const onApplyFiltersRef = useRef(onApplyFilters);

  // Update the ref whenever the callback changes
  useEffect(() => {
    onApplyFiltersRef.current = onApplyFilters;
  }, [onApplyFilters]);

  const allDays = useMemo(
    () => [
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
      WorkDayOfWeek.WEDNESDAY,
      WorkDayOfWeek.THURSDAY,
      WorkDayOfWeek.FRIDAY,
      WorkDayOfWeek.SATURDAY,
      WorkDayOfWeek.SUNDAY,
    ],
    [],
  );

  const dayOptions = useMemo(() => {
    return allDays.map((day) => ({
      label: t(`daysOfWeek.${day.toLowerCase()}`),
      value: day,
    }));
  }, [allDays, t]);

  const handleWorkHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFilterWorkHours(value === "" ? undefined : Number(value));
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterCode(e.target.value);
  };

  const handleDayFilterChange = (selectedValues: string[]) => {
    setFilterDaysOfWeek(selectedValues as WorkDayOfWeek[]);
    setIsCleared(false);
  };

  const handleClearFilters = () => {
    setFilterCode("");
    setFilterDaysOfWeek([] as WorkDayOfWeek[]);
    setIsCleared(true);
    setFilterWorkHours(undefined);
    onClearFilters();
  };

  const hasActiveFilters =
    filterCode.trim() ||
    filterDaysOfWeek.length > 0 ||
    filterWorkHours !== undefined;

  useEffect(() => {
    // Perform side effect when hasActiveFilters changes
    if (!hasActiveFilters) {
      // Use the ref to call the latest callback without causing re-renders
      onApplyFiltersRef.current();
    }
  }, [hasActiveFilters]); // Only depend on hasActiveFilters

  return (
    <div className="flex mb-6 items-end justify-between w-full">
      <div className="flex gap-4 items-end">
        <div className="flex flex-col gap-1">
          <Label className="text-sm text-muted-foreground">
            Filter by Code :
          </Label>
          <Input
            placeholder="Code"
            className="w-40"
            value={filterCode}
            onChange={handleCodeChange}
          />
        </div>
        <div className="flex flex-col gap-1">
          <Label className="text-sm text-muted-foreground">
            Filter by Day :
          </Label>
          <CustomMultiSelect
            options={dayOptions}
            defaultValue={filterDaysOfWeek}
            onValueChange={handleDayFilterChange}
            placeholder="All days"
            className="w-48"
            isCleared={isCleared}
          />
        </div>
        <div className="flex flex-col gap-1">
          <Label className="text-sm text-muted-foreground">
            Filter by Work Hours :
          </Label>
          <div className="relative w-40">
            <Input
              type="number"
              value={filterWorkHours === undefined ? "" : filterWorkHours}
              onChange={handleWorkHoursChange}
              step="0.1"
              placeholder="e.g., 8.5"
            />
          </div>
        </div>{" "}
        <Button
          variant="secondary"
          disabled={!hasActiveFilters}
          className={`${
            hasActiveFilters
              ? "bg-blue-500 text-white hover:bg-blue-600"
              : "bg-gray-300 text-gray-700"
          } transition-colors`}
          onClick={onApplyFilters}
        >
          Filter {hasActiveFilters && "●"}
        </Button>
        <Button
          variant="outline"
          className="border-gray-300 text-gray-700 hover:bg-gray-100 bg-transparent"
          onClick={handleClearFilters}
          disabled={!hasActiveFilters}
        >
          Clear
        </Button>
      </div>
      <div className="flex gap-2 items-end justify-end w-full">
        <IconButton
          icon={() => <CalendarDays className="mr-2 h-4 w-4" />}
          label={t("EffectiveDate")}
          color="default"
          type="button"
          variant="default"
          id="import-schedule-button"
          onClick={() => setIsRamadanModalOpen(true)}
          className="border border-black flex items-center gap-2"
        />
        <Button
          variant={workingHoursActivated ? "default" : "secondary"}
          className={
            workingHoursActivated
              ? "bg-green-500 text-white hover:bg-green-600"
              : "bg-gray-300 text-gray-700"
          }
        >
          {workingHoursActivated
            ? "✓ Activate Working hours"
            : "Working hours activated"}
        </Button>
      </div>
      <div>
        <SelectEffectiveDateModal
          open={isRamadanModalOpen}
          onClose={() => setIsRamadanModalOpen(false)}
          selectedScheduleTypeId={activeTabId}
        />
      </div>
    </div>
  );
};
