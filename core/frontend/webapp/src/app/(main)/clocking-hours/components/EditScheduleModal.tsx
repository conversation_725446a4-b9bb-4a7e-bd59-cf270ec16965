"use client";

import type React from "react";
import { useState, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CalendarDays, Clock } from "lucide-react";
import { useScheduleStore } from "../store/scheduleStore";
import CustomIcon from "@/components/common/CustomIcons";
import {
  WorkDayOfWeek,
  type WorkScheduleRequestDto,
  type WorkScheduleResponseDto,
} from "../types/clockingHoursTypes";
import { useTranslations } from "next-intl";

interface EditScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: WorkScheduleResponseDto;
}

export const EditScheduleModal: React.FC<EditScheduleModalProps> = ({
  isOpen,
  onClose,
  item,
}) => {
  const t = useTranslations("clocking");
  const { updateSchedule, availableScheduleTypes, isUpdating } =
    useScheduleStore();

  // Local state for selected schedule type - decoupled from global activeTabId
  const [localScheduleTypeId, setLocalScheduleTypeId] = useState<string>(
    item.scheduleTypeId,
  );
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [formData, setFormData] = useState<WorkScheduleRequestDto>({
    profile: "",
    code: "",
    designation: "",
    entryTime: "",
    exitTime: "",
    breakDurationMinutes: 0,
    daysOfWeek: [],
    scheduleTypeId: "",
    workHours: 0,
  });

  const days = useMemo(
    () => [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ],
    [],
  );

  const workDayOfWeekToDisplay = useMemo(() => {
    const map: Record<WorkDayOfWeek, string> = {
      [WorkDayOfWeek.MONDAY]: "Monday",
      [WorkDayOfWeek.TUESDAY]: "Tuesday",
      [WorkDayOfWeek.WEDNESDAY]: "Wednesday",
      [WorkDayOfWeek.THURSDAY]: "Thursday",
      [WorkDayOfWeek.FRIDAY]: "Friday",
      [WorkDayOfWeek.SATURDAY]: "Saturday",
      [WorkDayOfWeek.SUNDAY]: "Sunday",
    };
    return map;
  }, []);

  const displayToWorkDayOfWeek = useMemo(() => {
    const map: Record<string, WorkDayOfWeek> = {
      Monday: WorkDayOfWeek.MONDAY,
      Tuesday: WorkDayOfWeek.TUESDAY,
      Wednesday: WorkDayOfWeek.WEDNESDAY,
      Thursday: WorkDayOfWeek.THURSDAY,
      Friday: WorkDayOfWeek.FRIDAY,
      Saturday: WorkDayOfWeek.SATURDAY,
      Sunday: WorkDayOfWeek.SUNDAY,
    };
    return map;
  }, []);

  // Initialize form with item data when modal opens or item changes
  useEffect(() => {
    if (isOpen && item) {
      setLocalScheduleTypeId(item.scheduleTypeId);
      setFormData({
        profile: item.profile,
        code: item.code,
        designation: item.designation,
        entryTime: item.entryTime,
        exitTime: item.exitTime,
        breakDurationMinutes: item.breakDurationMinutes,
        daysOfWeek: item.daysOfWeek,
        scheduleTypeId: item.scheduleTypeId,
        workHours: item.workHours,
      });

      // Convert WorkDayOfWeek enum values to display day names
      const selectedDayNames = item.daysOfWeek.map(
        (day) => workDayOfWeekToDisplay[day],
      );
      setSelectedDays(selectedDayNames);
    }
  }, [isOpen, item, workDayOfWeekToDisplay]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setLocalScheduleTypeId("");
      setSelectedDays([]);
      setFormData({
        profile: "",
        code: "",
        designation: "",
        entryTime: "",
        exitTime: "",
        breakDurationMinutes: 0,
        daysOfWeek: [],
        scheduleTypeId: "",
        workHours: 0,
      });
    }
  }, [isOpen]);

  const toggleDay = (day: string) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day],
    );
  };

  const handleInputChange = (
    field: keyof WorkScheduleRequestDto,
    value: string,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleNumberInputChange = (
    field: keyof WorkScheduleRequestDto,
    value: string,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: Number(value) }));
  };

  const handleRadioChange = (value: string) => {
    // Only update local state and form data, not the global activeTabId
    setLocalScheduleTypeId(value);
    setFormData((prev) => ({ ...prev, scheduleTypeId: value }));
  };

  const handleSave = async () => {
    try {
      const daysOfWeekEnum = selectedDays.map(
        (day) => displayToWorkDayOfWeek[day],
      );

      const updatedSchedule: WorkScheduleRequestDto = {
        ...formData,
        daysOfWeek: daysOfWeekEnum,
      };

      await updateSchedule(item.id, updatedSchedule);
      onClose();
    } catch (error) {
      console.error("Error updating schedule:", error);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[650px]">
        <DialogHeader>
          <DialogTitle>{t("editProfil")}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Icon */}
          <div className="flex justify-center">
            <div className="flex items-center justify-center">
              <CustomIcon
                name="clockSetting"
                className="w-6 h-6 text-blue-500"
              />
            </div>
          </div>

          {/* Mode Selection */}
          <RadioGroup
            value={localScheduleTypeId}
            onValueChange={handleRadioChange}
            className="flex gap-8"
          >
            {availableScheduleTypes.map((type) => (
              <div key={type.id} className="flex items-center space-x-3">
                <RadioGroupItem
                  value={type.id}
                  id={`edit-${type.id}`}
                  className="border-2 border-gray-300 bg-white text-blue-500 focus:ring-blue-500 data-[state=checked]:bg-white data-[state=checked]:border-blue-500 data-[state=checked]:text-blue-500 [&>span>span>svg]:fill-white"
                />
                <Label
                  htmlFor={`edit-${type.id}`}
                  className="text-base font-medium text-gray-700 cursor-pointer flex items-center gap-2"
                >
                  {type.name === "Normal day" && "Normal day"}
                  {type.name === "Reda day" && "Reda day"}
                  {type.name === "Ramadan" && (
                    <>
                      <CustomIcon name="ramadane" /> Ramadan
                    </>
                  )}
                  {!["Normal day", "Reda day", "Ramadan"].includes(type.name) &&
                    type.name}
                </Label>
              </div>
            ))}
          </RadioGroup>

          {/* Form Fields */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label
                htmlFor="edit-profile"
                className="text-sm text-muted-foreground"
              >
                {t("profile")} :
              </Label>
              <Input
                id="edit-profile"
                placeholder={t("profile")}
                value={formData.profile}
                onChange={(e) => handleInputChange("profile", e.target.value)}
              />
            </div>
            <div>
              <Label
                htmlFor="edit-code"
                className="text-sm text-muted-foreground"
              >
                {t("code")} :
              </Label>
              <Input
                id="edit-code"
                placeholder={t("code")}
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
              />
            </div>
            <div>
              <Label
                htmlFor="edit-designation"
                className="text-sm text-muted-foreground"
              >
                {t("designation")} :
              </Label>
              <Input
                id="edit-designation"
                placeholder={t("designation")}
                value={formData.designation}
                onChange={(e) =>
                  handleInputChange("designation", e.target.value)
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label
                htmlFor="edit-timeEntry"
                className="text-sm text-muted-foreground"
              >
                {t("timeEntry")} :
              </Label>
              <div className="relative">
                <Input
                  id="edit-timeEntry"
                  type="time"
                  className="pr-10
                  [&::-webkit-calendar-picker-indicator]:opacity-0
                  [&::-webkit-calendar-picker-indicator]:absolute
                  [&::-webkit-calendar-picker-indicator]:right-0
                  [&::-webkit-calendar-picker-indicator]:z-10"
                  value={formData.entryTime}
                  onChange={(e) =>
                    handleInputChange("entryTime", e.target.value)
                  }
                />
                <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>
            <div>
              <Label
                htmlFor="edit-timeExit"
                className="text-sm text-muted-foreground"
              >
                {t("timeExit")} :
              </Label>
              <div className="relative">
                <Input
                  id="edit-timeExit"
                  type="time"
                  className="pr-10
                  [&::-webkit-calendar-picker-indicator]:opacity-0
                  [&::-webkit-calendar-picker-indicator]:absolute
                  [&::-webkit-calendar-picker-indicator]:right-0
                  [&::-webkit-calendar-picker-indicator]:z-10"
                  value={formData.exitTime}
                  onChange={(e) =>
                    handleInputChange("exitTime", e.target.value)
                  }
                />
                <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>
            <div>
              <Label
                htmlFor="edit-breakDuration"
                className="text-sm text-muted-foreground"
              >
                {t("break")} (Min) :
              </Label>
              <Input
                id="edit-breakDuration"
                type="number"
                placeholder={t("break")}
                min="0"
                max="120"
                step="5"
                value={formData.breakDurationMinutes}
                onChange={(e) =>
                  handleNumberInputChange(
                    "breakDurationMinutes",
                    e.target.value,
                  )
                }
              />
            </div>
          </div>

          {/* Work Hours Input */}
          <div>
            <Label
              htmlFor="edit-workHours"
              className="text-sm text-muted-foreground"
            >
              {t("workHours")} :
            </Label>
            <Input
              id="edit-workHours"
              type="number"
              placeholder="e.g., 8.5"
              min="0"
              step="0.01"
              value={formData.workHours}
              onChange={(e) =>
                handleNumberInputChange("workHours", e.target.value)
              }
            />
          </div>

          {/* Days Selection */}
          <div>
            <Label className="text-sm text-muted-foreground mb-3 block">
              {t("day")} :
            </Label>
            <div className="flex gap-2 flex-wrap">
              {days.map((day) => (
                <Button
                  key={day}
                  variant={selectedDays.includes(day) ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleDay(day)}
                  className={`rounded-full ${selectedDays.includes(day) ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground"}`}
                >
                  {day}
                </Button>
              ))}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isUpdating}
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={handleSave}
              disabled={isUpdating}
            >
              <CalendarDays className="w-4 h-4 mr-2" />
              {isUpdating ? t("updating") : t("updateEvent")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
