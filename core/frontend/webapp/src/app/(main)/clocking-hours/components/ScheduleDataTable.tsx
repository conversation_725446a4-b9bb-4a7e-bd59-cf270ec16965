"use client";

import React, { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Edit, Trash2, MoreVertical } from "lucide-react";
import { useScheduleStore } from "../store/scheduleStore";
import { EditScheduleModal } from "./EditScheduleModal";
import { useTranslations } from "next-intl";
import type {
  ScheduleTypeDto,
  WorkScheduleResponseDto,
} from "../types/clockingHoursTypes";

interface ScheduleDataTableProps {
  data: WorkScheduleResponseDto[];
  scheduleType: ScheduleTypeDto;
}

function groupByProfileAndDesignation(data: WorkScheduleResponseDto[]) {
  const profileMap = new Map<string, Map<string, WorkScheduleResponseDto[]>>();

  for (const item of data) {
    if (!profileMap.has(item.profile)) profileMap.set(item.profile, new Map());
    const designationMap = profileMap.get(item.profile)!;
    if (!designationMap.has(item.designation))
      designationMap.set(item.designation, []);
    designationMap.get(item.designation)!.push(item);
  }

  return Array.from(profileMap.entries()).map(([profile, designations]) => ({
    profile,
    designations: Array.from(designations.entries()).map(
      ([designation, rows]) => ({
        designation,
        rows,
      }),
    ),
  }));
}

export const ScheduleDataTable: React.FC<ScheduleDataTableProps> = ({
  data,
}) => {
  const t = useTranslations("clocking");
  const { deleteSchedule, isDeleting } = useScheduleStore();

  const [editingItem, setEditingItem] =
    useState<WorkScheduleResponseDto | null>(null);
  const [deletingItem, setDeletingItem] =
    useState<WorkScheduleResponseDto | null>(null);

  const groupedData = useMemo(() => groupByProfileAndDesignation(data), [data]);

  const confirmDelete = async () => {
    if (deletingItem) {
      try {
        await deleteSchedule(deletingItem.id);
        setDeletingItem(null);
      } catch (error) {
        console.error("Error deleting schedule:", error);
      }
    }
  };

  return (
    <div className="overflow-x-auto rounded-lg border">
      <table className="min-w-full table-auto">
        <thead className="bg-gray-100">
          <tr>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("profile")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("code")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("designation")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("timeEntry")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("timeExit")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("day")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("break")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b border-r">
              {t("workHours")}
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-500 tracking-wider border-b">
              {t("actions")}
            </th>
          </tr>
        </thead>
        <tbody>
          {groupedData.map((group, groupIdx) => {
            const profileRowSpan = group.designations.reduce(
              (acc, des) => acc + des.rows.length,
              0,
            );
            return (
              <React.Fragment key={group.profile}>
                {group.designations.map((designationGroup, dIdx) => {
                  const desRowSpan = designationGroup.rows.length;
                  return designationGroup.rows.map((row, rIdx) => (
                    <tr
                      key={row.id}
                      className={`border-b last:border-b-0 ${
                        (groupIdx % 2 === 0 && dIdx === 0 && rIdx === 0) ||
                        (groupIdx % 2 !== 0 && dIdx === 0 && rIdx === 0)
                          ? "bg-white"
                          : "bg-gray-50"
                      }`}
                    >
                      {dIdx === 0 && rIdx === 0 && (
                        <td
                          rowSpan={profileRowSpan}
                          className="p-3 border-r align-middle font-semibold text-gray-700 text-center"
                        >
                          {group.profile}
                        </td>
                      )}
                      <td className="p-3 border-r text-gray-800">{row.code}</td>
                      {rIdx === 0 && (
                        <td
                          rowSpan={desRowSpan}
                          className="p-3 border-r align-middle font-medium text-gray-700 text-center"
                        >
                          {designationGroup.designation}
                        </td>
                      )}
                      <td className="p-3 border-r text-gray-800">
                        {row.entryTime}
                      </td>
                      <td className="p-3 border-r text-gray-800">
                        {row.exitTime}
                      </td>
                      <td className="p-3 border-r text-gray-800">
                        {row.daysOfWeek.join(", ")}
                      </td>
                      <td className="p-3 border-r text-gray-800">
                        {(() => {
                          const h = Math.floor(row.breakDurationMinutes / 60);
                          const m = row.breakDurationMinutes % 60;
                          return h ? `${h}h ${m ? m + "min" : ""}` : `${m}min`;
                        })()}
                      </td>
                      <td className="p-3 border-r text-gray-800">
                        {row.workHours}h
                      </td>
                      <td className="p-3 text-gray-800">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="h-4 w-4 text-gray-500" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => setEditingItem(row)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              {t("edit")}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => setDeletingItem(row)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t("delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ));
                })}
              </React.Fragment>
            );
          })}
        </tbody>
      </table>

      {editingItem && (
        <EditScheduleModal
          isOpen={!!editingItem}
          onClose={() => setEditingItem(null)}
          item={editingItem}
        />
      )}

      <AlertDialog
        open={!!deletingItem}
        onOpenChange={() => setDeletingItem(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("confirmDeleteTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("confirmDeleteMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeletingItem(null)}>
              {t("cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 disabled:opacity-50"
            >
              {isDeleting ? t("deleting") : t("delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
