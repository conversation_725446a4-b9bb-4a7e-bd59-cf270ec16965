import {
  type WorkScheduleResponseDto,
  WorkDayOfWeek,
  type ScheduleTypeDto,
} from "../../types/clockingHoursTypes";

export const dummyScheduleTypes: ScheduleTypeDto[] = [
  {
    id: "normal-day-id",
    name: "Normal day",
    description: "Standard working hours schedule",
    isActive: true,
    country: "US",
    displayOrder: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "ramadan-id",
    name: "Ramadan",
    description: "Reduced working hours during Ramadan",
    isActive: true,
    country: "US",
    displayOrder: 2,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "reda-id",
    name: "Reda day",
    description: "Special Reda day schedule",
    isActive: true,
    country: "US",
    displayOrder: 3,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const dummySchedules: WorkScheduleResponseDto[] = [
  {
    id: "1",
    profile: "Profile A",
    code: "CODE001",
    designation: "Engineer",
    entryTime: "08:00",
    exitTime: "17:00",
    breakDurationMinutes: 60,
    daysOfWeek: [
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
      WorkDayOfWeek.WEDNESDAY,
      WorkDayOfWeek.THURSDAY,
      WorkDayOfWeek.FRIDAY,
    ],
    scheduleTypeId: "normal-day-id",
    workHours: 8,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "2",
    profile: "Profile A",
    code: "CODE001",
    designation: "Senior Engineer",
    entryTime: "09:00",
    exitTime: "18:00",
    breakDurationMinutes: 60,
    daysOfWeek: [
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
      WorkDayOfWeek.WEDNESDAY,
      WorkDayOfWeek.THURSDAY,
      WorkDayOfWeek.FRIDAY,
    ],
    scheduleTypeId: "normal-day-id",
    workHours: 8,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "3",
    profile: "Profile B",
    code: "CODE002",
    designation: "Manager",
    entryTime: "07:30",
    exitTime: "16:30",
    breakDurationMinutes: 30,
    daysOfWeek: [
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
      WorkDayOfWeek.WEDNESDAY,
      WorkDayOfWeek.THURSDAY,
      WorkDayOfWeek.FRIDAY,
    ],
    scheduleTypeId: "normal-day-id",
    workHours: 8.5,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "4",
    profile: "Profile C",
    code: "CODE003",
    designation: "Technician",
    entryTime: "06:00",
    exitTime: "14:00",
    breakDurationMinutes: 45,
    daysOfWeek: [WorkDayOfWeek.SATURDAY, WorkDayOfWeek.SUNDAY],
    scheduleTypeId: "normal-day-id",
    workHours: 7.25,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "5",
    profile: "Ramadan Profile X",
    code: "RAM001",
    designation: "Engineer",
    entryTime: "09:00",
    exitTime: "15:00",
    breakDurationMinutes: 30,
    daysOfWeek: [
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
      WorkDayOfWeek.WEDNESDAY,
      WorkDayOfWeek.THURSDAY,
      WorkDayOfWeek.FRIDAY,
    ],
    scheduleTypeId: "ramadan-id",
    workHours: 5.5,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "6",
    profile: "Ramadan Profile Y",
    code: "RAM002",
    designation: "Manager",
    entryTime: "10:00",
    exitTime: "16:00",
    breakDurationMinutes: 30,
    daysOfWeek: [
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
      WorkDayOfWeek.WEDNESDAY,
      WorkDayOfWeek.THURSDAY,
      WorkDayOfWeek.FRIDAY,
    ],
    scheduleTypeId: "ramadan-id",
    workHours: 5.5,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];
