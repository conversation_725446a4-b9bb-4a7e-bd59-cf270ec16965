"use client";

import type React from "react";
import { useState, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CalendarDays, Clock } from "lucide-react";
import { useScheduleStore } from "../store/scheduleStore";
import CustomIcon from "@/components/common/CustomIcons";
import {
  WorkDayOfWeek,
  type WorkScheduleRequestDto,
} from "../types/clockingHoursTypes"; // Import new types
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CreateScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreateScheduleModal: React.FC<CreateScheduleModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { addSchedule, availableScheduleTypes, activeTabId, isCreating } =
    useScheduleStore();

  // Local state for selected schedule type - decoupled from global activeTabId
  const [localScheduleTypeId, setLocalScheduleTypeId] = useState<string>("");

  const [selectedDays, setSelectedDays] = useState<string[]>([
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
  ]);
  const [formData, setFormData] = useState<WorkScheduleRequestDto>({
    profile: "",
    code: "",
    designation: "",
    entryTime: "",
    exitTime: "",
    breakDurationMinutes: 0,
    daysOfWeek: [],
    scheduleTypeId: "",
    workHours: 0,
  });

  const days = useMemo(
    () => [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ],
    [],
  );

  const displayToWorkDayOfWeek = useMemo(() => {
    const map: Record<string, WorkDayOfWeek> = {
      Monday: WorkDayOfWeek.MONDAY,
      Tuesday: WorkDayOfWeek.TUESDAY,
      Wednesday: WorkDayOfWeek.WEDNESDAY,
      Thursday: WorkDayOfWeek.THURSDAY,
      Friday: WorkDayOfWeek.FRIDAY,
      Saturday: WorkDayOfWeek.SATURDAY,
      Sunday: WorkDayOfWeek.SUNDAY,
    };
    return map;
  }, []);

  // Initialize local schedule type ID when modal opens or available types change
  useEffect(() => {
    if (isOpen && availableScheduleTypes.length > 0) {
      // Initialize with activeTabId or default to first available type
      const initialTypeId = activeTabId || availableScheduleTypes[0].id;
      setLocalScheduleTypeId(initialTypeId);
      setFormData((prev) => ({ ...prev, scheduleTypeId: initialTypeId }));
    }
  }, [isOpen, availableScheduleTypes, activeTabId]);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      const initialTypeId = activeTabId || availableScheduleTypes[0]?.id || "";
      setLocalScheduleTypeId(initialTypeId);

      setFormData({
        profile: "",
        code: "",
        designation: "",
        entryTime: "",
        exitTime: "",
        breakDurationMinutes: 0,
        daysOfWeek: [],
        scheduleTypeId: initialTypeId,
        workHours: 0,
      });
      setSelectedDays(["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]);
    }
  }, [isOpen, activeTabId, availableScheduleTypes]);

  const toggleDay = (day: string) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day],
    );
  };

  const handleInputChange = (
    field: keyof WorkScheduleRequestDto,
    value: string,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleNumberInputChange = (
    field: keyof WorkScheduleRequestDto,
    value: string,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: Number(value) }));
  };

  const handleRadioChange = (value: string) => {
    // Only update local state and form data, not the global activeTabId
    setLocalScheduleTypeId(value);
    setFormData((prev) => ({ ...prev, scheduleTypeId: value }));
  };

  const handleSave = async () => {
    try {
      const daysOfWeekEnum = selectedDays.map(
        (day) => displayToWorkDayOfWeek[day],
      );

      const newSchedule: WorkScheduleRequestDto = {
        profile: formData.profile,
        code: formData.code,
        designation: formData.designation,
        entryTime: formData.entryTime,
        exitTime: formData.exitTime,
        breakDurationMinutes: formData.breakDurationMinutes,
        daysOfWeek: daysOfWeekEnum,
        scheduleTypeId: formData.scheduleTypeId,
        workHours: formData.workHours,
      };

      await addSchedule(newSchedule);
      onClose();
    } catch (error) {
      console.error("Error saving schedule:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[650px]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Create new profile
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Icon */}
          <div className="flex justify-center">
            <div className="flex items-center justify-center">
              <CustomIcon
                name="clockSetting"
                className="w-6 h-6 text-blue-500"
              />
            </div>
          </div>

          {/* Mode Selection */}
          <RadioGroup
            value={localScheduleTypeId}
            onValueChange={handleRadioChange}
            className="flex gap-8"
          >
            {availableScheduleTypes.map((type) => (
              <div key={type.id} className="flex items-center space-x-3">
                <RadioGroupItem
                  value={type.id}
                  id={type.id}
                  className="border-2 border-gray-300 bg-white text-blue-500 focus:ring-blue-500 data-[state=checked]:bg-white data-[state=checked]:border-blue-500 data-[state=checked]:text-blue-500 [&>span>span>svg]:fill-white"
                />
                <Label
                  htmlFor={type.id}
                  className="text-base font-medium text-gray-700 cursor-pointer flex items-center gap-2"
                >
                  {type.name === "Normal day" && "Normal day"}
                  {type.name === "Reda day" && "Reda day"}
                  {type.name === "Ramadan" && (
                    <>
                      <CustomIcon name="ramadane" /> Ramadan
                    </>
                  )}
                  {!["Normal day", "Reda day", "Ramadan"].includes(type.name) &&
                    type.name}
                </Label>
              </div>
            ))}
          </RadioGroup>

          {/* Form Fields */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label
                htmlFor="profile"
                className="text-sm text-muted-foreground"
              >
                Profile :
              </Label>
              <Input
                id="profile"
                placeholder="Profile"
                value={formData.profile}
                onChange={(e) => handleInputChange("profile", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="code" className="text-sm text-muted-foreground">
                Code :
              </Label>
              <Input
                id="code"
                placeholder="Code"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
              />
            </div>
            <div>
              <Label
                htmlFor="designation"
                className="text-sm text-muted-foreground"
              >
                Designation :
              </Label>
              <Input
                id="designation"
                placeholder="Designation"
                value={formData.designation}
                onChange={(e) =>
                  handleInputChange("designation", e.target.value)
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label
                htmlFor="timeEntry"
                className="text-sm text-muted-foreground"
              >
                Time of entry :
              </Label>
              <div className="relative">
                <Input
                  id="timeEntry"
                  type="time"
                  className="pr-10
                  [&::-webkit-calendar-picker-indicator]:opacity-0
                  [&::-webkit-calendar-picker-indicator]:absolute
                  [&::-webkit-calendar-picker-indicator]:right-0
                  [&::-webkit-calendar-picker-indicator]:z-10"
                  value={formData.entryTime}
                  onChange={(e) =>
                    handleInputChange("entryTime", e.target.value)
                  }
                />
                <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>
            <div>
              <Label
                htmlFor="timeExit"
                className="text-sm text-muted-foreground"
              >
                Exit Time :
              </Label>
              <div className="relative">
                <Input
                  id="timeExit"
                  type="time"
                  className="pr-10
                  [&::-webkit-calendar-picker-indicator]:opacity-0
                  [&::-webkit-calendar-picker-indicator]:absolute
                  [&::-webkit-calendar-picker-indicator]:right-0
                  [&::-webkit-calendar-picker-indicator]:z-10"
                  value={formData.exitTime}
                  onChange={(e) =>
                    handleInputChange("exitTime", e.target.value)
                  }
                />
                <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>
            <div>
              <Label
                htmlFor="breakDuration"
                className="text-sm text-muted-foreground"
              >
                Break Duration (Min) :
              </Label>
              <Input
                id="breakDuration"
                type="number"
                placeholder="Break Duration"
                min="0"
                max="120"
                step="5"
                value={formData.breakDurationMinutes}
                onChange={(e) =>
                  handleNumberInputChange(
                    "breakDurationMinutes",
                    e.target.value,
                  )
                }
              />
            </div>
          </div>

          {/* Work Hours Input */}
          <div>
            <Label
              htmlFor="workHours"
              className="text-sm text-muted-foreground"
            >
              Work Hours :
            </Label>
            <Input
              id="workHours"
              type="number"
              placeholder="e.g., 8.5"
              min="0"
              step="0.01"
              value={formData.workHours}
              onChange={(e) =>
                handleNumberInputChange("workHours", e.target.value)
              }
            />
          </div>

          {/* Days Selection */}
          <div>
            <Label className="text-sm text-muted-foreground mb-3 block">
              Days :
            </Label>
            <div className="flex gap-2 flex-wrap">
              {days.map((day) => (
                <Button
                  key={day}
                  variant={selectedDays.includes(day) ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleDay(day)}
                  className={`rounded-full ${selectedDays.includes(day) ? "bg-primary text-primary-foreground" : "bg-background text-muted-foreground"}`}
                >
                  {day}
                </Button>
              ))}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-between pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={handleSave}
              disabled={isCreating}
            >
              <CalendarDays className="w-4 h-4 mr-2" />
              {isCreating ? "Creating..." : "Save Event"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
