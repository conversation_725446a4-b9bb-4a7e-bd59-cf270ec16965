import { create } from "zustand";
import api from "@/lib/axios"; // Assuming you have an axios instance configured
import { toast } from "@/hooks/use-toast";
import {
  type WorkScheduleRequestDto,
  type WorkScheduleResponseDto,
  type EffectiveDateRange,
  ImportResult,
  WorkDayOfWeek,
  ScheduleTypeDto,
} from "../types/clockingHoursTypes";

interface ScheduleFilters {
  profile?: string;
  dayOfWeek?: WorkDayOfWeek[];
  code?: string;
  workHours?: number;
}

interface ScheduleStore {
  scheduleData: WorkScheduleResponseDto[]; // Consolidated data source
  effectiveDateRange: EffectiveDateRange;
  filters: ScheduleFilters;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isScheduleTypeLoading: boolean;
  isUpdatingScheduleType: boolean; // New state for updating schedule type
  availableScheduleTypes: ScheduleTypeDto[];
  isImporting: boolean;
  isImportModalOpen: boolean;
  activeTabId: string;
  isCreateScheduleModalOpen: boolean;
  workingHoursActivated: boolean;
  isDownloadingCanvas: boolean; // New state for downloading canvas

  fetchSchedules: (
    scheduleTypeId: string,
    filters?: ScheduleFilters,
  ) => Promise<void>;
  addSchedule: (item: WorkScheduleRequestDto) => Promise<void>;
  updateSchedule: (id: string, item: WorkScheduleRequestDto) => Promise<void>;
  deleteSchedule: (id: string) => Promise<void>;
  setEffectiveDateRange: (from: Date, to: Date) => void;
  setFilters: (newFilters: Partial<ScheduleFilters>) => void;
  clearFilters: () => void;
  fetchAvailableScheduleTypes: () => Promise<void>;
  updateScheduleTypeEffectiveDates: (
    id: string,
    effectiveStartDate: Date,
    effectiveEndDate: Date,
  ) => Promise<void>; // New action
  importSchedules: (file: File) => Promise<ImportResult>;
  downloadCanvas: () => Promise<void>; // New action for downloading canvas
  closeImportModal: () => void;
  openImportModal: () => void;
  setActiveTabId: (id: string) => void;
  openCreateScheduleModal: () => void;
  closeCreateScheduleModal: () => void;
  setWorkingHoursActivated: (activated: boolean) => void;
}

export const useScheduleStore = create<ScheduleStore>((set, get) => ({
  scheduleData: [], // Initialize with a single data array
  effectiveDateRange: { from: null, to: null },
  filters: {},
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isScheduleTypeLoading: false,
  isUpdatingScheduleType: false, // Initial state
  availableScheduleTypes: [],
  isImporting: false,
  isImportModalOpen: false,
  activeTabId: "",
  isCreateScheduleModalOpen: false,
  workingHoursActivated: false,
  isDownloadingCanvas: false, // Initial state

  fetchSchedules: async (scheduleTypeId: string, filters?: ScheduleFilters) => {
    set({ isLoading: true });
    try {
      const currentFilters = filters || get().filters;
      const queryParams = new URLSearchParams();
      queryParams.append("scheduleTypeId", scheduleTypeId);
      if (currentFilters.profile) {
        queryParams.append("profile", currentFilters.profile);
      }
      if (currentFilters.code) {
        queryParams.append("code", currentFilters.code);
      }
      if (currentFilters.dayOfWeek && currentFilters.dayOfWeek.length > 0) {
        currentFilters.dayOfWeek.forEach((day) => {
          queryParams.append("dayOfWeek", day);
        });
      }
      if (
        currentFilters.workHours !== undefined &&
        currentFilters.workHours !== null
      ) {
        queryParams.append("workHours", currentFilters.workHours.toString());
      }

      const response = await api.get<WorkScheduleResponseDto[]>(
        `/clocking-hours/api/schedules?${queryParams.toString()}`,
      );
      const data = response.data;

      console.log(
        `Fetched ${data.length} schedules for type ID: ${scheduleTypeId}`,
        data,
      );

      set({ scheduleData: data }); // Always update scheduleData
      toast({
        title: "Success",
        description: `Schedules loaded successfully.`,
        variant: "default",
      });
    } catch (error) {
      console.error(
        `Failed to fetch schedules for type ID ${scheduleTypeId}:`,
        error,
      );
      toast({
        title: "Error",
        description: `Failed to fetch schedules. Please try again.`,
        variant: "destructive",
      });
    } finally {
      set({ isLoading: false });
    }
  },

  addSchedule: async (item: WorkScheduleRequestDto) => {
    set({ isCreating: true });
    try {
      const response = await api.post<WorkScheduleResponseDto>(
        "/clocking-hours/api/schedules",
        item,
      );
      const newSchedule = response.data;

      set((state) => ({
        scheduleData: [...state.scheduleData, newSchedule], // Add to scheduleData
      }));
      toast({
        title: "Success",
        description: "Schedule created successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to add schedule:", error);
      toast({
        title: "Error",
        description: "Failed to create schedule. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      set({ isCreating: false });
    }
  },

  updateSchedule: async (id: string, item: WorkScheduleRequestDto) => {
    set({ isUpdating: true });
    try {
      const response = await api.put<WorkScheduleResponseDto>(
        `/clocking-hours/api/schedules/${id}`,
        item,
      );
      const updatedSchedule = response.data;

      set((state) => ({
        scheduleData: state.scheduleData.map((s) =>
          s.id === id ? updatedSchedule : s,
        ), // Update in scheduleData
      }));

      set((state) => ({
        scheduleData:
          state.activeTabId !== updatedSchedule.scheduleTypeId
            ? state.scheduleData.filter(
                (s) => s.scheduleTypeId !== item.scheduleTypeId,
              )
            : state.scheduleData,
      }));
      toast({
        title: "Success",
        description: "Schedule updated successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to update schedule:", error);
      toast({
        title: "Error",
        description: "Failed to update schedule. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      set({ isUpdating: false });
    }
  },

  deleteSchedule: async (id: string) => {
    set({ isDeleting: true });
    try {
      await api.delete(`/clocking-hours/api/schedules/${id}`);

      set((state) => ({
        scheduleData: state.scheduleData.filter((s) => s.id !== id), // Filter from scheduleData
      }));
      toast({
        title: "Success",
        description: "Schedule deleted successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to delete schedule:", error);
      toast({
        title: "Error",
        description: "Failed to delete schedule. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      set({ isDeleting: false });
    }
  },

  setEffectiveDateRange: (from: Date, to: Date) => {
    set({ effectiveDateRange: { from, to } });
    const today = new Date();
    const activated = today >= from && today <= to;
    set({ workingHoursActivated: activated });
  },

  setFilters: (newFilters: Partial<ScheduleFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    }));
  },

  clearFilters: () => {
    set({ filters: {} });
  },

  fetchAvailableScheduleTypes: async () => {
    set({ isScheduleTypeLoading: true });
    try {
      const response = await api.get<ScheduleTypeDto[]>(
        "/clocking-hours/api/schedule-types",
      );
      const fetchedTypes = response.data;

      set({
        availableScheduleTypes: fetchedTypes,
        isScheduleTypeLoading: false,
      });
    } catch (error) {
      console.error("Failed to fetch available schedule types:", error);
      toast({
        title: "Error",
        description: "Failed to load schedule types. Please try again.",
        variant: "destructive",
      });
      set({ isScheduleTypeLoading: false });
    }
  },

  updateScheduleTypeEffectiveDates: async (
    id: string,
    effectiveStartDate: Date,
    effectiveEndDate: Date,
  ) => {
    set({ isUpdatingScheduleType: true });
    try {
      const state = get();
      const selectedType = state.availableScheduleTypes.find(
        (type) => type.id === id,
      );

      if (!selectedType) {
        throw new Error("Schedule type not found for update.");
      }

      // Create a new object with updated dates, preserving other properties
      const updatedPayload: ScheduleTypeDto = {
        ...selectedType,
        effectiveStartDate: effectiveStartDate.toISOString(),
        effectiveEndDate: effectiveEndDate.toISOString(),
      };

      const response = await api.put<ScheduleTypeDto>(
        `/clocking-hours/api/schedule-types/${id}`,
        updatedPayload,
      );
      const updatedType = response.data;

      set((state) => ({
        availableScheduleTypes: state.availableScheduleTypes.map((type) =>
          type.id === id ? { ...type, ...updatedType } : type,
        ),
      }));
      toast({
        title: "Success",
        description: "Schedule type effective dates updated successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to update schedule type effective dates:", error);
      toast({
        title: "Error",
        description:
          "Failed to update schedule type effective dates. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      set({ isUpdatingScheduleType: false });
    }
  },

  importSchedules: async (file: File): Promise<ImportResult> => {
    set({ isImporting: true });
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await api.post(
        "/clocking-hours/api/schedules/import",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      const result = response.data;

      toast({
        title: "Success",
        description:
          result.errors[0]?.message ?? "Schedules imported successfully.",
        variant: "default",
      });
      get().fetchSchedules(get().activeTabId, get().filters);
      return result;
    } catch (error) {
      console.error("Failed to import schedules:", error);
      toast({
        title: "Error",
        description: "Failed to import schedules. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      set({ isImporting: false });
    }
  },

  downloadCanvas: async () => {
    set({ isDownloadingCanvas: true });
    try {
      // -- API Call logic (commented for now) --
      const response = await api.get(
        `/clocking-hours/api/schedules/download-template`,
        { responseType: "blob" },
      );

      const blob = new Blob([response.data], {
        type: response.headers["content-type"] || "application/octet-stream",
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      const contentDisposition = response.headers["content-disposition"];
      let filename = "schedule-import-template.xlsx";
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
        );
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, "");
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Import template downloaded successfully from local file.",
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to download template:", error);
      toast({
        title: "Error",
        description: "Failed to download import template. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      set({ isDownloadingCanvas: false });
    }
  },

  closeImportModal: () => set({ isImportModalOpen: false }),
  openImportModal: () => set({ isImportModalOpen: true }),
  setActiveTabId: (id: string) => set({ activeTabId: id }),
  openCreateScheduleModal: () => set({ isCreateScheduleModalOpen: true }),
  closeCreateScheduleModal: () => set({ isCreateScheduleModalOpen: false }),
  setWorkingHoursActivated: (activated: boolean) =>
    set({ workingHoursActivated: activated }),
}));
