export interface ScheduleTypeDto {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  country: string;
  effectiveStartDate?: string; // ISO 8601 date-time
  effectiveEndDate?: string; // ISO 8601 date-time
  displayOrder: number;
  createdAt: string; // ISO 8601 date-time
  updatedAt: string; // ISO 8601 date-time
  createdBy?: string;
  updatedBy?: string;
}

export enum WorkDayOfWeek {
  MONDAY = "MONDAY",
  TUESDAY = "TUESDAY",
  WEDNESDAY = "WEDNESDAY",
  THURSDAY = "THURSDAY",
  FRIDAY = "FRIDAY",
  SATURDAY = "SATURDAY",
  SUNDAY = "SUNDAY",
}

export interface WorkScheduleRequestDto {
  profile: string;
  code: string;
  designation: string;
  entryTime: string; // HH:mm format
  exitTime: string; // HH:mm format
  breakDurationMinutes: number;
  daysOfWeek: WorkDayOfWeek[];
  scheduleTypeId: string; // Changed from ScheduleType enum to string ID
  workHours: number; // Added as per new API spec
}

export interface WorkScheduleResponseDto {
  id: string; // UUID
  profile: string;
  code: string;
  designation: string;
  entryTime: string; // HH:mm format
  exitTime: string; // HH:mm format
  breakDurationMinutes: number;
  daysOfWeek: WorkDayOfWeek[]; // Changed from DayOfWeek enum
  scheduleTypeId: string; // Changed from ScheduleType enum to string ID
  workHours: number; // Calculated by backend: Total work hours.
  createdAt: string; // ISO 8601
  updatedAt: string; // ISO 8601
}

export interface EffectiveDateRange {
  from: Date | null;
  to: Date | null;
}

// Define a specific interface for the API parameters
export interface FetchSchedulesApiParams {
  scheduleTypeId: string; // Changed from scheduleType enum to string ID
  profile?: string; // Changed from profileCode to profile
  dayOfWeek?: string[]; // Changed from dayOfWeeks to dayOfWeek, and DayOfWeek enum
  code?: string; // Added as per new API spec
  workHours?: number; // Added as per new API spec
}

export interface ImportResult {
  importedCount: number;
  errors: Array<{
    rowNumber: number;
    message: string;
  }>;
}
