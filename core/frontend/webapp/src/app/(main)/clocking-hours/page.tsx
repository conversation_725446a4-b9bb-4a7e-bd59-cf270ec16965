"use client";
import { useEffect, useCallback, useMemo } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { WorkingHoursHeader } from "./components/WorkingHoursHeader";
import { ScheduleFilters } from "./components/ScheduleFilters";
import { useScheduleStore } from "./store/scheduleStore";
import { Check } from "lucide-react";
import CustomIcon from "@/components/common/CustomIcons";
import { ScheduleDataTable } from "./components/ScheduleDataTable";
import { ImportModal } from "./components/ImportModal";

const Index = () => {
  const {
    scheduleData, // Now using a single data source
    effectiveDateRange,
    filters,
    isImportModalOpen,
    fetchSchedules,
    setFilters,
    clearFilters,
    isLoading,
    availableScheduleTypes,
    isScheduleTypeLoading,
    closeImportModal,
    fetchAvailableScheduleTypes,
    activeTabId,
    setActiveTabId,
    setWorkingHoursActivated,
    setEffectiveDateRange,
  } = useScheduleStore();

  const memoizedFetchSchedules = useCallback(
    (scheduleTypeId: string, currentFilters: typeof filters) => {
      fetchSchedules(scheduleTypeId, currentFilters);
    },
    [fetchSchedules],
  );

  useEffect(() => {
    fetchAvailableScheduleTypes();
  }, [fetchAvailableScheduleTypes]);

  useEffect(() => {
    if (availableScheduleTypes.length > 0 && !activeTabId) {
      const defaultScheduleType = availableScheduleTypes[0];
      if (defaultScheduleType) {
        setActiveTabId(defaultScheduleType.id);
      }
    }
  }, [availableScheduleTypes, activeTabId, setActiveTabId]);

  // Effect to update effectiveDateRange in store when activeTabId or availableScheduleTypes change
  useEffect(() => {
    if (activeTabId && availableScheduleTypes.length > 0) {
      const currentActiveType = availableScheduleTypes.find(
        (type) => type.id === activeTabId,
      );
      if (currentActiveType) {
        const from = currentActiveType.effectiveStartDate
          ? new Date(currentActiveType.effectiveStartDate)
          : new Date();
        const to = currentActiveType.effectiveEndDate
          ? new Date(currentActiveType.effectiveEndDate)
          : new Date();
        setEffectiveDateRange(from, to); // Update the store's effectiveDateRange
      }
    }
  }, [activeTabId, availableScheduleTypes, setEffectiveDateRange]);

  useEffect(() => {
    if (activeTabId) {
      memoizedFetchSchedules(activeTabId, {});
    }
  }, [activeTabId, memoizedFetchSchedules]);

  // This useEffect now solely manages workingHoursActivated based on effectiveDateRange
  useEffect(() => {
    const today = new Date();
    if (
      effectiveDateRange.from &&
      effectiveDateRange.to &&
      today >= new Date(effectiveDateRange.from) &&
      today <= new Date(effectiveDateRange.to)
    ) {
      // If the current date is within the Ramadan effective date range, activate working hours
      setWorkingHoursActivated(true);
    } else {
      // Otherwise, deactivate working hours
      setWorkingHoursActivated(false);
    }
  }, [
    effectiveDateRange,
    availableScheduleTypes,
    activeTabId,
    setActiveTabId,
    setWorkingHoursActivated,
  ]);

  const handleApplyFilters = () => {
    memoizedFetchSchedules(activeTabId, filters);
  };

  const handleClearFilters = () => {
    clearFilters();
    memoizedFetchSchedules(activeTabId, {});
  };

  // currentData now always points to scheduleData
  const currentData = useMemo(() => {
    return scheduleData;
  }, [scheduleData]);

  const activeScheduleType = useMemo(() => {
    return availableScheduleTypes.find((type) => type.id === activeTabId);
  }, [activeTabId, availableScheduleTypes]);

  return (
    <div className="flex-1 w-full h-full overflow-hidden">
      <div className="h-full w-full p-6 lg:p-8">
        <div className="h-full w-full rounded-lg border bg-white shadow-sm px-6 py-6">
          <WorkingHoursHeader />

          <Tabs
            value={activeTabId}
            onValueChange={(value) => setActiveTabId(value)}
            className="w-full"
          >
            <TabsList className="flex w-fit mb-6 gap-2 bg-transparent shadow-none border-none">
              {isScheduleTypeLoading ? (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
                  Loading schedule types...
                </div>
              ) : (
                <>
                  {availableScheduleTypes.map((type) => (
                    <TabsTrigger
                      key={type.id}
                      value={type.id}
                      className={`
                      flex items-center gap-2 px-4 py-2 rounded-none border-b-2 relative
                      ${
                        activeTabId === type.id
                          ? "border-b-blue-500 font-bold text-black"
                          : "border-b-transparent text-muted-foreground font-normal"
                      }
                      transition-all
                    `}
                    >
                      {type.name === "Ramadan" && (
                        <CustomIcon name="ramadane" className="w-4 h-4" />
                      )}
                      <span className="text-md">{type.name}</span>
                      {activeTabId === type.id && (
                        <div className="absolute top-0 right-1 bg-green-500 rounded-full">
                          <Check className="w-4 h-4 text-white" />
                        </div>
                      )}
                    </TabsTrigger>
                  ))}
                </>
              )}
            </TabsList>
            <ScheduleFilters
              filterDaysOfWeek={filters.dayOfWeek || []}
              setFilterDaysOfWeek={(days) => setFilters({ dayOfWeek: days })}
              filterWorkHours={filters.workHours}
              setFilterWorkHours={(hours) => setFilters({ workHours: hours })}
              filterCode={filters.code || ""}
              setFilterCode={(code) => setFilters({ code: code })}
              onApplyFilters={handleApplyFilters}
              onClearFilters={handleClearFilters}
              activeTabId={activeTabId}
            />
            {isLoading ? (
              <div className="flex-1 w-full h-full overflow-hidden flex items-center justify-center m-auto">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading...</p>
                </div>
              </div>
            ) : (
              activeScheduleType && (
                <TabsContent
                  value={activeScheduleType.id}
                  className="space-y-4"
                >
                  <ScheduleDataTable
                    data={currentData}
                    scheduleType={activeScheduleType}
                  />
                </TabsContent>
              )
            )}
          </Tabs>
        </div>
      </div>
      <ImportModal isOpen={isImportModalOpen} onClose={closeImportModal} />
    </div>
  );
};

export default Index;
