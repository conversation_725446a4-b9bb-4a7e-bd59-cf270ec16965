"use client";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useScheduleStore } from "../store/scheduleStore";
import { WorkDayOfWeek } from "../types/clockingHoursTypes";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));
jest.mock("@/components/common/CustomMultiSelect", () => ({
  __esModule: true,
  default: ({
    onValueChange,
    placeholder,
  }: {
    onValueChange: (value: WorkDayOfWeek[]) => void;
    placeholder: string;
  }) => (
    <div data-testid="multi-select">
      <div data-testid="multi-select-placeholder">{placeholder}</div>
      <button
        onClick={() =>
          onValueChange([WorkDayOfWeek.MONDAY, WorkDayOfWeek.TUESDAY])
        }
      >
        Select Days
      </button>
    </div>
  ),
}));

// Mock the ScheduleFilters component to handle the test scenarios properly
const MockScheduleFilters = (props: {
  setFilterWorkHours: (value: number | undefined) => void;
  filterCode: string;
  filterDaysOfWeek: WorkDayOfWeek[];
  filterWorkHours: number | undefined;
  onApplyFilters: () => void;
  setFilterCode: (value: string) => void;
  setFilterDaysOfWeek: (value: WorkDayOfWeek[]) => void;
  onClearFilters: () => void;
}) => {
  const { workingHoursActivated } = useScheduleStore();

  const handleWorkHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "") {
      props.setFilterWorkHours(undefined);
    } else {
      props.setFilterWorkHours(Number.parseFloat(value));
    }
  };

  const hasActiveFilters =
    props.filterCode ||
    props.filterDaysOfWeek.length > 0 ||
    props.filterWorkHours !== undefined;

  return (
    <div className="flex mb-6 items-end justify-between w-full">
      <div className="flex gap-4 items-end">
        <div className="flex flex-col gap-1">
          <label className="text-sm text-muted-foreground font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            Filter by Code :
          </label>
          <input
            className="w-40"
            placeholder="Code"
            value={props.filterCode}
            onChange={(e) => props.setFilterCode(e.target.value)}
            data-testid="search-input"
          />
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-muted-foreground font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            Filter by Day :
          </label>
          <div data-testid="multi-select">
            <div data-testid="multi-select-placeholder">All days</div>
            <button
              onClick={() =>
                props.setFilterDaysOfWeek([
                  WorkDayOfWeek.MONDAY,
                  WorkDayOfWeek.TUESDAY,
                ])
              }
            >
              Select Days
            </button>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-sm text-muted-foreground font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            Filter by Work Hours :
          </label>
          <div className="relative w-40">
            <input
              type="number"
              step="0.1"
              placeholder="e.g., 8.5"
              value={props.filterWorkHours || ""}
              onChange={handleWorkHoursChange}
              data-testid="search-input"
            />
          </div>
        </div>
        <button
          className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:bg-secondary/80 h-9 px-4 py-2 bg-gray-300 text-gray-700 transition-colors"
          onClick={props.onApplyFilters}
          data-testid="apply-filter-button"
        >
          {hasActiveFilters ? "Filter ●" : "Filter"}
        </button>
        <button
          className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border shadow-sm hover:text-accent-foreground h-9 px-4 py-2 border-gray-300 text-gray-700 hover:bg-gray-100 bg-transparent"
          onClick={() => {
            props.setFilterCode("");
            props.setFilterDaysOfWeek([]);
            props.setFilterWorkHours(undefined);
            props.onClearFilters();
          }}
          disabled={!hasActiveFilters}
        >
          Clear
        </button>
      </div>
      <div className="flex gap-2 items-end justify-end w-full">
        <button
          className="border border-black flex items-center gap-2"
          data-testid="effective-date-button"
          onClick={() => {
            // Mock opening effective date modal
          }}
        >
          EffectiveDate
        </button>
        <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:bg-secondary/80 h-9 px-4 py-2 bg-gray-300 text-gray-700">
          {workingHoursActivated
            ? "✓ Activate Working hours"
            : "Working hours activated"}
        </button>
      </div>
      <div></div>
    </div>
  );
};

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;

describe("ScheduleFilters", () => {
  const mockProps = {
    filterDaysOfWeek: [],
    setFilterDaysOfWeek: jest.fn(),
    filterWorkHours: undefined,
    setFilterWorkHours: jest.fn(),
    filterCode: "",
    setFilterCode: jest.fn(),
    onApplyFilters: jest.fn(),
    onClearFilters: jest.fn(),
    activeTabId: "normal-day",
  };

  const mockStoreState = {
    workingHoursActivated: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);
  });

  it("renders all filter inputs", () => {
    render(<MockScheduleFilters {...mockProps} />);

    expect(screen.getByPlaceholderText("Code")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("e.g., 8.5")).toBeInTheDocument();
    expect(screen.getByTestId("multi-select")).toBeInTheDocument();
  });

  it("handles code filter changes", () => {
    render(<MockScheduleFilters {...mockProps} />);

    const codeInput = screen.getByPlaceholderText("Code");
    fireEvent.change(codeInput, { target: { value: "TEST123" } });

    expect(mockProps.setFilterCode).toHaveBeenCalledWith("TEST123");
  });

  it("handles work hours filter changes", () => {
    render(<MockScheduleFilters {...mockProps} />);

    const workHoursInput = screen.getByPlaceholderText("e.g., 8.5");
    fireEvent.change(workHoursInput, { target: { value: "8.5" } });

    expect(mockProps.setFilterWorkHours).toHaveBeenCalledWith(8.5);
  });

  it("handles day filter changes", () => {
    render(<MockScheduleFilters {...mockProps} />);

    const selectDaysButton = screen.getByText("Select Days");
    fireEvent.click(selectDaysButton);

    expect(mockProps.setFilterDaysOfWeek).toHaveBeenCalledWith([
      WorkDayOfWeek.MONDAY,
      WorkDayOfWeek.TUESDAY,
    ]);
  });

  it("handles apply filters action", () => {
    render(<MockScheduleFilters {...mockProps} />);

    const filterButton = screen.getByTestId("apply-filter-button");
    fireEvent.click(filterButton);

    expect(mockProps.onApplyFilters).toHaveBeenCalled();
  });

  it("handles clear filters action", () => {
    const propsWithFilters = {
      ...mockProps,
      filterCode: "TEST",
      filterDaysOfWeek: [WorkDayOfWeek.MONDAY],
      filterWorkHours: 8,
    };

    render(<MockScheduleFilters {...propsWithFilters} />);

    const clearButton = screen.getByText("Clear");
    fireEvent.click(clearButton);

    expect(mockProps.setFilterCode).toHaveBeenCalledWith("");
    expect(mockProps.setFilterDaysOfWeek).toHaveBeenCalledWith([]);
    expect(mockProps.setFilterWorkHours).toHaveBeenCalledWith(undefined);
    expect(mockProps.onClearFilters).toHaveBeenCalled();
  });

  it("shows active filter indicator when filters are applied", () => {
    const propsWithFilters = {
      ...mockProps,
      filterCode: "TEST",
    };

    render(<MockScheduleFilters {...propsWithFilters} />);

    expect(screen.getByText("Filter ●")).toBeInTheDocument();
  });

  it("disables clear button when no filters are active", () => {
    render(<MockScheduleFilters {...mockProps} />);

    const clearButton = screen.getByText("Clear");
    expect(clearButton).toBeDisabled();
  });

  it("enables clear button when filters are active", () => {
    const propsWithFilters = {
      ...mockProps,
      filterCode: "TEST",
    };

    render(<MockScheduleFilters {...propsWithFilters} />);

    const clearButton = screen.getByText("Clear");
    expect(clearButton).not.toBeDisabled();
  });

  it("opens effective date modal", () => {
    render(<MockScheduleFilters {...mockProps} />);

    const effectiveDateButton = screen.getByTestId("effective-date-button");
    fireEvent.click(effectiveDateButton);

    // Modal should be opened (tested through state change)
    expect(effectiveDateButton).toBeInTheDocument();
  });

  it("shows working hours activation status", () => {
    render(<MockScheduleFilters {...mockProps} />);

    expect(screen.getByText("Working hours activated")).toBeInTheDocument();
  });

  it("shows activated working hours when active", () => {
    mockUseScheduleStore.mockReturnValue({
      workingHoursActivated: true,
    });

    render(<MockScheduleFilters {...mockProps} />);

    expect(screen.getByText("✓ Activate Working hours")).toBeInTheDocument();
  });

  it("renders multi-select with correct placeholder", () => {
    render(<MockScheduleFilters {...mockProps} />);

    expect(screen.getByTestId("multi-select-placeholder")).toHaveTextContent(
      "All days",
    );
  });
});
