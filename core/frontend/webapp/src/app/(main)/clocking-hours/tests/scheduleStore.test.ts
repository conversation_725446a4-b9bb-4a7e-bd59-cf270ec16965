import { useScheduleStore } from "../store/scheduleStore";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";
import {
  WorkDayOfWeek,
  WorkScheduleRequestDto,
} from "../types/clockingHoursTypes";

// Mock external dependencies
jest.mock("@/lib/axios");
jest.mock("@/hooks/use-toast");

// Create typed mocks
const mockApi = api as jest.Mocked<typeof api>;
const mockToast = toast as jest.MockedFunction<typeof toast>;

describe("useScheduleStore", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the store state before each test
    useScheduleStore.setState({
      scheduleData: [],
      effectiveDateRange: { from: null, to: null },
      filters: {},
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isScheduleTypeLoading: false,
      isUpdatingScheduleType: false,
      availableScheduleTypes: [],
      isImporting: false,
      isImportModalOpen: false,
      activeTabId: "",
      isCreateScheduleModalOpen: false,
      workingHoursActivated: false,
      isDownloadingCanvas: false, // Add this line
    });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  // --- fetchSchedules tests ---
  it("fetchSchedules fetches data successfully and updates state", async () => {
    const mockSchedules = [
      {
        id: "s1",
        profile: "P1",
        scheduleTypeId: "type-1",
        daysOfWeek: [WorkDayOfWeek.MONDAY],
      },
    ];
    mockApi.get.mockResolvedValue({ data: mockSchedules });

    await useScheduleStore.getState().fetchSchedules("type-1");

    expect(useScheduleStore.getState().scheduleData).toEqual(mockSchedules);
    expect(useScheduleStore.getState().isLoading).toBe(false);
    expect(mockApi.get).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules?scheduleTypeId=type-1",
    );
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );
  });

  it("fetchSchedules handles errors", async () => {
    mockApi.get.mockRejectedValue(new Error("Network error"));

    await useScheduleStore.getState().fetchSchedules("type-1");

    expect(useScheduleStore.getState().scheduleData).toEqual([]);
    expect(useScheduleStore.getState().isLoading).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );
  });

  it("fetchSchedules appends filters to query params", async () => {
    const filters = {
      profile: "Test",
      dayOfWeek: [WorkDayOfWeek.FRIDAY],
      workHours: 8,
    };
    mockApi.get.mockResolvedValue({ data: [] });

    await useScheduleStore.getState().fetchSchedules("type-1", filters);

    expect(mockApi.get).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules?scheduleTypeId=type-1&profile=Test&dayOfWeek=FRIDAY&workHours=8",
    );
  });

  // --- addSchedule tests ---
  it("addSchedule adds a schedule successfully", async () => {
    const newItem = {
      profile: "New",
      code: "N1",
      designation: "ND",
      entryTime: "08:00",
      exitTime: "16:00",
      breakDurationMinutes: 30,
      daysOfWeek: [WorkDayOfWeek.MONDAY],
      scheduleTypeId: "type-1",
      workHours: 7.5,
    };
    const addedItem = {
      ...newItem,
      id: "new-id",
      createdAt: "",
      updatedAt: "",
    };
    mockApi.post.mockResolvedValue({ data: addedItem });
    mockApi.get.mockResolvedValue({ data: [addedItem] }); // For refetch

    await useScheduleStore.getState().addSchedule(newItem);

    expect(useScheduleStore.getState().scheduleData).toEqual([addedItem]);
    expect(useScheduleStore.getState().isCreating).toBe(false);
    expect(mockApi.post).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules",
      newItem,
    );
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );
  });

  it("addSchedule handles errors", async () => {
    mockApi.post.mockRejectedValue(new Error("Add error"));

    await expect(
      useScheduleStore.getState().addSchedule({} as WorkScheduleRequestDto),
    ).rejects.toThrow("Add error");
    expect(useScheduleStore.getState().isCreating).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );
  });

  // --- updateSchedule tests ---
  it("updateSchedule updates a schedule successfully", async () => {
    const existingItem = {
      id: "s1",
      profile: "P1",
      code: "C1",
      designation: "D1",
      entryTime: "09:00",
      exitTime: "17:00",
      breakDurationMinutes: 60,
      daysOfWeek: [WorkDayOfWeek.MONDAY],
      scheduleTypeId: "type-1",
      workHours: 8,
      createdAt: "",
      updatedAt: "",
    };
    const updatedItem = {
      ...existingItem,
      profile: "Updated P1",
      workHours: 9,
    };
    useScheduleStore.setState({ scheduleData: [existingItem] });
    mockApi.put.mockResolvedValue({ data: updatedItem });
    mockApi.get.mockResolvedValue({ data: [updatedItem] }); // For refetch

    await useScheduleStore.getState().updateSchedule("s1", updatedItem);

    expect(useScheduleStore.getState().isUpdating).toBe(false);
    expect(mockApi.put).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules/s1",
      updatedItem,
    );
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );
  });

  it("updateSchedule handles errors", async () => {
    mockApi.put.mockRejectedValue(new Error("Update error"));

    await expect(
      useScheduleStore
        .getState()
        .updateSchedule("s1", {} as WorkScheduleRequestDto),
    ).rejects.toThrow("Update error");
    expect(useScheduleStore.getState().isUpdating).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );
  });

  // --- deleteSchedule tests ---
  it("deleteSchedule deletes a schedule successfully", async () => {
    const existingItem = {
      id: "s1",
      profile: "P1",
      code: "C1",
      designation: "D1",
      entryTime: "09:00",
      exitTime: "17:00",
      breakDurationMinutes: 60,
      daysOfWeek: [WorkDayOfWeek.MONDAY],
      scheduleTypeId: "type-1",
      workHours: 8,
      createdAt: "",
      updatedAt: "",
    };

    useScheduleStore.setState({
      scheduleData: [existingItem],
      activeTabId: "type-1",
    });
    mockApi.delete.mockResolvedValue({});
    mockApi.get.mockResolvedValue({ data: [] }); // For refetch

    await useScheduleStore.getState().deleteSchedule("s1");

    expect(useScheduleStore.getState().scheduleData).toEqual([]);
    expect(useScheduleStore.getState().isDeleting).toBe(false);
    expect(mockApi.delete).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules/s1",
    );
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );
  });

  it("deleteSchedule handles errors", async () => {
    mockApi.delete.mockRejectedValue(new Error("Delete error"));

    await expect(
      useScheduleStore.getState().deleteSchedule("s1"),
    ).rejects.toThrow("Delete error");
    expect(useScheduleStore.getState().isDeleting).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );
  });

  // --- setEffectiveDateRange tests ---
  it("setEffectiveDateRange sets the date range and workingHoursActivated", () => {
    const from = new Date("2024-01-01");
    const to = new Date("2024-01-31");
    jest.setSystemTime(new Date("2024-01-15")); // Set current date within range

    useScheduleStore.getState().setEffectiveDateRange(from, to);

    expect(useScheduleStore.getState().effectiveDateRange).toEqual({
      from,
      to,
    });
    expect(useScheduleStore.getState().workingHoursActivated).toBe(true);

    jest.setSystemTime(new Date("2024-02-15")); // Set current date outside range
    useScheduleStore.getState().setEffectiveDateRange(from, to);
    expect(useScheduleStore.getState().workingHoursActivated).toBe(false);
  });

  // --- setFilters and clearFilters tests ---
  it("setFilters updates filters correctly", () => {
    useScheduleStore.getState().setFilters({ profile: "NewProfile" });
    expect(useScheduleStore.getState().filters).toEqual({
      profile: "NewProfile",
    });

    useScheduleStore
      .getState()
      .setFilters({ code: "NewCode", dayOfWeek: [WorkDayOfWeek.TUESDAY] });
    expect(useScheduleStore.getState().filters).toEqual({
      profile: "NewProfile",
      code: "NewCode",
      dayOfWeek: [WorkDayOfWeek.TUESDAY],
    });
  });

  it("clearFilters resets filters to empty object", () => {
    useScheduleStore.setState({ filters: { profile: "Existing", code: "C" } });
    useScheduleStore.getState().clearFilters();
    expect(useScheduleStore.getState().filters).toEqual({});
  });

  // --- fetchAvailableScheduleTypes tests ---

  it("fetchAvailableScheduleTypes handles errors", async () => {
    // Suppress console.error for this test since we expect an error
    const consoleSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    mockApi.get.mockRejectedValue(new Error("Type fetch error"));

    await useScheduleStore.getState().fetchAvailableScheduleTypes();

    expect(useScheduleStore.getState().availableScheduleTypes).toEqual([]);
    expect(useScheduleStore.getState().isScheduleTypeLoading).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );

    // Restore console.error
    consoleSpy.mockRestore();
  });

  // --- updateScheduleTypeEffectiveDates tests ---
  it("updateScheduleTypeEffectiveDates updates dates successfully", async () => {
    const initialType = {
      id: "type-1",
      name: "Normal day",
      effectiveStartDate: "2024-01-01T00:00:00Z",
      effectiveEndDate: "2024-12-31T23:59:59Z",
      description: "",
      isActive: true,
      country: "GLOBAL",
      displayOrder: 0,
      createdAt: "",
      updatedAt: "",
    };
    useScheduleStore.setState({ availableScheduleTypes: [initialType] });

    const newStartDate = new Date("2025-01-01");
    const newEndDate = new Date("2025-01-31");
    const updatedType = {
      ...initialType,
      effectiveStartDate: newStartDate.toISOString(),
      effectiveEndDate: newEndDate.toISOString(),
    };
    mockApi.put.mockResolvedValue({ data: updatedType });

    await useScheduleStore
      .getState()
      .updateScheduleTypeEffectiveDates("type-1", newStartDate, newEndDate);

    const state = useScheduleStore.getState();
    expect(state.availableScheduleTypes[0].effectiveStartDate).toBe(
      newStartDate.toISOString(),
    );
    expect(state.availableScheduleTypes[0].effectiveEndDate).toBe(
      newEndDate.toISOString(),
    );
    expect(state.isUpdatingScheduleType).toBe(false);
    expect(mockApi.put).toHaveBeenCalledWith(
      "/clocking-hours/api/schedule-types/type-1",
      updatedType,
    );
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );
  });

  it("updateScheduleTypeEffectiveDates handles errors", async () => {
    const initialType = {
      id: "type-1",
      name: "Normal day",
      effectiveStartDate: "2024-01-01T00:00:00Z",
      effectiveEndDate: "2024-12-31T23:59:59Z",
      description: "",
      isActive: true,
      country: "GLOBAL",
      displayOrder: 0,
      createdAt: "",
      updatedAt: "",
    };
    useScheduleStore.setState({ availableScheduleTypes: [initialType] });
    mockApi.put.mockRejectedValue(new Error("Update effective dates error"));

    await expect(
      useScheduleStore
        .getState()
        .updateScheduleTypeEffectiveDates("type-1", new Date(), new Date()),
    ).rejects.toThrow("Update effective dates error");
    expect(useScheduleStore.getState().isUpdatingScheduleType).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );
  });

  it("updateScheduleTypeEffectiveDates throws error if type not found", async () => {
    useScheduleStore.setState({ availableScheduleTypes: [] });

    // Suppress console.error for this specific test since we expect an error
    const consoleSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    await expect(
      useScheduleStore
        .getState()
        .updateScheduleTypeEffectiveDates(
          "non-existent-id",
          new Date(),
          new Date(),
        ),
    ).rejects.toThrow("Schedule type not found for update.");

    consoleSpy.mockRestore();
  });

  // --- importSchedules tests ---
  it("importSchedules imports successfully", async () => {
    const mockFile = new File(["csv content"], "test.csv", {
      type: "text/csv",
    });
    const mockResult = { importedCount: 5, errors: [] };
    mockApi.post.mockResolvedValue({ data: mockResult });

    const result = await useScheduleStore.getState().importSchedules(mockFile);

    expect(result).toEqual(mockResult);
    expect(useScheduleStore.getState().isImporting).toBe(false);
    expect(mockApi.post).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules/import",
      expect.any(FormData),
      expect.any(Object),
    );
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );
  });

  it("importSchedules handles import with errors", async () => {
    const mockFile = new File(["csv content"], "test.csv", {
      type: "text/csv",
    });
    const mockResult = {
      importedCount: 3,
      errors: [{ rowNumber: 2, message: "Invalid data" }],
    };
    mockApi.post.mockResolvedValue({ data: mockResult });

    const result = await useScheduleStore.getState().importSchedules(mockFile);

    expect(result).toEqual(mockResult);
    expect(useScheduleStore.getState().isImporting).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    ); // Still success toast for partial import
  });

  it("importSchedules handles import failure", async () => {
    mockApi.post.mockRejectedValue(new Error("Import failed"));
    const mockFile = new File(["csv content"], "test.csv", {
      type: "text/csv",
    });

    await expect(
      useScheduleStore.getState().importSchedules(mockFile),
    ).rejects.toThrow("Import failed");
    expect(useScheduleStore.getState().isImporting).toBe(false);
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Error" }),
    );
  });

  // Add these tests after the existing importSchedules tests, before the Modal state actions section

  // --- downloadCanvas tests ---
  it.skip("downloadCanvas downloads template successfully", async () => {
    const mockBlob = new Blob(["template content"], { type: "text/csv" });
    const mockResponse = {
      data: mockBlob,
      headers: {
        "content-type": "text/csv",
        "content-disposition": 'attachment; filename="schedule-template.csv"',
      },
    };
    mockApi.get.mockResolvedValue(mockResponse);

    // Mock DOM methods
    const mockCreateElement = jest.spyOn(document, "createElement");
    const mockAppendChild = jest
      .spyOn(document.body, "appendChild")
      .mockImplementation();
    const mockRemoveChild = jest
      .spyOn(document.body, "removeChild")
      .mockImplementation();

    // Mock URL methods by adding them to the global object
    const mockCreateObjectURL = jest.fn().mockReturnValue("blob:mock-url");
    const mockRevokeObjectURL = jest.fn();

    Object.defineProperty(window, "URL", {
      value: {
        createObjectURL: mockCreateObjectURL,
        revokeObjectURL: mockRevokeObjectURL,
      },
      writable: true,
    });

    const mockLink = document.createElement("a");
    mockLink.href = "";
    mockLink.download = "";
    mockLink.click = jest.fn();

    mockCreateElement.mockReturnValue(mockLink);

    await useScheduleStore.getState().downloadCanvas();

    expect(useScheduleStore.getState().isDownloadingCanvas).toBe(false);
    expect(mockApi.get).toHaveBeenCalledWith(
      "/clocking-hours/api/schedules/download-template",
      {
        responseType: "blob",
      },
    );
    expect(mockCreateObjectURL).toHaveBeenCalledWith(expect.any(Blob));
    expect(mockLink.download).toBe("schedule-template.csv");
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockAppendChild).toHaveBeenCalledWith(mockLink);
    expect(mockRemoveChild).toHaveBeenCalledWith(mockLink);
    expect(mockRevokeObjectURL).toHaveBeenCalledWith("blob:mock-url");
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({ title: "Success" }),
    );

    // Restore mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  it.skip("downloadCanvas uses default filename when no content-disposition header", async () => {
    const mockBlob = new Blob(["template content"], { type: "text/csv" });
    const mockResponse = {
      data: mockBlob,
      headers: {
        "content-type": "text/csv",
      },
    };
    mockApi.get.mockResolvedValue(mockResponse);

    // Mock DOM methods
    const mockCreateElement = jest.spyOn(document, "createElement");
    const mockAppendChild = jest
      .spyOn(document.body, "appendChild")
      .mockImplementation();
    const mockRemoveChild = jest
      .spyOn(document.body, "removeChild")
      .mockImplementation();

    // Mock URL methods
    const mockCreateObjectURL = jest.fn().mockReturnValue("blob:mock-url");
    const mockRevokeObjectURL = jest.fn();

    Object.defineProperty(window, "URL", {
      value: {
        createObjectURL: mockCreateObjectURL,
        revokeObjectURL: mockRevokeObjectURL,
      },
      writable: true,
    });

    const mockLink = document.createElement("a");
    mockLink.href = "";
    mockLink.download = "";
    mockLink.click = jest.fn();

    mockCreateElement.mockReturnValue(mockLink);

    await useScheduleStore.getState().downloadCanvas();

    expect(mockLink.download).toBe("schedule-import-template.csv");

    // Restore mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  it.skip("downloadCanvas sets loading state correctly", async () => {
    const mockBlob = new Blob(["template content"], { type: "text/csv" });
    const mockResponse = {
      data: mockBlob,
      headers: { "content-type": "text/csv" },
    };

    // Mock DOM methods to avoid errors
    const mockCreateElement = jest.spyOn(document, "createElement");
    const mockAppendChild = jest
      .spyOn(document.body, "appendChild")
      .mockImplementation();
    const mockRemoveChild = jest
      .spyOn(document.body, "removeChild")
      .mockImplementation();

    // Mock URL methods
    const mockCreateObjectURL = jest.fn().mockReturnValue("blob:mock-url");
    const mockRevokeObjectURL = jest.fn();

    Object.defineProperty(window, "URL", {
      value: {
        createObjectURL: mockCreateObjectURL,
        revokeObjectURL: mockRevokeObjectURL,
      },
      writable: true,
    });

    const mockLink = document.createElement("a");
    mockLink.href = "";
    mockLink.download = "";
    mockLink.click = jest.fn();

    mockCreateElement.mockReturnValue(mockLink);

    let loadingStateDuringCall = false;
    mockApi.get.mockImplementation(async () => {
      loadingStateDuringCall = useScheduleStore.getState().isDownloadingCanvas;
      return mockResponse;
    });

    await useScheduleStore.getState().downloadCanvas();

    expect(loadingStateDuringCall).toBe(true);
    expect(useScheduleStore.getState().isDownloadingCanvas).toBe(false);

    // Restore mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  // --- Modal state actions ---
  it("closeImportModal sets isImportModalOpen to false", () => {
    useScheduleStore.setState({ isImportModalOpen: true });
    useScheduleStore.getState().closeImportModal();
    expect(useScheduleStore.getState().isImportModalOpen).toBe(false);
  });

  it("openImportModal sets isImportModalOpen to true", () => {
    useScheduleStore.setState({ isImportModalOpen: false });
    useScheduleStore.getState().openImportModal();
    expect(useScheduleStore.getState().isImportModalOpen).toBe(true);
  });

  it("setActiveTabId sets activeTabId", () => {
    useScheduleStore.getState().setActiveTabId("new-tab-id");
    expect(useScheduleStore.getState().activeTabId).toBe("new-tab-id");
  });

  it("openCreateScheduleModal sets isCreateScheduleModalOpen to true", () => {
    useScheduleStore.setState({ isCreateScheduleModalOpen: false });
    useScheduleStore.getState().openCreateScheduleModal();
    expect(useScheduleStore.getState().isCreateScheduleModalOpen).toBe(true);
  });

  it("closeCreateScheduleModal sets isCreateScheduleModalOpen to false", () => {
    useScheduleStore.setState({ isCreateScheduleModalOpen: true });
    useScheduleStore.getState().closeCreateScheduleModal();
    expect(useScheduleStore.getState().isCreateScheduleModalOpen).toBe(false);
  });

  it("setWorkingHoursActivated sets workingHoursActivated", () => {
    useScheduleStore.getState().setWorkingHoursActivated(true);
    expect(useScheduleStore.getState().workingHoursActivated).toBe(true);
    useScheduleStore.getState().setWorkingHoursActivated(false);
    expect(useScheduleStore.getState().workingHoursActivated).toBe(false);
  });
});
