"use client";

import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useScheduleStore } from "../store/scheduleStore";
import { toast } from "@/hooks/use-toast";
import React, { useState } from "react";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("@/hooks/use-toast");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string, params?: { [key: string]: string }) => {
    if (key === "importModal.importCompletedWithErrorsDescription") {
      return `Import completed with ${params?.importedCount} imported and ${params?.errorsLength} errors.`;
    }
    if (key === "importModal.importSuccessfulDescription") {
      return `Imported ${params?.importedCount} schedules successfully.`;
    }
    return key;
  },
}));

// Mock UI components
jest.mock("@/components/ui/button", () => ({
  Button: ({
    children,
    onClick,
    disabled,
    className,
  }: {
    children: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
    className?: string;
  }) => (
    <button onClick={onClick} disabled={disabled} className={className}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({ children, open }: { children: React.ReactNode; open: boolean }) =>
    open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dialog-content">{children}</div>
  ),
  DialogHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dialog-header">{children}</div>
  ),
  DialogTitle: ({ children }: { children: React.ReactNode }) => (
    <h2 data-testid="dialog-title">{children}</h2>
  ),
}));

jest.mock("lucide-react", () => ({
  XCircle: ({ onClick }: { onClick: () => void }) => (
    <button onClick={onClick} role="button" aria-label="">
      X
    </button>
  ),
  Upload: () => <div data-testid="upload-icon">Upload</div>,
}));

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;
const mockToast = toast as jest.MockedFunction<typeof toast>;

// Mock ImportModal component
const MockImportModal = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const { importSchedules, isImporting } = useScheduleStore();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validTypes = [
        "text/csv",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];
      if (!validTypes.includes(file.type)) {
        mockToast({
          title: "importModal.invalidFileTypeTitle",
          description: "importModal.invalidFileTypeDescription",
          variant: "destructive",
        });
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  const handleImport = async () => {
    if (!selectedFile) {
      mockToast({
        title: "importModal.noFileSelectedTitle",
        description: "importModal.noFileSelectedDescription",
        variant: "warning",
      });
      return;
    }

    try {
      const result = await importSchedules(selectedFile);
      if (result.errors && result.errors.length > 0) {
        mockToast({
          title: "importModal.importCompletedWithErrorsTitle",
          description: `Import completed with ${result.importedCount} imported and ${result.errors.length} errors.`,
          variant: "warning",
        });
      } else {
        mockToast({
          title: "importModal.importSuccessfulTitle",
          description: `Imported ${result.importedCount} schedules successfully.`,
          variant: "default",
        });
      }
      onClose();
    } catch (error) {
      console.error("Import failed:", error);
    }
  };

  // Reset file when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      setSelectedFile(null);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div data-testid="dialog">
      <div data-testid="dialog-content">
        <div data-testid="dialog-header">
          <h2 data-testid="dialog-title">importModal.title</h2>
        </div>

        <div>
          <label htmlFor="file-input">importModal.uploadFileLabel</label>
          <input
            id="file-input"
            type="file"
            accept=".csv,.xlsx"
            onChange={handleFileChange}
            aria-label="importModal.uploadFileLabel"
          />
          <p>importModal.acceptedFormats</p>
        </div>

        {selectedFile && (
          <div>
            <span>{selectedFile.name}</span>
            <button onClick={handleRemoveFile} role="button" aria-label="">
              X
            </button>
          </div>
        )}

        <div>
          <button onClick={onClose}>cancelButton</button>
          <button onClick={handleImport} disabled={isImporting}>
            {isImporting
              ? "importModal.importingButton"
              : "importModal.importButton"}
          </button>
        </div>
      </div>
    </div>
  );
};

describe("ImportModal", () => {
  const mockProps = {
    isOpen: true,
    onClose: jest.fn(),
  };

  const mockStoreState = {
    importSchedules: jest.fn(),
    isImporting: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);
  });

  it("renders when open", () => {
    render(<MockImportModal {...mockProps} />);

    expect(screen.getByText("importModal.title")).toBeInTheDocument();
    expect(screen.getByText("importModal.uploadFileLabel")).toBeInTheDocument();
    expect(screen.getByText("importModal.acceptedFormats")).toBeInTheDocument();
  });

  it("does not render when closed", () => {
    render(<MockImportModal {...mockProps} isOpen={false} />);

    expect(screen.queryByText("importModal.title")).not.toBeInTheDocument();
  });

  it("handles CSV file selection", () => {
    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const csvFile = new File(["test,data"], "test.csv", { type: "text/csv" });

    fireEvent.change(fileInput, { target: { files: [csvFile] } });

    expect(screen.getByText("test.csv")).toBeInTheDocument();
  });

  it("handles XLSX file selection", () => {
    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const xlsxFile = new File(["test data"], "test.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    fireEvent.change(fileInput, { target: { files: [xlsxFile] } });

    expect(screen.getByText("test.xlsx")).toBeInTheDocument();
  });

  it("rejects invalid file types", () => {
    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const invalidFile = new File(["test"], "test.txt", { type: "text/plain" });

    fireEvent.change(fileInput, { target: { files: [invalidFile] } });

    expect(mockToast).toHaveBeenCalledWith({
      title: "importModal.invalidFileTypeTitle",
      description: "importModal.invalidFileTypeDescription",
      variant: "destructive",
    });
  });

  it("handles file removal", () => {
    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const csvFile = new File(["test,data"], "test.csv", { type: "text/csv" });

    fireEvent.change(fileInput, { target: { files: [csvFile] } });
    expect(screen.getByText("test.csv")).toBeInTheDocument();

    const removeButton = screen.getByRole("button", { name: "X" }); // XCircle button
    fireEvent.click(removeButton);

    expect(screen.queryByText("test.csv")).not.toBeInTheDocument();
  });

  it("handles successful import", async () => {
    const mockResult = { importedCount: 5, errors: [] };
    mockStoreState.importSchedules.mockResolvedValue(mockResult);

    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const csvFile = new File(["test,data"], "test.csv", { type: "text/csv" });

    fireEvent.change(fileInput, { target: { files: [csvFile] } });

    const importButton = screen.getByText("importModal.importButton");
    fireEvent.click(importButton);

    await waitFor(() => {
      expect(mockStoreState.importSchedules).toHaveBeenCalledWith(csvFile);
    });

    expect(mockToast).toHaveBeenCalledWith({
      title: "importModal.importSuccessfulTitle",
      description: "Imported 5 schedules successfully.",
      variant: "default",
    });

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("handles import with errors", async () => {
    const mockResult = {
      importedCount: 3,
      errors: [{ rowNumber: 2, message: "Invalid data" }],
    };
    mockStoreState.importSchedules.mockResolvedValue(mockResult);

    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const csvFile = new File(["test,data"], "test.csv", { type: "text/csv" });

    fireEvent.change(fileInput, { target: { files: [csvFile] } });

    const importButton = screen.getByText("importModal.importButton");
    fireEvent.click(importButton);

    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: "importModal.importCompletedWithErrorsTitle",
        description: "Import completed with 3 imported and 1 errors.",
        variant: "warning",
      });
    });
  });

  it("handles import failure", async () => {
    const consoleSpy = jest.spyOn(console, "error").mockImplementation();
    mockStoreState.importSchedules.mockRejectedValue(
      new Error("Import failed"),
    );

    render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const csvFile = new File(["test,data"], "test.csv", { type: "text/csv" });

    fireEvent.change(fileInput, { target: { files: [csvFile] } });

    const importButton = screen.getByText("importModal.importButton");
    fireEvent.click(importButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        "Import failed:",
        expect.any(Error),
      );
    });

    consoleSpy.mockRestore();
  });

  it("shows warning when trying to import without file", () => {
    render(<MockImportModal {...mockProps} />);

    const importButton = screen.getByText("importModal.importButton");
    fireEvent.click(importButton);

    expect(mockToast).toHaveBeenCalledWith({
      title: "importModal.noFileSelectedTitle",
      description: "importModal.noFileSelectedDescription",
      variant: "warning",
    });
  });

  it("shows loading state during import", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isImporting: true,
    });

    render(<MockImportModal {...mockProps} />);

    expect(screen.getByText("importModal.importingButton")).toBeInTheDocument();
    expect(screen.getByText("importModal.importingButton")).toBeDisabled();
  });

  it("handles cancel action", () => {
    render(<MockImportModal {...mockProps} />);

    const cancelButton = screen.getByText("cancelButton");
    fireEvent.click(cancelButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("clears file selection when modal closes", () => {
    const { rerender } = render(<MockImportModal {...mockProps} />);

    const fileInput = screen.getByLabelText("importModal.uploadFileLabel");
    const csvFile = new File(["test,data"], "test.csv", { type: "text/csv" });

    fireEvent.change(fileInput, { target: { files: [csvFile] } });
    expect(screen.getByText("test.csv")).toBeInTheDocument();

    // Close modal
    rerender(<MockImportModal {...mockProps} isOpen={false} />);

    // Reopen modal
    rerender(<MockImportModal {...mockProps} isOpen={true} />);

    expect(screen.queryByText("test.csv")).not.toBeInTheDocument();
  });
});
