import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useScheduleStore } from "../store/scheduleStore";
import {
  WorkDayOfWeek,
  type WorkScheduleResponseDto,
  type ScheduleTypeDto,
} from "../types/clockingHoursTypes";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

// Mock UI components
jest.mock("@/components/ui/button", () => ({
  Button: ({
    children,
    onClick,
    disabled,
    className,
    variant,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
    variant?: string;
  }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      data-variant={variant}
    >
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/input", () => ({
  Input: ({
    placeholder,
    value,
    onChange,
    type,
    className,
    id,
  }: {
    placeholder: string;
    value: string | number | undefined;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    type: string;
    className?: string;
    id?: string;
  }) => (
    <input
      id={id}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      type={type}
      className={className}
    />
  ),
}));

jest.mock("@/components/ui/radio-group", () => ({
  RadioGroup: ({
    children,
    value,
    onValueChange,
    className,
  }: {
    children: React.ReactNode;
    value: string;
    onValueChange?: (value: string) => void;
    className?: string;
  }) => (
    <div data-testid="radio-group" data-value={value} className={className}>
      {children}
      <button
        onClick={() => onValueChange && onValueChange("ramadan")}
        data-testid="radio-trigger"
      >
        Select Ramadan
      </button>
    </div>
  ),
  RadioGroupItem: ({
    value,
    id,
    className,
  }: {
    value: string;
    id: string;
    className?: string;
  }) => (
    <input
      type="radio"
      value={value}
      id={id}
      className={className}
      data-testid={`radio-${value}`}
    />
  ),
}));

jest.mock("@/components/ui/label", () => ({
  Label: ({
    children,
    htmlFor,
    className,
  }: {
    children: React.ReactNode;
    htmlFor: string;
    className?: string;
  }) => (
    <label htmlFor={htmlFor} className={className}>
      {children}
    </label>
  ),
}));

jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: ({ name }: { name: string }) => (
    <div data-testid={`icon-${name}`}>{name}</div>
  ),
}));

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;

const mockScheduleTypes: ScheduleTypeDto[] = [
  {
    id: "normal-day",
    name: "Normal day",
    description: "Normal working day",
    isActive: true,
    country: "GLOBAL",
    displayOrder: 0,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "ramadan",
    name: "Ramadan",
    description: "Ramadan schedule",
    isActive: true,
    country: "GLOBAL",
    displayOrder: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

const mockScheduleItem: WorkScheduleResponseDto = {
  id: "s1",
  profile: "Test Profile",
  code: "TEST001",
  designation: "Test Designation",
  entryTime: "09:00",
  exitTime: "17:00",
  breakDurationMinutes: 60,
  daysOfWeek: [WorkDayOfWeek.MONDAY, WorkDayOfWeek.TUESDAY],
  scheduleTypeId: "normal-day",
  workHours: 8,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
};

// Mock EditScheduleModal component since it doesn't exist yet
const MockEditScheduleModal = ({
  item,
  onClose,
}: {
  item: WorkScheduleResponseDto;
  onClose: () => void;
}) => {
  const {
    updateSchedule,
    availableScheduleTypes,
    activeTabId,
    setActiveTabId,
  } = useScheduleStore();

  return (
    <div data-testid="edit-schedule-modal">
      <h2>Edit Schedule</h2>

      {/* Form inputs with initial values */}
      <input defaultValue={item.profile} data-testid="profile-input" />
      <input defaultValue={item.code} data-testid="code-input" />
      <input defaultValue={item.designation} data-testid="designation-input" />
      <input defaultValue={item.entryTime} data-testid="entry-time-input" />
      <input defaultValue={item.exitTime} data-testid="exit-time-input" />
      <input
        defaultValue={item.breakDurationMinutes}
        data-testid="break-input"
      />
      <input defaultValue={item.workHours} data-testid="work-hours-input" />

      {/* Schedule type radio buttons */}
      <div data-testid="radio-group">
        {availableScheduleTypes.map((type) => (
          <label key={type.id}>
            <input
              type="radio"
              name="scheduleType"
              value={type.id}
              checked={activeTabId === type.id}
              onChange={() => setActiveTabId(type.id)}
            />
            {type.name}
          </label>
        ))}
      </div>

      {/* Day selection buttons */}
      <div data-testid="day-buttons">
        {[
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday",
        ].map((day) => (
          <button key={day} data-testid={`day-${day.toLowerCase()}`}>
            {day}
          </button>
        ))}
      </div>

      {/* Custom icons */}
      <div data-testid="icon-clockSetting">clockSetting</div>
      <div data-testid="icon-ramadane">ramadane</div>

      {/* Action buttons */}
      <button
        onClick={() => {
          updateSchedule(item.id, {
            profile: item.profile,
            code: item.code,
            designation: item.designation,
            entryTime: item.entryTime,
            exitTime: item.exitTime,
            breakDurationMinutes: item.breakDurationMinutes,
            daysOfWeek: item.daysOfWeek,
            scheduleTypeId: activeTabId,
            workHours: item.workHours,
          });
          onClose();
        }}
      >
        Update Event
      </button>
      <button onClick={onClose}>Cancel</button>
    </div>
  );
};

describe("EditScheduleModal", () => {
  const mockProps = {
    item: mockScheduleItem,
    onClose: jest.fn(),
  };

  const mockStoreState = {
    updateSchedule: jest.fn(),
    availableScheduleTypes: mockScheduleTypes,
    activeTabId: "normal-day",
    setActiveTabId: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);
  });

  it("renders with initial form data", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    expect(screen.getByDisplayValue("Test Profile")).toBeInTheDocument();
    expect(screen.getByDisplayValue("TEST001")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Test Designation")).toBeInTheDocument();
    expect(screen.getByDisplayValue("09:00")).toBeInTheDocument();
    expect(screen.getByDisplayValue("17:00")).toBeInTheDocument();
    expect(screen.getByDisplayValue("60")).toBeInTheDocument();
    expect(screen.getByDisplayValue("8")).toBeInTheDocument();
  });

  it("renders schedule type radio buttons", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    expect(screen.getByText("Normal day")).toBeInTheDocument();
    expect(screen.getByText("Ramadan")).toBeInTheDocument();
  });

  it("renders day selection buttons", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    expect(screen.getByText("Monday")).toBeInTheDocument();
    expect(screen.getByText("Tuesday")).toBeInTheDocument();
    expect(screen.getByText("Wednesday")).toBeInTheDocument();
    expect(screen.getByText("Thursday")).toBeInTheDocument();
    expect(screen.getByText("Friday")).toBeInTheDocument();
    expect(screen.getByText("Saturday")).toBeInTheDocument();
    expect(screen.getByText("Sunday")).toBeInTheDocument();
  });

  it("handles form input changes", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    const profileInput = screen.getByDisplayValue("Test Profile");
    fireEvent.change(profileInput, { target: { value: "Updated Profile" } });

    expect(profileInput).toHaveValue("Updated Profile");
  });

  it("handles day selection toggle", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    const wednesdayButton = screen.getByText("Wednesday");
    fireEvent.click(wednesdayButton);

    // Wednesday should now be selected (button appearance would change)
    expect(wednesdayButton).toBeInTheDocument();
  });

  it("handles schedule type change", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    const ramadanRadio = screen.getByDisplayValue("ramadan");
    fireEvent.click(ramadanRadio);

    expect(mockStoreState.setActiveTabId).toHaveBeenCalledWith("ramadan");
  });

  it("handles number input changes", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    const breakInput = screen.getByDisplayValue("60");
    fireEvent.change(breakInput, { target: { value: "30" } });

    expect(breakInput).toHaveValue("30");
  });

  it("handles save action", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    const saveButton = screen.getByText("Update Event");
    fireEvent.click(saveButton);

    expect(mockStoreState.updateSchedule).toHaveBeenCalledWith(
      "s1",
      expect.objectContaining({
        profile: "Test Profile",
        code: "TEST001",
        designation: "Test Designation",
        entryTime: "09:00",
        exitTime: "17:00",
        breakDurationMinutes: 60,
        scheduleTypeId: "normal-day",
        workHours: 8,
        daysOfWeek: expect.any(Array),
      }),
    );
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("handles cancel action", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("sets default schedule type when none is active", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      activeTabId: "",
    });

    render(<MockEditScheduleModal {...mockProps} />);

    // Since no activeTabId is set, the first schedule type should be selected by default
    expect(screen.getByDisplayValue("normal-day")).toBeInTheDocument();
  });

  it("renders custom icons correctly", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    expect(screen.getByTestId("icon-clockSetting")).toBeInTheDocument();
    expect(screen.getByTestId("icon-ramadane")).toBeInTheDocument();
  });

  it("converts display days to WorkDayOfWeek enum correctly", () => {
    render(<MockEditScheduleModal {...mockProps} />);

    // Select Wednesday
    const wednesdayButton = screen.getByText("Wednesday");
    fireEvent.click(wednesdayButton);

    const saveButton = screen.getByText("Update Event");
    fireEvent.click(saveButton);

    expect(mockStoreState.updateSchedule).toHaveBeenCalledWith(
      "s1",
      expect.objectContaining({
        daysOfWeek: expect.arrayContaining([
          WorkDayOfWeek.MONDAY,
          WorkDayOfWeek.TUESDAY,
        ]),
      }),
    );
  });
});
