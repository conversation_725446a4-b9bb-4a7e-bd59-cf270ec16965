import "@testing-library/jest-dom";

// Mock Date for consistent testing of date-dependent logic
const MOCK_DATE = new Date("2025-07-24T00:00:00Z");
jest.useFakeTimers();
jest.setSystemTime(MOCK_DATE);

// Store original console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Mock console.error to prevent noise during tests unless explicitly needed
console.error = (...args: unknown[]) => {
  // Allow test-related errors to pass through
  if (
    typeof args[0] === "string" &&
    (args[0].includes("Error: Not implemented: navigation") ||
      args[0].includes("Warning: ReactDOM.render"))
  ) {
    return; // Suppress specific JSDOM warnings
  }
  // For actual test errors, call the original
  originalConsoleError.call(console, ...args);
};

console.warn = (...args: unknown[]) => {
  // Allow test-related warnings to pass through
  if (
    typeof args[0] === "string" &&
    args[0].includes("Warning: ReactDOM.render")
  ) {
    return; // Suppress specific React warnings
  }
  originalConsoleWarn.call(console, ...args);
};

// Mock next-intl useTranslations globally if not mocked per file
jest.mock("next-intl", () => ({
  useTranslations: jest.fn(
    () => (key: string, params?: Record<string, string>) => {
      if (key === "importModal.importCompletedWithErrorsDescription") {
        return `Import completed with ${params?.importedCount} imported and ${params?.errorsLength} errors.`;
      }
      if (key === "importModal.importSuccessfulDescription") {
        return `Imported ${params?.importedCount} schedules successfully.`;
      }
      return key; // Default: return the key itself
    },
  ),
}));
