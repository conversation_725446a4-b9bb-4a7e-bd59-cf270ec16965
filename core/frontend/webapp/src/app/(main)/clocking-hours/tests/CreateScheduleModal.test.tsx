"use client";

import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { CreateScheduleModal } from "../components/CreateScheduleModal";
import { useScheduleStore } from "../store/scheduleStore";
import {
  WorkDayOfWeek,
  type ScheduleTypeDto,
} from "../types/clockingHoursTypes";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

// Mock UI components
jest.mock("@/components/ui/button", () => ({
  Button: ({
    children,
    onClick,
    disabled,
    className,
    variant,
    size,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
    variant?: string;
    size?: string;
  }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      data-variant={variant}
      data-size={size}
    >
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/input", () => ({
  Input: ({
    placeholder,
    value,
    onChange,
    type,
    className,
    id,
    min,
    max,
    step,
  }: {
    placeholder: string;
    value: string | number | undefined;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    type: string;
    className?: string;
    id?: string;
    min?: number;
    max?: number;
    step?: number;
  }) => (
    <input
      id={id}
      placeholder={placeholder}
      value={type === "number" ? String(value) : value}
      onChange={onChange}
      type={type}
      className={className}
      min={min}
      max={max}
      step={step}
    />
  ),
}));

jest.mock("@/components/ui/radio-group", () => ({
  RadioGroup: ({
    children,
    value,
    onValueChange,
    className,
  }: {
    children: React.ReactNode;
    value: string;
    onValueChange?: (value: string) => void;
    className?: string;
  }) => (
    <div data-testid="radio-group" data-value={value} className={className}>
      {children}
      <button
        onClick={() => onValueChange && onValueChange("ramadan")}
        data-testid="radio-trigger"
      >
        Select Ramadan
      </button>
    </div>
  ),
  RadioGroupItem: ({
    value,
    id,
    className,
  }: {
    value: string;
    id: string;
    className?: string;
  }) => (
    <input
      type="radio"
      value={value}
      id={id}
      className={className}
      data-testid={`radio-${value}`}
    />
  ),
}));

jest.mock("@/components/ui/label", () => ({
  Label: ({
    children,
    htmlFor,
    className,
  }: {
    children: React.ReactNode;
    htmlFor: string;
    className?: string;
  }) => (
    <label htmlFor={htmlFor} className={className}>
      {children}
    </label>
  ),
}));

jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({
    children,
    open,
    onOpenChange,
  }: {
    children: React.ReactNode;
    open: boolean;
    onOpenChange: (open: boolean) => void;
  }) =>
    open ? (
      <div
        data-testid="dialog"
        onClick={() => onOpenChange && onOpenChange(false)}
      >
        {children}
      </div>
    ) : null,
  DialogContent: ({
    children,
    className,
  }: {
    children: React.ReactNode;
    className?: string;
  }) => (
    <div data-testid="dialog-content" className={className}>
      {children}
    </div>
  ),
  DialogHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dialog-header">{children}</div>
  ),
  DialogTitle: ({
    children,
    className,
  }: {
    children: React.ReactNode;
    className?: string;
  }) => (
    <h2 data-testid="dialog-title" className={className}>
      {children}
    </h2>
  ),
}));

jest.mock("lucide-react", () => ({
  CalendarDays: ({ className }: { className?: string }) => (
    <div data-testid="calendar-days-icon" className={className}>
      CalendarDays
    </div>
  ),
  Clock: ({ className }: { className?: string }) => (
    <div data-testid="clock-icon" className={className}>
      Clock
    </div>
  ),
}));

jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: ({ name, className }: { name: string; className?: string }) => (
    <div data-testid={`icon-${name}`} className={className}>
      {name}
    </div>
  ),
}));

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;

const mockScheduleTypes: ScheduleTypeDto[] = [
  {
    id: "normal-day",
    name: "Normal day",
    description: "Normal working day",
    isActive: true,
    country: "GLOBAL",
    displayOrder: 0,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "ramadan",
    name: "Ramadan",
    description: "Ramadan schedule",
    isActive: true,
    country: "GLOBAL",
    displayOrder: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

describe("CreateScheduleModal", () => {
  const mockProps = {
    isOpen: true,
    onClose: jest.fn(),
  };

  const mockStoreState = {
    addSchedule: jest.fn(),
    availableScheduleTypes: mockScheduleTypes,
    activeTabId: "normal-day",
    setActiveTabId: jest.fn(),
    isCreating: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);
  });

  it("renders when open", () => {
    render(<CreateScheduleModal {...mockProps} />);

    expect(screen.getByTestId("dialog")).toBeInTheDocument();
    expect(screen.getByTestId("dialog-title")).toHaveTextContent(
      "Create new profile",
    );
  });

  it("does not render when closed", () => {
    render(<CreateScheduleModal {...mockProps} isOpen={false} />);

    expect(screen.queryByTestId("dialog")).not.toBeInTheDocument();
  });

  it("renders form fields", () => {
    render(<CreateScheduleModal {...mockProps} />);

    expect(screen.getByPlaceholderText("Profile")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Code")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Designation")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Break Duration")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("e.g., 8.5")).toBeInTheDocument();
  });

  it("renders schedule type options", () => {
    render(<CreateScheduleModal {...mockProps} />);

    expect(screen.getByText("Normal day")).toBeInTheDocument();
    expect(screen.getByText("Ramadan")).toBeInTheDocument();
    expect(screen.getByTestId("icon-clockSetting")).toBeInTheDocument();
    expect(screen.getByTestId("icon-ramadane")).toBeInTheDocument();
  });

  it("renders day selection buttons", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const days = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];
    days.forEach((day) => {
      expect(screen.getByText(day)).toBeInTheDocument();
    });
  });

  it("renders time inputs with clock icons", () => {
    render(<CreateScheduleModal {...mockProps} />);

    expect(screen.getByLabelText(/Time of entry/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Exit Time/)).toBeInTheDocument();
    expect(screen.getAllByTestId("clock-icon")).toHaveLength(2);
  });

  it("handles form input changes", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const profileInput = screen.getByPlaceholderText("Profile");
    fireEvent.change(profileInput, { target: { value: "Test Profile" } });
    expect(profileInput).toHaveValue("Test Profile");

    const codeInput = screen.getByPlaceholderText("Code");
    fireEvent.change(codeInput, { target: { value: "TEST001" } });
    expect(codeInput).toHaveValue("TEST001");
  });

  it("handles number input changes", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const breakInput = screen.getByPlaceholderText("Break Duration");
    fireEvent.change(breakInput, { target: { value: 30 } });
    expect(breakInput).toHaveValue(30);

    const workHoursInput = screen.getByPlaceholderText("e.g., 8.5");
    fireEvent.change(workHoursInput, { target: { value: 8.5 } });
    expect(workHoursInput).toHaveValue(8.5);
  });

  it("handles day selection toggle", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const mondayButton = screen.getByText("Monday");
    expect(mondayButton).toBeInTheDocument();

    // Click to toggle selection
    fireEvent.click(mondayButton);
    expect(mondayButton).toBeInTheDocument();
  });

  it("handles schedule type selection", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const radioTrigger = screen.getByTestId("radio-trigger");
    fireEvent.click(radioTrigger);
  });

  it("handles save action with valid data", async () => {
    mockStoreState.addSchedule.mockResolvedValue(undefined);

    render(<CreateScheduleModal {...mockProps} />);

    // Fill in form fields
    fireEvent.change(screen.getByPlaceholderText("Profile"), {
      target: { value: "Test Profile" },
    });
    fireEvent.change(screen.getByPlaceholderText("Code"), {
      target: { value: "TEST001" },
    });
    fireEvent.change(screen.getByPlaceholderText("Designation"), {
      target: { value: "Test Designation" },
    });
    fireEvent.change(screen.getByLabelText(/Time of entry/), {
      target: { value: "09:00" },
    });
    fireEvent.change(screen.getByLabelText(/Exit Time/), {
      target: { value: "17:00" },
    });
    fireEvent.change(screen.getByPlaceholderText("Break Duration"), {
      target: { value: "60" },
    });
    fireEvent.change(screen.getByPlaceholderText("e.g., 8.5"), {
      target: { value: "8" },
    });

    const saveButton = screen.getByText("Save Event");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockStoreState.addSchedule).toHaveBeenCalledWith(
        expect.objectContaining({
          profile: "Test Profile",
          code: "TEST001",
          designation: "Test Designation",
          entryTime: "09:00",
          exitTime: "17:00",
          breakDurationMinutes: 60,
          workHours: 8,
          scheduleTypeId: "normal-day",
          daysOfWeek: expect.any(Array),
        }),
      );
    });

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("handles cancel action", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("shows loading state when creating", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isCreating: true,
    });

    render(<CreateScheduleModal {...mockProps} />);

    const saveButton = screen.getByText("Creating...");
    expect(saveButton).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });

  it("sets default active tab when none is set", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      activeTabId: "",
    });

    render(<CreateScheduleModal {...mockProps} />);
  });

  it("resets form when modal opens", () => {
    const { rerender } = render(
      <CreateScheduleModal {...mockProps} isOpen={false} />,
    );

    // Open modal
    rerender(<CreateScheduleModal {...mockProps} isOpen={true} />);

    // Check that form fields are reset
    expect(screen.getByPlaceholderText("Profile")).toHaveValue("");
    expect(screen.getByPlaceholderText("Code")).toHaveValue("");
    expect(screen.getByPlaceholderText("Designation")).toHaveValue("");
  });

  it("handles dialog close via onOpenChange", () => {
    render(<CreateScheduleModal {...mockProps} />);

    const dialog = screen.getByTestId("dialog");
    fireEvent.click(dialog);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("converts display days to WorkDayOfWeek enum", async () => {
    mockStoreState.addSchedule.mockResolvedValue(undefined);

    render(<CreateScheduleModal {...mockProps} />);

    // Fill required fields and save
    fireEvent.change(screen.getByPlaceholderText("Profile"), {
      target: { value: "Test" },
    });

    const saveButton = screen.getByText("Save Event");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockStoreState.addSchedule).toHaveBeenCalledWith(
        expect.objectContaining({
          daysOfWeek: expect.arrayContaining([
            WorkDayOfWeek.MONDAY,
            WorkDayOfWeek.TUESDAY,
            WorkDayOfWeek.WEDNESDAY,
            WorkDayOfWeek.THURSDAY,
            WorkDayOfWeek.FRIDAY,
          ]),
        }),
      );
    });
  });

  it("handles error during save", async () => {
    const consoleSpy = jest.spyOn(console, "error").mockImplementation();
    mockStoreState.addSchedule.mockRejectedValue(new Error("Save failed"));

    render(<CreateScheduleModal {...mockProps} />);

    const saveButton = screen.getByText("Save Event");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        "Error saving schedule:",
        expect.any(Error),
      );
    });

    consoleSpy.mockRestore();
  });
});
