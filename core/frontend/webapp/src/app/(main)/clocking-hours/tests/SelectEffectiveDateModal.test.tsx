"use client";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useScheduleStore } from "../store/scheduleStore";
import type { ScheduleTypeDto } from "../types/clockingHoursTypes";
import { useEffect, useState } from "react";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;

const mockScheduleType: ScheduleTypeDto = {
  id: "type-1",
  name: "Ramadan",
  description: "Ramadan schedule",
  isActive: true,
  country: "GLOBAL",
  displayOrder: 1,
  effectiveStartDate: "2024-01-01T00:00:00Z",
  effectiveEndDate: "2024-01-31T23:59:59Z",
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
};

// Mock SelectEffectiveDateModal component
const MockSelectEffectiveDateModal = ({
  open,
  onClose,
  selectedScheduleTypeId,
}: {
  open: boolean;
  onClose: () => void;
  selectedScheduleTypeId: string;
}) => {
  const {
    availableScheduleTypes,
    setEffectiveDateRange,
    updateScheduleTypeEffectiveDates,
    isUpdatingScheduleType,
  } = useScheduleStore();

  const [selectedRange, setSelectedRange] = useState<{
    from: Date | null;
    to: Date | null;
  }>({
    from: null,
    to: null,
  });

  const selectedType = availableScheduleTypes.find(
    (type) => type.id === selectedScheduleTypeId,
  );

  // Initialize with existing dates
  useEffect(() => {
    if (selectedType && open) {
      setSelectedRange({
        from: selectedType.effectiveStartDate
          ? new Date(selectedType.effectiveStartDate)
          : null,
        to: selectedType.effectiveEndDate
          ? new Date(selectedType.effectiveEndDate)
          : null,
      });
    }
  }, [selectedType, open]);

  // Reset when modal closes
  useEffect(() => {
    if (!open) {
      setSelectedRange({ from: null, to: null });
    }
  }, [open]);

  const handleSave = async () => {
    if (!selectedRange.from || !selectedRange.to) return;

    try {
      await updateScheduleTypeEffectiveDates(
        selectedScheduleTypeId,
        selectedRange.from,
        selectedRange.to,
      );
      setEffectiveDateRange(selectedRange.from, selectedRange.to);
      onClose();
    } catch (error) {
      console.error("Error saving effective dates:", error);
    }
  };

  const handleDateRangeSelect = (range: { from: Date; to: Date }) => {
    setSelectedRange(range);
  };

  if (!open) return null;

  return (
    <div data-testid="dialog-content" role="dialog">
      <div className="flex flex-col items-center w-full">
        <div data-testid="dialog-header">
          <h2 data-testid="dialog-title">Select Effective Date</h2>
          <div data-testid="dialog-description" />
        </div>

        <div className="flex flex-col items-center w-full px-6 pt-6">
          <div className="flex items-center justify-center">
            <span data-testid="custom-icon">Icon</span>
          </div>

          <div className="w-full flex flex-col items-center">
            <div data-testid="calendar">
              <button
                onClick={() =>
                  handleDateRangeSelect({
                    from: new Date("2024-01-01"),
                    to: new Date("2024-01-31"),
                  })
                }
              >
                Select Range
              </button>
              <div data-testid="selected-range">
                {selectedRange.from?.toISOString()} -{" "}
                {selectedRange.to?.toISOString()}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center w-full px-6 pb-6 pt-4 mt-2">
          <button
            className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 border-gray-300 w-28 bg-transparent"
            onClick={onClose}
          >
            Cancel
          </button>

          <button
            className="border border-black flex items-center gap-2"
            onClick={handleSave}
            disabled={
              !selectedRange.from || !selectedRange.to || isUpdatingScheduleType
            }
            data-testid="save-button"
          >
            {isUpdatingScheduleType ? "Saving..." : "save"}
          </button>
        </div>
      </div>
    </div>
  );
};

describe("SelectEffectiveDateModal", () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    selectedScheduleTypeId: "type-1",
  };

  const mockStoreState = {
    availableScheduleTypes: [mockScheduleType],
    setEffectiveDateRange: jest.fn(),
    updateScheduleTypeEffectiveDates: jest.fn(),
    isUpdatingScheduleType: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);
  });

  it("renders when open is true", () => {
    render(<MockSelectEffectiveDateModal {...mockProps} />);

    expect(screen.getByText("Select Effective Date")).toBeInTheDocument();
    expect(screen.getByTestId("calendar")).toBeInTheDocument();
  });

  it("does not render when open is false", () => {
    render(<MockSelectEffectiveDateModal {...mockProps} open={false} />);

    expect(screen.queryByText("Select Effective Date")).not.toBeInTheDocument();
  });

  it("initializes with existing effective dates", () => {
    render(<MockSelectEffectiveDateModal {...mockProps} />);

    expect(screen.getByTestId("calendar")).toBeInTheDocument();
    expect(screen.getByTestId("selected-range")).toHaveTextContent(
      "2024-01-01T00:00:00.000Z - 2024-01-31T23:59:59.000Z",
    );
  });

  it("handles date range selection", () => {
    render(<MockSelectEffectiveDateModal {...mockProps} />);

    const selectButton = screen.getByText("Select Range");
    fireEvent.click(selectButton);

    expect(screen.getByTestId("selected-range")).toHaveTextContent(
      "2024-01-01T00:00:00.000Z - 2024-01-31T00:00:00.000Z",
    );
  });

  it("handles save action successfully", async () => {
    mockStoreState.updateScheduleTypeEffectiveDates.mockResolvedValue(
      undefined,
    );

    render(<MockSelectEffectiveDateModal {...mockProps} />);

    // Select a date range first
    const selectButton = screen.getByText("Select Range");
    fireEvent.click(selectButton);

    // Click save
    const saveButton = screen.getByText("save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(
        mockStoreState.updateScheduleTypeEffectiveDates,
      ).toHaveBeenCalledWith("type-1", expect.any(Date), expect.any(Date));
    });

    expect(mockStoreState.setEffectiveDateRange).toHaveBeenCalled();
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("handles save action with error", async () => {
    const consoleSpy = jest.spyOn(console, "error").mockImplementation();
    mockStoreState.updateScheduleTypeEffectiveDates.mockRejectedValue(
      new Error("Save failed"),
    );

    render(<MockSelectEffectiveDateModal {...mockProps} />);

    // Select a date range first
    const selectButton = screen.getByText("Select Range");
    fireEvent.click(selectButton);

    // Click save
    const saveButton = screen.getByText("save");
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        "Error saving effective dates:",
        expect.any(Error),
      );
    });

    consoleSpy.mockRestore();
  });

  it("disables save button when no date range selected", () => {
    // Mock with no existing dates
    const mockStoreStateNoDate = {
      ...mockStoreState,
      availableScheduleTypes: [
        {
          ...mockScheduleType,
          effectiveStartDate: undefined,
          effectiveEndDate: undefined,
        },
      ],
    };
    mockUseScheduleStore.mockReturnValue(mockStoreStateNoDate);

    render(<MockSelectEffectiveDateModal {...mockProps} />);

    const saveButton = screen.getByText("save");
    expect(saveButton).toBeDisabled();
  });

  it("shows loading state when updating", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isUpdatingScheduleType: true,
    });

    render(<MockSelectEffectiveDateModal {...mockProps} />);

    expect(screen.getByText("Saving...")).toBeInTheDocument();
    expect(screen.getByText("Saving...")).toBeDisabled();
  });

  it("handles cancel action", () => {
    render(<MockSelectEffectiveDateModal {...mockProps} />);

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it("resets range when modal closes and reopens", () => {
    const { rerender } = render(
      <MockSelectEffectiveDateModal {...mockProps} />,
    );

    // Close modal
    rerender(<MockSelectEffectiveDateModal {...mockProps} open={false} />);

    // Reopen modal
    rerender(<MockSelectEffectiveDateModal {...mockProps} open={true} />);

    expect(screen.getByTestId("calendar")).toBeInTheDocument();
    // Should reinitialize with existing dates
    expect(screen.getByTestId("selected-range")).toHaveTextContent(
      "2024-01-01T00:00:00.000Z - 2024-01-31T23:59:59.000Z",
    );
  });
});
