"use client";
import { render, screen, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import Index from "../page";
import { useScheduleStore } from "../store/scheduleStore";
import type {
  ScheduleTypeDto,
  WorkScheduleResponseDto,
} from "../types/clockingHoursTypes";
import { WorkDayOfWeek } from "../types/clockingHoursTypes";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("../components/WorkingHoursHeader", () => ({
  WorkingHoursHeader: () => (
    <div data-testid="working-hours-header">Working Hours Header</div>
  ),
}));
jest.mock("../components/ScheduleFilters", () => ({
  ScheduleFilters: (props: {
    onApplyFilters: () => void;
    onClearFilters: () => void;
  }) => (
    <div data-testid="schedule-filters">
      <button onClick={props.onApplyFilters}>Apply Filters</button>
      <button onClick={props.onClearFilters}>Clear Filters</button>
    </div>
  ),
}));
jest.mock("../components/ScheduleDataTable", () => ({
  ScheduleDataTable: ({
    data,
    scheduleType,
  }: {
    data: WorkScheduleResponseDto[];
    scheduleType: ScheduleTypeDto;
  }) => (
    <div data-testid="schedule-data-table">
      <div data-testid="table-schedule-type">{scheduleType.name}</div>
      <div data-testid="table-data-count">{data.length}</div>
    </div>
  ),
}));
jest.mock("../components/ImportModal", () => ({
  ImportModal: ({ isOpen }: { isOpen: boolean }) =>
    isOpen ? <div data-testid="import-modal">Import Modal</div> : null,
}));
jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: ({ name }: { name: string }) => (
    <div data-testid={`icon-${name}`}>{name}</div>
  ),
}));

// Mock Tabs components to avoid onValueChange warning
jest.mock("@/components/ui/tabs", () => ({
  Tabs: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <div data-testid="tabs" data-value={value}>
      {children}
    </div>
  ),
  TabsList: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tabs-list">{children}</div>
  ),
  TabsTrigger: ({
    children,
    value,
    className,
  }: {
    children: React.ReactNode;
    value: string;
    className?: string;
  }) => (
    <button
      data-testid={`tab-trigger-${value}`}
      data-value={value}
      className={className}
    >
      {children}
    </button>
  ),
  TabsContent: ({
    children,
    value,
  }: {
    children: React.ReactNode;
    value: string;
  }) => (
    <div data-testid={`tab-content-${value}`} data-value={value}>
      {children}
    </div>
  ),
}));

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;

const mockScheduleTypes: ScheduleTypeDto[] = [
  {
    id: "normal-day",
    name: "Normal day",
    description: "Normal working day",
    isActive: true,
    country: "GLOBAL",
    displayOrder: 0,
    effectiveStartDate: "2024-01-01T00:00:00Z",
    effectiveEndDate: "2024-12-31T23:59:59Z",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "ramadan",
    name: "Ramadan",
    description: "Ramadan schedule",
    isActive: true,
    country: "GLOBAL",
    displayOrder: 1,
    effectiveStartDate: "2024-03-01T00:00:00Z",
    effectiveEndDate: "2024-04-01T23:59:59Z",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

const mockScheduleData: WorkScheduleResponseDto[] = [
  {
    id: "s1",
    profile: "Profile A",
    code: "CODE1",
    designation: "Designation 1",
    entryTime: "09:00",
    exitTime: "17:00",
    breakDurationMinutes: 60,
    daysOfWeek: [WorkDayOfWeek.MONDAY],
    scheduleTypeId: "normal-day",
    workHours: 8,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

describe("Index Page", () => {
  const mockStoreState = {
    scheduleData: mockScheduleData,
    effectiveDateRange: {
      from: new Date("2024-01-01"),
      to: new Date("2024-12-31"),
    },
    filters: {},
    isImportModalOpen: false,
    fetchSchedules: jest.fn(),
    setFilters: jest.fn(),
    clearFilters: jest.fn(),
    isLoading: false,
    availableScheduleTypes: mockScheduleTypes,
    isScheduleTypeLoading: false,
    closeImportModal: jest.fn(),
    fetchAvailableScheduleTypes: jest.fn(),
    activeTabId: "normal-day",
    setActiveTabId: jest.fn(),
    setWorkingHoursActivated: jest.fn(),
    setEffectiveDateRange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);

    // Suppress console.log for effectiveDateRange to reduce noise
    jest.spyOn(console, "log").mockImplementation((message, label) => {
      if (label !== "effectiveDateRange") {
        console.log(message, label);
      }
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("renders main layout components", () => {
    render(<Index />);

    expect(screen.getByTestId("working-hours-header")).toBeInTheDocument();
    expect(screen.getByTestId("schedule-filters")).toBeInTheDocument();
  });

  it("fetches available schedule types on mount", () => {
    render(<Index />);

    expect(mockStoreState.fetchAvailableScheduleTypes).toHaveBeenCalled();
  });

  it("renders schedule type tabs", () => {
    render(<Index />);

    // Use more specific selectors to avoid ambiguity
    expect(screen.getByTestId("tab-trigger-normal-day")).toBeInTheDocument();
    expect(screen.getByTestId("tab-trigger-ramadan")).toBeInTheDocument();
  });

  it("shows loading state for schedule types", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isScheduleTypeLoading: true,
      availableScheduleTypes: [],
    });

    render(<Index />);

    expect(screen.getByText("Loading schedule types...")).toBeInTheDocument();
  });

  it("shows loading state for schedules", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isLoading: true,
    });

    render(<Index />);

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders schedule data table when not loading", () => {
    render(<Index />);

    expect(screen.getByTestId("schedule-data-table")).toBeInTheDocument();
    expect(screen.getByTestId("table-schedule-type")).toHaveTextContent(
      "Normal day",
    );
    expect(screen.getByTestId("table-data-count")).toHaveTextContent("1");
  });

  it("renders import modal when open", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isImportModalOpen: true,
    });

    render(<Index />);

    expect(screen.getByTestId("import-modal")).toBeInTheDocument();
  });

  it("sets default active tab when none is set", async () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      activeTabId: "",
    });

    render(<Index />);

    await waitFor(() => {
      expect(mockStoreState.setActiveTabId).toHaveBeenCalledWith("normal-day");
    });
  });

  it("fetches schedules when active tab changes", async () => {
    render(<Index />);

    await waitFor(() => {
      expect(mockStoreState.fetchSchedules).toHaveBeenCalledWith(
        "normal-day",
        {},
      );
    });
  });

  it("updates effective date range when active tab changes", async () => {
    render(<Index />);

    await waitFor(() => {
      expect(mockStoreState.setEffectiveDateRange).toHaveBeenCalledWith(
        expect.any(Date),
        expect.any(Date),
      );
    });
  });

  it("activates working hours when within effective date range", async () => {
    // Mock current date to be within range
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2024-06-15"));

    render(<Index />);

    await waitFor(() => {
      expect(mockStoreState.setWorkingHoursActivated).toHaveBeenCalledWith(
        true,
      );
    });

    jest.useRealTimers();
  });

  it("deactivates working hours when outside effective date range", async () => {
    // Mock current date to be outside range
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2025-01-15"));

    render(<Index />);

    await waitFor(() => {
      expect(mockStoreState.setWorkingHoursActivated).toHaveBeenCalledWith(
        false,
      );
    });

    jest.useRealTimers();
  });

  it("renders Ramadan icon for Ramadan tab", () => {
    render(<Index />);

    expect(screen.getByTestId("icon-ramadane")).toBeInTheDocument();
  });

  it("shows check icon for active tab", () => {
    render(<Index />);

    // Use the specific tab trigger selector
    const activeTab = screen.getByTestId("tab-trigger-normal-day");
    expect(activeTab).toHaveAttribute("data-value", "normal-day");
  });
});
