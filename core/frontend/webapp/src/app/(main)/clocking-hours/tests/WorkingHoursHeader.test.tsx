"use client";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { WorkingHoursHeader } from "../components/WorkingHoursHeader";
import { useScheduleStore } from "../store/scheduleStore";

// Mock dependencies
jest.mock("../store/scheduleStore");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));
jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: ({ name }: { name: string }) => (
    <div data-testid={`icon-${name}`}>{name}</div>
  ),
}));
jest.mock("@/components/common/CustomButtons", () => ({
  IconButton: ({
    icon,
    label,
    onClick,
    className,
    id,
    disabled,
  }: {
    icon?: () => React.ReactElement;
    label: string;
    onClick: () => void;
    className?: string;
    id?: string;
    disabled?: boolean;
  }) => (
    <button
      onClick={onClick}
      className={className}
      id={id}
      disabled={disabled}
      data-testid="icon-button"
    >
      {icon && icon()}
      {label}
    </button>
  ),
}));
jest.mock("../components/CreateScheduleModal", () => ({
  CreateScheduleModal: ({
    isOpen,
    onClose,
  }: {
    isOpen: boolean;
    onClose: () => void;
  }) =>
    isOpen ? (
      <div data-testid="create-schedule-modal">
        <button onClick={onClose} data-testid="modal-close">
          Close Modal
        </button>
      </div>
    ) : null,
}));

const mockUseScheduleStore = useScheduleStore as jest.MockedFunction<
  typeof useScheduleStore
>;

describe("WorkingHoursHeader", () => {
  const mockStoreState = {
    openImportModal: jest.fn(),
    isCreateScheduleModalOpen: false,
    openCreateScheduleModal: jest.fn(),
    closeCreateScheduleModal: jest.fn(),
    downloadCanvas: jest.fn(),
    isDownloadingCanvas: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseScheduleStore.mockReturnValue(mockStoreState);
  });

  it("renders header title and icon", () => {
    render(<WorkingHoursHeader />);

    expect(
      screen.getByText("Working hours and TKS profile"),
    ).toBeInTheDocument();
    expect(screen.getByTestId("icon-clockhourWeekIcon")).toBeInTheDocument();
  });

  it("renders add schedule button", () => {
    render(<WorkingHoursHeader />);

    const addButton = screen.getByText("addSchedule");
    expect(addButton).toBeInTheDocument();
    expect(screen.getByTestId("icon-addEvent")).toBeInTheDocument();
  });

  it("renders import button", () => {
    render(<WorkingHoursHeader />);

    const importButton = screen.getByText("import");
    expect(importButton).toBeInTheDocument();
    expect(screen.getByTestId("icon-importRightIcon")).toBeInTheDocument();
  });

  it("handles add schedule button click", () => {
    render(<WorkingHoursHeader />);

    const addButton = screen.getByText("addSchedule");
    fireEvent.click(addButton);

    expect(mockStoreState.openCreateScheduleModal).toHaveBeenCalled();
  });

  it("handles import button click", () => {
    render(<WorkingHoursHeader />);

    const importButton = screen.getByText("import");
    fireEvent.click(importButton);

    expect(mockStoreState.openImportModal).toHaveBeenCalled();
  });

  it("does not render modal when closed", () => {
    render(<WorkingHoursHeader />);

    expect(
      screen.queryByTestId("create-schedule-modal"),
    ).not.toBeInTheDocument();
  });

  it("renders modal when open", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isCreateScheduleModalOpen: true,
    });

    render(<WorkingHoursHeader />);

    expect(screen.getByTestId("create-schedule-modal")).toBeInTheDocument();
  });

  it("passes correct props to CreateScheduleModal", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      isCreateScheduleModalOpen: true,
    });

    render(<WorkingHoursHeader />);

    const modalCloseButton = screen.getByTestId("modal-close");
    fireEvent.click(modalCloseButton);

    expect(mockStoreState.closeCreateScheduleModal).toHaveBeenCalled();
  });

  it("applies correct styling to buttons", () => {
    render(<WorkingHoursHeader />);

    const buttons = screen.getAllByTestId("icon-button");
    expect(buttons).toHaveLength(3); // Updated to 3 buttons

    // Add button styling
    const addButton = screen.getByText("addSchedule").closest("button");
    expect(addButton).toHaveClass(
      "border",
      "border-black",
      "flex",
      "items-center",
      "gap-2",
    );

    // Import button styling
    const importButton = screen.getByText("import").closest("button");
    expect(importButton).toHaveClass(
      "border",
      "border-black",
      "bg-black",
      "text-white",
      "hover:bg-black",
      "hover:text-white",
    );

    // Download button styling
    const downloadButton = screen
      .getByText("downloadTemplate")
      .closest("button");
    expect(downloadButton).toHaveClass(
      "border",
      "border-gray-300",
      "bg-green-500",
      "text-white",
      "hover:bg-green-600",
      "hover:text-white",
    );
  });

  it("has correct button IDs", () => {
    render(<WorkingHoursHeader />);

    expect(screen.getByText("addSchedule").closest("button")).toHaveAttribute(
      "id",
      "add-schedule-button",
    );
    expect(screen.getByText("import").closest("button")).toHaveAttribute(
      "id",
      "import-schedule-button",
    );
    expect(
      screen.getByText("downloadTemplate").closest("button"),
    ).toHaveAttribute("id", "download-template-button");
  });

  it("renders with correct layout structure", () => {
    render(<WorkingHoursHeader />);

    const header = screen
      .getByText("Working hours and TKS profile")
      .closest("div");
    expect(header?.parentElement).toHaveClass(
      "flex",
      "items-center",
      "justify-between",
      "mb-6",
    );
  });

  it("renders download template button", () => {
    render(<WorkingHoursHeader />);

    const downloadButton = screen.getByText("downloadTemplate");
    expect(downloadButton).toBeInTheDocument();
    expect(screen.getByTestId("icon-export")).toBeInTheDocument(); // Changed from icon-downloadIcon to icon-export
  });

  it("handles download template button click", () => {
    const mockDownloadCanvas = jest.fn();
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      downloadCanvas: mockDownloadCanvas,
      isDownloadingCanvas: false,
    });

    render(<WorkingHoursHeader />);

    const downloadButton = screen.getByText("downloadTemplate");
    fireEvent.click(downloadButton);

    expect(mockDownloadCanvas).toHaveBeenCalled();
  });

  it("shows loading state when downloading template", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      downloadCanvas: jest.fn(),
      isDownloadingCanvas: true,
    });

    render(<WorkingHoursHeader />);

    const downloadButton = screen.getByText("downloading");
    expect(downloadButton).toBeInTheDocument();
    expect(downloadButton).toBeDisabled();
  });

  it("handles download template error", async () => {
    const consoleSpy = jest.spyOn(console, "error").mockImplementation();
    const mockDownloadCanvas = jest
      .fn()
      .mockRejectedValue(new Error("Download failed"));

    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      downloadCanvas: mockDownloadCanvas,
      isDownloadingCanvas: false,
    });

    render(<WorkingHoursHeader />);

    const downloadButton = screen.getByText("downloadTemplate");
    fireEvent.click(downloadButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        "Error downloading canvas:",
        expect.any(Error),
      );
    });

    consoleSpy.mockRestore();
  });

  it("applies correct styling to download template button", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      downloadCanvas: jest.fn(),
      isDownloadingCanvas: false,
    });

    render(<WorkingHoursHeader />);

    const downloadButton = screen
      .getByText("downloadTemplate")
      .closest("button");
    expect(downloadButton).toHaveClass(
      "border",
      "border-gray-300",
      "bg-green-500",
      "text-white",
      "hover:bg-green-600",
      "hover:text-white",
    );
  });

  it("has correct download template button ID", () => {
    mockUseScheduleStore.mockReturnValue({
      ...mockStoreState,
      downloadCanvas: jest.fn(),
      isDownloadingCanvas: false,
    });

    render(<WorkingHoursHeader />);

    expect(
      screen.getByText("downloadTemplate").closest("button"),
    ).toHaveAttribute("id", "download-template-button");
  });
});
