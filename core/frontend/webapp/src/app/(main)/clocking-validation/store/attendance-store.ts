import { create } from "zustand";
import { AttendanceRecord, AttendanceFilters } from "../types";
import {
  mockAttendanceRecords,
  mockValidatedRecords,
  mockValidatedWithCommentRecords,
} from "../mock-data";

interface AttendanceStore {
  // Data
  records: AttendanceRecord[];

  // UI State
  activeView: "total" | "waiting" | "validated" | "commented";
  filters: AttendanceFilters;
  isLoading: boolean;

  // Actions
  setActiveView: (
    view: "total" | "waiting" | "validated" | "commented",
  ) => void;
  setFilters: (filters: AttendanceFilters) => void;
  setLoading: (loading: boolean) => void;
  updateRecord: (recordId: string, updates: Partial<AttendanceRecord>) => void;

  // Getters
  getFilteredData: () => AttendanceRecord[];
  getStats: () => {
    total: number;
    waiting: number;
    validated: number;
    commented: number;
  };
}

export const useAttendanceStore = create<AttendanceStore>((set, get) => ({
  // Initial state
  records: mockAttendanceRecords,
  activeView: "waiting",
  filters: {},
  isLoading: false,

  // Actions
  setActiveView: (activeView) => set({ activeView }),
  setFilters: (filters) => set({ filters }),
  setLoading: (isLoading) => set({ isLoading }),

  updateRecord: (recordId, updates) =>
    set((state) => ({
      records: state.records.map((record) =>
        record.id === recordId ? { ...record, ...updates } : record,
      ),
    })),

  // Get filtered data based on current view and filters
  getFilteredData: () => {
    const { records, activeView, filters } = get();
    let data: AttendanceRecord[];

    // Get data based on view first
    switch (activeView) {
      case "validated":
        data = [
          ...records.filter((record) => record.status === "validated"),
          ...mockValidatedRecords,
        ];
        break;
      case "commented":
        data = [
          ...records.filter(
            (record) => record.status === "validated_with_comment",
          ),
          ...mockValidatedWithCommentRecords,
        ];
        break;
      case "waiting":
        data = records.filter(
          (record) => record.status === "waiting_for_validation",
        );
        break;
      case "total":
      default:
        data = [
          ...records,
          ...mockValidatedRecords,
          ...mockValidatedWithCommentRecords,
        ];
    }

    // Then apply filters to the view-specific data
    return data.filter((record) => {
      // Date filter
      if (filters.date && record.date !== filters.date) {
        return false;
      }

      // Department filter
      if (filters.department && record.department !== filters.department) {
        return false;
      }

      // Status filter
      if (filters.status && record.status !== filters.status) {
        return false;
      }

      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase().trim();
        const fullName = `${record.firstName} ${record.lastName}`.toLowerCase();
        const firstName = record.firstName.toLowerCase();
        const lastName = record.lastName.toLowerCase();
        const employeeId = record.employeeId.toLowerCase();

        if (
          !fullName.includes(searchLower) &&
          !firstName.includes(searchLower) &&
          !lastName.includes(searchLower) &&
          !employeeId.includes(searchLower)
        ) {
          return false;
        }
      }

      return true;
    });
  },

  // Calculate statistics based on filtered data (real-time)
  getStats: () => {
    const { records, filters } = get();

    // If no filters are applied, show all data stats
    if (
      !filters.search &&
      !filters.date &&
      !filters.department &&
      !filters.status
    ) {
      const allRecords = [
        ...records,
        ...mockValidatedRecords,
        ...mockValidatedWithCommentRecords,
      ];

      return {
        total: allRecords.length,
        waiting: records.filter((r) => r.status === "waiting_for_validation")
          .length,
        validated: [
          ...records.filter((r) => r.status === "validated"),
          ...mockValidatedRecords,
        ].length,
        commented: [
          ...records.filter((r) => r.status === "validated_with_comment"),
          ...mockValidatedWithCommentRecords,
        ].length,
      };
    }

    // When filters are applied, calculate stats from filtered data
    const allRecords = [
      ...records,
      ...mockValidatedRecords,
      ...mockValidatedWithCommentRecords,
    ];

    // Apply filters to get filtered records
    const filteredRecords = allRecords.filter((record) => {
      // Date filter
      if (filters.date && record.date !== filters.date) {
        return false;
      }

      // Department filter
      if (filters.department && record.department !== filters.department) {
        return false;
      }

      // Status filter
      if (filters.status && record.status !== filters.status) {
        return false;
      }

      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase().trim();
        const fullName = `${record.firstName} ${record.lastName}`.toLowerCase();
        const firstName = record.firstName.toLowerCase();
        const lastName = record.lastName.toLowerCase();
        const employeeId = record.employeeId.toLowerCase();

        if (
          !fullName.includes(searchLower) &&
          !firstName.includes(searchLower) &&
          !lastName.includes(searchLower) &&
          !employeeId.includes(searchLower)
        ) {
          return false;
        }
      }

      return true;
    });

    // Calculate statistics from filtered data
    return {
      total: filteredRecords.length,
      waiting: filteredRecords.filter(
        (r) => r.status === "waiting_for_validation",
      ).length,
      validated: filteredRecords.filter((r) => r.status === "validated").length,
      commented: filteredRecords.filter(
        (r) => r.status === "validated_with_comment",
      ).length,
    };
  },
}));
