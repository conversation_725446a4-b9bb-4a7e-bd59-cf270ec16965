export interface AttendanceRecord {
  id: string;
  date: string;
  department: string;
  shift: string;
  role: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  approverId?: string;
  approverFirstName?: string;
  approverLastName?: string;
  status: AttendanceStatus;
  comment?: string;
  validatedAt?: string;
  submittedAt: string;
  closed?: boolean;
}

export type AttendanceStatus =
  | "waiting_for_validation"
  | "validated"
  | "validated_with_comment"
  | "rejected";

export interface AttendanceStatistics {
  total: number;
  waitingForValidation: number;
  validated: number;
  validatedWithComment: number;
  rejected: number;
}

export interface AttendanceFilters {
  date?: string;
  department?: string;
  status?: AttendanceStatus;
  search?: string;
}

export interface ValidationAction {
  recordId: string;
  action: "approve" | "reject" | "approve_with_comment";
  comment?: string;
  approverId: string;
}

export interface Department {
  id: string;
  name: string;
}

export interface Shift {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
}

export interface Role {
  id: string;
  name: string;
  department: string;
}
