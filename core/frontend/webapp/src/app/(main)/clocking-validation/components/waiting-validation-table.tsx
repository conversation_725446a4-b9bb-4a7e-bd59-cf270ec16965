"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import {
  X,
  ChevronLeft,
  ChevronRight,
  TriangleAlert,
  Check,
  Search,
} from "lucide-react";
import CustomIcon from "@/components/common/CustomIcons";
import { AttendanceRecord } from "../types";

interface WaitingValidationTableProps {
  data: AttendanceRecord[];
}

export const WaitingValidationTable = ({
  data,
}: WaitingValidationTableProps) => {
  const t = useTranslations("clocking-validation");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(
    null,
  );
  const [isCommentPopupOpen, setIsCommentPopupOpen] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState<Set<string>>(
    new Set(),
  );
  const [multiSelectComment, setMultiSelectComment] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isSuccessPopupOpen, setIsSuccessPopupOpen] = useState(false);
  const [showSummaryView, setShowSummaryView] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Reset to first page when data changes (for filters)
  useEffect(() => {
    setCurrentPage(1);
  }, [data]);

  // Employee data for the comment popup
  const employeeData = [
    {
      id: "6523",
      name: "Ahmed",
      lastName: "MALIK",
      status: "P",
      comment: "",
    },
    {
      id: "6533",
      name: "Ali",
      lastName: "ALAOUI",
      status: "P",
      comment: "Presence validated. Stay safe and efficient!",
    },
    {
      id: "6511",
      name: "Mehdi",
      lastName: "SALIMI",
      status: "P",
      comment: "",
    },
    {
      id: "6544",
      name: "Alae",
      lastName: "AZMI",
      status: "AB",
      comment: "Clocking recorded successfully. Have a productive shift!",
    },
    {
      id: "6599",
      name: "Ali",
      lastName: "MAHBOUB",
      status: "P",
      comment: "",
    },
    {
      id: "6521",
      name: "Salah",
      lastName: "HACHIMI",
      status: "P",
      comment: "",
    },
    {
      id: "6543",
      name: "Reda",
      lastName: "GHIATI",
      status: "AB",
      comment: "Time validated. Keep up the good work!",
    },
    {
      id: "6699",
      name: "Anas",
      lastName: "KAMALI",
      status: "P",
      comment: "",
    },
  ];

  const handleCellClick = (record: AttendanceRecord) => {
    setSelectedRecord(record);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedRecord(null);
  };

  const handleValidate = () => {
    // Handle validation logic here
    console.log("Validating record:", selectedRecord);
    setIsSuccessPopupOpen(true);
    closeModal();
  };

  const handleValidateWithComment = () => {
    setIsCommentPopupOpen(true);
  };

  const closeCommentPopup = () => {
    setIsCommentPopupOpen(false);
    setSelectedEmployees(new Set());
    setMultiSelectComment("");
    setSearchQuery("");
    setShowSummaryView(false);
  };

  const handleEmployeeSelect = (employeeId: string) => {
    const newSelected = new Set(selectedEmployees);
    if (newSelected.has(employeeId)) {
      newSelected.delete(employeeId);
    } else {
      newSelected.add(employeeId);
    }
    setSelectedEmployees(newSelected);
  };

  const handleValidateSelected = () => {
    console.log(
      "Validating selected employees:",
      Array.from(selectedEmployees),
    );
    console.log("With comment:", multiSelectComment);
    setShowSummaryView(true);
  };

  const closeSuccessPopup = () => {
    setIsSuccessPopupOpen(false);
  };

  const handleBackToComment = () => {
    setShowSummaryView(false);
  };

  const handleSubmitValidation = () => {
    console.log("Final validation submitted");
    setIsSuccessPopupOpen(true);
    closeCommentPopup();
    closeModal();
  };

  const handleSelectAll = () => {
    if (selectedEmployees.size === filteredEmployeeData.length) {
      // If all are selected, deselect all
      setSelectedEmployees(new Set());
    } else {
      // If not all are selected, select all
      setSelectedEmployees(new Set(filteredEmployeeData.map((emp) => emp.id)));
    }
  };

  // Filter employee data based on search query
  const filteredEmployeeData = employeeData.filter((employee) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      employee.id.toLowerCase().includes(query) ||
      employee.name.toLowerCase().includes(query) ||
      employee.lastName.toLowerCase().includes(query)
    );
  });

  const isAllSelected =
    selectedEmployees.size === filteredEmployeeData.length &&
    filteredEmployeeData.length > 0;

  // Pagination calculations
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = data.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePageClick = (page: number) => {
    setCurrentPage(page);
  };
  return (
    <div className="bg-white rounded-lg border">
      <div className="overflow-x-auto">
        <table className="w-full" style={{ border: "1px solid #9D9D9D" }}>
          <thead style={{ backgroundColor: "#BFD5F1" }}>
            <tr>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.date")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.department")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.shift")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.role")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.id")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.firstLastName")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.approver")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.id")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.firstLastName")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{ borderBottom: "1px solid #9D9D9D" }}
              >
                {t("table.status")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((record, index) => (
              <tr
                key={record.id}
                className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
              >
                <td
                  className="px-4 py-3 text-sm text-blue-600 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.date}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.department}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.shift}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-600 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.role}
                </td>
                <td
                  className="px-4 py-3 text-sm font-mono text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.employeeId}
                </td>
                <td
                  className="px-4 py-3 text-sm font-medium text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.firstName} {record.lastName}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-600 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {t("roles.teamLeader")}
                </td>
                <td
                  className="px-4 py-3 text-sm font-mono text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.approverId || "7826"}
                </td>
                <td
                  className="px-4 py-3 text-sm font-medium text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.approverFirstName || "Said"}{" "}
                  {record.approverLastName || "Honim"}
                </td>
                <td
                  className="px-4 py-3 text-sm bg-[#FFF4E6] text-[#C59800] font-medium text-center cursor-pointer hover:bg-[#FFF0D6]"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                  onClick={() => handleCellClick(record)}
                >
                  {t("status.waitingForValidation")}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            {t("pagination.showing")} {startIndex + 1} {t("pagination.to")}{" "}
            {Math.min(endIndex, data.length)} {t("pagination.of")} {data.length}{" "}
            {t("pagination.entries")}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
              className={`p-2 rounded ${
                currentPage === 1
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageClick(page)}
                className={`px-3 py-1 rounded text-sm ${
                  currentPage === page
                    ? "bg-blue-500 text-white"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className={`p-2 rounded ${
                currentPage === totalPages
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Sidebar */}
      {isModalOpen && selectedRecord && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50"
          onClick={closeModal}
        >
          <div
            className="absolute right-0 top-0 h-full w-full max-w-4xl bg-white shadow-xl overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold text-gray-900">
                {t("table.attendanceRecords")}
              </h2>
              <button
                onClick={closeModal}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Sidebar Content */}
            <div className="p-4">
              {/* Export Buttons */}
              <div className="flex justify-between gap-4 mb-4">
                <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 whitespace-nowrap">
                  <CustomIcon
                    name="exportClockingValidation"
                    className="w-4 h-4"
                    style={{ width: "auto" }}
                  />
                  {t("actions.export")}
                </button>
                <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 whitespace-nowrap">
                  <CustomIcon
                    name="exportClockingValidation"
                    className="w-4 h-4"
                    style={{ width: "auto" }}
                  />
                  Export selected team attendance sheet
                </button>
              </div>

              {/* Table */}
              <div className="overflow-x-auto mb-4">
                <table className="w-full border-collapse border border-gray-300">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700">
                        Mle
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700">
                        First Name
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700">
                        Last Name
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700">
                        Category
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700">
                        Function
                      </th>
                      <th className="border border-gray-300 px-3 py-2 text-xs font-medium text-gray-700">
                        16/07/2025
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Sample data - replace with actual attendance data */}
                    {Array.from({ length: 15 }, (_, i) => (
                      <tr
                        key={i}
                        className={i % 2 === 0 ? "bg-white" : "bg-gray-50"}
                      >
                        <td className="border border-gray-300 px-3 py-2 text-xs text-center">
                          6523
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-xs text-center">
                          Ahmed
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-xs text-center">
                          MALIK
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-xs text-center">
                          DH
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-xs text-center">
                          OPERATOR
                        </td>
                        <td className="border border-gray-300 px-3 py-2 text-xs text-center">
                          {i === 4 || i === 9 ? (
                            <span className="bg-red-100 text-red-700 px-2 py-1 rounded text-xs">
                              AB
                            </span>
                          ) : i === 10 ? (
                            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs">
                              CTP
                            </span>
                          ) : (
                            <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                              P
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center">
                <button
                  onClick={handleValidateWithComment}
                  className="flex items-center gap-2 px-6 py-2 bg-white text-red-600 border border-red-600 rounded-md text-sm hover:bg-red-50"
                >
                  <TriangleAlert className="w-4 h-4" />
                  {t("actions.approveWithComment")}
                </button>
                <button
                  onClick={handleValidate}
                  className="flex items-center gap-2 px-6 py-2 bg-green-500 text-white rounded-md text-sm hover:bg-green-600"
                >
                  <Check className="w-4 h-4" />
                  {t("modal.approve")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Comment Popup */}
      {isCommentPopupOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70] p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[95vh] overflow-hidden flex flex-col">
            {/* Popup Header */}
            <div className="flex items-center justify-between p-4 border-b bg-gray-50 flex-shrink-0">
              <h2 className="text-lg font-semibold text-gray-900">
                {showSummaryView
                  ? t("modal.previewValidationComment")
                  : t("modal.validateAttendanceWithComment")}
              </h2>
              <button
                onClick={closeCommentPopup}
                className="p-1 hover:bg-gray-200 rounded"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Popup Content */}
            <div className="p-4 overflow-y-auto flex-1">
              {!showSummaryView ? (
                // Original comment view
                <>
                  {/* Search Bar */}
                  <div className="mb-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder={t("filters.searchPlaceholder")}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-md text-sm pr-10"
                      />
                      <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>

                  {/* Table */}
                  <div className="overflow-x-auto mb-4">
                    <table className="w-full border-collapse">
                      <thead className="bg-gray-200">
                        <tr>
                          <th className="border border-gray-400 px-3 py-2 text-sm font-medium text-black">
                            <input
                              type="checkbox"
                              checked={isAllSelected}
                              onChange={handleSelectAll}
                              className="w-4 h-4"
                            />
                          </th>
                          <th className="border border-gray-400 px-3 py-2 text-sm font-medium text-black">
                            Mle
                          </th>
                          <th className="border border-gray-400 px-3 py-2 text-sm font-medium text-black">
                            {t("table.firstName")}
                          </th>
                          <th className="border border-gray-400 px-3 py-2 text-sm font-medium text-black">
                            {t("table.lastName")}
                          </th>
                          <th className="border border-gray-400 px-3 py-2 text-sm font-medium text-black">
                            16/07/2025
                          </th>
                          <th className="border border-gray-400 px-3 py-2 text-sm font-medium text-black">
                            {t("table.comment")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredEmployeeData.map((employee, index) => (
                          <tr
                            key={employee.id}
                            className={
                              index % 2 === 0 ? "bg-white" : "bg-gray-50"
                            }
                          >
                            <td className="border border-gray-400 px-3 py-2 text-center">
                              <input
                                type="checkbox"
                                checked={selectedEmployees.has(employee.id)}
                                onChange={() =>
                                  handleEmployeeSelect(employee.id)
                                }
                                className="w-4 h-4"
                              />
                            </td>
                            <td className="border border-gray-400 px-3 py-2 text-sm text-center">
                              {employee.id}
                            </td>
                            <td className="border border-gray-400 px-3 py-2 text-sm text-center">
                              {employee.name}
                            </td>
                            <td className="border border-gray-400 px-3 py-2 text-sm text-center">
                              {employee.lastName}
                            </td>
                            <td className="border border-gray-400 px-3 py-2 text-center">
                              <span
                                className={`px-2 py-1 rounded text-xs font-medium ${
                                  employee.status === "P"
                                    ? "bg-green-100 text-green-700"
                                    : employee.status === "AB"
                                      ? "bg-red-100 text-red-700"
                                      : "bg-orange-100 text-orange-700"
                                }`}
                              >
                                {employee.status}
                              </span>
                            </td>
                            <td className="border border-gray-400 px-3 py-2">
                              <input
                                type="text"
                                placeholder={t("modal.addCommentPlaceholder")}
                                defaultValue={employee.comment}
                                className="w-full px-2 py-1 text-xs border-none bg-transparent"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Multi-Selection Field */}
                  <div className="mb-4">
                    <label className="text-sm font-medium text-blue-600 mb-2 block">
                      Multi-Selection field :
                    </label>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {Array.from(selectedEmployees).map((employeeId) => {
                        const employee = employeeData.find(
                          (emp) => emp.id === employeeId,
                        );

                        return employee ? (
                          <span
                            key={employeeId}
                            className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                          >
                            {employeeId} {employee.name} {employee.lastName}
                            <button
                              onClick={() => handleEmployeeSelect(employeeId)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              ×
                            </button>
                          </span>
                        ) : null;
                      })}
                    </div>

                    <label className="text-sm text-gray-600 mb-1 block">
                      Multi-Selection comment : *
                    </label>
                    <textarea
                      value={multiSelectComment}
                      onChange={(e) => setMultiSelectComment(e.target.value)}
                      placeholder="Clocking recorded successfully. Have a productive shift!"
                      className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                      rows={3}
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-between items-center pt-4 border-t bg-white sticky bottom-0">
                    <button
                      onClick={closeCommentPopup}
                      className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50"
                    >
                      {t("modal.close")}
                    </button>
                    <button
                      onClick={handleValidateSelected}
                      className="px-6 py-2 bg-black text-white rounded-md text-sm hover:bg-gray-800"
                    >
                      {t("modal.approve")}
                    </button>
                  </div>
                </>
              ) : (
                // Summary view
                <>
                  {/* Icon and Title */}
                  <div className="flex flex-col items-center text-center mb-6">
                    <div className="w-16 h-16  rounded-full flex items-center justify-center mb-4">
                      <CustomIcon
                        name="checkmarkClockingValidation"
                        className="w-8 h-8"
                        style={{ width: "auto" }}
                      />
                    </div>
                    <h3 className="text-xl font-semibold text-blue-600 mb-2">
                      {t("modal.validateAttendanceWithComment")}
                    </h3>
                    <p className="text-gray-600 text-center max-w-md">
                      {t("actions.reviewFinalization")}
                    </p>
                  </div>

                  {/* Search Bar */}
                  <div className="mb-4">
                    <div className="relative max-w-md">
                      <input
                        type="text"
                        placeholder={t("filters.searchPlaceholder")}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm pr-10"
                      />
                      <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>

                  {/* Summary Table */}
                  <div className="overflow-x-auto mb-6">
                    <table className="w-full border-collapse">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700">
                            Mle
                          </th>
                          <th className="border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700">
                            {t("table.firstName")}
                          </th>
                          <th className="border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700">
                            {t("table.lastName")}
                          </th>
                          <th className="border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700">
                            16/07/2025
                          </th>
                          <th className="border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700">
                            {t("table.comment")}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {Array.from(selectedEmployees).map((employeeId) => {
                          const employee = employeeData.find(
                            (emp) => emp.id === employeeId,
                          );
                          return employee ? (
                            <tr key={employeeId} className="bg-white">
                              <td className="border border-gray-300 px-3 py-2 text-sm text-center">
                                {employee.id}
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm text-center">
                                {employee.name}
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm text-center">
                                {employee.lastName}
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-center">
                                <span
                                  className={`px-2 py-1 rounded text-xs font-medium ${
                                    employee.status === "P"
                                      ? "bg-green-100 text-green-700"
                                      : employee.status === "AB"
                                        ? "bg-red-100 text-red-700"
                                        : "bg-orange-100 text-orange-700"
                                  }`}
                                >
                                  {employee.status}
                                </span>
                              </td>
                              <td className="border border-gray-300 px-3 py-2 text-sm">
                                {employee.comment || "---"}
                              </td>
                            </tr>
                          ) : null;
                        })}
                      </tbody>
                    </table>
                  </div>

                  {/* Multi-Selection Summary */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-blue-600 mb-2">
                      Multi-Selection field :
                    </h4>
                    <div className="flex flex-wrap gap-2 mb-3">
                      {Array.from(selectedEmployees).map((employeeId) => {
                        const employee = employeeData.find(
                          (emp) => emp.id === employeeId,
                        );
                        return employee ? (
                          <span
                            key={employeeId}
                            className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                          >
                            {employeeId} {employee.name} {employee.lastName}
                          </span>
                        ) : null;
                      })}
                    </div>

                    <div className="bg-gray-50 p-3 rounded border">
                      <p className="text-sm text-gray-600 mb-1">
                        {t("table.comment")} :
                      </p>
                      <p className="text-sm text-gray-900">
                        {multiSelectComment ||
                          "Clocking recorded successfully. Have a productive shift!"}
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-between items-center pt-4 border-t bg-white sticky bottom-0">
                    <button
                      onClick={handleBackToComment}
                      className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50"
                    >
                      {t("pagination.previous")}
                    </button>
                    <button
                      onClick={handleSubmitValidation}
                      className="px-6 py-2 bg-black text-white rounded-md text-sm hover:bg-gray-800"
                    >
                      {t("actions.submitClockingValidation")}
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Success Popup */}
      {isSuccessPopupOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[80]">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 p-6">
            <div className="flex flex-col items-center text-center">
              {/* Success Icon */}
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Check className="w-8 h-8 text-green-600" />
              </div>

              {/* Success Message */}
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                Validation Successful
              </h2>
              <p className="text-gray-600 mb-6">
                All records have been validated with comments.
              </p>

              {/* Close Button */}
              <button
                onClick={closeSuccessPopup}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50"
              >
                {t("modal.close")}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
