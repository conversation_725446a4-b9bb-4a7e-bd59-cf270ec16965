"use client";

import { AttendanceRecord } from "../types";
import { useState, useEffect } from "react";
import { Eye, ChevronLeft, ChevronRight } from "lucide-react";
import { useTranslations } from "next-intl";
import CustomIcon from "@/components/common/CustomIcons";

interface SimpleTableProps {
  data: AttendanceRecord[];
}

export const SimpleTable = ({ data }: SimpleTableProps) => {
  const t = useTranslations("clocking-validation");
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(
    null,
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Reset to first page when data changes (for filters)
  useEffect(() => {
    setCurrentPage(1);
  }, [data]);

  const handleViewDetails = (record: AttendanceRecord) => {
    setSelectedRecord(record);
    setIsModalOpen(true);
  };

  // Pagination calculations
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedData = data.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePageClick = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="relative bg-white rounded-lg border">
      <div className="overflow-x-auto">
        <table className="w-full" style={{ border: "1px solid #9D9D9D" }}>
          <thead style={{ backgroundColor: "#BFD5F1" }}>
            <tr>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.date")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.department")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.shift")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.role")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.id")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.firstLastName")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.approver")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.id")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                First, Last name
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{ borderBottom: "1px solid #9D9D9D" }}
              >
                {t("table.status")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedData.map((record, index) => (
              <tr
                key={record.id}
                className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
              >
                <td
                  className="px-4 py-3 text-sm text-blue-600 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.date}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.department}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.shift}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-600 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.role}
                </td>
                <td
                  className="px-4 py-3 text-sm font-mono text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.employeeId}
                </td>
                <td
                  className="px-4 py-3 text-sm font-medium text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.firstName} {record.lastName}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-600 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  Shift leader
                </td>
                <td
                  className="px-4 py-3 text-sm font-mono text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.approverId}
                </td>
                <td
                  className="px-4 py-3 text-sm font-medium text-gray-900 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.approverFirstName} {record.approverLastName}
                </td>
                <td
                  className="px-4 py-3 text-sm text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    backgroundColor: "#EBF6EC",
                    color: "#4CAF50",
                  }}
                >
                  <div className="flex items-center justify-center gap-2">
                    Validated
                    <button
                      onClick={() => handleViewDetails(record)}
                      className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-transparent hover:bg-gray-200 transition-colors"
                    >
                      <Eye className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            Showing {startIndex + 1} to {Math.min(endIndex, data.length)} of{" "}
            {data.length} results
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
              className={`p-2 rounded ${
                currentPage === 1
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageClick(page)}
                className={`px-3 py-1 rounded text-sm ${
                  currentPage === page
                    ? "bg-blue-500 text-white"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className={`p-2 rounded ${
                currentPage === totalPages
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Sidebar for viewing details */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            {/* Backdrop */}
            <div
              className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={() => setIsModalOpen(false)}
            />

            {/* Sidebar */}
            <div className="absolute right-0 top-0 h-full w-1/2 bg-white shadow-xl transform transition-transform">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b bg-gray-50">
                  <h3 className="text-lg font-semibold">
                    Selected attendance sheet
                  </h3>
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                {/* Export buttons */}
                <div className="flex justify-between gap-4 mb-4 p-4 border-b">
                  <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 whitespace-nowrap">
                    <CustomIcon
                      name="exportClockingValidation"
                      className="w-4 h-4"
                      style={{ width: "auto" }}
                    />
                    Export this teams list
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 whitespace-nowrap">
                    <CustomIcon
                      name="exportClockingValidation"
                      className="w-4 h-4"
                      style={{ width: "auto" }}
                    />
                    Export selected team attendance sheet
                  </button>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto p-4">
                  {selectedRecord && (
                    <div className="overflow-x-auto">
                      <table
                        className="w-full"
                        style={{
                          border: "1px solid #9D9D9D",
                          borderLeft: "1px solid #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                          borderTop: "1px solid #9D9D9D",
                          borderBottom: "1px solid #9D9D9D",
                          borderRadius: "8px",
                          borderCollapse: "separate",
                          borderSpacing: "0",
                        }}
                      >
                        <thead style={{ backgroundColor: "#D1D1D1" }}>
                          <tr>
                            <th
                              className="px-2 py-2 text-center text-xs font-medium text-gray-700"
                              style={{
                                borderBottom: "1px dashed #9D9D9D",
                                borderRight: "1px solid #9D9D9D",
                                borderTopLeftRadius: "8px",
                              }}
                            >
                              Mle
                            </th>
                            <th
                              className="px-2 py-2 text-center text-xs font-medium text-gray-700"
                              style={{
                                borderBottom: "1px dashed #9D9D9D",
                                borderRight: "1px solid #9D9D9D",
                              }}
                            >
                              First Name
                            </th>
                            <th
                              className="px-2 py-2 text-center text-xs font-medium text-gray-700"
                              style={{
                                borderBottom: "1px dashed #9D9D9D",
                                borderRight: "1px solid #9D9D9D",
                              }}
                            >
                              Last Name
                            </th>
                            <th
                              className="px-2 py-2 text-center text-xs font-medium text-gray-700"
                              style={{
                                borderBottom: "1px dashed #9D9D9D",
                                borderRight: "1px solid #9D9D9D",
                              }}
                            >
                              Category
                            </th>
                            <th
                              className="px-2 py-2 text-center text-xs font-medium text-gray-700"
                              style={{
                                borderBottom: "1px dashed #9D9D9D",
                                borderRight: "1px solid #9D9D9D",
                              }}
                            >
                              Function
                            </th>
                            <th
                              className="px-2 py-2 text-center text-xs font-medium text-gray-700"
                              style={{
                                borderBottom: "1px dashed #9D9D9D",
                                borderTopRightRadius: "8px",
                              }}
                            >
                              16/07/2025
                            </th>
                          </tr>
                        </thead>
                        <tbody style={{ backgroundColor: "#fff" }}>
                          {Array.from({ length: 20 }, (_, index) => (
                            <tr key={index} className="bg-white">
                              <td
                                className="px-2 py-1 text-xs text-center"
                                style={{
                                  borderBottom:
                                    index === 19
                                      ? "none"
                                      : "1px dashed #9D9D9D",
                                  borderRight: "1px solid #9D9D9D",
                                  borderBottomLeftRadius:
                                    index === 19 ? "8px" : "0",
                                }}
                              >
                                6523
                              </td>
                              <td
                                className="px-2 py-1 text-xs text-center"
                                style={{
                                  borderBottom:
                                    index === 19
                                      ? "none"
                                      : "1px dashed #9D9D9D",
                                  borderRight: "1px solid #9D9D9D",
                                }}
                              >
                                Ahmed
                              </td>
                              <td
                                className="px-2 py-1 text-xs text-center"
                                style={{
                                  borderBottom:
                                    index === 19
                                      ? "none"
                                      : "1px dashed #9D9D9D",
                                  borderRight: "1px solid #9D9D9D",
                                }}
                              >
                                MALIK
                              </td>
                              <td
                                className="px-2 py-1 text-xs text-center"
                                style={{
                                  borderBottom:
                                    index === 19
                                      ? "none"
                                      : "1px dashed #9D9D9D",
                                  borderRight: "1px solid #9D9D9D",
                                }}
                              >
                                DH
                              </td>
                              <td
                                className="px-2 py-1 text-xs text-center"
                                style={{
                                  borderBottom:
                                    index === 19
                                      ? "none"
                                      : "1px dashed #9D9D9D",
                                  borderRight: "1px solid #9D9D9D",
                                }}
                              >
                                OPERATOR
                              </td>
                              <td
                                className="px-2 py-1 text-xs text-center"
                                style={{
                                  borderBottom:
                                    index === 19
                                      ? "none"
                                      : "1px dashed #9D9D9D",
                                  borderBottomRightRadius:
                                    index === 19 ? "8px" : "0",
                                }}
                              >
                                {index === 4 || index === 9 ? (
                                  <span className="bg-red-100 text-red-600 px-1 py-0.5 rounded text-xs font-medium">
                                    AB
                                  </span>
                                ) : index === 10 ? (
                                  <span className="bg-orange-100 text-orange-600 px-1 py-0.5 rounded text-xs font-medium">
                                    CTP
                                  </span>
                                ) : (
                                  <span className="bg-green-100 text-green-600 px-1 py-0.5 rounded text-xs font-medium">
                                    P
                                  </span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
