"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Eye, X, ChevronLeft, ChevronRight, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import CustomIcon from "@/components/common/CustomIcons";
import { AttendanceRecord } from "../types";

interface ValidatedWithCommentTableProps {
  data: AttendanceRecord[];
  onToggleClosed?: (recordId: string, closed: boolean) => void;
}

export const ValidatedWithCommentTable = ({
  data,
  onToggleClosed,
}: ValidatedWithCommentTableProps) => {
  const t = useTranslations("clocking-validation");
  const [records, setRecords] = useState(data);
  const [closedFilter, setClosedFilter] = useState<
    "all" | "closed" | "not-closed"
  >("all");
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(
    null,
  );

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Update records when data prop changes (for filters from parent)
  useEffect(() => {
    setRecords(data);
    setCurrentPage(1); // Reset to first page when data changes
  }, [data]);

  const handleToggleClosed = (recordId: string, currentClosed: boolean) => {
    const newClosed = !currentClosed;
    setRecords((prev) =>
      prev.map((record) =>
        record.id === recordId ? { ...record, closed: newClosed } : record,
      ),
    );
    onToggleClosed?.(recordId, newClosed);
  };

  const handlePreviewClick = (record: AttendanceRecord) => {
    setSelectedRecord(record);
    setIsPreviewModalOpen(true);
  };

  const closePreviewModal = () => {
    setIsPreviewModalOpen(false);
    setSelectedRecord(null);
  };

  const filteredRecords = records.filter((record) => {
    if (closedFilter === "closed") return record.closed;
    if (closedFilter === "not-closed") return !record.closed;
    return true; // "all"
  });

  // Reset to first page when filters change
  const handleFilterChange = (newFilter: "all" | "closed" | "not-closed") => {
    setClosedFilter(newFilter);
    setCurrentPage(1);
  };

  const allCount = records.length;
  const closedCount = records.filter((r) => r.closed).length;
  const notClosedCount = records.filter((r) => !r.closed).length;

  // Pagination calculations
  const totalPages = Math.ceil(filteredRecords.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedRecords = filteredRecords.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePageClick = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="bg-white rounded-lg border">
      {/* Filter Buttons */}
      <div className="p-4 border-b bg-gray-50">
        <div className="flex justify-end gap-2">
          <button
            onClick={() => handleFilterChange("all")}
            className={`px-3 py-1 text-sm font-medium rounded ${
              closedFilter === "all"
                ? "bg-blue-100 text-blue-800 border border-blue-300"
                : "bg-gray-100 text-gray-600 border border-gray-300"
            }`}
          >
            {t("filters.all")} ({allCount})
          </button>
          <button
            onClick={() => handleFilterChange("closed")}
            className={`px-3 py-1 text-sm font-medium rounded ${
              closedFilter === "closed"
                ? "bg-green-100 text-green-800 border border-green-300"
                : "bg-gray-100 text-gray-600 border border-gray-300"
            }`}
          >
            {t("table.closed")} ({closedCount})
          </button>
          <button
            onClick={() => handleFilterChange("not-closed")}
            className={`px-3 py-1 text-sm font-medium rounded ${
              closedFilter === "not-closed"
                ? "bg-red-100 text-red-800 border border-red-300"
                : "bg-gray-100 text-gray-600 border border-gray-300"
            }`}
          >
            {t("filters.notClosed")} ({notClosedCount})
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full" style={{ border: "1px solid #9D9D9D" }}>
          <thead style={{ backgroundColor: "#BFD5F1" }}>
            <tr>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.date")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.department")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.shift")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.role")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.id")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.firstLastName")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.approver")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.id")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.firstLastName")}
              </th>
              <th
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
              >
                {t("table.comment")}
              </th>
              <th
                className="px-4 py-3 text-center text-sm font-medium text-gray-700"
                style={{
                  borderBottom: "1px solid #9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                }}
                colSpan={2}
              >
                {t("table.closed")}
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedRecords.map((record, index) => (
              <tr
                key={record.id}
                className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
              >
                <td
                  className="px-4 py-3 text-sm text-blue-600"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.date}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-900"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.department}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-900"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.shift}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-600"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.role}
                </td>
                <td
                  className="px-4 py-3 text-sm font-mono text-gray-900"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.employeeId}
                </td>
                <td
                  className="px-4 py-3 text-sm font-medium text-gray-900"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.firstName} {record.lastName}
                </td>
                <td
                  className="px-4 py-3 text-sm text-gray-600"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  Shift leader
                </td>
                <td
                  className="px-4 py-3 text-sm font-mono text-gray-900"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.approverId}
                </td>
                <td
                  className="px-4 py-3 text-sm font-medium text-gray-900"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                  }}
                >
                  {record.approverFirstName} {record.approverLastName}
                </td>
                <td
                  className="px-0 py-0 text-sm text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    backgroundColor: "#FFEBEE",
                    color: "#D32F2F",
                  }}
                >
                  <div className="flex items-center justify-end gap-2 px-2 py-2">
                    <span className="text-sm">
                      {t("status.validatedWithComment")}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="px-2 py-1 rounded hover:bg-gray-50"
                      style={{ backgroundColor: "transparent" }}
                      onClick={() => handlePreviewClick(record)}
                    >
                      <Eye className="h-4 w-4 text-gray-500" />
                      <span className="text-xs text-gray-500 ml-1">
                        {t("modal.preview")}
                      </span>
                    </Button>
                  </div>
                </td>
                <td
                  className="px-0 py-0 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    backgroundColor: record.closed ? "#4CAF50" : "#FFFFFF",
                  }}
                >
                  <button
                    onClick={() =>
                      handleToggleClosed(record.id, record.closed || false)
                    }
                    className="w-full h-full text-xs font-medium"
                    style={{
                      backgroundColor: "transparent",
                      color: record.closed ? "#FFFFFF" : "#9D9D9D",
                      border: "none",
                      padding: "0",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      minHeight: "inherit",
                    }}
                  >
                    YES
                  </button>
                </td>
                <td
                  className="px-0 py-0 text-center"
                  style={{
                    borderBottom: "1px solid #9D9D9D",
                    backgroundColor: !record.closed ? "#D60000" : "#FFFFFF",
                  }}
                >
                  <button
                    onClick={() =>
                      handleToggleClosed(record.id, record.closed || false)
                    }
                    className="w-full h-full text-xs font-medium"
                    style={{
                      backgroundColor: "transparent",
                      color: !record.closed ? "#FFFFFF" : "#9D9D9D",
                      border: "none",
                      padding: "0",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      minHeight: "inherit",
                    }}
                  >
                    NO
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            {t("pagination.showing")} {startIndex + 1} {t("pagination.to")}{" "}
            {Math.min(endIndex, filteredRecords.length)} {t("pagination.of")}{" "}
            {filteredRecords.length} {t("pagination.entries")}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
              className={`p-2 rounded ${
                currentPage === 1
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageClick(page)}
                className={`px-3 py-1 rounded text-sm ${
                  currentPage === page
                    ? "bg-blue-500 text-white"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className={`p-2 rounded ${
                currentPage === totalPages
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {isPreviewModalOpen && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden border border-gray-300">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {t("modal.previewValidationComment")}
              </h2>
              <button
                onClick={closePreviewModal}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>

            {/* Icon and Title Section */}
            <div className="flex flex-col items-center py-6 border-b-2 border-blue-200">
              <CustomIcon
                name="checkmarkClockingValidation"
                className="w-8 h-8"
                style={{ width: "auto" }}
              />
              <h3 className="text-blue-600 font-medium text-lg mb-2 text-center">
                {t("modal.validateAttendanceWithComment")}
              </h3>
              <p className="text-sm text-gray-600 text-center max-w-md">
                {t("actions.reviewFinalization")}
              </p>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              {/* Search Bar - Right Aligned */}
              <div className="mb-6 flex justify-end">
                <div className="relative w-80">
                  <input
                    type="text"
                    placeholder={t("filters.searchPlaceholder")}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <Search className="h-4 w-4" />
                  </div>
                </div>
              </div>

              {/* Table */}
              <div className="overflow-x-auto mb-6">
                <table
                  className="w-full"
                  style={{
                    border: "1px solid #9D9D9D",
                    borderLeft: "1px solid #9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderTop: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                    borderRadius: "8px",
                    borderCollapse: "separate",
                    borderSpacing: "0",
                  }}
                >
                  <thead style={{ backgroundColor: "#D1D1D1" }}>
                    <tr>
                      <th
                        className="px-4 py-3 text-sm font-semibold text-gray-700 text-left"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                          borderTopLeftRadius: "8px",
                        }}
                      >
                        Mle
                      </th>
                      <th
                        className="px-4 py-3 text-sm font-semibold text-gray-700 text-left"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        First Name
                      </th>
                      <th
                        className="px-4 py-3 text-sm font-semibold text-gray-700 text-left"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        Last Name
                      </th>
                      <th
                        className="px-4 py-3 text-sm font-semibold text-gray-700 text-center"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        16/07/2025
                      </th>
                      <th
                        className="px-4 py-3 text-sm font-semibold text-gray-700 text-left"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderTopRightRadius: "8px",
                        }}
                      >
                        Comment
                      </th>
                    </tr>
                  </thead>
                  <tbody style={{ backgroundColor: "#fff" }}>
                    <tr className="bg-white">
                      <td
                        className="px-4 py-3 text-sm font-medium"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        6533
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        Ali
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        ALAOUI
                      </td>
                      <td
                        className="px-4 py-3 text-center"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        <span className="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-medium">
                          P
                        </span>
                      </td>
                      <td
                        className="px-4 py-3 text-sm text-gray-700"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                        }}
                      >
                        Presence validated. Stay safe and efficient!
                      </td>
                    </tr>
                    <tr className="bg-white">
                      <td
                        className="px-4 py-3 text-sm font-medium"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        6544
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        Alae
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        AZMI
                      </td>
                      <td
                        className="px-4 py-3 text-center"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                          AB
                        </span>
                      </td>
                      <td
                        className="px-4 py-3 text-sm text-gray-700"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                        }}
                      >
                        Clocking recorded successfully. Have a productive shift!
                      </td>
                    </tr>
                    <tr className="bg-white">
                      <td
                        className="px-4 py-3 text-sm font-medium"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        6543
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        Reda
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        GHIATI
                      </td>
                      <td
                        className="px-4 py-3 text-center"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                          AB
                        </span>
                      </td>
                      <td
                        className="px-4 py-3 text-sm text-gray-700"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                        }}
                      >
                        Time validated. Keep up the good work!
                      </td>
                    </tr>
                    <tr className="bg-white">
                      <td
                        className="px-4 py-3 text-sm font-medium"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        6583
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        Kamal
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        SENHAJA
                      </td>
                      <td
                        className="px-4 py-3 text-center"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        <span className="bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs font-medium">
                          CTP
                        </span>
                      </td>
                      <td
                        className="px-4 py-3 text-sm text-gray-700"
                        style={{
                          borderBottom: "1px dashed #9D9D9D",
                        }}
                      >
                        Presence check OK. Proceed with your tasks!
                      </td>
                    </tr>
                    <tr className="bg-white">
                      <td
                        className="px-4 py-3 text-sm font-medium"
                        style={{
                          borderRight: "1px solid #9D9D9D",
                          borderBottomLeftRadius: "8px",
                        }}
                      >
                        6523
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        Reda
                      </td>
                      <td
                        className="px-4 py-3 text-sm"
                        style={{
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        GHIATI
                      </td>
                      <td
                        className="px-4 py-3 text-center"
                        style={{
                          borderRight: "1px solid #9D9D9D",
                        }}
                      >
                        <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                          AB
                        </span>
                      </td>
                      <td
                        className="px-4 py-3 text-sm text-gray-700"
                        style={{
                          borderBottomRightRadius: "8px",
                        }}
                      >
                        Presence check OK. Proceed with your tasks!
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* Action Button */}
              <div className="flex justify-center">
                <button
                  onClick={closePreviewModal}
                  className="px-8 py-2 bg-gray-200 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-300 transition-colors border border-gray-300"
                >
                  {t("modal.close")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
