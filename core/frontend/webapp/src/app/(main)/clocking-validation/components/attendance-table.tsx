"use client";

import { useState, useMemo } from "react";
import { useTranslations } from "next-intl";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
  SortingState,
} from "@tanstack/react-table";
import { ArrowUpDown, Eye, MoreHorizontal } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AttendanceRecord,
  AttendanceFilters,
  ValidationAction,
} from "../types";
import { StatusBadge } from "./status-badge";
import { ValidationModal } from "./validation-modal";

interface AttendanceTableProps {
  data: AttendanceRecord[];
  filters: AttendanceFilters;
  onValidate: (action: ValidationAction) => void;
  isLoading?: boolean;
}

export const AttendanceTable = ({
  data,
  filters,
  onValidate,
  isLoading = false,
}: AttendanceTableProps) => {
  const t = useTranslations("clocking-validation");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(
    null,
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewDetails = (record: AttendanceRecord) => {
    setSelectedRecord(record);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedRecord(null);
  };

  const columns: ColumnDef<AttendanceRecord>[] = [
    {
      accessorKey: "date",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          {t("table.date")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: "department",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          {t("table.department")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: "shift",
      header: t("table.shift"),
      cell: ({ row }) => (
        <span className="font-medium">{row.getValue("shift")}</span>
      ),
    },
    {
      accessorKey: "role",
      header: t("table.role"),
      cell: ({ row }) => (
        <span className="text-gray-600">{row.getValue("role")}</span>
      ),
    },
    {
      accessorKey: "employeeId",
      header: t("table.id"),
      cell: ({ row }) => (
        <span className="font-mono text-sm">{row.getValue("employeeId")}</span>
      ),
    },
    {
      accessorKey: "firstName",
      header: t("table.firstLastName"),
      cell: ({ row }) => (
        <span className="font-medium">
          {row.getValue("firstName")} {row.original.lastName}
        </span>
      ),
    },
    {
      accessorKey: "approverFirstName",
      header: t("table.approver"),
      cell: ({ row }) => {
        const record = row.original;
        return record.approverFirstName ? (
          <span className="font-medium">
            {record.approverFirstName} {record.approverLastName}
          </span>
        ) : (
          <span className="text-gray-400">-</span>
        );
      },
    },
    {
      accessorKey: "approverId",
      header: t("table.id"),
      cell: ({ row }) => {
        const approverId = row.getValue("approverId") as string;
        return approverId ? (
          <span className="font-mono text-sm">{approverId}</span>
        ) : (
          <span className="text-gray-400">-</span>
        );
      },
    },
    {
      accessorKey: "status",
      header: t("table.status"),
      cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
    },
    {
      accessorKey: "comment",
      header: t("table.comment"),
      cell: ({ row }) => {
        const comment = row.getValue("comment") as string;
        return comment ? (
          <div className="max-w-[200px] truncate" title={comment}>
            {comment}
          </div>
        ) : (
          <span className="text-gray-400">-</span>
        );
      },
    },
    {
      id: "closed",
      header: t("table.closed"),
      cell: ({ row }) => {
        const status = row.original.status;
        const isClosed =
          status === "validated" || status === "validated_with_comment";
        return (
          <div className="text-center">
            {isClosed ? (
              <span className="inline-block w-3 h-3 bg-green-500 rounded-full"></span>
            ) : (
              <span className="inline-block w-3 h-3 bg-red-500 rounded-full"></span>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const record = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleViewDetails(record)}>
                <Eye className="mr-2 h-4 w-4" />
                {t("actions.viewDetails")}
              </DropdownMenuItem>
              {record.status === "waiting_for_validation" && (
                <>
                  <DropdownMenuItem
                    onClick={() =>
                      onValidate({
                        recordId: record.id,
                        action: "approve",
                        approverId: "7826",
                      })
                    }
                  >
                    {t("actions.quickApprove")}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleViewDetails(record)}>
                    {t("actions.approveWithComment")}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    return data.filter((record) => {
      if (filters.department && record.department !== filters.department) {
        return false;
      }
      if (filters.status && record.status !== filters.status) {
        return false;
      }
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const fullName = `${record.firstName} ${record.lastName}`.toLowerCase();
        const approverName = record.approverFirstName
          ? `${record.approverFirstName} ${record.approverLastName}`.toLowerCase()
          : "";

        if (
          !fullName.includes(searchLower) &&
          !approverName.includes(searchLower) &&
          !record.employeeId.includes(searchLower) &&
          !record.role.toLowerCase().includes(searchLower)
        ) {
          return false;
        }
      }
      return true;
    });
  }, [data, filters]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
    initialState: {
      pagination: {
        pageSize: 15,
      },
    },
  });

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>{t("table.attendanceRecords")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id} className="whitespace-nowrap">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="whitespace-nowrap">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      {isLoading ? t("loading") : t("noResults")}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between space-x-2 py-4">
            <div className="text-sm text-muted-foreground">
              {t("pagination.showing")}{" "}
              {table.getState().pagination.pageIndex *
                table.getState().pagination.pageSize +
                1}{" "}
              {t("pagination.to")}{" "}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) *
                  table.getState().pagination.pageSize,
                filteredData.length,
              )}{" "}
              {t("pagination.of")} {filteredData.length}{" "}
              {t("pagination.entries")}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                {t("pagination.previous")}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                {t("pagination.next")}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <ValidationModal
        record={selectedRecord}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onValidate={onValidate}
      />
    </>
  );
};
