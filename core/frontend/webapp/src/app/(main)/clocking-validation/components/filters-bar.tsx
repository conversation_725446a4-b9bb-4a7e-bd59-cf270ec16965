"use client";

import { useState } from "react";
import { Calendar, Search } from "lucide-react";
import { useTranslations } from "next-intl";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { AttendanceFilters, Department } from "../types";
import { cn } from "@/lib/utils";

interface FiltersBarProps {
  filters: AttendanceFilters;
  onFiltersChange: (filters: AttendanceFilters) => void;
  departments: Department[];
  className?: string;
}

export const FiltersBar = ({
  filters,
  onFiltersChange,
  departments,
  className,
}: FiltersBarProps) => {
  const t = useTranslations("clocking-validation.filters");

  const [date, setDate] = useState<Date | undefined>(
    filters.date ? new Date(filters.date) : undefined,
  );

  const handleDateSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate);
    onFiltersChange({
      ...filters,
      date: selectedDate ? format(selectedDate, "dd/MM/yyyy") : undefined,
    });
  };

  const handleSearchChange = (value: string) => {
    onFiltersChange({
      ...filters,
      search: value || undefined,
    });
  };

  const handleDepartmentChange = (value: string) => {
    onFiltersChange({
      ...filters,
      department: value === "all" ? undefined : value,
    });
  };

  return (
    <div className={`py-4 px-0 ${className}`}>
      <div className="grid grid-cols-4 gap-4">
        {/* Date Picker */}
        <div className="flex items-center">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-between text-left font-normal",
                  !date && "text-muted-foreground",
                )}
              >
                {date ? format(date, "dd/MM/yyyy") : t("selectDate")}
                <Calendar className="ml-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={date}
                onSelect={handleDateSelect}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Search */}
        <div className="flex items-center">
          <div className="relative w-full">
            <Input
              placeholder={t("searchPlaceholder")}
              value={filters.search || ""}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>
        </div>

        {/* Department Filter */}
        <div className="flex items-center">
          <Select
            value={filters.department || "all"}
            onValueChange={handleDepartmentChange}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t("selectDepartment")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("allDepartments")}</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept.id} value={dept.name}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Empty Column */}
        <div className="flex items-center">
          {/* Intentionally empty for spacing */}
        </div>
      </div>
    </div>
  );
};
