import { AttendanceStatistics } from "../types";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface StatisticsCardsProps {
  statistics: AttendanceStatistics;
  className?: string;
  onCardClick?: (type: "total" | "waiting" | "validated" | "commented") => void;
  activeCard?: string;
}

interface StatCardProps {
  title: string;
  value: number;
  variant: "total" | "waiting" | "validated" | "commented";
  className?: string;
  onClick?: () => void;
  isActive?: boolean;
}

const StatCard = ({
  title,
  value,
  variant,
  className,
  onClick,
  isActive,
}: StatCardProps) => {
  const variants = {
    total: "bg-[#F5FBFF] text-[#4762F1] border-[#4762F1]",
    waiting: "bg-[#FCF6DF] text-[#C59800] border-[#C59800]",
    validated: "bg-[#EBF6EC] text-[#4CAF50] border-[#4CAF50]",
    commented: "bg-[#FBE3E3] text-[#D60000] border-[#E6A5A5]",
  };

  const activeVariants = {
    total: "bg-[#F5FBFF] text-[#4762F1] border-[#4762F1]",
    waiting: "bg-[#C59800] text-white border-[#C59800]",
    validated: "bg-[#4CAF50] text-white border-[#4CAF50]",
    commented: "bg-[#D60000] text-white border-[#D60000]",
  };

  return (
    <Card
      className={cn(
        isActive ? activeVariants[variant] : variants[variant],
        "border-[1px] cursor-pointer hover:shadow-lg transition-all rounded-lg",
        className,
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center">
          <div className="text-2xl font-bold pr-4 border-r border-current">
            {value}
          </div>
          <div className="text-md font-medium text-left leading-tight pl-4 flex-1">
            {title}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const StatisticsCards = ({
  statistics,
  className,
  onCardClick,
  activeCard,
}: StatisticsCardsProps) => {
  const t = useTranslations("clocking-validation.statistics");

  return (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",
        className,
      )}
    >
      <StatCard
        title={t("totalAttendanceSheets")}
        value={statistics.total}
        variant="total"
        // onClick={() => onCardClick?.("total")}
        isActive={activeCard === "total"}
      />
      <StatCard
        title={t("waitingForValidation")}
        value={statistics.waitingForValidation}
        variant="waiting"
        onClick={() => onCardClick?.("waiting")}
        isActive={activeCard === "waiting"}
      />
      <StatCard
        title={t("validated")}
        value={statistics.validated}
        variant="validated"
        onClick={() => onCardClick?.("validated")}
        isActive={activeCard === "validated"}
      />
      <StatCard
        title={t("validatedWithComment")}
        value={statistics.validatedWithComment}
        variant="commented"
        onClick={() => onCardClick?.("commented")}
        isActive={activeCard === "commented"}
      />
    </div>
  );
};
