import { AttendanceStatus } from "../types";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface StatusBadgeProps {
  status: AttendanceStatus;
  className?: string;
}

export const StatusBadge = ({ status, className }: StatusBadgeProps) => {
  const t = useTranslations("clocking-validation.status");

  const statusConfig = {
    waiting_for_validation: {
      label: t("waitingForValidation"),
      className: "bg-amber-100 text-amber-800 border-amber-300",
    },
    validated: {
      label: t("validated"),
      className: "bg-green-100 text-green-800 border-green-300",
    },
    validated_with_comment: {
      label: t("validatedWithComment"),
      className: "bg-red-100 text-red-800 border-red-300",
    },
    rejected: {
      label: t("rejected"),
      className: "bg-gray-100 text-gray-800 border-gray-300",
    },
  };

  const config = statusConfig[status];

  return (
    <Badge
      variant="outline"
      className={cn(config.className, "font-medium", className)}
    >
      {config.label}
    </Badge>
  );
};
