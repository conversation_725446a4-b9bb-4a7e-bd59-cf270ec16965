"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Eye, MessageSquare, Check, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AttendanceRecord, ValidationAction } from "../types";
import { StatusBadge } from "./status-badge";

interface ValidationModalProps {
  record: AttendanceRecord | null;
  isOpen: boolean;
  onClose: () => void;
  onValidate: (action: ValidationAction) => void;
}

export const ValidationModal = ({
  record,
  isOpen,
  onClose,
  onValidate,
}: ValidationModalProps) => {
  const t = useTranslations("clocking-validation");
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!record) return null;

  const handleValidation = async (
    action: "approve" | "reject" | "approve_with_comment",
  ) => {
    if (action === "approve_with_comment" && !comment.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onValidate({
        recordId: record.id,
        action,
        comment: action === "approve_with_comment" ? comment : undefined,
        approverId: "7826", // This should come from current user context
      });

      setComment("");
      onClose();
    } catch (error) {
      console.error("Validation failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const canValidate = record.status === "waiting_for_validation";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t("modal.attendanceRecordDetails")}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Record Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t("modal.employeeInformation")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {t("table.date")}
                  </label>
                  <p className="font-semibold">{record.date}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {t("table.department")}
                  </label>
                  <p className="font-semibold">{record.department}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {t("table.shift")}
                  </label>
                  <p className="font-semibold">{record.shift}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {t("table.role")}
                  </label>
                  <p className="font-semibold">{record.role}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {t("table.id")}
                  </label>
                  <p className="font-semibold">{record.employeeId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {t("table.firstLastName")}
                  </label>
                  <p className="font-semibold">
                    {record.firstName} {record.lastName}
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {t("table.status")}
                    </label>
                    <div className="mt-1">
                      <StatusBadge status={record.status} />
                    </div>
                  </div>
                  {record.approverId && (
                    <div className="text-right">
                      <label className="text-sm font-medium text-gray-600">
                        Approver
                      </label>
                      <p className="font-semibold">
                        {record.approverFirstName} {record.approverLastName}
                      </p>
                      <p className="text-sm text-gray-500">
                        ID: {record.approverId}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {record.comment && (
                <div className="pt-4 border-t">
                  <label className="text-sm font-medium text-gray-600">
                    Existing Comment
                  </label>
                  <p className="mt-1 p-3 bg-gray-50 rounded-md text-sm">
                    {record.comment}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Validation Actions */}
          {canValidate && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t("modal.validationActions")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600 mb-2 block">
                    {t("modal.addCommentOptional")}
                  </label>
                  <Textarea
                    placeholder={t("modal.addCommentPlaceholder")}
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => handleValidation("approve")}
                    disabled={isSubmitting}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    {t("modal.approve")}
                  </Button>

                  <Button
                    onClick={() => handleValidation("approve_with_comment")}
                    disabled={isSubmitting || !comment.trim()}
                    variant="outline"
                    className="flex-1"
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    {t("modal.approveWithComment")}
                  </Button>

                  <Button
                    onClick={() => handleValidation("reject")}
                    disabled={isSubmitting}
                    variant="destructive"
                    className="flex-1"
                  >
                    <X className="mr-2 h-4 w-4" />
                    {t("modal.reject")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Preview Action */}
          <div className="flex justify-between items-center pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              {t("modal.close")}
            </Button>
            <Button variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              {t("modal.preview")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
