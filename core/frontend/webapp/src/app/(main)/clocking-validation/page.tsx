"use client";

import { useCallback } from "react";
import { useTranslations } from "next-intl";
import CustomPageCard from "@/components/common/CustomPageCard";
import CustomIcon from "@/components/common/CustomIcons";
import { Button } from "@/components/ui/button";
import {
  StatisticsCards,
  FiltersBar,
  AttendanceTable,
  SimpleTable,
  WaitingValidationTable,
} from "./components";
import { ValidatedWithCommentTable } from "./components/validated-with-comment-table";
import { AttendanceFilters, ValidationAction, AttendanceRecord } from "./types";
import { mockDepartments } from "./mock-data";
import { useAttendanceStore } from "./store";

export default function AttendanceValidationPage() {
  const t = useTranslations("clocking-validation");

  // استخدام الـ store
  const {
    activeView,
    filters,
    isLoading,
    setActiveView,
    setFilters,
    setLoading,
    updateRecord,
    getFilteredData,
    getStats,
  } = useAttendanceStore();

  // الحصول على البيانات والإحصائيات من الـ store
  const currentData = getFilteredData();
  const statistics = getStats();

  const handleFiltersChange = useCallback(
    (newFilters: AttendanceFilters) => {
      setFilters(newFilters);
    },
    [setFilters],
  );

  const handleCardClick = useCallback(
    (type: "total" | "waiting" | "validated" | "commented") => {
      setActiveView(type);
    },
    [setActiveView],
  );

  const handleValidation = useCallback(
    async (action: ValidationAction) => {
      setLoading(true);

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Update the record using store
        let newStatus: AttendanceRecord["status"];
        switch (action.action) {
          case "approve":
            newStatus = "validated";
            break;
          case "approve_with_comment":
            newStatus = "validated_with_comment";
            break;
          case "reject":
            newStatus = "rejected";
            break;
          default:
            newStatus = "waiting_for_validation";
        }

        updateRecord(action.recordId, {
          status: newStatus,
          comment: action.comment,
          validatedAt: new Date().toISOString(),
          approverId: action.approverId,
        });

        console.log("Validation completed:", action);
      } catch (error) {
        console.error("Validation failed:", error);
      } finally {
        setLoading(false);
      }
    },
    [setLoading, updateRecord],
  );

  const renderTable = () => {
    if (activeView === "validated") {
      return <SimpleTable data={currentData} />;
    }

    if (activeView === "commented") {
      return (
        <ValidatedWithCommentTable
          data={currentData}
          onToggleClosed={(recordId, closed) => {
            console.log(
              `Record ${recordId} closed status changed to: ${closed}`,
            );
          }}
        />
      );
    }

    if (activeView === "waiting") {
      return <WaitingValidationTable data={currentData} />;
    }

    // Default case for "total" view
    return (
      <AttendanceTable
        data={currentData}
        filters={filters}
        onValidate={handleValidation}
        isLoading={isLoading}
      />
    );
  };

  return (
    <CustomPageCard>
      <div className="container mx-auto p-6 space-y-6">
        {/* Filters */}
        <FiltersBar
          filters={filters}
          onFiltersChange={handleFiltersChange}
          departments={mockDepartments}
        />

        {/* Statistics Cards */}
        <StatisticsCards
          statistics={{
            total: statistics.total,
            waitingForValidation: statistics.waiting,
            validated: statistics.validated,
            validatedWithComment: statistics.commented,
            rejected: 0,
          }}
          onCardClick={handleCardClick}
          activeCard={activeView}
        />

        {/* Table */}
        {renderTable()}

        {/* Export Button */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            className="whitespace-nowrap flex items-center gap-2"
          >
            <CustomIcon
              name="exportClockingValidation"
              className="w-4 h-4"
              style={{ width: "auto" }}
            />
            {t("actions.export")}
          </Button>
        </div>
      </div>
    </CustomPageCard>
  );
}
