import { AttendanceRecord, AttendanceStatistics, Department } from "./types";

export const mockDepartments: Department[] = [
  { id: "assembly", name: "Assembly" },
  { id: "production", name: "Production" },
  { id: "quality", name: "Quality Control" },
  { id: "maintenance", name: "Maintenance" },
  { id: "logistics", name: "Logistics" },
];

export const mockAttendanceRecords: AttendanceRecord[] = [
  {
    id: "1",
    date: "16/07/2025",
    department: "Assembly",
    shift: "Morning",
    role: "Team leader",
    employeeId: "5260",
    firstName: "<PERSON><PERSON><PERSON>",
    lastName: "BERAZZI",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-16T08:00:00Z",
  },
  {
    id: "2",
    date: "17/07/2025",
    department: "Production",
    shift: "Morning",
    role: "Operator",
    employeeId: "5261",
    firstName: "<PERSON>",
    lastName: "MALIK",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-17T08:00:00Z",
  },
  {
    id: "3",
    date: "18/07/2025",
    department: "Quality Control",
    shift: "Evening",
    role: "Inspector",
    employeeId: "5262",
    firstName: "Sara",
    lastName: "HASSAN",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-18T08:00:00Z",
  },
  {
    id: "4",
    date: "16/07/2025",
    department: "Maintenance",
    shift: "Morning",
    role: "Technician",
    employeeId: "5263",
    firstName: "Omar",
    lastName: "BENALI",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-16T08:00:00Z",
  },
  {
    id: "5",
    date: "17/07/2025",
    department: "Logistics",
    shift: "Morning",
    role: "Coordinator",
    employeeId: "5264",
    firstName: "Fatima",
    lastName: "ZEROUALI",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-17T08:00:00Z",
  },
];

// Validated records
export const mockValidatedRecords: AttendanceRecord[] = [
  {
    id: "v1",
    date: "16/07/2025",
    department: "Assembly",
    shift: "Morning",
    role: "Team leader",
    employeeId: "5260",
    firstName: "Yassine",
    lastName: "BERAZZI",
    approverId: "1038",
    approverFirstName: "Amine",
    approverLastName: "MOSAFIR",
    status: "validated",
    validatedAt: "2025-07-16T10:00:00Z",
    submittedAt: "2025-07-16T08:00:00Z",
  },
  {
    id: "v2",
    date: "16/07/2025",
    department: "Production",
    shift: "Morning",
    role: "Operator",
    employeeId: "5261",
    firstName: "Ahmed",
    lastName: "MALIK",
    approverId: "1038",
    approverFirstName: "Amine",
    approverLastName: "MOSAFIR",
    status: "validated",
    validatedAt: "2025-07-16T10:00:00Z",
    submittedAt: "2025-07-16T08:00:00Z",
  },
  {
    id: "v3",
    date: "17/07/2025",
    department: "Quality Control",
    shift: "Evening",
    role: "Inspector",
    employeeId: "5262",
    firstName: "Sara",
    lastName: "HASSAN",
    approverId: "1038",
    approverFirstName: "Amine",
    approverLastName: "MOSAFIR",
    status: "validated",
    validatedAt: "2025-07-17T10:00:00Z",
    submittedAt: "2025-07-17T08:00:00Z",
  },
];

// Validated with comment records - Fixed data instead of random
export const mockValidatedWithCommentRecords: AttendanceRecord[] = [
  // Omar BENALI records
  {
    id: "vc1",
    date: "16/07/2025",
    department: "Maintenance",
    shift: "Morning",
    role: "Technician",
    employeeId: "5263",
    firstName: "Omar",
    lastName: "BENALI",
    approverId: "7312",
    approverFirstName: "Ahmed",
    approverLastName: "Sabiri",
    status: "validated_with_comment",
    comment: "Additional verification required for attendance timing",
    validatedAt: "2025-07-16T11:00:00Z",
    submittedAt: "2025-07-16T08:00:00Z",
    closed: true,
  },
  {
    id: "vc2",
    date: "17/07/2025",
    department: "Maintenance",
    shift: "Morning",
    role: "Technician",
    employeeId: "5263",
    firstName: "Omar",
    lastName: "BENALI",
    approverId: "7312",
    approverFirstName: "Ahmed",
    approverLastName: "Sabiri",
    status: "validated_with_comment",
    comment: "Time adjustment needed",
    validatedAt: "2025-07-17T11:00:00Z",
    submittedAt: "2025-07-17T08:00:00Z",
    closed: false,
  },
];

// Generate additional records for other employees
const departments = [
  "Assembly",
  "Production",
  "Quality Control",
  "Maintenance",
  "Logistics",
];
const dates = ["16/07/2025", "17/07/2025", "18/07/2025"];
const otherEmployees = [
  { id: "5260", firstName: "Yassine", lastName: "BERAZZI" },
  { id: "5261", firstName: "Ahmed", lastName: "MALIK" },
  { id: "5262", firstName: "Sara", lastName: "HASSAN" },
  { id: "5264", firstName: "Fatima", lastName: "ZEROUALI" },
];

for (let i = 3; i <= 21; i++) {
  const deptIndex = (i - 3) % departments.length;
  const dateIndex = (i - 3) % dates.length;
  const employeeIndex = (i - 3) % otherEmployees.length;

  const randomDept = departments[deptIndex];
  const randomDate = dates[dateIndex];
  const randomEmployee = otherEmployees[employeeIndex];

  mockValidatedWithCommentRecords.push({
    id: `vc${i}`,
    date: randomDate,
    department: randomDept,
    shift: "Morning",
    role: "Team leader",
    employeeId: randomEmployee.id,
    firstName: randomEmployee.firstName,
    lastName: randomEmployee.lastName,
    approverId: "7312",
    approverFirstName: "Ahmed",
    approverLastName: "Sabiri",
    status: "validated_with_comment",
    comment: "Additional verification required for attendance timing",
    validatedAt: "2025-07-16T11:00:00Z",
    submittedAt: "2025-07-16T08:00:00Z",
    closed: i % 2 === 0, // Alternate closed status
  });
}

// Generate more mock data for Omar BENALI specifically to test filtering
for (let i = 4; i <= 6; i++) {
  mockAttendanceRecords.push({
    id: i.toString() + "_omar",
    date: i === 4 ? "16/07/2025" : i === 5 ? "17/07/2025" : "16/07/2025",
    department: "Maintenance",
    shift: "Morning",
    role: "Technician",
    employeeId: "5263",
    firstName: "Omar",
    lastName: "BENALI",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-16T08:00:00Z",
  });
}

// Generate more mock data for other employees
for (let i = 7; i <= 86; i++) {
  mockAttendanceRecords.push({
    id: i.toString(),
    date: "16/07/2025",
    department: "Assembly",
    shift: "Morning",
    role: "Team leader",
    employeeId: "5260",
    firstName: "Yassine",
    lastName: "BERAZZI",
    approverId: "7826",
    approverFirstName: "Said",
    approverLastName: "Hanim",
    status: "waiting_for_validation",
    submittedAt: "2025-07-16T08:00:00Z",
  });
}

export const mockStatistics: AttendanceStatistics = {
  total: 150,
  waitingForValidation: 86,
  validated: 43,
  validatedWithComment: 21,
  rejected: 0,
};
