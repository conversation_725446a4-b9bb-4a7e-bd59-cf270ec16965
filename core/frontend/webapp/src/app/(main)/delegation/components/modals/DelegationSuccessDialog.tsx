import React from "react";
import { Check } from "lucide-react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { ReusableButton } from "@/components/common/CustomButtons";

interface DelegationSuccessDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  onHomeClick?: () => void;
  onDelegationClick?: () => void;
}

const DelegationSuccessDialog: React.FC<DelegationSuccessDialogProps> = ({
  isOpen,
  onClose,
  title = "The delegation has been successfully done.",
  onHomeClick,
  onDelegationClick,
}) => {
  const handleHomeClick = () => {
    if (onHomeClick) {
      onHomeClick();
    } else {
      // Default navigation logic to Home
      onClose();
    }
  };

  const handleDelegationClick = () => {
    if (onDelegationClick) {
      onDelegationClick();
    } else {
      // Default navigation logic to Delegation
      onClose();
    }
  };

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={onClose}
      id="delegation-success-dialog"
    >
      <div className="flex flex-col items-center justify-center py-6">
        <Check className="h-20 w-20 bg-green-100 text-green-600 rounded-full p-2 mb-4" />
        <p className="text-center text-lg font-medium">{title}</p>
      </div>
      <div className="flex justify-between mt-4 px-4 pb-2">
        <ReusableButton
          label="Home"
          variant="outline"
          onClick={handleHomeClick}
        />
        <ReusableButton
          label="Delegation"
          color="primary"
          className="ml-2"
          onClick={handleDelegationClick}
        />
      </div>
    </ReusableDialog>
  );
};

export default DelegationSuccessDialog;
