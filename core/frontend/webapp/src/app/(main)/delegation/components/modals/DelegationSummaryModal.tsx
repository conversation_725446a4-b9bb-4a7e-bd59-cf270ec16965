"use client";

import React, { useState } from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { ReusableButton } from "@/components/common/CustomButtons";
import { Users2, Calendar } from "lucide-react";
import CustomIcon from "@/components/common/CustomIcons";
import { CreateDelegationData } from "./CreateDelegationModal";
import DelegationSuccessDialog from "./DelegationSuccessDialog";

interface DelegationSummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onBack: () => void;
  delegationData: CreateDelegationData;
}

const mockUsers = [
  { label: "1542 - Samir SABIRI", value: "1542" },
  { label: "1543 - Ahmed HASSAN", value: "1543" },
  { label: "1544 - Sarah MARTINEZ", value: "1544" },
  { label: "1545 - John SMITH", value: "1545" },
];

export default function DelegationSummaryModal({
  isOpen,
  onClose,
  onConfirm,
  onBack,
  delegationData,
}: DelegationSummaryModalProps) {
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const getDelegatorInfo = () => {
    if (delegationData.type === "forme") {
      return "Me : Ali JABER";
    } else {
      const teamMember = mockUsers.find(
        (user) => user.value === delegationData.teamMember,
      );
      return teamMember ? teamMember.label : "Unknown";
    }
  };

  const getDelegateToInfo = () => {
    const delegateTo = mockUsers.find(
      (user) => user.value === delegationData.delegatedTo,
    );
    return delegateTo ? delegateTo.label : "Unknown";
  };

  const handleConfirm = () => {
    // Close the summary modal and show success dialog
    onClose();
    setShowSuccessDialog(true);
    // Call the original onConfirm if needed
    onConfirm();
  };

  const handleSuccessClose = () => {
    setShowSuccessDialog(false);
  };

  const handleHomeClick = () => {
    setShowSuccessDialog(false);
    // Add navigation logic to home here
  };

  const handleDelegationClick = () => {
    setShowSuccessDialog(false);
    // Add navigation logic to delegation page here
  };

  const footer = (
    <div className="flex justify-between w-full">
      <ReusableButton
        label="Back"
        variant="outline"
        onClick={onBack}
        className="px-6 py-2"
      />
      <ReusableButton
        label="Confirm"
        variant="default"
        onClick={handleConfirm}
        className="px-6 py-2 bg-black text-white hover:bg-gray-900"
      />
    </div>
  );

  return (
    <>
      <ReusableDialog
        isOpen={isOpen}
        onClose={onClose}
        title="Delegation Summary"
        size="2xl"
        footer={footer}
        className="max-w-7xl w-full"
      >
        <div className="space-y-6 py-4">
          {/* Delegation Icon */}
          <div className="flex flex-col items-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <CustomIcon name="checkMark" className="w-10 h-10" />
            </div>
            <p className="text-sm text-gray-500 text-center">
              Please confirm the delegation details before submission.
            </p>
          </div>

          {/* Delegation Details */}
          <div className="space-y-6">
            {/* Delegator and Delegate To */}
            <div className="flex items-center relative">
              <div
                className="flex items-center space-x-3 min-w-0"
                style={{ width: "70%" }}
              >
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Users2 className="w-4 h-4 text-gray-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-gray-500 font-medium">Delegator</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {getDelegatorInfo()}
                  </p>
                </div>
              </div>

              <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center z-10">
                <div
                  style={{
                    width: "58px",
                    height: "5px",
                    background: "#4762F1",
                    opacity: 1,
                    borderRadius: "2.5px",
                  }}
                />
                <div
                  style={{
                    width: "9px",
                    height: "18px",
                    background: "#4762F1",
                    opacity: 1,
                    clipPath: "polygon(0 0, 100% 50%, 0 100%)",
                    marginLeft: "-1px",
                  }}
                />
              </div>

              <div
                className="flex items-center space-x-3 min-w-0 ml-auto"
                style={{ width: "30%" }}
              >
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Users2 className="w-4 h-4 text-gray-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-gray-500 font-medium">
                    Delegate To :
                  </p>
                  <p className="text-sm font-semibold text-gray-900">
                    {getDelegateToInfo()}
                  </p>
                </div>
              </div>
            </div>

            {/* Date Range */}
            <div className="flex items-center relative">
              <div
                className="flex items-center space-x-3 min-w-0"
                style={{ width: "40%" }}
              >
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-4 h-4 text-gray-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-gray-500 font-medium">From :</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {delegationData.startDate}
                  </p>
                </div>
              </div>

              <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center z-10">
                <div
                  style={{
                    width: "58px",
                    height: "5px",
                    background: "#4762F1",
                    opacity: 1,
                    borderRadius: "2.5px",
                  }}
                />
                <div
                  style={{
                    width: "9px",
                    height: "18px",
                    background: "#4762F1",
                    opacity: 1,
                    clipPath: "polygon(0 0, 100% 50%, 0 100%)",
                    marginLeft: "-1px",
                  }}
                />
              </div>

              <div
                className="flex items-center space-x-3 min-w-0 ml-auto"
                style={{ width: "30%" }}
              >
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-4 h-4 text-gray-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-gray-500 font-medium">To :</p>
                  <p className="text-sm font-semibold text-gray-900">
                    {delegationData.endDate}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ReusableDialog>

      <DelegationSuccessDialog
        isOpen={showSuccessDialog}
        onClose={handleSuccessClose}
        onHomeClick={handleHomeClick}
        onDelegationClick={handleDelegationClick}
      />
    </>
  );
}
