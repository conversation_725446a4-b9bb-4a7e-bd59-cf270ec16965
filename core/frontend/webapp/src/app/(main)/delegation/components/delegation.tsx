"use client";

import React, { useState } from "react";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
  CustomTabsContent,
} from "@/components/common/CustomTabs";
import { DataTable } from "@/components/common/tables/DataTable";
import { ReusableButton } from "@/components/common/CustomButtons";
import { PlusIcon, ListFilter } from "lucide-react";
import CustomIcon from "@/components/common/CustomIcons";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import RevokeDelegationModal from "./modals/RevokeDelegationModal";
import { useDelegationStore } from "../store/delegationStore";
import {
  DelegationRow,
  SortDirection,
  DelegationTab,
} from "../types/delegation";
import CreateDelegationModal, {
  CreateDelegationData,
} from "./modals/CreateDelegationModal";

// Sort button for delegation table
function SortButton({
  sortDirection,
  onClick,
}: {
  sortDirection: SortDirection;
  onClick: () => void;
}) {
  return (
    <button
      onClick={onClick}
      className="flex items-center gap-2 px-4 py-2 rounded-xl border border-gray-200 bg-white text-gray-700 text-sm font-medium shadow-sm hover:bg-gray-50 transition-colors duration-150"
    >
      <ListFilter size={18} className="text-gray-400" />
      <span>
        Sort by : <span className="font-semibold text-gray-900">Date</span>
      </span>
      <svg
        className={`ml-1 transform transition-transform ${sortDirection === "desc" ? "rotate-180" : ""}`}
        width="14"
        height="14"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M7 10l3 3 3-3"
          stroke="#A3A3A3"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  );
}

// Example columns for DataTable
import type { ColumnDef } from "@tanstack/react-table";

export default function DelegationInterface() {
  const [showRevokeModal, setShowRevokeModal] = useState(false);
  const [revokeId, setRevokeId] = useState<string | null>(null);

  // Show modal when Revoke is clicked
  function handleCancelDelegation(id: string) {
    setRevokeId(id);
    setShowRevokeModal(true);
  }

  // Confirm revoke action
  function handleConfirmRevoke() {
    // TODO: Add API/store logic to revoke delegation using revokeId
    console.log("Revoking delegation", revokeId);
    setShowRevokeModal(false);
    setRevokeId(null);
  }

  // Cancel modal
  function handleCloseRevokeModal() {
    setShowRevokeModal(false);
    setRevokeId(null);
  }
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const {
    currentTab,
    statusFilter,
    sortDirection,
    setCurrentTab,
    setStatusFilter,
    toggleSortDirection,
    getFilteredDelegations,
    getStatusCounts,
    getTabCounts,
  } = useDelegationStore();

  const filteredData = getFilteredDelegations();
  const statusCounts = getStatusCounts();
  const tabCounts = getTabCounts();

  const handleCreateDelegation = (delegationData: CreateDelegationData) => {
    // TODO: Add logic to handle the new delegation data
    console.log("Creating new delegation:", delegationData);
    // Here you would typically call an API or update the store
  };

  const tabList = [
    { value: "all", label: `All delegations (${tabCounts.all})` },
    { value: "assigned", label: `Assigned to me (${tabCounts.assigned})` },
    { value: "my", label: `My Assignations (${tabCounts.my})` },
  ];

  // Columns for DataTable
  const columns: ColumnDef<DelegationRow>[] = [
    {
      accessorKey: "delegator",
      header: "Delegator",
      cell: ({ getValue, row }) => (
        <span
          className={
            row.index === 0 || row.index === 1
              ? "font-medium"
              : "text-gray-400 font-medium"
          }
        >
          {getValue<string>()}
        </span>
      ),
    },
    {
      accessorKey: "delegatedTo",
      header: "Delegated to",
      cell: ({ getValue, row }) => (
        <span
          className={
            row.index === 0 || row.index === 1
              ? "font-medium"
              : "text-gray-400 font-medium"
          }
        >
          {getValue<string>()}
        </span>
      ),
    },
    {
      accessorKey: "status",
      header: "Statut",
      cell: ({ getValue }) => {
        const value = getValue<string>();
        let badge;
        if (value === "Approved & Not Started Yet") {
          badge = (
            <span
              className="inline-flex items-center gap-2 px-4 py-1 rounded-full text-xs"
              style={{ background: "#DEF2DB", color: "#3A7D44" }}
            >
              <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
                <path
                  d="M7.5 13.5L4.5 10.5L5.56 9.44L7.5 11.38L13.44 5.44L14.5 6.5L7.5 13.5Z"
                  fill="#22C55E"
                />
              </svg>
              Approved & Not Started Yet
            </span>
          );
        } else if (value === "Starting...") {
          badge = (
            <span
              className="inline-flex items-center gap-2 px-4 py-1 rounded-full text-xs"
              style={{ background: "#DAE8F9", color: "#4762F1" }}
            >
              <CustomIcon
                name="blueClock"
                className="w-4 h-4"
                style={{ color: "#4762F1" }}
              />
              Starting...
            </span>
          );
        } else if (value === "Terminated") {
          badge = (
            <span
              className="inline-flex items-center gap-2 px-4 py-1 rounded-full text-xs"
              style={{ background: "#E4E4E7", color: "#848484" }}
            >
              <CustomIcon
                name="finishFlag"
                className="w-4 h-4"
                style={{ color: "#848484" }}
              />
              Terminated
            </span>
          );
        } else if (value === "Canceled") {
          badge = (
            <span
              className="inline-flex items-center gap-2 px-4 py-1 rounded-full text-xs"
              style={{ background: "#F2DDDD", color: "#C95454" }}
            >
              <CustomIcon
                name="unavailable"
                className="w-4 h-4"
                style={{ color: "#C95454" }}
              />
              Canceled
            </span>
          );
        } else {
          badge = value;
        }
        return badge;
      },
    },
    {
      accessorKey: "fromDate",
      header: "From date",
      cell: ({ getValue, row }) => (
        <span
          className={
            row.index === 0 || row.index === 1
              ? "font-medium"
              : "text-gray-400 "
          }
        >
          {getValue<string>()}
        </span>
      ),
    },
    {
      accessorKey: "toDate",
      header: "To date",
      cell: ({ getValue, row }) => (
        <span
          className={
            row.index === 0 || row.index === 1
              ? "font-medium"
              : "text-gray-400 "
          }
        >
          {getValue<string>()}
        </span>
      ),
    },
    {
      accessorKey: "createdDate",
      header: "Created date",
      cell: ({ getValue, row }) => (
        <span
          className={
            row.index === 0 || row.index === 1 ? "font-medium" : "text-gray-400"
          }
        >
          {getValue<string>()}
        </span>
      ),
    },
    {
      accessorKey: "action",
      header: () => (
        <span className="text-gray-500 font-medium flex w-full justify-end text-right">
          Action
        </span>
      ),
      cell: ({ row }) => (
        <div className="flex justify-end w-full">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Actions"
              >
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="9" cy="4.5" r="1" fill="#BDBDBD" />
                  <circle cx="9" cy="9" r="1" fill="#BDBDBD" />
                  <circle cx="9" cy="13.5" r="1" fill="#BDBDBD" />
                </svg>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => handleCancelDelegation(row.original.id)}
              >
                Revoke
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      enableSorting: false,
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <CustomTabs
          value={currentTab}
          onValueChange={(value) => setCurrentTab(value as DelegationTab)}
        >
          <CustomTabsList>
            {tabList.map((t) => (
              <CustomTabsTrigger key={t.value} value={t.value}>
                {t.label}
              </CustomTabsTrigger>
            ))}
          </CustomTabsList>
        </CustomTabs>
        <div className="flex gap-3">
          <SortButton
            sortDirection={sortDirection}
            onClick={toggleSortDirection}
          />
          <ReusableButton
            icon={PlusIcon}
            label="Create new Delegation"
            color="primary"
            variant="default"
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-black text-white hover:bg-gray-900 border-black"
          />
        </div>
      </div>
      {/* Custom status badges styled as in the image */}
      <div className="flex gap-2 mb-2">
        <button
          onClick={() =>
            setStatusFilter(statusFilter === "approved" ? null : "approved")
          }
          className={`flex items-center gap-1 px-4 py-1 rounded-full border text-xs transition-colors duration-150 cursor-pointer shadow-sm
            ${statusFilter === "approved" ? "border-green-300" : "border-green-200"}`}
          style={{ background: "#DEF2DB", color: "#3A7D44" }}
        >
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <path
              d="M7.5 13.5L4.5 10.5L5.56 9.44L7.5 11.38L13.44 5.44L14.5 6.5L7.5 13.5Z"
              fill="#22C55E"
            />
          </svg>
          <span>Approved & Not Started Yet</span>
          <span className="ml-1 font-normal">({statusCounts.approved})</span>
        </button>
        <button
          onClick={() =>
            setStatusFilter(statusFilter === "starting" ? null : "starting")
          }
          className={`flex items-center gap-1 px-4 py-1 rounded-full border text-xs transition-colors duration-150 cursor-pointer shadow-sm
            ${statusFilter === "starting" ? "border-blue-300" : "border-blue-200"}`}
          style={{ background: "#DAE8F9", color: "#4762F1" }}
        >
          <span className="flex items-center gap-1">
            <CustomIcon
              name="blueClock"
              className="w-3 h-3"
              style={{ color: "#4762F1" }}
            />
            <span>Starting...</span>
          </span>
          <span className="ml-1 font-normal">({statusCounts.starting})</span>
        </button>
        <button
          onClick={() =>
            setStatusFilter(statusFilter === "terminated" ? null : "terminated")
          }
          className={`flex items-center gap-1 px-4 py-1 rounded-full border text-xs transition-colors duration-150 cursor-pointer shadow-sm
            ${statusFilter === "terminated" ? "border-gray-300" : "border-gray-200"}`}
          style={{ background: "#E4E4E7", color: "#848484" }}
        >
          <span className="flex items-center gap-1">
            <CustomIcon
              name="finishFlag"
              className="w-4 h-4"
              style={{ color: "#848484" }}
            />
            <span>Terminated</span>
          </span>
          <span className="ml-1 font-normal">({statusCounts.terminated})</span>
        </button>
        <button
          onClick={() =>
            setStatusFilter(statusFilter === "canceled" ? null : "canceled")
          }
          className={`flex items-center gap-1 px-4 py-1 rounded-full border text-xs transition-colors duration-150 cursor-pointer shadow-sm
            ${statusFilter === "canceled" ? "border-red-300" : "border-red-200"}`}
          style={{ background: "#F2DDDD", color: "#C95454" }}
        >
          <span className="flex items-center gap-1">
            <CustomIcon
              name="unavailable"
              className="w-4 h-4"
              style={{ color: "#C95454" }}
            />
            <span>Canceled</span>
          </span>
          <span className="ml-1 font-normal">({statusCounts.canceled})</span>
        </button>
      </div>
      <CustomTabs
        value={currentTab}
        onValueChange={(value) => setCurrentTab(value as DelegationTab)}
      >
        {tabList.map((t) => (
          <CustomTabsContent key={t.value} value={t.value}>
            <DataTable
              columns={columns}
              data={filteredData}
              showSearchBar={false}
              showStatusFilter={false}
            />
          </CustomTabsContent>
        ))}
      </CustomTabs>

      {/* Create Delegation Modal */}
      <CreateDelegationModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateDelegation}
      />

      {/* Revoke Delegation Modal */}
      <RevokeDelegationModal
        open={showRevokeModal}
        onClose={handleCloseRevokeModal}
        onConfirm={handleConfirmRevoke}
      />
    </div>
  );
}
