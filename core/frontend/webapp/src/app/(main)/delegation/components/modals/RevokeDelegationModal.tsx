import React from "react";
import { ReusableButton } from "@/components/common/CustomButtons";

interface RevokeDelegationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const RevokeDelegationModal: React.FC<RevokeDelegationModalProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md mx-auto p-0 relative">
        {/* Header */}
        <div className="flex items-center justify-between px-4 pt-4 pb-2">
          <h2 className="text-lg font-bold">Revoke Delegation</h2>
          <button
            className="text-2xl text-gray-400 hover:text-gray-700"
            onClick={onClose}
          >
            ×
          </button>
        </div>
        <div className="border-b border-gray-200 mx-4 mb-6" />
        {/* Icon */}
        <div className="flex justify-center mt-2 mb-4">
          <div className="bg-red-100 rounded-full p-3 flex items-center justify-center">
            {/* Warning icon */}
            <svg width="40" height="40" viewBox="0 0 48 48" fill="none">
              <circle cx="24" cy="24" r="24" fill="#F87171" />
              <path
                d="M24 14V28"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <circle cx="24" cy="34" r="2.5" fill="white" />
            </svg>
          </div>
        </div>
        {/* Message */}
        <div className="px-4 text-center mb-4">
          <h3 className="font-semibold mb-2">Revoke Delegation</h3>
          <p className="text-gray-700 text-sm mb-6">
            Are you sure you want to revoke this delegation?
          </p>
        </div>
        {/* Footer */}
        <div className="flex flex-col sm:flex-row justify-between items-center px-4 py-4 gap-3 sm:gap-0">
          <ReusableButton
            label="Cancel"
            onClick={onClose}
            className="px-2 py-1 text-sm w-full sm:w-auto border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 font-medium"
            variant="outline"
          />
          <ReusableButton
            label="Confirm"
            onClick={onConfirm}
            className="px-2 py-1 text-sm w-full sm:w-auto bg-black text-white hover:bg-gray-900 font-medium flex items-center justify-center gap-2"
            variant="default"
            // If you want to keep the icon, you can pass icon as a prop
            icon={() => (
              <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
                <circle cx="10" cy="10" r="10" fill="white" />
                <path
                  d="M6 10l3 3 5-5"
                  stroke="black"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default RevokeDelegationModal;
