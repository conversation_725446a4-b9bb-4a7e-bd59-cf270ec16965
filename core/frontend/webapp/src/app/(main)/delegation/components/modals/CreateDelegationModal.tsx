"use client";

import React, { useState } from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { ReusableButton } from "@/components/common/CustomButtons";
import CustomSelect from "@/components/common/CustomSelect";
import CustomIcon from "@/components/common/CustomIcons";
import { DatePicker } from "@/components/ui/datePicker";
import DelegationSummaryModal from "./DelegationSummaryModal";

interface CreateDelegationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (delegationData: CreateDelegationData) => void;
}

export interface CreateDelegationData {
  type: "forme" | "team";
  delegatedTo: string;
  teamMember?: string;
  startDate: string;
  endDate: string;
}

const mockUsers = [
  { label: "1542 - Samir SABIRI", value: "1542" },
  { label: "1543 - Ahmed HASSAN", value: "1543" },
  { label: "1544 - Sarah MARTINEZ", value: "1544" },
  { label: "1545 - John SMITH", value: "1545" },
];

export default function CreateDelegationModal({
  isOpen,
  onClose,
  onSubmit,
}: CreateDelegationModalProps) {
  const [formData, setFormData] = useState<CreateDelegationData>({
    type: "forme",
    delegatedTo: "",
    teamMember: "",
    startDate: "",
    endDate: "",
  });

  const [showSummary, setShowSummary] = useState(false);

  const handleSubmit = () => {
    setShowSummary(true);
  };

  const handleConfirmSubmit = () => {
    onSubmit(formData);
    setShowSummary(false);
    onClose();
    // Reset form
    setFormData({
      type: "forme",
      delegatedTo: "",
      teamMember: "",
      startDate: "",
      endDate: "",
    });
  };

  const handleBackToForm = () => {
    setShowSummary(false);
  };

  const handleCancel = () => {
    setShowSummary(false);
    onClose();
    // Reset form
    setFormData({
      type: "forme",
      delegatedTo: "",
      teamMember: "",
      startDate: "",
      endDate: "",
    });
  };

  const footer = (
    <div className="flex justify-between gap-3">
      <ReusableButton
        label="Cancel"
        variant="outline"
        onClick={handleCancel}
        className="px-6 py-2"
      />
      <ReusableButton
        label="Submit"
        variant="default"
        onClick={handleSubmit}
        disabled={!formData.delegatedTo}
        className="px-6 py-2 bg-black text-white hover:bg-gray-900 disabled:bg-gray-300"
      />
    </div>
  );

  return (
    <>
      <ReusableDialog
        isOpen={isOpen && !showSummary}
        onClose={onClose}
        title="Add New Delegation"
        size="lg"
        footer={footer}
        className="max-w-lg"
      >
        <div className="space-y-6 py-4">
          {/* Delegation Icon and subtitle */}
          <div className="flex flex-col items-center mb-2">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <CustomIcon name="checkMark" className="w-10 h-10" />
            </div>
            <div className="text-sm font-semibold text-blue-600 mb-2 flex items-center gap-2">
              Add New Delegation
            </div>
          </div>
          {/* Radio buttons centered */}
          <div className="flex items-center gap-8 mb-4 justify-center">
            <label className="flex items-center cursor-pointer gap-2">
              <span
                className={`inline-block w-5 h-5 rounded-full border-2 flex items-center justify-center ${formData.type === "forme" ? "border-black" : "border-gray-300"}`}
              >
                {formData.type === "forme" && (
                  <span className="w-3 h-3 bg-black rounded-full" />
                )}
              </span>
              <input
                type="radio"
                id="forme"
                name="delegationType"
                value="forme"
                checked={formData.type === "forme"}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    type: e.target.value as "forme" | "team",
                  })
                }
                className="hidden"
              />
              <span className="text-sm font-semibold text-black">For me</span>
            </label>
            <label className="flex items-center cursor-pointer gap-2">
              <span
                className={`inline-block w-5 h-5 rounded-full border-2 flex items-center justify-center ${formData.type === "team" ? "border-black" : "border-gray-300"}`}
              >
                {formData.type === "team" && (
                  <span className="w-3 h-3 bg-black rounded-full" />
                )}
              </span>
              <input
                type="radio"
                id="team"
                name="delegationType"
                value="team"
                checked={formData.type === "team"}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    type: e.target.value as "forme" | "team",
                  })
                }
                className="hidden"
              />
              <span className="text-sm font-semibold text-gray-400">
                For a member of my team
              </span>
            </label>
          </div>
          {/* Delegate to dropdown */}
          {formData.type === "team" && (
            <div className="space-y-2 mb-2">
              <label className="text-sm font-medium text-gray-700">
                Select a member of my team{" "}
                <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <CustomSelect
                  options={mockUsers}
                  value={formData.teamMember || ""}
                  onValueChange={(value: string) =>
                    setFormData({ ...formData, teamMember: value })
                  }
                  placeholder="Select a member..."
                  className="w-full text-sm font-semibold"
                />
              </div>
            </div>
          )}
          <div className="space-y-2 mb-2">
            <label className="text-sm font-medium text-gray-700">
              Delegate to : <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <CustomSelect
                options={mockUsers}
                value={formData.delegatedTo}
                onValueChange={(value: string) =>
                  setFormData({ ...formData, delegatedTo: value })
                }
                placeholder="Select a person..."
                className="w-full text-sm font-semibold"
              />
            </div>
          </div>
          {/* Date inputs */}
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <DatePicker
                label={"Delegation start date : "}
                name="startDate"
                defaultDate={
                  formData.startDate ? new Date(formData.startDate) : undefined
                }
                onChange={(date) =>
                  setFormData({
                    ...formData,
                    startDate: date ? date.toLocaleDateString("en-GB") : "",
                  })
                }
                placeholder="DD/MM/YYYY"
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <DatePicker
                label={"Delegation end date : "}
                name="endDate"
                defaultDate={
                  formData.endDate ? new Date(formData.endDate) : undefined
                }
                onChange={(date) =>
                  setFormData({
                    ...formData,
                    endDate: date ? date.toLocaleDateString("en-GB") : "",
                  })
                }
                placeholder="DD/MM/YYYY"
                className="w-full"
              />
            </div>
          </div>
        </div>
      </ReusableDialog>

      <DelegationSummaryModal
        isOpen={showSummary}
        onClose={() => {
          setShowSummary(false);
          onClose();
        }}
        onConfirm={handleConfirmSubmit}
        onBack={handleBackToForm}
        delegationData={formData}
      />
    </>
  );
}
