# Delegation Component Testing Guide

## Test Commands

### Run All Tests

```bash
pnpm run test
```

### Run Component Tests

```bash
# Comprehensive component tests
pnpm run test -- --testPathPattern='delegation.test'
```

### Run Store Tests

```bash
# Comprehensive store tests
pnpm run test -- --testPathPattern='delegationStore.test'
```

### Run Both Test Files

```bash
# Run component and store tests together (clean output with suppressed warnings)
pnpm run test -- --testPathPattern='(delegation|delegationStore).test'

# Run with single worker for more stable output
pnpm run test -- --testPathPattern='(delegation|delegationStore).test' --maxWorkers=1

# Run silently (minimal output)
pnpm run test -- --testPathPattern='(delegation|delegationStore).test' --silent
```

### Run Specific Test Suites

```bash
# Run only component rendering tests
pnpm run test -- --testPathPattern='delegation.test' --testNamePattern='Component Rendering'

# Run only store integration tests
pnpm run test -- --testPathPattern='delegationStore.test' --testNamePattern='Store Actions'

# Run only modal tests
pnpm run test -- --testPathPattern='delegation.test' --testNamePattern='Modal'

# Run only filtering tests
pnpm run test -- --testPathPattern='delegation.test' --testNamePattern='Filtering'

# Run only tab navigation tests
pnpm run test -- --testPathPattern='delegation.test' --testNamePattern='Tab Navigation'

# Run only status filtering tests
pnpm run test -- --testPathPattern='delegation.test' --testNamePattern='Status Filter'
```

### Run with Coverage

```bash
# Run tests with coverage report
pnpm run test -- --testPathPattern='(delegation|delegationStore).test' --coverage

# Run tests with coverage for specific components
pnpm run test -- --testPathPattern='delegation.test' --coverage --collectCoverageFrom='**/delegation/**/*.{ts,tsx}'
```

### Run in Watch Mode

```bash
# Run tests in watch mode for development
pnpm run test -- --testPathPattern='(delegation|delegationStore).test' --watch

# Run specific test file in watch mode
pnpm run test -- --testPathPattern='delegation.test' --watch
```

## Test Structure

### Component Tests (`components/delegation.test.tsx`)

- **Component Rendering**: Basic rendering and UI elements
- **Tab Navigation**: Tab switching functionality
- **Status Filtering**: Status badge filtering
- **Data Table**: Table display and interactions
- **Modal Operations**: Create and revoke delegation modals
- **User Interactions**: Button clicks and dropdown actions
- **Store Integration**: Component-store interaction

### Store Tests (`store/delegationStore.test.tsx`)

- **Store Actions**: State management actions
- **Data Filtering**: Filter and sort functionality
- **CRUD Operations**: Create, update, delete delegations
- **State Validation**: Data integrity checks
- **Business Logic**: Tab counts and status counts
- **Error Handling**: Edge cases and error states

## Mock Strategy

The tests use comprehensive mocking to:

- Mock external dependencies (next/image, lucide-react icons)
- Mock UI components for isolation
- Mock store for component tests
- Mock API calls and external services
- Provide predictable test data

## Test Data

Tests use realistic mock data that mirrors production scenarios:

- Multiple delegation statuses
- Various delegators and delegated users
- Different date ranges
- Mixed ownership patterns (assigned to me vs. my delegations)

## Assertions

Tests verify:

- Component rendering and UI presence
- User interaction handling
- State changes and updates
- Data filtering and sorting
- Modal behavior and form submissions
- Accessibility features
- Error handling

## Running Tests

All tests are designed to run independently and can be executed in any order. The test suite includes:

- Unit tests for individual functions
- Integration tests for component-store interaction
- UI tests for user interactions
- State management tests

For optimal performance during development, use watch mode and focus on specific test patterns when working on particular features.
