"use client";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import {
  DelegationStore,
  DelegationRow,
  DelegationTab,
  StatusFilter,
  SortDirection,
} from "../types/delegation";

// Mock data
const initialDelegations: DelegationRow[] = [
  {
    id: "1",
    delegator: "Me",
    delegatedTo: "<PERSON><PERSON><PERSON>",
    status: "Approved & Not Started Yet",
    fromDate: "10/07/2025",
    toDate: "12/07/2025",
    createdDate: "01/07/2025",
    action: "revoke",
  },
  {
    id: "2",
    delegator: "<PERSON><PERSON><PERSON><PERSON>",
    delegatedTo: "Me",
    status: "Starting...",
    fromDate: "10/07/2025",
    toDate: "12/07/2025",
    createdDate: "01/07/2025",
    action: "revoke",
  },
  {
    id: "3",
    delegator: "Me",
    delegatedTo: "<PERSON>ine <PERSON>d",
    status: "Terminated",
    fromDate: "10/07/2025",
    toDate: "12/07/2025",
    createdDate: "01/07/2025",
    action: "revoke",
  },
  {
    id: "4",
    delegator: "<PERSON><PERSON>ra<PERSON> sadik",
    delegatedTo: "Me",
    status: "Terminated",
    fromDate: "10/07/2025",
    toDate: "12/07/2025",
    createdDate: "01/07/2025",
    action: "revoke",
  },
  {
    id: "5",
    delegator: "Me",
    delegatedTo: "Anass SANHAJI",
    status: "Canceled",
    fromDate: "06/06/2025",
    toDate: "07/06/2025",
    createdDate: "04/06/2025",
    action: "revoke",
  },
];

export const useDelegationStore = create<DelegationStore>()(
  devtools(
    (set, get) => ({
      // State
      delegations: initialDelegations,
      currentTab: "all",
      statusFilter: null,
      sortDirection: "desc",
      isLoading: false,
      error: null,

      // Actions
      setCurrentTab: (tab: DelegationTab) =>
        set({ currentTab: tab }, false, "setCurrentTab"),

      setStatusFilter: (filter: StatusFilter) =>
        set({ statusFilter: filter }, false, "setStatusFilter"),

      setSortDirection: (direction: SortDirection) =>
        set({ sortDirection: direction }, false, "setSortDirection"),

      toggleSortDirection: () => {
        const currentDirection = get().sortDirection;
        set(
          {
            sortDirection: currentDirection === "desc" ? "asc" : "desc",
          },
          false,
          "toggleSortDirection",
        );
      },

      getFilteredDelegations: () => {
        const { delegations, statusFilter } = get();

        if (!statusFilter) return delegations;

        return delegations.filter((row) => {
          if (statusFilter === "approved")
            return row.status === "Approved & Not Started Yet";
          if (statusFilter === "starting") return row.status === "Starting...";
          if (statusFilter === "terminated") return row.status === "Terminated";
          if (statusFilter === "canceled") return row.status === "Canceled";
          return true;
        });
      },

      getStatusCounts: () => {
        const { delegations } = get();
        return {
          approved: delegations.filter(
            (row) => row.status === "Approved & Not Started Yet",
          ).length,
          starting: delegations.filter((row) => row.status === "Starting...")
            .length,
          terminated: delegations.filter((row) => row.status === "Terminated")
            .length,
          canceled: delegations.filter((row) => row.status === "Canceled")
            .length,
        };
      },

      getTabCounts: () => {
        const { delegations } = get();
        const assignedToMe = delegations.filter(
          (row) => row.delegatedTo === "Me",
        ).length;
        const myAssignations = delegations.filter(
          (row) => row.delegator === "Me",
        ).length;
        return {
          all: delegations.length,
          assigned: assignedToMe,
          my: myAssignations,
        };
      },

      createDelegation: (delegation: Omit<DelegationRow, "id">) => {
        const newDelegation: DelegationRow = {
          ...delegation,
          id: Date.now().toString(), // Simple ID generation
        };
        set(
          (state) => ({
            delegations: [...state.delegations, newDelegation],
          }),
          false,
          "createDelegation",
        );
      },

      updateDelegation: (id: string, updates: Partial<DelegationRow>) => {
        set(
          (state) => ({
            delegations: state.delegations.map((delegation) =>
              delegation.id === id ? { ...delegation, ...updates } : delegation,
            ),
          }),
          false,
          "updateDelegation",
        );
      },

      deleteDelegation: (id: string) => {
        set(
          (state) => ({
            delegations: state.delegations.filter(
              (delegation) => delegation.id !== id,
            ),
          }),
          false,
          "deleteDelegation",
        );
      },
    }),
    {
      name: "delegation-store",
    },
  ),
);
