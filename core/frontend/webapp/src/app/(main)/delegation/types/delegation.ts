export type DelegationStatus =
  | "Approved & Not Started Yet"
  | "Starting..."
  | "Terminated"
  | "Canceled";

export type DelegationRow = {
  id: string;
  delegator: string;
  delegatedTo: string;
  status: DelegationStatus;
  fromDate: string;
  toDate: string;
  createdDate: string;
  action: string;
};

export type DelegationTab = "all" | "assigned" | "my";

export type StatusFilter =
  | "approved"
  | "starting"
  | "terminated"
  | "canceled"
  | null;

export type SortDirection = "asc" | "desc";

export interface DelegationState {
  delegations: DelegationRow[];
  currentTab: DelegationTab;
  statusFilter: StatusFilter;
  sortDirection: SortDirection;
  isLoading: boolean;
  error: string | null;
}

export interface DelegationActions {
  setCurrentTab: (tab: DelegationTab) => void;
  setStatusFilter: (filter: StatusFilter) => void;
  setSortDirection: (direction: SortDirection) => void;
  toggleSortDirection: () => void;
  getFilteredDelegations: () => DelegationRow[];
  getStatusCounts: () => {
    approved: number;
    starting: number;
    terminated: number;
    canceled: number;
  };
  getTabCounts: () => {
    all: number;
    assigned: number;
    my: number;
  };
  createDelegation: (delegation: Omit<DelegationRow, "id">) => void;
  updateDelegation: (id: string, updates: Partial<DelegationRow>) => void;
  deleteDelegation: (id: string) => void;
}

export type DelegationStore = DelegationState & DelegationActions;
