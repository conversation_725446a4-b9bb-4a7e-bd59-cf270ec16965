import React, { useCallback } from "react";
import { FormFieldType } from "./FormBuilder";
import { Move } from "lucide-react";
import { Reorder, AnimatePresence } from "framer-motion";
import { FieldItem } from "./FieldItem";
import { useDynamicFormStore } from "../../store/dynamicFormStore";

export type FormFieldOrGroup = FormFieldType | FormFieldType[];

type FormFieldListProps = {
  updateFormField: (path: number[], updates: Partial<FormFieldType>) => void;
  openEditDialog: (field: FormFieldType) => void;
};

export const FormFieldList: React.FC<FormFieldListProps> = ({
  updateFormField,
  openEditDialog,
}) => {
  const { formFields, setFormFields } = useDynamicFormStore();

  const handleVerticalReorder = useCallback(
    (newOrder: FormFieldOrGroup[]) => {
      // Immediately update the form fields to prevent duplicate renders
      setFormFields(newOrder);
    },
    [setFormFields],
  );

  const handleHorizontalReorder = useCallback(
    (index: number, newOrder: FormFieldType[]) => {
      setFormFields((prevFields) => {
        const updatedFields = [...prevFields];
        updatedFields[index] = newOrder;
        return updatedFields;
      });
    },
    [setFormFields],
  );

  return (
    <div className="mt-3 lg:mt-0">
      <Reorder.Group
        axis="y"
        onReorder={handleVerticalReorder}
        values={formFields}
        className="flex flex-col gap-1"
      >
        {formFields.map((item, index) => {
          // Generate stable unique keys for better drag performance
          const itemKey = Array.isArray(item)
            ? `group-${index}-${item.map((f) => f.name).join("-")}`
            : `field-${index}-${item.name}`;

          return (
            <Reorder.Item
              key={itemKey}
              value={item}
              className="flex items-center gap-1"
              whileDrag={{ backgroundColor: "#e5e7eb", borderRadius: "12px" }}
            >
              <Move className="cursor-grab w-4 h-4" />
              {Array.isArray(item) ? (
                <div className="w-full">
                  <Reorder.Group
                    as="div"
                    axis="x"
                    onReorder={(newOrder: FormFieldType[]) =>
                      handleHorizontalReorder(index, newOrder)
                    }
                    values={item}
                    className="w-full grid grid-cols-12 gap-1"
                  >
                    <AnimatePresence initial={false}>
                      {item.map((field: FormFieldType, fieldIndex: number) => (
                        <FieldItem
                          key={`${field.name}-${fieldIndex}`}
                          index={index}
                          subIndex={fieldIndex}
                          field={field}
                          updateFormField={updateFormField}
                          openEditDialog={openEditDialog}
                        />
                      ))}
                    </AnimatePresence>
                  </Reorder.Group>
                </div>
              ) : (
                <FieldItem
                  field={item}
                  index={index}
                  updateFormField={updateFormField}
                  openEditDialog={openEditDialog}
                />
              )}
            </Reorder.Item>
          );
        })}
      </Reorder.Group>
    </div>
  );
};
