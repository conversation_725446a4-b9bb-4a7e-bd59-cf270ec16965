# Forms Management Test Suite

This directory contains comprehensive CRUD tests for the forms-management module, following the same pattern as the documents-templates tests.

## Test Structure

```
test/
├── jest.setup.ts              # Jest configuration and mocks
├── tsconfig.jest.json         # TypeScript configuration for tests
├── simple.test.ts             # Basic test setup verification
├── store/
│   └── dynamicFormStore.test.ts  # Store CRUD operations tests
├── pages/
│   ├── page.test.tsx          # Main page CRUD tests
│   └── new-form.test.tsx      # New form page tests
└── README.md                  # This file
```

## Test Coverage

### Store Tests (`store/dynamicFormStore.test.ts`)

- **State Management**: Initialization, field updates, layout updates
- **CRUD Operations**:
  - Create: `submitFormPreview`
  - Read: `fetchForms`, `fetchFormFields`, `fetchFormForEdit`
  - Update: `updateForm`, `updateFormField`
  - Delete: `deleteForm`
- **Error Handling**: API errors, validation errors, network failures
- **Data Transformation**: API response mapping, default values

### Page Tests (`pages/page.test.tsx`)

- **Component Rendering**: Data table, loading states, error states
- **User Interactions**: Add, edit, delete, preview actions
- **Navigation**: Router interactions, error handling
- **Data Display**: Form listing, date formatting, metadata handling

### New Form Tests (`pages/new-form.test.tsx`)

- **Form Creation**: Field addition, validation, submission
- **Form Editing**: Loading existing forms, updating fields
- **Store Integration**: State management, API interactions
- **Complex Scenarios**: Multi-field forms, layout management

## Running Tests

### Prerequisites

- Node.js and npm/yarn installed
- Jest and Testing Library configured in the project

### Run All Tests

```bash
npm test -- --testPathPattern=forms-management
```

### Run Specific Test Files

```bash
# Store tests only
npm test -- --testPathPattern=dynamicFormStore.test.ts

# Page tests only
npm test -- --testPathPattern=page.test.tsx

# New form tests only
npm test -- --testPathPattern=new-form.test.tsx
```

### Run with Coverage

```bash
npm test -- --testPathPattern=forms-management --coverage
```

## Test Patterns

### Mocking Strategy

- **API Calls**: Mocked using `jest.mock("@/lib/axios")`
- **Toast Notifications**: Mocked using `jest.mock("@/hooks/use-toast")`
- **Router**: Mocked using `jest.mock("next/navigation")`
- **Components**: Mocked to focus on business logic

### Test Data

- **Form Fields**: Complete `FormFieldType` objects with all required properties
- **Forms**: Complete `Form` objects with metadata, dates, and layout
- **API Responses**: Realistic response structures matching the actual API

### Assertions

- **State Changes**: Verify store state updates correctly
- **API Calls**: Check correct endpoints and parameters
- **User Feedback**: Verify toast notifications and error handling
- **Navigation**: Confirm router calls with correct paths

## Key Features Tested

### CRUD Operations

1. **Create**: Form creation with fields and layout
2. **Read**: Fetching forms list and individual forms
3. **Update**: Modifying existing forms and fields
4. **Delete**: Removing forms with confirmation

### Error Scenarios

- Network failures
- API validation errors
- Missing data handling
- Navigation errors

### Edge Cases

- Empty forms list
- Forms with missing optional properties
- Complex field configurations
- Large datasets

## Maintenance

### Adding New Tests

1. Follow the existing naming conventions
2. Use the established mocking patterns
3. Include both success and error scenarios
4. Test edge cases and boundary conditions

### Updating Tests

1. Update mock data when API changes
2. Adjust assertions when component behavior changes
3. Maintain test isolation and independence

### Best Practices

- Keep tests focused and single-purpose
- Use descriptive test names
- Mock external dependencies consistently
- Test both happy path and error scenarios
- Maintain test data that reflects real usage
