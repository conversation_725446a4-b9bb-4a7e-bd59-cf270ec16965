{"extends": "../../../../../../../tsconfig.json", "compilerOptions": {"jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "noImplicitAny": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["jest", "node", "@testing-library/jest-dom"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}