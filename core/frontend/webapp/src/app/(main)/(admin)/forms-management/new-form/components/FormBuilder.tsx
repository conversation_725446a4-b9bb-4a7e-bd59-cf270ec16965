"use client";
import * as Locales from "date-fns/locale";
import React, { useState } from "react";
import { FieldSelector } from "./FieldSelector";
import { Separator } from "@/components/ui/separator";
import If from "@/components/ui/if";
import { FormFieldList } from "./FormFieldList";
import { FormPreview } from "./FormPreview";
import { EditFieldDialog } from "./EditFieldDialog";
import { useDynamicFormStore } from "../../store/dynamicFormStore";

const defaultFieldConfig: {
  [key: string]: { label: string; description: string; placeholder: string };
} = {
  text: {
    label: "Text",
    description: "Enter text",
    placeholder: "Type here...",
  },
  number: {
    label: "Number",
    description: "Enter number",
    placeholder: "Type number...",
  },
};

export type FormFieldType = {
  type: string;
  variant: string;
  name: string;
  label: string;
  placeholder?: string;
  description?: string;
  disabled: boolean;
  value: string | boolean | Date | number | string[];
  setValue: (value: string | boolean) => void;
  checked: boolean;
  onChange: (
    value: string | string[] | boolean | Date | number | number[],
  ) => void;
  onSelect: (
    value: string | string[] | boolean | Date | number | number[],
  ) => void;
  rowIndex: number;
  required?: boolean;
  min?: number;
  max?: number;
  step?: number;
  locale?: keyof typeof Locales;
  hour12?: boolean;
  className?: string;
  options?: { label: string; value: string }[];
};

export type FieldType = { name: string; isNew: boolean; index?: number };

export type FormFieldOrGroup = FormFieldType | FormFieldType[];

export default function FormBuilder() {
  // const [formFields, setFormFields] = useState<FormFieldOrGroup[]>([]);
  const { formFields, setFormFields } = useDynamicFormStore();
  const [selectedField, setSelectedField] = useState<FormFieldType | null>(
    null,
  );
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const addFormField = (variant: string, index: number) => {
    const newFieldName = `name_${Math.random().toString().slice(-10)}`;
    const { label, description, placeholder } = defaultFieldConfig[variant] || {
      label: "",
      description: "",
      placeholder: "",
    };

    const newField: FormFieldType = {
      checked: true,
      description: description || "",
      disabled: false,
      label: label || newFieldName,
      name: newFieldName,
      onChange: () => {},
      onSelect: () => {},
      placeholder: placeholder || "Placeholder",
      required: true,
      rowIndex: index,
      setValue: () => {},
      // type: variant as
      //   | "text"
      //   | "number"
      //   | "email"
      //   | "password"
      //   | "checkbox"
      //   | "radio"
      //   | "select"
      //   | "textarea"
      //   | "date"
      //   | "time"
      //   | "file"
      //   | "phone"
      //   | "url"
      //   | "rich_text"
      //   | "signature",
      value: "",
      type: "text", // Default type, can be adjusted based on variant
      variant,
    };
    setFormFields([...formFields, newField]);
  };

  const findFieldPath = (
    fields: FormFieldOrGroup[],
    name: string,
  ): number[] | null => {
    const search = (
      currentFields: FormFieldOrGroup[],
      currentPath: number[],
    ): number[] | null => {
      for (let i = 0; i < currentFields.length; i++) {
        const field = currentFields[i];
        if (Array.isArray(field)) {
          const result = search(field, [...currentPath, i]);
          if (result) return result;
        } else if (field.name === name) {
          return [...currentPath, i];
        }
      }
      return null;
    };
    return search(fields, []);
  };

  const updateFormField = (path: number[], updates: Partial<FormFieldType>) => {
    const updatedFields = JSON.parse(JSON.stringify(formFields));
    let current: FormFieldOrGroup[] | FormFieldOrGroup = updatedFields;
    for (let i = 0; i < path.length - 1; i++) {
      if (Array.isArray(current)) {
        current = current[path[i]] as FormFieldOrGroup[];
      }
    }
    (current as FormFieldOrGroup[])[path[path.length - 1]] = {
      ...(current as FormFieldOrGroup[])[path[path.length - 1]],
      ...updates,
    };
    setFormFields(updatedFields);
  };

  const openEditDialog = (field: FormFieldType) => {
    setSelectedField(field);
    setIsDialogOpen(true);
  };

  const handleSaveField = (updatedField: FormFieldType) => {
    if (selectedField) {
      const path = findFieldPath(formFields, selectedField.name);
      if (path) {
        updateFormField(path, updatedField);
      }
    }
    setIsDialogOpen(false);
  };

  const FieldSelectorWithSeparator = ({
    addFormField,
  }: {
    addFormField: (variant: string, index?: number) => void;
  }) => (
    <div className="flex flex-col col-span-2 justify-center mb-4 items-center">
      <FieldSelector addFormField={addFormField} />
      <p className="text-gray-500">Add element</p>
    </div>
  );

  const DashedSeparator = () => (
    <Separator className="border-t border-dashed border-gray-300 my-4" />
  );

  return (
    <section className="md:max-h-screen space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 items-start gap-8 md:px-5 h-full">
        <div className="w-full h-full col-span-1 md:space-x-3 md:max-h-[75vh] flex flex-col md:flex-row ">
          <div className="flex flex-col flex-1">
            <h2 className="text-xl font-semibold mb-3 flex items-center justify-start">
              {"Form builder"}
            </h2>
            <DashedSeparator />
            <div className="overflow-y-auto flex-1 space-y-3">
              <If
                condition={formFields.length > 0}
                render={() => (
                  <>
                    <FormFieldList
                      updateFormField={updateFormField}
                      openEditDialog={openEditDialog}
                    />
                    <FieldSelectorWithSeparator
                      addFormField={(variant: string, index: number = 0) =>
                        addFormField(variant, index)
                      }
                    />
                  </>
                )}
                otherwise={() => (
                  <div className="flex flex-col justify-center md:flex-row items-center gap-3 md:px-5">
                    <FieldSelectorWithSeparator
                      addFormField={(variant: string, index: number = 0) =>
                        addFormField(variant, index)
                      }
                    />
                  </div>
                )}
              />
            </div>
          </div>
          <Separator orientation="vertical" className="border-gray-300" />
        </div>
        <div className="col-span-1 w-full h-full space-y-3 flex flex-col">
          <h2 className="text-xl font-semibold mb-4 flex items-center justify-start ">
            {"Preview"}
          </h2>
          <DashedSeparator />
          <FormPreview formFields={formFields} />
        </div>
      </div>
      <EditFieldDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        field={selectedField}
        onSave={handleSaveField}
      />
    </section>
  );
}
