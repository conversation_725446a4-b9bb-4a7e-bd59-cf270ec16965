"use client";
import React from "react";
import { FormHeader } from "./components/FormHeader";
import FormBuilder from "./components/FormBuilder";
import { Separator } from "@/components/ui/separator";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { FormFieldOrGroup } from "./components/FieldItem";
import { useDynamicFormStore } from "../store/dynamicFormStore";
import CustomPageCard from "@/components/common/CustomPageCard";

const schema = z.object({
  form: z.string(),
  description: z.string(),
  category: z.string(),
});
const NewForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams?.get("id");

  const {
    submitFormPreview,
    formFields,
    setFormFields,
    formForEdit,
    fetchFormForEdit,
    updateForm,
    resetFormState,
  } = useDynamicFormStore();

  const methods = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
  });

  // Populate form for edit
  React.useEffect(() => {
    if (editId) {
      fetchFormForEdit(editId);
    } else {
      // Reset form state when creating new form
      resetFormState();
      methods.reset({
        form: "",
        description: "",
        category: "",
      });
    }
  }, [editId, fetchFormForEdit, resetFormState, methods]);

  React.useEffect(() => {
    if (editId && formForEdit) {
      // Set form fields and form values for editing
      setFormFields(formForEdit.fields ?? []);

      // Get category from both possible locations (direct property or metadata)
      const categoryFromForm = formForEdit.category;
      const categoryFromMetadata = formForEdit.metadata?.category;
      const categoryValue = categoryFromForm || categoryFromMetadata || "";

      methods.reset({
        form: formForEdit.name || "",
        description: formForEdit.description || "",
        category: typeof categoryValue === "string" ? categoryValue : "",
      });
    }
  }, [editId, formForEdit, setFormFields, methods]);

  const transformFields = (fields: FormFieldOrGroup[]): unknown[] => {
    return fields.flatMap((fieldOrGroup) => {
      if (Array.isArray(fieldOrGroup)) {
        return fieldOrGroup.map((field) => ({
          label: field.label,
          name: field.name,
          type: field.type,
          placeholder: field.placeholder,
          required: field.required,
          disabled: field.disabled,
          checked: field.checked,
          description: field.description,
          value: field.value,
          variant: field.variant,
          className: field.className,
          validationRules: [], // Add validation rules if needed
          config: {}, // Add config if needed
        }));
      } else {
        return {
          label: fieldOrGroup.label,
          name: fieldOrGroup.name,
          type: fieldOrGroup.type,
          placeholder: fieldOrGroup.placeholder,
          required: fieldOrGroup.required,
          disabled: fieldOrGroup.disabled,
          checked: fieldOrGroup.checked,
          description: fieldOrGroup.description,
          value: fieldOrGroup.value,
          variant: fieldOrGroup.variant,
          className: fieldOrGroup.className,
          validationRules: [], // Add validation rules if needed
          config: {}, // Add config if needed
        };
      }
    });
  };

  const onSubmit = async (data: z.infer<typeof schema>) => {
    // Use layout from formForEdit if editing, otherwise use default
    const layout = {
      rows: formFields.map((fieldOrGroup) => {
        if (Array.isArray(fieldOrGroup)) {
          return {
            cols: fieldOrGroup.length,
            ids: fieldOrGroup.map((f) => f.name),
            spacing: 16,
          };
        } else {
          return {
            cols: 1,
            ids: [fieldOrGroup.name],
            spacing: 16,
          };
        }
      }),
    };

    const obj = {
      name: data.form,
      description: data.description,
      fields: transformFields(formFields),
      category: data.category,
      layout,
    };
    console.log("Submitting form preview with data:", obj);

    try {
      if (editId) {
        await updateForm(editId, obj);
        router.push("/forms-management");
      } else {
        await submitFormPreview(obj);
        router.push("/forms-management");
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  return (
    <CustomPageCard>
      <div className="col-span-2 space-y-5 ">
        <Form {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
            <FormHeader />
          </form>
        </Form>
        <Separator orientation="horizontal" className="border-gray-300" />
        <FormBuilder />
      </div>
    </CustomPageCard>
  );
};

export default NewForm;
