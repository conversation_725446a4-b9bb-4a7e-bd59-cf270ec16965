import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FormManagement from "../../page";
import { useDynamicFormStore } from "../../store/dynamicFormStore";
import { useRouter } from "next/navigation";

// Mock dependencies
jest.mock("../../store/dynamicFormStore");
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/",
}));

// Mock all components used in FormManagement
jest.mock("@/components/common/CellActions", () => ({
  CellActions: function MockCellActions({
    actions,
  }: {
    actions: Array<{ label?: string; onClick?: () => void }>;
  }) {
    return React.createElement(
      "div",
      { "data-testid": "cell-actions" },
      actions?.map((action, index) =>
        React.createElement(
          "button",
          {
            key: index,
            onClick: action.onClick,
            "data-testid": `action-${action.label?.toLowerCase()}`,
          },
          action.label,
        ),
      ),
    );
  },
}));

jest.mock("@/components/common/CustomButtons", () => ({
  IconButton: function MockIconButton({
    onClick,
    label,
    nameButton,
  }: {
    onClick?: () => void;
    label?: string;
    nameButton?: string;
  }) {
    return React.createElement(
      "button",
      {
        onClick,
        "data-testid": nameButton || "icon-button",
      },
      label || "Icon Button",
    );
  },
}));

jest.mock("@/components/common/tables/DataTable", () => ({
  DataTable: function MockDataTable({
    data,
    buttons,
    isLoading,
  }: {
    data?: Array<{ id?: string; name?: string }>;
    buttons?: Array<{ label?: string; onClick?: () => void }>;
    isLoading?: boolean;
  }) {
    return React.createElement(
      "div",
      { "data-testid": "datatable" },
      [
        // Render buttons
        buttons?.map((button, index: number) =>
          React.createElement(
            "button",
            {
              key: index,
              onClick: button.onClick,
              "data-testid": `button-${button.label}`,
            },
            button.label,
          ),
        ),
        // Render loading state
        isLoading &&
          React.createElement(
            "div",
            { "data-testid": "loading" },
            "Loading...",
          ),
        // Render data
        !isLoading &&
          data?.map((item, index: number) =>
            React.createElement(
              "div",
              {
                key: index,
                "data-testid": `row-${item.id || index}`,
              },
              item.name || "Form",
            ),
          ),
      ].filter(Boolean),
    );
  },
}));

jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({ children, open }: { children: React.ReactNode; open: boolean }) =>
    open
      ? React.createElement("div", { "data-testid": "dialog" }, children)
      : null,
  DialogContent: ({ children }: { children: React.ReactNode }) =>
    React.createElement("div", { "data-testid": "dialog-content" }, children),
  DialogHeader: ({ children }: { children: React.ReactNode }) =>
    React.createElement("div", { "data-testid": "dialog-header" }, children),
}));

jest.mock("@radix-ui/react-dialog", () => ({
  DialogTitle: ({ children }: { children: React.ReactNode }) =>
    React.createElement("h2", { "data-testid": "dialog-title" }, children),
}));

jest.mock("@/hooks/use-toast", () => ({
  toast: jest.fn(),
}));

jest.mock("@/components/common/CustomIcons", () => {
  return function MockCustomIcon({
    name,
    ...props
  }: {
    name: string;
    [key: string]: unknown;
  }) {
    return React.createElement(
      "span",
      {
        "data-testid": `icon-${name}`,
        ...props,
      },
      name,
    );
  };
});

jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

jest.mock("lucide-react", () => ({
  Eye: () => React.createElement("span", { "data-testid": "eye-icon" }, "👁️"),
}));

jest.mock("../../new-form/components/FormPreview", () => ({
  FormPreview: function MockFormPreview({
    formFields,
    isEditing,
  }: {
    formFields: unknown[];
    isEditing: boolean;
  }) {
    return React.createElement(
      "div",
      { "data-testid": "form-preview" },
      `Form Preview - Fields: ${formFields.length}, Editing: ${isEditing}`,
    );
  },
}));

const mockUseDynamicFormStore = useDynamicFormStore as jest.MockedFunction<
  typeof useDynamicFormStore
>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe("FormManagement CRUD", () => {
  const mockStore = {
    loading: false,
    formFields: [],
    setFormFields: jest.fn(),
    layout: { rows: [] },
    setLayout: jest.fn(),
    addFormField: jest.fn(),
    updateFormField: jest.fn(),
    submitFormPreview: jest.fn(),
    data: {
      items: [],
      meta: {
        pageSize: 5,
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    },
    forms: [],
    formsLoading: false,
    formsError: null,
    fetchForms: jest.fn(),
    fetchPaginatedData: jest.fn(),
    searchForms: jest.fn(),
    deleteForm: jest.fn(),
    fetchFormFields: jest.fn(),
    formForEdit: null,
    fetchFormForEdit: jest.fn(),
    updateForm: jest.fn(),
  };

  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the store to handle selectors properly
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(mockStore);
      }
      return mockStore;
    });
    mockUseRouter.mockReturnValue(mockRouter);
  });

  it("should import FormManagement", () => {
    expect(FormManagement).toBeDefined();
  });

  it("should render the page with data table", () => {
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should display forms when data is available", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        metadata: { category: "General" },
        layout: { rows: [] },
      },
      {
        id: "form-2",
        name: "Test Form 2",
        description: "Test Description 2",
        fields: [],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        metadata: { category: "HR" },
        layout: { rows: [] },
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle loading state", () => {
    const storeWithLoading = {
      ...mockStore,
      formsLoading: true,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithLoading);
      }
      return storeWithLoading;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle error state", () => {
    const storeWithError = {
      ...mockStore,
      formsError: "Failed to fetch forms",
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithError);
      }
      return storeWithError;
    });
    render(<FormManagement />);
    // When there's an error, the component should still render but with empty data
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle add new form button click", () => {
    render(<FormManagement />);
    const addButton = screen.getByTestId("button-home.Addbutton");
    fireEvent.click(addButton);
    expect(mockRouter.push).toHaveBeenCalledWith("/forms-management/new-form");
  });

  it("should handle edit action", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        metadata: { category: "General" },
        layout: { rows: [] },
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle delete action", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        metadata: { category: "General" },
        layout: { rows: [] },
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle preview action", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [
          {
            type: "text",
            variant: "Input",
            name: "field-1",
            label: "Test Field",
            placeholder: "Test placeholder",
            description: "Test description",
            disabled: false,
            value: "",
            setValue: () => {},
            checked: false,
            onChange: () => {},
            onSelect: () => {},
            rowIndex: 0,
            required: true,
          },
        ],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        metadata: { category: "General" },
        layout: { rows: [{ cols: 1, ids: ["field-1"], spacing: 16 }] },
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should format date time correctly", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T12:00:00Z",
        updatedAt: "2024-01-01T12:00:00Z",
        metadata: { category: "General" },
        layout: { rows: [] },
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle empty forms list", () => {
    const storeWithEmptyForms = {
      ...mockStore,
      forms: [],
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithEmptyForms);
      }
      return storeWithEmptyForms;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle navigation error", () => {
    mockRouter.push.mockImplementation(() => {
      throw new Error("Navigation failed");
    });

    render(<FormManagement />);
    const addButton = screen.getByTestId("button-home.Addbutton");
    fireEvent.click(addButton);
    expect(mockRouter.push).toHaveBeenCalledWith("/forms-management/new-form");
  });

  it("should call fetchPaginatedData on component mount", () => {
    render(<FormManagement />);
    expect(mockStore.fetchPaginatedData).toHaveBeenCalled();
  });

  it("should handle form with missing metadata", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [],
        version: 1,
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        layout: { rows: [] },
        // No metadata
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });

  it("should handle form with missing dates", () => {
    const mockForms = [
      {
        id: "form-1",
        name: "Test Form 1",
        description: "Test Description 1",
        fields: [],
        version: 1,
        status: "active",
        metadata: { category: "General" },
        layout: { rows: [] },
        // No createdAt/updatedAt
      },
    ];

    const storeWithData = {
      ...mockStore,
      forms: mockForms,
    };
    mockUseDynamicFormStore.mockImplementation((selector) => {
      if (typeof selector === "function") {
        return selector(storeWithData);
      }
      return storeWithData;
    });
    render(<FormManagement />);
    expect(screen.getByTestId("datatable")).toBeInTheDocument();
  });
});
