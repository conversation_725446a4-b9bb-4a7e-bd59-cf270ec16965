import { useDynamicFormStore } from "../../store/dynamicFormStore";

// Mock dependencies
jest.mock("../../store/dynamicFormStore");

const mockUseDynamicFormStore = useDynamicFormStore as jest.MockedFunction<
  typeof useDynamicFormStore
>;

describe("NewForm Store Functionality", () => {
  const mockStore = {
    formFields: [],
    setFormFields: jest.fn(),
    layout: { rows: [] },
    setLayout: jest.fn(),
    addFormField: jest.fn(),
    updateFormField: jest.fn(),
    submitFormPreview: jest.fn(),
    loading: false,
    formForEdit: null,
    fetchFormForEdit: jest.fn(),
    updateForm: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDynamicFormStore.mockReturnValue(mockStore);
  });

  it("should handle form creation mode", () => {
    expect(mockStore.formForEdit).toBeNull();
  });

  it("should handle form edit mode", () => {
    const mockFormForEdit = {
      id: "form-1",
      name: "Test Form",
      description: "Test Description",
      fields: [
        {
          type: "text",
          variant: "Input",
          name: "field-1",
          label: "Test Field",
          placeholder: "Test placeholder",
          description: "Test description",
          disabled: false,
          value: "",
          setValue: () => {},
          checked: false,
          onChange: () => {},
          onSelect: () => {},
          rowIndex: 0,
          required: true,
        },
      ],
      version: 1,
      status: "active",
      layout: { rows: [{ cols: 1, ids: ["field-1"], spacing: 16 }] },
    };

    const storeWithEditForm = {
      ...mockStore,
      formForEdit: mockFormForEdit,
    };
    mockUseDynamicFormStore.mockReturnValue(storeWithEditForm);

    expect(storeWithEditForm.formForEdit).toEqual(mockFormForEdit);
  });

  it("should handle form field addition", () => {
    const newField = {
      type: "text",
      variant: "Input",
      name: "new-field",
      label: "New Field",
      placeholder: "New placeholder",
      description: "New description",
      disabled: false,
      value: "",
      setValue: () => {},
      checked: false,
      onChange: () => {},
      onSelect: () => {},
      rowIndex: 0,
      required: false,
    };

    mockStore.addFormField(newField);
    expect(mockStore.addFormField).toHaveBeenCalledWith(newField);
  });

  it("should handle form field updates", () => {
    const updates = { label: "Updated Label" };
    const path = [0];

    mockStore.updateFormField(path, updates);
    expect(mockStore.updateFormField).toHaveBeenCalledWith(path, updates);
  });

  it("should handle form submission", async () => {
    const formData = {
      name: "Test Form",
      description: "Test Description",
      fields: [],
    };

    mockStore.submitFormPreview.mockResolvedValue({
      id: "form-1",
      ...formData,
    });

    const result = await mockStore.submitFormPreview(formData);
    expect(mockStore.submitFormPreview).toHaveBeenCalledWith(formData);
    expect(result).toEqual({ id: "form-1", ...formData });
  });

  it("should handle form submission error", async () => {
    const formData = {
      name: "Test Form",
      description: "Test Description",
      fields: [],
    };

    mockStore.submitFormPreview.mockRejectedValue(
      new Error("Submission failed"),
    );

    await expect(mockStore.submitFormPreview(formData)).rejects.toThrow(
      "Submission failed",
    );
  });

  it("should handle form update", async () => {
    const updates = {
      name: "Updated Form",
      description: "Updated Description",
    };

    mockStore.updateForm.mockResolvedValue(undefined);

    await mockStore.updateForm("form-1", updates);
    expect(mockStore.updateForm).toHaveBeenCalledWith("form-1", updates);
  });

  it("should handle form update error", async () => {
    const updates = {
      name: "Updated Form",
      description: "Updated Description",
    };

    mockStore.updateForm.mockRejectedValue(new Error("Update failed"));

    await expect(mockStore.updateForm("form-1", updates)).rejects.toThrow(
      "Update failed",
    );
  });

  it("should handle layout updates", () => {
    const newLayout = {
      rows: [{ cols: 2, ids: ["field-1", "field-2"], spacing: 16 }],
    };

    mockStore.setLayout(newLayout);
    expect(mockStore.setLayout).toHaveBeenCalledWith(newLayout);
  });

  it("should handle form fields updates", () => {
    const newFields = [
      {
        type: "text",
        variant: "Input",
        name: "field-1",
        label: "Field 1",
        placeholder: "Placeholder 1",
        description: "Description 1",
        disabled: false,
        value: "",
        setValue: () => {},
        checked: false,
        onChange: () => {},
        onSelect: () => {},
        rowIndex: 0,
        required: true,
      },
      {
        type: "email",
        variant: "Input",
        name: "field-2",
        label: "Field 2",
        placeholder: "Placeholder 2",
        description: "Description 2",
        disabled: false,
        value: "",
        setValue: () => {},
        checked: false,
        onChange: () => {},
        onSelect: () => {},
        rowIndex: 1,
        required: false,
      },
    ];

    mockStore.setFormFields(newFields);
    expect(mockStore.setFormFields).toHaveBeenCalledWith(newFields);
  });

  it("should handle loading state", () => {
    const storeWithLoading = {
      ...mockStore,
      loading: true,
    };
    mockUseDynamicFormStore.mockReturnValue(storeWithLoading);

    expect(storeWithLoading.loading).toBe(true);
  });

  it("should handle form with complex fields", () => {
    const complexForm = {
      id: "form-1",
      name: "Complex Form",
      description: "Complex Description",
      fields: [
        {
          type: "text",
          variant: "Input",
          name: "text-field",
          label: "Text Field",
          placeholder: "Enter text",
          description: "A text input field",
          disabled: false,
          value: "",
          setValue: () => {},
          checked: false,
          onChange: () => {},
          onSelect: () => {},
          rowIndex: 0,
          required: true,
        },
        {
          type: "email",
          variant: "Input",
          name: "email-field",
          label: "Email Field",
          placeholder: "Enter email",
          description: "An email input field",
          disabled: false,
          value: "",
          setValue: () => {},
          checked: false,
          onChange: () => {},
          onSelect: () => {},
          rowIndex: 1,
          required: true,
        },
        {
          type: "checkbox",
          variant: "Checkbox",
          name: "checkbox-field",
          label: "Checkbox Field",
          description: "A checkbox field",
          disabled: false,
          value: false,
          setValue: () => {},
          checked: false,
          onChange: () => {},
          onSelect: () => {},
          rowIndex: 2,
          required: false,
        },
      ],
      version: 1,
      status: "active",
      layout: {
        rows: [
          { cols: 1, ids: ["text-field"], spacing: 16 },
          { cols: 1, ids: ["email-field"], spacing: 16 },
          { cols: 1, ids: ["checkbox-field"], spacing: 16 },
        ],
      },
    };

    const storeWithComplexForm = {
      ...mockStore,
      formForEdit: complexForm,
    };
    mockUseDynamicFormStore.mockReturnValue(storeWithComplexForm);

    expect(storeWithComplexForm.formForEdit).toEqual(complexForm);
    expect(storeWithComplexForm.formForEdit?.fields).toHaveLength(3);
  });

  it("should handle form with missing optional properties", () => {
    const minimalForm = {
      id: "form-1",
      name: "Minimal Form",
      description: "Minimal Description",
      fields: [],
      version: 1,
      status: "active",
      // No layout, metadata, createdAt, updatedAt
    };

    const storeWithMinimalForm = {
      ...mockStore,
      formForEdit: minimalForm,
    };
    mockUseDynamicFormStore.mockReturnValue(storeWithMinimalForm);

    expect(storeWithMinimalForm.formForEdit).toEqual(minimalForm);
  });
});
