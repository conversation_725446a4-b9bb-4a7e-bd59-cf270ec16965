import React from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Form, FormField, FormItem, FormControl } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import If from "@/components/ui/if";
import { FormFieldType } from "./FormBuilder";
import { generateDefaultValues, generateZodSchema } from "./GenerateCodeParts";
import { RenderFormField } from "./RenderFormField";

export type FormFieldOrGroup = FormFieldType | FormFieldType[];

export type FormPreviewProps = {
  formFields: FormFieldOrGroup[];
  isEditing?: boolean;
  layout?: { rows: { cols: number; ids: string[]; spacing: number }[] };
};

const renderFormFields = (
  fields: FormFieldOrGroup[],
  form: ReturnType<
    typeof useForm<z.infer<ReturnType<typeof generateZodSchema>>>
  >,
  isEditing?: boolean | undefined,
  layout?:
    | { rows: { cols: number; ids: string[]; spacing: number }[] }
    | undefined,
) => {
  // If editing and layout.rows is provided, group fields according to layout.rows
  if (isEditing && layout && layout.rows) {
    // Flatten all fields to a map by name for quick lookup
    const fieldMap: Record<string, FormFieldType> = {};
    fields.forEach((fieldOrGroup) => {
      if (Array.isArray(fieldOrGroup)) {
        fieldOrGroup.forEach((f) => {
          fieldMap[f.name] = f;
        });
      } else {
        fieldMap[fieldOrGroup.name] = fieldOrGroup;
      }
    });
    // Build groups as in layout
    return layout.rows.map((row, rowIdx) => {
      const group = row.ids.map((id) => fieldMap[id]).filter(Boolean);
      if (group.length > 1) {
        // Render as a group (array)
        const getColSpan = (totalFields: number) => {
          switch (totalFields) {
            case 2:
              return 6;
            case 3:
              return 4;
            default:
              return 12;
          }
        };
        return (
          <div key={rowIdx} className="grid grid-cols-12 gap-4">
            {group.map((field) => (
              <FormField
                key={field.name}
                control={form.control}
                name={field.name}
                render={({ field: formField }) => (
                  <FormItem className={`col-span-${getColSpan(group.length)}`}>
                    <FormControl>
                      {React.cloneElement(
                        RenderFormField(field, form) as React.ReactElement,
                        {
                          ...formField,
                        },
                      )}
                    </FormControl>
                  </FormItem>
                )}
              />
            ))}
          </div>
        );
      } else if (group.length === 1) {
        // Render as a single field
        const field = group[0];
        return (
          <FormField
            key={field.name}
            control={form.control}
            name={field.name}
            render={({ field: formField }) => (
              <FormItem className="col-span-12">
                <FormControl>
                  {React.cloneElement(
                    RenderFormField(field, form) as React.ReactElement,
                    {
                      ...formField,
                    },
                  )}
                </FormControl>
              </FormItem>
            )}
          />
        );
      } else {
        return null;
      }
    });
  }
  // If not editing or no layout.rows, return the original rendering logic
  return fields.map((fieldOrGroup, index) => {
    if (Array.isArray(fieldOrGroup)) {
      const getColSpan = (totalFields: number) => {
        switch (totalFields) {
          case 2:
            return 6;
          case 3:
            return 4;
          default:
            return 12;
        }
      };
      return (
        <div key={index} className="grid grid-cols-12 gap-4">
          {fieldOrGroup.map((field) => (
            <FormField
              key={field.name}
              control={form.control}
              name={field.name}
              render={({ field: formField }) => (
                <FormItem
                  className={`col-span-${getColSpan(fieldOrGroup.length)}`}
                >
                  <FormControl>
                    {React.cloneElement(
                      RenderFormField(field, form) as React.ReactElement,
                      {
                        ...formField,
                      },
                    )}
                  </FormControl>
                </FormItem>
              )}
            />
          ))}
        </div>
      );
    } else {
      return (
        <FormField
          key={index}
          control={form.control}
          name={fieldOrGroup.name}
          render={({ field: formField }) => (
            <FormItem className="col-span-12">
              <FormControl>
                {React.cloneElement(
                  RenderFormField(fieldOrGroup, form) as React.ReactElement,
                  {
                    ...formField,
                  },
                )}
              </FormControl>
            </FormItem>
          )}
        />
      );
    }
  });
};

export const FormPreview: React.FC<FormPreviewProps> = ({
  formFields,
  isEditing,
  layout,
}) => {
  const formSchema = generateZodSchema(
    formFields,
  ) as z.ZodObject<z.ZodRawShape>;

  const defaultVals = generateDefaultValues(formFields);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultVals,
  });

  function onSubmit() {
    //data: z.infer<typeof formSchema>
    // try {
    //   toast(
    //     <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
    //       <code className="text-white">{JSON.stringify(data, null, 2)}</code>
    //     </pre>
    //   );
    // } catch (error) {
    //   console.error("Form submission error", error);
    //   toast.error("Failed to submit the form. Please try again.");
    // }
  }

  return (
    <div className="w-full h-full col-span-1 rounded-xl flex justify-center">
      <div className="w-full space-y-4 h-full md:max-h-[70vh] overflow-auto">
        <If
          condition={formFields.length > 0}
          render={() => (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4 py-5 max-w-lg mx-auto"
              >
                {renderFormFields(formFields, form, isEditing, layout)}
                <Button type="submit">{"Submit"}</Button>
              </form>
            </Form>
          )}
          otherwise={() => (
            <div className="h-[50vh] flex justify-center items-center">
              <p>{"No form element selected yet."}</p>
            </div>
          )}
        />
      </div>
    </div>
  );
};
