"use client";
import CellActions from "@/components/common/CellActions";
import { IconButton } from "@/components/common/CustomButtons";
import { DataTable } from "@/components/common/tables/DataTable";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { Eye } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React from "react";
import { Form, useDynamicFormStore } from "./store/dynamicFormStore";
import {
  FormFieldOrGroup,
  FormPreview,
} from "./new-form/components/FormPreview";
import { DialogTitle } from "@radix-ui/react-dialog";
import { toast } from "@/hooks/use-toast";
import CustomIcon from "@/components/common/CustomIcons";
import CustomPageCard from "@/components/common/CustomPageCard";
import { SortingState } from "@tanstack/react-table";
import DeleteDialog from "@/components/common/DeleteDialog";

const FormManagement = () => {
  const t = useTranslations("formsManagement");
  const router = useRouter();

  const {
    data,
    setLayout,
    layout,
    fetchPaginatedData,
    searchForms,
    deleteForm,
    formsLoading,
  } = useDynamicFormStore((state) => state);

  // Server-side pagination state
  const [paginationState, setPaginationState] = React.useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [searchQuery, setSearchQuery] = React.useState<string>("");
  const [sorting, setSorting] = React.useState<SortingState>([]);

  // Preview dialog state
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [previewFields, setPreviewFields] = React.useState<FormFieldOrGroup[]>(
    [],
  );
  const [previewFormName, setPreviewFormName] = React.useState<string>("");

  // Delete dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [formToDelete, setFormToDelete] = React.useState<Form | null>(null);
  const [isDeleting, setIsDeleting] = React.useState(false);

  React.useEffect(() => {
    const fetchData = async () => {
      try {
        if (searchQuery) {
          await searchForms(
            searchQuery,
            paginationState.pageIndex,
            paginationState.pageSize,
            sorting[0]?.id || "createdAt",
            sorting[0]?.desc ? "desc" : "asc",
          );
        } else {
          await fetchPaginatedData(
            paginationState.pageIndex,
            paginationState.pageSize,
            sorting[0]?.id || "createdAt",
            sorting[0]?.desc ? "desc" : "asc",
          );
        }
      } catch (error) {
        console.error("Error fetching forms data:", error);
      }
    };
    fetchData();
  }, [
    searchQuery,
    paginationState.pageIndex,
    paginationState.pageSize,
    sorting,
    searchForms,
    fetchPaginatedData,
  ]);

  const handlePaginationChange = async (
    pageIndex: number,
    pageSize: number,
  ) => {
    try {
      setPaginationState({ pageIndex, pageSize });
      if (searchQuery) {
        await searchForms(
          searchQuery,
          pageIndex,
          pageSize,
          sorting[0]?.id || "createdAt",
          sorting[0]?.desc ? "desc" : "asc",
        );
      } else {
        await fetchPaginatedData(
          pageIndex,
          pageSize,
          sorting[0]?.id || "createdAt",
          sorting[0]?.desc ? "desc" : "asc",
        );
      }
    } catch (error) {
      console.error("Error handling pagination:", error);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setPaginationState({ pageIndex: 0, pageSize: paginationState.pageSize });
  };

  const handleSortingChange = (sorting: SortingState) => {
    setSorting(sorting);
  };

  const confirmDelete = async () => {
    if (formToDelete?.id) {
      setIsDeleting(true);
      try {
        await deleteForm(formToDelete.id);
        // Refresh the current page after deletion
        if (searchQuery) {
          await searchForms(
            searchQuery,
            paginationState.pageIndex,
            paginationState.pageSize,
            sorting[0]?.id || "createdAt",
            sorting[0]?.desc ? "desc" : "asc",
          );
        } else {
          await fetchPaginatedData(
            paginationState.pageIndex,
            paginationState.pageSize,
            sorting[0]?.id || "createdAt",
            sorting[0]?.desc ? "desc" : "asc",
          );
        }
        setIsDeleteDialogOpen(false);
        setFormToDelete(null);
      } catch (error) {
        console.error("Error deleting form:", error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const formatDateTime = (dateStr?: string) =>
    dateStr ? new Date(dateStr).toLocaleString() : "";

  const columns = [
    { accessorKey: "name", header: t("home.title"), enableSorting: true },
    {
      accessorKey: "description",
      header: t("home.discription"),
      enableSorting: true,
    },
    {
      accessorKey: "category",
      header: t("home.category"),
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: t("home.createdDate"),
      enableSorting: true,
      cell: ({ row }: { row: { original: { createdAt?: string } } }) =>
        formatDateTime(row.original.createdAt),
    },
    {
      accessorKey: "updatedAt",
      header: t("home.updatedDate"),
      enableSorting: true,
      cell: ({ row }: { row: { original: { updatedAt?: string } } }) =>
        formatDateTime(row.original.updatedAt),
    },
    {
      accessorKey: "preview",
      header: t("home.preview"),
      cell: ({ row }: { row: { original: Form } }) => (
        <div>
          <IconButton
            icon={Eye}
            className="shadow-none bg-inherit text-black hover:bg-gray-100"
            onClick={async () => {
              setPreviewFormName(row.original.name || "");
              // const fields = await fetchFormFields(row.original.id);
              console.log("row:", row.original);

              setPreviewFields(row.original?.fields || []);
              setLayout(row.original?.layout || { rows: [] });
              setPreviewOpen(true);
            }}
          />
        </div>
      ),
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: ({ row }: { row: { original: { id: string } } }) => (
        <div>
          <CellActions
            actions={[
              {
                label: "Edit",
                onClick: () => {
                  // Navigate to edit form page with form id
                  router.push(
                    `/forms-management/new-form?id=${row.original.id}`,
                  );
                },
              },
              {
                label: "Delete",
                onClick: async () => {
                  if (row.original.id) {
                    const form = data.items.find(
                      (f) => f.id === row.original.id,
                    );
                    if (form) {
                      setFormToDelete(form);
                      setIsDeleteDialogOpen(true);
                    }
                  }
                },
              },
            ]}
          />
        </div>
      ),
    },
  ];

  const handleNavigate = () => {
    try {
      router.push("/forms-management/new-form");
    } catch (error) {
      toast({
        title: "Navigation failed",
        description: String(error),
        variant: "destructive",
      });
    }
  };
  const createIcon = () => (
    <span className="flex items-center">
      <CustomIcon
        name="addEvent"
        style={{
          width: "16px",
          height: "16px",
          fill: "#FFFFFF",
        }}
      />
    </span>
  );

  return (
    <CustomPageCard>
      <DataTable
        columns={columns}
        data={(data?.items || []).map((form: Form) => ({
          ...form,
        }))}
        showSearchBar
        isLoading={formsLoading}
        serverPagination={true}
        onPaginationChange={handlePaginationChange}
        totalItems={data?.meta?.totalCount || 0}
        serverSearch={true}
        onSearchChange={handleSearchChange}
        sorting={sorting}
        onSortingChange={handleSortingChange}
        currentPage={paginationState.pageIndex}
        pageSize={paginationState.pageSize}
        searchValue={searchQuery}
        buttons={[
          {
            show: true,
            label: t("home.Addbutton"),
            onClick: handleNavigate,
            variant: "default",
            icon: createIcon,
          },
        ]}
      />

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader className="flex flex-row items-center justify-between p-0 pb-4 border-b">
            <DialogTitle>{previewFormName} - Preview</DialogTitle>
          </DialogHeader>
          <FormPreview
            formFields={previewFields}
            layout={layout}
            isEditing={true}
          />
        </DialogContent>
      </Dialog>
      <DeleteDialog
        isDialogOpen={isDeleteDialogOpen}
        setIsDialogOpen={setIsDeleteDialogOpen}
        handleDelete={confirmDelete}
        labelCancel={t("home.cancel")}
        labelConfirm={t("home.delete")}
        loading={isDeleting}
      >
        <div className="text-center">
          <p className="text-lg font-medium mb-2">
            {t("home.deleteConfirmation")}
          </p>
          {formToDelete && (
            <p className="text-sm text-gray-600">
              &ldquo;{formToDelete.name}&rdquo;
            </p>
          )}
        </div>
      </DeleteDialog>
    </CustomPageCard>
  );
};

export default FormManagement;
