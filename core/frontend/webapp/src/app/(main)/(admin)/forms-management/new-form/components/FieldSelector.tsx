import React from "react";
import { Button } from "@/components/ui/button";
import { fieldTypes } from "@/constants/FieldType";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";

type FieldSelectorProps = {
  addFormField: (variant: string, index?: number) => void;
};

export const FieldSelector: React.FC<FieldSelectorProps> = ({
  addFormField,
}) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button
        variant="outline"
        size="icon"
        className="min-w-9 w-9 h-9 rounded-full"
      >
        +
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent>
      <DropdownMenuLabel>Select Component</DropdownMenuLabel>
      <DropdownMenuSeparator />
      {fieldTypes.map((fieldType) => (
        <DropdownMenuItem
          key={fieldType.name}
          onClick={() => addFormField(fieldType.name, fieldType.index)}
        >
          {fieldType.name}
        </DropdownMenuItem>
      ))}
    </DropdownMenuContent>
  </DropdownMenu>
);
