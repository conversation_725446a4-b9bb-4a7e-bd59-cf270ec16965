import "@testing-library/jest-dom";
import { TextE<PERSON><PERSON>, TextDecoder } from "util";
import React from "react";

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/",
}));

// Mock next-intl
jest.mock("next-intl", () => ({
  useTranslations: jest.fn(() => (key: string) => key),
  useLocale: jest.fn(() => "en"),
}));

// Mock axios
jest.mock("@/lib/axios", () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock toast
jest.mock("@/hooks/use-toast", () => ({
  toast: jest.fn(),
}));

// Mock CustomIcon component
jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: () => React.createElement("span", { "data-testid": "custom-icon" }),
}));

// Mock DataTable component to return a valid React element
jest.mock("@/components/common/tables/DataTable", () => ({
  DataTable: () => React.createElement("div", { "data-testid": "datatable" }),
}));

// Mock CellActions component
jest.mock("@/components/common/CellActions", () => ({
  __esModule: true,
  default: () => React.createElement("div", { "data-testid": "cell-actions" }),
}));

// Mock IconButton component
jest.mock("@/components/common/CustomButtons", () => ({
  IconButton: () =>
    React.createElement("button", { "data-testid": "icon-button" }),
}));

// Mock Dialog components
jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({ children }: { children?: React.ReactNode }) =>
    React.createElement("div", { "data-testid": "dialog" }, children),
  DialogContent: ({ children }: { children?: React.ReactNode }) =>
    React.createElement("div", { "data-testid": "dialog-content" }, children),
  DialogHeader: ({ children }: { children?: React.ReactNode }) =>
    React.createElement("div", { "data-testid": "dialog-header" }, children),
}));

// Mock FormPreview component
jest.mock("../new-form/components/FormPreview", () => ({
  FormPreview: () =>
    React.createElement("div", { "data-testid": "form-preview" }),
}));

// Global setup
global.TextEncoder = TextEncoder;
// @ts-expect-error - TextDecoder type mismatch in test environment
global.TextDecoder = TextDecoder;

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation(() => ({
    matches: false,
    media: "",
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
