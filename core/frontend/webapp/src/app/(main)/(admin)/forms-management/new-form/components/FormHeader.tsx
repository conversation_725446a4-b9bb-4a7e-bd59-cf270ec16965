"use client";
import { Save, Edit } from "lucide-react";
import { IconButton } from "@/components/common/CustomButtons";
import { useTranslations } from "next-intl";
import CustomInput from "@/components/common/CustomInput";
import CustomSelect from "@/components/common/CustomSelect";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { useFormContext } from "react-hook-form";
import { useDynamicFormStore } from "../../store/dynamicFormStore";
import React from "react";

export function FormHeader() {
  const t = useTranslations("documentsTemplates");
  const {
    loading,
    formForEdit,
    categories,
    categoriesLoading,
    fetchCategories,
  } = useDynamicFormStore();
  const { control, setValue, watch } = useFormContext();
  const isEdit = !!formForEdit;

  // Fetch categories when component mounts
  React.useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const categoryOptions = React.useMemo(
    () =>
      categories.map((category) => ({
        value: category.name,
        label: category.displayName,
      })),
    [categories],
  );

  // Dynamically populate fields when editing
  React.useEffect(() => {
    if (isEdit && formForEdit) {
      setValue("form", formForEdit.name || "");
      setValue("description", formForEdit.description || "");
      const categoryFromForm = formForEdit.category;
      const categoryFromMetadata = formForEdit.metadata?.category;
      const categoryValue = categoryFromForm || categoryFromMetadata || "";

      // Only set if the category exists in our options
      const validCategory = categoryOptions.find(
        (opt) => opt.value === categoryValue,
      )
        ? categoryValue
        : "";

      setValue("category", validCategory);
    }
  }, [isEdit, formForEdit, setValue, categoryOptions]);

  const formValue = watch("form");
  const descriptionValue = watch("description");
  const categoryValue = watch("category");

  return (
    <div className="flex items-end gap-4 p-4">
      <FormField
        control={control}
        name="form"
        render={({ field }) => (
          <FormItem className="w-80">
            <FormControl>
              <CustomInput
                {...field}
                label={t("NewForm.form")}
                className="flex-1"
                value={formValue}
                onChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormControl>
              <CustomInput
                {...field}
                label={t("NewForm.description")}
                className="flex-1"
                value={descriptionValue}
                onChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="category"
        render={({ field }) => (
          <FormItem className="w-80">
            <FormControl>
              <CustomSelect
                {...field}
                label={t("NewForm.category")}
                options={categoryOptions}
                defaultValue={categoryValue ?? ""}
                disabled={categoriesLoading}
                placeholder={
                  categoriesLoading
                    ? "Loading categories..."
                    : "Select a category"
                }
                onValueChange={(val) => {
                  setValue("category", val);
                  field.onChange(val);
                }}
                className=" text-base"
              />
            </FormControl>
          </FormItem>
        )}
      />
      <div className="flex items-center">
        <IconButton
          className="gap-2"
          icon={isEdit ? <Edit /> : <Save />}
          type="submit"
          label={isEdit ? "Update" : "Save"}
          isLoading={loading}
        />
      </div>
    </div>
  );
}
