import { renderHook, act } from "@testing-library/react";
import { useDynamicFormStore } from "../../store/dynamicFormStore";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";

// Mock dependencies
jest.mock("@/lib/axios");
jest.mock("@/hooks/use-toast");

const mockApi = api as jest.Mocked<typeof api>;
const mockToast = toast as jest.MockedFunction<typeof toast>;

describe("useDynamicFormStore", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
    const { result } = renderHook(() => useDynamicFormStore());
    act(() => {
      result.current.setFormFields([]);
      result.current.setLayout({ rows: [] });
    });
  });

  describe("State Management", () => {
    it("should initialize with default values", () => {
      const { result } = renderHook(() => useDynamicFormStore());

      expect(result.current.loading).toBe(false);
      expect(result.current.formFields).toEqual([]);
      expect(result.current.layout).toEqual({ rows: [] });
      expect(result.current.forms).toEqual([]);
      expect(result.current.formsLoading).toBe(false);
      expect(result.current.formsError).toBeNull();
      expect(result.current.formForEdit).toBeNull();
    });

    it("should update form fields", () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const testFields = [
        {
          type: "text",
          variant: "Input",
          name: "field-1",
          label: "Test Field",
          placeholder: "Test placeholder",
          description: "Test description",
          disabled: false,
          value: "",
          setValue: () => {},
          checked: false,
          onChange: () => {},
          onSelect: () => {},
          rowIndex: 0,
          required: true,
        },
      ];

      act(() => {
        result.current.setFormFields(testFields);
      });

      expect(result.current.formFields).toEqual(testFields);
    });

    it("should update layout", () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const testLayout = {
        rows: [{ cols: 2, ids: ["field-1", "field-2"], spacing: 16 }],
      };

      act(() => {
        result.current.setLayout(testLayout);
      });

      expect(result.current.layout).toEqual(testLayout);
    });

    it("should add form field", () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const newField = {
        type: "text",
        variant: "Input",
        name: "field-1",
        label: "New Field",
        placeholder: "New placeholder",
        description: "New description",
        disabled: false,
        value: "",
        setValue: () => {},
        checked: false,
        onChange: () => {},
        onSelect: () => {},
        rowIndex: 0,
        required: false,
      };

      act(() => {
        result.current.addFormField(newField);
      });

      expect(result.current.formFields).toHaveLength(1);
      expect(result.current.formFields[0]).toEqual(newField);
    });

    it("should update form field", () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const initialField = {
        type: "text",
        variant: "Input",
        name: "field-1",
        label: "Original Label",
        placeholder: "Original placeholder",
        description: "Original description",
        disabled: false,
        value: "",
        setValue: () => {},
        checked: false,
        onChange: () => {},
        onSelect: () => {},
        rowIndex: 0,
        required: false,
      };

      act(() => {
        result.current.addFormField(initialField);
      });

      act(() => {
        result.current.updateFormField([0], { label: "Updated Label" });
      });

      expect((result.current.formFields[0] as { label: string }).label).toBe(
        "Updated Label",
      );
    });
  });

  describe("submitFormPreview", () => {
    it("should submit form successfully", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const formData = {
        name: "Test Form",
        description: "Test Description",
        fields: [],
      };

      mockApi.post.mockResolvedValueOnce({
        data: { id: "form-1", ...formData },
      });

      await act(async () => {
        const response = await result.current.submitFormPreview(formData);
        expect(response).toEqual({ id: "form-1", ...formData });
      });

      expect(mockApi.post).toHaveBeenCalledWith(
        "/dynamic-forms/forms",
        formData,
      );
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Form created successfully",
        variant: "success",
      });
    });

    it("should handle API error", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const formData = {
        name: "Test Form",
        description: "Test Description",
        fields: [],
      };

      // Mock API error with response data that contains a message
      const apiError = {
        response: {
          data: {
            message: "Backend error message",
          },
        },
      };
      mockApi.post.mockRejectedValueOnce(apiError);

      await act(async () => {
        await expect(
          result.current.submitFormPreview(formData),
        ).rejects.toEqual(apiError);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Backend error message",
        variant: "destructive",
      });
    });
  });

  describe("fetchForms", () => {
    it("should fetch forms successfully", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const mockForms = [
        {
          id: "form-1",
          name: "Test Form 1",
          description: "Test Description 1",
          fields: [],
          version: 1,
          status: "active",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
          layout: { rows: [] },
        },
        {
          id: "form-2",
          name: "Test Form 2",
          description: "Test Description 2",
          fields: [],
          version: 1,
          status: "active",
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
          layout: { rows: [] },
        },
      ];

      mockApi.get.mockResolvedValueOnce({ data: { items: mockForms } });

      await act(async () => {
        await result.current.fetchForms();
      });

      expect(mockApi.get).toHaveBeenCalledWith("/dynamic-forms/forms");
      expect(result.current.forms).toEqual(mockForms);
      expect(result.current.formsLoading).toBe(false);
      expect(result.current.formsError).toBeNull();
    });

    it("should handle fetch error", async () => {
      const { result } = renderHook(() => useDynamicFormStore());

      // Mock API error with response data that contains a message
      const apiError = {
        response: {
          data: {
            message: "Backend fetch error",
          },
        },
      };
      mockApi.get.mockRejectedValueOnce(apiError);

      await act(async () => {
        await result.current.fetchForms();
      });

      expect(result.current.formsError).toBe("Backend fetch error");
      expect(result.current.formsLoading).toBe(false);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Backend fetch error",
        variant: "destructive",
      });
    });

    it("should handle empty response", async () => {
      const { result } = renderHook(() => useDynamicFormStore());

      mockApi.get.mockResolvedValueOnce({ data: {} });

      await act(async () => {
        await result.current.fetchForms();
      });

      expect(result.current.forms).toEqual([]);
      expect(result.current.formsLoading).toBe(false);
    });
  });

  describe("deleteForm", () => {
    it("should delete form successfully", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const initialForms = [
        {
          id: "form-1",
          name: "Test Form 1",
          description: "Test Description 1",
          fields: [],
          version: 1,
          status: "active",
          layout: { rows: [] },
        },
        {
          id: "form-2",
          name: "Test Form 2",
          description: "Test Description 2",
          fields: [],
          version: 1,
          status: "active",
          layout: { rows: [] },
        },
      ];

      // Set initial forms
      act(() => {
        result.current.forms = initialForms;
      });

      mockApi.delete.mockResolvedValueOnce({});

      await act(async () => {
        await result.current.deleteForm("form-1");
      });

      expect(mockApi.delete).toHaveBeenCalledWith(
        "/dynamic-forms/forms/form-1",
      );
      // Note: With server-side pagination, local state is not updated
      // The parent component handles refreshing the data
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Form deleted successfully",
        variant: "success",
      });
    });

    it("should handle delete error", async () => {
      const { result } = renderHook(() => useDynamicFormStore());

      // Mock API error with response data that contains a message
      const apiError = {
        response: {
          data: {
            message: "Delete operation failed",
          },
        },
      };
      mockApi.delete.mockRejectedValueOnce(apiError);

      await act(async () => {
        await expect(result.current.deleteForm("form-1")).rejects.toEqual(
          apiError,
        );
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Delete operation failed",
        variant: "destructive",
      });
    });
  });

  describe("fetchFormFields", () => {
    it("should fetch form fields successfully", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const mockFields = [
        {
          id: "field-1",
          type: "text",
          label: "Test Field",
          required: true,
        },
      ];

      mockApi.get.mockResolvedValueOnce({ data: { fields: mockFields } });

      const fields = await result.current.fetchFormFields("form-1");

      expect(mockApi.get).toHaveBeenCalledWith("/dynamic-forms/forms/form-1");
      expect(fields).toEqual(mockFields);
    });

    it("should return empty array on error", async () => {
      const { result } = renderHook(() => useDynamicFormStore());

      mockApi.get.mockRejectedValueOnce(new Error("Fetch failed"));

      const fields = await result.current.fetchFormFields("form-1");

      expect(fields).toEqual([]);
    });
  });

  describe("fetchFormForEdit", () => {
    it("should fetch form for edit successfully", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const mockForm = {
        id: "form-1",
        name: "Test Form",
        description: "Test Description",
        fields: [],
        version: 1,
        status: "active",
        layout: { rows: [] },
      };

      mockApi.get.mockResolvedValueOnce({ data: mockForm });

      await act(async () => {
        await result.current.fetchFormForEdit("form-1");
      });

      expect(mockApi.get).toHaveBeenCalledWith("/dynamic-forms/forms/form-1");
      expect(result.current.formForEdit).toEqual(mockForm);
      expect(result.current.loading).toBe(false);
    });

    it("should handle fetch error", async () => {
      const { result } = renderHook(() => useDynamicFormStore());

      // Mock API error with response data that contains a message
      const apiError = {
        response: {
          data: {
            message: "Form not found",
          },
        },
      };
      mockApi.get.mockRejectedValueOnce(apiError);

      await act(async () => {
        await result.current.fetchFormForEdit("form-1");
      });

      expect(result.current.formForEdit).toBeNull();
      expect(result.current.loading).toBe(false);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Form not found",
        variant: "destructive",
      });
    });
  });

  describe("updateForm", () => {
    it("should update form successfully", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const initialForms = [
        {
          id: "form-1",
          name: "Original Name",
          description: "Original Description",
          fields: [],
          version: 1,
          status: "active",
          layout: { rows: [] },
        },
      ];

      // Set initial forms
      act(() => {
        result.current.forms = initialForms;
      });

      const updates = {
        name: "Updated Name",
        description: "Updated Description",
      };

      const updatedForm = {
        id: "form-1",
        ...updates,
        fields: [],
        version: 1,
        status: "active",
        layout: { rows: [] },
      };

      mockApi.put.mockResolvedValueOnce({ data: updatedForm });

      await act(async () => {
        await result.current.updateForm("form-1", updates);
      });

      expect(mockApi.put).toHaveBeenCalledWith(
        "/dynamic-forms/forms/form-1",
        updates,
      );
      // Note: With server-side pagination, the forms array is not updated locally
      // Only formForEdit is updated for the edit form functionality
      expect(result.current.formForEdit).toEqual(updatedForm);
      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Form updated successfully",
        variant: "success",
      });
    });

    it("should handle update error", async () => {
      const { result } = renderHook(() => useDynamicFormStore());

      // Mock API error with response data that contains a message
      const apiError = {
        response: {
          data: {
            message: "Update validation failed",
          },
        },
      };
      mockApi.put.mockRejectedValueOnce(apiError);

      await act(async () => {
        await result.current.updateForm("form-1", { name: "Updated Name" });
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Update validation failed",
        variant: "destructive",
      });
    });
  });

  describe("Data Transformation", () => {
    it("should transform API response with name/title mapping", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const apiResponse = [
        {
          id: "form-1",
          title: "API Title", // API uses 'title' instead of 'name'
          discription: "API Description", // API uses 'discription' instead of 'description'
          fields: [],
          version: 1,
          status: "active",
          layout: { rows: [] },
        },
      ];

      mockApi.get.mockResolvedValueOnce({ data: { items: apiResponse } });

      await act(async () => {
        await result.current.fetchForms();
      });

      expect(result.current.forms[0].name).toBe("API Title");
      expect(result.current.forms[0].description).toBe("API Description");
    });

    it("should handle missing layout with default values", async () => {
      const { result } = renderHook(() => useDynamicFormStore());
      const apiResponse = [
        {
          id: "form-1",
          name: "Test Form",
          description: "Test Description",
          fields: [],
          version: 1,
          status: "active",
          // No layout property
        },
      ];

      mockApi.get.mockResolvedValueOnce({ data: { items: apiResponse } });

      await act(async () => {
        await result.current.fetchForms();
      });

      expect(result.current.forms[0].layout).toEqual({
        columns: 2,
        spacing: 16,
      });
    });
  });
});
