import api from "@/lib/axios";
import { create } from "zustand";
import {
  FormFieldOrGroup,
  FormFieldType,
} from "../new-form/components/FormBuilder";
import { Dispatch, SetStateAction } from "react";
import { toast } from "@/hooks/use-toast";

// Helper function to extract message from backend response
// Returns null if no backend message is found
const extractMessageFromResponse = (responseData: unknown): string | null => {
  if (!responseData || typeof responseData !== "object") {
    return null;
  }

  const data = responseData as Record<string, unknown>;

  // Check if response has a message field
  if (data?.message && typeof data.message === "string") {
    return data.message;
  }

  // Check if message is nested in details
  if (
    data?.details &&
    typeof data.details === "object" &&
    data.details !== null
  ) {
    const details = data.details as Record<string, unknown>;
    if (details.message && typeof details.message === "string") {
      return details.message;
    }

    if (details.error && typeof details.error === "string") {
      return details.error;
    }

    if (details.errors && Array.isArray(details.errors) && details.errors[0]) {
      const error = details.errors[0] as Record<string, unknown>;
      if (
        error.field &&
        error.constraints &&
        Array.isArray(error.constraints) &&
        error.constraints[0]
      ) {
        return `${error.field}: ${error.constraints[0]}`;
      }
    }
  }

  // Return null if no backend message found
  return null;
};

// Helper function to show toast only if there's a backend message
const showToastIfMessage = (
  responseData: unknown,
  title: string,
  variant: "default" | "destructive" | "success" = "default",
) => {
  const message = extractMessageFromResponse(responseData);
  if (message) {
    toast({
      title,
      description: message,
      variant,
    });
  }
};

interface ValidationError {
  code: string;
  message: string;
  statusCode: number;
  timestamp: string;
  details?: {
    errors: Array<{
      field: string;
      value: string;
      constraints: string[];
    }>;
  };
  path: string;
}

interface ApiError {
  response?: {
    data: ValidationError;
  };
}

export type Category = {
  id: string;
  name: string;
  displayName: string;
  description: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

export type Form = {
  id: string;
  name: string;
  description: string;
  fields: FormFieldOrGroup[];
  metadata?: Record<string, unknown>;
  category?: string;
  version: number;
  status: string;
  layout?: {
    rows: { cols: number; ids: string[]; spacing: number }[];
  };
  createdAt?: string;
  updatedAt?: string;
};

type DynamicFormState = {
  loading: boolean;
  formFields: FormFieldOrGroup[];
  setFormFields: Dispatch<SetStateAction<FormFieldOrGroup[]>>;
  layout: {
    rows: { cols: number; ids: string[]; spacing: number }[];
  };
  setLayout: Dispatch<
    SetStateAction<
      { rows: { cols: number; ids: string[]; spacing: number }[] } | undefined
    >
  >;
  addFormField: (field: FormFieldType) => void;
  updateFormField: (path: number[], updates: Partial<FormFieldType>) => void;
  submitFormPreview: (
    formData: Record<string, unknown>,
  ) => Promise<Record<string, unknown>>;
  // Form management with server-side pagination
  data: {
    items: Form[];
    meta: {
      pageSize: number;
      currentPage: number;
      totalPages: number;
      totalCount: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  };
  forms: Form[];
  formsLoading: boolean;
  formsError: string | null;
  fetchForms: () => Promise<void>;
  fetchPaginatedData: (
    pageIndex: number,
    pageSize: number,
    sortBy: string,
    sortOrder: string,
  ) => Promise<void>;
  searchForms: (
    query: string,
    pageIndex: number,
    pageSize: number,
    sortBy?: string,
    sortOrder?: string,
  ) => Promise<void>;
  deleteForm: (id: string) => Promise<void>;
  fetchFormFields: (formId: string) => Promise<FormFieldOrGroup[]>;
  formForEdit: Form | null;
  fetchFormForEdit: (id: string) => Promise<void>;
  updateForm: (id: string, updates: Record<string, unknown>) => Promise<void>;
  // Categories management
  categories: Category[];
  categoriesLoading: boolean;
  categoriesError: string | null;
  fetchCategories: () => Promise<void>;
  // Form state management
  resetFormState: () => void;
};

export const useDynamicFormStore = create<DynamicFormState>((set) => ({
  loading: false,
  formFields: [],
  layout: {
    rows: [],
  },
  data: {
    items: [],
    meta: {
      pageSize: 5,
      currentPage: 1,
      totalPages: 0,
      totalCount: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    },
  },
  // Categories initialization
  categories: [],
  categoriesLoading: false,
  categoriesError: null,
  setFormFields: (fields) =>
    set((state) => ({
      formFields:
        typeof fields === "function" ? fields(state.formFields) : fields,
    })),
  setLayout: (layout) =>
    set((state) => ({
      layout: typeof layout === "function" ? layout(state.layout) : layout,
    })),
  addFormField: (field) =>
    set((state) => ({
      formFields: [...state.formFields, field],
    })),
  updateFormField: (path, updates) =>
    set((state) => {
      // Set loading true before updating
      set({ loading: true });
      const updatedFields = JSON.parse(JSON.stringify(state.formFields));
      let current: FormFieldOrGroup[] | FormFieldOrGroup = updatedFields;

      for (let i = 0; i < path.length - 1; i++) {
        if (Array.isArray(current)) {
          current = current[path[i]] as FormFieldOrGroup[];
        }
      }
      (current as FormFieldOrGroup[])[path[path.length - 1]] = {
        ...(current as FormFieldOrGroup[])[path[path.length - 1]],
        ...updates,
      };

      // Set loading false after update
      setTimeout(() => set({ loading: false }), 0);

      return { formFields: updatedFields };
    }),
  submitFormPreview: async (formData) => {
    try {
      set({ loading: true });
      const response = await api.post("/dynamic-forms/forms", formData);

      // Use hardcoded success message since backend doesn't send response message
      toast({
        title: "Success",
        description: "Form created successfully",
        variant: "success",
      });

      return response.data;
    } catch (error) {
      const apiError = error as ApiError;
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  forms: [],
  formsLoading: false,
  formsError: null,
  fetchForms: async () => {
    set({ formsLoading: true, formsError: null });
    try {
      const response = await api.get("/dynamic-forms/forms");
      const formItems = (response.data?.items || []).map(
        (form: Record<string, unknown>) => ({
          ...form,
          name:
            (form as { name?: string; title?: string }).name ??
            (form as { title?: string }).title,
          description:
            (form as { description?: string; discription?: string })
              .description ?? (form as { discription?: string }).discription,
          layout: (form as { layout?: { columns?: number; spacing?: number } })
            .layout ?? { columns: 2, spacing: 16 },
        }),
      );
      set({
        forms: formItems,
        data: {
          items: formItems,
          meta: {
            pageSize: response.data?.meta?.pageSize || 5,
            currentPage: response.data?.meta?.currentPage || 1,
            totalPages: response.data?.meta?.totalPages || 1,
            totalCount: response.data?.meta?.totalCount || formItems.length,
            hasNextPage: response.data?.meta?.hasNextPage || false,
            hasPreviousPage: response.data?.meta?.hasPreviousPage || false,
          },
        },
        formsLoading: false,
      });
    } catch (error) {
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      set({
        formsError: message || "Failed to fetch forms",
        formsLoading: false,
      });
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    } finally {
      set({ loading: false });
    }
  },

  fetchPaginatedData: async (
    pageIndex: number,
    pageSize: number,
    sortBy: string,
    sortOrder: string,
  ) => {
    set({ formsLoading: true, formsError: null });
    try {
      const response = await api.get("/dynamic-forms/forms", {
        params: {
          page: pageIndex + 1, // Backend expects 1-based page numbers
          pageSize,
          sortBy: sortBy || "createdAt",
          sortOrder: sortOrder || "desc",
        },
      });

      if (response.data && response.data.items) {
        const formItems = response.data.items.map(
          (form: Record<string, unknown>) => ({
            ...form,
            name:
              (form as { name?: string; title?: string }).name ??
              (form as { title?: string }).title,
            description:
              (form as { description?: string; discription?: string })
                .description ?? (form as { discription?: string }).discription,
            layout: (
              form as { layout?: { columns?: number; spacing?: number } }
            ).layout ?? { columns: 2, spacing: 16 },
          }),
        );

        set({
          data: {
            items: formItems,
            meta: {
              pageSize: response.data.meta.pageSize,
              currentPage: response.data.meta.currentPage,
              totalPages: response.data.meta.totalPages,
              totalCount: response.data.meta.totalCount,
              hasNextPage: response.data.meta.hasNextPage,
              hasPreviousPage: response.data.meta.hasPreviousPage,
            },
          },
          forms: formItems,
          formsLoading: false,
        });
      } else {
        console.error("Invalid response format:", response.data);
        set({ formsLoading: false });
        showToastIfMessage(
          { message: "Invalid response format from server" },
          "Error",
          "destructive",
        );
      }
    } catch (error) {
      console.error("Failed to fetch paginated forms:", error);
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      set({
        formsError: message || "Failed to fetch forms",
        formsLoading: false,
      });
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  searchForms: async (
    query: string,
    pageIndex: number,
    pageSize: number,
    sortBy = "createdAt",
    sortOrder = "desc",
  ) => {
    set({ formsLoading: true, formsError: null });
    try {
      const response = await api.get("/dynamic-forms/forms", {
        params: {
          search: query, // Use search parameter instead of query
          page: pageIndex + 1, // Backend expects 1-based page numbers
          pageSize,
          sortBy,
          sortOrder,
        },
      });

      if (response.data && response.data.items) {
        const formItems = response.data.items.map(
          (form: Record<string, unknown>) => ({
            ...form,
            name:
              (form as { name?: string; title?: string }).name ??
              (form as { title?: string }).title,
            description:
              (form as { description?: string; discription?: string })
                .description ?? (form as { discription?: string }).discription,
            layout: (
              form as { layout?: { columns?: number; spacing?: number } }
            ).layout ?? { columns: 2, spacing: 16 },
          }),
        );

        set({
          data: {
            items: formItems,
            meta: {
              pageSize: response.data.meta.pageSize,
              currentPage: response.data.meta.currentPage,
              totalPages: response.data.meta.totalPages,
              totalCount: response.data.meta.totalCount,
              hasNextPage: response.data.meta.hasNextPage,
              hasPreviousPage: response.data.meta.hasPreviousPage,
            },
          },
          forms: formItems,
          formsLoading: false,
        });
      } else {
        console.error("Invalid search response format:", response.data);
        set({ formsLoading: false });
        showToastIfMessage(
          { message: "Invalid response format from search" },
          "Error",
          "destructive",
        );
      }
    } catch (error) {
      console.error("Failed to search forms:", error);
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      set({
        formsError: message || "Failed to search forms",
        formsLoading: false,
      });
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },
  deleteForm: async (id: string) => {
    try {
      await api.delete(`/dynamic-forms/forms/${id}`);

      // Use hardcoded success message since backend doesn't send response message
      toast({
        title: "Success",
        description: "Form deleted successfully",
        variant: "success",
      });
    } catch (error) {
      const apiError = error as ApiError;
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
      throw error;
    }
  },
  fetchFormFields: async (formId: string) => {
    try {
      const response = await api.get(`/dynamic-forms/forms/${formId}`);
      return response.data.fields || [];
    } catch {
      return [];
    }
  },
  formForEdit: null,
  fetchFormForEdit: async (id: string) => {
    set({ loading: true });
    try {
      const response = await api.get(`/dynamic-forms/forms/${id}`);
      set({
        formForEdit: {
          ...response.data,
          name: response.data.name ?? response.data.title,
          description: response.data.description ?? response.data.discription,
          layout: response.data.layout ?? { columns: 2, spacing: 16 },
        } as Form,
      });
    } catch (error) {
      const apiError = error as ApiError;
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
      set({ formForEdit: null });
    } finally {
      set({ loading: false });
    }
  },
  updateForm: async (id: string, updates: Partial<Form>) => {
    set({ loading: true });
    try {
      const response = await api.put(`/dynamic-forms/forms/${id}`, updates);
      set(() => ({
        formForEdit: {
          ...response.data,
          name: response.data.name ?? response.data.title,
          description: response.data.description ?? response.data.discription,
          layout: response.data.layout ?? { columns: 2, spacing: 16 },
        } as Form,
      }));

      // Use hardcoded success message since backend doesn't send response message
      toast({
        title: "Success",
        description: "Form updated successfully",
        variant: "success",
      });
    } catch (error) {
      const apiError = error as ApiError;
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    } finally {
      set({ loading: false });
    }
  },

  // Fetch categories from API
  fetchCategories: async () => {
    set({ categoriesLoading: true, categoriesError: null });
    try {
      const response = await api.get("/dynamic-forms/categories");
      const categories = response.data || [];
      set({
        categories: categories.filter((cat: Category) => cat.isActive), // Only show active categories
        categoriesLoading: false,
      });
    } catch (error) {
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      set({
        categoriesError: message || "Failed to fetch categories",
        categoriesLoading: false,
      });
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  // Reset form state when creating new form
  resetFormState: () => {
    set({
      formFields: [],
      layout: { rows: [] },
      formForEdit: null,
      loading: false,
    });
  },
}));
