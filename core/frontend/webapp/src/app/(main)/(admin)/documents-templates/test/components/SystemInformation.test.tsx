import React from "react";
import { render, screen } from "@testing-library/react";
import { SystemInformation } from "../../new-document-template/components/SysthemInformation";
import { useDocumentTemplateStore } from "../../store/documentsStore";

// Mock dependencies
jest.mock("../../store/documentsStore");

const mockUseDocumentTemplateStore =
  useDocumentTemplateStore as jest.MockedFunction<
    typeof useDocumentTemplateStore
  >;

describe("SystemInformation CRUD", () => {
  const mockStore = {
    groupedFields: [
      {
        source: "USER",
        fields: [{ id: "1", name: "firstName", label: "First Name" }],
      },
    ],
    isFieldsLoading: false,
    fetchGroupedFields: jest.fn(),
    signatures: [],
    isSignaturesLoading: false,
    fetchSignatures: jest.fn(),
    hierarchyFields: [],
    isHierarchyFieldsLoading: false,
    fetchHierarchyFields: jest.fn(),
    // Add any other missing properties that might be used
    name: "",
    description: "",
    type: "",
    setName: jest.fn(),
    setDescription: jest.fn(),
    setType: jest.fn(),
    saveTemplate: jest.fn(),
    documentTypes: [],
    isDocumentTypesLoading: false,
    documentTypesError: null,
    fetchDocumentTypes: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDocumentTemplateStore.mockReturnValue(mockStore);
  });

  it("should render the component", () => {
    render(<SystemInformation />);
    // Check for the main container div with the expected class
    expect(screen.getByRole("button", { name: /USER/i })).toBeInTheDocument();
  });

  it("should be defined", () => {
    expect(SystemInformation).toBeDefined();
  });
});
