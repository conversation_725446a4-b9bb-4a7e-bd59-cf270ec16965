"use client";
import React from "react";
import TiptapEditor from "./components/TiptapEditor";
import { DocumentHeader } from "./components/DocumentHeader";
import { CircleChevronRight, Download, Loader2 } from "lucide-react";
import { SystemInformation } from "./components/SysthemInformation";
import { IconButton, ReusableButton } from "@/components/common/CustomButtons";
import { useTranslations } from "next-intl";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { CustomImport } from "@/components/common/CustomImport";
import { FileType } from "@/enum/fileTypeEnum";
import { useDocumentTemplateStore } from "../store/documentsStore";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { DocumentTemplate } from "../types/documentsTypes";
import CustomPageCard from "@/components/common/CustomPageCard";

const NewDocumentTemplate = () => {
  const t = useTranslations("documentsTemplates");
  const {
    importDialogOpen,
    setImportDialogOpen,
    file,
    setFile,
    // progress,
    // setProgress,
    isImporting,
    handleFileUpload,
    setName,
    setDescription,
    setType,
    setContent,
    setCurrentSignatureId,
    fetchTemplateById,
  } = useDocumentTemplateStore();
  const [progress, setProgress] = React.useState(0);
  const [isPrefilling, setIsPrefilling] = useState(false);
  const [ready, setReady] = useState(false);
  const [editingOriginalTemplate, setEditingOriginalTemplate] =
    useState<DocumentTemplate | null>(null);

  // Get editTemplate from store
  const editTemplateFromStore = useDocumentTemplateStore(
    (state) => state.editTemplate,
  );
  const searchParams = useSearchParams();
  const editId = searchParams ? searchParams.get("editId") : null;

  const handleDialogOpen = () => setImportDialogOpen(true);
  const handleDialogClose = () => setImportDialogOpen(false);
  const handleCancel = () => {
    setFile(null);
    setImportDialogOpen(false);
  };

  useEffect(() => {
    let cancelled = false;
    async function loadTemplate() {
      setIsPrefilling(true);
      let template = null;
      if (editId) {
        template = await fetchTemplateById(editId);
      }
      if (template && !cancelled) {
        setEditingOriginalTemplate(template);
        setName(template.name || "");
        setDescription(template.description || "");
        setType(template.documentTypeId || template.category || ""); // Use documentTypeId first, fallback to category
        setContent(template.templateContent || "<p></p>");
        setCurrentSignatureId(template.signatureId || null);
      } else if (!cancelled) {
        setName("");
        setDescription("");
        setType("");
        setContent("<p></p>");
        setCurrentSignatureId(null);
        setEditingOriginalTemplate(null);
      }
      setTimeout(() => {
        if (!cancelled) {
          setIsPrefilling(false);
          setReady(true);
        }
      }, 100);
    }
    if (editId) {
      loadTemplate();
    } else {
      setName("");
      setDescription("");
      setType("");
      setContent("<p></p>");
      setCurrentSignatureId(null);
      setEditingOriginalTemplate(null);
      setReady(true);
      setIsPrefilling(false);
    }
    return () => {
      cancelled = true;
    };
  }, [
    editId,
    fetchTemplateById,
    setName,
    setDescription,
    setType,
    setContent,
    setCurrentSignatureId,
  ]);

  return (
    <CustomPageCard>
      {isPrefilling || !ready ? (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-[2px] z-[100] flex items-center justify-center">
          <div className=" p-4 rounded-lg ">
            {" "}
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      ) : (
        <>
          <DocumentHeader
            key={editTemplateFromStore?.id || "empty"}
            editingOriginalTemplate={editingOriginalTemplate}
          />
          <div className="flex flex-1 gap-4 p-4">
            <div className="flex-1 space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium">
                  {t("NewDocumentTemplate.documentTemplateBuilder")}
                </h2>
                <IconButton
                  className="gap-2"
                  icon={Download}
                  label={t("NewDocumentTemplate.ImportDocumentTemplate")}
                  onClick={handleDialogOpen}
                />
              </div>
              <TiptapEditor />
            </div>
            <SystemInformation />
          </div>
        </>
      )}
      <ReusableDialog
        isOpen={importDialogOpen}
        title={t("NewDocumentTemplate.ImportDocumentTemplate")}
        onClose={handleDialogClose}
        id="import-dialog"
        description="Import a document template file by selecting a Word document (.docx) file."
      >
        <CustomImport
          allowedFileTypes={[FileType.WORD, FileType.WORD_OPENXML]}
          file={file}
          progress={progress}
          setFile={setFile}
          setProgress={setProgress}
        />
        <div className="flex items-center justify-between mt-4">
          <ReusableButton
            label={t("NewDocumentTemplate.cancel")}
            color="secondary"
            variant="outline"
            onClick={handleCancel}
            id="cancel-import"
          />
          <IconButton
            label={t("NewDocumentTemplate.upload")}
            icon={CircleChevronRight}
            color="primary"
            onClick={() => file && handleFileUpload(file)}
            disabled={!file || progress < 100}
            isLoading={isImporting}
            id="upload-file"
          />
        </div>
      </ReusableDialog>
    </CustomPageCard>
  );
};

export default NewDocumentTemplate;
