import { create } from "zustand";
import { DocumentTemplate, GroupedFieldsData } from "../types/documentsTypes";
import { toast } from "@/hooks/use-toast";
import React from "react";
import api from "@/lib/axios";

export interface Signature {
  id: string;
  partitionKey: string;
  name: string;
  imageUrl: string;
  sasUrl: string;
  blobName: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  signatureMetadata: {
    originalFileName: string;
    fileSize: string;
    mimeType: string;
    department: string;
  };
  version: number;
}

export interface HierarchyField {
  id: string;
  displayName: string;
  fieldType: string;
  role: string;
  description: string;
  availableFields: string[];
  resolutionStrategy: string;
  createdAt: string;
  updatedAt: string;
  partitionKey: string;
}

export interface DocumentType {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  displayOrder: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Helper function to extract message from backend response
const extractMessageFromResponse = (responseData: unknown): string => {
  if (!responseData || typeof responseData !== "object") {
    return "Operation completed";
  }

  const data = responseData as Record<string, unknown>;

  // Check if response has a message field
  if (data?.message && typeof data.message === "string") {
    return data.message;
  }

  // Check if message is nested in details
  if (
    data?.details &&
    typeof data.details === "object" &&
    data.details !== null
  ) {
    const details = data.details as Record<string, unknown>;
    if (details.message && typeof details.message === "string") {
      return details.message;
    }

    if (details.error && typeof details.error === "string") {
      return details.error;
    }

    if (details.errors && Array.isArray(details.errors) && details.errors[0]) {
      const error = details.errors[0] as Record<string, unknown>;
      if (
        error.field &&
        error.constraints &&
        Array.isArray(error.constraints) &&
        error.constraints[0]
      ) {
        return `${error.field}: ${error.constraints[0]}`;
      }
    }
  }

  // Fallback to a generic message
  return "Operation completed";
};

// Helper function to convert field names to IDs in content
const convertFieldNamesToIds = (
  content: string,
  groupedFields: GroupedFieldsData[],
  hierarchyFields: HierarchyField[],
): string => {
  let convertedContent = content;

  // Create a mapping of field names to IDs
  const fieldNameToIdMap = new Map<string, string>();

  groupedFields.forEach((group) => {
    if (group.fields) {
      group.fields.forEach((field) => {
        fieldNameToIdMap.set(field.name, field.id);
      });
    }
    if (group.forms) {
      group.forms.forEach((form) => {
        form.fields.forEach((field) => {
          fieldNameToIdMap.set(field.name, field.id);
        });
      });
    }
  });

  // Replace field names with IDs in the content
  fieldNameToIdMap.forEach((id, name) => {
    // Escape special regex characters in the field name
    const escapedName = name.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Handle both wrapped {{fieldName}} and unwrapped fieldName
    const wrappedRegex = new RegExp(`\\{\\{${escapedName}\\}\\}`, "g");
    const unwrappedRegex = new RegExp(`\\b${escapedName}\\b`, "g");

    console.log(`Converting field "${name}" to ID "${id}"`);
    console.log(`Wrapped regex: /\\{\\{${escapedName}\\}\\}/g`);
    console.log(`Unwrapped regex: /\\b${escapedName}\\b/g`);

    // First replace wrapped field names
    convertedContent = convertedContent.replace(wrappedRegex, `{{${id}}}`);
    // Then replace unwrapped field names (for backward compatibility)
    convertedContent = convertedContent.replace(unwrappedRegex, `{{${id}}}`);
  });

  // Convert hierarchy field display names to IDs
  hierarchyFields.forEach((hf) => {
    const escapedDisplayName = hf.displayName.replace(
      /[.*+?^${}()|[\]\\]/g,
      "\\$&",
    );
    const hierarchyRegex = new RegExp(
      `\\{\\{hierarchy:${escapedDisplayName}\\.([^}]+)\\}\\}`,
      "g",
    );
    convertedContent = convertedContent.replace(
      hierarchyRegex,
      `{{hierarchy:${hf.id}.$1}}`,
    );
  });

  return convertedContent;
};

// Helper function to convert field IDs to names using fieldIds array from API response
const convertFieldIdsToNamesFromResponse = (
  content: string,
  fieldIds: Array<{ name: string; id: string }>,
  signatures: Signature[],
): string => {
  let convertedContent = content;

  // Convert field IDs to names using the fieldIds from API response
  fieldIds.forEach(({ name, id }) => {
    const regex = new RegExp(`\\{\\{${id}\\}\\}`, "g");
    convertedContent = convertedContent.replace(regex, `{{${name}}}`);
  });

  // Convert signature placeholders back to image tags for display
  signatures.forEach((sig) => {
    const signatureRegex = new RegExp(`\\{\\{signature:${sig.id}\\}\\}`, "g");
    convertedContent = convertedContent.replace(
      signatureRegex,
      `<img src="${sig.imageUrl}" alt="Signature" />`,
    );
  });

  return convertedContent;
};
const convertFieldIdsToNames = (
  content: string,
  groupedFields: GroupedFieldsData[],
  hierarchyFields: HierarchyField[],
): string => {
  let convertedContent = content;

  // Create a mapping of field IDs to names
  const fieldIdToNameMap = new Map<string, string>();

  groupedFields.forEach((group) => {
    if (group.fields) {
      group.fields.forEach((field) => {
        fieldIdToNameMap.set(field.id, field.name);
      });
    }
    if (group.forms) {
      group.forms.forEach((form) => {
        form.fields.forEach((field) => {
          fieldIdToNameMap.set(field.id, field.name);
        });
      });
    }
  });

  // Replace field IDs with names in the content
  fieldIdToNameMap.forEach((name, id) => {
    const regex = new RegExp(`\\{\\{${id}\\}\\}`, "g");
    convertedContent = convertedContent.replace(regex, `{{${name}}}`);
  });

  // Convert hierarchy field IDs to display names
  hierarchyFields.forEach((hf) => {
    const hierarchyRegex = new RegExp(
      `\\{\\{hierarchy:${hf.id}\\.([^}]+)\\}\\}`,
      "g",
    );
    convertedContent = convertedContent.replace(
      hierarchyRegex,
      `{{hierarchy:${hf.displayName}.$1}}`,
    );
  });

  return convertedContent;
};

const convertSignatureImagesToPlaceholders = (
  content: string,
  signatures: Signature[],
): string => {
  let convertedContent = content;
  signatures.forEach((sig) => {
    // Match <img ... src="imageUrl" ...>
    const imgRegex = new RegExp(
      `<img[^>]*src=["']${sig.imageUrl.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}["'][^>]*>`,
      "g",
    );
    convertedContent = convertedContent.replace(
      imgRegex,
      `{{signature:${sig.id}}}`,
    );

    // Also match anchor tags containing the signature URL
    const anchorRegex = new RegExp(
      `<a[^>]*href=["']${sig.imageUrl.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}["'][^>]*>.*?</a>`,
      "g",
    );
    convertedContent = convertedContent.replace(
      anchorRegex,
      `{{signature:${sig.id}}}`,
    );

    // Also match plain URLs wrapped in {{}}
    const urlRegex = new RegExp(
      `\\{\\{${sig.imageUrl.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\}\\}`,
      "g",
    );
    convertedContent = convertedContent.replace(
      urlRegex,
      `{{signature:${sig.id}}}`,
    );
  });
  return convertedContent;
};

// Helper function to extract field IDs from content
const extractFieldIdsFromContent = (
  content: string,
  groupedFields: GroupedFieldsData[],
): Array<{ name: string; id: string }> => {
  const fieldIds: Array<{ name: string; id: string }> = [];

  // Create a mapping of field IDs to names
  const fieldIdToNameMap = new Map<string, string>();

  groupedFields.forEach((group) => {
    if (group.fields) {
      group.fields.forEach((field) => {
        fieldIdToNameMap.set(field.id, field.name);
      });
    }
    if (group.forms) {
      group.forms.forEach((form) => {
        form.fields.forEach((field) => {
          fieldIdToNameMap.set(field.id, field.name);
        });
      });
    }
  });

  // Find all field placeholders in content: {{fieldId}}
  const fieldRegex = /\{\{([^}]+)\}\}/g;
  let match;

  while ((match = fieldRegex.exec(content)) !== null) {
    const fieldId = match[1];
    // Skip signature placeholders
    if (!fieldId.startsWith("signature:")) {
      const fieldName = fieldIdToNameMap.get(fieldId);
      if (fieldName) {
        fieldIds.push({ name: fieldName, id: fieldId });
      }
    }
  }

  return fieldIds;
};

interface ValidationError {
  code: string;
  message: string;
  statusCode: number;
  timestamp: string;
  details?: {
    errors: Array<{
      field: string;
      value: string;
      constraints: string[];
    }>;
  };
  path: string;
}

interface ApiError {
  response?: {
    data: ValidationError;
  };
}

interface DocumentTemplateState {
  name: string;
  description: string;
  type: string;
  templateContent: string;
  currentSignatureId: string | null;
  setName: (name: string) => void;
  setDescription: (description: string) => void;
  setType: (type: string) => void;
  setContent: (templateContent: string) => void;
  setCurrentSignatureId: (signatureId: string | null) => void;
  saveTemplate: (
    originalTemplate?: DocumentTemplate | null,
    documentTypeName?: string,
  ) => Promise<void>;
  loadTemplate: (id: string) => Promise<void>;
  fetchTemplateById: (id: string) => Promise<DocumentTemplate | null>;
  deleteTemplate: (id: string) => Promise<void>;

  // Table data and pagination
  data: {
    items: DocumentTemplate[];
    meta: {
      pageSize: number;
      currentPage: number;
      totalPages: number;
      totalCount: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  };
  isLoading: boolean;
  fetchPaginatedData: (
    pageIndex: number,
    pageSize: number,
    sortBy: string,
    sortOrder: string,
    country?: string,
    tenantId?: string,
  ) => Promise<void>;
  searchTemplates: (
    search: string,
    pageIndex: number,
    pageSize: number,
    sortBy?: string,
    sortOrder?: string,
    country?: string,
    tenantId?: string,
  ) => Promise<void>;

  // DOCX import logic
  importDialogOpen: boolean;
  file: File | null;
  progress: number;
  isImporting: boolean;
  setImportDialogOpen: (open: boolean) => void;
  setFile: (file: File | null) => void;
  setProgress: React.Dispatch<React.SetStateAction<number>>;
  setIsImporting: (loading: boolean) => void;
  uploadDocx: () => Promise<void>;
  handleFileUpload: (file: File) => Promise<void>;

  // Grouped fields logic
  groupedFields: GroupedFieldsData[];
  isFieldsLoading: boolean;
  fieldsError: string | null;
  fetchGroupedFields: () => Promise<void>;

  signatures: Signature[];
  isSignaturesLoading: boolean;
  signaturesError: string | null;
  fetchSignatures: () => Promise<void>;

  hierarchyFields: HierarchyField[];
  isHierarchyFieldsLoading: boolean;
  hierarchyFieldsError: string | null;
  fetchHierarchyFields: () => Promise<void>;

  documentTypes: DocumentType[];
  isDocumentTypesLoading: boolean;
  documentTypesError: string | null;
  fetchDocumentTypes: () => Promise<void>;

  editTemplate: DocumentTemplate | null;
  setEditTemplate: (template: DocumentTemplate) => void;
  clearEditTemplate: () => void;
}

export const useDocumentTemplateStore = create<DocumentTemplateState>(
  (set, get) => ({
    name: "",
    description: "",
    type: "",
    templateContent: "",
    currentSignatureId: null,
    setName: (name) => set({ name }),
    setDescription: (description) => set({ description }),
    setType: (type) => set({ type }),
    setContent: (templateContent) => set({ templateContent }),
    setCurrentSignatureId: (signatureId) =>
      set({ currentSignatureId: signatureId }),
    saveTemplate: async (originalTemplate, documentTypeName) => {
      const {
        name,
        description,
        type,
        templateContent,
        currentSignatureId,
        groupedFields,
        signatures,
        hierarchyFields,
      } = get();

      // 1. Convert signature images to placeholders
      const contentWithPlaceholders = convertSignatureImagesToPlaceholders(
        templateContent,
        signatures,
      );

      // 2. Convert field names to IDs as before
      const convertedContent = convertFieldNamesToIds(
        contentWithPlaceholders,
        groupedFields,
        hierarchyFields,
      );

      // 3. Extract field IDs from the converted content
      const fieldIds = extractFieldIdsFromContent(
        convertedContent,
        groupedFields,
      );

      const wrappedContent = `<html><body>${convertedContent}</body></html>`;

      try {
        if (originalTemplate?.id) {
          // Edit mode - use PUT endpoint with payload without createdBy
          const putPayload: DocumentTemplate = {
            name,
            description,
            documentTypeId: type, // Send document type ID in documentTypeId field
            documentTypeName: documentTypeName || "", // Send document type name
            templateContent: wrappedContent,
            // Use original values if editing, otherwise use defaults
            country: originalTemplate?.country || "US",
            region: originalTemplate?.region || "NA-EAST",
            tenantId: originalTemplate?.tenantId || "aptiv-us-manufacturing",
            templateVersion: originalTemplate?.templateVersion || "v1.0",
            fieldIds: fieldIds,
            signatureId: currentSignatureId || undefined,
            // category: "HR", // <-- Added fixed category (commented out for now)
          };

          await api.put(
            `/document-template/templates/${originalTemplate.id}`,
            putPayload,
            {
              headers: { "Content-Type": "application/json" },
            },
          );
        } else {
          // Create mode - use POST endpoint with payload including createdBy
          const postPayload: DocumentTemplate = {
            name,
            description,
            documentTypeId: type, // Send document type ID in documentTypeId field
            documentTypeName: documentTypeName || "", // Send document type name
            templateContent: wrappedContent,
            // Use original values if editing, otherwise use defaults
            country: originalTemplate?.country || "US",
            region: originalTemplate?.region || "NA-EAST",
            tenantId: originalTemplate?.tenantId || "aptiv-us-manufacturing",
            createdBy:
              originalTemplate?.createdBy ||
              "123e9356-e89b-12d3-a456-426614178963",
            templateVersion: originalTemplate?.templateVersion || "v1.0",
            fieldIds: fieldIds,
            signatureId: currentSignatureId || undefined,
            // category: "HR", // <-- Added fixed category (commented out for now)
          };

          await api.post("/document-template/templates", postPayload, {
            headers: { "Content-Type": "application/json" },
          });
        }

        toast({
          title: "Success",
          description: "Template saved successfully.",
          variant: "success",
        });

        // Redirect to documents templates page
        if (typeof window !== "undefined") {
          window.location.href = "/documents-templates";
        }
      } catch (error) {
        let errorMessage = "Failed to save template.";
        const apiError = error as ApiError;

        if (apiError.response?.data) {
          errorMessage = extractMessageFromResponse(apiError.response.data);
        } else if (error instanceof Error && error.message) {
          errorMessage = error.message;
        }

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },

    // Table data and pagination
    data: {
      items: [],
      meta: {
        pageSize: 5,
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    },
    isLoading: false,

    fetchPaginatedData: async (
      pageIndex: number,
      pageSize: number,
      sortBy: string,
      sortOrder: string,
      country: string = "US",
      tenantId: string = "aptiv-us-manufacturing",
    ) => {
      set({ isLoading: true });
      try {
        const response = await api.get(`/document-template/templates`, {
          params: {
            pageNumber: pageIndex + 1,
            pageSize,
            sortBy: sortBy || "createdAt",
            sortOrder: sortOrder || "desc",
            country,
            tenantId,
          },
        });

        if (response.data && response.data.items) {
          set({
            data: {
              items: response.data.items,
              meta: {
                pageSize: response.data.meta.pageSize,
                currentPage: response.data.meta.currentPage,
                totalPages: response.data.meta.totalPages,
                totalCount: response.data.meta.totalCount,
                hasNextPage: response.data.meta.hasNextPage,
                hasPreviousPage: response.data.meta.hasPreviousPage,
              },
            },
            isLoading: false,
          });
        } else {
          console.error("Invalid response format:", response.data);
          set({ isLoading: false });
          toast({
            title: "Error",
            description: extractMessageFromResponse({
              message: "Invalid response format from server",
            }),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Failed to fetch paginated data:", error);
        set({ isLoading: false });
        const apiError = error as ApiError;
        toast({
          title: "Error",
          description: extractMessageFromResponse(
            apiError.response?.data || {
              message: "Failed to fetch paginated data",
            },
          ),
          variant: "destructive",
        });
      }
    },

    searchTemplates: async (
      search: string,
      pageIndex: number,
      pageSize: number,
      sortBy = "createdAt",
      sortOrder = "desc",
      country: string = "US",
      tenantId: string = "aptiv-us-manufacturing",
    ) => {
      set({ isLoading: true });
      try {
        const response = await api.get("/document-template/templates", {
          params: {
            search,
            pageNumber: pageIndex + 1,
            pageSize,
            sortBy,
            sortOrder,
            country,
            tenantId,
          },
        });

        if (response.data && response.data.items) {
          set({
            data: {
              items: response.data.items,
              meta: {
                pageSize: response.data.meta.pageSize,
                currentPage: response.data.meta.currentPage,
                totalPages: response.data.meta.totalPages,
                totalCount: response.data.meta.totalCount,
                hasNextPage: response.data.meta.hasNextPage,
                hasPreviousPage: response.data.meta.hasPreviousPage,
              },
            },
            isLoading: false,
          });
        } else {
          console.error("Invalid search response format:", response.data);
          set({ isLoading: false });
          toast({
            title: "Error",
            description: extractMessageFromResponse({
              message: "Invalid response format from search",
            }),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Failed to search templates:", error);
        set({ isLoading: false });
        const apiError = error as ApiError;
        toast({
          title: "Error",
          description: extractMessageFromResponse(
            apiError.response?.data || {
              message: "Failed to search templates",
            },
          ),
          variant: "destructive",
        });
      }
    },

    // DOCX import logic
    importDialogOpen: false,
    file: null,
    progress: 0,
    isImporting: false,
    setImportDialogOpen: (open) => set({ importDialogOpen: open }),
    setFile: (file) => set({ file }),
    setProgress: (value) =>
      set((state) => ({
        progress: typeof value === "function" ? value(state.progress) : value,
      })),
    setIsImporting: (loading) => set({ isImporting: loading }),
    uploadDocx: async () => {
      const {
        file,
        setContent,
        setImportDialogOpen,
        setFile,
        setProgress,
        setIsImporting,
        groupedFields,
        hierarchyFields,
      } = get();
      if (!file) return;
      setIsImporting(true);
      const formData = new FormData();
      formData.append("file", file);
      setProgress(10);
      try {
        const response = await api.post(
          "/document-template/docx-import",
          formData,
          {
            headers: { "Content-Type": "multipart/form-data" },
          },
        );
        setProgress(80);
        if (response.status === 200) {
          const data = response.data;
          // Convert any field IDs back to names for display in editor
          const convertedContent = convertFieldIdsToNames(
            data.htmlContent,
            groupedFields,
            hierarchyFields,
          );
          setContent(convertedContent); // Set the converted content in the editor
          setProgress(100);
          setTimeout(() => setProgress(0), 500);
          setImportDialogOpen(false);
          setFile(null);
          toast({
            title: "Success",
            description: "Document imported successfully.",
            variant: "success",
          });
        } else {
          setProgress(0);
          toast({
            title: "Error",
            description: "Failed to import document.",
            variant: "destructive",
          });
        }
      } catch {
        setProgress(0);
        toast({
          title: "Error",
          description: "An error occurred during import.",
          variant: "destructive",
        });
      } finally {
        setIsImporting(false);
      }
    },
    handleFileUpload: async (file: File) => {
      const { setFile, uploadDocx } = get();
      setFile(file);
      await uploadDocx();
    },

    // Grouped fields logic
    groupedFields: [],
    isFieldsLoading: false,
    fieldsError: null,
    fetchGroupedFields: async () => {
      set({ isFieldsLoading: true, fieldsError: null });
      try {
        const response = await api.get(
          "/document-template/fields/grouped-by-source",
        );

        if (response.data && response.data.data) {
          set({
            groupedFields: response.data.data,
            isFieldsLoading: false,
          });
        } else {
          set({
            isFieldsLoading: false,
            fieldsError: "Invalid response format from grouped fields endpoint",
          });
          toast({
            title: "Error",
            description: "Invalid response format from grouped fields endpoint",
            variant: "destructive",
          });
        }
      } catch {
        set({ isFieldsLoading: false, fieldsError: "Could not load fields." });
        toast({
          title: "Error",
          description: "Failed to fetch grouped fields",
          variant: "destructive",
        });
      }
    },

    signatures: [],
    isSignaturesLoading: false,
    signaturesError: null,
    fetchSignatures: async () => {
      set({ isSignaturesLoading: true, signaturesError: null });
      try {
        const response = await api.get("/document-template/signatures");
        if (Array.isArray(response.data)) {
          set({ signatures: response.data, isSignaturesLoading: false });
        } else {
          set({
            isSignaturesLoading: false,
            signaturesError: "Invalid response format from signatures endpoint",
          });
          toast({
            title: "Error",
            description: "Invalid response format from signatures endpoint",
            variant: "destructive",
          });
        }
      } catch {
        set({
          isSignaturesLoading: false,
          signaturesError: "Could not load signatures.",
        });
        toast({
          title: "Error",
          description: "Failed to fetch signatures",
          variant: "destructive",
        });
      }
    },

    hierarchyFields: [],
    isHierarchyFieldsLoading: false,
    hierarchyFieldsError: null,
    fetchHierarchyFields: async () => {
      set({ isHierarchyFieldsLoading: true, hierarchyFieldsError: null });
      try {
        const response = await api.get("/document-template/hierarchy-fields");
        if (Array.isArray(response.data)) {
          set({
            hierarchyFields: response.data,
            isHierarchyFieldsLoading: false,
          });
        } else {
          set({
            isHierarchyFieldsLoading: false,
            hierarchyFieldsError:
              "Invalid response format from hierarchy fields endpoint",
          });
          toast({
            title: "Error",
            description:
              "Invalid response format from hierarchy fields endpoint",
            variant: "destructive",
          });
        }
      } catch {
        set({
          isHierarchyFieldsLoading: false,
          hierarchyFieldsError: "Could not load hierarchy fields.",
        });
        toast({
          title: "Error",
          description: "Failed to fetch hierarchy fields",
          variant: "destructive",
        });
      }
    },

    loadTemplate: async (id: string) => {
      set({ isLoading: true });
      try {
        const response = await api.get(`/document-template/templates/${id}`);

        if (response.data && response.data.template) {
          const template = response.data.template;
          const { signatures } = get();

          // Convert field IDs to names for display in editor using fieldIds from API response
          const convertedContent = convertFieldIdsToNamesFromResponse(
            template.templateContent,
            template.fieldIds || [],
            signatures,
          );

          set({
            name: template.name,
            description: template.description,
            type: template.documentTypeId || template.type, // Use documentTypeId if available, fallback to type
            templateContent: convertedContent,
            isLoading: false,
          });
        } else {
          console.error("Invalid template response format:", response.data);
          set({ isLoading: false });
          toast({
            title: "Error",
            description: extractMessageFromResponse({
              message: "Invalid response format from template endpoint",
            }),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Failed to load template:", error);
        set({ isLoading: false });
        const apiError = error as ApiError;
        toast({
          title: "Error",
          description: extractMessageFromResponse(
            apiError.response?.data || { message: "Failed to load template" },
          ),
          variant: "destructive",
        });
      }
    },

    documentTypes: [],
    isDocumentTypesLoading: false,
    documentTypesError: null,
    fetchDocumentTypes: async () => {
      set({ isDocumentTypesLoading: true, documentTypesError: null });
      try {
        const response = await api.get("/document-template/document-types");
        if (Array.isArray(response.data)) {
          // Filter only active document types and sort by displayOrder
          const activeDocumentTypes = response.data
            .filter((docType: DocumentType) => docType.isActive)
            .sort(
              (a: DocumentType, b: DocumentType) =>
                a.displayOrder - b.displayOrder,
            );
          set({
            documentTypes: activeDocumentTypes,
            isDocumentTypesLoading: false,
          });
        } else {
          set({
            isDocumentTypesLoading: false,
            documentTypesError:
              "Invalid response format from document types endpoint",
          });
          toast({
            title: "Error",
            description: "Invalid response format from document types endpoint",
            variant: "destructive",
          });
        }
      } catch {
        set({
          isDocumentTypesLoading: false,
          documentTypesError: "Could not load document types.",
        });
        toast({
          title: "Error",
          description: "Failed to fetch document types",
          variant: "destructive",
        });
      }
    },

    editTemplate: null,
    setEditTemplate: (template) => set({ editTemplate: template }),
    clearEditTemplate: () => set({ editTemplate: null }),
    fetchTemplateById: async (id: string) => {
      try {
        const response = await api.get(`/document-template/templates/${id}`);
        const template = response.data.template || response.data;

        if (template && template.templateContent && template.fieldIds) {
          const { signatures } = get();
          // Convert field IDs to names for display
          const convertedContent = convertFieldIdsToNamesFromResponse(
            template.templateContent,
            template.fieldIds || [],
            signatures,
          );

          return {
            ...template,
            templateContent: convertedContent,
          };
        }

        return template;
      } catch {
        return null;
      }
    },
    deleteTemplate: async (id: string) => {
      try {
        await api.delete(`/document-template/templates/${id}`);
        toast({
          title: "Success",
          description: "Template deleted successfully.",
          variant: "success",
        });
        // Optionally, refresh the list of templates
        get().fetchPaginatedData(0, 5, "createdAt", "desc");
      } catch (error) {
        let errorMessage = "Failed to delete template.";
        const apiError = error as ApiError;

        if (apiError.response?.data) {
          errorMessage = extractMessageFromResponse(apiError.response.data);
        } else if (error instanceof Error && error.message) {
          errorMessage = error.message;
        }

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
  }),
);
