/// <reference types="jest" />

import "@testing-library/jest-dom";
import React from "react";

// Jest setup file for documents-templates tests

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/",
}));

// Mock Next.js internationalization
jest.mock("next-intl", () => ({
  useTranslations: jest.fn(() => (key: string) => key),
  useLocale: () => "en",
  getTranslations: () => (key: string) => key,
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
    readText: jest.fn(),
  },
});

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = (...args: unknown[]) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("Warning: ReactDOM.render is no longer supported")
    ) {
      return;
    }
    originalConsoleError.call(console, ...args);
  };

  console.warn = (...args: unknown[]) => {
    if (
      typeof args[0] === "string" &&
      (args[0].includes("Warning: componentWillReceiveProps") ||
        args[0].includes("Warning: componentWillUpdate"))
    ) {
      return;
    }
    originalConsoleWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Extend global types for test utilities
declare global {
  var testUtils: {
    createMockFile: (name: string, content: string, type: string) => File;
    waitFor: (ms: number) => Promise<void>;
    mockApiResponse: (
      data: unknown,
      status?: number,
    ) => {
      data: unknown;
      status: number;
      statusText: string;
      headers: Record<string, unknown>;
      config: Record<string, unknown>;
    };
    mockApiError: (
      message: string,
      status?: number,
    ) => {
      response: {
        data: {
          message: string;
          statusCode: number;
        };
        status: number;
        statusText: string;
      };
    };
  };
}

// Global test utilities
global.testUtils = {
  // Helper to create mock files
  createMockFile: (name: string, content: string, type: string) => {
    return new File([content], name, { type });
  },

  // Helper to wait for async operations
  waitFor: (ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),

  // Helper to mock API responses
  mockApiResponse: (data: unknown, status = 200) => ({
    data,
    status,
    statusText: "OK",
    headers: {},
    config: {},
  }),

  // Helper to mock API errors
  mockApiError: (message: string, status = 400) => ({
    response: {
      data: {
        message,
        statusCode: status,
      },
      status,
      statusText: "Bad Request",
    },
  }),
};

// Mock IconButton from CustomButtons
jest.mock("@/components/common/CustomButtons", () => ({
  __esModule: true,
  IconButton: (props: Record<string, unknown>) => {
    const { loading, label, ...validProps } = props;
    return React.createElement(
      "button",
      {
        ...validProps,
        "data-testid": "icon-button",
        "aria-label": label as string,
        // Convert loading to string to avoid React warning
        loading: loading ? "true" : undefined,
      },
      (label as string) || "IconButton",
    );
  },
  ReusableButton: (props: Record<string, unknown>) => {
    const { label, ...validProps } = props;
    return React.createElement(
      "button",
      {
        ...validProps,
        "data-testid": "reusable-button",
        "aria-label": label as string,
      },
      (label as string) || "ReusableButton",
    );
  },
}));

// Mock DataTable component
jest.mock("@/components/common/tables/DataTable", () => ({
  __esModule: true,
  DataTable: (props: Record<string, unknown>) => {
    // Filter out invalid DOM props to prevent console errors
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const {
      columns,
      data,
      buttons,
      currentPage,
      pageSize,
      isLoading,
      searchValue,
      sorting,
      totalItems,
      ...validProps
    } = props;
    /* eslint-enable @typescript-eslint/no-unused-vars */

    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "datatable",
      },
      "DataTable",
    );
  },
}));

// Mock CellActions component
jest.mock("@/components/common/CellActions", () => ({
  __esModule: true,
  default: (props: Record<string, unknown>) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { actions: _actions, ...validProps } = props;
    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "cell-actions",
      },
      "CellActions",
    );
  },
}));

// Mock CustomIcon component
jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: (props: Record<string, unknown>) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { name: _name, style: _style, ...validProps } = props;
    return React.createElement(
      "span",
      {
        ...validProps,
        "data-testid": "custom-icon",
      },
      "Icon",
    );
  },
}));

// Mock DeleteDialog component
jest.mock("@/components/common/DeleteDialog", () => ({
  __esModule: true,
  default: (props: Record<string, unknown>) => {
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const {
      isDialogOpen,
      setIsDialogOpen,
      handleDelete,
      labelCancel,
      labelConfirm,
      loading,
      children,
      ...validProps
    } = props;
    /* eslint-enable @typescript-eslint/no-unused-vars */
    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "delete-dialog",
      },
      "DeleteDialog",
    );
  },
}));

// Mock TiptapEditor component
jest.mock("../new-document-template/components/TiptapEditor", () => ({
  __esModule: true,
  default: (props: Record<string, unknown>) => {
    const { ...validProps } = props;
    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "tiptap-editor",
      },
      "TiptapEditor",
    );
  },
}));

// Mock SystemInformation component
jest.mock("../new-document-template/components/SysthemInformation", () => ({
  __esModule: true,
  SystemInformation: (props: Record<string, unknown>) => {
    const { ...validProps } = props;
    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "system-information",
      },
      "SystemInformation",
    );
  },
}));

// Mock ReusableDialog component
jest.mock("@/components/common/CustomDialog", () => ({
  __esModule: true,
  ReusableDialog: (props: Record<string, unknown>) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { isOpen, title, onClose, id, description, children, ...validProps } =
      props;
    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "reusable-dialog",
      },
      "ReusableDialog",
    );
  },
}));

// Mock CustomImport component
jest.mock("@/components/common/CustomImport", () => ({
  __esModule: true,
  CustomImport: (props: Record<string, unknown>) => {
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const {
      allowedFileTypes,
      file,
      progress,
      setFile,
      setProgress,
      ...validProps
    } = props;
    /* eslint-enable @typescript-eslint/no-unused-vars */
    return React.createElement(
      "div",
      {
        ...validProps,
        "data-testid": "custom-import",
      },
      "CustomImport",
    );
  },
}));

// Mock FileType enum
jest.mock("@/enum/fileTypeEnum", () => ({
  __esModule: true,
  FileType: {
    WORD: "application/msword",
    WORD_OPENXML:
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  },
}));
