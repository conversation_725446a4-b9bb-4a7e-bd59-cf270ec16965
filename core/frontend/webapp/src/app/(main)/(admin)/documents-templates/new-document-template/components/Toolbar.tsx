import { Editor } from "@tiptap/react";
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Heading1,
  Heading2,
  Heading3,
} from "lucide-react";
import { useState, useEffect } from "react";

type ToolbarProps = {
  editor: Editor | null;
};

const ToolbarButton = ({
  icon: Icon,
  onClick,
  isActive,
  disabled,
}: {
  icon: React.FC<{ className: string }>;
  onClick: () => void;
  isActive?: boolean;
  disabled?: boolean;
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`p-2 rounded transition-colors ${
      isActive ? "bg-gray-200" : "hover:bg-gray-100"
    }`}
  >
    <Icon className="w-5 h-5" />
  </button>
);

const Toolbar = ({ editor }: ToolbarProps) => {
  const [currentFontFamily, setCurrentFontFamily] = useState("");

  useEffect(() => {
    if (editor) {
      const updateFontFamily = () => {
        const fontFamily = editor.getAttributes("textStyle").fontFamily || "";
        setCurrentFontFamily(fontFamily);
      };

      editor.on("selectionUpdate", updateFontFamily);
      editor.on("focus", updateFontFamily);

      // Initial update
      updateFontFamily();

      return () => {
        editor.off("selectionUpdate", updateFontFamily);
        editor.off("focus", updateFontFamily);
      };
    }
  }, [editor]);

  if (!editor) return null;

  // Standard block-level alignment handler
  const handleAlign = (align: "left" | "center" | "right" | "justify") => {
    if (!editor) return;
    editor.chain().focus().setTextAlign(align).run();
  };

  const isActive = (
    name: string | Record<string, unknown>,
    attributes?: Record<string, unknown>,
  ) => {
    if (typeof name === "string") {
      return editor.isActive(name, attributes || {});
    }
    return editor.isActive(name);
  };

  return (
    <div className="border-b border-gray-300 p-2 flex flex-wrap gap-2 items-center">
      {/* Font Family Dropdown */}
      <select
        className="p-2 rounded border border-gray-300 mr-2 hover:bg-gray-100"
        value={currentFontFamily}
        onChange={(e) => {
          const value = e.target.value;
          if (value) {
            editor.chain().focus().setFontFamily(value).run();
          } else {
            editor.chain().focus().unsetFontFamily().run();
          }
        }}
      >
        <option value="">Default</option>
        <option value="Arial, sans-serif">Arial</option>
        <option value="Times New Roman, serif">Times New Roman</option>
        <option value="Courier New, monospace">Courier New</option>
        <option value="Georgia, serif">Georgia</option>
        <option value="Verdana, sans-serif">Verdana</option>
        <option value="Tahoma, sans-serif">Tahoma</option>
        <option value="Impact, fantasy">Impact</option>
      </select>
      <ToolbarButton
        icon={Heading1}
        onClick={() => {
          console.log("H1 clicked");
          // Ensure we're working with the current block only
          editor.chain().focus().toggleHeading({ level: 1 }).run();
        }}
        isActive={isActive("heading", { level: 1 })}
        disabled={
          !editor.can().chain().focus().toggleHeading({ level: 1 }).run()
        }
      />
      <ToolbarButton
        icon={Heading2}
        onClick={() => {
          console.log("H2 clicked");
          // Ensure we're working with the current block only
          editor.chain().focus().toggleHeading({ level: 2 }).run();
        }}
        isActive={isActive("heading", { level: 2 })}
        disabled={
          !editor.can().chain().focus().toggleHeading({ level: 2 }).run()
        }
      />
      <ToolbarButton
        icon={Heading3}
        onClick={() => {
          console.log("H3 clicked");
          // Ensure we're working with the current block only
          editor.chain().focus().toggleHeading({ level: 3 }).run();
        }}
        isActive={isActive("heading", { level: 3 })}
        disabled={
          !editor.can().chain().focus().toggleHeading({ level: 3 }).run()
        }
      />
      <ToolbarButton
        icon={Bold}
        onClick={() => editor.chain().focus().toggleBold().run()}
        isActive={isActive("bold")}
        disabled={!editor.can().chain().focus().toggleBold().run()}
      />
      <ToolbarButton
        icon={Italic}
        onClick={() => editor.chain().focus().toggleItalic().run()}
        isActive={isActive("italic")}
        disabled={!editor.can().chain().focus().toggleItalic().run()}
      />
      <ToolbarButton
        icon={Underline}
        onClick={() => editor.chain().focus().toggleUnderline().run()}
        isActive={isActive("underline")}
        disabled={!editor.can().chain().focus().toggleUnderline().run()}
      />
      <ToolbarButton
        icon={Strikethrough}
        onClick={() => editor.chain().focus().toggleStrike().run()}
        isActive={isActive("strike")}
        disabled={!editor.can().chain().focus().toggleStrike().run()}
      />
      <ToolbarButton
        icon={List}
        onClick={() => {
          console.log("Bullet list clicked");
          editor.commands.toggleBulletList();
        }}
        isActive={isActive("bulletList")}
        disabled={!editor.can().toggleBulletList()}
      />
      <ToolbarButton
        icon={ListOrdered}
        onClick={() => {
          console.log("Ordered list clicked");
          editor.commands.toggleOrderedList();
        }}
        isActive={isActive("orderedList")}
        disabled={!editor.can().toggleOrderedList()}
      />
      <ToolbarButton
        icon={Quote}
        onClick={() => {
          console.log("Blockquote clicked");
          editor.commands.toggleBlockquote();
        }}
        isActive={isActive("blockquote")}
        disabled={!editor.can().toggleBlockquote()}
      />
      <ToolbarButton
        icon={Undo}
        onClick={() => editor.chain().focus().undo().run()}
      />
      <ToolbarButton
        icon={Redo}
        onClick={() => editor.chain().focus().redo().run()}
      />
      <ToolbarButton
        icon={AlignLeft}
        onClick={() => handleAlign("left")}
        isActive={isActive({ textAlign: "left" })}
      />
      <ToolbarButton
        icon={AlignCenter}
        onClick={() => handleAlign("center")}
        isActive={isActive({ textAlign: "center" })}
      />
      <ToolbarButton
        icon={AlignRight}
        onClick={() => handleAlign("right")}
        isActive={isActive({ textAlign: "right" })}
      />
      <ToolbarButton
        icon={AlignJustify}
        onClick={() => handleAlign("justify")}
        isActive={isActive({ textAlign: "justify" })}
      />
      <input
        type="color"
        onInput={(event) =>
          editor
            .chain()
            .focus()
            .setColor((event.target as HTMLInputElement).value)
            .run()
        }
        value={editor.getAttributes("textStyle").color || "#000000"}
        className="w-6 h-6 border-2 border-gray-300"
      />
    </div>
  );
};

export default Toolbar;
