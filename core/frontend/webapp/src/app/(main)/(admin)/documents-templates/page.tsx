"use client";
import CellActions from "@/components/common/CellActions";
import { DataTable } from "@/components/common/tables/DataTable";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useDocumentTemplateStore } from "./store/documentsStore";
import { SortingState, Row } from "@tanstack/react-table";
import { DocumentTemplate } from "./types/documentsTypes";
import CustomIcon from "@/components/common/CustomIcons";
import DeleteDialog from "@/components/common/DeleteDialog";
import CustomPageCard from "@/components/common/CustomPageCard";

const DocumentsTemplates = () => {
  const t = useTranslations("documentsTemplates");
  const router = useRouter();

  const {
    data,
    fetchPaginatedData,
    searchTemplates,
    isLoading,
    deleteTemplate,
  } = useDocumentTemplateStore();

  const [paginationState, setPaginationState] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] =
    useState<DocumentTemplate | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (searchQuery) {
          await searchTemplates(
            searchQuery,
            paginationState.pageIndex,
            paginationState.pageSize,
            sorting[0]?.id?.replace("column-", "") || "createdAt",
            sorting[0]?.desc ? "desc" : "asc",
          );
        } else {
          await fetchPaginatedData(
            paginationState.pageIndex,
            paginationState.pageSize,
            sorting[0]?.id?.replace("column-", "") || "createdAt",
            sorting[0]?.desc ? "desc" : "asc",
          );
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  }, [
    searchQuery,
    paginationState.pageIndex,
    paginationState.pageSize,
    sorting,
    searchTemplates,
    fetchPaginatedData,
  ]);

  const handlePaginationChange = async (
    pageIndex: number,
    pageSize: number,
  ) => {
    try {
      setPaginationState({ pageIndex, pageSize });
      if (searchQuery) {
        await searchTemplates(
          searchQuery,
          pageIndex,
          pageSize,
          sorting[0]?.id?.replace("column-", "") || "createdAt",
          sorting[0]?.desc ? "desc" : "asc",
        );
      } else {
        await fetchPaginatedData(
          pageIndex,
          pageSize,
          sorting[0]?.id?.replace("column-", "") || "createdAt",
          sorting[0]?.desc ? "desc" : "asc",
        );
      }
    } catch (error) {
      console.error("Error handling pagination:", error);
    }
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setPaginationState((prev) => ({ ...prev, pageIndex: 0 }));
  };

  const handleEdit = (template: DocumentTemplate) => {
    localStorage.setItem("editingTemplate", JSON.stringify(template));
    router.push(
      `/documents-templates/new-document-template?editId=${template.id}`,
    );
  };

  const handleDelete = (template: DocumentTemplate) => {
    setTemplateToDelete(template);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!templateToDelete?.id) return;

    setIsDeleting(true);
    try {
      await deleteTemplate(templateToDelete.id);
      setIsDeleteDialogOpen(false);
      setTemplateToDelete(null);
    } catch (error) {
      console.error("Error deleting template:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: t("home.title"),
      enableSorting: true,
      id: "column-name",
    },
    {
      accessorKey: "documentTypeName",
      header: t("home.documentType"),
      enableSorting: true,
      id: "column-documentTypeName",
    },
    {
      accessorKey: "description",
      header: t("home.discription"),
      enableSorting: true,
      id: "column-description",
    },
    {
      accessorKey: "createdAt",
      header: t("home.createdDate"),
      enableSorting: true,
      id: "column-createdAt",
      cell: ({ row }: { row: Row<DocumentTemplate> }) => {
        if (row.original.createdAt) {
          const date = new Date(row.original.createdAt);
          return date.toLocaleDateString();
        }
        return "";
      },
    },
    {
      accessorKey: "actions",
      header: t("home.actions"),
      cell: ({ row }: { row: Row<DocumentTemplate> }) => (
        <div>
          <CellActions
            actions={[
              {
                label: t("home.edit"),
                onClick: () => handleEdit(row.original),
              },
              {
                label: t("home.delete"),
                onClick: () => handleDelete(row.original),
              },
            ]}
          />
        </div>
      ),
    },
  ];

  const handleNavigate = () => {
    try {
      router.push("/documents-templates/new-document-template");
    } catch (error) {
      console.error("Navigation failed:", error);
    }
  };

  const createIcon = () => (
    <span className="flex items-center">
      <CustomIcon
        name="addEvent"
        style={{
          width: "16px",
          height: "16px",
          fill: "#FFFFFF",
        }}
      />
    </span>
  );

  return (
    <CustomPageCard>
      {/* <div className="mb-4 text-lg font-semibold">
          {t("home.title")} ({data.meta.totalCount || 0})
        </div> */}
      <DataTable
        columns={columns}
        data={data.items || []}
        showSearchBar
        serverPagination
        serverSearch
        onSearchChange={handleSearchChange}
        onPaginationChange={handlePaginationChange}
        totalItems={data.meta.totalCount}
        sorting={sorting}
        onSortingChange={setSorting}
        currentPage={data.meta.currentPage - 1}
        pageSize={data.meta.pageSize}
        isLoading={isLoading}
        searchValue={searchQuery}
        buttons={[
          {
            show: true,
            label: t("home.Addbutton"),
            onClick: handleNavigate,
            variant: "default",
            icon: createIcon,
          },
        ]}
      />
      <DeleteDialog
        isDialogOpen={isDeleteDialogOpen}
        setIsDialogOpen={setIsDeleteDialogOpen}
        handleDelete={confirmDelete}
        labelCancel={t("NewDocumentTemplate.cancel")}
        labelConfirm={t("home.delete")}
        loading={isDeleting}
      >
        <div className="text-center">
          <p className="text-lg font-medium mb-2">
            {t("home.deleteConfirmation")}
          </p>
          {templateToDelete && (
            <p className="text-sm text-gray-600">
              &ldquo;{templateToDelete.name}&rdquo;
            </p>
          )}
        </div>
      </DeleteDialog>
    </CustomPageCard>
  );
};

export default DocumentsTemplates;
