"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import TextAlign from "@tiptap/extension-text-align";
import { Color } from "@tiptap/extension-color";
import TextStyle from "@tiptap/extension-text-style";
import FontFamily from "@tiptap/extension-font-family";
import Toolbar from "./Toolbar";
import { useDocumentTemplateStore } from "../../store/documentsStore";
import { useEffect } from "react";

const TiptapEditor = () => {
  const setContent = useDocumentTemplateStore((state) => state.setContent);
  const templateContent = useDocumentTemplateStore(
    (state) => state.templateContent,
  );
  const setCurrentSignatureId = useDocumentTemplateStore(
    (state) => state.setCurrentSignatureId,
  );
  const signatures = useDocumentTemplateStore((state) => state.signatures);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Link.configure({ openOnClick: false }),
      Image,
      TextAlign.configure({
        types: [
          "heading",
          "paragraph",
          "bulletList",
          "orderedList",
          "blockquote",
        ],
        alignments: ["left", "center", "right", "justify"],
      }),
      Color,
      TextStyle,
      FontFamily,
      // HardBreak removed to avoid duplicate extension warning
    ],
    content: "<p></p>",
    onUpdate: ({ editor }) => {
      const htmlContent = editor.getHTML();
      setContent(htmlContent);
    },
    parseOptions: {
      preserveWhitespace: "full",
    },
    editorProps: {
      handleKeyDown(view, event) {
        if (event.key === "Tab" && !event.shiftKey) {
          // Indent list item if possible
          if (
            editor?.commands.sinkListItem &&
            editor.commands.sinkListItem("listItem")
          ) {
            event.preventDefault();
            return true;
          }
          // Otherwise, insert spaces
          event.preventDefault();
          editor?.commands.insertContent("    ");
          return true;
        }
        if (event.key === "Tab" && event.shiftKey) {
          // Outdent list item if possible
          if (
            editor?.commands.liftListItem &&
            editor.commands.liftListItem("listItem")
          ) {
            event.preventDefault();
            return true;
          }
        }
        return false;
      },
      handlePaste(view, event) {
        const text = event.clipboardData?.getData("text/plain");
        console.log("Pasted text:", text);

        // Handle both plain URLs and wrapped URLs {{url}}
        let url = text;
        if (text && text.startsWith("{{") && text.endsWith("}}")) {
          url = text.slice(2, -2); // Remove {{ and }}
        }

        // Adjust the regex to match your signature image URLs
        if (
          url &&
          /^https?:\/\/.*signatures.*\.(png|jpg|jpeg|gif)$/i.test(url)
        ) {
          // Find the signature by imageUrl
          const signature = signatures.find((sig) => sig.imageUrl === url);
          if (signature) {
            // Set the current signature ID (only one signature allowed)
            setCurrentSignatureId(signature.id);
            if (editor) {
              editor.commands.insertContent(
                `<img src="${url}" alt="Signature" />`,
              );
            }
          }
          return true;
        }
        return false;
      },
    },
  });

  // Update editor content when templateContent changes (e.g., after import)
  useEffect(() => {
    if (editor && templateContent && editor.getHTML() !== templateContent) {
      // Only clean imported HTML if it's not already in proper format
      const needsCleaning =
        templateContent.includes("<br") && !templateContent.includes("<p>");
      const content = needsCleaning
        ? cleanImportedHTML(templateContent)
        : templateContent;
      editor.commands.setContent(content, false);
    }
  }, [templateContent, editor]);

  // Function to clean and format imported HTML
  const cleanImportedHTML = (html: string): string => {
    if (typeof html !== "string") return "";

    // Don't clean if the content is already properly structured
    if (html.includes("<p>") && !html.includes("<br")) {
      return html;
    }

    // Remove excessive tabs and normalize whitespace
    let cleaned = html.replace(/\t+/g, " ");

    // First, normalize all line breaks to consistent format
    cleaned = cleaned.replace(/\r\n/g, "\n").replace(/\r/g, "\n");

    // Replace multiple consecutive <br> tags with paragraph breaks
    // But be more conservative to avoid accumulation
    cleaned = cleaned.replace(/(<br\s*\/?>\s*){2,}/gi, "</p><p>");

    // Only add paragraph tags if they don't exist
    if (!cleaned.includes("<p>")) {
      // Split by double line breaks and wrap in paragraphs
      const parts = cleaned.split(/\n\s*\n/);
      cleaned = parts
        .map((part) => {
          const trimmed = part.trim().replace(/<br\s*\/?>/gi, "");
          return trimmed ? `<p>${trimmed}</p>` : "";
        })
        .filter(Boolean)
        .join("");
    }

    // Ensure we have at least one paragraph
    if (!cleaned.includes("<p>")) {
      cleaned = `<p>${cleaned.replace(/<br\s*\/?>/gi, "")}</p>`;
    }

    return cleaned;
  };

  if (!editor) return null;

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      <Toolbar editor={editor} />
      <EditorContent
        editor={editor}
        className="prose max-w-none p-4 custom-editor-content"
      />
      <style jsx global>{`
        .custom-editor-content .ProseMirror {
          min-height: 300px;
          white-space: pre-wrap;
          word-break: break-word;
        }
        .custom-editor-content .ProseMirror span[style*="font-size"] {
          font-size: inherit !important;
        }
        /* Preserve imported text alignment */
        .custom-editor-content .ProseMirror [style*="text-align: center"] {
          text-align: center !important;
        }
        .custom-editor-content .ProseMirror [style*="text-align: right"] {
          text-align: right !important;
        }
        .custom-editor-content .ProseMirror [style*="text-align: left"] {
          text-align: left !important;
        }
        .custom-editor-content .ProseMirror [style*="text-align: justify"] {
          text-align: justify !important;
        }
        /* Preserve imported positioning and margins */
        .custom-editor-content .ProseMirror [style*="margin"] {
          margin: inherit !important;
        }
        .custom-editor-content .ProseMirror [style*="padding"] {
          padding: inherit !important;
        }
        /* Preserve imported font styles */
        .custom-editor-content .ProseMirror [style*="font-weight"] {
          font-weight: inherit !important;
        }
        .custom-editor-content .ProseMirror [style*="font-style"] {
          font-style: inherit !important;
        }
        .custom-editor-content .ProseMirror [style*="text-decoration"] {
          text-decoration: inherit !important;
        }
        .custom-editor-content .ProseMirror ul {
          list-style-type: disc;
          padding-left: 1.5em;
          margin: 0.5em 0;
        }
        .custom-editor-content .ProseMirror ol {
          list-style-type: decimal;
          padding-left: 1.5em;
          margin: 0.5em 0;
        }
        .custom-editor-content .ProseMirror li {
          margin: 0.25em 0;
        }
        .custom-editor-content .ProseMirror blockquote {
          border-left: 3px solid #ddd;
          margin: 0.5em 0;
          padding-left: 1em;
          font-style: italic;
          color: #666;
        }
        .custom-editor-content .ProseMirror h1 {
          font-size: 2em;
          font-weight: bold;
          margin: 0.67em 0;
          line-height: 1.2;
        }
        .custom-editor-content .ProseMirror h2 {
          font-size: 1.5em;
          font-weight: bold;
          margin: 0.75em 0;
          line-height: 1.3;
        }
        .custom-editor-content .ProseMirror h3 {
          font-size: 1.17em;
          font-weight: bold;
          margin: 0.83em 0;
          line-height: 1.4;
        }
      `}</style>
    </div>
  );
};

export default TiptapEditor;
