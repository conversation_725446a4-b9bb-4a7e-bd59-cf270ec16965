export type DocumentTemplate = {
  id?: string;
  name: string;
  description: string;
  type?: string;
  documentTypeId?: string;
  documentTypeName?: string;
  templateContent: string; // HTML
  category?: string;
  country?: string;
  region?: string;
  tenantId?: string;
  fieldIds?: Array<{
    name: string;
    id: string;
  }>;
  localFields?: Array<{
    formId: string;
    fieldName: string;
  }>;
  userFields?: string[];
  systemFields?: string[];
  templateVersion?: string;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
  signatureId?: string;
};

// Types for the grouped fields endpoint
export enum FieldSource {
  USER = "user",
  FORM = "form",
  SYSTEM = "system",
}

export interface FormField {
  id: string;
  name: string;
  label: string;
  formLabel?: string;
  type: string;
  source: FieldSource;
  formId?: string;
  inputId: string;
  required: boolean;
  placeholder: string;
  validationRules: Array<{
    errorMessage: string;
  }>;
  metadata: {
    category: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface FormGroup {
  formId: string;
  formLabel: string;
  fields: FormField[];
}

export interface GroupedFieldsData {
  source: FieldSource;
  forms?: FormGroup[];
  fields?: FormField[];
}
