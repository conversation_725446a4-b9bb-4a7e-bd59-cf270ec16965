{"extends": "../../../../../../tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node", "@testing-library/jest-dom"], "baseUrl": ".", "paths": {"@/*": ["../../../../../../src/*"], "@/components/*": ["../../../../../../src/components/*"], "@/lib/*": ["../../../../../../src/lib/*"], "@/hooks/*": ["../../../../../../src/hooks/*"]}}, "include": ["**/*.ts", "**/*.tsx", "../../../../../../src/**/*.ts", "../../../../../../src/**/*.tsx"], "exclude": ["node_modules", ".next", "out"]}