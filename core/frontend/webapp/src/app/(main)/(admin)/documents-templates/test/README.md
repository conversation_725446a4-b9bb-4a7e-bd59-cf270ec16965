# Documents Templates - Unit Tests

This directory contains comprehensive unit tests for the Documents Templates functionality in the Connected Workers application.

## 📁 Test Structure

```
test/
├── jest.config.js              # Jest configuration
├── jest.setup.ts               # Test setup and global mocks
├── README.md                   # This file
├── store/
│   └── documentsStore.test.ts  # Store state management tests
├── components/
│   ├── DocumentHeader.test.tsx # Document header component tests
│   └── SystemInformation.test.tsx # System information component tests
└── pages/
    ├── page.test.tsx           # Main documents templates page tests
    └── new-document-template.test.tsx # New document template page tests
```

## 🧪 Test Coverage

### Store Tests (`store/documentsStore.test.ts`)

- **State Management**: Basic state updates (name, description, type, content)
- **Document Types**: Fetching, filtering active types, sorting by display order
- **Grouped Fields**: Fetching user, system, and form fields
- **Signatures**: Fetching and managing signature data
- **Hierarchy Fields**: Fetching and managing hierarchy field data
- **Save Template**: Template saving with field conversion and error handling
- **Load Template**: Template loading with field conversion
- **Pagination & Search**: Data fetching with pagination and search
- **DOCX Import**: File upload and content conversion

### Component Tests (`components/`)

- **DocumentHeader.test.tsx**:

  - Form field rendering and interactions
  - Document type selection and loading states
  - Save functionality
  - Input validation and error handling

- **SystemInformation.test.tsx**:
  - Field display and organization
  - Search functionality across all field types
  - Copy to clipboard operations
  - Loading states and error handling
  - Hierarchy field collapsible sections

### Page Tests (`pages/`)

- **page.test.tsx** (Main Documents Templates Page):

  - Table rendering and data display
  - Pagination and sorting
  - Search functionality
  - Navigation to new template page
  - Edit and delete actions
  - Loading states and error handling

- **new-document-template.test.tsx** (New Document Template Page):
  - File upload dialog management
  - DOCX import functionality
  - Progress tracking and loading states
  - Error handling for upload failures
  - Component integration

## 🚀 Running Tests

### Prerequisites

Make sure you have the following dependencies installed:

```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom @types/jest jest ts-jest
```

### Run All Tests

```bash
npm test
```

### Run Tests with Coverage

```bash
npm test -- --coverage
```

### Run Specific Test Files

```bash
# Run store tests only
npm test store/documentsStore.test.ts

# Run component tests only
npm test components/

# Run page tests only
npm test pages/
```

### Run Tests in Watch Mode

```bash
npm test -- --watch
```

### Run Tests with Verbose Output

```bash
npm test -- --verbose
```

## 📊 Test Configuration

### Jest Configuration (`jest.config.js`)

- **Environment**: jsdom for DOM testing
- **Coverage**: 80% threshold for branches, functions, lines, and statements
- **Timeout**: 10 seconds per test
- **Module Mapping**: Configured for Next.js path aliases
- **Transform**: TypeScript support with ts-jest

### Test Setup (`jest.setup.ts`)

- **Global Mocks**: Next.js router, internationalization, clipboard API
- **Browser APIs**: ResizeObserver, IntersectionObserver, matchMedia
- **Console Filtering**: Suppress React warnings in tests
- **Test Utilities**: Helper functions for common test operations

## 🎯 Test Patterns

### Store Testing

```typescript
// Example store test pattern
describe("useDocumentTemplateStore", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
  });

  it("should update state correctly", () => {
    const { result } = renderHook(() => useDocumentTemplateStore());

    act(() => {
      result.current.setName("Test Template");
    });

    expect(result.current.name).toBe("Test Template");
  });
});
```

### Component Testing

```typescript
// Example component test pattern
describe('ComponentName', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDocumentTemplateStore.mockReturnValue(mockStore);
  });

  it('should render correctly', () => {
    render(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('should handle user interactions', () => {
    render(<ComponentName />);
    fireEvent.click(screen.getByRole('button'));
    expect(mockFunction).toHaveBeenCalled();
  });
});
```

### API Testing

```typescript
// Example API test pattern
it("should handle API success", async () => {
  mockApi.get.mockResolvedValueOnce({ data: mockData });

  const { result } = renderHook(() => useDocumentTemplateStore());

  await act(async () => {
    await result.current.fetchData();
  });

  expect(result.current.data).toEqual(mockData);
  expect(result.current.isLoading).toBe(false);
});
```

## 🔧 Mocking Strategy

### Store Mocking

- Mock the entire store hook to control state
- Provide realistic mock data structures
- Test both success and error scenarios

### API Mocking

- Mock axios for HTTP requests
- Test different response formats
- Verify error handling

### Component Mocking

- Mock child components when needed
- Mock external dependencies (router, translations)
- Test component integration

## 📈 Coverage Goals

- **Lines**: 80% minimum
- **Functions**: 80% minimum
- **Branches**: 80% minimum
- **Statements**: 80% minimum

## 🐛 Debugging Tests

### Common Issues

1. **Module not found**: Check path aliases in jest.config.js
2. **TypeScript errors**: Ensure ts-jest is configured correctly
3. **Async test failures**: Use `waitFor` for async operations
4. **Mock not working**: Check mock setup and cleanup

### Debug Commands

```bash
# Run single test with debug output
npm test -- --verbose --no-coverage --testNamePattern="test name"

# Run tests with Node.js debugger
node --inspect-brk node_modules/.bin/jest --runInBand
```

## 📝 Adding New Tests

### For New Components

1. Create test file in appropriate directory
2. Follow existing test patterns
3. Mock dependencies appropriately
4. Test user interactions and edge cases
5. Add to coverage goals

### For New Store Functions

1. Add tests to `documentsStore.test.ts`
2. Test both success and error scenarios
3. Verify state updates correctly
4. Test API integration

### For New Pages

1. Create test file in `pages/` directory
2. Test component integration
3. Test navigation and routing
4. Test user workflows

## 🤝 Contributing

When adding new tests:

1. Follow existing patterns and conventions
2. Ensure good test coverage
3. Use descriptive test names
4. Mock external dependencies
5. Test both success and error scenarios
6. Update this README if needed

## 📚 Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Library User Events](https://testing-library.com/docs/user-event/intro/)
- [TypeScript Jest Configuration](https://jestjs.io/docs/getting-started#using-typescript)
