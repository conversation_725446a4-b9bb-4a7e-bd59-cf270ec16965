import { renderHook, act } from "@testing-library/react";
import { useDocumentTemplateStore } from "../../store/documentsStore";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast";

// Mock dependencies
jest.mock("@/lib/axios");
jest.mock("@/hooks/use-toast");

const mockApi = api as jest.Mocked<typeof api>;
const mockToast = toast as jest.MockedFunction<typeof toast>;

describe("useDocumentTemplateStore", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
    const { result } = renderHook(() => useDocumentTemplateStore());
    act(() => {
      result.current.setName("");
      result.current.setDescription("");
      result.current.setType("");
      result.current.setContent("");
      result.current.setCurrentSignatureId(null);
    });
  });

  describe("State Management", () => {
    it("should initialize with default values", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      expect(result.current.name).toBe("");
      expect(result.current.description).toBe("");
      expect(result.current.type).toBe("");
      expect(result.current.templateContent).toBe("");
      expect(result.current.currentSignatureId).toBeNull();
    });

    it("should update name", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      act(() => {
        result.current.setName("Test Template");
      });

      expect(result.current.name).toBe("Test Template");
    });

    it("should update description", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      act(() => {
        result.current.setDescription("Test Description");
      });

      expect(result.current.description).toBe("Test Description");
    });

    it("should update type", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      act(() => {
        result.current.setType("contract");
      });

      expect(result.current.type).toBe("contract");
    });

    it("should update template content", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      act(() => {
        result.current.setContent("<p>Test content</p>");
      });

      expect(result.current.templateContent).toBe("<p>Test content</p>");
    });

    it("should update current signature ID", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      act(() => {
        result.current.setCurrentSignatureId("sig-123");
      });

      expect(result.current.currentSignatureId).toBe("sig-123");
    });
  });

  describe("saveTemplate", () => {
    it("should save template successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.post.mockResolvedValueOnce({ data: { message: "Success" } });

      act(() => {
        result.current.setName("Test Template");
        result.current.setDescription("Test Description");
        result.current.setType("contract");
        result.current.setContent("<p>Test content</p>");
      });

      await act(async () => {
        await result.current.saveTemplate();
      });

      expect(mockApi.post).toHaveBeenCalledWith(
        "/document-template/templates",
        expect.objectContaining({
          name: "Test Template",
          description: "Test Description",
          documentTypeId: "contract",
          templateContent: "<html><body><p>Test content</p></body></html>",
          country: "US",
          region: "NA-EAST",
          tenantId: "aptiv-us-manufacturing",
          createdBy: "123e9356-e89b-12d3-a456-426614178963",
          templateVersion: "v1.0",
          fieldIds: [],
          signatureId: undefined,
        }),
        expect.objectContaining({
          headers: { "Content-Type": "application/json" },
        }),
      );

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Template saved successfully.",
        variant: "success",
      });
    });

    it("should handle API error with validation details", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const validationError = {
        code: "VALIDATION_ERROR",
        message: "Validation failed",
        statusCode: 400,
        timestamp: "2024-01-01T00:00:00Z",
        details: {
          errors: [
            {
              field: "name",
              value: "",
              constraints: ["Name is required"],
            },
          ],
        },
        path: "/document-template/templates",
      };

      mockApi.post.mockRejectedValueOnce({
        response: { data: validationError },
      });

      act(() => {
        result.current.setName("Test Template");
        result.current.setDescription("Test Description");
        result.current.setType("contract");
        result.current.setContent("<p>Test content</p>");
      });

      await act(async () => {
        await result.current.saveTemplate();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Validation failed",
        variant: "destructive",
      });
    });

    it("should handle API error with simple message", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.post.mockRejectedValueOnce({
        response: {
          data: { message: "Template name already exists" },
        },
      });

      act(() => {
        result.current.setName("Test Template");
        result.current.setDescription("Test Description");
        result.current.setType("contract");
        result.current.setContent("<p>Test content</p>");
      });

      await act(async () => {
        await result.current.saveTemplate();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Template name already exists",
        variant: "destructive",
      });
    });

    it("should handle network error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.post.mockRejectedValueOnce(new Error("Network error"));

      act(() => {
        result.current.setName("Test Template");
        result.current.setDescription("Test Description");
        result.current.setType("contract");
        result.current.setContent("<p>Test content</p>");
      });

      await act(async () => {
        await result.current.saveTemplate();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Network error",
        variant: "destructive",
      });
    });
  });

  describe("fetchPaginatedData", () => {
    it("should fetch paginated data successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: {
          items: [
            {
              id: "1",
              name: "Template 1",
              description: "Description 1",
              category: "contract",
              createdAt: "2024-01-01T00:00:00Z",
            },
          ],
          meta: {
            pageSize: 5,
            currentPage: 1,
            totalPages: 1,
            totalCount: 1,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        },
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.fetchPaginatedData(0, 5, "createdAt", "desc");
      });

      expect(mockApi.get).toHaveBeenCalledWith("/document-template/templates", {
        params: {
          pageNumber: 1,
          pageSize: 5,
          sortBy: "createdAt",
          sortOrder: "desc",
          country: "US",
          tenantId: "aptiv-us-manufacturing",
        },
      });

      expect(result.current.data.items).toEqual(mockResponse.data.items);
      expect(result.current.data.meta).toEqual(mockResponse.data.meta);
      expect(result.current.isLoading).toBe(false);
    });

    it("should handle invalid response format", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockResolvedValueOnce({ data: { invalid: "format" } });

      await act(async () => {
        await result.current.fetchPaginatedData(0, 5, "createdAt", "desc");
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Invalid response format from server",
        variant: "destructive",
      });
      expect(result.current.isLoading).toBe(false);
    });

    it("should handle API error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockRejectedValueOnce({
        response: { data: { message: "Failed to fetch data" } },
      });

      await act(async () => {
        await result.current.fetchPaginatedData(0, 5, "createdAt", "desc");
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Failed to fetch data",
        variant: "destructive",
      });
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe("searchTemplates", () => {
    it("should search templates successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: {
          items: [
            {
              id: "1",
              name: "Search Result",
              description: "Description",
              category: "contract",
              createdAt: "2024-01-01T00:00:00Z",
            },
          ],
          meta: {
            pageSize: 5,
            currentPage: 1,
            totalPages: 1,
            totalCount: 1,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        },
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.searchTemplates("test", 0, 5, "name", "asc");
      });

      expect(mockApi.get).toHaveBeenCalledWith("/document-template/templates", {
        params: {
          search: "test",
          pageNumber: 1,
          pageSize: 5,
          sortBy: "name",
          sortOrder: "asc",
          country: "US",
          tenantId: "aptiv-us-manufacturing",
        },
      });

      expect(result.current.data.items).toEqual(mockResponse.data.items);
      expect(result.current.data.meta).toEqual(mockResponse.data.meta);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe("fetchGroupedFields", () => {
    it("should fetch grouped fields successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: {
          data: [
            {
              source: "USER",
              fields: [{ id: "1", name: "firstName", label: "First Name" }],
            },
          ],
        },
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.fetchGroupedFields();
      });

      expect(mockApi.get).toHaveBeenCalledWith(
        "/document-template/fields/grouped-by-source",
      );
      expect(result.current.groupedFields).toEqual(mockResponse.data.data);
      expect(result.current.isFieldsLoading).toBe(false);
      expect(result.current.fieldsError).toBeNull();
    });

    it("should handle grouped fields error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockRejectedValueOnce(new Error("Network error"));

      await act(async () => {
        await result.current.fetchGroupedFields();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Failed to fetch grouped fields",
        variant: "destructive",
      });
      expect(result.current.isFieldsLoading).toBe(false);
      expect(result.current.fieldsError).toBe("Could not load fields.");
    });
  });

  describe("fetchSignatures", () => {
    it("should fetch signatures successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: [
          {
            id: "sig-1",
            name: "John Doe",
            imageUrl: "https://example.com/signature1.png",
          },
        ],
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.fetchSignatures();
      });

      expect(mockApi.get).toHaveBeenCalledWith("/document-template/signatures");
      expect(result.current.signatures).toEqual(mockResponse.data);
      expect(result.current.isSignaturesLoading).toBe(false);
      expect(result.current.signaturesError).toBeNull();
    });

    it("should handle signatures error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockRejectedValueOnce(new Error("Network error"));

      await act(async () => {
        await result.current.fetchSignatures();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Failed to fetch signatures",
        variant: "destructive",
      });
      expect(result.current.isSignaturesLoading).toBe(false);
      expect(result.current.signaturesError).toBe("Could not load signatures.");
    });
  });

  describe("fetchHierarchyFields", () => {
    it("should fetch hierarchy fields successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: [
          {
            id: "hier-1",
            displayName: "Department",
            availableFields: ["name", "code"],
          },
        ],
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.fetchHierarchyFields();
      });

      expect(mockApi.get).toHaveBeenCalledWith(
        "/document-template/hierarchy-fields",
      );
      expect(result.current.hierarchyFields).toEqual(mockResponse.data);
      expect(result.current.isHierarchyFieldsLoading).toBe(false);
      expect(result.current.hierarchyFieldsError).toBeNull();
    });

    it("should handle hierarchy fields error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockRejectedValueOnce(new Error("Network error"));

      await act(async () => {
        await result.current.fetchHierarchyFields();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Failed to fetch hierarchy fields",
        variant: "destructive",
      });
      expect(result.current.isHierarchyFieldsLoading).toBe(false);
      expect(result.current.hierarchyFieldsError).toBe(
        "Could not load hierarchy fields.",
      );
    });
  });

  describe("fetchDocumentTypes", () => {
    it("should fetch document types successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: [
          {
            id: "type-1",
            name: "Contract",
            description: "Contract template",
            isActive: true,
            displayOrder: 1,
          },
          {
            id: "type-2",
            name: "Invoice",
            description: "Invoice template",
            isActive: false,
            displayOrder: 2,
          },
        ],
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.fetchDocumentTypes();
      });

      expect(mockApi.get).toHaveBeenCalledWith(
        "/document-template/document-types",
      );
      expect(result.current.documentTypes).toEqual([mockResponse.data[0]]); // Only active ones
      expect(result.current.isDocumentTypesLoading).toBe(false);
      expect(result.current.documentTypesError).toBeNull();
    });

    it("should handle document types error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockRejectedValueOnce(new Error("Network error"));

      await act(async () => {
        await result.current.fetchDocumentTypes();
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Failed to fetch document types",
        variant: "destructive",
      });
      expect(result.current.isDocumentTypesLoading).toBe(false);
      expect(result.current.documentTypesError).toBe(
        "Could not load document types.",
      );
    });
  });

  describe("loadTemplate", () => {
    it("should load template successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockResponse = {
        data: {
          template: {
            name: "Loaded Template",
            description: "Loaded Description",
            type: "contract",
            templateContent: "<p>Loaded content</p>",
          },
        },
      };

      mockApi.get.mockResolvedValueOnce(mockResponse);

      await act(async () => {
        await result.current.loadTemplate("template-1");
      });

      expect(mockApi.get).toHaveBeenCalledWith(
        "/document-template/templates/template-1",
      );
      expect(result.current.name).toBe("Loaded Template");
      expect(result.current.description).toBe("Loaded Description");
      expect(result.current.type).toBe("contract");
      expect(result.current.templateContent).toBe("<p>Loaded content</p>");
      expect(result.current.isLoading).toBe(false);
    });

    it("should handle load template error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      mockApi.get.mockRejectedValueOnce({
        response: { data: { message: "Template not found" } },
      });

      await act(async () => {
        await result.current.loadTemplate("template-1");
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "Template not found",
        variant: "destructive",
      });
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe("DOCX Import", () => {
    it("should handle file upload successfully", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockFile = new File(["test content"], "test.docx", {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });
      const mockResponse = {
        status: 200,
        data: { htmlContent: "<p>Imported content</p>" },
      };

      mockApi.post.mockResolvedValueOnce(mockResponse);

      act(() => {
        result.current.setFile(mockFile);
      });

      await act(async () => {
        await result.current.handleFileUpload(mockFile);
      });

      expect(mockApi.post).toHaveBeenCalledWith(
        "/document-template/docx-import",
        expect.any(FormData),
        expect.objectContaining({
          headers: { "Content-Type": "multipart/form-data" },
        }),
      );

      expect(mockToast).toHaveBeenCalledWith({
        title: "Success",
        description: "Document imported successfully.",
        variant: "success",
      });
    });

    it("should handle file upload error", async () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockFile = new File(["test content"], "test.docx", {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });

      mockApi.post.mockRejectedValueOnce(new Error("Upload failed"));

      act(() => {
        result.current.setFile(mockFile);
      });

      await act(async () => {
        await result.current.handleFileUpload(mockFile);
      });

      expect(mockToast).toHaveBeenCalledWith({
        title: "Error",
        description: "An error occurred during import.",
        variant: "destructive",
      });
    });
  });

  describe("Import Dialog State", () => {
    it("should manage import dialog state", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      expect(result.current.importDialogOpen).toBe(false);

      act(() => {
        result.current.setImportDialogOpen(true);
      });

      expect(result.current.importDialogOpen).toBe(true);

      act(() => {
        result.current.setImportDialogOpen(false);
      });

      expect(result.current.importDialogOpen).toBe(false);
    });

    it("should manage file state", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      const mockFile = new File(["test"], "test.docx", {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });

      // The initial state should be null, but in tests it might be an empty object
      // So we check if it's either null or an empty object
      expect(
        result.current.file === null ||
          Object.keys(result.current.file || {}).length === 0,
      ).toBe(true);

      act(() => {
        result.current.setFile(mockFile);
      });

      expect(result.current.file).toBeInstanceOf(File);

      act(() => {
        result.current.setFile(null);
      });

      expect(result.current.file).toBeNull();
    });

    it("should manage progress state", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      expect(result.current.progress).toBe(0);

      act(() => {
        result.current.setProgress(50);
      });

      expect(result.current.progress).toBe(50);

      act(() => {
        result.current.setProgress((prev) => prev + 25);
      });

      expect(result.current.progress).toBe(75);
    });

    it("should manage importing state", () => {
      const { result } = renderHook(() => useDocumentTemplateStore());

      expect(result.current.isImporting).toBe(false);

      act(() => {
        result.current.setIsImporting(true);
      });

      expect(result.current.isImporting).toBe(true);

      act(() => {
        result.current.setIsImporting(false);
      });

      expect(result.current.isImporting).toBe(false);
    });
  });
});
