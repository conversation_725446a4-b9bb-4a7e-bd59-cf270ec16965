"use client";

import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Loader2, Search } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useDocumentTemplateStore } from "../../store/documentsStore";
import { FieldSource, FormField } from "../../types/documentsTypes";
import { useRouter } from "next/navigation";

export function SystemInformation() {
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [userSearchTerm, setUserSearchTerm] = useState<string>("");
  const [systemSearchTerm, setSystemSearchTerm] = useState<string>("");
  const [formsGlobalSearchTerm, setFormsGlobalSearchTerm] =
    useState<string>("");
  const [signatureSearchTerm, setSignatureSearchTerm] = useState<string>("");
  const [hierarchySearchTerm, setHierarchySearchTerm] = useState<string>("");
  const t = useTranslations("documentsTemplates");
  const router = useRouter();

  const {
    groupedFields,
    isFieldsLoading,
    fetchGroupedFields,
    signatures,
    isSignaturesLoading,
    fetchSignatures,
    hierarchyFields,
    isHierarchyFieldsLoading,
    fetchHierarchyFields,
  } = useDocumentTemplateStore();

  useEffect(() => {
    fetchGroupedFields();
    fetchSignatures();
    fetchHierarchyFields();
  }, [fetchGroupedFields, fetchSignatures, fetchHierarchyFields]);

  const handleCopy = (value: string, id: string) => {
    navigator.clipboard.writeText(value);
    setCopiedField(id);
    setTimeout(() => setCopiedField(null), 2000);
  };

  const getSourceDisplayName = (source: FieldSource): string => {
    switch (source) {
      case FieldSource.USER:
        return t("NewDocumentTemplate.userInformation");
      case FieldSource.FORM:
        return t("NewDocumentTemplate.formsList");
      case FieldSource.SYSTEM:
        return t("NewDocumentTemplate.systemInformation");
      default:
        return source;
    }
  };

  const filterFieldsBySearch = (fields: FormField[], searchTerm: string) => {
    if (!searchTerm) return fields;
    return fields.filter(
      (field) =>
        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        field.name.toLowerCase().includes(searchTerm.toLowerCase()),
    );
  };

  const renderFieldItem = (
    field: FormField,
    index: number,
    isAlternate: boolean = false,
  ) => (
    <div
      key={field.id}
      className={`flex items-center gap-2 text-sm text-gray-600 ${
        isAlternate ? "bg-cyan-50 dark:bg-cyan-900" : ""
      }`}
    >
      <div className="flex items-center gap-2">
        <span className="inline-block h-4 w-4">📄</span>
        <span>{field.label}</span>
        <Button
          variant="ghost"
          size="icon"
          className="ml-auto h-6 w-6"
          onClick={() => handleCopy(`{{${field.name}}}`, field.id)}
        >
          <Copy />
        </Button>
        {copiedField === field.id && (
          <span>{t("NewDocumentTemplate.copied")}</span>
        )}
      </div>
    </div>
  );

  // --- Signature Section ---
  const filteredSignatures = !signatureSearchTerm
    ? signatures
    : signatures.filter((sig) =>
        sig.name.toLowerCase().includes(signatureSearchTerm.toLowerCase()),
      );

  // --- Hierarchy Fields Section ---
  const filteredHierarchyFields = !hierarchySearchTerm
    ? hierarchyFields
    : hierarchyFields.filter((hf) =>
        hf.displayName
          .toLowerCase()
          .includes(hierarchySearchTerm.toLowerCase()),
      );

  return (
    <div className="w-80 border-l">
      {/* Dynamic Fields from API */}
      {groupedFields.map((group) => (
        <Collapsible key={group.source} defaultOpen>
          <CollapsibleTrigger className="flex w-full items-center justify-between bg-black p-2 text-white">
            <span>{getSourceDisplayName(group.source)}</span>
            <ChevronDown className="h-4 w-4" />
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 space-y-4">
              {/* User Information Section Search */}
              {group.source === FieldSource.USER && group.fields && (
                <>
                  <div className="relative mb-2">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      placeholder={t("NewDocumentTemplate.searchUserFields")}
                      className="pl-9"
                      value={userSearchTerm}
                      onChange={(e) => setUserSearchTerm(e.target.value)}
                    />
                  </div>
                  {isFieldsLoading ? (
                    <div className="flex justify-center items-center text-black">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {filterFieldsBySearch(group.fields, userSearchTerm).map(
                        (field, index) =>
                          renderFieldItem(field, index, index % 2 === 1),
                      )}
                    </div>
                  )}
                </>
              )}

              {/* System Information Section Search */}
              {group.source === FieldSource.SYSTEM && group.fields && (
                <>
                  <div className="relative mb-2">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      placeholder={t("NewDocumentTemplate.searchSystemFields")}
                      className="pl-9"
                      value={systemSearchTerm}
                      onChange={(e) => setSystemSearchTerm(e.target.value)}
                    />
                  </div>
                  {isFieldsLoading ? (
                    <div className="flex justify-center items-center text-black">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {filterFieldsBySearch(group.fields, systemSearchTerm).map(
                        (field, index) =>
                          renderFieldItem(field, index, index % 2 === 1),
                      )}
                    </div>
                  )}
                </>
              )}

              {/* Forms List Section - Global Search */}
              {group.source === FieldSource.FORM && group.forms && (
                <>
                  <div className="relative mb-2">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      placeholder={t("NewDocumentTemplate.searchAllFormFields")}
                      className="pl-9"
                      value={formsGlobalSearchTerm}
                      onChange={(e) => setFormsGlobalSearchTerm(e.target.value)}
                    />
                  </div>
                  {isFieldsLoading ? (
                    <div className="flex justify-center items-center text-black">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  ) : (
                    <div className="space-y-4 max-h-80 overflow-y-auto">
                      {group.forms
                        .map((form) => {
                          const filteredFields = filterFieldsBySearch(
                            form.fields,
                            formsGlobalSearchTerm,
                          );
                          if (filteredFields.length === 0) return null;
                          return (
                            <Collapsible key={form.formId} defaultOpen>
                              <CollapsibleTrigger className="flex w-full items-center justify-between bg-gray-100 p-2 text-gray-700 hover:bg-gray-200">
                                <span>{form.formLabel}</span>
                                <ChevronDown className="h-4 w-4" />
                              </CollapsibleTrigger>
                              <CollapsibleContent>
                                <div className="p-2 space-y-2 max-h-40 overflow-y-auto">
                                  {filteredFields.map((field, index) =>
                                    renderFieldItem(
                                      field,
                                      index,
                                      index % 2 === 1,
                                    ),
                                  )}
                                </div>
                              </CollapsibleContent>
                            </Collapsible>
                          );
                        })
                        .filter(Boolean)}
                      {/* Add button below the forms list */}
                      <div className="flex justify-end">
                        <Button
                          variant="outline"
                          className="mt-4 rounded-lg"
                          onClick={() =>
                            router.push("/forms-management/new-form")
                          }
                        >
                          Add a new form
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      ))}

      {/* Hierarchy Fields Section */}
      {!isHierarchyFieldsLoading && hierarchyFields.length > 0 && (
        <Collapsible defaultOpen>
          <CollapsibleTrigger className="flex w-full items-center justify-between bg-black p-2 text-white">
            <span>{t("NewDocumentTemplate.hierarchyFields")}</span>
            <ChevronDown className="h-4 w-4" />
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 space-y-4">
              <div className="relative mb-2">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder={t("NewDocumentTemplate.searchHierarchyFields")}
                  className="pl-9"
                  value={hierarchySearchTerm}
                  onChange={(e) => setHierarchySearchTerm(e.target.value)}
                />
              </div>
              <div className="space-y-4 max-h-60 overflow-y-auto">
                {filteredHierarchyFields.map((hf) => (
                  <Collapsible key={hf.id} defaultOpen>
                    <CollapsibleTrigger className="flex w-full items-center justify-between bg-gray-100 p-2 text-gray-700 hover:bg-gray-200">
                      <span>{hf.displayName}</span>
                      <ChevronDown className="h-4 w-4" />
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <div className="p-2 space-y-2 max-h-40 overflow-y-auto">
                        {hf.availableFields.map((field, index) => (
                          <div
                            key={`${hf.id}-${field}`}
                            className={`flex items-center gap-2 text-sm text-gray-600 ${index % 2 === 1 ? "bg-cyan-50 dark:bg-cyan-900" : ""}`}
                          >
                            <span className="inline-block h-4 w-4">📄</span>
                            <span>{field}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="ml-auto h-6 w-6"
                              onClick={() =>
                                handleCopy(
                                  `{{${hf.fieldType}:${hf.role}.${field}}}`,
                                  `${hf.id}-${field}`,
                                )
                              }
                            >
                              <Copy />
                            </Button>
                            {copiedField === `${hf.id}-${field}` && (
                              <span>{t("NewDocumentTemplate.copied")}</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}

      {/* Signatures Section */}
      {!isSignaturesLoading && signatures.length > 0 && (
        <Collapsible defaultOpen>
          <CollapsibleTrigger className="flex w-full items-center justify-between bg-black p-2 text-white">
            <span>{t("NewDocumentTemplate.signatures")}</span>
            <ChevronDown className="h-4 w-4" />
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 space-y-4">
              <div className="relative mb-2">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder={t("NewDocumentTemplate.searchSignatures")}
                  className="pl-9"
                  value={signatureSearchTerm}
                  onChange={(e) => setSignatureSearchTerm(e.target.value)}
                />
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {filteredSignatures.map((sig, idx) => (
                  <div
                    key={sig.id}
                    className={`flex items-center gap-2 text-sm text-gray-600 ${idx % 2 === 1 ? "bg-cyan-50 dark:bg-cyan-900" : ""}`}
                  >
                    <span className="inline-block h-4 w-4">✒️</span>
                    <span>{sig.name}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-auto h-6 w-6"
                      onClick={() => handleCopy(sig.imageUrl, sig.id)}
                    >
                      <Copy />
                    </Button>
                    {copiedField === sig.id && (
                      <span>{t("NewDocumentTemplate.copied")}</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  );
}
