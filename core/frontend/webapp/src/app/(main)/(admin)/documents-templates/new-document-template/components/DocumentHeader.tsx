"use client";
import { Save } from "lucide-react";
import { Icon<PERSON>utton } from "@/components/common/CustomButtons";
import { useTranslations } from "next-intl";
import CustomSelect from "@/components/common/CustomSelect";
import CustomInput from "@/components/common/CustomInput";
import { useDocumentTemplateStore } from "../../store/documentsStore";
import { useEffect } from "react";
import { DocumentTemplate } from "../../types/documentsTypes";

export function DocumentHeader({
  editingOriginalTemplate,
}: {
  editingOriginalTemplate?: DocumentTemplate | null;
}) {
  const t = useTranslations("documentsTemplates");

  const {
    type,
    name,
    description,
    setType,
    setName,
    setDescription,
    saveTemplate,
    documentTypes,
    isDocumentTypesLoading,
    fetchDocumentTypes,
  } = useDocumentTemplateStore();

  useEffect(() => {
    fetchDocumentTypes();
  }, [fetchDocumentTypes]);

  // When editing, populate form with template data including documentTypeId
  useEffect(() => {
    if (editingOriginalTemplate && documentTypes.length > 0) {
      // Use documentTypeId from the template to find and set the correct type
      const templateDocumentTypeId = editingOriginalTemplate.documentTypeId;

      if (templateDocumentTypeId) {
        // Verify the documentTypeId exists in the fetched document types
        const matchingDocumentType = documentTypes.find(
          (docType) => docType.id === templateDocumentTypeId,
        );

        if (matchingDocumentType) {
          setType(templateDocumentTypeId);
        }
      }
    }
  }, [editingOriginalTemplate, documentTypes, setType]);

  const documentTypeOptions = documentTypes.map((docType) => ({
    value: docType.id,
    label: docType.name,
  }));

  // Find the current document type for display
  // When editing, prioritize documentTypeId from template, then fallback to store type
  const currentTypeId = editingOriginalTemplate?.documentTypeId || type;
  const currentDocumentType = documentTypeOptions.find(
    (option) => option.value === currentTypeId,
  );

  // Use the document type ID for the value, ensure we have a valid option
  const selectValue = currentDocumentType ? currentTypeId : "";

  // Get the document type name for saving
  const getDocumentTypeName = () => {
    const docType = documentTypeOptions.find((option) => option.value === type);
    return docType ? docType.label : "";
  };

  return (
    <div className="flex items-end gap-4 p-4">
      <CustomSelect
        key={`document-type-${selectValue || "empty"}`} // Force re-render when value changes
        id="documentType"
        label={t("NewDocumentTemplate.documentType")}
        aria-label={t("NewDocumentTemplate.documentType")}
        options={documentTypeOptions}
        defaultValue={selectValue}
        onValueChange={setType}
        aria-describedby={t("NewDocumentTemplate.documentType")}
        className="w-52 text-base"
        disabled={isDocumentTypesLoading}
        placeholder={
          isDocumentTypesLoading ? "Loading..." : "Select document type"
        }
        data-testid="documentType"
      />
      <CustomInput
        id="documentTemplateName"
        label={t("NewDocumentTemplate.documentTemplateName")}
        aria-label={t("NewDocumentTemplate.documentTemplateName")}
        value={name}
        onChange={(e) => setName(e.target.value)}
        aria-describedby={t("NewDocumentTemplate.documentTemplateName")}
        className="flex-1"
        data-testid="documentTemplateName"
      />
      <CustomInput
        id="description"
        label={t("NewDocumentTemplate.description")}
        aria-label={t("NewDocumentTemplate.description")}
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        aria-describedby={t("NewDocumentTemplate.description")}
        className="flex-1"
        data-testid="description"
      />
      <div className="flex items-center">
        <IconButton
          className="gap-2"
          icon={Save}
          label={t("NewDocumentTemplate.save")}
          onClick={() =>
            saveTemplate(editingOriginalTemplate, getDocumentTypeName())
          }
        />
      </div>
    </div>
  );
}
