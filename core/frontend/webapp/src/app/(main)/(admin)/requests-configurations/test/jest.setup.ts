import "@testing-library/jest-dom";
import React from "react";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: "/",
    query: {},
    asPath: "/",
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/",
}));

// Mock Next.js image component
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: Record<string, unknown>) => {
    return React.createElement("img", {
      ...props,
      alt: (props.alt as string) || "",
    });
  },
}));

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = (...args: unknown[]) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("Warning: ReactDOM.render is no longer supported")
    ) {
      return;
    }
    originalConsoleError.call(console, ...args);
  };

  console.warn = (...args: unknown[]) => {
    if (
      typeof args[0] === "string" &&
      (args[0].includes("Warning: componentWillReceiveProps") ||
        args[0].includes("Warning: componentWillUpdate"))
    ) {
      return;
    }
    originalConsoleWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Mock @xyflow/react components
jest.mock("@xyflow/react", () => ({
  ReactFlow: ({ children, ...props }: Record<string, unknown>) =>
    React.createElement(
      "div",
      { "data-testid": "react-flow", ...props },
      children as React.ReactNode,
    ),
  Background: ({ ...props }: Record<string, unknown>) =>
    React.createElement("div", { "data-testid": "background", ...props }),
  Controls: ({ ...props }: Record<string, unknown>) =>
    React.createElement("div", { "data-testid": "controls", ...props }),
  MiniMap: ({ ...props }: Record<string, unknown>) =>
    React.createElement("div", { "data-testid": "mini-map", ...props }),
  useReactFlow: () => ({
    getNodes: jest.fn(),
    getEdges: jest.fn(),
    setNodes: jest.fn(),
    setEdges: jest.fn(),
    addNodes: jest.fn(),
    addEdges: jest.fn(),
    deleteElements: jest.fn(),
    fitView: jest.fn(),
    zoomIn: jest.fn(),
    zoomOut: jest.fn(),
    setViewport: jest.fn(),
    getViewport: jest.fn(),
  }),
  useNodesState: () => [[], jest.fn()],
  useEdgesState: () => [[], jest.fn()],
  addEdge: jest.fn(),
  applyNodeChanges: jest.fn(),
  applyEdgeChanges: jest.fn(),
  getBezierPath: jest.fn(),
  getSmoothStepPath: jest.fn(),
  getStraightPath: jest.fn(),
  getEdgeCenter: jest.fn(),
  getNodeCenter: jest.fn(),
  getRectOfNodes: jest.fn(),
  getTransformForBounds: jest.fn(),
  getBoundsofRects: jest.fn(),
  Connection: jest.fn(),
  Edge: jest.fn(),
  NodeChange: jest.fn(),
  EdgeChange: jest.fn(),
}));

// Mock zustand stores
jest.mock("zustand", () => ({
  create: jest.fn((fn) => fn),
}));
