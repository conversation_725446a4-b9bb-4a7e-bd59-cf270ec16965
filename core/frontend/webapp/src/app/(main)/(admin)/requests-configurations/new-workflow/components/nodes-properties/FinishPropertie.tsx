import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import React from "react";
import DynamicProperties from "./DynamicProperties";

interface FinishPropertieProps {
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
}

const FinishPropertie: React.FC<FinishPropertieProps> = ({
  localProperties,
  setLocalProperties,
}) => {
  const t = useTranslations("requestsConfigurations");
  return (
    <div className="space-y-6">
      {/* node */}
      <div className="p-2  ">
        <div
          className={`p-2 rounded-lg border-2 cursor-move flex justify-center items-center bg-green-200 mx-auto max-w-52`}
        >
          {t("newWorkflow.finish")}
        </div>
      </div>
      <Separator orientation="horizontal" className="border-gray-300" />
      <DynamicProperties
        nodeType="END"
        localProperties={localProperties}
        setLocalProperties={setLocalProperties}
      />
    </div>
  );
};

export default FinishPropertie;
