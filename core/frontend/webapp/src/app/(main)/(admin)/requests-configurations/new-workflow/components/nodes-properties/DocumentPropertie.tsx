import React, { useEffect } from "react";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { useWorkflowStore } from "../../../store/workflowStore";
import { useWorkflowMetadataStore } from "../../../store/workflowStore";
import DynamicProperties from "./DynamicProperties";

interface DocumentPropertieProps {
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
}

const DocumentPropertie: React.FC<DocumentPropertieProps> = ({
  localProperties,
  setLocalProperties,
}) => {
  useWorkflowStore();
  const { metadata, fetchMetadata } = useWorkflowMetadataStore();
  const t = useTranslations("requestsConfigurations");

  useEffect(() => {
    if (!metadata) {
      fetchMetadata();
    }
  }, [metadata, fetchMetadata]);

  return (
    <div className="space-y-6">
      {/* node */}
      <div className="p-2  ">
        <div
          className={`p-2 rounded-lg border-2 cursor-move flex justify-center items-center bg-yellow-200 mx-auto max-w-52`}
        >
          {t("newWorkflow.document")}
        </div>
      </div>
      <Separator orientation="horizontal" className="border-gray-300" />
      <DynamicProperties
        nodeType="DOCUMENT"
        localProperties={localProperties}
        setLocalProperties={setLocalProperties}
      />
    </div>
  );
};

export default DocumentPropertie;
