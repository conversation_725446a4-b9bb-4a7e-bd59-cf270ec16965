{"EXAMPLE_NODE": {"fields": [{"label": "username", "type": "input", "required": true, "inputType": "text", "multiline": false}, {"label": "description", "type": "input", "required": false, "inputType": "text", "multiline": true}, {"label": "age", "type": "input", "required": false, "inputType": "number", "multiline": false}, {"label": "role", "type": "select", "required": true, "options": ["Admin", "User", "Guest"]}, {"label": "userRole", "type": "select", "required": true, "options": [{"id": "role1", "name": "Administrator"}, {"id": "role2", "name": "Regular User"}, {"id": "role3", "name": "Guest User"}]}, {"label": "tags", "type": "multiselect", "required": false, "options": ["Tag1", "Tag2", "Tag3"]}, {"label": "userTags", "type": "multiselect", "required": false, "options": [{"id": "tag1", "name": "Important"}, {"id": "tag2", "name": "<PERSON><PERSON>"}, {"id": "tag3", "name": "Review"}]}, {"label": "preferences", "type": "checkbox_list", "required": false, "options": ["Email Notifications", "SMS Alerts", "Push Notifications"]}, {"label": "userPreferences", "type": "checkbox_list", "required": false, "options": [{"id": "pref1", "name": "Email Notifications"}, {"id": "pref2", "name": "SMS Alerts"}, {"id": "pref3", "name": "Push Notifications"}]}, {"label": "isActive", "type": "checkbox", "required": false}, {"label": "profile", "type": "object", "required": false, "fields": [{"label": "bio", "type": "input", "required": false, "inputType": "text", "multiline": true}, {"label": "contactMethod", "type": "select", "required": false, "options": ["Email", "Phone"]}, {"label": "department", "type": "select", "required": false, "options": [{"id": "dept1", "name": "Engineering"}, {"id": "dept2", "name": "Marketing"}, {"id": "dept3", "name": "Sales"}]}]}]}}