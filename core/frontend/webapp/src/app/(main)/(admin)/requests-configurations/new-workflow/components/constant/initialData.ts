import { Edge } from "@xyflow/react";
import { TNode } from "../types/types";
import { NodeEnum } from "../types/NodeEnum";

export const initialNodes: TNode[] = [
  {
    id: "1",
    type: "start" as NodeEnum,
    position: { x: 250, y: 0 },
    data: { label: "START" },
    dragging: false,
    zIndex: 0,
    isConnectable: true,
    positionAbsoluteX: 250,
    positionAbsoluteY: 0,
  },
];

export const initialEdges: Edge[] = [];
