// Types for workflow list and workflow engine API

export interface WorkflowNode {
  id: string;
  formId: string;
  type: string;
  title: string;
  position: { x: number; y: number };
  config: Record<string, unknown>;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
}

export interface WorkflowListItem {
  id: string;
  name: string;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowListMeta {
  pageSize: number;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  hasMoreResults: boolean;
  continuationToken?: string;
}

export interface WorkflowFull extends WorkflowListItem {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

// Option types for select/multiselect fields
export type WorkflowOption = string | { id: string; name: string };

// Workflow Metadata Types
export interface WorkflowMetadataField {
  name: string;
  type: string;
  required: boolean;
  options?: WorkflowOption[];
  itemType?: string;
  itemFields?: WorkflowMetadataField[];
  fields?: WorkflowMetadataField[];
  defaultValue?: unknown;
}

export interface WorkflowMetadataNode {
  fields: WorkflowMetadataField[];
}

export interface WorkflowMetadata {
  SINGLE_APPROVAL: WorkflowMetadataNode;
  PARALLEL_APPROVAL: WorkflowMetadataNode;
  DOCUMENT: WorkflowMetadataNode;
  NOTIFICATION: WorkflowMetadataNode;
  START: WorkflowMetadataNode;
  END: WorkflowMetadataNode;
  CONDITION: WorkflowMetadataNode;
  UPDATE_USER_STATUS: WorkflowMetadataNode;
}
