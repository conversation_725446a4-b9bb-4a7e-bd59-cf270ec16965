"use client";
import { ColumnDef } from "@tanstack/react-table";
import CellActions from "@/components/common/CellActions";
import { DataTable } from "@/components/common/tables/DataTable";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import CustomIcon from "@/components/common/CustomIcons";
import { useWorkflowListStore } from "./store/workflowStore";
import { WorkflowListItem } from "./types/workflowTypes";
import { SortingState } from "@tanstack/react-table";
import DeleteDialog from "@/components/common/DeleteDialog";
import CustomPageCard from "@/components/common/CustomPageCard";

const RequestsConfigurations = () => {
  const t = useTranslations("requestsConfigurations");
  const router = useRouter();
  const { data, isLoading, fetchPaginatedData, deleteWorkflow } =
    useWorkflowListStore();
  const [paginationState, setPaginationState] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [workflowToDelete, setWorkflowToDelete] =
    useState<WorkflowListItem | null>(null);

  useEffect(() => {
    fetchPaginatedData(
      searchQuery,
      paginationState.pageIndex,
      paginationState.pageSize,
      sorting[0]?.id || "createdDate",
      sorting[0]?.desc ? "desc" : "asc",
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    searchQuery,
    paginationState.pageIndex,
    paginationState.pageSize,
    sorting,
  ]);

  const handlePaginationChange = async (
    pageIndex: number,
    pageSize: number,
  ) => {
    setPaginationState({ pageIndex, pageSize });
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setPaginationState((prev) => ({ ...prev, pageIndex: 0 }));
  };

  const handleDelete = async () => {
    if (!workflowToDelete) return;
    await deleteWorkflow(
      workflowToDelete!.id,
      paginationState.pageIndex,
      paginationState.pageSize,
      sorting[0]?.id || "createdDate",
      sorting[0]?.desc ? "desc" : "asc",
    );
    setIsDeleteDialogOpen(false);
    setWorkflowToDelete(null);
  };

  const StatusCell = ({ status }: { status: string }) => {
    const getStatusStyle = (status: string) => {
      switch (status?.toLowerCase()) {
        case "draft":
          return "bg-[#E3E3E3] text-[#979797]";
        case "operational":
          return "bg-[#E9F8E6] text-[#3BB525]";
        case "inactif":
          return "bg-[#FFE3DD] text-[#F84018]";
        default:
          return "bg-gray-100 text-gray-800";
      }
    };

    return (
      <span
        className={`inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${getStatusStyle(status)}`}
      >
        {status}
      </span>
    );
  };

  const columns: ColumnDef<WorkflowListItem>[] = [
    { accessorKey: "name", enableSorting: true, header: t("home.title") },
    {
      accessorKey: "description",
      enableSorting: true,
      header: t("home.description"),
    },
    {
      accessorKey: "status",
      header: t("home.status"),
      enableSorting: true,
      cell: ({ row }) => <StatusCell status={row.original.status} />,
    },
    {
      accessorKey: "createdDate",
      header: t("home.createdDate"),
      enableSorting: true,
      cell: ({ row }) => {
        if (!row.original.createdAt) return "-";
        const date = new Date(row.original.createdAt);
        const dateStr = date.toISOString().split("T")[0];
        const timeStr = date.toTimeString().split(" ")[0].substring(0, 5);
        return `${dateStr} at ${timeStr}`;
      },
    },
    {
      accessorKey: "updatedDate",
      header: t("home.updatedDate"),
      enableSorting: true,
      cell: ({ row }) => {
        if (!row.original.updatedAt) return "-";
        const date = new Date(row.original.updatedAt);
        const dateStr = date.toISOString().split("T")[0];
        const timeStr = date.toTimeString().split(" ")[0].substring(0, 5);
        return `${dateStr} at ${timeStr}`;
      },
    },
    {
      accessorKey: "actions",
      header: "Action",
      cell: ({ row }) => (
        <div>
          <CellActions
            actions={[
              {
                label: "Edit",
                onClick: () => {
                  router.push(
                    `/requests-configurations/new-workflow?editId=${row.original.id}`,
                  );
                },
              },
              {
                label: "Delete",
                onClick: () => {
                  setWorkflowToDelete(row.original);
                  setIsDeleteDialogOpen(true);
                },
              },
            ]}
          />
        </div>
      ),
    },
  ];

  const handleNavigate = () => {
    try {
      router.push("/requests-configurations/new-workflow");
    } catch (error) {
      console.error("Navigation failed:", error);
    }
  };
  const createIcon = () => (
    <span className="flex items-center">
      <CustomIcon
        name="addEvent"
        style={{
          width: "16px",
          height: "16px",
          fill: "#FFFFFF",
        }}
      />
    </span>
  );

  return (
    <CustomPageCard>
      <DataTable
        columns={columns}
        data={data.items || []}
        showSearchBar
        serverPagination
        serverSearch
        onSearchChange={handleSearchChange}
        onPaginationChange={handlePaginationChange}
        totalItems={data.meta.totalCount}
        sorting={sorting}
        onSortingChange={setSorting}
        currentPage={data.meta.currentPage - 1}
        pageSize={data.meta.pageSize}
        isLoading={isLoading}
        searchValue={searchQuery}
        buttons={[
          {
            show: true,
            label: t("home.addButton"),
            onClick: handleNavigate,
            variant: "default",
            icon: createIcon,
          },
        ]}
      />

      <DeleteDialog
        isDialogOpen={isDeleteDialogOpen}
        setIsDialogOpen={setIsDeleteDialogOpen}
        handleDelete={handleDelete}
        labelCancel="Cancel"
        labelConfirm="Confirm"
        loading={isLoading}
      >
        <div className="text-center">
          <p className="text-lg font-medium mb-2">
            Are you sure you want to delete this workflow?
          </p>
          {workflowToDelete && (
            <p className="text-sm text-gray-600">
              &ldquo;{workflowToDelete.name}&rdquo;
            </p>
          )}
        </div>
      </DeleteDialog>
    </CustomPageCard>
  );
};

export default RequestsConfigurations;
