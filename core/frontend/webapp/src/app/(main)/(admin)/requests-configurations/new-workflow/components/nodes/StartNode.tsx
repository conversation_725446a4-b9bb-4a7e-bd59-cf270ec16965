import { <PERSON>le, Position } from "@xyflow/react";
import { twMerge } from "tailwind-merge";
import { CustomNodeProps } from "../types/types";
import { NodeTypeEnum } from "../types/NodeEnum";

export const StartNode = ({
  id,
  data,
  isConnectable,
  className,
  showHandles = true,
  type,
}: CustomNodeProps) => {
  return (
    <div
      id={`node-${id}`}
      className={twMerge(
        "px-4 py-2 rounded-lg",
        type === NodeTypeEnum.Start ? "bg-green-200" : "",
        className,
      )}
    >
      {data.label}
      {showHandles && (
        <Handle
          type="source"
          position={Position.Bottom}
          isConnectable={isConnectable}
        />
      )}
    </div>
  );
};
