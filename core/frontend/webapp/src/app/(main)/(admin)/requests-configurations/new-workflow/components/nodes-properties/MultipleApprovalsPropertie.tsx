import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import React from "react";
import DynamicProperties from "./DynamicProperties";

interface MultipleApprovalsPropertieProps {
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
}

const MultipleApprovalsPropertie: React.FC<MultipleApprovalsPropertieProps> = ({
  localProperties,
  setLocalProperties,
}) => {
  const t = useTranslations("requestsConfigurations");
  return (
    <div className="space-y-6">
      {/* node */}
      <div className="p-2  ">
        <div
          className={`p-2 rounded-lg border-2 cursor-move flex justify-center items-center bg-blue-200 mx-auto max-w-52`}
        >
          {t("newWorkflow.multipleApprovals")}
        </div>
      </div>
      <Separator orientation="horizontal" className="border-gray-300" />
      <DynamicProperties
        nodeType="PARALLEL_APPROVAL"
        localProperties={localProperties}
        setLocalProperties={setLocalProperties}
      />
    </div>
  );
};

export default MultipleApprovalsPropertie;
