import { <PERSON>le, Position } from "@xyflow/react";
import { twMerge } from "tailwind-merge";
import { CustomNodeProps } from "../types/types";
import { NodeTypeEnum } from "../types/NodeEnum";

export const NotificationNode = ({
  id,
  data,
  isConnectable,
  className,
  showHandles = true,
  type,
}: CustomNodeProps) => {
  return (
    <div
      id={`node-${id}`}
      className={twMerge(
        "px-4 py-2 rounded-lg",
        type === NodeTypeEnum.Notification ? "bg-orange-200" : "",
        className,
      )}
    >
      {data.label}
      {showHandles && (
        <>
          <Handle
            type="target"
            position={Position.Top}
            isConnectable={isConnectable}
          />
          <Handle
            type="source"
            position={Position.Bottom}
            isConnectable={isConnectable}
          />
        </>
      )}
    </div>
  );
};
