# Unique Nodes Implementation

## Overview

This implementation ensures that only one node of the same type can be placed in the workflow, similar to how start nodes work. This prevents users from accidentally creating multiple instances of nodes that should be unique.

## Changes Made

### 1. WorkflowBuilder.tsx

- **Enhanced `onDrop` function**: Added logic to check for existing nodes of unique types before allowing new nodes to be placed
- **Unique node types**: All node types (Start, Finish, Document, Condition, Single Approval, Multiple Approvals, and Notification) are now restricted to one instance each
- **Toast notifications**: Added user-friendly error messages when attempting to place duplicate nodes
- **Visual feedback**: Users receive immediate feedback when trying to add duplicate nodes

### 2. ToolBox.tsx

- **Visual indicators**: Nodes that are already placed are visually disabled and show a checkmark
- **Drag prevention**: Disabled nodes cannot be dragged
- **Tooltips**: Added helpful tooltips to indicate node status
- **Dynamic styling**: Placed nodes have grayed-out appearance

## Unique Node Types

The following node types are restricted to one instance each:

- **Start Node**: Only one start node allowed per workflow
- **Finish Node**: Only one finish node allowed per workflow
- **Document Node**: Only one document node allowed per workflow
- **Condition Node**: Only one condition node allowed per workflow
- **Single Approval Node**: Only one single approval node allowed per workflow
- **Multiple Approvals Node**: Only one multiple approvals node allowed per workflow
- **Notification Node**: Only one notification node allowed per workflow

## User Experience

1. **Visual Feedback**: Users can see which nodes are already placed in the toolbox
2. **Error Prevention**: Drag and drop is prevented for already placed unique nodes
3. **Clear Messaging**: Toast notifications explain why a node cannot be placed
4. **Intuitive Design**: Checkmarks and grayed-out styling make it clear which nodes are used

## Technical Implementation

- Uses Zustand store to track current nodes
- Implements type checking for unique node types
- Integrates with existing toast notification system
- Maintains backward compatibility with existing workflow functionality

## Future Enhancements

- Configurable unique node types through settings
- Different validation rules for different workflow types
- Enhanced visual feedback for complex workflows
