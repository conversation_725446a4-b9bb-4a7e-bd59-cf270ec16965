export enum NodeEnum {
  Start = "START",
  Document = "DOCUMENT",
  Approvals = "APPROVALS",
  SingleApproval = "SINGLE APPROVAL",
  MultipleApprovals = "MULTIPLE APPROVALS",
  Notification = "NOTIFICATION",
  Condition = "CONDITION",
  Finish = "FINISH",
}

export enum NodeTypeEnum {
  Start = "start",
  Document = "document",
  Approvals = "approvals",
  SingleApproval = "singleApproval",
  MultipleApprovals = "multipleApprovals",
  Notification = "notification",
  Condition = "condition",
  Finish = "finish",
}
