import { memo, useState, useEffect, useCallback } from "react";
import { Trash2, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { IconButton } from "@/components/common/CustomButtons";
import { NodeTypeEnum } from "./types/NodeEnum";
import { useWorkflowStore } from "../../store/workflowStore";
import NotificationPropertie from "./nodes-properties/NotificationPropertie";
import StartPropertie from "./nodes-properties/StartPropertie";
import DocumentPropertie from "./nodes-properties/DocumentPropertie";
import SingleApprovalPropertie from "./nodes-properties/SingleApprovalPropertie";
import MultipleApprovalsPropertie from "./nodes-properties/MultipleApprovalsPropertie";
import ConditionPropertie from "./nodes-properties/ConditionPropertie";
import FinishPropertie from "./nodes-properties/FinishPropertie";
import ApprovalsPropertie from "./nodes-properties/ApprovalsPropertie";

// --- NEW: localPropertiesMap for all nodes ---
const localPropertiesMapGlobal: { [nodeId: string]: Record<string, unknown> } =
  {};

// Add at the top, after imports:
declare global {
  interface Window {
    getAllNodeLocalProperties?: () => Record<string, Record<string, unknown>>;
  }
}

const PropertiesPanelComponent = () => {
  const t = useTranslations("requestsConfigurations");
  const { removeNode, selectedNode, setSelectedNode } = useWorkflowStore();
  const [localPropertiesMap, setLocalPropertiesMap] = useState<{
    [nodeId: string]: Record<string, unknown>;
  }>(localPropertiesMapGlobal);
  const [localProperties, setLocalProperties] = useState<
    Record<string, unknown>
  >(selectedNode?.data || {});

  useEffect(() => {
    if (selectedNode) {
      setLocalProperties(
        localPropertiesMap[selectedNode.id] || selectedNode.data || {},
      );
    }
  }, [selectedNode, localPropertiesMap]);

  // Save local edits for this node
  const handleSetLocalProperties = (props: Record<string, unknown>) => {
    if (!selectedNode) return;
    setLocalProperties(props);
    setLocalPropertiesMap((prev) => {
      const updated = { ...prev, [selectedNode.id]: props };
      // Also update the global reference for access in main save
      Object.assign(localPropertiesMapGlobal, updated);
      return updated;
    });
  };

  // Expose a function to get all local properties for all nodes
  window.getAllNodeLocalProperties = () => ({ ...localPropertiesMapGlobal });

  const onNodeDelete = useCallback(
    (nodeId: string) => {
      removeNode(nodeId);
      setSelectedNode(null);
      setLocalPropertiesMap((prev) => {
        const updated = { ...prev };
        delete updated[nodeId];
        Object.assign(localPropertiesMapGlobal, updated);
        return updated;
      });
    },
    [removeNode, setSelectedNode],
  );

  if (!selectedNode) {
    return (
      <div className="w-96 p-4 bg-white border-l h-full overflow-y-auto">
        <p className="text-gray-500">Select a node to view properties</p>
      </div>
    );
  }

  return (
    <div className="w-96 p-4 bg-white border-l flex flex-col h-full overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">{t("newWorkflow.properties")}</h2>
        <button
          onClick={() => setSelectedNode(null)}
          className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          title="Close properties panel"
        >
          <X className="w-5 h-5 text-gray-500 hover:text-gray-700" />
        </button>
      </div>
      {selectedNode.type === NodeTypeEnum.Start && (
        <StartPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {selectedNode.type === NodeTypeEnum.Document && (
        <DocumentPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {selectedNode.type === NodeTypeEnum.SingleApproval && (
        <SingleApprovalPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {selectedNode.type === NodeTypeEnum.MultipleApprovals && (
        <MultipleApprovalsPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {selectedNode.type === NodeTypeEnum.Approvals && (
        <ApprovalsPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {selectedNode.type === NodeTypeEnum.Notification && (
        <NotificationPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {selectedNode.type === NodeTypeEnum.Condition && (
        <ConditionPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}

      {selectedNode.type === NodeTypeEnum.Finish && (
        <FinishPropertie
          localProperties={localProperties}
          setLocalProperties={handleSetLocalProperties}
        />
      )}
      {/* Add a spacer here */}
      <div className="flex-grow" />

      {/* IconButton stays at the bottom */}
      <IconButton
        variant="link"
        className="w-full text-lg text-red-500 flex items-center justify-start mt-4"
        onClick={() => onNodeDelete(selectedNode.id)}
        icon={Trash2}
        label={t("newWorkflow.deleteNode")}
      />
    </div>
  );
};

export const PropertiesPanel = memo(PropertiesPanelComponent);
PropertiesPanel.displayName = "PropertiesPanel";
