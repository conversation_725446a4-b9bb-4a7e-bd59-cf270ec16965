# Workflow Properties Panel - Option Handling

This document explains how the workflow properties panel handles different types of options for select, multiselect, and checkbox_list fields.

## Option Formats

The workflow properties panel now supports two different formats for options:

### 1. Simple String Options

For simple string-based options, use an array of strings:

```json
{
  "label": "role",
  "type": "select",
  "required": true,
  "options": ["Admin", "User", "Guest"]
}
```

**Behavior:**

- Display: Shows the string value as the label
- Backend: Sends the same string value to the backend

### 2. Object Options with ID/Name

For options that need to display a name but send an ID to the backend, use an array of objects:

```json
{
  "label": "userRole",
  "type": "select",
  "required": true,
  "options": [
    { "id": "role1", "name": "Administrator" },
    { "id": "role2", "name": "Regular User" },
    { "id": "role3", "name": "Guest User" }
  ]
}
```

**Behavior:**

- Display: Shows the `name` field as the label
- Backend: Sends the `id` field to the backend

## Supported Field Types

This functionality works with the following field types:

### Select

- Single selection dropdown
- Supports both string and object options

### Multiselect

- Multiple selection dropdown
- Supports both string and object options

### Checkbox List

- Multiple checkbox selection
- Supports both string and object options

## Implementation Details

### Frontend Changes

1. **Type Definitions**: Updated `WorkflowOption` type to support both formats
2. **Utility Functions**: Added helper functions to process options:

   - `processOptions()`: Converts options to the format expected by UI components
   - `getDisplayValue()`: Gets the display value for single selection
   - `getDisplayValues()`: Gets the display values for multiple selection

3. **Value Conversion**: The components automatically handle:
   - Converting display values back to actual values for backend submission
   - Maintaining the correct data types for both formats

### Backend Integration

The backend already supports both formats through the existing `ConfigField` interface:

```typescript
interface ConfigField {
  // ... other fields
  options?: any[]; // Can be string[] or {id: string, name: string}[]
  dataSource?: DataSourceConfig; // For dynamic options from database
}
```

## Usage Examples

### Example 1: Simple String Options

```json
{
  "label": "notificationChannels",
  "type": "multiselect",
  "required": true,
  "options": ["Email", "SMS", "Push", "In-App"]
}
```

### Example 2: Object Options

```json
{
  "label": "approvers",
  "type": "multiselect",
  "required": true,
  "options": [
    { "id": "user1", "name": "John Doe" },
    { "id": "user2", "name": "Jane Smith" },
    { "id": "role1", "name": "Department Manager" }
  ]
}
```

### Example 3: Mixed Usage in Object Fields

```json
{
  "label": "approvalConfig",
  "type": "object",
  "required": true,
  "fields": [
    {
      "label": "approvalType",
      "type": "select",
      "required": true,
      "options": ["Single", "Multiple", "Parallel"]
    },
    {
      "label": "approvers",
      "type": "multiselect",
      "required": true,
      "options": [
        { "id": "role1", "name": "Manager" },
        { "id": "role2", "name": "Director" },
        { "id": "user1", "name": "John Doe" }
      ]
    }
  ]
}
```

## Migration Notes

- Existing configurations with string options will continue to work without changes
- New configurations can use either format based on requirements
- The system automatically detects the format and handles it appropriately
