"use client";

import { use<PERSON><PERSON>back, useEffect, useRef } from "react";
// import "reactflow/dist/style.css";
import { useWorkflowStore } from "../../store/workflowStore";
import { useWorkflowMetadataStore } from "../../store/workflowStore";
import { Toolbox } from "./ToolBox";
import { PropertiesPanel } from "./PropertiesPanel";
import { StartNode } from "./nodes/StartNode";
import { DocumentNode } from "./nodes/DocumentNode";
import { NotificationNode } from "./nodes/NotificationNode";
import { ConditionNode } from "./nodes/ConditionNode";
import { FinishNode } from "./nodes/FinishNode";
import ApprovalsNode from "./nodes/ApprovalsNode";
import {
  Background,
  Connection,
  Controls,
  EdgeChange,
  MiniMap,
  NodeChange,
  NodeTypes,
  ReactFlow,
  useReactFlow,
} from "@xyflow/react";
import { TNode } from "./types/types";
import { NodeEnum, NodeTypeEnum } from "./types/NodeEnum";
import "@xyflow/react/dist/style.css";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { toast } from "@/hooks/use-toast";
import { WorkflowNode, WorkflowEdge } from "../../types/workflowTypes";

const nodeTypes: NodeTypes = {
  start: StartNode,
  document: DocumentNode,
  notification: NotificationNode,
  condition: ConditionNode,
  finish: FinishNode,
  approvals: ApprovalsNode,
};

// Utility: Map backend config to form state using metadata fields
function mapConfigToFormState(
  config: unknown,
  metadataFields: unknown[],
): unknown {
  if (!metadataFields) return config;
  const result: Record<string, unknown> = {};
  for (const field of metadataFields as Array<Record<string, unknown>>) {
    const f = field as Record<string, unknown>;
    const key = f.label as string;
    if (
      f.type === "object" &&
      config &&
      typeof config === "object" &&
      key in config &&
      (config as Record<string, unknown>)[key]
    ) {
      result[key] = mapConfigToFormState(
        (config as Record<string, unknown>)[key],
        f.fields as unknown[],
      );
    } else if (
      f.type === "array" &&
      config &&
      typeof config === "object" &&
      Array.isArray((config as Record<string, unknown>)[key])
    ) {
      const arr = (config as Record<string, unknown>)[key] as unknown[];
      if (
        Array.isArray(f.fields) &&
        f.fields.length === 1 &&
        (f.fields as Array<Record<string, unknown>>)[0].type === "input"
      ) {
        result[key] = arr.map((val) => ({
          [(f.fields as Array<Record<string, unknown>>)[0].label as string]:
            val,
        }));
      } else if (Array.isArray(f.fields)) {
        result[key] = arr.map((item) =>
          mapConfigToFormState(item, f.fields as unknown[]),
        );
      } else {
        result[key] = arr;
      }
    } else if (config && typeof config === "object" && key in config) {
      result[key] = (config as Record<string, unknown>)[key];
    }
  }
  return result;
}

export default function WorkflowBuilder({
  initialNodes,
  initialEdges,
}: {
  initialNodes?: WorkflowNode[];
  initialEdges?: WorkflowEdge[];
}) {
  const { screenToFlowPosition } = useReactFlow();
  const {
    nodes,
    edges,
    updateNodes,
    updateEdges,
    addNode,
    updateNode,
    clearFlow,
    undoAction,
    redoAction,
    addEdge,
    selectedNode,
    setSelectedNode,
    set,
  } = useWorkflowStore();
  const { metadata } = useWorkflowMetadataStore();
  const t = useTranslations("requestsConfigurations");
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // Function to get translated label for node type
  const getTranslatedLabel = useCallback(
    (nodeEnum: NodeEnum): string => {
      const labelMap: Record<NodeEnum, string> = {
        [NodeEnum.Start]: t("newWorkflow.start"),
        [NodeEnum.Document]: t("newWorkflow.document"),
        [NodeEnum.Approvals]: t("newWorkflow.approvals"),
        [NodeEnum.SingleApproval]: t("newWorkflow.singleApproval"),
        [NodeEnum.MultipleApprovals]: t("newWorkflow.multipleApprovals"),
        [NodeEnum.Notification]: t("newWorkflow.notification"),
        [NodeEnum.Condition]: t("newWorkflow.condition"),
        [NodeEnum.Finish]: t("newWorkflow.finish"),
      };
      return labelMap[nodeEnum] || nodeEnum;
    },
    [t],
  );

  const onNodesChange = useCallback(
    (changes: NodeChange[]) => updateNodes(changes),
    [updateNodes],
  );

  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) => updateEdges(changes),
    [updateEdges],
  );

  const onConnect = useCallback(
    (connection: Connection) => addEdge(connection),
    [addEdge],
  );

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const type = event.dataTransfer.getData(
        "application/reactflow",
      ) as NodeEnum;
      if (!type) return;

      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      const typeMap: Record<NodeEnum, NodeTypeEnum> = {
        [NodeEnum.SingleApproval]: NodeTypeEnum.SingleApproval,
        [NodeEnum.MultipleApprovals]: NodeTypeEnum.MultipleApprovals,
        [NodeEnum.Start]: NodeTypeEnum.Start,
        [NodeEnum.Document]: NodeTypeEnum.Document,
        [NodeEnum.Approvals]: NodeTypeEnum.Approvals,
        [NodeEnum.Notification]: NodeTypeEnum.Notification,
        [NodeEnum.Condition]: NodeTypeEnum.Condition,
        [NodeEnum.Finish]: NodeTypeEnum.Finish,
      };

      const nodeType = typeMap[type];
      if (!nodeType) return; // Exit if the type is invalid

      // Check for existing nodes that should be unique
      const uniqueNodeTypes = [
        NodeTypeEnum.Start,
        NodeTypeEnum.Finish,
        NodeTypeEnum.Document,
        NodeTypeEnum.Condition,
        NodeTypeEnum.SingleApproval,
        NodeTypeEnum.MultipleApprovals,
        NodeTypeEnum.Notification,
      ];

      const isUniqueNodeType = uniqueNodeTypes.includes(nodeType);
      const existingNodeOfSameType = nodes.some(
        (node) => node.type === nodeType,
      );

      if (isUniqueNodeType && existingNodeOfSameType) {
        const nodeTypeName = type.replace(/([A-Z])/g, " $1").trim();
        toast({
          title: "Node Already Exists",
          description: `Only one ${nodeTypeName} node can be placed in the workflow.`,
          variant: "destructive",
        });
        return;
      }

      const newNode: TNode = {
        id: `node-${Date.now()}`,
        type: nodeType,
        position: { x: position.x - 64, y: position.y - 16 },
        data: { label: getTranslatedLabel(type) },
        isConnectable: true,
        dragging: false,
        zIndex: 0,
        positionAbsoluteX: position.x - 64,
        positionAbsoluteY: position.y - 16,
        properties: {},
      };

      addNode(newNode);
    },
    [screenToFlowPosition, addNode, nodes, getTranslatedLabel],
  );

  const onNodeClick = useCallback(
    (_: React.MouseEvent, node: unknown) => {
      setSelectedNode(node as TNode); // Use store's setSelectedNode
    },
    [setSelectedNode],
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === "z") {
        undoAction();
      } else if (
        (event.ctrlKey && event.key === "y") ||
        (event.ctrlKey && event.shiftKey && event.key === "z")
      ) {
        redoAction();
      }
    },
    [undoAction, redoAction],
  );

  const handleClearFlow = () => {
    clearFlow();
    setSelectedNode(null);
  };

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  const didUpdate = useRef(false);

  useEffect(() => {
    if (didUpdate.current) return;
    const startNode = nodes.find((node) => node.type === "start");
    if (startNode && startNode.data.label === "START") {
      updateNode(startNode.id, { label: t("newWorkflow.start") });
      didUpdate.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t, updateNode]);

  useEffect(() => {
    if (!initialNodes || !metadata) {
      console.log("⏳ WorkflowBuilder waiting for data:", {
        hasInitialNodes: !!initialNodes,
        hasMetadata: !!metadata,
        initialNodesCount: initialNodes?.length || 0,
      });
      return;
    }

    console.log("✅ WorkflowBuilder processing nodes with metadata:", {
      nodesCount: initialNodes.length,
      metadataKeys: Object.keys(metadata || {}),
    });

    const mappedNodes = initialNodes.map((node) => {
      // Map API node types to internal node types and labels
      const mapNodeType = (type: string) => {
        switch (type.toUpperCase()) {
          case "START":
            return { type: "start", label: t("newWorkflow.start") };
          case "END":
            return { type: "finish", label: t("newWorkflow.finish") };
          case "SINGLE_APPROVAL":
            return {
              type: "approvals",
              label: t("newWorkflow.singleApproval"),
            };
          case "PARALLEL_APPROVAL":
            return {
              type: "approvals",
              label: t("newWorkflow.multipleApprovals"),
            };
          case "DOCUMENT":
            return { type: "document", label: t("newWorkflow.document") };
          case "NOTIFICATION":
            return {
              type: "notification",
              label: t("newWorkflow.notification"),
            };
          case "CONDITION":
            return { type: "condition", label: t("newWorkflow.condition") };
          case "UPDATE_USER_STATUS":
            return {
              type: "notification",
              label: t("newWorkflow.notification"),
            };
          default:
            return { type: type.toLowerCase(), label: type };
        }
      };

      const mappedType = mapNodeType(node.type);
      // Get metadata fields for this node type (backend type)
      const metaFields = (
        metadata as unknown as Record<string, { fields?: unknown[] }>
      )?.[node.type]?.fields;
      console.log(`🔧 Mapping node ${node.type}:`, {
        mappedType: mappedType.type,
        hasMetaFields: !!metaFields,
        metaFieldsCount: metaFields?.length || 0,
        nodeConfig: node.config,
      });

      // Map config to form state
      const mappedConfig = mapConfigToFormState(
        node.config || {},
        metaFields ?? [],
      );
      return {
        ...node,
        type: mappedType.type,
        data: {
          label: mappedType.label,
          ...(mappedConfig ?? {}),
          // Preserve original styling and properties
          originalType: node.type,
          originalTitle: node.title,
          formId: node.formId,
          // Keep any existing styling from the config
          ...(node.config?.fields ? { fields: node.config.fields } : {}),
          ...(node.config?.initiators
            ? { initiators: node.config.initiators }
            : {}),
          ...(node.config?.approver ? { approver: node.config.approver } : {}),
          ...(node.config?.notification
            ? { notification: node.config.notification }
            : {}),
          ...(node.config?.onReject ? { onReject: node.config.onReject } : {}),
        },
      };
    });

    console.log("✅ Setting mapped nodes:", mappedNodes);
    set({ nodes: mappedNodes });
    if (initialEdges) {
      set({ edges: initialEdges });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialNodes, initialEdges, t, metadata]);

  return (
    <div className="flex h-screen min-h-0">
      <Toolbox nodes={nodes} />
      <div className="flex-1 min-w-0" ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onDrop={onDrop}
          onNodeClick={onNodeClick}
          nodeTypes={nodeTypes}
          onDragOver={(e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = "move";
          }}
          fitView
          proOptions={{ hideAttribution: true }}
        >
          <MiniMap />
          <Controls />
          <Background />
        </ReactFlow>
      </div>

      <div className="absolute bottom-4 right-4 space-x-2">
        <Button
          type="button"
          variant="destructive"
          className="w-full"
          onClick={handleClearFlow}
        >
          {t("newWorkflow.clearFlow")}
        </Button>
      </div>

      {selectedNode && <PropertiesPanel />}
    </div>
  );
}
