import React from "react";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import DynamicProperties from "./DynamicProperties";

interface SingleApprovalPropertieProps {
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
}

const SingleApprovalPropertie: React.FC<SingleApprovalPropertieProps> = ({
  localProperties,
  setLocalProperties,
}) => {
  const t = useTranslations("requestsConfigurations");
  return (
    <div className="space-y-6">
      {/* node */}
      <div className="p-2  ">
        <div
          className={`p-2 rounded-lg border-2 cursor-move flex justify-center items-center bg-blue-200 mx-auto max-w-52`}
        >
          {t("newWorkflow.singleApproval")}
        </div>
      </div>
      <Separator orientation="horizontal" className="border-gray-300" />
      <DynamicProperties
        nodeType="SINGLE_APPROVAL"
        localProperties={localProperties}
        setLocalProperties={setLocalProperties}
      />
    </div>
  );
};

export default SingleApprovalPropertie;
