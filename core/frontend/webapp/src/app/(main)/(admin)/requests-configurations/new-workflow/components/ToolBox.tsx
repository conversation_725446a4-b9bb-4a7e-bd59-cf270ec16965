import { memo } from "react";
import { useTranslations } from "next-intl";
import { NodeEnum, NodeTypeEnum } from "./types/NodeEnum";

interface TNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: { [key: string]: unknown; label: string };
  properties?: Record<string, unknown>;
}

const tools = [
  {
    type: NodeEnum.Start,
    nodeType: NodeTypeEnum.Start,
    labelKey: "newWorkflow.start",
    className: "bg-green-200",
    isUnique: true,
  },
  {
    type: NodeEnum.Document,
    nodeType: NodeTypeEnum.Document,
    labelKey: "newWorkflow.document",
    className: "bg-yellow-200",
    isUnique: true,
  },
  {
    type: NodeEnum.Approvals,
    nodeType: NodeTypeEnum.Approvals,
    labelKey: "newWorkflow.approvals",
    className: "bg-blue-200",
    isUnique: true,
  },
  {
    type: NodeEnum.Notification,
    nodeType: NodeTypeEnum.Notification,
    labelKey: "newWorkflow.notification",
    className: "bg-orange-200",
    isUnique: true,
  },
  {
    type: NodeEnum.Condition,
    nodeType: NodeTypeEnum.Condition,
    labelKey: "newWorkflow.condition",
    className: "bg-gray-200",
    isUnique: true,
  },
  {
    type: NodeEnum.Finish,
    nodeType: NodeTypeEnum.Finish,
    labelKey: "newWorkflow.finish",
    className: "bg-green-200",
    isUnique: true,
  },
];

interface ToolboxProps {
  nodes: TNode[];
}

export const Toolbox = memo(({ nodes }: ToolboxProps) => {
  const t = useTranslations("requestsConfigurations");

  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData("application/reactflow", nodeType);
    event.dataTransfer.effectAllowed = "move";
  };

  const isNodeTypePlaced = (nodeType: NodeTypeEnum) => {
    return nodes.some((node) => node.type === nodeType);
  };

  return (
    <div className="w-64 bg-white border-r flex flex-col h-full">
      <div className="p-2 border-b flex-shrink-0">
        <h2 className="text-lg font-semibold mb-4">
          {t("newWorkflow.toolBox")}
        </h2>
      </div>
      <div className="flex-1 overflow-y-auto p-2 min-h-0">
        <div className="space-y-2 p-4">
          {tools.map((tool) => {
            const isPlaced = tool.isUnique && isNodeTypePlaced(tool.nodeType);
            return (
              <div
                key={tool.type}
                className={`p-3 rounded-lg border-2 flex justify-center items-center ${
                  isPlaced
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : `${tool.className} cursor-move`
                }`}
                onDragStart={
                  isPlaced ? undefined : (e) => onDragStart(e, tool.type)
                }
                draggable={!isPlaced}
                title={
                  isPlaced
                    ? `${t(tool.labelKey)} already placed`
                    : `Drag to add ${t(tool.labelKey)}`
                }
              >
                {t(tool.labelKey)}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
});

Toolbox.displayName = "Toolbox";
