# Workflow Configuration Tests

This directory contains comprehensive tests for the workflow configuration functionality, following the same pattern as the forms-management tests.

## Test Structure

```
test/
├── store/
│   └── workflowStore.test.ts    # Tests for workflow store CRUD operations
├── pages/
│   ├── page.test.tsx           # Tests for main requests-configurations page
│   └── newWorkflow.test.tsx    # Tests for new-workflow page
├── components/
│   └── PropertiesPanel.test.tsx # Tests for PropertiesPanel component
├── jest.setup.ts                # Jest configuration and mocks
├── tsconfig.jest.json          # TypeScript configuration for tests
├── simple.test.ts              # Basic test to verify setup
└── README.md                   # This file
```

## Test Coverage

### Workflow Store Tests (`workflowStore.test.ts`)

The tests cover all CRUD operations and state management functions:

### Page Component Tests (`pages/page.test.tsx`)

The tests cover the main requests-configurations page functionality:

#### CRUD Operations

- ✅ **Data Fetching**: Pagination, sorting, and search functionality
- ✅ **Navigation**: Add new workflow and edit existing workflows
- ✅ **Delete Workflow**: Delete confirmation dialog and API calls
- ✅ **Status Display**: Proper status styling and formatting
- ✅ **Date Formatting**: Correct date/time display in table

#### User Interactions

- ✅ **Search**: Real-time search with pagination reset
- ✅ **Pagination**: Page navigation and size changes
- ✅ **Sorting**: Column sorting with proper API calls
- ✅ **Actions**: Edit and delete actions for each workflow

#### Error Handling

- ✅ **Empty States**: Handle empty workflow lists
- ✅ **Loading States**: Show loading indicators
- ✅ **Navigation Errors**: Graceful error handling

### New Workflow Page Tests (`pages/newWorkflow.test.tsx`)

The tests cover the workflow creation and editing functionality:

#### Form Management

- ✅ **Create Mode**: Initialize empty form and create new workflow
- ✅ **Edit Mode**: Load existing workflow data and update
- ✅ **Form Validation**: Proper form submission and validation
- ✅ **Status Management**: Draft, Operational, Inactive statuses

#### Node Management

- ✅ **Node Type Transformation**: APPROVALS → SINGLE/MULTIPLE_APPROVALS, FINISH → END
- ✅ **Properties to Fields**: Transform node properties to backend format
- ✅ **Local Properties**: Persist local node edits before saving
- ✅ **Global Properties**: Access to all node local properties

#### Workflow Builder Integration

- ✅ **Initial Data**: Pass correct initial nodes and edges
- ✅ **Reset Functionality**: Clear form and workflow on mount/unmount
- ✅ **Component Integration**: WorkflowHeader, WorkflowBuilder, ReactFlowProvider

#### Error Handling

- ✅ **API Errors**: Handle creation and update failures
- ✅ **Missing Data**: Graceful handling of missing workflow data
- ✅ **Navigation**: Proper redirects after successful operations

### Properties Panel Tests (`components/PropertiesPanel.test.tsx`)

The tests cover the node properties management functionality:

#### Node Type Rendering

- ✅ **All Node Types**: Start, Document, Single/Multiple Approvals, Notification, Condition, Finish
- ✅ **Property Components**: Correct property component rendering for each type
- ✅ **Data Binding**: Proper data flow between node and property components

#### Local Properties Management

- ✅ **Property Updates**: Real-time property updates and persistence
- ✅ **Global Access**: Window function for accessing all node properties
- ✅ **Property Clearing**: Clear properties when nodes are deleted

#### Node Operations

- ✅ **Node Deletion**: Remove nodes and clear associated properties
- ✅ **Panel Controls**: Close panel and deselect nodes
- ✅ **Property Synchronization**: Sync local properties with global state

#### Error Handling

- ✅ **Missing Data**: Handle nodes without data gracefully
- ✅ **Global Function**: Handle missing global function gracefully
- ✅ **Component Integration**: Proper integration with child components

#### State Management

- ✅ Initialize with default values
- ✅ Add, update, and remove nodes
- ✅ Add and remove edges
- ✅ Reset workflow state
- ✅ Undo/redo functionality
- ✅ History management

#### CRUD Operations

- ✅ **Create Workflow**: POST `/workflowengine/workflows`

  - Success case with proper toast messages
  - Error handling with validation errors
  - No toast when no message in response

- ✅ **Update Workflow**: PUT `/workflowengine/workflows/{id}`

  - Success case with proper toast messages
  - Error handling for not found workflows

- ✅ **Fetch Paginated Data**: GET `/workflowengine/workflows`

  - Success case with pagination metadata
  - Error handling with proper toast messages
  - No toast when no error message

- ✅ **Search Workflows**: GET `/workflowengine/workflows/search`

  - Success case with search results
  - Error handling for search failures

- ✅ **Delete Workflow**: DELETE `/workflowengine/workflows/{id}`

  - Success case with proper toast messages
  - Error handling for not found workflows

- ✅ **Get Workflow by ID**: GET `/workflowengine/workflows/{id}`
  - Success case returning workflow data
  - Error handling returning null

#### Metadata Store Tests

- ✅ **Fetch Metadata**: GET `/workflowengine/workflow-metadata`
  - Success case with node types, form types, and statuses
  - Error handling with proper toast messages

#### Message Extraction Tests

- ✅ Extract messages from first level response
- ✅ Extract messages from nested validation errors
- ✅ Handle empty responses

## Mocked Dependencies

The tests mock the following dependencies:

- `@/lib/axios` - API client
- `@/hooks/use-toast` - Toast notifications
- `next/navigation` - Next.js router
- `next/image` - Next.js image component
- `@xyflow/react` - React Flow components
- `zustand` - State management

## Running Tests

To run the tests, use the following commands:

```bash
# Run all tests in this directory
npm test -- requests-configurations

# Run tests with coverage
npm test -- requests-configurations --coverage

# Run tests in watch mode
npm test -- requests-configurations --watch

# Run a specific test file
npm test -- workflowStore.test.ts
```

## Test Patterns

The tests follow these patterns from the forms-management tests:

1. **Setup**: Each test suite resets mocks and store state in `beforeEach`
2. **State Management**: Test individual state mutations and their effects
3. **API Calls**: Mock axios calls and verify correct endpoints and payloads
4. **Error Handling**: Test both success and error scenarios
5. **Toast Messages**: Verify proper toast notifications for user feedback
6. **Message Extraction**: Test the helper function for extracting messages from API responses

## Key Features Tested

### Workflow Builder State

- Node and edge management
- History and undo/redo functionality
- Workflow reset functionality

### API Integration

- All CRUD operations with proper error handling
- Pagination and search functionality
- Metadata fetching

### User Experience

- Toast notifications for success and error states
- Proper message extraction from backend responses
- Loading states and error handling

## Adding New Tests

When adding new functionality to the workflow configuration:

1. Add state management tests for new store functions
2. Add API integration tests for new endpoints
3. Add error handling tests for new failure scenarios
4. Update this README with new test coverage

## Test Data

The tests use realistic mock data that matches the expected API responses:

- Workflow objects with proper structure
- Pagination metadata
- Error responses with validation details
- Node and edge data for workflow builder

This ensures tests accurately reflect real-world usage and catch potential issues early.
