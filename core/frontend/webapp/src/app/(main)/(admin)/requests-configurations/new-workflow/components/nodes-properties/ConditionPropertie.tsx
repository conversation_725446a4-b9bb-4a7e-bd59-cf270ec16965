import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import React from "react";
import DynamicProperties from "./DynamicProperties";

interface ConditionPropertieProps {
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
}

const ConditionPropertie: React.FC<ConditionPropertieProps> = ({
  localProperties,
  setLocalProperties,
}) => {
  const t = useTranslations("requestsConfigurations");
  return (
    <div className="space-y-6">
      {/* node */}
      <div className="p-2  ">
        <div
          className={`p-2 rounded-lg border-2 cursor-move flex justify-center items-center bg-gray-200 mx-auto max-w-52`}
        >
          {t("newWorkflow.condition")}
        </div>
      </div>
      <Separator orientation="horizontal" className="border-gray-300" />
      <DynamicProperties
        nodeType="CONDITION"
        localProperties={localProperties}
        setLocalProperties={setLocalProperties}
      />
    </div>
  );
};

export default ConditionPropertie;
