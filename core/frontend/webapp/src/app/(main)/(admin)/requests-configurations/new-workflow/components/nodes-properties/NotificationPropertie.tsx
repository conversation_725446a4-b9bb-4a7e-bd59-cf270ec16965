import React, { useEffect } from "react";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import { useWorkflowStore } from "../../../store/workflowStore";
import { useWorkflowMetadataStore } from "../../../store/workflowStore";
import DynamicProperties from "./DynamicProperties";

interface NotificationPropertieProps {
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
}
const NotificationPropertie: React.FC<NotificationPropertieProps> = ({
  localProperties,
  setLocalProperties,
}) => {
  useWorkflowStore();
  const { metadata, fetchMetadata } = useWorkflowMetadataStore();
  const t = useTranslations("requestsConfigurations");

  useEffect(() => {
    if (!metadata) {
      fetchMetadata();
    }
  }, [metadata, fetchMetadata]);

  return (
    <div className="space-y-6 flex-grow">
      <div className="p-2  ">
        <div
          className={`p-2 rounded-lg border-2 cursor-move flex justify-center items-center bg-orange-200 mx-auto max-w-52`}
        >
          {t("newWorkflow.notification")}
        </div>
      </div>
      <Separator orientation="horizontal" className="border-gray-300" />
      <DynamicProperties
        nodeType="NOTIFICATION"
        localProperties={localProperties}
        setLocalProperties={setLocalProperties}
      />
    </div>
  );
};

export default NotificationPropertie;
