import { NodeProps } from "@xyflow/react";
import { NodeEnum } from "./NodeEnum";

export interface TNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: { label: string };
  isConnectable: boolean;
  dragging: boolean;
  zIndex: number;
  positionAbsoluteX: number;
  positionAbsoluteY: number;
  properties?: Record<string, unknown>;
}

export type TNodeType = NodeEnum;

export interface CustomNodeProps extends NodeProps {
  id: string;
  type: string;
  data: { label: string; [key: string]: string | number | boolean };
  isConnectable: boolean;
  className?: string;
  showHandles?: boolean;
}
