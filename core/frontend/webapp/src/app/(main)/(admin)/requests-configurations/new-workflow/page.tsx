"use client";
import React from "react";
import { WorkflowHeader } from "./components/WorkflowHeader";
import WorkflowBuilder from "./components/WorkflowBuilder";
import { Separator } from "@/components/ui/separator";
import { ReactFlowProvider } from "@xyflow/react";
import { useWorkflowStore } from "../store/workflowStore";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import {
  useWorkflowListStore,
  useWorkflowMetadataStore,
} from "../store/workflowStore";
import { useEffect, useState, useCallback } from "react";
import { WorkflowFull } from "../types/workflowTypes";
import CustomPageCard from "@/components/common/CustomPageCard";

declare global {
  interface Window {
    getAllNodeLocalProperties?: () => Record<string, Record<string, unknown>>;
  }
}

export interface Node {
  id: string;
  type?: string;
  position: { x: number; y: number };
  data: { label: string; [key: string]: string | number | boolean };
}

export interface NotificationConfig {
  types: {
    all: boolean;
    mail: boolean;
    inSystem: boolean;
    sms: boolean;
  };
  content: string;
  receivers: {
    all: boolean;
    nPlusOne: boolean;
    self: boolean;
  };
}
type FormData = {
  title: string;
  description: string;
  status: string;
};

// Helper function to group flat dot-notation keys into nested objects
function groupFlatKeys(data: Record<string, unknown>): Record<string, unknown> {
  const result: Record<string, unknown> = {};

  Object.entries(data).forEach(([key, value]) => {
    // Skip UI-specific keys
    if (
      ["label", "form", "originalType", "originalTitle", "formId"].includes(key)
    ) {
      return;
    }

    if (key.includes(".")) {
      // Handle dot notation (e.g., "initiators.roles.0")
      const parts = key.split(".");
      let current = result;

      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (!current[part]) {
          // Check if next part is a number to determine if it's an array
          const nextPart = parts[i + 1];
          if (!isNaN(Number(nextPart))) {
            current[part] = [];
          } else {
            current[part] = {};
          }
        }
        current = current[part] as Record<string, unknown>;
      }

      const lastPart = parts[parts.length - 1];
      if (!isNaN(Number(lastPart))) {
        // Array index
        const arrayKey = parts[parts.length - 2];
        const parentKey = parts.slice(0, -2).join(".");
        let parent = result;

        if (parentKey) {
          const parentParts = parentKey.split(".");
          for (const part of parentParts) {
            parent = parent[part] as Record<string, unknown>;
          }
        }

        if (!Array.isArray(parent[arrayKey])) {
          parent[arrayKey] = [];
        }

        const array = parent[arrayKey] as unknown[];
        const index = Number(lastPart);
        array[index] = value;
      } else {
        // Regular property
        current[lastPart] = value;
      }
    } else {
      // Direct property
      result[key] = value;
    }
  });

  return result;
}

// Transform frontend metadata-based structure to clean backend config
function transformNodeDataToConfig(
  nodeData: Record<string, unknown>,
): Record<string, unknown> {
  const config: Record<string, unknown> = {};

  // Group the flat structure into nested objects
  const groupedData = groupFlatKeys(nodeData);

  // Handle different node types and their specific config structures
  const nodeType = nodeData.label?.toString().toUpperCase() || "";

  // Generic approach: handle all node types dynamically
  switch (nodeType) {
    case "START":
      // For START nodes, ensure initiators structure exists
      if (groupedData.initiators) {
        const initiators = groupedData.initiators as Record<string, unknown>;
        config.initiators = {
          roles: Array.isArray(initiators.roles) ? initiators.roles : [],
          departments: Array.isArray(initiators.departments)
            ? initiators.departments
            : [],
          teams: Array.isArray(initiators.teams) ? initiators.teams : [],
        };
      } else {
        // If no initiators object, create one with empty arrays
        config.initiators = {
          roles: [],
          departments: [],
          teams: [],
        };
      }
      break;

    case "SINGLE APPROVAL":
    case "MULTIPLE APPROVALS":
      // For approval nodes, handle nested structures if they exist
      if (groupedData.approver) {
        config.approver = groupedData.approver;
      }
      if (groupedData.notification) {
        config.notification = groupedData.notification;
      }
      if (groupedData.onReject) {
        config.onReject = groupedData.onReject;
      }
      break;

    case "FINISH":
      // For end nodes, just mark as completed
      config.completed = true;
      break;

    default:
      // For all other node types (DOCUMENT, NOTIFICATION, CONDITION, etc.)
      // Use all the grouped data directly as config
      Object.assign(config, groupedData);
      break;
  }

  return config;
}

const NewWorkflow = () => {
  const { nodes, edges, createWorkflow, updateWorkflow, resetWorkflow } =
    useWorkflowStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams!.get("editId");
  const [resetKey, setResetKey] = useState(0); // Add key for forcing re-render
  const methods = useForm({
    defaultValues: {
      title: "",
      description: "",
      status: "Draft",
    },
  });

  // Add state to store raw workflow data and track metadata availability
  const [rawWorkflowData, setRawWorkflowData] = useState<WorkflowFull | null>(
    null,
  );
  const [processedWorkflow, setProcessedWorkflow] =
    useState<WorkflowFull | null>(null);
  const {
    metadata,
    fetchMetadata,
    isLoading: metadataLoading,
  } = useWorkflowMetadataStore();

  // Cleanup function to reset everything
  const cleanupAndReset = useCallback(() => {
    // Reset form
    methods.reset({
      title: "",
      description: "",
      status: "Draft" as const,
    });

    // Reset workflow builder
    resetWorkflow();

    // Clear any local node properties
    if (window.getAllNodeLocalProperties) {
      window.getAllNodeLocalProperties = () => ({});
    }

    // Force re-render
    setResetKey((prev) => prev + 1);
  }, [methods, resetWorkflow]);

  // Cleanup function that only resets if not editing
  const cleanupIfNotEditing = useCallback(() => {
    if (!editId) {
      cleanupAndReset();
    }
  }, [editId, cleanupAndReset]);

  const onSubmit = async (data: FormData) => {
    // --- NEW: persist all local node edits to the store before saving ---
    const localPropsMap = window.getAllNodeLocalProperties?.() || {};
    if (localPropsMap && typeof localPropsMap === "object") {
      nodes.forEach((node) => {
        if (localPropsMap[node.id]) {
          node.data = { ...node.data, ...localPropsMap[node.id] };
        }
      });
    }
    // --- END NEW ---

    const formattedNodes = nodes.map((node) => {
      let nodeType = node.type
        ? node.type.toUpperCase()
        : node.data.label.toUpperCase();

      // Special handling for approvals node - determine if single or multiple based on receivers
      if (nodeType === "APPROVALS") {
        const receivers = node.data.receivers as
          | Record<string, boolean>
          | undefined;
        if (receivers) {
          // Count selected receivers (excluding "All")
          const selectedReceivers = Object.entries(receivers).filter(
            ([key, value]) => key !== "All" && value === true,
          ).length;

          // If more than one receiver is selected, it's multiple approvals
          nodeType =
            selectedReceivers > 1 ? "MULTIPLE_APPROVALS" : "SINGLE_APPROVAL";
        } else {
          // Default to single approval if no receivers configured
          nodeType = "SINGLE_APPROVAL";
        }
      }

      // Special handling for finish node
      if (nodeType === "FINISH") nodeType = "END";

      console.log(`🔧 Transforming node ${node.id} (${nodeType}):`, {
        originalData: node.data,
        nodeType: nodeType,
      });

      // Transform node.data to clean backend config format
      const config = transformNodeDataToConfig(node.data);

      console.log(`✅ Transformed config for ${node.id}:`, config);
      console.log(`📊 Grouped data for ${node.id}:`, groupFlatKeys(node.data));
      console.log(
        `🎯 Node type: ${nodeType}, Config keys:`,
        Object.keys(config),
      );

      return {
        id: node.id,
        formId: node.data.form as string | undefined,
        type: nodeType,
        title: node.data.label,
        position: node.position,
        config: config,
      };
    });

    const formattedData = {
      name: data.title,
      description: data.description,
      status: data.status,
      nodes: formattedNodes,
      isBulk: false,
      edges: edges.map((edge) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: edge.type || "DEFAULT", // Use DEFAULT instead of "default"
      })),
    };

    console.log("📤 Sending formatted data to backend:", formattedData);

    try {
      if (editId) {
        // Update existing workflow
        const response = await updateWorkflow(editId, formattedData);
        console.log("Updated workflow response:", response);
      } else {
        // Create new workflow
        const response = await createWorkflow(formattedData);
        console.log("Created workflow response:", response);
      }

      // Reset form and workflow builder
      cleanupAndReset();

      // Redirect to configurations page
      router.push("/requests-configurations");
    } catch (error) {
      console.error("Error saving workflow:", error);
    }
  };

  const { getWorkflowById } = useWorkflowListStore();

  // Reset everything when component mounts (user navigates to page) - only if not editing
  useEffect(() => {
    cleanupIfNotEditing();
  }, [cleanupIfNotEditing]);

  // Fetch metadata when component mounts (always needed for dynamic properties)
  useEffect(() => {
    console.log("🔄 Fetching metadata...");
    fetchMetadata();
  }, [fetchMetadata]);

  // Load workflow data if editing
  useEffect(() => {
    if (editId) {
      console.log("🔄 Fetching workflow data for editId:", editId);
      getWorkflowById(editId).then((data) => {
        console.log("✅ Workflow data received:", data);
        setRawWorkflowData(data as WorkflowFull | null);
      });
    }
  }, [editId, getWorkflowById]);

  // Process workflow data when both raw data and metadata are available
  useEffect(() => {
    if (editId && rawWorkflowData && metadata) {
      console.log(
        "✅ Both workflow data and metadata available, processing...",
      );
      console.log("📊 Raw workflow data:", rawWorkflowData);
      console.log("📋 Metadata:", metadata);
      // Now we have both the workflow data and metadata, so we can process it
      setProcessedWorkflow(rawWorkflowData);
    } else if (editId) {
      console.log("⏳ Waiting for data:", {
        hasRawWorkflowData: !!rawWorkflowData,
        hasMetadata: !!metadata,
        metadataLoading,
      });
    }
  }, [editId, rawWorkflowData, metadata, metadataLoading]);

  // Show loading state when in edit mode and waiting for data
  const isLoading =
    editId && (!rawWorkflowData || !metadata || metadataLoading);

  // Debug logging for WorkflowHeader initialValues
  useEffect(() => {
    if (processedWorkflow) {
      console.log("🎯 Passing to WorkflowHeader:", {
        name: processedWorkflow.name,
        description: processedWorkflow.description,
        status: processedWorkflow.status,
      });
    }
  }, [processedWorkflow]);

  // Cleanup when component unmounts (user navigates away)
  useEffect(() => {
    return () => {
      cleanupAndReset();
    };
  }, [cleanupAndReset]);

  return (
    <Form {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
        <CustomPageCard>
          <div className="col-span-2 space-y-5">
            <WorkflowHeader
              initialValues={
                processedWorkflow
                  ? {
                      name: processedWorkflow.name,
                      description: processedWorkflow.description,
                      status: processedWorkflow.status,
                    }
                  : undefined
              }
            />
            <Separator orientation="horizontal" className="border-gray-300" />
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading workflow data...</p>
                </div>
              </div>
            ) : (
              <ReactFlowProvider>
                <WorkflowBuilder
                  key={resetKey}
                  initialNodes={processedWorkflow?.nodes}
                  initialEdges={processedWorkflow?.edges}
                />
              </ReactFlowProvider>
            )}
          </div>
        </CustomPageCard>
      </form>
    </Form>
  );
};

export default NewWorkflow;
