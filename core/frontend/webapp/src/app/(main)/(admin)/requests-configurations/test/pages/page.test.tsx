import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import RequestsConfigurations from "../../page";
import { useWorkflowListStore } from "../../store/workflowStore";

// Mock dependencies
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

jest.mock("next-intl", () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      "home.title": "Title",
      "home.description": "Description",
      "home.status": "Status",
      "home.createdDate": "Created Date",
      "home.updatedDate": "Updated Date",
      "home.addButton": "Add Workflow",
    };
    return translations[key] || key;
  }),
}));

jest.mock("../../store/workflowStore", () => ({
  useWorkflowListStore: jest.fn(),
}));

jest.mock("@/components/common/CellActions", () => ({
  __esModule: true,
  default: ({
    actions,
  }: {
    actions: Array<{ label: string; onClick: () => void }>;
  }) => (
    <div data-testid="cell-actions">
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={action.onClick}
          data-testid={`action-${action.label.toLowerCase()}`}
        >
          {action.label}
        </button>
      ))}
    </div>
  ),
}));

jest.mock("@/components/common/tables/DataTable", () => ({
  DataTable: ({
    data,
    onSearchChange,
    onPaginationChange,
    onSortingChange,
    buttons,
    isLoading,
  }: {
    data?: Array<{ name: string; status: string }>;
    onSearchChange?: (value: string) => void;
    onPaginationChange?: (pageIndex: number, pageSize: number) => void;
    onSortingChange?: (sorting: Array<{ id: string; desc: boolean }>) => void;
    buttons?: Array<{ label: string; onClick: () => void }>;
    isLoading?: boolean;
  }) => (
    <div data-testid="data-table">
      <input
        data-testid="search-input"
        onChange={(e) => onSearchChange?.(e.target.value)}
        placeholder="Search..."
      />
      <div data-testid="table-data">
        {data?.map((item, index: number) => (
          <div key={index} data-testid={`row-${index}`}>
            <span data-testid={`name-${index}`}>{item.name}</span>
            <span data-testid={`status-${index}`}>{item.status}</span>
          </div>
        ))}
      </div>
      <button
        data-testid="pagination-next"
        onClick={() => onPaginationChange?.(1, 5)}
      >
        Next
      </button>
      <button
        data-testid="sort-name"
        onClick={() => onSortingChange?.([{ id: "name", desc: false }])}
      >
        Sort Name
      </button>
      {buttons?.map((btn, index: number) => (
        <button key={index} onClick={btn.onClick} data-testid="add-button">
          {btn.label}
        </button>
      ))}
      {isLoading && <div data-testid="loading">Loading...</div>}
    </div>
  ),
}));

jest.mock("@/components/common/DeleteDialog", () => ({
  __esModule: true,
  default: ({
    isDialogOpen,
    setIsDialogOpen,
    handleDelete,
    children,
  }: {
    isDialogOpen: boolean;
    setIsDialogOpen: (open: boolean) => void;
    handleDelete: () => void;
    children: React.ReactNode;
  }) =>
    isDialogOpen ? (
      <div data-testid="delete-dialog">
        {children}
        <button
          onClick={() => setIsDialogOpen(false)}
          data-testid="cancel-delete"
        >
          Cancel
        </button>
        <button onClick={handleDelete} data-testid="confirm-delete">
          Confirm
        </button>
      </div>
    ) : null,
}));

jest.mock("@/components/common/CustomIcons", () => ({
  __esModule: true,
  default: ({ name }: { name: string }) => (
    <div data-testid={`icon-${name}`}>{name}</div>
  ),
}));

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  pathname: "/",
  query: {},
  asPath: "/",
};

const mockWorkflows = [
  {
    id: "workflow-1",
    name: "Test Workflow 1",
    description: "Test Description 1",
    status: "Draft",
    isActive: false,
    createdAt: "2024-01-01T10:00:00Z",
    updatedAt: "2024-01-01T11:00:00Z",
  },
  {
    id: "workflow-2",
    name: "Test Workflow 2",
    description: "Test Description 2",
    status: "Operational",
    isActive: true,
    createdAt: "2024-01-02T10:00:00Z",
    updatedAt: "2024-01-02T11:00:00Z",
  },
];

const mockMeta = {
  pageSize: 5,
  currentPage: 1,
  totalPages: 1,
  totalCount: 2,
  hasNextPage: false,
  hasPreviousPage: false,
  hasMoreResults: false,
  continuationToken: undefined,
};

describe("RequestsConfigurations Page", () => {
  const mockFetchPaginatedData = jest.fn();
  const mockDeleteWorkflow = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useWorkflowListStore as unknown as jest.Mock).mockReturnValue({
      data: { items: mockWorkflows, meta: mockMeta },
      isLoading: false,
      fetchPaginatedData: mockFetchPaginatedData,
      deleteWorkflow: mockDeleteWorkflow,
    });
  });

  describe("Initial Render", () => {
    it("should render the page title", () => {
      render(<RequestsConfigurations />);

      expect(screen.getByTestId("data-table")).toBeInTheDocument();
    });

    it("should load workflows on mount", () => {
      render(<RequestsConfigurations />);

      expect(mockFetchPaginatedData).toHaveBeenCalledWith(
        "",
        0,
        5,
        "createdDate",
        "asc",
      );
    });

    it("should display workflows in the table", () => {
      render(<RequestsConfigurations />);

      expect(screen.getByTestId("name-0")).toHaveTextContent("Test Workflow 1");
      expect(screen.getByTestId("status-0")).toHaveTextContent("Draft");
      expect(screen.getByTestId("name-1")).toHaveTextContent("Test Workflow 2");
      expect(screen.getByTestId("status-1")).toHaveTextContent("Operational");
    });
  });

  describe("Search Functionality", () => {
    it("should handle search input changes", async () => {
      render(<RequestsConfigurations />);

      const searchInput = screen.getByTestId("search-input");
      fireEvent.change(searchInput, { target: { value: "test" } });

      await waitFor(() => {
        expect(mockFetchPaginatedData).toHaveBeenCalledWith(
          "test",
          0,
          5,
          "createdDate",
          "asc",
        );
      });
    });

    it("should debounce search requests", async () => {
      jest.useFakeTimers();

      render(<RequestsConfigurations />);

      const searchInput = screen.getByTestId("search-input");
      fireEvent.change(searchInput, { target: { value: "test" } });

      jest.advanceTimersByTime(300);

      await waitFor(() => {
        expect(mockFetchPaginatedData).toHaveBeenCalledWith(
          "test",
          0,
          5,
          "createdDate",
          "asc",
        );
      });

      jest.useRealTimers();
    });
  });

  describe("Pagination", () => {
    it("should handle pagination changes", () => {
      render(<RequestsConfigurations />);

      const nextButton = screen.getByTestId("pagination-next");
      fireEvent.click(nextButton);

      expect(mockFetchPaginatedData).toHaveBeenCalledWith(
        "",
        1,
        5,
        "createdDate",
        "asc",
      );
    });
  });

  describe("Sorting", () => {
    it("should handle sorting changes", () => {
      render(<RequestsConfigurations />);

      const sortButton = screen.getByTestId("sort-name");
      fireEvent.click(sortButton);

      expect(mockFetchPaginatedData).toHaveBeenCalledWith(
        "",
        0,
        5,
        "name",
        "asc",
      );
    });
  });

  describe("Navigation", () => {
    it("should navigate to new workflow page when add button is clicked", () => {
      render(<RequestsConfigurations />);

      const addButton = screen.getByTestId("add-button");
      fireEvent.click(addButton);

      expect(mockRouter.push).toHaveBeenCalledWith(
        "/requests-configurations/new-workflow",
      );
    });
  });

  describe("Loading States", () => {
    it("should show loading state when fetching data", () => {
      (useWorkflowListStore as unknown as jest.Mock).mockReturnValue({
        data: { items: [], meta: mockMeta },
        isLoading: true,
        fetchPaginatedData: mockFetchPaginatedData,
        deleteWorkflow: mockDeleteWorkflow,
      });

      render(<RequestsConfigurations />);

      expect(screen.getByTestId("loading")).toBeInTheDocument();
    });
  });

  describe("Component Integration", () => {
    it("should integrate with DataTable component correctly", () => {
      render(<RequestsConfigurations />);

      expect(screen.getByTestId("data-table")).toBeInTheDocument();
      expect(screen.getByTestId("search-input")).toBeInTheDocument();
    });

    it("should integrate with DeleteDialog component correctly", () => {
      render(<RequestsConfigurations />);

      // The DeleteDialog should be available but not visible initially
      expect(screen.queryByTestId("delete-dialog")).not.toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("should handle empty data gracefully", () => {
      (useWorkflowListStore as unknown as jest.Mock).mockReturnValue({
        data: { items: [], meta: mockMeta },
        isLoading: false,
        fetchPaginatedData: mockFetchPaginatedData,
        deleteWorkflow: mockDeleteWorkflow,
      });

      render(<RequestsConfigurations />);

      expect(screen.getByTestId("data-table")).toBeInTheDocument();
    });

    it("should handle missing data gracefully", () => {
      (useWorkflowListStore as unknown as jest.Mock).mockReturnValue({
        data: { items: [], meta: mockMeta },
        isLoading: false,
        fetchPaginatedData: mockFetchPaginatedData,
        deleteWorkflow: mockDeleteWorkflow,
      });

      render(<RequestsConfigurations />);

      expect(screen.getByTestId("data-table")).toBeInTheDocument();
    });
  });
});
