import { create } from "zustand";
import {
  Edge,
  Connection,
  NodeChange,
  EdgeChange,
  applyNodeChanges,
  applyEdgeChanges,
} from "@xyflow/react";
import {
  initialNodes,
  initialEdges,
} from "../new-workflow/components/constant/initialData";
import { toast } from "@/hooks/use-toast";
import api from "@/lib/axios";
import {
  WorkflowListItem,
  WorkflowListMeta,
  WorkflowMetadata,
} from "../types/workflowTypes";

// Helper function to extract message from backend response
const extractMessageFromResponse = (responseData: unknown): string | null => {
  if (!responseData || typeof responseData !== "object") {
    return null;
  }

  const data = responseData as Record<string, unknown>;

  // Check if response has a message field at first level
  if (data?.message && typeof data.message === "string") {
    return data.message;
  }

  // Check if message is nested in details
  if (
    data?.details &&
    typeof data.details === "object" &&
    data.details !== null
  ) {
    const details = data.details as Record<string, unknown>;

    // Check for direct message in details
    if (details.message && typeof details.message === "string") {
      return details.message;
    }

    // Check for direct error in details
    if (details.error && typeof details.error === "string") {
      return details.error;
    }

    // Check for validation errors array
    if (
      details.errors &&
      Array.isArray(details.errors) &&
      details.errors.length > 0
    ) {
      const firstError = details.errors[0] as Record<string, unknown>;

      // Check for constraints array (validation errors)
      if (
        firstError.constraints &&
        Array.isArray(firstError.constraints) &&
        firstError.constraints.length > 0
      ) {
        const constraint = firstError.constraints[0];
        if (typeof constraint === "string") {
          // Include field name if available
          if (firstError.field && typeof firstError.field === "string") {
            return `${firstError.field}: ${constraint}`;
          }
          return constraint;
        }
      }

      // Check for direct error message in the error object
      if (firstError.message && typeof firstError.message === "string") {
        return firstError.message;
      }

      // Check for error field in the error object
      if (firstError.error && typeof firstError.error === "string") {
        return firstError.error;
      }
    }
  }

  // Check for success message patterns
  if (data?.success && typeof data.success === "string") {
    return data.success;
  }

  // Check for status message
  if (data?.status && typeof data.status === "string") {
    return data.status;
  }

  // Return null if no message found
  return null;
};

interface ValidationError {
  code: string;
  message: string;
  statusCode: number;
  timestamp: string;
  details?: {
    errors: Array<{
      field: string;
      value: string;
      constraints: string[];
    }>;
  };
  path: string;
}

interface ApiError {
  response?: {
    data: ValidationError;
  };
}

interface TNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: { [key: string]: unknown; label: string };
  properties?: Record<string, unknown>;
}

interface WorkflowState {
  nodes: TNode[];
  edges: Edge[];
  inputs: { id: string; value: string }[]; // Define according to your actual input type
  outputs: { id: string; value: string }[]; // Define according to your actual output type
  flowPool: Record<string, unknown>; // Define according to your actual flow pool structure
  history: { nodes: TNode[]; edges: Edge[] }[]; // **Added: History for undo functionality**
  redoStack: { nodes: TNode[]; edges: Edge[] }[]; // **Added: Redo stack for redo functionality**
  selectedNode: TNode | null; // **Added: Selected node state**
  loading: boolean; // **Added: Loading state**
  updateNodes: (changes: NodeChange[]) => void;
  updateEdges: (changes: EdgeChange[]) => void;
  addNode: (node: TNode) => void;
  updateNode: (id: string, data: Partial<TNode["data"]>) => void;
  removeNode: (id: string) => void;
  addEdge: (connection: Connection) => void;
  removeEdge: (id: string) => void;
  resetFlow: (flow: { data: { nodes: TNode[]; edges: Edge[] } }) => void; // Define flow type
  setInputs: (inputs: { id: string; value: string }[]) => void;
  setOutputs: (outputs: { id: string; value: string }[]) => void;
  setFlowPool: (flowPool: Record<string, unknown>) => void;
  clearFlow: () => void;
  undoAction: () => void; // **Added: Undo action method**
  redoAction: () => void; // **Added: Redo action method**
  updateNodeProperties: (
    nodeId: string,
    properties: Record<string, unknown>,
  ) => void;
  setSelectedNode: (node: TNode | null) => void; // **Added: Action to set selected node**
  createWorkflow: (formattedData: WorkflowFormattedData) => Promise<unknown>;
  updateWorkflow: (
    id: string,
    formattedData: WorkflowFormattedData,
  ) => Promise<unknown>;
  resetWorkflow: () => void;
  set: (fn: Partial<WorkflowState>) => void;
}

interface WorkflowFormattedData {
  name: string;
  description: string;
  status: string;
  isActive?: boolean; // Made optional since backend rejects it
  nodes: {
    id: string;
    type: string;
    title: string;
    position: { x: number; y: number };
    formId?: string;
    config: Record<string, unknown>;
  }[];
  edges: {
    id: string;
    source: string;
    target: string;
  }[];
}

interface WorkflowListState {
  data: {
    items: WorkflowListItem[];
    meta: WorkflowListMeta;
  };
  isLoading: boolean;
  fetchPaginatedData: (
    query: string,
    pageIndex: number,
    pageSize: number,
    sortBy?: string,
    sortOrder?: string,
  ) => Promise<void>;
  // searchWorkflows: (
  //   search: string,
  //   pageIndex: number,
  //   pageSize: number,
  //   sortBy?: string,
  //   sortOrder?: string,
  // ) => Promise<void>;
  deleteWorkflow: (
    id: string,
    pageIndex: number,
    pageSize: number,
    sortBy?: string,
    sortOrder?: string,
  ) => Promise<void>;
  getWorkflowById: (id: string) => Promise<WorkflowListItem | null>;
}

interface WorkflowMetadataState {
  metadata: WorkflowMetadata | null;
  isLoading: boolean;
  fetchMetadata: () => Promise<void>;
}

const getInputsAndOutputs = (nodes: TNode[]) => {
  const inputs: TNode[] = [];
  const outputs: TNode[] = [];
  nodes.forEach((node) => {
    if (node.type === "inputNode") {
      inputs.push(node);
    } else if (node.type === "outputNode") {
      outputs.push(node);
    }
  });

  return { inputs, outputs };
};

export const useWorkflowStore = create<WorkflowState>((set) => ({
  nodes: initialNodes,
  edges: initialEdges,
  inputs: [],
  outputs: [],
  flowPool: {},
  history: [], // Undo stack
  selectedNode: null, // **Initialize selectedNode**
  loading: false, // **Initialize loading state**
  redoStack: [], // Initialize redo stack

  // Handle node changes and apply them to the state
  updateNodes: (changes: NodeChange[]) =>
    set((state) => ({
      nodes: applyNodeChanges(changes, state.nodes) as TNode[],
    })),

  // Handle edge changes and apply them to the state
  updateEdges: (changes: EdgeChange[]) =>
    set((state) => ({
      edges: applyEdgeChanges(changes, state.edges),
    })),

  // **Added: Add a new node and save current state to history**
  /**
   * Adds a new node to the workflow and records the current state for undo functionality.
   * @param node - The node to add.
   */
  addNode: (node: TNode) =>
    set((state) => ({
      history: [...state.history, { nodes: state.nodes, edges: state.edges }], // Save current state to history
      redoStack: [], // Clear redo stack
      nodes: [...state.nodes, { ...node, id: `node-${Date.now()}` }], // Add new node with unique ID
    })),

  // Update an existing node's data
  updateNode: (id: string, data: Partial<TNode["data"]>) =>
    set((state) => ({
      nodes: state.nodes.map((node) =>
        node.id === id
          ? {
              ...node,
              data: {
                ...node.data,
                ...Object.fromEntries(
                  Object.entries(data).filter(
                    ([, value]) => value !== undefined,
                  ),
                ),
              },
            }
          : node,
      ),
    })),

  // **Added: Remove a node and its associated edges, then save state to history**
  /**
   * Removes a node by its ID and deletes any edges connected to it. Saves the current state to history.
   * @param id - The ID of the node to remove.
   */
  removeNode: (id: string) =>
    set((state) => ({
      history: [...state.history, { nodes: state.nodes, edges: state.edges }], // Save current state to history
      redoStack: [], // Clear redo stack
      nodes: state.nodes.filter((node) => node.id !== id),
      edges: state.edges.filter(
        (edge) => edge.source !== id && edge.target !== id,
      ),
    })),

  // **Added: Add a new edge and save current state to history**
  /**
   * Adds a new edge to the workflow and records the current state for undo functionality.
   * @param connection - The connection information for the new edge.
   */
  addEdge: (connection: Connection) =>
    set((state) => ({
      history: [...state.history, { nodes: state.nodes, edges: state.edges }], // Save current state to history
      redoStack: [], // Clear redo stack
      edges: [...state.edges, { ...connection, id: `edge-${Date.now()}` }], // Add new edge with unique ID
    })),

  // Remove an edge by its ID
  removeEdge: (id: string) =>
    set((state) => ({
      edges: state.edges.filter((edge) => edge.id !== id),
    })),

  // Reset the entire flow to a given state
  resetFlow: (flow) => {
    const nodes = flow?.data?.nodes ?? [];
    const edges = flow?.data?.edges ?? [];
    const { inputs, outputs } = getInputsAndOutputs(nodes);

    set({
      nodes,
      edges,
      inputs: inputs.map((input) => ({
        id: input.id,
        value: input.data.label,
      })),
      outputs: outputs.map((output) => ({
        id: output.id,
        value: output.data.label,
      })),
      flowPool: {},
    });
  },

  // Set the inputs array
  setInputs: (inputs) => {
    set({ inputs });
  },

  // Set the outputs array
  setOutputs: (outputs) => {
    set({ outputs });
  },

  // Set the flowPool object
  setFlowPool: (flowPool) => {
    set({ flowPool });
  },

  // **Added: Clear the entire flow and save current state to history**
  /**
   * Clears all nodes and edges from the workflow, effectively resetting it. Saves the current state to history for undo functionality.
   */
  clearFlow: () =>
    set((state) => ({
      history: [...state.history, { nodes: state.nodes, edges: state.edges }], // Save current state to history
      redoStack: [], // Clear redo stack
      nodes: [],
      edges: [],
      inputs: [],
      outputs: [],
      flowPool: {},
    })),

  // **Added: Undo the last action by reverting to the previous state in history**
  /**
   * Reverts the workflow to the previous state by popping the last state from the history array.
   */
  undoAction: () =>
    set((state) => {
      if (state.history.length > 0) {
        const lastState = state.history[state.history.length - 1];
        return {
          nodes: lastState.nodes,
          edges: lastState.edges,
          history: state.history.slice(0, -1), // Remove the last state from history
          redoStack: [
            ...state.redoStack,
            { nodes: state.nodes, edges: state.edges },
          ], // Save current state to redo stack
        };
      }
      return state;
    }),

  // **Added: Redo the last undone action by reverting to the next state in redo stack**
  /**
   * Reverts the workflow to the next state by popping the last state from the redo stack.
   */
  redoAction: () =>
    set((state) => {
      if (state.redoStack.length > 0) {
        const nextState = state.redoStack[state.redoStack.length - 1];
        return {
          nodes: nextState.nodes,
          edges: nextState.edges,
          redoStack: state.redoStack.slice(0, -1), // Remove the last state from redo stack
          history: [
            ...state.history,
            { nodes: state.nodes, edges: state.edges },
          ], // Save current state to history
        };
      }
      return state;
    }),
  updateNodeProperties: (nodeId, properties) =>
    set((state) => ({
      nodes: state.nodes.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...properties } }
          : node,
      ),
      history: [...state.history, { nodes: state.nodes, edges: state.edges }],
      redoStack: [],
    })),
  setSelectedNode: (node: TNode | null) => set(() => ({ selectedNode: node })), // **Set selectedNode action**

  /**
   * Saves a formatted workflow object to the backend.
   * @param formattedData - Workflow data in the required API format
   */
  createWorkflow: async (formattedData: WorkflowFormattedData) => {
    try {
      set({ loading: true }); // Start loading
      const response = await api.post(
        "/workflowengine/workflows",
        formattedData,
      );

      // Only show toast if backend provides a message
      const message = extractMessageFromResponse(response.data);
      if (message) {
        toast({
          title: "Success",
          description: message,
          variant: "success",
        });
      }

      return response.data;
    } catch (error) {
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      throw error;
    } finally {
      set({ loading: false }); // Stop loading
    }
  },
  updateWorkflow: async (id: string, formattedData: WorkflowFormattedData) => {
    try {
      set({ loading: true }); // Start loading
      const response = await api.put(
        `/workflowengine/workflows/${id}`,
        formattedData,
      );

      // Only show toast if backend provides a message
      const message = extractMessageFromResponse(response.data);
      if (message) {
        toast({
          title: "Success",
          description: message,
          variant: "success",
        });
      }

      return response.data;
    } catch (error) {
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      throw error;
    } finally {
      set({ loading: false }); // Stop loading
    }
  },
  resetWorkflow: () => {
    set({
      nodes: initialNodes,
      edges: initialEdges,
      inputs: [],
      outputs: [],
      flowPool: {},
      history: [],
      redoStack: [],
      selectedNode: null,
      loading: false,
    });
  },
  set: (fn) => set(fn),
}));

export const useWorkflowListStore = create<WorkflowListState>((set, get) => ({
  data: {
    items: [],
    meta: {
      pageSize: 5,
      currentPage: 1,
      totalPages: 0,
      totalCount: 0,
      hasNextPage: false,
      hasPreviousPage: false,
      hasMoreResults: false,
      continuationToken: undefined,
    },
  },
  isLoading: false,

  fetchPaginatedData: async (
    query,
    pageIndex,
    pageSize,
    sortBy = "createdDate",
    sortOrder = "desc",
  ) => {
    set({ isLoading: true });
    try {
      const params: Record<string, unknown> = {
        pageNumber: pageIndex + 1,
        pageSize,
        sortBy,
        sortOrder,
      };
      if (query) {
        params.query = query;
      }
      const response = await api.get("/workflowengine/workflows", { params });
      if (response.data && response.data.items) {
        set({
          data: {
            items: response.data.items as WorkflowListItem[],
            meta: response.data.meta as WorkflowListMeta,
          },
          isLoading: false,
        });
      } else {
        set({ isLoading: false });
        // Don't show toast for invalid response format - let backend handle it
      }
    } catch (error) {
      set({ isLoading: false });
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
    }
  },
  // searchWorkflows: async (
  //   search,
  //   pageIndex,
  //   pageSize,
  //   sortBy = "createdDate",
  //   sortOrder = "desc",
  // ) => {
  //   set({ isLoading: true });
  //   try {
  //     console.log("Searching workflows with params:", {
  //       query: search,
  //       pageIndex,
  //       pageSize,
  //       sortBy,
  //       sortOrder,
  //     });
  //     const response = await api.get("/workflowengine/workflows/search", {
  //       params: {
  //         query: search,
  //         pageNumber: pageIndex + 1,
  //         pageSize,
  //         sortBy,
  //         sortOrder,
  //       },
  //     });

  //     console.log("Search response:", response.data);

  //     if (response.data && response.data.items) {
  //       const newData = {
  //         items: response.data.items as WorkflowListItem[],
  //         meta: response.data.meta as WorkflowListMeta,
  //       };
  //       console.log("Setting new workflow data:", newData);
  //       set((state) => ({
  //         ...state,
  //         data: newData,
  //         isLoading: false,
  //       }));
  //     } else {
  //       console.error("Invalid search response format:", response.data);
  //       set((state) => ({
  //         ...state,
  //         isLoading: false,
  //       }));
  //       // Don't show toast for invalid response format - let backend handle it
  //     }
  //   } catch (error) {
  //     console.error("Failed to search workflows:", error);
  //     set((state) => ({
  //       ...state,
  //       isLoading: false,
  //     }));
  //     const apiError = error as ApiError;
  //     const message = extractMessageFromResponse(apiError.response?.data);
  //     if (message) {
  //       toast({
  //         title: "Error",
  //         description: message,
  //         variant: "destructive",
  //       });
  //     }
  //   }
  // },
  deleteWorkflow: async (
    id,
    pageIndex,
    pageSize,
    sortBy = "createdDate",
    sortOrder = "desc",
  ) => {
    set({ isLoading: true });
    try {
      const response = await api.delete(`/workflowengine/workflows/${id}`);
      const message = extractMessageFromResponse(response.data);
      if (message) {
        toast({
          title: "Success",
          description: message,
          variant: "success",
        });
      }
      // Refresh the list after deletion
      await get().fetchPaginatedData(
        "",
        pageIndex,
        pageSize,
        sortBy,
        sortOrder,
      );
    } catch (error) {
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
    } finally {
      set({ isLoading: false });
    }
  },
  getWorkflowById: async (id: string) => {
    try {
      const response = await api.get(`/workflowengine/workflows/${id}`);
      return response.data as WorkflowListItem;
    } catch (error) {
      const apiError = error as ApiError;
      const message = extractMessageFromResponse(apiError.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      return null;
    }
  },
}));

export const useWorkflowMetadataStore = create<WorkflowMetadataState>(
  (set) => ({
    metadata: null,
    isLoading: false,
    fetchMetadata: async () => {
      set({ isLoading: true });
      try {
        const response = await api.get("/workflowengine/workflow-metadata");
        set({
          metadata: response.data as WorkflowMetadata,
          isLoading: false,
        });
      } catch (error) {
        set({ isLoading: false });
        const apiError = error as ApiError;
        const message = extractMessageFromResponse(apiError.response?.data);
        if (message) {
          toast({
            title: "Error",
            description: message,
            variant: "destructive",
          });
        }
      }
    },
  }),
);
