import React, { useCallback, useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import CustomInput from "@/components/common/CustomInput";
import CustomSelect from "@/components/common/CustomSelect";
import CustomMultiSelect from "@/components/common/CustomMultiSelect";
import { useWorkflowStore } from "../../../store/workflowStore";
import { useWorkflowMetadataStore } from "../../../store/workflowStore";
import {
  WorkflowMetadataField,
  WorkflowOption,
} from "../../../types/workflowTypes";
import { Loader2, ChevronDown, ChevronRight } from "lucide-react";

// Helper to get a unique field key using label (for now)
type FieldWithLabel = WorkflowMetadataField & { label: string };

// Utility function to process options for select/multiselect components
const processOptions = (options: WorkflowOption[] | undefined) => {
  if (!options) return [];

  return options.map((opt) => {
    if (typeof opt === "string") {
      return { label: opt, value: opt };
    } else {
      return { label: opt.name, value: opt.id };
    }
  });
};

// Utility function to get display value for object options
const getDisplayValue = (
  value: unknown,
  options: WorkflowOption[] | undefined,
): string => {
  if (!options || typeof value !== "string")
    return typeof value === "string" ? value : "";

  const option = options.find((opt) => {
    if (typeof opt === "string") {
      return opt === value;
    } else {
      return opt.id === value;
    }
  });

  if (option && typeof option === "object") {
    return option.name;
  }

  return value;
};

// Utility function to get display values for multiselect object options
const getDisplayValues = (
  values: unknown[],
  options: WorkflowOption[] | undefined,
): string[] => {
  if (!options || !Array.isArray(values))
    return Array.isArray(values)
      ? (values.filter((v) => typeof v === "string") as string[])
      : [];

  return values.map((value) => {
    if (typeof value !== "string") return "";

    const option = options.find((opt) => {
      if (typeof opt === "string") {
        return opt === value;
      } else {
        return opt.id === value;
      }
    });

    if (option && typeof option === "object") {
      return option.name;
    }

    return value;
  });
};

const getFieldKey = (field: FieldWithLabel, parentKey = "") =>
  parentKey ? `${parentKey}.${field.label}` : field.label;

// ArrayCollapsible component for array fields
const ArrayCollapsible: React.FC<{
  field: FieldWithLabel;
  parentKey: string;
  currentValue: unknown;
  renderField: (field: FieldWithLabel, parentKey: string) => React.ReactNode;
}> = ({ field, parentKey, currentValue, renderField }) => {
  const [open, setOpen] = useState(false);
  const fieldKey = getFieldKey(field, parentKey);
  const arr: unknown[] = Array.isArray(currentValue)
    ? (currentValue as unknown[])
    : [];

  // Helper to render a single empty input if array is empty
  const renderEmptyInput = () => {
    if (!Array.isArray(field.fields) || field.fields.length === 0) return null;
    // Only support single-field array items for now (like roles)
    return (
      <div className=" p-2 mb-3 relative">
        {field.fields.map((subField) =>
          // For empty array, use index 0
          renderField(subField as FieldWithLabel, `${fieldKey}.0`),
        )}
      </div>
    );
  };

  return (
    <div className="border rounded-md mb-2 mt-2">
      <button
        type="button"
        className="flex items-center w-full px-2 py-2 bg-gray-50 hover:bg-gray-100 border-b"
        onClick={() => setOpen((o) => !o)}
      >
        {open ? (
          <ChevronDown className="w-4 h-4 mr-2" />
        ) : (
          <ChevronRight className="w-4 h-4 mr-2" />
        )}
        <span className="text-sm font-medium">{field.label}</span>
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </button>
      {open && (
        <div className="p-2 space-y-4">
          {arr.length === 0
            ? renderEmptyInput()
            : arr.map((item, idx) => (
                <div key={idx} className=" p-2  mb-3 relative">
                  {Array.isArray(field.fields) &&
                    (field.fields as FieldWithLabel[]).map((subField) =>
                      renderField(subField, `${fieldKey}.${idx}`),
                    )}
                  {/* Removed Remove button */}
                </div>
              ))}
          {/* Removed Add button */}
        </div>
      )}
    </div>
  );
};

const DynamicProperties: React.FC<{
  localProperties: Record<string, unknown>;
  setLocalProperties: (props: Record<string, unknown>) => void;
  nodeType: string;
}> = ({ localProperties, setLocalProperties, nodeType }) => {
  useWorkflowStore();
  const { metadata, fetchMetadata, isLoading } = useWorkflowMetadataStore();

  React.useEffect(() => {
    if (!metadata) {
      fetchMetadata();
    }
  }, [metadata, fetchMetadata]);

  const handleFieldChange = useCallback(
    (fieldKey: string, value: unknown) => {
      const keys = (fieldKey ?? "").split(".");
      const updatedProperties = { ...localProperties };
      let obj = updatedProperties;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!obj[keys[i]]) obj[keys[i]] = {};
        obj = obj[keys[i]] as Record<string, unknown>;
      }
      obj[keys[keys.length - 1]] = value;
      setLocalProperties(updatedProperties);
    },
    [localProperties, setLocalProperties],
  );

  const getValue = (fieldKey: string) => {
    const keys = (fieldKey ?? "").split(".");
    let value: unknown = localProperties;
    for (const key of keys) {
      if (value == null || typeof value !== "object") return undefined;
      value = (value as Record<string, unknown>)[key];
    }
    return value;
  };

  // Checkbox list for checkbox_list type
  const CheckboxList: React.FC<{
    options: WorkflowOption[];
    value: string[];
    onChange: (value: string[]) => void;
    label?: string;
    required?: boolean;
  }> = ({ options, value, onChange, label, required }) => {
    const allSelected = value.length === options.length;
    const toggleAll = () => {
      if (allSelected) onChange([]);
      else {
        const allValues = options.map((opt) =>
          typeof opt === "string" ? opt : opt.id,
        );
        onChange(allValues);
      }
    };
    const toggleOption = (optionValue: string) => {
      if (value.includes(optionValue)) {
        onChange(value.filter((v) => v !== optionValue));
      } else {
        onChange([...value, optionValue]);
      }
    };
    return (
      <div className="space-y-2">
        {label && (
          <Label className="text-sm font-medium">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        <div className="border rounded-md p-2 bg-white">
          <div className="flex items-center border-b pb-1 mb-1">
            <Checkbox
              id="all"
              checked={allSelected}
              onCheckedChange={toggleAll}
            />
            <Label htmlFor="all" className="ml-2 text-sm text-gray-500">
              All
            </Label>
          </div>
          {options.map((option) => {
            const optionValue = typeof option === "string" ? option : option.id;
            const optionLabel =
              typeof option === "string" ? option : option.name;
            return (
              <div key={optionValue} className="flex items-center py-1">
                <Checkbox
                  id={optionValue}
                  checked={value.includes(optionValue)}
                  onCheckedChange={() => toggleOption(optionValue)}
                />
                <Label htmlFor={optionValue} className="ml-2 text-sm">
                  {optionLabel}
                </Label>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Main field renderer
  const renderField = (
    field: FieldWithLabel,
    parentKey = "",
  ): React.ReactNode => {
    const fieldKey = getFieldKey(field, parentKey);
    const currentValue = getValue(fieldKey);
    const getInputType = (
      f: Partial<FieldWithLabel & { inputType?: string }>,
    ): string => {
      return f.inputType === "number" ? "number" : "text";
    };
    switch (field.type) {
      case "input":
        return (
          <CustomInput
            key={fieldKey}
            id={fieldKey}
            label={field.label}
            type={getInputType(field)}
            value={
              currentValue !== undefined && currentValue !== null
                ? String(currentValue)
                : ""
            }
            onChange={(e) =>
              handleFieldChange(
                fieldKey,
                getInputType(field) === "number"
                  ? Number(e.target.value)
                  : e.target.value,
              )
            }
            placeholder={`Enter ${field.label}`}
            maxLength={255}
            required={field.required}
            multiline={
              typeof (field as unknown as Record<string, unknown>).multiline ===
              "boolean"
                ? ((field as unknown as Record<string, unknown>)
                    .multiline as boolean)
                : false
            }
          />
        );
      case "select":
        return (
          <CustomSelect
            key={fieldKey}
            label={field.label}
            options={processOptions(field.options)}
            defaultValue={getDisplayValue(currentValue, field.options)}
            onValueChange={(val) => {
              // Convert display value back to actual value for backend
              const actualValue = field.options?.find((opt) => {
                if (typeof opt === "string") {
                  return opt === val;
                } else {
                  return opt.name === val;
                }
              });
              const valueToSend =
                typeof actualValue === "string"
                  ? actualValue
                  : actualValue?.id || val;
              handleFieldChange(fieldKey, valueToSend);
            }}
            placeholder={`Select ${field.label}`}
          />
        );
      case "multiselect":
        return (
          <CustomMultiSelect
            key={fieldKey}
            label={field.label}
            options={processOptions(field.options)}
            defaultValue={getDisplayValues(
              currentValue as unknown[],
              field.options,
            )}
            onValueChange={(vals) => {
              // Convert display values back to actual values for backend
              const actualValues = vals.map((val) => {
                const actualValue = field.options?.find((opt) => {
                  if (typeof opt === "string") {
                    return opt === val;
                  } else {
                    return opt.name === val;
                  }
                });
                return typeof actualValue === "string"
                  ? actualValue
                  : actualValue?.id || val;
              });
              handleFieldChange(fieldKey, actualValues);
            }}
            placeholder={`Select ${field.label}`}
          />
        );
      case "checkbox_list":
        return (
          <CheckboxList
            key={fieldKey}
            label={field.label}
            options={field.options || []}
            value={
              Array.isArray(currentValue) ? (currentValue as string[]) : []
            }
            onChange={(vals) => handleFieldChange(fieldKey, vals)}
            required={field.required}
          />
        );
      case "checkbox":
        return (
          <div key={fieldKey} className="flex items-center space-x-2">
            <Checkbox
              id={fieldKey}
              checked={!!currentValue}
              onCheckedChange={(checked) =>
                handleFieldChange(fieldKey, checked)
              }
            />
            <Label htmlFor={fieldKey} className="text-sm">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
          </div>
        );
      case "object":
        // Render as a section with the label as a heading and all fields shown directly underneath
        return (
          <div key={fieldKey} className="space-y-2 border rounded-md p-4">
            <div className="text-base font-semibold mb-2">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </div>
            {field.fields?.map((subField) =>
              renderField(subField as FieldWithLabel, fieldKey),
            )}
          </div>
        );
      case "array":
        return (
          <ArrayCollapsible
            key={fieldKey}
            field={field}
            parentKey={parentKey}
            currentValue={currentValue}
            renderField={renderField}
          />
        );
      default:
        return (
          <div key={fieldKey} className="space-y-2">
            <Label htmlFor={fieldKey} className="text-sm font-medium">
              {field.label} (Unsupported type: {field.type})
            </Label>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] space-y-4">
        <Loader2 className="w-8 h-8 text-black animate-spin" />
      </div>
    );
  }

  if (!metadata || !metadata[nodeType as keyof typeof metadata]) {
    return <div>No metadata found for this node type.</div>;
  }

  const fields = metadata[nodeType as keyof typeof metadata]?.fields || [];

  return (
    <div className="space-y-4">
      {fields.map((field: WorkflowMetadataField) =>
        renderField(field as FieldWithLabel),
      )}
    </div>
  );
};

export default DynamicProperties;
