{"extends": "../../../../../../../tsconfig.json", "compilerOptions": {"jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["jest", "node"]}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}