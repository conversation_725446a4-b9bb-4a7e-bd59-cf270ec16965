"use client";
import { Save } from "lucide-react";
import { IconButton } from "@/components/common/CustomButtons";
import { useTranslations } from "next-intl";
import CustomInput from "@/components/common/CustomInput";
import CustomSelect from "@/components/common/CustomSelect";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";
import { useWorkflowStore } from "../../store/workflowStore";
import { useEffect } from "react";

export function WorkflowHeader({
  initialValues,
}: {
  initialValues?: {
    name?: string;
    description?: string;
    status?: string;
  };
}) {
  const t = useTranslations("requestsConfigurations");
  const { control, setValue } = useFormContext();
  const { loading } = useWorkflowStore();

  useEffect(() => {
    if (initialValues) {
      console.log(
        "🔄 Setting initial values in WorkflowHeader:",
        initialValues,
      );
      if (initialValues.name !== undefined)
        setValue("title", initialValues.name);
      if (initialValues.description !== undefined)
        setValue("description", initialValues.description);
      if (initialValues.status !== undefined) {
        console.log("📝 Setting status value:", initialValues.status);
        setValue("status", initialValues.status);
      }
    }
  }, [initialValues, setValue]);

  const workflowStatusOptions = [
    {
      value: "Draft",
      label: "Draft",
    },
    {
      value: "Operational",
      label: "Operational",
    },
    {
      value: "Inactive",
      label: "Inactive",
    },
  ];

  const handleDocumentTypeChange = (value: string) => {
    console.log("Select status:", value);
  };

  return (
    <div className="flex items-end gap-4 p-4">
      <FormField
        control={control}
        name="title"
        render={({ field }) => (
          <FormItem className="w-80">
            <FormLabel>{t("newWorkflow.title")}</FormLabel>
            <FormControl>
              <CustomInput {...field} />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex-1">
            <FormLabel>{t("newWorkflow.description")}</FormLabel>
            <FormControl>
              <CustomInput {...field} />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="status"
        render={({ field }) => {
          console.log("🔍 Status field render:", {
            fieldValue: field.value,
            defaultValue: initialValues?.status,
            initialValues: initialValues,
          });
          return (
            <FormItem className="w-80">
              <FormLabel>{t("newWorkflow.status")}</FormLabel>
              <FormControl>
                <CustomSelect
                  {...field}
                  options={workflowStatusOptions}
                  onValueChange={handleDocumentTypeChange}
                  className="text-base"
                  defaultValue={initialValues?.status}
                />
              </FormControl>
            </FormItem>
          );
        }}
      />
      <div className="flex items-center">
        <IconButton
          className="gap-2"
          icon={Save}
          label={t("newWorkflow.save")}
          type="submit"
          isLoading={loading}
        />
      </div>
    </div>
  );
}
