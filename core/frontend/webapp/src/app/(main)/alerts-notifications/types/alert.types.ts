// Types for alerts and notifications
export interface Alert {
  id: string;
  title: string;
  type: string;
  actionIdentifierId?: string;
  requestId?: string;
  timestamp?: string;
  createdAt?: string;
  requesterID?: string | null;
  requesterName?: string | null;
  redirectTarget?: string | null;
  status?: string;
  requester?: string;
  requestDate?: string;
  actionMessage?: string;
  content?: string;
  isRead?: boolean;
}

// Detailed workflow data from API
export interface DetailedAlertData {
  workflowId: string;
  requestId: string;
  requestType: string;
  status: string;
  requestedBy: {
    userId: string;
    fullName: string;
  };
  startNodeFormData: {
    formData: {
      reason: string;
      From: string;
      To: string;
      type: string;
    };
    requestDate: string;
  };
  approvingHistory: Array<{
    nodeId: string;
    title: string;
    type: string;
    decision: string;
    decidedBy: string;
    decidedAt: string;
    comments: string;
  }>;
  currentApprovalNode: {
    nodeId: string;
    title: string;
    type: string;
    formId: string;
    approvers: string[];
    canUserApprove: boolean;
    otherApprovers: string[];
  };
  createdAt: string;
}

export interface TrackingStep {
  status: "approved" | "waiting" | "rejected";
  actor: string;
  timestamp: string;
  type: "approval" | "processing";
}

export interface Notification {
  id: string;
  title: string;
  date?: string;
  timestamp?: string;
  type: string;
  content?: string;
  redirectTarget?: string | null;
  actionMessage?: string;
  requesterID?: string;
  requesterName?: string;
  isRead?: boolean;
}

// Pagination types
export interface PaginationMeta {
  pageSize: number;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  meta: PaginationMeta;
}
