import { create } from "zustand";
import { toast } from "@/hooks/use-toast";
import { Notification, PaginationMeta } from "../types/alert.types";
import { Notification as SignalRNotification } from "@/types/notification.types";
import api from "@/lib/axios";
import { signalRService } from "@/services/signalRService";
import { useMockAuthStore } from "@/store/mockAuthStore";

// API endpoints
const NOTIFICATIONS_API_BASE = "/notifications-signalr/api";

// Helper function to get current user ID
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID when no mock user is selected
  return "2221_MAR Morocco 3"; // Default user ID
};

// Interface for API notification data
interface ApiNotification {
  id?: string;
  title?: string;
  type?: string;
  timestamp?: string;
  content?: string;
  message?: string;
  description?: string;
  actionMessage?: string;
  requesterID?: string;
  requesterName?: string;
  isRead?: boolean;
}

// Interface for API error response
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

// Helper function to format date and time
export const formatDateTime = (
  dateString: string | null | undefined,
): string => {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid Date";

    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid Date";
  }
};

// Helper function to get relative time
export const getRelativeTime = (formattedDate: string): string => {
  if (
    !formattedDate ||
    formattedDate === "N/A" ||
    formattedDate === "Invalid Date"
  )
    return "just now";

  try {
    // Parse the formatted date back to a Date object
    const date = new Date(formattedDate);
    if (isNaN(date.getTime())) return "just now";

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return `${Math.floor(diffInSeconds / 2592000)}mo ago`;
  } catch (error) {
    console.error("Error calculating relative time:", error);
    return "just now";
  }
};

interface NotificationsState {
  // State
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  userId: string | null;
  isSignalRConnected: boolean;
  pagination: PaginationMeta;

  // Actions
  fetchNotifications: (
    userId?: string,
    page?: number,
    pageSize?: number,
  ) => Promise<void>;
  refreshNotifications: (
    userId?: string,
    page?: number,
    pageSize?: number,
  ) => Promise<void>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  clearError: () => void;

  // SignalR Actions
  connectSignalR: (userId: string) => Promise<void>;
  disconnectSignalR: () => void;
  addNotificationFromSignalR: (notification: Notification) => void;
  removeNotificationFromSignalR: (notificationId: string) => void;
}

// Helper function to transform API data to existing Notification interface
const transformApiNotification = (
  apiNotification: ApiNotification,
): Notification => {
  // Safety checks
  if (!apiNotification || typeof apiNotification !== "object") {
    console.warn("Invalid notification data:", apiNotification);
    return {
      id: "unknown",
      title: "Invalid Notification",
      type: "INFO",
      content: "No content available",
      isRead: false,
    };
  }

  return {
    id: apiNotification.id || "unknown",
    title: apiNotification.title || "No Title",
    type: apiNotification.type || "INFO",
    timestamp: apiNotification.timestamp,
    content:
      apiNotification.content ||
      apiNotification.message ||
      apiNotification.description ||
      undefined,
    actionMessage: apiNotification.actionMessage,
    requesterID: apiNotification.requesterID,
    requesterName: apiNotification.requesterName,
    isRead: apiNotification.isRead || false,
  };
};

// Helper function to transform SignalR notification to Notification interface
const transformSignalRNotification = (
  signalRNotification: SignalRNotification,
): Notification => {
  return {
    id: signalRNotification.id || "unknown",
    title:
      signalRNotification.title || signalRNotification.subject || "No Title",
    type: "INFO", // Default type since SignalR Notification doesn't have it
    timestamp: signalRNotification.createdAt,
    content: undefined, // SignalR Notification doesn't have content
    actionMessage: undefined,
    requesterID: signalRNotification.userId,
    requesterName: signalRNotification.senderName,
    isRead: signalRNotification.isRead || false,
  };
};

const initialState = {
  notifications: [],
  loading: false,
  error: null,
  userId: null,
  isSignalRConnected: false,
  pagination: {
    pageSize: 5,
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },
};

// Track current connected user to prevent duplicate connections
let currentConnectedUserId: string | null = null;
let isConnecting = false;

export const useNotificationsStore = create<NotificationsState>((set, get) => ({
  ...initialState,

  fetchNotifications: async (
    userId?: string,
    page: number = 1,
    pageSize: number = 5,
  ) => {
    const currentUserId = userId || getCurrentUserId();

    // Prevent duplicate calls if data is already loaded or currently loading
    const currentState = useNotificationsStore.getState();
    if (
      currentState.loading ||
      (currentState.notifications.length > 0 && !currentState.error)
    ) {
      console.log("Skipping fetch - already loading or data exists");
      return;
    }

    set({ loading: true, error: null });
    try {
      console.log(
        "Fetching notifications for user:",
        currentUserId,
        "page:",
        page,
        "pageSize:",
        pageSize,
      );
      const response = await api.get(
        `${NOTIFICATIONS_API_BASE}/Notification/all/${currentUserId}`,
        {
          params: {
            page,
            pageSize,
          },
        },
      );

      console.log("API Response:", response.data);

      // Handle both old format (array) and new format (paginated)
      let apiData: ApiNotification[] = [];
      let paginationData: PaginationMeta = initialState.pagination;

      if (response.data.items && response.data.meta) {
        // New paginated format
        apiData = response.data.items;
        paginationData = response.data.meta;
      } else if (Array.isArray(response.data)) {
        // Old format - array
        apiData = response.data;
        paginationData = {
          pageSize: response.data.length,
          currentPage: 1,
          totalPages: 1,
          totalCount: response.data.length,
          hasNextPage: false,
          hasPreviousPage: false,
        };
      } else {
        console.error("Unexpected API response format:", response.data);
        throw new Error("Invalid API response format");
      }

      console.log("Number of notifications received:", apiData.length);

      // Transform API notifications to match existing Notification interface
      const transformedNotifications: Notification[] = apiData.map(
        transformApiNotification,
      );

      console.log("Transformed notifications:", transformedNotifications);

      set({
        notifications: transformedNotifications,
        loading: false,
        userId: currentUserId,
        pagination: paginationData,
      });

      // Connect to SignalR for real-time updates
      await get().connectSignalR(currentUserId);
    } catch (error: unknown) {
      console.error("Failed to fetch notifications:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to fetch notifications";

      set({
        error: errorMessage,
        loading: false,
      });

      // Show toast notification for the error
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  refreshNotifications: async (
    userId?: string,
    page: number = 1,
    pageSize: number = 5,
  ) => {
    const currentUserId = userId || getCurrentUserId();

    // Force refresh - clear data first then fetch
    set({ notifications: [], loading: true, error: null });

    try {
      console.log(
        "Force refreshing notifications for user:",
        currentUserId,
        "page:",
        page,
        "pageSize:",
        pageSize,
      );
      const response = await api.get(
        `${NOTIFICATIONS_API_BASE}/Notification/all/${currentUserId}`,
        {
          params: {
            page,
            pageSize,
          },
        },
      );

      console.log("API Response:", response.data);

      // Handle both old format (array) and new format (paginated)
      let apiData: ApiNotification[] = [];
      let paginationData: PaginationMeta = initialState.pagination;

      if (response.data.items && response.data.meta) {
        // New paginated format
        apiData = response.data.items;
        paginationData = response.data.meta;
      } else if (Array.isArray(response.data)) {
        // Old format - array
        apiData = response.data;
        paginationData = {
          pageSize: response.data.length,
          currentPage: 1,
          totalPages: 1,
          totalCount: response.data.length,
          hasNextPage: false,
          hasPreviousPage: false,
        };
      } else {
        console.error("Unexpected API response format:", response.data);
        throw new Error("Invalid API response format");
      }

      console.log("Number of notifications received:", apiData.length);

      // Transform API notifications to match existing Notification interface
      const transformedNotifications: Notification[] = apiData.map(
        transformApiNotification,
      );

      console.log("Transformed notifications:", transformedNotifications);

      set({
        notifications: transformedNotifications,
        loading: false,
        userId: currentUserId,
        pagination: paginationData,
      });
    } catch (error: unknown) {
      console.error("Failed to refresh notifications:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to refresh notifications";

      set({
        error: errorMessage,
        loading: false,
      });

      // Show toast notification for the error
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  clearError: () => {
    set({ error: null });
  },

  markNotificationAsRead: async (notificationId: string) => {
    const currentUserId = getCurrentUserId();

    try {
      console.log("Marking notification as read:", {
        notificationId,
        userId: currentUserId,
      });

      await api.patch(
        `${NOTIFICATIONS_API_BASE}/Notification/mark-as-read/${currentUserId}/${notificationId}`,
      );

      // Update the notification in the store to mark it as read
      set((state) => ({
        notifications: state.notifications.map((notification) =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification,
        ),
      }));

      console.log("Notification marked as read successfully");
    } catch (error: unknown) {
      console.error("Error marking notification as read:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to mark notification as read";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // SignalR Methods
  connectSignalR: async (userId: string) => {
    // Prevent duplicate connections
    if (isConnecting) {
      return;
    }
    if (currentConnectedUserId === userId) {
      return;
    }

    isConnecting = true;

    try {
      // Disconnect existing connection if different user
      if (currentConnectedUserId && currentConnectedUserId !== userId) {
        get().disconnectSignalR();
      }

      // Connect to SignalR
      await signalRService.connect(userId);
      currentConnectedUserId = userId;

      // Set up notification callback
      signalRService.onNotification((signalRNotification) => {
        console.log("SignalR Notification received:", signalRNotification);
        const transformedNotification =
          transformSignalRNotification(signalRNotification);
        get().addNotificationFromSignalR(transformedNotification);
      });

      set({ isSignalRConnected: true, userId });
      console.log("SignalR connected for notifications, user:", userId);
    } catch (error) {
      console.error("Failed to connect SignalR for notifications:", error);
      set({ isSignalRConnected: false });
    } finally {
      isConnecting = false;
    }
  },

  disconnectSignalR: () => {
    signalRService.disconnect();
    currentConnectedUserId = null;
    set({ isSignalRConnected: false });
    console.log("SignalR disconnected for notifications");
  },

  addNotificationFromSignalR: (notification: Notification) => {
    set((state) => {
      // Check if notification already exists to prevent duplicates
      const existingNotificationIndex = state.notifications.findIndex(
        (n) => n.id === notification.id,
      );

      if (existingNotificationIndex >= 0) {
        // Update existing notification
        const updatedNotifications = [...state.notifications];
        updatedNotifications[existingNotificationIndex] = notification;
        return { notifications: updatedNotifications };
      } else {
        // Add new notification at the beginning
        return { notifications: [notification, ...state.notifications] };
      }
    });
  },

  removeNotificationFromSignalR: (notificationId: string) => {
    set((state) => ({
      notifications: state.notifications.filter(
        (notification) => notification.id !== notificationId,
      ),
    }));
  },
}));
