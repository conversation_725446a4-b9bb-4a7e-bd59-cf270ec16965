import { create } from "zustand";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, DetailedAlertData, PaginationMeta } from "../types/alert.types";
import { Alert as SignalRAlert } from "@/types/notification.types";
import api from "@/lib/axios";
import { signalRService } from "@/services/signalRService";
import { useMockAuthStore } from "@/store/mockAuthStore";

// API endpoints
const ALERTS_API_BASE = "/notifications-signalr/api";

// Helper function to get current user ID
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID when no mock user is selected
  return "2221_MAR Morocco 3"; // Default user ID
};

// Interface for API alert data
interface ApiAlert {
  id?: string;
  title?: string;
  type?: string;
  actionIdentifierId?: string;
  requestId?: string;
  timestamp?: string;
  createdAt?: string;
  requesterID?: string;
  requesterName?: string;
  redirectTarget?: string;
  status?: string;
  requester?: string;
  requestDate?: string;
  actionMessage?: string;
  content?: string;
  isRead?: boolean;
}

// Interface for API error response
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

// Helper function to format date and time
export const formatDateTime = (
  dateString: string | null | undefined,
): string => {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid Date";

    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid Date";
  }
};

// Helper function to get relative time
export const getRelativeTime = (formattedDate: string): string => {
  if (
    !formattedDate ||
    formattedDate === "N/A" ||
    formattedDate === "Invalid Date"
  )
    return "just now";

  try {
    // Parse the formatted date back to a Date object
    const date = new Date(formattedDate);
    if (isNaN(date.getTime())) return "just now";

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return `${Math.floor(diffInSeconds / 2592000)}mo ago`;
  } catch (error) {
    console.error("Error calculating relative time:", error);
    return "just now";
  }
};

interface AlertsState {
  // State
  alerts: Alert[];
  loading: boolean;
  error: string | null;
  detailedData: DetailedAlertData | null;
  detailedLoading: boolean;
  detailedError: string | null;
  userId: string | null;
  isSignalRConnected: boolean;
  pagination: PaginationMeta;

  // Actions
  fetchAlerts: (
    userId?: string,
    page?: number,
    pageSize?: number,
  ) => Promise<void>;
  refreshAlerts: (
    userId?: string,
    page?: number,
    pageSize?: number,
  ) => Promise<void>;
  fetchDetailedAlert: (requestId?: string, userId?: string) => Promise<void>;
  approveRequest: (requestId: string, comments?: string) => Promise<void>;
  rejectRequest: (requestId: string, comments?: string) => Promise<void>;
  markAlertAsRead: (alertId: string) => Promise<void>;
  clearError: () => void;
  clearDetailedData: () => void;

  // SignalR Actions
  connectSignalR: (userId: string) => Promise<void>;
  disconnectSignalR: () => void;
  addAlertFromSignalR: (alert: Alert) => void;
  removeAlertFromSignalR: (alertId: string) => void;
}

// Helper function to transform API data to existing Alert interface
const transformApiAlert = (apiAlert: ApiAlert): Alert => {
  // Safety checks
  if (!apiAlert || typeof apiAlert !== "object") {
    console.warn("Invalid alert data:", apiAlert);
    return {
      id: "unknown",
      title: "Invalid Alert",
      type: "ERROR",
      actionIdentifierId: "unknown",
      requesterID: null,
      requesterName: null,
      redirectTarget: null,
      status: "Error",
      requester: undefined,
      isRead: false,
    };
  }

  return {
    id: apiAlert.id || "unknown",
    title: apiAlert.title || "No Title",
    type: apiAlert.type || "INFO",
    actionIdentifierId: apiAlert.actionIdentifierId || apiAlert.requestId || "",
    requesterID: apiAlert.requesterID || null,
    requesterName: apiAlert.requesterName || null,
    redirectTarget: apiAlert.redirectTarget || null,
    status: apiAlert.status || "Pending",
    requester: apiAlert.requester || apiAlert.requesterName || undefined,
    timestamp: apiAlert.timestamp || apiAlert.createdAt,
    createdAt: apiAlert.createdAt,
    requestId: apiAlert.requestId,
    requestDate: apiAlert.requestDate,
    actionMessage: apiAlert.actionMessage,
    content: apiAlert.content,
    isRead: apiAlert.isRead || false,
  };
};

// Helper function to transform SignalR alert to Alert interface
const transformSignalRAlert = (signalRAlert: SignalRAlert): Alert => {
  return {
    id: signalRAlert.id || "unknown",
    title: signalRAlert.title || signalRAlert.subject || "No Title",
    type: "INFO", // Default type since SignalR Alert doesn't have it
    actionIdentifierId: "",
    requesterID: signalRAlert.userId || null,
    requesterName: signalRAlert.senderName || null,
    redirectTarget: null,
    status: "Pending",
    requester: signalRAlert.senderName || undefined,
    timestamp: signalRAlert.createdAt,
    createdAt: signalRAlert.createdAt,
    requestId: undefined,
    requestDate: undefined,
    actionMessage: undefined,
    content: undefined,
    isRead: signalRAlert.isRead || false,
  };
};

const initialState = {
  alerts: [],
  loading: false,
  error: null,
  detailedData: null,
  detailedLoading: false,
  detailedError: null,
  userId: null,
  isSignalRConnected: false,
  pagination: {
    pageSize: 5,
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },
};

// Track current connected user to prevent duplicate connections
let currentConnectedUserId: string | null = null;
let isConnecting = false;

export const useAlertsStore = create<AlertsState>((set, get) => ({
  ...initialState,

  fetchAlerts: async (
    userId?: string,
    page: number = 1,
    pageSize: number = 5,
  ) => {
    const currentUserId = userId || getCurrentUserId();

    // Prevent duplicate calls if data is already loaded or currently loading
    const currentState = useAlertsStore.getState();
    if (
      currentState.loading ||
      (currentState.alerts.length > 0 && !currentState.error)
    ) {
      console.log("Skipping fetch - already loading or data exists");
      return;
    }

    set({ loading: true, error: null });
    try {
      console.log(
        "Fetching alerts for user:",
        currentUserId,
        "page:",
        page,
        "pageSize:",
        pageSize,
      );
      const response = await api.get(
        `${ALERTS_API_BASE}/Alert/all/${currentUserId}`,
        {
          params: {
            page,
            pageSize,
          },
        },
      );

      console.log("API Response:", response.data);

      // Handle both old format (array) and new format (paginated)
      let apiData: ApiAlert[] = [];
      let paginationData: PaginationMeta = initialState.pagination;

      if (response.data.items && response.data.meta) {
        // New paginated format
        apiData = response.data.items;
        paginationData = response.data.meta;
      } else if (Array.isArray(response.data)) {
        // Old format - array
        apiData = response.data;
        paginationData = {
          pageSize: response.data.length,
          currentPage: 1,
          totalPages: 1,
          totalCount: response.data.length,
          hasNextPage: false,
          hasPreviousPage: false,
        };
      } else {
        console.error("Unexpected API response format:", response.data);
        throw new Error("Invalid API response format");
      }

      console.log("Number of alerts received:", apiData.length);

      // Transform API alerts to match existing Alert interface
      const transformedAlerts: Alert[] = apiData.map(transformApiAlert);

      console.log("Transformed alerts:", transformedAlerts);

      set({
        alerts: transformedAlerts,
        loading: false,
        userId: currentUserId,
        pagination: paginationData,
      });

      // Connect to SignalR for real-time updates
      await get().connectSignalR(currentUserId);
    } catch (error: unknown) {
      console.error("Failed to fetch alerts:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to fetch alerts";

      set({
        error: errorMessage,
        loading: false,
      });

      // Show toast notification for the error
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  refreshAlerts: async (
    userId?: string,
    page: number = 1,
    pageSize: number = 5,
  ) => {
    const currentUserId = userId || getCurrentUserId();

    // Force refresh - clear data first then fetch
    set({ alerts: [], loading: true, error: null });

    try {
      console.log(
        "Force refreshing alerts for user:",
        currentUserId,
        "page:",
        page,
        "pageSize:",
        pageSize,
      );
      const response = await api.get(
        `${ALERTS_API_BASE}/Alert/all/${currentUserId}`,
        {
          params: {
            page,
            pageSize,
          },
        },
      );

      console.log("API Response:", response.data);

      // Handle both old format (array) and new format (paginated)
      let apiData: ApiAlert[] = [];
      let paginationData: PaginationMeta = initialState.pagination;

      if (response.data.items && response.data.meta) {
        // New paginated format
        apiData = response.data.items;
        paginationData = response.data.meta;
      } else if (Array.isArray(response.data)) {
        // Old format - array
        apiData = response.data;
        paginationData = {
          pageSize: response.data.length,
          currentPage: 1,
          totalPages: 1,
          totalCount: response.data.length,
          hasNextPage: false,
          hasPreviousPage: false,
        };
      } else {
        console.error("Unexpected API response format:", response.data);
        throw new Error("Invalid API response format");
      }

      console.log("Number of alerts received:", apiData.length);

      // Transform API alerts to match existing Alert interface
      const transformedAlerts: Alert[] = apiData.map(transformApiAlert);

      console.log("Transformed alerts:", transformedAlerts);

      set({
        alerts: transformedAlerts,
        loading: false,
        userId: currentUserId,
        pagination: paginationData,
      });
    } catch (error: unknown) {
      console.error("Failed to refresh alerts:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to refresh alerts";

      set({
        error: errorMessage,
        loading: false,
      });

      // Show toast notification for the error
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  clearError: () => {
    set({ error: null });
  },

  fetchDetailedAlert: async (
    requestId: string = "f848d5d8-7cdd-4b44-be49-f059bc028015",
    userId: string = "eaf6cc26-4a82-4b77-b9f6-34bdea969921",
  ) => {
    set({ detailedLoading: true, detailedError: null });

    try {
      console.log("Fetching detailed alert data:", { requestId, userId });

      const response = await api.get(
        `/workflow/workflow/detailed-approval-request/${requestId}/${userId}`,
      );

      console.log("Detailed alert API response:", response.data);

      set({
        detailedData: response.data,
        detailedLoading: false,
      });
    } catch (error: unknown) {
      console.error("Failed to fetch detailed alert data:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to load detailed information";

      set({
        detailedError: errorMessage,
        detailedLoading: false,
      });

      // Show toast notification for the error
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  approveRequest: async (requestId: string, comments?: string) => {
    try {
      // Use hardcoded user ID for now - should come from auth store in production
      const approverId = "eaf6cc26-4a82-4b77-b9f6-34bdea969921";

      await api.post(
        `/workflow/workflow/${requestId}/nodes/approval-node-1/decide`,
        {
          decision: "APPROVE",
          decidedBy: approverId,
          formData: {
            comments: comments || "Request approved",
          },
        },
      );

      // Show success toast
      toast({
        title: "Success",
        description: "Request approved successfully",
        variant: "default",
      });

      // Refresh detailed data to show updated status
      const currentState = useAlertsStore.getState();
      if (currentState.detailedData) {
        await currentState.fetchDetailedAlert(requestId);
      }
    } catch (error: unknown) {
      console.error("Error approving request:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to approve request";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  rejectRequest: async (requestId: string, comments?: string) => {
    try {
      // Use hardcoded user ID for now - should come from auth store in production
      const approverId = "eaf6cc26-4a82-4b77-b9f6-34bdea969921";

      await api.post(
        `/workflow/workflow/${requestId}/nodes/approval-node-1/decide`,
        {
          decision: "REJECT",
          decidedBy: approverId,
          formData: {
            comments: comments || "Request rejected",
          },
        },
      );

      // Show success toast
      toast({
        title: "Success",
        description: "Request rejected successfully",
        variant: "default",
      });

      // Refresh detailed data to show updated status
      const currentState = useAlertsStore.getState();
      if (currentState.detailedData) {
        await currentState.fetchDetailedAlert(requestId);
      }
    } catch (error: unknown) {
      console.error("Error rejecting request:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to reject request";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  clearDetailedData: () => {
    set({
      detailedData: null,
      detailedError: null,
      detailedLoading: false,
    });
  },

  markAlertAsRead: async (alertId: string) => {
    const currentUserId = getCurrentUserId();

    try {
      console.log("Marking alert as read:", { alertId, userId: currentUserId });

      await api.patch(
        `${ALERTS_API_BASE}/Alert/mark-as-read/${currentUserId}/${alertId}`,
      );

      // Update the alert in the store to mark it as read
      set((state) => ({
        alerts: state.alerts.map((alert) =>
          alert.id === alertId ? { ...alert, isRead: true } : alert,
        ),
      }));

      console.log("Alert marked as read successfully");
    } catch (error: unknown) {
      console.error("Error marking alert as read:", error);
      const apiError = error as ApiError;
      const errorMessage =
        apiError?.response?.data?.message ||
        apiError?.message ||
        "Failed to mark alert as read";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  },

  // SignalR Methods
  connectSignalR: async (userId: string) => {
    // Prevent duplicate connections
    if (isConnecting) {
      return;
    }
    if (currentConnectedUserId === userId) {
      return;
    }

    isConnecting = true;

    try {
      // Disconnect existing connection if different user
      if (currentConnectedUserId && currentConnectedUserId !== userId) {
        get().disconnectSignalR();
      }

      // Connect to SignalR
      await signalRService.connect(userId);
      currentConnectedUserId = userId;

      // Set up alert callback
      signalRService.onAlert((signalRAlert) => {
        console.log("SignalR Alert received:", signalRAlert);
        const transformedAlert = transformSignalRAlert(signalRAlert);
        get().addAlertFromSignalR(transformedAlert);
      });

      set({ isSignalRConnected: true, userId });
      console.log("SignalR connected for alerts, user:", userId);
    } catch (error) {
      console.error("Failed to connect SignalR for alerts:", error);
      set({ isSignalRConnected: false });
    } finally {
      isConnecting = false;
    }
  },

  disconnectSignalR: () => {
    signalRService.disconnect();
    currentConnectedUserId = null;
    set({ isSignalRConnected: false });
    console.log("SignalR disconnected for alerts");
  },

  addAlertFromSignalR: (alert: Alert) => {
    set((state) => {
      // Check if alert already exists to prevent duplicates
      const existingAlertIndex = state.alerts.findIndex(
        (a) => a.id === alert.id,
      );

      if (existingAlertIndex >= 0) {
        // Update existing alert
        const updatedAlerts = [...state.alerts];
        updatedAlerts[existingAlertIndex] = alert;
        return { alerts: updatedAlerts };
      } else {
        // Add new alert at the beginning
        return { alerts: [alert, ...state.alerts] };
      }
    });
  },

  removeAlertFromSignalR: (alertId: string) => {
    set((state) => ({
      alerts: state.alerts.filter((alert) => alert.id !== alertId),
    }));
  },
}));
