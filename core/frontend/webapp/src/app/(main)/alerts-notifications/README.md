# Alerts & Notifications Module

## Overview

The alerts-notifications module provides real-time updates for alerts and notifications using SignalR, ensuring users always see the latest data without manual refresh.

## Architecture

### Real-Time Updates with SignalR

The module integrates with SignalR to provide seamless real-time updates:

1. **Initial Load**: When the page loads, it fetches data from the backend API endpoints
2. **SignalR Connection**: Establishes a SignalR connection for real-time updates
3. **Live Updates**: New alerts/notifications appear instantly via SignalR
4. **Seamless Experience**: Users don't notice the transition between API and real-time data

### Data Flow

```
Page Load → API Fetch → SignalR Connect → Real-time Updates
     ↓           ↓           ↓              ↓
Initial Data → Store → Live Updates → UI Updates
```

## Key Components

### Stores

#### `alertsStore.ts`

- Manages alert data and SignalR connection
- Handles approval/rejection workflows
- Transforms API and SignalR data to consistent format

#### `notificationsStore.ts`

- Manages notification data and SignalR connection
- Handles real-time notification updates
- Transforms API and SignalR data to consistent format

### Custom Hook

#### `useAlertsNotificationsSignalR.ts`

- Manages SignalR connection lifecycle
- Handles user changes and reconnections
- Provides unified interface for both stores

### Components

#### `AlertsNotificationsContainer.tsx`

- Main container with tabbed interface
- Shows SignalR connection status indicators
- Handles search and filtering

#### `AlertsTable.tsx`

- Displays alerts in table format
- Different actions for approval vs redirect alerts
- Auto-opening functionality for specific alerts

#### `AlertDetailModal.tsx`

- Detailed view for approval workflows
- Timeline visualization
- Response submission interface

## SignalR Integration

### Connection Management

```typescript
// Automatic connection on page load
useEffect(() => {
  if (!userId) return;

  // Fetch initial data and connect SignalR
  fetchAlerts(userId);
  fetchNotifications(userId);
}, [userId]);
```

### Real-Time Updates

```typescript
// SignalR callbacks
signalRService.onAlert((signalRAlert) => {
  const transformedAlert = transformSignalRAlert(signalRAlert);
  addAlertFromSignalR(transformedAlert);
});

signalRService.onNotification((signalRNotification) => {
  const transformedNotification =
    transformSignalRNotification(signalRNotification);
  addNotificationFromSignalR(transformedNotification);
});
```

### User Change Handling

```typescript
// Listen for user changes from header
useEffect(() => {
  const handleUserChange = (event: CustomEvent) => {
    const newUser = event.detail;
    if (newUser?.id && newUser.id !== userId) {
      // Reconnect SignalR for new user
      disconnectSignalR();
      fetchAlerts(newUser.id);
      fetchNotifications(newUser.id);
    }
  };

  window.addEventListener("userChanged", handleUserChange);
  return () => window.removeEventListener("userChanged", handleUserChange);
}, [userId]);
```

## API Endpoints

### Alerts

- `GET /notifications-signalr/api/Alert/all/{userId}` - Fetch all alerts
- `GET /notifications-signalr/api/Alert/recent/{userId}` - Fetch recent alerts
- `GET /notifications-signalr/api/Alert/unread-count/{userId}` - Get unread count

### Notifications

- `GET /notifications-signalr/api/Notification/all/{userId}` - Fetch all notifications
- `GET /notifications-signalr/api/Notification/recent/{userId}` - Fetch recent notifications
- `GET /notifications-signalr/api/Notification/unread-count/{userId}` - Get unread count

### Workflow Actions

- `POST /workflow/workflow/{requestId}/nodes/approval-node-1/decide` - Approve/reject requests

## SignalR Events

### Alerts

- `NewAlert` - New alert received
- `AlertUpdated` - Alert status updated
- `AlertRemoved` - Alert removed

### Notifications

- `NewNotification` - New notification received
- `NotificationUpdated` - Notification updated
- `NotificationRemoved` - Notification removed

## Features

### Real-Time Updates

- ✅ Instant new alert/notification display
- ✅ Live status updates
- ✅ Seamless user experience

### User Management

- ✅ Automatic reconnection on user change
- ✅ Proper cleanup on unmount
- ✅ Connection status indicators

### Data Consistency

- ✅ API fallback for initial load
- ✅ Duplicate prevention
- ✅ Error handling with toast notifications

### UI/UX

- ✅ Connection status badges
- ✅ Loading states
- ✅ Auto-opening for specific items
- ✅ Search and filtering

## Usage

```typescript
// In your component
const {
  alerts,
  notifications,
  alertsLoading,
  notificationsLoading,
  isAlertsSignalRConnected,
  isNotificationsSignalRConnected,
} = useAlertsNotificationsSignalR({
  userId: "user-id",
});
```

## Configuration

### Environment Variables

```env
NEXT_PUBLIC_SIGNALR_URL=https://ingress.connected-workersdev.aptiv.com/notifications-signalr/hub/notification
```

### SignalR Connection Settings

- **Transport**: WebSockets with LongPolling fallback
- **Reconnection**: Automatic with exponential backoff
- **Authentication**: User ID in query parameters
- **Credentials**: Enabled for Azure SignalR

## Troubleshooting

### Common Issues

1. **SignalR Connection Failed**

   - Check network connectivity
   - Verify SignalR URL configuration
   - Check SSL certificate acceptance

2. **Duplicate Data**

   - Ensure proper cleanup on user changes
   - Check for duplicate SignalR callbacks

3. **Missing Real-Time Updates**
   - Verify SignalR connection status
   - Check browser console for errors
   - Ensure user ID is correct

### Debug Mode

Enable debug logging by checking browser console for:

- SignalR connection status
- Data transformation logs
- User change events
- API response logs
