/// <reference types="jest" />

import { render, screen } from "@testing-library/react";
import AlertsNotificationsPage from "../page";

// Mock the hook directly
jest.mock("../hooks/useAlertsNotificationsSignalR", () => ({
  useAlertsNotificationsSignalR: () => ({
    alerts: [],
    notifications: [],
    alertsLoading: false,
    notificationsLoading: false,
    alertsError: null,
    notificationsError: null,
    refreshAlerts: jest.fn(),
    refreshNotifications: jest.fn(),
    markAlertAsRead: jest.fn(),
    markNotificationAsRead: jest.fn(),
    disconnectAll: jest.fn(),
    alertsPagination: {
      pageSize: 5,
      currentPage: 1,
      totalPages: 0,
      totalCount: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    },
    notificationsPagination: {
      pageSize: 5,
      currentPage: 1,
      totalPages: 0,
      totalCount: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    },
  }),
}));

// Mock the container component
jest.mock("../components/AlertsNotificationsContainer", () => ({
  AlertsNotificationsContainer: ({
    alerts,
    notifications,
  }: {
    alerts: unknown[];
    notifications: unknown[];
  }) => (
    <div data-testid="alerts-notifications-container">
      <div data-testid="alerts-count">{alerts.length}</div>
      <div data-testid="notifications-count">{notifications.length}</div>
    </div>
  ),
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useSearchParams: () => ({
    get: jest.fn(() => null),
  }),
}));

describe("AlertsNotificationsPage", () => {
  it("should render the page component", () => {
    render(<AlertsNotificationsPage />);

    expect(
      screen.getByTestId("alerts-notifications-container"),
    ).toBeInTheDocument();
    expect(screen.getByTestId("alerts-count")).toHaveTextContent("0");
    expect(screen.getByTestId("notifications-count")).toHaveTextContent("0");
  });

  it("should render without crashing", () => {
    expect(() => render(<AlertsNotificationsPage />)).not.toThrow();
  });
});
