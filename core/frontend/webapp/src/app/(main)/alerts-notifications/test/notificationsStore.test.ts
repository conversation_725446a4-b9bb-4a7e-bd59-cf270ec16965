/// <reference types="jest" />

import { useNotificationsStore } from "../store/notificationsStore";
import { getRelativeTime } from "../store/notificationsStore";

// Mock axios
jest.mock("@/lib/axios", () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

// Mock toast
jest.mock("@/hooks/use-toast", () => ({
  toast: jest.fn(),
}));

describe("NotificationsStore", () => {
  beforeEach(() => {
    // Reset store state before each test
    useNotificationsStore.setState({
      notifications: [],
      loading: false,
      error: null,
    });
  });

  it("should initialize with default state", () => {
    const state = useNotificationsStore.getState();
    expect(state.notifications).toEqual([]);
    expect(state.loading).toBe(false);
    expect(state.error).toBe(null);
  });

  it("should clear error", () => {
    // Set an error first
    useNotificationsStore.setState({ error: "Test error" });

    // Clear the error
    useNotificationsStore.getState().clearError();

    const state = useNotificationsStore.getState();
    expect(state.error).toBe(null);
  });
});

describe("getRelativeTime (notifications)", () => {
  it('should return "just now" for empty string', () => {
    expect(getRelativeTime("")).toBe("just now");
  });

  it("should return formatted time for recent dates", () => {
    const result = getRelativeTime("2025-01-01T10:00:00Z");
    expect(typeof result).toBe("string");
    expect(result.length).toBeGreaterThan(0);
  });

  it("should handle formatted display format", () => {
    const result = getRelativeTime("01/01/2025 at 10:00");
    expect(typeof result).toBe("string");
    expect(result.length).toBeGreaterThan(0);
  });
});
