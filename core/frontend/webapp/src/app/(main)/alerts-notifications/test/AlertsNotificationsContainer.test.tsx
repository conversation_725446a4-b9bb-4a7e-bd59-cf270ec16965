/// <reference types="jest" />

import { render } from "@testing-library/react";

interface MockProps {
  alerts: unknown[];
  notifications: unknown[];
  onResponseSubmit?: () => void;
}

// Mock the entire component module
jest.mock("../components/AlertsNotificationsContainer", () => ({
  AlertsNotificationsContainer: ({ alerts, notifications }: MockProps) => (
    <div data-testid="alerts-notifications-container">
      <div data-testid="alerts-table">Alerts: {alerts.length}</div>
      <div data-testid="notifications-list">
        Notifications: {notifications.length}
      </div>
    </div>
  ),
}));

import { AlertsNotificationsContainer } from "../components/AlertsNotificationsContainer";

// Mock next-intl
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

describe("AlertsNotificationsContainer", () => {
  const defaultProps = {
    alerts: [],
    notifications: [],
    onResponseSubmit: jest.fn(),
    alertsLoading: false,
    notificationsLoading: false,
  };

  it("should render the container component", () => {
    const result = render(<AlertsNotificationsContainer {...defaultProps} />);

    expect(result.getByTestId("alerts-notifications-container")).toBeTruthy();
  });

  it("should render without crashing with empty data", () => {
    expect(() =>
      render(<AlertsNotificationsContainer {...defaultProps} />),
    ).not.toThrow();
  });

  it("should handle loading states", () => {
    const props = {
      ...defaultProps,
      alertsLoading: true,
      notificationsLoading: true,
    };

    expect(() =>
      render(<AlertsNotificationsContainer {...props} />),
    ).not.toThrow();
  });
});
