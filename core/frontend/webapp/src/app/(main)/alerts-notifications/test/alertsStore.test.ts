/// <reference types="jest" />

import { useAlertsStore } from "../store/alertsStore";
import { getRelativeTime } from "../store/alertsStore";

// Mock axios
jest.mock("@/lib/axios", () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

// Mock toast
jest.mock("@/hooks/use-toast", () => ({
  toast: jest.fn(),
}));

describe("AlertsStore", () => {
  beforeEach(() => {
    // Reset store state before each test
    useAlertsStore.setState({
      alerts: [],
      loading: false,
      error: null,
      detailedData: null,
      detailedLoading: false,
      detailedError: null,
    });
  });

  it("should initialize with default state", () => {
    const state = useAlertsStore.getState();
    expect(state.alerts).toEqual([]);
    expect(state.loading).toBe(false);
    expect(state.error).toBe(null);
  });

  it("should clear error", () => {
    // Set an error first
    useAlertsStore.setState({ error: "Test error" });

    // Clear the error
    useAlertsStore.getState().clearError();

    const state = useAlertsStore.getState();
    expect(state.error).toBe(null);
  });

  it("should clear detailed data", () => {
    // Set detailed data first
    useAlertsStore.setState({
      detailedData: { workflowId: "test" } as never,
      detailedError: "Test error",
      detailedLoading: true,
    });

    // Clear detailed data
    useAlertsStore.getState().clearDetailedData();

    const state = useAlertsStore.getState();
    expect(state.detailedData).toBe(null);
    expect(state.detailedError).toBe(null);
    expect(state.detailedLoading).toBe(false);
  });
});

describe("getRelativeTime", () => {
  it('should return "just now" for empty string', () => {
    expect(getRelativeTime("")).toBe("just now");
  });

  it("should return formatted time for recent dates", () => {
    const result = getRelativeTime("2025-01-01T10:00:00Z");
    expect(typeof result).toBe("string");
    expect(result.length).toBeGreaterThan(0);
  });

  it("should handle formatted display format", () => {
    const result = getRelativeTime("01/01/2025 at 10:00");
    expect(typeof result).toBe("string");
    expect(result.length).toBeGreaterThan(0);
  });
});
