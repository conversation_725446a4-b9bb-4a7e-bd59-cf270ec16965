import { useEffect, useRef } from "react";
import { useAlertsStore } from "../store/alertsStore";
import { useNotificationsStore } from "../store/notificationsStore";

export const useAlertsNotificationsSignalR = () => {
  const {
    alerts,
    loading: alertsLoading,
    error: alertsError,
    fetchAlerts,
    refreshAlerts,
    markAlertAsRead,
    disconnectSignalR: disconnectAlertsSignalR,
    //isSignalRConnected: isAlertsSignalRConnected,
    pagination: alertsPagination,
  } = useAlertsStore();

  const {
    notifications,
    loading: notificationsLoading,
    error: notificationsError,
    fetchNotifications,
    refreshNotifications,
    markNotificationAsRead,
    disconnectSignalR: disconnectNotificationsSignalR,
    //isSignalRConnected: isNotificationsSignalRConnected,
    pagination: notificationsPagination,
  } = useNotificationsStore();

  const hasInitialized = useRef(false);

  // Handle initial data fetch and SignalR connection
  useEffect(() => {
    // Only fetch if we haven't initialized
    if (!hasInitialized.current) {
      console.log("Initializing alerts and notifications");
      fetchAlerts(); // Will use getCurrentUserId() internally
      fetchNotifications(); // Will use getCurrentUserId() internally
      hasInitialized.current = true;
    }
  }, [fetchAlerts, fetchNotifications]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log("Cleaning up SignalR connections");
      disconnectAlertsSignalR();
      disconnectNotificationsSignalR();
      hasInitialized.current = false;
    };
  }, [disconnectAlertsSignalR, disconnectNotificationsSignalR]);

  // Listen for user change events from the header
  useEffect(() => {
    const handleUserChange = (event: CustomEvent) => {
      console.log(
        "User changed via header, updating alerts/notifications",
        event.detail,
      );
      // Reset initialization flag to trigger re-fetch
      hasInitialized.current = false;
      // Re-fetch data for new user
      fetchAlerts();
      fetchNotifications();
    };

    window.addEventListener("userChanged", handleUserChange as EventListener);

    return () => {
      window.removeEventListener(
        "userChanged",
        handleUserChange as EventListener,
      );
    };
  }, [fetchAlerts, fetchNotifications]);

  return {
    alerts,
    notifications,
    alertsLoading,
    notificationsLoading,
    alertsError,
    notificationsError,
    refreshAlerts,
    refreshNotifications,
    markAlertAsRead,
    markNotificationAsRead,
    disconnectAll: () => {
      disconnectAlertsSignalR();
      disconnectNotificationsSignalR();
    },
    // Pagination data from stores
    alertsPagination,
    notificationsPagination,
  };
};
