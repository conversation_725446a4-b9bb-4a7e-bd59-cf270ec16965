"use client";

import { useSearchParams } from "next/navigation";
import { AlertsNotificationsContainer } from "./components/AlertsNotificationsContainer";
import { useAlertsNotificationsSignalR } from "./hooks/useAlertsNotificationsSignalR";
import { useState } from "react";
import { SortingState } from "@tanstack/react-table";
import CustomPageCard from "@/components/common/CustomPageCard";

export default function AlertsNotificationsPage() {
  const searchParams = useSearchParams();
  const alertId = searchParams?.get("alertId");
  const notificationId = searchParams?.get("notificationId");
  const tab = searchParams?.get("tab");

  // Server pagination state
  const [alertsPagination, setAlertsPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [notificationsPagination, setNotificationsPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const [alertsSearchQuery, setAlertsSearchQuery] = useState("");
  const [notificationsSearchQuery, setNotificationsSearchQuery] = useState("");
  const [alertsSorting, setAlertsSorting] = useState<SortingState>([]);
  const [notificationsSorting, setNotificationsSorting] =
    useState<SortingState>([]);

  const {
    alerts,
    notifications,
    alertsLoading,
    notificationsLoading,
    refreshAlerts,
    refreshNotifications,
    markAlertAsRead,
    markNotificationAsRead,
    // Pagination data from stores
    alertsPagination: alertsPaginationData,
    notificationsPagination: notificationsPaginationData,
  } = useAlertsNotificationsSignalR();

  const handleResponseSubmit = (
    alertId: string,
    response: "Approve" | "Reject",
    comment: string,
  ) => {
    console.log("Submitting response:", {
      alertId,
      response,
      comment,
    });
  };

  // Handle alerts pagination change
  const handleAlertsPaginationChange = (
    pageIndex: number,
    pageSize: number,
  ) => {
    setAlertsPagination({ pageIndex, pageSize });
    // Call the store method with new pagination params
    refreshAlerts(undefined, pageIndex + 1, pageSize);
  };

  // Handle notifications pagination change
  const handleNotificationsPaginationChange = (
    pageIndex: number,
    pageSize: number,
  ) => {
    setNotificationsPagination({ pageIndex, pageSize });
    // Call the store method with new pagination params
    refreshNotifications(undefined, pageIndex + 1, pageSize);
  };

  // Handle alerts search change
  const handleAlertsSearchChange = (searchQuery: string) => {
    setAlertsSearchQuery(searchQuery);
    setAlertsPagination({ pageIndex: 0, pageSize: alertsPagination.pageSize });
    // Call the store method with search params
    refreshAlerts(undefined, 1, alertsPagination.pageSize);
  };

  // Handle notifications search change
  const handleNotificationsSearchChange = (searchQuery: string) => {
    setNotificationsSearchQuery(searchQuery);
    setNotificationsPagination({
      pageIndex: 0,
      pageSize: notificationsPagination.pageSize,
    });
    // Call the store method with search params
    refreshNotifications(undefined, 1, notificationsPagination.pageSize);
  };

  // Handle alerts sorting change
  const handleAlertsSortingChange = (sorting: SortingState) => {
    setAlertsSorting(sorting);
    // Call the store method with sorting params
    refreshAlerts(
      undefined,
      alertsPagination.pageIndex + 1,
      alertsPagination.pageSize,
    );
  };

  // Handle notifications sorting change
  const handleNotificationsSortingChange = (sorting: SortingState) => {
    setNotificationsSorting(sorting);
    // Call the store method with sorting params
    refreshNotifications(
      undefined,
      notificationsPagination.pageIndex + 1,
      notificationsPagination.pageSize,
    );
  };

  return (
    <CustomPageCard>
      <AlertsNotificationsContainer
        alerts={alerts}
        notifications={notifications}
        onResponseSubmit={handleResponseSubmit}
        autoOpenAlertId={alertId}
        autoOpenNotificationId={notificationId}
        initialTab={tab}
        alertsLoading={alertsLoading}
        notificationsLoading={notificationsLoading}
        markAlertAsRead={markAlertAsRead}
        markNotificationAsRead={markNotificationAsRead}
        // Server pagination props
        serverPagination={true}
        onAlertsPaginationChange={handleAlertsPaginationChange}
        onNotificationsPaginationChange={handleNotificationsPaginationChange}
        alertsTotalItems={alertsPaginationData?.totalCount || 0}
        notificationsTotalItems={notificationsPaginationData?.totalCount || 0}
        serverSearch={true}
        onAlertsSearchChange={handleAlertsSearchChange}
        onNotificationsSearchChange={handleNotificationsSearchChange}
        alertsSorting={alertsSorting}
        notificationsSorting={notificationsSorting}
        onAlertsSortingChange={handleAlertsSortingChange}
        onNotificationsSortingChange={handleNotificationsSortingChange}
        alertsCurrentPage={alertsPagination.pageIndex}
        notificationsCurrentPage={notificationsPagination.pageIndex}
        alertsPageSize={alertsPagination.pageSize}
        notificationsPageSize={notificationsPagination.pageSize}
        alertsSearchValue={alertsSearchQuery}
        notificationsSearchValue={notificationsSearchQuery}
      />
    </CustomPageCard>
  );
}
