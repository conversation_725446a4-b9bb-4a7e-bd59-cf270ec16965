"use client";

import { Notification } from "../types/alert.types";
import { NotificationDetailModal } from "./NotificationDetailModal";
import { NotificationFilters } from "./NotificationFilters";
import { useState, useMemo, useEffect, useRef } from "react";
import { DataTable } from "@/components/common/tables/DataTable";
import { createNotificationsColumns } from "./notificationsColumns";
import { SortingState } from "@tanstack/react-table";

interface NotificationsListProps {
  notifications: Notification[];
  autoOpenNotificationId?: string | null;
  onAutoOpened?: () => void;
  loading?: boolean;
  markNotificationAsRead?: (notificationId: string) => Promise<void>;
  // Server pagination props
  serverPagination?: boolean;
  onPaginationChange?: (pageIndex: number, pageSize: number) => void;
  totalItems?: number;
  serverSearch?: boolean;
  onSearchChange?: (searchQuery: string) => void;
  sorting?: SortingState;
  onSortingChange?: (sorting: SortingState) => void;
  currentPage?: number;
  pageSize?: number;
  searchValue?: string;
}

export function NotificationsList({
  notifications,
  autoOpenNotificationId,
  onAutoOpened,
  loading = false,
  markNotificationAsRead,
  // Server pagination props
  serverPagination = false,
  onPaginationChange,
  totalItems,
  serverSearch = false,
  onSearchChange,
  sorting,
  onSortingChange,
  currentPage = 0,
  pageSize = 5,
  searchValue = "",
}: NotificationsListProps) {
  const [activeFilter, setActiveFilter] = useState("all");
  const [openModalNotificationId, setOpenModalNotificationId] = useState<
    string | null
  >(null);
  const hasAutoOpened = useRef(false);

  // Auto-open modal when autoOpenNotificationId is provided and matches a notification
  // But only do this once, not every time the component re-renders
  useEffect(() => {
    if (
      autoOpenNotificationId &&
      !hasAutoOpened.current &&
      notifications.some(
        (notification) => notification.id === autoOpenNotificationId,
      )
    ) {
      const notification = notifications.find(
        (n) => n.id === autoOpenNotificationId,
      );

      if (notification?.redirectTarget) {
        // If notification has redirect target, redirect instead of opening modal
        handleNotificationRedirect(notification);
      } else {
        // Open modal for notifications without redirect
        setOpenModalNotificationId(autoOpenNotificationId);
      }

      hasAutoOpened.current = true;
      onAutoOpened?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    autoOpenNotificationId,
    notifications,
    onAutoOpened,
    markNotificationAsRead,
  ]);

  const handleModalOpenChange = (open: boolean, notificationId: string) => {
    if (open) {
      setOpenModalNotificationId(notificationId);
      // Mark notification as read when dialog opens
      markNotificationAsRead?.(notificationId);
    } else {
      setOpenModalNotificationId(null);
    }
  };

  const handleNotificationSelect = (notification: Notification) => {
    console.log("Selected notification:", notification);
  };

  // Handle notification redirect and mark as read
  const handleNotificationRedirect = (notification: Notification) => {
    // Mark notification as read when redirecting
    if (markNotificationAsRead) {
      markNotificationAsRead(notification.id);
    }

    // Get the redirect URL
    const redirectUrl = getRedirectUrl(notification.redirectTarget);

    // Navigate to the redirect URL
    if (redirectUrl !== "#") {
      window.open(redirectUrl, "_blank");
    }
  };

  // Mapping function for redirect targets
  const getRedirectUrl = (
    redirectTarget: string | null | undefined,
  ): string => {
    if (!redirectTarget) return "#";

    const redirectMapping: Record<string, string> = {
      VISUAL_CHECK: "/my-team",
      OVERTIME_APPROVAL: "/overtime-planification",
      // Add more mappings as needed based on your application routes
    };

    return redirectMapping[redirectTarget] || "#";
  };

  // Filter notifications based on selected type
  const filteredNotifications = useMemo(() => {
    if (activeFilter === "all") {
      return notifications;
    }
    return notifications.filter(
      (notification) =>
        notification.type.toLowerCase() === activeFilter.toLowerCase(),
    );
  }, [notifications, activeFilter]);

  // Calculate counts for each type
  const notificationCounts = useMemo(
    () => ({
      all: notifications.length,
      information: notifications.filter(
        (n) => n.type.toLowerCase() === "information",
      ).length,
      warning: notifications.filter((n) => n.type.toLowerCase() === "warning")
        .length,
    }),
    [notifications],
  );

  // Create columns configuration
  const columns = createNotificationsColumns({
    onNotificationSelect: handleNotificationSelect,
    markNotificationAsRead,
    openModalNotificationId,
    onModalOpenChange: handleModalOpenChange,
  });

  return (
    <div className="space-y-4">
      {/* Filter Buttons */}
      <NotificationFilters
        onFilterChange={setActiveFilter}
        activeFilter={activeFilter}
        notificationCounts={notificationCounts}
      />

      {/* Notifications DataTable */}
      <DataTable
        columns={columns}
        data={filteredNotifications}
        isLoading={loading}
        id="notifications-table"
        // Server pagination props
        serverPagination={serverPagination}
        onPaginationChange={onPaginationChange}
        totalItems={totalItems}
        serverSearch={serverSearch}
        onSearchChange={onSearchChange}
        sorting={sorting}
        onSortingChange={onSortingChange}
        currentPage={currentPage}
        pageSize={pageSize}
        searchValue={searchValue}
      />

      {/* Notification Detail Modal */}
      {openModalNotificationId && (
        <NotificationDetailModal
          notification={
            notifications.find(
              (notification) => notification.id === openModalNotificationId,
            )!
          }
          onNotificationSelect={handleNotificationSelect}
          isOpen={!!openModalNotificationId}
          onOpenChange={(open) =>
            handleModalOpenChange(open, openModalNotificationId)
          }
        />
      )}
    </div>
  );
}
