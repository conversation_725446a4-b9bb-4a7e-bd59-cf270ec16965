"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Alert } from "../types/alert.types";
import { Button } from "@/components/ui/button";
import { Eye, ExternalLink } from "lucide-react";
import { useTranslations } from "next-intl";
import { formatDateTime, getRelativeTime } from "../store/alertsStore";
import CustomIcon from "@/components/common/CustomIcons";

interface AlertsColumnsProps {
  onAlertSelect?: (alert: Alert) => void;
  onResponseSubmit: (
    alertId: string,
    response: "Approve" | "Reject",
    comment: string,
  ) => void;
  markAlertAsRead?: (alertId: string) => Promise<void>;
  openModalAlertId?: string | null;
  onModalOpenChange?: (open: boolean, alertId: string) => void;
}

export const createAlertsColumns = ({
  onAlertSelect,
  //onResponseSubmit,
  markAlertAsRead,
  //openModalAlertId,
  onModalOpenChange,
}: AlertsColumnsProps): ColumnDef<Alert>[] => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = useTranslations("alerts_notifications");

  // Mapping function for redirect targets
  const getRedirectUrl = (
    redirectTarget: string | null | undefined,
  ): string => {
    if (!redirectTarget) return "#";

    const redirectMapping: Record<string, string> = {
      VISUAL_CHECK: "/my-team",
      OVERTIME_APPROVAL: "/overtime-planification",
      // Add more mappings as needed based on your application routes
    };

    return redirectMapping[redirectTarget] || "#";
  };

  // Handle alert redirect and mark as read
  const handleAlertRedirect = (alert: Alert) => {
    // Mark alert as read when redirecting
    if (markAlertAsRead) {
      markAlertAsRead(alert.id);
    }

    // Get the redirect URL
    const redirectUrl = getRedirectUrl(alert.redirectTarget);

    // Navigate to the redirect URL
    if (redirectUrl !== "#") {
      window.open(redirectUrl, "_blank");
    }
  };

  return [
    {
      accessorKey: "title",
      header: t("table.headers.title"),
      cell: ({ row }) => {
        const alert = row.original;
        return (
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 flex-shrink-0">
              <CustomIcon name="alertNotificationIcon" />
            </div>
            <span className="text-xs leading-4">{alert.title}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "requesterID",
      header: t("table.headers.requesterID"),
      cell: ({ row }) => (
        <div className="text-xs text-[#848484]">
          {row.original.requesterID || "N/A"}
        </div>
      ),
    },
    {
      accessorKey: "requesterName",
      header: t("table.headers.requesterName"),
      cell: ({ row }) => (
        <div className="text-xs text-[#848484]">
          {row.original.requesterName || "N/A"}
        </div>
      ),
    },
    {
      accessorKey: "type",
      header: t("table.headers.type"),
      cell: ({ row }) => (
        <div className="text-xs text-[#848484]">{row.original.type}</div>
      ),
    },
    {
      accessorKey: "timestamp",
      header: t("table.headers.datetime"),
      cell: ({ row }) => {
        const alert = row.original;
        const date = alert.timestamp || alert.createdAt;
        const formattedDate = date ? formatDateTime(date) : "N/A";
        const relativeTime = date ? getRelativeTime(formattedDate) : "";

        return (
          <div className="text-xs text-[#848484]">
            <div>{formattedDate}</div>
            <div className="text-xs text-gray-400 font-light">
              ({relativeTime})
            </div>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: t("table.headers.details"),
      cell: ({ row }) => {
        const alert = row.original;

        if (alert.type === "APPROVAL") {
          return (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onAlertSelect?.(alert);
                onModalOpenChange?.(true, alert.id);
                markAlertAsRead?.(alert.id);
              }}
              className="text-[#010101] hover:bg-transparent"
            >
              <Eye className="h-4 w-4 mr-1" />
              {t("table.actions.view_details")}
            </Button>
          );
        } else if (alert.type === "REDIRECT" || alert.redirectTarget) {
          return (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAlertRedirect(alert)}
              className="text-[#010101] hover:bg-transparent"
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              {t("table.actions.view_details")}
            </Button>
          );
        } else {
          return (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onAlertSelect?.(alert);
                onModalOpenChange?.(true, alert.id);
                markAlertAsRead?.(alert.id);
              }}
              className="text-[#010101] hover:bg-transparent"
            >
              <Eye className="h-4 w-4 mr-1" />
              {t("table.actions.view_details")}
            </Button>
          );
        }
      },
    },
  ];
};
