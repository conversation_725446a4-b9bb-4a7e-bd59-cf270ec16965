"use client";

import { useState, useMemo, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
  CustomTabsContent,
} from "@/components/common/CustomTabs";
import { Search } from "lucide-react";
import { useTranslations } from "next-intl";
import { Alert, Notification } from "../types/alert.types";
import { AlertFilters } from "./AlertFilters";
import { AlertsTable } from "./AlertsTable";
import { NotificationsList } from "./NotificationsList";
import { SortingState } from "@tanstack/react-table";

interface AlertsNotificationsContainerProps {
  alerts: Alert[];
  notifications: Notification[];
  onResponseSubmit: (
    alertId: string,
    response: "Approve" | "Reject",
    comment: string,
  ) => void;
  autoOpenAlertId?: string | null;
  autoOpenNotificationId?: string | null;
  initialTab?: string | null;
  alertsLoading?: boolean;
  notificationsLoading?: boolean;
  markAlertAsRead?: (alertId: string) => Promise<void>;
  markNotificationAsRead?: (notificationId: string) => Promise<void>;
  // Server pagination props
  serverPagination?: boolean;
  onAlertsPaginationChange?: (pageIndex: number, pageSize: number) => void;
  onNotificationsPaginationChange?: (
    pageIndex: number,
    pageSize: number,
  ) => void;
  alertsTotalItems?: number;
  notificationsTotalItems?: number;
  serverSearch?: boolean;
  onAlertsSearchChange?: (searchQuery: string) => void;
  onNotificationsSearchChange?: (searchQuery: string) => void;
  alertsSorting?: SortingState;
  notificationsSorting?: SortingState;
  onAlertsSortingChange?: (sorting: SortingState) => void;
  onNotificationsSortingChange?: (sorting: SortingState) => void;
  alertsCurrentPage?: number;
  notificationsCurrentPage?: number;
  alertsPageSize?: number;
  notificationsPageSize?: number;
  alertsSearchValue?: string;
  notificationsSearchValue?: string;
}

export function AlertsNotificationsContainer({
  alerts,
  notifications,
  onResponseSubmit,
  autoOpenAlertId,
  autoOpenNotificationId,
  initialTab,
  alertsLoading = false,
  notificationsLoading = false,
  markAlertAsRead,
  markNotificationAsRead,
  // Server pagination props
  serverPagination = false,
  onAlertsPaginationChange,
  onNotificationsPaginationChange,
  alertsTotalItems,
  notificationsTotalItems,
  serverSearch = false,
  onAlertsSearchChange,
  onNotificationsSearchChange,
  alertsSorting,
  notificationsSorting,
  onAlertsSortingChange,
  onNotificationsSortingChange,
  alertsCurrentPage = 0,
  notificationsCurrentPage = 0,
  alertsPageSize = 5,
  notificationsPageSize = 5,
  alertsSearchValue = "",
  notificationsSearchValue = "",
}: AlertsNotificationsContainerProps) {
  const t = useTranslations("alerts_notifications");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const [activeTab, setActiveTab] = useState(
    initialTab === "notifications" ? "notifications" : "alerts",
  );
  const hasAutoOpenedAlert = useRef(false);
  const hasAutoOpenedNotification = useRef(false);

  // Auto-switch to alerts tab when autoOpenAlertId is provided
  // Auto-switch to notifications tab when autoOpenNotificationId is provided
  useEffect(() => {
    if (autoOpenAlertId) {
      setActiveTab("alerts");
    } else if (autoOpenNotificationId) {
      setActiveTab("notifications");
    }
  }, [autoOpenAlertId, autoOpenNotificationId]);

  const filteredAlerts = useMemo(() => {
    let filtered = alerts.filter(
      (alert) =>
        alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (alert.actionIdentifierId &&
          alert.actionIdentifierId
            .toLowerCase()
            .includes(searchTerm.toLowerCase())),
    );

    // Apply type filter
    if (activeFilter !== "all") {
      filtered = filtered.filter((alert) => alert.type === activeFilter);
    }

    return filtered;
  }, [alerts, searchTerm, activeFilter]);

  const alertCounts = useMemo(
    () => ({
      all: alerts.length,
      approval: alerts.filter((a) => a.type === "APPROVAL").length,
      redirect: alerts.filter((a) => a.type === "REDIRECT").length,
    }),
    [alerts],
  );

  const handleResponseSubmit = (
    alertId: string,
    response: "Approve" | "Reject",
    comment: string,
  ) => {
    onResponseSubmit(alertId, response, comment);
  };

  return (
    <div>
      {/* Header Section */}

      {/* Main Content */}
      <div>
        <CustomTabs
          value={activeTab}
          onValueChange={(value) =>
            setActiveTab(value as "alerts" | "notifications")
          }
          className="w-full"
        >
          <div className="flex items-center justify-between mb-4">
            <CustomTabsList>
              <CustomTabsTrigger value="alerts" className="text-xs">
                {t("tabs.alerts")} ({alerts.length})
              </CustomTabsTrigger>
              <CustomTabsTrigger value="notifications" className="text-xs">
                {t("tabs.notifications")} ({notifications.length})
              </CustomTabsTrigger>
            </CustomTabsList>

            <div className="relative w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t("search.placeholder")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <CustomTabsContent value="alerts" className="space-y-4">
            <AlertFilters
              onFilterChange={setActiveFilter}
              activeFilter={activeFilter}
              alertCounts={alertCounts}
            />

            <AlertsTable
              alerts={filteredAlerts}
              onResponseSubmit={handleResponseSubmit}
              autoOpenAlertId={
                autoOpenAlertId && !hasAutoOpenedAlert.current
                  ? autoOpenAlertId
                  : null
              }
              onAutoOpened={() => {
                hasAutoOpenedAlert.current = true;
              }}
              loading={alertsLoading}
              markAlertAsRead={markAlertAsRead}
              // Server pagination props
              serverPagination={serverPagination}
              onPaginationChange={onAlertsPaginationChange}
              totalItems={alertsTotalItems}
              serverSearch={serverSearch}
              onSearchChange={onAlertsSearchChange}
              sorting={alertsSorting}
              onSortingChange={onAlertsSortingChange}
              currentPage={alertsCurrentPage}
              pageSize={alertsPageSize}
              searchValue={alertsSearchValue}
            />
          </CustomTabsContent>

          <CustomTabsContent value="notifications" className="space-y-4">
            <NotificationsList
              notifications={notifications}
              autoOpenNotificationId={
                autoOpenNotificationId && !hasAutoOpenedNotification.current
                  ? autoOpenNotificationId
                  : null
              }
              onAutoOpened={() => {
                hasAutoOpenedNotification.current = true;
              }}
              loading={notificationsLoading}
              markNotificationAsRead={markNotificationAsRead}
              // Server pagination props
              serverPagination={serverPagination}
              onPaginationChange={onNotificationsPaginationChange}
              totalItems={notificationsTotalItems}
              serverSearch={serverSearch}
              onSearchChange={onNotificationsSearchChange}
              sorting={notificationsSorting}
              onSortingChange={onNotificationsSortingChange}
              currentPage={notificationsCurrentPage}
              pageSize={notificationsPageSize}
              searchValue={notificationsSearchValue}
            />
          </CustomTabsContent>
        </CustomTabs>
      </div>
    </div>
  );
}
