"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Notification } from "../types/alert.types";
import { Button } from "@/components/ui/button";
import { Eye, ExternalLink } from "lucide-react";
import { useTranslations } from "next-intl";
import { formatDateTime, getRelativeTime } from "../store/notificationsStore";
import CustomIcon from "@/components/common/CustomIcons";

interface NotificationsColumnsProps {
  onNotificationSelect?: (notification: Notification) => void;
  markNotificationAsRead?: (notificationId: string) => Promise<void>;
  openModalNotificationId?: string | null;
  onModalOpenChange?: (open: boolean, notificationId: string) => void;
}

export const createNotificationsColumns = ({
  onNotificationSelect,
  markNotificationAsRead,
  //openModalNotificationId,
  onModalOpenChange,
}: NotificationsColumnsProps): ColumnDef<Notification>[] => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = useTranslations("alerts_notifications");

  // Mapping function for redirect targets
  const getRedirectUrl = (
    redirectTarget: string | null | undefined,
  ): string => {
    if (!redirectTarget) return "#";

    const redirectMapping: Record<string, string> = {
      VISUAL_CHECK: "/my-team",
      OVERTIME_APPROVAL: "/overtime-planification",
      // Add more mappings as needed based on your application routes
    };

    return redirectMapping[redirectTarget] || "#";
  };

  // Handle notification redirect and mark as read
  const handleNotificationRedirect = (notification: Notification) => {
    // Mark notification as read when redirecting
    if (markNotificationAsRead) {
      markNotificationAsRead(notification.id);
    }

    // Get the redirect URL
    const redirectUrl = getRedirectUrl(notification.redirectTarget);

    // Navigate to the redirect URL
    if (redirectUrl !== "#") {
      window.open(redirectUrl, "_blank");
    }
  };

  return [
    {
      accessorKey: "title",
      header: t("table.headers.title"),
      cell: ({ row }) => {
        const notification = row.original;
        return (
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 flex-shrink-0">
              <CustomIcon name="alertNotificationIcon" />
            </div>
            <span className="text-xs leading-4">{notification.title}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "type",
      header: t("table.headers.type"),
      cell: ({ row }) => (
        <div className="text-xs text-[#717179]">{row.original.type}</div>
      ),
    },
    {
      accessorKey: "timestamp",
      header: t("table.headers.datetime"),
      cell: ({ row }) => {
        const notification = row.original;
        const date = notification.timestamp || notification.date;
        const formattedDate = date ? formatDateTime(date) : "N/A";
        const relativeTime = date ? getRelativeTime(formattedDate) : "";

        return (
          <div className="text-xs text-gray-600">
            <div>{formattedDate}</div>
            <div className="text-xs text-gray-400 font-light">
              ({relativeTime})
            </div>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: t("table.headers.details"),
      cell: ({ row }) => {
        const notification = row.original;

        if (notification.redirectTarget) {
          return (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNotificationRedirect(notification)}
              className="text-[#010101] hover:bg-transparent"
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              {t("table.actions.view_details")}
            </Button>
          );
        } else {
          return (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onNotificationSelect?.(notification);
                onModalOpenChange?.(true, notification.id);
                markNotificationAsRead?.(notification.id);
              }}
              className="text-[#010101] hover:bg-transparent"
            >
              <Eye className="h-4 w-4 mr-1" />
              {t("table.actions.view_details")}
            </Button>
          );
        }
      },
    },
  ];
};
