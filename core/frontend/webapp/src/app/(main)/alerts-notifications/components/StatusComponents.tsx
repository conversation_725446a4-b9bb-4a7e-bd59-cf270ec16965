"use client";

import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, XCircle, Bell } from "lucide-react";
import { Alert } from "../types/alert.types";

interface StatusBadgeProps {
  status: Alert["status"];
}

export function StatusBadge({ status }: StatusBadgeProps) {
  switch (status) {
    case "Waiting treatment":
      return (
        <Badge
          variant="secondary"
          className="bg-[#FFF5D1] text-[#C59800] rounded-full"
        >
          Waiting treatment
        </Badge>
      );
    case "Approved":
      return (
        <Badge
          variant="secondary"
          className="bg-[#DEF2DB] text-[#4B923E] rounded-full"
        >
          Approved
        </Badge>
      );
    case "Rejected":
      return (
        <Badge
          variant="secondary"
          className="bg-[#F2DDDD] text-[#C95454] rounded-full"
        >
          Rejected
        </Badge>
      );
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
}

interface StatusIconProps {
  status: Alert["status"];
}

export function StatusIcon({ status }: StatusIconProps) {
  switch (status) {
    case "Waiting treatment":
      return <Clock className="h-4 w-4 text-[#E8AA00]" />;
    case "Approved":
      return <CheckCircle className="h-4 w-4 text-[#4CAF50]" />;
    case "Rejected":
      return <XCircle className="h-4 w-4 text-[#F84018]" />;
    default:
      return <Bell className="h-4 w-4" />;
  }
}
