"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface NotificationFiltersProps {
  onFilterChange: (filter: string) => void;
  activeFilter: string;
  notificationCounts: {
    all: number;
    information: number;
    warning: number;
  };
}

export function NotificationFilters({
  onFilterChange,
  activeFilter,
  notificationCounts,
}: NotificationFiltersProps) {
  const t = useTranslations("alerts_notifications");

  const filters = [
    {
      key: "all",
      label: `${t("filters.all")} (${notificationCounts.all})`,
      className: "bg-gray-100 text-gray-700",
      activeClassName: "bg-gray-700 text-white border-gray-700",
    },
    {
      key: "information",
      label: `${t("filters.information")} (${notificationCounts.information})`,
      className: "bg-blue-100 text-blue-700",
      activeClassName: "bg-blue-700 text-white border-blue-700",
    },
    {
      key: "warning",
      label: `${t("filters.warning")} (${notificationCounts.warning})`,
      className: "bg-yellow-100 text-yellow-700",
      activeClassName: "bg-yellow-700 text-white border-yellow-700",
    },
  ];

  return (
    <div className="flex space-x-2 mb-6">
      {filters.map((filter) => (
        <Button
          key={filter.key}
          variant="outline"
          size="sm"
          className={`text-xs rounded-[19px] ${
            activeFilter === filter.key
              ? filter.activeClassName
              : filter.className
          } ${
            activeFilter === filter.key
              ? "shadow-sm"
              : "border-gray-200 hover:border-gray-300"
          }`}
          onClick={() => onFilterChange(filter.key)}
        >
          {filter.label}
        </Button>
      ))}
    </div>
  );
}
