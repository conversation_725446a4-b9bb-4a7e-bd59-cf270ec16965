"use client";

import { TrackingStep } from "../types/alert.types";

interface TrackingVisualizationProps {
  tracking: TrackingStep[];
}

export function TrackingVisualization({
  tracking,
}: TrackingVisualizationProps) {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-sm text-gray-700">Tracking</h4>
      <div className="relative">
        {tracking.map((step, index) => (
          <div
            key={index}
            className="flex items-center space-x-3 pb-6 last:pb-0"
          >
            <div className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${
                  step.status === "approved"
                    ? "bg-green-500"
                    : step.status === "waiting"
                      ? "bg-yellow-500"
                      : "bg-red-500"
                }`}
              >
                {step.status === "approved"
                  ? "✓"
                  : step.status === "waiting"
                    ? "..."
                    : "✗"}
              </div>
              {index < tracking.length - 1 && (
                <div className="w-0.5 h-6 bg-gray-300 mt-2"></div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {step.status === "approved"
                    ? "Approved"
                    : step.status === "waiting"
                      ? "Waiting approval"
                      : "Rejected"}
                </span>
                {step.timestamp && (
                  <span className="text-xs text-gray-500">
                    {step.timestamp}
                  </span>
                )}
              </div>
              <div className="text-sm text-gray-600">by {step.actor}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
