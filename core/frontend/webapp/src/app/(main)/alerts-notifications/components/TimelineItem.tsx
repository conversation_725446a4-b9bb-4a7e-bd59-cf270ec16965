import { cn } from "@/lib/utils";
import { Check, X, MoreHorizontal } from "lucide-react";

interface TimelineItemProps {
  title: string;
  description: string;
  date: string;
  status: "completed" | "current" | "pending";
  isLast?: boolean;
  orientation?: "vertical" | "horizontal";
}

export function TimelineItem({
  title,
  description,
  date,
  status,
  isLast = false,
  orientation = "vertical",
}: TimelineItemProps) {
  const getStatusStyles = () => {
    switch (status) {
      case "completed":
        // Check if it's a rejection or approval based on title
        if (title.toLowerCase().includes("rejected")) {
          return {
            dot: "bg-red-500 border-white",
            line: "bg-black",
            text: "text-gray-900",
            title: "text-red-600",
            icon: "reject",
          };
        } else if (title.toLowerCase().includes("approved")) {
          return {
            dot: "bg-green-500 border-white",
            line: "bg-black",
            text: "text-gray-900",
            title: "text-green-600",
            icon: "approve",
          };
        } else {
          // Default completed (like "Request being processed")
          return {
            dot: "bg-orange-500 border-white",
            line: "bg-black",
            text: "text-gray-900",
            title: "text-orange-600",
            icon: "approve",
          };
        }
      case "current":
        return {
          dot: "bg-yellow-400 border-white animate-pulse",
          line: "bg-black",
          text: "text-gray-900",
          title: "text-yellow-600",
          icon: "pending",
        };
      case "pending":
        return {
          dot: "bg-yellow-400 border-yellow-400",
          line: "bg-black",
          text: "text-gray-700",
          title: "text-yellow-600",
          icon: "pending",
        };
    }
  };

  const getIcon = (iconType: string) => {
    switch (iconType) {
      case "approve":
        return <Check className="w-4 h-4 text-white" />;
      case "reject":
        return <X className="w-4 h-4 text-white" />;
      case "pending":
        return <MoreHorizontal className="w-4 h-4 text-white" />;
      default:
        return null;
    }
  };

  const styles = getStatusStyles();

  if (orientation === "horizontal") {
    return (
      <div className="flex items-center">
        {/* Dot with Icon */}
        <div
          className={cn(
            "w-3 h-3 rounded-full border-2 flex-shrink-0 flex items-center justify-center",
            styles.dot,
          )}
        >
          {getIcon(styles.icon)}
        </div>

        {/* Line (if not last) */}
        {!isLast && <div className={cn("flex-1 h-0.5 mx-2", styles.line)} />}
      </div>
    );
  }

  return (
    <div className="relative pl-6 pb-4">
      {/* Timeline line */}
      {!isLast && (
        <div
          className={cn("absolute left-[10px] top-3 w-1 h-full", styles.line)}
        />
      )}

      {/* Dot with Icon */}
      <div
        className={cn(
          "absolute left-0 top-1 w-6 h-6 rounded-full border-2 z-10 flex items-center justify-center",
          styles.dot,
        )}
      >
        {getIcon(styles.icon)}
      </div>

      <div className="ml-4">
        <h3 className={cn("text-xs font-medium", styles.title)}>{title}</h3>
        <p className={cn("text-xs mt-0.5", styles.text)}>{description}</p>
        <span className="text-xs text-gray-400 mt-1 block">{date}</span>
      </div>
    </div>
  );
}
