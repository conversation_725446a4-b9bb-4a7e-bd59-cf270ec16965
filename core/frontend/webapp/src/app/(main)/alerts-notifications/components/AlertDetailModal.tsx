"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Eye, MessageSquare } from "lucide-react";
import { useTranslations } from "next-intl";
import { Alert } from "../types/alert.types";
import { getRelativeTime, useAlertsStore } from "../store/alertsStore";
import { TimelineItem } from "./TimelineItem";
import { ResponseSection } from "./ResponseSection";
import { useEffect } from "react";

interface AlertDetailModalProps {
  alert: Alert;
  onAlertSelect?: (alert: Alert) => void;
  onResponseSubmit: (
    alertId: string,
    response: "Approve" | "Reject",
    comment: string,
  ) => void;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function AlertDetailModal({
  alert,
  onAlertSelect,
  onResponseSubmit,
  isOpen = false,
  onOpenChange,
}: AlertDetailModalProps) {
  const t = useTranslations("alerts_notifications");
  const {
    detailedData,
    detailedLoading,
    detailedError,
    fetchDetailedAlert,
    clearDetailedData,
    approveRequest,
    rejectRequest,
  } = useAlertsStore();

  useEffect(() => {
    // Only fetch when modal is open
    if (!isOpen) return;

    // Clear previous detailed data when modal opens with new alert
    clearDetailedData();

    // Fetch detailed data when modal opens
    if (alert.actionIdentifierId) {
      // For now using hardcoded values - these should ideally come from alert data or user context
      // const workflowId = "4aac6674-be6d-4268-94c1-43d879996748";
      // const userId = "eaf6cc26-4a82-4b77-b9f6-34bdea969921";

      // fetchDetailedAlert(workflowId, userId);
      fetchDetailedAlert();
    }
  }, [isOpen, alert.actionIdentifierId, fetchDetailedAlert, clearDetailedData]);

  const handleResponseSubmit = async (
    response: "Approve" | "Reject",
    comment: string,
  ) => {
    if (!detailedData?.requestId) return;

    try {
      if (response === "Approve") {
        console.log(detailedData.requestId);
        await approveRequest(detailedData.requestId, comment);
      } else {
        await rejectRequest(detailedData.requestId, comment);
      }
      // Also call the original callback for any additional handling
      onResponseSubmit(alert.id, response, comment);
    } catch (error) {
      console.error("Error submitting response:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onAlertSelect?.(alert)}
          className="text-[#010101] hover:bg-transparent"
        >
          <Eye className="h-4 w-4 mr-1" />
          {t("table.actions.view_details")}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="relative">
          <DialogTitle className="text-lg font-semibold">
            {t("modal.title")}
          </DialogTitle>
        </DialogHeader>

        {detailedLoading && (
          <div className="flex justify-center p-8">
            <div className="text-sm text-gray-500">
              Loading detailed information...
            </div>
          </div>
        )}

        {detailedError && (
          <div className="flex justify-center p-8">
            <div className="text-sm text-red-500">{detailedError}</div>
          </div>
        )}

        {detailedData && (
          <div className="grid grid-cols-2 gap-8 mt-6">
            {/* Left Side - Request Details */}
            <div className="space-y-6">
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  {t("modal.fields.type")}
                </Label>
                <div className="text-lg font-semibold mt-1">
                  {detailedData.requestType.replace(/-/g, " ")}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  Request Date:{" "}
                  {new Date(
                    detailedData.startNodeFormData.requestDate,
                  ).toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                  })}{" "}
                  at{" "}
                  {new Date(
                    detailedData.startNodeFormData.requestDate,
                  ).toLocaleTimeString("en-GB", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                  <span className="text-xs text-gray-400 font-light ml-2">
                    (
                    {getRelativeTime(
                      new Date(
                        detailedData.startNodeFormData.requestDate,
                      ).toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                      }) +
                        " at " +
                        new Date(
                          detailedData.startNodeFormData.requestDate,
                        ).toLocaleTimeString("en-GB", {
                          hour: "2-digit",
                          minute: "2-digit",
                        }),
                    )}
                    )
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 border-t pt-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Requester
                  </Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-blue-600">
                        {detailedData.requestedBy.fullName
                          .split(" ")
                          .map((n: string) => n[0])
                          .join("")
                          .toUpperCase()}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        {detailedData.requestedBy.fullName}
                      </span>
                      <span className="text-xs text-gray-500">
                        ID: {detailedData.requestedBy.userId}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    Status
                  </Label>
                  <div className="text-sm font-medium mt-1">
                    {detailedData.status.replace(/_/g, " ")}
                  </div>
                </div>
              </div>

              {/* Date Range */}
              <div className="grid grid-cols-2 gap-4 border-t pt-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    From Date
                  </Label>
                  <div className="text-sm font-medium mt-1">
                    {new Date(
                      detailedData.startNodeFormData.formData.From,
                    ).toLocaleDateString("en-GB", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    })}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">
                    To Date
                  </Label>
                  <div className="text-sm font-medium mt-1">
                    {new Date(
                      detailedData.startNodeFormData.formData.To,
                    ).toLocaleDateString("en-GB", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    })}
                  </div>
                </div>
              </div>

              {/* Details */}
              <div className="space-y-4 border-t pt-4">
                <Label className="flex items-center text-sm font-medium text-gray-600">
                  <MessageSquare className="w-4 h-4 mr-1 text-black fill-black stroke-black" />
                  Details
                </Label>
                <div className="bg-gray-50 p-3 rounded-md mt-1 text-sm">
                  <div>
                    <strong>Reason:</strong>{" "}
                    {detailedData.startNodeFormData.formData.reason}
                  </div>
                  <div>
                    <strong>Type:</strong>{" "}
                    {detailedData.startNodeFormData.formData.type}
                  </div>
                  <div>
                    <strong>Request ID:</strong> {detailedData.requestId}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Approval Timeline */}
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">
                  Approval Timeline
                </Label>
                <div className="mt-4">
                  {/* Initial request */}
                  <TimelineItem
                    title="Request Submitted"
                    description={`Submitted by ${detailedData.requestedBy.fullName}`}
                    date={
                      new Date(detailedData.createdAt).toLocaleDateString(
                        "en-GB",
                        {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        },
                      ) +
                      " at " +
                      new Date(detailedData.createdAt).toLocaleTimeString(
                        "en-GB",
                        { hour: "2-digit", minute: "2-digit" },
                      )
                    }
                    status="completed"
                    isLast={false}
                    orientation="vertical"
                  />

                  {/* Approval history */}
                  {detailedData.approvingHistory.map((approval, index) => (
                    <TimelineItem
                      key={approval.nodeId}
                      title={approval.title}
                      description={`${approval.decision === "WAITING_APPROVAL_BY_YOU" ? "Waiting for your approval" : approval.comments}`}
                      date={
                        approval.decidedAt
                          ? new Date(approval.decidedAt).toLocaleDateString(
                              "en-GB",
                              {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                              },
                            ) +
                            " at " +
                            new Date(approval.decidedAt).toLocaleTimeString(
                              "en-GB",
                              { hour: "2-digit", minute: "2-digit" },
                            )
                          : ""
                      }
                      status={
                        approval.decision === "WAITING_APPROVAL_BY_YOU"
                          ? "pending"
                          : "completed"
                      }
                      isLast={
                        index === detailedData.approvingHistory.length - 1
                      }
                      orientation="vertical"
                    />
                  ))}
                </div>
              </div>

              {/* Response Section */}
              {detailedData.currentApprovalNode.canUserApprove && (
                <div className="mt-6">
                  <Label className="text-sm font-medium text-gray-600">
                    Your Response
                  </Label>
                  <div className="mt-2">
                    <ResponseSection
                      onSubmit={handleResponseSubmit}
                      onCancel={() => {}}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
