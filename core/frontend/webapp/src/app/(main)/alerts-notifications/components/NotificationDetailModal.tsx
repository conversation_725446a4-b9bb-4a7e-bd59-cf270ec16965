"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Eye, MessageSquare, Bell } from "lucide-react";
import { useTranslations } from "next-intl";
import { Notification } from "../types/alert.types";

interface NotificationDetailModalProps {
  notification: Notification;
  onNotificationSelect: (notification: Notification) => void;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function NotificationDetailModal({
  notification,
  onNotificationSelect,
  isOpen = false,
  onOpenChange,
}: NotificationDetailModalProps) {
  const t = useTranslations("alerts_notifications");

  const getTypeBadge = (type: string) => {
    if (type.toLowerCase() === "information") {
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          Info
        </Badge>
      );
    } else if (type.toLowerCase() === "warning") {
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          Warning
        </Badge>
      );
    }
    return (
      <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
        {type}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onNotificationSelect(notification)}
          className="text-[#010101] hover:bg-transparent"
        >
          <Eye className="h-4 w-4 mr-1" />
          {t("table.actions.view_details")}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="relative">
          <DialogTitle className="text-sm font-semibold flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-blue-600" />
              <span>{notification.title}</span>
              {getTypeBadge(notification.type)}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Content Section */}
          <div className="space-y-4">
            <Label className="flex items-center text-sm font-medium text-gray-600">
              <MessageSquare className="w-4 h-4 mr-1 text-black fill-black stroke-black" />
              Content
            </Label>
            <div className="bg-gray-50 p-4 rounded-md mt-1 text-sm">
              <div className="space-y-2">
                <p>{notification.content}</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
