"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface AlertFiltersProps {
  onFilterChange: (filter: string) => void;
  activeFilter: string;
  alertCounts: {
    all: number;
    approval: number;
    redirect: number;
  };
}

export function AlertFilters({
  onFilterChange,
  activeFilter,
  alertCounts,
}: AlertFiltersProps) {
  const t = useTranslations("alerts_notifications");

  const filters = [
    {
      key: "all",
      label: `${t("filters.all")} (${alertCounts.all})`,
      className: "bg-gray-100 text-gray-700",
      activeClassName: "bg-gray-700 text-white border-gray-700",
    },
    {
      key: "APPROVAL",
      label: `${t("filters.approval")} (${alertCounts.approval})`,
      className: "bg-[#FFF5D1] text-[#C59800]",
      activeClassName: "bg-[#C59800] text-white border-[#C59800]",
    },
    {
      key: "REDIRECT",
      label: `${t("filters.redirect")} (${alertCounts.redirect})`,
      className: "bg-[#E3F2FD] text-[#1976D2]",
      activeClassName: "bg-[#1976D2] text-white border-[#1976D2]",
    },
  ];

  return (
    <div className="flex space-x-2 mb-6">
      {filters.map((filter) => (
        <Button
          key={filter.key}
          variant="outline"
          size="sm"
          className={`text-xs rounded-[19px] ${
            activeFilter === filter.key
              ? filter.activeClassName
              : filter.className
          } ${
            activeFilter === filter.key
              ? "shadow-sm"
              : "border-gray-200 hover:border-gray-300"
          }`}
          onClick={() => onFilterChange(filter.key)}
        >
          {filter.label}
        </Button>
      ))}
    </div>
  );
}
