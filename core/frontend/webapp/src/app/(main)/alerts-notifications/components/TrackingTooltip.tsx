"use client";

import CustomIcon from "@/components/common/CustomIcons";
import { useTranslations } from "next-intl";
import { Alert } from "../types/alert.types";
import { TimelineItem } from "./TimelineItem";

interface TrackingTooltipProps {
  alert: Alert;
}

export function TrackingTooltip({ alert }: TrackingTooltipProps) {
  const t = useTranslations("alerts_notifications");

  const getTimelineSteps = () => {
    const steps: Array<{
      title: string;
      description: string;
      date: string;
      status: "completed" | "current" | "pending";
    }> = [
      {
        title: t("tracking.steps.request_processing"),
        description: t("tracking.submitted_by", { name: alert.requester }),
        date: alert.requestDate || alert.timestamp || alert.createdAt || "",
        status: "completed",
      },
    ];

    if (alert.status === "Approved") {
      steps.push({
        title: t("tracking.steps.request_approved"),
        description: t("tracking.approved_by", {
          role: t("tracking.actors.shift_leader"),
        }),
        date: alert.requestDate || alert.timestamp || alert.createdAt || "",
        status: "completed",
      });
    } else if (alert.status === "Rejected") {
      steps.push({
        title: t("tracking.steps.request_rejected"),
        description: t("tracking.rejected_by", {
          role: t("tracking.actors.shift_leader"),
        }),
        date: alert.requestDate || alert.timestamp || alert.createdAt || "",
        status: "completed",
      });
    } else {
      steps.push({
        title: t("tracking.steps.waiting_approval"),
        description: t("tracking.pending_approval", {
          name: `${t("tracking.actors.you")} (Ali Johari)`,
        }),
        date: "",
        status: "pending",
      });
    }

    return steps;
  };

  const steps = getTimelineSteps();

  // Get the current step (the last one that's not pending)
  const getCurrentStep = () => {
    const currentStep =
      steps.find((step) => step.status === "current") ||
      steps.filter((step) => step.status === "completed").pop() ||
      steps[0];
    return currentStep;
  };

  const currentStep = getCurrentStep();

  return (
    <div className="relative group inline-block">
      {/* Info Icon */}
      <div className="inline-flex items-start gap-1">
        <div className="w-6 h-6 flex-shrink-0">
          <CustomIcon
            name="info"
            className="text-gray-500 hover:text-gray-700 cursor-pointer"
          />
        </div>
        <div className="text-xs">{currentStep.title}</div>
      </div>

      {/* Tooltip Content */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none group-hover:pointer-events-auto z-50">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-4">
            {t("tracking.title")}
          </h3>

          {/* Vertical Timeline */}
          <div>
            {steps.map((step, index) => (
              <TimelineItem
                key={index}
                title={step.title}
                description={step.description}
                date={step.date}
                status={step.status}
                isLast={index === steps.length - 1}
                orientation="vertical"
              />
            ))}
          </div>
        </div>

        {/* Tooltip Arrow */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"></div>
      </div>
    </div>
  );
}
