"use client";

import { useState, useEffect, useRef } from "react";
import { Alert } from "../types/alert.types";
import { AlertDetailModal } from "./AlertDetailModal";
import { DataTable } from "@/components/common/tables/DataTable";
import { createAlertsColumns } from "./alertsColumns";
import { SortingState } from "@tanstack/react-table";

interface AlertsTableProps {
  alerts: Alert[];
  onAlertSelect?: (alert: Alert) => void;
  onResponseSubmit: (
    alertId: string,
    response: "Approve" | "Reject",
    comment: string,
  ) => void;
  autoOpenAlertId?: string | null;
  onAutoOpened?: () => void;
  loading?: boolean;
  markAlertAsRead?: (alertId: string) => Promise<void>;
  // Server pagination props
  serverPagination?: boolean;
  onPaginationChange?: (pageIndex: number, pageSize: number) => void;
  totalItems?: number;
  serverSearch?: boolean;
  onSearchChange?: (searchQuery: string) => void;
  sorting?: SortingState;
  onSortingChange?: (sorting: SortingState) => void;
  currentPage?: number;
  pageSize?: number;
  searchValue?: string;
}

export function AlertsTable({
  alerts,
  onAlertSelect,
  onResponseSubmit,
  autoOpenAlertId,
  onAutoOpened,
  loading = false,
  markAlertAsRead,
  // Server pagination props
  serverPagination = false,
  onPaginationChange,
  totalItems,
  serverSearch = false,
  onSearchChange,
  sorting,
  onSortingChange,
  currentPage = 0,
  pageSize = 5,
  searchValue = "",
}: AlertsTableProps) {
  const [openModalAlertId, setOpenModalAlertId] = useState<string | null>(null);
  const hasAutoOpened = useRef(false);

  // Auto-open modal when autoOpenAlertId is provided and matches an alert
  // But only do this once, not every time the component re-renders
  useEffect(() => {
    if (
      autoOpenAlertId &&
      !hasAutoOpened.current &&
      alerts.some((alert) => alert.id === autoOpenAlertId)
    ) {
      setOpenModalAlertId(autoOpenAlertId);
      hasAutoOpened.current = true;
      onAutoOpened?.();
      // Mark alert as read when dialog opens
      markAlertAsRead?.(autoOpenAlertId);
    }
  }, [autoOpenAlertId, alerts, onAutoOpened, markAlertAsRead]);

  const handleModalOpenChange = (open: boolean, alertId: string) => {
    if (open) {
      setOpenModalAlertId(alertId);
      // Mark alert as read when dialog opens
      markAlertAsRead?.(alertId);
    } else {
      setOpenModalAlertId(null);
    }
  };

  // Create columns configuration
  const columns = createAlertsColumns({
    onAlertSelect,
    onResponseSubmit,
    markAlertAsRead,
    openModalAlertId,
    onModalOpenChange: handleModalOpenChange,
  });

  return (
    <div className="space-y-4">
      {/* Alerts DataTable */}
      <DataTable
        columns={columns}
        data={alerts}
        isLoading={loading}
        id="alerts-table"
        // Server pagination props
        serverPagination={serverPagination}
        onPaginationChange={onPaginationChange}
        totalItems={totalItems}
        serverSearch={serverSearch}
        onSearchChange={onSearchChange}
        sorting={sorting}
        onSortingChange={onSortingChange}
        currentPage={currentPage}
        pageSize={pageSize}
        searchValue={searchValue}
      />

      {/* Alert Detail Modal */}
      {openModalAlertId && (
        <AlertDetailModal
          alert={alerts.find((alert) => alert.id === openModalAlertId)!}
          onAlertSelect={onAlertSelect}
          onResponseSubmit={onResponseSubmit}
          isOpen={!!openModalAlertId}
          onOpenChange={(open) => handleModalOpenChange(open, openModalAlertId)}
        />
      )}
    </div>
  );
}
