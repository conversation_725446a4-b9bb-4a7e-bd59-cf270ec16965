import { Alert } from "../types/alert.types";

interface HorizontalTimelineProps {
  alert: Alert;
}

export function HorizontalTimeline({ alert }: HorizontalTimelineProps) {
  const getTimelineData = () => {
    let baseStep, secondStep;

    if (alert.status === "Approved") {
      baseStep = {
        label: "Request approved",
        status: "completed" as const,
        color: "green",
      };
      secondStep = {
        label: "Request approved",
        status: "completed" as const,
        color: "green",
      };
    } else if (alert.status === "Rejected") {
      baseStep = {
        label: "Request rejected",
        status: "completed" as const,
        color: "red",
      };
      secondStep = {
        label: "Request rejected",
        status: "completed" as const,
        color: "red",
      };
    } else {
      // Waiting treatment - only first step is active
      baseStep = {
        label: "Request being processed",
        status: "completed" as const,
        color: "orange",
      };
      secondStep = {
        label: "Request being processed",
        status: "completed" as const,
        color: "orange",
      };
    }

    return { baseStep, secondStep };
  };

  const getStepColors = (colorName: string) => {
    switch (colorName) {
      case "green":
        return {
          dot: "bg-green-500",
          line: "bg-green-300",
        };
      case "red":
        return {
          dot: "bg-red-500",
          line: "bg-red-300",
        };
      case "orange":
        return {
          dot: "bg-orange-500",
          line: "bg-orange-300",
        };
      case "gray":
      default:
        return {
          dot: "bg-gray-400",
          line: "bg-gray-300",
        };
    }
  };

  const { baseStep, secondStep } = getTimelineData();
  const baseColors = getStepColors(baseStep.color);
  const secondColors = getStepColors(secondStep.color);
  const isCompleted = secondStep.status === "completed";

  return (
    <div className="flex items-center w-32 relative">
      {/* First Point - Always orange for "Request being processed" */}
      <div className={`w-3 h-3 rounded-full ${baseColors.dot} z-10`}></div>

      {/* Connecting Line */}
      <div className="flex-1 h-0.5 relative">
        <div className="w-full h-full bg-gray-200"></div>
        {isCompleted && (
          <div
            className={`absolute top-0 left-0 w-full h-full ${secondColors.line}`}
          ></div>
        )}
      </div>

      {/* Second Point */}
      <div
        className={`w-3 h-3 rounded-full z-10 ${
          isCompleted
            ? secondColors.dot
            : "bg-gray-300 border-2 border-gray-300"
        } ${!isCompleted ? "animate-pulse" : ""}`}
      ></div>
    </div>
  );
}
