"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { MessageSquare } from "lucide-react";
import { useTranslations } from "next-intl";

interface ResponseSectionProps {
  onSubmit: (response: "Approve" | "Reject", comment: string) => void;
  onCancel: () => void;
}

export function ResponseSection({ onSubmit, onCancel }: ResponseSectionProps) {
  const t = useTranslations("alerts_notifications");
  const [response, setResponse] = useState<"Approve" | "Reject">("Approve");
  const [comment, setComment] = useState("");

  const handleSubmit = () => {
    onSubmit(response, comment);
    setResponse("Approve");
    setComment("");
  };

  return (
    <div className="space-y-4 border-t pt-4">
      <h4 className="font-medium">{t("modal.response.title")}*</h4>
      <RadioGroup
        value={response}
        onValueChange={(value: "Approve" | "Reject") => setResponse(value)}
        className="flex items-center space-x-4"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem
            value="Approve"
            id="approve"
            className="border-gray-400 text-black data-[state=checked]:bg-black data-[state=checked]:border-black"
          />
          <Label
            htmlFor="approve"
            className={
              response === "Approve"
                ? "text-green-600 font-semibold"
                : "text-gray-400"
            }
          >
            {t("modal.response.approve")}
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem
            value="Reject"
            id="reject"
            className="border-gray-400 text-black data-[state=checked]:bg-black data-[state=checked]:border-black"
          />
          <Label
            htmlFor="reject"
            className={
              response === "Reject"
                ? "text-red-600 font-semibold"
                : "text-gray-400"
            }
          >
            {t("modal.response.reject")}
          </Label>
        </div>
      </RadioGroup>

      <div>
        <Label
          htmlFor="comment"
          className="flex items-center text-sm font-medium text-gray-600"
        >
          <MessageSquare className="w-4 h-4 mr-1 text-black fill-black stroke-black" />{" "}
          Comment:
        </Label>
        <Textarea
          id="comment"
          placeholder={t("modal.response.comment_placeholder")}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          className="mt-1"
          rows={3}
        />
        <div className="text-right text-xs text-gray-500 mt-1">
          {comment.length}/500
        </div>
      </div>

      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>{t("modal.response.submit")}</Button>
      </div>
    </div>
  );
}
