import { create } from "zustand";
import api from "@/lib/axios";
import { fetchPdfAsBlob } from "../utils/pdfUtils";
import { toast } from "@/hooks/use-toast";

// Helper function to extract error message from API response
const getErrorMessage = (error: unknown, fallbackMessage: string): string => {
  if (error && typeof error === "object" && "response" in error) {
    const axiosError = error as {
      response?: {
        data?: {
          message?: string;
          error?: string;
        };
        statusText?: string;
      };
      message?: string;
    };
    // Try to get message from response data
    if (axiosError.response?.data?.message) {
      return axiosError.response.data.message;
    } else if (axiosError.response?.data?.error) {
      return axiosError.response.data.error;
    } else if (axiosError.response?.statusText) {
      return axiosError.response.statusText;
    } else if (axiosError.message) {
      return axiosError.message;
    }
  } else if (error instanceof Error) {
    return error.message;
  }
  return fallbackMessage;
};

interface Workflow {
  id: string;
  name: string;
  description: string;
  formID: string;
}

interface WorkflowType {
  requesttype: string;
  workflows: Workflow[];
}

interface FormField {
  name: string;
  label: string;
  placeholder: string;
  required: boolean;
  variant: string;
  // ...add other field properties as needed
}

interface DynamicForm {
  fields: FormField[];
  // ...add other form properties as needed
}

interface LatestRequest {
  id: string;
  generatedfile: string;
  requestType: string;
  requesterId: string;
  targetOperatorId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  pdfObjectUrl?: string; // Optional object URL for CORS-protected PDFs
}

// Response type for the create request API
interface CreateRequestResponse {
  id: string;
  requestType: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  // Add any other fields returned by the API
}

// Submit request payload type
interface SubmitRequestPayload {
  requesterId: string;
  targetOperatorIds: string[];
  formData: Record<string, string | number | boolean | Date>;
  workflowInstanceId: string;
}

interface RequestStoreState {
  workflowTypes: WorkflowType[];
  loading: boolean;
  formLoading: boolean;
  loadingFormId: string | null;
  error: string | null;
  selectedForm: DynamicForm | null;
  fetchWorkflowTypes: () => Promise<void>;
  fetchFormByFormID: (formID: string) => Promise<void>;
  latestRequests: LatestRequest[];
  requestsLoaded: boolean; // Track if requests have been loaded
  currentRequesterId: string | null; // Track current requester ID
  fetchLatestRequests: (
    requesterId: string,
    forceRefresh?: boolean,
  ) => Promise<void>;
  fetchPdfForRequest: (requestId: string) => Promise<Blob | null>;
  submitRequest: (payload: SubmitRequestPayload) => Promise<{
    success: boolean;
    message: string;
    data?: CreateRequestResponse;
  }>;
}

export const useRequestStore = create<RequestStoreState>((set, get) => ({
  workflowTypes: [],
  loading: false,
  formLoading: false,
  loadingFormId: null,
  error: null,
  selectedForm: null,
  latestRequests: [],
  requestsLoaded: false,
  currentRequesterId: null,
  fetchWorkflowTypes: async () => {
    set({ loading: true, error: null });
    try {
      const res = await api.get("/request/requests/workflows/types");
      set({ workflowTypes: res.data, loading: false });
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(
        error,
        "Failed to fetch workflow types",
      );
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      set({
        loading: false,
      });
    }
  },

  fetchFormByFormID: async (formID: string) => {
    set({ formLoading: true, loadingFormId: formID, error: null });
    try {
      const res = await api.get(`/request/requests/workflows/${formID}/form`);
      set({ selectedForm: res.data, formLoading: false, loadingFormId: null });
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, "Failed to fetch form");
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      set({ formLoading: false, loadingFormId: null });
    }
  },

  fetchLatestRequests: async (requesterId: string, forceRefresh = false) => {
    // Check if we already have data for this requester and forceRefresh is not enabled
    const { requestsLoaded, currentRequesterId } = get();
    if (requestsLoaded && currentRequesterId === requesterId && !forceRefresh) {
      // Data already loaded for this requester, no need to fetch again
      return;
    }

    set({ loading: true, error: null });
    try {
      const res = await api.get(
        `/request/requests/requesters/${requesterId}/latest`,
      );
      // Use the actual data from the API response
      set({
        latestRequests: res.data,
        loading: false,
        requestsLoaded: true,
        currentRequesterId: requesterId,
      });
    } catch (error: unknown) {
      console.error("Error fetching latest requests:", error);
      const errorMessage = getErrorMessage(
        error,
        "Failed to fetch latest requests",
      );
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      set({
        latestRequests: [], // Return empty array instead of fake data
        loading: false,
        requestsLoaded: false,
        currentRequesterId: null,
      });
    }
  },

  // Fetch PDF for a specific request to handle CORS issues
  fetchPdfForRequest: async (requestId: string) => {
    const { latestRequests } = get();
    const request = latestRequests.find((req) => req.id === requestId);

    if (!request?.generatedfile) {
      return null;
    }

    try {
      const pdfBlob = await fetchPdfAsBlob(request.generatedfile);
      const objectUrl = URL.createObjectURL(pdfBlob);

      // Update the request with the object URL
      set({
        latestRequests: latestRequests.map((req) =>
          req.id === requestId ? { ...req, pdfObjectUrl: objectUrl } : req,
        ),
      });

      return pdfBlob;
    } catch (error: unknown) {
      console.error("Failed to fetch PDF:", error);
      toast({
        title: "Error",
        description: "Failed to load document preview",
        variant: "destructive",
      });
      return null;
    }
  },

  // Submit a new request to the API
  submitRequest: async (payload: SubmitRequestPayload) => {
    set({ loading: true, error: null });

    try {
      // Call the API to create the request
      const response = await api.post<CreateRequestResponse>(
        "/request/requests",
        payload,
      );

      console.log("Request created successfully:", response.data);

      // Show success toast
      toast({
        title: "Success",
        description: "Your request has been submitted successfully!",
        variant: "default",
      });

      // Refresh the latest requests after successful submission
      if (payload.requesterId) {
        await get().fetchLatestRequests(payload.requesterId, true); // Force refresh
      }

      set({ loading: false });

      // Return success response
      return {
        success: true,
        message: "Your request has been submitted successfully!",
        data: response.data,
      };
    } catch (error: unknown) {
      console.error("Error creating request:", error);
      const errorMessage = getErrorMessage(error, "Failed to submit request");

      // Show error toast
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      set({
        loading: false,
      });

      // Return error response
      return {
        success: false,
        message: errorMessage,
      };
    }
  },
}));
