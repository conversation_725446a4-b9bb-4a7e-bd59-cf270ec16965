import React, { useEffect, useRef, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Check, FileText, Home, Info } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import CustomSelect from "@/components/common/CustomSelect";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { FIELD_RENDERERS } from "../store/fieldRenderers";
import { format } from "date-fns";
import { useRequestStore } from "../store/requestStore";
import CustomIcon from "@/components/common/CustomIcons";
import useMockAuthStore from "@/store/mockAuthStore";
import { FieldData } from "../types/profileTypes";

const DocumentRequest = () => {
  const t = useTranslations();
  const { toast } = useToast();
  const { currentUser } = useMockAuthStore();
  const [selectedDocument, setSelectedDocument] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [formValue, setFormValue] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const dynamicFormRef = useRef<{ getFormValues: () => any } | null>(null); // eslint-disable-line @typescript-eslint/no-explicit-any

  // Fetch workflow types from API
  const {
    workflowTypes,
    fetchWorkflowTypes,
    fetchFormByFormID,
    selectedForm,
    loadingFormId,
    submitRequest,
  } = useRequestStore();

  useEffect(() => {
    fetchWorkflowTypes();
  }, [fetchWorkflowTypes]);

  // Get administrative_document workflows from API
  const documentWorkflows =
    workflowTypes.find(
      (t) => t.requesttype === "administrative-documents-requests",
    )?.workflows || [];

  const documentOptions = documentWorkflows.map((wf) => ({
    label: wf.name,
    value: wf.id,
  }));

  const handleDocumentSelect = (value: string) => {
    setSelectedDocument(value);

    // Find selected workflow and fetch its form if it has formID
    const workflow = documentWorkflows.find((wf) => wf.id === value);
    if (workflow) {
      setSelectedWorkflow(workflow);
      // إذا كان عندو formID، نجيب الـ form
      if (workflow.formID) {
        fetchFormByFormID(workflow.formID);
      }
    }
  };

  const handleSubmitForm = () => {
    if (dynamicFormRef.current) {
      const formData = dynamicFormRef.current.getFormValues();
      setFormValue(formData);
      setDialogOpen(false);
      setConfirmDialogOpen(true);
    }
  };

  // Helper to map API field type to FIELD_RENDERERS variant
  const mapFieldTypeToVariant = (type: string) => {
    switch (type) {
      case "text":
        return "Input";
      case "select":
        return "Select";
      case "checkbox":
        return "Checkbox";
      case "date":
        return "Date";
      default:
        return "Input";
    }
  };

  // DynamicForm component for forms with formID
  const DynamicForm = React.forwardRef<
    { getFormValues: () => any }, // eslint-disable-line @typescript-eslint/no-explicit-any
    { fields: (FieldData & { label: string })[] }
  >(({ fields }, ref) => {
    const [localValue, setLocalValue] = useState<Record<string, any>>({}); // eslint-disable-line @typescript-eslint/no-explicit-any
    const [fieldLabels, setFieldLabels] = useState<Record<string, string>>({});

    // Store field labels when component mounts
    useEffect(() => {
      const labels: Record<string, string> = {};
      fields.forEach((field) => {
        labels[field.name] = field.label;
      });
      setFieldLabels(labels);
    }, [fields]);

    const handleChange = (
      fieldName: string,
      newValue: string | Date | number | boolean,
    ) => {
      setLocalValue((prev) => ({
        ...prev,
        [fieldName]: newValue,
      }));
    };

    React.useImperativeHandle(ref, () => ({
      getFormValues: () => ({
        values: localValue || {},
        labels: fieldLabels,
      }),
    }));

    return (
      <form className="space-y-4">
        {fields.map((field) => {
          const Renderer = FIELD_RENDERERS[field.variant];
          if (!Renderer) return null;
          return (
            <div key={field.name} className="space-y-2">
              <Label>{field.label}</Label>
              <Renderer
                fieldData={field}
                value={localValue[field.name] || ""}
                onChange={(newValue: string | Date | number | boolean) =>
                  handleChange(field.name, newValue)
                }
              />
            </div>
          );
        })}
      </form>
    );
  });

  DynamicForm.displayName = "DynamicForm";

  // Prepare fields for DynamicForm
  const dynamicFormFields: (FieldData & { label: string })[] =
    selectedForm?.fields
      ? selectedForm.fields.map((field) => {
          const apiField = field as any; // eslint-disable-line @typescript-eslint/no-explicit-any
          return {
            name: apiField.name,
            label: apiField.label,
            placeholder: apiField.placeholder || apiField.label,
            variant: mapFieldTypeToVariant(apiField.type),
            options: apiField.options,
            minDate: apiField.minDate,
            maxDate: apiField.maxDate,
          };
        })
      : [];

  const handleSubmit = async () => {
    if (!selectedDocument) return;

    setIsSubmitting(true);

    // Find selected workflow
    const workflow = documentWorkflows.find((wf) => wf.id === selectedDocument);
    if (!workflow) {
      toast({
        title: t("DocumentRequest.error"),
        description: t("DocumentRequest.documentNotFound"),
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Create request payload
    const requestPayload = {
      requesterId: currentUser?.id || "fab9d951-9eae-4949-bd8f-ce500dfd3c95", // Use current user's UUID or fallback
      targetOperatorIds: ["123e4567-e89b-12d3-a456-************"], // Should be actual IDs
      formData: formValue?.values || {}, // Use form data if available, otherwise empty object
      workflowInstanceId: workflow.id,
    };

    try {
      // Use submitRequest from the store
      const result = await submitRequest(requestPayload);

      if (result.success) {
        setConfirmDialogOpen(false);
        toast({
          title: t("DocumentRequest.success"),
          description: t("Common.successMessage"),
          variant: "default",
        });
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : t("Common.errorMessage");
      toast({
        title: t("DocumentRequest.error"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card className="w-full overflow-hidden shadow-lg">
        <CardContent className="p-0">
          <div
            style={{
              background: "linear-gradient(295deg, #E7F0FD 0%, #FFFFFF 100%)",
            }}
          >
            <div className="flex pl-4 pt-4">
              <h2 className="text-xl font-bold mb-4">
                {t("DocumentRequest.title")}
              </h2>
              <Info className="w-5 h-5 text-gray-400 ml-2 mt-1" />
            </div>
            <div
              style={{
                backgroundImage: `url(/image/documentrequest.png)`,
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
              }}
            >
              <div className="flex items-center gap-4  pl-4">
                <Image
                  height="50"
                  width="50"
                  alt={t("DocumentRequest.title")}
                  src="image/document.svg"
                />
              </div>
              <div className="flex flex-col sm:flex-row gap-3 p-4 pb-1 items-center">
                <CustomSelect
                  options={documentOptions}
                  onValueChange={handleDocumentSelect}
                  defaultValue={selectedDocument}
                  placeholder={t("DocumentRequest.selectDocument")}
                  label={t("DocumentRequest.selectedRequest")}
                  className="w-full mb-4"
                  id="document-select"
                  isLoading={selectedDocument === loadingFormId}
                />
                <Button
                  className="min-w-[200px] mt-2"
                  onClick={() => {
                    // إذا كان عندو form، نعرض الـ dialog للـ form
                    // إذا ما عندوش form، نعرض الـ confirmation مباشرة
                    if (selectedWorkflow?.formID && selectedForm) {
                      setDialogOpen(true);
                    } else {
                      setConfirmDialogOpen(true);
                    }
                  }}
                  disabled={!selectedDocument}
                >
                  <CustomIcon
                    name="submitIcon"
                    className="h-4 w-4"
                    style={{ width: "auto", height: "auto" }}
                  />
                  <span>{t("DocumentRequest.submitRequest")}</span>
                </Button>

                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>
                        {selectedWorkflow?.name ||
                          t("DocumentRequest.requestSummary")}
                      </DialogTitle>
                    </DialogHeader>

                    {/* إذا كان عندنا form، نعرضوه */}
                    {selectedForm && dynamicFormFields.length > 0 ? (
                      <>
                        <DynamicForm
                          fields={dynamicFormFields}
                          ref={dynamicFormRef}
                        />
                        <div className="flex justify-between mt-4">
                          <Button
                            variant="outline"
                            onClick={() => setDialogOpen(false)}
                          >
                            {t("DocumentRequest.cancel")}
                          </Button>
                          <Button
                            className="bg-black hover:bg-black/90 text-white"
                            onClick={handleSubmitForm}
                          >
                            <ArrowRight className="h-4 w-4" />
                            <span>{t("DocumentRequest.submitRequest")}</span>
                          </Button>
                        </div>
                      </>
                    ) : (
                      /* إذا ما عندناش form، نعرض الـ confirmation البسيط */
                      <>
                        <div className="flex items-center space-x-2 py-4">
                          <div className="text-center w-full">
                            <Image
                              alt=""
                              className="mx-auto pb-4"
                              height="72"
                              width="82"
                              src="/image/icons8_information.svg"
                            />
                            <p className="text-sm font-medium text-[#010101]">
                              {t("DocumentRequest.confirmationMessage")}
                            </p>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <div>
                            <Button
                              type="button"
                              variant="secondary"
                              onClick={() => setDialogOpen(false)}
                            >
                              {t("DocumentRequest.cancel")}
                            </Button>
                          </div>
                          <div>
                            <Button
                              onClick={handleSubmit}
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? (
                                <span className="flex items-center">
                                  <span className="animate-spin h-4 w-4 mr-2 border-2 border-b-0 border-white rounded-full"></span>
                                  {t("DocumentRequest.submitting")}
                                </span>
                              ) : (
                                <>
                                  <ArrowRight className="h-4 w-4" />
                                  <span>{t("DocumentRequest.submit")}</span>
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                  </DialogContent>
                </Dialog>

                <Dialog
                  open={confirmDialogOpen}
                  onOpenChange={setConfirmDialogOpen}
                >
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>
                        {t("DocumentRequest.requestSummary")}
                      </DialogTitle>
                    </DialogHeader>

                    {/* إذا كان عندنا form data، نعرضوه */}
                    {formValue &&
                    formValue.values &&
                    Object.keys(formValue.values).length > 0 ? (
                      <div className="py-4 space-y-4">
                        <div className="text-center border-b pb-4">
                          <h3 className="text-blue-600 font-medium text-lg mb-2">
                            {t("DocumentRequest.confirmRequest")}
                          </h3>
                          <p className="text-gray-500 text-sm">
                            {t("DocumentRequest.reviewDetails")}
                          </p>
                        </div>
                        <div className="border-b pb-4">
                          <div className="mb-2">
                            {Object.entries(formValue.values).map(
                              ([key, value]) => (
                                <div key={key}>
                                  <p className="text-sm text-gray-500">
                                    {formValue.labels?.[key] || key}:
                                  </p>
                                  <p className="font-medium">
                                    {value instanceof Date
                                      ? format(value, "dd/MM/yyyy")
                                      : value?.toString()}
                                  </p>
                                </div>
                              ),
                            )}
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setConfirmDialogOpen(false);
                              setDialogOpen(true);
                            }}
                          >
                            {t("DocumentRequest.back")}
                          </Button>
                          <Button
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <span className="flex items-center">
                                <span className="animate-spin h-4 w-4 mr-2 border-2 border-b-0 border-white rounded-full"></span>
                                {t("DocumentRequest.submitting")}
                              </span>
                            ) : (
                              <>
                                <ArrowRight className="h-4 w-4" />
                                <span>{t("DocumentRequest.submit")}</span>
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      /* الـ modal البسيط إذا ما عندناش form */
                      <>
                        <div className="flex items-center space-x-2 py-4">
                          <div className="text-center w-full">
                            <div className="inline-block">
                              <Check className="bg-[#C8E6C9] w-20 h-20 rounded-full text-[#4CAF50]" />
                            </div>
                            <p className="text-sm font-medium text-[#010101]">
                              {t("DocumentRequest.successMessage")}
                            </p>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <div>
                            <Button
                              type="button"
                              variant="secondary"
                              onClick={() => setConfirmDialogOpen(false)}
                            >
                              <Home className="h-4 w-4" />
                              <span>{t("DocumentRequest.home")}</span>
                            </Button>
                          </div>
                          <div>
                            <Button
                              onClick={() => {
                                setConfirmDialogOpen(false);
                              }}
                            >
                              <FileText className="h-4 w-4" />
                              <span>
                                {t("DocumentRequest.trackingRequests")}
                              </span>
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default DocumentRequest;
