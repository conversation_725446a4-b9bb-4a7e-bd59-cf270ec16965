import { FIELD_RENDERERS } from "../../store/fieldRenderers";

describe("fieldRenderers - Basic", () => {
  it("should export FIELD_RENDERERS as an object", () => {
    expect(typeof FIELD_RENDERERS).toBe("object");
  });
  it("should contain Input, Select, Checkbox, Date keys", () => {
    ["Input", "Select", "Checkbox", "Date"].forEach((key) => {
      expect(FIELD_RENDERERS).toHaveProperty(key);
    });
  });
  it("each renderer should be a function", () => {
    Object.values(FIELD_RENDERERS).forEach((val) => {
      expect(typeof val).toBe("function");
    });
  });
});
