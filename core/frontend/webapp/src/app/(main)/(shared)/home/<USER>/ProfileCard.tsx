import { User, Phone, Mail, Building, UserCog } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useEffect } from "react";
import { useTranslations } from "next-intl";
import { useProfileStore } from "@/app/(main)/(shared)/home/<USER>/profileStore";
import { ProfileData } from "../types/profileTypes";
import Image from "next/image";
import useMockAuthStore from "@/store/mockAuthStore";

type ProfileCardProps = Partial<ProfileData>;

const ProfileCard = (props: ProfileCardProps = {}) => {
  const t = useTranslations();
  const { profile, loading, error, fetchUserProfile } = useProfileStore();
  const { currentUser } = useMockAuthStore();

  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  const {
    name = currentUser?.name || profile?.name,
    role = currentUser?.role || profile?.role,
    id = currentUser?.legacyId || profile?.id,
    plant = profile?.plant,
    manager = profile?.manager,
    phone = profile?.phone,
    email = profile?.email,
    imageUrl = profile?.imageUrl || "",
  } = { ...profile, ...props };
  if (loading) {
    return (
      <Card className="w-full overflow-hidden shadow-lg">
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-full bg-gray-200"></div>
              <div className="flex-1">
                <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    console.error("Profile error:", error);
  }

  return (
    <Card className="w-full overflow-hidden shadow-lg">
      <CardContent className="p-0">
        <div
          className="p-4"
          style={{
            background: "linear-gradient(180deg, #E6F1FF 0%, #FFFFFF 100%)",
          }}
        >
          <h2 className="text-xl font-bold mb-4">
            {t("ProfileCard.myprofile")}
          </h2>
          <div className="flex items-center gap-4">
            <div className="relative w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
              {imageUrl ? (
                <Image
                  src={imageUrl || "/placeholder.svg"}
                  alt={`${name}'s profile`}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              ) : (
                <User className="w-8 h-8 text-[#010101]" />
              )}
            </div>
            <div>
              <h3 className="font-bold text-lg">{name}</h3>
              <p className="text-gray-500 text-sm">{role}</p>
              {id && (
                <p className="text-gray-500 text-sm">
                  {t("ProfileCard.employeeNumber")}: {id}
                </p>
              )}
            </div>
          </div>
        </div>
        <Separator />
        <div className="p-4">
          <div className="grid grid-cols-2 gap-4">
            {plant && (
              <div className="flex items-center gap-2">
                <Building size={18} className="text-[#010101]" />
                <div className="flex items-center gap-1">
                  <p className="text-xs text-gray-500">
                    {t("ProfileCard.location")}:
                  </p>
                  <p className="text-sm">{plant}</p>
                </div>
              </div>
            )}

            {phone && (
              <div className="flex items-center gap-2">
                <Phone size={18} className="text-[#010101]" />
                <div className="flex items-center gap-1">
                  <p className="text-xs text-gray-500">
                    {t("ProfileCard.phone")}:
                  </p>
                  <p className="text-sm">{phone}</p>
                </div>
              </div>
            )}

            {manager && (
              <div className="flex items-center gap-2">
                <UserCog size={18} className="text-[#010101]" />
                <div className="flex items-center gap-1">
                  <p className="text-xs text-gray-500">
                    {t("ProfileCard.manager")}:
                  </p>
                  <p className="text-sm">{manager}</p>
                </div>
              </div>
            )}

            {email && (
              <div className="flex items-center gap-2">
                <Mail size={18} className="text-[#010101]" />
                <div className="flex items-center gap-1">
                  <p className="text-xs text-gray-500">
                    {t("ProfileCard.email")}:
                  </p>
                  <p className="text-sm break-words">{email}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCard;
