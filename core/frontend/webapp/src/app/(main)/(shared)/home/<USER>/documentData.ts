export const documentData = [
  {
    name: "Absence authorizations",
    fields: [
      {
        checked: true,
        description: "This is your public display name.",
        disabled: false,
        label: "Absence authorizations",
        name: "name_9413444689",
        placeholder: "Username",
        required: true,
        rowIndex: 0,
        type: "",
        value: "",
        variant: "Input",
      },
      {
        checked: true,
        description: "You can manage email addresses in your email settings.",
        disabled: false,
        label: "optins",
        name: "name_3221354sss589",
        placeholder: "Travel Order",
        required: true,
        rowIndex: 0,
        type: "",
        value: "",
        variant: "Select",
      },
    ],
  },
  {
    name: "Absence requests",
    fields: [
      {
        checked: true,
        description: "This is your public display name.",
        disabled: false,
        label: "Absence requests",
        name: "name_9413444689",
        placeholder: "Username",
        required: true,
        rowIndex: 0,
        type: "",
        value: "",
        variant: "Input",
      },
      {
        checked: true,
        description:
          "You can manage your mobile notifications in the mobile settings page.",
        disabled: false,
        label: "Inter-site movement",
        name: "name_0577451693",
        placeholder: "Yes",
        required: true,
        rowIndex: 0,
        type: "",
        value: "",
        variant: "Checkbox",
      },
      {
        checked: true,
        description: "You can manage",
        disabled: false,
        label: "Start date",
        name: "start_date",
        placeholder: "start date",
        required: true,
        rowIndex: 0,
        type: "",
        value: "",
        variant: "Date",
      },
    ],
  },
];
