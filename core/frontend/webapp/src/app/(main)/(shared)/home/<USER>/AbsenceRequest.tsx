import React, { useEffect, useRef, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Clock, FileText, Info } from "lucide-react";
import Image from "next/image";
import CustomSelect from "@/components/common/CustomSelect";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { FIELD_RENDERERS } from "../store/fieldRenderers";
import { format } from "date-fns";
import { useRequestStore } from "../store/requestStore";
import CustomIcon from "@/components/common/CustomIcons";
import { FieldData } from "../types/profileTypes";
import useMockAuthStore from "@/store/mockAuthStore";

interface Workflow {
  id: string;
  name: string;
  description: string;
  formID: string;
}

interface ErrorWithMessage {
  message?: string;
  [key: string]: unknown;
}

// Type guard function to check if error has message
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return typeof error === "object" && error !== null && "message" in error;
}

// Helper function to get error message safely
function getErrorMessage(error: unknown): string {
  if (isErrorWithMessage(error)) {
    return error.message || "An unexpected error occurred";
  }
  return "An unexpected error occurred";
}

// Type for API field from the backend
interface ApiField {
  name: string;
  label: string;
  type: string;
  placeholder?: string;
  options?: string[];
  minDate?: Date;
  maxDate?: Date;
  [key: string]: unknown;
}

const AbsenceRequest = () => {
  const t = useTranslations();
  const { toast } = useToast();
  const { currentUser } = useMockAuthStore();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formValue, setFormValue] = useState<Record<string, any> | null>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const dynamicFormRef = useRef<{
    getFormValues: () => Record<string, any>; // eslint-disable-line @typescript-eslint/no-explicit-any
  } | null>(null);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(
    null,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    workflowTypes,
    fetchWorkflowTypes,
    fetchFormByFormID,
    selectedForm,
    loadingFormId,
    submitRequest,
  } = useRequestStore();

  // Fetch workflow types on mount
  useEffect(() => {
    fetchWorkflowTypes();
  }, [fetchWorkflowTypes]);

  // Only show workflows of type ABSENCE_AUTHORIZATION
  const absenceWorkflows =
    workflowTypes.find((t) => t.requesttype === "absence-requests")
      ?.workflows || [];

  // When workflow is selected, fetch its form
  const handleWorkflowSelect = (workflowId: string) => {
    const workflow = absenceWorkflows.find((w) => w.id === workflowId);
    if (workflow) {
      setSelectedWorkflow(workflow);
      fetchFormByFormID(workflow.formID);
    }
  };

  const handleSubmitForm = () => {
    if (dynamicFormRef.current) {
      const formData = dynamicFormRef.current.getFormValues();
      setFormValue(formData);
      setDialogOpen(false);
      setConfirmDialogOpen(true);
    }
  };

  const DynamicForm = React.forwardRef<
    { getFormValues: () => Record<string, any> }, // eslint-disable-line @typescript-eslint/no-explicit-any
    { fields: (FieldData & { label: string })[] }
  >(({ fields }, ref) => {
    const [localValue, setLocalValue] = useState<Record<string, any>>({}); // eslint-disable-line @typescript-eslint/no-explicit-any
    const [fieldLabels, setFieldLabels] = useState<Record<string, string>>({});

    // Store field labels when component mounts
    useEffect(() => {
      const labels: Record<string, string> = {};
      fields.forEach((field) => {
        labels[field.name] = field.label;
      });
      setFieldLabels(labels);
    }, [fields]);

    const handleChange = (
      fieldName: string,
      newValue: string | Date | number | boolean,
    ) => {
      setLocalValue((prev) => ({
        ...prev,
        [fieldName]: newValue,
      }));
    };

    React.useImperativeHandle(ref, () => ({
      getFormValues: () => ({
        values: localValue || {},
        labels: fieldLabels,
      }),
    }));

    return (
      <form className="space-y-4">
        {fields.map((field) => {
          const Renderer = FIELD_RENDERERS[field.variant];
          if (!Renderer) return null;
          return (
            <div key={field.name} className="space-y-2">
              <Label>{field.label}</Label>
              <Renderer
                fieldData={field}
                value={localValue[field.name] || ""}
                onChange={(newValue: string | Date | number | boolean) =>
                  handleChange(field.name, newValue)
                }
              />
            </div>
          );
        })}
      </form>
    );
  });

  DynamicForm.displayName = "DynamicForm";

  const handleFinalSubmit = async () => {
    if (!formValue || !selectedWorkflow) return;

    setIsSubmitting(true);

    // Create the request payload
    const requestPayload = {
      requesterId: currentUser?.id || "fab9d951-9eae-4949-bd8f-ce500dfd3c95", // Use current user's UUID or fallback
      targetOperatorIds: [],
      formData: formValue?.values || {}, // Use only the values for API
      workflowInstanceId: selectedWorkflow.id, // Use the selected workflow ID
    };

    try {
      // Use the submitRequest function from the store instead of direct API call
      const result = await submitRequest(requestPayload);

      // Close the confirmation dialog and show success toast
      if (result.success) {
        setConfirmDialogOpen(false);
        toast({
          title: t("AbsenceRequest.successTitle"),
          description: result.message,
          variant: "default",
        });
      }
    } catch (error: unknown) {
      // Show error toast
      console.log("error22", error);
      toast({
        title: t("AbsenceRequest.errorTitle"),
        description: getErrorMessage(error),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper to map API field type to FIELD_RENDERERS variant
  const mapFieldTypeToVariant = (type: string) => {
    switch (type) {
      case "text":
        return "Input";
      case "select":
        return "Select";
      case "checkbox":
        return "Checkbox";
      case "date":
        return "Date";
      default:
        return "Input";
    }
  };

  // Prepare fields for DynamicForm
  const dynamicFormFields: (FieldData & { label: string })[] =
    selectedForm?.fields
      ? selectedForm.fields.map((field) => {
          const apiField = field as unknown as ApiField;
          return {
            name: apiField.name,
            label: apiField.label,
            placeholder: apiField.placeholder || apiField.label,
            variant: mapFieldTypeToVariant(apiField.type),
            options: apiField.options,
            minDate: apiField.minDate,
            maxDate: apiField.maxDate,
          };
        })
      : [];

  return (
    <>
      <Card className="w-full overflow-hidden shadow-lg">
        <CardContent className="p-0">
          <div
            style={{
              backgroundImage: `url(/image/absencerequest.png)`,
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
            }}
          >
            <div className="flex pl-4 pt-4">
              <h2 className="text-xl font-bold mb-4">
                {t("AbsenceRequest.title")}
              </h2>
              <Info className="w-5 h-5 text-gray-400 ml-2 mt-1" />
            </div>
            <div className="flex items-center gap-4  pl-4">
              <Image height="50" width="50" alt="" src="image/absence.svg" />
            </div>
            <div className="flex flex-col sm:flex-row gap-3 p-4 pb-1  items-center">
              <CustomSelect
                options={absenceWorkflows.map((wf) => ({
                  label: wf.name, // ABSENCE AUTHORIZATION
                  value: wf.id,
                }))}
                onValueChange={handleWorkflowSelect}
                defaultValue={selectedWorkflow?.id}
                placeholder={t("AbsenceRequest.selectPlaceholder")}
                label={t("AbsenceRequest.selectedRequestLabel")}
                className="w-full mb-4"
                id="workflow-select"
                isLoading={selectedWorkflow?.id === loadingFormId}
              />
              <Button
                className="min-w-[200px] mt-2"
                onClick={() => setDialogOpen(true)}
                disabled={!selectedWorkflow || !selectedForm}
              >
                <CustomIcon
                  name="submitIcon"
                  className="h-4 w-4"
                  style={{ width: "auto", height: "auto" }}
                />
                <span>{t("AbsenceRequest.submitRequest")}</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between p-0 pb-4 border-b">
            <DialogTitle>{selectedWorkflow?.name || ""}</DialogTitle>
          </DialogHeader>
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-full">
                <FileText className="h-6 w-6 text-blue-900" />
              </div>
            </div>
            <div className="text-center flex-1">
              <p>{t("AbsenceRequest.insertInformation")}</p>
            </div>
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full">
                <Clock className="h-6 w-6 text-green-900" />
              </div>
            </div>
          </div>
          {selectedForm && (
            <DynamicForm fields={dynamicFormFields} ref={dynamicFormRef} />
          )}

          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              {t("AbsenceRequest.close")}
            </Button>
            <Button
              className="bg-black hover:bg-black/90 text-white"
              onClick={handleSubmitForm}
            >
              <ArrowRight className="h-4 w-4" />
              <span>{t("AbsenceRequest.submitRequest")}</span>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="flex flex-row items-center justify-between p-0 border-b pb-4">
            <DialogTitle>{t("AbsenceRequest.requestSummary")}</DialogTitle>
          </DialogHeader>

          <div className="text-center border-b pb-4">
            <h3 className="text-blue-600 font-medium text-lg mb-2">
              {t("AbsenceRequest.confirmRequest")}
            </h3>
            <p className="text-gray-500 text-sm">
              {t("AbsenceRequest.reviewDetails")}
            </p>
          </div>

          <div className="py-4 space-y-4">
            {selectedWorkflow && formValue && (
              <div className="border-b pb-4">
                <div className="mb-2">
                  {Object.entries(formValue.values || {}).map(
                    ([key, value]) => (
                      <div key={key}>
                        <p className="text-sm text-gray-500">
                          {formValue.labels?.[key] || key}:
                        </p>
                        <p className="font-medium">
                          {value instanceof Date
                            ? format(value, "dd/MM/yyyy")
                            : value?.toString()}
                        </p>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-between mt-4">
            <Button
              variant="outline"
              onClick={() => {
                setConfirmDialogOpen(false);
                setDialogOpen(true);
              }}
            >
              {t("AbsenceRequest.back")}
            </Button>
            <Button
              className="bg-black hover:bg-black/90 text-white"
              onClick={handleFinalSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <span className="animate-spin h-4 w-4 mr-2 border-2 border-b-0 border-white rounded-full"></span>
                  {t("AbsenceRequest.submitting")}
                </span>
              ) : (
                <>
                  <ArrowRight className="h-4 w-4" />
                  <span>{t("AbsenceRequest.submitRequest")}</span>
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AbsenceRequest;
