"use client";
import { useEffect, useState } from "react";
import { FileTex<PERSON>, Eye } from "lucide-react";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useRequestStore } from "../store/requestStore";

interface LatestRequestsProps {
  requesterId: string;
}

export default function LatestRequests({ requesterId }: LatestRequestsProps) {
  const t = useTranslations();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any

  const { latestRequests, fetchLatestRequests, loading } = useRequestStore();

  useEffect(() => {
    if (requesterId) fetchLatestRequests(requesterId);
  }, [requesterId, fetchLatestRequests]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "generated":
      case "GENERATED":
      case "approved":
      case "APPROVED":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "in-progress":
      case "PENDING":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      case "rejected":
      case "REJECTED":
        return "bg-red-100 text-red-800 hover:bg-red-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    const statusKey = status.toLowerCase();
    switch (statusKey) {
      case "generated":
        return t("LatestRequests.status.generated");
      case "approved":
        return t("LatestRequests.status.approved");
      case "in-progress":
      case "pending":
        return t("LatestRequests.status.pending");
      case "rejected":
        return t("LatestRequests.status.rejected");
      default:
        return statusKey;
    }
  };

  const formatDate = (iso: string) => {
    const d = new Date(iso);
    return d.toLocaleDateString();
  };

  const formatTime = (iso: string) => {
    const d = new Date(iso);
    return d.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const formatRequestType = (type: string) => {
    if (!type) return "";
    return type
      .split(/[-_]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  return (
    <>
      <Card
        className="w-full overflow-hidden shadow-lg"
        style={{
          background: "linear-gradient(180deg, #E6F1FF 0%, #FFFFFF 100%)",
        }}
      >
        <CardHeader className="pb-2">
          <CardTitle className="text-xl font-bold">
            {t("LatestRequests.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 h-[180px] overflow-auto">
          <div className="divide-y divide-gray-100">
            {loading ? (
              <div className="p-4 text-center">Loading...</div>
            ) : latestRequests.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {t("LatestRequests.noRequests")}
              </div>
            ) : (
              latestRequests.map((request) => (
                <div
                  key={request.id}
                  className="flex items-center justify-between p-3"
                >
                  <div className="flex items-center gap-1">
                    <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-md">
                      <FileText className="w-4 h-4 text-gray-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        {formatRequestType(request.requestType)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge
                      className={`mt-1 font-normal text-xs px-2 py-0.5 ${getStatusColor(
                        request.status,
                      )}`}
                      variant="outline"
                    >
                      {getStatusText(request.status)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <p className="text-xs text-gray-500 text-right">
                      <span>Request date </span>
                      {formatDate(request.createdAt)}-
                      {formatTime(request.createdAt)}
                    </p>
                  </div>
                  <div>
                    <button
                      className="p-1 rounded-full hover:bg-gray-100"
                      onClick={() => {
                        setSelectedRequest(request);
                        setDialogOpen(true);
                      }}
                    >
                      <Eye className="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader className="border-b pb-4">
            <DialogTitle>{t("DocumentRequest.title")}</DialogTitle>
          </DialogHeader>
          <div className="flex items-center space-x-2 py-4">
            <div className="w-full">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">
                  {t("DocumentRequest.title")}:{" "}
                  <span className="text-[#4762F1]">
                    {formatRequestType(selectedRequest?.requestType)}
                  </span>
                </h3>
              </div>

              <div className="w-full h-[600px] border rounded-lg overflow-hidden bg-white">
                <iframe
                  src={`${selectedRequest?.generatedfile || "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"}#toolbar=0`}
                  className="w-full h-full"
                  title={`${formatRequestType(selectedRequest?.requestType) || t("DocumentRequest.title")}`}
                />
              </div>

              <div className="flex justify-center mt-4">
                <Button
                  variant="outline"
                  className="text-black bg-white hover:bg-gray-50"
                  onClick={() => setDialogOpen(false)}
                >
                  {t("Common.cancel")}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
