import React from "react";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/datePicker";
import { FieldData } from "../types/profileTypes"; // Import the FieldData type

export const FIELD_RENDERERS: Record<
  string,
  React.FC<{
    fieldData: FieldData;
    value: string | boolean | Date | null;
    onChange: (value: string | boolean | Date) => void;
  }>
> = {
  Input: ({ fieldData, value, onChange }) => (
    <Input
      placeholder={fieldData.placeholder}
      value={value as string}
      onChange={(e) => onChange(e.target.value)}
    />
  ),
  Select: ({ fieldData, value, onChange }) => (
    <Select
      onValueChange={(val) => onChange(val as string)}
      defaultValue={value as string}
    >
      <SelectTrigger>
        <SelectValue placeholder={fieldData.placeholder} />
      </SelectTrigger>
      <SelectContent>
        {(fieldData.options ?? ["Option 1", "Option 2"]).map((opt) => (
          <SelectItem key={opt} value={opt}>
            {opt}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  ),
  Checkbox: ({ fieldData, value, onChange }) => (
    <div className="flex items-center space-x-2">
      <Checkbox
        id={fieldData.name}
        checked={value as boolean}
        onCheckedChange={(val) => onChange(val as boolean)}
      />
      <label
        htmlFor={fieldData.name}
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {fieldData.placeholder}
      </label>
    </div>
  ),
  Date: ({ fieldData, value, onChange }) => (
    <DatePicker
      name={fieldData.name}
      minDate={fieldData.minDate}
      maxDate={fieldData.maxDate}
      dateFormat="dd/MM/yyyy"
      defaultDate={value as Date}
      placeholder={fieldData.placeholder}
      onChange={(val) => onChange(val as Date)}
    />
  ),
};
