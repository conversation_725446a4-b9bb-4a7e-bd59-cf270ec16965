import { create } from "zustand";
import { useAuthStore } from "../../../../../store/authStore";
import { useMockAuthStore } from "../../../../../store/mockAuthStore";
import { ProfileData } from "../types/profileTypes";

interface ProfileState {
  profile: ProfileData | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchUserProfile: () => Promise<void>;
  clearError: () => void;
}

export const useProfileStore = create<ProfileState>((set) => ({
  profile: null,
  loading: false,
  error: null,

  fetchUserProfile: async () => {
    try {
      set({ loading: true, error: null });

      // Get user data from auth store
      const userData = useAuthStore.getState().user;
      const mockUser = useMockAuthStore.getState().currentUser;

      let profile: ProfileData;

      if (!userData) {
        // If no real user, check mock user
        if (!mockUser) {
          throw new Error("No user found in either auth or mock store");
        }

        // Create profile from mock user data
        profile = {
          name: mockUser.name || "Youssef Said",
          role: mockUser.role || "Team leader",
          id: mockUser.legacyId || "4574",
          email: "<EMAIL>", // Default email
          department: "Production", // Default department
          plant: "M3 - Kenitra", // Default plant
          manager: "Shift leader (Karim Hassib)", // Default manager
          phone: "0675262211", // Default phone
          imageUrl: "", // Default empty image URL
        };
      } else {
        // Create profile from real auth data
        profile = {
          name:
            userData.displayName ||
            `${userData.firstName || ""} ${userData.lastName || ""}`.trim(),
          role:
            userData.jobTitle ||
            (userData.roles.length > 0 ? userData.roles[0] : "User"),
          id: userData.id,
          email: userData.email,
          department: userData.department,
        };
      }

      set({ profile, loading: false });
    } catch (error) {
      console.error("Error fetching user profile:", error);
      set({
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch user profile",
        loading: false,
      });
    }
  },

  clearError: () => set({ error: null }),
}));

// Selector hooks for optimized component re-renders
export const useProfile = () => useProfileStore((state) => state.profile);
export const useProfileLoading = () =>
  useProfileStore((state) => state.loading);
export const useProfileError = () => useProfileStore((state) => state.error);
