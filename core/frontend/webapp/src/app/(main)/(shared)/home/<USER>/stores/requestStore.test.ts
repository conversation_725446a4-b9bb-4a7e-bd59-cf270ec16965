import { useRequestStore } from "../../store/requestStore";

describe("requestStore - Basic", () => {
  it("should pass a simple test", () => {
    expect(true).toBe(true);
  });
  it("should have a loading state", () => {
    expect(useRequestStore.getState().loading).toBeDefined();
  });
  it("should have an error state", () => {
    expect(useRequestStore.getState().error).toBeDefined();
  });
  it("should not hardcode the API base URL in tests", () => {
    const endpoint = "/request/requests";
    expect(endpoint.startsWith("/")).toBe(true);
  });
  it("should have initial workflowTypes as empty array", () => {
    expect(Array.isArray(useRequestStore.getState().workflowTypes)).toBe(true);
    expect(useRequestStore.getState().workflowTypes.length).toBe(0);
  });
  it("should have initial latestRequests as empty array", () => {
    expect(Array.isArray(useRequestStore.getState().latestRequests)).toBe(true);
    expect(useRequestStore.getState().latestRequests.length).toBe(0);
  });
  it("submitRequest should return a promise", () => {
    const result = useRequestStore.getState().submitRequest({
      requestType: "test",
      requesterId: "1",
      targetOperatorIds: ["2"],
      data: {},
      workflowInstanceId: "w1",
    });
    expect(result).toBeInstanceOf(Promise);
  });
});
