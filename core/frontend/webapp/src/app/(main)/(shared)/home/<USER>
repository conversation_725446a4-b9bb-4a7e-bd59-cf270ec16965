"use client";
import React from "react";
import { useTranslations } from "next-intl";
import ProfileCard from "./components/ProfileCard";
import DocumentRequest from "./components/DocumentRequest";
import LeaveBalance from "./components/LeaveBalance";
import OvertimeHours from "./components/OvertimeHours";
import AbsenceRequest from "./components/AbsenceRequest";
import LatestRequests from "./components/LatestRequests";
import OtherRequest from "./components/OtherRequest";
import useMockAuthStore from "@/store/mockAuthStore";

export default function ExamplePage() {
  const t = useTranslations();
  const { currentUser } = useMockAuthStore();

  // Use currentUser.uuid as requesterId if available, otherwise fallback
  const requesterId = currentUser?.id || "fab9d951-9eae-4949-bd8f-ce500dfd3c95";

  return (
    <div className="bg-white lg:m-5 lg:p-4 rounded-xl">
      <h1 className="text-[#4762F1] text-xl font-semibold">
        {t("HomePage.title")}
        {currentUser?.name ? ` ${currentUser.name}` : ""}
      </h1>
      <p className="text-[#848484] pt-2 mb-4">
        {t("HomePage.subtitle", {
          defaultMessage: "This Is Your Aptiv Connected Workers Home Page",
        })}
      </p>
      <div className="grid lg:grid-cols-2 md:grid-cols-1 gap-3">
        <div>
          <ProfileCard />
          <div className="grid lg:grid-cols-2 md:grid-cols-1 gap-2 mt-4 mb-4">
            <div>
              <LeaveBalance days={16} />
            </div>
            <div>
              <OvertimeHours hours={13} />
            </div>
          </div>
          <div>
            <LatestRequests requesterId={requesterId} />
          </div>
        </div>
        <div>
          <div className="mb-4">
            <DocumentRequest />
          </div>
          <div className="mb-4">
            <AbsenceRequest />
          </div>
          <div>
            <OtherRequest />
          </div>
        </div>
      </div>
    </div>
  );
}
