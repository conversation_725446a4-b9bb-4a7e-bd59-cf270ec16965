import React, { useEffect, useRef, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Info } from "lucide-react";
import Image from "next/image";
import CustomSelect from "@/components/common/CustomSelect";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { FIELD_RENDERERS } from "../store/fieldRenderers";
import { format } from "date-fns";
import { useRequestStore } from "../store/requestStore";
import CustomIcon from "@/components/common/CustomIcons";
import type { Workflow } from "../types/profileTypes";
import useMockAuthStore from "@/store/mockAuthStore";

const OtherRequest = () => {
  const t = useTranslations();
  const { toast } = useToast();
  const { currentUser } = useMockAuthStore();
  const [selectedDocument, setSelectedDocument] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formValue, setFormValue] = useState<FormData | null>(null);
  const dynamicFormRef = useRef<{ getFormValues: () => FormData } | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(
    null,
  );

  // Fetch workflow types from API
  const {
    workflowTypes,
    fetchWorkflowTypes,
    fetchFormByFormID,
    selectedForm,
    loadingFormId,
    submitRequest,
  } = useRequestStore();

  useEffect(() => {
    fetchWorkflowTypes();
  }, [fetchWorkflowTypes]);

  // Get 'others' workflows from API
  const otherWorkflows =
    workflowTypes.find((t) => t.requesttype === "others")?.workflows || [];

  const documentOptions = otherWorkflows.map((wf) => ({
    label: wf.name,
    value: wf.id,
  }));

  const handleDocumentSelect = (value: string) => {
    setSelectedDocument(value);

    // Find selected workflow and fetch its form
    const workflow = otherWorkflows.find((wf) => wf.id === value);
    if (workflow) {
      setSelectedWorkflow(workflow);
      fetchFormByFormID(workflow.formID);
    }
  };

  const handleSubmit = async () => {
    if (!selectedDocument) return;

    setIsSubmitting(true);

    // Find selected workflow
    const workflow = otherWorkflows.find((wf) => wf.id === selectedDocument);
    if (!workflow) {
      toast({
        title: t("OtherRequest.errorTitle"),
        description: t("OtherRequest.requestNotFound"),
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Create request payload
    const requestPayload = {
      requesterId: currentUser?.id || "fab9d951-9eae-4949-bd8f-ce500dfd3c95", // Use current user's UUID or fallback
      targetOperatorIds: [],
      formData: formValue?.values || {}, // Use only the values for API
      workflowInstanceId: workflow.id, // Use the selected workflow ID
    };

    try {
      // Use submitRequest from the store
      const result = await submitRequest(requestPayload);

      if (result.success) {
        setConfirmDialogOpen(false);
        toast({
          title: t("OtherRequest.successTitle"),
          description: t("Common.successMessage"),
          variant: "default",
        });
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : t("Common.errorMessage");
      toast({
        title: t("OtherRequest.errorTitle"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitForm = () => {
    if (dynamicFormRef.current) {
      const formValues = dynamicFormRef.current.getFormValues();
      setFormValue(formValues);
      setDialogOpen(false);
      setConfirmDialogOpen(true);
    }
  };

  interface DynamicFormField {
    name: string;
    label: string;
    variant: string;
    placeholder: string;
    required: boolean;
  }

  interface FormValues {
    [key: string]: string | boolean | Date;
  }

  interface FormData {
    values: FormValues;
    labels: Record<string, string>;
  }

  const DynamicForm = React.forwardRef<
    { getFormValues: () => FormData },
    { fields: DynamicFormField[] }
  >(({ fields }, ref) => {
    const [localValue, setLocalValue] = useState<FormValues>({});
    const [fieldLabels, setFieldLabels] = useState<Record<string, string>>({});

    // Store field labels when component mounts
    useEffect(() => {
      const labels: Record<string, string> = {};
      fields.forEach((field) => {
        labels[field.name] = field.label;
      });
      setFieldLabels(labels);
    }, [fields]);

    const handleChange = (
      fieldName: string,
      newValue: string | boolean | Date,
    ) => {
      if (newValue !== null && newValue !== undefined && newValue !== "") {
        setLocalValue((prev: FormValues) => ({
          ...prev,
          [fieldName]: newValue,
        }));
      } else {
        // Remove the field if value is empty/null
        setLocalValue((prev: FormValues) => {
          const newState = { ...prev };
          delete newState[fieldName];
          return newState;
        });
      }
    };

    React.useImperativeHandle(ref, () => ({
      getFormValues: () => ({
        values: localValue || {},
        labels: fieldLabels,
      }),
    }));

    return (
      <form className="space-y-4">
        {fields.map((field) => {
          const Renderer = FIELD_RENDERERS[field.variant];
          if (!Renderer) return null;
          return (
            <div key={field.name} className="space-y-2">
              <Label>{field.label}</Label>
              <Renderer
                fieldData={field}
                value={localValue[field.name] || ""}
                onChange={(newValue: string | boolean | Date) =>
                  handleChange(field.name, newValue)
                }
              />
            </div>
          );
        })}
      </form>
    );
  });

  DynamicForm.displayName = "DynamicForm";

  // Helper to map API field type to FIELD_RENDERERS variant
  const mapFieldTypeToVariant = (type: string) => {
    switch (type) {
      case "text":
        return "Input";
      case "select":
        return "Select";
      case "checkbox":
        return "Checkbox";
      case "date":
        return "Date";
      default:
        return "Input";
    }
  };

  // Prepare fields for DynamicForm
  const dynamicFormFields = selectedForm?.fields
    ? selectedForm.fields.map((field) => ({
        ...field,
        variant: mapFieldTypeToVariant(field.variant),
      }))
    : [];

  return (
    <Card className="w-full overflow-hidden shadow-lg">
      <CardContent className="p-0">
        <div
          style={{
            backgroundImage: `url(/image/otherrequests.png)`,
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
          }}
        >
          <div className="flex pl-4 pt-4">
            <h2 className="text-xl font-bold mb-4">
              {t("OtherRequest.title")}
            </h2>
            <Info className="w-5 h-5 text-gray-400 ml-2 mt-1" />
          </div>
          <div className="flex items-center gap-4  pl-4">
            <Image height="50" width="50" alt="" src="image/other.svg" />
          </div>
          <div className="flex flex-col sm:flex-row gap-3 p-4 pb-1  items-center">
            <CustomSelect
              options={documentOptions}
              onValueChange={handleDocumentSelect}
              defaultValue={selectedDocument}
              placeholder={t("OtherRequest.selectPlaceholder")}
              label={t("OtherRequest.selectedRequestLabel")}
              className="w-full mb-4"
              id="document-select"
              isLoading={selectedDocument === loadingFormId}
            />
            <Button
              className="min-w-[200px]  mt-2"
              onClick={() => setDialogOpen(true)}
              disabled={!selectedDocument}
            >
              <CustomIcon
                name="submitIcon"
                className="h-4 w-4"
                style={{ width: "auto", height: "auto" }}
              />
              <span>{t("OtherRequest.submitRequest")}</span>
            </Button>

            <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
              <DialogContent className="sm:max-w-md">
                <DialogHeader className="flex flex-row items-center justify-between p-0 pb-4 border-b">
                  <DialogTitle>
                    {selectedWorkflow?.name || t("OtherRequest.title")}
                  </DialogTitle>
                </DialogHeader>

                {selectedForm && (
                  <DynamicForm
                    fields={dynamicFormFields}
                    ref={dynamicFormRef}
                  />
                )}

                <div className="flex justify-between mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setDialogOpen(false)}
                  >
                    {t("OtherRequest.cancel")}
                  </Button>
                  <Button
                    className="bg-black hover:bg-black/90 text-white"
                    onClick={handleSubmitForm}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <span className="animate-spin h-4 w-4 mr-2 border-2 border-b-0 border-white rounded-full"></span>
                        {t("OtherRequest.submitting")}
                      </span>
                    ) : (
                      <>
                        <ArrowRight className="h-4 w-4" />
                        <span>{t("OtherRequest.submitRequest")}</span>
                      </>
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog
              open={confirmDialogOpen}
              onOpenChange={setConfirmDialogOpen}
            >
              <DialogContent className="sm:max-w-md">
                <DialogHeader className="flex flex-row items-center justify-between p-0 border-b pb-4">
                  <DialogTitle>{t("OtherRequest.requestSummary")}</DialogTitle>
                </DialogHeader>

                <div className="text-center border-b pb-4">
                  <h3 className="text-blue-600 font-medium text-lg mb-2">
                    {t("OtherRequest.confirmRequest")}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {t("OtherRequest.reviewDetails")}
                  </p>
                </div>

                <div className="py-4 space-y-4">
                  {selectedWorkflow && formValue && (
                    <div className="border-b pb-4">
                      <div className="mb-2">
                        {Object.entries(formValue.values || {}).map(
                          ([key, value]) => (
                            <div key={key}>
                              <p className="text-sm text-gray-500">
                                {formValue.labels?.[key] || key}:
                              </p>
                              <p className="font-medium">
                                {value instanceof Date
                                  ? format(value, "dd/MM/yyyy")
                                  : value?.toString()}
                              </p>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between mt-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setConfirmDialogOpen(false);
                      setDialogOpen(true);
                    }}
                  >
                    {t("OtherRequest.back")}
                  </Button>
                  <Button
                    className="bg-black hover:bg-black/90 text-white"
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <span className="animate-spin h-4 w-4 mr-2 border-2 border-b-0 border-white rounded-full"></span>
                        {t("OtherRequest.submitting")}
                      </span>
                    ) : (
                      <>
                        <ArrowRight className="h-4 w-4" />
                        <span>{t("OtherRequest.submitRequest")}</span>
                      </>
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OtherRequest;
