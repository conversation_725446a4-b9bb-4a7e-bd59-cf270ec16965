export interface ProfileData {
  name: string;
  role: string;
  id: string;
  plant?: string;
  manager?: string;
  phone?: string;
  email: string;
  imageUrl?: string;
  department?: string;
  jobTitle?: string;
}

// Workflow types
export interface Workflow {
  id: string;
  name: string;
  description: string;
  formID: string;
}

// Form field definition types
export interface FormField {
  name: string;
  label: string;
  variant: string;
  description?: string;
  options?: Array<{
    label: string;
    value: string;
  }>;
  placeholder: string; // Added placeholder property
}

// Document data types
export interface DocumentType {
  name: string;
  fields: FormField[];
}

// Form values type
export interface FormValues {
  [key: string]: string | Date | number | boolean;
}

// Dynamic form props
export interface DynamicFormProps {
  data: DocumentType;
  ref: React.Ref<{
    getFormValues: () => FormValues;
  }>;
}

export interface FieldData {
  name: string;
  placeholder: string;
  variant: string;
  options?: string[];
  minDate?: Date;
  maxDate?: Date;
}

export interface FormField extends Omit<FieldData, "options"> {
  options?: Array<{
    label: string;
    value: string;
  }>;
  value: string | number | boolean | Date | null;
}
