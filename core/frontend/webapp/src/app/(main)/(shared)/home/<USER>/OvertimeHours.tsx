import { Info } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import Image from "next/image";

interface OvertimeHoursProps {
  hours: number;
  showHelp?: boolean;
}
export default function OvertimeHours({
  hours = 13,
  showHelp = true,
}: OvertimeHoursProps) {
  const t = useTranslations();
  return (
    <Card className="w-full overflow-hidden shadow-lg">
      <CardContent className="p-0">
        <div
          style={{
            background: "linear-gradient(180deg, #E6F1FF 0%, #FFFFFF 100%)",
          }}
        >
          <div className="flex pl-4 pt-4">
            <h2 className="text-xl font-bold mb-4">
              {t("OvertimeHours.title")}
            </h2>
            {showHelp && <Info className="w-5 h-5 text-gray-400 ml-2 mt-1" />}
          </div>
          <div className="flex items-center p-4 justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-20 h-20 bg-[#E1F5FF] rounded-full">
                <Image
                  src="/image/timehours.svg"
                  alt={t("OvertimeHours.title")}
                  width={32}
                  height={32}
                />
              </div>
              <div className="block">
                <p className="text-2xl font-bold">{hours}</p>
                <p className="text-[#010101]">{t("OvertimeHours.hours")}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
