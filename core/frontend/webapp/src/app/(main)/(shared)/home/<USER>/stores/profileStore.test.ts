import { useProfileStore } from "../../store/profileStore";

describe("profileStore - Basic", () => {
  it("should pass a simple test", () => {
    expect(true).toBe(true);
  });
  it("should have a loading state", () => {
    expect(useProfileStore.getState().loading).toBeDefined();
  });
  it("should have an error state", () => {
    expect(useProfileStore.getState().error).toBeDefined();
  });
  it("should have initial profile as null or object", () => {
    const profile = useProfileStore.getState().profile;
    expect(profile === null || typeof profile === "object").toBe(true);
  });
  it("fetchProfile should return a promise", () => {
    if (typeof useProfileStore.getState().fetchUserProfile === "function") {
      const result = useProfileStore.getState().fetchUserProfile();
      expect(result).toBeInstanceOf(Promise);
    } else {
      expect(true).toBe(true); // fallback if function not present
    }
  });
});
