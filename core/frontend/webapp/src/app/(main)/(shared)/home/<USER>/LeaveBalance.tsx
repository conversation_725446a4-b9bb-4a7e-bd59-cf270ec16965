import { Card, CardContent } from "@/components/ui/card";
import { Info } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";

interface LeaveBalanceProps {
  days: number;
  showHelp?: boolean;
}

export default function LeaveBalance({
  days = 16,
  showHelp = true,
}: LeaveBalanceProps) {
  const t = useTranslations();
  return (
    <Card className="w-full overflow-hidden shadow-lg">
      <CardContent className="p-0">
        <div
          style={{
            background: "linear-gradient(180deg, #E6F1FF 0%, #FFFFFF 100%)",
          }}
        >
          <div className="flex pl-4 pt-4">
            <h2 className="text-xl font-bold mb-4">
              {t("LeaveBalance.title")}
            </h2>
            {showHelp && <Info className="w-5 h-5 text-gray-400 ml-2 mt-1" />}
          </div>
          <div className="flex items-center p-4 justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center justify-center w-20 h-20 bg-[#E1F5FF] rounded-full">
                <Image
                  src="/image/absence.svg"
                  alt={t("LeaveBalance.title")}
                  width={32}
                  height={32}
                />
              </div>
              <div className="block">
                <p className="text-2xl font-bold">{days}</p>
                <p className="text-[#010101]">{t("LeaveBalance.days")}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
