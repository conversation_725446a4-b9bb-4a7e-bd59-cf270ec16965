// This is a local utility for PDF handling specific to home components
// It provides PDF handling capabilities without modifying shared axios utility

/**
 * Fetches a PDF as a blob with CORS handling
 * @param url URL of the PDF to fetch
 * @returns Blob of the PDF
 */
export async function fetchPdfAsBlob(url: string): Promise<Blob> {
  try {
    // First try with no-cors mode
    try {
      const response = await fetch(url, {
        method: "GET",
        mode: "cors",
        cache: "no-cache",
        credentials: "omit",
        headers: {
          Accept: "application/pdf",
        },
      });

      if (response.ok) {
        return await response.blob();
      }
    } catch (corsError) {
      console.warn(
        "CORS fetch failed, trying alternative approach:",
        corsError,
      );
    }

    // If first attempt fails, try with proxy
    // Use a CORS proxy as a fallback if available
    const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(url)}`;
    const proxyResponse = await fetch(proxyUrl, {
      method: "GET",
      cache: "no-cache",
      headers: {
        Accept: "application/pdf",
      },
    });

    if (!proxyResponse.ok) {
      throw new Error(
        `Failed to fetch PDF: ${proxyResponse.status} ${proxyResponse.statusText}`,
      );
    }

    return await proxyResponse.blob();
  } catch (error) {
    console.error("Error fetching PDF:", error);
    throw error;
  }
}

/**
 * Creates an object URL from a PDF URL by first fetching it as a blob
 * This helps with CORS issues when trying to display PDFs
 * @param url URL of the PDF
 * @returns Object URL that can be used with react-pdf
 */
export async function createPdfObjectUrl(url: string): Promise<string> {
  try {
    const blob = await fetchPdfAsBlob(url);
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error("Error creating object URL for PDF:", error);
    throw error;
  }
}

/**
 * Converts a PDF to a data URL for display
 * @param url URL of the PDF
 * @returns Data URL representation of the PDF
 */
export async function getPdfAsDataUrl(url: string): Promise<string> {
  try {
    const blob = await fetchPdfAsBlob(url);
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error("Error converting PDF to data URL:", error);
    throw error;
  }
}

/**
 * Check if a URL is accessible (for CORS validation)
 * @param url URL to check
 * @returns Promise resolving to boolean indicating if URL is accessible
 */
export async function isUrlAccessible(url: string): Promise<boolean> {
  try {
    await fetch(url, {
      method: "HEAD",
      mode: "no-cors",
    });
    return true;
  } catch (error) {
    console.error("URL is not accessible:", error);
    return false;
  }
}
