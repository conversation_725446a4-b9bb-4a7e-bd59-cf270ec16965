// Enhanced store following Project 1 patterns
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import type {
  CalendarEvent,
  EventFormData,
  EventCategory,
  CalendarSummary,
  ImportResult,
  CalendarViewMode,
  EventFilter,
  EventHistoryItem,
  ApiHistoryEventType,
} from "../types/calendarTypes";
import { toast } from "@/hooks/use-toast";
import api from "@/lib/axios";
import { format } from "date-fns";

export interface CalendarState {
  // Loading states
  isLoading: boolean;
  isEventLoading: boolean;
  isCategoriesLoading: boolean;
  isSummaryLoading: boolean;
  isImporting: boolean;
  isExporting: boolean;
  isHistoryLoading: boolean;
  isLoadingMoreHistory: boolean;
  isDownloadingCanvas: boolean;

  // New filter states for history
  historyStartDate: Date | null;
  historyEndDate: Date | null;
  historyFilterType: ApiHistoryEventType | "ALL";

  // Client-side pagination states for history
  historyPageSize: number;
  displayedHistoryCount: number;
  hasMoreHistory: boolean;

  // Data
  events: CalendarEvent[];
  filteredEvents: CalendarEvent[];
  eventCategories: EventCategory[];
  calendarSummary: CalendarSummary | null;
  eventHistory: EventHistoryItem[]; // All fetched history items
  allEventHistory: EventHistoryItem[]; // Complete history data from API

  // UI state
  currentDate: Date;
  viewMode: CalendarViewMode;
  searchTerm: string;
  activeFilters: EventFilter;
  selectedEventId: string | null;

  // Modal states
  isEventModalOpen: boolean;
  isEventDetailsModalOpen: boolean;
  isHistoryModalOpen: boolean;
  isImportModalOpen: boolean;
  isDeleteModalOpen: boolean;
  deleteModalEventId: string | null;

  // Modal data
  editingEventId: string | null;
  selectedDate: Date | null;

  // Actions - Data fetching
  fetchEvents: (year?: number, searchTerm?: string) => Promise<void>;
  fetchEventCategories: () => Promise<void>;
  fetchCalendarSummary: (year: number) => Promise<void>;
  fetchEventHistory: (reset?: boolean) => Promise<void>;
  loadMoreHistory: () => void; // Changed to sync function for client-side pagination

  // Actions - DeleteModal management
  openDeleteModal: (eventId: string) => void;
  closeDeleteModal: () => void;

  // Actions - Event management
  createEvent: (eventData: EventFormData) => Promise<void>;
  updateEvent: (id: string, eventData: EventFormData) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
  importEvents: (file: File) => Promise<ImportResult>;
  downloadCanvas: () => Promise<void>;
  exportEvents: (format?: "csv" | "xlsx") => Promise<void>;

  // Actions - UI state
  setCurrentDate: (date: Date) => void;
  setViewMode: (mode: CalendarViewMode) => void;
  setSearchTerm: (term: string) => void;
  setActiveFilters: (filters: EventFilter) => void;
  setSelectedEventId: (id: string | null) => void;

  // Actions - History filter management
  setHistoryStartDate: (date: Date | null) => void;
  setHistoryEndDate: (date: Date | null) => void;
  setHistoryFilterType: (type: ApiHistoryEventType | "ALL") => void;

  // Actions - Modal management
  openEventModal: (eventId?: string, selectedDate?: Date) => void;
  closeEventModal: () => void;
  openEventDetailsModal: (eventId: string) => void;
  closeEventDetailsModal: () => void;
  openHistoryModal: () => void;
  closeHistoryModal: () => void;
  openImportModal: () => void;
  closeImportModal: () => void;

  // Actions - Search and filter
  searchEvents: (term: string) => void;
  applyFilters: (filters: EventFilter) => void;
  resetFilters: () => void;

  // Actions - Navigation
  goToNextMonth: () => void;
  goToPreviousMonth: () => void;
  goToNextYear: () => void;
  goToPreviousYear: () => void;
  goToToday: () => void;

  // Utility functions
  getEventById: (id: string) => CalendarEvent | undefined;
  getEventsForDate: (date: Date) => CalendarEvent[];
  getTotalWorkingDays: () => number;
}

const useCalendarStore = create<CalendarState>()(
  persist(
    (set, get) => ({
      // Loading states
      isLoading: false,
      isEventLoading: false,
      isCategoriesLoading: false,
      isSummaryLoading: false,
      isImporting: false,
      isExporting: false,
      isHistoryLoading: false,
      isLoadingMoreHistory: false,
      isDownloadingCanvas: false,

      // Data
      events: [],
      filteredEvents: [],
      eventCategories: [],
      calendarSummary: null,
      eventHistory: [], // Currently displayed history items
      allEventHistory: [], // All fetched history items

      // UI state
      currentDate: new Date(),
      viewMode: { mode: "year", date: new Date() },
      searchTerm: "",
      activeFilters: {},
      selectedEventId: null,

      // History filter states
      historyStartDate: null,
      historyEndDate: null,
      historyFilterType: "ALL",

      // Client-side pagination states
      historyPageSize: 10, // Show 10 items initially, then 10 more each time
      displayedHistoryCount: 10,
      hasMoreHistory: false,

      // Modal states
      isEventModalOpen: false,
      isEventDetailsModalOpen: false,
      isHistoryModalOpen: false,
      isImportModalOpen: false,
      isDeleteModalOpen: false,
      deleteModalEventId: null,

      // Modal data
      editingEventId: null,
      selectedDate: null,

      // DeleteModal management actions
      openDeleteModal: (eventId: string) => {
        set({ isDeleteModalOpen: true, deleteModalEventId: eventId });
      },
      closeDeleteModal: () => {
        set({ isDeleteModalOpen: false, deleteModalEventId: null });
      },

      // Data fetching actions
      fetchEvents: async (year?: number, searchTerm?: string) => {
        set({ isLoading: true });
        try {
          const currentYear = year || get().currentDate.getFullYear();
          const response = await api.get(
            `/annual-calendar/api/calendar/events?year=${currentYear}&search=${searchTerm || ""}`,
          );
          const events: CalendarEvent[] = response.data;

          set({
            events: events,
            filteredEvents: events,
            isLoading: false,
          });

          toast({
            title: "Success",
            description: `Loaded ${events.length} events for ${currentYear}`,
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to fetch events:", error);
          toast({
            title: "Error",
            description: "Failed to fetch events. Please try again.",
            variant: "destructive",
          });
          set({ isLoading: false });
        }
      },

      fetchEventCategories: async () => {
        set({ isCategoriesLoading: true });
        try {
          const response = await api.get(
            "/annual-calendar/api/calendar/category",
          );
          const categories = response.data;

          set({
            eventCategories: categories,
            isCategoriesLoading: false,
          });

          toast({
            title: "Success",
            description: "Categories loaded successfully",
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to fetch categories:", error);
          toast({
            title: "Error",
            description: "Failed to fetch categories. Please try again.",
            variant: "destructive",
          });
          set({ isCategoriesLoading: false });
        }
      },

      fetchCalendarSummary: async (year: number) => {
        set({ isSummaryLoading: true });
        try {
          const response = await api.get(
            `/annual-calendar/api/calendar/events/summary/${year}`,
          );
          const summary = response.data;

          set({
            calendarSummary: summary,
            isSummaryLoading: false,
          });

          toast({
            title: "Success",
            description: `Calendar summary loaded for ${year}`,
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to fetch calendar summary:", error);
          toast({
            title: "Error",
            description: "Failed to fetch calendar summary. Please try again.",
            variant: "destructive",
          });
          set({ isSummaryLoading: false });
        }
      },

      fetchEventHistory: async (reset = false) => {
        set({ isHistoryLoading: true });
        console.log("Fetching event history with reset:", reset);
        try {
          const {
            historyStartDate,
            historyEndDate,
            historyFilterType,
            historyPageSize,
          } = get();

          // Default to a reasonable range if not set in store
          const defaultStartDate = new Date();
          defaultStartDate.setMonth(defaultStartDate.getMonth() - 1);
          const defaultEndDate = new Date();

          const startDate = historyStartDate || defaultStartDate;
          const endDate = historyEndDate || defaultEndDate;

          const params: {
            startDate: string;
            endDate: string;
            type?: ApiHistoryEventType;
          } = {
            startDate: format(startDate, "MM-dd-yyyy"),
            endDate: format(endDate, "MM-dd-yyyy"),
          };

          if (historyFilterType !== "ALL") {
            params.type = historyFilterType;
          }

          const response = await api.get<EventHistoryItem[]>(
            "/annual-calendar/api/calendar/activities",
            {
              params,
            },
          );

          const allHistory: EventHistoryItem[] = response.data || [];

          // Sort by performedAt date (newest first)
          const sortedHistory = allHistory.sort(
            (a, b) =>
              new Date(b.performedAt).getTime() -
              new Date(a.performedAt).getTime(),
          );

          // For client-side pagination, show initial page size
          const initialDisplayCount = historyPageSize;
          const displayedHistory = sortedHistory.slice(0, initialDisplayCount);

          set({
            allEventHistory: sortedHistory,
            eventHistory: displayedHistory,
            displayedHistoryCount: initialDisplayCount,
            hasMoreHistory: sortedHistory.length > initialDisplayCount,
            isHistoryLoading: false,
          });

          toast({
            title: "Success",
            description: `Loaded ${allHistory.length} history items`,
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to fetch event history:", error);
          toast({
            title: "Error",
            description: "Failed to fetch event history. Please try again.",
            variant: "destructive",
          });
          set({
            isHistoryLoading: false,
            allEventHistory: [],
            eventHistory: [],
            hasMoreHistory: false,
          });
        }
      },

      loadMoreHistory: () => {
        const { allEventHistory, displayedHistoryCount, historyPageSize } =
          get();

        if (!allEventHistory || allEventHistory.length === 0) {
          return;
        }

        set({ isLoadingMoreHistory: true });

        // Simulate a small delay for better UX (optional)
        setTimeout(() => {
          const newDisplayCount = displayedHistoryCount + historyPageSize;
          const newDisplayedHistory = allEventHistory.slice(0, newDisplayCount);

          set({
            eventHistory: newDisplayedHistory,
            displayedHistoryCount: newDisplayCount,
            hasMoreHistory: allEventHistory.length > newDisplayCount,
            isLoadingMoreHistory: false,
          });

          toast({
            title: "Success",
            description: `Loaded ${historyPageSize} more history items`,
            variant: "default",
          });
        }, 300); // Small delay for better UX
      },

      // Event management actions
      createEvent: async (eventData: EventFormData) => {
        set({ isEventLoading: true });
        try {
          const response = await api.post(
            "/annual-calendar/api/calendar/events",
            eventData,
          );
          const newEvent = response.data;

          const { events } = get();
          set({
            events: [...events, newEvent],
            isEventLoading: false,
          });

          get().applyFilters(get().activeFilters);
          get().fetchCalendarSummary(get().currentDate.getFullYear());

          toast({
            title: "Success",
            description: "Event created successfully",
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to create event:", error);
          toast({
            title: "Error",
            description: "Failed to create event. Please try again.",
            variant: "destructive",
          });
          set({ isEventLoading: false });
          throw error;
        }
      },

      updateEvent: async (id: string, eventData: EventFormData) => {
        set({ isEventLoading: true });
        try {
          const response = await api.put(
            `/annual-calendar/api/calendar/events/${id}`,
            eventData,
          );
          const updatedEvent = response.data;

          const { events } = get();
          const updatedEvents = events.map((event) =>
            event.id === id ? updatedEvent : event,
          );

          set({
            events: updatedEvents,
            isEventLoading: false,
          });

          get().applyFilters(get().activeFilters);
          get().fetchCalendarSummary(get().currentDate.getFullYear());

          toast({
            title: "Success",
            description: "Event updated successfully",
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to update event:", error);
          toast({
            title: "Error",
            description: "Failed to update event. Please try again.",
            variant: "destructive",
          });
          set({ isEventLoading: false });
          throw error;
        }
      },

      deleteEvent: async (id: string) => {
        set({ isEventLoading: true });
        try {
          await api.delete(`/annual-calendar/api/calendar/events/${id}`);

          const { events } = get();
          set({
            events: events.filter((event) => event.id !== id),
            isEventLoading: false,
          });

          get().applyFilters(get().activeFilters);
          get().fetchCalendarSummary(get().currentDate.getFullYear());

          toast({
            title: "Success",
            description: "Event deleted successfully",
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to delete event:", error);
          toast({
            title: "Error",
            description: "Failed to delete event. Please try again.",
            variant: "destructive",
          });
          set({ isEventLoading: false });
          throw error;
        }
      },

      importEvents: async (file: File) => {
        set({ isImporting: true });
        try {
          const formData = new FormData();
          formData.append("file", file);

          const response = await api.post(
            "/annual-calendar/api/calendar/events/import",
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            },
          );
          const result = response.data;

          await get().fetchEvents();
          get().fetchCalendarSummary(get().currentDate.getFullYear());
          get().fetchEventHistory(true); // Reset and fetch fresh history

          set({ isImporting: false });

          toast({
            title: "Success",
            description: `Successfully imported ${result.importedCount} events`,
            variant: "default",
          });

          if (result.errors && result.errors.length > 0) {
            toast({
              title: "Warning",
              description: `${result.errors.length} rows had errors during import`,
              variant: "destructive",
            });
          }

          return result;
        } catch (error) {
          console.error("Failed to import events:", error);
          toast({
            title: "Error",
            description:
              "Failed to import events. Please check your file format.",
            variant: "destructive",
          });
          set({ isImporting: false });
          throw error;
        }
      },

      downloadCanvas: async () => {
        set({ isDownloadingCanvas: true });
        try {
          const response = await api.get(
            `/annual-calendar/api/calendar/events/download-template`,
            {
              responseType: "blob",
            },
          );

          const blob = new Blob([response.data], {
            type:
              response.headers["content-type"] || "application/octet-stream",
          });
          const url = window.URL.createObjectURL(blob);

          const link = document.createElement("a");
          link.href = url;

          const contentDisposition = response.headers["content-disposition"];
          let filename = "schedule-import-template.xlsx";

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(
              /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
            );
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1].replace(/['"]/g, "");
            }
          }

          link.download = filename;
          document.body.appendChild(link);
          link.click();

          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          toast({
            title: "Success",
            description: "Import template downloaded successfully.",
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to download template:", error);
          toast({
            title: "Error",
            description:
              "Failed to download import template. Please try again.",
            variant: "destructive",
          });
          throw error;
        } finally {
          set({ isDownloadingCanvas: false });
        }
      },

      exportEvents: async (format: "csv" | "xlsx" = "xlsx") => {
        set({ isExporting: true });
        try {
          const currentYear = get().currentDate.getFullYear();
          const response = await api.get(
            `/annual-calendar/api/calendar/events/export`,
            {
              params: {
                year: currentYear,
                format,
              },
              responseType: "blob",
            },
          );

          const blob = new Blob([response.data], {
            type:
              format === "csv"
                ? "text/csv"
                : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = `calendar-events-${currentYear}.${format}`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          set({ isExporting: false });

          toast({
            title: "Success",
            description: `Events exported successfully as ${format.toUpperCase()}`,
            variant: "default",
          });
        } catch (error) {
          console.error("Failed to export events:", error);
          toast({
            title: "Error",
            description: "Failed to export events. Please try again.",
            variant: "destructive",
          });
          set({ isExporting: false });
          throw error;
        }
      },

      // UI state actions
      setCurrentDate: (date: Date) => {
        set({ currentDate: date });
      },

      setViewMode: (mode: CalendarViewMode) => {
        set({ viewMode: mode });
      },

      setSearchTerm: (term: string) => {
        set({ searchTerm: term });
        get().searchEvents(term);
      },

      setActiveFilters: (filters: EventFilter) => {
        set({ activeFilters: filters });
        get().applyFilters(filters);
      },

      setSelectedEventId: (id: string | null) => {
        set({ selectedEventId: id });
      },

      // History filter management actions
      setHistoryStartDate: (date: Date | null) => {
        set({ historyStartDate: date });
      },
      setHistoryEndDate: (date: Date | null) => {
        set({ historyEndDate: date });
      },
      setHistoryFilterType: (type: ApiHistoryEventType | "ALL") => {
        set({ historyFilterType: type });
      },

      // Modal management actions
      openEventModal: (eventId?: string, selectedDate?: Date) => {
        set({
          isEventModalOpen: true,
          editingEventId: eventId || null,
          selectedDate: selectedDate || null,
        });
      },

      closeEventModal: () => {
        set({
          isEventModalOpen: false,
          editingEventId: null,
          selectedDate: null,
        });
      },

      openEventDetailsModal: (eventId: string) => {
        set({
          isEventDetailsModalOpen: true,
          selectedEventId: eventId,
        });
      },

      closeEventDetailsModal: () => {
        set({
          isEventDetailsModalOpen: false,
          selectedEventId: null,
        });
      },

      openHistoryModal: () => {
        set({ isHistoryModalOpen: true });
        // Reset and fetch fresh history when modal opens
        get().fetchEventHistory(true);
      },

      closeHistoryModal: () => {
        set({ isHistoryModalOpen: false });
      },

      openImportModal: () => {
        set({ isImportModalOpen: true });
      },

      closeImportModal: () => {
        set({ isImportModalOpen: false });
      },

      // Search and filter actions
      searchEvents: (term: string) => {
        const { events } = get();

        if (!term.trim()) {
          set({ filteredEvents: events });
          return;
        }

        const searchLower = term.toLowerCase();
        const filtered = events.filter(
          (event) =>
            event.name.toLowerCase().includes(searchLower) ||
            event.description.toLowerCase().includes(searchLower),
        );

        set({ filteredEvents: filtered });
      },

      applyFilters: (filters: EventFilter) => {
        const { events, searchTerm } = get();
        let filtered = [...events];

        if (searchTerm.trim()) {
          const searchLower = searchTerm.toLowerCase();
          filtered = filtered.filter(
            (event) =>
              event.name.toLowerCase().includes(searchLower) ||
              event.description.toLowerCase().includes(searchLower),
          );
        }

        if (filters.categoryId && filters.categoryId.length > 0) {
          filtered = filtered.filter((event) =>
            filters.categoryId!.includes(event.category.id),
          );
        }

        if (filters.dateRange) {
          const { start, end } = filters.dateRange;
          filtered = filtered.filter((event) => {
            const eventStart = new Date(event.startDate);
            const eventEnd = new Date(event.endDate);

            return (
              (eventStart >= start && eventStart <= end) ||
              (eventEnd >= start && eventEnd <= end) ||
              (eventStart <= start && eventEnd >= end)
            );
          });
        }

        set({ filteredEvents: filtered });
      },

      resetFilters: () => {
        set({
          activeFilters: {},
          searchTerm: "",
          filteredEvents: get().events,
        });
      },

      // Navigation actions
      goToNextMonth: () => {
        const { currentDate } = get();
        const nextMonth = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          1,
        );
        set({ currentDate: nextMonth });
      },

      goToPreviousMonth: () => {
        const { currentDate } = get();
        const prevMonth = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() - 1,
          1,
        );
        set({ currentDate: prevMonth });
      },

      goToNextYear: () => {
        const { currentDate } = get();
        const nextYear = new Date(
          currentDate.getFullYear() + 1,
          currentDate.getMonth(),
          1,
        );
        set({ currentDate: nextYear });
        get().fetchCalendarSummary(nextYear.getFullYear());
        get().fetchEvents(nextYear.getFullYear());
      },

      goToPreviousYear: () => {
        const { currentDate } = get();
        const prevYear = new Date(
          currentDate.getFullYear() - 1,
          currentDate.getMonth(),
          1,
        );
        set({ currentDate: prevYear });
        get().fetchCalendarSummary(prevYear.getFullYear());
        get().fetchEvents(prevYear.getFullYear());
      },

      goToToday: () => {
        const today = new Date();
        set({ currentDate: today });
        get().fetchCalendarSummary(today.getFullYear());
        get().fetchEvents(today.getFullYear());
      },

      // Utility functions
      getEventById: (id: string) => {
        const { events } = get();
        return events.find((event) => event.id === id);
      },

      getEventsForDate: (date: Date) => {
        const { filteredEvents } = get();
        const targetDate = date.toISOString().split("T")[0];

        return filteredEvents.filter((event) => {
          const start = event.startDate;
          const end = event.endDate;
          return targetDate >= start && targetDate <= end;
        });
      },

      getTotalWorkingDays: () => {
        const { calendarSummary } = get();
        return calendarSummary?.workedDaysPlant || 268;
      },
    }),
    {
      name: "calendar-storage",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        currentDate: state.currentDate.toISOString(),
        viewMode: {
          mode: state.viewMode.mode,
          date: state.viewMode.date.toISOString(),
        },
        activeFilters: state.activeFilters,
        historyStartDate: state.historyStartDate?.toISOString() || null,
        historyEndDate: state.historyEndDate?.toISOString() || null,
        historyFilterType: state.historyFilterType,
        historyPageSize: state.historyPageSize,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          if (typeof state.currentDate === "string") {
            state.currentDate = new Date(state.currentDate);
          }
          if (state.viewMode && typeof state.viewMode.date === "string") {
            state.viewMode.date = new Date(state.viewMode.date);
          }
          if (typeof state.historyStartDate === "string") {
            state.historyStartDate = new Date(state.historyStartDate);
          }
          if (typeof state.historyEndDate === "string") {
            state.historyEndDate = new Date(state.historyEndDate);
          }
          if (!state.currentDate || isNaN(state.currentDate.getTime())) {
            state.currentDate = new Date();
          }
          if (!state.viewMode?.date || isNaN(state.viewMode.date.getTime())) {
            state.viewMode = { mode: "year", date: new Date() };
          }
        }
      },
    },
  ),
);

export default useCalendarStore;
