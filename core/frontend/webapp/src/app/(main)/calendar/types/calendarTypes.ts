// Enhanced types following Project 1 patterns
export type EventType =
  | "religious_holidays"
  | "national_holidays"
  | "unpaid_public_holiday"
  | "tlo"
  | "total_plant_holiday"
  | "ramadane"
  | "fourth_saturday";

export interface EventCategory {
  id: string;
  name: string;
  color: string;
  isWorkingHoliday: boolean;
  country: string;
  site: string;
}

export interface CalendarEvent {
  id: string;
  year: number;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  name: string;
  description: string;
  category: EventCategory; // Corrected: category is a nested object
  country: string;
  site: string;
  created_at?: Date;
  updated_at?: Date;
  created_by?: string;
}

export interface EventFormData {
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  name: string;
  description: string;
  categoryId: string;
}

// New interfaces for API response
export interface PerformedBy {
  id: string;
  name: string;
  country: string;
  site: string;
}

export interface ApiHistoryEventObject {
  id: string;
  year: number;
  startDate: string;
  endDate: string;
  name: string;
  description: string;
  country: string;
  site: string;
  category?: string;
}
export interface HistoryEventObject {
  id: string;
  year: string;
  startDate: string;
  endDate: string;
  name: string;
  description: string;
  category: string;
  country: string;
  site: string;
}

export enum ApiHistoryEventType {
  CREATED = "CREATED",
  UPDATED = "UPDATED",
  DELETED = "DELETED",
  IMPORTED = "IMPORTED",
}

// This is the type for the raw API response item
export interface RawApiEventHistoryItem {
  id: string;
  event: string; // Event title
  type: ApiHistoryEventType; // Action type
  performedBy: PerformedBy;
  performedAt: string; // ISO date string
  details: string;
  beforeObject?: ApiHistoryEventObject;
  afterObject?: ApiHistoryEventObject;
}

// This is the transformed type used in the Zustand store and passed to UI components
export interface EventHistoryItem {
  id: string;
  eventTitle: string;
  action: ApiHistoryEventType; // Keep the API type for filtering
  performedBy: PerformedBy;
  performedAt: Date;
  details: string;
  // Optional fields for UI display, derived from raw API data
  oldValue?: string;
  newValue?: string;
  changedFields?: ChangedFields[]; // Array of changed fields for better UI display
  // Keep before/after objects if needed for more complex UI logic
  beforeObject?: ApiHistoryEventObject;
  afterObject?: ApiHistoryEventObject;
  type: ApiHistoryEventType; // Optional type for additional categorization
}

export interface CalendarStats {
  totalEvents: number;
  totalWorkingDays: number;
  eventsByType: Record<EventType, number>;
  upcomingEvents: CalendarEvent[];
}

export interface CategoryCount {
  categoryName: string;
  isWorking: boolean;
  count: number;
}

export interface MonthSummary {
  month: number;
  name: string;
  totalDays: number;
  sundays: number;
  saturdays: number;
  workedDaysPlant: number;
  nonWorkedDaysPlant: number;
  categoryCounts: CategoryCount[];
}

export interface CalendarSummary {
  id: string;
  year: number;
  country: string;
  site: string;
  status: string;
  totalDays: number;
  sundays: number;
  saturdays: number;
  categoryCounts: CategoryCount[];
  totalPlantHolidays: number;
  workedDaysPlant: number;
  lastCalculated: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  monthSummaries: MonthSummary[];
}

export interface ImportResult {
  importedCount: number;
  errors: Array<{
    rowNumber: number;
    message: string;
  }>;
}

export interface CalendarViewMode {
  mode: "month" | "year";
  date: Date;
}

export interface EventFilter {
  categoryId?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchTerm?: string;
}

// UI Component Props Types
export interface TabOption {
  key: string;
  label: string;
}

export interface FilterConfig {
  key: string;
  label: string;
  type: "select" | "multiselect" | "daterange";
  options?: { label: string; value: string }[];
}

export interface ChangedFields {
  field: string;
  oldValue: string;
  newValue: string;
}
