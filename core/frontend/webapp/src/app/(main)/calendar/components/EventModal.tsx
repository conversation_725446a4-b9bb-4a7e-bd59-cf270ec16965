"use client";

import type React from "react";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import useCalendarStore from "../store/calendarStore";
import type { EventFormData } from "../types/calendarTypes";
import { useTranslations } from "next-intl"; // Import useTranslations
import CustomIcon from "@/components/common/CustomIcons";

const EventModal: React.FC = () => {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations

  const {
    // State
    isEventModalOpen,
    editingEventId,
    selectedDate,
    eventCategories,
    isEventLoading,

    // Actions
    closeEventModal,
    createEvent,
    updateEvent,
    getEventById,
  } = useCalendarStore();

  const eventToEdit = editingEventId ? getEventById(editingEventId) : null;
  const [formData, setFormData] = useState<EventFormData>({
    startDate: "",
    endDate: "",
    name: "",
    description: "",
    categoryId: "",
  });

  useEffect(() => {
    if (eventToEdit) {
      setFormData({
        startDate: eventToEdit.startDate,
        endDate: eventToEdit.endDate,
        name: eventToEdit.name,
        description: eventToEdit.description,
        categoryId: eventToEdit.category.id,
      });
    } else {
      const dateStr = selectedDate
        ? selectedDate.toISOString().split("T")[0]
        : new Date().toISOString().split("T")[0];
      setFormData({
        startDate: dateStr,
        endDate: dateStr,
        name: "",
        description: "",
        categoryId: eventCategories[0]?.id || "",
      });
    }
  }, [eventToEdit, selectedDate, eventCategories, isEventModalOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      alert(t("eventModal.nameRequiredAlert"));
      return;
    }

    try {
      if (eventToEdit) {
        await updateEvent(eventToEdit.id, formData);
      } else {
        await createEvent(formData);
      }
      closeEventModal();
    } catch (error) {
      console.error("Failed to save event:", error);
      alert(t("eventModal.saveErrorAlert"));
    }
  };

  return (
    <Dialog open={isEventModalOpen} onOpenChange={closeEventModal}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="border-b pb-4 mb-4">
            {eventToEdit
              ? t("eventModal.editTitle")
              : t("eventModal.createTitle")}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Icon */}
          <div className="flex justify-center">
            <div className="flex items-center justify-center">
              <CustomIcon
                name="calendarModal"
                style={{ width: "50%", height: "50%" }}
              />
            </div>
          </div>{" "}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">{t("eventModal.eventNameLabel")}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder={t("eventModal.eventNamePlaceholder")}
                required
                disabled={isEventLoading}
              />
            </div>
            <div>
              <Label>{t("eventModal.categoryLabel")}</Label>
              <Select
                value={formData.categoryId}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, categoryId: value }))
                }
                disabled={isEventLoading}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t("eventModal.selectCategoryPlaceholder")}
                  />
                </SelectTrigger>
                <SelectContent>
                  {eventCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <span className="flex items-center gap-2">
                        <span
                          className="w-3 h-3 rounded"
                          style={{ backgroundColor: category.color }}
                        ></span>
                        {category.name}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>{t("eventModal.startDateLabel")}</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.startDate && "text-muted-foreground",
                    )}
                    disabled={isEventLoading}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.startDate ? (
                      format(new Date(formData.startDate), "PPP")
                    ) : (
                      <span>{t("eventModal.pickDatePlaceholder")}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={
                      formData.startDate
                        ? new Date(formData.startDate)
                        : undefined
                    }
                    onSelect={(date) =>
                      date &&
                      setFormData((prev) => ({
                        ...prev,
                        startDate: date.toISOString().split("T")[0],
                      }))
                    }
                    initialFocus
                    className="p-3"
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label>{t("eventModal.endDateLabel")}</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.endDate && "text-muted-foreground",
                    )}
                    disabled={isEventLoading}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.endDate ? (
                      format(new Date(formData.endDate), "PPP")
                    ) : (
                      <span>{t("eventModal.pickDatePlaceholder")}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={
                      formData.endDate ? new Date(formData.endDate) : undefined
                    }
                    onSelect={(date) =>
                      date &&
                      setFormData((prev) => ({
                        ...prev,
                        endDate: date.toISOString().split("T")[0],
                      }))
                    }
                    initialFocus
                    className="p-3"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div>
            <Label htmlFor="description">
              {t("eventModal.descriptionLabel")}
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder={t("eventModal.descriptionPlaceholder")}
              rows={3}
              disabled={isEventLoading}
            />
          </div>
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={closeEventModal}
              disabled={isEventLoading}
            >
              {t("cancelButton")}
            </Button>
            <Button type="submit" disabled={isEventLoading}>
              {isEventLoading
                ? eventToEdit
                  ? t("eventModal.updatingButton")
                  : t("eventModal.creatingButton")
                : eventToEdit
                  ? t("eventModal.updateButton")
                  : t("eventModal.saveButton")}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EventModal;
