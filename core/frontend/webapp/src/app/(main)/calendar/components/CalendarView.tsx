"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import useCalendarStore from "../store/calendarStore";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  getDay,
  isSameMonth,
  isToday,
  startOfWeek,
  endOfWeek,
  getWeek,
} from "date-fns";
import type { CalendarEvent } from "../types/calendarTypes";
import { useTranslations } from "next-intl"; // Import useTranslations

const CalendarView: React.FC = () => {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations

  const {
    // State
    currentDate,
    viewMode,

    // Actions
    setCurrentDate,
    setSelectedEventId,
    openEventModal,
    openEventDetailsModal,
    getEventsForDate,
  } = useCalendarStore();

  const [showHiddenEvents, setShowHiddenEvents] = React.useState<{
    date: Date;
    events: CalendarEvent[];
  } | null>(null);

  const handleDayClick = (date: Date) => {
    openEventModal(undefined, date);
  };

  const handleEventClick = (eventId: string) => {
    setSelectedEventId(eventId);
    // openEventModal(eventId)
    openEventDetailsModal(eventId);
  };

  const handleDateChange = (date: Date) => {
    setCurrentDate(date);
    if (viewMode.mode === "year") {
      // Switch to month view when clicking on a month
      // setViewMode({ mode: "month", date })
    }
  };

  const renderMonthView = (
    monthDate: Date,
    isFullSize = true,
    onTitleClick?: () => void,
  ) => {
    const monthStart = startOfMonth(monthDate);
    const monthEnd = endOfMonth(monthDate);
    const weekOptions = { weekStartsOn: 1 as const };

    const calendarStart = startOfWeek(monthStart, weekOptions);
    const calendarEnd = endOfWeek(monthEnd, weekOptions);
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

    const weeks: Date[][] = [];
    for (let i = 0; i < days.length; i += 7) {
      weeks.push(days.slice(i, i + 7));
    }

    return (
      <div className={`${isFullSize ? "h-full" : "h-72"}`}>
        <div className="mb-2">
          <div className="flex items-center justify-between mb-2">
            <div className="flex-1 flex justify-center">
              <h3
                className={`font-semibold ${isFullSize ? "text-xl" : "text-sm"} text-center ${
                  !isFullSize ? "cursor-pointer hover:underline" : ""
                }`}
                onClick={!isFullSize ? onTitleClick : undefined}
              >
                {format(monthDate, "MMMM")}
              </h3>
            </div>

            <div className="flex justify-end space-x-2 items-center">
              <div className="bg-white border border-[#F84018] text-[#F84018] text-sm font-normal w-6 h-6 rounded flex items-center justify-center">
                1
              </div>
              <div className="bg-[#4762F1] text-white text-sm font-normal w-6 h-6 rounded flex items-center justify-center">
                23
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-8 gap-1 mb-2">
          <div
            className={`text-center font-medium text-gray-500 ${isFullSize ? "text-sm" : "text-xs"}`}
          >
            {t("calendarView.weekHeader")}
          </div>
          {[
            t("calendarView.mon"),
            t("calendarView.tue"),
            t("calendarView.wed"),
            t("calendarView.thu"),
            t("calendarView.fri"),
            t("calendarView.sat"),
            t("calendarView.sun"),
          ].map((day, index) => (
            <div
              key={day}
              className={`text-center font-medium ${isFullSize ? "text-sm" : "text-xs"} ${
                index === 6 ? "text-red-600" : "text-gray-500"
              }`}
            >
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-8 gap-1 flex-1">
          {weeks.map((week, weekIndex) => (
            <React.Fragment key={weekIndex}>
              <div
                className={`${isFullSize ? "min-h-24 p-1" : "min-h-8 p-0.5"} bg-gray-50 flex items-center justify-center`}
              >
                <span
                  className={`text-blue-600 font-medium ${isFullSize ? "text-sm" : "text-xs"}`}
                >
                  W{String(getWeek(week[0], weekOptions)).padStart(2, "0")}
                </span>
              </div>

              {week.map((day, dayIndex) => {
                const dayEvents = getEventsForDate(day);
                const isCurrentMonth = isSameMonth(day, monthDate);
                const isCurrentDay = isToday(day);

                return (
                  <div
                    key={`${weekIndex}-${dayIndex}`}
                    className={`${isFullSize ? "min-h-24 p-1" : "min-h-8 p-0.5"} cursor-pointer hover:bg-blue-50 transition-colors flex flex-col items-center
                    ${isCurrentDay ? "bg-blue-100 border-blue-300" : ""}
                    ${!isCurrentMonth ? "bg-gray-50 text-gray-400" : ""}
                    ${getDay(day) === 0 ? "text-red-600" : ""}
                    bg-gray-50`}
                    onClick={() => handleDayClick(day)}
                  >
                    <div
                      className={`text-center ${isFullSize ? "text-sm mb-1" : "text-xs"} font-medium`}
                    >
                      <span
                        className={`inline-block ${isFullSize ? "text-sm" : "text-xs"} font-normal`}
                      >
                        {format(day, "d")}
                      </span>
                    </div>

                    {dayEvents.length > 0 && (
                      <div
                        className={`flex flex-wrap items-center justify-center gap-x-1 ${isFullSize ? "" : "mt-1"}`}
                      >
                        {dayEvents.slice(0, isFullSize ? 4 : 2).map((event) => (
                          <div
                            key={event.id}
                            title={event.name}
                            className={`text-[10px] px-1 py-0.5 rounded truncate cursor-pointer hover:opacity-80 w-2 h-2`}
                            style={{ backgroundColor: event.category.color }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEventClick(event.id);
                            }}
                          ></div>
                        ))}
                        {dayEvents.length > (isFullSize ? 4 : 2) && (
                          <div
                            className="text-[10px] rounded-full w-2 h-2 text-gray-500 px-1 hover:underline cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              const hiddenEvents = dayEvents.slice(
                                isFullSize ? 4 : 2,
                              );
                              setShowHiddenEvents({
                                date: day,
                                events: hiddenEvents,
                              });
                            }}
                          >
                            +{dayEvents.length - (isFullSize ? 4 : 2)}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  const renderMonth = (monthDate: Date, isFullSize = true) => {
    const monthStart = startOfMonth(monthDate);
    const monthEnd = endOfMonth(monthDate);
    const weekOptions = { weekStartsOn: 1 as const };

    const calendarStart = startOfWeek(monthStart, weekOptions);
    const calendarEnd = endOfWeek(monthEnd, weekOptions);
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

    const weeks: Date[][] = [];
    for (let i = 0; i < days.length; i += 7) {
      weeks.push(days.slice(i, i + 7));
    }

    return (
      <div className={`${isFullSize ? "h-full" : "h-72"}`}>
        <div className="bg-white mb-4">
          {/* Header row with empty space for week column + day names */}
          <div className="flex">
            {/* Empty space to match week number section */}
            <div className="w-12">
              {/* Empty header space for week column alignment */}
            </div>

            {/* 7-column grid for day names with border */}
            <div className="flex-1 grid grid-cols-7 border border-gray-300 rounded-tl-lg rounded-tr-lg">
              {[
                t("calendarView.mon"),
                t("calendarView.tue"),
                t("calendarView.wed"),
                t("calendarView.thu"),
                t("calendarView.fri"),
                t("calendarView.sat"),
                t("calendarView.sun"),
              ].map((day, index) => (
                <div
                  key={day}
                  className={`p-3 text-center font-medium text-sm  ${
                    index < 6 ? "border-r border-gray-300" : ""
                  } ${index === 6 ? "text-red-600" : "text-gray-700"}`}
                >
                  {day.toUpperCase()}
                </div>
              ))}
            </div>
          </div>

          {/* Calendar grid with week numbers on the side */}
          <div>
            {weeks.map((week, weekIndex) => (
              <div key={weekIndex} className="flex">
                {/* Week number section on the left side - NO BORDER */}
                <div className="w-12 flex items-center justify-center py-2">
                  <span className="text-blue-600 font-medium text-sm">
                    W{String(getWeek(week[0], weekOptions)).padStart(2, "0")}
                  </span>
                </div>

                {/* 7-column grid for days with borders */}
                <div
                  className={`flex-1 grid grid-cols-7 border-l border-r border-b border-gray-300
                  ${weekIndex === weeks.length - 1 ? "rounded-b-lg" : ""}`}
                >
                  {week.map((day, dayIndex) => {
                    const dayEvents = getEventsForDate(day);
                    const isCurrentMonth = isSameMonth(day, monthDate);
                    const isCurrentDay = isToday(day);

                    return (
                      <div
                        key={`${weekIndex}-${dayIndex}`}
                        className={`${isFullSize ? "min-h-[120px] p-2" : "min-h-16 p-1"} cursor-pointer hover:bg-blue-50 transition-colors ${
                          dayIndex < 6 ? "border-r border-gray-300" : ""
                        } ${isCurrentDay ? "bg-blue-100" : "bg-white"}
                      ${!isCurrentMonth ? "bg-gray-50 text-gray-400" : ""}
                      ${getDay(day) === 0 ? "text-red-600" : "text-gray-900"}
                      ${weekIndex === weeks.length - 1 ? "rounded-b-lg" : ""}
                      flex flex-col`}
                        onClick={() => handleDayClick(day)}
                      >
                        {/* Day number */}
                        <div className="text-left mb-1">
                          <span
                            className={`${isFullSize ? "text-sm" : "text-xs"} font-medium`}
                          >
                            {format(day, "d")}
                          </span>
                        </div>

                        {/* Events */}
                        {dayEvents.length > 0 && (
                          <div className="flex-1 flex flex-col gap-1">
                            {dayEvents
                              .slice(0, isFullSize ? 3 : 1)
                              .map((event) => (
                                <div
                                  key={event.id}
                                  className={`text-xs px-2 py-1 rounded text-white cursor-pointer hover:opacity-80 truncate`}
                                  style={{
                                    backgroundColor: event.category.color,
                                  }}
                                  title={event.name}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEventClick(event.id);
                                  }}
                                >
                                  {event.name}
                                </div>
                              ))}
                            {dayEvents.length > (isFullSize ? 3 : 1) && (
                              <div
                                className="text-xs text-gray-500 cursor-pointer hover:underline"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const hiddenEvents = dayEvents.slice(
                                    isFullSize ? 3 : 1,
                                  );
                                  setShowHiddenEvents({
                                    date: day,
                                    events: hiddenEvents,
                                  });
                                }}
                              >
                                +{dayEvents.length - (isFullSize ? 3 : 1)} more
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderYearView = () => {
    const months = Array.from({ length: 12 }, (_, i) => {
      return new Date(currentDate.getFullYear(), i, 1);
    });

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
        {months.map((month, index) => (
          <Card
            key={index}
            className="p-3 hover:shadow-md transition-shadow cursor-default"
          >
            {renderMonthView(month, false, () => handleDateChange(month))}
          </Card>
        ))}
      </div>
    );
  };

  return (
    <>
      {viewMode.mode === "year" ? (
        renderYearView()
      ) : (
        <div className="h-full bg-white">
          <div className="bg-white  shadow p-6 hover:shadow-md transition-shadow mb-4">
            {renderMonth(currentDate, true)}
          </div>
        </div>
      )}

      {/* Hidden Events Modal */}
      {showHiddenEvents && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 shadow-lg w-[300px] max-h-[400px] overflow-auto">
            <h2 className="text-lg font-bold mb-2">
              {format(showHiddenEvents.date, "PPP")}
            </h2>
            <ul className="space-y-2">
              {showHiddenEvents.events.map((event) => (
                <li
                  key={event.id}
                  className="p-2 rounded cursor-pointer"
                  style={{ backgroundColor: event.category.color }}
                  onClick={() => {
                    handleEventClick(event.id);
                    setShowHiddenEvents(null);
                  }}
                >
                  {event.name}
                </li>
              ))}
            </ul>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setShowHiddenEvents(null)}
                className="text-sm text-blue-500 hover:underline"
              >
                {t("closeButton")}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CalendarView;
