"use client";

import { Calendar, User, Edit, Trash2, Plus } from "lucide-react";
import { useTranslations } from "next-intl"; // Import useTranslations

interface EventHistoryItemProps {
  dateTime: string;
  event: string;
  action: string;
  actionType: "changed" | "deleted" | "added";
  oldValue?: string[];
  newValue?: string[];
  doneBy: string;
}

const EventHistoryItem = ({
  dateTime,
  event,
  action,
  actionType,
  oldValue,
  newValue,
  doneBy,
}: EventHistoryItemProps) => {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations
  console.log("new value:", newValue);
  console.log("old value:", oldValue);
  const getActionIcon = () => {
    switch (actionType) {
      case "changed":
        return <Edit className="h-4 w-4 text-blue-500" />;
      case "deleted":
        return <Trash2 className="h-4 w-4 text-red-500" />;
      case "added":
        return <Plus className="h-4 w-4 text-green-500" />;
      default:
        return <Edit className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionColor = () => {
    switch (actionType) {
      case "changed":
        return "text-blue-600";
      case "deleted":
        return "text-red-600";
      case "added":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const getBorderColor = () => {
    switch (actionType) {
      case "changed":
        return "border-l-blue-500";
      case "deleted":
        return "border-l-red-500";
      case "added":
        return "border-l-green-500";
      default:
        return "border-l-gray-300";
    }
  };

  return (
    <div
      className={`bg-white border border-gray-200 border-l-4 ${getBorderColor()} rounded-lg p-4 mb-3 shadow-sm hover:shadow-md transition-shadow`}
    >
      {/* Date & Time */}
      <div className="flex items-center gap-2 mb-2 text-sm text-gray-600">
        <div className="flex items-center gap-2 w-40">
          <Calendar className="h-4 w-4" />
          <span>{t("eventHistoryItem.dateTimeLabel")}</span>
        </div>
        <span className="font-medium text-gray-900">{dateTime}</span>
      </div>

      {/* Event */}
      <div className="flex items-center gap-2 mb-2 text-sm text-gray-600">
        <div className="flex items-center gap-2 w-40">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span>{t("eventHistoryItem.eventLabel")}</span>
        </div>
        <span className="font-medium text-gray-900 truncate" title={event}>
          {event}
        </span>
      </div>

      {/* Action */}
      <div className="flex items-center gap-2 mb-2 text-sm text-gray-600">
        <div className="flex items-center gap-2 w-40">
          {getActionIcon()}
          <span>{t("eventHistoryItem.actionLabel")}</span>
        </div>
        <span className={`font-medium ${getActionColor()}`}>{action}</span>
      </div>

      {/* Old Value */}
      {oldValue && (
        <div className="flex items-start gap-2 mb-1 text-sm text-gray-600">
          <div className="w-40 flex-shrink-0" />
          <div className="flex items-center gap-2">
            <span className="font-medium">
              {t("eventHistoryItem.oldValueLabel")}
            </span>
            {oldValue.map((oldValue, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-red-50 border border-red-200 text-red-800 rounded text-xs max-w-xs truncate"
                title={oldValue?.toString()}
              >
                {oldValue?.toString()}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* New Value */}
      {newValue && (
        <div className="flex items-start gap-2 mb-2 text-sm text-gray-600">
          <div className="w-40 flex-shrink-0" />
          <div className="flex items-center gap-2">
            <span className="font-medium">
              {t("eventHistoryItem.newValueLabel")}
            </span>
            {newValue.map((newValue, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-green-50 border border-green-200 text-green-800 rounded text-xs max-w-xs truncate"
                title={newValue?.toString()}
              >
                {newValue?.toString()}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Done By */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="flex items-center gap-2 w-40">
          <User className="h-4 w-4" />
          <span>{t("eventHistoryItem.doneByLabel")}</span>
        </div>
        <span className="font-medium text-gray-900">{doneBy}</span>
      </div>
    </div>
  );
};

export default EventHistoryItem;
