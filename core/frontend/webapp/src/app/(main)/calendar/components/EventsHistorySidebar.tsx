"use client";

import { useState, useEffect } from "react";
import { X, CalendarIcon, Info } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { format } from "date-fns";
import type { DateRange } from "react-day-picker";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import EventHistoryItem from "./EventHistoryItem";
import useCalendarStore from "../store/calendarStore";
import type {
  ApiHistoryEventType,
  ChangedFields,
} from "../types/calendarTypes"; // Import the new type
import { useTranslations } from "next-intl"; // Import useTranslations

interface EventsHistorySidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const EventsHistorySidebar = ({
  isOpen,
  onClose,
}: EventsHistorySidebarProps) => {
  const t = useTranslations("rolesPermissions");

  const {
    // State
    eventHistory,
    allEventHistory,
    isHistoryLoading,
    isLoadingMoreHistory,
    hasMoreHistory,
    displayedHistoryCount,
    historyStartDate,
    historyEndDate,
    historyFilterType,

    // Actions
    fetchEventHistory,
    loadMoreHistory,
    setHistoryStartDate,
    setHistoryEndDate,
    setHistoryFilterType,
  } = useCalendarStore();

  // Local state for the date range picker, synced with store
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    if (historyStartDate && historyEndDate) {
      return { from: historyStartDate, to: historyEndDate };
    }
    const defaultFrom = new Date();
    defaultFrom.setMonth(defaultFrom.getMonth() - 1);
    const defaultTo = new Date();
    return { from: defaultFrom, to: defaultTo };
  });

  // Sync local dateRange state with store's historyStartDate/EndDate
  useEffect(() => {
    setHistoryStartDate(dateRange?.from || null);
    setHistoryEndDate(dateRange?.to || null);
  }, [dateRange, setHistoryStartDate, setHistoryEndDate]);

  // Fetch history when sidebar opens or filters change
  useEffect(() => {
    if (isOpen) {
      fetchEventHistory(true); // Reset and fetch fresh data when filters change
    }
  }, [
    isOpen,
    fetchEventHistory,
    historyStartDate,
    historyEndDate,
    historyFilterType,
  ]);

  // Handle load more
  const handleLoadMore = () => {
    loadMoreHistory();
  };

  // Grouping logic with null check
  const groupedEvents = (eventHistory || []).reduce(
    (groups, event) => {
      const eventDate = new Date(event.performedAt);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let timeGroup = t("eventsHistorySidebar.earlierGroup");
      if (eventDate.toDateString() === today.toDateString()) {
        timeGroup = t("eventsHistorySidebar.todayGroup");
      } else if (eventDate.toDateString() === yesterday.toDateString()) {
        timeGroup = t("eventsHistorySidebar.yesterdayGroup");
      } else {
        timeGroup = format(eventDate, "yyyy-MM-dd");
      }

      if (!groups[timeGroup]) groups[timeGroup] = [];
      groups[timeGroup].push(event);
      return groups;
    },
    {} as Record<string, typeof eventHistory>,
  );

  const getActionDisplayName = (action: ApiHistoryEventType) => {
    switch (action) {
      case "CREATED":
        return t("eventsHistorySidebar.actionCreated");
      case "UPDATED":
        return t("eventsHistorySidebar.actionUpdated");
      case "DELETED":
        return t("eventsHistorySidebar.actionDeleted");
      case "IMPORTED":
        return t("eventsHistorySidebar.actionImported");
      default:
        return action;
    }
  };

  function getChangedFieldsText(changes: ChangedFields[]): string {
    if (!changes || changes.length === 0) return "No changes";
    const fields = changes.map((c) => c.field).join(", ");
    return `changed fields: ${fields}`;
  }

  function getOldValues(changes: ChangedFields[]): string[] {
    if (!changes) return [];
    return changes.map((c) => String(c.oldValue));
  }

  function getNewValues(changes: ChangedFields[]): string[] {
    if (!changes) return [];
    return changes.map((c) => String(c.newValue));
  }

  const getActionType = (
    action: ApiHistoryEventType,
  ): "changed" | "deleted" | "added" => {
    switch (action) {
      case "UPDATED":
      case "IMPORTED":
        return "changed";
      case "DELETED":
        return "deleted";
      case "CREATED":
        return "added";
      default:
        return "changed";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* Sidebar */}
      <div className="relative ml-auto w-100 h-full bg-white shadow-xl overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {t("eventsHistorySidebar.title", {
                year: new Date().getFullYear(),
              })}
            </h2>
            {allEventHistory && allEventHistory.length > 0 && (
              <p className="text-sm text-gray-500">
                Showing {displayedHistoryCount} of {allEventHistory.length}{" "}
                items
              </p>
            )}
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Info Banner */}
        <div className="p-4 border-b">
          <p className="text-sm text-gray-700 bg-[#F7F7F7] p-4 rounded-lg">
            <Info className="h-4 w-4 text-gray-500 inline mr-2" />
            {t("eventsHistorySidebar.infoText")}
          </p>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-6 p-4">
          {/* Action Filter */}
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              {t("eventsHistorySidebar.actionLabel")}
            </label>
            <Select
              value={historyFilterType}
              onValueChange={(value: ApiHistoryEventType | "ALL") =>
                setHistoryFilterType(value)
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue
                  placeholder={t(
                    "eventsHistorySidebar.selectActionPlaceholder",
                  )}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">
                  {t("eventsHistorySidebar.allActions")}
                </SelectItem>
                <SelectItem value="CREATED">
                  {t("eventsHistorySidebar.actionCreated")}
                </SelectItem>
                <SelectItem value="UPDATED">
                  {t("eventsHistorySidebar.actionUpdated")}
                </SelectItem>
                <SelectItem value="DELETED">
                  {t("eventsHistorySidebar.actionDeleted")}
                </SelectItem>
                <SelectItem value="IMPORTED">
                  {t("eventsHistorySidebar.actionImported")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filter */}
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              {t("eventsHistorySidebar.dateLabel")}
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-[300px] justify-start text-left font-normal",
                    !dateRange?.from && "text-muted-foreground",
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange?.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} -{" "}
                        {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>{t("eventsHistorySidebar.pickDatePlaceholder")}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange?.from}
                  selected={dateRange}
                  onSelect={setDateRange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Events List */}
        <div className="flex-1 overflow-y-auto p-4">
          {isHistoryLoading && (!eventHistory || eventHistory.length === 0) ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">
                {t("eventsHistorySidebar.loadingHistory")}
              </span>
            </div>
          ) : Object.keys(groupedEvents).length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <Info className="w-8 h-8 mx-auto mb-2" />
              <p>{t("eventsHistorySidebar.noHistoryFound")}</p>
              {historyFilterType !== "ALL" && (
                <p className="text-sm mt-1">
                  {t("eventsHistorySidebar.tryChangingFilter")}
                </p>
              )}
            </div>
          ) : (
            Object.entries(groupedEvents).map(([timeGroup, events]) => (
              <div key={timeGroup} className="mb-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-4 h-4 rounded-full border-2 border-blue-500 bg-blue-500" />
                  <h3 className="font-medium text-gray-900">{timeGroup}</h3>
                  <span className="text-sm text-gray-500">
                    ({events.length})
                  </span>
                </div>

                {events.map((event) => (
                  <EventHistoryItem
                    key={event.id}
                    dateTime={format(
                      new Date(event.performedAt),
                      "dd MMM yyyy, HH:mm",
                    )}
                    event={
                      event.details ||
                      event.details?.split(". ")[0] ||
                      "Unknown event"
                    }
                    action={
                      event.type === "UPDATED"
                        ? getChangedFieldsText(event.changedFields || [])
                        : getActionDisplayName(event.type)
                    }
                    actionType={getActionType(event.type)}
                    oldValue={
                      event.changedFields && event.type === "UPDATED"
                        ? getOldValues(event.changedFields || [])
                        : undefined
                    }
                    newValue={
                      event.changedFields && event.type === "UPDATED"
                        ? getNewValues(event.changedFields || [])
                        : undefined
                    }
                    doneBy={event.performedBy?.name || "Unknown user"}
                  />
                ))}
              </div>
            ))
          )}

          {/* Loading more indicator */}
          {isLoadingMoreHistory && (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading more...</span>
            </div>
          )}
        </div>

        {/* Load More */}
        <div className="p-4 border-t">
          <Button
            variant="outline"
            className="w-full bg-black text-white hover:bg-gray-800 disabled:bg-gray-400 disabled:text-gray-600"
            disabled={!hasMoreHistory || isLoadingMoreHistory}
            onClick={handleLoadMore}
          >
            {isLoadingMoreHistory
              ? t("eventsHistorySidebar.loadingButton")
              : hasMoreHistory
                ? t("eventsHistorySidebar.loadMoreButton")
                : "No more items to load"}
          </Button>
          {!hasMoreHistory && eventHistory && eventHistory.length > 0 && (
            <p className="text-center text-sm text-gray-500 mt-2">
              You ve reached the end of the history
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default EventsHistorySidebar;
