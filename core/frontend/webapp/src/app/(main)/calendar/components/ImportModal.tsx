"use client";

import type React from "react";
import { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UploadCloud, FileText, FileSpreadsheet, XCircle } from "lucide-react";
import useCalendarStore from "../store/calendarStore";
import { toast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl"; // Import useTranslations

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ImportModal({ isOpen, onClose }: ImportModalProps) {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations

  const { importEvents, isImporting } = useCalendarStore();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileExtension = file.name.split(".").pop()?.toLowerCase();
      if (fileExtension === "csv" || fileExtension === "xlsx") {
        setSelectedFile(file);
      } else {
        setSelectedFile(null);
        toast({
          title: t("importModal.invalidFileTypeTitle"),
          description: t("importModal.invalidFileTypeDescription"),
          variant: "destructive",
        });
        if (fileInputRef.current) {
          fileInputRef.current.value = ""; // Clear the file input
        }
      }
    }
  };

  const handleImport = async () => {
    if (selectedFile) {
      try {
        const result = await importEvents(selectedFile);
        if (result.errors && result.errors.length > 0) {
          toast({
            title: t("importModal.importCompletedWithErrorsTitle"),
            description: t("importModal.importCompletedWithErrorsDescription", {
              importedCount: result.importedCount,
              errorsLength: result.errors.length,
            }),
            variant: "warning",
          });
        } else {
          toast({
            title: t("importModal.importSuccessfulTitle"),
            description: t("importModal.importSuccessfulDescription", {
              importedCount: result.importedCount,
            }),
            variant: "default",
          });
        }
        setSelectedFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = ""; // Clear the file input
        }
        onClose();
      } catch (error) {
        // Error toast is already handled by the store
        console.error("Import failed:", error);
      }
    } else {
      toast({
        title: t("importModal.noFileSelectedTitle"),
        description: t("importModal.noFileSelectedDescription"),
        variant: "warning",
      });
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; // Clear the file input
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UploadCloud className="w-5 h-5" /> {t("importModal.title")}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="file">{t("importModal.uploadFileLabel")}</Label>
            <Input
              id="file"
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" // Allow CSV and XLSX
              className="cursor-pointer"
            />
            <p className="text-sm text-gray-500 mt-1">
              {t("importModal.acceptedFormats")}
            </p>
          </div>
          {selectedFile && (
            <div className="flex items-center gap-2 text-sm text-gray-700">
              {selectedFile.name.endsWith(".csv") ? (
                <FileText className="w-4 h-4 text-blue-500" />
              ) : (
                <FileSpreadsheet className="w-4 h-4 text-green-500" />
              )}
              <span>{selectedFile.name}</span>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSelectedFile(null)}
                className="h-auto w-auto p-1"
              >
                <XCircle className="w-4 h-4 text-red-500" />
              </Button>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isImporting}
          >
            {t("cancelButton")}
          </Button>
          <Button
            onClick={handleImport}
            disabled={!selectedFile || isImporting}
          >
            {isImporting
              ? t("importModal.importingButton")
              : t("importModal.importButton")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
