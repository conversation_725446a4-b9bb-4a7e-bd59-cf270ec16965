"use client";

import type React from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { format } from "date-fns";
import useCalendarStore from "../store/calendarStore";
import { useTranslations } from "next-intl"; // Import useTranslations

const EventDetailsModal: React.FC = () => {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations

  const {
    // State
    isEventDetailsModalOpen,
    selectedEventId,
    isEventLoading,

    // Actions
    closeEventDetailsModal,
    openEventModal,
    openDeleteModal,
    getEventById,
  } = useCalendarStore();

  const selectedEvent = selectedEventId ? getEventById(selectedEventId) : null;

  const handleEdit = () => {
    if (selectedEvent) {
      closeEventDetailsModal();
      openEventModal(selectedEvent.id);
    }
  };

  const handleDelete = () => {
    if (selectedEvent) {
      openDeleteModal(selectedEvent.id);
    }
  };

  if (!selectedEvent) return null;

  const eventDate = new Date(selectedEvent.startDate);
  const dayNumber = format(eventDate, "d");
  const formattedDate = format(eventDate, "MMMM d, yyyy");

  return (
    <ReusableDialog
      isOpen={isEventDetailsModalOpen}
      onClose={closeEventDetailsModal}
      title={selectedEvent.name}
      size="xl"
      id="event-details-modal"
      footer={
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={closeEventDetailsModal}
            disabled={isEventLoading}
            className="px-6 bg-transparent"
          >
            {t("closeButton")}
          </Button>
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              onClick={handleDelete}
              disabled={isEventLoading}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 px-4"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {t("deleteButton")}
            </Button>
            <Button
              onClick={handleEdit}
              disabled={isEventLoading}
              className="bg-black text-white hover:bg-gray-800 px-6"
            >
              <Edit className="h-4 w-4 mr-2" />
              {t("editButton")}
            </Button>
          </div>
        </div>
      }
    >
      {/* Calendar Icon */}
      <div className="flex justify-center pb-6">
        <div className="relative">
          <div className="w-16 h-16 bg-white border-2 border-gray-200 rounded-lg shadow-sm flex flex-col items-center justify-center">
            {/* Calendar top bar */}
            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-12 h-3 bg-red-500 rounded-t-md"></div>
            {/* Day number */}
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {dayNumber}
            </span>
          </div>
        </div>
      </div>

      {/* Event Details */}
      <div className="px-6 space-y-4">
        {/* Title */}
        <div>
          <label className="text-sm font-medium text-gray-600">
            {t("eventDetailsModal.titleLabel")}
          </label>
          <p className="text-base text-gray-900 mt-1">{selectedEvent.name}</p>
        </div>

        {/* Date */}
        <div>
          <label className="text-sm font-medium text-gray-600">
            {t("eventDetailsModal.dateLabel")}
          </label>
          <p className="text-base text-gray-900 mt-1">{formattedDate}</p>
        </div>

        {/* Type */}
        <div>
          <label className="text-sm font-medium text-gray-600">
            {t("eventDetailsModal.typeLabel")}
          </label>
          <p className="text-base text-gray-900 mt-1">
            {selectedEvent.category.name}
          </p>
        </div>

        {/* Description */}
        {selectedEvent.description && (
          <div>
            <label className="text-sm font-medium text-gray-600">
              {t("eventDetailsModal.descriptionLabel")}
            </label>
            <p className="text-base text-gray-900 mt-1 leading-relaxed">
              {selectedEvent.description}
            </p>
          </div>
        )}
      </div>
    </ReusableDialog>
  );
};

export default EventDetailsModal;
