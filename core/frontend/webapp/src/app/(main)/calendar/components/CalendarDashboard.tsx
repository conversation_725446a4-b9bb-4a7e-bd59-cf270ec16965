"use client";

import type React from "react";
import { useEffect } from "react";
import CalendarHeader from "./CalendarHeader";
import CalendarView from "./CalendarView";
import EventModal from "./EventModal";
import useCalendarStore from "../store/calendarStore";
import EventDetailsModal from "./EventDetailsModal";
import { DeleteModal } from "./DeleteModal";
import { ImportModal } from "./ImportModal";
// No hardcoded strings to translate in this component.

export const CalendarDashboard: React.FC = () => {
  const {
    // State
    currentDate,
    isImportModalOpen, // Add this
    searchTerm,

    // Actions
    fetchEvents,
    fetchEventCategories,
    fetchCalendarSummary,
    closeImportModal, // Add this
  } = useCalendarStore();

  // Initialize data on mount
  useEffect(() => {
    const year = currentDate.getFullYear();
    fetchEvents(year, searchTerm);
    // fetchEventCategories()
    fetchCalendarSummary(year);
  }, [
    fetchEvents,
    fetchEventCategories,
    fetchCalendarSummary,
    currentDate,
    searchTerm,
  ]);

  return (
    <div className="h-screen flex bg-gray-50">
      <div className="flex-1 flex flex-col">
        <CalendarHeader />

        <div className="flex-1 overflow-auto">
          <CalendarView />
        </div>
      </div>

      <EventModal />
      <EventDetailsModal />
      <DeleteModal />
      {/* Import Events Modal */}
      <ImportModal isOpen={isImportModalOpen} onClose={closeImportModal} />
    </div>
  );
};
