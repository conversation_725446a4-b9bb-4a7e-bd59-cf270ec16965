"use client";
import { motion } from "framer-motion";
import React from "react";
import { AlertCircle, X } from "lucide-react";
import { clsx } from "clsx";
import { ReusableDialog } from "@/components/common/CustomDialog";
import useCalendarStore from "../store/calendarStore";
import { useTranslations } from "next-intl"; // Import useTranslations

// removed erroneous destructuring outside function

export function DeleteModal() {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations

  const {
    isDeleteModalOpen,
    deleteModalEventId,
    isEventLoading,
    deleteEvent,
    closeDeleteModal,
    getEventById,
    closeEventDetailsModal,
  } = useCalendarStore();

  const [error, setError] = React.useState<string | null>(null);

  const handleDelete = async () => {
    if (!deleteModalEventId) return;
    setError(null);
    try {
      await deleteEvent(deleteModalEventId);
      closeDeleteModal();
      closeEventDetailsModal();
    } catch (e) {
      console.log("Error deleting event:", e);
      setError(t("deleteModal.deleteError"));
    }
  };

  const event = deleteModalEventId ? getEventById(deleteModalEventId) : null;

  const dialogFooter = (
    <div className="flex items-center justify-between gap-4">
      <button
        type="button"
        onClick={closeDeleteModal}
        disabled={isEventLoading}
        className="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium disabled:opacity-50"
      >
        {t("cancelButton")}
      </button>
      <motion.button
        type="button"
        onClick={handleDelete}
        disabled={isEventLoading}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={clsx(
          "flex-1 flex items-center justify-center gap-2 px-4 py-2 text-white bg-[#FF5630] rounded-lg font-medium transition-colors",
          "hover:bg-[#FF5630]/90 focus:outline-none focus:ring-2 focus:ring-[#FF5630] focus:ring-offset-2",
          "disabled:opacity-50 disabled:cursor-not-allowed",
        )}
      >
        <AlertCircle className="w-4 h-4" />
        {isEventLoading
          ? t("deleteModal.deletingButton")
          : t("deleteModal.deleteButton")}
      </motion.button>
    </div>
  );

  return (
    <ReusableDialog
      isOpen={isDeleteModalOpen}
      onClose={closeDeleteModal}
      title={t("deleteModal.title")}
      size="md"
      footer={dialogFooter}
    >
      <div className="flex flex-col items-center text-center px-2 py-4">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-[#FFEDED] rounded-full p-3">
            <X className="h-8 w-8 text-[#FF5630]" />
          </div>
        </div>
        <h3 className="text-lg font-semibold mb-2">
          {t("deleteModal.confirmationTitle")}
        </h3>
        {event && (
          <p className="text-gray-600 mb-1">
            {t("deleteModal.confirmationMessage", { eventName: event.name })}
          </p>
        )}
        {error && <p className="text-red-600 mt-2">{error}</p>}
      </div>
    </ReusableDialog>
  );
}
