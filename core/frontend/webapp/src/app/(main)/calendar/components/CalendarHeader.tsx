"use client";

import type React from "react";
import { useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Upload,
  Search,
  Calendar,
  Plus,
  History,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import EventsHistorySidebar from "./EventsHistorySidebar";
import useCalendarStore from "../store/calendarStore";
import { useTranslations } from "next-intl"; // Import useTranslations
import { IconButton } from "@/components/common/CustomButtons";
import CustomIcon from "@/components/common/CustomIcons";

const CalendarHeader: React.FC = () => {
  const t = useTranslations("rolesPermissions"); // Initialize useTranslations

  const {
    // State
    currentDate,
    viewMode,
    searchTerm,
    eventCategories,
    isCategoriesLoading,
    isHistoryModalOpen,
    isSummaryLoading,
    isDownloadingCanvas,

    // Actions
    setViewMode,
    setSearchTerm,
    openEventModal,
    openImportModal,
    openHistoryModal,
    fetchEventCategories,
    fetchCalendarSummary,
    goToNextMonth,
    goToPreviousMonth,
    goToNextYear,
    goToPreviousYear,
    getTotalWorkingDays,
    closeHistoryModal,
    downloadCanvas,
  } = useCalendarStore();

  useEffect(() => {
    fetchCalendarSummary(currentDate.getFullYear());
  }, [fetchEventCategories, fetchCalendarSummary, currentDate]);

  useEffect(() => {
    fetchEventCategories();
  }, [fetchEventCategories]);

  const handlePrevious = () => {
    if (viewMode.mode === "month") {
      goToPreviousMonth();
    } else {
      goToPreviousYear();
    }
  };

  const handleNext = () => {
    if (viewMode.mode === "month") {
      goToNextMonth();
    } else {
      goToNextYear();
    }
  };

  const getDisplayText = () => {
    if (viewMode.mode === "year") {
      return `${currentDate.getFullYear()}`;
    }
    return format(currentDate, "MMMM yyyy");
  };

  const getDisplayMonthYear = () => {
    if (viewMode.mode === "year") {
      return null;
    }
    return format(currentDate, "MMMM yyyy");
  };

  const firstRowCategories = eventCategories.slice(
    0,
    Math.ceil(eventCategories.length / 2),
  );
  const secondRowCategories = eventCategories.slice(
    Math.ceil(eventCategories.length / 2),
  );

  const handleDownloadCanvas = async () => {
    try {
      await downloadCanvas();
    } catch (error) {
      console.error("Error downloading canvas:", error);
    }
  };

  return (
    <div className="bg-white">
      {/* Top bar with search and actions */}
      <div className="flex items-center justify-between px-6 py-3 border-b border-gray-100">
        <div className="flex items-center gap-4">
          <Calendar className="w-6 h-6 text-gray-600" />
          <h1 className="text-xl font-semibold text-gray-800">
            {t("calendarHeader.plantCalendar", {
              year: currentDate.getFullYear(),
            })}
          </h1>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder={t("calendarHeader.searchEventsPlaceholder")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          <Button
            variant="outline"
            onClick={() => openEventModal()}
            className="border border-black bg-transparent"
          >
            <Plus className="w-4 h-4 mr-2" />
            {t("calendarHeader.addEventButton")}
          </Button>

          <Button
            variant="default"
            onClick={() => openImportModal()}
            className="bg-black text-white hover:bg-gray-800"
          >
            <Upload className="w-4 h-4 mr-2" />
            {t("calendarHeader.importEventsButton")}
          </Button>

          <IconButton
            icon={() => (
              <CustomIcon
                name="export"
                style={{
                  width: "1rem",
                  height: "1rem",
                }}
              />
            )}
            label={
              isDownloadingCanvas ? t("downloading") : t("downloadTemplate")
            }
            color="default"
            type="button"
            variant="outline"
            id="download-template-button"
            onClick={handleDownloadCanvas}
            disabled={isDownloadingCanvas}
            className="border border-gray-300 bg-green-500 text-white hover:bg-green-600 hover:text-white flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          />

          <Button
            variant="default"
            onClick={() => openHistoryModal()}
            className="bg-black text-white hover:bg-gray-800"
          >
            <History className="w-4 h-4 mr-2" />
            {t("calendarHeader.historyButton")}
          </Button>
        </div>
      </div>

      {/* Legend - Dynamic categories */}
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side: Event type legends - Dynamic */}
        <div className="flex flex-col gap-3">
          {isCategoriesLoading ? (
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
              {t("calendarHeader.loadingCategories")}
            </div>
          ) : (
            <>
              {firstRowCategories.length > 0 && (
                <div className="flex items-center gap-4">
                  {firstRowCategories.map((category) => (
                    <div
                      key={category.id}
                      className="flex items-center gap-2 text-sm"
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      ></div>
                      <span>{category.name}</span>
                    </div>
                  ))}
                </div>
              )}
              {secondRowCategories.length > 0 && (
                <div className="flex items-center gap-4">
                  {secondRowCategories.map((category) => (
                    <div
                      key={category.id}
                      className="flex items-center gap-2 text-sm"
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      ></div>
                      <span>{category.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>

        {/* Right side: Total working days - Dynamic */}
        <div className="bg-blue-500 text-white rounded-xl p-4 flex items-center gap-4 shadow-md">
          <div className="bg-white/20 p-2 rounded-full">
            <Calendar className="h-6 w-6 text-white" />
          </div>
          <div>
            <p className="text-sm">
              {t("calendarHeader.totalWorkingDaysIn", {
                year: currentDate.getFullYear(),
              })}
            </p>
            {isSummaryLoading ? (
              <div className="w-8 h-6 bg-white/20 rounded animate-pulse"></div>
            ) : (
              <p className="text-xl font-semibold">{getTotalWorkingDays()}</p>
            )}
          </div>
        </div>
      </div>

      {/* Calendar controls */}
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-extrabold text-gray-800 min-w-[200px]">
            {getDisplayMonthYear()}
          </h2>
        </div>

        <div className="flex items-center gap-2">
          <ToggleGroup
            type="single"
            value={viewMode.mode}
            onValueChange={(value) => {
              if (value)
                setViewMode({
                  mode: value as "month" | "year",
                  date: currentDate,
                });
            }}
            className="bg-muted rounded-[7px] border-[#D1D1D1] border-[1px] flex"
          >
            <ToggleGroupItem
              value="month"
              className="px-8 py-1 text-sm data-[state=on]:bg-black data-[state=on]:text-white data-[state=off]:text-black rounded-[7px]"
            >
              {t("calendarHeader.monthView")}
            </ToggleGroupItem>
            <ToggleGroupItem
              value="year"
              className="px-8 py-1 text-sm data-[state=on]:bg-black data-[state=on]:text-white data-[state=off]:text-black rounded-[7px]"
            >
              {t("calendarHeader.yearView")}
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {/* <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-center">
            <Button variant="ghost" size="icon" onClick={handlePrevious}>
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <h2 className="text-lg font-semibold text-gray-800 min-w-[200px]">
              {getDisplayText()}
            </h2>
            <Button variant="ghost" size="icon" onClick={handleNext}>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div> */}

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handlePrevious}
            className="rounded-xl border-gray-300 hover:bg-gray-50 h-12 w-12 p-0 flex items-center justify-center"
          >
            <ChevronLeft
              className="w-6 h-6"
              style={{ width: "30px", height: "30px" }}
            />
          </Button>

          <div className="h-12 w-32 border border-gray-300 rounded-xl bg-white flex items-center justify-center">
            <h2 className="text-lg font-semibold text-gray-800">
              {getDisplayText()}
            </h2>
          </div>

          <Button
            variant="outline"
            onClick={handleNext}
            className="rounded-xl border-gray-300 hover:bg-gray-50 h-12 w-12 p-0 flex items-center justify-center"
          >
            <ChevronRight
              className="w-6 h-6"
              style={{ width: "30px", height: "30px" }}
            />
          </Button>
        </div>
      </div>

      <EventsHistorySidebar
        isOpen={isHistoryModalOpen}
        onClose={() => closeHistoryModal()}
      />
    </div>
  );
};

export default CalendarHeader;
