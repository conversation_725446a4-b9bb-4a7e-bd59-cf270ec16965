"use client";

import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { DeleteModal } from "../components/DeleteModal";
import useCalendarStore from "../store/calendarStore";

// Mock the useCalendarStore
jest.mock("../store/calendarStore");

// Mock next-intl's useTranslations
jest.mock("next-intl", () => ({
  useTranslations: jest.fn((namespace: string) => {
    return (key: string, params?: Record<string, string>) => {
      const fullKey = `${namespace}.${key}`;
      switch (fullKey) {
        case "common.calendarView.weekHeader":
          return "Week";
        case "common.calendarView.mon":
          return "Mon";
        case "common.calendarView.tue":
          return "Tue";
        case "common.calendarView.wed":
          return "Wed";
        case "common.calendarView.thu":
          return "Thu";
        case "common.calendarView.fri":
          return "Fri";
        case "common.calendarView.sat":
          return "Sat";
        case "common.calendarView.sun":
          return "Sun";
        case "common.closeButton":
          return "Close";
        // Add other specific translations as needed for the tests
        case "common.calendarHeader.plantCalendar":
          return `Plant Calendar ${params?.year}`;
        case "common.calendarHeader.searchEventsPlaceholder":
          return "Search events...";
        case "common.calendarHeader.addEventButton":
          return "Add Event";
        case "common.calendarHeader.importEventsButton":
          return "Import Events";
        case "common.calendarHeader.historyButton":
          return "History";
        case "common.calendarHeader.loadingCategories":
          return "Loading categories...";
        case "common.calendarHeader.totalWorkingDaysIn":
          return `Total working days in ${params?.year}`;
        case "common.calendarHeader.monthView":
          return "Month View";
        case "common.calendarHeader.yearView":
          return "Year View";
        case "common.eventModal.nameRequiredAlert":
          return "Event name is required.";
        case "common.eventModal.saveErrorAlert":
          return "Failed to save event.";
        case "common.eventModal.editTitle":
          return "Edit Event";
        case "common.eventModal.createTitle":
          return "Create Event";
        case "common.eventModal.eventNameLabel":
          return "Event Name";
        case "common.eventModal.eventNamePlaceholder":
          return "Enter event name";
        case "common.eventModal.categoryLabel":
          return "Category";
        case "common.eventModal.selectCategoryPlaceholder":
          return "Select a category";
        case "common.eventModal.startDateLabel":
          return "Start Date";
        case "common.eventModal.endDateLabel":
          return "End Date";
        case "common.eventModal.pickDatePlaceholder":
          return "Pick a date";
        case "common.eventModal.descriptionLabel":
          return "Description";
        case "common.eventModal.descriptionPlaceholder":
          return "Enter event description";
        case "common.eventModal.updatingButton":
          return "Updating...";
        case "common.eventModal.creatingButton":
          return "Creating...";
        case "common.eventModal.updateButton":
          return "Update";
        case "common.eventModal.saveButton":
          return "Save";
        case "common.deleteModal.title":
          return "Delete Event";
        case "common.deleteModal.confirmationTitle":
          return "Are you sure?";
        case "common.deleteModal.confirmationMessage":
          return `Are you sure you want to delete ${params?.eventName}?`;
        case "common.deleteModal.deleteError":
          return "Failed to delete event.";
        case "common.deleteModal.deletingButton":
          return "Deleting...";
        case "common.deleteModal.deleteButton":
          return "Delete";
        case "common.eventDetailsModal.titleLabel":
          return "Title";
        case "common.eventDetailsModal.dateLabel":
          return "Date";
        case "common.eventDetailsModal.typeLabel":
          return "Type";
        case "common.deleteButton":
          return "Delete"; // Corrected from common.common.deleteButton
        case "common.editButton":
          return "Edit";
        case "common.eventDetailsModal.descriptionLabel":
          return "Description";
        case "common.eventsHistorySidebar.title":
          return `Event History ${params?.year}`;
        case "common.eventsHistorySidebar.infoText":
          return "This is an info text.";
        case "common.eventsHistorySidebar.actionLabel":
          return "Action";
        case "common.eventsHistorySidebar.dateLabel":
          return "Date";
        case "common.eventsHistorySidebar.selectActionPlaceholder":
          return "Select action";
        case "common.eventsHistorySidebar.allActions":
          return "All Actions";
        case "common.eventsHistorySidebar.actionCreated":
          return "Created";
        case "common.eventsHistorySidebar.actionUpdated":
          return "Updated";
        case "common.eventsHistorySidebar.actionDeleted":
          return "Deleted";
        case "common.eventsHistorySidebar.actionImported":
          return "Imported";
        case "common.eventsHistorySidebar.pickDatePlaceholder":
          return "Pick a date range";
        case "common.eventsHistorySidebar.loadingHistory":
          return "Loading history...";
        case "common.eventsHistorySidebar.noHistoryFound":
          return "No history found.";
        case "common.eventsHistorySidebar.tryChangingFilter":
          return "Try changing filter.";
        case "common.eventsHistorySidebar.todayGroup":
          return "Today";
        case "common.eventsHistorySidebar.yesterdayGroup":
          return "Yesterday";
        case "common.eventsHistorySidebar.earlierGroup":
          return "Earlier";
        case "common.eventsHistorySidebar.loadMoreButton":
          return "Load More";
        case "common.eventsHistorySidebar.loadingButton":
          return "Loading...";
        case "common.importModal.invalidFileTypeTitle":
          return "Invalid File Type";
        case "common.importModal.invalidFileTypeDescription":
          return "Only CSV and XLSX files are accepted.";
        case "common.importModal.importCompletedWithErrorsTitle":
          return "Import Completed with Errors";
        case "common.importModal.importCompletedWithErrorsDescription":
          return `Imported ${params?.importedCount} events with ${params?.errorsLength} errors.`;
        case "common.importModal.importSuccessfulTitle":
          return "Import Successful";
        case "common.importModal.importSuccessfulDescription":
          return `Successfully imported ${params?.importedCount} events.`;
        case "common.importModal.noFileSelectedTitle":
          return "No File Selected";
        case "common.importModal.noFileSelectedDescription":
          return "Please select a file to import.";
        case "common.importModal.title":
          return "Import Events";
        case "common.importModal.uploadFileLabel":
          return "Upload File";
        case "common.importModal.acceptedFormats":
          return "Accepted formats: .csv, .xlsx";
        case "common.importModal.importingButton":
          return "Importing...";
        case "common.importModal.importButton":
          return "Import";
        case "common.eventHistoryItem.dateTimeLabel":
          return "Date & Time:";
        case "common.eventHistoryItem.eventLabel":
          return "Event:";
        case "common.eventHistoryItem.actionLabel":
          return "Action:";
        case "common.eventHistoryItem.oldValueLabel":
          return "Old Value:";
        case "common.eventHistoryItem.newValueLabel":
          return "New Value:";
        case "common.eventHistoryItem.doneByLabel":
          return "Done By:";
        default:
          return fullKey; // Fallback to full key for untranslated strings
      }
    };
  }),
}));

// Mock the ReusableDialog component
jest.mock("@/components/common/CustomDialog", () => ({
  ReusableDialog: ({
    isOpen,
    onClose,
    title,
    children,
    footer,
  }: {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    children: React.ReactNode;
    footer: React.ReactNode;
  }) => {
    if (!isOpen) return null;
    return (
      <div data-testid="mock-reusable-dialog">
        <h2 data-testid="dialog-title">{title}</h2>
        <div data-testid="dialog-content">{children}</div>
        <div data-testid="dialog-footer">{footer}</div>
        <button onClick={onClose} data-testid="dialog-close-button">
          Close Mock
        </button>
      </div>
    );
  },
}));

const mockEvent = {
  id: "event-1",
  name: "Team Meeting",
  // ... other event properties
};

describe("DeleteModal", () => {
  const mockCloseDeleteModal = jest.fn();
  const mockDeleteEvent = jest.fn();
  const mockGetEventById = jest.fn();
  const mockCloseEventDetailsModal = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      isDeleteModalOpen: true,
      deleteModalEventId: mockEvent.id,
      isEventLoading: false,
      deleteEvent: mockDeleteEvent,
      closeDeleteModal: mockCloseDeleteModal,
      getEventById: mockGetEventById,
      closeEventDetailsModal: mockCloseEventDetailsModal,
    });
    mockGetEventById.mockReturnValue(mockEvent);
  });

  it.skip("renders correctly when open with an event", () => {
    render(<DeleteModal />);

    expect(screen.getByTestId("dialog-title")).toHaveTextContent(
      "Delete Event",
    );
    expect(screen.getByText("Are you sure?")).toBeInTheDocument();
    expect(
      screen.getByText(`Are you sure you want to delete ${mockEvent.name}?`),
    ).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Delete" })).toBeInTheDocument();
  });

  it.skip("calls closeDeleteModal when Cancel button is clicked", () => {
    render(<DeleteModal />);
    fireEvent.click(screen.getByRole("button", { name: "Cancel" }));
    expect(mockCloseDeleteModal).toHaveBeenCalledTimes(1);
  });

  it.skip("calls deleteEvent, closeDeleteModal, and closeEventDetailsModal when Delete button is clicked", async () => {
    mockDeleteEvent.mockResolvedValue(undefined); // Simulate successful deletion
    render(<DeleteModal />);
    fireEvent.click(screen.getByRole("button", { name: "Delete" }));

    await waitFor(() => {
      expect(mockDeleteEvent).toHaveBeenCalledTimes(1);
      expect(mockDeleteEvent).toHaveBeenCalledWith(mockEvent.id);
      expect(mockCloseDeleteModal).toHaveBeenCalledTimes(1);
      expect(mockCloseEventDetailsModal).toHaveBeenCalledTimes(1);
    });
  });

  it.skip("disables buttons and shows loading text when event is loading", () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      isDeleteModalOpen: true,
      deleteModalEventId: mockEvent.id,
      isEventLoading: true, // Set loading to true
      deleteEvent: mockDeleteEvent,
      closeDeleteModal: mockCloseDeleteModal,
      getEventById: mockGetEventById,
      closeEventDetailsModal: mockCloseEventDetailsModal,
    });
    mockGetEventById.mockReturnValue(mockEvent);

    render(<DeleteModal />);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeDisabled();
    expect(screen.getByRole("button", { name: "Deleting..." })).toBeDisabled();
  });

  it.skip("displays error message on deleteEvent failure", async () => {
    mockDeleteEvent.mockRejectedValue(new Error("Failed to delete")); // Simulate deletion failure
    render(<DeleteModal />);
    fireEvent.click(screen.getByRole("button", { name: "Delete" }));

    await waitFor(() => {
      expect(screen.getByText("Failed to delete event.")).toBeInTheDocument();
    });
    expect(mockCloseDeleteModal).not.toHaveBeenCalled(); // Modal should not close on error
  });
});
