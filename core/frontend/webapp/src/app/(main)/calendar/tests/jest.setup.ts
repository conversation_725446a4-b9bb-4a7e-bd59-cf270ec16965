import "@testing-library/jest-dom";

// Mock localStorage for Zustand persist middleware
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, "localStorage", { value: localStorageMock });

// Mock next-intl useTranslations
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key, // Simple mock that returns the key itself
}));

// Removed the global mock for toast from here.
// It will now be mocked directly in calendarStore.test.ts for better isolation.

// Mock date-fns format if needed for specific tests, otherwise it uses its real implementation
// For now, we'll let it use its real implementation unless it causes issues.
