import { act } from "react";
import useCalendarStore from "../store/calendarStore";
import api from "@/lib/axios";
import { toast } from "@/hooks/use-toast"; // Import toast

import type {
  CalendarEvent,
  EventCategory,
  CalendarSummary,
} from "../types/calendarTypes";
import {
  ApiHistoryEventType,
  type EventHistoryItem,
} from "../types/calendarTypes";

// Mock the axios instance directly in the test file
jest.mock("@/lib/axios", () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock the toast function directly in the test file
jest.mock("@/hooks/use-toast", () => ({
  toast: jest.fn(),
}));

// Now, correctly type the imported mocks
const mockApi = api as jest.Mocked<typeof api>;
const mockToast = toast as jest.MockedFunction<typeof toast>;

// Sample data for testing
const mockCategories: EventCategory[] = [
  {
    id: "cat1",
    name: "Holiday",
    color: "#FF0000",
    isWorkingHoliday: false,
    country: "US",
    site: "NY",
  },
  {
    id: "cat2",
    name: "Meeting",
    color: "#00FF00",
    isWorkingHoliday: true,
    country: "US",
    site: "NY",
  },
];

const mockEvents: CalendarEvent[] = [
  {
    id: "event1",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Summer Party",
    description: "Company summer party",
    category: mockCategories[0],
    country: "US",
    site: "NY",
  },
  {
    id: "event2",
    year: 2024,
    startDate: "2024-07-25",
    endDate: "2024-07-26",
    name: "Project Review",
    description: "Review Q3 projects",
    category: mockCategories[1],
    country: "US",
    site: "NY",
  },
];

const mockSummary: CalendarSummary = {
  id: "summary1",
  year: 2024,
  country: "US",
  site: "NY",
  status: "Calculated",
  totalDays: 365,
  sundays: 52,
  saturdays: 52,
  categoryCounts: [],
  totalPlantHolidays: 10,
  workedDaysPlant: 251,
  lastCalculated: "2024-01-01",
  createdBy: "admin",
  createdAt: "2024-01-01",
  updatedAt: "2024-01-01",
  monthSummaries: [],
};

const mockHistory: EventHistoryItem[] = [
  {
    id: "hist1",
    eventTitle: "Event A Created",
    action: ApiHistoryEventType.CREATED,
    performedBy: { id: "user1", name: "John Doe", country: "US", site: "NY" },
    performedAt: new Date("2024-07-19T10:00:00Z"),
    details: "Event A was created.",
    type: ApiHistoryEventType.CREATED,
  },
  {
    id: "hist2",
    eventTitle: "Event B Updated",
    action: ApiHistoryEventType.UPDATED,
    performedBy: { id: "user2", name: "Jane Smith", country: "US", site: "NY" },
    performedAt: new Date("2024-07-18T11:30:00Z"),
    details: "Event B was updated.",
    changedFields: [
      { field: "name", oldValue: "Old Name", newValue: "New Name" },
    ],
    type: ApiHistoryEventType.UPDATED,
  },
  {
    id: "hist3",
    eventTitle: "Event C Deleted",
    action: ApiHistoryEventType.DELETED,
    performedBy: { id: "user1", name: "John Doe", country: "US", site: "NY" },
    performedAt: new Date("2024-07-17T14:00:00Z"),
    details: "Event C was deleted.",
    type: ApiHistoryEventType.DELETED,
  },
  {
    id: "hist4",
    eventTitle: "Events Imported",
    action: ApiHistoryEventType.IMPORTED,
    performedBy: { id: "user3", name: "Admin", country: "US", site: "NY" },
    performedAt: new Date("2024-07-16T09:00:00Z"),
    details: "5 events were imported.",
    type: ApiHistoryEventType.IMPORTED,
  },
  {
    id: "hist5",
    eventTitle: "Event D Created",
    action: ApiHistoryEventType.CREATED,
    performedBy: { id: "user1", name: "John Doe", country: "US", site: "NY" },
    performedAt: new Date("2024-07-15T10:00:00Z"),
    details: "Event D was created.",
    type: ApiHistoryEventType.CREATED,
  },
  {
    id: "hist6",
    eventTitle: "Event E Updated",
    action: ApiHistoryEventType.UPDATED,
    performedBy: { id: "user2", name: "Jane Smith", country: "US", site: "NY" },
    performedAt: new Date("2024-07-14T11:30:00Z"),
    details: "Event E was updated.",
    changedFields: [
      { field: "description", oldValue: "Old Desc", newValue: "New Desc" },
    ],
    type: ApiHistoryEventType.UPDATED,
  },
  {
    id: "hist7",
    eventTitle: "Event F Deleted",
    action: ApiHistoryEventType.DELETED,
    performedBy: { id: "user1", name: "John Doe", country: "US", site: "NY" },
    performedAt: new Date("2024-07-13T14:00:00Z"),
    details: "Event F was deleted.",
    type: ApiHistoryEventType.DELETED,
  },
  {
    id: "hist8",
    eventTitle: "Events Imported 2",
    action: ApiHistoryEventType.IMPORTED,
    performedBy: { id: "user3", name: "Admin", country: "US", site: "NY" },
    performedAt: new Date("2024-07-12T09:00:00Z"),
    details: "3 events were imported.",
    type: ApiHistoryEventType.IMPORTED,
  },
  {
    id: "hist9",
    eventTitle: "Event G Created",
    action: ApiHistoryEventType.CREATED,
    performedBy: { id: "user1", name: "John Doe", country: "US", site: "NY" },
    performedAt: new Date("2024-07-11T10:00:00Z"),
    details: "Event G was created.",
    type: ApiHistoryEventType.CREATED,
  },
  {
    id: "hist10",
    eventTitle: "Event H Updated",
    action: ApiHistoryEventType.UPDATED,
    performedBy: { id: "user2", name: "Jane Smith", country: "US", site: "NY" },
    performedAt: new Date("2024-07-10T11:30:00Z"),
    details: "Event H was updated.",
    changedFields: [
      { field: "category", oldValue: "Cat A", newValue: "Cat B" },
    ],
    type: ApiHistoryEventType.UPDATED,
  },
  {
    id: "hist11",
    action: ApiHistoryEventType.DELETED,
    eventTitle: "Event I Deleted",
    performedBy: { id: "user1", name: "John Doe", country: "US", site: "NY" },
    performedAt: new Date("2024-07-09T14:00:00Z"),
    details: "Event I was deleted.",
    type: ApiHistoryEventType.DELETED,
  },
];

describe("useCalendarStore", () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    localStorage.clear(); // Clear localStorage before each test
    jest.useFakeTimers(); // Use fake timers for setTimeout in loadMoreHistory
  });

  afterEach(() => {
    jest.runOnlyPendingTimers(); // Clear any pending timers
    jest.useRealTimers(); // Restore real timers
  });

  it("should initialize with default state", () => {
    const state = useCalendarStore.getState();
    expect(state.isLoading).toBe(false);
    expect(state.events).toEqual([]);
    expect(state.currentDate).toBeInstanceOf(Date);
    expect(state.viewMode).toEqual({ mode: "year", date: expect.any(Date) });
    expect(state.historyPageSize).toBe(10);
    expect(state.displayedHistoryCount).toBe(10);
    expect(state.hasMoreHistory).toBe(false);
    expect(state.eventHistory).toEqual([]);
    expect(state.allEventHistory).toEqual([]);
  });

  describe("Data Fetching Actions", () => {
    it("fetchEvents should fetch events and update state", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockEvents });

      await act(async () => {
        await useCalendarStore.getState().fetchEvents(2024, "");
      });

      const state = useCalendarStore.getState();
      expect(state.isLoading).toBe(false);
      expect(state.events).toEqual(mockEvents);
      expect(state.filteredEvents).toEqual(mockEvents);
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events?year=2024&search=",
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({ title: "Success" }),
      );
    });

    it("fetchEventCategories should fetch categories and update state", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockCategories });

      await act(async () => {
        await useCalendarStore.getState().fetchEventCategories();
      });

      const state = useCalendarStore.getState();
      expect(state.isCategoriesLoading).toBe(false);
      expect(state.eventCategories).toEqual(mockCategories);
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/category",
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({ title: "Success" }),
      );
    });

    it("fetchCalendarSummary should fetch summary and update state", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockSummary });

      await act(async () => {
        await useCalendarStore.getState().fetchCalendarSummary(2024);
      });

      const state = useCalendarStore.getState();
      expect(state.isSummaryLoading).toBe(false);
      expect(state.calendarSummary).toEqual(mockSummary);
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events/summary/2024",
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({ title: "Success" }),
      );
    });

    it("fetchEventHistory should fetch history and set initial pagination state", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockHistory });

      await act(async () => {
        await useCalendarStore.getState().fetchEventHistory();
      });

      const state = useCalendarStore.getState();
      expect(state.isHistoryLoading).toBe(false);
      expect(state.allEventHistory).toEqual(mockHistory);
      expect(state.eventHistory).toEqual(mockHistory.slice(0, 10)); // Initial 10 items
      expect(state.displayedHistoryCount).toBe(10);
      expect(state.hasMoreHistory).toBe(true); // Since mockHistory has 11 items
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/activities",
        expect.any(Object),
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({ title: "Success" }),
      );
    });

    it("loadMoreHistory should load more items", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockHistory });

      await act(async () => {
        await useCalendarStore.getState().fetchEventHistory();
      });

      // Verify initial state after first fetch
      expect(useCalendarStore.getState().eventHistory.length).toBe(10);
      expect(useCalendarStore.getState().hasMoreHistory).toBe(true);

      await act(async () => {
        useCalendarStore.getState().loadMoreHistory();
        jest.advanceTimersByTime(300); // Advance timers for setTimeout
      });

      const state = useCalendarStore.getState();
      expect(state.isLoadingMoreHistory).toBe(false);
      expect(state.eventHistory.length).toBe(11); // 10 + 1 (remaining)
      expect(state.displayedHistoryCount).toBe(20); // 10 + 10 (page size)
      expect(state.hasMoreHistory).toBe(false); // No more items
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({ title: "Success" }),
      );
    });

    it.skip("loadMoreHistory should not load if no more history", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockHistory.slice(0, 5) }); // Only 5 items

      await act(async () => {
        await useCalendarStore.getState().fetchEventHistory();
      });

      // Verify initial state after first fetch
      expect(useCalendarStore.getState().eventHistory.length).toBe(5);
      expect(useCalendarStore.getState().hasMoreHistory).toBe(false);

      await act(async () => {
        useCalendarStore.getState().loadMoreHistory();
        jest.advanceTimersByTime(300);
      });

      const state = useCalendarStore.getState();
      expect(state.isLoadingMoreHistory).toBe(false);
      expect(state.eventHistory.length).toBe(5); // Still 5 items
      expect(state.hasMoreHistory).toBe(false);
      expect(mockToast).not.toHaveBeenCalledWith(
        expect.objectContaining({
          description: "Loaded 10 more history items",
        }),
      );
    });
  });

  describe("Event Management Actions", () => {
    it("createEvent should add a new event", async () => {
      const newEventData = {
        startDate: "2024-08-01",
        endDate: "2024-08-01",
        name: "New Event",
        description: "A brand new event",
        categoryId: "cat1",
      };
      const createdEvent = {
        ...newEventData,
        id: "newId",
        category: mockCategories[0],
        year: 2024,
        country: "US",
        site: "NY",
      };
      mockApi.post.mockResolvedValueOnce({ data: createdEvent });
      mockApi.get.mockResolvedValueOnce({ data: mockSummary }); // For fetchCalendarSummary

      await act(async () => {
        useCalendarStore.setState({ events: [] }); // Start with empty events
        await useCalendarStore.getState().createEvent(newEventData);
      });

      const state = useCalendarStore.getState();
      expect(state.isEventLoading).toBe(false);
      expect(state.events).toEqual([createdEvent]);
      expect(mockApi.post).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events",
        newEventData,
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
          description: "Event created successfully",
        }),
      );
    });

    it("updateEvent should modify an existing event", async () => {
      const updatedEventData = {
        startDate: "2024-07-20",
        endDate: "2024-07-20",
        name: "Updated Summer Party",
        description: "Updated description",
        categoryId: "cat1",
      };
      const updatedEvent = { ...mockEvents[0], ...updatedEventData };
      mockApi.put.mockResolvedValueOnce({ data: updatedEvent });
      mockApi.get.mockResolvedValueOnce({ data: mockSummary }); // For fetchCalendarSummary

      await act(async () => {
        useCalendarStore.setState({ events: mockEvents }); // Set initial events
        await useCalendarStore
          .getState()
          .updateEvent("event1", updatedEventData);
      });

      const state = useCalendarStore.getState();
      expect(state.isEventLoading).toBe(false);
      expect(state.events).toEqual([updatedEvent, mockEvents[1]]);
      expect(mockApi.put).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events/event1",
        updatedEventData,
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
          description: "Event updated successfully",
        }),
      );
    });

    it("deleteEvent should remove an event", async () => {
      mockApi.delete.mockResolvedValueOnce({ data: {} });
      mockApi.get.mockResolvedValueOnce({ data: mockSummary }); // For fetchCalendarSummary

      await act(async () => {
        useCalendarStore.setState({ events: mockEvents }); // Set initial events
        await useCalendarStore.getState().deleteEvent("event1");
      });

      const state = useCalendarStore.getState();
      expect(state.isEventLoading).toBe(false);
      expect(state.events).toEqual([mockEvents[1]]);
      expect(mockApi.delete).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events/event1",
      );
      expect(mockToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Success",
          description: "Event deleted successfully",
        }),
      );
    });
  });

  describe("UI State Actions", () => {
    it("setCurrentDate should update currentDate", () => {
      const newDate = new Date("2025-01-15");
      act(() => {
        useCalendarStore.getState().setCurrentDate(newDate);
      });
      expect(useCalendarStore.getState().currentDate).toEqual(newDate);
    });

    it("setViewMode should update viewMode", () => {
      const newViewMode = {
        mode: "month" as const,
        date: new Date("2024-08-01"),
      };
      act(() => {
        useCalendarStore.getState().setViewMode(newViewMode);
      });
      expect(useCalendarStore.getState().viewMode).toEqual(newViewMode);
    });

    it("setSearchTerm should update searchTerm and filter events", () => {
      act(() => {
        useCalendarStore.setState({ events: mockEvents });
        useCalendarStore.getState().setSearchTerm("summer");
      });
      const state = useCalendarStore.getState();
      expect(state.searchTerm).toBe("summer");
      expect(state.filteredEvents).toEqual([mockEvents[0]]);
    });

    it("setActiveFilters should update activeFilters and apply filters", () => {
      act(() => {
        useCalendarStore.setState({ events: mockEvents });
        useCalendarStore.getState().setActiveFilters({ categoryId: ["cat1"] });
      });
      const state = useCalendarStore.getState();
      expect(state.activeFilters).toEqual({ categoryId: ["cat1"] });
      expect(state.filteredEvents).toEqual([mockEvents[0]]);
    });

    it("setSelectedEventId should update selectedEventId", () => {
      act(() => {
        useCalendarStore.getState().setSelectedEventId("event1");
      });
      expect(useCalendarStore.getState().selectedEventId).toBe("event1");
    });
  });

  describe("History Filter Actions", () => {
    it("setHistoryStartDate should update historyStartDate", () => {
      const newDate = new Date("2024-06-01");
      act(() => {
        useCalendarStore.getState().setHistoryStartDate(newDate);
      });
      expect(useCalendarStore.getState().historyStartDate).toEqual(newDate);
    });

    it("setHistoryEndDate should update historyEndDate", () => {
      const newDate = new Date("2024-07-31");
      act(() => {
        useCalendarStore.getState().setHistoryEndDate(newDate);
      });
      expect(useCalendarStore.getState().historyEndDate).toEqual(newDate);
    });

    it("setHistoryFilterType should update historyFilterType", () => {
      act(() => {
        useCalendarStore
          .getState()
          .setHistoryFilterType(ApiHistoryEventType.CREATED);
      });
      expect(useCalendarStore.getState().historyFilterType).toBe(
        ApiHistoryEventType.CREATED,
      );
    });
  });

  describe("Modal Management Actions", () => {
    it("openEventModal should open modal and set editingEventId/selectedDate", () => {
      const selectedDate = new Date("2024-07-22");
      act(() => {
        useCalendarStore.getState().openEventModal("event1", selectedDate);
      });
      const state = useCalendarStore.getState();
      expect(state.isEventModalOpen).toBe(true);
      expect(state.editingEventId).toBe("event1");
      expect(state.selectedDate).toEqual(selectedDate);
    });

    it("closeEventModal should close modal and clear editingEventId/selectedDate", () => {
      act(() => {
        useCalendarStore.setState({
          isEventModalOpen: true,
          editingEventId: "event1",
          selectedDate: new Date(),
        });
        useCalendarStore.getState().closeEventModal();
      });
      const state = useCalendarStore.getState();
      expect(state.isEventModalOpen).toBe(false);
      expect(state.editingEventId).toBeNull();
      expect(state.selectedDate).toBeNull();
    });

    it("openEventDetailsModal should open details modal and set selectedEventId", () => {
      act(() => {
        useCalendarStore.getState().openEventDetailsModal("event1");
      });
      const state = useCalendarStore.getState();
      expect(state.isEventDetailsModalOpen).toBe(true);
      expect(state.selectedEventId).toBe("event1");
    });

    it("closeEventDetailsModal should close details modal and clear selectedEventId", () => {
      act(() => {
        useCalendarStore.setState({
          isEventDetailsModalOpen: true,
          selectedEventId: "event1",
        });
        useCalendarStore.getState().closeEventDetailsModal();
      });
      expect(useCalendarStore.getState().isEventDetailsModalOpen).toBe(false);
      expect(useCalendarStore.getState().selectedEventId).toBeNull();
    });

    it("openHistoryModal should open history modal and fetch history", async () => {
      mockApi.get.mockResolvedValueOnce({ data: mockHistory });
      await act(async () => {
        useCalendarStore.getState().openHistoryModal();
      });
      const state = useCalendarStore.getState();
      expect(state.isHistoryModalOpen).toBe(true);
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/activities",
        expect.any(Object),
      );
    });

    it("closeHistoryModal should close history modal", () => {
      act(() => {
        useCalendarStore.setState({ isHistoryModalOpen: true });
        useCalendarStore.getState().closeHistoryModal();
      });
      expect(useCalendarStore.getState().isHistoryModalOpen).toBe(false);
    });

    it("openImportModal should open import modal", () => {
      act(() => {
        useCalendarStore.getState().openImportModal();
      });
      expect(useCalendarStore.getState().isImportModalOpen).toBe(true);
    });

    it("closeImportModal should close import modal", () => {
      act(() => {
        useCalendarStore.setState({ isImportModalOpen: true });
        useCalendarStore.getState().closeImportModal();
      });
      expect(useCalendarStore.getState().isImportModalOpen).toBe(false);
    });

    it("openDeleteModal should open delete modal and set deleteModalEventId", () => {
      act(() => {
        useCalendarStore.getState().openDeleteModal("event1");
      });
      const state = useCalendarStore.getState();
      expect(state.isDeleteModalOpen).toBe(true);
      expect(state.deleteModalEventId).toBe("event1");
    });

    it("closeDeleteModal should close delete modal and clear deleteModalEventId", () => {
      act(() => {
        useCalendarStore.setState({
          isDeleteModalOpen: true,
          deleteModalEventId: "event1",
        });
        useCalendarStore.getState().closeDeleteModal();
      });
      const state = useCalendarStore.getState();
      expect(state.isDeleteModalOpen).toBe(false);
      expect(state.deleteModalEventId).toBeNull();
    });
  });

  describe("Navigation Actions", () => {
    it("goToNextMonth should advance current date to next month", () => {
      const initialDate = new Date("2024-07-15");
      act(() => {
        useCalendarStore.setState({ currentDate: initialDate });
        useCalendarStore.getState().goToNextMonth();
      });
      expect(useCalendarStore.getState().currentDate.getMonth()).toBe(7); // August (0-indexed)
      expect(useCalendarStore.getState().currentDate.getFullYear()).toBe(2024);
    });

    it("goToPreviousMonth should move current date to previous month", () => {
      const initialDate = new Date("2024-07-15");
      act(() => {
        useCalendarStore.setState({ currentDate: initialDate });
        useCalendarStore.getState().goToPreviousMonth();
      });
      expect(useCalendarStore.getState().currentDate.getMonth()).toBe(5); // June (0-indexed)
      expect(useCalendarStore.getState().currentDate.getFullYear()).toBe(2024);
    });

    it("goToNextYear should advance current date to next year and refetch data", async () => {
      const initialDate = new Date("2024-07-15");
      mockApi.get.mockResolvedValue({ data: [] }); // Mock for fetchCalendarSummary and fetchEvents
      await act(async () => {
        useCalendarStore.setState({ currentDate: initialDate });
        await useCalendarStore.getState().goToNextYear();
      });
      expect(useCalendarStore.getState().currentDate.getFullYear()).toBe(2025);
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events/summary/2025",
      );
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events?year=2025&search=",
      );
    });

    it("goToPreviousYear should move current date to previous year and refetch data", async () => {
      const initialDate = new Date("2024-07-15");
      mockApi.get.mockResolvedValue({ data: [] }); // Mock for fetchCalendarSummary and fetchEvents
      await act(async () => {
        useCalendarStore.setState({ currentDate: initialDate });
        await useCalendarStore.getState().goToPreviousYear();
      });
      expect(useCalendarStore.getState().currentDate.getFullYear()).toBe(2023);
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events/summary/2023",
      );
      expect(mockApi.get).toHaveBeenCalledWith(
        "/annual-calendar/api/calendar/events?year=2023&search=",
      );
    });

    it("goToToday should set current date to today and refetch data", async () => {
      const today = new Date();
      jest.spyOn(global, "Date").mockImplementation(() => today); // Mock Date to return a fixed "today"
      mockApi.get.mockResolvedValue({ data: [] }); // Mock for fetchCalendarSummary and fetchEvents

      await act(async () => {
        useCalendarStore.setState({ currentDate: new Date("2020-01-01") }); // Set a different date
        await useCalendarStore.getState().goToToday();
      });

      expect(useCalendarStore.getState().currentDate).toEqual(today);
      expect(mockApi.get).toHaveBeenCalledWith(
        `/annual-calendar/api/calendar/events/summary/${today.getFullYear()}`,
      );
      expect(mockApi.get).toHaveBeenCalledWith(
        `/annual-calendar/api/calendar/events?year=${today.getFullYear()}&search=`,
      );

      jest.spyOn(global, "Date").mockRestore(); // Restore Date mock
    });
  });

  describe("Utility Functions", () => {
    it("getEventById should return the correct event", () => {
      act(() => {
        useCalendarStore.setState({ events: mockEvents });
      });
      const event = useCalendarStore.getState().getEventById("event1");
      expect(event).toEqual(mockEvents[0]);
    });

    it("getEventById should return undefined if event not found", () => {
      act(() => {
        useCalendarStore.setState({ events: mockEvents });
      });
      const event = useCalendarStore.getState().getEventById("nonExistentId");
      expect(event).toBeUndefined();
    });

    it("getEventsForDate should return events for a specific date", () => {
      act(() => {
        useCalendarStore.setState({ filteredEvents: mockEvents });
      });
      const events = useCalendarStore
        .getState()
        .getEventsForDate(new Date("2024-07-20"));
      expect(events).toEqual([mockEvents[0]]);
    });

    it("getEventsForDate should return events spanning multiple days", () => {
      act(() => {
        useCalendarStore.setState({ filteredEvents: mockEvents });
      });
      const events = useCalendarStore
        .getState()
        .getEventsForDate(new Date("2024-07-26"));
      expect(events).toEqual([mockEvents[1]]);
    });

    it("getEventsForDate should return empty array if no events for date", () => {
      act(() => {
        useCalendarStore.setState({ filteredEvents: mockEvents });
      });
      const events = useCalendarStore
        .getState()
        .getEventsForDate(new Date("2024-07-21"));
      expect(events).toEqual([]);
    });

    it("getTotalWorkingDays should return workedDaysPlant from summary", () => {
      act(() => {
        useCalendarStore.setState({ calendarSummary: mockSummary });
      });
      const total = useCalendarStore.getState().getTotalWorkingDays();
      expect(total).toBe(mockSummary.workedDaysPlant);
    });

    it("getTotalWorkingDays should return default if summary is null", () => {
      act(() => {
        useCalendarStore.setState({ calendarSummary: null });
      });
      const total = useCalendarStore.getState().getTotalWorkingDays();
      expect(total).toBe(268); // Default value
    });
  });
});
