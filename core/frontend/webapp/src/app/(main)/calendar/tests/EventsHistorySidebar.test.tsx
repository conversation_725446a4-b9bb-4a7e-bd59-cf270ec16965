"use client";

import { render, screen, fireEvent } from "@testing-library/react";
import EventsHistorySidebar from "../components/EventsHistorySidebar";
import useCalendarStore from "../store/calendarStore";
import { format } from "date-fns";

// Mock the useCalendarStore
jest.mock("../store/calendarStore");

// Mock next-intl's useTranslations
jest.mock("next-intl", () => ({
  useTranslations: jest.fn((namespace: string) => {
    return (key: string, params?: Record<string, string>) => {
      const fullKey = `${namespace}.${key}`;
      switch (fullKey) {
        case "common.calendarView.weekHeader":
          return "Week";
        case "common.calendarView.mon":
          return "Mon";
        case "common.calendarView.tue":
          return "Tue";
        case "common.calendarView.wed":
          return "Wed";
        case "common.calendarView.thu":
          return "Thu";
        case "common.calendarView.fri":
          return "Fri";
        case "common.calendarView.sat":
          return "Sat";
        case "common.calendarView.sun":
          return "Sun";
        case "common.closeButton":
          return "Close";
        // Add other specific translations as needed for the tests
        case "common.calendarHeader.plantCalendar":
          return `Plant Calendar ${params?.year}`;
        case "common.calendarHeader.searchEventsPlaceholder":
          return "Search events...";
        case "common.calendarHeader.addEventButton":
          return "Add Event";
        case "common.calendarHeader.importEventsButton":
          return "Import Events";
        case "common.calendarHeader.historyButton":
          return "History";
        case "common.calendarHeader.loadingCategories":
          return "Loading categories...";
        case "common.calendarHeader.totalWorkingDaysIn":
          return `Total working days in ${params?.year}`;
        case "common.calendarHeader.monthView":
          return "Month View";
        case "common.calendarHeader.yearView":
          return "Year View";
        case "common.eventModal.nameRequiredAlert":
          return "Event name is required.";
        case "common.eventModal.saveErrorAlert":
          return "Failed to save event.";
        case "common.eventModal.editTitle":
          return "Edit Event";
        case "common.eventModal.createTitle":
          return "Create Event";
        case "common.eventModal.eventNameLabel":
          return "Event Name";
        case "common.eventModal.eventNamePlaceholder":
          return "Enter event name";
        case "common.eventModal.categoryLabel":
          return "Category";
        case "common.eventModal.selectCategoryPlaceholder":
          return "Select a category";
        case "common.eventModal.startDateLabel":
          return "Start Date";
        case "common.eventModal.endDateLabel":
          return "End Date";
        case "common.eventModal.pickDatePlaceholder":
          return "Pick a date";
        case "common.eventModal.descriptionLabel":
          return "Description";
        case "common.eventModal.descriptionPlaceholder":
          return "Enter event description";
        case "common.eventModal.updatingButton":
          return "Updating...";
        case "common.eventModal.creatingButton":
          return "Creating...";
        case "common.eventModal.updateButton":
          return "Update";
        case "common.eventModal.saveButton":
          return "Save";
        case "common.deleteModal.title":
          return "Delete Event";
        case "common.deleteModal.confirmationTitle":
          return "Are you sure?";
        case "common.deleteModal.confirmationMessage":
          return `Are you sure you want to delete ${params?.eventName}?`;
        case "common.deleteModal.deleteError":
          return "Failed to delete event.";
        case "common.deleteModal.deletingButton":
          return "Deleting...";
        case "common.deleteModal.deleteButton":
          return "Delete";
        case "common.eventDetailsModal.titleLabel":
          return "Title";
        case "common.eventDetailsModal.dateLabel":
          return "Date";
        case "common.eventDetailsModal.typeLabel":
          return "Type";
        case "common.deleteButton":
          return "Delete";
        case "common.editButton":
          return "Edit";
        case "common.eventDetailsModal.descriptionLabel":
          return "Description";
        case "common.eventsHistorySidebar.title":
          return `Event History ${params?.year}`;
        case "common.eventsHistorySidebar.infoText":
          return "This is an info text.";
        case "common.eventsHistorySidebar.actionLabel":
          return "Action";
        case "common.eventsHistorySidebar.dateLabel":
          return "Date";
        case "common.eventsHistorySidebar.selectActionPlaceholder":
          return "Select action";
        case "common.eventsHistorySidebar.allActions":
          return "All Actions";
        case "common.eventsHistorySidebar.actionCreated":
          return "Created";
        case "common.eventsHistorySidebar.actionUpdated":
          return "Updated";
        case "common.eventsHistorySidebar.actionDeleted":
          return "Deleted";
        case "common.eventsHistorySidebar.actionImported":
          return "Imported";
        case "common.eventsHistorySidebar.pickDatePlaceholder":
          return "Pick a date range";
        case "common.eventsHistorySidebar.loadingHistory":
          return "Loading history...";
        case "common.eventsHistorySidebar.noHistoryFound":
          return "No history found.";
        case "common.eventsHistorySidebar.tryChangingFilter":
          return "Try changing filter.";
        case "common.eventsHistorySidebar.todayGroup":
          return "Today";
        case "common.eventsHistorySidebar.yesterdayGroup":
          return "Yesterday";
        case "common.eventsHistorySidebar.earlierGroup":
          return "Earlier";
        case "common.eventsHistorySidebar.loadMoreButton":
          return "Load More";
        case "common.eventsHistorySidebar.loadingButton":
          return "Loading...";
        case "common.importModal.invalidFileTypeTitle":
          return "Invalid File Type";
        case "common.importModal.invalidFileTypeDescription":
          return "Only CSV and XLSX files are accepted.";
        case "common.importModal.importCompletedWithErrorsTitle":
          return "Import Completed with Errors";
        case "common.importModal.importCompletedWithErrorsDescription":
          return `Imported ${params?.importedCount} events with ${params?.errorsLength} errors.`;
        case "common.importModal.importSuccessfulTitle":
          return "Import Successful";
        case "common.importModal.importSuccessfulDescription":
          return `Successfully imported ${params?.importedCount} events.`;
        case "common.importModal.noFileSelectedTitle":
          return "No File Selected";
        case "common.importModal.noFileSelectedDescription":
          return "Please select a file to import.";
        case "common.importModal.title":
          return "Import Events";
        case "common.importModal.uploadFileLabel":
          return "Upload File";
        case "common.importModal.acceptedFormats":
          return "Accepted formats: .csv, .xlsx";
        case "common.importModal.importingButton":
          return "Importing...";
        case "common.importModal.importButton":
          return "Import";
        case "common.eventHistoryItem.dateTimeLabel":
          return "Date & Time:";
        case "common.eventHistoryItem.eventLabel":
          return "Event:";
        case "common.eventHistoryItem.actionLabel":
          return "Action:";
        case "common.eventHistoryItem.oldValueLabel":
          return "Old Value:";
        case "common.eventHistoryItem.newValueLabel":
          return "New Value:";
        case "common.eventHistoryItem.doneByLabel":
          return "Done By:";
        default:
          return fullKey; // Fallback to full key for untranslated strings
      }
    };
  }),
}));

// Mock the EventHistoryItem component to prevent it from causing issues
// and to ensure its props are correctly passed.
jest.mock("../components/EventHistoryItem", () => {
  // 1) Create a named arrow (or function) component
  const EventHistoryItemMock = ({
    dateTime,
    event,
    action,
    actionType,
    oldValue,
    newValue,
    doneBy,
  }: {
    dateTime: string;
    event: string;
    action: string;
    actionType: string;
    oldValue?: string[];
    newValue?: string[];
    doneBy: string;
  }) => (
    <div data-testid="mock-event-history-item">
      <span data-testid="mock-event-history-item-datetime">{dateTime}</span>
      <span data-testid="mock-event-history-item-event">{event}</span>
      <span data-testid="mock-event-history-item-action">{action}</span>
      {oldValue && (
        <span data-testid="mock-event-history-item-old-value">
          {oldValue.join(",")}
        </span>
      )}
      {newValue && (
        <span data-testid="mock-event-history-item-new-value">
          {newValue.join(",")}
        </span>
      )}
      <span data-testid="mock-event-history-item-done-by">{doneBy}</span>
      <span data-testid="mock-event-history-item-action-type">
        {actionType}
      </span>
    </div>
  );

  // 2) Assign the displayName so ESLint is happy
  EventHistoryItemMock.displayName = "EventHistoryItem";

  // 3) Return it as the module’s default export
  return {
    __esModule: true,
    default: EventHistoryItemMock,
  };
});

const mockHistoryItems = [
  {
    id: "hist-1",
    eventTitle: "Event A created",
    action: "CREATED",
    performedBy: { id: "user-1", name: "John Doe", country: "USA", site: "NY" },
    performedAt: new Date(), // Today
    details: "Event A was created.",
    type: "CREATED",
  },
  {
    id: "hist-2",
    eventTitle: "Event B updated",
    action: "UPDATED",
    performedBy: {
      id: "user-2",
      name: "Jane Smith",
      country: "USA",
      site: "LA",
    },
    performedAt: new Date(new Date().setDate(new Date().getDate() - 1)), // Yesterday
    details: "Event B was updated. Name changed from Old Name to New Name.",
    beforeObject: {
      id: "event-b",
      name: "Old Name",
      year: 2024,
      startDate: "2024-01-01",
      endDate: "2024-01-01",
      description: "",
      country: "USA",
      site: "LA",
    },
    afterObject: {
      id: "event-b",
      name: "New Name",
      year: 2024,
      startDate: "2024-01-01",
      endDate: "2024-01-01",
      description: "",
      country: "USA",
      site: "LA",
    },
    type: "UPDATED",
  },
  {
    id: "hist-3",
    eventTitle: "Event C deleted",
    action: "DELETED",
    performedBy: { id: "user-1", name: "John Doe", country: "USA", site: "NY" },
    performedAt: new Date(new Date().setDate(new Date().getDate() - 5)), // 5 days ago
    details: "Event C was deleted.",
    type: "DELETED",
  },
];

describe("EventsHistorySidebar", () => {
  const mockOnClose = jest.fn();
  const mockFetchEventHistory = jest.fn();
  const mockSetHistoryStartDate = jest.fn();
  const mockSetHistoryEndDate = jest.fn();
  const mockSetHistoryFilterType = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      eventHistory: mockHistoryItems,
      isHistoryLoading: false,
      historyStartDate: new Date(new Date().setDate(new Date().getDate() - 30)),
      historyEndDate: new Date(),
      historyFilterType: "ALL",
      fetchEventHistory: mockFetchEventHistory,
      setHistoryStartDate: mockSetHistoryStartDate,
      setHistoryEndDate: mockSetHistoryEndDate,
      setHistoryFilterType: mockSetHistoryFilterType,
    });
  });

  it.skip("renders correctly when open", () => {
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);

    expect(
      screen.getByText(`Event History ${new Date().getFullYear()}`),
    ).toBeInTheDocument();
    expect(screen.getByText("This is an info text.")).toBeInTheDocument();
    expect(screen.getByText("Action")).toBeInTheDocument();
    expect(screen.getByText("Date")).toBeInTheDocument();
    expect(screen.getByText("Today")).toBeInTheDocument();
    expect(screen.getByText("Yesterday")).toBeInTheDocument();
    expect(
      screen.getByText(format(mockHistoryItems[2].performedAt, "yyyy-MM-dd")),
    ).toBeInTheDocument(); // Older event
    expect(screen.getByRole("button", { name: "Close" })).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Load More" }),
    ).toBeInTheDocument();
  });

  it.skip("does not render when isOpen is false", () => {
    const { container } = render(
      <EventsHistorySidebar isOpen={false} onClose={mockOnClose} />,
    );
    expect(container).toBeEmptyDOMElement();
  });

  it.skip("calls onClose when Close button is clicked", () => {
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);
    fireEvent.click(screen.getByRole("button", { name: "Close" }));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it.skip("calls fetchEventHistory on mount when open", () => {
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);
    expect(mockFetchEventHistory).toHaveBeenCalledTimes(1);
  });

  it.skip("calls setHistoryFilterType when action filter changes", () => {
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);
    const selectTrigger = screen.getByRole("combobox", {
      name: "Select action",
    });
    fireEvent.mouseDown(selectTrigger); // Open the select
    fireEvent.click(screen.getByText("Created")); // Click an option
    expect(mockSetHistoryFilterType).toHaveBeenCalledTimes(1);
    expect(mockSetHistoryFilterType).toHaveBeenCalledWith("CREATED");
  });

  it.skip("displays loading state", () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      eventHistory: [],
      isHistoryLoading: true,
      historyStartDate: null,
      historyEndDate: null,
      historyFilterType: "ALL",
      fetchEventHistory: mockFetchEventHistory,
      setHistoryStartDate: mockSetHistoryStartDate,
      setHistoryEndDate: mockSetHistoryEndDate,
      setHistoryFilterType: mockSetHistoryFilterType,
    });
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);
    expect(screen.getByText("Loading history...")).toBeInTheDocument();
  });

  it.skip('displays "No history found" message when eventHistory is empty', () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      eventHistory: [],
      isHistoryLoading: false,
      historyStartDate: null,
      historyEndDate: null,
      historyFilterType: "ALL",
      fetchEventHistory: mockFetchEventHistory,
      setHistoryStartDate: mockSetHistoryStartDate,
      setHistoryEndDate: mockSetHistoryEndDate,
      setHistoryFilterType: mockSetHistoryFilterType,
    });
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);
    expect(screen.getByText("No history found.")).toBeInTheDocument();
    expect(screen.getByText("Try changing filter.")).toBeInTheDocument();
  });

  it.skip("renders EventHistoryItem components with correct props", () => {
    render(<EventsHistorySidebar isOpen={true} onClose={mockOnClose} />);

    // Check for the first event (CREATED)
    const item1 = screen.getAllByTestId("mock-event-history-item")[0];
    expect(item1).toHaveTextContent(
      format(mockHistoryItems[0].performedAt, "dd MMM yyyy, HH:mm"),
    );
    expect(item1).toHaveTextContent("Event A created");
    expect(item1).toHaveTextContent("Event A was created."); // details
    expect(item1).toHaveTextContent("John Doe");
    expect(item1).toHaveTextContent("CREATED"); // actionType

    // Check for the second event (UPDATED)
    const item2 = screen.getAllByTestId("mock-event-history-item")[1];
    expect(item2).toHaveTextContent(
      format(mockHistoryItems[1].performedAt, "dd MMM yyyy, HH:mm"),
    );
    expect(item2).toHaveTextContent("Event B updated");
    expect(item2).toHaveTextContent("Name changed from Old Name to New Name."); // details
    expect(item2).toHaveTextContent("Jane Smith");
    expect(item2).toHaveTextContent("UPDATED"); // actionType
    expect(item2).toHaveTextContent("Old Name,2024-01-01"); // oldValue
    expect(item2).toHaveTextContent("New Name,2024-01-01"); // newValue

    // Check for the third event (DELETED)
    const item3 = screen.getAllByTestId("mock-event-history-item")[2];
    expect(item3).toHaveTextContent(
      format(mockHistoryItems[2].performedAt, "dd MMM yyyy, HH:mm"),
    );
    expect(item3).toHaveTextContent("Event C deleted");
    expect(item3).toHaveTextContent("Event C was deleted."); // details
    expect(item3).toHaveTextContent("John Doe");
    expect(item3).toHaveTextContent("DELETED"); // actionType
  });
});
