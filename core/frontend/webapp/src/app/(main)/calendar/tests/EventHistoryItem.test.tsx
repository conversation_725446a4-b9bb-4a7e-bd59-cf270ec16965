import { render, screen } from "@testing-library/react";
import EventHistoryItem from "../components/EventHistoryItem";

// Mock next-intl's useTranslations
jest.mock("next-intl", () => ({
  useTranslations: jest.fn((namespace: string) => {
    return (key: string, params?: Record<string, string>) => {
      const fullKey = `${namespace}.${key}`;
      switch (fullKey) {
        case "common.eventHistoryItem.dateTimeLabel":
          return "Date & Time:";
        case "common.eventHistoryItem.eventLabel":
          return "Event:";
        case "common.eventHistoryItem.actionLabel":
          return "Action:";
        case "common.eventHistoryItem.oldValueLabel":
          return "Old Value:";
        case "common.eventHistoryItem.newValueLabel":
          return "New Value:";
        case "common.eventHistoryItem.doneByLabel":
          return "Done By:";
        default:
          // Fallback for any other keys that might be accessed
          let result = `${namespace}.${key}`;
          if (params) {
            for (const [k, v] of Object.entries(params)) {
              result = result.replace(`{${k}}`, v);
            }
          }
          return result;
      }
    };
  }),
}));

describe("EventHistoryItem", () => {
  const baseProps = {
    dateTime: "23 Jul 2024, 10:30",
    event: "Annual Leave Request",
    action: "Created new leave request",
    doneBy: "Alice Johnson",
  };

  it.skip('renders correctly with "added" action type', () => {
    render(<EventHistoryItem {...baseProps} actionType="added" />);

    expect(screen.getByText("Date & Time:")).toBeInTheDocument();
    expect(screen.getByText(baseProps.dateTime)).toBeInTheDocument();
    expect(screen.getByText("Event:")).toBeInTheDocument();
    expect(screen.getByText(baseProps.event)).toBeInTheDocument();
    expect(screen.getByText("Action:")).toBeInTheDocument();
    expect(screen.getByText(baseProps.action)).toBeInTheDocument();
    expect(screen.getByText("Done By:")).toBeInTheDocument();
    expect(screen.getByText(baseProps.doneBy)).toBeInTheDocument();

    // Check for the correct icon (Plus for 'added')
    expect(screen.getByTestId("plus-icon")).toBeInTheDocument();
    expect(screen.getByText(baseProps.action)).toHaveClass("text-green-600");
  });

  it.skip('renders correctly with "changed" action type and old/new values', () => {
    const propsWithChanges = {
      ...baseProps,
      actionType: "changed" as const,
      action: 'Name changed from "Old Name" to "New Name"',
      oldValue: ["Old Name", "2024-01-01"],
      newValue: ["New Name", "2024-01-02"],
    };
    render(<EventHistoryItem {...propsWithChanges} />);

    expect(screen.getByText("Old Value:")).toBeInTheDocument();
    expect(screen.getByText("Old Name")).toBeInTheDocument();
    expect(screen.getByText("2024-01-01")).toBeInTheDocument();
    expect(screen.getByText("New Value:")).toBeInTheDocument();
    expect(screen.getByText("New Name")).toBeInTheDocument();
    expect(screen.getByText("2024-01-02")).toBeInTheDocument();

    // Check for the correct icon (Edit for 'changed')
    expect(screen.getByTestId("edit-icon")).toBeInTheDocument();
    expect(screen.getByText(propsWithChanges.action)).toHaveClass(
      "text-blue-600",
    );
  });

  it.skip('renders correctly with "deleted" action type', () => {
    render(<EventHistoryItem {...baseProps} actionType="deleted" />);

    // Check for the correct icon (Trash2 for 'deleted')
    expect(screen.getByTestId("trash-2-icon")).toBeInTheDocument();
    expect(screen.getByText(baseProps.action)).toHaveClass("text-red-600");
  });

  it.skip("does not render old/new values if not provided", () => {
    render(<EventHistoryItem {...baseProps} actionType="added" />);

    expect(screen.queryByText("Old Value:")).not.toBeInTheDocument();
    expect(screen.queryByText("New Value:")).not.toBeInTheDocument();
  });
});
