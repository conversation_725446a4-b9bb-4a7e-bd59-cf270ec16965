import { render, screen, fireEvent } from "@testing-library/react";
import CalendarView from "../components/CalendarView";
import useCalendarStore from "../store/calendarStore";
import { format } from "date-fns"; // Import jest here

// Mock the useCalendarStore
jest.mock("../store/calendarStore");

// Mock next-intl's useTranslations
jest.mock("next-intl", () => ({
  useTranslations: jest.fn((namespace: string) => {
    return (key: string, params?: Record<string, string>) => {
      const fullKey = `${namespace}.${key}`;
      switch (fullKey) {
        case "common.calendarView.weekHeader":
          return "Week";
        case "common.calendarView.mon":
          return "Mon";
        case "common.calendarView.tue":
          return "Tue";
        case "common.calendarView.wed":
          return "Wed";
        case "common.calendarView.thu":
          return "Thu";
        case "common.calendarView.fri":
          return "Fri";
        case "common.calendarView.sat":
          return "Sat";
        case "common.calendarView.sun":
          return "Sun";
        case "common.closeButton":
          return "Close";
        // Add other specific translations as needed for the tests
        case "common.calendarHeader.plantCalendar":
          return `Plant Calendar ${params?.year}`;
        case "common.calendarHeader.searchEventsPlaceholder":
          return "Search events...";
        case "common.calendarHeader.addEventButton":
          return "Add Event";
        case "common.calendarHeader.importEventsButton":
          return "Import Events";
        case "common.calendarHeader.historyButton":
          return "History";
        case "common.calendarHeader.loadingCategories":
          return "Loading categories...";
        case "common.calendarHeader.totalWorkingDaysIn":
          return `Total working days in ${params?.year}`;
        case "common.calendarHeader.monthView":
          return "Month View";
        case "common.calendarHeader.yearView":
          return "Year View";
        case "common.eventModal.nameRequiredAlert":
          return "Event name is required.";
        case "common.eventModal.saveErrorAlert":
          return "Failed to save event.";
        case "common.eventModal.editTitle":
          return "Edit Event";
        case "common.eventModal.createTitle":
          return "Create Event";
        case "common.eventModal.eventNameLabel":
          return "Event Name";
        case "common.eventModal.eventNamePlaceholder":
          return "Enter event name";
        case "common.eventModal.categoryLabel":
          return "Category";
        case "common.eventModal.selectCategoryPlaceholder":
          return "Select a category";
        case "common.eventModal.startDateLabel":
          return "Start Date";
        case "common.eventModal.endDateLabel":
          return "End Date";
        case "common.eventModal.pickDatePlaceholder":
          return "Pick a date";
        case "common.eventModal.descriptionLabel":
          return "Description";
        case "common.eventModal.descriptionPlaceholder":
          return "Enter event description";
        case "common.eventModal.updatingButton":
          return "Updating...";
        case "common.eventModal.creatingButton":
          return "Creating...";
        case "common.eventModal.updateButton":
          return "Update";
        case "common.eventModal.saveButton":
          return "Save";
        case "common.deleteModal.title":
          return "Delete Event";
        case "common.deleteModal.confirmationTitle":
          return "Are you sure?";
        case "common.deleteModal.confirmationMessage":
          return `Are you sure you want to delete ${params?.eventName}?`;
        case "common.deleteModal.deleteError":
          return "Failed to delete event.";
        case "common.deleteModal.deletingButton":
          return "Deleting...";
        case "common.deleteModal.deleteButton":
          return "Delete";
        case "common.eventDetailsModal.titleLabel":
          return "Title";
        case "common.eventDetailsModal.dateLabel":
          return "Date";
        case "common.eventDetailsModal.typeLabel":
          return "Type";
        case "common.deleteButton":
          return "Delete"; // Corrected from common.common.deleteButton
        case "common.editButton":
          return "Edit";
        case "common.eventDetailsModal.descriptionLabel":
          return "Description";
        case "common.eventsHistorySidebar.title":
          return `Event History ${params?.year}`;
        case "common.eventsHistorySidebar.infoText":
          return "This is an info text.";
        case "common.eventsHistorySidebar.actionLabel":
          return "Action";
        case "common.eventsHistorySidebar.dateLabel":
          return "Date";
        case "common.eventsHistorySidebar.selectActionPlaceholder":
          return "Select action";
        case "common.eventsHistorySidebar.allActions":
          return "All Actions";
        case "common.eventsHistorySidebar.actionCreated":
          return "Created";
        case "common.eventsHistorySidebar.actionUpdated":
          return "Updated";
        case "common.eventsHistorySidebar.actionDeleted":
          return "Deleted";
        case "common.eventsHistorySidebar.actionImported":
          return "Imported";
        case "common.eventsHistorySidebar.pickDatePlaceholder":
          return "Pick a date range";
        case "common.eventsHistorySidebar.loadingHistory":
          return "Loading history...";
        case "common.eventsHistorySidebar.noHistoryFound":
          return "No history found.";
        case "common.eventsHistorySidebar.tryChangingFilter":
          return "Try changing filter.";
        case "common.eventsHistorySidebar.todayGroup":
          return "Today";
        case "common.eventsHistorySidebar.yesterdayGroup":
          return "Yesterday";
        case "common.eventsHistorySidebar.earlierGroup":
          return "Earlier";
        case "common.eventsHistorySidebar.loadMoreButton":
          return "Load More";
        case "common.eventsHistorySidebar.loadingButton":
          return "Loading...";
        case "common.importModal.invalidFileTypeTitle":
          return "Invalid File Type";
        case "common.importModal.invalidFileTypeDescription":
          return "Only CSV and XLSX files are accepted.";
        case "common.importModal.importCompletedWithErrorsTitle":
          return "Import Completed with Errors";
        case "common.importModal.importCompletedWithErrorsDescription":
          return `Imported ${params?.importedCount} events with ${params?.errorsLength} errors.`;
        case "common.importModal.importSuccessfulTitle":
          return "Import Successful";
        case "common.importModal.importSuccessfulDescription":
          return `Successfully imported ${params?.importedCount} events.`;
        case "common.importModal.noFileSelectedTitle":
          return "No File Selected";
        case "common.importModal.noFileSelectedDescription":
          return "Please select a file to import.";
        case "common.importModal.title":
          return "Import Events";
        case "common.importModal.uploadFileLabel":
          return "Upload File";
        case "common.importModal.acceptedFormats":
          return "Accepted formats: .csv, .xlsx";
        case "common.importModal.importingButton":
          return "Importing...";
        case "common.importModal.importButton":
          return "Import";
        case "common.eventHistoryItem.dateTimeLabel":
          return "Date & Time:";
        case "common.eventHistoryItem.eventLabel":
          return "Event:";
        case "common.eventHistoryItem.actionLabel":
          return "Action:";
        case "common.eventHistoryItem.oldValueLabel":
          return "Old Value:";
        case "common.eventHistoryItem.newValueLabel":
          return "New Value:";
        case "common.eventHistoryItem.doneByLabel":
          return "Done By:";
        default:
          return fullKey; // Fallback to full key for untranslated strings
      }
    };
  }),
}));

const mockEvents = [
  {
    id: "event-1",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Meeting",
    description: "Team sync",
    category: {
      id: "cat-1",
      name: "Work",
      color: "#FF0000",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-2",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-22",
    name: "Project Deadline",
    description: "Final submission",
    category: {
      id: "cat-2",
      name: "Deadline",
      color: "#0000FF",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-3",
    year: 2024,
    startDate: "2024-07-25",
    endDate: "2024-07-25",
    name: "Holiday",
    description: "National holiday",
    category: {
      id: "cat-3",
      name: "Holiday",
      color: "#00FF00",
      isWorkingHoliday: false,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-4",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Extra Event 1",
    description: "",
    category: {
      id: "cat-1",
      name: "Work",
      color: "#FF0000",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-5",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Extra Event 2",
    description: "",
    category: {
      id: "cat-1",
      name: "Work",
      color: "#FF0000",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-6",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Extra Event 3",
    description: "",
    category: {
      id: "cat-1",
      name: "Work",
      color: "#FF0000",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-7",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Extra Event 4",
    description: "",
    category: {
      id: "cat-1",
      name: "Work",
      color: "#FF0000",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
  {
    id: "event-8",
    year: 2024,
    startDate: "2024-07-20",
    endDate: "2024-07-20",
    name: "Extra Event 5",
    description: "",
    category: {
      id: "cat-1",
      name: "Work",
      color: "#FF0000",
      isWorkingHoliday: true,
      country: "USA",
      site: "NY",
    },
    country: "USA",
    site: "NY",
  },
];

describe("CalendarView", () => {
  const mockSetCurrentDate = jest.fn();
  const mockSetSelectedEventId = jest.fn();
  const mockOpenEventModal = jest.fn();
  const mockOpenEventDetailsModal = jest.fn();
  const mockGetEventsForDate = jest.fn((date: Date) => {
    const targetDate = format(date, "yyyy-MM-dd");
    return mockEvents.filter(
      (event) => targetDate >= event.startDate && targetDate <= event.endDate,
    );
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      currentDate: new Date(2024, 6, 15), // July 15, 2024
      viewMode: { mode: "month", date: new Date(2024, 6, 15) },
      setCurrentDate: mockSetCurrentDate,
      setSelectedEventId: mockSetSelectedEventId,
      openEventModal: mockOpenEventModal,
      openEventDetailsModal: mockOpenEventDetailsModal,
      getEventsForDate: mockGetEventsForDate,
    });
  });

  it.skip("renders month view by default", () => {
    render(<CalendarView />);
    expect(screen.getByText("July")).toBeInTheDocument();
    expect(screen.getByText("Mon")).toBeInTheDocument();
    expect(screen.getByText("Sun")).toBeInTheDocument();
    expect(screen.queryByText("January")).not.toBeInTheDocument(); // Not in year view
  });

  it('renders year view when viewMode is "year"', () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      ...useCalendarStore(),
      viewMode: { mode: "year", date: new Date(2024, 6, 15) },
    });
    render(<CalendarView />);
    expect(screen.getByText("January")).toBeInTheDocument();
    expect(screen.getByText("February")).toBeInTheDocument();
    expect(screen.getByText("December")).toBeInTheDocument();
    expect(screen.queryByText("calendarView.mon")).not.toBeInTheDocument(); // Not in month view header
  });

  it("calls openEventModal when a day is clicked", () => {
    render(<CalendarView />);
    const day20 = screen.getAllByText("20")[0]; // Find the day 20
    fireEvent.click(day20);
    expect(mockOpenEventModal).toHaveBeenCalledTimes(1);
    expect(mockOpenEventModal).toHaveBeenCalledWith(
      undefined,
      expect.any(Date),
    );
  });

  it("calls openEventDetailsModal when an event dot is clicked", () => {
    render(<CalendarView />);
    const eventDot = screen.getAllByTitle("Meeting")[0]; // Find an event dot
    fireEvent.click(eventDot);
    expect(mockOpenEventDetailsModal).toHaveBeenCalledTimes(1);
    expect(mockOpenEventDetailsModal).toHaveBeenCalledWith("event-1");
  });

  it.skip("opens and closes hidden events modal", () => {
    render(<CalendarView />);
    const day20 = screen.getAllByText("20")[0].closest(".flex-col"); // Get the day container for July 20

    // Assuming there are more than 4 events for July 20 in full size view
    const hiddenEventsTrigger = day20?.querySelector(".rounded-full"); // The '+X' element

    if (hiddenEventsTrigger) {
      fireEvent.click(hiddenEventsTrigger);
      expect(
        screen.getByText(format(new Date(2024, 6, 20), "MMMM d, yyyy")),
      ).toBeInTheDocument(); // Hidden events modal title
      expect(screen.getByText("Extra Event 1")).toBeInTheDocument();
      expect(screen.getByText("Extra Event 2")).toBeInTheDocument();
      expect(screen.getByText("Extra Event 3")).toBeInTheDocument();
      expect(screen.getByText("Extra Event 4")).toBeInTheDocument();
      expect(screen.getByText("Extra Event 5")).toBeInTheDocument();

      fireEvent.click(
        screen.getByRole("button", { name: "common.closeButton" }),
      );
      expect(screen.queryByText("July 20, 2024")).not.toBeInTheDocument(); // Modal should be closed
    } else {
      // If the trigger is not found, it means there aren't enough events to trigger it.
      // This might happen if the mockEvents array or the slice logic changes.
      console.warn(
        "Hidden events trigger not found. Check mockEvents or slice logic.",
      );
    }
  });

  it("calls setCurrentDate when month title is clicked in year view", () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      ...useCalendarStore(),
      viewMode: { mode: "year", date: new Date(2024, 6, 15) },
    });
    render(<CalendarView />);
    const januaryMonthTitle = screen.getByText("January");
    fireEvent.click(januaryMonthTitle);
    expect(mockSetCurrentDate).toHaveBeenCalledTimes(1);
    expect(mockSetCurrentDate).toHaveBeenCalledWith(new Date(2024, 0, 1)); // January 1st, 2024
  });
});
