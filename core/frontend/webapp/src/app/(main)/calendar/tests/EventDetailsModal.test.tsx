import { render, screen, fireEvent } from "@testing-library/react";
import EventDetailsModal from "../components/EventDetailsModal";
import useCalendarStore from "../store/calendarStore";

// Mock the useCalendarStore
jest.mock("../store/calendarStore");

// Mock next-intl's useTranslations
jest.mock("next-intl", () => ({
  useTranslations: jest.fn(
    (key: string) => (str: string, params?: Record<string, string>) => {
      let result = `${key}.${str}`;
      if (params) {
        for (const [k, v] of Object.entries(params)) {
          result = result.replace(`{${k}}`, v);
        }
      }
      return result;
    },
  ),
}));

const mockEvent = {
  id: "event-1",
  year: 2024,
  startDate: "2024-07-20T10:00:00Z",
  endDate: "2024-07-20T12:00:00Z",
  name: "Team Meeting",
  description: "Discuss Q3 strategy.",
  category: {
    id: "cat-1",
    name: "Work",
    color: "#FF0000",
    isWorkingHoliday: true,
    country: "USA",
    site: "NY",
  },
  country: "USA",
  site: "NY",
};

describe("EventDetailsModal", () => {
  const mockCloseEventDetailsModal = jest.fn();
  const mockOpenEventModal = jest.fn();
  const mockOpenDeleteModal = jest.fn();
  const mockGetEventById = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      isEventDetailsModalOpen: true,
      selectedEventId: mockEvent.id,
      isEventLoading: false,
      closeEventDetailsModal: mockCloseEventDetailsModal,
      openEventModal: mockOpenEventModal,
      openDeleteModal: mockOpenDeleteModal,
      getEventById: mockGetEventById,
    });
    mockGetEventById.mockReturnValue(mockEvent);
  });

  it.skip("renders correctly when open with an event", () => {
    render(<EventDetailsModal />);

    expect(screen.getByText("Team Meeting")).toBeInTheDocument();
    expect(
      screen.getByText("eventDetailsModal.titleLabel"),
    ).toBeInTheDocument();
    expect(screen.getByText("eventDetailsModal.dateLabel")).toBeInTheDocument();
    expect(screen.getByText("July 20, 2024")).toBeInTheDocument();
    expect(screen.getByText("eventDetailsModal.typeLabel")).toBeInTheDocument();
    expect(screen.getByText("Work")).toBeInTheDocument();
    expect(
      screen.getByText("eventDetailsModal.descriptionLabel"),
    ).toBeInTheDocument();
    expect(screen.getByText("Discuss Q3 strategy.")).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "common.closeButton" }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "common.deleteButton" }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "common.editButton" }),
    ).toBeInTheDocument();
  });

  it.skip("does not render when selectedEventId is null", () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      isEventDetailsModalOpen: true,
      selectedEventId: null,
      isEventLoading: false,
      closeEventDetailsModal: mockCloseEventDetailsModal,
      openEventModal: mockOpenEventModal,
      openDeleteModal: mockOpenDeleteModal,
      getEventById: mockGetEventById,
    });
    mockGetEventById.mockReturnValue(null);

    const { container } = render(<EventDetailsModal />);
    expect(container).toBeEmptyDOMElement();
  });

  it.skip("calls closeEventDetailsModal when Close button is clicked", () => {
    render(<EventDetailsModal />);
    fireEvent.click(screen.getByRole("button", { name: "common.closeButton" }));
    expect(mockCloseEventDetailsModal).toHaveBeenCalledTimes(1);
  });

  it.skip("calls closeEventDetailsModal and openEventModal when Edit button is clicked", () => {
    render(<EventDetailsModal />);
    fireEvent.click(screen.getByRole("button", { name: "common.editButton" }));
    expect(mockCloseEventDetailsModal).toHaveBeenCalledTimes(1);
    expect(mockOpenEventModal).toHaveBeenCalledTimes(1);
    expect(mockOpenEventModal).toHaveBeenCalledWith(mockEvent.id);
  });

  it.skip("calls openDeleteModal when Delete button is clicked", () => {
    render(<EventDetailsModal />);
    fireEvent.click(
      screen.getByRole("button", { name: "common.deleteButton" }),
    );
    expect(mockOpenDeleteModal).toHaveBeenCalledTimes(1);
    expect(mockOpenDeleteModal).toHaveBeenCalledWith(mockEvent.id);
  });

  it.skip("disables buttons when event is loading", () => {
    (useCalendarStore as unknown as jest.Mock).mockReturnValue({
      isEventDetailsModalOpen: true,
      selectedEventId: mockEvent.id,
      isEventLoading: true, // Set loading to true
      closeEventDetailsModal: mockCloseEventDetailsModal,
      openEventModal: mockOpenEventModal,
      openDeleteModal: mockOpenDeleteModal,
      getEventById: mockGetEventById,
    });
    mockGetEventById.mockReturnValue(mockEvent);

    render(<EventDetailsModal />);

    expect(
      screen.getByRole("button", { name: "common.closeButton" }),
    ).toBeDisabled();
    expect(
      screen.getByRole("button", { name: "common.deleteButton" }),
    ).toBeDisabled();
    expect(
      screen.getByRole("button", { name: "common.editButton" }),
    ).toBeDisabled();
  });
});
