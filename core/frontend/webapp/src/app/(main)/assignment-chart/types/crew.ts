export type CrewMember = {
  id: string;
  legacyId?: string;
  legacySiteId: string;
  name: string;
  surname?: string;
  department: string;
  category: string;
  function: string;
  role: string;
  roleStatus?: string;
  assignedCount?: number;
  children?: CrewMember[];
  backup?: CrewMember[];
};

export type Worker = {
  id: string;
  legacyId?: string;
  legacySiteId: string;
  name: string;
  role: string;
  surname: string;
  department: string;
  category: string;
  skills: string[];
  function: string;
};
