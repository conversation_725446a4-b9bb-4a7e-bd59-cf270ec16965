"use client";

import { useEffect, useState } from "react";
import CrewTree from "./components/CrewTree";
import AssignmentPanel from "./components/AssignmentPanel";
import { Separator } from "@/components/ui/separator";
import { useCrewStore } from "./store/assignmentStore";
import useIsSmallScreen from "@/hooks/useIsSmallScreen";
import { ReusableButton } from "@/components/common/CustomButtons";
import { UserRole } from "@/enum/rolesEnum";
import useAuthStore from "@/store/old";
import { useMockAuthStore } from "@/store/mockAuthStore";

export default function CrewManagementT() {
  const { selectedCrewMember, selectedDepartment } = useCrewStore();
  const { user, isAuthenticated } = useAuthStore();
  const { currentUser } = useMockAuthStore();

  const [role, setRole] = useState<UserRole | null>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const isSmallScreen = useIsSmallScreen();

  useEffect(() => {
    if (isAuthenticated && user?.role) {
      console.log("Setting role:", user.role);
      setRole(user.role as UserRole);
    }
  }, [isAuthenticated, user]);

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  return (
    <div className="relative mx-auto w-full max-w-full space-y-8 p-6 lg:p-8">
      {/* Breadcrumbs or header */}
      {/* Remove translation for header.breadcrumbs.assignmentChart */}
      {/* <h1>{t("header.breadcrumbs.assignmentChart")}</h1> */}
      <div className="flex space-x-8 rounded-lg border bg-white p-6 shadow-sm">
        <div className="flex-1 relative pt-2">
          {currentUser?.role === "trainer" && !selectedDepartment && (
            <div className="mb-3 p-2 bg-yellow-100 border border-yellow-300 text-yellow-800 rounded">
              Please select a department before assigning workers.
            </div>
          )}
          <div
            style={{
              display:
                currentUser?.role === "trainer" && !selectedDepartment
                  ? "none"
                  : undefined,
            }}
          >
            <CrewTree />
          </div>
        </div>
        {!isSmallScreen && (
          <>
            <Separator
              orientation="vertical"
              className="self-stretch border-gray-300"
            />
            <div className="w-1/3">
              <AssignmentPanel
                role={role ?? undefined}
                onClose={toggleDrawer}
              />
            </div>
          </>
        )}
      </div>
      {isSmallScreen && isDrawerOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-10 z-50">
          <div className="fixed right-0 top-0 bottom-0 w-1/2 bg-white shadow-lg p-6">
            <AssignmentPanel role={role ?? undefined} onClose={toggleDrawer} />
          </div>
        </div>
      )}
      {isSmallScreen && (
        <ReusableButton
          className="fixed bottom-4 right-4"
          onClick={toggleDrawer}
          label={`Assign Workers (${useCrewStore.getState().filteredWorkers.length})`}
          disabled={
            currentUser?.role === "trainer" ? false : !selectedCrewMember
          }
        />
      )}
    </div>
  );
}
