/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON><PERSON>, Worker } from "../types/crew";
import api from "@/lib/axios";
import { useMockAuthStore } from "@/store/mockAuthStore";
// import useAuthStore from "@/store/old";

// Helper function to get current user ID for API headers
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID when no mock user is selected
  return "908_MAR Morocco 3"; // Default user ID
};

// Helper function to extract message from backend response
const extractMessageFromResponse = (responseData: unknown): string | null => {
  if (!responseData || typeof responseData !== "object") {
    return null;
  }

  const data = responseData as Record<string, unknown>;

  // Check if response has a message field
  if (data?.message && typeof data.message === "string") {
    return data.message;
  }

  // Check if message is nested in details
  if (
    data?.details &&
    typeof data.details === "object" &&
    data.details !== null
  ) {
    const details = data.details as Record<string, unknown>;
    if (details.message && typeof details.message === "string") {
      return details.message;
    }

    if (details.error && typeof details.error === "string") {
      return details.error;
    }

    if (details.errors && Array.isArray(details.errors) && details.errors[0]) {
      const error = details.errors[0] as Record<string, unknown>;
      if (
        error.field &&
        error.constraints &&
        Array.isArray(error.constraints) &&
        error.constraints[0]
      ) {
        return `${error.field}: ${error.constraints[0]}`;
      }
    }
  }

  // Return null if no meaningful message is found
  return null;
};

interface CrewState {
  crew: CrewMember | null;
  unassignedWorkers: Worker[];
  filteredShiftLeaders: CrewMember[] | null;
  filteredWorkers: Worker[];
  selectedCrewMember: CrewMember | null;
  selectedWorkers: Set<string>;
  selectedAssignedWorkers: Set<string>;
  isLoading: boolean;
  error: string | null;
  selectedCategory: string;
  selectedDepartment: string;
  selectedSkills: string[];
  selectedFunction: string;

  // Pagination state
  unassignedWorkersPagination: {
    currentPage: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  // Actions
  clearCrew: () => void;
  fetchCreww: (userId: string) => Promise<CrewMember[]>;
  fetchOperators: (employeeId: string) => Promise<Worker[]>;
  fetchUnassignedWorkers: (
    userId: string,
    pageNumber?: number,
    pageSize?: number,
  ) => Promise<void>;
  toggleWorkerSelection: (workerId: string) => void;
  toggleAssignedWorkerSelection: (workerId: string) => void;
  selectWorkers: (workerIds: string[]) => void;
  selectCrewMember: (crew: CrewMember | null) => void;
  selectAllAssignedWorkers: (workerIds?: string[]) => void;
  clearWorkerSelection: () => void;
  clearAssignedWorkerSelection: () => void;
  clearAssignedWorkersOnly: () => void; // new: clear only assigned workers (crew tree) without touching unassigned selection
  clearCrewMemberSelection: () => void;
  setDepartment: (department: string) => void;
  setSkills: (skills: string[]) => void;
  setCategory: (category: string) => void;
  updateCrew: (updatedCrew: CrewMember) => void;
  updateCrewNodeChildren: (nodeId: string, newChildren: CrewMember[]) => void;
  handleConfirmReassignment: (
    onClose: () => void,
    userId: string,
    newTeamName: string,
  ) => Promise<void>;

  handleConfirmAssignment: (
    onClose: () => void,
    userId: string,
  ) => Promise<void>;

  assignWorkers: (
    payload: {
      assignFromId: string;
      assignToId: string;
      operatorsIds: string[];
    },
    callback?: () => void,
  ) => Promise<void>;
  setFunction: (func: string) => void;
  filterWorkers: () => void;
  handleAssignToBackup: (onClose: () => void, userId: string) => Promise<void>;
  setUnassignedWorkersPagination: (
    pageNumber: number,
    pageSize: number,
  ) => void;
  fetchRoles: () => Promise<{ value: string; label: string }[]>;
  fetchUnRoles: () => Promise<{ value: string; label: string }[]>;
  fetchSkills: () => Promise<{ value: string; label: string }[]>; // <-- add this
  fetchDepartments: () => Promise<{ value: string; label: string }[]>; // <-- add this
  fetchedOperators: Worker[]; // <-- add this
}

export const useCrewStore = create<CrewState>((set, get) => ({
  crew: null,
  unassignedWorkers: [],
  filteredWorkers: [],
  filteredShiftLeaders: null,
  selectedWorkers: new Set(),
  selectedAssignedWorkers: new Set(),
  isLoading: false,
  error: null,
  selectedDepartment: "",
  selectedCategory: "",
  selectedCrewMember: null,
  selectedSkills: [],
  selectedFunction: "",
  fetchedOperators: [], // <-- add this

  clearCrew: () => set({ crew: null }),
  // Initialize pagination state
  unassignedWorkersPagination: {
    currentPage: 1,
    pageSize: 5,
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },

  updateCrew: (updatedCrew) => {
    set({ crew: updatedCrew });
  },
  updateCrewNodeChildren: (nodeId, newChildren) => {
    const state = get();
    if (state.crew) {
      const updatedTree = updateNodeChildren(state.crew, nodeId, newChildren);
      set({ crew: updatedTree });
    }
  },

  fetchCreww: async (userId: string): Promise<CrewMember[]> => {
    try {
      const currentUserId = getCurrentUserId();
      const { data } = await api.get(
        `direct-dependents/api/organization-chart-Assignment/user/${userId}`,
        {
          params: {
            connected_user: currentUserId,
          },
        },
      );

      // Separate backup and non-backup
      const backupOperators = data
        .filter((item: any) => item.roleStatus === "backup shiftleader")
        .map((item: any) => {
          const [name, surname = ""] = (item.fullname || "").split(",");
          return {
            id: item.id, // Use UUID as the primary ID
            legacyId: (item.legacyId || "").trim(),
            legacySiteId: (item.legacySiteId || "").trim(),
            name: (item.firstName || name).trim(),
            surname: (item.lastName || surname).trim(),
            role: item.role || "",
            department: "",
            category: "",
            function: "",
            children: [],
          };
        });

      const nonBackupChildren = data
        .filter((item: any) => item.roleStatus !== "back up list")
        .map((item: any) => {
          const [name, surname = ""] = (item.fullname || "").split(" ");
          return {
            id: item.id, // Use UUID as the primary ID
            legacyId: (item.legacyId || "").trim(),
            legacySiteId: (item.legacySiteId || "").trim(),
            name: (item.firstName || name).trim(),
            surname: (item.lastName || surname).trim(),
            role: item.role || "",
            roleStatus: item.roleStatus || "",
            department: item.department,
            category: "",
            function: "",
            children: (item.children || []).map((child: any) => ({
              id: child.id, // Use UUID as the primary ID
              legacyId: (child.legacyId || "").trim(),
              legacySiteId: (child.legacySiteId || "").trim(),
              name: (child.firstName || "").trim(),
              surname: (child.lastName || "").trim(),
              role: child.role || "",
              roleStatus: child.roleStatus || "",
              department: child.department || "",
              category: child.category || "",
              function: child.function || "",
              children: [],
            })),
          };
        });

      // Combine real backup operators with mock data
      const allBackupOperators = [...backupOperators];

      // Build the backup node if there are backup operators or mock data
      const backupNode =
        allBackupOperators.length > 0
          ? [
              {
                id: "",
                legacyId: "",
                legacySiteId: "",
                name: "Back",
                surname: "UP",
                role: "BACKUP",
                department: "",
                category: "",
                function: "",
                children: allBackupOperators,
              },
            ]
          : [];

      const crewObj = {
        id: userId || "", // keep as is
        legacyId: userId || "",
        legacySiteId: userId || "",
        name: "",
        surname: "",
        role: "",
        department: "",
        category: "",
        function: "",
        children: nonBackupChildren,
        backup: backupNode,
      };

      set({
        crew: crewObj,
      });

      return nonBackupChildren;
    } catch (error: any) {
      console.error("Failed to fetch children:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      return [];
    }
  },
  fetchOperators: async (employeeId: string): Promise<Worker[]> => {
    try {
      const currentUserId = getCurrentUserId();
      const { data } = await api.get(
        `/direct-dependents/api/organization-chart-Assignment/workers/${currentUserId}/${employeeId}`,
      );

      const operators = data.map((op: any) => {
        // Safely split fullname with fallback
        const fullname = op.fullname || "";
        const [name, surname] = fullname.split(" ");
        return {
          id: op.id, // Use UUID as the primary ID
          legacyId: op.legacyId,
          legacySiteId: op.legacySiteId,
          name: op.firstName || name || "",
          surname: op.lastName || surname || "",
          role: op.role, // "Operator"
          department: op.department || "",
          category: op.category || "",
          function: op.function || "",
        };
      });

      set({ fetchedOperators: operators }); // <-- set fetchedOperators in state

      return operators;
    } catch (error: any) {
      console.error("Failed to fetch operators:", error); // Check if this is a team-related fetch to show appropriate message
      return [];
    }
  }, // Fetch Unassigned Workers
  fetchUnassignedWorkers: async (
    userId: string,
    pageNumber = 1,
    pageSize = 5,
  ) => {
    // Clear workers before fetching
    set({
      unassignedWorkers: [],
      filteredWorkers: [],
      isLoading: true,
      error: null,
    });

    try {
      const currentUserId = getCurrentUserId();
      const response = await api.get(
        `/direct-dependents/api/organization-chart-Assignment/unassigned-workers/${userId}`,
        {
          params: {
            connected_user: currentUserId,
            pageNumber,
            pageSize,
          },
        },
      );

      // Adapt for paginated response: response.data.items is the array
      const items = response.data.items || [];
      const meta = response.data.meta || {};

      const mappedWorkers = items.map((item: any) => {
        return {
          id: item.id, // Use UUID as the primary ID
          legacyId: item.legacyId,
          legacySiteId: item.legacySiteId,
          name: item.firstName || "",
          surname: item.lastName || "",
          role: item.role,
          department: item.department || "",
          category: item.category || "",
          function: item.function || "",
          skills: item.skills || [],
        };
      });

      set({
        unassignedWorkers: mappedWorkers,
        filteredWorkers: mappedWorkers,
        isLoading: false,
        unassignedWorkersPagination: {
          currentPage: meta.currentPage || pageNumber,
          pageSize: meta.pageSize || pageSize,
          totalItems: meta.totalCount || 0,
          totalPages: meta.totalPages || 0,
          hasNextPage: meta.hasNextPage || false,
          hasPreviousPage: meta.hasPreviousPage || false,
        },
      });
    } catch (error: any) {
      console.error("Failed to fetch unassigned workers:", error);

      // toast({
      //   title: "Error",
      //   description:
      //     error?.response?.data?.message ||
      //     "Failed to fetch unassigned workers",
      //   variant: "destructive",
      // });

      set({ error: "Failed to fetch unassigned workers", isLoading: false });
    }
  },

  // Select Crew Member
  selectCrewMember: (member: CrewMember | null) => {
    const state = get();
    // Only clear selection if switching to a different member
    if (
      state.selectedCrewMember &&
      member &&
      state.selectedCrewMember.id === member.id
    ) {
      set({ selectedCrewMember: member });
    } else {
      set({
        selectedCrewMember: member,
      });
    }
  },

  // Toggle Worker Selection
  toggleWorkerSelection: (workerId) => {
    set((state) => {
      const newSelection = new Set(state.selectedWorkers);
      if (newSelection.has(workerId)) {
        newSelection.delete(workerId);
      } else {
        newSelection.add(workerId);
      }
      // Always clear selectedAssignedWorkers when selecting workers
      return {
        selectedWorkers: newSelection,
        selectedAssignedWorkers: new Set(),
      };
    });
  },

  // Toggle Assigned Worker Selection
  toggleAssignedWorkerSelection: (workerId) => {
    set((state) => {
      const newSelection = new Set(state.selectedAssignedWorkers);
      if (newSelection.has(workerId)) {
        newSelection.delete(workerId);
      } else {
        newSelection.add(workerId);
      }
      // Always clear selectedWorkers when selecting assigned workers
      return {
        selectedAssignedWorkers: newSelection,
        selectedWorkers: new Set(),
      };
    });
  },

  selectWorkers: (workerIds) => {
    // Always clear selectedAssignedWorkers when selecting workers
    set({
      selectedWorkers: new Set(workerIds),
      selectedAssignedWorkers: new Set(),
    });
  },

  selectAllAssignedWorkers: (workerIds = []) => {
    // Always clear selectedWorkers when selecting assigned workers
    set({
      selectedAssignedWorkers: new Set(workerIds),
      selectedWorkers: new Set(),
    });
  },

  clearWorkerSelection: () => {
    // Always clear selectedAssignedWorkers when clearing worker selection
    set({ selectedWorkers: new Set(), selectedAssignedWorkers: new Set() });
  },

  clearAssignedWorkerSelection: () => {
    set({ selectedAssignedWorkers: new Set(), selectedWorkers: new Set() });
  },

  clearAssignedWorkersOnly: () => {
    // Only clear assigned workers (crew tree selection) and keep unassigned workers selection intact
    set({ selectedAssignedWorkers: new Set() });
  },

  assignWorkers: async (payload, callback) => {
    set({ isLoading: true, error: null });

    try {
      const state = get();

      const isTeamAssignment = state.selectedCrewMember?.role === "team";

      const requestBody = isTeamAssignment
        ? {
            teamId: state.selectedCrewMember?.id || "",
            assignees: payload.operatorsIds.map((id) => ({
              id: id,
            })),
          }
        : {
            assignTo: {
              id: payload.assignToId,
            },
            assignees: payload.operatorsIds.map((id) => ({
              id: id,
            })),
          };
      const endpoint = isTeamAssignment
        ? "/employee-assignment/assignments/assign-to-team"
        : "/employee-assignment/assignments/assign-to-employee";

      const currentUserId = getCurrentUserId();
      const response = await api.post(endpoint, requestBody, {
        params: {
          connected_user: currentUserId,
        },
      });

      const message = extractMessageFromResponse(response?.data);
      if (message) {
        toast({
          title: "Success",
          description: message,
          variant: "success",
        });
      }

      if (callback) callback();
      get().fetchCreww(payload.assignFromId);

      const { currentPage, pageSize } = get().unassignedWorkersPagination;
      get().fetchUnassignedWorkers(payload.assignFromId, currentPage, pageSize);

      set({ selectedWorkers: new Set(), isLoading: false });
    } catch (error: any) {
      console.error("Failed to assign workers:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      set({ error: "Failed to assign workers", isLoading: false });
    }
  },

  clearCrewMemberSelection: () => {
    set({ selectedCrewMember: null, selectedAssignedWorkers: new Set() });
  },

  setDepartment: (department) => {
    set({ selectedDepartment: department });

    const state = get();

    if (!department) {
      set({
        filteredWorkers: state.unassignedWorkers,
        filteredShiftLeaders: null,
      });
      return;
    }

    const filteredWorkers = state.unassignedWorkers.filter(
      (worker) =>
        (worker.department || "").trim().toLowerCase() ===
        department.trim().toLowerCase(),
    );

    // Filter shift leaders by department
    let filteredShiftLeaders = (state.crew?.children || []).filter(
      (shiftLeader) =>
        (shiftLeader.department || "").trim().toLowerCase() ===
        department.trim().toLowerCase(),
    );

    if (filteredShiftLeaders.length === 0) {
      filteredShiftLeaders = [];
    }

    set({
      filteredWorkers,
      filteredShiftLeaders,
    });
  },

  setSkills: (skills: string[]) => {
    set({ selectedSkills: skills });
    get().filterWorkers();
  },

  setFunction: (func) => {
    set({ selectedFunction: func });
    get().filterWorkers();
  },
  setCategory: (category) => {
    set({ selectedCategory: category });
    get().filterWorkers();
  },

  filterWorkers: () => {
    set({ filteredWorkers: get().unassignedWorkers });
  },
  handleConfirmAssignment: async (onClose, userId) => {
    const state = get();
    const { selectedCrewMember, selectedWorkers } = state;

    if (!selectedCrewMember || selectedWorkers.size === 0) return;

    const payload = {
      assignFromId: userId,
      assignToId: selectedCrewMember.id,
      operatorsIds: Array.from(selectedWorkers),
    };
    try {
      await state.assignWorkers(payload, () => {
        onClose();
      });
    } catch (error: any) {
      console.error("Error assigning workers:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
    }
  },

  handleConfirmReassignment: async (callback, userId, newAssigneeId) => {
    const { selectedCrewMember, selectedAssignedWorkers, crew } = get();

    console.log("handleConfirmReassignment called", {
      selectedCrewMember,
      selectedAssignedWorkers: Array.from(selectedAssignedWorkers),
      userId,
      newAssigneeId,
    });

    if (!selectedCrewMember || selectedAssignedWorkers.size === 0) {
      console.log("Missing selectedCrewMember or no selectedAssignedWorkers");
      return;
    }

    // Helper to find CrewMember by ID in crew tree
    function findCrewMemberById(
      node: CrewMember | null | undefined,
      id: string,
    ): CrewMember | null {
      if (!node) return null;
      if (node.id === id) return node;
      if (node.children) {
        for (const child of node.children) {
          const found: CrewMember | null = findCrewMemberById(child, id);
          if (found) return found;
        }
      }
      if ((node as any).backup) {
        for (const backup of (node as any).backup) {
          if (backup.id === id) return backup;
          if (backup.children) {
            for (const child of backup.children) {
              const found: CrewMember | null = findCrewMemberById(child, id);
              if (found) return found;
            }
          }
        }
      }
      return null;
    }

    // Get selected worker objects
    const selectedWorkerObjects = Array.from(selectedAssignedWorkers)
      .map((id) => findCrewMemberById(crew, id))
      .filter(Boolean);

    // If any selected worker is backup, use BACKUP_TO_TEAMLEADER
    const isFromBackup = selectedWorkerObjects.some(
      (worker) => worker && worker.roleStatus === "backup shiftleader",
    );
    const reassignmentAction = isFromBackup
      ? "BACKUP_TO_TEAMLEADER"
      : "REGULAR";

    // Check if both old and new assignee are teams
    const isTeamToTeam = selectedCrewMember.role === "team";
    try {
      const currentUserId = getCurrentUserId();

      if (isTeamToTeam) {
        // Use IDs for newTeamId and oldTeamId
        const payload = {
          newTeamId: newAssigneeId,
          oldTeamId: selectedCrewMember.id,
          assignees: Array.from(selectedAssignedWorkers).map((id) => ({
            id: id,
          })),
        };
        const response = await api.put(
          "/employee-assignment/assignments/reassign-to-team",
          payload,
          {
            params: {
              connected_user: currentUserId,
            },
          },
        );

        const message = extractMessageFromResponse(response?.data);
        if (message) {
          toast({
            title: "Success",
            description: message,
            variant: "success",
          });
        }
      } else {
        // Use current user ID if selectedCrewMember has no ID (for backup nodes)
        const unassignFromId = selectedCrewMember.id || currentUserId;

        // Regular employee reassignment or backup to team leader
        const payload = {
          reassignmentAction: reassignmentAction,
          assignTo: {
            id: newAssigneeId,
          },
          unassignFrom: { id: unassignFromId },
          assignees: Array.from(selectedAssignedWorkers).map((id) => ({
            id: id,
          })),
        };
        const response = await api.put(
          "/employee-assignment/assignments/reassign-to-employee",
          payload,
          {
            params: {
              connected_user: currentUserId,
            },
          },
        );
        const message = extractMessageFromResponse(response?.data);
        if (message) {
          toast({
            title: "Success",
            description: message,
            variant: "success",
          });
        }
      }
      if (callback) callback();
      get().fetchCreww(currentUserId);
      get().fetchUnassignedWorkers(currentUserId, 1, 5);
      set({ selectedAssignedWorkers: new Set(), isLoading: false });
    } catch (error: any) {
      console.error("Reassignment error:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      set({ error: "Reassignment failed", isLoading: false });
      if (callback) callback();
    }
  },

  // Assign workers to backup node
  handleAssignToBackup: async (onClose: () => void, userId: string) => {
    const { selectedCrewMember, selectedWorkers } = get();

    if (!selectedCrewMember || selectedWorkers.size === 0) {
      console.log("Missing selectedCrewMember or no selectedWorkers");
      return;
    }

    // Immediately indicate loading so dependent UI disables right away
    set({ isLoading: true });

    // Capture required data for payload before clearing UI selections
    const unassignFromId = selectedCrewMember.id;
    const assignees = Array.from(selectedWorkers).map((id) => ({ id }));

    // Clear selections immediately at call start (requested behavior)
    set({
      selectedAssignedWorkers: new Set(),
      selectedWorkers: new Set(),
      selectedCrewMember: null,
    });

    // Build the correct payload structure for backup assignment
    const payload = {
      reassignmentAction: "TEAMLEADER_TO_BACKUP",
      assignTo: { id: userId },
      unassignFrom: { id: unassignFromId },
      assignees,
    };

    try {
      const currentUserId = getCurrentUserId();
      const response = await api.put(
        "/employee-assignment/assignments/reassign-to-employee",
        payload,
        {
          params: {
            connected_user: currentUserId,
          },
        },
      );
      const message = extractMessageFromResponse(response?.data);
      if (message) {
        toast({
          title: "Success",
          description: message,
          variant: "success",
        });
      }
      if (onClose) onClose();
      get().fetchCreww(currentUserId);
      get().fetchUnassignedWorkers(currentUserId, 1, 5);
      set({
        selectedAssignedWorkers: new Set(),
        selectedWorkers: new Set(),
        isLoading: false,
      });
    } catch (error: any) {
      console.error("Backup assignment error:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      set({ error: "Backup assignment failed", isLoading: false });
      if (onClose) onClose();
    }
  },

  fetchRoles: async () => {
    try {
      const userId = getCurrentUserId();
      const { data } = await api.get(
        `/direct-dependents/api/organization-chart-Assignment/Assigned/roles/${userId}`,
      );
      const rolesArray = Array.isArray(data?.roles) ? data.roles : [];
      return rolesArray.map((role: string) => ({
        value: role,
        label: role,
      }));
    } catch (error: any) {
      console.error("Failed to fetch roles:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      return [];
    }
  },

  fetchUnRoles: async () => {
    try {
      const userId = getCurrentUserId();
      const { data } = await api.get(
        `/direct-dependents/api/organization-chart-Assignment/Unassigned/roles/${userId}`,
      );
      const rolesArray = Array.isArray(data?.roles) ? data.roles : [];
      return rolesArray.map((role: string) => ({
        value: role,
        label: role,
      }));
    } catch (error: any) {
      console.error("Failed to fetch all roles:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      return [];
    }
  },

  fetchSkills: async () => {
    try {
      const { data } = await api.get(
        `/direct-dependents/api/organization-chart-Assignment/Lookups/skills`,
      );
      const skillsArray = Array.isArray(data?.skills) ? data.skills : [];
      return skillsArray.map((skill: string) => ({
        value: skill,
        label: skill,
      }));
    } catch (error: any) {
      console.error("Failed to fetch skills:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      return [];
    }
  },

  fetchDepartments: async () => {
    try {
      const { data } = await api.get(
        `/direct-dependents/api/organization-chart-Assignment/Lookups/departments`,
      );
      const departmentsArray = Array.isArray(data?.departments)
        ? data.departments
        : [];
      return departmentsArray.map((department: string) => ({
        value: department.toLowerCase(),
        label: department,
      }));
    } catch (error: any) {
      console.error("Failed to fetch departments:", error);
      const message = extractMessageFromResponse(error?.response?.data);
      if (message) {
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      }
      return [];
    }
  },

  // Pagination setter method
  setUnassignedWorkersPagination: (pageNumber: number, pageSize: number) => {
    const state = get();
    const userId = getCurrentUserId();
    state.fetchUnassignedWorkers(userId, pageNumber, pageSize);
  },
}));

function updateNodeChildren(
  node: CrewMember,
  nodeKeyId: string,
  newChildren: CrewMember[],
): CrewMember {
  // Check if this node matches by keyId (legacyId or id)
  const currentNodeKeyId = node.legacyId?.trim() || node.id?.trim() || "";
  if (currentNodeKeyId === nodeKeyId) {
    // If the node is a shift leader and newChildren are operators (workers), convert them to CrewMember type
    const normalizedChildren = newChildren.map((child) => ({
      ...child,
      children: child.children ?? [],
    }));
    return { ...node, children: normalizedChildren };
  }
  if (!node.children || node.children.length === 0) {
    return node;
  }
  return {
    ...node,
    children: node.children.map((child) =>
      updateNodeChildren(child, nodeKeyId, newChildren),
    ),
  };
}
