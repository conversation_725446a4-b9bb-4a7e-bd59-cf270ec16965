"use client";

import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import type { ColumnDef } from "@tanstack/react-table";
import { useCrewStore } from "../store/assignmentStore";
import { DataTable } from "@/components/common/tables/DataTable";
import { IconButton, ReusableButton } from "@/components/common/CustomButtons";
import { Worker } from "../types/crew";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Check, X } from "lucide-react";
import CustomSelect from "@/components/common/CustomSelect";
import { Separator } from "@/components/ui/separator";
import useIsSmallScreen from "@/hooks/useIsSmallScreen";
import CustomMultiSelect from "@/components/common/CustomMultiSelect";
import { useMockAuthStore } from "@/store/mockAuthStore";

// Helper function to get current user ID
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID when no mock user is selected
  return "908_MAR Morocco 3"; // Default user ID
};

interface AssignmentPanelProps {
  onClose: () => void;
  role?: string;
}

export default function AssignmentPanel({ onClose }: AssignmentPanelProps) {
  const { currentUser } = useMockAuthStore();
  const fetchUnRoles = useCrewStore((state) => state.fetchUnRoles);
  const fetchSkills = useCrewStore((state) => state.fetchSkills);
  const fetchDepartments = useCrewStore((state) => state.fetchDepartments);
  const {
    selectedCrewMember,
    filteredWorkers,
    selectedWorkers,
    toggleWorkerSelection,
    selectWorkers,
    clearWorkerSelection,
    handleConfirmAssignment,
    selectedDepartment,
    isLoading,
    setDepartment,
    setSkills,
    setFunction,
    fetchUnassignedWorkers,
    unassignedWorkersPagination,
    setUnassignedWorkersPagination,
  } = useCrewStore();
  const isSmallScreen = useIsSmallScreen();

  const [selectionCount, setSelectionCount] = useState<number>(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [skillsOptions, setSkillsOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [departmentOptions, setDepartmentOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);

  // Show all workers by default, filter only when a role is selected
  let filteredByWorkers =
    selectedSkills.length > 0
      ? filteredWorkers.filter((w) =>
          (w.skills || []).some((skill: string) =>
            selectedSkills.includes(skill),
          ),
        )
      : selectedDepartment
        ? filteredWorkers.filter(
            (w) =>
              (w.department || "").trim().toLowerCase() ===
              selectedDepartment.trim().toLowerCase(),
          )
        : useCrewStore.getState().selectedFunction
          ? filteredWorkers.filter(
              (w) =>
                (w.function || "").trim().toLowerCase() ===
                useCrewStore.getState().selectedFunction.trim().toLowerCase(),
            )
          : filteredWorkers;

  // For trainer: only show unassigned workers after department is selected
  if (currentUser?.role === "trainer" && !selectedDepartment) {
    filteredByWorkers = [];
  }

  // Handle initial data loading and user changes
  useEffect(() => {
    const loadData = () => {
      const userId = getCurrentUserId();
      // Clear previous data and fetch fresh data for the current user
      clearWorkerSelection();
      setSelectionCount(0);
      // Clear pagination completely before fetching new data
      useCrewStore.setState({
        unassignedWorkersPagination: {
          currentPage: 1,
          pageSize: 5,
          totalItems: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
      fetchUnassignedWorkers(userId, 1, 5);
    };

    // Load data initially
    loadData();

    // Listen for user change events to force refresh
    const handleUserChange = () => {
      loadData();
    };

    window.addEventListener("mockUserChanged", handleUserChange);

    return () => {
      window.removeEventListener("mockUserChanged", handleUserChange);
    };
  }, [fetchUnassignedWorkers, currentUser, clearWorkerSelection]);

  // Pagination change handler
  const handlePaginationChange = (pageIndex: number, pageSize: number) => {
    const pageNumber = pageIndex + 1; // Convert 0-based to 1-based
    setUnassignedWorkersPagination(pageNumber, pageSize);
  };

  useEffect(() => {
    if (selectionCount === 0) {
      clearWorkerSelection();
    } else {
      const workersToSelect = filteredWorkers.slice(0, selectionCount);
      selectWorkers(workersToSelect.map((w) => w.id));
    }
  }, [selectionCount, filteredWorkers, selectWorkers, clearWorkerSelection]);

  useEffect(() => {
    // Fetch skills if user is shift leader, team leader, or trainer
    if (
      currentUser?.role === "shift leader" ||
      currentUser?.role === "team leader" ||
      currentUser?.role === "trainer"
    ) {
      fetchSkills().then((options) => setSkillsOptions(options));
    }
    // Fetch departments if user is trainer or training specialist
    if (
      currentUser?.role === "trainer" ||
      currentUser?.role === "training specialist"
    ) {
      fetchDepartments().then((options) => setDepartmentOptions(options));
    }
  }, [currentUser, fetchUnRoles, fetchSkills, fetchDepartments]);

  const columns: ColumnDef<Worker>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => {
            table.toggleAllPageRowsSelected(!!value);
            if (value) {
              selectWorkers(filteredWorkers.map((w) => w.id));
            } else {
              clearWorkerSelection();
            }
          }}
          className="h-4 w-4 rounded-sm"
          id="select-all-workers"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedWorkers.has(row.original.id)}
          onCheckedChange={() => toggleWorkerSelection(row.original.id)}
          className="h-4 w-4 rounded-sm"
          id={`worker-${row.original.id}`}
        />
      ),
    },
    {
      accessorKey: "id",
      header: "ID",
      cell: ({ row }) => (
        <strong>{row.original.legacyId || row.original.id}</strong>
      ),
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => <span>{row.original.name ?? ""}</span>,
    },
    {
      accessorKey: "surname",
      header: "Surname",
      cell: ({ row }) => <span>{row.original.surname ?? ""}</span>,
    },
    {
      accessorKey: "DepartmentProcess",
      header:
        currentUser?.role === "training specialist"
          ? "Category / Depart"
          : "Depart / Process",
      cell: ({ row }) =>
        currentUser?.role === "training specialist" ? (
          <div>
            <div>
              {row.original.category
                ? row.original.category.charAt(0).toUpperCase() +
                  row.original.category.slice(1)
                : ""}
            </div>
            <div className="text-xs text-gray-500">
              {row.original.department
                ? row.original.department.charAt(0).toUpperCase() +
                  row.original.department.slice(1)
                : ""}
            </div>
          </div>
        ) : (
          <div>
            <div>
              {row.original.department
                ? row.original.department.charAt(0).toUpperCase() +
                  row.original.department.slice(1)
                : ""}
            </div>
          </div>
        ),
    },
    {
      accessorKey: "function",
      header: "Function",
      cell: ({ row }) => (
        <div>
          <div className="text-xs text-gray-500">{row.original.role}</div>
        </div>
      ),
    },
  ];
  const handleConfirm = async () => {
    try {
      const userId = getCurrentUserId();
      await handleConfirmAssignment(() => {
        setIsDialogOpen(false);
      }, userId);
    } catch (error) {
      console.error("Error assigning workers:", error);
    }
  };

  const handleSelectionChange = (value: string) => {
    const count = Number(value);
    setSelectionCount(count);
    const workersToSelect = filteredWorkers.slice(0, count);
    selectWorkers(workersToSelect.map((w) => w.id));
  };

  const handleUnselectAll = () => {
    clearWorkerSelection();
    setSelectionCount(0);
  };

  // Update setSkills to also update local selectedSkills state
  const handleSkillsChange = (skills: string[]) => {
    setSkills(skills);
    setSelectedSkills(skills);
  };

  // Check if any filters are applied or if we have filtered data that differs from server data
  const hasActiveFilters =
    selectedSkills.length > 0 ||
    selectedDepartment ||
    useCrewStore.getState().selectedCategory ||
    useCrewStore.getState().selectedFunction ||
    (currentUser?.role === "trainer" && !selectedDepartment) ||
    filteredByWorkers.length !== filteredWorkers.length;

  return (
    <>
      <div className="p-3">
        {isSmallScreen && (
          <IconButton
            icon={X}
            variant="ghost"
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            onClick={onClose}
            id="close-panel"
          />
        )}
      </div>

      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-bold text-black">
            Unassigned Workers ({filteredByWorkers.length})
          </h3>
          <Separator
            orientation="horizontal"
            className="mb-3 border-blue-600 border-2"
          />
          {/* Conditionally Show Filters Based on Role */}
          {(currentUser?.role === "training specialist" ||
            currentUser?.role === "trainer") && (
            <CustomSelect
              options={departmentOptions}
              label="Department"
              onValueChange={(value) => {
                console.log("Selected department:", value);
                setDepartment(value);
              }}
              id="select-department"
            />
          )}
          {(currentUser?.role === "shift leader" ||
            currentUser?.role === "team leader" ||
            currentUser?.role === "trainer") && (
            <CustomMultiSelect
              options={skillsOptions}
              label="Operation Skills"
              onValueChange={handleSkillsChange}
              id="select-skills"
              className="mt-2"
            />
          )}
          {/* Show Category and Function for training specialist */}
          {currentUser?.role === "training specialist" && (
            <div className="mb-4 mt-2">
              <CustomSelect
                options={[{ value: "operator", label: "Operator" }]}
                label="Function"
                onValueChange={setFunction}
                id="select-function"
              />
            </div>
          )}

          <div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-black">
                Selected {selectedWorkers.size}/{filteredByWorkers.length}
                {selectionCount > 0 && (
                  <ReusableButton
                    variant="ghost"
                    className="text-sm font-normal h-9 text-blue-300"
                    onClick={handleUnselectAll}
                    label="Clear"
                    id="clear-selection"
                  />
                )}
              </span>

              <Select
                value={String(selectionCount)}
                onValueChange={handleSelectionChange}
              >
                <SelectTrigger className="w-32 h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent id="select-count">
                  <SelectItem value="0">Select</SelectItem>
                  {/* Generate options in increments of 5 up to the total available workers */}
                  {Array.from(
                    { length: Math.ceil(filteredByWorkers.length / 5) },
                    (_, index) => {
                      const count = (index + 1) * 5;
                      const displayCount = Math.min(
                        count,
                        filteredByWorkers.length,
                      );
                      return (
                        <SelectItem
                          key={displayCount}
                          value={String(displayCount)}
                        >
                          Select {displayCount}
                        </SelectItem>
                      );
                    },
                  ).filter((item, index, array) => {
                    const currentValue = array[index]?.props?.value;
                    const previousValue =
                      index > 0 ? array[index - 1]?.props?.value : null;
                    return currentValue !== previousValue;
                  })}
                </SelectContent>
              </Select>
            </div>{" "}
            <div style={{ maxHeight: "400px", overflowY: "auto" }}>
              <DataTable
                columns={columns}
                data={filteredByWorkers}
                showSearchBar={false}
                id="workers-table"
                serverPagination={!hasActiveFilters}
                onPaginationChange={handlePaginationChange}
                totalItems={
                  hasActiveFilters
                    ? filteredByWorkers.length
                    : unassignedWorkersPagination.totalItems
                }
                currentPage={
                  hasActiveFilters
                    ? 0
                    : unassignedWorkersPagination.currentPage - 1
                } // Convert 1-based to 0-based
                pageSize={unassignedWorkersPagination.pageSize}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <ReusableButton
            className="h-10 mt-4 bg-black hover:bg-gray-800 text-white"
            label={
              !selectedCrewMember
                ? "Select Workers to Assign"
                : `Assign to: ${selectedCrewMember.name} ${selectedCrewMember.surname}`
            }
            onClick={() => setIsDialogOpen(true)}
            disabled={
              !selectedCrewMember || selectedWorkers.size === 0 || isLoading
            }
            isLoading={isLoading}
            id="assign-button"
          />
        </div>

        <ReusableDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          id="confirm-dialog"
        >
          <div className="flex items-center justify-center">
            <Check className="h-20 w-20 bg-green-100 text-green-600 rounded-full p-2" />
          </div>
          {selectedCrewMember && (
            <p className="p-6 text-center">
              Assigning {selectedWorkers.size} workers to{" "}
              {selectedCrewMember.name} {selectedCrewMember.surname}
            </p>
          )}
          <div className="flex justify-between mt-4">
            <ReusableButton
              label="Cancel"
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
            />
            <div className="flex-1" />
            <ReusableButton
              label="Confirm"
              onClick={handleConfirm}
              isLoading={isLoading}
            />
          </div>
        </ReusableDialog>
      </div>
    </>
  );
}
