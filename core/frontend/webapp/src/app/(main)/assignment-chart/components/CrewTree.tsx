"use client";
import { X, ChevronR<PERSON>, ChevronDown, User2Icon, Loader2 } from "lucide-react";
import { useCallback, useEffect, useState, type ReactElement } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import type { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/common/tables/DataTable";
import { useCrewStore } from "../store/assignmentStore";
import { CrewMember } from "../types/crew";
import CustomSelect from "@/components/common/CustomSelect";
import { AnimatePresence, motion } from "framer-motion";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { ReusableButton } from "@/components/common/CustomButtons";
import { useMockAuthStore } from "@/store/mockAuthStore";

// Helper function to get current user ID
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID when no mock user is selected
  return "908_MAR Morocco 3"; // Default user ID
};
import { useTranslations } from "next-intl";
import CustomIcon from "@/components/common/CustomIcons";

export default function CrewTree() {
  const t = useTranslations("crew_management");
  const { currentUser } = useMockAuthStore();

  const {
    crew,
    selectCrewMember,
    selectedAssignedWorkers,
    selectedCrewMember,
    toggleAssignedWorkerSelection,
    selectAllAssignedWorkers,
    clearAssignedWorkerSelection,
    clearAssignedWorkersOnly,
    handleConfirmReassignment,
    handleAssignToBackup,
    clearCrewMemberSelection,
    filteredShiftLeaders,
  } = useCrewStore();
  const [selectedForReassign, setSelectedForReassign] = useState<string | null>(
    null,
  );
  const [isReassignDialogOpen, setIsReassignDialogOpen] = useState(false);
  const [teamLeaders, setTeamLeaders] = useState<
    { value: string; label: string }[]
  >([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(
    new Set(crew ? [crew.id] : []),
  );
  const [loadingNodes, setLoadingNodes] = useState<Set<string>>(new Set());
  const [isSubmittingReassign, setIsSubmittingReassign] = useState(false);
  // Show all crew members including the connected user
  // If current user is trainer, use filteredShiftLeaders, else use crew.children
  let filteredMembers;
  const isNotBackup = (member: CrewMember) =>
    (member.roleStatus || "").trim() !== "backup shiftleader";
  if (currentUser?.role === "trainer") {
    filteredMembers = (filteredShiftLeaders ?? [])
      .filter((child) => child.id != null || child.legacySiteId != null)
      .filter(isNotBackup);
  } else {
    filteredMembers = (crew?.children || [])
      .filter((child) => child.id != null || child.legacySiteId != null)
      .filter(isNotBackup);
  }

  const findMemberInCrew = useCallback(
    (node: CrewMember, memberId: string): CrewMember | null => {
      // Try to match by both UUID and legacyId
      if (node.id === memberId || node.legacyId === memberId) return node;
      for (const child of node.children || []) {
        const found = findMemberInCrew(child, memberId);
        if (found) return found;
      }
      return null;
    },
    [],
  );

  // Handle initial crew loading and user changes
  useEffect(() => {
    const loadCrew = async () => {
      const userId = getCurrentUserId();
      // Clear any selected data and UI state
      clearCrewMemberSelection();
      clearAssignedWorkerSelection();
      setExpandedNodes(new Set());
      setSelectedForReassign(null);
      setIsReassignDialogOpen(false);

      // Clear crew data before fetching new data
      useCrewStore.getState().clearCrew();

      try {
        await useCrewStore.getState().fetchCreww(userId);
        // Expand the root node after fetching
        setExpandedNodes(new Set([userId]));
      } catch {
        // If fetch fails, crew remains cleared
        useCrewStore.getState().clearCrew();
      }
    };

    // Load crew initially and on user changes
    loadCrew();

    // Listen for user change events to force refresh
    const handleUserChange = async () => {
      await loadCrew();
    };

    window.addEventListener("mockUserChanged", handleUserChange);

    return () => {
      window.removeEventListener("mockUserChanged", handleUserChange);
    };
  }, [currentUser, clearCrewMemberSelection, clearAssignedWorkerSelection]);

  // Handle team leaders options for reassign dialog and crew member updates
  useEffect(() => {
    // Update team leaders for reassign dialog
    if (isReassignDialogOpen && crew) {
      const formatted = (crew.children || [])
        .filter((item) => item.id !== selectedCrewMember?.id && item.id != null)
        .map((item) => ({
          value: item.id!,
          label: `${item.name} ${item.surname}`,
        }));
      setTeamLeaders(formatted);
    }

    // Update selected crew member if crew data changes
    if (crew && selectedCrewMember) {
      const updatedMember = findMemberInCrew(crew, selectedCrewMember.id);
      if (updatedMember) {
        selectCrewMember(updatedMember);
      }
    }
  }, [
    crew,
    selectedCrewMember,
    isReassignDialogOpen,
    findMemberInCrew,
    selectCrewMember,
  ]);

  if (!crew) return null;
  const toggleNode = async (keyId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const { crew, fetchOperators, updateCrewNodeChildren } =
      useCrewStore.getState();
    if (!crew) return;

    const isExpanded = expandedNodes.has(keyId);

    if (isExpanded) {
      setExpandedNodes((prev) => {
        const newSet = new Set(prev);
        newSet.delete(keyId);
        return newSet;
      });
      setLoadingNodes((prev) => {
        if (!prev.has(keyId)) return prev;
        const next = new Set(prev);
        next.delete(keyId);
        return next;
      });
      return;
    }

    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      newSet.add(keyId);
      return newSet;
    });

    const findNodeByKeyId = (
      node: CrewMember,
      targetKeyId: string,
    ): CrewMember | null => {
      const nodeKeyId = node.legacyId?.trim() || node.id?.trim() || "";
      if (nodeKeyId === targetKeyId) return node;
      for (const child of node.children || []) {
        const found = findNodeByKeyId(child, targetKeyId);
        if (found) return found;
      }
      return null;
    };

    const targetNode = findNodeByKeyId(crew, keyId);
    if (!targetNode) {
      // Nothing to load, ensure not marked loading
      setLoadingNodes((prev) => {
        if (!prev.has(keyId)) return prev;
        const next = new Set(prev);
        next.delete(keyId);
        return next;
      });
      return;
    }

    // Only fetch operators if id exists, otherwise skip fetching
    if (targetNode.id) {
      // Only non-backup nodes with an id trigger a fetch & loading state
      setLoadingNodes((prev) => {
        const next = new Set(prev);
        next.add(keyId);
        return next;
      });
      // Now id contains the UUID, so use it directly for API calls
      const employeeId = targetNode.id;

      const workers = await fetchOperators(employeeId);
      const operatorNodes = workers.map((op) => ({
        ...op,
        children: [],
      }));

      updateCrewNodeChildren(keyId, operatorNodes);

      // If current user is trainer, also update the children in filteredShiftLeaders
      if (
        currentUser?.role === "trainer" &&
        Array.isArray(filteredShiftLeaders)
      ) {
        const idx = filteredShiftLeaders.findIndex((node) => {
          const nodeKeyId = node.legacyId?.trim() || node.id?.trim() || "";
          return nodeKeyId === keyId;
        });
        if (idx !== -1) {
          filteredShiftLeaders[idx] = {
            ...filteredShiftLeaders[idx],
            children: operatorNodes,
          };
        }
      }
    }
    // Fetch complete (success or skip)
    setLoadingNodes((prev) => {
      if (!prev.has(keyId)) return prev;
      const next = new Set(prev);
      next.delete(keyId);
      return next;
    });
    // If legacySiteId is missing, do not fetch, but still allow expansion/collapse
  };

  // Function to handle reassignment logic
  const handleReassignment = async () => {
    if (!selectedForReassign) {
      return;
    }
    setIsSubmittingReassign(true);
    try {
      await handleConfirmReassignment(
        async () => {
          setIsReassignDialogOpen(false);
          // Fetch operators for the newly assigned team and the source node
          const crewState = useCrewStore.getState();
          const crew = crewState.crew;
          const findNodeById = (
            node: CrewMember,
            targetId: string,
          ): CrewMember | null => {
            if (node.id === targetId) return node;
            for (const child of node.children || []) {
              const found = findNodeById(child, targetId);
              if (found) return found;
            }
            return null;
          };
          // Fetch for destination node
          const targetNode = crew
            ? findNodeById(crew, selectedForReassign)
            : null;
          if (
            crew &&
            targetNode &&
            crew.legacySiteId &&
            targetNode.legacySiteId
          ) {
            const employeeId = crew.legacySiteId;
            const workers = await crewState.fetchOperators(employeeId);
            const operatorNodes = workers.map((op: CrewMember) => ({
              ...op,
              children: [],
            }));
            crewState.updateCrewNodeChildren(
              selectedForReassign,
              operatorNodes,
            );
          }
          // Collapse the source node (the node we took the operator from)
          setExpandedNodes((prev) => {
            const newSet = new Set(prev);
            if (selectedCrewMember) {
              if (selectedCrewMember.id) newSet.delete(selectedCrewMember.id);
              if (selectedCrewMember.legacyId)
                newSet.delete(selectedCrewMember.legacyId);
            }
            return newSet;
          });
          setLoadingNodes((prev) => {
            if (!selectedCrewMember) return prev;
            const next = new Set(prev);
            if (selectedCrewMember.id) next.delete(selectedCrewMember.id);
            if (selectedCrewMember.legacyId)
              next.delete(selectedCrewMember.legacyId);
            return next;
          });
          // Clear selected crew member after reassignment
          crewState.clearCrewMemberSelection();
        },
        getCurrentUserId(),
        selectedForReassign,
      );
      clearAssignedWorkerSelection();
    } catch (error) {
      console.error("Error assigning workers:", error);
    } finally {
      setIsSubmittingReassign(false);
    }
  };

  // Removed old select-all handler now that selection is scoped per member table

  const getCustomIcon = () => (
    <CustomIcon
      name="reassign"
      style={{
        width: "10px",
        height: "10px",
      }}
      className="mr-2"
    />
  );
  type ButtonConfig = {
    show: boolean;
    label: string;
    icon: () => ReactElement;
    onClick: () => void;
    variant:
      | "link"
      | "default"
      | "destructive"
      | "outline"
      | "secondary"
      | "ghost";
    disabled?: boolean;
    id?: string;
  };

  function getDataTableButtons({
    isBackupNode,
    member,
  }: {
    role: string;
    isBackupNode: boolean;
    member: CrewMember;
  }): ButtonConfig[] {
    const buttons: ButtonConfig[] = [];
    // Determine if this member node is currently selected
    const isCurrentNodeSelected =
      !!selectedCrewMember && selectedCrewMember.id === member.id;

    // Move to Backup button (for shift leader, not backup node)
    if (
      currentUser?.role?.toLowerCase().startsWith("shift leader") &&
      !isBackupNode
    ) {
      buttons.push({
        show: true,
        label: "Move to Backup",
        icon: () => <User2Icon className="w-4 h-4 mr-2" />,
        onClick: async () => {
          const selectedIds = Array.from(selectedAssignedWorkers);
          if (selectedIds.length === 0) {
            return;
          }
          // Node is already selected (button disabled otherwise); proceed
          await new Promise<void>((resolve) => {
            useCrewStore.getState().selectWorkers(selectedIds);
            setTimeout(resolve, 0);
          });
          await handleAssignToBackup(() => {
            clearAssignedWorkersOnly();
            useCrewStore.getState().clearCrewMemberSelection();
          }, getCurrentUserId());
        },
        variant: "secondary",
        // Enabled only if this node is selected AND operators selected
        disabled: !isCurrentNodeSelected || selectedAssignedWorkers.size === 0,
        id: "btn-move-to-backup",
      });
    }

    // Reassign button: only enabled if this is the selected node and at least one operator is selected
    buttons.push({
      show: true,
      label: "Reassign",
      icon: getCustomIcon,
      onClick: () => {
        if (!isCurrentNodeSelected) {
          selectCrewMember(member);
        }
        setIsReassignDialogOpen(true);
      },
      variant: "secondary",
      disabled: !isCurrentNodeSelected || selectedAssignedWorkers.size === 0,
      id: "btn-reassign-team-leader",
    });

    return buttons;
  }

  const renderMember = (member: CrewMember): ReactElement => {
    // Use legacyId for display and keys, but id (UUID) for API calls
    const displayId = member.legacyId?.trim() ?? member.id?.trim() ?? ""; // Use legacyId for display, fallback to id
    const keyId = member.legacyId?.trim() || member.id?.trim() || ""; // Use legacyId for React key, fallback to UUID
    const hasChildren =
      (member.children && member.children.length > 0) ||
      (member.backup && member.backup.length > 0);
    const isLoading = loadingNodes.has(keyId);
    // Treat node as expanded for arrow orientation only if expanded and (has children or loading)
    const isExpanded = expandedNodes.has(keyId) && (hasChildren || isLoading);
    // Auto-clean expanded state if no children & not loading
    if (expandedNodes.has(keyId) && !hasChildren && !isLoading) {
      queueMicrotask(() => {
        setExpandedNodes((prev) => {
          if (!prev.has(keyId)) return prev;
          const next = new Set(prev);
          next.delete(keyId);
          return next;
        });
      });
    }
    const isSelected =
      selectedCrewMember &&
      (keyId ===
        (selectedCrewMember.legacyId?.trim() ||
          selectedCrewMember.id?.trim() ||
          "") ||
        member.id === selectedCrewMember.id);

    // Check if this is a backup node
    const isBackupNode = (member.role || "").trim().toUpperCase() === "BACKUP";

    return (
      <div key={keyId} id={"crew-member"}>
        <div
          className={cn(
            "flex items-center p-2 rounded-lg mb-1 cursor-pointer relative border shadow-sm",
            // Different styling for backup nodes
            isBackupNode
              ? "bg-gradient-to-r from-[#F5F5F5] to-[#E0E0E0] border-[#9E9E9E] border-2"
              : "bg-[#DFECFE] border-[#A8A8A8]",
            isSelected &&
              (isBackupNode
                ? "bg-gradient-to-r from-[#616161] to-[#757575] text-white border-[#424242]"
                : "bg-black text-white border-[#A8A8A8]"),
          )}
          onClick={(e) => {
            e.stopPropagation(); // Prevent event bubbling
            // Prevent selection if backup node or root crew
            const isRootCrew = member === crew;
            if (!isRootCrew && !isBackupNode) {
              // If switching to a new node, clear CrewTree's selected operators
              if (!selectedCrewMember || selectedCrewMember.id !== member.id) {
                // Only clear assigned workers (keep assignment panel selection)
                clearAssignedWorkersOnly();
              }
              selectCrewMember(member);
            }
          }}
          id={"crew-card"}
        >
          {/* Always show the arrow button */}
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-5 w-5 mr-2 hover:bg-transparent"
            onClick={(e) => toggleNode(keyId, e)}
            id={"toggle-btn"}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
          <CustomIcon
            name="circleUser"
            style={{
              width: "21px",
              height: "21px",
              fill: isBackupNode ? "#9E9E9E" : "#4762F1",
            }}
            className="mr-2"
          />
          <div className="flex-1" id={"member-info"}>
            <div className="flex items-center justify-between">
              <div>
                <span
                  className={`font-medium text-sm ${
                    isSelected
                      ? "text-white"
                      : isBackupNode
                        ? "text-[#616161]"
                        : "text-black"
                  }`}
                >
                  {/* Only show legacyId if not a team and not backup */}
                  {member.role?.toLowerCase() !== "team" && !isBackupNode
                    ? `${displayId} - `
                    : ""}
                  {member.name} {member.surname}
                  {/* Add backup indicator */}
                  {isBackupNode && (
                    <span className="ml-2 text-xs text-gray-500 px-2 py-1 rounded-full">
                      BACKUP
                    </span>
                  )}
                </span>
                {!isBackupNode && (
                  <span className={"ml-2 text-xs text-[#4762F1]"}>
                    {member.role}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        <AnimatePresence initial={false}>
          {isExpanded && hasChildren && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{
                height: { duration: 0.3 },
                opacity: { duration: 0.2 },
              }}
              className="ml-7"
            >
              {member.children?.some((child) => {
                const role = (child.role || "").trim().toLowerCase();
                return role === "operator" || role === "containment operator";
              }) ? (
                <div className="bg-white rounded-xl border shadow-sm mt-2">
                  <div className="p-3">
                    <DataTable
                      columns={getAssignedWorkerColumns(member)}
                      data={member.children || []}
                      showSearchBar
                      id="assigned-workers-table-shift-leader"
                      buttons={getDataTableButtons({
                        role: crew.role,
                        isBackupNode:
                          (member.role || "").trim().toUpperCase() === "BACKUP",
                        member, // pass the current member
                      })}
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  {[...(member.children || []), ...(member.backup || [])].map(
                    (child) =>
                      renderMember({
                        ...child,
                        role: child.role || "BACKUP",
                        children: child.children ?? [], // ensures we can expand later
                      }),
                  )}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const getAssignedWorkerColumns = (
    member: CrewMember,
  ): ColumnDef<CrewMember>[] => [
    {
      id: "select",
      header: () => (
        <Checkbox
          checked={
            (member.children?.length ?? 0) > 0 &&
            member.children?.every(
              (worker) =>
                worker.id != null && selectedAssignedWorkers.has(worker.id),
            )
          }
          onCheckedChange={(value) => {
            // Always clear previous selection before selecting all in a new node
            clearAssignedWorkerSelection();
            if (!!value) {
              selectCrewMember(member);
              const ids = (member.children || [])
                .filter((w) => w.id != null)
                .map((w) => w.id as string);
              selectAllAssignedWorkers(ids);
            } else {
              clearCrewMemberSelection();
            }
          }}
          aria-label="Select all"
          className="h-4 w-4 rounded-sm"
          id="checkbox-select-all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={
            row.original.id != null &&
            selectedAssignedWorkers.has(row.original.id)
          }
          onCheckedChange={() => {
            // Enforce single-node selection: if switching tables, clear previous selections
            if (!row.original.id) return;
            const isDifferentMember =
              !selectedCrewMember || selectedCrewMember.id !== member.id;
            if (isDifferentMember) {
              // Move focus to this member and clear any previous operator selection
              clearAssignedWorkerSelection();
              selectCrewMember(member);
            }
            toggleAssignedWorkerSelection(row.original.id);
          }}
          className="h-4 w-4 rounded-sm"
          id="checkbox-select"
        />
      ),
    },
    {
      accessorKey: "legacyId",
      header: t("id"),
      cell: ({ row }) => row.original.legacyId || row.original.id,
    },
    {
      accessorKey: "name",
      header: t("name"),
    },
    {
      accessorKey: "surname",
      header: t("surname"),
    },
  ];

  return (
    <div className="relative">
      <div className="flex w-full mb-3">
        {selectedCrewMember && (
          <div className="bg-[#FFF5CE] border border-[#D6D6D6] shadow-[0_3px_6px_#00000029] rounded-[13px] px-3 py-2 flex items-center justify-between gap-3 max-w-full min-w-0">
            <div className="text-xs flex items-center gap-2 flex-1 min-w-0 overflow-hidden">
              <span className="text-[#804707] font-semibold truncate min-w-0">
                {!currentUser?.role?.toLowerCase().startsWith("team leader")
                  ? `${selectedCrewMember?.legacyId || selectedCrewMember?.id} - `
                  : ""}
                {selectedCrewMember?.name} {selectedCrewMember?.surname}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 bg-transparent hover:bg-[#F5E8A8] rounded-full flex-shrink-0 transition-colors duration-200"
              onClick={() => clearCrewMemberSelection()}
              id="btn-clear"
            >
              <X className="h-4 w-4 text-[#804707]" />
            </Button>
          </div>
        )}

        <div className="flex-1"></div>
      </div>
      <div
        className="space-y-1"
        style={{ maxHeight: "600px", overflowY: "auto" }}
      >
        {/* Render backup node first */}
        {crew.backup && crew.backup.length > 0 && (
          <div className="space-y-2">
            {crew.backup.map((backupWorker) =>
              renderMember({
                ...backupWorker,
                role: "BACKUP",
              } as CrewMember),
            )}
          </div>
        )}
        {/* Then render regular crew members */}
        {filteredMembers.map((member) => renderMember(member))}
      </div>
      {/* Reassign Dialog */}
      <ReusableDialog
        isOpen={isReassignDialogOpen}
        onClose={() => setIsReassignDialogOpen(false)}
        id="dialog-reassign"
        title={"Reassign"}
      >
        <div className="flex flex-col space-y-4">
          <p>Select where to assign workers.</p>

          <CustomSelect
            options={teamLeaders}
            label="Select New "
            id="select-new-leader-or-team"
            onValueChange={(value: string) => setSelectedForReassign(value)}
          />

          <div className="flex justify-between mt-4">
            <ReusableButton
              label="Cancel"
              color="secondary"
              variant="outline"
              onClick={() => setIsReassignDialogOpen(false)}
              id="btn-cancel-reassign"
            />
            <ReusableButton
              label={
                isSubmittingReassign ? "Submitting..." : "Confirm Reassignment"
              }
              color="primary"
              disabled={!selectedForReassign || isSubmittingReassign}
              onClick={handleReassignment}
              id="btn-confirm-reassign"
              isLoading={isSubmittingReassign}
            />
          </div>
        </div>
      </ReusableDialog>
    </div>
  );
}
