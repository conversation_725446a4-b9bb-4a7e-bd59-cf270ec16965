# Assignment Chart Component Testing Guide

## Test Commands

### Run All Tests

```bash
pnpm run test
```

### Run Component Tests

```bash
# Comprehensive component tests
pnpm run test -- --testPathPattern='assignmentChart.test'
```

### Run Store Tests

```bash
# Comprehensive store tests
pnpm run test -- --testPathPattern='assignmentStore.test'
```

### Run Both Test Files

```bash
# Run component and store tests together (clean output with suppressed warnings)
pnpm run test -- --testPathPattern='(assignmentChart|assignmentStore).test'

# Run with single worker for more stable output
pnpm run test -- --testPathPattern='(assignmentChart|assignmentStore).test' --maxWorkers=1

# Run silently (minimal output)
pnpm run test -- --testPathPattern='(assignmentChart|assignmentStore).test' --silent
```

### Run Specific Test Suites

```bash
# Run only component rendering tests
pnpm run test -- --testPathPattern='assignmentChart.test' --testNamePattern='Component Rendering'

# Run only store integration tests
pnpm run test -- --testPathPattern='assignmentStore.test' --testNamePattern='Store Actions'

# Run only assignment panel tests
pnpm run test -- --testPathPattern='assignmentChart.test' --testNamePattern='Assignment Panel'

# Run only crew tree tests
pnpm run test -- --testPathPattern='assignmentChart.test' --testNamePattern='Crew Tree'
```

## Test Files Structure

### 📁 Components

**assignmentChart.test.tsx** - Comprehensive tests

- **Component Rendering** (20+ tests)

  - Basic component rendering
  - Role-based UI visibility
  - Filter components display
  - Data table rendering
  - Button states

- **User Interactions** (25+ tests)

  - Worker selection/deselection
  - Bulk selection operations
  - Filter interactions
  - Assignment operations
  - Modal interactions

- **Assignment Panel** (15+ tests)

  - Panel opening/closing
  - Role-based filter visibility
  - Department selection
  - Skills selection
  - Worker assignment flow

- **Crew Tree** (10+ tests)

  - Tree structure rendering
  - Crew member selection
  - Hierarchical display
  - State management

- **Data Management** (15+ tests)

  - Pagination handling
  - Search functionality
  - Filter application
  - Data loading states

- **Error Handling** (10+ tests)
  - API error responses
  - Invalid data handling
  - Network failures
  - User input validation

### 📁 Store

**assignmentStore.test.tsx** - Store functionality tests

- **Store State Management** (8+ tests)

  - Initial state validation
  - State updates
  - Data persistence
  - State clearing

- **API Integration** (12+ tests)

  - Crew fetching
  - Worker fetching
  - Assignment operations
  - Error handling

- **Business Logic** (10+ tests)

  - Worker filtering
  - Role-based permissions
  - Assignment validation
  - Pagination logic

- **Data Transformation** (5+ tests)
  - Data normalization
  - State updates
  - Response handling

## Test Coverage Goals

- **Component Tests**: 80+ comprehensive tests
- **Store Tests**: 35+ tests covering all store functionality
- **Integration Tests**: Cross-component interaction tests
- **Role-based Tests**: Different user role scenarios
- **Error Scenarios**: Edge cases and error handling

## Test Patterns

### Component Testing

- Mock external dependencies
- Test user interactions
- Validate role-based behavior
- Check accessibility features

### Store Testing

- Mock API calls
- Test state transitions
- Validate business logic
- Handle error scenarios

### Integration Testing

- Component-store interactions
- User workflow testing
- End-to-end scenarios

## Mock Strategies

### External Dependencies

- API calls with axios
- Authentication store
- UI components
- Icons and assets

### User Roles

- Department clerk
- Shift leader
- Team leader
- Trainer
- Training specialist

### Data Mocking

- Crew members
- Workers
- Departments
- Skills
- Assignment data

## Running Tests in Development

```bash
# Watch mode for development
pnpm run test:watch -- --testPathPattern='assignmentChart'

# Run with coverage
pnpm run test:coverage -- --testPathPattern='assignment'

# Debug specific test
pnpm run test -- --testPathPattern='assignmentChart.test' --testNamePattern='specific test name'
```

## Best Practices

1. **Test Structure**: Follow AAA pattern (Arrange, Act, Assert)
2. **Descriptive Names**: Use clear, descriptive test names
3. **Mock Management**: Properly mock external dependencies
4. **Cleanup**: Clean up after each test
5. **Coverage**: Aim for high test coverage
6. **Role Testing**: Test all user role scenarios
7. **Error Handling**: Test error conditions
8. **Accessibility**: Include accessibility tests
9. **Clean Output**: React warnings are automatically suppressed during tests

## Warning Suppression

The test setup automatically suppresses common React warnings that occur in test environments:

- HTML structure warnings (div in select)
- Act() warnings for state updates
- Hydration warnings
- DOM nesting validation warnings
- Missing key prop warnings

This provides cleaner test output while maintaining actual error visibility.

## Common Issues and Solutions

### Mock Authentication

```tsx
// Mock the authentication store
jest.mock("@/store/mockAuthStore", () => ({
  useMockAuthStore: () => ({
    currentUser: { id: "test-user", role: "department clerk" },
  }),
}));
```

### API Mocking

```tsx
// Mock axios calls
jest.mock("@/lib/axios", () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));
```

### Component Mocking

```tsx
// Mock complex UI components
jest.mock("@/components/common/tables/DataTable", () => ({
  DataTable: ({ children }: any) => (
    <div data-testid="data-table">{children}</div>
  ),
}));
```
