"use client";

import useIsSmallScreen from "@/hooks/useIsSmallScreen";
import { CrewDashboard } from "./components/CrewDashboard";
import { useState, useEffect } from "react";
import { Separator } from "@/components/ui/separator";
import AssignmentPanel from "./components/AssignmentPanel";
import { ReusableButton } from "@/components/common/CustomButtons";
import useCrewManagementStore from "./store/crewManagementStore";
import { useTranslations } from "next-intl";
// import { PermissionGate } from "@/components/auth/PermissionGate";
// import { PermissionLevel } from "@/config/permissionsConfig";

export default function Home() {
  const t = useTranslations("crew_management_page");

  const {
    fetchUnassignedWorkers,
    selectedCrewMember,
    unassignedWorkers,
    setSelectedCrewMember,
  } = useCrewManagementStore();
  const isSmallScreen = useIsSmallScreen();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  useEffect(() => {
    fetchUnassignedWorkers();
  }, [fetchUnassignedWorkers]);

  const toggleDrawer = () => setIsDrawerOpen((prev) => !prev);

  return (
    <div className="mx-auto w-full max-w-full space-y-8 p-6 lg:p-8">
      <div className="flex space-x-8 rounded-lg border bg-white p-6 shadow-sm">
        <div className="flex-1 min-w-0 overflow-x-auto relative">
          <CrewDashboard onSelectCrewMember={setSelectedCrewMember} />
        </div>
        {/* <PermissionGate
          route="/crew-management"
          permissionLevel={PermissionLevel.Write}
        > */}
        {!isSmallScreen && (
          <>
            <Separator
              orientation="vertical"
              className="self-stretch border-gray-300"
            />
            <div className="w-1/3">
              <AssignmentPanel onClose={toggleDrawer} />
            </div>
          </>
        )}
        {/* </PermissionGate> */}
      </div>
      {/* <PermissionGate
        route="/crew-management"
        permissionLevel={PermissionLevel.Write}
      > */}
      {isSmallScreen && isDrawerOpen && (
        <div className="fixed inset-0  z-50">
          <div className="fixed right-0 top-0 bottom-0 w-1/2 bg-white shadow-lg p-6">
            <AssignmentPanel onClose={toggleDrawer} />
          </div>
        </div>
      )}
      {isSmallScreen && (
        <ReusableButton
          className="fixed bottom-4 right-4"
          onClick={toggleDrawer}
          label={t("assignWorkersWithCount", {
            count: unassignedWorkers.length,
          })}
          disabled={!selectedCrewMember}
        />
      )}
      {/* </PermissionGate> */}
    </div>
  );
}
