import { create } from "zustand";
import type {
  FilterOption,
  Worker,
  WorkerUpdate,
  StationsByRole,
  AssignWorkersPayload,
  MeDefinitionResponse,
  MeDefinitionVersion,
  WorkstationAssignment,
  WorkstationAssignmentsResponse,
  WorkstationOption,
  OperatorForReassignment,
  AssignToWorkstationRequest,
  AssignToWorkstationResponse,
  ZoningOptionsResponse,
  Skills,
} from "../types/crewManagementTypes";
import { toast } from "@/hooks/use-toast";
import api from "@/lib/axios";
import { useMockAuthStore } from "@/store/mockAuthStore";

// Helper function to get current user ID
const getCurrentUserId = (): string => {
  const mockAuthState = useMockAuthStore.getState();
  const currentUser = mockAuthState.currentUser;

  // If mock user is selected, use their ID, otherwise use default
  if (currentUser) {
    return currentUser.id;
  }

  // Default fallback ID
  return "2443_MAR Morocco 3";
};

// Helper function to normalize level names (remove _id suffix)
const normalizeLevel = (level: string): string => {
  return level.replace(/_id$/, "");
};

// Helper function to extract message from backend response
// Returns null if no backend message is found
const extractMessageFromResponse = (responseData: unknown): string | null => {
  if (!responseData || typeof responseData !== "object") {
    return null;
  }

  const data = responseData as Record<string, unknown>;

  // Check if response has a message field
  if (data?.message && typeof data.message === "string") {
    return data.message;
  }

  // Check if message is nested in details.error.message (for the specific error structure you're seeing)
  if (
    data?.details &&
    typeof data.details === "object" &&
    data.details !== null
  ) {
    const details = data.details as Record<string, unknown>;

    // Check for details.error.message structure
    if (
      details.error &&
      typeof details.error === "object" &&
      details.error !== null
    ) {
      const error = details.error as Record<string, unknown>;
      if (error.message && typeof error.message === "string") {
        return error.message;
      }
    }

    // Check for direct details.message
    if (details.message && typeof details.message === "string") {
      return details.message;
    }

    if (details.error && typeof details.error === "string") {
      return details.error;
    }

    if (details.errors && Array.isArray(details.errors) && details.errors[0]) {
      const error = details.errors[0] as Record<string, unknown>;
      if (
        error.field &&
        error.constraints &&
        Array.isArray(error.constraints) &&
        error.constraints[0]
      ) {
        return `${error.field}: ${error.constraints[0]}`;
      }
    }
  }

  // Return null if no backend message found
  return null;
};

// Helper function to show toast only if there's a backend message
const showToastIfMessage = (
  responseData: unknown,
  title: string,
  variant: "default" | "destructive" | "success" = "default",
) => {
  // Handle axios error objects
  let dataToExtract = responseData;
  if (
    responseData &&
    typeof responseData === "object" &&
    "response" in responseData
  ) {
    const axiosError = responseData as { response?: { data?: unknown } };
    dataToExtract = axiosError.response?.data;
  }

  const message = extractMessageFromResponse(dataToExtract);
  if (message) {
    toast({
      title,
      description: message,
      variant,
    });
  }
};

// Helper to extract backend error message (keeping for backward compatibility)
// interface ErrorDetails {
//   error?: { message?: string };
// }
// interface ErrorResponseData {
//   message?: string;
//   details?: ErrorDetails;
// }
// interface AxiosLikeError {
//   response?: { data?: ErrorResponseData };
//   message?: string;
// }
// function getApiErrorMessage(error: unknown): string | undefined {
//   if (!error) return undefined;
//   if (typeof error === "object" && error !== null && "response" in error) {
//     const errObj = error as AxiosLikeError;
//     const data = errObj.response?.data;
//     if (data) {
//       if (data.details?.error?.message) return data.details.error.message;
//       if (data.message) return data.message;
//     }
//     if (errObj.message) return errObj.message;
//   }
//   if (typeof error === "string") return error;
//   return undefined;
// }

interface ManufacturingState {
  // Data
  data: {
    customers: FilterOption[];
    projects: Record<string, FilterOption[]>;
    families: Record<string, FilterOption[]>;
    valueStreams: Record<string, FilterOption[]>;
    areas: Record<string, FilterOption[]>;
    teams: Record<string, FilterOption[]>;
    workers: Record<string, Worker[]>;
    stationsByRole: StationsByRole;
    meta: {
      pageSize: number;
      currentPage: number;
      totalPages: number;
      totalCount: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  };
  unassignedWorkers: Worker[];
  // Pagination state
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  // Search and sorting state
  searchQuery: string;
  sorting: { id: string; desc: boolean }[];
  setSorting: (sorting: { id: string; desc: boolean }[]) => void;
  setSearchQuery: (query: string) => void;

  // Separate sorting states for each table
  workstationSorting: { id: string; desc: boolean }[];
  setWorkstationSorting: (sorting: { id: string; desc: boolean }[]) => void;
  unassignedWorkersSorting: { id: string; desc: boolean }[];
  setUnassignedWorkersSorting: (
    sorting: { id: string; desc: boolean }[],
  ) => void;

  // Options for each select
  customerOptions: FilterOption[];
  projectOptions: FilterOption[];
  familyOptions: FilterOption[];
  valueStreamOptions: FilterOption[];
  areaOptions: FilterOption[];
  teamOptions: FilterOption[];
  meDefinitionOptions: FilterOption[];
  meDefinitionVersions: MeDefinitionVersion[];
  selectedMeDefinitionEffectiveDate: string;

  // Selected values
  selectedCustomer: string;
  selectedProject: string;
  selectedFamily: string;
  selectedValueStream: string;
  selectedArea: string;
  selectedTeam: string;
  selectedMeDefinition: string; // Move this to the top-level state

  // Loading states
  isLoading: boolean;
  isWorkstationAssignmentsLoading: boolean;
  isUnassignedWorkersLoading: boolean;
  isInitialOptionsFetched: boolean;

  // Workers data
  workers: Worker[];

  // Selected workers for the table
  selectedWorkers: string[];
  selectAll: boolean;

  // UI state
  editingRowIndex: number | null;
  editedWorker: Worker | null;
  workerIdsToDelete: string[];
  selectedRow: Worker | WorkstationAssignment | null;
  newWorker: Worker | null;
  // Operation skills
  operationSkills: Skills[];
  selectedSkills: string[];

  // Actions
  fetchUnassignedWorkers: (
    pageIndex?: number,
    pageSize?: number,
    searchQuery?: string,
  ) => Promise<void>;
  setSelectedCustomer: (id: string) => void;
  setSelectedProject: (id: string) => void;
  setSelectedFamily: (id: string) => void;
  setSelectedValueStream: (id: string) => void;
  setSelectedArea: (id: string) => void;
  setSelectedTeam: (id: string) => void;
  setSelectedMeDefinition: (id: string) => void;
  toggleSelectWorker: (id: string) => void;
  toggleSelectAll: () => void;
  updateWorker: (workerUpdate: WorkerUpdate) => Promise<void>;
  selectedCrewMember: Worker | WorkstationAssignment | null;
  setSelectedCrewMember: (
    worker: Worker | WorkstationAssignment | null,
  ) => void;
  assignWorkers: (payload: AssignWorkersPayload) => Promise<void>;
  toggleWorkerSelection: (id: string) => void;
  selectWorkers: (ids: string[]) => void;
  clearWorkerSelection: () => void;
  setSkills: (skills: string[]) => void;
  addNewWorker: (worker: Worker) => Promise<void>;
  filteredUnassignedWorkers: () => Worker[];
  fetchTableData: (page: number, pageSize: number) => Promise<void>;
  fetchOptions: <T>(
    endpoint: string,
    params: Record<string, string>,
  ) => Promise<T>;
  updateSelection: (
    level: string,
    id: string,
    dependentFields: Partial<ManufacturingState>,
  ) => Promise<void>;
  fetchFilteredOptions: (filters: Record<string, string>) => Promise<void>;

  // New centralized actions
  startEditing: (index: number) => Promise<void>;
  cancelEditing: () => void;
  saveEdits: () => Promise<void>;
  initiateNewWorker: () => void;
  saveNewWorker: () => Promise<void>;
  handleDelete: () => Promise<void>;
  handleRowClick: (worker: Worker | WorkstationAssignment | null) => void;
  handleSelect: (level: string, value: string) => void;
  handleWorkerSelection: (id: string) => void;
  handleSelectionChange: (value: string) => void;
  handleUnselectAll: () => void;
  handleConfirm: () => Promise<void>;
  setEditingRowIndex: (index: number | null) => void;
  setEditedWorker: (worker: Worker | null) => void;
  setWorkerIdsToDelete: (ids: string[]) => void;
  setSelectedRow: (worker: Worker | WorkstationAssignment | null) => void;
  setNewWorker: (worker: Worker | null) => void;

  fetchMeDefinitionOptions: (filters: Record<string, string>) => Promise<void>;
  fetchMeDefinitionVersions: (filters: Record<string, string>) => Promise<void>;

  setSelectedWorkers: (workerIds: string[]) => void;

  // Operation skills actions
  fetchOperationSkills: () => Promise<void>;
  setSelectedSkills: (skills: string[]) => void;

  workstationAssignments: WorkstationAssignment[];
  workstationPagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  fetchWorkstationAssignments: (
    pageIndex?: number,
    pageSize?: number,
    searchQuery?: string,
  ) => Promise<void>;

  // Workstation options
  workstationOptions: WorkstationOption[];
  selectedWorkstation: string | null;

  // Workstation actions
  fetchWorkstationOptions: (teamleader_id: string) => Promise<void>;
  setSelectedWorkstation: (workstationId: string | null) => void;

  // Operator reassignment
  operatorsForReassignment: OperatorForReassignment[];
  selectedOperator: OperatorForReassignment | null;

  // Operator reassignment actions
  fetchOperatorsForReassignment: (
    workstationId: string,
    teamleader_id: string,
    team_name: string,
  ) => Promise<void>;

  // New function specifically for MS station creation
  fetchOperatorsForMsStationCreation: (
    msStationId: string,
    teamleader_id: string,
    team_name: string,
    customer_id: string,
    project_id: string,
    family_id: string,
    value_stream_id: string,
    area_id: string,
    me_definition_version: string,
    effective_date: string,
  ) => Promise<void>;

  setSelectedOperator: (operator: OperatorForReassignment | null) => void;

  clearOperatorsForReassignment: () => void;

  // Assignment actions
  assignToWorkstation: (
    request: AssignToWorkstationRequest,
  ) => Promise<AssignToWorkstationResponse>;

  createWorkstation: (
    request: CreateWorkstationRequest,
  ) => Promise<CreateWorkstationResponse>;

  deleteWorkstation: (
    request: DeleteWorkstationRequest,
  ) => Promise<DeleteWorkstationResponse>;

  handleFieldDeselection: (level: string) => void;

  meDefinitionTeamLeaderLegacySite: string;

  workstationMeta: {
    totalUnassignedME: number;
    totalUnassignedMS: number;
    totalAssignedME: number;
    totalAssignedMS: number;
  };
}

interface CreateWorkstationRequest {
  msStationId: string;
  teamLeader_id: string;
  team_id: string;
  customer_id: string;
  project_id: string;
  family_id: string;
  value_stream_id: string;
  area_id: string;
  me_definition_version: string;
  effective_date: string;
  assignedId?: string;
}

interface CreateWorkstationResponse {
  statusCode: number;
  message: string;
}

interface DeleteWorkstationRequest {
  stationId: string;
  teamleader_id: string;
  operatorId?: string;
  team_id?: string;
}

interface DeleteWorkstationResponse {
  statusCode: number;
  message: string;
}

// Type guard to check if it's a Worker
const isWorker = (member: Worker | WorkstationAssignment): member is Worker => {
  return "idWorker" in member;
};

const useCrewManagementStore = create<ManufacturingState>((set, get) => ({
  // Data
  data: {
    customers: [],
    projects: {},
    families: {},
    valueStreams: {},
    areas: {},
    teams: {},
    workers: {},
    stationsByRole: {
      "ME STRUCTURE": [],
      "MANUFACTURING STRUCTURE": [],
    },
    meta: {
      pageSize: 5,
      currentPage: 1,
      totalPages: 0,
      totalCount: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    },
  },

  // Options for each select
  customerOptions: [],
  projectOptions: [],
  familyOptions: [],
  valueStreamOptions: [],
  areaOptions: [],
  teamOptions: [],
  meDefinitionOptions: [],
  meDefinitionVersions: [],
  selectedMeDefinitionEffectiveDate: "",

  // Selected values
  selectedCustomer: "",
  selectedProject: "",
  selectedFamily: "",
  selectedValueStream: "",
  selectedArea: "",
  selectedTeam: "",
  selectedMeDefinition: "",

  // Loading states
  isLoading: false,
  isWorkstationAssignmentsLoading: false,
  isUnassignedWorkersLoading: false,
  isInitialOptionsFetched: false,

  // Workers data
  workers: [],

  // Selected workers for the table
  selectedWorkers: [],
  selectAll: false,

  // UI state
  editingRowIndex: null,
  editedWorker: null,
  workerIdsToDelete: [],
  selectedRow: null,
  newWorker: null,

  // Pagination state
  pagination: {
    currentPage: 1,
    pageSize: 5,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },

  // Operation skills
  operationSkills: [],
  selectedSkills: [],

  // Search and sorting state
  searchQuery: "",
  sorting: [],
  setSorting: (sorting) => set({ sorting }),
  setSearchQuery: (query) => set({ searchQuery: query }),

  // Separate sorting states for each table
  workstationSorting: [],
  setWorkstationSorting: (sorting) => set({ workstationSorting: sorting }),
  unassignedWorkersSorting: [],
  setUnassignedWorkersSorting: (sorting) =>
    set({ unassignedWorkersSorting: sorting }),

  // Centralized API handler
  fetchOptions: async <T>(
    endpoint: string,
    params: Record<string, string>,
  ): Promise<T> => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.get<T>(endpoint, {
        params: {
          ...params,
          connected_user: currentUserId,
        },
      });
      set({ isLoading: false });
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch data from ${endpoint}:`, error);
      set({ isLoading: false });
      throw error;
    }
  },

  // Helper function for updating selection
  updateSelection: async (
    level: string,
    id: string,
    dependentFields: Partial<ManufacturingState>,
  ) => {
    const data = await get().fetchOptions("/crew-management-list", {
      [`${level}Id`]: id,
    });
    set({
      ...dependentFields,
      [`selected${level.charAt(0).toUpperCase() + level.slice(1)}`]: id,
      ...(typeof data === "object" && data !== null ? data : {}),
    });
  },
  setSelectedCustomer: async (id: string) => {
    try {
      // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
      if (!id || id.trim() === "") {
        set((state) => ({
          ...state,
          selectedCustomer: "",
          // Clear dependent fields
          projectOptions: [],
          familyOptions: [],
          valueStreamOptions: [],
          areaOptions: [],
          teamOptions: [],
          selectedProject: "",
          selectedFamily: "",
          selectedValueStream: "",
          selectedArea: "",
          selectedTeam: "",
        }));
        return;
      }

      // Call the new hierarchical filtering API
      await get().fetchFilteredOptions({ customer_id: id });

      // Update the selected customer
      set((state) => ({
        ...state,
        selectedCustomer: id,
        // Clear dependent fields
        projectOptions: [],
        familyOptions: [],
        valueStreamOptions: [],
        areaOptions: [],
        teamOptions: [],
        selectedProject: "",
        selectedFamily: "",
        selectedValueStream: "",
        selectedArea: "",
        selectedTeam: "",
      }));
    } catch (error) {
      console.error("Error setting selected customer:", error);
    }
  },
  setSelectedProject: async (id: string) => {
    try {
      // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
      if (!id || id.trim() === "") {
        set((state) => ({
          ...state,
          selectedProject: "",
          familyOptions: [],
          valueStreamOptions: [],
          areaOptions: [],
          teamOptions: [],
          selectedFamily: "",
          selectedValueStream: "",
          selectedArea: "",
          selectedTeam: "",
        }));
        return;
      }

      const { selectedCustomer } = get();
      await get().fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: id,
      });

      set((state) => ({
        ...state,
        selectedProject: id,
        familyOptions: [],
        valueStreamOptions: [],
        areaOptions: [],
        teamOptions: [],
        selectedFamily: "",
        selectedValueStream: "",
        selectedArea: "",
        selectedTeam: "",
      }));
    } catch (error) {
      console.error("Error setting selected project:", error);
    }
  },
  setSelectedFamily: async (id: string) => {
    try {
      // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
      if (!id || id.trim() === "") {
        set((state) => ({
          ...state,
          selectedFamily: "",
          valueStreamOptions: [],
          areaOptions: [],
          teamOptions: [],
          selectedValueStream: "",
          selectedArea: "",
          selectedTeam: "",
        }));
        return;
      }

      const { selectedCustomer, selectedProject } = get();
      await get().fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: id,
      });

      set((state) => ({
        ...state,
        selectedFamily: id,
        valueStreamOptions: [],
        areaOptions: [],
        teamOptions: [],
        selectedValueStream: "",
        selectedArea: "",
        selectedTeam: "",
      }));
    } catch (error) {
      console.error("Error setting selected family:", error);
    }
  },
  setSelectedValueStream: async (id: string) => {
    try {
      // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
      if (!id || id.trim() === "") {
        set((state) => ({
          ...state,
          selectedValueStream: "",
          areaOptions: [],
          teamOptions: [],
          selectedArea: "",
          selectedTeam: "",
        }));
        return;
      }

      const { selectedCustomer, selectedProject, selectedFamily } = get();
      await get().fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
        value_stream_id: id,
      });

      set((state) => ({
        ...state,
        selectedValueStream: id,
        areaOptions: [],
        teamOptions: [],
        selectedArea: "",
        selectedTeam: "",
      }));
    } catch (error) {
      console.error("Error setting selected value stream:", error);
    }
  },
  setSelectedArea: async (id: string) => {
    try {
      // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
      if (!id || id.trim() === "") {
        set((state) => ({
          ...state,
          selectedArea: "",
          teamOptions: [],
          selectedTeam: "",
        }));
        return;
      }

      const {
        selectedCustomer,
        selectedProject,
        selectedFamily,
        selectedValueStream,
      } = get();
      await get().fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
        value_stream_id: selectedValueStream,
        area_id: id,
      });

      set((state) => ({
        ...state,
        selectedArea: id,
        teamOptions: [],
        selectedTeam: "",
      }));
    } catch (error) {
      console.error("Error setting selected area:", error);
    }
  },
  setSelectedTeam: async (id: string) => {
    try {
      // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
      if (!id || id.trim() === "") {
        set((state) => ({
          ...state,
          selectedTeam: "",
        }));
        return;
      }

      set((state) => ({
        ...state,
        selectedTeam: id,
      }));
    } catch (error) {
      console.error("Error setting selected team:", error);
    }
  },
  setSelectedMeDefinition: async (id: string) => {
    console.log("Debug - setSelectedMeDefinition called with id:", id);

    // Early return if id is empty or falsy (prevent unnecessary API call on deselection)
    if (!id || id.trim() === "") {
      set({
        selectedMeDefinition: "",
        selectedMeDefinitionEffectiveDate: "",
      });
      return;
    }

    const { meDefinitionVersions } = get();
    console.log("Debug - Current meDefinitionVersions:", meDefinitionVersions);

    const selectedVersion = meDefinitionVersions.find(
      (version) => version.me_definition_version === id,
    );
    console.log("Debug - Selected version:", selectedVersion);

    set({
      selectedMeDefinition: id,
      selectedMeDefinitionEffectiveDate: selectedVersion?.effective_date || "",
    });

    // Fetch workstation assignments and unassigned workers
    console.log(
      "Debug - About to call fetchWorkstationAssignments and fetchUnassignedWorkers",
    );
    try {
      await Promise.all([
        get().fetchWorkstationAssignments(0, 5),
        get().fetchUnassignedWorkers(0, 5),
      ]);
      console.log(
        "Debug - fetchWorkstationAssignments and fetchUnassignedWorkers completed successfully",
      );
    } catch (error) {
      console.error(
        "Debug - Error in fetchWorkstationAssignments or fetchUnassignedWorkers:",
        error,
      );
    }

    // Fetch operation skills after setting ME definition
    get().fetchOperationSkills();
  },
  fetchTableData: async (page: number, pageSize: number) => {
    set({ isLoading: true });
    try {
      const {
        selectedCustomer,
        selectedProject,
        selectedFamily,
        selectedValueStream,
        selectedArea,
        selectedTeam,
        selectedMeDefinition,
      } = get();

      const currentUserId = getCurrentUserId();
      const params: Record<string, string | number> = {
        page,
        pageSize,
        connected_user: currentUserId,
      };

      // Add all selected values to the request if they exist
      if (selectedCustomer) params.customer_id = selectedCustomer;
      if (selectedProject) params.project_id = selectedProject;
      if (selectedFamily) params.family_id = selectedFamily;
      if (selectedValueStream) params.value_stream_id = selectedValueStream;
      if (selectedArea) params.area_id = selectedArea;
      if (selectedTeam) params.team_id = selectedTeam;
      if (selectedMeDefinition)
        params.me_definition_version = selectedMeDefinition;

      const response = await api.get("/workstation/crew-management-table", {
        params,
      });
      set({ workers: response.data || [], isLoading: false });
    } catch (error) {
      console.error("Failed to fetch table data:", error);
      set({ isLoading: false });
    }
  },

  toggleSelectWorker: (id: string) => {
    const { selectedWorkers, workers } = get();
    const isSelected = selectedWorkers.includes(id);
    set({
      selectedWorkers: isSelected
        ? selectedWorkers.filter((workerId) => workerId !== id)
        : [...selectedWorkers, id],
      selectAll: !isSelected && selectedWorkers.length + 1 === workers.length,
    });
  },

  toggleSelectAll: () => {
    const { selectAll, workers } = get();
    set({
      selectedWorkers: selectAll ? [] : workers.map((worker) => worker.id),
      selectAll: !selectAll,
    });
  },
  updateWorker: async (workerUpdate: WorkerUpdate) => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.post(
        "/workstation/api/workers/update",
        workerUpdate,
        {
          params: {
            connected_user: currentUserId,
          },
        },
      );
      const { workers } = get();
      const updatedWorkers = workers.map((worker) =>
        worker.id === workerUpdate.id
          ? {
              ...worker,
              role: workerUpdate.role,
              station: workerUpdate.station,
            }
          : worker,
      );
      set({ workers: updatedWorkers, isLoading: false });
      // Show success toast if backend provides a message for POST operation
      showToastIfMessage(response.data, "Success", "success");
    } catch (error) {
      console.error("Failed to update worker:", error);
      set({ isLoading: false });
      // Only show error toast if backend provides a message
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  assignWorkers: async (payload: AssignWorkersPayload) => {
    set({ isLoading: true });
    try {
      const { selectedCrewMember } = get();

      if (!selectedCrewMember) {
        throw new Error("No crew member selected");
      }

      let assignFromId: string;
      if (isWorker(selectedCrewMember)) {
        assignFromId = selectedCrewMember.idWorker || "";
      } else {
        assignFromId = selectedCrewMember.stationId || "";
      }
      const assignPayload = {
        ...payload,
        assignFromId,
      };

      const currentUserId = getCurrentUserId();
      const response = await api.post(
        "/workstation/crew-management-list/assign",
        assignPayload,
        {
          params: {
            connected_user: currentUserId,
          },
        },
      );
      // Show success toast if backend provides a message for POST operation
      showToastIfMessage(response.data, "Success", "success");
      get().fetchWorkstationAssignments();
    } catch (error) {
      console.error("Failed to assign workers:", error);
      // Only show error toast if backend provides a message
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    } finally {
      set({ isLoading: false });
    }
  },

  selectedCrewMember: null,
  unassignedWorkers: [],
  setSelectedCrewMember: (worker: Worker | WorkstationAssignment | null) =>
    set({ selectedCrewMember: worker }),
  fetchUnassignedWorkers: async (
    pageIndex = 0,
    pageSize = 5,
    searchQuery?: string,
  ) => {
    const { selectedMeDefinition } = get();

    // Only fetch if ME definition is selected
    if (!selectedMeDefinition) {
      set({
        unassignedWorkers: [],
        pagination: {
          currentPage: 1,
          pageSize: 5,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
        isUnassignedWorkersLoading: false,
      });
      return;
    }

    set({ isUnassignedWorkersLoading: true });
    try {
      const {
        selectedTeam,
        meDefinitionTeamLeaderLegacySite,
        selectedSkills,
        unassignedWorkersSorting,
      } = get();

      // Build query parameters for the new endpoint
      const currentUserId = getCurrentUserId();
      const params: Record<string, string> = {
        pageNumber: String(pageIndex + 1), // Convert to 1-based index for API
        pageSize: String(pageSize),
        sortBy: unassignedWorkersSorting[0]?.id || "fullname",
        sortOrder: unassignedWorkersSorting[0]?.desc ? "desc" : "asc",
        connected_user: currentUserId,
      };

      // Add search query if provided
      if (searchQuery) {
        params.search = searchQuery;
      }

      // Add all selected values to the request if they exist
      if (selectedTeam) params.team_id = selectedTeam;
      if (meDefinitionTeamLeaderLegacySite)
        params.teamleader_id = meDefinitionTeamLeaderLegacySite;

      // Add selected skills if any
      if (selectedSkills && selectedSkills.length > 0) {
        params.skills = selectedSkills.join(",");
      }

      const response = await api.get(
        "/workstation/workstation/unassigned-operators",
        {
          params,
        },
      );

      if (response.data && response.data.items) {
        set({
          unassignedWorkers: response.data.items,
          pagination: {
            currentPage: response.data.meta.currentPage,
            pageSize: response.data.meta.pageSize,
            totalCount: response.data.meta.totalCount,
            totalPages: response.data.meta.totalPages,
            hasNextPage: response.data.meta.hasNextPage,
            hasPreviousPage: response.data.meta.hasPreviousPage,
          },
          isUnassignedWorkersLoading: false,
        });
      } else {
        console.error("Invalid response format:", response.data);
        set({ isUnassignedWorkersLoading: false });
        toast({
          title: "Error",
          description: "Invalid response format from server",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to fetch unassigned workers:", error);
      set({ isUnassignedWorkersLoading: false });
      showToastIfMessage(error, "Error", "destructive");
    }
  },
  toggleWorkerSelection: (id: string) => {
    const { selectedWorkers } = get();
    if (selectedWorkers.includes(id)) {
      set({
        selectedWorkers: selectedWorkers.filter((workerId) => workerId !== id),
      });
    } else {
      set({
        selectedWorkers: [...selectedWorkers, id],
      });
    }
  },
  selectWorkers: (ids: string[]) => {
    set({ selectedWorkers: ids });
  },
  clearWorkerSelection: () => {
    set({ selectedWorkers: [] });
  },
  setSkills: (skills: string[]) => {
    console.log("Setting skills:", skills);
  },
  addNewWorker: async (worker: Worker) => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      await api.post("/workstation/api/workers/add", worker, {
        params: {
          connected_user: currentUserId,
        },
      });

      const { workers, selectedTeam, data } = get();
      const updatedWorkers = [...workers, worker];

      const updatedData = { ...data };
      if (selectedTeam && updatedData.workers[selectedTeam]) {
        updatedData.workers[selectedTeam] = updatedWorkers;
      }

      set({
        workers: updatedWorkers,
        data: updatedData,
        isLoading: false,
      });

      toast({
        title: "Success",
        description: "Worker added successfully",
        variant: "success",
      });

      return Promise.resolve();
    } catch (error) {
      console.error("Failed to add worker:", error);
      showToastIfMessage(error, "Error", "destructive");
      set({ isLoading: false });
      return Promise.reject(error);
    }
  },
  filteredUnassignedWorkers: () => {
    const { unassignedWorkers, selectedTeam, selectedMeDefinition } = get();

    if (!selectedTeam || !selectedMeDefinition) return [];

    // Return all unassigned workers when both team and ME definition are selected
    return unassignedWorkers;
  },
  fetchFilteredOptions: async (filters: Record<string, string>) => {
    try {
      const currentUserId = getCurrentUserId();

      // Extract the level from filters if provided
      const { level, ...otherFilters } = filters;

      const response = await api.get<ZoningOptionsResponse>(
        `/workstation/zoning/options/${currentUserId}`,
        {
          params: {
            level,
            ...otherFilters,
          },
        },
      );
      const { values, metadata } = response.data;

      // Update the appropriate options based on the level returned
      const responseLevel = metadata.level.toLowerCase();
      const formattedOptions = values.map((item) => ({
        id: item.id,
        label: item.value,
        value: item.value,
      }));

      // Map the level from API response to our store field names
      const levelMapping: Record<string, string> = {
        customer: "customerOptions",
        customer_id: "customerOptions",
        project: "projectOptions",
        project_id: "projectOptions",
        family: "familyOptions",
        family_id: "familyOptions",
        value_stream: "valueStreamOptions",
        value_stream_id: "valueStreamOptions",
        area: "areaOptions",
        area_id: "areaOptions",
        team: "teamOptions",
        team_id: "teamOptions",
        team_name: "teamOptions",
      };

      const optionsField = levelMapping[responseLevel];
      if (!optionsField) {
        console.error("Unknown level received from API:", responseLevel);
        return;
      }

      // Normalize the response level for consistent handling
      const normalizedResponseLevel = normalizeLevel(responseLevel);

      console.log(
        "Updating options for level:",
        responseLevel,
        "(normalized:",
        normalizedResponseLevel,
        ") with values:",
        formattedOptions,
      ); // Debug log

      set((state) => {
        const updates: Partial<ManufacturingState> = {
          [optionsField]: formattedOptions,
        };

        // Clear dependent fields if we're fetching a higher level
        if (normalizedResponseLevel === "customer") {
          updates.projectOptions = [];
          updates.familyOptions = [];
          updates.valueStreamOptions = [];
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.selectedProject = "";
          updates.selectedFamily = "";
          updates.selectedValueStream = "";
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        } else if (normalizedResponseLevel === "project") {
          updates.familyOptions = [];
          updates.valueStreamOptions = [];
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.meDefinitionOptions = [];
          updates.meDefinitionVersions = [];
          updates.selectedFamily = "";
          updates.selectedValueStream = "";
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        } else if (normalizedResponseLevel === "family") {
          updates.valueStreamOptions = [];
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.meDefinitionOptions = [];
          updates.meDefinitionVersions = [];
          updates.selectedValueStream = "";
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        } else if (normalizedResponseLevel === "value_stream") {
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.meDefinitionOptions = [];
          updates.meDefinitionVersions = [];
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        } else if (normalizedResponseLevel === "area") {
          updates.teamOptions = [];
          updates.meDefinitionOptions = [];
          updates.meDefinitionVersions = [];
          updates.selectedTeam = "";
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        } else if (normalizedResponseLevel === "team") {
          updates.meDefinitionOptions = [];
          updates.meDefinitionVersions = [];
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        } else if (normalizedResponseLevel === "me_definition") {
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
        }

        return { ...state, ...updates };
      });
    } catch (error) {
      console.error("Error fetching filtered options:", error);
      showToastIfMessage(error, "Error", "destructive");
    }
  },

  handleFieldDeselection: (level: string) => {
    set((state) => {
      const updates: Partial<ManufacturingState> = {};

      // Normalize the level name (remove _id suffix)
      const normalizedLevel = normalizeLevel(level);

      // Clear the selected value for the deselected field
      const selectedKey =
        `selected${normalizedLevel.charAt(0).toUpperCase() + normalizedLevel.slice(1)}` as keyof Pick<
          ManufacturingState,
          | "selectedCustomer"
          | "selectedProject"
          | "selectedFamily"
          | "selectedValueStream"
          | "selectedArea"
          | "selectedTeam"
          | "selectedMeDefinition"
        >;
      updates[selectedKey] = "";

      // Reset pagination state
      updates.pagination = {
        currentPage: 1,
        pageSize: 5,
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      };

      // Reset sorting states
      updates.workstationSorting = [];
      updates.unassignedWorkersSorting = [];

      // Clear all dependent fields based on the hierarchy
      switch (normalizedLevel.toLowerCase()) {
        case "customer":
          updates.projectOptions = [];
          updates.familyOptions = [];
          updates.valueStreamOptions = [];
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.selectedProject = "";
          updates.selectedFamily = "";
          updates.selectedValueStream = "";
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          break;
        case "project":
          updates.familyOptions = [];
          updates.valueStreamOptions = [];
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.selectedFamily = "";
          updates.selectedValueStream = "";
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          break;
        case "family":
          updates.valueStreamOptions = [];
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.selectedValueStream = "";
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          break;
        case "value_stream":
          updates.areaOptions = [];
          updates.teamOptions = [];
          updates.selectedArea = "";
          updates.selectedTeam = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          break;
        case "area":
          updates.teamOptions = [];
          updates.selectedTeam = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          break;
        case "team":
          // Only clear ME definition related fields when clearing team
          updates.meDefinitionOptions = [];
          updates.meDefinitionVersions = [];
          updates.selectedMeDefinition = "";
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
          break;
        case "me_definition":
          updates.selectedMeDefinitionEffectiveDate = "";
          updates.meDefinitionTeamLeaderLegacySite = "";
          updates.workstationAssignments = [];
          updates.unassignedWorkers = [];
          updates.workstationSorting = [];
          updates.unassignedWorkersSorting = [];
          updates.workstationMeta = {
            totalUnassignedME: 0,
            totalUnassignedMS: 0,
            totalAssignedME: 0,
            totalAssignedMS: 0,
          };
          break;
      }

      return { ...state, ...updates };
    });
  },

  fetchMeDefinitionOptions: async (filters: Record<string, string>) => {
    set({ isLoading: true });
    try {
      // Use the actual API endpoint for ME Definition options
      const currentUserId = getCurrentUserId();
      const response = await api.get("/workstation/zoning/me-definition", {
        params: {
          ...filters,
          connected_user: currentUserId,
        },
      });
      const { values } = response.data;

      // Map the list of strings into FilterOption objects
      const options = values.map((value: string) => ({
        id: value,
        value,
        label: value,
        role: "",
      }));

      // Update the ME Definition options
      set({ meDefinitionOptions: options });
    } catch (error) {
      console.error("Failed to fetch ME Definition options:", error);
      showToastIfMessage(error, "Error", "destructive");
    } finally {
      set({ isLoading: false });
    }
  },

  fetchMeDefinitionVersions: async (filters: Record<string, string>) => {
    set({ isLoading: true });
    try {
      console.log(
        "Debug - Fetching ME definition versions with filters:",
        filters,
      );
      const currentUserId = getCurrentUserId();
      const response = await api.get<MeDefinitionResponse>(
        "/workstation/zoning/me-definition-versions",
        {
          params: {
            customer_id: filters.customer_id,
            project_id: filters.project_id,
            family_id: filters.family_id,
            value_stream_id: filters.value_stream_id,
            area_id: filters.area_id,
            team_id: filters.team_id || filters.team_name || filters.team,
            connected_user: currentUserId,
          },
        },
      );

      console.log("Debug - ME definition versions response:", response.data);

      const { versions, teamLeader_id } = response.data;

      // Update the ME Definition options with version information and include teamLeader_id
      const updatedVersions = versions.map((version: MeDefinitionVersion) => ({
        ...version,
        teamLeader_id: teamLeader_id,
      }));

      const updatedOptions = updatedVersions.map(
        (version: MeDefinitionVersion) => ({
          id: version.me_definition_version,
          value: version.me_definition_version,
          label: version.isCurrent
            ? `${version.me_definition_version} (Current version)`
            : version.me_definition_version,
          role: "",
          isCurrent: version.isCurrent,
          effectiveDate: version.effective_date,
        }),
      );

      console.log("Debug - Setting ME definition versions:", updatedVersions);
      console.log("Debug - Setting teamLeader_id:", teamLeader_id);

      set({
        meDefinitionOptions: updatedOptions,
        meDefinitionVersions: updatedVersions,
        meDefinitionTeamLeaderLegacySite: teamLeader_id,
      });
    } catch (error) {
      console.error("Failed to fetch ME Definition versions:", error);
      // Only show error toast if backend provides a message
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    } finally {
      set({ isLoading: false });
    }
  },

  // New centralized actions
  startEditing: async (index: number) => {
    const {
      workstationAssignments,
      meDefinitionVersions,
      selectedMeDefinition,
      selectedTeam,
      fetchOperatorsForReassignment,
      meDefinitionTeamLeaderLegacySite,
    } = get();

    const row = workstationAssignments[index];
    console.log("startEditing called with:", {
      index,
      row,
      selectedMeDefinition,
      selectedTeam,
      meDefinitionVersions,
      meDefinitionTeamLeaderLegacySite,
    });

    if (row) {
      // Clear operators list before starting edit
      get().clearOperatorsForReassignment();

      // Create a Worker object from the WorkstationAssignment
      const worker: Worker = {
        id: row.id,
        idWorker: row.operatorId || "",
        name: row.operatorFirstName || "",
        surname: row.operatorLastName || "",
        role: row.stationRole || "", // Use stationRole instead of role, with fallback
        station: row.station,
        stationId: row.stationId,
        skills: row.workstationSkills || [],
        legacyId: row.operatorLegacyId || 0,
        fullname:
          `${row.operatorFirstName || ""} ${row.operatorLastName || ""}`.trim(),
        department: "", // Default value for required field
        department_id: "", // Default value for required field
      };

      set({
        editingRowIndex: index,
        editedWorker: worker,
      });

      try {
        // Always use fetchOperatorsForReassignment when editing with pencil
        await fetchOperatorsForReassignment(
          row.stationId,
          meDefinitionTeamLeaderLegacySite,
          selectedTeam,
        );
      } catch (error) {
        console.error("Error fetching operators for reassignment:", error);
        toast({
          title: "Error",
          description: "Failed to fetch operators for reassignment",
          variant: "destructive",
        });
      }
    } else {
      console.log("No row found at index:", index);
    }
  },

  cancelEditing: () => {
    set({
      editingRowIndex: null,
      editedWorker: null,
    });
  },

  saveEdits: async () => {
    const { editedWorker, updateWorker } = get();
    if (!editedWorker) return;

    await updateWorker({
      id: editedWorker.id,
      role: editedWorker.role || "",
      station: editedWorker.station || "",
    });

    set({
      editingRowIndex: null,
      editedWorker: null,
    });
  },

  initiateNewWorker: async () => {
    const { meDefinitionTeamLeaderLegacySite } = get();

    if (!meDefinitionTeamLeaderLegacySite) {
      console.error(
        "Missing teamLeader_legacy_site for fetching workstation options",
      );
      // Do not show a toast, only log error
      return;
    }

    try {
      // Clear operators list before starting new worker
      get().clearOperatorsForReassignment();

      // For new MS line, always fetch MS station options
      await get().fetchWorkstationOptions(meDefinitionTeamLeaderLegacySite);

      // Then set up the new worker
      set({
        newWorker: {
          id: "",
          idWorker: "",
          name: "",
          surname: "",
          role: "",
          station: "",
          stationId: "",
          skills: [],
          legacyId: 0,
          fullname: "",
          department: "",
          department_id: "",
        },
        editingRowIndex: 0, // Set to 0 since it's the first row when adding new
      });
    } catch (error) {
      console.error("Error fetching workstation options:", error);
      showToastIfMessage(error, "Error", "destructive");
    }
  },

  setNewWorker: (worker: Worker | null) => {
    set({ newWorker: worker });

    // If we have a new worker with a workstationId, fetch operators for MS station
    if (worker?.workstationId) {
      const {
        selectedTeam,
        selectedMeDefinition,
        meDefinitionVersions,
        selectedCustomer,
        selectedProject,
        selectedFamily,
        selectedValueStream,
        selectedArea,
        selectedMeDefinitionEffectiveDate,
        fetchOperatorsForMsStationCreation,
      } = get();

      const currentVersion = meDefinitionVersions.find(
        (version) => version.me_definition_version === selectedMeDefinition,
      );

      if (currentVersion?.teamLeader_id) {
        console.log("Debug - Fetching operators for MS station creation");
        fetchOperatorsForMsStationCreation(
          worker.workstationId,
          currentVersion.teamLeader_id,
          selectedTeam,
          selectedCustomer || "",
          selectedProject || "",
          selectedFamily || "",
          selectedValueStream || "",
          selectedArea || "",
          selectedMeDefinition,
          selectedMeDefinitionEffectiveDate,
        );
      }
    }
  },

  saveNewWorker: async () => {
    const { newWorker, addNewWorker } = get();
    if (!newWorker) return;

    await addNewWorker(newWorker);
    set({
      editingRowIndex: null,
      newWorker: null,
    });
  },

  handleDelete: async () => {
    const { workerIdsToDelete, updateWorker } = get();

    for (const id of workerIdsToDelete) {
      await updateWorker({
        id,
        role: "",
        station: "",
      });
    }

    set({
      workerIdsToDelete: [],
    });
  },

  handleRowClick: (worker: Worker | WorkstationAssignment | null) => {
    set({ selectedRow: worker });
  },

  handleSelect: (level: string, value: string) => {
    // Normalize the level name (remove _id suffix)

    const normalizedLevel = normalizeLevel(level);

    const actions: Record<string, () => void> = {
      customer: () => get().setSelectedCustomer(value),
      project: () => get().setSelectedProject(value),
      family: () => get().setSelectedFamily(value),
      valueStream: () => get().setSelectedValueStream(value),
      area: () => get().setSelectedArea(value),
      team: () => get().setSelectedTeam(value),
      team_name: () => get().setSelectedTeam(value),
      meDefinition: () => get().setSelectedMeDefinition(value),
    };

    if (actions[normalizedLevel]) {
      actions[normalizedLevel]();
    }
  },

  handleWorkerSelection: (id: string) => {
    const { selectedWorkers, selectWorkers } = get();
    if (selectedWorkers.includes(id)) {
      selectWorkers(selectedWorkers.filter((workerId) => workerId !== id));
    } else {
      selectWorkers([...selectedWorkers, id]);
    }
  },

  handleSelectionChange: (value: string) => {
    const count = parseInt(value, 10);
    if (isNaN(count)) return;

    const { unassignedWorkers, selectWorkers, clearWorkerSelection } = get();

    if (count === 0) {
      clearWorkerSelection();
    } else {
      const workersToSelect = unassignedWorkers.slice(0, count);
      selectWorkers(workersToSelect.map((w) => w.id));
    }
  },

  handleUnselectAll: () => {
    get().clearWorkerSelection();
  },

  handleConfirm: async () => {
    const { selectedCrewMember, selectedWorkers, selectedTeam } = get();
    if (!selectedCrewMember || !selectedTeam || selectedWorkers.length === 0) {
      return;
    }

    let assignFromId: string;
    if (isWorker(selectedCrewMember)) {
      assignFromId = selectedCrewMember.idWorker || "";
    } else {
      assignFromId = selectedCrewMember.stationId || "";
    }

    const assignPayload: AssignWorkersPayload = {
      assignmentType: "DEFAULT",
      site: selectedCrewMember.station || "",
      assignFromId,
      assignToId: selectedTeam,
      operatorsIds: selectedWorkers,
    };

    await get().assignWorkers(assignPayload);
    get().clearWorkerSelection();
  },

  // UI state setters
  setEditingRowIndex: (index: number | null) => set({ editingRowIndex: index }),
  setEditedWorker: (worker: Worker | null) => set({ editedWorker: worker }),
  setWorkerIdsToDelete: (ids: string[]) => set({ workerIdsToDelete: ids }),
  setSelectedRow: (worker: Worker | WorkstationAssignment | null) => {
    // Only update if the selection actually changed
    const currentSelection = get().selectedRow;
    if (currentSelection?.id !== worker?.id) {
      set({ selectedRow: worker });
    }
  },
  setSelectedWorkers: (workerIds: string[]) =>
    set((state) => ({
      ...state,
      selectedWorkers: workerIds,
    })),

  // Operation skills actions
  fetchOperationSkills: async () => {
    const {
      selectedCustomer,
      selectedProject,
      selectedFamily,
      selectedValueStream,
      selectedMeDefinition,
      selectedMeDefinitionEffectiveDate,
    } = get();

    // Only fetch if we have all required parameters
    if (
      !selectedCustomer ||
      !selectedProject ||
      !selectedFamily ||
      !selectedValueStream ||
      !selectedMeDefinition ||
      !selectedMeDefinitionEffectiveDate
    ) {
      return;
    }

    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.get<Skills[]>(
        "/workstation/workstation/value-stream-skills",
        {
          params: {
            customer_id: selectedCustomer,
            project_id: selectedProject,
            family_id: selectedFamily,
            value_stream_id: selectedValueStream,
            me_definition_version: selectedMeDefinition,
            effective_date: selectedMeDefinitionEffectiveDate,
            connected_user: currentUserId,
          },
        },
      );

      set({
        operationSkills: response.data || [],
        isLoading: false,
      });
    } catch (error) {
      console.error("Failed to fetch operation skills:", error);
      set({ isLoading: false });
      showToastIfMessage(error, "Error", "destructive");
    }
  },

  setSelectedSkills: (skills: string[]) => {
    set({ selectedSkills: skills });
    // Refetch unassigned workers with the new skills
    const { pagination, fetchUnassignedWorkers } = get();
    // Ensure we use a minimum page size of 5 if pagination.pageSize is 0
    const pageSize = pagination.pageSize || 5;
    fetchUnassignedWorkers(pagination.currentPage, pageSize);
  },

  workstationAssignments: [],
  workstationPagination: {
    currentPage: 1,
    pageSize: 5,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },

  fetchWorkstationAssignments: async (
    pageIndex = 0,
    pageSize = 5,
    searchQuery?: string,
  ) => {
    const {
      selectedCustomer,
      selectedProject,
      selectedFamily,
      selectedValueStream,
      selectedArea,
      selectedTeam,
      selectedMeDefinition,
      selectedMeDefinitionEffectiveDate,
      meDefinitionTeamLeaderLegacySite,
      workstationSorting,
      workstationPagination,
    } = get();

    if (
      !selectedCustomer ||
      !selectedProject ||
      !selectedFamily ||
      !selectedValueStream ||
      !selectedArea ||
      !selectedTeam ||
      !selectedMeDefinition ||
      !selectedMeDefinitionEffectiveDate ||
      !meDefinitionTeamLeaderLegacySite
    ) {
      return;
    }

    set({ isWorkstationAssignmentsLoading: true });
    try {
      const validPageSize = pageSize || workstationPagination.pageSize || 5;
      const pageNumber = pageIndex + 1; // Convert 0-based to 1-based for API
      const currentUserId = getCurrentUserId();

      const params: Record<string, string> = {
        // Required parameters
        connected_user: currentUserId,
        teamleader_id: meDefinitionTeamLeaderLegacySite,
        team_id: selectedTeam,
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
        value_stream_id: selectedValueStream,
        area_id: selectedArea,
        me_definition_version: selectedMeDefinition,
        effective_date: selectedMeDefinitionEffectiveDate,

        // Optional pagination and sorting parameters
        pageNumber: String(pageNumber),
        pageSize: String(validPageSize),
        sortBy: workstationSorting[0]?.id || "station",
        sortOrder: workstationSorting[0]?.desc ? "desc" : "asc",
      };

      if (searchQuery) {
        params.search = searchQuery;
      }

      const response = await api.get<WorkstationAssignmentsResponse>(
        "/workstation/workstation/assignments",
        { params },
      );

      if (response.data && response.data.items) {
        set({
          workstationAssignments: response.data.items,
          workstationPagination: {
            currentPage: response.data.meta.currentPage, // Keep 1-based indexing for table
            pageSize: response.data.meta.pageSize,
            totalCount: response.data.meta.totalCount,
            totalPages: response.data.meta.totalPages,
            hasNextPage: response.data.meta.hasNextPage,
            hasPreviousPage: response.data.meta.hasPreviousPage,
          },
          workstationMeta: {
            totalUnassignedME: response.data.meta.totalUnassignedME,
            totalUnassignedMS: response.data.meta.totalUnassignedMS,
            totalAssignedME: response.data.meta.totalAssignedME,
            totalAssignedMS: response.data.meta.totalAssignedMS,
          },
          isWorkstationAssignmentsLoading: false,
        });
      } else {
        console.error("Invalid response format:", response.data);
        set({ isWorkstationAssignmentsLoading: false });
        toast({
          title: "Error",
          description: "Invalid response format from server",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to fetch workstation assignments:", error);
      set({ isWorkstationAssignmentsLoading: false });
      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  // Workstation options
  workstationOptions: [],
  selectedWorkstation: null,

  // Workstation actions
  fetchWorkstationOptions: async (teamleader_id: string) => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.get<WorkstationOption[]>(
        "/workstation/workstation/ms-stations",
        {
          params: {
            teamleader_id,
            connected_user: currentUserId,
          },
        },
      );
      set({
        workstationOptions: Array.isArray(response.data) ? response.data : [],
        isLoading: false,
      });
    } catch (error) {
      console.error("Failed to fetch workstation options:", error);
      set({ isLoading: false });
      showToastIfMessage(error, "Error", "destructive");
      throw error;
    }
  },

  setSelectedWorkstation: async (workstationId: string | null) => {
    const {
      selectedCustomer,
      selectedProject,
      selectedFamily,
      selectedValueStream,
      selectedArea,
      selectedTeam,
      selectedMeDefinition,
      selectedMeDefinitionEffectiveDate,
      meDefinitionTeamLeaderLegacySite,
    } = get();

    set({ selectedWorkstation: workstationId });

    if (!workstationId) return;

    try {
      // Check if it's an MS station (starts with 'MS')
      if (workstationId.startsWith("MS")) {
        console.log("Debug - Fetching operators for MS station");
        await get().fetchOperatorsForMsStationCreation(
          workstationId,
          meDefinitionTeamLeaderLegacySite,
          selectedTeam,
          selectedCustomer,
          selectedProject,
          selectedFamily,
          selectedValueStream,
          selectedArea,
          selectedMeDefinition,
          selectedMeDefinitionEffectiveDate,
        );
      }
      // Check if it's an ME station (starts with 'ME')
      else if (workstationId.startsWith("ME")) {
        console.log("Debug - Fetching operators for ME station");
        await get().fetchOperatorsForReassignment(
          workstationId,
          meDefinitionTeamLeaderLegacySite,
          selectedTeam,
        );
      } else {
        console.error("Invalid workstation ID format:", workstationId);
        // Do not show a toast, only log error
      }
    } catch (error) {
      console.error("Error fetching operators:", error);
      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  // Operator reassignment
  operatorsForReassignment: [],
  selectedOperator: null,

  // Operator reassignment actions
  fetchOperatorsForReassignment: async (
    workstationId: string,
    teamleader_id: string,
    team_name: string,
  ) => {
    // Prevent API call with empty workstation ID
    if (!workstationId || workstationId.trim() === "") {
      console.log(
        "Debug - Skipping fetchOperatorsForReassignment: empty workstationId",
      );
      set({ operatorsForReassignment: [], isLoading: false });
      return;
    }

    try {
      set({ isLoading: true });
      const currentUserId = getCurrentUserId();
      const response = await api.get<OperatorForReassignment[]>(
        `${process.env.NEXT_PUBLIC_API_URL}/workstation/workstation/operators-for-reassignment`,
        {
          params: {
            stationId: workstationId,
            teamleader_id,
            team_name,
            connected_user: currentUserId,
          },
        },
      );

      set({
        operatorsForReassignment: response.data,
        isLoading: false,
      });
    } catch (error) {
      console.error("Error fetching operators for reassignment:", error);
      set({ isLoading: false });

      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  // New function specifically for MS station creation
  fetchOperatorsForMsStationCreation: async (
    msStationId: string,
    teamleader_id: string,
    team_name: string,
    customer_id: string,
    project_id: string,
    family_id: string,
    value_stream_id: string,
    area_id: string,
    me_definition_version: string,
    effective_date: string,
  ) => {
    // Prevent API call with empty station ID
    if (!msStationId || msStationId.trim() === "") {
      console.log(
        "Debug - Skipping fetchOperatorsForMsStationCreation: empty msStationId",
      );
      set({ operatorsForReassignment: [], isLoading: false });
      return;
    }

    try {
      console.log(
        "Debug - Starting fetchOperatorsForMsStationCreation with params:",
        {
          msStationId,
          teamleader_id,
          team_name,
          customer_id,
          project_id,
          family_id,
          value_stream_id,
          area_id,
          me_definition_version,
          effective_date,
        },
      );

      set({ isLoading: true });
      const currentUserId = getCurrentUserId();
      const response = await api.get<OperatorForReassignment[]>(
        "/workstation/workstation/operator-reassign-ms-stations-creation",
        {
          params: {
            msStationId,
            teamleader_id,
            team_name,
            customer_id,
            project_id,
            family_id,
            value_stream_id,
            area_id,
            me_definition_version,
            effective_date,
            connected_user: currentUserId,
          },
        },
      );

      console.log(
        "Debug - fetchOperatorsForMsStationCreation response:",
        response.data,
      );

      set({
        operatorsForReassignment: response.data,
        isLoading: false,
      });
    } catch (error) {
      console.error("Error fetching operators for MS station creation:", error);
      set({ isLoading: false });
      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
    }
  },

  setSelectedOperator: (operator: OperatorForReassignment | null) => {
    set({ selectedOperator: operator });
  },

  clearOperatorsForReassignment: () => {
    set({
      operatorsForReassignment: [],
      selectedOperator: null,
    });
  },

  // Assignment actions
  assignToWorkstation: async (request: AssignToWorkstationRequest) => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.post<AssignToWorkstationResponse>(
        "/employee-assignment/assignments/assign-reassign-to-workstation",
        request,
        {
          params: {
            connected_user: currentUserId,
          },
        },
      );
      set({ isLoading: false });
      toast({
        title: "Success",
        description: response.data.message,
        variant: "success",
      });
      return response.data;
    } catch (error) {
      console.error("Failed to assign to workstation:", error);
      set({ isLoading: false });
      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
      throw error;
    }
  },
  createWorkstation: async (request: CreateWorkstationRequest) => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.post<CreateWorkstationResponse>(
        "/workstation/workstation-management/create-workstation",
        request,
        {
          params: {
            connected_user: currentUserId,
          },
        },
      );
      set({ isLoading: false });
      toast({
        title: "Success",
        description: response.data.message,
        variant: "success",
      });
      return response.data;
    } catch (error) {
      console.error("Failed to create workstation:", error);
      set({ isLoading: false });
      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");
      throw error;
    }
  },
  deleteWorkstation: async (request: DeleteWorkstationRequest) => {
    set({ isLoading: true });
    try {
      const currentUserId = getCurrentUserId();
      const response = await api.delete<DeleteWorkstationResponse>(
        "/workstation/workstation-management/delete-workstation",
        {
          data: request,
          params: {
            connected_user: currentUserId,
          },
        },
      );
      set({ isLoading: false });

      // Get current pagination state
      const { workstationPagination } = get();
      const currentPage = workstationPagination.currentPage;
      const totalCount = workstationPagination.totalCount;
      const pageSize = workstationPagination.pageSize;

      // Calculate new total count after deletion
      const newTotalCount = totalCount - 1;
      const newTotalPages = Math.ceil(newTotalCount / pageSize);

      // Calculate the correct page to show
      let newPage = currentPage;

      // If we're on the last page and it's not the first page
      if (currentPage === newTotalPages + 1 && currentPage > 1) {
        newPage = currentPage - 1; // Move to previous page
      }

      // Ensure we don't go below page 1
      newPage = Math.max(1, newPage);

      // Refetch both tables with updated pagination
      const { pagination } = get();
      await Promise.all([
        get().fetchWorkstationAssignments(newPage - 1, pageSize),
        get().fetchUnassignedWorkers(
          pagination.currentPage - 1,
          pagination.pageSize,
        ),
      ]);

      toast({
        title: "Success",
        description: response.data.message,
        variant: "success",
      });
      return response.data;
    } catch (error: unknown) {
      console.error("Failed to delete workstation:", error);
      set({ isLoading: false });

      // Use the proper error handling function to show backend messages
      const apiError = error as { response?: { data?: unknown } };
      showToastIfMessage(apiError.response?.data, "Error", "destructive");

      // Re-throw the error for the caller to handle
      throw error;
    }
  },

  meDefinitionTeamLeaderLegacySite: "",

  workstationMeta: {
    totalUnassignedME: 0,
    totalUnassignedMS: 0,
    totalAssignedME: 0,
    totalAssignedMS: 0,
  },
}));

export default useCrewManagementStore;
