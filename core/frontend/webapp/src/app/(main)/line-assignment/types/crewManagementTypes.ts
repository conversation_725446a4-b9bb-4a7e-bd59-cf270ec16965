export enum Structure {
  Customer = "CUSTOMER",
  Project = "PROJECT",
  Family = "FAMILY",
  ValueStream = "VALUE_STREAM",
  Area = "AREA",
  Teams = "TEAMS",
}

// New type for the API response values
export type ApiOptionValue = {
  id: string;
  value: string;
};

// New type for the API response metadata
export type ApiResponseMetadata = {
  level: string;
  filters: {
    customer_id?: string;
    project_id?: string;
    family_id?: string;
    value_stream_id?: string;
    area_id?: string;
  };
  timestamp: string;
};

// New type for the complete API response
export type ZoningOptionsResponse = {
  values: ApiOptionValue[];
  total: number;
  metadata: ApiResponseMetadata;
};

export type FilterOption = {
  id: string;
  label: string;
  value: string;
  role: string;
  isCurrent?: boolean;
  effectiveDate?: string;
};

export type MeDefinitionVersion = {
  me_definition_version: string;
  effective_date: string;
  isCurrent: boolean;
  teamLeader_id: string; // Changed from teamLeader_legacy_site
};

export type MeDefinitionResponse = {
  versions: MeDefinitionVersion[];
  teamLeader_id: string; // Changed from teamLeader_legacy_site
  total: number;
};

export type Worker = {
  id: string; // Changed from legacySiteId for backend communication
  legacyId: number; // Changed to number to match new API response
  fullname: string;
  name: string;
  surname: string;
  department: string;
  department_id: string;
  role: string;
  skills: Skills[]; // Changed to array to match new API response
  // Legacy fields for backward compatibility
  station?: string;
  stationId?: string;
  idWorker?: string;
  process?: string;
  function?: string;
  workstationId?: string;
  stationRole?: string;
  operatorId?: string;
  operatorFirstName?: string;
  operatorLastName?: string;
  workstationSkills?: string[];
  operatorLegacyId?: string;
  legacySite?: string; // Keep for display purposes
};

export type WorkerRole = "ME STRUCTURE" | "MANUFACTURING STRUCTURE";

export type StationsByRole = {
  [key in WorkerRole]: string[];
};

export type WorkerUpdate = {
  id: string;
  role: string;
  station: string;
};

export type AssignWorkersPayload = {
  assignmentType: string;
  site: string;
  assignFromId: string;
  assignToId: string;
  operatorsIds: string[];
};

export type Skills = {
  skill_name: string;
  skill_id: string;
};

export type WorkstationAssignment = {
  id: string;
  stationId: string;
  station: string;
  stationRole?: string;
  workstationSkills: Skills[];
  workstationIsMeStructure: boolean;
  operatorId: string | null;
  operatorLegacyId: number | null;
  operatorFirstName: string | null;
  operatorLastName: string | null;
};

export type WorkstationAssignmentsResponse = {
  items: WorkstationAssignment[];
  meta: {
    totalCount: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    totalUnassignedME: number;
    totalUnassignedMS: number;
    totalAssignedME: number;
    totalAssignedMS: number;
  };
};

export type WorkstationOption = {
  id: string;
  label: string;
};

export type OperatorForReassignment = {
  operatorId: string; // Changed to match new API response
  operatorLegacyId: number;
  operatorFullName: string;
};

export type AssignmentTeam = {
  teamId: string;
  teamLeaderId: string; // Changed from teamLeaderLegacySiteId
};

export type AssignmentEntity = {
  id: string; // Changed from legacySiteId
};

export type AssignToWorkstationRequest = {
  stationId: string;
  team: AssignmentTeam;
  assigned: AssignmentEntity;
  unassigned?: AssignmentEntity;
};

export type AssignToWorkstationResponse = {
  statusCode: number;
  message: string;
};
