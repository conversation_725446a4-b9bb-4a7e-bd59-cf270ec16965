import React from "react";
import { render, screen } from "@testing-library/react";

jest.mock("../store/crewManagementStore", () => () => ({
  fetchUnassignedWorkers: jest.fn(),
  fetchWorkstationAssignments: jest.fn(),
  selectedCrewMember: null,
  unassignedWorkers: [],
  setSelectedCrewMember: jest.fn(),
  customerOptions: [{ id: "1", value: "Customer 1" }],
  projectOptions: [{ id: "1", value: "Project 1" }],
  familyOptions: [{ id: "1", value: "Family 1" }],
  valueStreamOptions: [{ id: "1", value: "ValueStream 1" }],
  areaOptions: [{ id: "1", value: "Area 1" }],
  teamOptions: [{ id: "1", value: "Team 1" }],
  meDefinitionOptions: [{ label: "MeDef 1", value: "1" }],
  workstationPagination: { totalCount: 1, currentPage: 1, pageSize: 5 },
}));
jest.mock("../components/CrewDashboard", () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const MockCrewDashboard = (props: {
    onSelectCrewMember: (worker: unknown) => void;
  }) => <div data-testid="crew-dashboard">CrewDashboard</div>;
  MockCrewDashboard.displayName = "MockCrewDashboard";
  return { CrewDashboard: MockCrewDashboard };
});
jest.mock("../components/AssignmentPanel", () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const MockAssignmentPanel = (props: { onClose: () => void }) => (
    <div data-testid="assignment-panel">AssignmentPanel</div>
  );
  MockAssignmentPanel.displayName = "MockAssignmentPanel";
  return MockAssignmentPanel;
});
jest.mock("@/components/common/CustomButtons", () => ({
  ReusableButton: (
    props: React.ButtonHTMLAttributes<HTMLButtonElement> & { label: string },
  ) => <button {...props}>{props.label}</button>,
}));
jest.mock("@/components/ui/separator", () => ({
  Separator: () => <div data-testid="separator" />,
}));
jest.mock("@/hooks/useIsSmallScreen", () => () => false);
jest.mock("next-intl", () => ({ useTranslations: () => (key: string) => key }));

import Home from "../page";

describe("CrewManagement Page", () => {
  it("renders dashboard and assignment panel", () => {
    render(<Home />);
    expect(screen.getByTestId("crew-dashboard")).toBeInTheDocument();
    expect(screen.getByTestId("assignment-panel")).toBeInTheDocument();
  });
});
