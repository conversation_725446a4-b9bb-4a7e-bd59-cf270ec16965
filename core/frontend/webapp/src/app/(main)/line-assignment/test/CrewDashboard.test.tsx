import React from "react";
import { render, screen } from "@testing-library/react";

jest.mock("../components/CrewDashboard", () => ({
  CrewDashboard: () => (
    <div data-testid="crew-dashboard">Mocked CrewDashboard</div>
  ),
}));

import { CrewDashboard } from "../components/CrewDashboard";

describe("CrewDashboard", () => {
  it("renders without crashing", () => {
    render(<CrewDashboard onSelectCrewMember={() => {}} />);
    expect(screen.getByTestId("crew-dashboard")).toBeInTheDocument();
  });
});
