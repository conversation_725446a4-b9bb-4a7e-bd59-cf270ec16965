import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";

jest.mock("../store/crewManagementStore", () => () => ({
  selectedTeam: "Team1",
  isLoading: false,
  isUnassignedWorkersLoading: false,
  selectedCrewMember: { station: "Station1", stationId: "S1" },
  selectedWorkers: ["W1"],
  setSelectedWorkers: jest.fn(),
  clearWorkerSelection: jest.fn(),
  fetchUnassignedWorkers: jest.fn(),
  pagination: { currentPage: 1, pageSize: 5, totalCount: 1 },
  operationSkills: [
    { skill_id: "skill1", skill_name: "Skill1" },
    { skill_id: "skill2", skill_name: "Skill2" },
  ],
  selectedSkills: [],
  setSelectedSkills: jest.fn(),
  fetchOperationSkills: jest.fn(),
  assignToWorkstation: jest.fn().mockResolvedValue({}),
  sorting: [],
  setSorting: jest.fn(),
  unassignedWorkers: [
    {
      legacyId: "W1",
      name: "<PERSON>",
      surname: "<PERSON><PERSON>",
      department: "Dept",
      skills: "Skill1",
      role: "<PERSON>tor",
    },
  ],
  meDefinitionTeamLeaderLegacySite: "TL1",
  fetchWorkstationAssignments: jest.fn(),
  workstationPagination: { currentPage: 1, pageSize: 5 },
  searchQuery: "",
}));
jest.mock("@/components/common/tables/DataTable", () => ({
  DataTable: () => <div data-testid="datatable">DataTable</div>,
}));
jest.mock("@/components/common/CustomButtons", () => {
  const ReusableButton = (
    props: React.ButtonHTMLAttributes<HTMLButtonElement> & {
      label: string;
      isLoading?: boolean;
    },
  ) => {
    // Remove isLoading from props before passing to button
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { isLoading, ...rest } = props;
    return <button {...rest}>{props.label}</button>;
  };
  ReusableButton.displayName = "ReusableButton";
  const IconButton = (props: React.ButtonHTMLAttributes<HTMLButtonElement>) => (
    <button {...props} />
  );
  IconButton.displayName = "IconButton";
  return { ReusableButton, IconButton };
});
jest.mock("@/components/common/CustomDialog", () => ({
  ReusableDialog: (props: {
    isOpen: boolean;
    onClose: () => void;
    id: string;
    children: React.ReactNode;
  }) =>
    props.isOpen ? <div data-testid="dialog">{props.children}</div> : null,
}));
jest.mock("@/components/common/CustomMultiSelect", () => {
  const MockCustomMultiSelect = () => (
    <div data-testid="multiselect">MultiSelect</div>
  );
  MockCustomMultiSelect.displayName = "MockCustomMultiSelect";
  return MockCustomMultiSelect;
});
jest.mock("@/hooks/useIsSmallScreen", () => () => false);
jest.mock("next-intl", () => ({ useTranslations: () => (key: string) => key }));
jest.mock("@/components/ui/checkbox", () => ({
  Checkbox: (props: React.InputHTMLAttributes<HTMLInputElement>) => (
    <input type="checkbox" {...props} />
  ),
}));

import AssignmentPanel from "../components/AssignmentPanel";

describe("AssignmentPanel", () => {
  it("renders and opens dialog on assign", () => {
    render(<AssignmentPanel onClose={jest.fn()} />);
    // Match heading containing 'unassignedWorkers' and the count
    expect(
      screen.getByText(
        (content, element) =>
          element?.tagName.toLowerCase() === "h3" &&
          /unassignedWorkers\s*\(\s*1\s*\)/.test(content),
      ),
    ).toBeInTheDocument();
    const assignBtn = screen.getByRole("button", { name: /assignTo/i });
    fireEvent.click(assignBtn);
    expect(screen.getByTestId("dialog")).toBeInTheDocument();
  });
});
