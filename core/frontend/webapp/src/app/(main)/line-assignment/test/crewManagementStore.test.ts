// Mocks
jest.mock("@/store/mockAuthStore", () => ({
  useMockAuthStore: { getState: () => ({ currentUser: { id: "user-1" } }) },
}));
jest.mock("@/lib/axios", () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
  },
}));
jest.mock("@/hooks/use-toast", () => ({ toast: jest.fn() }));

import { renderHook, act } from "@testing-library/react";
import useCrewManagementStore from "../store/crewManagementStore";
import axios from "@/lib/axios";
import { toast } from "@/hooks/use-toast";
import type { Worker, WorkerUpdate } from "../types/crewManagementTypes";

const mockWorker: Worker = {
  id: "1",
  idWorker: "W1",
  name: "<PERSON>",
  surname: "<PERSON><PERSON>",
  role: "Operator",
  station: "Station A",
  stationId: "S1",
  skills: "Skill1,Skill2",
  legacyId: "L1",
  fullname: "<PERSON>",
};

const mockWorkerUpdate: WorkerUpdate = {
  id: "1",
  role: "Supervisor",
  station: "Station B",
};

describe("useCrewManagementStore CRUD", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (toast as jest.Mock).mockClear();
  });

  it("should fetch table data successfully", async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce({ data: [mockWorker] });
    const { result } = renderHook(() => useCrewManagementStore());
    await act(async () => {
      await result.current.fetchTableData(1, 5);
    });
    expect(axios.get).toHaveBeenCalled();
    expect(result.current.workers.length).toBe(1);
  });

  it("should handle error in fetchTableData", async () => {
    (axios.get as jest.Mock).mockRejectedValueOnce(new Error("Network error"));
    const { result } = renderHook(() => useCrewManagementStore());
    await act(async () => {
      await result.current.fetchTableData(1, 5);
    });
    expect(result.current.isLoading).toBe(false);
  });

  it("should update worker successfully", async () => {
    (axios.post as jest.Mock).mockResolvedValueOnce({
      data: { message: "Worker updated successfully" },
    });
    const { result } = renderHook(() => useCrewManagementStore());
    // Set initial workers
    act(() => {
      result.current.workers = [mockWorker];
    });
    await act(async () => {
      await result.current.updateWorker(mockWorkerUpdate);
    });
    expect(axios.post).toHaveBeenCalled();
    expect((toast as jest.Mock).mock.calls.length).toBeGreaterThan(0);
    expect(result.current.workers[0].role).toBe("Supervisor");
    expect(result.current.workers[0].station).toBe("Station B");
  });

  it("should handle error in updateWorker", async () => {
    (axios.post as jest.Mock).mockRejectedValueOnce({
      response: { data: { message: "Update error" } },
    });
    const { result } = renderHook(() => useCrewManagementStore());
    act(() => {
      result.current.workers = [mockWorker];
    });
    await act(async () => {
      await result.current.updateWorker(mockWorkerUpdate);
    });
    expect((toast as jest.Mock).mock.calls.length).toBeGreaterThan(0);
    expect(result.current.isLoading).toBe(false);
  });

  it("should add new worker successfully", async () => {
    (axios.post as jest.Mock).mockResolvedValueOnce({
      data: { message: "Worker added successfully" },
    });
    const { result } = renderHook(() => useCrewManagementStore());
    act(() => {
      result.current.workers = [];
    });
    await act(async () => {
      await result.current.addNewWorker(mockWorker);
    });
    expect(axios.post).toHaveBeenCalled();
    expect((toast as jest.Mock).mock.calls.length).toBeGreaterThan(0);
    expect(result.current.workers.length).toBe(1);
  });

  it("should handle error in addNewWorker", async () => {
    const errorWithMessage = {
      response: {
        data: {
          message: "Add error",
          details: {
            error: {
              message: "Add error",
            },
          },
        },
      },
      message: "Add error",
    };
    (axios.post as jest.Mock).mockRejectedValueOnce(errorWithMessage);
    const { result } = renderHook(() => useCrewManagementStore());
    act(() => {
      result.current.workers = [];
    });
    await act(async () => {
      try {
        await result.current.addNewWorker(mockWorker);
      } catch {}
    });
    expect((toast as jest.Mock).mock.calls.length).toBeGreaterThan(0);
    expect(result.current.isLoading).toBe(false);
  });
});
