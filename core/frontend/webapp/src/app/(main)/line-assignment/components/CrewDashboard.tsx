"use client";

import React, { useEffect, useState } from "react";
import { Trash2, Pencil, Check, X, Clock4, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import useCrewManagementStore from "../store/crewManagementStore";
import { DataTable } from "@/components/common/tables/DataTable";
import CustomSelect from "@/components/common/CustomSelect";
import {
  Worker,
  WorkstationAssignment,
  AssignToWorkstationRequest,
  OperatorForReassignment,
  Skills,
} from "../types/crewManagementTypes";
// import { PermissionGate } from "@/components/auth/PermissionGate";
import CustomIcon from "@/components/common/CustomIcons";
import ValidationDialog from "@/components/common/ValidationDialog";
import { TruncatedText } from "@/components/common/TruncatedText";
import { toast } from "@/hooks/use-toast";
import DeleteDialog from "@/components/common/DeleteDialog";
import { useTranslations } from "next-intl";

const createIcon = (
  name:
    | "fileText"
    | "user"
    | "error"
    | "bell"
    | "circleUser"
    | "reassign"
    | "project"
    | "family"
    | "lineChart"
    | "areaChart"
    | "team"
    | "customer"
    | "meDefinition",
  fill: string,
) => (
  <span className="flex items-center">
    <CustomIcon
      name={name}
      className="mr-2"
      style={{
        width: "17px",
        height: "18px",
        fill,
      }}
    />
  </span>
);

const ProjectIcons = createIcon("project", "#000000");
const FamilyIcons = createIcon("family", "#9E7A00");
const LineChartIcons = createIcon("lineChart", "#4CAF50");
const AreaIcons = createIcon("areaChart", "#c59800");
const TeamIcons = createIcon("team", "#8804A1");
const CustomerIcons = createIcon("customer", "#ffb72f");

const createOperatorSelectionColumn = (
  t: (key: string) => string,
  editingRowIndex: number | null,
  newWorker: Worker | null,
  operatorsForReassignment: OperatorForReassignment[],
  selectedOperator: OperatorForReassignment | null,
  selectedWorkstationForReassign: WorkstationAssignment | null,
  isLoading: boolean,
  setNewWorker: (worker: Worker | null) => void,
  setSelectedWorkstationForReassign: (
    assignment: WorkstationAssignment | null,
  ) => void,
  setSelectedOperator: (operator: OperatorForReassignment | null) => void,
) => ({
  accessorKey: "operatorLegacyId",
  enableSorting: true,
  header: () => <span>{t("operatorId")}</span>,
  cell: ({
    row,
  }: {
    row: { original: Worker | WorkstationAssignment; index: number };
  }) => {
    if (editingRowIndex === row.index) {
      if (newWorker && editingRowIndex === row.index) {
        return (
          <div style={{ minWidth: 200 }}>
            <CustomSelect
              key={
                newWorker.operatorLegacyId
                  ? String(newWorker.operatorLegacyId)
                  : "empty"
              }
              options={operatorsForReassignment.map((op) => ({
                label: `${op.operatorLegacyId} - ${op.operatorFullName}`,
                value: op.operatorLegacyId.toString(),
              }))}
              onValueChange={(value) => {
                const selectedOperator = operatorsForReassignment.find(
                  (op) => op.operatorLegacyId.toString() === value,
                );
                if (selectedOperator) {
                  setNewWorker({
                    ...newWorker,
                    operatorId: selectedOperator.operatorId,
                    operatorLegacyId:
                      selectedOperator.operatorLegacyId.toString(),
                    operatorFirstName:
                      selectedOperator.operatorFullName.split(" ")[0],
                    operatorLastName: selectedOperator.operatorFullName
                      .split(" ")
                      .slice(1)
                      .join(" "),
                    stationRole: "MS",
                  });
                }
              }}
              defaultValue={
                newWorker.operatorLegacyId
                  ? String(newWorker.operatorLegacyId)
                  : ""
              }
              placeholder={t("selectOperator")}
              disabled={
                isLoading || Boolean(newWorker && !newWorker.workstationId)
              }
            />
          </div>
        );
      } else {
        const currentOperator = operatorsForReassignment.find(
          (op) =>
            op.operatorLegacyId.toString() ===
            String(row.original.operatorLegacyId),
        );

        const options = [...operatorsForReassignment];
        if (row.original.operatorLegacyId && !currentOperator) {
          options.push({
            operatorId: row.original.operatorId || "",
            operatorLegacyId: Number(row.original.operatorLegacyId),
            operatorFullName:
              `${row.original.operatorFirstName || ""} ${row.original.operatorLastName || ""}`.trim(),
          });
        }

        let currentValue = "";
        if (
          selectedOperator &&
          selectedWorkstationForReassign?.stationId === row.original.stationId
        ) {
          currentValue = String(selectedOperator.operatorLegacyId);
        } else if (row.original.operatorLegacyId) {
          currentValue = String(row.original.operatorLegacyId);
        }

        return (
          <div style={{ minWidth: 200 }}>
            <CustomSelect
              key={`operator-select-${row.original.stationId}-${currentValue}`}
              options={options.map((op) => ({
                label: `${op.operatorLegacyId} - ${op.operatorFullName}`,
                value: op.operatorLegacyId.toString(),
              }))}
              onValueChange={(value) => {
                const selectedOp = options.find(
                  (op) => op.operatorLegacyId.toString() === value,
                );
                if (selectedOp) {
                  setSelectedWorkstationForReassign(
                    row.original as WorkstationAssignment,
                  );
                  setSelectedOperator(selectedOp);
                }
              }}
              defaultValue={currentValue}
              placeholder={t("selectOperator")}
              disabled={isLoading}
            />
          </div>
        );
      }
    }
    return <span>{row.original.operatorLegacyId || ""}</span>;
  },
});

export function CrewDashboard({
  onSelectCrewMember,
}: {
  onSelectCrewMember: (worker: Worker | WorkstationAssignment | null) => void;
}) {
  const t = useTranslations("crew_dashboard");
  const {
    // fetchData,
    isLoading,
    isWorkstationAssignmentsLoading,
    selectedTeam,
    selectedCustomer,
    selectedProject,
    selectedFamily,
    selectedValueStream,
    selectedArea,
    selectedMeDefinition,
    selectedMeDefinitionEffectiveDate,
    meDefinitionVersions,
    workstationAssignments,
    fetchWorkstationAssignments,
    fetchUnassignedWorkers,
    workstationPagination,
    pagination,
    assignToWorkstation,
    selectedOperator,
    createWorkstation,
    deleteWorkstation,
    fetchFilteredOptions,
    customerOptions,
    projectOptions,
    familyOptions,
    valueStreamOptions,
    areaOptions,
    teamOptions,
    meDefinitionOptions,
    fetchMeDefinitionVersions,
    setSelectedMeDefinition,
    handleSelect,
    editingRowIndex,
    setEditingRowIndex,
    newWorker,
    setNewWorker,
    fetchWorkstationOptions,
    fetchOperatorsForReassignment,
    fetchOperatorsForMsStationCreation,
    setSelectedOperator,
    selectedRow,
    startEditing,
    initiateNewWorker,
    workstationSorting,
    setWorkstationSorting,
    searchQuery,
    setSearchQuery,
    workstationOptions,
    operatorsForReassignment,
  } = useCrewManagementStore();

  const {
    totalUnassignedME,
    totalUnassignedMS,
    totalAssignedME,
    totalAssignedMS,
  } = useCrewManagementStore((state) => state.workstationMeta || {});

  const [isReassignDialogOpen, setIsReassignDialogOpen] = useState(false);
  const [selectedWorkstationForReassign, setSelectedWorkstationForReassign] =
    useState<WorkstationAssignment | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedWorkstationForDelete, setSelectedWorkstationForDelete] =
    useState<WorkstationAssignment | null>(null);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  useEffect(() => {
    // Fetch initial customer options on component mount
    fetchFilteredOptions({});
  }, [fetchFilteredOptions]);

  useEffect(() => {
    // Fetch project options when a customer is selected
    if (selectedCustomer && selectedCustomer.trim() !== "") {
      fetchFilteredOptions({ customer_id: selectedCustomer });
    }
  }, [selectedCustomer, fetchFilteredOptions]);

  useEffect(() => {
    // Fetch family options when a project is selected
    if (
      selectedCustomer &&
      selectedCustomer.trim() !== "" &&
      selectedProject &&
      selectedProject.trim() !== ""
    ) {
      fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
      });
    }
  }, [selectedCustomer, selectedProject, fetchFilteredOptions]);

  useEffect(() => {
    // Fetch value stream options when a family is selected
    if (
      selectedCustomer &&
      selectedCustomer.trim() !== "" &&
      selectedProject &&
      selectedProject.trim() !== "" &&
      selectedFamily &&
      selectedFamily.trim() !== ""
    ) {
      fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
      });
    }
  }, [selectedCustomer, selectedProject, selectedFamily, fetchFilteredOptions]);

  useEffect(() => {
    // Fetch area options when a value stream is selected
    if (
      selectedCustomer &&
      selectedCustomer.trim() !== "" &&
      selectedProject &&
      selectedProject.trim() !== "" &&
      selectedFamily &&
      selectedFamily.trim() !== "" &&
      selectedValueStream &&
      selectedValueStream.trim() !== ""
    ) {
      fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
        value_stream_id: selectedValueStream,
      });
    }
  }, [
    selectedCustomer,
    selectedProject,
    selectedFamily,
    selectedValueStream,
    fetchFilteredOptions,
  ]);

  useEffect(() => {
    // Fetch team options when an area is selected
    if (
      selectedCustomer &&
      selectedCustomer.trim() !== "" &&
      selectedProject &&
      selectedProject.trim() !== "" &&
      selectedFamily &&
      selectedFamily.trim() !== "" &&
      selectedValueStream &&
      selectedValueStream.trim() !== "" &&
      selectedArea &&
      selectedArea.trim() !== ""
    ) {
      fetchFilteredOptions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
        value_stream_id: selectedValueStream,
        area_id: selectedArea,
      });
    }
  }, [
    selectedCustomer,
    selectedProject,
    selectedFamily,
    selectedValueStream,
    selectedArea,
    fetchFilteredOptions,
  ]);

  // Fetch ME Definition versions when team is selected
  useEffect(() => {
    if (
      selectedCustomer &&
      selectedCustomer.trim() !== "" &&
      selectedProject &&
      selectedProject.trim() !== "" &&
      selectedFamily &&
      selectedFamily.trim() !== "" &&
      selectedValueStream &&
      selectedValueStream.trim() !== "" &&
      selectedArea &&
      selectedArea.trim() !== "" &&
      selectedTeam &&
      selectedTeam.trim() !== ""
    ) {
      fetchMeDefinitionVersions({
        customer_id: selectedCustomer,
        project_id: selectedProject,
        family_id: selectedFamily,
        value_stream_id: selectedValueStream,
        area_id: selectedArea,
        team_id: selectedTeam,
      });
    }
  }, [
    selectedCustomer,
    selectedProject,
    selectedFamily,
    selectedValueStream,
    selectedArea,
    selectedTeam,
    fetchMeDefinitionVersions,
  ]);

  // Add effect to fetch workstation assignments when ME definition is selected
  useEffect(() => {
    if (
      selectedCustomer &&
      selectedProject &&
      selectedFamily &&
      selectedValueStream &&
      selectedArea &&
      selectedTeam &&
      selectedMeDefinition &&
      selectedMeDefinitionEffectiveDate
    ) {
      // Only fetch if we actually have a selected ME definition
      if (selectedMeDefinition.trim() !== "") {
        Promise.all([
          fetchWorkstationAssignments(0, 5),
          fetchUnassignedWorkers(0, pagination.pageSize),
        ]);
      }
    } else {
      // Clear the table when any required field is missing
      useCrewManagementStore.setState({
        workstationAssignments: [],
        workstationPagination: {
          currentPage: 1,
          pageSize: 5,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
        workstationMeta: {
          totalUnassignedME: 0,
          totalUnassignedMS: 0,
          totalAssignedME: 0,
          totalAssignedMS: 0,
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    selectedCustomer,
    selectedProject,
    selectedFamily,
    selectedValueStream,
    selectedArea,
    selectedTeam,
    selectedMeDefinition,
    selectedMeDefinitionEffectiveDate,
    fetchWorkstationAssignments,
  ]);

  // Add effect to fetch workstation options when ME definition is selected
  useEffect(() => {
    if (selectedMeDefinition && selectedMeDefinition.trim() !== "") {
      const currentVersion = meDefinitionVersions.find(
        (version) => version.me_definition_version === selectedMeDefinition,
      );
      if (currentVersion?.teamLeader_id) {
        fetchWorkstationOptions(currentVersion.teamLeader_id);
      }
    }
  }, [selectedMeDefinition, meDefinitionVersions, fetchWorkstationOptions]);

  // Add effect to fetch operators when editing existing row
  useEffect(() => {
    if (
      editingRowIndex !== null &&
      !newWorker &&
      selectedTeam &&
      selectedTeam.trim() !== "" &&
      selectedMeDefinition &&
      selectedMeDefinition.trim() !== ""
    ) {
      const currentVersion = meDefinitionVersions.find(
        (version) => version.me_definition_version === selectedMeDefinition,
      );
      if (currentVersion?.teamLeader_id) {
        const row = workstationAssignments[editingRowIndex];
        if (row) {
          // Always use fetchOperatorsForReassignment when editing with pencil
          fetchOperatorsForReassignment(
            row.stationId,
            currentVersion.teamLeader_id,
            selectedTeam,
          );
        }
      }
    }
  }, [
    editingRowIndex,
    newWorker,
    selectedTeam,
    selectedMeDefinition,
    meDefinitionVersions,
    workstationAssignments,
    fetchOperatorsForReassignment,
  ]);

  // Add effect to fetch operators when station is selected for new MS line
  useEffect(() => {
    if (
      newWorker?.workstationId &&
      newWorker.workstationId.trim() !== "" &&
      selectedTeam &&
      selectedTeam.trim() !== "" &&
      selectedMeDefinition &&
      selectedMeDefinition.trim() !== ""
    ) {
      const currentVersion = meDefinitionVersions.find(
        (version) => version.me_definition_version === selectedMeDefinition,
      );
      if (currentVersion?.teamLeader_id) {
        fetchOperatorsForMsStationCreation(
          newWorker.workstationId || "",
          currentVersion.teamLeader_id,
          selectedTeam,
          selectedCustomer || "",
          selectedProject || "",
          selectedFamily || "",
          selectedValueStream || "",
          selectedArea || "",
          selectedMeDefinition,
          selectedMeDefinitionEffectiveDate,
        );
      }
    } else if (
      newWorker &&
      (!newWorker.workstationId || newWorker.workstationId.trim() === "")
    ) {
      // Clear operators list when workstationId is empty or invalid
      useCrewManagementStore.getState().clearOperatorsForReassignment();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    newWorker?.workstationId,
    selectedTeam,
    selectedMeDefinition,
    meDefinitionVersions,
    selectedCustomer,
    selectedProject,
    selectedFamily,
    selectedValueStream,
    selectedArea,
    selectedMeDefinitionEffectiveDate,
    fetchOperatorsForMsStationCreation,
  ]);

  const handleRowClickWithCallback = (
    worker: Worker | WorkstationAssignment | null,
  ) => {
    if (worker) {
      onSelectCrewMember(worker);
      useCrewManagementStore.getState().handleRowClick(worker);
    } else {
      onSelectCrewMember(null);
      useCrewManagementStore.getState().handleRowClick(null);
    }
  };

  const handleCheckClick = () => {
    if (newWorker?.workstationId) {
      setIsCreateDialogOpen(true);
    } else if (editingRowIndex !== null && !newWorker) {
      // If we're editing an existing row (pencil button case)
      const row = workstationAssignments[editingRowIndex];
      if (row && selectedOperator) {
        // Only show dialog if an operator is selected
        setSelectedWorkstationForReassign(row as WorkstationAssignment);
        setIsReassignDialogOpen(true);
      } else {
        // Show a toast or message that operator selection is required
        toast({
          title: "Warning",
          description: "Please select an operator before validating",
          variant: "destructive",
        });
      }
    } else if (selectedWorkstationForReassign && selectedOperator) {
      setIsReassignDialogOpen(true);
    }
  };

  const handleReassignConfirm = async () => {
    if (!selectedWorkstationForReassign || !selectedOperator || !selectedTeam)
      return;

    // Get the current ME definition version to get the correct teamLeader_id
    const currentVersion = meDefinitionVersions.find(
      (version) => version.me_definition_version === selectedMeDefinition,
    );

    if (!currentVersion?.teamLeader_id) {
      toast({
        title: "Error",
        description: "Could not find team leader id",
        variant: "destructive",
      });
      return;
    }

    const request: AssignToWorkstationRequest = {
      stationId: selectedWorkstationForReassign.stationId,
      team: {
        teamId: selectedTeam,
        teamLeaderId: currentVersion.teamLeader_id,
      },
      assigned: {
        id: selectedOperator.operatorId,
      },
      unassigned: selectedWorkstationForReassign.operatorId
        ? {
            id: selectedWorkstationForReassign.operatorId,
          }
        : undefined,
    };

    try {
      await assignToWorkstation(request);
      setIsReassignDialogOpen(false);
      setSelectedWorkstationForReassign(null);
      setSelectedOperator(null);
      setEditingRowIndex(null);
      // Clear operators list after successful reassignment
      useCrewManagementStore.getState().clearOperatorsForReassignment();
      // FIX: Use the correct 0-based page index for fetchWorkstationAssignments
      Promise.all([
        fetchWorkstationAssignments(
          workstationPagination.currentPage - 1,
          workstationPagination.pageSize,
          searchQuery === "" ? undefined : searchQuery,
        ),
        fetchUnassignedWorkers(pagination.currentPage - 1, pagination.pageSize),
      ]);
    } catch (error) {
      console.error("Failed to reassign operator:", error);
    }
  };

  const handleCreateWorkstation = async () => {
    if (
      !newWorker ||
      !selectedTeam ||
      !selectedMeDefinition ||
      !selectedMeDefinitionEffectiveDate
    )
      return;

    const currentVersion = meDefinitionVersions.find(
      (version) => version.me_definition_version === selectedMeDefinition,
    );

    if (!currentVersion) return;

    // First fetch operators for MS station creation
    try {
      // Additional safety check to ensure workstationId is not empty
      if (!newWorker.workstationId || newWorker.workstationId.trim() === "") {
        throw new Error("Workstation ID is required");
      }

      await fetchOperatorsForMsStationCreation(
        newWorker.workstationId,
        currentVersion.teamLeader_id,
        selectedTeam,
        selectedCustomer || "",
        selectedProject || "",
        selectedFamily || "",
        selectedValueStream || "",
        selectedArea || "",
        selectedMeDefinition,
        selectedMeDefinitionEffectiveDate,
      );

      const request = {
        msStationId: newWorker.workstationId || "",
        teamLeader_id: currentVersion.teamLeader_id,
        team_id: selectedTeam,
        customer_id: selectedCustomer || "",
        project_id: selectedProject || "",
        family_id: selectedFamily || "",
        value_stream_id: selectedValueStream || "",
        area_id: selectedArea || "",
        me_definition_version: selectedMeDefinition,
        effective_date: selectedMeDefinitionEffectiveDate,
        assignedId: newWorker.operatorId || undefined,
      };

      await createWorkstation(request);
      setIsCreateDialogOpen(false);
      setNewWorker(null);
      setEditingRowIndex(null);
      // Clear operators list after successful creation
      useCrewManagementStore.getState().clearOperatorsForReassignment();
      // Refetch the table with the current pagination and search query
      Promise.all([
        fetchWorkstationAssignments(
          workstationPagination.currentPage - 1,
          workstationPagination.pageSize,
          searchQuery || undefined,
        ),
        fetchUnassignedWorkers(pagination.currentPage - 1, pagination.pageSize),
      ]);
    } catch (error) {
      console.error("Failed to create workstation:", error);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedWorkstationForDelete) return;

    const currentVersion = meDefinitionVersions.find(
      (version) => version.me_definition_version === selectedMeDefinition,
    );

    if (!currentVersion) return;

    setIsDeleteLoading(true);
    try {
      const deletePayload = {
        stationId: selectedWorkstationForDelete.stationId,
        teamleader_id: currentVersion.teamLeader_id,
        ...(selectedWorkstationForDelete.operatorId && {
          operatorId: selectedWorkstationForDelete.operatorId,
        }),
        ...(selectedTeam && { team_id: selectedTeam }),
      };
      await deleteWorkstation(deletePayload);
      setSelectedWorkstationForDelete(null);
    } catch (error) {
      console.error("Failed to delete workstation:", error);
      // Error handling is now done in the store with proper backend message extraction
      // No need to show a generic toast here as the store will show the proper message
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const columns = [
    {
      id: "select",
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment; index: number };
      }) => (
        <Checkbox
          checked={selectedRow?.id === row.original.id}
          onCheckedChange={() => {
            if (selectedRow?.id === row.original.id) {
              handleRowClickWithCallback(null);
            } else {
              handleRowClickWithCallback(row.original);
            }
          }}
          aria-label="Select row"
          className="h-4 w-4 rounded-sm"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "station",
      enableSorting: true,
      header: () => <span>{t("stationSubRole")}</span>,
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment; index: number };
      }) => {
        if (
          newWorker &&
          editingRowIndex === row.index &&
          !row.original.stationId
        ) {
          return (
            <div style={{ minWidth: 200 }}>
              <CustomSelect
                options={workstationOptions.map((option) => ({
                  label: option.label,
                  value: option.id,
                }))}
                onValueChange={(value) => {
                  if (newWorker) {
                    const updatedWorker = {
                      ...newWorker,
                      workstationId: value,
                      station: value,
                    };
                    setNewWorker(updatedWorker);

                    // Only fetch operators if a valid station is selected
                    if (value && value.trim() !== "") {
                      const currentVersion = meDefinitionVersions.find(
                        (version) =>
                          version.me_definition_version ===
                          selectedMeDefinition,
                      );

                      if (currentVersion?.teamLeader_id) {
                        fetchOperatorsForMsStationCreation(
                          value,
                          currentVersion.teamLeader_id,
                          selectedTeam || "",
                          selectedCustomer || "",
                          selectedProject || "",
                          selectedFamily || "",
                          selectedValueStream || "",
                          selectedArea || "",
                          selectedMeDefinition,
                          selectedMeDefinitionEffectiveDate,
                        );
                      }
                    } else {
                      // Clear operators list when station is deselected
                      useCrewManagementStore
                        .getState()
                        .clearOperatorsForReassignment();
                    }
                  }
                }}
                defaultValue={newWorker?.workstationId}
                placeholder={t("selectStation")}
                disabled={isLoading}
              />
            </div>
          );
        }
        return <span>{row.original.station || ""}</span>;
      },
    },
    {
      accessorKey: "stationRole",
      enableSorting: true,
      header: () => <span>{t("role")}</span>,
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment; index: number };
      }) => <span>{row.original.stationRole || ""}</span>,
    },
    {
      accessorKey: "workstationSkills",
      header: () => <span>{t("workstationSkills")}</span>,
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment };
      }) => {
        const skills = row.original.workstationSkills;
        if (Array.isArray(skills)) {
          // Handle both old format (string[]) and new format (WorkstationSkill[])
          if (
            skills.length > 0 &&
            typeof skills[0] === "object" &&
            "skill_name" in skills[0]
          ) {
            return (
              <span>
                {skills.map((skill) => (skill as Skills).skill_name).join(", ")}
              </span>
            );
          }
          return <span>{skills.join(", ")}</span>;
        }
        return <span>{skills || ""}</span>;
      },
    },
    createOperatorSelectionColumn(
      t,
      editingRowIndex,
      newWorker,
      operatorsForReassignment,
      selectedOperator,
      selectedWorkstationForReassign,
      isLoading,
      setNewWorker,
      setSelectedWorkstationForReassign,
      setSelectedOperator,
    ),
    {
      accessorKey: "operatorFirstName",
      enableSorting: true,
      header: () => <span>{t("name")}</span>,
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment; index: number };
      }) => <span>{row.original.operatorFirstName || ""}</span>,
    },
    {
      accessorKey: "operatorLastName",
      enableSorting: true,
      header: () => <span>{t("surname")}</span>,
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment; index: number };
      }) => <span>{row.original.operatorLastName || ""}</span>,
    },
    {
      accessorKey: "actions",
      header: () => <span>{t("actions")}</span>,
      cell: ({
        row,
      }: {
        row: { original: Worker | WorkstationAssignment; index: number };
      }) => (
        <div className="flex space-x-2">
          {editingRowIndex === row.index ? (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-green-600"
                onClick={handleCheckClick}
              >
                <Check className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-red-600"
                onClick={() => {
                  setNewWorker(null);
                  setEditingRowIndex(null);
                  // Clear operators list when canceling
                  useCrewManagementStore
                    .getState()
                    .clearOperatorsForReassignment();
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-blue-50 hover:text-blue-600"
                onClick={async () => {
                  try {
                    await startEditing(row.index);
                  } catch (error) {
                    console.error("Error starting edit mode:", error);
                  }
                }}
              >
                <Pencil className="h-4 w-4 text-blue-600" />
              </Button>
              {!("workstationIsMeStructure" in row.original) ||
              !row.original.workstationIsMeStructure ? (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                  onClick={() => {
                    const assignment = row.original as WorkstationAssignment;
                    setSelectedWorkstationForDelete(assignment);
                  }}
                >
                  <Trash2 className="h-4 w-4 text-red-600" />
                </Button>
              ) : null}
            </>
          )}
        </div>
      ),
    },
  ];

  // Ensure the temporary newWorker row is removed whenever the assignment table is fetched
  useEffect(() => {
    setNewWorker(null);
    setEditingRowIndex(null);
    // Clear operators list when table is refreshed
    useCrewManagementStore.getState().clearOperatorsForReassignment();
  }, [workstationAssignments, setNewWorker, setEditingRowIndex]);

  // Cleanup effect to clear store state when component unmounts
  useEffect(() => {
    return () => {
      // Clear all store state when component unmounts
      useCrewManagementStore.setState({
        // Clear selected values
        selectedCustomer: "",
        selectedProject: "",
        selectedFamily: "",
        selectedValueStream: "",
        selectedArea: "",
        selectedTeam: "",
        selectedMeDefinition: "",
        selectedMeDefinitionEffectiveDate: "",
        meDefinitionTeamLeaderLegacySite: "",

        // Clear options
        projectOptions: [],
        familyOptions: [],
        valueStreamOptions: [],
        areaOptions: [],
        teamOptions: [],
        meDefinitionOptions: [],
        meDefinitionVersions: [],

        // Clear table data
        workstationAssignments: [],
        unassignedWorkers: [],

        // Clear pagination
        workstationPagination: {
          currentPage: 1,
          pageSize: 5,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
        pagination: {
          currentPage: 1,
          pageSize: 5,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },

        // Clear meta data
        workstationMeta: {
          totalUnassignedME: 0,
          totalUnassignedMS: 0,
          totalAssignedME: 0,
          totalAssignedMS: 0,
        },

        // Clear other state
        workstationOptions: [],
        operatorsForReassignment: [],
        selectedOperator: null,
        editingRowIndex: null,
        editedWorker: null,
        newWorker: null,
        selectedRow: null,
        searchQuery: "",
        sorting: [],
        workstationSorting: [],
        unassignedWorkersSorting: [],
        selectedWorkers: [],
        selectAll: false,
        workerIdsToDelete: [],
        selectedCrewMember: null,
        workers: [],
        operationSkills: [],
        selectedSkills: [],
        isLoading: false,
        isWorkstationAssignmentsLoading: false,
        isUnassignedWorkersLoading: false,
        selectedWorkstation: null,
      });
    };
  }, []);

  return (
    <div className="p-2 h-full w-full">
      <div
        className={
          "flex items-center p-3 rounded-lg mb-1  relative border shadow-sm bg-gradient-to-b from-[#FFFFFF] via-[#FAFAFA] to-[#C2C2C2] border-[#010101]"
        }
        id="selection-card"
      >
        <div className="grid grid-cols-4 gap-2 mb-5 w-full">
          <CustomSelect
            options={customerOptions.map((option) => ({
              label: option.value,
              value: option.id,
            }))}
            onValueChange={(value) => handleSelect("customer", value)}
            defaultValue={selectedCustomer}
            placeholder={t("selectCustomer")}
            label={
              <span className="flex items-center">
                {CustomerIcons}
                {t("customer")}
              </span>
            }
            disabled={isLoading}
            id="customer-select"
          />
          <CustomSelect
            options={projectOptions.map((option) => ({
              label: option.value,
              value: option.id,
            }))}
            onValueChange={(value) => handleSelect("project", value)}
            defaultValue={selectedProject}
            placeholder={t("selectProject")}
            label={
              <span className="flex items-center">
                {ProjectIcons}
                {t("project")}
              </span>
            }
            disabled={
              !selectedCustomer || isLoading || projectOptions.length === 0
            }
            id="project-select"
          />
          <CustomSelect
            options={familyOptions.map((option) => ({
              label: option.value,
              value: option.id,
            }))}
            onValueChange={(value) => handleSelect("family", value)}
            defaultValue={selectedFamily}
            placeholder={t("selectFamily")}
            label={
              <span className="flex items-center">
                {FamilyIcons}
                {t("family")}
              </span>
            }
            disabled={
              !selectedProject || isLoading || familyOptions.length === 0
            }
            id="family-select"
          />
          <CustomSelect
            options={valueStreamOptions.map((option) => ({
              label: option.value,
              value: option.id,
            }))}
            onValueChange={(value) => handleSelect("valueStream", value)}
            defaultValue={selectedValueStream}
            placeholder={t("selectValueStream")}
            label={
              <span className="flex items-center">
                {LineChartIcons}
                {t("valueStream")}
              </span>
            }
            disabled={
              !selectedFamily || isLoading || valueStreamOptions.length === 0
            }
            id="value-stream-select"
          />
          <CustomSelect
            options={areaOptions.map((option) => ({
              label: option.value,
              value: option.id,
            }))}
            onValueChange={(value) => handleSelect("area", value)}
            defaultValue={selectedArea}
            placeholder={t("selectArea")}
            label={
              <span className="flex items-center">
                {AreaIcons}
                {t("area")}
              </span>
            }
            disabled={
              !selectedValueStream || isLoading || areaOptions.length === 0
            }
            id="area-select"
          />
          <CustomSelect
            options={teamOptions.map((option) => ({
              label: option.value,
              value: option.id,
            }))}
            onValueChange={(value) => handleSelect("team", value)}
            defaultValue={selectedTeam}
            placeholder={t("selectTeam")}
            label={
              <span className="flex items-center">
                {TeamIcons}
                {t("team")}
              </span>
            }
            disabled={!selectedArea || isLoading || teamOptions.length === 0}
            id="team-select"
          />
          <CustomSelect
            label={t("meDefinition")}
            options={meDefinitionOptions.map((option) => ({
              label: option.label,
              value: option.value,
            }))}
            onValueChange={(value) => {
              setSelectedMeDefinition(value);
              if (!value) {
                useCrewManagementStore.setState({
                  selectedMeDefinitionEffectiveDate: "",
                });
              }
            }}
            value={selectedTeam ? selectedMeDefinition : ""}
            key={selectedTeam || "no-team"}
            disabled={
              !selectedTeam || isLoading || meDefinitionOptions.length === 0
            }
          />
          <div className="flex flex-col items-start gap-1.5 text-sm w-full max-w-[250px]">
            <div className="flex items-center gap-1 w-full">
              <Clock4 className="h-5 w-5 flex-shrink-0" />
              <TruncatedText
                text={t("meDefinitionEffectiveDate")}
                className="text-sm"
              />
            </div>
            <div className="w-full p-2 bg-gray-200 border border-gray-300 rounded-md shadow-sm">
              <TruncatedText
                text={
                  selectedMeDefinitionEffectiveDate
                    ? new Date(
                        selectedMeDefinitionEffectiveDate,
                      ).toLocaleDateString()
                    : t("notSelected")
                }
                className="font-semibold"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-xl border shadow-sm mt-2">
        <div className="p-3">
          <DataTable
            columns={columns}
            data={
              newWorker
                ? [newWorker, ...workstationAssignments]
                : workstationAssignments
            }
            serverPagination={true}
            onPaginationChange={(pageIndex, pageSize) => {
              fetchWorkstationAssignments(pageIndex, pageSize, searchQuery);
            }}
            totalItems={workstationPagination.totalCount}
            currentPage={workstationPagination.currentPage - 1}
            pageSize={workstationPagination.pageSize}
            showSearchBar={true}
            serverSearch={true}
            onSearchChange={(query) => {
              setSearchQuery(query);
              fetchWorkstationAssignments(
                0,
                workstationPagination.pageSize,
                query,
              );
            }}
            showStatusFilter={false}
            sorting={workstationSorting}
            onSortingChange={(newSorting) => {
              setWorkstationSorting(newSorting);
              fetchWorkstationAssignments(
                workstationPagination.currentPage - 1,
                workstationPagination.pageSize,
                searchQuery,
              );
            }}
            isLoading={isWorkstationAssignmentsLoading}
            searchValue={searchQuery}
            buttons={[
              {
                show: true,
                id: "btn-add-ms-line",
                label: t("addMsLine"),
                icon: Plus,
                onClick: initiateNewWorker,
                variant: "default",
              },
            ]}
          />
        </div>
      </div>
      <div className="flex flex-col sm:flex-row justify-between gap-4 p-2">
        <div className="flex items-center justify-between sm:justify-start sm:space-x-8 bg-black text-white p-2 sm:p-3 rounded-lg w-full sm:w-auto">
          <div className="flex items-center whitespace-nowrap">
            <span className="mr-1 sm:mr-2 text-[10px] xs:text-xs sm:text-sm">
              {t("totalUnassigned")}
            </span>
            <span className="font-medium text-[10px] xs:text-xs sm:text-sm">
              ME: {totalUnassignedME ?? 0}
            </span>
          </div>
          <div className="flex items-center whitespace-nowrap">
            <span className="font-medium text-[10px] xs:text-xs sm:text-sm">
              MFG: {totalUnassignedMS ?? 0}
            </span>
          </div>
        </div>
        <div className="flex items-center justify-between sm:justify-start sm:space-x-8 bg-black text-white p-2 sm:p-3 rounded-lg w-full sm:w-auto">
          <div className="flex items-center whitespace-nowrap">
            <span className="mr-1 sm:mr-2 text-[10px] xs:text-xs sm:text-sm">
              {t("totalAssigned")}
            </span>
            <span className="font-medium text-[10px] xs:text-xs sm:text-sm">
              ME: {totalAssignedME ?? 0}
            </span>
          </div>
          <div className="flex items-center whitespace-nowrap">
            <span className="font-medium text-[10px] xs:text-xs sm:text-sm">
              MFG: {totalAssignedMS ?? 0}
            </span>
          </div>
        </div>
      </div>
      <DeleteDialog
        isDialogOpen={selectedWorkstationForDelete !== null}
        setIsDialogOpen={() => {
          if (!isDeleteLoading) {
            setSelectedWorkstationForDelete(null);
          }
        }}
        handleDelete={handleDeleteConfirm}
        labelCancel={t("cancel")}
        labelConfirm={t("confirm")}
        loading={isDeleteLoading}
      >
        <p className="flex justify-center items-center">
          {selectedWorkstationForDelete
            ? t("deleteWorkstation", {
                station: selectedWorkstationForDelete.station,
              })
            : ""}
        </p>
      </DeleteDialog>
      <ValidationDialog
        isDialogOpen={isReassignDialogOpen}
        setIsDialogOpen={setIsReassignDialogOpen}
        handleConfirm={handleReassignConfirm}
        labelCancel={t("cancel")}
        labelConfirm={t("confirm")}
        isLoading={isLoading}
      >
        <p className="text-center">
          {selectedWorkstationForReassign?.operatorFirstName &&
          selectedWorkstationForReassign?.operatorLastName
            ? t("reassignOperator", {
                oldName: `${selectedWorkstationForReassign.operatorFirstName} ${selectedWorkstationForReassign.operatorLastName}`,
                newName: selectedOperator?.operatorFullName,
              })
            : t("assignOperator", {
                name: selectedOperator?.operatorFullName,
              })}
        </p>
      </ValidationDialog>

      <ValidationDialog
        isDialogOpen={isCreateDialogOpen}
        setIsDialogOpen={setIsCreateDialogOpen}
        handleConfirm={handleCreateWorkstation}
        labelCancel={t("cancel")}
        labelConfirm={t("confirm")}
        isLoading={isLoading}
      >
        <p className="text-center">{t("addMsLineConfirm")}</p>
      </ValidationDialog>
    </div>
  );
}
