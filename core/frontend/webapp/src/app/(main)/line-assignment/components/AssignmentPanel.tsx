"use client";

import React, { useEffect, useState } from "react";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import type { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/common/tables/DataTable";
import { IconButton, ReusableButton } from "@/components/common/CustomButtons";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Check, X } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import useCrewManagementStore from "../store/crewManagementStore";
import type {
  Worker as CrewWorker,
  AssignToWorkstationRequest,
  Skills,
} from "../types/crewManagementTypes";
import useIsSmallScreen from "@/hooks/useIsSmallScreen";
import CustomMultiSelect from "@/components/common/CustomMultiSelect";
import { useTranslations } from "next-intl";

interface AssignmentPanelProps {
  onClose: () => void;
}

export default function AssignmentPanel({ onClose }: AssignmentPanelProps) {
  const t = useTranslations("line_assignment");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const {
    selectedTeam,
    isLoading,
    isUnassignedWorkersLoading,
    selectedCrewMember,
    selectedWorkers,
    setSelectedWorkers,
    clearWorkerSelection,
    fetchUnassignedWorkers,
    pagination,
    operationSkills,
    selectedSkills,
    setSelectedSkills,
    fetchOperationSkills,
    assignToWorkstation,
    unassignedWorkersSorting,
    setUnassignedWorkersSorting,
    unassignedWorkers,
    meDefinitionTeamLeaderLegacySite,
    fetchWorkstationAssignments,
    workstationPagination,
    searchQuery,
  } = useCrewManagementStore();

  const [paginationState, setPaginationState] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const isSmallScreen = useIsSmallScreen();
  // const selectionCount = selectedWorkers.length;

  useEffect(() => {
    if (selectedTeam && selectedCrewMember) {
      fetchUnassignedWorkers(
        paginationState.pageIndex,
        paginationState.pageSize,
      );
    } else if (!selectedTeam) {
      // Clear unassigned workers and pagination if team is not selected
      useCrewManagementStore.setState({
        unassignedWorkers: [],
        pagination: {
          currentPage: 1,
          pageSize: 5,
          totalCount: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      });
    }
  }, [
    selectedTeam,
    selectedCrewMember,
    paginationState,
    fetchUnassignedWorkers,
  ]);

  useEffect(() => {
    fetchOperationSkills();
  }, [fetchOperationSkills]);

  const handleCancel = () => {
    setSelectedWorkers([]);
    onClose();
    setIsDialogOpen(false);
  };

  const handleSkillsChange = (skills: string[]) => {
    setSelectedSkills(skills);
    // Always reset pagination to first page when filtering
    setPaginationState((prev) => {
      const newState = { pageIndex: 0, pageSize: prev.pageSize };
      // Fetch with new pagination after state update
      fetchUnassignedWorkers(0, prev.pageSize);
      return newState;
    });
  };

  const handleWorkerSelection = (workerId: string) => {
    if (selectedWorkers.includes(workerId)) {
      setSelectedWorkers([]);
    } else {
      setSelectedWorkers([workerId]);
    }
  };

  const columns: ColumnDef<CrewWorker>[] = [
    {
      id: "select",
      // header: () => (
      //   <div>
      //     <Checkbox
      //       checked={selectedWorkers.length > 0}
      //       onCheckedChange={(value) => {
      //         if (!value) {
      //           setSelectedWorkers([]);
      //         }
      //       }}
      //       className="h-4 w-4 rounded-sm"
      //       id="select-all-workers"
      //     />
      //   </div>
      // ),
      cell: ({ row }) => (
        <div>
          <Checkbox
            checked={selectedWorkers.includes(String(row.original.legacyId))}
            onCheckedChange={() =>
              handleWorkerSelection(String(row.original.legacyId))
            }
            className="h-4 w-4 rounded-sm"
            id={`worker-${row.original.legacyId}`}
          />
        </div>
      ),
    },
    {
      accessorKey: "legacyId",
      header: t("id"),
      enableSorting: true,
      cell: ({ row }) => <strong>{row.original.legacyId}</strong>,
    },
    {
      accessorKey: "name",
      header: t("name"),
      enableSorting: true,

      cell: ({ row }) => <span>{row.original.name ?? ""}</span>,
    },
    {
      accessorKey: "surname",
      header: t("surname"),
      enableSorting: true,

      cell: ({ row }) => <span>{row.original.surname ?? ""}</span>,
    },
    {
      accessorKey: "department",
      header: t("departProcess"),
      enableSorting: true,
      cell: ({ row }) => {
        const { skills } = row.original;
        const renderSkills = () => {
          if (!skills || !Array.isArray(skills)) {
            return (
              <div>
                <strong>{row.original.department}</strong>
                {skills && <br />}
                <span>{skills || ""}</span>
              </div>
            );
          }

          if (skills.length === 0) {
            return (
              <div>
                <strong>{row.original.department}</strong>
              </div>
            );
          } // Check if it's the new format with objects containing skill_name
          const firstSkill = skills[0];
          if (
            typeof firstSkill === "object" &&
            firstSkill !== null &&
            "skill_name" in firstSkill
          ) {
            return (
              <div>
                <strong>{row.original.department}</strong>
                <br />
                <span>
                  {skills
                    .map((skill) => (skill as Skills).skill_name)
                    .filter(Boolean) // Remove any undefined/null skill names
                    .join(", ")}
                </span>
              </div>
            );
          } // Handle old format (array of strings)
          if (typeof firstSkill === "string") {
            return (
              <div>
                <strong>{row.original.department}</strong>
                <br />
                <span>{skills.join(", ")}</span>
              </div>
            );
          }

          // Fallback
          return (
            <span>
              <strong>{row.original.department}</strong>
            </span>
          );
        };

        return renderSkills();
      },
    },
    {
      accessorKey: "role",
      header: t("function"),
      enableSorting: true,
    },
  ];

  const handleConfirm = async () => {
    if (!selectedCrewMember || !selectedTeam || selectedWorkers.length === 0)
      return;

    const request: AssignToWorkstationRequest = {
      stationId: selectedCrewMember.stationId || "",
      team: {
        teamId: selectedTeam,
        teamLeaderId: meDefinitionTeamLeaderLegacySite,
      },
      assigned: {
        id:
          unassignedWorkers.find(
            (w) => w.legacyId === Number(selectedWorkers[0]),
          )?.id || "",
      },
      unassigned: selectedCrewMember.operatorId
        ? {
            id: selectedCrewMember.operatorId,
          }
        : undefined,
    };

    try {
      await assignToWorkstation(request);
      clearWorkerSelection();
      // Refetch both tables with their current pagination and search states
      await Promise.all([
        fetchUnassignedWorkers(pagination.currentPage - 1, pagination.pageSize),
        fetchWorkstationAssignments(
          workstationPagination.currentPage - 1,
          workstationPagination.pageSize,
          searchQuery,
        ),
      ]);
      setIsDialogOpen(false);
      onClose();
    } catch (error) {
      console.error("Failed to assign to workstation:", error);
    }
  };

  return (
    <div className="flex h-full flex-col relative">
      {isSmallScreen && (
        <div className="mb-4 flex items-center justify-between pb-5">
          <IconButton
            icon={X}
            onClick={onClose}
            className="h-8 w-8 rounded-full absolute top-4 right-4"
          />
        </div>
      )}
      <div className="space-y-4 flex-1 overflow-auto pb-16">
        <div>
          <h3 className="text-sm font-bold text-black">
            {t("unassignedWorkers")} ({selectedTeam ? pagination.totalCount : 0}
            )
          </h3>
          <Separator
            orientation="horizontal"
            className="mb-3 border-blue-600 border-2"
          />
          <div className="flex items-center justify-between mb-6 mt-4">
            <span className="text-lg text-[#4762F1]">{t("finalAssembly")}</span>
          </div>{" "}
          <CustomMultiSelect
            options={operationSkills.map((skill) => ({
              value: skill.skill_id,
              label: skill.skill_name,
            }))}
            label={t("operationSkills")}
            onValueChange={handleSkillsChange}
            id="select-skills"
            value={selectedSkills}
            placeholder={t("selectOperationSkills")}
            className="min-w-[300px]"
          />
          <div>
            <div className="flex items-center justify-between mt-4">
              {/* <span className="text-sm text-black">
                Selected {selectedWorkers.length}/
                {selectedTeam ? pagination.totalCount : 0}
                {selectionCount > 0 && (
                  <ReusableButton
                    variant="ghost"
                    className="text-sm font-normal h-9 text-blue-300"
                    onClick={() => setSelectedWorkers([])}
                    label="Clear"
                    id="clear-selection"
                  />
                )}
              </span> */}

              {/* <Select
                value={String(selectionCount)}
                onValueChange={() => {
                  setSelectedWorkers([]);
                }}
                disabled={!selectedTeam}
              >
                <SelectTrigger className="w-32 h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent id="select-count">
                  <SelectItem value="0">Select</SelectItem>
                  <SelectItem value="1">Select 1</SelectItem>
                </SelectContent>
              </Select> */}
            </div>

            <div className="mt-4">
              <DataTable
                columns={columns}
                data={unassignedWorkers}
                serverPagination={true}
                onPaginationChange={(pageIndex, pageSize) => {
                  setPaginationState({ pageIndex, pageSize });
                  fetchUnassignedWorkers(pageIndex, pageSize);
                }}
                totalItems={pagination.totalCount}
                currentPage={paginationState.pageIndex}
                pageSize={paginationState.pageSize}
                showStatusFilter={false}
                sorting={unassignedWorkersSorting}
                onSortingChange={(newSorting) => {
                  setUnassignedWorkersSorting(newSorting);
                  fetchUnassignedWorkers(
                    paginationState.pageIndex,
                    paginationState.pageSize,
                  );
                }}
                isLoading={isUnassignedWorkersLoading}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-0 right-0 w-full bg-white">
        <div className="flex justify-end">
          <ReusableButton
            className="h-10 bg-black hover:bg-gray-800 text-white"
            label={
              !selectedCrewMember
                ? t("selectWorkersToAssign")
                : t("assignTo", { station: selectedCrewMember.station })
            }
            onClick={() => setIsDialogOpen(true)}
            disabled={
              !selectedCrewMember || selectedWorkers.length === 0 || isLoading
            }
            isLoading={isLoading}
            id="assign-button"
          />
        </div>
      </div>

      <ReusableDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        id="confirm-dialog"
      >
        <div className="flex items-center justify-center">
          <Check className="h-20 w-20 bg-green-100 text-green-600 rounded-full p-2" />
        </div>
        {selectedCrewMember && (
          <p className="p-6 text-center">
            {selectedCrewMember.operatorFirstName &&
            selectedCrewMember.operatorLastName
              ? t("reassignOperator", {
                  oldName: `${selectedCrewMember.operatorFirstName} ${selectedCrewMember.operatorLastName}`,
                  newName:
                    unassignedWorkers.find(
                      (w) => w.legacyId === Number(selectedWorkers[0]),
                    )?.fullname || "",
                })
              : t("assigningWorkers", {
                  operatorName:
                    unassignedWorkers.find(
                      (w) => w.legacyId === Number(selectedWorkers[0]),
                    )?.fullname || "",
                  station: selectedCrewMember.station,
                })}
          </p>
        )}
        <div className="flex justify-between mt-4">
          <ReusableButton
            label={t("cancel")}
            variant="outline"
            onClick={handleCancel}
          />
          <ReusableButton
            label={t("confirm")}
            onClick={handleConfirm}
            isLoading={isLoading}
          />
        </div>
      </ReusableDialog>
    </div>
  );
}
