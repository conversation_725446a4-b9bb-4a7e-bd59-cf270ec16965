{"crew": [{"id": "7488", "name": "<PERSON>", "surname": "SANADI", "role": "trainingResponsible", "assignedCount": 4, "department": "Training", "category": "A", "function": "Training Responsible", "children": [{"id": "2310", "name": "<PERSON><PERSON><PERSON>", "surname": "BARSSIM", "role": "trainer", "assignedCount": 0, "children": [], "department": "Training", "category": "A", "function": "Trainer"}, {"id": "2311", "name": "<PERSON><PERSON><PERSON>", "surname": "BARSSIM", "role": "trainer", "assignedCount": 0, "children": [], "department": "Training", "category": "A", "function": "Trainer"}, {"id": "6652", "name": "<PERSON><PERSON><PERSON>", "surname": "TEKKA", "role": "trainer", "assignedCount": 0, "children": [{"id": "12365", "name": "<PERSON><PERSON>", "surname": "CHACHOU", "department": "Cutting", "category": "DH", "function": "Operator", "role": "operator"}, {"id": "12366", "name": "<PERSON>", "surname": "ALI", "department": "Assembly", "category": "DH", "function": "Supervisor", "role": "supervisor"}], "department": "Training", "category": "B", "function": "Trainer"}, {"id": "2312", "name": "Tarek", "surname": "<PERSON><PERSON><PERSON>", "role": "trainer", "assignedCount": 0, "children": [], "department": "Training", "category": "B", "function": "Trainer"}]}, {"id": "9001", "name": "<PERSON>", "surname": "MOUSTAPHA", "role": "SHIFT_LEADER", "assignedCount": 3, "department": "Production", "category": "SL", "function": "Shift Leader", "children": [{"id": "9100", "name": "<PERSON><PERSON><PERSON>", "surname": "BENJELLOUN", "role": "TEAM_LEADER", "assignedCount": 2, "department": "Production", "category": "TL", "function": "Team Leader", "children": [{"id": "9201", "name": "Amine", "surname": "BENALI", "role": "operator", "assignedCount": 2, "department": "Cutting", "children": []}, {"id": "9202", "name": "Sami", "surname": "RAHIM", "role": "operator", "assignedCount": 1, "department": "Assembly", "children": []}]}, {"id": "9101", "name": "Rachid", "surname": "TALIB", "role": "TEAM_LEADER", "assignedCount": 1, "department": "Quality", "category": "TL", "function": "Team Leader", "children": [{"id": "9203", "name": "<PERSON>", "surname": "NACIRI", "role": "operator", "assignedCount": 1, "department": "Quality", "children": []}]}]}, {"id": "9001", "name": "<PERSON><PERSON>", "surname": "RONALDO", "role": "TEAM_LEADER", "assignedCount": 3, "department": "Production", "category": "SL", "function": "Shift Leader", "children": [{"id": "9100", "name": "<PERSON><PERSON><PERSON>", "surname": "BENJELLOUN", "role": "team", "assignedCount": 2, "department": "Production", "category": "TL", "function": "Team Leader", "children": [{"id": "9201", "name": "Amine", "surname": "BENALI", "role": "operator", "assignedCount": 2, "department": "Cutting", "children": []}, {"id": "9202", "name": "Sami", "surname": "RAHIM", "role": "operator", "assignedCount": 1, "department": "Assembly", "children": []}]}, {"id": "9101", "name": "Rachid", "surname": "TALIB", "role": "team", "assignedCount": 1, "department": "Quality", "category": "TL", "function": "Team Leader", "children": [{"id": "9203", "name": "<PERSON>", "surname": "NACIRI", "role": "operator", "assignedCount": 1, "department": "Quality", "children": []}]}]}, {"id": "9001", "name": "<PERSON>", "surname": "SON", "role": "TRAINER", "assignedCount": 3, "department": "Production", "category": "SL", "function": "Shift Leader", "children": [{"id": "9100", "name": "<PERSON><PERSON><PERSON>", "surname": "BENJELLOUN", "role": "SHIFT_LEADER", "assignedCount": 2, "department": "Assembly", "category": "TL", "function": "Team Leader", "children": [{"id": "9201", "name": "Amine", "surname": "BENALI", "role": "operator", "assignedCount": 2, "department": "Cutting", "children": []}, {"id": "9202", "name": "Sami", "surname": "RAHIM", "role": "operator", "assignedCount": 1, "department": "Assembly", "children": []}]}, {"id": "9101", "name": "Rachid", "surname": "TALIB", "role": "SHIFT_LEADER", "assignedCount": 1, "department": "Quality", "category": "TL", "function": "Team Leader", "children": [{"id": "9203", "name": "<PERSON>", "surname": "NACIRI", "role": "operator", "assignedCount": 1, "department": "Quality", "children": []}]}]}, {"id": "8976", "name": "<PERSON>", "surname": "BENALI", "role": "TRAINING_RESPONSIBLE", "assignedCount": 3, "department": "Production", "category": "SL", "function": "Shift Leader", "children": [{"id": "9100", "name": "<PERSON><PERSON><PERSON>", "surname": "BENJELLOUN", "role": "TRAINER", "assignedCount": 2, "department": "Production", "category": "TL", "function": "Team Leader", "children": [{"id": "9201", "name": "Amine", "surname": "BENALI", "role": "operator", "assignedCount": 2, "department": "Cutting", "children": []}, {"id": "9202", "name": "Sami", "surname": "RAHIM", "role": "operator", "assignedCount": 1, "department": "Assembly", "children": []}]}, {"id": "9101", "name": "Rachid", "surname": "TALIB", "role": "TRAINER", "assignedCount": 1, "department": "Quality", "category": "TL", "function": "Team Leader", "children": [{"id": "9203", "name": "<PERSON>", "surname": "NACIRI", "role": "operator", "assignedCount": 1, "department": "Quality", "children": []}]}]}], "unassignedWorkers": [{"id": "4", "name": "<PERSON>", "surname": "<PERSON>", "role": "operator", "department": "Cutting", "category": "B", "function": "Operator"}, {"id": "5", "name": "<PERSON>", "surname": "<PERSON>", "role": "operator", "department": "Engineering", "category": "B", "function": "Operator"}, {"id": "6", "name": "<PERSON><PERSON>", "surname": "HASSAN", "role": "operator", "department": "Assembly", "category": "B", "function": "Operator"}, {"id": "7", "name": "Layla", "surname": "MIR", "role": "operator", "department": "Quality", "category": "B", "function": "Operator"}]}