{"projects": [{"id": "x-body", "label": "Project", "value": "X-BODY", "role": "PROJECT"}, {"id": "y-body", "label": "Project", "value": "Y-BODY", "role": "PROJECT"}], "families": {"x-body": [{"id": "front-part", "label": "Family", "value": "Front part", "role": "FAMILY"}, {"id": "rear-part", "label": "Family", "value": "Rear part", "role": "FAMILY"}], "y-body": [{"id": "side-part", "label": "Family", "value": "Side part", "role": "FAMILY"}, {"id": "top-part", "label": "Family", "value": "Top part", "role": "FAMILY"}]}, "valueStreams": {"front-part": [{"id": "bilboard", "label": "Value Stream", "value": "BILBOARD", "role": "VALUE_STREAM"}, {"id": "wake-stream", "label": "Value Stream", "value": "Wake Stream", "role": "VALUE_STREAM"}], "rear-part": [{"id": "assembly", "label": "Value Stream", "value": "Assembly", "role": "VALUE_STREAM"}, {"id": "finishing", "label": "Value Stream", "value": "Finishing", "role": "VALUE_STREAM"}], "side-part": [{"id": "side-assembly", "label": "Value Stream", "value": "Side Assembly", "role": "VALUE_STREAM"}, {"id": "side-finishing", "label": "Value Stream", "value": "Side Finishing", "role": "VALUE_STREAM"}], "top-part": [{"id": "top-assembly", "label": "Value Stream", "value": "Top Assembly", "role": "VALUE_STREAM"}, {"id": "top-finishing", "label": "Value Stream", "value": "Top Finishing", "role": "VALUE_STREAM"}]}, "areas": {"bilboard": [{"id": "area-1", "label": "Area", "value": "Area 1", "role": "AREA"}, {"id": "area-2", "label": "Area", "value": "Area 2", "role": "AREA"}], "wake-stream": [{"id": "area-3", "label": "Area", "value": "Area 3", "role": "AREA"}, {"id": "area-4", "label": "Area", "value": "Area 4", "role": "AREA"}], "assembly": [{"id": "area-5", "label": "Area", "value": "Area 5", "role": "AREA"}, {"id": "area-6", "label": "Area", "value": "Area 6", "role": "AREA"}], "finishing": [{"id": "area-7", "label": "Area", "value": "Area 7", "role": "AREA"}, {"id": "area-8", "label": "Area", "value": "Area 8", "role": "AREA"}], "side-assembly": [{"id": "area-9", "label": "Area", "value": "Area 9", "role": "AREA"}, {"id": "area-10", "label": "Area", "value": "Area 10", "role": "AREA"}], "side-finishing": [{"id": "area-11", "label": "Area", "value": "Area 11", "role": "AREA"}, {"id": "area-12", "label": "Area", "value": "Area 12", "role": "AREA"}], "top-assembly": [{"id": "area-13", "label": "Area", "value": "Area 13", "role": "AREA"}, {"id": "area-14", "label": "Area", "value": "Area 14", "role": "AREA"}], "top-finishing": [{"id": "area-15", "label": "Area", "value": "Area 15", "role": "AREA"}, {"id": "area-16", "label": "Area", "value": "Area 16", "role": "AREA"}]}, "teams": {"area-1": [{"id": "team-1", "label": "Team", "value": "Team 1", "role": "TEAMS"}, {"id": "team-2", "label": "Team", "value": "Team 2", "role": "TEAMS"}], "area-2": [{"id": "team-3", "label": "Team", "value": "Team 3", "role": "TEAMS"}, {"id": "team-4", "label": "Team", "value": "Team 4", "role": "TEAMS"}], "area-3": [{"id": "team-5", "label": "Team", "value": "Team 5", "role": "TEAMS"}, {"id": "team-6", "label": "Team", "value": "Team 6", "role": "TEAMS"}], "area-4": [{"id": "team-7", "label": "Team", "value": "Team 7", "role": "TEAMS"}, {"id": "team-8", "label": "Team", "value": "Team 8", "role": "TEAMS"}]}, "workers": {"team-1": [{"id": "1", "idWorker": "", "name": "", "surname": "", "role": "ME STRUCTURE", "station": "CELL 1", "skills": "ULTRASONIC, SOUDAGE"}, {"id": "2", "idWorker": "", "name": "", "surname": "", "role": "ME STRUCTURE", "station": "CELL 2", "skills": "SOUDAGE, EMBALLAGE, ROB"}, {"id": "3", "idWorker": "", "name": "", "surname": "", "role": "MANUFACTURING STRUCTURE", "station": "ROB", "skills": "ROB"}], "team-2": [{"id": "4", "idWorker": "", "name": "", "surname": "", "role": "ME STRUCTURE", "station": "CELL 4", "skills": "ULTRASONIC, SOUDAGE, EMBALLAGE, ROB"}, {"id": "5", "idWorker": "", "name": "", "surname": "", "role": "MANUFACTURING STRUCTURE", "station": "POLYVALENT", "skills": "ULTRASONIC, SOUDAGE, EMBALLAGE, ROB"}], "team-3": [{"id": "6", "idWorker": "", "name": "", "surname": "", "role": "ME STRUCTURE", "station": "CELL 6", "skills": "ULTRASONIC, SOUDAGE"}, {"id": "7", "idWorker": "", "name": "", "surname": "", "role": "MANUFACTURING STRUCTURE", "station": "RÉPARATEUR", "skills": "EMBALLAGE, ROB"}], "team-4": [{"id": "8", "idWorker": "", "name": "", "surname": "", "role": "ME STRUCTURE", "station": "CELL 9", "skills": "ULTRASONIC, SOUDAGE, EMBALLAGE"}, {"id": "9", "idWorker": "", "name": "", "surname": "", "role": "MANUFACTURING STRUCTURE", "station": "SOP", "skills": "ROB, SOUDAGE"}], "unassigned": [{"id": "12374", "idWorker": "12374", "name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "fullname": "<PERSON>", "department": "Assembly", "process": "ULTRASONIC, SOUDAGE", "function": "OPERATOR"}, {"id": "12375", "idWorker": "12375", "name": "<PERSON>", "surname": "El Amrani", "fullname": "<PERSON>", "department": "Assembly", "process": "EMBALLAGE, ROB", "function": "OPERATOR"}, {"id": "12376", "idWorker": "12376", "name": "<PERSON><PERSON><PERSON>", "surname": "El Amrani", "fullname": "<PERSON><PERSON><PERSON>", "department": "Assembly", "process": "ULTRASONIC, SOUDAGE, EMBALLAGE, ROB", "function": "OPERATOR"}]}, "stationsByRole": {"ME STRUCTURE": ["CELL 1", "CELL 2", "CELL 4", "CELL 6", "CELL 9", "CELL 10", "CELL 12"], "MANUFACTURING STRUCTURE": ["ROB", "POLYVALENT", "RÉPARATEUR", "SOP", "SUPPORT (BACK UP)", "SOS ASSEMBLAGE"]}}