{"customer": {"values": ["Customer1", "Customer2", "Customer3"], "metadata": {"level": "customer", "filters": {}}}, "project": {"values": ["ProjectA", "ProjectB", "ProjectC"], "metadata": {"level": "project", "filters": {"customer": "Customer1"}}}, "family": {"values": ["FamilyX", "FamilyY", "FamilyZ"], "metadata": {"level": "family", "filters": {"customer": "Customer1", "projet": "ProjectA"}}}, "valueStream": {"values": ["ValueStream1", "ValueStream2"], "metadata": {"level": "valueStream", "filters": {"customer": "Customer1", "projet": "ProjectA", "famille": "FamilyX"}}}, "area": {"values": ["Area1", "Area2", "Area3"], "metadata": {"level": "area", "filters": {"customer": "Customer1", "projet": "ProjectA", "famille": "FamilyX", "value_stream": "ValueStream1"}}}, "team": {"values": ["Team1", "Team2"], "metadata": {"level": "team", "filters": {"customer": "Customer1", "projet": "ProjectA", "famille": "FamilyX", "value_stream": "ValueStream1", "area": "Area1"}}}}