{"evaluation": {"scheduleTrainingSession": "Schedule training session", "trainingSessionEvaluation": "Training Session Evaluation", "evaluationInstructions": "Please evaluate the operator's performance based on the following categories. Select the most appropriate statement for each.", "totalScore": "Total Score", "status": "Status", "pass": "Pass", "fail": "Fail", "passMessage": "Pass and meets all performance expectations.", "failMessage": "Fail and requires additional training.", "excellentMessage": "Excellent! The operator meets expectations across all evaluation categories. No additional training is needed at this time.", "improvementMessage": "The operator requires additional training and support to meet performance standards in several evaluation categories.", "back": "Back", "calculateScore": "Calculate Evaluation Score", "calculating": "Calculating...", "submit": "Submit", "close": "Close"}, "HomePage": {"title": "Welcome", "subtitle": "This Is Your Aptiv Connected Workers Home Page"}, "ProfileCard": {"myprofile": "My Profile", "employeeNumber": "Employee Number", "position": "Position", "department": "Department", "location": "Location", "joinDate": "Join Date", "phone": "Phone", "manager": "Manager", "email": "Email"}, "LeaveBalance": {"title": "Leave Balance", "daysRemaining": "Days Remaining", "days": "days"}, "OvertimeHours": {"title": "Overtime Hours", "hoursWorked": "Hours Worked", "hours": "hours"}, "LatestRequests": {"title": "Latest Requests", "noRequests": "No recent requests", "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "generated": "Generated"}, "viewAll": "View All"}, "DocumentRequest": {"title": "Request administrative document", "selectDocument": "Select a document", "selectedRequest": "Selected request", "submitRequest": "Submit Request", "requestSummary": "Request Summary", "confirmationMessage": "You have made a request for a work certificate. Please confirm if you would like to proceed with this request or cancel it.", "successMessage": "Your request has been successfully sent. You will be notified when the document is ready", "cancel": "Cancel", "submit": "Submit", "submitting": "Submitting...", "home": "Home", "trackingRequests": "Tracking Requests", "documentNotFound": "Document not found", "success": "Success", "error": "Error"}, "AbsenceRequest": {"title": "Absence Request", "selectType": "Select absence type", "submitRequest": "Submit Request", "selectPlaceholder": "Select a request", "selectedRequestLabel": "Selected request type", "insertInformation": "Please insert information", "close": "Close", "requestSummary": "Request Summary", "confirmRequest": "Confirm Request", "reviewDetails": "Please review the request details", "back": "Back", "submit": "Submit", "submitting": "Submitting...", "successTitle": "Success", "errorTitle": "Error", "unexpectedError": "An unexpected error occurred"}, "OtherRequest": {"title": "Other Requests", "selectRequest": "Select request", "selectPlaceholder": "Select other requests", "selectedRequestLabel": "Selected request :", "submitRequest": "Submit Request", "requestSummary": "Request Summary", "confirmRequest": "Confirm Your Request", "reviewDetails": "Please review the request details and confirm or make changes as necessary.", "cancel": "Cancel", "back": "Back", "submit": "Submit", "submitting": "Submitting...", "successTitle": "Success", "errorTitle": "Error", "requestNotFound": "Selected request not found"}, "Common": {"close": "Close", "cancel": "Cancel", "done": "Done", "tryAgain": "Try Again", "successMessage": "Your request has been submitted successfully!", "errorMessage": "An error occurred. Please try again."}, "header": {"user": {"title": "My Account", "profile": "Profile", "settings": "Settings", "logout": "Log out"}, "notifications": {"latestAlerts": "Latest <PERSON><PERSON>s", "latestNotifications": "Latest Notifications", "viewAllAlerts": "View all alerts", "viewAllNotifications": "View all notifications"}, "breadcrumbs": {"home": "Home", "dashboard": "Dashboard", "adminConfiguration": "Admin Configuration", "formsManagement": "Forms Management", "documentsTemplates": "Documents Templates", "newDocumentTemplate": "New Document Template", "newForm": "New Form", "requestsConfigurations": "Requests Configurations", "newWorkflow": "New Workflow", "crewManagement": "Line Assignment", "newWorkers": "New Workers", "zoning": "Zoning", "organizationChart": "Organization Chart", "calendar": "calendar", "assignmentChart": "Assignment Chart", "myTeam": "My Team", "clockingHours": "Clocking Hours", "disciplinaryMeasure": "Disciplinary Measure", "versatilityMatrix": "Versatility Matrix", "alertNotification": "Alerts & Notifications", "TrackingRequests": "Tracking Requests", "overtime": "Overtime", "plannedExecuted": "Planned vs Executed", "report": "Report", "plannification": "Plannification", "training": "Training", "trainingList": "Training List", "trainingWorkflow": "Training Workflow", "nurse": "Nurse", "transportPlanning": "Transport Planning", "newTransportPlanning": "New Transport Planning", "reportingplanning": "Reporting", "projectHierarchy": "Project Hierarchy", "clockingValidation": "Clocking Validation"}}, "sidebar": {"title": "APTIV", "subtitle": "CONNECTED WORKERS", "navigation": {"home": "Home", "documents_templates": "Documents Templates", "forms_management": "Forms Management", "requests_configurations": "Requests Configurations", "notifications": "Notifications", "my_profile": "My Profile", "logout": "Logout", "new_workers": "New Workers", "assignment_chart": "Assignment Chart", "organization_chart": "Organization Chart", "crew_management": "Crew Management", "line_assignment": "Line Assignment", "zoning": "Zoning", "calendar": "Calendar", "clocking_hours": "Clocking Hours", "disciplinary_measure": "Disciplinary Measure", "versatility_matrix": "Versatility Matrix", "my_team": "My Team", "alert_notification": "Alerts & Notifications", "tracking_requests": "Tracking Requests", "overtime": "Overtime", "planned-executed": "Planned vs Executed", "report": "Report", "plannification": "Plannification", "training": "Training", "training_list": "Training List", "training_workflow": "Training Workflow", "skills": "Skills", "workcenter_criticity": "Workcenter Criticity", "transportplanning": "Transport Planning", "project-hierarchy": "Project Hierarchy", "clocking_validation": "Clocking Validation"}, "admin": {"configuration": "Admin configuration"}}, "formsManagement": {"home": {"Addbutton": "Add new form", "title": "Forms title", "discription": "Discription", "category": "Category", "createdDate": "Created Date", "updatedDate": "Updated Date", "preview": "Preview", "cancel": "Cancel", "delete": "Delete", "deleteConfirmation": "Would you like to delete this form?"}}, "TrackingRequests": {"title": "Tracking Requests", "myRequests": "My Requests", "requestsOfMyTeam": "Requests of My Team", "administrativeDocuments": "Administrative Documents", "abssenceRequests": "Absence Requests", "otherRequests": "Other Requests", "waitingApproval": "Waiting Approval", "detailModal": {"title": "Request Details", "loading": "Loading request details...", "error": "Failed to load request details", "requestType": "Request Type", "requestDate": "Request Date", "requester": "Requester", "status": "Status", "pendingApproval": "pending approval", "requestInformation": "Request Information", "reason": "Reason", "approvalTimeline": "Approval Timeline", "requestSubmitted": "Request Submitted", "submittedBy": "Submitted by", "yourActionRequired": "Your Action Required", "approve": "Approve", "reject": "Reject", "viewDetails": "View Details"}}, "Overtime": {"Plannification": {"search": "Search", "cancel": "Cancel", "save": "Save", "allStatus": "All Status", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "createOvertime": "Create Overtime", "requestApproved": "Request Approved"}, "PlannedVsExecuted": {"showAll": "Show All", "onlyMatches": "Only Matches", "onlyMismatches": "Only Mismatches"}}, "documentsTemplates": {"home": {"Addbutton": "Create New Document Template", "title": "Documents Templates Title", "documentType": "Document Type", "discription": "Description", "createdDate": "Created Date", "actions": "Actions", "edit": "Edit", "delete": "Delete", "deleteConfirmation": "Would you like to delete this document template?"}, "NewDocumentTemplate": {"title": "New Document Template", "documentTemplateBuilder": "Document Template Builder", "ImportDocumentTemplate": "Import Document Template", "documentType": "Document Type", "administrativeDocument": "Administrative Document", "documentTemplateName": "Document Template Name", "description": "Description", "save": "Save", "systemInformations": "System informations", "copied": "Copied!", "dataInput": "Data Input", "searchForm": "Search a form", "signatures": "Signatures", "hierarchyFields": "Hierarchy Fields", "searchHierarchyFields": "Search hierarchy fields", "cancel": "Cancel", "upload": "Upload", "userInformation": "User information", "formsList": "Forms list", "systemInformation": "System information", "searchUserFields": "Search user fields", "searchSystemFields": "Search system fields", "searchAllFormFields": "Search all form fields", "searchSignatures": "Search signatures"}, "NewForm": {"title": "New Form", "form": "Form", "description": "Description", "category": "Category", "save": "Save"}}, "newTransportPlanning": {"title": "Transport Planning", "addButton": "Add new transport planning", "status": "Status", "description": "Description", "createdDate": "Created Date", "updatedDate": "Updated Date"}, "requestsConfigurations": {"home": {"title": "Requests Configurations", "addButton": "Add new workFlow", "status": "Status", "description": "Description", "createdDate": "Created Date", "updatedDate": "Updated Date"}, "newWorkflow": {"title": "Flow title", "description": "Description", "status": "Status", "save": "Save", "toolBox": "Tool Box", "properties": "Properties", "deleteNode": "Delete Node", "start": "START", "document": "DOCUMENT", "singleApproval": "SINGLE APPROVAL", "multipleApprovals": "MULTIPLE APPROVALS", "notification": "NOTIFICATION", "condition": "CONDITION", "finish": "FINISH", "clearFlow": "Clear Flow", "approvals": "Approvals", "loading": "Loading"}}, "CrewManagement": {"assignWorkers": "Assign Workers", "selectedTeamLeader": "Selected Team Leader", "selectedTrainer": "Selected Trainer", "unassignedWorkers": "Unassigned Workers", "operatorsSkills": "Operators Skills", "department": "Department", "category": "Category", "function": "Function", "selectWorkersToAssign": "Select Workers to Assign", "assignTo": "Assign to", "select": "Select", "select10": "Select 10", "select20": "Select 20", "select50": "Select 50", "clear": "Clear", "cancel": "Cancel", "confirm": "Confirm", "deleteSelected": "Delete Selected", "areYouSureDelete": "Are you sure you want to delete", "workers": "workers", "worker": "worker", "nPlusOne": "N+1", "supervisor": "Supervisor", "fullName": "Full Name", "name": "Name", "surname": "Surname", "id": "ID", "idFullName": "ID / Full Name"}, "crew_management": {"shiftLeader": "Shift Leader", "trainer": "Trainer", "operator": "Operator", "assigned": "assigned", "deleteSelected": "Delete Selected", "id": "ID", "name": "Name", "surname": "Surname", "deleteConfirmSingle": "Are you sure you want to delete", "deleteConfirmMultiple": "Are you sure you want to delete", "selectedShiftLeader": "Selected Shift leader", "n1": "N+1", "supervisor": "Supervisor", "unassignedWorkers": "Unassigned Workers", "department": "Department", "skills": "Skills", "selectWorkersToAssign": "Select Workers to Assign", "clearSelection": "Clear", "assignTo": "Assign to", "function": "Function", "skillsDepartment": "Skills / Department", "confirmAssignment": "Confirm Assignment", "workerAssignment": "Worker Assignment", "IDFullName": "ID / Full Name", "selectAll": "Select all", "selectCount": "Select Count", "select10": "Select 10", "select20": "Select 20", "select50": "Select 50", "select": "Select", "selectWorkersMessage": "Selected {selectedWorkersCount}/{totalWorkersCount} workers", "selectDepartment": "Select Department", "selectSkills": "Select Skills", "assignWorkerDialogMessage": "You are assigning {workerCount} workers to {shiftLeaderName}", "cancel": "Cancel", "confirm": "Confirm", "fullName": "Full Name"}, "newWorkers": {"selectAll": "Select all", "workerId": "Worker ID", "name": "Name", "surname": "Surname", "hiringDate": "Hiring Date", "category": "Category", "department": "Department", "function": "Function", "delete": "Delete", "importedDate": "Imported date", "addWorker": "Add Worker", "deleteWorker": "Delete Worker", "reason": "Reason", "confirmDelete": "Confirm", "importWorkers": "Import Workers", "importingFile": "Importing file", "cancel": "Cancel", "upload": "Upload", "addingNewWorker": "Adding new worker", "id": "ID", "operator": "operator", "assembly": "Assembly", "maintenance": "Maintenance", "logistics": "Logistics", "development": "Development", "engineering": "Engineering", "dh": "Hourly - Direct", "ih": "Hourly - Indirect", "option2": "Option 2", "option3": "Option 3", "add": "Add"}, "calendar": {"Weekend_short": "W", "Close": "Close", "Month": "Month", "Year": "Year", "AddEvent": "Add event", "ImportEvent": "Import event", "History": "History", "EventTitle": "Event Title", "Type": "Type", "StartDate": "Start date", "EndDate": "End date", "Description": "Description", "SaveEvent": "Save event", "Cancel": "Cancel", "PlantCalendar": "Plant calendar", "TotalDayWorking": "Total Working Days in", "Search": "Search", "MonthName": {"January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December"}, "WeekDays": {"Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat", "Sun": "Sun"}, "FormattedDate": "{day} {month} {year}"}, "table": {"search": "Search...", "noResults": "No results.", "showing": "Showing", "to": "to", "of": "of", "entries": "entries", "noEntries": "No entries", "page": "Page", "ofPages": "of", "noPages": "No pages", "previousPage": "Go to previous page", "nextPage": "Go to next page"}, "zoning": {"title": "Zoning", "search": "Search zoning...", "team": "Team", "teamLeader": "Team Leader", "shiftLeader": "Shift Leader", "coordinator": "Coordinator", "createTeam": "Create Team", "edit": "Edit", "save": "Save", "cancel": "Cancel", "create": "Create", "teamName": "Team Name", "selectTeamLeader": "Select Team Leader", "noTeamLeader": "No Team Leader selected", "noTeam": "No Team selected", "noShiftLeader": "No Shift Leader selected", "noCoordinator": "No Coordinator selected", "loading": "Loading...", "error": "Error", "success": "Success", "teamCreated": "Team created successfully", "teamUpdated": "Team updated successfully", "teamDeleted": "Team deleted successfully", "validation": {"required": "This field is required", "invalidTeam": "Invalid team configuration", "duplicateTeam": "Team already exists"}, "customer": "Customer", "project": "Project", "family": "Family/Zone", "valueStream": "Value Stream", "area": "Area", "selectCoordinator": "Select Coordinator", "selectShiftLeader": "Select Shift Leader", "selectTeam": "Select Team", "actions": "Actions", "projects": "Projects", "addTeam": "Add Team", "enterTeamName": "Enter team name", "confirm": "Confirm"}, "organizationChart": {"title": "Organization Chart", "loading": "Loading...", "assigned": "assigned", "id": "ID", "fullName": "Full Name", "function": "Function", "skills": "Skills", "n1": "N+1", "supervisor": "Supervisor", "shiftLeader": "Shift Leader", "teamLeader": "Team Leader", "operator": "Operator", "noSubordinates": "No subordinates", "expand": "Expand", "collapse": "Collapse", "search": "Search...", "noResults": "No results found", "name": "Name", "surname": "Surname"}, "line_assignment": {"unassignedWorkers": "Unassigned Workers", "finalAssembly": "Final Assembly:", "operationSkills": "Operation Skills", "selectOperationSkills": "Select operation skills", "idFullName": "ID / Full Name", "departProcess": "Depart / Process", "function": "Function", "selectWorkersToAssign": "Select Workers to Assign", "assignTo": "Assign to: {station}", "replaceWorker": "A worker is already assigned to station {station}. Do you want to replace the existing worker?", "assigningWorkers": "Assigning {operatorName} to {station}", "reassignOperator": "Are you sure you want to reassign from \"{oldName}\" to \"{newName}\"?", "cancel": "Cancel", "confirm": "Confirm", "id": "ID", "name": "Name", "surname": "Surname"}, "crew_dashboard": {"stationSubRole": "Station / SubRole", "role": "Role", "workstationSkills": "Workstation skills", "operatorId": "Operator ID", "name": "Name", "surname": "Surname", "actions": "Actions", "selectStation": "Select station", "selectOperator": "Select operator", "customer": "Customer", "selectCustomer": "Select customer", "project": "Program(Project)", "selectProject": "Select project", "family": "Family", "selectFamily": "Select family", "valueStream": "Value Stream", "selectValueStream": "Select value stream", "area": "Area", "selectArea": "Select area", "team": "Team", "selectTeam": "Select team", "meDefinition": "ME Definition", "meDefinitionEffectiveDate": "Me definition effective date", "notSelected": "Not selected", "addMsLine": "Add MFG line", "totalUnassigned": "Total unassigned:", "totalAssigned": "Total assigned:", "deleteWorkstation": "Are you sure you want to delete workstation \"{station}\"?", "reassignOperator": "Are you sure you want to reassign from \"{oldName}\" to \"{newName}\"?", "assignOperator": "Are you sure you want to assign \"{name}\" to this workstation?", "addMsLineConfirm": "Are you sure you want to add this MFG line?", "cancel": "Cancel", "confirm": "Confirm"}, "crew_management_page": {"assignWorkers": "Assign Workers", "assignWorkersWithCount": "Assign Workers ({count})"}, "training": {"scheduleTrainingSession": "Schedule training session", "skill": "Skill", "id": "ID", "firstName": "First Name", "lastName": "Last Name", "date": "Date", "phase": "Phase", "hiredDate": "Hired date", "score": "Score", "selectScore": "Select score", "selectFileToUpload": "Please select a file to upload", "noFileChosen": "No file chosen", "chooseFile": "Choose <PERSON>", "supportedFormats": "Supported formats", "cancel": "Cancel", "validate": "Validate", "validating": "Validating...", "close": "Close", "mie": "MIE", "department": "Department", "position": "Position", "area": "Area", "next": "Next", "scheduling": "Scheduling..."}, "clocking": {"cancelButton": "Cancel", "title": "Working Hours and TKS Profile", "workingHours": "Working Hours", "tksProfile": "TKS Profile", "addSchedule": "Add Schedule", "editSchedule": "Edit Schedule", "deleteSchedule": "Delete Schedule", "import": "Import", "export": "Export", "download": "Download", "downloadTemplate": "Download Template", "downloading": "Downloading...", "profile": "Profile", "code": "Code", "designation": "Designation", "timeEntry": "Entry Time", "timeExit": "Exit Time", "entryTime": "Entry Time", "exitTime": "Exit Time", "break": "Break", "breakDuration": "Break Duration", "workHours": "Work Hours", "day": "Day", "days": "Days", "scheduleType": "Schedule Type", "effectiveDate": "Effective Date", "effectiveDateRange": "Effective Date Range", "startDate": "Start Date", "endDate": "End Date", "normalDay": "Normal Day", "ramadan": "<PERSON><PERSON>", "redaDay": "Reda Day", "actions": "Actions", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "update": "Update", "create": "Create", "add": "Add", "remove": "Remove", "clear": "Clear", "apply": "Apply", "filter": "Filter", "search": "Search", "loading": "Loading...", "noData": "No data available", "noResults": "No results found", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirmation": "Confirmation", "createSchedule": "Create Schedule", "editProfil": "Edit Profile", "createNewProfile": "Create New Profile", "updateEvent": "Update Event", "saveEvent": "Save Event", "deleteEvent": "Delete Event", "confirmDelete": "Confirm Delete", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this schedule? This action cannot be undone.", "deleting": "Deleting...", "updating": "Updating...", "creating": "Creating...", "saving": "Saving...", "workingHoursActivated": "Working Hours Activated", "activateWorkingHours": "Activate Working Hours", "EffectiveDate": "Effective Date", "selectEffectiveDate": "Select Effective Date", "daysOfWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "filters": {"filterByCode": "Filter by Code", "filterByDay": "Filter by Day", "filterByWorkHours": "Filter by Work Hours", "filterByProfile": "Filter by Profile", "allDays": "All Days", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters"}, "table": {"profile": "Profile", "code": "Code", "designation": "Designation", "entryTime": "Entry Time", "exitTime": "Exit Time", "days": "Days", "break": "Break", "workHours": "Work Hours", "actions": "Actions", "noData": "No schedules found", "loading": "Loading schedules...", "itemsPerPage": "Items per page", "page": "Page", "of": "of", "previous": "Previous", "next": "Next", "first": "First", "last": "Last"}, "validation": {"required": "This field is required", "invalidTime": "Please enter a valid time", "invalidNumber": "Please enter a valid number", "invalidEmail": "Please enter a valid email address", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "min": "Minimum value is {min}", "max": "Maximum value is {max}"}, "messages": {"scheduleCreated": "Schedule created successfully", "scheduleUpdated": "Schedule updated successfully", "scheduleDeleted": "Schedule deleted successfully", "schedulesImported": "Schedules imported successfully", "templateDownloaded": "Template downloaded successfully", "effectiveDatesUpdated": "Effective dates updated successfully", "errorCreatingSchedule": "Error creating schedule", "errorUpdatingSchedule": "Error updating schedule", "errorDeletingSchedule": "Error deleting schedule", "errorImportingSchedules": "Error importing schedules", "errorDownloadingTemplate": "Error downloading template", "errorUpdatingEffectiveDates": "Error updating effective dates", "errorLoadingSchedules": "Error loading schedules", "errorLoadingScheduleTypes": "Error loading schedule types"}, "placeholders": {"enterProfile": "Enter profile", "enterCode": "Enter code", "enterDesignation": "Enter designation", "selectTime": "Select time", "enterBreakDuration": "Enter break duration in minutes", "enterWorkHours": "e.g., 8.5", "selectDays": "Select days", "searchCode": "Search by code...", "searchProfile": "Search by profile..."}, "tooltips": {"addSchedule": "Add a new schedule", "editSchedule": "Edit this schedule", "deleteSchedule": "Delete this schedule", "importSchedules": "Import schedules from file", "downloadTemplate": "Download import template", "setEffectiveDate": "Set effective date range", "filterSchedules": "Filter schedules", "clearFilters": "Clear all filters"}, "importModal": {"title": "Import Schedules", "uploadFileLabel": "Upload File", "acceptedFormats": "Accepted formats: CSV, XLSX", "invalidFileTypeTitle": "Invalid File Type", "invalidFileTypeDescription": "Please select a CSV or XLSX file.", "noFileSelectedTitle": "No File Selected", "noFileSelectedDescription": "Please select a file to import.", "importButton": "Import", "importingButton": "Importing...", "importSuccessfulTitle": "Import Successful", "importSuccessfulDescription": "Imported {importedCount} schedules successfully.", "importCompletedWithErrorsTitle": "Import Completed with Errors", "importCompletedWithErrorsDescription": "Import completed with {importedCount} imported and {errorsLength} errors."}}, "alerts_notifications": {"title": "Alerts & Notifications", "tabs": {"alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications"}, "search": {"placeholder": "Search alerts and notifications..."}, "filters": {"all": "All", "approval": "Approval", "redirect": "Redirect", "waiting": "Waiting treatment", "approved": "Approved", "rejected": "Rejected", "information": "Information", "warning": "Warning", "error": "Error"}, "table": {"headers": {"title": "Title", "requesterID": "Requester ID", "requesterName": "Requester Name", "type": "Type", "datetime": "Date & Time", "details": "Details"}, "actions": {"view_details": "View details", "approve": "Approve", "reject": "Reject"}, "empty": "No alerts or notifications found"}, "status": {"waiting_treatment": "Waiting treatment", "approved": "Approved", "rejected": "Rejected", "generated": "Generated"}, "types": {"absence_authorization": "Absence authorization", "leave_request": "Request for leave (Holidays)", "information": "Information", "warning": "Warning", "error": "Error"}, "tracking": {"title": "Tracking", "steps": {"request_submitted": "Request submitted", "request_processing": "Request being processed", "waiting_approval": "Waiting approval", "request_approved": "Request approved", "request_rejected": "Request rejected", "notification_generated": "Notification generated"}, "actors": {"system": "System", "shift_leader": "Shift Leader", "you": "you"}, "submitted_by": "Submitted by {name}", "approved_by": "by {role}", "rejected_by": "by {role}", "pending_approval": "by {name}"}, "modal": {"title": "<PERSON><PERSON>", "close": "Close", "sections": {"general": "General Information", "details": "Details", "tracking": "Tracking", "response": "Response"}, "fields": {"title": "Title", "type": "Type", "status": "Status", "requester": "Requester", "request_date": "Request Date", "duration": "Duration", "from_date": "From Date", "to_date": "To Date", "details": "Details"}, "response": {"title": "Your Response", "approve": "Approve", "reject": "Reject", "comment_placeholder": "Add your comment here...", "submit": "Submit Response"}}}, "versatilityMatrix": {"title": "Versatility Matrix", "qualificationsTitle": "Qualifications Matrix", "polyvalenceTitle": "Polyvalence Matrix", "legendTitle": "Versatility Legend", "qualificationsLegendTitle": "Qualifications Legend", "operators": "Operators", "legend": {"level1": "< 1 month", "level2": "1 to 2 months", "level3": "2 to 6 months", "level4": "> 6 months", "currentPosition": "Current position", "polyvalencePlanning": "Polyvalence planning", "normal": "Normal", "medium": "Medium", "critical": "Critical"}, "qualifications": {"ojt": "OJT", "learningCurve": "Learning curve", "trained": "Trained operator", "validated": "Validated operator", "certified": "Certified operator", "recertified": "Re-certified operator", "notQualified": "Not qualified"}, "table": {"headers": {"number": "N°", "mle": "<PERSON><PERSON>", "firstName": "First Name", "lastName": "Last Name", "position": "Position", "hiredDate": "Hired date", "postes": "POSTES", "qualificationsRequired": "Required Qualifications", "criticity": "Criticity", "target": "Target", "actual": "Actual"}, "tooltips": {"requiredSkills": "Required Skills: {skills}", "currentPositionNotEditable": "Current position - not editable", "level1": "< 1 month", "level2": "1 to 2 months", "level3": "2 to 6 months", "level4": "> 6 months", "currentPosition": "Current position", "polyvalencePlanning": "Polyvalence planning", "normal": "Normal", "medium": "Medium", "critical": "Critical"}}, "buttons": {"export": "Export"}, "loading": "Loading versatility matrix...", "qualificationsLoading": "Loading qualification matrix...", "navigation": {"search": "Search...", "projects": "Projects", "families": "Families", "valueStreams": "Value Streams", "areas": "Areas", "items": "Items", "clickToView": "Click to view it bellow", "selectCustomer": "Select a Customer to see their projects here", "selectProject": "Select a Project to see their families here", "selectFamily": "Select a Family to see their value streams here", "selectValueStream": "Select a Value Stream to see their areas here", "selectMatrix": "Select an Area to access the matrix", "selectA": "Select a", "titles": {"customers": "Customers", "projects": "Projects", "families": "Families", "valueStream": "Value Stream", "area": "Area", "versatilityMatrix": "Versatility Matrix", "qualificationsMatrix": "Qualifications Matrix", "polyvalenceMatrix": "Polyvalence Matrix"}}, "breadcrumb": {"versatilityMatrix": "Versatility Matrix", "qualificationMatrix": "Qualification Matrix"}, "departments": {"finalAssemblyDepartment": "Final assembly department", "cutingLeadPrepDepartment": "Cuting & lead prep department"}, "emptyState": {"selectMatrixTitle": "Select a Matrix Type From Navigation", "selectMatrixDescription": "Please select one of the matrix types from the navigation cards above", "selectAreaFirst": "First select an area, then select a matrix type from the navigation cards above"}, "loadingStates": {"matrixDataTitle": "Loading Matrix Data...", "matrixDataDescription": "Please wait while we fetch the latest data from the server."}}, "confirmation": {"areYouSureSubmit": "Are you sure you want to submit this evaluation?", "submissionWarning": "Once submitted, the evaluation and score will be saved and cannot be modified. Please make sure all fields are completed correctly.", "cancel": "Cancel", "confirm": "Confirm", "confirming": "Confirming...", "close": "Close"}, "success": {"evaluationSubmittedSuccessfully": "Evaluation Submitted Successfully", "evaluationSaved": "Your evaluation has been saved. The operator scored", "passMessage": "Pass and meets all performance expectations.", "failMessage": "Fail and requires additional training.", "done": "Done", "close": "Close"}, "common": {"search": "Search...", "noResultsFound": "No results found.", "selectOption": "Select an option"}, "commonCalendar": {"search": "Search...", "noResultsFound": "No results found.", "selectOption": "Select option"}, "rolesPermissions": {"title": "Roles & Permissions", "searchPlaceholder": "Search a role...", "addNewRole": "Add a new role", "download": "Download", "downloadTemplate": "Download Template", "downloading": "Downloading...", "deletingRole": "Deleting role...", "columns": {"fullNameRoleId": "Full Name / Role/ID", "permissions": "Permissions", "adGroups": "AD groups (Roles)", "description": "Description", "actions": "Actions"}, "actions": {"editRole": "Edit role", "copyRole": "Copy role", "deleteRole": "Delete role", "close": "Close"}, "modal": {"editTitle": "Edit Role & Permissions", "duplicateTitle": "Duplicate Role & Permissions", "addTitle": "Add New Role & Permissions", "loadingRoleData": "Loading role data...", "tabs": {"roleDetails": "Role details", "permissions": "Permissions", "adGroups": "AD Groups (Roles)"}, "details": {"createMessage": "Define a new role with appropriate permissions and AD group mappings.", "editMessage": "Edit the role details, permissions, and AD group mappings.", "duplicateMessage": "Create a new role based on the selected role. The permissions and AD groups have been copied. Please update the Role ID and Name as needed.", "roleId": "Role ID", "roleIdPlaceholder": "Enter role ID", "roleIdHelp": "Uppercase letters, numbers, and underscores only.", "name": "Name", "namePlaceholder": "Enter role name", "description": "Description", "descriptionPlaceholder": "Enter description", "required": "*"}, "permissions": {"title": "Permission Assignment", "description": "Select the permissions to assign to this role. Users with this role will be able to perform these actions.", "searchPlaceholder": "Search permissions...", "selectAll": "Select All", "unselectAll": "Unselect All", "loadingPermissions": "Loading permissions...", "noPermissionsFound": "No permissions found"}, "adGroups": {"title": "Azure AD Group Mapping", "description": "Map Azure AD security groups to this role. Users in these groups will automatically be assigned this role.", "searchPlaceholder": "Search Azure AD groups...", "selectAll": "Select All", "unselectAll": "Unselect All", "groups": {"systemAdministrators": "System Administrators", "systemAdministratorsDesc": "Global administrators of the system", "executiveLeadership": "Executive Leadership", "executiveLeadershipDesc": "Executive leadership team", "plantManagers": "Plant Managers", "plantManagersDesc": "All plant managers", "shiftLeaders": "Shift Leaders", "shiftLeadersDesc": "All shift leaders", "teamLeaders": "Team Leaders", "teamLeadersDesc": "All team leaders", "plantOperators": "Plant Operators", "plantOperatorsDesc": "All plant operators", "engineers": "Engineers", "engineersDesc": "Engineering staff"}}, "buttons": {"cancel": "Cancel", "updateRole": "Update role", "updating": "Updating...", "duplicateRole": "Duplicate role", "duplicating": "Duplicating...", "createRole": "Create role", "creating": "Creating..."}}, "success": {"title": "Success!", "message": "The role {roleId} has been {operation} successfully", "operations": {"created": "created", "duplicated": "duplicated", "updated": "updated"}, "close": "Close"}, "delete": {"title": "Delete Role", "message": "Are you sure you want to delete the role \"{roleName}\"?", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting..."}, "selectOption": "Select option", "calendarHeader": {"plantCalendar": "Plant Calendar {year}", "searchEventsPlaceholder": "Search events...", "addEventButton": "Add Event", "importEventsButton": "Import Events", "historyButton": "History", "loadingCategories": "Loading categories...", "totalWorkingDaysIn": "Total Working Days in {year}", "monthView": "Month", "yearView": "Year"}, "importModal": {"title": "Import Events", "uploadFileLabel": "Upload File", "acceptedFormats": "Accepted formats: .csv, .xlsx", "invalidFileTypeTitle": "Invalid File Type", "invalidFileTypeDescription": "Please select a .csv or .xlsx file.", "noFileSelectedTitle": "No File Selected", "noFileSelectedDescription": "Please select a file to import.", "importCompletedWithErrorsTitle": "Import Completed with Errors", "importCompletedWithErrorsDescription": "Successfully imported {importedCount} events, but {errorsLength} rows had issues.", "importSuccessfulTitle": "Import Successful", "importSuccessfulDescription": "Successfully imported {importedCount} events.", "importingButton": "Importing...", "importButton": "Import"}, "eventDetailsModal": {"titleLabel": "Title :", "dateLabel": "Date :", "typeLabel": "Type :", "descriptionLabel": "Description :"}, "calendarView": {"weekHeader": "W", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun"}, "eventsHistorySidebar": {"title": "Events history - {year}", "infoText": "You can track all changes made to the calendar — including added, edited, or deleted events.", "actionLabel": "Action", "selectActionPlaceholder": "Select action", "allActions": "All", "actionCreated": "Entire event added", "actionUpdated": "Event updated", "actionDeleted": "Event deleted", "actionImported": "Events imported", "dateLabel": "Date", "pickDatePlaceholder": "Pick a date", "loadingHistory": "Loading history...", "noHistoryFound": "No history events found", "tryChangingFilter": "Try changing the filter criteria", "todayGroup": "Today", "yesterdayGroup": "Yesterday", "earlierGroup": "Earlier", "loadMoreButton": "Load more...", "loadingButton": "Loading..."}, "eventHistoryItem": {"dateTimeLabel": "Date & Time:", "eventLabel": "Event:", "actionLabel": "Action:", "oldValueLabel": "Old Value:", "newValueLabel": "New Value:", "doneByLabel": "Done by:"}, "deleteModal": {"title": "Delete Event", "deletingButton": "Deleting...", "deleteButton": "Delete Event", "confirmationTitle": "Are you sure you want to delete this event?", "confirmationMessage": "This action cannot be undone. Once deleted, {eventName} will be permanently removed from the calendar.", "deleteError": "Failed to delete event. Please try again."}, "eventModal": {"editTitle": "Edit Event", "createTitle": "Create New Event", "eventNameLabel": "Event Name *", "eventNamePlaceholder": "Enter event name", "categoryLabel": "Category", "selectCategoryPlaceholder": "Select category", "startDateLabel": "Start Date", "endDateLabel": "End Date", "pickDatePlaceholder": "Pick a date", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter event description", "updatingButton": "Updating...", "creatingButton": "Creating...", "updateButton": "Update Event", "saveButton": "Save Event", "nameRequiredAlert": "Event name is required", "saveErrorAlert": "Failed to save event. Please try again."}, "cancelButton": "Cancel", "closeButton": "Close", "deleteButton": "Delete", "editButton": "Edit"}, "timeAgo": {"justNow": "just now", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "month": "month", "months": "months", "year": "year", "years": "years", "ago": "ago", "in": "in"}, "toast": {"notificationTitle": "🔔 New Notification", "alertTitle": "🚨 New Alert"}, "nurseAbility": {"confirmInability": "Confirm inability", "confirmInabilityMessage": "Are you sure you want to confirm inability?", "idWorker": "ID Worker", "medicalDecision": "Medical decision", "cancel": "Cancel", "confirm": "Confirm", "submitting": "Submitting...", "statementOfInability": "Statement of inability", "searchWorker": "Search for a worker", "id": "ID", "firstName": "First name", "lastName": "Last name", "natureOfIllness": "Nature of illness", "validate": "Validate", "stopInability": "Stop inability", "confirmCancelInability": "Are you sure to cancel the inability?", "stopping": "Stopping...", "listOfOperators": "List of operators", "sortBy": "Sort by", "date": "Date", "loadingEmployees": "Loading employees...", "name": "Name", "surname": "Surname", "declarationDate": "Declaration date", "operatorDetails": "Operator Details", "employeeId": "Employee ID", "reasonForInability": "Reason for Inability", "createdAt": "Created At"}, "disciplinary_measures": {"title": "Disciplinary Measures", "submit_title": "Submit Disciplinary Measure", "id": "ID", "firstName": "First Name", "lastName": "Last Name", "function": "Function", "department": "Department", "category": "Category", "status": "Status", "startDate": "Start Date", "endDate": "End Date", "days": "Nbr of Days", "actions": "Action", "edit": "Edit", "delete": "Delete", "openMenu": "Open menu", "operator_id": "Operator ID", "operator_id_placeholder": "9865", "first_name": "First Name", "first_name_placeholder": "First name", "last_name": "Last Name", "last_name_placeholder": "Last name", "department_placeholder": "Department", "start_date": "Start Date", "number_of_days": "Number of Days", "number_of_days_placeholder": "Enter days", "end_date": "End Date", "comment": "Comment", "comment_placeholder": "Enter your comment here...", "reason": "Reason", "reason_placeholder": "Enter the reason for disciplinary measure...", "disciplinary_type": "Disciplinary Type", "select_disciplinary_type": "Select type", "edit_title": "Edit Disciplinary Measure", "update": "Update", "saving": "Saving...", "file_upload": "Please select a file to upload", "file_upload_button": "Choose <PERSON>", "file_no_file_chosen": "No file chosen", "file_files_selected": "{count} file{plural} selected", "file_supported_formats": "Supported formats: ", "file_remove": "Remove", "file_maximum_files": "Maximum {maxFiles} files allowed", "save_success_title": "Success", "save_success_message": "Disciplinary measure has been saved successfully.", "save_error_title": "Error", "save_error_message": "Failed to save disciplinary measure. Please try again.", "close": "Close", "save": "Save", "delete_confirmation": "Are you sure you want to delete this record?", "search_placeholder": "Search by department, category, employee", "filter": "Filter", "sort_by": "Sort by", "date": "Date", "name": "Name", "export": "Export", "search": "Search", "showing_results": "Showing {filteredItems} of {totalItems} results", "total_results": "{totalItems} total results", "view": "View", "confirm_delete": "Are you sure you want to delete this disciplinary measure?", "confirm_delete_title": "Confirm Deletion", "confirm_delete_message": "Are you sure you want to delete this disciplinary measure? This action cannot be undone.", "confirm": "Confirm", "delete_success": "Deleted Successfully", "delete_success_message": "The disciplinary measure has been deleted successfully.", "delete_error": "Delete Failed", "delete_error_message": "Failed to delete the disciplinary measure. Please try again.", "error": "Error", "loading": "Loading...", "no_data": "No data available"}, "workcenterList": {"childlineName": "Childline Name", "workcenterName": "Workcenter Name", "workcenterCriticality": "Workcenter Criticality", "normal": "Normal", "critical": "Critical", "medium": "Medium", "value_stream": "Value Stream", "area": "Area", "customer": "Customer", "project": "Project", "family": "Family", "skillsReq": "Skills Requirement", "saveCriticality": "Save Criticality", "loading": "Loading..."}, "transportPlanning": {"title": "Transport Planning", "create": "Create Transport Planning", "edit": "Edit Transport Planning", "delete": "Delete Transport Planning", "view": "View Transport Planning"}, "skillList": {"skillId": "Skill ID", "description": "Description", "shortDesc": "Short Description", "criticality": "Criticality", "BasicProcess": "Basic Process", "CriticalProcess": "Critical Process", "actions": "Actions", "search": "Search", "loading": "Loading...", "common": {"import": "Import", "export": "Export"}}, "project-hierarchy": {"customer": {"title": "Customer", "description": "Select a Customer or click on + to create new customer ", "selectPlaceholder": "Select customer...", "addNew": "Add new customer", "customerName": "Customer name: "}, "project": {"title": "Project name", "description": "Select a Project or click on + to create new project ", "projectName": "Project name : ", "selectPlaceholder": "Select project...", "addNew": "Add new project"}, "families": {"title": "Families", "createFamily": "Create a family"}, "valueStream": {"title": "Value Stream (Line)", "createValueStream": "Value Stream (Line)", "createFamilyFirst": "! Create a family first"}}, "clocking-validation": {"pageTitle": "Clocking Validation", "statistics": {"totalAttendanceSheets": "Total received attendance sheets", "waitingForValidation": "Waiting for validation", "validated": "Validated", "validatedWithComment": "Validated with comment"}, "table": {"date": "Date", "department": "Department", "shift": "Shift", "role": "Role", "id": "ID", "firstLastName": "First, Last name", "firstName": "First Name", "lastName": "Last Name", "approver": "Approver", "status": "Status", "comment": "Comment", "closed": "Closed", "attendanceRecords": "Attendance Records"}, "filters": {"searchPlaceholder": "Search by ID, first name, or last name...", "selectDate": "Select Date", "selectDepartment": "Select Department", "allDepartments": "All Departments", "all": "All", "notClosed": "Not Closed"}, "actions": {"viewDetails": "View Details", "quickApprove": "Quick Approve", "approveWithComment": "Approve with Comment", "export": "Export this teams list", "submitClockingValidation": "Submit clocking validation with comment", "reviewFinalization": "Review all attendance statuses and comments before finalizing. Confirm the details before submitting your validation."}, "departments": {"assembly": "Assembly", "production": "Production", "qualityControl": "Quality Control", "maintenance": "Maintenance", "logistics": "Logistics"}, "shifts": {"morning": "Morning", "evening": "Evening", "night": "Night"}, "roles": {"teamLeader": "Team leader", "operator": "Operator", "inspector": "Inspector", "technician": "Technician", "coordinator": "Coordinator"}, "status": {"waitingForValidation": "Waiting for validation", "validated": "Validated", "validatedWithComment": "Validated with comment", "rejected": "Rejected"}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next"}, "loading": "Loading...", "noResults": "No results.", "modal": {"attendanceRecordDetails": "Attendance Record Details", "employeeInformation": "Employee Information", "validationActions": "Validation Actions", "addCommentOptional": "Add Comment (Optional)", "addCommentPlaceholder": "Add a comment for this validation...", "approve": "Approve", "approveWithComment": "Approve with Comment", "reject": "Reject", "close": "Close", "preview": "Preview", "previewValidationComment": "Preview of Validation with a comment", "validateAttendanceWithComment": "Validate Attendance with a Comment"}}}