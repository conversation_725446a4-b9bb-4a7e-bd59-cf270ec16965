{"evaluation": {"scheduleTrainingSession": "Planifier une session de formation", "trainingSessionEvaluation": "Évaluation de la session de formation", "evaluationInstructions": "Veuillez évaluer la performance de l'opérateur selon les catégories suivantes. Sélectionnez l'énoncé le plus approprié pour chacune.", "totalScore": "Score total", "status": "Statut", "pass": "<PERSON><PERSON><PERSON><PERSON>", "fail": "Échec", "passMessage": "Excellent ! L'opérateur répond aux attentes dans toutes les catégories d'évaluation. Aucune formation supplémentaire n'est nécessaire pour le moment.", "failMessage": "L'opérateur nécessite une formation supplémentaire pour répondre aux normes de performance.", "back": "Retour", "calculateScore": "Calculer le score d'évaluation", "submit": "So<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "HomePage": {"title": "Bienvenue", "subtitle": "Ceci est votre page d'accueil Aptiv Connected Workers"}, "ProfileCard": {"myprofile": "Mon profil", "employeeNumber": "Numéro <PERSON>", "position": "Poste", "department": "Département", "location": "Emplacement", "joinDate": "Date d'entrée", "phone": "<PERSON><PERSON><PERSON>", "manager": "Manager", "email": "Email"}, "LeaveBalance": {"title": "Solde des congés", "daysRemaining": "Jours restants", "days": "jours"}, "OvertimeHours": {"title": "Heures supplémentaires", "hoursWorked": "Heures travaillées", "hours": "heures"}, "LatestRequests": {"title": "Dernières demandes", "noRequests": "Aucune demande récente", "status": {"pending": "En attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "generated": "<PERSON><PERSON><PERSON><PERSON>"}, "viewAll": "Voir tout"}, "DocumentRequest": {"title": "Demande de document administratif", "selectDocument": "Sélectionner un document", "selectedRequest": "<PERSON><PERSON><PERSON>", "submitRequest": "Soumettre la demande", "requestSummary": "R<PERSON><PERSON><PERSON> demande", "confirmRequest": "Confirmer la demande", "reviewDetails": "Veuillez vérifier les détails de votre demande", "back": "Retour", "confirmationMessage": "Vous avez fait une demande de certificat de travail. V<PERSON><PERSON>z confirmer si vous souhaitez procéder à cette demande ou l'annuler.", "successMessage": "Votre demande a été envoyée avec succès. Vous serez notifié quand le document sera prêt", "cancel": "Annuler", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours...", "home": "Accueil", "trackingRequests": "Suivi des demandes", "documentNotFound": "Document non trouvé", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>"}, "AbsenceRequest": {"title": "<PERSON><PERSON><PERSON> d'absence", "selectType": "Sélectionner un type d'absence", "submitRequest": "Soumettre la demande", "selectPlaceholder": "Sélectionner une demande", "selectedRequestLabel": "Type de demande sélectionné", "insertInformation": "Veuillez saisir les informations", "close": "<PERSON><PERSON><PERSON>", "requestSummary": "R<PERSON><PERSON><PERSON> demande", "confirmRequest": "Confirmer la demande", "reviewDetails": "Veuillez vérifier les détails de votre demande", "back": "Retour", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours...", "successTitle": "Su<PERSON>ès", "errorTitle": "<PERSON><PERSON><PERSON>", "unexpectedError": "Une erreur inattendue s'est produite"}, "OtherRequest": {"title": "Autres demandes", "selectRequest": "Sélectionner une demande", "selectPlaceholder": "Sélectionner d'autres demandes", "selectedRequestLabel": "<PERSON><PERSON><PERSON> s<PERSON>lect<PERSON> :", "submitRequest": "Soumettre la demande", "requestSummary": "R<PERSON><PERSON><PERSON> demande", "confirmRequest": "Confirmer votre demande", "reviewDetails": "Veuillez vérifier les détails de la demande et confirmer ou apporter les modifications nécessaires.", "cancel": "Annuler", "back": "Retour", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours...", "successTitle": "Su<PERSON>ès", "errorTitle": "<PERSON><PERSON><PERSON>", "requestNotFound": "De<PERSON>e sélectionnée non trouvée"}, "Common": {"close": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "done": "<PERSON><PERSON><PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "successMessage": "Votre demande a été soumise avec succès !", "errorMessage": "Une erreur est survenue. Veuillez réessayer."}, "header": {"user": {"title": "Mon Compte", "profile": "Profil", "settings": "Paramètres", "logout": "Déconnexion"}, "notifications": {"latestAlerts": "Dernières Alertes", "latestNotifications": "Dernières Notifications", "viewAllAlerts": "Voir toutes les alertes", "viewAllNotifications": "Voir toutes les notifications"}, "breadcrumbs": {"home": "Accueil", "dashboard": "Tableau de bord", "adminConfiguration": "Configuration Admin", "formsManagement": "Gestion des Formulaires", "documentsTemplates": "Modèles de Documents", "newDocumentTemplate": "Nouveau Modèle de Document", "newForm": "Nouveau Formulaire", "requestsConfigurations": "Configurations des Demandes", "newWorkflow": "Nouveau Flux de Travail", "crewManagement": "Affectation des Lignes", "newWorkers": "Nouveaux Travailleurs", "dhWalk": "DH Walk", "calendar": "calendrier", "zoning": "Zonage", "organizationChart": "Organigramme", "assignmentChart": "Tableau d'Affectation", "myTeam": "Mon Equipe", "clockingHours": "Heures de Pointage", "disciplinaryMeasure": "Mesures Disciplinaires", "versatilityMatrix": "<PERSON><PERSON>", "alertNotification": "Alertes & Notifications", "TrackingRequests": "Documents et demandes", "overtime": "Overtime", "plannedExecuted": "Planned vs Executed", "report": "Report", "plannification": "Plannification", "training": "Formation", "trainingList": "Liste de Formation", "trainingWorkflow": "Workflow de Formation", "nurse": "<PERSON><PERSON>rm<PERSON>", "projectHierarchy": "Hiérarchie du Projet", "clockingValidation": "Validation du Pointage"}}, "sidebar": {"title": "APTIV", "subtitle": "CONNECTED WORKERS", "navigation": {"home": "Accueil", "documents_templates": "Modèles de Documents", "forms_management": "Gestion des Formulaires", "requests_configurations": "Configuration des Demandes", "notifications": "Notifications", "my_profile": "Mon Profil", "logout": "Déconnexion", "new_workers": "Nouveaux Travailleurs", "assignment_chart": "Tableau d'Affectation", "organization_chart": "Organigramme", "crew_management": "Gestion des équipes", "line_assignment": "Affectation des lignes", "zoning": "Zonage", "calendar": "<PERSON><PERSON><PERSON>", "my_team": "Mon Equipe", "disciplinary_measure": "Mesures Disciplinaires", "versatility_matrix": "<PERSON><PERSON>", "alert_notification": "Alertes & Notifications", "tracking_requests": "<PERSON><PERSON><PERSON> de suivi", "overtime": "Overtime", "planned-executed": "Planned vs Executed", "report": "Report", "plannification": "Plannification", "training": "Formation", "training_list": "Liste de Formation", "training_workflow": "Workflow de Formation", "clocking_hours": "Heures de Pointage", "workcenter_criticity": "Criticité du Centre de Travail", "project-hierarchy": "Hiérarchie du Projet", "skills": "Compétences", "clocking_validation": "Validation du Pointage"}, "admin": {"configuration": "Configuration admin"}}, "formsManagement": {"home": {"Addbutton": "Ajouter un nouveau formulaire", "title": "Titre des formulaires", "discription": "Description", "category": "<PERSON><PERSON><PERSON><PERSON>", "createdDate": "Date de création", "updatedDate": "Date de mise à jour", "preview": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirmation": "Voulez-vous supprimer ce formulaire ?"}}, "Overtime": {"Plannification": {"cancel": "Cancel", "save": "Save", "allStatus": "Tout statut", "pending": "En attente", "approved": "A<PERSON><PERSON><PERSON><PERSON>", "rejected": "Rejetée", "createOvertime": "Create Overtime", "requestApproved": "<PERSON><PERSON><PERSON> approuvé"}, "PlannedVsExecuted": {"showAll": "Show All", "onlyMatches": "Only Matches"}}, "TrackingRequests": {"title": "Tracking Requests", "noResults": "Aucun résultat trouvé", "myRequests": "Me<PERSON> demandes", "requestsOfMyTeam": "De<PERSON><PERSON> de mon équipe", "administrativeDocuments": "Documents administratifs", "abssenceRequests": "Demandes d'absence", "otherRequests": "Autres demandes", "waitingApproval": "En attente d'approbation", "detailModal": {"title": "<PERSON><PERSON><PERSON> de la demande", "loading": "Chargement des détails de la demande...", "error": "Échec du chargement des détails de la demande", "requestType": "Type de demande", "requestDate": "Date de demande", "requester": "De<PERSON>eur", "status": "Statut", "pendingApproval": "en attente d'approbation", "requestInformation": "Informations de la demande", "reason": "<PERSON>son", "approvalTimeline": "Chronologie d'approbation", "requestSubmitted": "<PERSON><PERSON><PERSON> soumise", "submittedBy": "Soumise par", "yourActionRequired": "Votre action requise", "approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "viewDetails": "Voir les détails"}}, "documentsTemplates": {"home": {"Addbutton": "Créer un nouveau modèle de document", "title": "Titre des modèles de documents", "documentType": "Type de document", "discription": "Description", "createdDate": "Date de création", "actions": "Actions", "edit": "É<PERSON>er", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirmation": "Voulez-vous supprimer ce modèle de document ?"}, "NewDocumentTemplate": {"title": "Nouveau Modèle de Document", "documentTemplateBuilder": "Constructeur de Modèles de Document", "ImportDocumentTemplate": "Importer un Modèle de Document", "documentType": "Type de document", "administrativeDocument": "Document administratif", "documentTemplateName": "Nom du modèle de document", "description": "Description", "save": "Enregistrer", "systemInformations": "Informations système", "copied": "Copié!", "dataInput": "Entr<PERSON> de donn<PERSON>", "searchForm": "Rechercher un formulaire", "signatures": "Signatures", "hierarchyFields": "Champs de hiérarchie", "searchHierarchyFields": "Rechercher des champs de hiérarchie", "cancel": "Annuler", "upload": "Télécharger", "userInformation": "Informations utilisateur", "formsList": "Liste des formulaires", "systemInformation": "Informations système", "searchUserFields": "Rechercher des champs utilisateur", "searchSystemFields": "Rechercher des champs système", "searchAllFormFields": "Rechercher tous les champs de formulaire", "searchSignatures": "Rechercher des signatures"}, "NewForm": {"title": "Nouveau Formulaire", "form": "Formulaire", "description": "Description", "category": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer"}}, "requestsConfigurations": {"home": {"title": "Configurations des Demandes", "addButton": "Ajouter un nouveau flux de travail", "status": "Statut", "description": "Description", "createdDate": "Date de création", "updatedDate": "Date de mise à jour"}, "newWorkflow": {"title": "<PERSON><PERSON>re du <PERSON>", "description": "Description", "status": "Statut", "save": "Enregistrer", "toolBox": "Boîte à outils", "properties": "Propriétés", "deleteNode": "<PERSON><PERSON><PERSON><PERSON> le nœud", "start": "DÉBUT", "document": "DOCUMENT", "approvals": "APPROBATIONS", "singleApproval": "APPROBATION UNIQUE", "multipleApprovals": "APPROBATIONS MULTIPLES", "notification": "NOTIFICATION", "condition": "CONDITION", "finish": "FIN", "clearFlow": "Effacer le flux"}}, "CrewManagement": {"assignWorkers": "Affecter des travailleurs", "selectedTeamLeader": "Chef d'é<PERSON>pe s<PERSON>", "selectedTrainer": "Formateur sélectionné", "unassignedWorkers": "Travailleurs non affectés", "operatorsSkills": "Compétences des opérateurs", "department": "Département", "category": "<PERSON><PERSON><PERSON><PERSON>", "function": "Fonction", "selectWorkersToAssign": "Sélectionner les travailleurs à affecter", "assignTo": "Affecter à", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select10": "Sélectionner 10", "select20": "Sélectionner 20", "select50": "Sélectionner 50", "clear": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "confirm": "Confirmer", "deleteSelected": "Supprimer la sélection", "areYouSureDelete": "Êtes-vous sûr de vouloir supprimer", "workers": "travailleurs", "worker": "travailleur", "nPlusOne": "N+1", "supervisor": "Superviseur", "fullName": "Nom complet", "process": "Processus", "station": "Station", "workstationSkills": "Compétences de poste de travail", "id": "ID", "categoryDepartment": "Catégorie / Département", "actions": "Actions", "assignButton": "Affecter", "closeButton": "<PERSON><PERSON><PERSON>", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "deleteDialogTitle": "Confirmation de suppression", "deleteDialogMessage": "Êtes-vous sûr de vouloir supprimer les travailleurs sélectionnés?", "deleteDialogConfirm": "Confirmer", "deleteDialogCancel": "Annuler", "assignDialogTitle": "Affecter des travailleurs", "assignDialogMessage": "Vous affectez {count} travailleurs à {trainer}", "assignDialogConfirm": "Confirmer", "assignDialogCancel": "Annuler"}, "crew_management": {"shiftLeader": "Chef d'é<PERSON>pe", "trainer": "Formateur", "operator": "Opérateur", "assigned": "assigné", "deleteSelected": "Supprimer la sélection", "id": "ID", "name": "Prénom", "surname": "Nom", "deleteConfirmSingle": "Êtes-vous sûr de vouloir supprimer cette personne ?", "deleteConfirmMultiple": "Êtes-vous sûr de vouloir supprimer ces personnes ?", "selectedShiftLeader": "Chef d'é<PERSON>pe s<PERSON>", "n1": "N+1", "supervisor": "Superviseur", "unassignedWorkers": "Travailleurs non attribués", "department": "Département", "skills": "Compétences", "selectWorkersToAssign": "Sélectionner des travailleurs à attribuer", "clearSelection": "Effacer la sélection", "assignTo": "Attribuer à", "function": "Fonction", "skillsDepartment": "Compétences / Département", "confirmAssignment": "Confirmer l'attribution", "workerAssignment": "Attribution de travailleur", "IDFullName": "ID / Nom complet", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "selectCount": "Sélectionner le nombre", "select10": "Sélectionner 10", "select20": "Sélectionner 20", "select50": "Sélectionner 50", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorkersMessage": "Travailleurs sélectionnés {selectedWorkersCount}/{totalWorkersCount}", "selectDepartment": "Sélectionner le département", "selectSkills": "Sélectionner les compétences", "assignWorkerDialogMessage": "Vous attribuez {workerCount} travailleurs à {shiftLeaderName}", "cancel": "Annuler", "confirm": "Confirmer", "fullName": "Nom complet"}, "newWorkers": {"selectAll": "<PERSON><PERSON>", "workerId": "ID du travailleur", "name": "Prénom", "surname": "Nom", "hiringDate": "Date d'embauche", "category": "<PERSON><PERSON><PERSON><PERSON>", "department": "Département", "function": "Fonction", "delete": "<PERSON><PERSON><PERSON><PERSON>", "importedDate": "Date d'importation", "addWorker": "Ajouter un travailleur", "importWorkers": "Importer des travailleurs", "importingFile": "Importation de fichier", "cancel": "Annuler", "upload": "Télécharger", "addingNewWorker": "Ajout d'un nouveau travailleur", "id": "ID", "operator": "Opérateur", "assembly": "Assemblage", "dh": "DH", "option2": "Option 2", "option3": "Option 3", "add": "Ajouter"}, "calendar": {"Weekend_short": "S", "Close": "<PERSON><PERSON><PERSON>", "Month": "<PERSON><PERSON>", "Year": "<PERSON><PERSON>", "AddEvent": "Ajouter un événement", "ImportEvent": "Importer un événement", "History": "Historique", "EventTitle": "Titre de l'événement", "Type": "Type", "StartDate": "Date de début", "EndDate": "Date de fin", "Description": "Description", "SaveEvent": "Enregistrer l'événement", "Cancel": "Annuler", "PlantCalendar": "<PERSON><PERSON><PERSON> de l'usine", "TotalDayWorking": "Total des jours ouvrables en", "Search": "<PERSON><PERSON><PERSON>", "MonthName": {"January": "<PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON><PERSON>", "March": "Mars", "April": "Avril", "May": "<PERSON>", "June": "Juin", "July": "<PERSON><PERSON><PERSON>", "August": "Août", "September": "Septembre", "October": "Octobre", "November": "Novembre", "December": "Décembre"}, "WeekDays": {"Mon": "<PERSON>n", "Tue": "Mar", "Wed": "<PERSON><PERSON>", "Thu": "<PERSON><PERSON>", "Fri": "Ven", "Sat": "Sam", "Sun": "<PERSON><PERSON>"}, "FormattedDate": "{day} {month} {year}"}, "table": {"search": "Rechercher...", "noResults": "Aucun résultat.", "showing": "Affichage de", "to": "à", "of": "sur", "entries": "entrées", "noEntries": "<PERSON><PERSON><PERSON> entrée", "page": "Page", "ofPages": "sur", "noPages": "Aucune page", "previousPage": "Aller à la page précédente", "nextPage": "Aller à la page suivante"}, "zoning": {"title": "Zonage", "search": "Rechercher un zonage...", "team": "Équipe", "teamLeader": "Chef d'é<PERSON>pe", "shiftLeader": "Chef de quart", "coordinator": "Coordinateur", "createTeam": "Créer une équipe", "edit": "Modifier", "save": "Enregistrer", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "teamName": "Nom de l'équipe", "selectTeamLeader": "Sélectionner un chef d'équipe", "noTeamLeader": "Aucun chef d'équipe sé<PERSON>", "noTeam": "Aucune équipe sélectionnée", "noShiftLeader": "Aucun chef de quart sélectionné", "noCoordinator": "Aucun coordinateur sélectionn<PERSON>", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "teamCreated": "Équipe c<PERSON> avec succès", "teamUpdated": "Équipe mise à jour avec succès", "teamDeleted": "Équipe supprimée avec succès", "validation": {"required": "Ce champ est obligatoire", "invalidTeam": "Configuration d'équipe invalide", "duplicateTeam": "L'équipe existe déjà"}, "customer": "Client", "project": "Projet", "family": "Famille/Zone", "valueStream": "Flux de valeur", "area": "Zone", "selectCoordinator": "Sélectionner un coordinateur", "selectShiftLeader": "Sélectionner un chef de quart", "selectTeam": "Sélectionner une équipe", "actions": "Actions", "projects": "Projets", "addTeam": "Ajouter une équipe", "enterTeamName": "Entrer le nom de l'équipe", "confirm": "Confirmer"}, "organizationChart": {"title": "Organigramme", "loading": "Chargement...", "assigned": "assigné", "id": "ID", "fullName": "Nom complet", "function": "Fonction", "skills": "Compétences", "n1": "N+1", "supervisor": "Superviseur", "shiftLeader": "Chef d'é<PERSON>pe", "teamLeader": "Chef d'é<PERSON>pe", "operator": "Opérateur", "noSubordinates": "Aucun subordonné", "expand": "Développer", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "search": "Rechercher...", "noResults": "Aucun résultat trouvé", "name": "Nom", "surname": "Nom de famille"}, "line_assignment": {"unassignedWorkers": "Travailleurs non affectés", "finalAssembly": "Assemblage final :", "operationSkills": "Compétences opérationnelles", "selectOperationSkills": "Sélectionner les compétences opérationnelles", "idFullName": "ID / Nom complet", "departProcess": "Département / Processus", "function": "Fonction", "selectWorkersToAssign": "Sélectionner les travailleurs à affecter", "assignTo": "Affecter à : {station}", "replaceWorker": "Un travailleur est déjà affecté à la station {station}. Voulez-vous remplacer le travailleur existant ?", "assigningWorkers": "Affectation de {operatorName} à {station}", "reassignOperator": "Êtes-vous sûr de vouloir ré<PERSON><PERSON> de \"{oldName}\" à \"{newName}\" ?", "cancel": "Annuler", "confirm": "Confirmer"}, "crew_dashboard": {"stationSubRole": "Station / Sous-rôle", "role": "R<PERSON><PERSON>", "workstationSkills": "Compétences du poste de travail", "operatorId": "ID opérateur", "name": "Prénom", "surname": "Nom", "actions": "Actions", "selectStation": "Sélectionner la station", "selectOperator": "Sélectionner l'opérateur", "customer": "Client", "selectCustomer": "Sélectionner le client", "project": "Programme(Projet)", "selectProject": "Sélectionner le projet", "family": "<PERSON><PERSON><PERSON>", "selectFamily": "Sélectionner la famille", "valueStream": "Flux de valeur", "selectValueStream": "Sélectionner le flux de valeur", "area": "Zone", "selectArea": "Sélectionner la zone", "team": "Équipe", "selectTeam": "Sélectionner l'équipe", "meDefinition": "Définition ME", "meDefinitionEffectiveDate": "Date d'effet de la définition ME", "notSelected": "Non sélectionné", "addMsLine": "Ajouter une ligne MFG", "totalUnassigned": "Total non assigné :", "totalAssigned": "Total assigné :", "deleteWorkstation": "Êtes-vous sûr de vouloir supprimer le poste de travail \"{station}\" ?", "reassignOperator": "Êtes-vous sûr de vouloir ré<PERSON><PERSON> de \"{oldName}\" à \"{newName}\" ?", "assignOperator": "Êtes-vous sûr de vouloir assigner \"{name}\" à ce poste de travail ?", "addMsLineConfirm": "Êtes-vous sûr de vouloir ajouter cette ligne MS ?", "cancel": "Annuler", "confirm": "Confirmer"}, "crew_management_page": {"assignWorkers": "Affecter les travailleurs", "assignWorkersWithCount": "Affecter les travailleurs ({count})"}, "training": {"scheduleTrainingSession": "Planifier une session de formation", "skill": "Compétence", "id": "ID", "firstName": "Prénom", "lastName": "Nom", "date": "Date", "phase": "Phase", "hiredDate": "Date d'embauche", "score": "Score", "selectScore": "Sélectionner un score", "selectFileToUpload": "Veuillez sélectionner un fichier à télécharger", "noFileChosen": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi", "chooseFile": "<PERSON><PERSON> un fichier", "supportedFormats": "Formats supportés", "cancel": "Annuler", "validate": "Valider", "validating": "Validation...", "close": "<PERSON><PERSON><PERSON>", "mie": "MIE", "department": "Département", "position": "Poste", "area": "Zone", "next": "Suivant", "scheduling": "Planification..."}, "clocking": {"cancelButton": "Annuler", "title": "Heures de Travail et Profil TKS", "workingHours": "Heures de Travail", "tksProfile": "Profil TKS", "addSchedule": "Ajouter un Horaire", "editSchedule": "Modifier l'Horaire", "deleteSchedule": "Supprimer l'Horaire", "import": "Importer", "export": "Exporter", "download": "Télécharger", "downloadTemplate": "Télécharger le Modèle", "downloading": "Téléchargement...", "profile": "Profil", "code": "Code", "designation": "Désignation", "timeEntry": "<PERSON><PERSON> d'Entrée", "timeExit": "<PERSON><PERSON> de Sortie", "entryTime": "<PERSON><PERSON> d'Entrée", "exitTime": "<PERSON><PERSON> de Sortie", "break": "Pause", "breakDuration": "<PERSON><PERSON><PERSON>", "workHours": "Heures de Travail", "day": "Jour", "days": "Jours", "scheduleType": "Type d'Horaire", "effectiveDate": "Date d'E<PERSON>t", "effectiveDateRange": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "Date de Début", "endDate": "Date de Fin", "normalDay": "Jour Normal", "ramadan": "<PERSON><PERSON>", "redaDay": "<PERSON><PERSON>", "actions": "Actions", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "update": "Mettre à jour", "create": "<PERSON><PERSON><PERSON>", "add": "Ajouter", "remove": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Appliquer", "filter": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "noResults": "Aucun résultat trouvé", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "info": "Information", "confirmation": "Confirmation", "createSchedule": "<PERSON><PERSON><PERSON> un Horaire", "editProfil": "Modifier le Profil", "createNewProfile": "Créer un Nouveau Profil", "updateEvent": "Mettre à jour l'Événement", "saveEvent": "Enregistrer l'Événement", "deleteEvent": "Supprimer l'Événement", "confirmDelete": "Confirmer la Suppression", "confirmDeleteTitle": "Confirmer la Suppression", "confirmDeleteMessage": "Êtes-vous sûr de vouloir supprimer cet horaire ? Cette action ne peut pas être annulée.", "deleting": "Suppression...", "updating": "Mise à jour...", "creating": "Création...", "saving": "Enregistrement...", "workingHoursActivated": "Heures de Travail Activées", "activateWorkingHours": "Activer les Heures de Travail", "EffectiveDate": "Date d'E<PERSON>t", "selectEffectiveDate": "Sélectionner la Date d'<PERSON>", "daysOfWeek": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}, "filters": {"filterByCode": "Filtrer par Code", "filterByDay": "Filtrer par Jour", "filterByWorkHours": "Filtrer par Heures de Travail", "filterByProfile": "Filtrer par Profil", "allDays": "Tous les Jours", "clearFilters": "Effacer les Filtres", "applyFilters": "Appliquer les Filtres"}, "table": {"profile": "Profil", "code": "Code", "designation": "Désignation", "entryTime": "<PERSON><PERSON> d'Entrée", "exitTime": "<PERSON><PERSON> de Sortie", "days": "Jours", "break": "Pause", "workHours": "Heures de Travail", "actions": "Actions", "noData": "<PERSON><PERSON>n horaire trouvé", "loading": "Chargement des horaires...", "itemsPerPage": "Éléments par page", "page": "Page", "of": "de", "previous": "Précédent", "next": "Suivant", "first": "Premier", "last": "<PERSON><PERSON>"}, "validation": {"required": "Ce champ est obligatoire", "invalidTime": "Veuillez entrer une heure valide", "invalidNumber": "Veuillez entrer un nombre valide", "invalidEmail": "Veuillez entrer une adresse e-mail valide", "minLength": "La longueur minimale est de {min} caractères", "maxLength": "La longueur maximale est de {max} caractères", "min": "La valeur minimale est {min}", "max": "La valeur maximale est {max}"}, "messages": {"scheduleCreated": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "scheduleUpdated": "Horaire mis à jour avec succès", "scheduleDeleted": "Horaire supprimé avec succès", "schedulesImported": "Horaires importés avec succès", "templateDownloaded": "<PERSON><PERSON><PERSON><PERSON> avec succès", "effectiveDatesUpdated": "Dates d'effet mises à jour avec succès", "errorCreatingSchedule": "Erreur lors de la création de l'horaire", "errorUpdatingSchedule": "Erreur lors de la mise à jour de l'horaire", "errorDeletingSchedule": "Erreur lors de la suppression de l'horaire", "errorImportingSchedules": "Erreur lors de l'importation des horaires", "errorDownloadingTemplate": "Erreur lors du téléchargement du modèle", "errorUpdatingEffectiveDates": "Erreur lors de la mise à jour des dates d'effet", "errorLoadingSchedules": "Erreur lors du chargement des horaires", "errorLoadingScheduleTypes": "Erreur lors du chargement des types d'horaires"}, "placeholders": {"enterProfile": "En<PERSON><PERSON> le profil", "enterCode": "Entrer le code", "enterDesignation": "Entrer la désignation", "selectTime": "Sélectionner l'heure", "enterBreakDuration": "Entrer la durée de pause en minutes", "enterWorkHours": "ex: 8,5", "selectDays": "Sélectionner les jours", "searchCode": "Rechercher par code...", "searchProfile": "Rechercher par profil..."}, "tooltips": {"addSchedule": "Ajouter un nouvel horaire", "editSchedule": "Modifier cet horaire", "deleteSchedule": "Supprimer cet horaire", "importSchedules": "Importer des horaires depuis un fichier", "downloadTemplate": "Télécharger le modèle d'importation", "setEffectiveDate": "Définir la période d'effet", "filterSchedules": "Filtrer les horaires", "clearFilters": "Effacer tous les filtres"}, "importModal": {"title": "Importer les Horaires", "uploadFileLabel": "<PERSON><PERSON>léverser un Fichier", "acceptedFormats": "Formats acceptés : CSV, XLSX", "invalidFileTypeTitle": "Type de Fichier Invalide", "invalidFileTypeDescription": "Veuillez sélectionner un fichier CSV ou XLSX.", "noFileSelectedTitle": "<PERSON><PERSON><PERSON>", "noFileSelectedDescription": "Veuillez sélectionner un fichier à importer.", "importButton": "Importer", "importingButton": "Importation...", "importSuccessfulTitle": "Importation Réussie", "importSuccessfulDescription": "{importedCount} horaires importés avec succès.", "importCompletedWithErrorsTitle": "Importation Terminée avec Erreurs", "importCompletedWithErrorsDescription": "Importation terminée avec {importedCount} importés et {errorsLength} erreurs."}}, "disciplinary_measures": {"title": "Mesures disciplinaires", "submit_title": "Soumettre une mesure disciplinaire", "id": "ID", "firstName": "Prénom", "lastName": "Nom", "function": "Fonction", "department": "Département", "category": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "startDate": "Date de début", "endDate": "Date de fin", "days": "Nb de jours", "actions": "Action", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "openMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "operator_id": "ID Opérateur", "operator_id_placeholder": "9865", "first_name": "Prénom", "first_name_placeholder": "Prénom", "last_name": "Nom", "last_name_placeholder": "Nom", "department_placeholder": "Département", "start_date": "Date de début", "number_of_days": "Nombre de jours", "number_of_days_placeholder": "Entrer les jours", "end_date": "Date de fin", "comment": "Commentaire", "comment_placeholder": "Entrez votre commentaire ici...", "file_upload": "Veuillez sélectionner un fichier à télécharger", "file_upload_button": "<PERSON><PERSON> un fichier", "file_no_file_chosen": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi", "file_files_selected": "{count} fichier{plural} sélectionné{plural}", "file_supported_formats": "Formats supportés: ", "file_remove": "<PERSON><PERSON><PERSON><PERSON>", "file_maximum_files": "Maximum {maxFiles} fichiers autorisés", "save_success_title": "Su<PERSON>ès", "save_success_message": "La mesure disciplinaire a été enregistrée avec succès.", "save_error_title": "<PERSON><PERSON><PERSON>", "save_error_message": "Échec de l'enregistrement de la mesure disciplinaire. Veuillez réessayer.", "close": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer cet enregistrement?", "search_placeholder": "Rechercher par département, caté<PERSON>ie, employé", "filter": "<PERSON><PERSON><PERSON>", "sort_by": "Trier par", "date": "Date", "name": "Nom", "export": "Exporter", "search": "Recherche", "showing_results": "Affichage de {filteredItems} sur {totalItems} résultats", "total_results": "{totalItems} résultats au total"}, "alerts_notifications": {"title": "Alertes & Notifications", "tabs": {"alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications"}, "search": {"placeholder": "Rechercher des alertes et notifications..."}, "filters": {"all": "Tous", "approval": "Approbation", "redirect": "Redirection", "waiting": "En attente de traitement", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "information": "Information", "warning": "Avertissement", "error": "<PERSON><PERSON><PERSON>"}, "table": {"headers": {"title": "Titre", "requesterID": "<PERSON> du Demandeur", "requesterName": "Nom du Demandeur", "type": "Type", "datetime": "Date et heure", "details": "Détails"}, "actions": {"view_details": "Voir les détails", "approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>"}, "empty": "Aucune alerte ou notification trouvée"}, "status": {"waiting_treatment": "En attente de traitement", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "generated": "<PERSON><PERSON><PERSON><PERSON>"}, "types": {"absence_authorization": "Autorisation d'absence", "leave_request": "<PERSON><PERSON><PERSON> (Vacances)", "information": "Information", "warning": "Avertissement", "error": "<PERSON><PERSON><PERSON>"}, "tracking": {"title": "<PERSON><PERSON><PERSON>", "steps": {"request_submitted": "<PERSON><PERSON><PERSON> soumise", "request_processing": "Demande en cours de traitement", "waiting_approval": "En attente d'approbation", "request_approved": "<PERSON><PERSON><PERSON> approu<PERSON>", "request_rejected": "<PERSON><PERSON><PERSON>", "notification_generated": "Notification générée"}, "actors": {"system": "Système", "shift_leader": "Chef d'é<PERSON>pe", "you": "vous"}, "submitted_by": "<PERSON><PERSON>s par {name}", "approved_by": "par {role}", "rejected_by": "par {role}", "pending_approval": "par {name}"}, "modal": {"title": "Dé<PERSON> de l'alerte", "close": "<PERSON><PERSON><PERSON>", "sections": {"general": "Informations générales", "details": "Détails", "tracking": "<PERSON><PERSON><PERSON>", "response": "Réponse"}, "fields": {"title": "Titre", "type": "Type", "status": "Statut", "requester": "De<PERSON>eur", "request_date": "Date de demande", "duration": "<PERSON><PERSON><PERSON>", "from_date": "Date de début", "to_date": "Date de fin", "details": "Détails"}, "response": {"title": "Votre réponse", "approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "comment_placeholder": "Ajoutez votre commentaire ici...", "submit": "Soumettre la réponse"}}}, "versatilityMatrix": {"title": "<PERSON><PERSON>", "qualificationsTitle": "<PERSON><PERSON>", "polyvalenceTitle": "<PERSON><PERSON>", "legendTitle": "Légende polyvalence", "qualificationsLegendTitle": "Légende qualifications", "operators": "Opérateurs", "legend": {"level1": "< 1 mois", "level2": "1 à 2 mois", "level3": "2 à 6 mois", "level4": "> 6 mois", "currentPosition": "Position actuelle", "polyvalencePlanning": "Planning de polyvalence", "normal": "Normal", "medium": "<PERSON><PERSON><PERSON>", "critical": "Critique"}, "qualifications": {"ojt": "OJT", "learningCurve": "Courbe d'apprentissage", "trained": "Opérateur formé", "validated": "Opérateur validé", "certified": "Opérateur certifié", "recertified": "Opérateur re-certifié", "notQualified": "Non qualifié"}, "table": {"headers": {"number": "N°", "mle": "<PERSON><PERSON>", "firstName": "Prénom", "lastName": "Nom", "position": "Position", "hiredDate": "Date d'embauche", "postes": "POSTES", "qualificationsRequired": "Qualifications Requises", "criticity": "Criticité", "target": "Cible", "actual": "Actuel"}, "tooltips": {"requiredSkills": "Compétences requises : {skills}", "currentPositionNotEditable": "Position actuelle - non modifiable", "level1": "< 1 mois", "level2": "1 à 2 mois", "level3": "2 à 6 mois", "level4": "> 6 mois", "currentPosition": "Position actuelle", "polyvalencePlanning": "Planning de polyvalence", "normal": "Normal", "medium": "<PERSON><PERSON><PERSON>", "critical": "Critique"}}, "buttons": {"export": "Exporter"}, "loading": "Chargement de la matrice de polyvalence...", "qualificationsLoading": "Chargement de la matrice des qualifications...", "navigation": {"search": "Rechercher...", "projects": "Projets", "families": "Familles", "valueStreams": "Flux de valeur", "areas": "Zones", "items": "Éléments", "clickToView": "Cliquez pour voir ci-dessous", "selectCustomer": "Sélectionnez un client pour voir ses projets ici", "selectProject": "Sélectionnez un projet pour voir ses familles ici", "selectFamily": "Sélectionnez une famille pour voir ses flux de valeur ici", "selectValueStream": "Sélectionnez un flux de valeur pour voir ses zones ici", "selectMatrix": "Sélectionnez une zone pour accéder à la matrice", "selectA": "Sélectionnez un", "titles": {"customers": "Clients", "projects": "Projets", "families": "Familles", "valueStream": "Flux de valeur", "area": "Zone", "versatilityMatrix": "<PERSON><PERSON>", "qualificationsMatrix": "<PERSON><PERSON>", "polyvalenceMatrix": "<PERSON><PERSON>"}}, "breadcrumb": {"versatilityMatrix": "<PERSON><PERSON>", "qualificationMatrix": "<PERSON><PERSON>"}, "departments": {"finalAssemblyDepartment": "Département d'assemblage final", "cutingLeadPrepDepartment": "Département de découpe et préparation"}, "emptyState": {"selectMatrixTitle": "Sélectionnez un type de matrice dans la navigation", "selectMatrixDescription": "Veuillez sélectionner l'un des types de matrice dans les cartes de navigation ci-dessus", "selectAreaFirst": "Sélectionnez d'abord une zone, puis sélectionnez un type de matrice dans les cartes de navigation ci-dessus"}, "loadingStates": {"matrixDataTitle": "Chargement des données de la matrice...", "matrixDataDescription": "Veuillez patienter pendant que nous récupérons les dernières données du serveur."}}, "confirmation": {"areYouSureSubmit": "Êtes-vous sûr de vouloir soumettre cette évaluation ?", "submissionWarning": "Une fois soumise, l'évaluation et le score seront enregistrés et ne pourront pas être modifiés. Veuillez vous assurer que tous les champs sont correctement remplis.", "cancel": "Annuler", "confirm": "Confirmer", "confirming": "Confirmation...", "close": "<PERSON><PERSON><PERSON>"}, "success": {"evaluationSubmittedSuccessfully": "Évaluation soumise avec succès", "evaluationSaved": "Votre évaluation a été enregistrée. L'opérateur a obtenu", "passMessage": "<PERSON><PERSON><PERSON><PERSON> et répond à toutes les attentes de performance.", "failMessage": "Échec et nécessite une formation supplémentaire.", "done": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "common": {"search": "Rechercher...", "noResultsFound": "Aucun résultat trouvé.", "selectOption": "Sélectionner une option"}, "commonCalendar": {"search": "Rechercher...", "noResultsFound": "Aucun résultat trouvé.", "selectOption": "Sélectionner une option"}, "rolesPermissions": {"title": "Rôles et Autorisations", "searchPlaceholder": "Rechercher un rôle...", "addNewRole": "Ajouter un nouveau rôle", "download": "Télécharger", "downloadTemplate": "Télécharger le Modèle", "downloading": "Téléchargement...", "deletingRole": "Suppression du rôle...", "columns": {"fullNameRoleId": "Nom complet / ID de rôle", "permissions": "Autorisations", "adGroups": "Groupes AD (Rôles)", "description": "Description", "actions": "Actions"}, "actions": {"editRole": "Modifier le rôle", "copyRole": "<PERSON><PERSON><PERSON> rô<PERSON>", "deleteRole": "<PERSON><PERSON><PERSON><PERSON> le rôle", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"editTitle": "Modifier le Rôle et les Autorisations", "duplicateTitle": "Dupliquer le Rôle et les Autorisations", "addTitle": "Ajouter un Nouveau Rôle et des Autorisations", "loadingRoleData": "Chargement des données du rôle...", "tabs": {"roleDetails": "<PERSON>é<PERSON> du rôle", "permissions": "Autorisations", "adGroups": "Groupes AD (Rôles)"}, "details": {"createMessage": "Définir un nouveau rôle avec les autorisations appropriées et les mappages de groupes AD.", "editMessage": "Modifier les détails du rôle, les autorisations et les mappages de groupes AD.", "duplicateMessage": "Créer un nouveau rôle basé sur le rôle sélectionné. Les autorisations et les groupes AD ont été copiés. Veuillez mettre à jour l'ID et le nom du rôle selon les besoins.", "roleId": "ID du Rôle", "roleIdPlaceholder": "Entrer l'ID du rôle", "roleIdHelp": "Lettres majuscules, chiffres et tirets de soulignement uniquement.", "name": "Nom", "namePlaceholder": "Entrer le nom du rôle", "description": "Description", "descriptionPlaceholder": "Entrer la <PERSON>", "required": "*"}, "permissions": {"title": "Attribution des Autorisations", "description": "Sélectionnez les autorisations à attribuer à ce rôle. Les utilisateurs avec ce rôle pourront effectuer ces actions.", "searchPlaceholder": "Rechercher des autorisations...", "selectAll": "<PERSON><PERSON>", "unselectAll": "<PERSON><PERSON>", "loadingPermissions": "Chargement des autorisations...", "noPermissionsFound": "Aucune autorisation trouvée"}, "adGroups": {"title": "Mappage des Groupes Azure AD", "description": "Mapper les groupes de sécurité Azure AD à ce rôle. Les utilisateurs de ces groupes se verront automatiquement attribuer ce rôle.", "searchPlaceholder": "Rechercher des groupes Azure AD...", "selectAll": "<PERSON><PERSON>", "unselectAll": "<PERSON><PERSON>", "groups": {"systemAdministrators": "Administrateurs Système", "systemAdministratorsDesc": "Administrateurs globaux du système", "executiveLeadership": "Direction Exécutive", "executiveLeadershipDesc": "Équipe de direction exécutive", "plantManagers": "Gestionnaires d'Usine", "plantManagersDesc": "Tous les gestionnaires d'usine", "shiftLeaders": "Chefs d'Équipe", "shiftLeadersDesc": "Tous les chefs d'équipe", "teamLeaders": "Chefs d'Équipe", "teamLeadersDesc": "Tous les chefs d'équipe", "plantOperators": "Opérateurs d'Usine", "plantOperatorsDesc": "Tous les opérateurs d'usine", "engineers": "Ingénieurs", "engineersDesc": "Personnel d'ingénierie"}}, "buttons": {"cancel": "Annuler", "updateRole": "Mettre à jour le rôle", "updating": "Mise à jour...", "duplicateRole": "<PERSON><PERSON><PERSON><PERSON> le rô<PERSON>", "duplicating": "Duplication...", "createRole": "<PERSON><PERSON><PERSON>ô<PERSON>", "creating": "Création..."}}, "success": {"title": "Succès !", "message": "Le rôle {roleId} a été {operation} avec succès", "operations": {"created": "c<PERSON><PERSON>", "duplicated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updated": "mis à jour"}, "close": "<PERSON><PERSON><PERSON>"}, "delete": {"title": "Supp<PERSON>er le Rôle", "message": "Êtes-vous sûr de vouloir supprimer le rôle \"{roleName}\" ?", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression..."}, "selectOption": "Sélectionner une option", "calendarHeader": {"plantCalendar": "<PERSON><PERSON><PERSON> de l'usine {year}", "searchEventsPlaceholder": "Rechercher des événements...", "addEventButton": "Ajouter un événement", "importEventsButton": "Importer des événements", "historyButton": "Historique", "loadingCategories": "Chargement des catégories...", "totalWorkingDaysIn": "Total des jours ouvrables en {year}", "monthView": "<PERSON><PERSON>", "yearView": "<PERSON><PERSON>"}, "importModal": {"title": "Importer des événements", "uploadFileLabel": "Télécharger le fichier", "acceptedFormats": "Formats acceptés : .csv, .xlsx", "invalidFileTypeTitle": "Type de fichier invalide", "invalidFileTypeDescription": "Veuillez sélectionner un fichier .csv ou .xlsx.", "noFileSelectedTitle": "<PERSON><PERSON><PERSON> fichier s<PERSON>", "noFileSelectedDescription": "Veuillez sélectionner un fichier à importer.", "importCompletedWithErrorsTitle": "Importation terminée avec des erreurs", "importCompletedWithErrorsDescription": "{importedCount} événements importés avec succès, mais {errorsLength} lignes ont eu des problèmes.", "importSuccessfulTitle": "Importation réussie", "importSuccessfulDescription": "{importedCount} événements importés avec succès.", "importingButton": "Importation...", "importButton": "Importer"}, "eventDetailsModal": {"titleLabel": "Titre :", "dateLabel": "Date :", "typeLabel": "Type :", "descriptionLabel": "Description :"}, "calendarView": {"weekHeader": "S", "mon": "<PERSON>n", "tue": "Mar", "wed": "<PERSON><PERSON>", "thu": "<PERSON><PERSON>", "fri": "Ven", "sat": "Sam", "sun": "<PERSON><PERSON>"}, "eventsHistorySidebar": {"title": "Historique des événements - {year}", "infoText": "Vous pouvez suivre toutes les modifications apportées au calendrier — y compris les événements ajoutés, modifiés ou supprimés.", "actionLabel": "Action", "selectActionPlaceholder": "Sélectionner une action", "allActions": "<PERSON>ut", "actionCreated": "Événement entier a<PERSON>", "actionUpdated": "Événement mis à jour", "actionDeleted": "Événement supprimé", "actionImported": "Événements importés", "dateLabel": "Date", "pickDatePlaceholder": "Sélectionner une date", "loadingHistory": "Chargement de l'historique...", "noHistoryFound": "Aucun événement historique trouvé", "tryChangingFilter": "Essayez de modifier les critères de filtre", "todayGroup": "<PERSON><PERSON><PERSON>'hui", "yesterdayGroup": "<PERSON>er", "earlierGroup": "Plus tôt", "loadMoreButton": "Charger plus...", "loadingButton": "Chargement..."}, "eventHistoryItem": {"dateTimeLabel": "Date et heure :", "eventLabel": "Événement :", "actionLabel": "Action :", "oldValueLabel": "Ancienne valeur :", "newValueLabel": "Nouvelle valeur :", "doneByLabel": "Fait par :"}, "deleteModal": {"title": "Supprimer l'événement", "deletingButton": "Suppression...", "deleteButton": "Supprimer l'événement", "confirmationTitle": "Êtes-vous sûr de vouloir supprimer cet événement ?", "confirmationMessage": "Cette action est irréversible. Une fois supprimé, {eventName} sera définitivement retiré du calendrier.", "deleteError": "Échec de la suppression de l'événement. Veuillez réessayer."}, "eventModal": {"editTitle": "Modifier l'événement", "createTitle": "Créer un nouvel événement", "eventNameLabel": "Nom de l'événement *", "eventNamePlaceholder": "Saisir le nom de l'événement", "categoryLabel": "<PERSON><PERSON><PERSON><PERSON>", "selectCategoryPlaceholder": "Sélectionner une catégorie", "startDateLabel": "Date de début", "endDateLabel": "Date de fin", "pickDatePlaceholder": "Sélectionner une date", "descriptionLabel": "Description", "descriptionPlaceholder": "Saisir la description de l'événement", "updatingButton": "Mise à jour...", "creatingButton": "Création...", "updateButton": "Mettre à jour l'événement", "saveButton": "Enregistrer l'événement", "nameRequiredAlert": "Le nom de l'événement est requis", "saveErrorAlert": "Échec de l'enregistrement de l'événement. Veuillez réessayer."}, "cancelButton": "Annuler", "closeButton": "<PERSON><PERSON><PERSON>", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "editButton": "Modifier"}, "timeAgo": {"justNow": "à l'instant", "minute": "minute", "minutes": "minutes", "hour": "heure", "hours": "heures", "day": "jour", "days": "jours", "week": "semaine", "weeks": "semaines", "month": "mois", "months": "mois", "year": "an", "years": "ans", "ago": "il y a", "in": "dans"}, "toast": {"notificationTitle": "🔔 Nouvelle notification", "alertTitle": "🚨 Nouvelle alerte"}, "nurseAbility": {"confirmInability": "Confirmer l'incapacité", "confirmInabilityMessage": "Êtes-vous sûr de vouloir confirmer l'incapacité ?", "idWorker": "ID Travailleur", "medicalDecision": "Décision médicale", "cancel": "Annuler", "confirm": "Confirmer", "submitting": "Soumission en cours...", "statementOfInability": "Déclaration d'incapacité", "searchWorker": "Rechercher un travailleur", "id": "ID", "firstName": "Prénom", "lastName": "Nom", "natureOfIllness": "Nature de la maladie", "validate": "Valider", "stopInability": "<PERSON>rr<PERSON>ter l'incapacité", "confirmCancelInability": "Êtes-vous sûr d'annuler l'incapacité ?", "stopping": "Arrêt en cours...", "listOfOperators": "Liste des opérateurs", "sortBy": "Trier par", "date": "Date", "loadingEmployees": "Chargement des employés...", "name": "Nom", "surname": "Prénom", "declarationDate": "Date de déclaration", "operatorDetails": "Détails de l'opérateur", "employeeId": "ID Employé", "reasonForInability": "Raison de l'incapacité", "createdAt": "<PERSON><PERSON><PERSON>"}, "workcenterList": {"childlineName": "Nom de la ligne enfant", "workcenterName": "Nom du centre de travail", "workcenterCriticality": "Criticité du centre de travail", "normal": "Normal", "critical": "Critique", "medium": "<PERSON><PERSON><PERSON>", "value_stream": "Flux de valeur", "area": "Zone", "customer": "Client", "project": "Projet", "family": "<PERSON><PERSON><PERSON>", "skillsReq": "Exigence de compétences", "saveCriticality": "Enregistrer la criticité", "loading": "Chargement..."}, "skillList": {"skillId": "ID de compétence", "description": "Description", "shortDesc": "Description courte", "criticality": "Criticité", "BasicProcess": "Processus de base", "CriticalProcess": "Processus critique", "actions": "Actions", "search": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "common": {"import": "Importer", "export": "Exporter"}}, "project-hierarchy": {"customer": {"title": "Client", "description": "Sélectionnez un client ou cliquez sur + pour en créer un nouveau ", "selectPlaceholder": "Sélectionner un client...", "addNew": "Ajouter un nouveau client", "customerName": "Nom du client: "}, "project": {"title": "Nom du projet", "description": "Sélectionnez un projet ou cliquez sur + pour créer un nouveau projet ", "projectName": "Nom du projet : ", "selectPlaceholder": "Sélectionner un projet...", "addNew": "Ajouter un nouveau projet"}, "families": {"title": "Familles", "createFamily": "<PERSON><PERSON><PERSON> une famille"}, "valueStream": {"title": "Flux de valeur (Ligne)", "createValueStream": "<PERSON><PERSON><PERSON> un flux de valeur (Ligne)", "createFamilyFirst": "! Créez d'abord une famille"}}, "clocking-validation": {"pageTitle": "Validation de Pointage", "statistics": {"totalAttendanceSheets": "Total des feuilles de présence reçues", "waitingForValidation": "En attente de validation", "validated": "<PERSON><PERSON><PERSON>", "validatedWithComment": "Validé avec commentaire"}, "table": {"date": "Date", "department": "Département", "shift": "Équipe", "role": "R<PERSON><PERSON>", "id": "ID", "firstLastName": "Prénom, Nom", "firstName": "Prénom", "lastName": "Nom", "approver": "Approbateur", "status": "Statut", "comment": "Commentaire", "closed": "<PERSON><PERSON><PERSON>", "attendanceRecords": "Registres de présence"}, "filters": {"searchPlaceholder": "Rechercher par ID, prénom ou nom...", "selectDate": "Sélectionner la date", "selectDepartment": "Sélectionner le département", "allDepartments": "Tous les départements", "all": "Tous", "notClosed": "Non fermé"}, "actions": {"viewDetails": "Voir les détails", "quickApprove": "Approbation rapide", "approveWithComment": "Approuver avec commentaire", "export": "Exporter cette liste d'équipes", "submitClockingValidation": "Soumettre la validation de pointage avec commentaire", "reviewFinalization": "Examinez tous les statuts de présence et commentaires avant de finaliser. Confirmez les détails avant de soumettre votre validation."}, "departments": {"assembly": "Assemblage", "production": "Production", "qualityControl": "Contrôle qualité", "maintenance": "Maintenance", "logistics": "Logistique"}, "shifts": {"morning": "<PERSON>in", "evening": "Soir", "night": "Nuit"}, "roles": {"teamLeader": "Chef d'é<PERSON>pe", "operator": "Opérateur", "inspector": "Inspecteur", "technician": "Technicien", "coordinator": "Coordinateur"}, "status": {"waitingForValidation": "En attente de validation", "validated": "<PERSON><PERSON><PERSON>", "validatedWithComment": "Validé avec commentaire", "rejected": "<PERSON><PERSON><PERSON>"}, "pagination": {"showing": "Affichage", "to": "à", "of": "sur", "entries": "entrées", "previous": "Précédent", "next": "Suivant"}, "loading": "Chargement...", "noResults": "Aucun résultat.", "modal": {"attendanceRecordDetails": "Détails du registre de présence", "employeeInformation": "Informations sur l'employé", "validationActions": "Actions de validation", "addCommentOptional": "Ajouter un commentaire (Optionnel)", "addCommentPlaceholder": "Ajouter un commentaire pour cette validation...", "approve": "Approuver", "approveWithComment": "Approuver avec commentaire", "reject": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "previewValidationComment": "Aperçu de la validation avec commentaire", "validateAttendanceWithComment": "Valider la présence avec un commentaire"}}}