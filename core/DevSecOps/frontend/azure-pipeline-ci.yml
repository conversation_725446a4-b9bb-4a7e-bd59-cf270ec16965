# =========================================================================
# Connected Workers Frontend Continuous Integration Pipeline
# =========================================================================
# This pipeline is designed for continuous integration of the frontend web application.
# It performs dependency installation, code quality checks, and builds the application.
#
# The pipeline is triggered on commits to the stage branch that affect
# the frontend code.
# =========================================================================

name: CW-CI-Pipeline-Frontend

pr: none

trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - "core/frontend/webapp/**"

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu].

variables:
  NODE_VERSION: "20.x" # Node.js version to use for the build
  IMAGE_NAME: "cw-webfrontend-dev" # Name of the Docker image
  ACR_NAME: "acr003nonprdemearepo" # Azure Container Registry name
  IMAGE_TAG: "$(Build.BuildId)" # Use the build ID as a unique tag for the Docker image
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/frontend/webapp # Root directory of the project
  # TODO: Change the Azure Subscription name based on the Service Connection name
  AZURE_SUBSCRIPTION: "RG-Dev-ConnectedWorker-Apps" # Azure Subscription name - Based on the Service Connection name

stages:
# =========================================================================
# Stage 1: Continuous Integration Stage
# =========================================================================
# This stage handles dependency installation, code quality checks,
# and building the application
# =========================================================================
- stage: InstallDependencies_Test_BuildArtifact
  displayName: 'Continuous Integration Stage'
  jobs:
  - job: InstallDependencies_Test_BuildArtifact
    displayName: 'Install, Test and Build'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install Node.js environment
    - task: NodeTool@0
      inputs:
        versionSpec: $(NODE_VERSION)
      displayName: 'Install Node.js'

    # Install pnpm package manager
    - script: |
        echo "Installing pnpm package manager..."
        npm install -g pnpm
      displayName: 'Install pnpm'

    # Install project dependencies using pnpm
    - script: |
        echo "Installing dependencies for frontend application..."
        pnpm install --no-frozen-lockfile  # Clean install dependencies
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Install dependencies'

    # Cache node_modules to improve pipeline performance
    - task: Cache@2
      inputs:
        key: 'pnpm | "$(Agent.OS)" | $(BUILD_SOURCES_DIR)/pnpm-lock.yaml'  # Cache key based on pnpm-lock.yaml
        path: $(BUILD_SOURCES_DIR)/node_modules  # Directory to cache
        cacheHitVar: CACHE_RESTORED  # Variable indicating if the cache was restored
      displayName: 'Cache node_modules'

    # =========================================================================
    # Code Quality Checks
    # =========================================================================
    
    # Format code using the project's formatting rules
    - script: |
        echo "Formatting code for frontend application..."
        pnpm run format:all
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Format code'

    # Lint code to ensure code quality
    - script: |
        echo "Running linting checks for frontend application..."
        pnpm run lint || echo 'Linting failed. Please check the code quality.'
        
        # Uncomment the line below to make the pipeline fail on lint errors
        # pnpm run lint
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Lint code'

    # =========================================================================
    # Build and Publish Artifacts
    # =========================================================================
    
    # Build the application
    - script: |
        echo "Building frontend application..."
        pnpm run build
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Building project .next generation'

    # Prepare artifacts for deployment
    - script: |
        echo "Creating deployment package..."
        mkdir -p $(Build.ArtifactStagingDirectory)/app
      
        # Copy build output to staging directory
        echo "Copying build artifacts to staging directory..."
        cp -Rr .next $(Build.ArtifactStagingDirectory)/app/
        cp -Rr public $(Build.ArtifactStagingDirectory)/app/
        cp -Rr messages $(Build.ArtifactStagingDirectory)/app/
        cp -Rr package*.json $(Build.ArtifactStagingDirectory)/app/
        cp -Rr pnpm-lock.yaml $(Build.ArtifactStagingDirectory)/app/
      
        # Optional: Include version information
        echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/app/version.txt
      
        echo "Build completed successfully for frontend application"
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Build and prepare artifacts'

    #Publish build artifacts
    #- task: PublishBuildArtifacts@1
    #  inputs:
    #    pathToPublish: $(Build.ArtifactStagingDirectory)
    #    artifactName: 'FrontEndBuild'
    #    publishLocation: 'Container'
    #  displayName: 'Publish Build Artifacts'

    # Add to CI pipeline
    - script: |
        echo "Running security scanning..."
        pnpm audit --audit-level=high --production --json > audit-report.json || echo "Security scanning failed"
        
        # Consider adding additional security tools
        # npm install -g snyk
        # snyk test
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Security Scanning'

    # Trigger CD pipeline for the dev branch - Not profitable for the moment
    # The id of the CD pipeline should be changed once the pipeline is created or re-created.
    #- script: |
    #    echo "Triggering CD pipeline..."
    #    curl -X POST \
    #      "https://dev.azure.com/AptivHostingProjects/AZ-ConnectedWorkers/_apis/pipelines/53/runs?api-version=6.0" \
    #      -H "Content-Type: application/json" \
    #      -H "Authorization: Bearer $(System.AccessToken)" \
    #      -d '{"resources":{"repositories":{"self":{"refName":"refs/heads/dev"}}}}'
    #  displayName: 'Trigger CD Pipeline'
    #  env:
    #    SYSTEM_ACCESSTOKEN: $(System.AccessToken)

# ========================================
# Azure CLI Setup
# ========================================
    
    # Install Azure CLI to interact with Azure resources
    - script: |
        echo "Installing Azure CLI..."
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash # Install Azure CLI
      displayName: 'Install Azure CLI'

    # Grant Docker permissions to avoid permission issues
    - script: |
        echo "Granting Docker socket permissions..."
        sudo chmod 666 /var/run/docker.sock # Grant Docker permissions
      displayName: 'Grant Docker permissions'

# =========================================================================
# Azure Authentication
# =========================================================================
    
    # Login to Azure using Managed Identity
    - script: |
        echo "Authenticating with Azure using Managed Identity..."
        az login --identity
        az account show
      displayName: 'Login to Azure'

# =========================================================================
# Docker Image Build
# =========================================================================
    
    # Build Docker image for the frontend
    - script: |
        echo "##############################################################################################################"
        echo "Navigating to frontend directory..."
        cd $(BUILD_SOURCES_DIR)
        
        echo "Building Docker image for the frontend..." 
        docker build --no-cache -f $(BUILD_SOURCES_DIR)/Dockerfile -t $(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG) .
      displayName: 'Build Docker Image'
# =========================================================================
# Push to Azure Container Registry
# =========================================================================
    
    # Login to Azure Container Registry
    - script: |
        echo "##############################################################################################################"
        echo "Logging into Azure Container Registry..."
        az acr login -n $(ACR_NAME)
      displayName: 'Login to Azure Container Registry'
    
    # Push Docker image to ACR
    - script: |
        echo "Pushing Docker image to ACR..."
        # Delete existing image with the same tag if it exists
        az acr repository delete --name $(ACR_NAME) --repository $(IMAGE_NAME) --tag $(IMAGE_TAG) --yes || echo "Image could not be deleted or does not exist."
        
        # Push the new image
        docker push "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)"
        echo "Docker image pushed successfully."
      displayName: 'Push Docker Image'

# =========================================================================
# Cleanup
# =========================================================================
    
    # Clean up local Docker image to free up space
    - script: |
        echo "Removing the Docker image from the runner..."
        docker images
        docker rmi -f "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)" || echo "Image could not be removed."
        echo "Image removed."
      displayName: 'Clean up Docker Image'

    # Verify Docker image is removed and clean up build cache
    - script: |
        # Verify the Docker build cache 
        echo "Re-Checking Docker build cache..."
        docker system df
        
        # Force remove the Docker build cache
        echo "Removing Docker build cache..."
        docker system prune -a -f
        docker image prune -a -f
        
        echo "Verifying if the image is removed..."
        docker images | grep "$(IMAGE_NAME)" || echo "Image removed successfully."
      displayName: 'Verify Docker Image Removal - Cache Clean'

  # =========================================================================
  # Stage 1: Continuous Integration Stage
  # =========================================================================
  # This stage handles dependency installation, code quality checks,
  # and building the application
  # =========================================================================
  - stage: InstallDependencies_Test_BuildArtifact
    displayName: "Continuous Integration Stage"
    jobs:
      - job: InstallDependencies_Test_BuildArtifact
        displayName: "Install, Test and Build"
        steps:
          # =========================================================================
          # Environment Setup
          # =========================================================================

          # Install Node.js environment
          - task: NodeTool@0
            inputs:
              versionSpec: $(NODE_VERSION)
            displayName: "Install Node.js"

          # Install pnpm package manager
          - script: |
              echo "Installing pnpm package manager..."
              npm install -g pnpm
            displayName: "Install pnpm"

          # Install project dependencies using pnpm
          - script: |
              echo "Installing dependencies for frontend application..."
              pnpm install --no-frozen-lockfile  # Clean install dependencies
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Install dependencies"

          # Cache node_modules to improve pipeline performance
          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | $(BUILD_SOURCES_DIR)/pnpm-lock.yaml' # Cache key based on pnpm-lock.yaml
              path: $(BUILD_SOURCES_DIR)/node_modules # Directory to cache
              cacheHitVar: CACHE_RESTORED # Variable indicating if the cache was restored
            displayName: "Cache node_modules"

          # =========================================================================
          # Code Quality Checks
          # =========================================================================

          # Format code using the project's formatting rules
          - script: |
              echo "Formatting code for frontend application..."
              pnpm run format:all
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Format code"

          # Lint code to ensure code quality
          - script: |
              echo "Running linting checks for frontend application..."
              pnpm run lint || echo 'Linting failed. Please check the code quality.'

              # Uncomment the line below to make the pipeline fail on lint errors
              # pnpm run lint
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Lint code"

          # =========================================================================
          # Build and Publish Artifacts
          # =========================================================================

          # Build the application
          - script: |
              echo "Building frontend application..."
              pnpm run build
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Building project .next generation"

          # Prepare artifacts for deployment
          - script: |
              echo "Creating deployment package..."
              mkdir -p $(Build.ArtifactStagingDirectory)/app

              # Copy build output to staging directory
              echo "Copying build artifacts to staging directory..."
              cp -Rr .next $(Build.ArtifactStagingDirectory)/app/
              cp -Rr public $(Build.ArtifactStagingDirectory)/app/
              cp -Rr messages $(Build.ArtifactStagingDirectory)/app/
              cp -Rr package*.json $(Build.ArtifactStagingDirectory)/app/
              cp -Rr pnpm-lock.yaml $(Build.ArtifactStagingDirectory)/app/

              # Optional: Include version information
              echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/app/version.txt

              echo "Build completed successfully for frontend application"
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Build and prepare artifacts"

          # #Publish build artifacts
          # - task: PublishBuildArtifacts@1
          #   inputs:
          #     pathToPublish: $(Build.ArtifactStagingDirectory)
          #     artifactName: 'FrontEndBuild'
          #     publishLocation: 'Container'
          #   displayName: 'Publish Build Artifacts'

          # Add to CI pipeline
          - script: |
              echo "Running security scanning..."
              pnpm audit --audit-level=high --production --json > audit-report.json || echo "Security scanning failed"

              # Consider adding additional security tools
              # npm install -g snyk
              # snyk test
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Security Scanning"

          # Trigger CD pipeline for the dev branch - Not profitable for the moment
          # The id of the CD pipeline should be changed once the pipeline is created or re-created.
          #- script: |
          #    echo "Triggering CD pipeline..."
          #    curl -X POST \
          #      "https://dev.azure.com/AptivHostingProjects/AZ-ConnectedWorkers/_apis/pipelines/53/runs?api-version=6.0" \
          #      -H "Content-Type: application/json" \
          #      -H "Authorization: Bearer $(System.AccessToken)" \
          #      -d '{"resources":{"repositories":{"self":{"refName":"refs/heads/dev"}}}}'
          #  displayName: 'Trigger CD Pipeline'
          #  env:
          #    SYSTEM_ACCESSTOKEN: $(System.AccessToken)

          # ========================================
          # Azure CLI Setup
          # ========================================

          # Install Azure CLI to interact with Azure resources
          - script: |
              echo "Installing Azure CLI..."
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash # Install Azure CLI
            displayName: "Install Azure CLI"

          # Grant Docker permissions to avoid permission issues
          - script: |
              echo "Granting Docker socket permissions..."
              sudo chmod 666 /var/run/docker.sock # Grant Docker permissions
            displayName: "Grant Docker permissions"

          # =========================================================================
          # Azure Authentication
          # =========================================================================
          # Login to Azure using Managed Identity
          - script: |
              echo "Authenticating with Azure using Managed Identity..."
              az login --identity
              az account show
            displayName: "Login to Azure"

          # =========================================================================
          # Docker Image Build
          # =========================================================================

          # Build Docker image for the frontend
          - script: |
              echo "##############################################################################################################"
              echo "Navigating to frontend directory..."
              cd $(BUILD_SOURCES_DIR)

              echo "Building Docker image for the frontend..." 
              docker build --no-cache -f $(BUILD_SOURCES_DIR)/Dockerfile -t $(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG) .
            displayName: "Build Docker Image"
          # =========================================================================
          # Push to Azure Container Registry
          # =========================================================================

          # Login to Azure Container Registry
          - script: |
              echo "##############################################################################################################"
              echo "Logging into Azure Container Registry..."
              az acr login -n $(ACR_NAME)
            displayName: "Login to Azure Container Registry"

          # Push Docker image to ACR
          - script: |
              echo "Pushing Docker image to ACR..."
              # Delete existing image with the same tag if it exists
              az acr repository delete --name $(ACR_NAME) --repository $(IMAGE_NAME) --tag $(IMAGE_TAG) --yes || echo "Image could not be deleted or does not exist."

              # Push the new image
              docker push "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)"
              echo "Docker image pushed successfully."
            displayName: "Push Docker Image"

          # =========================================================================
          # Cleanup
          # =========================================================================

          # Clean up local Docker image to free up space
          - script: |
              echo "Removing the Docker image from the runner..."
              docker images
              docker rmi -f "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)" || echo "Image could not be removed."
              echo "Image removed."
            displayName: "Clean up Docker Image"

          # Verify Docker image is removed and clean up build cache
          - script: |
              # Verify the Docker build cache 
              echo "Re-Checking Docker build cache..."
              docker system df

              # Force remove the Docker build cache
              echo "Removing Docker build cache..."
              docker system prune -a -f
              docker image prune -a -f

              echo "Verifying if the image is removed..."
              docker images | grep "$(IMAGE_NAME)" || echo "Image removed successfully."
            displayName: "Verify Docker Image Removal - Cache Clean"
