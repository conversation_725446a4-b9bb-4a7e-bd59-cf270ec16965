# =========================================================================
# Connected Workers Frontend Pull Request Pipeline
# =========================================================================
# This pipeline is designed to run on pull requests for the frontend web application.
# It performs dependency installation, linting, formatting, security scanning,
# and builds the application to ensure code quality before merging.
#
# The pipeline is triggered on pull requests to the dev branch that affect
# the frontend code, excluding documentation changes.
# =========================================================================

name: CW-PR-Pipeline-Frontend

trigger: none

pr:
  branches:
    include:
      - dev  # Enable the pipeline for pull requests to the `dev` branch.
  paths:
    include:
      - 'core/frontend/webapp/**'  # More specific path to frontend code
    exclude:
      - '**/*.md'  # Exclude all markdown files


pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu].
  #name: MigTestPool  # Use the Self Hosted Runner for the pipeline agent [Windows].
  #vmImage: ubuntu-latest  # Use the latest Ubuntu image for the pipeline agent.

variables:
  NODE_VERSION: '20.x'  # Node.js version to use for the build
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/frontend/webapp  # Root directory of the project


stages:

# =========================================================================
# Stage 1: Install Dependencies, Test & Build Artifact
# =========================================================================
# This stage handles dependency installation, code quality checks,
# and building the application artifact
# =========================================================================
- stage: InstallDependencies_Test_BuildArtifact
  displayName: 'Install and Build Stage'

  jobs:
  - job: Install
    displayName: 'Install and Quality Checks'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install Node.js environment
    - task: NodeTool@0
      inputs:
        versionSpec: $(NODE_VERSION)
      displayName: 'Install Node.js'

    # Install pnpm package manager
    - script: |
        echo "Installing pnpm package manager..."
        npm install -g pnpm
      displayName: 'Install pnpm'

    # Install project dependencies using pnpm
    - script: |
        echo "Installing dependencies for frontend application..."
        pnpm install --no-frozen-lockfile
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Install dependencies'

    # =========================================================================
    # Code Quality Checks
    # =========================================================================
    
    # Format code using the project's formatting rules
    - script: |
        echo "Formatting code for frontend application..."
        pnpm run format:all
        
        # Optional: Check if formatting would make changes without actually changing files
        # pnpm run format:check || echo "Code formatting issues detected. Please run 'pnpm format:all' locally."
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Format code'

    # Lint code to ensure code quality
    - script: |
        echo "Running linting checks for frontend application..."
        pnpm run lint || echo "Linting failed. Please fix the issues before proceeding."
        
        # Uncomment the line below to make the pipeline fail on lint errors
        # pnpm run lint
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Lint code'

    # =========================================================================
    # Testing
    # =========================================================================
    # Uncomment this section when tests are implemented
    
    # # Run unit tests
    # - script: |
    #     echo "Running unit tests for frontend application..."
    #     pnpm run test || echo "Unit tests failed. Please fix the issues before proceeding."
    #   workingDirectory: $(BUILD_SOURCES_DIR)
    #   displayName: 'Run unit tests'
    #
    # # Generate test coverage reports
    # - script: |
    #     echo "Generating test coverage for frontend application..."
    #     pnpm run test:cov || echo "Test coverage failed. Please fix the issues before proceeding."
    #   workingDirectory: $(BUILD_SOURCES_DIR)
    #   displayName: 'Generate test coverage reports'
    #
    # # Publish test results
    # - task: PublishTestResults@2
    #   inputs:
    #     testResultsFiles: '**/test-results.xml'
    #     mergeTestResults: true
    #     testRunTitle: 'Frontend Tests'
    #   displayName: 'Publish Test Results'
    #   condition: succeededOrFailed() # Run even if tests fail

    # =========================================================================
    # Security Scanning
    # =========================================================================
    
    # Run security audit on dependencies
    - script: |
        echo "Running security audit for frontend application..."
        pnpm audit --audit-level=high --production > audit-report.txt || true
        cat audit-report.txt
        
        if grep -q "high" audit-report.txt; then
          echo "⚠️ High-severity vulnerabilities found! Please review and address these issues.";
        else
          echo "✅ No high-severity vulnerabilities found.";
        fi
        
        # Optional: Archive the audit report as an artifact
        mkdir -p $(Build.ArtifactStagingDirectory)/security
        cp audit-report.txt $(Build.ArtifactStagingDirectory)/security/
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Run security audit'

    # =========================================================================
    # Build and Publish Artifacts
    # =========================================================================
    
    # Build the application and prepare artifacts
    - script: |
        echo "Building frontend application..."
        pnpm run build  # Build the project
        
        # Create deployment package
        echo "Creating deployment package..."
        mkdir -p $(Build.ArtifactStagingDirectory)/app
        
        # Copy build output to staging directory
        echo "Copying build artifacts to staging directory..."
        cp -R .next $(Build.ArtifactStagingDirectory)/app/
        cp -R public $(Build.ArtifactStagingDirectory)/app/
        cp -R .next/standalone/* $(Build.ArtifactStagingDirectory)/app/standalone/ 2>/dev/null || true
        cp package*.json $(Build.ArtifactStagingDirectory)/app/
        
        # Optional: Include version information
        echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/app/version.txt
        
        echo "Build completed successfully for frontend application"
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Build and prepare artifacts'

    # Publish build artifacts
    #- task: PublishBuildArtifacts@1
    #  inputs:
    #    pathToPublish: $(Build.ArtifactStagingDirectory)
    #    artifactName: 'FrontEndBuild'
    #    publishLocation: 'Container'
    #  displayName: 'Publish Build Artifacts'

    # Add to PR pipeline
    - script: |
        echo "Running basic performance checks..."
        pnpm run lighthouse || echo "Performance checks failed"
      workingDirectory: $(BUILD_SOURCES_DIR)
      displayName: 'Performance Testing'
      continueOnError: true





