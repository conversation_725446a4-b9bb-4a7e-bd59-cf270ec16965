# =========================================================================
# Connected Workers Frontend Combined CI/CD Pipeline
# =========================================================================
# This pipeline combines CI and CD processes for the frontend web application.
# It performs dependency installation, code quality checks, security scanning,
# builds the application, and deploys to Azure App Services.
#
# The pipeline is triggered on commits to the stage branch that affect
# the frontend code.
# =========================================================================

name: CW-Combined-Pipeline-Frontend

trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - "core/frontend/webapp/**"
    exclude:
      - "**/*.md" # Exclude documentation changes

pr: none # This pipeline is not for PRs

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu].

variables:
  NODE_VERSION: "20.x" # Node.js version to use for the build
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/frontend/webapp # Root directory of the project
  APP1_NAME: "appserv01-NonPrd-EMEA" # Azure App1 Service name
  APP2_NAME: "appserv02-NonPrd-EMEA" # Azure App2 Service name
  IMAGE_NAME: "cw-webfrontend-dev" # Name of the Docker image
  ACR_NAME: "acr003nonprdemearepo" # Azure Container Registry name
  IMAGE_TAG: "$(Build.BuildId)" # Use the build ID as a unique tag for the Docker image

  RESOURCE_GROUP: "RG-Dev-ConnectedWorker-Apps" # Azure Resource Group name
  API_URL: "https://ingress.connected-workersdev.aptiv.com" # API URL for the application

  # TODO: Change the Azure Subscription name based on the Service Connection name

  ISSUE_OWNER: "Bn-Youssef" # Default issue owner

stages:
  # =========================================================================
  # Stage 1: Build and Test
  # =========================================================================
  # This stage handles dependency installation, code quality checks,
  # security scanning, and building the application
  # =========================================================================
  - stage: BuildAndTest
    displayName: "Build and Test"
    jobs:
      - job: BuildAndTest
        displayName: "Build, Test, and Quality Checks"
        variables:
          - group: GitHub-Secrets # Group containing GitHub secrets for issue creation
        steps:
          # =========================================================================
          # Repository Verification
          # =========================================================================

          # Verify the repository is connected and accessible
          - script: |
              echo "Current directory: $(pwd)"
              echo "Repository contents:"
              ls -la
                echo "Build sources directory:"
                ls -la $(BUILD_SOURCES_DIR)
            displayName: "Verify Repository Contents"

          # =========================================================================
          # Environment Setup
          # =========================================================================

          # Install Node.js environment
          - task: NodeTool@0
            inputs:
              versionSpec: $(NODE_VERSION)
            displayName: "Install Node.js"

          # Install pnpm package manager
          - script: |
              echo "Installing pnpm package manager..."
              npm install -g pnpm
            displayName: "Install pnpm"

          # Install project dependencies using pnpm
          - script: |
              echo "Installing dependencies for frontend application..."
              pnpm install --no-frozen-lockfile  # Clean install dependencies
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Install dependencies"

          # Cache node_modules to improve pipeline performance
          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | $(BUILD_SOURCES_DIR)/pnpm-lock.yaml' # Cache key based on pnpm-lock.yaml
              path: $(BUILD_SOURCES_DIR)/node_modules # Directory to cache
              cacheHitVar: CACHE_RESTORED # Variable indicating if the cache was restored
            displayName: "Cache node_modules"

          # =========================================================================
          # Code Quality Checks
          # =========================================================================

          # Format code using the project's formatting rules
          - script: |
              echo "Formatting code for frontend application..."
              pnpm run format:all || echo "Code formatting issues detected. Please run 'pnpm format' locally."
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Format code"

          # Perform linting to ensure code quality
          - script: |
              echo "Running linting checks for Frontend..."
              pnpm run lint 2>&1 | tee lint_errors.txt || echo "Linting failed. Please check the code and run 'pnpm run lint' locally."
              LINT_ERRORS=$(cat lint_errors.txt)
              if grep -q -E "(error|Error|ERROR)" lint_errors.txt; then
                echo "Linting failed. Errors captured:"
                echo "##vso[task.setvariable variable=LintFailed;isOutput=true]true"
              else
                echo "Linting passed successfully."
                echo "##vso[task.setvariable variable=LintFailed;isOutput=true]false"
              fi
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Lint code"
            name: "runLint"

          # Capture linting errors and create a GitHub issue if linting failed
          #    - task: Bash@3
          #      condition: eq(variables['runLint.LintFailed'], 'true')
          #      inputs:
          #        targetType: "inline"
          #        script: |
          #
          #          # Install jq if not available
          #          if ! command -v jq &> /dev/null; then
          #            echo "Installing jq..."
          #            sudo apt-get update && sudo apt-get install -y jq
          #          fi
          #
          #          # Read lint errors and clean them properly
          #          ERRORS=$(head -c 8000 "$(BUILD_SOURCES_DIR)/lint_errors.txt" | \
          #            tr -d '\0' | \
          #            iconv -f utf-8 -t utf-8 -c | \
          #            sed 's/\x1b\[[0-9;]*m//g' | \
          #            sed 's/[[:cntrl:]]//g' | \
          #            tr -cd '[:print:]\n\t' | \
          #            sed 's/[ \t]*$//' | \
          #            sed '/^$/d')
          #
          #          # Create the issue body with proper newlines
          #          BODY="## Linting Failure Report for Frontend
          #
          #          **Build Information:**
          #          - Branch: $(Build.SourceBranch)
          #          - Commit: $(Build.SourceVersion)
          #
          #          **Linting Errors:**
          #          \`\`\`
          #          ${ERRORS}
          #          \`\`\`
          #
          #          Please investigate and fix the linting issues."
          #
          #          # Use jq to properly construct JSON payload
          #          jq -n \
          #            --arg title "Linting Failures in Frontend" \
          #            --arg body "$BODY" \
          #            --argjson labels '["code-quality", "linting"]' \
          #            --argjson assignees '["$(ISSUE_OWNER)"]' \
          #            '{
          #              title: $title,
          #              body: $body,
          #              labels: $labels,
          #              assignees: $assignees
          #            }' > issue_body.json
          #
          #          # Create GitHub issue
          #          GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
          #            -H "Accept: application/vnd.github+json" \
          #            -H "Authorization: Bearer $GITHUB_TOKEN" \
          #            -H "Content-Type: application/json" \
          #            -d @issue_body.json \
          #            "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
          #
          #          # Check if the issue was created successfully
          #          if [ "$GitHub_API" -ne 201 ]; then
          #            echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
          #            echo "Response body:"
          #            cat response_body.json
          #            rm -f response_body.json issue_body.json
          #            exit 1
          #          fi
          #
          #          echo "GitHub issue created successfully for linting failures"
          #          rm -f response_body.json issue_body.json
          #          exit 1
          #        env:
          #        GITHUB_TOKEN: $(GitHub_Issues_PAT)
          #      displayName: "Create GitHub Issue for Linting Failures"

          # =========================================================================
          # Testing
          # =========================================================================
          # Run unit tests for the frontend
          - script: |
              echo "Running unit tests for frontend..."
              # Run tests and capture both output and exit code properly
              set -o pipefail  # This ensures the exit code of the first command in a pipeline is preserved
              pnpm run test 2>&1 | tee test_errors.txt
              TEST_EXIT_CODE=$?

              TEST_ERRORS=$(cat test_errors.txt)

              # Check for various failure conditions
              if [ $TEST_EXIT_CODE -ne 0 ]; then
                if grep -q "No tests found" test_errors.txt; then
                  echo "ERROR: No tests found in the project!"
                  echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
                  echo "##vso[task.logissue type=error]No unit tests found. Please add tests to the project."
                elif grep -q -E "(coverage threshold.*not met|Test Suites:.*failed|Tests:.*failed|FAIL)" test_errors.txt; then
                  echo "Unit tests or coverage failed. Errors captured:"
                  echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
                elif grep -q "ELIFECYCLE.*Command failed" test_errors.txt; then
                  echo "Unit tests failed with ELIFECYCLE error:"
                  echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
                else
                  echo "Unit tests failed with exit code $TEST_EXIT_CODE"
                  echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
                fi
              else
                echo "Unit tests passed successfully."
                echo "##vso[task.setvariable variable=TestFailed;isOutput=true]false"
              fi
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Run unit tests"
            name: "runTests"

          # Capture test errors and create a GitHub issue if tests failed
          - task: Bash@3
            condition: eq(variables['runTests.TestFailed'], 'true')
            inputs:
              targetType: "inline"
              script: |

                # Install jq if not available
                if ! command -v jq &> /dev/null; then
                  echo "Installing jq..."
                  sudo apt-get update && sudo apt-get install -y jq
                fi

                # Read test errors and clean them properly
                ERRORS=$(head -c 8000 "$(BUILD_SOURCES_DIR)/test_errors.txt" | \
                  tr -d '\0' | \
                  iconv -f utf-8 -t utf-8 -c | \
                  sed 's/\x1b\[[0-9;]*m//g' | \
                  sed 's/[[:cntrl:]]//g' | \
                  tr -cd '[:print:]\n\t' | \
                  sed 's/[ \t]*$//' | \
                  sed '/^$/d')

                # Determine the failure reason
                if grep -q "No tests found" "$(BUILD_SOURCES_DIR)/test_errors.txt"; then
                  FAILURE_REASON="No unit tests found in the project"
                  ISSUE_TITLE="Missing Unit Tests in Frontend"
                elif grep -q "coverage threshold.*not met" "$(BUILD_SOURCES_DIR)/test_errors.txt"; then
                  FAILURE_REASON="Unit test coverage not met"
                  ISSUE_TITLE="Unit Test Coverage Failed in Frontend"
                else
                  FAILURE_REASON="Unit tests failed"
                  ISSUE_TITLE="Unit Tests Failed in Frontend"
                fi

                # Create the issue body with proper newlines
                BODY="## Unit Test Issue Report for Frontend

                **Build Information:**
                - Branch: $(Build.SourceBranch)
                - Commit: $(Build.SourceVersion)
                - Reason: ${FAILURE_REASON}

                **Test Output:**
                \`\`\`
                ${ERRORS}
                \`\`\`

                **Action Required:**
                $(if grep -q "No tests found" "$(BUILD_SOURCES_DIR)/test_errors.txt"; then echo "Please add unit tests to the project. All components and pages should have corresponding test files."; elif grep -q "coverage threshold.*not met" "$(BUILD_SOURCES_DIR)/test_errors.txt"; then echo "Please add unit tests for the pages that don't meet the 100% coverage. All page.tsx files require 100% test coverage."; else echo "Please investigate and fix the failing tests."; fi)"

                # Use jq to properly construct JSON payload
                jq -n \
                  --arg title "$ISSUE_TITLE" \
                  --arg body "$BODY" \
                  --argjson labels '["Testing", "bug"]' \
                  --argjson assignees '["$(ISSUE_OWNER)"]' \
                  '{
                    title: $title,
                    body: $body,
                    labels: $labels,
                    assignees: $assignees
                  }' > issue_body.json

                # Create GitHub issue
                GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
                  -H "Accept: application/vnd.github+json" \
                  -H "Authorization: Bearer $GITHUB_TOKEN" \
                  -H "Content-Type: application/json" \
                  -d @issue_body.json \
                  "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")

                # Check if the issue was created successfully
                if [ "$GitHub_API" -ne 201 ]; then
                  echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
                  echo "Response body:"
                  cat response_body.json
                  rm -f response_body.json issue_body.json
                  exit 1
                fi

                echo "GitHub issue created successfully for test failures"
                rm -f response_body.json issue_body.json
                exit 1
            env:
              GITHUB_TOKEN: $(GitHub_Issues_PAT)
            displayName: "Create GitHub Issue for Test Failures"

          # Generate test coverage reports
          - script: |
              echo "Generating test coverage for frontend application..."
              pnpm run test:cov || echo "Test coverage generation failed, please check the code quality."
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Generate test coverage reports"

          # Run performance tests
          - script: |
              echo "Running basic performance checks..."
              pnpm run lighthouse || echo "Performance checks failed"
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Performance Testing"
            continueOnError: true

          # =========================================================================
          # Security Scanning
          # =========================================================================

          # Run security audit on dependencies
          - script: |
              echo "Running security audit for frontend application..."
              pnpm audit --audit-level=high --production 2>&1 | tee security_audit_errors.txt || echo "Security audit found high severity issues. Please fix the issues before proceeding."
              AUDIT_ERRORS=$(cat security_audit_errors.txt)
              if grep -q -E "(high|critical)" security_audit_errors.txt; then
                echo "Security audit failed. High/Critical vulnerabilities found:"
                echo "##vso[task.setvariable variable=SecurityFailed;isOutput=true]true"
              else
                echo "Security audit passed successfully."
                echo "##vso[task.setvariable variable=SecurityFailed;isOutput=true]false"
              fi
              # Optional: Generate a security report
              pnpm audit --json > $(Build.ArtifactStagingDirectory)/security-audit.json || echo "Failed to generate security audit JSON report"
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Run security audit"
            name: "runSecurityAudit"

          # Capture security audit errors and create a GitHub issue if audit failed
          #    - task: Bash@3
          #      condition: eq(variables['runSecurityAudit.SecurityFailed'], 'true')
          #      inputs:
          #        targetType: "inline"
          #        script: |
          #
          #          # Install jq if not available
          #          if ! command -v jq &> /dev/null; then
          #            echo "Installing jq..."
          #            sudo apt-get update && sudo apt-get install -y jq
          #          fi
          #
          #          # Read security audit errors and clean them properly
          #          ERRORS=$(head -c 8000 "$($BUILD_SOURCES_DIR)/security_audit_errors.txt" | \
          #            tr -d '\0' | \
          #            iconv -f utf-8 -t utf-8 -c | \
          #            sed 's/\x1b\[[0-9;]*m//g' | \
          #            sed 's/[[:cntrl:]]//g' | \
          #            tr -cd '[:print:]\n\t' | \
          #            sed 's/[ \t]*$//' | \
          #            sed '/^$/d')
          #
          #          # Create the issue body with proper newlines
          #          BODY="## Security Vulnerability Report
          #
          #          **Build Information for Frontend:**
          #          - Branch: $(Build.SourceBranch)
          #          - Commit: $(Build.SourceVersion)
          #
          #          **Security Issues:**
          #          \`\`\`
          #          ${ERRORS}
          #          \`\`\`
          #
          #          **Action Required:**
          #          High or critical severity vulnerabilities have been detected in the dependencies. Please review and update the affected packages to secure versions."
          #
          #          # Use jq to properly construct JSON payload
          #          jq -n \
          #            --arg title "Security Vulnerabilities in Frontend" \
          #            --arg body "$BODY" \
          #            --argjson labels '["security", "vulnerability", "high-priority"]' \
          #            --argjson assignees '["$(ISSUE_OWNER)"]' \
          #            '{
          #              title: $title,
          #              body: $body,
          #              labels: $labels,
          #              assignees: $assignees
          #            }' > issue_body.json
          #
          #          # Create GitHub issue
          #          GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
          #            -H "Accept: application/vnd.github+json" \
          #            -H "Authorization: Bearer $GITHUB_TOKEN" \
          #            -H "Content-Type: application/json" \
          #            -d @issue_body.json \
          #            "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
          #
          #          # Check if the issue was created successfully
          #          if [ "$GitHub_API" -ne 201 ]; then
          #            echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
          #            echo "Response body:"
          #            cat response_body.json
          #            rm -f response_body.json issue_body.json
          #            exit 1
          #          fi
          #
          #          echo "GitHub issue created successfully for security vulnerabilities"
          #          rm -f response_body.json issue_body.json
          #          exit 1
          #      env:
          #        GITHUB_TOKEN: $(GitHub_Issues_PAT)
          #      displayName: "Create GitHub Issue for Security Vulnerabilities"

          # =========================================================================
          # Build and Publish Artifacts
          # =========================================================================
          # Build the application
          - script: |
              echo "Building frontend application..."
              pnpm run build 2>&1 | tee build_errors.txt || echo "Build failed for frontend"
              BUILD_ERRORS=$(cat build_errors.txt)
              if grep -q -E "(error|Error|ERROR|failed|Failed|FAILED)" build_errors.txt; then
                echo "Build failed. Errors captured:"
                echo "##vso[task.setvariable variable=BuildFailed;isOutput=true]true"
              else
                echo "Build completed successfully."
                echo "##vso[task.setvariable variable=BuildFailed;isOutput=true]false"
                
                # Create directory if it doesn't exist
                mkdir -p $(Build.ArtifactStagingDirectory)
                
                # Copy built files to the staging directory
                echo "Copying build artifacts to staging directory..."
                
                # Copy the entire .next directory
                echo "Copying .next build output..."
                cp -R .next $(Build.ArtifactStagingDirectory)/
                
                # Copy public directory if it exists
                if [ -d "public" ]; then
                  echo "Copying public directory..."
                  cp -R public $(Build.ArtifactStagingDirectory)/
                fi
                
                # Copy essential files
                echo "Copying essential files..."
                cp package.json $(Build.ArtifactStagingDirectory)/
                cp next.config.* $(Build.ArtifactStagingDirectory)/ 2>/dev/null || echo "No next.config file found"
                
                # Create a simple version file
                echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/version.txt
                
                # Clean up any broken symlinks
                echo "Cleaning up broken symlinks..."
                find $(Build.ArtifactStagingDirectory) -type l ! -exec test -e {} \; -delete 2>/dev/null || echo "No broken symlinks found"
                echo "Build completed successfully for Frontend"
              fi
            workingDirectory: $(BUILD_SOURCES_DIR)
            displayName: "Building artifact"
            name: "runBuild"

          # Capture build errors and create a GitHub issue if build failed
          - task: Bash@3
            condition: eq(variables['runBuild.BuildFailed'], 'true')
            inputs:
              targetType: "inline"
              script: |

                # Install jq if not available
                if ! command -v jq &> /dev/null; then
                  echo "Installing jq..."
                  sudo apt-get update && sudo apt-get install -y jq
                fi

                # Read build errors and clean them properly
                ERRORS=$(head -c 8000 "$(BUILD_SOURCES_DIR)/build_errors.txt" | \
                  tr -d '\0' | \
                  iconv -f utf-8 -t utf-8 -c | \
                  sed 's/\x1b\[[0-9;]*m//g' | \
                  sed 's/[[:cntrl:]]//g' | \
                  tr -cd '[:print:]\n\t' | \
                  sed 's/[ \t]*$//' | \
                  sed '/^$/d')

                # Create the issue body with proper newlines
                BODY="## Build Failure Report for frontend

                **Build Information for Frontend:**
                - Branch: $(Build.SourceBranch)
                - Commit: $(Build.SourceVersion)
                - Reason: Build failed

                **Build Errors:**
                \`\`\`
                ${ERRORS}
                \`\`\`

                Please investigate and fix the build issues."

                # Use jq to properly construct JSON payload
                jq -n \
                  --arg title "Build Failures in Frontend" \
                  --arg body "$BODY" \
                  --argjson labels '["bug", "build"]' \
                  --argjson assignees '["$(ISSUE_OWNER)"]' \
                  '{
                    title: $title,
                    body: $body,
                    labels: $labels,
                    assignees: $assignees
                  }' > issue_body.json

                # Create GitHub issue
                GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
                  -H "Accept: application/vnd.github+json" \
                  -H "Authorization: Bearer $GITHUB_TOKEN" \
                  -H "Content-Type: application/json" \
                  -d @issue_body.json \
                  "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")

                # Check if the issue was created successfully
                if [ "$GitHub_API" -ne 201 ]; then
                  echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
                  echo "Response body:"
                  cat response_body.json
                  rm -f response_body.json issue_body.json
                  exit 1
                fi

                echo "GitHub issue created successfully for build failures"
                rm -f response_body.json issue_body.json
                exit 1
            env:
              GITHUB_TOKEN: $(GitHub_Issues_PAT)
            displayName: "Create GitHub Issue for Build Failures"

          # Publish build artifacts for further stages or downloads
          - task: PublishBuildArtifacts@1
            condition: eq(variables['runBuild.BuildFailed'], 'false')
            inputs:
              pathToPublish: $(Build.ArtifactStagingDirectory) # Path to the build directory
              artifactName: "frontend-artifact" # Name of the artifact
              publishLocation: "Container" # Publish to Azure Pipelines
            displayName: "Publish build artifacts"

  # =========================================================================
  # Stage 2: Build Docker Image
  # =========================================================================
  # This stage builds and pushes the Docker image to Azure Container Registry
  # =========================================================================
  - stage: BuildDockerImage
    displayName: "Build Docker Image"
    dependsOn: BuildAndTest
    condition: succeeded()
    jobs:
      - job: DockerBuildPush
        displayName: "Build and Push Docker Image"
        steps:
          # =========================================================================
          # Docker Environment Setup
          # =========================================================================
          # Install Docker to build the Docker image
          - task: Bash@3
            displayName: "Install and Configure Docker"
            retryCountOnTaskFailure: 3
            inputs:
              targetType: "inline"
              script: |
                echo "Installing Docker..."
                # Remove old versions if they exist
                timeout 10m bash -c "
                  sudo apt-get remove docker docker-engine docker.io containerd runc || true

                  sudo apt-get update
                  sudo apt-get install -y \
                      apt-transport-https \
                      ca-certificates \
                      curl \
                      gnupg \
                      lsb-release

                  curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

                  echo \
                    \"deb [arch=\$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
                    \$(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

                  sudo apt-get update
                  sudo apt-get install -y docker-ce docker-ce-cli containerd.io

                  sudo systemctl stop docker || true
                  sudo mkdir -p /etc/docker
                  sudo mkdir -p /etc/systemd/system/docker.service.d

                  echo '{
                    \"exec-opts\": [\"native.cgroupdriver=systemd\"],
                    \"log-driver\": \"json-file\",
                    \"log-opts\": {
                      \"max-size\": \"100m\"
                    },
                    \"storage-driver\": \"overlay2\"
                  }' | sudo tee /etc/docker/daemon.json

                  sudo systemctl daemon-reload
                  sudo systemctl start docker
                  sudo systemctl enable docker

                  echo \"Waiting for Docker daemon to start...\"
                  sleep 20

                  sudo chmod 666 /var/run/docker.sock
                  sudo usermod -aG docker $USER

                  echo \"Verifying Docker setup...\"
                  docker version
                  docker info

                  echo \"Testing Docker with hello-world...\"
                  docker run hello-world
                " || { echo "Docker installation failed. This task will retry 3 times."; exit 1; }

          # Additional Docker verification step
          - script: |
              echo "Performing extended Docker verification..."
              # Check Docker socket permissions
              ls -l /var/run/docker.sock
              # Check Docker service status
              sudo systemctl status docker
              # Verify Docker can pull images
              docker pull alpine:latest
              # Run a test container
              docker run --rm alpine echo "Docker is working correctly"
            displayName: "Verify Docker Setup"
            timeoutInMinutes: 5
          # ========================================
          # Azure CLI Setup
          # ========================================
          # Install Azure CLI to interact with Azure resources
          - script: |
              echo "Installing Azure CLI..."
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash # Install Azure CLI
            displayName: "Install Azure CLI"

          # =========================================================================
          # Azure Authentication
          # =========================================================================

          # Login to Azure using Managed Identity
          - script: |
              echo "Authenticating with Azure using Managed Identity..."
              az login --identity
              az account show
            displayName: "Login to Azure"

          # =========================================================================
          # Docker Image Build
          # =========================================================================

          # Build Docker image for the frontend
          - script: |
              echo "##############################################################################################################"
              echo "Navigating to frontend directory..."
              cd $(BUILD_SOURCES_DIR)

              echo "Building Docker image for the frontend..." 
              docker build --no-cache -f $(BUILD_SOURCES_DIR)/Dockerfile -t $(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG) --build-arg NEXT_PUBLIC_API_URL=$(API_URL) .
            displayName: "Build Docker Image"

          # =========================================================================
          # Push to Azure Container Registry
          # =========================================================================

          # Login to Azure Container Registry
          - script: |
              echo "##############################################################################################################"
              echo "Logging into Azure Container Registry..."
              az acr login -n $(ACR_NAME)
            displayName: "Login to Azure Container Registry"

          # Push Docker image to ACR
          - script: |
              echo "Pushing Docker image to ACR..."
              # Delete existing image with the same tag if it exists
              az acr repository delete --name $(ACR_NAME) --repository $(IMAGE_NAME) --tag $(IMAGE_TAG) --yes || echo "Image could not be deleted or does not exist."

              # Push the new image
              docker push "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)"
              echo "Docker image pushed successfully."
            displayName: "Push Docker Image"

          # =========================================================================
          # Cleanup
          # =========================================================================

          # Clean up local Docker image to free up space
          - script: |
              echo "Removing the Docker image from the runner..."
              docker images
              docker rmi -f "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)" || echo "Image could not be removed."
              echo "Image removed."
            displayName: "Clean up Docker Image"

          # Verify Docker image is removed and clean up build cache
          - script: |
              # Verify the Docker build cache 
              echo "Re-Checking Docker build cache..."
              docker system df

              # Force remove the Docker build cache
              echo "Removing Docker build cache..."
              docker system prune -a -f
              docker image prune -a -f

              echo "Verifying if the image is removed..."
              docker images | grep "$(IMAGE_NAME)" || echo "Image removed successfully."
            displayName: "Verify Docker Image Removal - Cache Clean"

  # =========================================================================
  # Stage 3: Deploy image to Azure App Service Stage
  # =========================================================================
  - stage: DeployImage
    displayName: "Deploy Docker Image"
    dependsOn: BuildDockerImage
    condition: succeeded()
    jobs:
      - job: DeployDockerImage
        displayName: "Deploy Docker Image to Azure App Service"
        steps:
          - task: AzureWebAppContainer@1
            inputs:
              azureSubscription: "$(RESOURCE_GROUP)"
              appName: "$(APP1_NAME)"
              resourceGroupName: "$(RESOURCE_GROUP)"
              containers: "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)"
            displayName: "Deploy image to Azure App Service"

          - task: AzureWebAppContainer@1
            inputs:
              azureSubscription: "$(RESOURCE_GROUP)"
              appName: "$(APP2_NAME)"
              resourceGroupName: "$(RESOURCE_GROUP)"
              containers: "$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)"
            displayName: "Deploy image to Azure App Service"
# =========================================================================
# Stage 2: Deploy to Azure App Services
# =========================================================================
# This stage deploys the application to Azure App Services
# =========================================================================
#- stage: Deploy
#  displayName: 'Deploy to Azure App Services'
#  dependsOn: BuildAndTest
#  condition: succeeded()
#  jobs:
#  - deployment: DeployWebApp
#    displayName: 'Deploy Next.js App to Azure App Services'
#    environment: 'development'
#    strategy:
#      runOnce:
#        deploy:
#          steps:
#          # =========================================================================
#          # Deploy to Primary App Service
#          # =========================================================================
#
#          # Deploy to first Azure Web App instance
#          - task: AzureRmWebAppDeployment@4
#            inputs:
#              ConnectionType: 'AzureRM'
#              azureSubscription: '$(RESOURCE_GROUP)'
#              appType: 'webApp'
#              WebAppName: '$(APP1_NAME)'
#              packageForLinux: '$(Pipeline.Workspace)/FrontEndBuild/app'
#              enableCustomDeployment: true
#              DeploymentType: 'zipDeploy'
#              StartupCommand: 'cd /home/<USER>/wwwroot && echo "Hello from App Service 1" > health.txt'
#              # StartupCommand: 'cd /home/<USER>/wwwroot && npm install --force'
#            displayName: 'Deploy to Azure Web App-1'
#
#          # =========================================================================
#          # Deploy to Secondary App Service
#          # =========================================================================
#
#          # Deploy to second Azure Web App instance
#          - task: AzureRmWebAppDeployment@4
#            inputs:
#              ConnectionType: 'AzureRM'
#              azureSubscription: '$(RESOURCE_GROUP)'
#              appType: 'webApp'
#              WebAppName: '$(APP2_NAME)'
#              packageForLinux: '$(Pipeline.Workspace)/FrontEndBuild/app'
#              enableCustomDeployment: true
#              DeploymentType: 'zipDeploy'
#              StartupCommand: 'cd /home/<USER>/wwwroot && echo "Hello from App Service 2" > health.txt'
#              # StartupCommand: 'cd /home/<USER>/wwwroot && npm install --force'
#            displayName: 'Deploy to Azure Web App-2'

# =========================================================================
# Deployment Verification
# =========================================================================

#- script: |
#    echo "Verifying deployment to $(APP1_NAME)..."
#    curl -s https://$(APP1_NAME).azurewebsites.net/health || echo "Health check failed for App Service 1"
#
#    echo "Verifying deployment to $(APP2_NAME)..."
#    curl -s https://$(APP2_NAME).azurewebsites.net/health || echo "Health check failed for App Service 2"
#  displayName: 'Verify Deployment'
#  continueOnError: true

# =========================================================================
# Rollback Strategy
# =========================================================================
# This step will deploy the previous successful build if the current deployment fails
#- task: AzureRmWebAppDeployment@4
#  condition: failed()
#  inputs:
#    ConnectionType: 'AzureRM'
#    azureSubscription: '$(RESOURCE_GROUP)'
#    appType: 'webApp'
#    WebAppName: '$(APP1_NAME)'
#    packageForLinux: '$(Pipeline.Workspace)/previous-successful-build'
#    enableCustomDeployment: true
#    DeploymentType: 'zipDeploy'
#  displayName: 'Rollback Deployment App-1'
#  continueOnError: true

#- task: AzureRmWebAppDeployment@4
#  condition: failed()
#  inputs:
#    ConnectionType: 'AzureRM'
#    azureSubscription: '$(RESOURCE_GROUP)'
#    appType: 'webApp'
#    WebAppName: '$(APP2_NAME)'
#    packageForLinux: '$(Pipeline.Workspace)/previous-successful-build'
#    enableCustomDeployment: true
#    DeploymentType: 'zipDeploy'
#  displayName: 'Rollback Deployment App-2'
#  continueOnError: true
