# Frontend Pipeline Documentation

## Overview
This repository contains Azure DevOps pipeline configurations for the frontend Next.js application. The pipelines are designed to work together in a sequence, from PR validation through to production deployment.

![Frontend Pipeline Architecture](https://mermaid.ink/img/pako:eNqNkk9PwzAMxb9KlBOI9QNwQKCJw4QYEhyAQy5NvK6iTSrHQYOq786aFgZiB3rxK_7l_ew8oEkNYoQbZ3vvjCXYOm-JvHFkKZANpJwdyZHfUfKWYJPJkYJnWOcpkXgbKGQXyZPNJJvJkYu0Iy_QRnIUyeQcuQg7iiZHMZnMkfMUKdrkHLlIO_LJ5Bw5SjsKyeQcuUg78iaZHMVI2lFMJufIRdqRTyZHMZF2FJLJOXKRduSTyTlyJe0oJJNz5CLtyJtkcpRIaUfRJJNz5CLtyJtkcuQr0o5CMjlHLtKOfDI5R66lHYVkco5cpB15k0yOopJ2FE0yOUcu0o68SSZHXks7CsnkHLlIO_LJ5By5kXYUksk5cpF25E0yOYpG2lE0yeQcuUg78iaZHPla2lFIJufIRdqRTyZHvpN2FJLJOfIXaUfBJJOjaCXtKJpkco5cpB15k0yOfCftKCSTs-QfLgEbxQ?type=png)

## Pipeline Flow

### Pipeline Types and Triggers
1. **PR Pipeline** (Pull Request Validation)
   - Triggered by pull requests to `dev` branch
   - Path filters:
     - Include: `**/frontend/webapp/**`
     - Exclude: `**/*.md`

2. **CI Pipeline** (Continuous Integration)
   - Triggered by commits to `dev` branch
   - Path filters: `core/frontend/webapp/**`
   - Builds and tests the application
   - Creates Docker images
   - Pushes to Azure Container Registry

3. **CD Pipeline** (Continuous Deployment)
   - Triggered by successful CI pipeline completion
   - Deploys to development environment
   - Validates deployment status

## Pipeline Stages Detailed Workflow

### PR Pipeline Workflow
![PR Pipeline Flow](https://mermaid.ink/img/pako:eNqNkk9rwzAMxb-K8KmFdp9jsCZbd9jWwzrYLsYf1mAXO9hOoaXsd5-0dMla1sNOlp5-7z1JPoA2GsQKdtYO3mlDsHfOEznjyFAgG0g5O5Ijv6PkLME-kyMXPMM2T4nE20Ahu0iebCbZTY5cpB15gTaSI08m58hF2FEwOYrJZI6cp0jRJufIRdqRTybnyCntKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Install Dependencies**
   - Installs Node.js (v20.x)
   - Sets up pnpm
   - Installs project dependencies
   - Caches node_modules for performance

2. **Lint and Format**
   - Runs code formatting checks
   - Validates code style
   - Ensures code quality standards

3. **Build and Test**
   - Builds the Next.js application
   - Runs unit tests
   - Validates application functionality
   - Generates test coverage reports

### CI Pipeline Workflow
![CI Pipeline Flow](https://mermaid.ink/img/pako:eNqFksFqwzAMhl9F-NRCu-cQWMnWHQbrsIXtYvxhNWaxg-0UWsrefdLSJWvZQTpZ-vX9kiwfoDUGsYKdtYN3xhLsnfNEzjiyFMgGUs6O5MjvKDlLsM_kyAXPsM1TIvE2UMgukiebSXaTIxdpR16gjeTIk8k5chF2FEyOYjKZI-cpUrTJOXKRduSTyTlylHYUksk5cpF25E0yOYqRtKOYTM6Ri7Qjn0yOYiLtKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Install Dependencies**
   - Installs Node.js (v20.x)
   - Sets up pnpm
   - Installs project dependencies
   - Caches node_modules for performance

2. **Build and Test**
   - Builds the Next.js application
   - Runs unit tests
   - Validates application functionality
   - Generates test coverage reports

3. **Docker Build and Push**
   - Builds Docker image
   - Pushes to Azure Container Registry (ACR)
   - Tags with appropriate version
   - Publishes build artifacts

### CD Pipeline Workflow
![CD Pipeline Flow](https://mermaid.ink/img/pako:eNqNkk9rwzAMxb-K8KmFdp9jsCZbd9jWwzrYLsYf1mAXO9hOoaXsd5-0dMla1sNOlp5-7z1JPoA2GsQKdtYO3mlDsHfOEznjyFAgG0g5O5Ijv6PkLME-kyMXPMM2T4nE20Ahu0iebCbZTY5cpB15gTaSI08m58hF2FEwOYrJZI6cp0jRJufIRdqRTybnyCntKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Download Artifacts**
   - Downloads build artifacts from CI pipeline
   - Prepares for deployment

2. **Deploy to Dev Environment**
   - Deploys to Azure Web App
   - Configures environment variables
   - Sets up application settings
   - Validates deployment status

## Pipeline Parameters and Variables

### CI Pipeline Variables
| Variable | Description | Default |
|----------|-------------|---------|
| `BUILD_SOURCES_DIR` | Directory containing the source code | `$(Build.SourcesDirectory)/core/frontend/webapp` |
| `NODE_VERSION` | Node.js version | `20.x` |
| `DOCKER_REPOSITORY` | Docker repository name | `frontend-webapp` |
| `ACR_NAME` | Azure Container Registry name | `acr003nonprdemearepo` |

### CD Pipeline Variables
| Variable | Description | Default |
|----------|-------------|---------|
| `AZURE_SUBSCRIPTION` | Azure subscription for deployment | `Azure-Dev-Subscription` |
| `APP_NAME` | Azure Web App name | `cw-frontend-webapp-dev` |
| `APP_SERVICE_PLAN` | App Service Plan | `ASP-RGDevConnWorkers-9a40` |
| `RESOURCE_GROUP` | Azure Resource Group | `RG-Dev-ConnWorkers` |

## Benefits of Reusable Pipelines

### 1. Consistency
- Standardized build and deployment processes
- Uniform testing and quality checks
- Consistent security practices
- Predictable pipeline behavior

### 2. Maintainability
- Centralized template management
- Reduced duplication
- Easier updates and improvements
- Simplified onboarding for new features

### 3. Time Savings
- Faster implementation of new features
- Reduced time spent on pipeline configuration
- Quick implementation of changes
- Reusable code blocks and templates

### 4. Quality Control
- Consistent quality checks across all builds
- Standardized security scanning
- Uniform testing practices
- Centralized policy enforcement

## Best Practices
1. Always use the template files for consistency
2. Maintain proper versioning for Docker images
3. Keep security scanning enabled
4. Ensure proper test coverage
5. Use caching for better pipeline performance

## Security Considerations
- Security audit runs on production dependencies
- Docker images are scanned for vulnerabilities
- Azure authentication uses managed identities
- Access controls are implemented through Azure RBAC

## Troubleshooting

### Common Issues
1. **Build Failures**
   - Check dependency versions
   - Verify Node.js compatibility
   - Review test failures
   - Check for linting errors

2. **Deployment Issues**
   - Validate Azure credentials
   - Check resource permissions
   - Review deployment logs

### Monitoring
- Azure DevOps pipeline logs
- App Service logs
- Build and deployment artifacts
- Pipeline metrics (success rates, duration)

## Contact and Support
- **Team**: Frontend Development Team
- **Repository**: core/frontend/webapp
- **Pipeline Configs**: core/DevSecOps/frontend/ 