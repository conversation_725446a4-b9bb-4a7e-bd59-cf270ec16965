# =========================================================================
# Connected Workers Frontend Continuous Deployment Pipeline
# =========================================================================
# This pipeline is designed for continuous deployment of the frontend web application.
# It deploys the built application to Azure App Services.
#
# The pipeline is triggered by the successful completion of the CI pipeline.
# =========================================================================

name: CW-CD-Pipeline-Frontend

trigger: none
pr: none

resources:
  pipelines:
  - pipeline: CI_Pipeline
    project: AZ-ConnectedWorkers
    source: CW-CI-Pipeline-Frontend
    trigger:
      branches:
        include:
          - dev

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu].

variables:
  APP1_NAME: 'appserv01-NonPrd-EMEA'  # Azure App1 Service name
  APP2_NAME: 'appserv02-NonPrd-EMEA'  # Azure App2 Service name
  RESOURCE_GROUP: 'RG-Dev-ConnectedWorker-Apps'  # Azure Resource Group name

  # TODO: Change the Azure Subscription name based on the Service Connection name
  AZURE_SUBSCRIPTION: 'RG-Dev-ConnectedWorker-Apps'  # Azure Subscription name - Based on the Service Connection name

  IMAGE_NAME: 'cw-webfrontend-dev'  # Name of the Docker image
  ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name - Based on the Service Connection name
  IMAGE_TAG: 'latest'  # Use the latest build tag for the Docker image

stages:
- stage: DeployImage
  displayName: 'Deploy Docker Image'
  jobs:
  - job: DeployDockerImage
    displayName: 'Deploy Docker Image to Azure App Service'
    steps:
     - task: AzureWebAppContainer@1
       inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        appName: '$(APP1_NAME)' 
        resourceGroupName: '$(RESOURCE_GROUP)' 
        containers: '$(ACR_NAME).azurecr.io/$(IMAGE_NAME):$(IMAGE_TAG)'
       displayName: 'Deploy image to Azure App Service'


# # =========================================================================
# # Stage 1: Deploy to Azure App Service
# # =========================================================================
# # This stage deploys the application to Azure App Services
# # =========================================================================
# - stage: Deploy
#   displayName: 'Deploy to Azure App Service'
#   jobs:
#   - deployment: DeployWebApp
#     displayName: 'Deploy Next.js App to Azure App Service'
#     environment: 'development'
#     strategy:
#       runOnce:
#         deploy:
#           steps:
#           # =========================================================================
#           # Artifact Download
#           # =========================================================================
          
#           # Download build artifacts from CI pipeline
#           - download: CI_Pipeline
#             artifact: 'FrontEndBuild'
#             displayName: 'Download Build Artifacts'

#           # =========================================================================
#           # Deploy to Primary App Service
#           # =========================================================================
          
#           # Deploy to first Azure Web App instance
#           - task: AzureRmWebAppDeployment@4
#             inputs:
#               ConnectionType: 'AzureRM'
#               azureSubscription: '$(AZURE_SUBSCRIPTION)'
#               appType: 'webApp'
#               WebAppName: '$(APP1_NAME)'
#               packageForLinux: '$(Pipeline.Workspace)/CI_Pipeline/FrontEndBuild/app'
#               enableCustomDeployment: true
#               DeploymentType: 'zipDeploy'
#               StartupCommand: 'cd /home/<USER>/wwwroot && echo "Hello from App Service 1" > health'
#             displayName: 'Deploy to Azure Web App-1'
          
#           # =========================================================================
#           # Deploy to Secondary App Service
#           # =========================================================================
          
#           # Deploy to second Azure Web App instance
#           - task: AzureRmWebAppDeployment@4
#             inputs:
#               ConnectionType: 'AzureRM'
#               azureSubscription: '$(AZURE_SUBSCRIPTION)'
#               appType: 'webApp'
#               WebAppName: '$(APP2_NAME)'
#               packageForLinux: '$(Pipeline.Workspace)/CI_Pipeline/FrontEndBuild/app'
#               enableCustomDeployment: true
#               DeploymentType: 'zipDeploy'
#               StartupCommand: 'cd /home/<USER>/wwwroot && echo "Hello from App Service 2" > health'
#             displayName: 'Deploy to Azure Web App-2'

          # =========================================================================
          # Optional: Deployment Verification
          # =========================================================================
          # Uncomment this section to add deployment verification steps
          # This section will verify that the deployment to both App Services is successful
          # - script: |
          #     echo "Verifying deployment to $(APP1_NAME)..."
          #     curl -s https://$(APP1_NAME).azurewebsites.net/health || echo "Health check failed for App Service 1"
          #     
          #     echo "Verifying deployment to $(APP2_NAME)..."
          #     curl -s https://$(APP2_NAME).azurewebsites.net/health || echo "Health check failed for App Service 2"
          #   displayName: 'Verify Deployment'
          #   continueOnError: true
           
          
          # =========================================================================
          # Example rollback step
          # =========================================================================
          # Uncomment this section to add a rollback step
          # This step will deploy the previous successful build if the current deployment fails
          #- task: AzureRmWebAppDeployment@4
          #  condition: failed()
          #  inputs:
          #    ConnectionType: 'AzureRM'
          #    azureSubscription: '$(AZURE_SUBSCRIPTION)'
          #    appType: 'webApp'
          #    WebAppName: '$(APP1_NAME)'
          #    packageForLinux: '$(Pipeline.Workspace)/previous-successful-build'
          #    enableCustomDeployment: true
          #    DeploymentType: 'zipDeploy'
          #  displayName: 'Rollback Deployment'



# =========================================================================
# Optional: Alternative Deployment Method
# =========================================================================
# This section contains an alternative deployment approach using Azure CLI
# It's commented out but can be used if the AzureRmWebAppDeployment task
# doesn't meet your requirements
# =========================================================================

# - script: |
#     echo "Installing Azure CLI..."
#     curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
#   displayName: 'Install Azure CLI'
#
# - script: |
#     echo "Authenticating with Azure using Managed Identity..."
#     az login --identity
#     
#     echo "Listing resource groups..."
#     az group list --output table
#
#     echo "Listing available App Services..."
#     az webapp list --resource-group $(RESOURCE_GROUP) --output table
#   displayName: 'Login to Azure'
#
# - task: DownloadPipelineArtifact@2
#   inputs:
#     buildType: 'specific'
#     project: 'AZ-ConnectedWorkers'  # Azure DevOps project name
#     pipeline: '13' # CI pipeline ID
#     runVersion: 'latest'
#     artifact: 'drop'
#     targetPath: '$(Build.ArtifactStagingDirectory)'
#   displayName: 'Download Build Artifacts'
#
# - script: |
#     sudo apt-get update -y
#     sudo apt-get install -y zip
#   displayName: 'Install zip utility'
#
# - task: ArchiveFiles@2
#   inputs:
#     rootFolderOrFile: '$(Build.ArtifactStagingDirectory)'
#     includeRootFolder: false
#     archiveType: 'zip'
#     archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
#     replaceExistingArchive: true
#   displayName: 'Archive build output'
#
# - task: AzureWebApp@1
#   inputs:
#     azureSubscription: $(AZURE_SUBSCRIPTION)
#     appType: 'webAppLinux'
#     appName: $(APP_NAME)
#     package: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
#     #slotName: 'staging'
#   displayName: 'Deploy to Azure Web App'



