# =========================================================================
# ProductionPlan Microservice Pull Request Pipeline
# =========================================================================
# This pipeline is designed to run on pull requests for the ProductionPlan microservice.
# It performs build, testing, and code coverage analysis to ensure code quality
# before merging.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name and other configuration options.
# =========================================================================

name: CW-PR-Pipeline-ProductionPlan-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
      - feature/production-plan-latest
  paths:
    include:
      - '**/microservices/dotnetMicroservice/Headcount Control.TKS/ProductionPlan/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'productionplan'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/Headcount Control.TKS/ProductionPlan
    DOTNET_VERSION: '8.0.x'  # .NET SDK version to use for the build
