# =========================================================================
# ProductionPlan Microservice Combined CI/CD Pipeline
# =========================================================================
# This pipeline is designed for continuous integration and deployment of the ProductionPlan microservice.
# It performs build, testing, creates and pushes a Docker image to Azure Container Registry,
# and deploys the image to an AKS cluster.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name and other configuration options.
# =========================================================================

name: CW-CICD-Pipeline-ProductionPlan-Microservice

trigger:
  branches:
    include:
      - dev
      - feature/production-plan-latest
  paths:
    include:
      - '**/microservices/dotnetMicroservice/Headcount Control.TKS/ProductionPlan/**'
      - '**/core/DevSecOps/helm/productionplan/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

# Define variables used across the pipeline
variables:
  # Microservice specific variables
  MICROSERVICE_NAME: 'productionplan'
  
  # Environment and configuration variables
  DOTNET_VERSION: '8.0.x'  # .NET SDK version to use for the build
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/Headcount Control.TKS/ProductionPlan
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm
  
  # Azure resources
  ACR_NAME: 'acr003nonprdemearepo'
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
  MICROSERVICE_NAMESPACE: 'troubleshootingnamespace'
  
  # Dynamic variables
  IMAGE_NAME: 'productionplan'
  IMAGE_TAG: $(Build.BuildId)  # Build ID as tag for the Docker image

# Use the combined CI/CD template
stages:
- template: ../temp/azure-pipeline-cicd.yml
  parameters:
    # Microservice specific parameters
    MICROSERVICE_NAME: $(MICROSERVICE_NAME)
    BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
    
    # Environment and configuration parameters
    DOTNET_VERSION: $(DOTNET_VERSION)
    HELM_CHART_DIR: $(HELM_CHART_DIR)
    
    # Image parameters
    IMAGE_NAME: $(IMAGE_NAME)
    ACR_NAME: $(ACR_NAME)
    IMAGE_TAG: $(IMAGE_TAG)
    
    # Deployment parameters
    AKS_CLUSTER: $(AKS_CLUSTER)
    AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
    MICROSERVICE_NAMESPACE: $(MICROSERVICE_NAMESPACE)



# =========================================================================
# Approval Stage for Production Deployment (Optional)
# =========================================================================
# - stage: ApproveForProduction
#   displayName: 'Approve for Production'
#   dependsOn: PublishImageTag
#   jobs:
#   - job: WaitForApproval
#     displayName: 'Manual Approval Required'
#     pool: server
#     timeoutInMinutes: 4320 # 3 days
#     steps:
#     - task: ManualValidation@0
#       timeoutInMinutes: 4320 # 3 days
#       inputs:
#         instructions: 'Please validate the dev deployment and approve if ready for production.'
#         onTimeout: 'reject'

# =========================================================================
# Production Deployment Stage (Optional)
# =========================================================================
# - template: ../temp/azure-pipeline-cicd.yml
#   parameters:
#     MICROSERVICE_NAME: $(MICROSERVICE_NAME)
#     BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
#     HELM_CHART_DIR: $(HELM_CHART_DIR)
#     IMAGE_NAME: $(IMAGE_NAME)
#     ACR_NAME: $(ACR_NAME)
#     IMAGE_TAG: $(IMAGE_TAG)
#     AKS_CLUSTER: $(AKS_CLUSTER)
#     AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
#     MICROSERVICE_NAMESPACE: 'production' 