# =========================================================================
# ProductionPlan Microservice Continuous Deployment Pipeline
# =========================================================================
# This pipeline is designed for continuous deployment of the ProductionPlan microservice to Kubernetes.
# It deploys the Docker image from Azure Container Registry to an AKS cluster,
# configures ingress, and verifies the deployment.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name, namespace, and other configuration options.
# =========================================================================

name: CW-CD-Pipeline-ProductionPlan-Microservice

trigger:
  - none

pr:
  - none

resources:
  pipelines:
  - pipeline: productionplan-ci-pipeline   # Name for the resource
    source: CW-CI-Pipeline-ProductionPlan-Microservice  # Name of CI pipeline in Azure DevOps
    trigger: true                   # Automatically trigger when CI pipeline completes successfully

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

stages:
# =========================================================================
# Stage 1: Deploy to AKS (Dev Environment)
# =========================================================================
# This stage deploys the Docker image to the Dev environment in AKS
# and verifies the deployment
# =========================================================================
- stage: DownloadImageTagStage
  displayName: 'Download Image Tag Stage'
  jobs:
  - job: DownloadImageTag
    displayName: 'Download Image Tag Artifact'
    steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'specific'
        project: 'AZ-ConnectedWorkers'
        pipeline: 'TBD'  # To be updated with actual pipeline ID
        buildVersionToDownload: 'latest'
        downloadType: 'single'
        artifactName: 'ImageTag'
        downloadPath: '$(Build.ArtifactStagingDirectory)'
      displayName: 'Download Image Tag Artifact'

    - script: |
        # Read the updated IMAGE_TAG value from the downloaded artifact file
        IMAGE_TAG=$(cat $(Build.ArtifactStagingDirectory)/ImageTag/imageTag.txt)
        
        # Update the IMAGE_TAG variable for subsequent tasks or jobs
        echo "##vso[task.setvariable variable=IMAGE_TAG]$IMAGE_TAG"
        
        # Print the updated IMAGE_TAG value for debugging/logging
        echo "Updated IMAGE_TAG: $IMAGE_TAG"
      displayName: 'Set IMAGE_TAG variable'

- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'productionplan'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/Headcount Control.TKS/ProductionPlan
    IMAGE_NAME: 'productionplan'
    ACR_NAME: 'acr003nonprdemearepo'
    IMAGE_TAG: $(IMAGE_TAG)
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'troubleshootingnamespace'

# =========================================================================
# Optional: Post-Deployment Notification Stage
# =========================================================================
# Uncomment this section if you want to send notifications after deployment
#
# - stage: Notify
#   displayName: 'Send Deployment Notifications'
#   dependsOn: DeployToDev
#   condition: succeededOrFailed()
#   jobs:
#   - job: SendNotifications
#     displayName: 'Send Notifications'
#     steps:
#     - script: |
#         echo "Sending deployment notification for ${{ parameters.MICROSERVICE_NAME }}..."
#         # Add your notification logic here (email, Teams, Slack, etc.)
#       displayName: 'Send Deployment Notification' 