# =========================================================================
# ProductionPlan Microservice Continuous Integration Pipeline
# =========================================================================
# This pipeline is designed for continuous integration of the ProductionPlan microservice.
# It performs build, testing, and creates and pushes a Docker image to 
# Azure Container Registry.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name and other configuration options.
# =========================================================================

name: CW-CI-Pipeline-ProductionPlan-Microservice

trigger:
  - none

pr:
  - none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

variables:
  IMAGE_TAG: $(Build.BuildId)  # Build ID as tag for the Docker image

stages:
# =========================================================================
# Stage 1: Build and Test
# =========================================================================
# This stage builds the .NET solution and runs tests
# =========================================================================
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'productionplan'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/Headcount Control.TKS/ProductionPlan
    IMAGE_NAME: 'productionplan'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    DOTNET_VERSION: '8.0.x'  # .NET SDK version to use for the build
    IMAGE_TAG: $(IMAGE_TAG)  # Use build ID as tag
