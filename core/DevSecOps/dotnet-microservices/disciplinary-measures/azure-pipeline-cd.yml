name: CW-CD-Pipeline-Disciplinary-Measures-Microservice

trigger:
  - none

pr:
  - none

resources:
  pipelines:
  - pipeline: disciplinary-measures-ci-pipeline   # Name for the resource
    source: CW-CI-Pipeline-Disciplinary-Measures-Microservice  # Name of CI pipeline in Azure DevOps
    trigger: true                   # Automatically trigger when CI pipeline completes successfully

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- stage: DownloadImageTagStage
  displayName: 'Download Image Tag Stage'
  jobs:
  - job: DownloadImageTag
    displayName: 'Download Image Tag Artifact'
    steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'specific'
        project: 'AZ-ConnectedWorkers'
        pipeline: '18'  # This should be the ID of your CI pipeline
        buildVersionToDownload: 'latest'
        downloadType: 'single'
        artifactName: 'ImageTag'
        downloadPath: '$(Build.ArtifactStagingDirectory)'
      displayName: 'Download Image Tag Artifact'

    - script: |
        # Read the updated IMAGE_TAG value from the downloaded artifact file
        IMAGE_TAG=$(cat $(Build.ArtifactStagingDirectory)/ImageTag/imageTag.txt)
        
        # Update the IMAGE_TAG variable for subsequent tasks or jobs
        echo "##vso[task.setvariable variable=IMAGE_TAG]$IMAGE_TAG"
        
        # Print the updated IMAGE_TAG value for debugging/logging
        echo "Updated IMAGE_TAG: $IMAGE_TAG"
      displayName: 'Set IMAGE_TAG variable'

- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'disciplinary-measures'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/Headcount Control.TKS/DisciplinaryMeasuresService
    IMAGE_NAME: 'disciplinary-measures'
    ACR_NAME: 'acr003nonprdemearepo'
    IMAGE_TAG: 1017  # This will be overridden by the downloaded tag
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'troubleshootingnamespace'
    HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm  # Path to the Helm charts directory 