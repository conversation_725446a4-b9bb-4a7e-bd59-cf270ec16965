name: CW-PR-Pipeline-crew-management-cfp-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
      - feature/crewChangefeed
  paths:
    include:
      - '**/microservices/dotnetMicroservice/crewManagementCFP/**'

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'crew-management-cfp'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/crewManagementCFP
    DOTNET_VERSION: '9.0.x' 
