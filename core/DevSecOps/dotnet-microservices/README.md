# .NET Microservices Pipeline Documentation

## Overview
This repository contains Azure DevOps pipeline configurations for .NET microservices. The pipeline structure follows a template-based approach for consistency across services while maintaining separate execution contexts for better observability and troubleshooting.

![Pipeline Architecture](https://mermaid.ink/img/pako:eNqNkk9PwzAMxb9KlBOI9QNwQKCJw4QYEhyAQy5NvK6iTSrHQYOq786aFgZiB3rxK_7l_ew8oEkNYoQbZ3vvjCXYOm-JvHFkKZANpJwdyZHfUfKWYJPJkYJnWOcpkXgbKGQXyZPNJJvJkYu0Iy_QRnIUyeQcuQg7iiZHMZnMkfMUKdrkHLlIO_LJ5Bw5SjsKyeQcuUg78iaZHMVI2lFMJufIRdqRTyZHMZF2FJLJOXKRduSTyTlyJe0oJJNz5CLtyJtkcpRIaUfRJJNz5CLtyJtkcuQr0o5CMjlHLtKOfDI5R66lHYVkco5cpB15k0yOopJ2FE0yOUcu0o68SSZHXks7CsnkHLlIO_LJ5By5kXYUksk5cpF25E0yOYpG2lE0yeQcuUg78iaZHPla2lFIJufIRdqRTyZHvpN2FJLJOfIXaUfBJJOjaCXtKJpkco5cpB15k0yOfCftKCSTs-QfLgEbxQ?type=png)

## Pipeline Structure

### Services
The following microservices are configured with dedicated pipeline sets:
- SignalR Notification Service

### Pipeline Types
Each service has three pipeline configurations:
1. **CI Pipeline** (Continuous Integration)
   - Builds and tests the application
   - Creates Docker images
   - Pushes to Azure Container Registry
   
2. **CD Pipeline** (Continuous Deployment)
   - Triggered by successful CI pipeline completion
   - Deploys to Kubernetes using Helm charts
   - Validates deployment status
   
3. **PR Pipeline** (Pull Request Validation)
   - Validates code quality on pull requests
   - Runs tests and security scans
   - Ensures code meets standards before merging

### Template Files
Located in `/temp` directory:
- `azure-pipeline-ci.yml`: Template for CI pipelines
- `azure-pipeline-cd.yml`: Template for CD pipelines
- `azure-pipeline-pr.yml`: Template for PR validation pipelines

## Pipeline Stages Detailed Workflow

### CI Pipeline Workflow
![CI Pipeline Flow](https://mermaid.ink/img/pako:eNqFksFqwzAMhl9F-NRCu-cQWMnWHQbrsIXtYvxhNWaxg-0UWsrefdLSJWvZQTpZ-vX9kiwfoDUGsYKdtYN3xhLsnfNEzjiyFMgGUs6O5MjvKDlLsM_kyAXPsM1TIvE2UMgukiebSXaTIxdpR16gjeTIk8k5chF2FEyOYjKZI-cpUrTJOXKRduSTyTlylHYUksk5cpF25E0yOYqRtKOYTM6Ri7Qjn0yOYiLtKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Build and Test**
   - Installs .NET SDK
   - Restores dependencies
   - Builds the solution
   - Runs unit tests
   - Publishes test results

2. **Docker Build and Push**
   - Builds Docker image
   - Pushes to Azure Container Registry (ACR)
   - Tags with appropriate version
   - Publishes image tag as artifact

### CD Pipeline Workflow
![CD Pipeline Flow](https://mermaid.ink/img/pako:eNqNkk9rwzAMxb-K8KmFdp9jsCZbd9jWwzrYLsYf1mAXO9hOoaXsd5-0dMla1sNOlp5-7z1JPoA2GsQKdtYO3mlDsHfOEznjyFAgG0g5O5Ijv6PkLME-kyMXPMM2T4nE20Ahu0iebCbZTY5cpB15gTaSI08m58hF2FEwOYrJZI6cp0jRJufIRdqRTybnyCntKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Download Image Tag Stage**
   - Downloads image tag artifact from CI pipeline
   - Sets the image tag variable for deployment

2. **Deploy to Dev**
   - Authenticates with Azure
   - Configures AKS access
   - Creates or updates namespaces
   - Installs NGINX Ingress Controller if needed
   - Deploys to AKS cluster using Helm
   - Validates deployment status

### PR Pipeline Workflow
![PR Pipeline Flow](https://mermaid.ink/img/pako:eNqNkk9rwzAMxb-K8KmFdp9jsCZbd9jWwzrYLsYf1mAXO9hOoaXsd5-0dMla1sNOlp5-7z1JPoA2GsQKdtYO3mlDsHfOEznjyFAgG0g5O5Ijv6PkLME-kyMXPMM2T4nE20Ahu0iebCbZTY5cpB15gTaSI08m58hF2FEwOYrJZI6cp0jRJufIRdqRTybnyCntKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Build and Test**
   - Installs .NET SDK
   - Restores dependencies
   - Builds the solution
   - Runs unit tests with code coverage
   - Publishes test and coverage results

## Configuration Parameters

### CI Pipeline Parameters
| Parameter | Description | Default |
|-----------|-------------|---------|
| `MICROSERVICE_NAME` | Name of the microservice | `default-service` |
| `BUILD_SOURCES_DIR` | Directory containing the source code | `''` |
| `IMAGE_NAME` | Name of the Docker image | `default-service` |
| `ACR_NAME` | Azure Container Registry name | `acr003nonprdemearepo` |
| `DOTNET_VERSION` | .NET SDK version | `8.0.x` |
| `IMAGE_TAG` | Docker image tag | `latest` |

### CD Pipeline Parameters
| Parameter | Description | Default |
|-----------|-------------|---------|
| `MICROSERVICE_NAME` | Name of the microservice | `default-service` |
| `MICROSERVICE_NAMESPACE` | Kubernetes namespace | `troubleshootingnamespace` |
| `BUILD_SOURCES_DIR` | Directory containing the source code | `''` |
| `HELM_CHART_DIR` | Directory containing Helm charts | `$(Build.SourcesDirectory)/core/DevSecOps/helm` |
| `IMAGE_NAME` | Name of the Docker image | `default-service` |
| `IMAGE_TAG` | Docker image tag | `latest` |
| `ACR_NAME` | Azure Container Registry name | `acr003nonprdemearepo` |
| `AKS_CLUSTER` | AKS cluster name | `aks003-NonPrd-EMEA` |
| `AKS_RESOURCE_GROUP` | Resource group for AKS | `RG-Dev-CONNWORKERS-AKS` |

### PR Pipeline Parameters
| Parameter | Description | Default |
|-----------|-------------|---------|
| `MICROSERVICE_NAME` | Name of the microservice | `default-service` |
| `BUILD_SOURCES_DIR` | Directory containing the source code | `''` |
| `DOTNET_VERSION` | .NET SDK version | `8.0.x` |

## Benefits of Reusable Pipelines

### 1. Consistency
- Standardized build and deployment processes
- Uniform testing and quality checks
- Consistent security practices
- Predictable pipeline behavior

### 2. Maintainability
- Centralized template management
- Reduced duplication
- Easier updates and improvements
- Simplified onboarding for new services

### 3. Time Savings
- Faster onboarding of new microservices
- Reduced time spent on pipeline configuration
- Quick implementation of new services
- Reusable code blocks and templates

### 4. Quality Control
- Consistent quality checks across all services
- Standardized security scanning
- Uniform testing practices
- Centralized policy enforcement

### 5. Cost Optimization
- Reduced maintenance overhead
- Optimized pipeline execution
- Shared resources and caching
- Efficient use of build agents

### 6. Separate Execution Contexts
- Isolated logs for each microservice
- Easier troubleshooting and debugging
- Independent execution and failure handling
- Better visibility into individual service pipelines

## Best Practices
1. Always use the template files for consistency
2. Maintain proper versioning for Docker images
3. Keep security scanning enabled
4. Ensure proper test coverage
5. Use caching for better pipeline performance

## Security Considerations
- Security audit runs on production dependencies
- Docker images are scanned for vulnerabilities
- Azure authentication uses managed identities
- Access controls are implemented through Azure RBAC

## Troubleshooting
- Check pipeline logs for detailed error messages
- Verify Azure credentials and permissions
- Ensure Docker daemon is running for CI builds
- Validate Kubernetes configurations

### Debugging Tips
- Use the `echo` command to print debug information
- Check resource availability in Azure
- Verify network connectivity between services
- Review Kubernetes events for deployment issues 