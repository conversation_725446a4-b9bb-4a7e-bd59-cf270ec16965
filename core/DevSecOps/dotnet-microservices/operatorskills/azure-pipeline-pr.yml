name: CW-PR-Pipeline-OperatorSkills-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/dotnetMicroservice/OperatorSkillsService/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'operatorskills'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/OperatorSkillsService
    DOTNET_VERSION: '9.0.100-preview.1.24101.2'  # Updated to specific preview version 