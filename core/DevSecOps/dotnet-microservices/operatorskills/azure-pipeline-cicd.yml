name: CW-CICD-Pipeline-OperatorSkills-Microservice

trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/dotnetMicroservice/OperatorSkillsService/**'
      - '**/core/DevSecOps/helm/operatorskills/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

# Define variables used across the pipeline
variables:
  # Microservice specific variables
  MICROSERVICE_NAME: 'operatorskills'
  
  # Environment and configuration variables
  DOTNET_VERSION: '9.0.100-preview.1.24101.2'  # Using the specific preview version from original CI
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/OperatorSkillsService
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm
  SERVICE_ACCOUNT_PATH: "crewmanagementserviceaccount.yaml"
  
  # Azure resources
  ACR_NAME: 'acr003nonprdemearepo'
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
  MICROSERVICE_NAMESPACE: 'crew-management'
  
  # Dynamic variables
  IMAGE_NAME: 'operatorskills'
  IMAGE_TAG: $(Build.BuildId)  # Build ID as tag for the Docker image

# Use the combined CI/CD template
stages:
- template: ../temp/azure-pipeline-cicd.yml
  parameters:
    # Microservice specific parameters
    MICROSERVICE_NAME: $(MICROSERVICE_NAME)
    BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
    
    # Environment and configuration parameters
    DOTNET_VERSION: $(DOTNET_VERSION)
    HELM_CHART_DIR: $(HELM_CHART_DIR)
    
    # Image parameters
    IMAGE_NAME: $(IMAGE_NAME)
    ACR_NAME: $(ACR_NAME)
    IMAGE_TAG: $(IMAGE_TAG)
    
    # Deployment parameters
    AKS_CLUSTER: $(AKS_CLUSTER)
    AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
    MICROSERVICE_NAMESPACE: $(MICROSERVICE_NAMESPACE)

# Additional stage to publish image tag (carried over from original CI pipeline)
- stage: PublishImageTag
  displayName: 'Publish Image Tag'
  dependsOn: DeployToAKS
  jobs:
  - job: PublishTag
    displayName: 'Publish Image Tag as Artifact'
    steps:
    # Save the image tag to a file
    - script: |
        echo $(IMAGE_TAG) > $(Build.ArtifactStagingDirectory)/imageTag.txt
      displayName: 'Save Image Tag to File'

    # Publish the image tag as an artifact
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(Build.ArtifactStagingDirectory)
        artifactName: 'ImageTag'
      displayName: 'Publish Image Tag Artifact'

# =========================================================================
# Approval Stage for Production Deployment (Optional)
# =========================================================================
# - stage: ApproveForProduction
#   displayName: 'Approve for Production'
#   dependsOn: PublishImageTag
#   jobs:
#   - job: WaitForApproval
#     displayName: 'Manual Approval Required'
#     pool: server
#     timeoutInMinutes: 4320 # 3 days
#     steps:
#     - task: ManualValidation@0
#       timeoutInMinutes: 4320 # 3 days
#       inputs:
#         instructions: 'Please validate the dev deployment and approve if ready for production.'
#         onTimeout: 'reject'

# =========================================================================
# Production Deployment Stage (Optional)
# =========================================================================
# - template: ../temp/azure-pipeline-cicd.yml
#   parameters:
#     MICROSERVICE_NAME: $(MICROSERVICE_NAME)
#     BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
#     HELM_CHART_DIR: $(HELM_CHART_DIR)
#     IMAGE_NAME: $(IMAGE_NAME)
#     ACR_NAME: $(ACR_NAME)
#     IMAGE_TAG: $(IMAGE_TAG)
#     AKS_CLUSTER: $(AKS_CLUSTER)
#     AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
#     MICROSERVICE_NAMESPACE: 'production' 
