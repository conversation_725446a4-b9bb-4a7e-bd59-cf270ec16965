name:  CW-PR-Pipeline-SignalRNotif-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/dotnetMicroservice/NotificationSystem/NotificationSignalRService/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'notification'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/NotificationSystem/NotificationSignalRService
    DOTNET_VERSION: '8.0.x' 