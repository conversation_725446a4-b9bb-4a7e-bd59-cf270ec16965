name:  CW-CI-Pipeline-SignalRNotif-Microservice

trigger:
  - none

pr:
  - none

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

variables:
  IMAGE_TAG: $(Build.BuildId)  # Build ID as tag for the Docker image

stages:
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'notification'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/NotificationSystem/NotificationSignalRService
    IMAGE_NAME: 'notificationsignalr'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    DOTNET_VERSION: '8.0.x'
    IMAGE_TAG: $(Build.BuildId)

# Publish Image Tag
- stage: PublishImageTag
  displayName: 'Publish Image Tag'
  dependsOn: DockerBuildAndPush
  jobs:
  - job: PublishTag
    displayName: 'Publish Image Tag as Artifact'
    steps:
    # Save the image tag to a file
    - script: |
        echo $(IMAGE_TAG) > $(Build.ArtifactStagingDirectory)/imageTag.txt
      displayName: 'Save Image Tag to File'

    # Publish the image tag as an artifact
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(Build.ArtifactStagingDirectory)
        artifactName: 'ImageTag'
      displayName: 'Publish Image Tag Artifact' 