name: CW-PR-Pipeline-module1-cfp-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
      - feature/crewChangefeed
  paths:
    include:
      - '**/microservices/dotnetMicroservice/administrativeDocCFP/**'

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'module1-cfp'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/crewManagementCFP
    DOTNET_VERSION: '9.0.x' 
