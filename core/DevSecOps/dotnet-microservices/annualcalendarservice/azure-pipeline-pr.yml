name: CW-PR-Pipeline-AnnualCalendarService-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
      - feature/sprint14-annual-calendar
  paths:
    include:
      - '**/microservices/dotnetMicroservice/Headcount Control.TKS/AnnualCalendarService/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'annual-calendar-service'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/Headcount Control.TKS/AnnualCalendarService
    DOTNET_VERSION: '9.0.x' 