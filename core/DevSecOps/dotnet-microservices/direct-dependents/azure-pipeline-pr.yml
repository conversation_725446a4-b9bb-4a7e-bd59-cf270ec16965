name: CW-PR-Pipeline-DirectDependents-Microservice

trigger:
  - none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/dotnetMicroservice/DirectDependentsService/**'

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'direct-dependents'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice/DirectDependentsService
    DOTNET_VERSION: '9.0.x' 