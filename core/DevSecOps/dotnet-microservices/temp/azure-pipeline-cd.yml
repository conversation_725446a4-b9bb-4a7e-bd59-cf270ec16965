# =========================================================================
# .NET Microservice Continuous Deployment Pipeline
# =========================================================================
# This pipeline is designed for continuous deployment of .NET microservices to Kubernetes.
# It deploys the Docker image from Azure Container Registry to an AKS cluster,
# configures ingress, and verifies the deployment.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name, namespace, and other configuration options.
# =========================================================================

parameters:
  MICROSERVICE_NAME: 'default-service'         # Name of the microservice to deploy
  MICROSERVICE_NAMESPACE: 'troubleshootingnamespace'  # Kubernetes namespace for deployment
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/dotnetMicroservice                         # Root directory of the project
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm  # Directory containing Helm charts
  IMAGE_NAME: 'default-service'                # Name of the Docker image to deploy
  IMAGE_TAG: 'latest'                          # Tag of the Docker image to deploy
  ACR_NAME: 'acr003nonprdemearepo'             # Azure Container Registry name
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'            # AKS cluster name
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS' # Resource group containing the AKS cluster

stages:
# =========================================================================
# Stage 1: Deploy to AKS (Dev Environment)
# =========================================================================
# This stage deploys the Docker image to the Dev environment in AKS
# and verifies the deployment
# =========================================================================
- stage: DeployToDev
  displayName: 'Deploy to Dev Environment'
  jobs:
  - job: DeployToDev
    displayName: 'Deploy Application to Dev'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install required tools: Azure CLI and Helm
    - script: |
        echo "Installing Azure CLI..."
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
        
        echo "Installing Helm 3..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
      displayName: 'Install Azure CLI and Helm'
    
    # =========================================================================
    # Azure Authentication and Configuration
    # =========================================================================
    
    # Login to Azure and setup kubectl
    - script: |
        echo "Logging in to Azure using managed identity..."
        az login --identity
        
        echo "Logging in to Azure Container Registry..."
        az acr login -n ${{ parameters.ACR_NAME }} || echo "Failed to login to ACR. Will continue anyway as this might not be critical."
        
        echo "Setting up kubectl for AKS cluster..."
        az aks get-credentials --resource-group ${{ parameters.AKS_RESOURCE_GROUP }} --name ${{ parameters.AKS_CLUSTER }} --overwrite-existing || echo "Failed to set up kubectl. This is critical - please check permissions and cluster availability."
        
        echo "Verifying AKS connection..."
        kubectl cluster-info || echo "Failed to connect to AKS cluster. This is critical - please check network connectivity and credentials."
      displayName: 'Azure Authentication and kubectl Setup'

    # =========================================================================
    # Namespace and Ingress Setup
    # =========================================================================
    
    # Create namespaces
    - script: |
        echo "Creating or verifying ingress-nginx namespace..."
        kubectl create namespace ingress-nginx || echo "Namespace ingress-nginx already exists"
        
        echo "Creating or verifying ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl create namespace ${{ parameters.MICROSERVICE_NAMESPACE }} || echo "Namespace ${{ parameters.MICROSERVICE_NAMESPACE }} already exists"
      displayName: 'Create Namespaces'

    # Apply shared service account
    - script: |
        echo "Applying shared service account..."
        kubectl apply -f ${{ parameters.HELM_CHART_DIR }}/sharedserviceaccount.yaml || echo "Service account already exists or failed to apply"
      displayName: 'Apply Shared Service Account'

    # =========================================================================
    # Application Deployment
    # =========================================================================
    
    # Deploy the application to AKS using Helm
    - script: |
        echo "Deploying ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."
        
        # Debug information to verify paths
        echo "Current directory: $(pwd)"
        echo "Checking if Helm chart directory exists:"
        ls -la .

        helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }},image.tag=${{ parameters.IMAGE_TAG }}
        
        #echo "Deploying ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."
        #helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . \
        #  --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} \
        #  --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }} \
        #  --set image.tag=${{ parameters.IMAGE_TAG }} \
        #  --set fullnameOverride=${{ parameters.MICROSERVICE_NAME }} \
        #  --set nameOverride=${{ parameters.MICROSERVICE_NAME }} \
        #  --set serviceAccount.create=false \
        #  --set serviceAccount.name=shared-service-account \
        #  --debug
        #
        #echo "Deployment initiated. Waiting for rollout to complete..."
        #kubectl rollout status deployment/${{ parameters.MICROSERVICE_NAME }} -n ${{ parameters.MICROSERVICE_NAMESPACE }} --timeout=180s || echo "Deployment rollout timed out or failed. Check deployment status for details."
      workingDirectory: ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
      displayName: 'Deploy to AKS Dev Environment'

    # =========================================================================
    # Deployment Verification
    # =========================================================================
    
    # Verify deployment resources
    - script: |
        echo "Verifying deployments in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get deployments -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide
        
        echo "Verifying pods in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide
        
        echo "Verifying services in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get svc -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide
        
        echo "Verifying ingresses in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get ing -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide
      displayName: 'Verify Deployment Resources'

    # Check pod logs
    - script: |
        echo "Checking logs for ${{ parameters.MICROSERVICE_NAME }} pods..."
        for pod in $(kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o name); do
          echo "======================================================"
          echo "Logs for $pod:"
          echo "======================================================"
          kubectl logs -n ${{ parameters.MICROSERVICE_NAMESPACE }} $pod --tail=50 || echo "Failed to get logs for $pod"
          echo "======================================================"
        done
      displayName: 'Check Pod Logs'
      
    # =========================================================================
    # Optional: Additional Verification
    # =========================================================================
    # Uncomment this section for additional verification steps
    
    # # Get events to troubleshoot any issues
    # - script: |
    #     echo "Getting events in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
    #     kubectl get events --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --sort-by='.lastTimestamp' | grep ${{ parameters.MICROSERVICE_NAME }} || echo "No events found for ${{ parameters.MICROSERVICE_NAME }}"
    #   displayName: 'Get Events'
    #
    # # Get ConfigMaps
    # - script: |
    #     echo "Getting ConfigMaps in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
    #     kubectl get cm --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
    #   displayName: 'Get ConfigMaps'
    #
    # # Get Service Accounts
    # - script: |
    #     echo "Getting Service Accounts in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
    #     kubectl get sa --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
    #   displayName: 'Get Service Accounts'
    #
    # # Get Horizontal Pod Autoscalers
    # - script: |
    #     echo "Getting HPAs in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
    #     kubectl get hpa --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No HPAs found."
    #   displayName: 'Get HPAs'
    #
    # # Get ReplicaSets
    # - script: |
    #     echo "Getting ReplicaSets in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
    #     kubectl get rs --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
    #   displayName: 'Get ReplicaSets'
    
    # =========================================================================
    # Optional: Application Health Check
    # =========================================================================
    # Uncomment this section to add application health checks
    
    # # Validate the application is accessible (if it has an endpoint)
    # - script: |
    #     echo "Checking if application has an ingress..."
    #     INGRESS_HOST=$(kubectl get ing -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o jsonpath='{.items[0].spec.rules[0].host}' 2>/dev/null)
    #     
    #     if [ -n "$INGRESS_HOST" ]; then
    #       echo "Application is exposed via ingress host: $INGRESS_HOST"
    #       echo "Attempting to validate application health..."
    #       # You can add curl commands here to check application health endpoints
    #       # curl -k https://$INGRESS_HOST/health || echo "Health check failed, but deployment will continue."
    #     else
    #       echo "No ingress found for the application. Skipping validation."
    #     fi
    #   displayName: 'Validate Application Accessibility'
    #   continueOnError: true

# =========================================================================
# Optional: Post-Deployment Notification Stage
# =========================================================================
# Uncomment this section if you want to send notifications after deployment
#
# - stage: Notify
#   displayName: 'Send Deployment Notifications'
#   dependsOn: DeployToDev
#   condition: succeededOrFailed()
#   jobs:
#   - job: SendNotifications
#     displayName: 'Send Notifications'
#     steps:
#     - script: |
#         echo "Sending deployment notification for ${{ parameters.MICROSERVICE_NAME }}..."
#         # Add your notification logic here (email, Teams, Slack, etc.)
#       displayName: 'Send Deployment Notification' 