# =========================================================================
# .NET Microservice Combined CI/CD Pipeline
# =========================================================================
# This pipeline is designed for continuous integration and deployment of .NET microservices.
# It performs build, testing, creates and pushes a Docker image to Azure Container Registry,
# and deploys the image to an AKS cluster.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name and other configuration options.
# =========================================================================

parameters:
  # Microservice specific parameters
  MICROSERVICE_NAME: "default-service" # Name of the microservice to build and deploy
  BUILD_SOURCES_DIR: "" # Root directory of the project

  # Environment and configuration parameters
  DOTNET_VERSION: "8.0.x" # .NET SDK version to use for the build
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm # Directory containing Helm charts

  # Image parameters
  IMAGE_NAME: "default-service" # Name of the Docker image to build
  ACR_NAME: "acr003nonprdemearepo" # Azure Container Registry name
  IMAGE_TAG: "latest" # Tag for the Docker image

  # Deployment parameters
  AKS_CLUSTER: "aks003-NonPrd-EMEA" # AKS cluster name
  AKS_RESOURCE_GROUP: "RG-Dev-CONNWORKERS-AKS" # Resource group containing the AKS cluster
  MICROSERVICE_NAMESPACE: "troubleshootingnamespace" # Kubernetes namespace for deployment

stages:
  # =========================================================================
  # Stage 1: Build and Test
  # =========================================================================
  # This stage builds the .NET solution and runs tests
  # =========================================================================
  - stage: BuildAndTest
    displayName: "Build and Test Stage"
    jobs:
      - job: BuildTest
        displayName: "Build and Test"
        variables:
          - group: "GitHub-Secrets" # Group containing GitHub PAT for automatic issue creation
        steps:
          # =========================================================================
          # Environment Setup
          # =========================================================================

          # Install .NET SDK
          - task: UseDotNet@2
            inputs:
              version: ${{ parameters.DOTNET_VERSION }}
            displayName: "Install .NET SDK"

          # =========================================================================
          # Build
          # =========================================================================

          # Restore dependencies and build the solution
          - script: |
              echo "Restoring NuGet packages for ${{ parameters.MICROSERVICE_NAME }}..."
              dotnet restore

              echo "Building ${{ parameters.MICROSERVICE_NAME }} in Release configuration..."
              dotnet build --configuration Release
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Build Solution"

        # =========================================================================
        # Testing
        # =========================================================================
          # Run tests
          - script: |
              echo "Running tests for ${{ parameters.MICROSERVICE_NAME }}..."
              dotnet test --no-build --configuration Release 2>&1 | tee test_errors.txt || echo "Unit Tests Failed or Not Exist"
              TEST_ERRORS=$(cat test_errors.txt)
              if grep -q "FAIL" test_errors.txt; then
                echo "Unit tests failed. Errors captured:"
                echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
              else
                echo "Unit tests passed successfully or did not exist."
                echo "##vso[task.setvariable variable=TestFailed;isOutput=true]false"
              fi
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: 'Run Tests'

            name: RunTests
            # Capture test errors and create a GitHub issue if tests failed
          - task: Bash@3
            condition: eq(variables['runTests.TestFailed'], 'true')
            inputs:
              targetType: "inline"
              script: |

                # Install jq if not available
                if ! command -v jq &> /dev/null; then
                  echo "Installing jq..."
                  sudo apt-get update && sudo apt-get install -y jq
                fi

                # Read test errors and clean them properly
                ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/test_errors.txt" | \
                  tr -d '\0' | \
                  iconv -f utf-8 -t utf-8 -c | \
                  sed 's/\x1b\[[0-9;]*m//g' | \
                  sed 's/[[:cntrl:]]//g' | \
                  tr -cd '[:print:]\n\t' | \
                  sed 's/[ \t]*$//' | \
                  sed '/^$/d')

                # Create the issue body with proper newlines
                BODY="## Unit Test Failure Report
                **Build Information:**
                - Microservice: ${{ parameters.MICROSERVICE_NAME }}
                - Branch: $(Build.SourceBranch)
                - Commit: $(Build.SourceVersion)
                **Test Errors:**
                \`\`\`
                ${ERRORS}
                \`\`\`
                Please investigate and fix the failing tests."

                # Use jq to properly construct JSON payload
                jq -n \
                  --arg title "Unit Test Failures in ${{ parameters.MICROSERVICE_NAME }}" \
                  --arg body "$BODY" \
                  --argjson labels '["bug"]' \
                  --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
                  '{
                    title: $title,
                    body: $body,
                    labels: $labels,
                    assignees: $assignees
                  }' > issue_body.json

                # Create GitHub issue
                RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
                  -H "Accept: application/vnd.github+json" \
                  -H "Authorization: Bearer $GITHUB_TOKEN" \
                  -H "Content-Type: application/json" \
                  -d @issue_body.json \
                  "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
                # remove json file
                rm issue_body.json

                exit 1
            env:
              GITHUB_TOKEN: $(GitHub_Issues_PAT)
            displayName: "Create GitHub Issue for Test Failures"

  # =========================================================================
  # Stage 2: Docker Build and Push
  # =========================================================================
  # This stage builds a Docker image and pushes it to Azure Container Registry
  # =========================================================================
  - stage: DockerBuildAndPush
    dependsOn: BuildAndTest
    condition: succeeded()
    displayName: "Docker Build and Push Stage"
    jobs:
      - job: DockerBuildPush
        displayName: "Build and Push Docker Image"
        steps:
          # =========================================================================
          # Environment Setup
          # =========================================================================

          # Install Docker to build the Docker image
          - task: Bash@3
            displayName: "Install and Configure Docker"
            retryCountOnTaskFailure: 3
            inputs:
              targetType: "inline"
              script: |
                echo "Installing Docker..."
                # Remove old versions if they exist
                timeout 10m bash -c "
                  sudo apt-get remove docker docker-engine docker.io containerd runc || true

                  sudo apt-get update
                  sudo apt-get install -y \
                      apt-transport-https \
                      ca-certificates \
                      curl \
                      gnupg \
                      lsb-release

                  curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

                  echo \
                    \"deb [arch=\$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
                    \$(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

                  sudo apt-get update
                  sudo apt-get install -y docker-ce docker-ce-cli containerd.io

                  sudo systemctl stop docker || true
                  sudo mkdir -p /etc/docker
                  sudo mkdir -p /etc/systemd/system/docker.service.d

                  echo '{
                    \"exec-opts\": [\"native.cgroupdriver=systemd\"],
                    \"log-driver\": \"json-file\",
                    \"log-opts\": {
                      \"max-size\": \"100m\"
                    },
                    \"storage-driver\": \"overlay2\"
                  }' | sudo tee /etc/docker/daemon.json

                  sudo systemctl daemon-reload
                  sudo systemctl start docker
                  sudo systemctl enable docker

                  echo \"Waiting for Docker daemon to start...\"
                  sleep 20

                  sudo chmod 666 /var/run/docker.sock
                  sudo usermod -aG docker $USER

                  echo \"Verifying Docker setup...\"
                  docker version
                  docker info

                  echo \"Testing Docker with hello-world...\"
                  docker run hello-world
                " || { echo "Docker installation failed. This task will retry 3 times."; exit 1; }

          # Additional Docker verification step
          - script: |
              echo "Performing extended Docker verification..."
              # Check Docker socket permissions
              ls -l /var/run/docker.sock
              # Check Docker service status
              sudo systemctl status docker
              # Verify Docker can pull images
              docker pull alpine:latest
              # Run a test container
              docker run --rm alpine echo "Docker is working correctly"
            displayName: "Verify Docker Setup"
            timeoutInMinutes: 5
          
          # Install Azure CLI
          - script: |
              echo "Installing Azure CLI..."
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
            displayName: "Install Azure CLI"

          # Validate installed tools to ensure everything is ready
          - script: |
              echo "Validating installed tools..."
              az version || echo "-----------No Azure CLI Installed-----------"
              docker -v  || echo "-----------No Docker Installed-----------"
              docker info || echo "-----------Docker Daemon Not Running-----------"
            displayName: "Validate Installed Tools"

          # =========================================================================
          # Docker Build and Push
          # =========================================================================

          # Build and push Docker image to ACR
          - task: AzureCLI@2
            inputs:
              azureSubscription: "RG-Stg-ConnectedWorker-Apps"
              scriptType: "bash"
              scriptLocation: "inlineScript"
              workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
              inlineScript: |
                set -e

                echo "Authenticating with Azure..."
                az login --identity

                echo "Logging in to ACR..."
                az acr login -n ${{ parameters.ACR_NAME }}

                echo "Building Docker image for ${{ parameters.MICROSERVICE_NAME }}..."
                docker build --no-cache -t ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }}:${{ parameters.IMAGE_TAG }} .
                echo "Docker image built successfully."

                echo "Listing Docker images..."
                docker images

                echo "Pushing Docker image to ACR..."
                # Delete existing image with the same tag if it exists
                az acr repository delete --name ${{ parameters.ACR_NAME }} --repository ${{ parameters.IMAGE_NAME }} --tag ${{ parameters.IMAGE_TAG }} --yes || echo "Image could not be deleted or does not exist."

                # Push the new image
                docker push "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}"
                echo "Docker image pushed successfully to ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}"

                echo "Cleaning up local Docker resources..."
                docker system df
                docker rmi -f "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" || echo "Image could not be removed."
                docker system prune -a -f 
                docker image prune -a -f
                echo "Cleanup completed."
            displayName: "Build and Push Docker Image"

  # =========================================================================
  # Stage 3: Deploy to AKS
  # =========================================================================
  # This stage deploys the Docker image in AKS
  # and verifies the deployment
  # =========================================================================
  - stage: DeployToAKS
    displayName: "Deploy to AKS"
    dependsOn: DockerBuildAndPush
    condition: succeeded()
    jobs:
      - job: DeployToAKS
        displayName: "Deploy Application in AKS"
        steps:
          # =========================================================================
          # Environment Setup
          # =========================================================================

          # Install required tools: Azure CLI and Helm
          - script: |
              echo "Installing Azure CLI..."
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

              echo "Installing Helm 3..."
              curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
            displayName: "Install Azure CLI and Helm"

          # =========================================================================
          # Azure Authentication and Configuration
          # =========================================================================

          # Login to Azure and setup kubectl
          - script: |
              echo "Logging in to Azure using managed identity..."
              az login --identity

              echo "Logging in to Azure Container Registry..."
              az acr login -n ${{ parameters.ACR_NAME }} || echo "Failed to login to ACR. Will continue anyway as this might not be critical."

              echo "Setting up kubectl for AKS cluster..."
              az aks get-credentials --resource-group ${{ parameters.AKS_RESOURCE_GROUP }} --name ${{ parameters.AKS_CLUSTER }} --overwrite-existing || echo "Failed to set up kubectl. This is critical - please check permissions and cluster availability."

              echo "Verifying AKS connection..."
              kubectl cluster-info || echo "Failed to connect to AKS cluster. This is critical - please check network connectivity and credentials."
            displayName: "Azure Authentication and kubectl Setup"

          # =========================================================================
          # Namespace and Ingress Setup
          # =========================================================================

          # Create namespaces
          - script: |
              echo "Creating or verifying ingress-nginx namespace..."
              kubectl create namespace ingress-nginx || echo "Namespace ingress-nginx already exists"

              echo "Creating or verifying ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl create namespace ${{ parameters.MICROSERVICE_NAMESPACE }} || echo "Namespace ${{ parameters.MICROSERVICE_NAMESPACE }} already exists"
            displayName: "Create Namespaces"

          # Apply shared service account
          - script: |
              echo "Applying shared service account..."
              kubectl apply -f ${{ parameters.HELM_CHART_DIR }}/serviceaccounts/${{ parameters.SERVICE_ACCOUNT_PATH }} || echo "Service account already exists or failed to apply"
            displayName: 'Apply Shared Service Account'


          # =========================================================================
          # Application Deployment
          # =========================================================================

          # Dry run the deployment
          - script: |
              echo "Dry running the deployment of ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."
              helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }},image.tag=${{ parameters.IMAGE_TAG }} --dry-run
            workingDirectory: ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
            displayName: "Dry Run Deployment"

          # Show templated values
          - script: |
              echo "Showing templated values..."
              helm template ${{ parameters.MICROSERVICE_NAME }} . --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }},image.tag=${{ parameters.IMAGE_TAG }}
            workingDirectory: ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
            displayName: "Show Templated Values"

          # Deploy the application to AKS using Helm
          - script: |
              echo "Deploying ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."

              # Debug information to verify paths
              echo "Current directory: $(pwd)"
              echo "Checking if Helm chart directory exists:"
              ls -la .

              helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }},image.tag=${{ parameters.IMAGE_TAG }}
            workingDirectory: ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
            displayName: "Deploy to AKS"

          # Verify shared ingress configuration
          - script: |
              echo "Verifying shared ingress configuration..."
              echo "Waiting for ingress to be ready..."
              sleep 30  # Give time for the ingress to be fully updated
              
              # Get and display ingress configuration
              echo "Current ingress configuration:"
              kubectl get ingress shared-ingress -n sharedingressnamespace -o yaml
              
              # Verify all expected services are present
              # Not Hardcode - to be added later
              #SERVICES=("request" "user" "workflow" "auth" "document-generation" "document-template" "dynamic-forms" "notification" "notifications-signalr" "operator-management" "workflowengine" "direct-dependents")
              #INGRESS_CONFIG=$(kubectl get ingress shared-ingress -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o yaml)
              #
              #for SERVICE in "${SERVICES[@]}"; do
              #  if echo "$INGRESS_CONFIG" | grep -q "name: $SERVICE"; then
              #    echo "✓ Service $SERVICE found in ingress configuration"  
              #  else
              #    echo "⚠ WARNING: Service $SERVICE not found in ingress configuration"
              #    echo "##vso[task.logissue type=warning]Service $SERVICE is missing from shared ingress configuration"
              #  fi
              #done
            workingDirectory: $(System.DefaultWorkingDirectory)
            displayName: 'Verify shared ingress configuration'

          # =========================================================================
          # Deployment Verification
          # =========================================================================

          # Verify deployment resources
          - script: |
              echo "Verifying deployments in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get deployments -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide

              echo "Verifying pods in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide

              echo "Verifying services in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get svc -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide

              echo "Verifying ingresses in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get ing -n ${{ parameters.MICROSERVICE_NAMESPACE }} -o wide
            displayName: "Verify Deployment Resources"

          # Check pod logs
          - script: |
              echo "Checking logs for ${{ parameters.MICROSERVICE_NAME }} pods..."
              for pod in $(kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o name); do
                echo "======================================================"
                echo "Logs for $pod:"
                echo "======================================================"
                kubectl logs -n ${{ parameters.MICROSERVICE_NAMESPACE }} $pod --tail=50 || echo "Failed to get logs for $pod"
                echo "======================================================"
              done
            displayName: "Check Pod Logs"
# =========================================================================
# Optional: Approval Stage for Production Deployment
# =========================================================================
# - stage: ApproveForProduction
#   displayName: 'Approve for Production'
#   dependsOn: DeployToDev
#   jobs:
#   - job: WaitForApproval
#     displayName: 'Manual Approval Required'
#     pool: server
#     timeoutInMinutes: 4320 # 3 days
#     steps:
#     - task: ManualValidation@0
#       timeoutInMinutes: 4320 # 3 days
#       inputs:
#         instructions: 'Please validate the dev deployment and approve if ready for production.'
#         onTimeout: 'reject'

# =========================================================================
# Optional: Production Deployment Stage
# =========================================================================
# - stage: DeployToProd
#   displayName: 'Deploy to Production Environment'
#   dependsOn: ApproveForProduction
#   condition: succeeded()
#   jobs:
#   - job: DeployToProd
#     displayName: 'Deploy Application to Production'
#     steps:
#     # Similar steps as DeployToDev but with production namespace and configuration
#     # ...
