# =========================================================================
# .NET Microservice Pull Request Pipeline
# =========================================================================
# This pipeline is designed to run on pull requests for .NET microservices.
# It performs build, testing, and code coverage analysis to ensure code quality
# before merging.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name and other configuration options.
# =========================================================================

parameters:
  MICROSERVICE_NAME: 'default-service'  # Name of the microservice to build and test
  BUILD_SOURCES_DIR: ''                 # Root directory of the project
  DOTNET_VERSION: '8.0.x'               # .NET SDK version to use for the build

stages:
# =========================================================================
# Stage 1: Build and Test
# =========================================================================
# This stage builds the .NET solution and runs tests with code coverage
# =========================================================================
- stage: BuildAndTest
  displayName: 'Build and Test'
  jobs:
  - job: BuildTest
    displayName: 'Build and Test'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install .NET SDK
    - task: UseDotNet@2
      inputs:
        version: ${{ parameters.DOTNET_VERSION }}
      displayName: 'Install .NET SDK'

    # =========================================================================
    # Build
    # =========================================================================
    
    # Restore dependencies and build the solution
    - script: |
        echo "Restoring NuGet packages for ${{ parameters.MICROSERVICE_NAME }}..."
        dotnet restore
        
        echo "Building ${{ parameters.MICROSERVICE_NAME }} in Release configuration..."
        dotnet build --configuration Release --no-restore
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Build Solution'

    # =========================================================================
    # Testing
    # =========================================================================
    
    # Run tests without generating coverage reports
    - script: |
        echo "Running tests for ${{ parameters.MICROSERVICE_NAME }}..."
        dotnet test --no-build --configuration Release
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Run Tests'

    # Run tests with code coverage collection
    - task: DotNetCoreCLI@2
      inputs:
        command: 'test'
        projects: '${{ parameters.BUILD_SOURCES_DIR }}/**/*Tests.csproj'
        arguments: '--configuration Release --no-build --collect:"XPlat Code Coverage"'
      displayName: 'Run Tests with Coverage'

    # Publish code coverage results
    - task: PublishCodeCoverageResults@1
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
        reportDirectory: '$(Agent.TempDirectory)/**/coverage'
        failIfCoverageEmpty: false
      displayName: 'Publish Code Coverage'
      
    # =========================================================================
    # Optional: Code Quality Analysis
    # =========================================================================
    # Uncomment this section to add code quality analysis
    
    # # Run code quality analysis
    # - task: DotNetCoreCLI@2
    #   inputs:
    #     command: 'custom'
    #     custom: 'tool'
    #     arguments: 'install --global dotnet-reportgenerator-globaltool'
    #   displayName: 'Install ReportGenerator Tool'
    #
    # - script: |
    #     echo "Running code quality analysis for ${{ parameters.MICROSERVICE_NAME }}..."
    #     dotnet tool install --global dotnet-sonarscanner
    #     dotnet sonarscanner begin /k:"${{ parameters.MICROSERVICE_NAME }}" /d:sonar.host.url="$(SonarQubeUrl)" /d:sonar.login="$(SonarQubeToken)" /d:sonar.cs.opencover.reportsPaths="$(Agent.TempDirectory)/**/coverage.opencover.xml"
    #     dotnet build --configuration Release --no-restore
    #     dotnet sonarscanner end /d:sonar.login="$(SonarQubeToken)"
    #   workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
    #   displayName: 'Run SonarQube Analysis'
    #   condition: and(succeeded(), eq(variables['RunSonarQube'], 'true'))
    
    # =========================================================================
    # Optional: Security Scanning
    # =========================================================================
    # Uncomment this section to add security scanning
    
    # # Run security scanning
    # - task: DotNetCoreCLI@2
    #   inputs:
    #     command: 'custom'
    #     custom: 'tool'
    #     arguments: 'install --global dotnet-security-scanner'
    #   displayName: 'Install Security Scanner Tool'
    #
    # - script: |
    #     echo "Running security scanning for ${{ parameters.MICROSERVICE_NAME }}..."
    #     dotnet security-scan --project ${{ parameters.BUILD_SOURCES_DIR }} --report-path $(Build.ArtifactStagingDirectory)/security-report.json
    #   displayName: 'Run Security Scanning'
    #   continueOnError: true
    #
    # - task: PublishBuildArtifacts@1
    #   inputs:
    #     pathToPublish: '$(Build.ArtifactStagingDirectory)/security-report.json'
    #     artifactName: 'SecurityReport'
    #     publishLocation: 'Container'
    #   displayName: 'Publish Security Report'
    #   condition: succeededOrFailed() 