# =========================================================================
# .NET Microservice Continuous Integration Pipeline
# =========================================================================
# This pipeline is designed for continuous integration of .NET microservices.
# It performs build, testing, and creates and pushes a Docker image to 
# Azure Container Registry.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name and other configuration options.
# =========================================================================

parameters:
  MICROSERVICE_NAME: 'default-service'  # Name of the microservice to build and test
  BUILD_SOURCES_DIR: ''                 # Root directory of the project
  IMAGE_NAME: 'default-service'         # Name of the Docker image to build
  ACR_NAME: 'acr003nonprdemearepo'      # Azure Container Registry name
  DOTNET_VERSION: '8.0.x'               # .NET SDK version to use for the build
  IMAGE_TAG: 'latest'                   # Tag for the Docker image

stages:
# =========================================================================
# Stage 1: Build and Test
# =========================================================================
# This stage builds the .NET solution and runs tests
# =========================================================================
- stage: BuildAndTest
  displayName: 'Build and Test Stage'
  jobs:
  - job: BuildTest
    displayName: 'Build and Test'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install .NET SDK
    - task: UseDotNet@2
      inputs:
        version: ${{ parameters.DOTNET_VERSION }}
      displayName: 'Install .NET SDK'

    # =========================================================================
    # Build
    # =========================================================================
    
    # Restore dependencies and build the solution
    - script: |
        echo "Restoring NuGet packages for ${{ parameters.MICROSERVICE_NAME }}..."
        dotnet restore
        
        echo "Building ${{ parameters.MICROSERVICE_NAME }} in Release configuration..."
        dotnet build --configuration Release
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Build Solution'

    # =========================================================================
    # Testing
    # =========================================================================
    
    # Run tests
    - script: |
        echo "Running tests for ${{ parameters.MICROSERVICE_NAME }}..."
        dotnet test --no-build --configuration Release 2>&1 | tee test_errors.txt || echo "Unit Tests Failed or Not Exist"
        TEST_ERRORS=$(cat test_errors.txt)
        if grep -q "FAIL" test_errors.txt; then
          echo "Unit tests failed. Errors captured:"
          echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
        else
          echo "Unit tests passed successfully or did not exist."
          echo "##vso[task.setvariable variable=TestFailed;isOutput=true]false"
        fi
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Run Tests'
      name: RunTests

    # Capture test errors and create a GitHub issue if tests failed 
    - task: Bash@3
      condition: eq(variables['runTests.TestFailed'], 'true')
      inputs:
        targetType: "inline"
        script: |

          # Install jq if not available
          if ! command -v jq &> /dev/null; then
            echo "Installing jq..."
            sudo apt-get update && sudo apt-get install -y jq
          fi

          # Read test errors and clean them properly
          ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/test_errors.txt" | \
            tr -d '\0' | \
            iconv -f utf-8 -t utf-8 -c | \
            sed 's/\x1b\[[0-9;]*m//g' | \
            sed 's/[[:cntrl:]]//g' | \
            tr -cd '[:print:]\n\t' | \
            sed 's/[ \t]*$//' | \
            sed '/^$/d')

          # Create the issue body with proper newlines
          BODY="## Unit Test Failure Report
          **Build Information:**
          - Microservice: ${{ parameters.MICROSERVICE_NAME }}
          - Branch: $(Build.SourceBranch)
          - Commit: $(Build.SourceVersion)
          **Test Errors:**
          \`\`\`
          ${ERRORS}
          \`\`\`
          Please investigate and fix the failing tests."

          # Use jq to properly construct JSON payload
          jq -n \
            --arg title "Unit Test Failures in ${{ parameters.MICROSERVICE_NAME }}" \
            --arg body "$BODY" \
            --argjson labels '["bug"]' \
            --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
            '{
              title: $title,
              body: $body,
              labels: $labels,
              assignees: $assignees
            }' > issue_body.json

          # Create GitHub issue
          RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer $GITHUB_TOKEN" \
            -H "Content-Type: application/json" \
            -d @issue_body.json \
            "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
          # remove json file
          rm issue_body.json
          exit 1
      env:
        GITHUB_TOKEN: $(GitHub_Issues_PAT)
      displayName: "Create GitHub Issue for Test Failures"

    # =========================================================================
    # Optional: Publish Artifacts
    # =========================================================================
    # Uncomment this section to publish build artifacts
    
    # # Publish the build artifacts
    # - task: DotNetCoreCLI@2
    #   inputs:
    #     command: 'publish'
    #     publishWebProjects: false
    #     projects: '${{ parameters.BUILD_SOURCES_DIR }}/**/*.csproj'
    #     arguments: '--configuration Release --output $(Build.ArtifactStagingDirectory)'
    #     zipAfterPublish: true
    #   displayName: 'Publish .NET Application'
    #
    # - task: PublishBuildArtifacts@1
    #   inputs:
    #     pathToPublish: '$(Build.ArtifactStagingDirectory)'
    #     artifactName: 'drop'
    #     publishLocation: 'Container'
    #   displayName: 'Publish Build Artifacts'

# =========================================================================
# Stage 2: Docker Build and Push
# =========================================================================
# This stage builds a Docker image and pushes it to Azure Container Registry
# =========================================================================
- stage: DockerBuildAndPush
  condition: succeeded()
  displayName: 'Docker Build and Push Stage'
  jobs:
  - job: DockerBuildPush
    displayName: 'Build and Push Docker Image'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install Azure CLI
    - script: |
        echo "Installing Azure CLI..."
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
      displayName: 'Install Azure CLI'
    
    # Grant Docker permissions
    - script: |
        echo "Granting Docker socket permissions..."
        sudo chmod 666 /var/run/docker.sock
      displayName: 'Grant Docker permissions'
    
    # =========================================================================
    # Docker Build and Push
    # =========================================================================
    
    # Build and push Docker image to ACR
    - task: AzureCLI@2
      inputs:
        azureSubscription: 'RG-Dev-CONNWORKERS-AKS'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
        inlineScript: |
          set -e
          
          echo "Authenticating with Azure..."
          az login --identity
          
          echo "Logging in to ACR..."
          az acr login -n ${{ parameters.ACR_NAME }}

          echo "Building Docker image for ${{ parameters.MICROSERVICE_NAME }}..."
          docker build --no-cache -t ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }}:${{ parameters.IMAGE_TAG }} .
          echo "Docker image built successfully."
          
          echo "Listing Docker images..."
          docker images

          echo "Pushing Docker image to ACR..."
          # Delete existing image with the same tag if it exists
          az acr repository delete --name ${{ parameters.ACR_NAME }} --repository ${{ parameters.IMAGE_NAME }} --tag ${{ parameters.IMAGE_TAG }} --yes || echo "Image could not be deleted or does not exist."
          
          # Push the new image
          docker push "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}"
          echo "Docker image pushed successfully to ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}"

          echo "Cleaning up local Docker resources..."
          docker rmi -f "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" || echo "Image could not be removed."
          docker system prune -a -f 
          echo "Cleanup completed."
      displayName: 'Build and Push Docker Image'
      
    # =========================================================================
    # Optional: Image Verification
    # =========================================================================
    # Uncomment this section to verify the pushed image
    
    # # Verify the pushed image
    # - script: |
    #     echo "Verifying pushed image in ACR..."
    #     az acr repository show --name ${{ parameters.ACR_NAME }} --image ${{ parameters.IMAGE_NAME }}:${{ parameters.IMAGE_TAG }}
    #     
    #     # Get image digest for verification
    #     IMAGE_DIGEST=$(az acr repository show --name ${{ parameters.ACR_NAME }} --image ${{ parameters.IMAGE_NAME }}:${{ parameters.IMAGE_TAG }} --query "digest" -o tsv)
    #     echo "Image digest: $IMAGE_DIGEST"
    #     
    #     # Optional: Scan image for vulnerabilities
    #     # az acr run --registry ${{ parameters.ACR_NAME }} --cmd "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" /dev/null
    #   displayName: 'Verify Pushed Image'
    #   continueOnError: true

# =========================================================================
# Optional: Deployment Trigger Stage
# =========================================================================
# Uncomment this section if you want to trigger a deployment pipeline
# after successful image push
#
# - stage: TriggerDeployment
#   displayName: 'Trigger Deployment Pipeline'
#   dependsOn: DockerBuildAndPush
#   condition: succeeded()
#   jobs:
#   - job: TriggerCD
#     displayName: 'Trigger CD Pipeline'
#     steps:
#     - script: |
#         echo "Triggering deployment pipeline for ${{ parameters.MICROSERVICE_NAME }}..."
#         echo "Image tag: ${{ parameters.IMAGE_TAG }}"
#         echo "Image repository: ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }}"
#       displayName: 'Prepare Deployment Trigger'
#       
#     # Use the Azure DevOps REST API to trigger the CD pipeline
#     - task: AzureCLI@2
#       inputs:
#         azureSubscription: 'Azure-DevOps-Service-Connection'
#         scriptType: 'bash'
#         scriptLocation: 'inlineScript'
#         inlineScript: |
#           echo "Triggering CD pipeline via Azure DevOps API..."
#           
#           # Get Azure DevOps access token
#           TOKEN=$(az account get-access-token --resource "499b84ac-1321-427f-aa17-267ca6975798" --query "accessToken" -o tsv)
#           
#           # Define pipeline parameters
#           PARAMS='{
#             "templateParameters": {
#               "MICROSERVICE_NAME": "${{ parameters.MICROSERVICE_NAME }}",
#               "IMAGE_NAME": "${{ parameters.IMAGE_NAME }}",
#               "IMAGE_TAG": "${{ parameters.IMAGE_TAG }}",
#               "MICROSERVICE_NAMESPACE": "${{ parameters.MICROSERVICE_NAME }}-namespace"
#             }
#           }'
#           
#           # Trigger the pipeline
#           curl -X POST \
#             -H "Content-Type: application/json" \
#             -H "Authorization: Bearer $TOKEN" \
#             -d "$PARAMS" \
#             "https://dev.azure.com/$(System.TeamProject)/_apis/pipelines/$(CD_PIPELINE_ID)/runs?api-version=6.0"
#             
#           echo "CD pipeline triggered successfully."
#       displayName: 'Trigger CD Pipeline'
#       env:
#         CD_PIPELINE_ID: $(cdPipelineId)  # Define this as a variable in your pipeline