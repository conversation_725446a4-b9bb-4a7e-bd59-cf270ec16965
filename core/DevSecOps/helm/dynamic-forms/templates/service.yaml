apiVersion: v1
kind: Service
metadata:
  name: {{ include "dynamic-forms.fullname" . }}
  labels:
    {{- include "dynamic-forms.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.grpcPort | default 5000 }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "dynamic-forms.selectorLabels" . | nindent 4 }}
