apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # All environment variables are defined here
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}
  AZURE_SERVICE_BUS_NAMESPACE: {{ .Values.env.AZURE_SERVICE_BUS_NAMESPACE | quote }}
  #AZURE_SERVICE_BUS_CONNECTION_STRING: {{ .Values.env.AZURE_SERVICE_BUS_CONNECTION_STRING | quote }}
  SERVICE_BUS_TOPIC_NAME: {{ .Values.env.SERVICE_BUS_TOPIC_NAME | quote }}
  SERVICE_BUS_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME | quote }}
  SERVICE_BUS_MAX_CONCURRENT_CALLS: {{ .Values.env.SERVICE_BUS_MAX_CONCURRENT_CALLS | quote }}
  SERVICE_BUS_PREFETCH_COUNT: {{ .Values.env.SERVICE_BUS_PREFETCH_COUNT | quote }}
  SERVICE_BUS_MAX_RETRIES: {{ .Values.env.SERVICE_BUS_MAX_RETRIES | quote }}
  SERVICE_BUS_MAX_RETRY_DELAY_IN_MS: {{ .Values.env.SERVICE_BUS_MAX_RETRY_DELAY_IN_MS | quote }}
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  #  COSMOS_KEY: {{ .Values.env.COSMOS_KEY | quote }}
  COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}
  #AZURE_TENANT_ID: {{ .Values.env.AZURE_TENANT_ID | quote }}
  #AZURE_CLIENT_ID: {{ .Values.env.AZURE_CLIENT_ID | quote }}
  #AZURE_CLIENT_SECRET: {{ .Values.env.AZURE_CLIENT_SECRET | quote }}

