apiVersion: v1
kind: Service
metadata:
  name: {{ include "operator-management.fullname" . }}
  labels:
    {{- include "operator-management.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: 5006
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "operator-management.selectorLabels" . | nindent 4 }} 