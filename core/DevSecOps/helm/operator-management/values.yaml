# Default values for operator-management.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/operator-management
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "crewmanagement-access-sa"

podAnnotations: {}
podLabels: 
   azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

deployment:
  container_port: 3000

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /operator-management(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /operator-management(/|$)(.*)
  #        pathType: Prefix
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls
  
resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 500m
  #   memory: 512Mi
  # requests:
  #   cpu: 100m
  #   memory: 256Mi

# Uncomment and update health checks for Kubernetes to properly monitor the application
#livenessProbe:
#  httpGet:
#    path: /health
#    port: http
#  initialDelaySeconds: 30
#  periodSeconds: 10
#  timeoutSeconds: 5
#  failureThreshold: 3
#readinessProbe:
#  httpGet:
#    path: /health
#    port: http
#  initialDelaySeconds: 10
#  periodSeconds: 10
#  timeoutSeconds: 5

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

# Set client ID for workload identity
workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

# Environment variables for the operator-management service
env:
  # Application Configuration
  NODE_ENV: development  # Changed from Development to production
  HTTP_PORT: "3000"
  CORS_ORIGIN: "*"
  LOG_LEVEL: "debug"  # Changed from debug to info for production
  SERVICE_NAME: "operator-management"
  
  # GRPC Configuration (from main.ts)
  GRPC_HOST: "0.0.0.0"
  GRPC_PORT: "5006"
  
  # Azure Authentication (Using Managed Identity)
  AZURE_USE_MANAGED_IDENTITY: "true"
  
  # Azure Cosmos DB Configuration (from .env.example and configuration.ts)
  COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  COSMOS_DATABASE_ID: "CW_CoreDB_Crew_Management"
  COSMOS_REQUEST_TIMEOUT: "30000"
  COSMOS_CONNECT_TIMEOUT: "30000"
  COSMOS_MAX_RETRIES: "3"
  COSMOS_RETRY_INTERVAL: "1000"
  COSMOS_MAX_WAIT_TIME: "60"
  
  # Azure SQL Database Configuration (from .env.example)
  DB_SERVER: "sql01-nonprd-emea-srv.database.windows.net"
  SQL_PORT: "1433"
  DB_NAME: "CWDB"
  
  # SQL Connection Pool Settings
  DB_MIN_POOL: "1"
  DB_MAX_POOL: "10"
  DB_IDLE_TIMEOUT: "30000"
  
  # SQL Timeout Settings
  DB_CONNECT_TIMEOUT: "30000"
  DB_REQUEST_TIMEOUT: "30000"
  
  # Azure Service Bus Configuration (from servicebus.config.ts)
  AZURE_SERVICE_BUS_NAMESPACE: "ServBus01-NonPrd-EMEA.servicebus.windows.net"
  SERVICE_BUS_TOPIC_NAME: "operator_updates-local-topic"
  SERVICE_BUS_SUBSCRIPTION_NAME: "operator_updates-local-sub"
  SERVICE_BUS_MAX_CONCURRENT_CALLS: "10"
  SERVICE_BUS_PREFETCH_COUNT: "100"
  SERVICE_BUS_MAX_RETRIES: "3"
  SERVICE_BUS_MAX_RETRY_DELAY_IN_MS: "30000"
  SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_MS: "300000" 
