{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "notification.serviceAccountName" . }}
  labels:
    {{- include "notification.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    azure.workload.identity/client-id: "{{ .Values.workloadIdentityClientId }}"
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ .Values.serviceAccount.automount }}
{{- end }}
