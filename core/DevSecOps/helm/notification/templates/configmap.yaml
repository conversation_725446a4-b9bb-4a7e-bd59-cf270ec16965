apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Server Configuration
  NODE_ENV: {{ .Values.env.NODE_ENV | default "production" | quote }}
  HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}
  GLOBAL_PREFIX: {{ .Values.env.GLOBAL_PREFIX | quote }}
  CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | default "*" | quote }}
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }}
  LOG_FORMAT: {{ .Values.env.LOG_FORMAT | default "json" | quote }}
  
  # Azure Managed Identity Configuration
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}

  # Azure Service Bus Configuration
  AZURE_SERVICE_BUS_NAMESPACE: {{ .Values.env.AZURE_SERVICE_BUS_NAMESPACE | quote }}
  SERVICE_BUS_TOPIC_NAME: {{ .Values.env.SERVICE_BUS_TOPIC_NAME | quote }}
  SERVICE_BUS_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME | quote }}
  SERVICE_BUS_TOPIC_NAME_PUBLISHING: {{ .Values.env.SERVICE_BUS_TOPIC_NAME_PUBLISHING | quote }}
  SERVICE_BUS_MAX_CONCURRENT_CALLS: {{ .Values.env.SERVICE_BUS_MAX_CONCURRENT_CALLS | quote }}
  SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_IN_MS: {{ .Values.env.SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_IN_MS | quote }}
  SERVICE_BUS_MAX_RETRIES: {{ .Values.env.SERVICE_BUS_MAX_RETRIES | quote }}

  # Dead Letter Queue Configuration
  AZURE_SERVICE_BUS_DLQ_TOPIC: {{ .Values.env.AZURE_SERVICE_BUS_DLQ_TOPIC | quote }}
  AZURE_SERVICE_BUS_DLQ_SUBSCRIPTION: {{ .Values.env.AZURE_SERVICE_BUS_DLQ_SUBSCRIPTION | quote }}
  AZURE_SERVICE_BUS_DLQ_MAX_RETRIES: {{ .Values.env.AZURE_SERVICE_BUS_DLQ_MAX_RETRIES | quote }}
  AZURE_SERVICE_BUS_DLQ_MAX_CONCURRENT_CALLS: {{ .Values.env.AZURE_SERVICE_BUS_DLQ_MAX_CONCURRENT_CALLS | quote }}
  AZURE_SERVICE_BUS_DLQ_MAX_LOCK_RENEWAL_MS: {{ .Values.env.AZURE_SERVICE_BUS_DLQ_MAX_LOCK_RENEWAL_MS | quote }}

  # Azure Cosmos DB Configuration
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  COSMOS_KEY: {{ .Values.env.COSMOS_KEY | default "" | quote }}
  COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | default "30000" | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | default "3" | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | default "1000" | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | default "60" | quote }}

  # Azure Communication Service
  AZURE_COMMUNICATION_SERVICES_ENDPOINT: {{ .Values.env.AZURE_COMMUNICATION_SERVICES_ENDPOINT | quote }}
  AZURE_COMMUNICATION_SENDER_EMAIL: {{ .Values.env.AZURE_COMMUNICATION_SENDER_EMAIL | quote }}
  AZURE_COMMUNICATION_SENDER_NAME: {{ .Values.env.AZURE_COMMUNICATION_SENDER_NAME | quote }}
  AZURE_COMMUNICATION_SMS_SENDER: {{ .Values.env.AZURE_COMMUNICATION_SMS_SENDER | quote }}

  # Monitoring Configuration
  NOTIFICATION_ERROR_RATE_THRESHOLD: {{ .Values.env.NOTIFICATION_ERROR_RATE_THRESHOLD | default "0.1" | quote }}
  NOTIFICATION_DELIVERY_DELAY_THRESHOLD: {{ .Values.env.NOTIFICATION_DELIVERY_DELAY_THRESHOLD | default "5000" | quote }}
  NOTIFICATION_THROUGHPUT_THRESHOLD: {{ .Values.env.NOTIFICATION_THROUGHPUT_THRESHOLD | default "1000" | quote }}
  NOTIFICATION_METRICS_WINDOW_MS: {{ .Values.env.NOTIFICATION_METRICS_WINDOW_MS | default "60000" | quote }}

  # Email Configuration
  EMAIL_MAX_RETRIES: {{ .Values.env.EMAIL_MAX_RETRIES | default "3" | quote }}
  EMAIL_RETRY_DELAY_MS: {{ .Values.env.EMAIL_RETRY_DELAY_MS | default "1000" | quote }}
  EMAIL_POLLING_INTERVAL_MS: {{ .Values.env.EMAIL_POLLING_INTERVAL_MS | default "2000" | quote }}
  EMAIL_TEMPLATES_DIRECTORY: {{ .Values.env.EMAIL_TEMPLATES_DIRECTORY | default "templates/email" | quote }}
  DEFAULT_EMAIL_LOCALE: {{ .Values.env.DEFAULT_EMAIL_LOCALE | default "en-US" | quote }}

  # SMS Configuration
  SMS_MAX_RETRIES: {{ .Values.env.SMS_MAX_RETRIES | default "3" | quote }}
  SMS_RETRY_DELAY_MS: {{ .Values.env.SMS_RETRY_DELAY_MS | default "1000" | quote }}
  SMS_DEFAULT_CONCURRENCY: {{ .Values.env.SMS_DEFAULT_CONCURRENCY | default "5" | quote }}
  SMS_TEMPLATES_DIRECTORY: {{ .Values.env.SMS_TEMPLATES_DIRECTORY | default "templates/sms" | quote }}
  DEFAULT_SMS_LOCALE: {{ .Values.env.DEFAULT_SMS_LOCALE | default "en-US" | quote }}

  # Notification Configuration
  NOTIFICATION_MAX_RETRIES: {{ .Values.env.NOTIFICATION_MAX_RETRIES | default "3" | quote }}


