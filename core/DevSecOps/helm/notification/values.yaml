# Default values for notification.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/notification
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "shared-access-sa"

podAnnotations: {}
podLabels: 
   azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

deployment:
  container_port: 3000

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /notification(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  #- host: ingress.connected-workersdev.aptiv.com
  #  - paths:
  #      - path: /notification(/|$)(.*)
  #        pathType: Prefix
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

#livenessProbe:
#  httpGet:
#    path: /health
#    port: http
#  initialDelaySeconds: 30
#  periodSeconds: 30
#  timeoutSeconds: 10
#  failureThreshold: 3
#
#readinessProbe:
#  httpGet:
#    path: /health
#    port: http
#  initialDelaySeconds: 15
#  periodSeconds: 10
#  timeoutSeconds: 5
#  failureThreshold: 3

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env: 
  # Server Configuration
  NODE_ENV: "production"
  HTTP_PORT: "3000"
  GLOBAL_PREFIX: "api/v1"
  CORS_ORIGIN: "*"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  
  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: "true"

  # Azure Service Bus Configuration
  AZURE_SERVICE_BUS_NAMESPACE: "ServBus01-NonPrd-EMEA.privatelink.servicebus.windows.net"
  # AZURE_SERVICE_BUS_CONNECTION_STRING: "" # Not needed with managed identity
  SERVICE_BUS_TOPIC_NAME: "notification-topic"
  SERVICE_BUS_SUBSCRIPTION_NAME: "notification-test"
  SERVICE_BUS_TOPIC_NAME_PUBLISHING: "notification-topic"
  SERVICE_BUS_MAX_CONCURRENT_CALLS: "10"
  SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_IN_MS: "300000"
  SERVICE_BUS_MAX_RETRIES: "3"

  # Dead Letter Queue Configuration
  AZURE_SERVICE_BUS_DLQ_TOPIC: "notification-topic"
  AZURE_SERVICE_BUS_DLQ_SUBSCRIPTION: "notification-test"
  AZURE_SERVICE_BUS_DLQ_MAX_RETRIES: "3"
  AZURE_SERVICE_BUS_DLQ_MAX_CONCURRENT_CALLS: "10"
  AZURE_SERVICE_BUS_DLQ_MAX_LOCK_RENEWAL_MS: "300000"

  # Azure Cosmos DB Configuration
  COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  # COSMOS_KEY: "" # Not needed with managed identity
  COSMOS_DATABASE_ID: "CW_CoreDB_Notification"
  COSMOS_REQUEST_TIMEOUT: "30000"
  COSMOS_MAX_RETRIES: "3"
  COSMOS_RETRY_INTERVAL: "1000"
  COSMOS_MAX_WAIT_TIME: "60"

  # Azure Communication Service
  # AZURE_COMMUNICATION_SERVICES_CONNECTION_STRING: "" # Not needed with managed identity
  AZURE_COMMUNICATION_SERVICES_ENDPOINT: "https://commserv01-nonprd-stg-emea.europe.communication.azure.com"
  AZURE_COMMUNICATION_SENDER_EMAIL: "<EMAIL>"
  AZURE_COMMUNICATION_SENDER_NAME: "Connected-Workers"
  AZURE_COMMUNICATION_SMS_SENDER: "+1234567890"

  # Monitoring Configuration
  NOTIFICATION_ERROR_RATE_THRESHOLD: "0.1"
  NOTIFICATION_DELIVERY_DELAY_THRESHOLD: "5000"
  NOTIFICATION_THROUGHPUT_THRESHOLD: "1000"
  NOTIFICATION_METRICS_WINDOW_MS: "60000"

  # Email Configuration
  EMAIL_MAX_RETRIES: "3"
  EMAIL_RETRY_DELAY_MS: "1000"
  EMAIL_POLLING_INTERVAL_MS: "2000"
  EMAIL_TEMPLATES_DIRECTORY: "templates/email"
  DEFAULT_EMAIL_LOCALE: "en-US"

  # SMS Configuration
  SMS_MAX_RETRIES: "3"
  SMS_RETRY_DELAY_MS: "1000"
  SMS_DEFAULT_CONCURRENCY: "5"
  SMS_TEMPLATES_DIRECTORY: "templates/sms"
  DEFAULT_SMS_LOCALE: "en-US"

  # Notification Configuration
  NOTIFICATION_MAX_RETRIES: "3"

