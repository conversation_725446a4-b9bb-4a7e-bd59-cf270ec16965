# Default values for direct-dependents.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/directdependents
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "crewmanagement-access-sa"

podAnnotations: {}
podLabels: 
  azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

deployment:
  container_port: 8080

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /direct-dependents(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL/2920004706684f4cb4e69954ca850510"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /direct-dependents(/|$)(.*)
  #        pathType: ImplementationSpecific
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 500m
  #   memory: 512Mi
  # requests:
  #   cpu: 100m
  #   memory: 256Mi

#livenessProbe:
#  httpGet:
#    path: /health
#    port: http
#readinessProbe:
#  httpGet:
#    path: /health
#    port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env:
  # Server Configuration
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
  ASPNETCORE_HTTPS_PORTS: "8080"
  CORS_ORIGIN: "*"
  LOG_LEVEL: "debug"
  
  # Add base path for swagger
  APP_BASE_PATH: "direct-dependents"

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: "true"
  
  # Cosmos DB Configuration - update to match the variable names in Program.cs
  COSMOS_DB_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  COSMOS_DB_DATABASE_NAME: "CW_CoreDB_Crew_Management"
  COSMOS_REQUEST_TIMEOUT: "30000"
  COSMOS_MAX_RETRIES: "3"
  COSMOS_RETRY_INTERVAL: "1000"
  COSMOS_MAX_WAIT_TIME: "60" 