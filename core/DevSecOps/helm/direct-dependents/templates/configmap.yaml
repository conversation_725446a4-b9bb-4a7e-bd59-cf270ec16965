apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:

  # Cosmos DB Configuration
  COSMOS_DB_ENDPOINT: {{ .Values.env.COSMOS_DB_ENDPOINT | quote }}
  COSMOS_DB_DATABASE_NAME: {{ .Values.env.COSMOS_DB_DATABASE_NAME | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}
  
  # Alternative format for Cosmos DB (nested configuration)
  #CosmosDB__Endpoint: {{ .Values.env.COSMOS_DB_ENDPOINT | quote }}
  #CosmosDB__DatabaseId: {{ .Values.env.COSMOS_DB_DATABASE_NAME | quote }}
  
  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}
  
  # Application Settings
  ASPNETCORE_ENVIRONMENT: {{ .Values.env.ASPNETCORE_ENVIRONMENT | quote }}
  ASPNETCORE_URLS: {{ .Values.env.ASPNETCORE_URLS | quote }}
  HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}
  CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | quote }}
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }}
  
  # Swagger Configuration
  APP_BASE_PATH: {{ .Values.env.APP_BASE_PATH | quote }} 