apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "direct-dependents.fullname" . }}-test-connection"
  labels:
    {{- include "direct-dependents.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "direct-dependents.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never 