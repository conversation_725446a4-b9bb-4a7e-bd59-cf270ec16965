apiVersion: v1
kind: Service
metadata:
  name: {{ include "direct-dependents.fullname" . }}
  labels:
    {{- include "direct-dependents.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "direct-dependents.selectorLabels" . | nindent 4 }} 