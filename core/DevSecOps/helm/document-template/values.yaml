# Default values for document-template.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/document-template-service #toDo
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "module1-access-sa"

podAnnotations: {}
podLabels: 
   azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3005

deployment:
  container_port: 3005

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /document-template(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations:
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /document-template(/|$)(.*)
  #        pathType: Prefix
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# livenessProbe:
#   httpGet:
#     path: /
#     port: http
# readinessProbe:
#   httpGet:
#     path: /
#     port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

env :
    # Application Configuration
    NODE_ENV: production
    HTTP_PORT: "3005"
    CORS_ORIGIN: "*"

    # Azure Identity
    AZURE_USE_MANAGED_IDENTITY : true
   
    # Azure Service Bus
    AZURE_SERVICE_BUS_NAMESPACE: ServBus01-NonPrd-EMEA.servicebus.windows.net
    SERVICE_BUS_TOPIC_NAME: workflow-topic
    SERVICE_BUS_SUBSCRIPTION_NAME : document-template-sub
    SERVICE_BUS_TOPIC_NAME_PUBLISHING : document-topic
    SERVICE_BUS_SUBSCRIPTION_NAME_PUBLISHING : document-generation-sub
    SERVICE_BUS_MAX_CONCURRENT_CALLS : 10
    SERVICE_BUS_PREFETCH_COUNT : 100
    SERVICE_BUS_MAX_RETRIES : 3

    # Azure Blob Storage Configuration
    BLOB_STORAGE_CONNECTION_STRING: "abc"  # Connection string for blob storage - required when not using managed identity
    BLOB_STORAGE_CONTAINER_NAME: "signatures"  # Container name for blob storage, defaults to 'signatures'
    AZURE_BLOB_STORAGE_ENDPOINT: "https://sacwdocgenration.blob.core.windows.net"  # Blob storage endpoint URL for managed identity

    # Cosmos
    COSMOS_ENDPOINT: https://cosmos01-nonprd-emea.documents.azure.com:443/
    COSMOS_DATABASE_ID: CW_CoreDB_Raise_Request
    COSMOS_REQUEST_TIMEOUT: 30000
    COSMOS_MAX_RETRIES: 3
    COSMOS_RETRY_INTERVAL: 1000
    COSMOS_MAX_WAIT_TIME: 60

