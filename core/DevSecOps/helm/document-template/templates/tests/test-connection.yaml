apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "document-template.fullname" . }}-test-connection"
  labels:
    {{- include "document-template.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "document-template.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
