apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
    # Application Configuration
    NODE_ENV: {{ .Values.env.NODE_ENV | quote }}
    HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}
    CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | quote }}
    
    # Azure Identity
    AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}
    
    # Azure Service Bus
    AZURE_SERVICE_BUS_CONNECTION_STRING: {{ .Values.env.AZURE_SERVICE_BUS_CONNECTION_STRING | quote }}
    AZURE_SERVICE_BUS_NAMESPACE: {{ .Values.env.AZURE_SERVICE_BUS_NAMESPACE | quote }}
    SERVICE_BUS_TOPIC_NAME: {{ .Values.env.SERVICE_BUS_TOPIC_NAME | quote }}
    SERVICE_BUS_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME | quote }}
    SERVICE_BUS_TOPIC_NAME_PUBLISHING: {{ .Values.env.SERVICE_BUS_TOPIC_NAME_PUBLISHING | quote }}
    SERVICE_BUS_SUBSCRIPTION_NAME_PUBLISHING: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME_PUBLISHING | quote }}
    SERVICE_BUS_MAX_CONCURRENT_CALLS: {{ .Values.env.SERVICE_BUS_MAX_CONCURRENT_CALLS | quote }}
    SERVICE_BUS_PREFETCH_COUNT: {{ .Values.env.SERVICE_BUS_PREFETCH_COUNT | quote }}
    SERVICE_BUS_MAX_RETRIES: {{ .Values.env.SERVICE_BUS_MAX_RETRIES | quote }}
    
    # Azure Blob Storage
    BLOB_STORAGE_CONNECTION_STRING: {{ .Values.env.BLOB_STORAGE_CONNECTION_STRING | quote }}
    BLOB_STORAGE_CONTAINER_NAME: {{ .Values.env.BLOB_STORAGE_CONTAINER_NAME | quote }}
    AZURE_BLOB_STORAGE_ENDPOINT: {{ .Values.env.AZURE_BLOB_STORAGE_ENDPOINT | quote }}
    
    # Cosmos DB
    COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
    COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
    COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
    COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
    COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
    COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}

