apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "dhwalk.fullname" . }}-config
  labels:
    {{- include "dhwalk.labels" . | nindent 4 }}
data:
  # Server configurations
  NODE_ENV: {{ .Values.env.NODE_ENV | default "development" | quote }}
  PORT: {{ .Values.env.PORT | default 3000 | quote }}
  SERVICE_NAME: {{ .Values.env.SERVICE_NAME | default "dhwalk-service" | quote }}
  GRPC_HOST: {{ .Values.env.GRPC_HOST | default "0.0.0.0" | quote }}
  GRPC_PORT: {{ .Values.env.GRPC_PORT | default 5006 | quote }}
  GLOBAL_PREFIX: {{ .Values.env.GLOBAL_PREFIX | default "api/v1" | quote }}
  CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | default "*" | quote }}
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | default "debug" | quote }}
  
  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | default "true" | quote }}
  
  # Database configuration (SQL Server)
  DB_SERVER: {{ .Values.env.DB_SERVER | quote }}
  DB_NAME: {{ .Values.env.DB_NAME | quote }}
  
  # Cosmos DB connection settings (non-sensitive)
  COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | default 30000 | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | default 3 | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | default 1000 | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | default 60 | quote }}
  
  # Logging
  LOG_LEVELS: {{ .Values.env.LOG_LEVELS | default "error,warn,log,debug,verbose" | quote }} 