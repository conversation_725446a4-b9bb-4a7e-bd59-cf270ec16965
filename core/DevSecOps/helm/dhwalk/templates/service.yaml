apiVersion: v1
kind: Service
metadata:
  name: {{ include "dhwalk.fullname" . }}
  labels:
    {{- include "dhwalk.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
    {{- if .Values.env.GRPC_PORT }}
    - port: {{ .Values.env.GRPC_PORT }}
      targetPort: {{ .Values.env.GRPC_PORT }}
      protocol: TCP
      name: grpc
    {{- end }}
  selector:
    {{- include "dhwalk.selectorLabels" . | nindent 4 }} 