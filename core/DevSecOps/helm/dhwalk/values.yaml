# Default values for dhwalk service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/dhwalk
  pullPolicy: Always # IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "crewmanagement-access-sa"

podAnnotations: {}
podLabels: 
  azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

deployment:
  container_port: 3000

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /dhwalk(/|$)(.*)
  servicePort: 80
  annotations: {}

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL/2920004706684f4cb4e69954ca850510"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /dhwalk(/|$)(.*)
  #        pathType: ImplementationSpecific
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# livenessProbe:
#   httpGet:
#     path: /health
#     port: http
# readinessProbe:
#   httpGet:
#     path: /health
#     port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env:
  # Server configurations
  NODE_ENV: development
  PORT: 3000
  SERVICE_NAME: dhwalk
  GRPC_HOST: 0.0.0.0
  GRPC_PORT: 5006
  GLOBAL_PREFIX: api/v1
  CORS_ORIGIN: "http://localhost:3000,https://ingress.connected-workersdev.aptiv.com,https://connected-workersdev.aptiv.com,http://localhost:5173"
  LOG_LEVEL: debug

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: true
  
  # Database configuration (SQL Server)
  DB_SERVER: "sql01-nonprd-emea-srv.database.windows.net"
  DB_NAME: "CWDB"
  
  # Azure Cosmos DB Configuration
  COSMOS_DATABASE_ID: CW_CoreDB_Crew_Management
  #COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.privatelink.documents.azure.com:443" # Use the private endpoint for Cosmos DB
  COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/" # Use the public endpoint for Cosmos DB
  COSMOS_REQUEST_TIMEOUT: 30000
  COSMOS_MAX_RETRIES: 3
  COSMOS_RETRY_INTERVAL: 1000
  COSMOS_MAX_WAIT_TIME: 60

  # Logging configurations
  LOG_LEVELS: "error,warn,log,debug,verbose"

# Sensitive environment variables stored in a Kubernetes Secret
# These will be created separately and not included in the ConfigMap
secrets:
  # Azure Cosmos DB sensitive configuration
  COSMOS_KEY: "" # This will be populated from a separate Kubernetes Secret
  # Database credentials
  DB_USER: "" # This will be populated from a separate Kubernetes Secret
  DB_PASSWORD: "" # This will be populated from a separate Kubernetes Secret 