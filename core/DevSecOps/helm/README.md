# Helm Charts for Microservices Deployment

## Overview
This directory contains Helm charts for deploying microservices to Kubernetes. <PERSON><PERSON> provides a powerful way to define, install, and upgrade Kubernetes applications through charts, which are packages of pre-configured Kubernetes resources.

![Helm Architecture](https://mermaid.ink/img/pako:eNp1ksFqwzAMhl9F-NRCu-cQWMnWHQbrsIXtYvxhNWaxg-0UWsrefdLSJWvZQTpZ-vX9kiwfoDUGsYKdtYN3xhLsnfNEzjiyFMgGUs6O5MjvKDlLsM_kyAXPsM1TIvE2UMgukiebSXaTIxdpR16gjeTIk8k5chF2FEyOYjKZI-cpUrTJOXKRduSTyTlylHYUksk5cpF25E0yOYqRtKOYTM6Ri7Qjn0yOYiLtKCSTc-Qi7cibZHIUlbSjaJLJOXKRduRNMjnylbSjkEzOkYu0I59MzpFraUchmZwjF2lH3iSTo6ikHUWTTM6Ri7Qjb5LJkdfSjkIyOUcu0o58MjlHbqQdhWRyjlykHXmTTI6ikXYUTTI5R_4i7SiYZHIUrbSjaJLJOXKRduRNMjnynbSjkEzOkn8BxLZXdw?type=png)

## Chart Structure

Each microservice has its own Helm chart with the following structure:

```
microservice-name/
├── .helmignore        # Files to ignore when packaging
├── Chart.yaml         # Chart metadata
├── templates/         # Kubernetes resource templates
│   ├── _helpers.tpl   # Template helpers
│   ├── configmap.yaml # ConfigMap for environment variables
│   ├── deployment.yaml # Deployment configuration
│   ├── hpa.yaml       # Horizontal Pod Autoscaler
│   ├── ingress.yaml   # Ingress configuration
│   ├── NOTES.txt      # Usage notes
│   ├── service.yaml   # Service configuration
│   ├── serviceaccount.yaml # Service account
│   └── tests/         # Test resources
└── values.yaml        # Default configuration values
```

## Available Charts

The following Helm charts are available:

1. **document-generation** - Document Generation Service
2. **document-template** - Document Template Service
3. **dynamic-forms** - Dynamic Forms Service
4. **notification** - Notification Service
5. **request** - Request Service
6. **user** - User Service
7. **workflowengine** - Workflow Engine Service

## Deployment Architecture

![Deployment Architecture](https://mermaid.ink/img/pako:eNqNk09v2zAMxb-K4FML7T7HQJpsXQ_betgG28X4wxrsYgfbKbSU_e6TlrZJm3aHnSw9_d57knwAbTSIFeyMHbzThmDvnCdyxpGhQDaQcnYkR35HyVmCfSZHLniGbZ4SiTeBQnaRPNlMspscuUg78gJtJEeeTM6Ri7CjYHIUk8kcOU-Rok3OkYu0I59MzpFT2lFIJufIRdqRN8nkKEbSjmIyOUcu0o58MjmKiTwHoUzOkYu0I2-SyVFU0omiSSbnyEXakTfJ5MhX8hyEZHKOXKQd-WRyjlzLcxCSyTlykXbkTTI5iko6UTTJ5By5SDvyJpkceS3PQUgm58hF2pFPJufIjTwHIZmcIxdpR94kk6NopBNFk0zOkYu0I2-SyZGv5TkIyeQcuUg78snkyHfyHIRkco5cpB15k0yOopVOFE0yOUcu0o68SSZHvpPnICSTs-QfLgEbxQ?type=png)

## Deployment Workflow

![Deployment Workflow](https://mermaid.ink/img/pako:eNp9ksFqwzAMhl9F-NRCu-cQWMnWHQbrsIXtYvxhNWaxg-0UWsrefdLSJWvZQTpZ-vX9kiwfoDUGsYKdtYN3xhLsnfNEzjiyFMgGUs6O5MjvKDlLsM_kyAXPsM1TIvE2UMgukiebSXaTIxdpR16gjeTIk8k5chF2FEyOYjKZI-cpUrTJOXKRduSTyTlylHYUksk5cpF25E0yOYqRtKOYTM6Ri7Qjn0yOYiLtKCSTc-Qi7cibZHIUlbSjaJLJOXKRduRNMjnylbSjkEzOkYu0I59MzpFraUchmZwjF2lH3iSTo6ikHUWTTM6Ri7Qjb5LJkdfSjkIyOUcu0o58MjlHbqQdhWRyjlykHXmTTI6ikXYUTTI5R_4i7SiYZHIUrbSjaJLJOXKRduRNMjnynbSjkEzOkn8BxLZXdw?type=png)

## Prerequisites
- Kubernetes cluster
- Helm 3.x installed
- Access to Azure Container Registry (ACR)

## Deploying a Chart

To deploy a microservice using its Helm chart:

```bash
# Add the Helm repository (if using a repository)
helm repo add my-repo <repository-url>
helm repo update

# Install or upgrade a chart
helm upgrade --install <release-name> ./microservice-name \
  --namespace <namespace> \
  --set image.repository=<acr-name>.azurecr.io/<image-name>,image.tag=<tag>
```

Example:
```bash
helm upgrade --install document-generation ./document-generation \
  --namespace troubleshootingnamespace \
  --set image.repository=acr003nonprdemearepo.azurecr.io/document-generation,image.tag=1017
```

## Configuration

Each chart can be configured through the `values.yaml` file or by providing values during installation:

```bash
helm upgrade --install <release-name> ./microservice-name \
  --set key1=value1,key2=value2
```

Common configuration parameters:

| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicaCount` | Number of replicas | `2` |
| `image.repository` | Docker image repository | `acr003nonprdemearepo.azurecr.io/<service-name>` |
| `image.tag` | Docker image tag | `""` (uses appVersion from Chart.yaml) |
| `image.pullPolicy` | Image pull policy | `IfNotPresent` |
| `imagePullSecrets` | Image pull secrets | `[]` |
| `nameOverride` | Override chart name | `""` |
| `fullnameOverride` | Override full chart name | `""` |
| `serviceAccount.create` | Create service account | `true` |
| `serviceAccount.annotations` | Service account annotations | `{}` |
| `serviceAccount.name` | Service account name | `""` |
| `podAnnotations` | Pod annotations | `{}` |
| `podSecurityContext` | Pod security context | `{}` |
| `securityContext` | Container security context | `{}` |
| `service.type` | Service type | `ClusterIP` |
| `service.port` | Service port | `80` |
| `ingress.enabled` | Enable ingress | `false` |
| `ingress.className` | Ingress class name | `""` |
| `ingress.annotations` | Ingress annotations | `{}` |
| `ingress.hosts` | Ingress hosts | `[]` |
| `ingress.tls` | Ingress TLS configuration | `[]` |
| `resources` | Resource requests and limits | `{}` |
| `autoscaling.enabled` | Enable autoscaling | `false` |
| `autoscaling.minReplicas` | Minimum replicas | `1` |
| `autoscaling.maxReplicas` | Maximum replicas | `100` |
| `autoscaling.targetCPUUtilizationPercentage` | Target CPU utilization | `80` |
| `nodeSelector` | Node selector | `{}` |
| `tolerations` | Tolerations | `[]` |
| `affinity` | Affinity | `{}` |
| `env` | Environment variables | `{}` |

## Environment-Specific Values

Each chart includes environment-specific values files:

- `values-dev.yaml` - Development environment
- `values-test.yaml` - Test environment
- `values-prod.yaml` - Production environment

To deploy to a specific environment:

```bash
helm upgrade --install <release-name> ./microservice-name \
  -f ./microservice-name/values-dev.yaml
```

## Helm Chart Benefits

![Helm Benefits](https://mermaid.ink/img/pako:eNp9ksFqwzAMhl9F-NRCu-cQWMnWHQbrsIXtYvxhNWaxg-0UWsrefdLSJWvZQTpZ-vX9kiwfoDUGsYKdtYN3xhLsnfNEzjiyFMgGUs6O5MjvKDlLsM_kyAXPsM1TIvE2UMgukiebSXaTIxdpR16gjeTIk8k5chF2FEyOYjKZI-cpUrTJOXKRduSTyTlylHYUksk5cpF25E0yOYqRtKOYTM6Ri7Qjn0yOYiLtKCSTc-Qi7cibZHIUlbSjaJLJOXKRduRNMjnylbSjkEzOkYu0I59MzpFraUchmZwjF2lH3iSTo6ikHUWTTM6Ri7Qjb5LJkdfSjkIyOUcu0o58MjlHbqQdhWRyjlykHXmTTI6ikXYUTTI5R_4i7SiYZHIUrbSjaJLJOXKRduRNMjnynbSjkEzOkn8BxLZXdw?type=png)

1. **Reproducibility**
   - Consistent deployments across environments
   - Version-controlled configurations
   - Deterministic results
   - Reduced configuration drift

2. **Templating**
   - Dynamic resource generation
   - Environment-specific configurations
   - Reusable components
   - Reduced duplication

3. **Dependency Management**
   - Managed service dependencies
   - Versioned dependencies
   - Controlled upgrades
   - Simplified rollbacks

4. **Lifecycle Management**
   - Controlled deployments
   - Managed upgrades
   - Easy rollbacks
   - Deployment history

## Troubleshooting

### Common Issues
- **Chart Installation Fails**: Check syntax errors in values or templates
- **Pods Not Starting**: Check resource constraints and image availability
- **Service Unavailable**: Verify service and ingress configurations
- **Configuration Issues**: Check environment variables and ConfigMaps

### Debugging Commands
```bash
# Check Helm release status
helm status <release-name> -n <namespace>

# Debug template rendering
helm template <chart-directory> --debug

# View Kubernetes resources
kubectl get all -n <namespace> -l app.kubernetes.io/instance=<release-name>

# Check pod logs
kubectl logs -n <namespace> -l app.kubernetes.io/instance=<release-name>
```

## Best Practices

1. **Version Control**
   - Store charts in version control
   - Use semantic versioning
   - Document changes in Chart.yaml

2. **Values Management**
   - Use environment-specific values files
   - Minimize hardcoded values
   - Document all configurable parameters

3. **Security**
   - Set appropriate resource limits
   - Configure network policies
   - Use RBAC for service accounts
   - Implement pod security contexts

4. **Testing**
   - Test chart installations
   - Validate template rendering
   - Check for syntax errors
   - Verify resource creation

## Contact

- Team: DevOps Team
- Email: <EMAIL>
- Slack: #devops-support 