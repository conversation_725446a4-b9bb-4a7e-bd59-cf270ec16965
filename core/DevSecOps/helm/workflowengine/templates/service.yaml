apiVersion: v1
kind: Service
metadata:
  name: {{ include "workflowengine.fullname" . }}
  labels:
    {{- include "workflowengine.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.grpcPort }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "workflowengine.selectorLabels" . | nindent 4 }}
