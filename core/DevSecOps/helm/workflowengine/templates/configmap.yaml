apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Key-value pairs for the environment variables
   HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}
   AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}
   CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | quote}}
   TCP_PORT: {{ .Values.env.TCP_PORT | quote }}
   LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }}
   NODE_ENV: {{ .Values.env.NODE_ENV | quote }}
   COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  #  COSMOS_KEY: {{ .Values.env.COSMOS_KEY | quote }}
   COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
   COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
   COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
   COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
   COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}

