apiVersion: v1
kind: Service
metadata:
  name: {{ include "user.fullname" . }}
  labels:
    {{- include "user.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.env.GRPC_PORT }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "user.selectorLabels" . | nindent 4 }}
