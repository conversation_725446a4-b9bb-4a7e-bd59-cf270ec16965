apiVersion: v1
kind: Service
metadata:
  name: {{ include "productionplan.fullname" . }}
  labels:
    {{- include "productionplan.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "productionplan.selectorLabels" . | nindent 4 }} 