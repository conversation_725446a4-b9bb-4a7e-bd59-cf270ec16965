apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Application Settings
  ASPNETCORE_ENVIRONMENT: {{ .Values.env.ASPNETCORE_ENVIRONMENT | quote }}
  ASPNETCORE_URLS: {{ .Values.env.ASPNETCORE_URLS | quote }}
  ASPNETCORE_HTTPS_PORTS: {{ .Values.env.ASPNETCORE_HTTPS_PORTS | quote }}
  CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | quote }}
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }}
  
  # Swagger Configuration
  APP_BASE_PATH: {{ .Values.env.APP_BASE_PATH | quote }}
  
  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}

  # Cosmos DB Configuration (Primary format - CosmosDB:*)
  # This format matches the appsettings.json structure used by the microservice
  # Using managed identity - no Account<PERSON><PERSON> needed
  CosmosDB__AccountEndpoint: {{ .Values.env.CosmosDB__AccountEndpoint | quote }}
  CosmosDB__DatabaseName: {{ .Values.env.CosmosDB__DatabaseName | quote }}
  CosmosDB__DatabaseId: {{ .Values.env.CosmosDB__DatabaseId | quote }}
  CosmosDB__ShiftDocumentContainerId: {{ .Values.env.CosmosDB__ShiftDocumentContainerId | quote }}
  CosmosDB__TypeShiftContainerId: {{ .Values.env.CosmosDB__TypeShiftContainerId | quote }}
  
  # Alternative Cosmos DB Configuration (CosmosDb:* format)
  # Also used by the microservice for Entity Framework configuration
  # Using managed identity - no AccountKey needed
  CosmosDb__AccountEndpoint: {{ .Values.env.CosmosDb__AccountEndpoint | quote }}
  CosmosDb__DatabaseName: {{ .Values.env.CosmosDb__DatabaseName | quote }}

  # Environment variable format (for future compatibility)
  COSMOS_DB_ENDPOINT: {{ .Values.env.COSMOS_DB_ENDPOINT | quote }}
  COSMOS_DB_DATABASE_NAME: {{ .Values.env.COSMOS_DB_DATABASE_NAME | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}
  
  # Azure Service Bus Configuration (Using managed identity)
  # Service Bus namespace for managed identity connection
  AZURE_SERVICEBUS_NAMESPACE: {{ .Values.env.AZURE_SERVICEBUS_NAMESPACE | quote }}
  AzureServiceBus__TopicShiftType: {{ .Values.env.AzureServiceBus__TopicShiftType | quote }}
  AzureServiceBus__TopicShifDocument: {{ .Values.env.AzureServiceBus__TopicShifDocument | quote }}
  AzureServiceBus__SubscriptionName: {{ .Values.env.AzureServiceBus__SubscriptionName | quote }}
  
  # Application Settings (Required by ProductionPlan microservice)
  AppSettings__useLocalCosmos: {{ .Values.env.AppSettings__useLocalCosmos | quote }}
  AppSettings__MaxItemCount: {{ .Values.env.AppSettings__MaxItemCount | quote }}
  
  # Logging Configuration
  Logging__LogLevel__Default: {{ .Values.env.Logging__LogLevel__Default | quote }}
  Logging__LogLevel__Microsoft__AspNetCore: {{ .Values.env.Logging__LogLevel__Microsoft__AspNetCore | quote }}
  
  # SQL Database Configuration (if needed for production planning)
  SQL_CONNECTION_STRING: {{ .Values.env.SQL_CONNECTION_STRING | quote }} 