1. Get the application URL by running these commands:
{{- if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "workstation.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "workstation.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "workstation.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.service.port }}
{{- else if contains "ClusterIP" .Values.service.type }}
  The Workstation service is accessible within the cluster at:
  http://{{ include "workstation.fullname" . }}.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.service.port }}

  To access the service externally, you can use port-forwarding:
  kubectl port-forward --namespace {{ .Release.Namespace }} service/{{ include "workstation.fullname" . }} {{ .Values.service.port }}:{{ .Values.service.port }}
{{- end }}

2. The service is configured with the following settings:
   - Environment: {{ .Values.env.NODE_ENV }}
   - HTTP Port: {{ .Values.env.HTTP_PORT }}
   - Database: {{ .Values.env.COSMOS_DATABASE_ID }}

3. To check the status of the deployment:
   kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "workstation.name" . }},app.kubernetes.io/instance={{ .Release.Name }}"

4. To view the logs:
   kubectl logs --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "workstation.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" 