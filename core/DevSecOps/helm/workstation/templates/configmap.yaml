apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Application Configuration
  NODE_ENV: {{ .Values.env.NODE_ENV | quote }}
  HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}

  # Azure Cosmos DB Configuration
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
  COSMOS_CONNECT_TIMEOUT: {{ .Values.env.COSMOS_CONNECT_TIMEOUT | quote }}
  COSMOS_MAX_RETRY_ATTEMPTS: {{ .Values.env.COSMOS_MAX_RETRY_ATTEMPTS | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}

  # Service Bus Configuration
  AZURE_SERVICE_BUS_NAMESPACE: {{ .Values.env.AZURE_SERVICE_BUS_NAMESPACE | quote }}
  SERVICE_BUS_TOPIC_NAME: {{ .Values.env.SERVICE_BUS_TOPIC_NAME | quote }}
  SERVICE_BUS_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME | quote }}
  SERVICE_BUS_MAX_CONCURRENT_CALLS: {{ .Values.env.SERVICE_BUS_MAX_CONCURRENT_CALLS | quote }}
  SERVICE_BUS_PREFETCH_COUNT: {{ .Values.env.SERVICE_BUS_PREFETCH_COUNT | quote }}
  SERVICE_BUS_MAX_RETRIES: {{ .Values.env.SERVICE_BUS_MAX_RETRIES | quote }}
  SERVICE_BUS_MAX_RETRY_DELAY_IN_MS: {{ .Values.env.SERVICE_BUS_MAX_RETRY_DELAY_IN_MS | quote }}

  # SQL Database Settings
  DB_SERVER: {{ .Values.env.DB_SERVER | quote }}
  DB_NAME: {{ .Values.env.DB_NAME | quote }}

  # Connection Pool Settings
  DB_MIN_POOL: {{ .Values.env.DB_MIN_POOL | quote }}
  DB_MAX_POOL: {{ .Values.env.DB_MAX_POOL | quote }}
  DB_IDLE_TIMEOUT: {{ .Values.env.DB_IDLE_TIMEOUT | quote }}

  # Timeout Settings
  DB_CONNECT_TIMEOUT: {{ .Values.env.DB_CONNECT_TIMEOUT | quote }}
  DB_REQUEST_TIMEOUT: {{ .Values.env.DB_REQUEST_TIMEOUT | quote }}

  # Logging
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }} 