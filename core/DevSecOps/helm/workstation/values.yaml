# Default values for workstation.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/workstation-service
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "" #todo

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "crewmanagement-access-sa"

podAnnotations: {}
podLabels: 
    azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3009

deployment:
  container_port: 3009

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /workstation(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env:
  # Application Configuration
  NODE_ENV: development
  HTTP_PORT: 3009

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: true

  # Service Bus Configuration
  AZURE_SERVICE_BUS_NAMESPACE: ServBus01-NonPrd-EMEA.servicebus.windows.net
  SERVICE_BUS_TOPIC_NAME: workstation-changes-local-topic
  SERVICE_BUS_SUBSCRIPTION_NAME: workstation-changes-local-sub
  SERVICE_BUS_MAX_CONCURRENT_CALLS: 10
  SERVICE_BUS_PREFETCH_COUNT: 100
  SERVICE_BUS_MAX_RETRIES: 3
  SERVICE_BUS_MAX_RETRY_DELAY_IN_MS: 30000

  # Azure Cosmos DB Configuration
  COSMOS_ENDPOINT: https://cosmos01-nonprd-emea.documents.azure.com:443/
  COSMOS_DATABASE_ID: CW_CoreDB_Crew_Management
  COSMOS_REQUEST_TIMEOUT: 30000
  COSMOS_CONNECT_TIMEOUT: 30000
  COSMOS_MAX_RETRY_ATTEMPTS: 3
  COSMOS_MAX_RETRIES: 3
  COSMOS_RETRY_INTERVAL: 1000
  COSMOS_MAX_WAIT_TIME: 60

  # SQL Database Settings
  DB_SERVER: sql01-nonprd-emea-srv.database.windows.net
  DB_NAME: CWDB

  # Connection Pool Settings
  DB_MIN_POOL: 1
  DB_MAX_POOL: 10
  DB_IDLE_TIMEOUT: 30000

  # Timeout Settings
  DB_CONNECT_TIMEOUT: 30000
  DB_REQUEST_TIMEOUT: 30000

  # Logging
  LOG_LEVEL: debug 