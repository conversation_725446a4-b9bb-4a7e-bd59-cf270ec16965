{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "disciplinary-measures.serviceAccountName" . }}
  labels:
    {{- include "disciplinary-measures.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  annotations:
    azure.workload.identity/client-id: {{ .Values.workloadIdentityClientId }}
{{- end }} 
