apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "disciplinary-measures.fullname" . }}-test-connection"
  labels:
    {{- include "disciplinary-measures.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "disciplinary-measures.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
