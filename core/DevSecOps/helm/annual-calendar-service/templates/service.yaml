apiVersion: v1
kind: Service
metadata:
  name: {{ include "annualcalendarservice.fullname" . }}
  labels:
    {{- include "annualcalendarservice.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "annualcalendarservice.selectorLabels" . | nindent 4 }} 