Thank you for installing {{ .Chart.Name }}.

Your shared ingress has been configured for the following services:
{{- range .Values.ingress.services }}
- {{ .name }} ({{ .path }})
{{- end }}

The ingress is configured with:
Host: {{ .Values.ingress.host }}
TLS Secret: {{ .Values.ingress.tls.secretName }}

To verify the ingress is working, try:

1. Check if the ingress controller is running:
   kubectl get pods -n ingress-system

2. Check if the ingress resource is created:
   kube<PERSON>l get ingress shared-ingress

3. Check the ingress status:
   kubectl describe ingress shared-ingress

4. Test the endpoints:
{{- range .Values.ingress.services }}
   curl -k https://{{ $.Values.ingress.host }}{{ .path }}
{{- end }}

Note: Make sure all the referenced services are deployed and running before testing the endpoints.

For troubleshooting:
1. Check ingress controller logs:
   kubectl logs -n ingress-system <ingress-controller-pod-name>

2. Verify TLS certificate:
   kubectl get secret {{ .Values.ingress.tls.secretName }} 