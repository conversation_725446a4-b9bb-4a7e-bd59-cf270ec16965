{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "shared-ingress.fullname" . }}
  labels:
    {{- include "shared-ingress.labels" . | nindent 4 }}
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: connected-workers
  annotations:
    {{- with .Values.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.className }}
  {{- if .Values.ingress.tls.enabled }}
  tls:
    - hosts:
        - {{ .Values.ingress.host | quote }}
      secretName: {{ .Values.ingress.tls.secretName }}
  {{- end }}
  rules:
    - host: {{ .Values.ingress.host | quote }}
      http:
        paths:
          {{- range .Values.ingress.services }}
          - path: {{ .path }}
            pathType: {{ $.Values.ingress.defaultServiceSettings.pathType }}
            backend:
              service:
                name: {{ .serviceName }}
                port:
                  number: {{ default $.Values.ingress.defaultServiceSettings.port .servicePort }}
          {{- end }}
{{- end }}