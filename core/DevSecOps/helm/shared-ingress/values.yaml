nameOverride: ""
fullnameOverride: "shared-ingress"

ingress:
  enabled: true
  className: "webapprouting.kubernetes.azure.com"
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "http://localhost:5173,http://localhost:3000,https://connected-workersdev.aptiv.com,https://ingress.connected-workersdev.aptiv.com,http://connected-workersdev.aptiv.com,http://ingress.connected-workersdev.aptiv.com"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization,Accept,Origin,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,ocp-apim-subscription-key,x-signalr-user-agent,x-ms-signalr-connectionid,x-ms-signalr-connectiontoken,x-ms-signalr-user-agent,x-ms-signalr-connectionid,x-ms-signalr-connectiontoken,x-ms-signalr-negotiate-version,x-ms-signalr-negotiate-version"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-max-age: "3600"
    nginx.ingress.kubernetes.io/rewrite-target: /$2

    kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL/abd78a4601554cf5963380c4cd974979"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/ssl-passthrough: "false"
    azure.application-routing.kubernetes.io/certificate-source: KeyVault
    kubernetes.azure.com/use-osm-mtls: "false"
  host: ingress.connected-workersdev.aptiv.com

  tls:
    enabled: true
    secretName: keyvault-shared-ingress
    secretNamespace: ""

  # Global settings for all services
  defaultServiceSettings:
    port: 80
    pathType: Prefix
    backendProtocol: "HTTP"

  services:
    - name: request
      path: /request(/|$)(.*)
      serviceName: request
      servicePort: 80
      annotations: {}

    - name: user
      path: /user(/|$)(.*)
      serviceName: user
      servicePort: 80
      annotations: {}

    - name: workflow
      path: /workflow(/|$)(.*)
      serviceName: workflow
      servicePort: 80
      annotations: {}

    - name: auth
      path: /authentication(/|$)(.*)
      serviceName: auth
      servicePort: 80
      annotations: {}

    - name: document-generation
      path: /document-generation(/|$)(.*)
      serviceName: document-generation
      servicePort: 80
      annotations: {}

    - name: document-template
      path: /document-template(/|$)(.*)
      serviceName: document-template
      servicePort: 80
      annotations: {}

    - name: dynamic-forms
      path: /dynamic-forms(/|$)(.*)
      serviceName: dynamic-forms
      servicePort: 80
      annotations: {}

    - name: notification
      path: /notification(/|$)(.*)
      serviceName: notification
      servicePort: 80
      annotations: {}

    - name: notifications-signalr
      path: /notifications-signalr(/|$)(.*)
      serviceName: notifications-signalr
      servicePort: 80
      annotations: {}

    - name: operator-management
      path: /operator-management(/|$)(.*)
      serviceName: operator-management
      servicePort: 80
      annotations: {}

    - name: workflowengine
      path: /workflowengine(/|$)(.*)
      serviceName: workflowengine
      servicePort: 80
      annotations: {}

    - name: dhwalk
      path: /dhwalk(/|$)(.*)
      serviceName: dhwalk
      servicePort: 80
      annotations: {}

    - name: direct-dependents
      path: /direct-dependents(/|$)(.*)
      serviceName: direct-dependents
      servicePort: 80
      annotations: {}

    - name: workstation
      path: /workstation(/|$)(.*)
      serviceName: workstation
      servicePort: 80
      annotations: {}

    - name: employee-assignment
      path: /employee-assignment(/|$)(.*)
      serviceName: employee-assignment
      servicePort: 80
      annotations: {}

    - name: operator-skills
      path: /operator-skills(/|$)(.*)
      serviceName: operator-skills
      servicePort: 80
      annotations: {}

    - name: annual-calendar
      path: /annual-calendar(/|$)(.*)
      serviceName: annual-calendar
      servicePort: 80
      annotations: {}

    - name: skillsmatrix
      path: /skillsmatrix(/|$)(.*)
      serviceName: skillsmatrix
      servicePort: 80
      annotations: {}

    - name: document-validation
      path: /document-validation(/|$)(.*)
      serviceName: document-validation
      servicePort: 80
      annotations: {}

    - name: training
      path: /training(/|$)(.*)
      serviceName: training-service
      servicePort: 80
      annotations: {}

    - name: nurse
      path: /nurse(/|$)(.*)
      serviceName: nurse-service
      servicePort: 80
      annotations: {}

    - name: productionplan
      path: /productionplan(/|$)(.*)
      serviceName: productionplan
      servicePort: 80
      annotations: {}

    - name: replacement
      path: /replacement(/|$)(.*)
      serviceName: replacement
      servicePort: 80
      annotations: {}

# Routing service to namespace
externalServices:
  # Crew Management Services
  - name: dhwalk
    namespace: crew-management
    port: 80
  - name: workstation
    namespace: crew-management
    port: 80
  - name: employee-assignment
    namespace: crew-management
    port: 80
  - name: direct-dependents
    namespace: crew-management
    port: 80
  - name: operator-skills
    namespace: crew-management
    port: 80
  - name: operator-management
    namespace: crew-management
    port: 80

  # Module 1 Services
  - name: request
    namespace: m1-administrative-docs
    port: 80
  - name: workflow
    namespace: m1-administrative-docs
    port: 80
  - name: workflowengine
    namespace: m1-administrative-docs
    port: 80
  - name: document-generation
    namespace: m1-administrative-docs
    port: 80
  - name: document-template
    namespace: m1-administrative-docs
    port: 80
  - name: dynamic-forms
    namespace: m1-administrative-docs
    port: 80
  - name: user
    namespace: m1-administrative-docs
    port: 80

  # Module 2 Services

  # Module 3 Services
  - name: annual-calendar
    namespace: m3-headcount-control
    port: 80

  # Module 4 Services

  # Notification Services
  - name: notifications-signalr
    namespace: notification
    port: 80

  # Authentication Services
  - name: auth
    namespace: authentication
    port: 80
