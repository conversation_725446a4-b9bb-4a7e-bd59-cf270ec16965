apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Server configurations
  NODE_ENV: {{ .Values.env.NODE_ENV | quote }}
  HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}
  GLOBAL_PREFIX: {{ .Values.env.GLOBAL_PREFIX | quote }}
  CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | quote }}
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }}
  USE_PRODUCTION_URLS: {{ .Values.env.USE_PRODUCTION_URLS | quote }}

  # JWT Authentication
  JWT_SECRET: {{ .Values.env.JWT_SECRET | quote }}
  JWT_EXPIRES_IN: {{ .Values.env.JWT_EXPIRES_IN | quote }}
  JWT_REFRESH_SECRET: {{ .Values.env.JWT_REFRESH_SECRET | quote }}
  JWT_REFRESH_EXPIRES_IN: {{ .Values.env.JWT_REFRESH_EXPIRES_IN | quote }}

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}
  AZURE_TENANT_ID: {{ .Values.env.AZURE_TENANT_ID | quote }}
  AZURE_APP_ID: {{ .Values.env.AZURE_APP_ID | quote }}
  AZURE_APP_SECRET: {{ .Values.env.AZURE_APP_SECRET | quote }}
  
  # SAML Configuration
  SAML_ENTRY_POINT: {{ .Values.env.SAML_ENTRY_POINT | quote }}
  SAML_ISSUER: {{ .Values.env.SAML_ISSUER | quote }}
  SAML_CALLBACK_URL: {{ .Values.env.SAML_CALLBACK_URL | quote }}
  SAML_AUDIENCE: {{ .Values.env.SAML_AUDIENCE | quote }}
  AZURE_ENTITY_ID: {{ .Values.env.AZURE_ENTITY_ID | quote }}
  AZURE_SSO_URL: {{ .Values.env.AZURE_SSO_URL | quote }}
  AZURE_SLO_URL: {{ .Values.env.AZURE_SLO_URL | quote }}
  SAML_LOCAL_CALLBACK_URL: {{ .Values.env.SAML_LOCAL_CALLBACK_URL | quote }}
  SAML_USE_REVERSE_PROXY: {{ .Values.env.SAML_USE_REVERSE_PROXY | quote }}

  # Client Configuration
  CLIENT_URL: {{ .Values.env.CLIENT_URL | quote }}

  # Azure Cosmos DB Configuration
  COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}

  # Logging
  LOG_LEVELS: {{ .Values.env.LOG_LEVELS | quote }} 