# Default values for auth service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/auth-service
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "shared-access-sa"

podAnnotations: {}
podLabels: 
  azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3004

deployment:
  container_port: 3004

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /authentication(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /authentication(/|$)(.*)
  #        pathType: Prefix
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# livenessProbe:
#   httpGet:
#     path: /health
#     port: http
# readinessProbe:
#   httpGet:
#     path: /health
#     port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env:
  # Server configurations
  NODE_ENV: development
  HTTP_PORT: 3004
  GLOBAL_PREFIX: api/v1
  CORS_ORIGIN: "*"
  LOG_LEVEL: debug
  USE_PRODUCTION_URLS: true

  # JWT Authentication
  JWT_SECRET: development_super_secret_key_at_least_16_chars
  JWT_EXPIRES_IN: 1h
  JWT_REFRESH_SECRET: development_refresh_secret_key_at_least_16_chars
  JWT_REFRESH_EXPIRES_IN: 7d

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: true
  
  AZURE_TENANT_ID: 6b1311e5-123f-49db-acdf-8847c2d00bed
  AZURE_APP_ID: 5441ae46-a8d7-4c7c-a3f6-aec1e9d35508
  AZURE_APP_SECRET: ****************************************

  # SAML Configuration
  SAML_ENTRY_POINT: https://login.microsoftonline.com/6b1311e5-123f-49db-acdf-8847c2d00bed/saml2
  SAML_ISSUER: ADM0004210
  SAML_CALLBACK_URL: https://ingress.connected-workersdev.aptiv.com/authentication/api/v1/auth/acs
  SAML_AUDIENCE: ADM0004210
  AZURE_ENTITY_ID: ADM0004210
  AZURE_SSO_URL: https://login.microsoftonline.com/6b1311e5-123f-49db-acdf-8847c2d00bed/saml2
  AZURE_SLO_URL: https://login.microsoftonline.com/6b1311e5-123f-49db-acdf-8847c2d00bed/saml2
  
  #SAML_USE_REVERSE_PROXY: false


  # Client Configuration
  CLIENT_URL: "https://connected-workersdev.aptiv.com"

  # Azure Cosmos DB Configuration
  COSMOS_DATABASE_ID: CW_CoreDB_Authentication
  COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  COSMOS_REQUEST_TIMEOUT: 30000
  COSMOS_MAX_RETRIES: 3
  COSMOS_RETRY_INTERVAL: 1000
  COSMOS_MAX_WAIT_TIME: 60

  # Logging
  LOG_LEVELS: "error,warn,log,debug,verbose" 