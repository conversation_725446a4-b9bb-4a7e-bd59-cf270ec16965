apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "document-generation.fullname" . }}-test-connection"
  labels:
    {{- include "document-generation.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "document-generation.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
