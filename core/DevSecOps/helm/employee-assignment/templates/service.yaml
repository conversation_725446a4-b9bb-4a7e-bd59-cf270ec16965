apiVersion: v1
kind: Service
metadata:
  name: {{ include "employee-assignment.fullname" . }}
  labels:
    {{- include "employee-assignment.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "employee-assignment.selectorLabels" . | nindent 4 }} 