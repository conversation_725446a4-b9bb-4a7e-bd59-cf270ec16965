apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "employee-assignment.fullname" . }}-config
  labels:
    {{- include "employee-assignment.labels" . | nindent 4 }}
data:
  # These are example configurations that would be loaded into the container
  # We can reference directly to .Values.env variables or create custom configurations
  {{- range $key, $val := .Values.env }}
  {{ $key }}: {{ $val | quote }}
  {{- end }}
  # You can also specify individual config items if needed:
  # app-config.json: |
  #   {
  #     "port": {{ .Values.service.targetPort }},
  #     "environment": "{{ .Values.env.NODE_ENV }}"
  #   } 