apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "employee-assignment.fullname" . }}-test-connection"
  labels:
    {{- include "employee-assignment.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "employee-assignment.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never 