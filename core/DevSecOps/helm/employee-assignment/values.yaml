# Default values for employee-assignment.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/employee-assignment
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "crewmanagement-access-sa"

podAnnotations: {}
podLabels:
  azure.workload.identity/use: "true" # Required. Only pods with this label can use workload identity.

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000
service:
  type: ClusterIP
  port: 80
  targetPort: 3008

deployment:
  container_port: 3008

ingress:
  enabled: true
  className: "webapprouting.kubernetes.azure.com"
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    #kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL/2920004706684f4cb4e69954ca850510"
    #nginx.ingress.kubernetes.io/ssl-redirect: "true"
    #nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    #nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    #nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    #azure.application-routing.kubernetes.io/certificate-source: KeyVault
    #kubernetes.azure.com/use-osm-mtls: "true"
  hosts:
    #- host: ingress.connected-workersdev.aptiv.com
    - paths:
        - path: /employee-assignment(/|$)(.*)
          pathType: ImplementationSpecific
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 500m
  #   memory: 512Mi
  # requests:
  #   cpu: 100m
  #   memory: 256Mi

# Commented out due to connection issues
#livenessProbe:
#  httpGet:
#    path: /
#    port: http
#readinessProbe:
#  httpGet:
#    path: /
#    port: http

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

env:
  # Environment variables based on .env.example from employee-assignment
  NODE_ENV: "production"
  HTTP_PORT: "3008"
  AZURE_USE_MANAGED_IDENTITY: "true"
  LOG_LEVEL: "debug"
  CORS_ORIGIN: "*"

  # Azure Cosmos DB Configuration
  COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  COSMOS_DATABASE_ID: "CW_CoreDB_Crew_Management"
  COSMOS_REQUEST_TIMEOUT: "30000"
  COSMOS_MAX_RETRIES: "3"
  COSMOS_RETRY_INTERVAL: "1000"
  COSMOS_MAX_WAIT_TIME: "60"

  # Optional GRPC settings if needed
  # GRPC_HOST: "0.0.0.0"
  # GRPC_PORT: "5008"
  # Optional CORS settings

  # Azure Service Bus Configuration
  AZURE_SERVICE_BUS_NAMESPACE: "ServBus01-NonPrd-EMEA.servicebus.windows.net"
  SERVICE_BUS_TOPIC_NAME: "employee-assignments-topic"
  SERVICE_BUS_SUBSCRIPTION_NAME: "employee-assignments-sub"
