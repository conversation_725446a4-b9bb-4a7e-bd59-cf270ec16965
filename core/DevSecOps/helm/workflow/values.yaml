# Default values for workflow.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/workflowservice
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "module1-access-sa"

podAnnotations: {}
podLabels: 
  azure.workload.identity/use: "true"  # Required. Only pods with this label can use workload identity.

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3005

deployment:
  container_port: 3005

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /workflow(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /workflow(/|$)(.*)
  #        pathType: Prefix
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi


#livenessProbe:
#  httpGet:
#    path: /health
#    port: http
#  initialDelaySeconds: 30
#  periodSeconds: 10
#  timeoutSeconds: 5
#  failureThreshold: 3
#
#readinessProbe:
#  httpGet:
#    path: /health
#    port: http
#  initialDelaySeconds: 10
#  periodSeconds: 5
#  timeoutSeconds: 3
#  failureThreshold: 3

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env:
  # Server configurations
  NODE_ENV: development
  HTTP_PORT: 3005
  GRPC_HOST: 0.0.0.0
  GRPC_PORT: 5006
  LOG_LEVEL: debug
  CORS_ORIGIN: "*"
  
  # gRPC Client configurations
  WORKFLOW_ENGINE_GRPC_HOST: workflowengine
  WORKFLOW_ENGINE_GRPC_PORT: 5006
  
  # gRPC Proto configuration - no volume mounting
  GRPC_PROTO_PATH: ""  # Empty to disable file-based proto loading
  GRPC_USE_REFLECTION: "true"  # Use gRPC reflection instead of proto files
  
  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: true
  
  # Azure Service Bus
  AZURE_SERVICE_BUS_NAMESPACE: ServBus01-NonPrd-EMEA.servicebus.windows.net
  SERVICE_BUS_TOPIC_NAME: workflows
  SERVICE_BUS_SUBSCRIPTION_NAME: workflow-start
  SERVICE_BUS_ESCALATION_SUBSCRIPTION_NAME: escalation.push
  SERVICE_BUS_MAX_CONCURRENT_CALLS: 10
  SERVICE_BUS_PREFETCH_COUNT: 100
  SERVICE_BUS_MAX_RETRIES: 3
  SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_IN_MS: 300000
  
  # Cosmos DB Configuration
  COSMOS_ENDPOINT: https://cosmos01-nonprd-emea.documents.azure.com:443/
  COSMOS_DATABASE_ID: CW_CoreDB_Raise_Request
  COSMOS_REQUEST_TIMEOUT: 30000
  COSMOS_MAX_RETRIES: 3
  COSMOS_RETRY_INTERVAL: 1000
  COSMOS_MAX_WAIT_TIME: 60
  COSMOS_KEY: "placeholder-key-managed-identity-will-override" # Placeholder to pass validation, managed identity handles actual auth
  
  # Workflow Engine Configuration
  WORKFLOW_ENGINE_HOST: workflowengine-service
  WORKFLOW_ENGINE_PORT: 3001
  WORKFLOW_ENGINE_TIMEOUT: 5000
  WORKFLOW_ENGINE_RETRIES: 3