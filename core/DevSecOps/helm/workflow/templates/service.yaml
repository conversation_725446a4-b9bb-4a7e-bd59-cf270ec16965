apiVersion: v1
kind: Service
metadata:
  name: {{ include "workflow.fullname" . }}
  labels:
    {{- include "workflow.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.env.GRPC_PORT | int }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "workflow.selectorLabels" . | nindent 4 }} 