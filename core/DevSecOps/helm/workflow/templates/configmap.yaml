apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-env-configmap
data:
  # Server configurations
  NODE_ENV: {{ .Values.env.NODE_ENV | quote }}
  HTTP_PORT: {{ .Values.env.HTTP_PORT | quote }}
  GRPC_HOST: {{ .Values.env.GRPC_HOST | quote }}
  GRPC_PORT: {{ .Values.env.GRPC_PORT | quote }}
  LOG_LEVEL: {{ .Values.env.LOG_LEVEL | quote }}
  CORS_ORIGIN: {{ .Values.env.CORS_ORIGIN | quote }}
  
  # gRPC Client configurations
  WORKFLOW_ENGINE_GRPC_HOST: {{ .Values.env.WORKFLOW_ENGINE_GRPC_HOST | quote }}
  WORKFLOW_ENGINE_GRPC_PORT: {{ .Values.env.WORKFLOW_ENGINE_GRPC_PORT | quote }}
  
  # gRPC Proto configuration - inline proto to avoid volume mounting
  GRPC_PROTO_PATH: ""  # Empty to disable file-based proto loading
  GRPC_USE_REFLECTION: "true"  # Use gRPC reflection instead of proto files
  
  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}
  
  # Azure Service Bus
  AZURE_SERVICE_BUS_NAMESPACE: {{ .Values.env.AZURE_SERVICE_BUS_NAMESPACE | quote }}
  SERVICE_BUS_TOPIC_NAME: {{ .Values.env.SERVICE_BUS_TOPIC_NAME | quote }}
  SERVICE_BUS_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME | quote }}
  SERVICE_BUS_ESCALATION_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_ESCALATION_SUBSCRIPTION_NAME | quote }}
  SERVICE_BUS_MAX_CONCURRENT_CALLS: {{ .Values.env.SERVICE_BUS_MAX_CONCURRENT_CALLS | quote }}
  SERVICE_BUS_PREFETCH_COUNT: {{ .Values.env.SERVICE_BUS_PREFETCH_COUNT | quote }}
  SERVICE_BUS_MAX_RETRIES: {{ .Values.env.SERVICE_BUS_MAX_RETRIES | quote }}
  SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_IN_MS: {{ .Values.env.SERVICE_BUS_MAX_AUTO_LOCK_RENEWAL_DURATION_IN_MS | quote }}
  
  # Cosmos DB Configuration
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  COSMOS_DATABASE_ID: {{ .Values.env.COSMOS_DATABASE_ID | quote }}
  COSMOS_REQUEST_TIMEOUT: {{ .Values.env.COSMOS_REQUEST_TIMEOUT | quote }}
  COSMOS_MAX_RETRIES: {{ .Values.env.COSMOS_MAX_RETRIES | quote }}
  COSMOS_RETRY_INTERVAL: {{ .Values.env.COSMOS_RETRY_INTERVAL | quote }}
  COSMOS_MAX_WAIT_TIME: {{ .Values.env.COSMOS_MAX_WAIT_TIME | quote }}
  COSMOS_KEY: {{ .Values.env.COSMOS_KEY | quote }}
  
  # Workflow Engine Configuration
  WORKFLOW_ENGINE_HOST: {{ .Values.env.WORKFLOW_ENGINE_HOST | quote }}
  WORKFLOW_ENGINE_PORT: {{ .Values.env.WORKFLOW_ENGINE_PORT | quote }}
  WORKFLOW_ENGINE_TIMEOUT: {{ .Values.env.WORKFLOW_ENGINE_TIMEOUT | quote }}
  WORKFLOW_ENGINE_RETRIES: {{ .Values.env.WORKFLOW_ENGINE_RETRIES | quote }} 