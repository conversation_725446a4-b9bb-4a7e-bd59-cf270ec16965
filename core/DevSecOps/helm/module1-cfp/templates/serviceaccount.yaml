{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "module1-cfp.serviceAccountName" . }}
  labels:
    {{- include "module1-cfp.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  annotations:
    azure.workload.identity/client-id: {{ .Values.workloadIdentityClientId }}
{{- end }} 

