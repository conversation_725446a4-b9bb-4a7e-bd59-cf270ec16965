replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/operatorskills
  pullPolicy: IfNotPresent
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: false
  automount: true
  annotations: {}
  name: "crewmanagement-access-sa"

podAnnotations: {}
podLabels: 
  azure.workload.identity/use: "true"

podSecurityContext: {}
securityContext: {}

service:
  type: ClusterIP
  port: 80
  amqps_port: 5671
  amqp_port: 5672

deployment:
  container_port: 5006
  amqps_port: 5671
  amqp_port: 5672

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /operator-skills(/|$)(.*)
  servicePort: 80
  annotations: {}  # Service-specific annotations if needed

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  - host: ingress.connected-workersdev.aptiv.com
  #    paths:
  #      - path: /operator-skills(/|$)(.*)
  #        pathType: Prefix
  #tls:
  #  - hosts:
  #      - ingress.connected-workersdev.aptiv.com
  #    secretName: connected-workers-tls

resources: {}
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

volumes: []
volumeMounts: []
nodeSelector: {}
tolerations: []
affinity: {}

env:
  # Service Bus Configuration
  # Uncomment the non-privatelink version if you continue having issues with privatelink
  SERVICE_BUS_NAMESPACE: "ServBus01-NonPrd-EMEA.servicebus.windows.net"
  #SERVICE_BUS_NAMESPACE: "ServBus01-NonPrd-EMEA.privatelink.servicebus.windows.net"
  #SERVICE_BUS_QUEUE_NAME: "operator_skills_queue" # No queue used in this version
  SERVICE_BUS_TOPIC_NAME: "operator_skills_topic"
  SERVICE_BUS_SUBSCRIPTION_NAME: "operator_skills_subscription"
  
  # Cosmos DB Configuration
  COSMOSDB_URL: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  COSMOS_DB_DATABASE_ID: "CW_CoreDB_Crew_Management"
  COSMOS_DB_CONTAINER_ID: "operator_skills_write"
  COSMOS_DB_LEASE_CONTAINER_ID: "operator_skills_changefeed"
  
  # SSL Configuration
  SSL_VALIDATION: "false"
  
  # .NET HTTP Settings for SSL/TLS with Private Link
  DOTNET_SYSTEM_NET_HTTP_SOCKETSHTTPHANDLER_HTTP2UNENCRYPTEDSUPPORT: "0"
  DOTNET_SYSTEM_NET_HTTP_SOCKETSHTTPHANDLER_HTTP2SUPPORT: "1"
  DOTNET_SYSTEM_NET_HTTP_USESOCKETSHTTPHANDLER: "1"
  
  # Globalization Setting (overrides Dockerfile setting)
  DOTNET_SYSTEM_GLOBALIZATION_INVARIANT: "0"
  
  # SQL Database Configuration
  SQL_CONNECTION_STRING: "Server=tcp:sql01-nonprd-emea-srv.database.windows.net,1433;Initial Catalog=CWDB;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30" 