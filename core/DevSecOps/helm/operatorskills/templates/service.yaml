apiVersion: v1
kind: Service
metadata:
  name: {{ include "operatorskills.fullname" . }}
  labels:
    {{- include "operatorskills.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.amqps_port }}
      targetPort: amqps
      protocol: TCP
      name: amqps
    - port: {{ .Values.service.amqp_port }}
      targetPort: amqp
      protocol: TCP
      name: amqp
  selector:
    {{- include "operatorskills.selectorLabels" . | nindent 4 }} 