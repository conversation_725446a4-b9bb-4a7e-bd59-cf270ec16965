apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Service Bus Configuration
  SERVICE_BUS_NAMESPACE: {{ .Values.env.SERVICE_BUS_NAMESPACE | quote }}
  SERVICE_BUS_TOPIC_NAME: {{ .Values.env.SERVICE_BUS_TOPIC_NAME | quote }}
  SERVICE_BUS_SUBSCRIPTION_NAME: {{ .Values.env.SERVICE_BUS_SUBSCRIPTION_NAME | quote }}
  
  # Cosmos DB Configuration
  COSMOSDB_URL: {{ .Values.env.COSMOSDB_URL | quote }}
  COSMOS_DB_DATABASE_ID: {{ .Values.env.COSMOS_DB_DATABASE_ID | quote }}
  COSMOS_DB_CONTAINER_ID: {{ .Values.env.COSMOS_DB_CONTAINER_ID | quote }}
  COSMOS_DB_LEASE_CONTAINER_ID: {{ .Values.env.COSMOS_DB_LEASE_CONTAINER_ID | quote }}
  
  # SSL Configuration
  SSL_VALIDATION: {{ .Values.env.SSL_VALIDATION | quote }}
  
  # Globalization Setting (overrides Dockerfile setting)
  DOTNET_SYSTEM_GLOBALIZATION_INVARIANT: {{ .Values.env.DOTNET_SYSTEM_GLOBALIZATION_INVARIANT | quote }}
  
  # SQL Database Configuration
  SQL_CONNECTION_STRING: {{ .Values.env.SQL_CONNECTION_STRING | quote }} 