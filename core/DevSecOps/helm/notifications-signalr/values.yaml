replicaCount: 1

image:
  repository: acr003nonprdemearepo.azurecr.io/notificationsignalr
  pullPolicy: IfNotPresent
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: false
  automount: true
  annotations: {}
  name: "notification-access-sa"

podAnnotations: {}
podLabels: 
   azure.workload.identity/use: "true"

podSecurityContext: {}
securityContext: {}

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

deployment:
  container_port: 8080

ingress:
  enabled: false  # Disabled as we're using shared ingress
  # Service-specific ingress settings that will be used by the shared ingress
  path: /notifications-signalr(/|$)(.*)
  servicePort: 80
  annotations: {} 

  #enabled: true
  #className: "webapprouting.kubernetes.azure.com"
  #annotations: 
  #  nginx.ingress.kubernetes.io/use-regex: "true"
  #  nginx.ingress.kubernetes.io/rewrite-target: /$2
  #  #kubernetes.azure.com/tls-cert-keyvault-uri: "https://kv-01-nonprd.vault.azure.net/certificates/ConnectedWorkers-SSL-APIM/e5ad9b3fc9ab4f60b7ed9288b8de1c3f"
  #  #nginx.ingress.kubernetes.io/ssl-redirect: "true"
  #  #nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
  #  #nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  #  #nginx.ingress.kubernetes.io/ssl-passthrough: "true"
  #  #azure.application-routing.kubernetes.io/certificate-source: KeyVault
  #  #kubernetes.azure.com/use-osm-mtls: "true"
  #hosts:
  #  #- host: ingress.connected-workersdev.aptiv.com
  #  - paths:
  #      - path: /notifications-signalr(/|$)(.*)
  #        pathType: Prefix
  ##tls:
  ##  - hosts:
  ##      - ingress.connected-workersdev.aptiv.com
  ##    secretName: connected-workers-tls
  

resources: {}
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

volumes: []
volumeMounts: []
nodeSelector: {}
tolerations: []
affinity: {}

workloadIdentityClientId: "5fc92022-7adc-4aee-9942-5c653601204f"

env:

  # Azure Authentication
  AZURE_USE_MANAGED_IDENTITY: "true"

  # Service Bus Configuration
  SERVICEBUS_NAMESPACE: "ServBus01-NonPrd-EMEA.servicebus.windows.net"
  SERVICEBUS_TOPICNAME: "notification-topic"
  SERVICEBUS_SUBSCRIPTIONNAME: "notification-signalr"
  
  # Cosmos DB Configuration
  COSMOS_ENDPOINT: "https://cosmos01-nonprd-emea.documents.azure.com:443/"
  COSMOSDB_DATABASEID: "CW_CoreDB_Notification"
  COSMOSDB_CONTAINERID: "notificationCNT"
  
  # SignalR Configuration
  SIGNALR_HUB_URI: "https://signalr01-nonprd-emea.service.signalr.net"
  
  # CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,https://connected-workersdev.aptiv.com,https://ingress.connected-workersdev.aptiv.com" 

  APP_BASE_PATH: "notifications-signalr"