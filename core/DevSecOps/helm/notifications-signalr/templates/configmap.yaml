apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-configmap
data:
  # Managed Identity Configuration
  AZURE_USE_MANAGED_IDENTITY: {{ .Values.env.AZURE_USE_MANAGED_IDENTITY | quote }}

  # Service Bus Configuration
  SERVICEBUS_NAMESPACE: {{ .Values.env.SERVICEBUS_NAMESPACE | quote }}
  SERVICEBUS_TOPICNAME: {{ .Values.env.SERVICEBUS_TOPICNAME | quote }}
  SERVICEBUS_SUBSCRIPTIONNAME: {{ .Values.env.SERVICEBUS_SUBSCRIPTIONNAME | quote }}
  
  # Cosmos DB Configuration
  COSMOS_ENDPOINT: {{ .Values.env.COSMOS_ENDPOINT | quote }}
  COSMOSDB_DATABASEID: {{ .Values.env.COSMOSDB_DATABASEID | quote }}
  COSMOSDB_CONTAINERID: {{ .Values.env.COSMOSDB_CONTAINERID | quote }}
  
  # SignalR Configuration
  SIGNALR_HUB_URI: {{ .Values.env.SIGNALR_HUB_URI | quote }}
  
  # CORS Configuration
  CORS_ALLOWED_ORIGINS: {{ .Values.env.CORS_ALLOWED_ORIGINS | quote }} 

  # Base Path Configuration
  APP_BASE_PATH: {{ .Values.env.APP_BASE_PATH | quote }}