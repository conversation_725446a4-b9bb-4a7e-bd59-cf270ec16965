apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "notifications-signalr.fullname" . }}-test-connection"
  labels:
    {{- include "notifications-signalr.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "notifications-signalr.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never 