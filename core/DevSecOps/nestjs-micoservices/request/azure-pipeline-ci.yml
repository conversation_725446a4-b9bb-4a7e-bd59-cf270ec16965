name: CW-CI-Pipeline-Request-Microsevice

trigger: none
#  branches:
#    include:
#      - stagev2
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/request-service/**'

pr:  none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

variables:
  IMAGE_TAG: $(Build.BuildId)  # Build ID as tag for the Docker image

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'request'
    FOLDER_NAME: 'request-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'request'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1017 # Updated image tag from 1030 to 2030


# Publish Image Tag
- stage: PublishImageTag
  displayName: 'Publish Image Tag'
  #dependsOn: DockerBuildAndPush
  jobs:
  - job: PublishTag
    displayName: 'Publish Image Tag as Artifact'
    steps:
    # Save the image tag to a file
    - script: |
        echo $(IMAGE_TAG) > $(Build.ArtifactStagingDirectory)/imageTag.txt
      displayName: 'Save Image Tag to File'

    # Publish the image tag as an artifact
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(Build.ArtifactStagingDirectory)
        artifactName: 'ImageTag'
      displayName: 'Publish Image Tag Artifact'







