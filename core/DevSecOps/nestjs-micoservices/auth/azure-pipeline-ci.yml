name: CW-CI-Pipeline-Auth-Microservice

trigger: none
#  branches:
#    include:
#      - stage
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/auth-service/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'auth'
    FOLDER_NAME: 'auth-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'auth'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1023 # Initial image tag

# Publish Image Tag
- stage: PublishImageTag
  displayName: 'Publish Image Tag'
  jobs:
  - job: PublishTag
    displayName: 'Publish Image Tag as Artifact'
    steps:
    # Save the image tag to a file
    - script: |
        echo $(IMAGE_TAG) > $(Build.ArtifactStagingDirectory)/imageTag.txt
      displayName: 'Save Image Tag to File'

    # Publish the image tag as an artifact
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(Build.ArtifactStagingDirectory)
        artifactName: 'ImageTag'
      displayName: 'Publish Image Tag Artifact' 


      