name: CW-PR-Pipeline-Auth-Microservice

trigger: none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/nestjsMicroservice/auth-service/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'auth'
    FOLDER_NAME: 'auth-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice 

    