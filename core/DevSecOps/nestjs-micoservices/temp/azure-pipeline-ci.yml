# =========================================================================
# NestJS Microservice Continuous Integration Pipeline
# =========================================================================
# This pipeline is designed for continuous integration of NestJS microservices.
# It performs dependency installation, code quality checks, testing, security scanning,
# builds the application, and creates and pushes a Docker image to Azure Container Registry.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name, folder, and other configuration options.
# =========================================================================

parameters:
  MICROSERVICE_NAME: 'default-service'  # Name of the microservice to build and test
  FOLDER_NAME: 'default-service'        # Folder name where the microservice code resides
  NODE_VERSION: '20.x'                  # Node.js version to use for the build
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice  # Root directory of the project
  IMAGE_NAME: 'default-service'         # Name of the Docker image to build
  ACR_NAME: 'acr003nonprdemearepo'   # Azure Container Registry name
  IMAGE_TAG: 'latest'                   # Tag for the Docker image


stages:
# =========================================================================
# Stage 1: Continuous Integration Stage
# =========================================================================
# This stage handles dependency installation, code quality checks,
# testing, security scanning, and building the application artifact
# =========================================================================
- stage: C_Integration_Stage
  displayName: 'Continuous Integration Stage'
  jobs:
  - job: InstallDependencies_Test_Build
    displayName: 'Install, Test and Build'
    steps:
    # =========================================================================
    # Repository Verification
    # =========================================================================
    
    # Verify repository contents to ensure we have the latest code
    - script: |
        echo "Current directory: $(pwd)"
        echo "Repository contents:"
        ls -la
        echo "Build sources directory:"
        ls -la ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Verify Repository Contents'

    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install the required version of Node.js
    - task: NodeTool@0
      inputs:
        versionSpec: ${{ parameters.NODE_VERSION }}  # Specify the Node.js version to install
      displayName: 'Install Node.js'

    # Install pnpm package manager
    - script: |
        echo "Installing pnpm package manager..."
        npm install -g pnpm
      displayName: 'Install pnpm'

    # Install project dependencies using pnpm
    - script: |
        echo "Installing dependencies for ${{ parameters.MICROSERVICE_NAME }} microservice..."
        pnpm install --no-frozen-lockfile  # Clean install dependencies
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Install dependencies'

    # Cache node_modules to improve pipeline performance
    - task: Cache@2
      inputs:
        key: 'pnpm | "$(Agent.OS)" | ${{ parameters.BUILD_SOURCES_DIR }}/pnpm-lock.yaml'  # Cache key based on pnpm-lock.yaml
        path: ${{ parameters.BUILD_SOURCES_DIR }}/node_modules  # Directory to cache
        cacheHitVar: CACHE_RESTORED  # Variable indicating if the cache was restored
      displayName: 'Cache node_modules'

    # =========================================================================
    # Code Quality Checks
    # =========================================================================
    
    # Format the code using the defined formatting rules
    - script: |
        echo "Formatting code for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run format 
        # Optional: Check if formatting would make changes without actually changing files
        # pnpm run format:check || echo "Code formatting issues detected. Please run 'pnpm format' locally."
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Format code'

    # Perform linting to ensure code quality
    - script: |
        echo "Running linting checks for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run lint || echo "Linting failed. Please check the code and run 'pnpm run lint' locally."
        # Uncomment the line below to make the pipeline fail on lint errors
        # pnpm run lint
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Lint code'

  
    # =========================================================================
    # Testing
    # =========================================================================
    # Run unit tests for the specific microservice
    - script: |
        echo "Running unit tests for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run test:${{ parameters.MICROSERVICE_NAME }} 2>&1 | tee test_errors.txt || echo "Unit tests failed or does not exist"
        TEST_ERRORS=$(cat test_errors.txt)
        if grep -q "FAIL" test_errors.txt; then
          echo "Unit tests failed. Errors captured:"
          echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
        else
          echo "Unit tests passed successfully or did not exist."
          echo "##vso[task.setvariable variable=TestFailed;isOutput=true]false"
        fi
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: "Run unit tests"
      name: "runTests"

    # Capture test errors and create a GitHub issue if tests failed
    - task: Bash@3
      condition: eq(variables['runTests.TestFailed'], 'true')
      inputs:
        targetType: "inline"
        script: |

          # Install jq if not available
          if ! command -v jq &> /dev/null; then
            echo "Installing jq..."
            sudo apt-get update && sudo apt-get install -y jq
          fi

          # Read test errors and clean them properly
          ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/test_errors.txt" | \
            tr -d '\0' | \
            iconv -f utf-8 -t utf-8 -c | \
            sed 's/\x1b\[[0-9;]*m//g' | \
            sed 's/[[:cntrl:]]//g' | \
            tr -cd '[:print:]\n\t' | \
            sed 's/[ \t]*$//' | \
            sed '/^$/d')

          # Create the issue body with proper newlines
          BODY="## Unit Test Failure Report

          **Build Information:**
          - Microservice: ${{ parameters.MICROSERVICE_NAME }}
          - Branch: $(Build.SourceBranch)
          - Commit: $(Build.SourceVersion)

          **Test Errors:**
          \`\`\`
          ${ERRORS}
          \`\`\`

          Please investigate and fix the failing tests."

          # Use jq to properly construct JSON payload
          jq -n \
            --arg title "Unit Test Failures in ${{ parameters.MICROSERVICE_NAME }}" \
            --arg body "$BODY" \
            --argjson labels '["bug", "testing"]' \
            --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
            '{
              title: $title,
              body: $body,
              labels: $labels,
              assignees: $assignees
            }' > issue_body.json

          # Create GitHub issue
          GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer $GITHUB_TOKEN" \
            -H "Content-Type: application/json" \
            -d @issue_body.json \
            "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")

          # Check if the issue was created successfully
          if [ "$GitHub_API" -ne 201 ]; then
            echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
            echo "Response body:"
            cat response_body.json
            rm -f response_body.json issue_body.json
            exit 1
          fi

          echo "GitHub issue created successfully for test failures"
          rm -f response_body.json issue_body.json
          exit 1 
      env:
        GITHUB_TOKEN: $(GitHub_Issues_PAT)
      displayName: "Create GitHub Issue for Test Failures"


    # Generate and check test coverage reports
    - script: |
        echo "Generating test coverage for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run test:${{ parameters.MICROSERVICE_NAME }}:cov  || echo "Test coverage failed. Please fix the issues before proceeding."
        # Optional: Enforce minimum coverage thresholds
        # pnpm run test:${{ parameters.MICROSERVICE_NAME }}:cov:check
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Generate test coverage reports'

    # Optional: Publish test results to Azure DevOps
    # - task: PublishTestResults@2
    #   inputs:
    #     testResultsFiles: '**/test-results.xml' # Ensure Jest or test runner outputs test results in this format
    #     mergeTestResults: true
    #     testRunTitle: '${{ parameters.MICROSERVICE_NAME }} Tests'
    #   displayName: 'Publish Test Results'
    #   condition: succeededOrFailed() # Run even if tests fail

    # =========================================================================
    # Security Scanning
    # =========================================================================
    
    # Run security audit on dependencies
    - script: |
        echo "Running security audit for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm audit --audit-level=high --production || echo "Security audit found high severity issues. Please fix the issues before proceeding."
        # Optional: Generate a security report
        # pnpm audit --json > $(Build.ArtifactStagingDirectory)/security-audit.json
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Run security audit'

    # =========================================================================
    # Build and Publish Artifacts
    # =========================================================================
    
    # Build the application and prepare artifacts for deployment
    - script: |
        echo "Building ${{ parameters.MICROSERVICE_NAME }} microservice..."
        pnpm run build:${{ parameters.MICROSERVICE_NAME }}  # Build the project
        
        # Create directory if it doesn't exist
        mkdir -p $(Build.ArtifactStagingDirectory)
        
        # Copy built files to the staging directory
        echo "Copying build artifacts to staging directory..."
        cp -R dist/apps/${{ parameters.FOLDER_NAME }}/* $(Build.ArtifactStagingDirectory)/
        
        # Optional: Include package.json for dependency information
        cp ${{ parameters.BUILD_SOURCES_DIR }}/package.json $(Build.ArtifactStagingDirectory)/
        
        # Optional: Create a version file
        echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/version.txt
        echo "Build completed successfully for ${{ parameters.MICROSERVICE_NAME }}"
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Build and prepare artifacts'

    # Publish build artifacts for further stages or downloads
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(Build.ArtifactStagingDirectory)  # Path to the build directory
        artifactName: '${{ parameters.MICROSERVICE_NAME }}-artifact'  # Name of the artifact
        publishLocation: 'Container'  # Publish to Azure Pipelines
      displayName: 'Publish build artifacts'

  # =========================================================================
  # Docker Build and Push Job
  # =========================================================================
  # This job builds a Docker image for the microservice and pushes it to
  # Azure Container Registry (ACR)
  # =========================================================================
  - job: DockerBuildPush
    displayName: 'Build and Push Docker Image'
    dependsOn: InstallDependencies_Test_Build
    steps:
    # =========================================================================
    # Azure CLI Setup
    # =========================================================================
    
    # Install Azure CLI to interact with Azure resources
    - script: |
        echo "Installing Azure CLI..."
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash # Install Azure CLI
      displayName: 'Install Azure CLI'
    
    # Grant Docker permissions to avoid permission issues
    - script: |
        echo "Granting Docker socket permissions..."
        sudo chmod 666 /var/run/docker.sock # Grant Docker permissions
      displayName: 'Grant Docker permissions'
    
    # Validate installed tools to ensure everything is ready
    - script: |
        echo "Validating installed tools..."
        az version || echo "-----------No Azure CLI Installed-----------"
        docker -v  || echo "-----------No Docker Installed-----------"
      displayName: 'Validate Installed Tools'

    # =========================================================================
    # Azure Authentication
    # =========================================================================
    
    # Login to Azure using Managed Identity
    - script: |
        echo "Authenticating with Azure using Managed Identity..."
        az login --identity
        az account show
      displayName: 'Login to Azure'
    
    # List available Azure Container Registries
    - script: |
        echo "Listing available Azure Container Registries..."
        az acr list --subscription "ConnectedWorkers-Dev" --query "[].{name:name, resourceGroup:resourceGroup}" -o table
        az group list
      displayName: 'List ACRs'

    # =========================================================================
    # Docker Image Build
    # =========================================================================
    
    # Build Docker image for the microservice
    - script: |
        echo "##############################################################################################################"
        echo "Navigating to microservice directory..."
        cd "${{ parameters.BUILD_SOURCES_DIR }}"
        
        # Verify the Docker build cache 
        echo "Checking Docker build cache..."
        docker system df
        echo "##############################################################################################################"

        echo "Building Docker image for ${{ parameters.MICROSERVICE_NAME }}..." 
        docker build --no-cache -f apps/${{ parameters.FOLDER_NAME }}/Dockerfile -t ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }}:${{ parameters.IMAGE_TAG }} .
        
        # Verify the built image by running a simple command
        echo "Verifying built Docker image..."
        docker run --entrypoint ls "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" -l || echo "-----------Failed to run ls -l in the container ${{ parameters.MICROSERVICE_NAME }}-----------"

        echo "Listing Docker images..."
        docker images 
      displayName: 'Build Docker Image'

    # =========================================================================
    # Push to Azure Container Registry
    # =========================================================================
    
    # Login to Azure Container Registry
    - script: |
        echo "##############################################################################################################"
        echo "Logging into Azure Container Registry..."
        az acr login -n ${{ parameters.ACR_NAME }}
      displayName: 'Login to Azure Container Registry'
    
    # Push Docker image to ACR
    - script: |
        echo "Pushing Docker image to ACR..."
        # Delete existing image with the same tag if it exists
        az acr repository delete --name ${{ parameters.ACR_NAME }} --repository ${{ parameters.IMAGE_NAME }} --tag ${{ parameters.IMAGE_TAG }} --yes || echo "Image could not be deleted or does not exist."
        
        # Push the new image
        docker push "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}"
        echo "Docker image pushed successfully."
      displayName: 'Push Docker Image'

    # Verify Docker image is pushed to ACR successfully
    - script: |
        echo "Listing images in ACR..."
        az acr repository list --name ${{ parameters.ACR_NAME }} --output table
        
        echo "Listing tags for the repository ${{ parameters.IMAGE_NAME}}..."
        az acr repository show-tags --name ${{ parameters.ACR_NAME }} --repository ${{ parameters.IMAGE_NAME}} --output table
      displayName: 'Verify Docker Image Push'

    # =========================================================================
    # Cleanup
    # =========================================================================
    
    # Clean up local Docker image to free up space
    - script: |
        echo "Removing the Docker image from the runner..."
        docker images
        docker rmi -f "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" || echo "Image could not be removed."
        echo "Image removed."
      displayName: 'Clean up Docker Image'
      
    # Verify Docker image is removed and clean up build cache
    - script: |
        # Verify the Docker build cache 
        echo "Re-Checking Docker build cache..."
        docker system df
        
        # Force remove the Docker build cache
        echo "Removing Docker build cache..."
        docker system prune -a -f
        docker image prune -a -f
        
        echo "Verifying if the image is removed..."
        docker images | grep "${{ parameters.IMAGE_NAME}}" || echo "Image removed successfully."
      displayName: 'Verify Docker Image Removal - Cache Clean'

    # =========================================================================
    # Optional: Image Size Check
    # =========================================================================
    # Uncomment this section if you want to enforce image size limits
    
    # - script: |
    #     echo "Checking Docker image size..."
    #     IMAGE_SIZE=$(docker image inspect "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" --format='{{.Size}}')
    #     MAX_SIZE=$((500 * 1024 * 1024)) # 500 MB in bytes
    #     
    #     if [ $IMAGE_SIZE -gt $MAX_SIZE ]; then
    #       echo "Docker image size exceeds 500 MB. Current size: $((IMAGE_SIZE / 1024 / 1024)) MB."
    #       exit 1
    #     fi
    #     echo "Docker image size is within the limit. Current size: $((IMAGE_SIZE / 1024 / 1024)) MB."
    #   displayName: 'Check Docker Image Size'
    #   condition: succeededOrFailed() # Run even if previous steps fail

# =========================================================================
# Optional: Deployment Trigger Stage
# =========================================================================
# This stage triggers the CD pipeline after successful image push to ACR
# =========================================================================
#- stage: TriggerDeployment
#  displayName: 'Trigger Deployment Pipeline'
#  dependsOn: C_Integration_Stage
#  condition: succeeded()
#  jobs:
#  - job: TriggerCD
#    displayName: 'Trigger CD Pipeline'
#    steps:
#    - script: |
#        echo "Triggering deployment pipeline for ${{ parameters.MICROSERVICE_NAME }}..."
#        echo "Image tag: ${{ parameters.IMAGE_TAG }}"
#        echo "Image repository: ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }}"
#      displayName: 'Prepare Deployment Trigger'
#      
#    # Use the Azure DevOps REST API to trigger the CD pipeline
#    - task: AzureCLI@2
#      inputs:
#        azureSubscription: 'Azure-DevOps-Service-Connection'
#        scriptType: 'bash'
#        scriptLocation: 'inlineScript'
#        inlineScript: |
#          echo "Triggering CD pipeline via Azure DevOps API..."
#          
#          # Get Azure DevOps access token
#          TOKEN=$(az account get-access-token --resource "499b84ac-1321-427f-aa17-267ca6975798" --query "accessToken" -o tsv)
#          
#          # Define pipeline parameters
#          PARAMS='{
#            "templateParameters": {
#              "MICROSERVICE_NAME": "${{ parameters.MICROSERVICE_NAME }}",
#              "IMAGE_NAME": "${{ parameters.IMAGE_NAME }}",
#              "IMAGE_TAG": "${{ parameters.IMAGE_TAG }}",
#              "MICROSERVICE_NAMESPACE": "${{ parameters.MICROSERVICE_NAME }}-namespace"
#            }
#          }'
#          
#          # Trigger the pipeline
#          curl -X POST \
#            -H "Content-Type: application/json" \
#            -H "Authorization: Bearer $TOKEN" \
#            -d "$PARAMS" \
#            "https://dev.azure.com/$(System.TeamProject)/_apis/pipelines/$(CD_PIPELINE_ID)/runs?api-version=6.0"
#            
#          echo "CD pipeline triggered successfully."
#      displayName: 'Trigger CD Pipeline'
#      env:
#        CD_PIPELINE_ID: $(cdPipelineId)  # Define this as a variable in your pipeline







