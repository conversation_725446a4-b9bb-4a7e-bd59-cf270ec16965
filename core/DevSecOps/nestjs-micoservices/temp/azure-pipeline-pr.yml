# =========================================================================
# NestJS Microservice Pull Request Pipeline
# =========================================================================
# This pipeline is designed to run on pull requests for NestJS microservices.
# It performs dependency installation, linting, testing, security scanning,
# and builds the application to ensure code quality before merging.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name, folder, and other configuration options.
# =========================================================================

parameters:
  MICROSERVICE_NAME: 'default-service'  # Name of the microservice to build and test
  FOLDER_NAME: 'default-service'        # Folder name where the microservice code resides
  NODE_VERSION: '20.x'                  # Node.js version to use for the build
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice  # Root directory of the project

stages:

# =========================================================================
# Stage 1: Install Dependencies, Test & Build Artifact
# =========================================================================
# This stage handles dependency installation, code quality checks,
# testing, and building the application artifact
# =========================================================================
- stage: InstallDependencies_Test_BuildArtifact
  displayName: 'Install, Test and Build Stage'
  jobs:
  - job: Install
    displayName: 'Install Dependencies and Quality Checks'
    steps:
    # Setup Node.js environment
    - task: NodeTool@0
      inputs:
        versionSpec: ${{ parameters.NODE_VERSION }}
      displayName: 'Install Node.js'

    # Install pnpm package manager
    - script: npm install -g pnpm
      displayName: 'Install pnpm'

    # Install project dependencies using pnpm
    - script: |
        echo "Installing dependencies for ${{ parameters.MICROSERVICE_NAME }} microservice..."
        pnpm install --no-frozen-lockfile
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Install dependencies'

    # Cache dependencies to speed up future builds
    - task: Cache@2
      inputs:
        key: 'pnpm | "$(Agent.OS)" | ${{ parameters.BUILD_SOURCES_DIR }}/pnpm-lock.yaml'
        path: ${{ parameters.BUILD_SOURCES_DIR }}/node_modules
        cacheHitVar: CACHE_RESTORED
      displayName: 'Cache dependencies'

    # =========================================================================
    # Code Quality Checks
    # =========================================================================
    
    # Run ESLint to check code quality
    - script: |
        echo "Running linting checks for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run lint  || echo "Linting failed. Please fix the issues before proceeding."
        # Uncomment the line below to make the pipeline fail on lint errors
        # pnpm run lint
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }} 
      displayName: 'Lint code'

    # Format code using Prettier
    - script: |
        echo "Formatting code for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run format 
        # Optional: Check if formatting would make changes without actually changing files
        # pnpm run format:check || echo "Code formatting issues detected. Please run 'pnpm format' locally."
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }} 
      displayName: 'Format code'

    # =========================================================================
    # Testing
    # =========================================================================
    
    # Run unit tests for the specific microservice
    - script: |
        echo "Running unit tests for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run test:${{ parameters.MICROSERVICE_NAME }}  || echo "Unit Tests failed. Please fix the issues before proceeding."
        # Uncomment the line below to make the pipeline fail on test errors
        # pnpm run test:${{ parameters.MICROSERVICE_NAME }}
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Run unit tests'

    # Generate test coverage reports
    - script: |
        echo "Generating test coverage for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm run test:${{ parameters.MICROSERVICE_NAME }}:cov || echo "Test coverage failed. Please fix the issues before proceeding."
        # Optional: Enforce minimum coverage thresholds
        # pnpm run test:${{ parameters.MICROSERVICE_NAME }}:cov:check
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Generate test coverage'

    # Optional: Run integration tests if available
    # - script: |
    #     echo "Running integration tests for ${{ parameters.MICROSERVICE_NAME }}..."
    #     pnpm run test:integration:${{ parameters.MICROSERVICE_NAME }} || echo "Integration tests failed."
    #   workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
    #   displayName: 'Run integration tests'
    #   condition: eq(variables['runIntegrationTests'], 'true')

    # =========================================================================
    # Publish Test Results
    # =========================================================================
    
    # Publish test results to Azure DevOps
    - task: PublishTestResults@2
      inputs:
        testResultsFiles: '**/test-results.xml' # Ensure Jest or test runner outputs test results in this format
        mergeTestResults: true
        testRunTitle: '${{ parameters.MICROSERVICE_NAME }} Tests'
      displayName: 'Publish Test Results'
      condition: succeededOrFailed() # Run even if tests fail

    # Publish code coverage results
    - task: PublishCodeCoverageResults@1
      inputs:
        codeCoverageTool: Cobertura # Assuming Jest generates coverage in Cobertura format
        summaryFileLocation: '$(Build.SourcesDirectory)/apps/${{ parameters.FOLDER_NAME }}/coverage/cobertura-coverage.xml'
        reportDirectory: '$(Build.SourcesDirectory)/apps/${{ parameters.FOLDER_NAME }}/coverage/lcov-report'
        additionalCodeCoverageFiles: '$(Build.SourcesDirectory)/apps/${{ parameters.FOLDER_NAME }}/coverage/coverage-final.json'
      displayName: 'Publish Code Coverage'
      condition: succeededOrFailed() # Run even if tests fail

    # =========================================================================
    # Security Scanning
    # =========================================================================
    
    # Run security audit on dependencies
    - script: |
        echo "Running security audit for ${{ parameters.MICROSERVICE_NAME }}..."
        pnpm audit --audit-level=high --production  || echo "Security audit found high severity issues. Please fix the issues before proceeding."
        # Optional: Generate a security report
        # pnpm audit --json > $(Build.ArtifactStagingDirectory)/security-audit.json
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Run security audit'

    # Optional: Run SonarQube analysis
    # - task: SonarQubePrepare@5
    #   inputs:
    #     SonarQube: 'SonarQube'
    #     scannerMode: 'CLI'
    #     configMode: 'manual'
    #     cliProjectKey: '${{ parameters.MICROSERVICE_NAME }}'
    #     cliProjectName: '${{ parameters.MICROSERVICE_NAME }}'
    #     cliSources: '$(Build.SourcesDirectory)/apps/${{ parameters.FOLDER_NAME }}'
    #   displayName: 'Prepare SonarQube Analysis'
    #   condition: eq(variables['runSonarQube'], 'true')
    
    # Optional: Run OWASP dependency check
    # - script: |
    #     npm install -g @owasp/dependency-check
    #     dependency-check --project "${{ parameters.MICROSERVICE_NAME }}" --scan "${{ parameters.BUILD_SOURCES_DIR }}" --out "$(Build.ArtifactStagingDirectory)/owasp-report"
    #   displayName: 'OWASP Dependency Check'
    #   condition: eq(variables['runOwaspCheck'], 'true')

    # =========================================================================
    # Build and Publish Artifacts
    # =========================================================================
    
    # Build the application
    - script: |
        echo "Building ${{ parameters.MICROSERVICE_NAME }} microservice..."
        pnpm run build:${{ parameters.MICROSERVICE_NAME }}  # Build the project.
        
        # Create directory if it doesn't exist
        mkdir -p $(Build.ArtifactStagingDirectory)
        
        # Copy built files to the staging directory
        echo "Copying build artifacts to staging directory..."
        cp -R dist/apps/${{ parameters.FOLDER_NAME }}/* $(Build.ArtifactStagingDirectory)/
        
        # Optional: Include package.json for dependency information
        cp ${{ parameters.BUILD_SOURCES_DIR }}/package.json $(Build.ArtifactStagingDirectory)/
        
        # Optional: Create a version file
        echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/version.txt
        echo "Build completed successfully for ${{ parameters.MICROSERVICE_NAME }}"
      workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
      displayName: 'Build and prepare artifacts'

    # Publish build artifacts
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(Build.ArtifactStagingDirectory)  # Path to the build directory
        artifactName: '${{ parameters.MICROSERVICE_NAME }}-artifact'  # Name of the artifact
        publishLocation: 'Container'  # Publish to Azure Pipelines
      displayName: 'Publish build artifacts'





