# =========================================================================
# NestJS Microservice Continuous Deployment Pipeline
# =========================================================================
# This pipeline is designed for continuous deployment of NestJS microservices to Kubernetes.
# It deploys the Docker image from Azure Container Registry to an AKS cluster,
# configures ingress, and verifies the deployment.
#
# The pipeline is parameterized to be reusable across different microservices
# by specifying the microservice name, namespace, and other configuration options.
# =========================================================================

parameters:
  MICROSERVICE_NAME: 'default-service'         # Name of the microservice to deploy
  MICROSERVICE_NAMESPACE: 'troubleshootingnamespace'  # Kubernetes namespace for deployment
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice  # Root directory of the project
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm  # Directory containing Helm charts
  IMAGE_NAME: 'default-service'                # Name of the Docker image to deploy
  IMAGE_TAG: 'latest'                          # Tag of the Docker image to deploy
  ACR_NAME: 'acr003nonprdemearepo'             # Azure Container Registry name
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'            # AKS cluster name
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS' # Resource group containing the AKS cluster

stages:
# =========================================================================
# Stage 1: Deploy to AKS (Dev Environment)
# =========================================================================
# This stage deploys the Docker image to the Dev environment in AKS
# and verifies the deployment
# =========================================================================
- stage: DeployToDev
  displayName: 'Deploy to Dev Environment'
  jobs:
  - job: DeployToDev
    displayName: 'Deploy Application to Dev'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install required tools: Azure CLI and Helm
    - script: |
        echo "Installing Azure CLI..."
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
        
        echo "Installing Helm 3..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
      displayName: 'Install Azure CLI and Helm'
    
    # Check kubectl version to ensure compatibility
    - script: |
        echo "Checking kubectl version..."
        kubectl version --client
      displayName: 'Check kubectl version'

    # =========================================================================
    # Azure Authentication and Configuration
    # =========================================================================
    
    # Login to Azure and configure access to AKS
    - script: |
        echo "Logging in to Azure using managed identity..."
        az login --identity
      
        echo "Logging in to Azure Container Registry..."
        az acr login -n ${{ parameters.ACR_NAME }} || echo "Failed to login to ACR. Will continue anyway as this might not be critical."
        
        echo "Setting up kubeconfig for AKS cluster..."
        az aks get-credentials --resource-group ${{ parameters.AKS_RESOURCE_GROUP }} --name ${{ parameters.AKS_CLUSTER }} --overwrite-existing || echo "Failed to set up kubeconfig. This is critical - please check permissions and cluster availability."
        
        echo "Verifying AKS connection..."
        kubectl cluster-info || echo "Failed to connect to AKS cluster. This is critical - please check network connectivity and credentials."
        
        echo "kubeconfig setup completed."
      displayName: 'Authenticate and Configure AKS Access'
    
    # =========================================================================
    # Optional: Ingress Controller Setup
    # =========================================================================
    
    # Create the ingress-nginx namespace if it doesn't exist
    #- script: |
    #    echo "Checking if ingress-nginx namespace exists..."
    #    if kubectl get namespace ingress-nginx >/dev/null 2>&1; then
    #        echo "Namespace 'ingress-nginx' already exists."
    #    else
    #        echo "Creating ingress-nginx namespace..."
    #        kubectl create namespace ingress-nginx
    #        echo "Namespace 'ingress-nginx' created successfully."
    #    fi
    #  displayName: 'Create ingress-nginx namespace'

    # Install or upgrade NGINX Ingress Controller using Helm
    #- script: |
    #    echo "Adding ingress-nginx Helm repository..."
    #    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    #    
    #    echo "Updating Helm repositories..."
    #    helm repo update
    #    
    #    echo "Installing or upgrading NGINX Ingress Controller..."
    #    helm upgrade --install nginx-ingress ingress-nginx/ingress-nginx \
    #      --namespace ingress-nginx \
    #      --values ${{ parameters.HELM_CHART_DIR }}/ingress-nginx-values.yaml || echo "Ingress Controller is already installed or already in latest version."
    #    
    #    echo "Waiting for NGINX Ingress Controller to be ready..."
    #    kubectl wait --namespace ingress-nginx \
    #      --for=condition=ready pod \
    #      --selector=app.kubernetes.io/component=controller \
    #      --timeout=120s || echo "Timed out waiting for Ingress Controller pods. Will continue anyway."
    #  displayName: 'Install or upgrade the NGINX Ingress Controller'

    # =========================================================================
    # Microservice Namespace Setup
    # =========================================================================
    
    # Create the namespace for the microservice if it doesn't exist
    - script: |
        echo "Creating namespace for ${{ parameters.MICROSERVICE_NAME }} if it doesn't exist..."
        kubectl create namespace ${{ parameters.MICROSERVICE_NAMESPACE }} || echo "Namespace already exists."
        
        echo "Labeling namespace for network policy..."
        kubectl label namespace ${{ parameters.MICROSERVICE_NAMESPACE }} name=${{ parameters.MICROSERVICE_NAMESPACE }} --overwrite
      displayName: 'Create Namespace for the microservice'
            
    # Apply shared service account for the microservice
    - script: |
        echo "Applying shared service account configuration..."
        kubectl apply -f ${{ parameters.HELM_CHART_DIR }}/sharedserviceaccount.yaml || echo "Failed to apply sharedserviceaccount.yaml. Will continue anyway."
      displayName: 'Apply shared service account'
       
    # =========================================================================
    # Application Deployment
    # =========================================================================
    
    # Deploy the application to AKS using Helm
    - script: |
        echo "Deploying ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."
        
        # Debug information to verify paths
        echo "Current directory: $(pwd)"
        echo "Checking if Helm chart directory exists:"
        ls -la .

        helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }},image.tag=${{ parameters.IMAGE_TAG }}
        
        #echo "Deploying ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."
        #helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . \
        #  --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} \
        #  --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }} \
        #  --set image.tag=${{ parameters.IMAGE_TAG }} \
        #  --set fullnameOverride=${{ parameters.MICROSERVICE_NAME }} \
        #  --set nameOverride=${{ parameters.MICROSERVICE_NAME }} \
        #  --set serviceAccount.create=false \
        #  --set serviceAccount.name=shared-service-account \
        #  --debug
        #
        #echo "Deployment initiated. Waiting for rollout to complete..."
        #kubectl rollout status deployment/${{ parameters.MICROSERVICE_NAME }} -n ${{ parameters.MICROSERVICE_NAMESPACE }} --timeout=180s || echo "Deployment rollout timed out or failed. Check deployment status for details."
      workingDirectory: ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
      displayName: 'Deploy to AKS Dev Environment'

    # =========================================================================
    # Deployment Verification
    # =========================================================================
    
    # List Helm repositories for verification
    - script: |
        echo "Listing Helm repositories..."
        helm repo list || echo "Failed to list Helm repositories."
      displayName: 'List Helm Repositories'

    # List Helm releases to verify deployment
    - script: |
        echo "Listing Helm releases in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        helm list -n ${{ parameters.MICROSERVICE_NAMESPACE }} || echo "Failed to list Helm releases."
      displayName: 'List Helm Releases'

    # Clean up Helm repository if needed
    - script: |
        echo "Removing Helm repository for ${{ parameters.MICROSERVICE_NAME }} if it exists..."
        helm repo remove ${{ parameters.MICROSERVICE_NAME }} || echo "Helm repository doesn't exist or couldn't be removed."
      displayName: 'Remove Helm Repository'

    # =========================================================================
    # Deployment Status Checks
    # =========================================================================
    
    # Get deployments to verify status
    - script: |
        echo "Getting deployments in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get deployments --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
      displayName: 'Get Deployments'
    

    # Describe the deployment for detailed information
    - script: |
        echo "Describing deployment for ${{ parameters.MICROSERVICE_NAME }}..."
        kubectl describe deployment ${{ parameters.MICROSERVICE_NAME }} --namespace ${{ parameters.MICROSERVICE_NAMESPACE }}
      displayName: 'Describe Deployment'

    # Get pods in the microservice namespace
    - script: |
        echo "Getting pods in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide --show-labels
      displayName: 'Get Pods in the current namespace'

    # Describe the pods for detailed information
    - script: |
        echo "Describing pods for ${{ parameters.MICROSERVICE_NAME }}..."
        kubectl describe pod -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }}
      displayName: 'Describe the Pod'

    # =========================================================================
    # Log Collection and Analysis
    # =========================================================================
    
    # Get logs from the microservice pods
    - script: |
        echo "Getting logs from ${{ parameters.MICROSERVICE_NAME }} pods..."
        kubectl logs -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} || echo "Containers are not running or no logs available."
      displayName: 'Get Logs'
     
    # Check individual pod logs
    - script: |
        echo "Checking individual pod logs..."
        for pod in $(kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o name); do
          echo "==================== Logs for $pod ===================="
          kubectl logs -n ${{ parameters.MICROSERVICE_NAMESPACE }} $pod --tail=50 || echo "Failed to get logs for $pod"
          echo "======================================================"
        done
      displayName: 'Check Pod Logs'

    # Get events to troubleshoot any issues
    - script: |
        echo "Getting events in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get events --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --sort-by='.lastTimestamp' | grep ${{ parameters.MICROSERVICE_NAME }} || echo "No events found for ${{ parameters.MICROSERVICE_NAME }}"
      displayName: 'Get Events'

    # =========================================================================
    # Resource Status Checks
    # =========================================================================
    
    # Get ConfigMaps
    - script: |
        echo "Getting ConfigMaps in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get cm --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
      displayName: 'Get ConfigMaps'

    # Get Services
    - script: |
        echo "Getting Services in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get svc --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
      displayName: 'Get Services'

    # Get Ingresses
    - script: |
        echo "Getting Ingresses in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get ing --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
      displayName: 'Get Ingresses'

    # Get Pods across all namespaces (filtered for ingress)
    - script: |
        echo "Getting Pods across all namespaces (filtered for ingress)..."
        kubectl get pods --all-namespaces --output wide | grep ingress || echo "No ingress pods found."
      displayName: 'Get Ingress Pods'

    # Get Service Accounts
    - script: |
        echo "Getting Service Accounts in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get sa --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
      displayName: 'Get Service Accounts'

    # Get Horizontal Pod Autoscalers
    - script: |
        echo "Getting HPAs in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get hpa --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No HPAs found."
      displayName: 'Get HPAs'

    # Get ReplicaSets
    - script: |
        echo "Getting ReplicaSets in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
        kubectl get rs --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
      displayName: 'Get ReplicaSets'

    # =========================================================================
    # Deployment Validation
    # =========================================================================
    
    # Validate the application is accessible (if it has an endpoint)
    #- script: |
    #    echo "Checking if application has an ingress..."
    #    INGRESS_HOST=$(kubectl get ing -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o jsonpath='{.items[0].spec.rules[0].host}' 2>/dev/null)
    #    
    #    if [ -n "$INGRESS_HOST" ]; then
    #      echo "Application is exposed via ingress host: $INGRESS_HOST"
    #      echo "Attempting to validate application health..."
    #      # You can add curl commands here to check application health endpoints
    #      # curl -k https://$INGRESS_HOST/health || echo "Health check failed, but deployment will continue."
    #    else
    #      echo "No ingress found for the application. Skipping validation."
    #    fi
    #  displayName: 'Validate Application Accessibility'
    #  continueOnError: true

# =========================================================================
# Optional: Post-Deployment Notification Stage
# =========================================================================
# Uncomment this section if you want to send notifications after deployment
#
# - stage: Notify
#   displayName: 'Send Deployment Notifications'
#   dependsOn: DeployToDev
#   condition: succeededOrFailed()
#   jobs:
#   - job: SendNotifications
#     displayName: 'Send Notifications'
#     steps:
#     - script: |
#         echo "Sending deployment notification for ${{ parameters.MICROSERVICE_NAME }}..."
#         # Add your notification logic here (email, Teams, Slack, etc.)
#       displayName: 'Send Deployment Notification'






