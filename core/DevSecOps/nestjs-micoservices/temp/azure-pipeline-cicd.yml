# =========================================================================
# NestJS Microservice Combined CI/CD Pipeline Template
# =========================================================================
# This template combines both CI and CD processes into a single reusable pipeline.
# It performs the full delivery lifecycle:
# - Build, test, and package the application (CI)
# - Deploy the application to Kubernetes (CD)
#
# The template is parameterized to be reusable across different microservices
# by specifying the microservice name, folder, and other configuration options.
# =========================================================================

parameters:
  # Microservice specific parameters
  MICROSERVICE_NAME: "default-service" # Name of the microservice
  FOLDER_NAME: "default-service" # Folder name where the microservice code resides
  MicroService_Owner: "allaoui70" # GitHub username of the microservice owner for issue assignment

  # Environment and configuration parameters
  NODE_VERSION: "20.x" # Node.js version to use
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice # Root directory of the project
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm # Directory containing Helm charts
  SERVICE_ACCOUNT_PATH: ""

  # Image parameters
  IMAGE_NAME: "default-service" # Name of the Docker image
  ACR_NAME: "acr003nonprdemearepo" # Azure Container Registry name
  IMAGE_TAG: "latest" # Tag for the Docker image

  # Deployment parameters
  AKS_CLUSTER: "aks003-NonPrd-EMEA" # AKS cluster name
  AKS_RESOURCE_GROUP: "RG-Dev-CONNWORKERS-AKS" # Resource group containing the AKS cluster
  MICROSERVICE_NAMESPACE: "troubleshootingnamespace" # Kubernetes namespace for deployment

stages:
  # =========================================================================
  # Stage 1: Continuous Integration Stage
  # =========================================================================
  - stage: C_Integration_Stage
    displayName: "Continuous Integration Stage"
    jobs:
      - job: InstallDependencies_Test_Build
        displayName: "Install, Test and Build"
        variables:
          - group: GitHub-Secrets
        steps:
          # =========================================================================
          # Repository Verification
          # =========================================================================

          # Verify repository contents to ensure we have the latest code
          - script: |
              echo "Current directory: $(pwd)"
              echo "Repository contents:"
              ls -la
              echo "Build sources directory:"
              ls -la ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Verify Repository Contents"

          # =========================================================================
          # Environment Setup
          # =========================================================================

          # Install the required version of Node.js
          - task: NodeTool@0
            inputs:
              versionSpec: ${{ parameters.NODE_VERSION }} # Specify the Node.js version to install
            displayName: "Install Node.js"

          # Install pnpm package manager
          - script: |
              echo "Installing pnpm package manager..."
              npm install -g pnpm
            displayName: "Install pnpm"

          # Install project dependencies using pnpm
          - script: |
              echo "Installing dependencies for ${{ parameters.MICROSERVICE_NAME }} microservice..."
              pnpm install --no-frozen-lockfile  # Clean install dependencies
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Install dependencies"

          # Cache node_modules to improve pipeline performance
          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | ${{ parameters.BUILD_SOURCES_DIR }}/pnpm-lock.yaml' # Cache key based on pnpm-lock.yaml
              path: ${{ parameters.BUILD_SOURCES_DIR }}/node_modules # Directory to cache
              cacheHitVar: CACHE_RESTORED # Variable indicating if the cache was restored
            displayName: "Cache node_modules"

          # =========================================================================
          # Code Quality Checks
          # =========================================================================

          # Format the code using the defined formatting rules
          - script: |
              echo "Formatting code for ${{ parameters.MICROSERVICE_NAME }}..."
              pnpm run format || echo "Code formatting issues detected. Please run 'pnpm format' locally."
              # Optional: Check if formatting would make changes without actually changing files
              # pnpm run format:check || echo "Code formatting issues detected. Please run 'pnpm format' locally."
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Format code"

          # Perform linting to ensure code quality
          - script: |
              echo "Running linting checks for ${{ parameters.MICROSERVICE_NAME }}..."
              pnpm run lint 2>&1 | tee lint_errors.txt || echo "Linting failed. Please check the code and run 'pnpm run lint' locally."
              LINT_ERRORS=$(cat lint_errors.txt)
              if grep -q -E "(error|Error|ERROR)" lint_errors.txt; then
                echo "Linting failed. Errors captured:"
                echo "##vso[task.setvariable variable=LintFailed;isOutput=true]true"
              else
                echo "Linting passed successfully."
                echo "##vso[task.setvariable variable=LintFailed;isOutput=true]false"
              fi
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Lint code"
            name: "runLint"

          # Capture linting errors and create a GitHub issue if linting failed
          #    - task: Bash@3
          #      condition: eq(variables['runLint.LintFailed'], 'true')
          #      inputs:
          #        targetType: "inline"
          #        script: |
          #
          #          # Install jq if not available
          #          if ! command -v jq &> /dev/null; then
          #            echo "Installing jq..."
          #            sudo apt-get update && sudo apt-get install -y jq
          #          fi
          #
          #          # Read lint errors and clean them properly
          #          ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/lint_errors.txt" | \
          #            tr -d '\0' | \
          #            iconv -f utf-8 -t utf-8 -c | \
          #            sed 's/\x1b\[[0-9;]*m//g' | \
          #            sed 's/[[:cntrl:]]//g' | \
          #            tr -cd '[:print:]\n\t' | \
          #            sed 's/[ \t]*$//' | \
          #            sed '/^$/d')
          #
          #          # Create the issue body with proper newlines
          #          BODY="## Linting Failure Report
          #
          #          **Build Information:**
          #          - Microservice: ${{ parameters.MICROSERVICE_NAME }}
          #          - Branch: $(Build.SourceBranch)
          #          - Commit: $(Build.SourceVersion)
          #
          #          **Linting Errors:**
          #          \`\`\`
          #          ${ERRORS}
          #          \`\`\`
          #
          #          Please investigate and fix the linting issues."
          #
          #          # Use jq to properly construct JSON payload
          #          jq -n \
          #            --arg title "Linting Failures in ${{ parameters.MICROSERVICE_NAME }}" \
          #            --arg body "$BODY" \
          #            --argjson labels '["code-quality", "linting"]' \
          #            --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
          #            '{
          #              title: $title,
          #              body: $body,
          #              labels: $labels,
          #              assignees: $assignees
          #            }' > issue_body.json
          #
          #          # Create GitHub issue
          #          GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
          #            -H "Accept: application/vnd.github+json" \
          #            -H "Authorization: Bearer $GITHUB_TOKEN" \
          #            -H "Content-Type: application/json" \
          #            -d @issue_body.json \
          #            "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
          #
          #          # Check if the issue was created successfully
          #          if [ "$GitHub_API" -ne 201 ]; then
          #            echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
          #            echo "Response body:"
          #            cat response_body.json
          #            rm -f response_body.json issue_body.json
          #            exit 1
          #          fi
          #
          #          echo "GitHub issue created successfully for linting failures"
          #          rm -f response_body.json issue_body.json
          #          exit 1
          #        env:
          #        GITHUB_TOKEN: $(GitHub_Issues_PAT)
          #      displayName: "Create GitHub Issue for Linting Failures"

          # =========================================================================
          # Testing
          # =========================================================================
          # Run unit tests for the specific microservice
          - script: |
              echo "Running unit tests for ${{ parameters.MICROSERVICE_NAME }}..."
              pnpm run test:${{ parameters.MICROSERVICE_NAME }} 2>&1 | tee test_errors.txt || echo "Unit tests failed or does not exist"
              TEST_ERRORS=$(cat test_errors.txt)
              if grep -q "FAIL" test_errors.txt; then
                echo "Unit tests failed. Errors captured:"
                echo "##vso[task.setvariable variable=TestFailed;isOutput=true]true"
              else
                echo "Unit tests passed successfully or did not exist."
                echo "##vso[task.setvariable variable=TestFailed;isOutput=true]false"
              fi
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Run unit tests"
            name: "runTests"

          # Capture test errors and create a GitHub issue if tests failed
#          - task: Bash@3
#            condition: eq(variables['runTests.TestFailed'], 'true')
#            inputs:
#              targetType: "inline"
#              script: |
#
#                # Install jq if not available
#                if ! command -v jq &> /dev/null; then
#                  echo "Installing jq..."
#                  sudo apt-get update && sudo apt-get install -y jq
#                fi
#
#                # Read test errors and clean them properly
#                ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/test_errors.txt" | \
#                  tr -d '\0' | \
#                  iconv -f utf-8 -t utf-8 -c | \
#                  sed 's/\x1b\[[0-9;]*m//g' | \
#                  sed 's/[[:cntrl:]]//g' | \
#                  tr -cd '[:print:]\n\t' | \
#                  sed 's/[ \t]*$//' | \
#                  sed '/^$/d')
#
#                # Create the issue body with proper newlines
#                BODY="## Unit Test Failure Report
#
#                **Build Information:**
#                - Microservice: ${{ parameters.MICROSERVICE_NAME }}
#                - Branch: $(Build.SourceBranch)
#                - Commit: $(Build.SourceVersion)
#
#                **Test Errors:**
#                \`\`\`
#                ${ERRORS}
#                \`\`\`
#
#                Please investigate and fix the failing tests."
#
#                # Use jq to properly construct JSON payload
#                jq -n \
#                  --arg title "Unit Test Failures in ${{ parameters.MICROSERVICE_NAME }}" \
#                  --arg body "$BODY" \
#                  --argjson labels '["bug", "testing"]' \
#                  --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
#                  '{
#                    title: $title,
#                    body: $body,
#                    labels: $labels,
#                    assignees: $assignees
#                  }' > issue_body.json
#
#                # Create GitHub issue
#                GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
#                  -H "Accept: application/vnd.github+json" \
#                  -H "Authorization: Bearer $GITHUB_TOKEN" \
#                  -H "Content-Type: application/json" \
#                  -d @issue_body.json \
#                  "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
#
#                # Check if the issue was created successfully
#                if [ "$GitHub_API" -ne 201 ]; then
#                  echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
#                  echo "Response body:"
#                  cat response_body.json
#                  rm -f response_body.json issue_body.json
#                  exit 1
#                fi
#
#                echo "GitHub issue created successfully for test failures"
#                rm -f response_body.json issue_body.json
#                exit 1
#            env:
#              GITHUB_TOKEN: $(GitHub_Issues_PAT)
#            displayName: "Create GitHub Issue for Test Failures"

          # =========================================================================
          # Security Scanning
          # =========================================================================

          # Run security audit on dependencies
          - script: |
              echo "Running security audit for ${{ parameters.MICROSERVICE_NAME }}..."
              pnpm audit --audit-level=high --production 2>&1 | tee security_audit_errors.txt || echo "Security audit found high severity issues. Please fix the issues before proceeding."
              AUDIT_ERRORS=$(cat security_audit_errors.txt)
              if grep -q -E "(high|critical)" security_audit_errors.txt; then
                echo "Security audit failed. High/Critical vulnerabilities found:"
                echo "##vso[task.setvariable variable=SecurityFailed;isOutput=true]true"
              else
                echo "Security audit passed successfully."
                echo "##vso[task.setvariable variable=SecurityFailed;isOutput=true]false"
              fi
              # Optional: Generate a security report
              pnpm audit --json > $(Build.ArtifactStagingDirectory)/security-audit.json || echo "Failed to generate security audit JSON report"
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Run security audit"
            name: "runSecurityAudit"

          # Capture security audit errors and create a GitHub issue if audit failed
          #    - task: Bash@3
          #      condition: eq(variables['runSecurityAudit.SecurityFailed'], 'true')
          #      inputs:
          #        targetType: "inline"
          #        script: |
          #
          #          # Install jq if not available
          #          if ! command -v jq &> /dev/null; then
          #            echo "Installing jq..."
          #            sudo apt-get update && sudo apt-get install -y jq
          #          fi
          #
          #          # Read security audit errors and clean them properly
          #          ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/security_audit_errors.txt" | \
          #            tr -d '\0' | \
          #            iconv -f utf-8 -t utf-8 -c | \
          #            sed 's/\x1b\[[0-9;]*m//g' | \
          #            sed 's/[[:cntrl:]]//g' | \
          #            tr -cd '[:print:]\n\t' | \
          #            sed 's/[ \t]*$//' | \
          #            sed '/^$/d')
          #
          #          # Create the issue body with proper newlines
          #          BODY="## Security Vulnerability Report
          #
          #          **Build Information:**
          #          - Microservice: ${{ parameters.MICROSERVICE_NAME }}
          #          - Branch: $(Build.SourceBranch)
          #          - Commit: $(Build.SourceVersion)
          #
          #          **Security Issues:**
          #          \`\`\`
          #          ${ERRORS}
          #          \`\`\`
          #
          #          **Action Required:**
          #          High or critical severity vulnerabilities have been detected in the dependencies. Please review and update the affected packages to secure versions."
          #
          #          # Use jq to properly construct JSON payload
          #          jq -n \
          #            --arg title "Security Vulnerabilities in ${{ parameters.MICROSERVICE_NAME }}" \
          #            --arg body "$BODY" \
          #            --argjson labels '["security", "vulnerability", "high-priority"]' \
          #            --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
          #            '{
          #              title: $title,
          #              body: $body,
          #              labels: $labels,
          #              assignees: $assignees
          #            }' > issue_body.json
          #
          #          # Create GitHub issue
          #          GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
          #            -H "Accept: application/vnd.github+json" \
          #            -H "Authorization: Bearer $GITHUB_TOKEN" \
          #            -H "Content-Type: application/json" \
          #            -d @issue_body.json \
          #            "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")
          #
          #          # Check if the issue was created successfully
          #          if [ "$GitHub_API" -ne 201 ]; then
          #            echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
          #            echo "Response body:"
          #            cat response_body.json
          #            rm -f response_body.json issue_body.json
          #            exit 1
          #          fi
          #
          #          echo "GitHub issue created successfully for security vulnerabilities"
          #          rm -f response_body.json issue_body.json
          #          exit 1
          #      env:
          #        GITHUB_TOKEN: $(GitHub_Issues_PAT)
          #      displayName: "Create GitHub Issue for Security Vulnerabilities"

          # =========================================================================
          # Build and Publish Artifacts
          # =========================================================================

          # Build the application and prepare artifacts for deployment
          - script: |
              echo "Building ${{ parameters.MICROSERVICE_NAME }} microservice..."
              pnpm run build:${{ parameters.MICROSERVICE_NAME }} 2>&1 | tee build_errors.txt || echo "Build failed for ${{ parameters.MICROSERVICE_NAME }}"
              BUILD_ERRORS=$(cat build_errors.txt)
              if grep -q -E "(error|Error|ERROR|failed|Failed|FAILED)" build_errors.txt; then
                echo "Build failed. Errors captured:"
                echo "##vso[task.setvariable variable=BuildFailed;isOutput=true]true"
              else
                echo "Build completed successfully."
                echo "##vso[task.setvariable variable=BuildFailed;isOutput=true]false"
                
                # Create directory if it doesn't exist
                mkdir -p $(Build.ArtifactStagingDirectory)
                
                # Copy built files to the staging directory
                echo "Copying build artifacts to staging directory..."
                cp -R dist/apps/${{ parameters.FOLDER_NAME }}/* $(Build.ArtifactStagingDirectory)/
                
                # Optional: Include package.json for dependency information
                cp ${{ parameters.BUILD_SOURCES_DIR }}/package.json $(Build.ArtifactStagingDirectory)/
                
                # Optional: Create a version file
                echo "$(Build.BuildNumber)" > $(Build.ArtifactStagingDirectory)/version.txt
                echo "Build completed successfully for ${{ parameters.MICROSERVICE_NAME }}"
              fi
            workingDirectory: ${{ parameters.BUILD_SOURCES_DIR }}
            displayName: "Build and prepare artifacts"
            name: "runBuild"

          # Capture build errors and create a GitHub issue if build failed
          - task: Bash@3
            condition: eq(variables['runBuild.BuildFailed'], 'true')
            inputs:
              targetType: "inline"
              script: |

                # Install jq if not available
                if ! command -v jq &> /dev/null; then
                  echo "Installing jq..."
                  sudo apt-get update && sudo apt-get install -y jq
                fi

                # Read build errors and clean them properly
                ERRORS=$(head -c 8000 "${{ parameters.BUILD_SOURCES_DIR }}/build_errors.txt" | \
                  tr -d '\0' | \
                  iconv -f utf-8 -t utf-8 -c | \
                  sed 's/\x1b\[[0-9;]*m//g' | \
                  sed 's/[[:cntrl:]]//g' | \
                  tr -cd '[:print:]\n\t' | \
                  sed 's/[ \t]*$//' | \
                  sed '/^$/d')

                # Create the issue body with proper newlines
                BODY="## Build Failure Report

                **Build Information:**
                - Microservice: ${{ parameters.MICROSERVICE_NAME }}
                - Branch: $(Build.SourceBranch)
                - Commit: $(Build.SourceVersion)

                **Build Errors:**
                \`\`\`
                ${ERRORS}
                \`\`\`

                Please investigate and fix the build issues."

                # Use jq to properly construct JSON payload
                jq -n \
                  --arg title "Build Failures in ${{ parameters.MICROSERVICE_NAME }}" \
                  --arg body "$BODY" \
                  --argjson labels '["bug", "build"]' \
                  --argjson assignees '["${{ parameters.MicroService_Owner }}"]' \
                  '{
                    title: $title,
                    body: $body,
                    labels: $labels,
                    assignees: $assignees
                  }' > issue_body.json

                # Create GitHub issue
                GitHub_API=$(curl -s -w "%{http_code}" -o response_body.json -X POST \
                  -H "Accept: application/vnd.github+json" \
                  -H "Authorization: Bearer $GITHUB_TOKEN" \
                  -H "Content-Type: application/json" \
                  -d @issue_body.json \
                  "https://api.github.com/repos/ABA-Connected-Talent/Connected-Workrers/issues")

                # Check if the issue was created successfully
                if [ "$GitHub_API" -ne 201 ]; then
                  echo "Failed to create GitHub issue. HTTP Code: $GitHub_API"
                  echo "Response body:"
                  cat response_body.json
                  rm -f response_body.json issue_body.json
                  exit 1
                fi

                echo "GitHub issue created successfully for build failures"
                rm -f response_body.json issue_body.json
                exit 1
            env:
              GITHUB_TOKEN: $(GitHub_Issues_PAT)
            displayName: "Create GitHub Issue for Build Failures"

          # Publish build artifacts for further stages or downloads
          - task: PublishBuildArtifacts@1
            condition: eq(variables['runBuild.BuildFailed'], 'false')
            inputs:
              pathToPublish: $(Build.ArtifactStagingDirectory) # Path to the build directory
              artifactName: "${{ parameters.MICROSERVICE_NAME }}-artifact" # Name of the artifact
              publishLocation: "Container" # Publish to Azure Pipelines
            displayName: "Publish build artifacts"

      # =========================================================================
      # Docker Build and Push Job
      # =========================================================================
      - job: DockerBuildPush
        displayName: "Build and Push Docker Image"
        dependsOn: InstallDependencies_Test_Build
        steps:
          # Install Docker to build the Docker image
          - task: Bash@3
            displayName: "Install and Configure Docker"
            retryCountOnTaskFailure: 3
            inputs:
              targetType: "inline"
              script: |
                echo "Installing Docker..."
                # Remove old versions if they exist
                timeout 10m bash -c "
                  sudo apt-get remove docker docker-engine docker.io containerd runc || true

                  sudo apt-get update
                  sudo apt-get install -y \
                      apt-transport-https \
                      ca-certificates \
                      curl \
                      gnupg \
                      lsb-release

                  curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

                  echo \
                    \"deb [arch=\$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
                    \$(lsb_release -cs) stable\" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

                  sudo apt-get update
                  sudo apt-get install -y docker-ce docker-ce-cli containerd.io

                  sudo systemctl stop docker || true
                  sudo mkdir -p /etc/docker
                  sudo mkdir -p /etc/systemd/system/docker.service.d

                  echo '{
                    \"exec-opts\": [\"native.cgroupdriver=systemd\"],
                    \"log-driver\": \"json-file\",
                    \"log-opts\": {
                      \"max-size\": \"100m\"
                    },
                    \"storage-driver\": \"overlay2\"
                  }' | sudo tee /etc/docker/daemon.json

                  sudo systemctl daemon-reload
                  sudo systemctl start docker
                  sudo systemctl enable docker

                  echo \"Waiting for Docker daemon to start...\"
                  sleep 20

                  sudo chmod 666 /var/run/docker.sock
                  sudo usermod -aG docker $USER

                  echo \"Verifying Docker setup...\"
                  docker version
                  docker info

                  echo \"Testing Docker with hello-world...\"
                  docker run hello-world
                " || { echo "Docker installation failed. This task will retry 3 times."; exit 1; }

          # Additional Docker verification step
          - script: |
              echo "Performing extended Docker verification..."
              # Check Docker socket permissions
              ls -l /var/run/docker.sock
              # Check Docker service status
              sudo systemctl status docker
              # Verify Docker can pull images
              docker pull alpine:latest
              # Run a test container
              docker run --rm alpine echo "Docker is working correctly"
            displayName: "Verify Docker Setup"
            timeoutInMinutes: 5
            
          # =========================================================================
          # Azure CLI Setup
          # =========================================================================

          # Install Azure CLI to interact with Azure resources
          - script: |
              echo "Installing Azure CLI..."
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash # Install Azure CLI
            displayName: "Install Azure CLI"            

          # Validate installed tools to ensure everything is ready
          - script: |
              echo "Validating installed tools..."
              az version || echo "-----------No Azure CLI Installed-----------"
              docker -v  || echo "-----------No Docker Installed-----------"
              docker info || echo "-----------Docker Daemon Not Running-----------"
            displayName: "Validate Installed Tools"

          # =========================================================================
          # Azure Authentication
          # =========================================================================

          # Login to Azure using Managed Identity
          - script: |
              echo "Authenticating with Azure using Managed Identity..."
              az login --identity
              az account show
            displayName: "Login to Azure"

          # List available Azure Container Registries
          - script: |
              echo "Listing available Azure Container Registries..."
              az acr list --subscription "ConnectedWorkers-stg" --query "[].{name:name, resourceGroup:resourceGroup}" -o table
              az group list
            displayName: "List ACRs"

          # =========================================================================
          # Docker Image Build
          # =========================================================================

          # Build Docker image for the microservice
          - script: |
              echo "##############################################################################################################"
              echo "Navigating to microservice directory..."
              cd "${{ parameters.BUILD_SOURCES_DIR }}"

              # Verify the Docker build cache 
              echo "Checking Docker build cache..."
              docker system df
              echo "##############################################################################################################"

              echo "Building Docker image for ${{ parameters.MICROSERVICE_NAME }}..." 
              docker build --no-cache -f apps/${{ parameters.FOLDER_NAME }}/Dockerfile -t ${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }}:${{ parameters.IMAGE_TAG }} .

              # Verify the built image by running a simple command
              echo "Verifying built Docker image..."
              docker run --entrypoint ls "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" -l || echo "-----------Failed to run ls -l in the container ${{ parameters.MICROSERVICE_NAME }}-----------"

              echo "Listing Docker images..."
              docker images
            displayName: "Build Docker Image"

          # =========================================================================
          # Push to Azure Container Registry
          # =========================================================================

          # Login to Azure Container Registry
          - script: |
              echo "##############################################################################################################"
              echo "Logging into Azure Container Registry..."
              az acr login -n ${{ parameters.ACR_NAME }}
            displayName: "Login to Azure Container Registry"

          # Push Docker image to ACR
          - script: |
              echo "Pushing Docker image to ACR..."
              # Delete existing image with the same tag if it exists
              az acr repository delete --name ${{ parameters.ACR_NAME }} --repository ${{ parameters.IMAGE_NAME }} --tag ${{ parameters.IMAGE_TAG }} --yes || echo "Image could not be deleted or does not exist."

              # Push the new image
              docker push "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}"
              echo "Docker image pushed successfully."
            displayName: "Push Docker Image"

          # Verify Docker image is pushed to ACR successfully
          - script: |
              echo "Listing images in ACR..."
              az acr repository list --name ${{ parameters.ACR_NAME }} --output table

              echo "Listing tags for the repository ${{ parameters.IMAGE_NAME}}..."
              az acr repository show-tags --name ${{ parameters.ACR_NAME }} --repository ${{ parameters.IMAGE_NAME}} --output table
            displayName: "Verify Docker Image Push"

          # =========================================================================
          # Cleanup
          # =========================================================================

          # Clean up local Docker image to free up space
          - script: |
              echo "Removing the Docker image from the runner..."
              docker images
              docker rmi -f "${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME}}:${{ parameters.IMAGE_TAG }}" || echo "Image could not be removed."
              echo "Image removed."
            displayName: "Clean up Docker Image"

  # =========================================================================
  # Stage 2: Deploy to AKS
  # =========================================================================
  - stage: DeployToAKS
    displayName: "Deploy to AKS"
    dependsOn: C_Integration_Stage
    #variables:
    #  IMAGE_TAG: $[ stageDependencies.DownloadImageTagStage.DownloadImageTag.outputs['setImageTag.IMAGE_TAG'] ]
    jobs:
      - job: DeployToAKS
        displayName: "Deploy Application to AKS"
        steps:
          # =========================================================================
          # Environment Setup
          # =========================================================================
          # Install required tools: Azure CLI and Helm
          - script: |
              echo "Installing Azure CLI..."
              curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
              echo "Installing Helm 3..."
              curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
            displayName: "Install Azure CLI and Helm"
          # Check kubectl version to ensure compatibility
          - script: |
              echo "Checking kubectl version..."
              kubectl version --client
            displayName: "Check kubectl version"
          # =========================================================================
          # Azure Authentication and Configuration
          # =========================================================================
          # Login to Azure and configure access to AKS
          - script: |
              echo "Logging in to Azure using managed identity..."
              az login --identity
              echo "Logging in to Azure Container Registry..."
              az acr login -n ${{ parameters.ACR_NAME }} || echo "Failed to login to ACR. Will continue anyway as this might not be critical."
              echo "Setting up kubeconfig for AKS cluster..."
              az aks get-credentials --resource-group ${{ parameters.AKS_RESOURCE_GROUP }} --name ${{ parameters.AKS_CLUSTER }} --overwrite-existing || echo "Failed to set up kubeconfig. This is critical - please check permissions and cluster availability."
              echo "Verifying AKS connection..."
              kubectl cluster-info || echo "Failed to connect to AKS cluster. This is critical - please check network connectivity and credentials."
              echo "kubeconfig setup completed."
            displayName: "Authenticate and Configure AKS Access"
          # =========================================================================
          # Microservice Namespace Setup
          # =========================================================================
          # Create the namespace for the microservice if it doesn't exist
          - script: |
              echo "Creating namespace for ${{ parameters.MICROSERVICE_NAME }} if it doesn't exist..."
              kubectl create namespace ${{ parameters.MICROSERVICE_NAMESPACE }} || echo "Namespace already exists."
              echo "Labeling namespace for network policy..."
              kubectl label namespace ${{ parameters.MICROSERVICE_NAMESPACE }} name=${{ parameters.MICROSERVICE_NAMESPACE }} --overwrite
            displayName: "Create Namespace for the microservice"

          # Apply service account for the microservice
          - script: |
              echo "Applying service account configuration..."
              kubectl apply -f ${{ parameters.HELM_CHART_DIR }}/serviceaccounts/${{ parameters.SERVICE_ACCOUNT_PATH }} || echo "Failed to apply Service Accounts. Will continue anyway."
            displayName: "Apply service account"

          # =========================================================================
          # Application Deployment
          # =========================================================================
          # Deploy the application to AKS using Helm
          - script: |
              echo "Deploying ${{ parameters.MICROSERVICE_NAME }} to AKS using Helm..."
              # Debug information to verify paths
              echo "Current directory: $(pwd)"
              echo "Navigating to the correct Helm chart directory"
              cd ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
              echo "Checking Helm chart directory contents:"
              ls -la
              helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }},image.tag=${{ parameters.IMAGE_TAG }}
              #helm upgrade --install ${{ parameters.MICROSERVICE_NAME }} . \
              #  --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} \
              #  --set image.repository=${{ parameters.ACR_NAME }}.azurecr.io/${{ parameters.IMAGE_NAME }} \
              #  --set image.tag=${{ parameters.IMAGE_TAG }} \
              #  --set fullnameOverride=${{ parameters.MICROSERVICE_NAME }} \
              #  --set nameOverride=${{ parameters.MICROSERVICE_NAME }} \
              #  --set serviceAccount.create=false \
              #  --set serviceAccount.name=shared-service-account
              #  --debug
              #
              #echo "Deployment initiated. Waiting for rollout to complete..."
              #kubectl rollout status deployment/${{ parameters.MICROSERVICE_NAME }} -n ${{ parameters.MICROSERVICE_NAMESPACE }} --timeout=180s || echo "Deployment rollout timed out or failed. Check deployment status for details."
            workingDirectory: ${{ parameters.HELM_CHART_DIR }}/${{ parameters.MICROSERVICE_NAME }}
            displayName: "Deploy to AKS"
          # =========================================================================
          # Deployment Verification
          # =========================================================================
          # Sleep for 30 seconds to allow the deployment to be fully updated
          - script: |
              echo "Sleeping for 30 seconds to allow the deployment to be fully updated..."
              sleep 30
            displayName: "Sleep for 30 seconds"
          # Verify deployment status
          - script: |
              echo "Getting deployments in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get deployments --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide
              echo "Getting pods in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide --show-labels | grep ${{ parameters.MICROSERVICE_NAME }} || echo "No pods found for ${{ parameters.MICROSERVICE_NAME }}"
              echo "Describing deployment for ${{ parameters.MICROSERVICE_NAME }}..."
              kubectl describe deployment ${{ parameters.MICROSERVICE_NAME }} --namespace ${{ parameters.MICROSERVICE_NAMESPACE }}
              echo "Getting events in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get events --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --sort-by='.lastTimestamp' | grep ${{ parameters.MICROSERVICE_NAME }} || echo "No events found for ${{ parameters.MICROSERVICE_NAME }}"
            displayName: "Verify Deployment Status"

          # =========================================================================
          # Log Collection and Analysis
          # =========================================================================

          # Get logs from the microservice pods
          - script: |
              echo "Getting logs from ${{ parameters.MICROSERVICE_NAME }} pods..."
              kubectl logs -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} --tail=50 || echo "Containers are not running or no logs available."
            displayName: "Get Pod Logs"
          # Check individual pod logs
          - script: |
              echo "Checking individual pod logs..."
              for pod in $(kubectl get pods -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o name); do
                echo "==================== Logs for $pod ===================="
                kubectl logs -n ${{ parameters.MICROSERVICE_NAMESPACE }} $pod --tail=50 || echo "Failed to get logs for $pod" 
                echo "======================================================"
              done
            displayName: "Check Pod Logs"
          # =========================================================================
          # Resource Status Checks
          # =========================================================================
          # Get ConfigMaps
          - script: |
              echo "Getting ConfigMaps in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get cm --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No ConfigMaps found in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace."
            displayName: "Get ConfigMaps"
          # Get Services
          - script: |
              echo "Getting Services in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get svc --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No Services found in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace."
            displayName: "Get Services"
          # Get Ingresses
          - script: |
              echo "Getting Ingresses in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get ing --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No Ingresses found in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace."
            displayName: "Get Ingresses"
          # Get Pods across all namespaces (filtered for ingress)
          - script: |
              echo "Getting Pods across all namespaces (filtered for ingress)..."
              kubectl get pods --all-namespaces --output wide | grep ingress || echo "No ingress pods found."
            displayName: "Get Ingress Pods"
          # Get Service Accounts
          - script: |
              echo "Getting Service Accounts in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get sa --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No Service Accounts found in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace."
            displayName: "Get Service Accounts"
          # Get Horizontal Pod Autoscalers
          - script: |
              echo "Getting HPAs in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get hpa --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No HPAs found in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace."
            displayName: "Get HPAs"
          # Get ReplicaSets
          - script: |
              echo "Getting ReplicaSets in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace..."
              kubectl get rs --namespace ${{ parameters.MICROSERVICE_NAMESPACE }} --output wide || echo "No ReplicaSets found in ${{ parameters.MICROSERVICE_NAMESPACE }} namespace."
            displayName: "Get ReplicaSets"

      # =========================================================================
      # Deployment Validation
      # =========================================================================

      # Validate the application is accessible (if it has an endpoint)
      #- script: |
      #    echo "Checking if application has an ingress..."
      #    INGRESS_HOST=$(kubectl get ing -n ${{ parameters.MICROSERVICE_NAMESPACE }} -l app.kubernetes.io/name=${{ parameters.MICROSERVICE_NAME }} -o jsonpath='{.items[0].spec.rules[0].host}' 2>/dev/null)
      #
      #    if [ -n "$INGRESS_HOST" ]; then
      #      echo "Application is exposed via ingress host: $INGRESS_HOST"
      #      echo "Attempting to validate application health..."
      #      # You can add curl commands here to check application health endpoints
      #      # curl -k https://$INGRESS_HOST/health || echo "Health check failed, but deployment will continue."
      #    else
      #      echo "No ingress found for the application. Skipping validation."
      #    fi
      #  displayName: 'Validate Application Accessibility'
      #  continueOnError: true
# =========================================================================
# Optional: Post-Deployment Notification Stage
# =========================================================================
# Uncomment this section if you want to send notifications after deployment
#
# - stage: Notify
#   displayName: 'Send Deployment Notifications'
#   dependsOn: DeployToDev
#   condition: succeededOrFailed()
#   jobs:
#   - job: SendNotifications
#     displayName: 'Send Notifications'
#     steps:
#     - script: |
#         echo "Sending deployment notification for ${{ parameters.MICROSERVICE_NAME }}..."
#         # Add your notification logic here (email, Teams, Slack, etc.)
#       displayName: 'Send Deployment Notification'

