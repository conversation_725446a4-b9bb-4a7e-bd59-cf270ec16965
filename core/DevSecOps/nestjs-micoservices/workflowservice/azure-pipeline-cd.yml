name:  CW-CD-Pipeline-Workflowservice-Microservice

trigger: none

pr: none

resources:
  pipelines:
  - pipeline: workflowengine-ci-pipeline   # Name for the resource
    source: CW-CI-Pipeline-Workflowservice-Microservice              # Name of CI pipeline in Azure DevOps
    trigger: true                   # Automatically trigger when CI pipeline completes successfully


pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'workflow'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'workflowservice'
    ACR_NAME: 'acr003nonprdemearepo'  
    IMAGE_TAG: 1011  # Use the 7071 tag for testing purposes, testing deployment to AKS cluster
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'  
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'm1-administrative-docs'
    SERVICE_ACCOUNT_PATH: "module1serviceaccount.yaml"



