name:  CW-CI-Pipeline-Workflowservice-Microservice

trigger: none
#  branches:
#    include:
#      - stagev2
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/workflow-service/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'workflow'
    FOLDER_NAME: 'workflow-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'workflowservice'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1011 # Build ID as tag for the Docker image


