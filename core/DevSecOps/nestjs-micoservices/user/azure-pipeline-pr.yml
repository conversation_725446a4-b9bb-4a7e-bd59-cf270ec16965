name: CW-PR-Pipeline-User-Microservice

trigger: none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/nestjsMicroservice/apps/user-service/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'user'
    FOLDER_NAME: 'user-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice







