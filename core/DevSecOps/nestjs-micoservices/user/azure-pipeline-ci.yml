name: CW-CI-Pipeline-User-Microsevice

trigger: none
#  branches:
#    include:
#      - stage
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/user-service/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'user'
    FOLDER_NAME: 'user-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'user'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1017 # Updated image tag from 1030 to 2030


#- stage:
#  displayName: 'Publish tag'
#  jobs:
#  - job: InstallDependencies
#    displayName: 'Install Node.js and Dependencies'
#    steps:
#    # Publish the IMAGE_TAG as a pipeline variable
#    - task: PublishPipelineArtifact@1
#      inputs:
#        targetPath: '$(Build.ArtifactStagingDirectory)/imageTag.txt'
#        artifactName: 'imageTag'
#      displayName: 'Publish IMAGE_TAG'
#      
#    - script: |
#        echo $(Build.BuildId) > $(Build.ArtifactStagingDirectory)/imageTag.txt

