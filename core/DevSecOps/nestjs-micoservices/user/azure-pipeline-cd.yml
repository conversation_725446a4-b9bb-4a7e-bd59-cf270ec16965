name: CW-CD-Pipeline-User-Microsevice

trigger: none

pr: none


pool:
  name: AZURE-VMSS-AP-DO

stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'user'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'user'
    ACR_NAME: 'acr003nonprdemearepo'
    IMAGE_TAG: 1017
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'm1-administrative-docs'


