name: CW-CICD-Pipeline-Document-Template-Microservice

trigger:
  branches:
    include:
      - devv2
  paths:
    include:
      - "**/microservices/nestjsMicroservice/apps/document-template-service/**"
      - "**/core/DevSecOps/helm/document-template/**"

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

# Define variables used across the pipeline
variables:
  # Microservice specific variables
  MICROSERVICE_NAME: "document-template"
  FOLDER_NAME: "document-template-service"
  IMAGE_NAME: "document-template"

  # Environment and configuration variables
  NODE_VERSION: "20.x"
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm
  SERVICE_ACCOUNT_PATH: "module1serviceaccount.yaml"

  # Azure resources
  ACR_NAME: 'acr003nonprdemearepo'
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
  MICROSERVICE_NAMESPACE: 'm1-administrative-docs'
  
  # Dynamic variables
  IMAGE_TAG: $(Build.BuildId) # Build ID as tag for the Docker image

  MicroService_Owner: 'ridaelayadi'   # GitHub username of the microservice owner for issue assignment

# Use the combined CI/CD template
stages:
  - template: ../temp/azure-pipeline-cicd.yml
    parameters:
      # Microservice specific parameters
      MICROSERVICE_NAME: $(MICROSERVICE_NAME)
      FOLDER_NAME: $(FOLDER_NAME)

      # Environment and configuration parameters
      NODE_VERSION: $(NODE_VERSION)
      BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
      HELM_CHART_DIR: $(HELM_CHART_DIR)

      # Image parameters
      IMAGE_NAME: $(IMAGE_NAME)
      ACR_NAME: $(ACR_NAME)
      IMAGE_TAG: $(IMAGE_TAG)

      # Deployment parameters
      AKS_CLUSTER: $(AKS_CLUSTER)
      AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
      MICROSERVICE_NAMESPACE: $(MICROSERVICE_NAMESPACE)

      # Microservice Owner
      MicroService_Owner: 'ridaelayadi'  # GitHub username of the microservice owner

# =========================================================================
# Approval Stage for Production Deployment (Optional)
# =========================================================================
# - stage: ApproveForProduction
#   displayName: 'Approve for Production'
#   dependsOn: DeployToDev
#   jobs:
#   - job: WaitForApproval
#     displayName: 'Manual Approval Required'
#     pool: server
#     timeoutInMinutes: 4320 # 3 days
#     steps:
#     - task: ManualValidation@0
#       timeoutInMinutes: 4320 # 3 days
#       inputs:
#         instructions: 'Please validate the dev deployment and approve if ready for production.'
#         onTimeout: 'reject'

# =========================================================================
# Production Deployment Stage (Optional)
# =========================================================================
# - template: ../temp/azure-pipeline-cd.yml
#   parameters:
#     MICROSERVICE_NAME: $(MICROSERVICE_NAME)
#     BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
#     HELM_CHART_DIR: $(HELM_CHART_DIR)
#     IMAGE_NAME: $(IMAGE_NAME)
#     ACR_NAME: $(ACR_NAME)
#     IMAGE_TAG: $(IMAGE_TAG)
#     AKS_CLUSTER: $(AKS_CLUSTER)
#     AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
#     MICROSERVICE_NAMESPACE: 'production'
