name: CW-CI-Pipeline-Document-Template-Microservice

trigger: none
#  branches:
#    include:
#      - stagev2
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/document-template-service/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'document-template'
    FOLDER_NAME: 'document-template-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'document-template'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1017 # Updated image tag from 1030 to 2030


