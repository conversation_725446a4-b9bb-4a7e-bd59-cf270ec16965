name: CW-PR-Pipeline-Document-Template-Microservice

trigger: none

pr:
  branches:
    include:
      - devv2
  paths:
    include:
      - "**/microservices/nestjsMicroservice/apps/document-template-service/**"

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu].

stages:
  - template: ../temp/azure-pipeline-pr.yml
    parameters:
      MICROSERVICE_NAME: "document-template"
      FOLDER_NAME: "document-template-service"
      NODE_VERSION: "20.x"
      BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
