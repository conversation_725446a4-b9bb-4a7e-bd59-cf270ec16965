name: CW-CD-Pipeline-Workstation-Microservice

trigger: none
pr: none

pool:
  name: AZURE-VMSS-AP-DO

variables:
  MICROSERVICE_NAME: 'workstation'
  FOLDER_NAME: 'workstation-service'
  IMAGE_NAME: 'workstation'
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm
  SERVICE_ACCOUNT_PATH: "crewmanagementserviceaccount.yaml"
  ACR_NAME: 'acr003nonprdemearepo'
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
  MICROSERVICE_NAMESPACE: 'crew-management'

# Use the CD template
stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: $(MICROSERVICE_NAME)
    FOLDER_NAME: $(FOLDER_NAME)
    HELM_CHART_DIR: $(HELM_CHART_DIR)
    IMAGE_NAME: $(IMAGE_NAME)
    ACR_NAME: $(ACR_NAME)
    AKS_CLUSTER: $(AKS_CLUSTER)
    AKS_RESOURCE_GROUP: $(AKS_RESOURCE_GROUP)
    MICROSERVICE_NAMESPACE: $(MICROSERVICE_NAMESPACE) 