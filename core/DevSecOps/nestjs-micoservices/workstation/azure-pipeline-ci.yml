name: CW-CI-Pipeline-Workstation-Microservice

trigger: none
pr: none

pool:
  name: AZURE-VMSS-AP-DO

variables:
  # Microservice specific variables
  MICROSERVICE_NAME: 'workstation'
  FOLDER_NAME: 'workstation-service'
  IMAGE_NAME: 'workstation'
  
  # Environment and configuration variables
  NODE_VERSION: '20.x'
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
  
  # Azure resources
  ACR_NAME: 'acr003nonprdemearepo'
  
  # Dynamic variables
  IMAGE_TAG: $(Build.BuildId)

# Use the CI template
stages:
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: $(MICROSERVICE_NAME)
    FOLDER_NAME: $(FOLDER_NAME)
    NODE_VERSION: $(NODE_VERSION)
    BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR)
    IMAGE_NAME: $(IMAGE_NAME)
    ACR_NAME: $(ACR_NAME)
    IMAGE_TAG: $(IMAGE_TAG) 