name: CW-PR-Pipeline-Workstation-Microservice

trigger: none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/nestjsMicroservice/apps/workstation-service/**'

pool:
  name: AZURE-VMSS-AP-DO

variables:
  MICROSERVICE_NAME: 'workstation'
  NODE_VERSION: '20.x'
  BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice

# Use the PR template
stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: $(MICROSERVICE_NAME)
    NODE_VERSION: $(NODE_VERSION)
    BUILD_SOURCES_DIR: $(BUILD_SOURCES_DIR) 