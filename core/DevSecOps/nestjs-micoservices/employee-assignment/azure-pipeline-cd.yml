name: CW-CD-Pipeline-Employee-Assignment-Microservice

trigger: none

pr: none

pool:
  name: AZURE-VMSS-AP-DO

stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'employee-assignment'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'employee-assignment'
    ACR_NAME: 'acr003nonprdemearepo'
    IMAGE_TAG: 1020 #$(Build.BuildId) # Dynamic image tag based on build ID
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'crew-management' 
    SERVICE_ACCOUNT_PATH: "crewmanagementserviceaccount.yaml"