name: CW-CI-Pipeline-Employee-Assignment-Microservice

trigger: none
#  branches:
#    include:
#      - stage
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/employee-assignment/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'employee-assignment'
    FOLDER_NAME: 'employee-assignment'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'employee-assignment'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1020 #$(Build.BuildId) # Dynamic image tag based on build ID 