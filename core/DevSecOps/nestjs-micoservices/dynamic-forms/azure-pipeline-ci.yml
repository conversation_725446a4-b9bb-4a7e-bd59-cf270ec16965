name: CW-CI-Pipeline-Dynamic-Forms-Microservice

trigger: none
#  branches:
#    include:
#      - stagev2
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/dynamic-forms-service/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'dynamic-forms'
    FOLDER_NAME: 'dynamic-forms-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'dynamic-forms'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1018 # Updated image tag from 1030 to 2030


