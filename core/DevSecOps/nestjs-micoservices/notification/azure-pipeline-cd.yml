name: CW-CD-Pipeline-Notification-Microservice

trigger: none

pr: none

resources:
  pipelines:
  - pipeline: notification-ci-pipeline   # Name for the resource
    source: CW-CI-Pipeline-Notification-Microservice              # Name of CI pipeline in Azure DevOps
    trigger: true                   # Automatically trigger when CI pipeline completes successfully


pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'notification'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'notification'
    ACR_NAME: 'acr003nonprdemearepo'  
    IMAGE_TAG: 1018
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'  
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'notification'



