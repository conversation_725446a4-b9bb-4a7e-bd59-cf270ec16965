name: CW-CI-Pipeline-Notification-Microservice

trigger: none
#  branches:
#    include:
#      - stage
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/notification/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu].

stages:
  # Main Pipeline Stages
  - template: ../temp/azure-pipeline-ci.yml
    parameters:
      MICROSERVICE_NAME: "notification"
      FOLDER_NAME: "notification"
      NODE_VERSION: "20.x"
      BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
      IMAGE_NAME: "notification"
      ACR_NAME: "acr003nonprdemearepo" # Azure Container Registry name
      IMAGE_TAG: 1018
