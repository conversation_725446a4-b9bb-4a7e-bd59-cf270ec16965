name: CW-CD-Pipeline-Operator-Management-Microservice

trigger: none

pr: none

resources:
  pipelines:
  - pipeline: operator-management-ci-pipeline   # Name for the resource
    source: CW-CI-Pipeline-Operator-Management-Microservice  # This should match EXACTLY the name of your CI pipeline in Azure DevOps
    trigger: true                   # Automatically trigger when CI pipeline completes successfully

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- stage: DownloadImageTagStage
  displayName: 'Download Image Tag Stage'
  jobs:
  - job: DownloadImageTag
    displayName: 'Download Image Tag Artifact'
    steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'specific'
        project: 'AZ-ConnectedWorkers'
        pipeline: '18'  # Update this with the correct pipeline ID for operator-management
        buildVersionToDownload: 'latest'
        downloadType: 'single'
        artifactName: 'ImageTag'
        downloadPath: '$(Build.ArtifactStagingDirectory)'
      displayName: 'Download Image Tag Artifact'

    - script: |
        # Read the updated IMAGE_TAG value from the downloaded artifact file
        IMAGE_TAG=$(cat $(Build.ArtifactStagingDirectory)/ImageTag/imageTag.txt)
        
        # Update the IMAGE_TAG variable for subsequent tasks or jobs
        echo "##vso[task.setvariable variable=IMAGE_TAG]$IMAGE_TAG"
        
        # Print the updated IMAGE_TAG value for debugging/logging
        echo "Updated IMAGE_TAG: $IMAGE_TAG"
      displayName: 'Set IMAGE_TAG variable'

- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'operator-management'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'operator-management'
    ACR_NAME: 'acr003nonprdemearepo'  
    IMAGE_TAG: 1014 #$(IMAGE_TAG)  # Use the downloaded image tag
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'  
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'crew-management'
    SERVICE_ACCOUNT_PATH: "crewmanagementserviceaccount.yaml"