name: CW-PR-Pipeline-Operator-Management-Microservice

trigger: none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/nestjsMicroservice/apps/operator-management/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'operator-management'
    FOLDER_NAME: 'operator-management'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice 