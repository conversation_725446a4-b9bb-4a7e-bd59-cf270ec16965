name: CW-CD-Pipeline-Document-Generation-Microservice

trigger: none

pr: none

resources:
  pipelines:
  - pipeline: document-generation-ci-pipeline   # Name for the resource
    source: CW-CI-Pipeline-Document-Generation-Microservice             # Name of CI pipeline in Azure DevOps
    trigger: true                   # Automatically trigger when CI pipeline completes successfully


pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'document-generation'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'document-generation'
    ACR_NAME: 'acr003nonprdemearepo'  
    IMAGE_TAG: 1017  # Updated image tag from 1030 to 2030
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'  
    MICROSERVICE_NAMESPACE: 'm1-administrative-docs'
    SERVICE_ACCOUNT_PATH: "module1serviceaccount.yaml"



