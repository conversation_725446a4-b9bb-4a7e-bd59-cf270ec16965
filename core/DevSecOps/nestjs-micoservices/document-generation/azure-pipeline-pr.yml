name: CW-PR-Pipeline-Document-Generation-Microservice

trigger: none

pr:
  branches:
    include:
      - devv2
  paths:
    include:
      - '**/microservices/nestjsMicroservice/apps/document-generation-service/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'document-generation'
    FOLDER_NAME: 'document-generation-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice







