# NestJS Microservices Pipeline Documentation

## Overview
This repository contains Azure DevOps pipeline configurations for multiple NestJS microservices. The pipeline structure follows a template-based approach for consistency across services while maintaining separate execution contexts for better observability and troubleshooting.

![Pipeline Architecture](https://mermaid.ink/img/pako:eNqNkk9PwzAMxb9KlBOI9QNwQKCJw4QYEhyAQy5NvK6iTSrHQYOq786aFgZiB3rxK_7l_ew8oEkNYoQbZ3vvjCXYOm-JvHFkKZANpJwdyZHfUfKWYJPJkYJnWOcpkXgbKGQXyZPNJJvJkYu0Iy_QRnIUyeQcuQg7iiZHMZnMkfMUKdrkHLlIO_LJ5Bw5SjsKyeQcuUg78iaZHMVI2lFMJufIRdqRTyZHMZF2FJLJOXKRduSTyTlyJe0oJJNz5CLtyJtkcpRIaUfRJJNz5CLtyJtkcuQr0o5CMjlHLtKOfDI5R66lHYVkco5cpB15k0yOopJ2FE0yOUcu0o68SSZHXks7CsnkHLlIO_LJ5By5kXYUksk5cpF25E0yOYpG2lE0yeQcuUg78iaZHPla2lFIJufIRdqRTyZHvpN2FJLJOfIXaUfBJJOjaCXtKJpkco5cpB15k0yOfCftKCSTs-QfLgEbxQ?type=png)

## Pipeline Structure

### Services
The following microservices are configured with dedicated pipeline sets:
- Document Generation Service
- Document Template Service
- Dynamic Forms Service
- Notification Service
- Request Service
- User Service
- Workflow Engine Service

### Pipeline Types
Each service has three pipeline configurations:
1. **CI Pipeline** (Continuous Integration)
   - Builds and tests the application
   - Creates Docker images
   - Pushes to Azure Container Registry
   
2. **CD Pipeline** (Continuous Deployment)
   - Triggered by successful CI pipeline completion
   - Deploys to Kubernetes using Helm charts
   - Validates deployment status
   
3. **PR Pipeline** (Pull Request Validation)
   - Validates code quality on pull requests
   - Runs tests and security scans
   - Ensures code meets standards before merging

### Template Files
Located in `/temp` directory:
- `azure-pipeline-ci.yml`: Template for CI pipelines
- `azure-pipeline-cd.yml`: Template for CD pipelines
- `azure-pipeline-pr.yml`: Template for PR validation pipelines

## Pipeline Stages Detailed Workflow

### CI Pipeline Workflow
![CI Pipeline Flow](https://mermaid.ink/img/pako:eNqNksFqwzAMhl9F-NRCu-cQWMnWHQbrsIXtYvxhNWaxg-0UWsrefdLSJWvZYTpZ-vX9kiwfoDUGsYKdtYN3xhLsnfNEzjiyFMgGUs6O5MjvKDlLsM_kyAXPsM1TIvE2UMgukiebSXaTIxdpR16gjeTIk8k5chF2FEyOYjKZI-cpUrTJOXKRduSTyTlylHYUksk5cpF25E0yOYqRtKOYTM6Ri7Qjn0yOYiLtKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Install Dependencies**
   - Installs Node.js (v20.x)
   - Sets up pnpm
   - Installs project dependencies
   - Caches node_modules for performance

2. **Lint and Format**
   - Runs code formatting
   - Performs linting checks
   - Ensures code quality standards

3. **Test**
   - Executes unit tests
   - Generates test coverage reports
   - Validates functionality

4. **Security Audit and Build**
   - Runs security audit on dependencies
   - Builds the application
   - Publishes build artifacts

5. **Docker Build and Push**
   - Builds Docker image
   - Pushes to Azure Container Registry (ACR)
   - Tags with appropriate version

### CD Pipeline Workflow
![CD Pipeline Flow](https://mermaid.ink/img/pako:eNqNkk9rwzAMxb-K8KmFdp9jsCZbd9jWwzrYLsYf1mAXO9hOoaXsd5-0dMla1sNOlp5-7z1JPoA2GsQKdtYO3mlDsHfOEznjyFAgG0g5O5Ijv6PkLME-kyMXPMM2T4nE20Ahu0iebCbZTY5cpB15gTaSI08m58hF2FEwOYrJZI6cp0jRJufIRdqRTybnyCntKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Deploy to Dev**
   - Authenticates with Azure
   - Configures AKS access
   - Creates or updates namespaces
   - Installs NGINX Ingress Controller if needed
   - Deploys to AKS cluster using Helm
   - Validates deployment status

### PR Pipeline Workflow
![PR Pipeline Flow](https://mermaid.ink/img/pako:eNqNkk9rwzAMxb-K8KmFdp9jsCZbd9jWwzrYLsYf1mAXO9hOoaXsd5-0dMla1sNOlp5-7z1JPoA2GsQKdtYO3mlDsHfOEznjyFAgG0g5O5Ijv6PkLME-kyMXPMM2T4nE20Ahu0iebCbZTY5cpB15gTaSI08m58hF2FEwOYrJZI6cp0jRJufIRdqRTybnyCntKCSTc-Qi7cibZHIUI2lHMZmcIxdpRz6ZHMVEnoNQJufIRdqRN8nkKCrpRNEkk3PkIu3Im2Ry5Ct5DkIyOUcu0o58MjlHruU5CMnkHLlIO_ImmRxFJZ0ommRyjlykHXmTTI68lucgJJNz5CLtyCeTc-RGnoOQTM6Ri7Qjb5LJUTTyHESTTM6Rv0g7CiaZHEUrz0E0yeQcuUg78iaZHPlOnoOQTM6Si7QjP_0Hx_ZXdw?type=png)

1. **Install Dependencies**
   - Sets up development environment
   - Installs required packages

2. **Lint and Format**
   - Validates code style
   - Checks for code quality issues

3. **Test**
   - Runs unit tests
   - Generates coverage reports

4. **Security Audit and Build**
   - Scans for vulnerabilities
   - Builds application to verify compilation

## Configuration Parameters

### Common Parameters
- `MICROSERVICE_NAME`: Name of the microservice
- `NODE_VERSION`: Node.js version (default: 20.x)
- `BUILD_SOURCES_DIR`: Source directory path
- `IMAGE_NAME`: Docker image name
- `ACR_NAME`: Azure Container Registry name
- `IMAGE_TAG`: Docker image tag

### CD-Specific Parameters
- `AKS_CLUSTER`: AKS cluster name
- `AKS_RESOURCE_GROUP`: Resource group name
- `MICROSERVICE_NAMESPACE`: Kubernetes namespace for deployment

## Infrastructure Details

### Azure Resources
- **Container Registry**: acr003nonprdemearepo
- **AKS Cluster**: aks003-NonPrd-EMEA
- **Resource Group**: RG-Dev-CONNWORKERS-AKS
- **Agent Pool**: AZURE-VMSS-AP-DO (Ubuntu-based self-hosted runner)

## Usage

### Setting up a New Microservice
1. Create a new directory for your microservice
2. Copy the three pipeline files (ci, cd, pr)
3. Update the parameters section with your service name
4. Configure the appropriate triggers and paths

### Running Pipelines
- **CI Pipeline**: Triggered manually or by schedule
- **CD Pipeline**: Triggered by successful CI pipeline completion
- **PR Pipeline**: Triggered on PR to dev branch with path filters


## Best Practices
1. Always use the template files for consistency
2. Maintain proper versioning for Docker images
3. Keep security scanning enabled
4. Ensure proper test coverage
5. Use caching for better pipeline performance

## Security Considerations
- Security audit runs on production dependencies
- Docker images are scanned for vulnerabilities
- Azure authentication uses managed identities
- Access controls are implemented through Azure RBAC

## Troubleshooting
- Check pipeline logs for detailed error messages
- Verify Azure credentials and permissions
- Ensure Docker daemon is running for CI builds
- Validate Kubernetes configurations 


## Reusable Pipeline Benefits and Best Practices

### Advantages of Reusable Pipelines

1. **Standardization**
   - Ensures consistent build and deployment processes across all microservices
   - Reduces configuration drift between services
   - Enforces organizational standards and best practices

2. **Maintenance Efficiency**
   - Single source of truth for pipeline logic
   - Updates to pipeline logic only need to be made once
   - Reduces the risk of configuration errors
   - Easier to implement global changes across all microservices

3. **Time Savings**
   - Faster onboarding of new microservices
   - Reduced time spent on pipeline configuration
   - Quick implementation of new services
   - Reusable code blocks and templates

4. **Quality Control**
   - Consistent quality checks across all services
   - Standardized security scanning
   - Uniform testing practices
   - Centralized policy enforcement

5. **Cost Optimization**
   - Reduced maintenance overhead
   - Optimized pipeline execution
   - Shared resources and caching
   - Efficient use of build agents

6. **Separate Execution Contexts**
   - Isolated logs for each microservice
   - Easier troubleshooting and debugging
   - Independent execution and failure handling
   - Better visibility into individual service pipelines

### Reusable vs. Dynamic Pipelines

| Feature | Reusable Pipelines | Dynamic Pipelines |
|---------|-------------------|-------------------|
| Log Separation | ✅ Separate logs per service | ❌ Combined logs |
| Troubleshooting | ✅ Easier to debug | ❌ More complex |
| Execution | ✅ Independent execution | ❌ Coupled execution |
| Visibility | ✅ Clear service boundaries | ❌ Mixed service data |
| Failure Isolation | ✅ Failures don't affect other services | ❌ Potential cascading failures |
| Configuration | ✅ Explicit per service | ❌ More complex conditions |

### Best Practices for Reusable Pipelines

1. **Template Design**
   - Keep templates modular and focused
   - Use clear parameter naming conventions
   - Document all parameters and their usage
   - Include default values where appropriate
   - Implement proper error handling

2. **Version Control**
   - Version your pipeline templates
   - Maintain a changelog
   - Use semantic versioning for templates
   - Test template changes before deployment

3. **Parameter Management**
   - Use clear parameter naming conventions
   - Provide default values where possible
   - Validate parameter inputs
   - Document all required and optional parameters

4. **Error Handling**
   - Implement proper error messages
   - Include troubleshooting guidance
   - Add validation steps
   - Provide fallback options

5. **Documentation**
   - Maintain comprehensive documentation
   - Include usage examples
   - Document common issues and solutions
   - Keep documentation up-to-date

6. **Testing**
   - Test templates with different parameter combinations
   - Validate templates across different environments
   - Maintain test cases for templates
   - Regular validation of template functionality

7. **Monitoring and Maintenance**
   - Monitor pipeline performance
   - Track usage patterns
   - Regular updates and improvements
   - Collect feedback from users

8. **Security**
   - Regular security reviews
   - Implement least privilege access
   - Secure sensitive parameters
   - Regular vulnerability scanning

## Troubleshooting

### Common Issues
- Check pipeline logs for detailed error messages
- Verify Azure credentials and permissions
- Ensure Docker daemon is running for CI builds
- Validate Kubernetes configurations 

### Debugging Tips
- Use the `echo` command to print debug information
- Check resource availability in Azure
- Verify network connectivity between services
- Review Kubernetes events for deployment issues




   