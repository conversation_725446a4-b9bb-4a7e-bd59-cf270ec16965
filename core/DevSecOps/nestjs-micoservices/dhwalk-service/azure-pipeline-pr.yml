name: CW-PR-Pipeline-DHWalk-Microservice

trigger: none

pr:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/microservices/nestjsMicroservice/apps/dhwalk-service/**'

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-pr.yml
  parameters:
    MICROSERVICE_NAME: 'dhwalk'
    FOLDER_NAME: 'dhwalk-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice 