name: CW-CI-Pipeline-DHWalk-Microservice

trigger: none
#  branches:
#    include:
#      - stage
#  paths:
#    include:
#      - '**/microservices/nestjsMicroservice/apps/dhwalk-service/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:

# Main Pipeline Stages
- template: ../temp/azure-pipeline-ci.yml
  parameters:
    MICROSERVICE_NAME: 'dhwalk'
    FOLDER_NAME: 'dhwalk-service'
    NODE_VERSION: '20.x'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'dhwalk'
    ACR_NAME: 'acr003nonprdemearepo'  # Azure Container Registry name
    IMAGE_TAG: 1013 #$(Build.BuildId) # Build ID as tag for the Docker image 