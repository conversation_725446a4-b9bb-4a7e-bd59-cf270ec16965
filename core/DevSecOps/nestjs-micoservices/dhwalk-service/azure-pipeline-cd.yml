name: CW-CD-Pipeline-DHWalk-Microservice

trigger: none

pr: none

#resources:
#  pipelines:
#  - pipeline: dhwalk-ci-pipeline   # Name for the resource
#    source: CW-CI-Pipeline-DHWalk-Microservice              # Name of CI pipeline in Azure DevOps
#    trigger: true                   # Automatically trigger when CI pipeline completes successfully


pool:
  name: AZURE-VMSS-AP-DO # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]. 

stages:
- template: ../temp/azure-pipeline-cd.yml
  parameters:
    MICROSERVICE_NAME: 'dhwalk'
    BUILD_SOURCES_DIR: $(Build.SourcesDirectory)/core/microservices/nestjsMicroservice
    IMAGE_NAME: 'dhwalk'
    ACR_NAME: 'acr003nonprdemearepo'  
    IMAGE_TAG: 1013 #$(Build.BuildId)  # Use the Build ID as tag for the Docker image
    AKS_CLUSTER: 'aks003-NonPrd-EMEA'  
    AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
    MICROSERVICE_NAMESPACE: 'crew-management' 
    SERVICE_ACCOUNT_PATH: "crewmanagementserviceaccount.yaml"