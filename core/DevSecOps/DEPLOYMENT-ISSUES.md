# Deployment issues:

We are documenting some deployment issues we have encountered so far in the development subscription to avoid these errors in the staging and production environments.

### AKS security configuration:

We are using connworkIdentity01 as User Defined Managed Identity to access Data and PaaS services from AKS.

We enabled on Dev environment:
- OIDC.
- Workload Identity.

We should take this into consideration on Test and Production environment Deployment.

### Private endpoints instead of Public URIs:

When using Public URIs for cosmos DB for example, we got a firewall error preventing us from connecting to Cosmos DB.

Solution : we should use private endpoints.

### Data plane config:

In order to connect our pods in AKS and Cosmos DB, we are using DocumentDB Account Contributor role.
But this role is set only to manage accounts.

But to do CRUD operations on Cosmos DB we need to add :

- Cosmos DB Account Reader Role.
- Cosmos DB Built-in Data Contributor.

For more details on the commands that we can use :

https://stackoverflow.com/questions/********/cannot-assign-azure-role-for-cosmos-db
