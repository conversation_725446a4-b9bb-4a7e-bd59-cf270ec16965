name: CW-Stage-Shared-Ingress-Deployment

trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - '**/DevSecOps/helm/shared-ingress/**'
      - '**/DevSecOps/Shared_Ingress_AKS/**'

pr: none

pool:
  name: AZURE-VMSS-AP-DO  # Use the Self Hosted Runner VMSS for the pipeline agent [Ubuntu]

# Define variables used across the pipeline
variables:
  # Environment and configuration variables
  HELM_CHART_DIR: $(Build.SourcesDirectory)/core/DevSecOps/helm
  
  # Azure resources
  AKS_CLUSTER: 'aks003-NonPrd-EMEA'
  AKS_RESOURCE_GROUP: 'RG-Dev-CONNWORKERS-AKS'
  MICROSERVICE_NAMESPACE: 'sharedingressnamespace'

stages:
# =========================================================================
# Stage 1: Deploy Shared Ingress
# =========================================================================
- stage: DeploySharedIngress
  displayName: 'Deploy Shared Ingress'
  jobs:
  - job: DeploySharedIngress
    displayName: 'Deploy Shared Ingress to AKS'
    steps:
    # =========================================================================
    # Environment Setup
    # =========================================================================
    
    # Install required tools: Azure CLI and Helm
    - script: |
        echo "Installing Azure CLI..."
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
        
        echo "Installing Helm 3..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
      displayName: 'Install Azure CLI and Helm'
    
    # Check kubectl version to ensure compatibility
    - script: |
        echo "Checking kubectl version..."
        kubectl version --client
      displayName: 'Check kubectl version'

    # =========================================================================
    # Azure Authentication and Configuration
    # =========================================================================
    
    # Login to Azure and configure access to AKS
    - script: |
        echo "Logging in to Azure using managed identity..."
        az login --identity
        
        echo "Setting up kubeconfig for AKS cluster..."
        az aks get-credentials --resource-group $(AKS_RESOURCE_GROUP) --name $(AKS_CLUSTER) --overwrite-existing || echo "Failed to set up kubeconfig. This is critical - please check permissions and cluster availability."
        
        echo "Verifying AKS connection..."
        kubectl cluster-info || echo "Failed to connect to AKS cluster. This is critical - please check network connectivity and credentials."
        
        echo "kubeconfig setup completed."
      displayName: 'Authenticate and Configure AKS Access'
    
    # =========================================================================
    # Namespace Setup
    # =========================================================================
    
    # Create the namespace if it doesn't exist
    - script: |
        echo "Creating namespace if it doesn't exist..."
        kubectl create namespace $(MICROSERVICE_NAMESPACE) || echo "Namespace already exists."
        
        echo "Labeling namespace for network policy..."
        kubectl label namespace $(MICROSERVICE_NAMESPACE) name=$(MICROSERVICE_NAMESPACE) --overwrite
      displayName: 'Create Namespace'

    # =========================================================================
    # Shared Ingress Deployment
    # =========================================================================
    
    # Deploy the shared ingress using Helm
    - script: |
        echo "Deploying shared ingress..."
        echo "Current directory: $(pwd)"
        echo "Navigating to shared ingress Helm chart directory..."
        cd $(HELM_CHART_DIR)/shared-ingress
        
        echo "Deploying shared ingress using Helm..."
        helm upgrade --install shared-ingress . --namespace $(MICROSERVICE_NAMESPACE)
      displayName: 'Deploy Shared Ingress'

    # =========================================================================
    # Deployment Verification
    # =========================================================================
    
    # Verify ingress deployment
    - script: |
        echo "Verifying shared ingress deployment..."
        echo "Waiting for ingress to be ready..."
        sleep 30  # Give time for the ingress to be fully updated

        echo "Current ingress configuration:"
        kubectl get ingress shared-ingress -n $(MICROSERVICE_NAMESPACE) -o yaml

        echo "Getting ingress details..."
        kubectl describe ingress shared-ingress -n $(MICROSERVICE_NAMESPACE)
      displayName: 'Verify Ingress Deployment'

    # Get events to troubleshoot any issues
    - script: |
        echo "Getting events in $(MICROSERVICE_NAMESPACE) namespace..."
        kubectl get events --namespace $(MICROSERVICE_NAMESPACE) --sort-by='.lastTimestamp' | grep shared-ingress || echo "No events found for shared-ingress"
      displayName: 'Get Events'

    # Get services to verify endpoints
    - script: |
        echo "Getting services in $(MICROSERVICE_NAMESPACE) namespace..."
        kubectl get svc --namespace $(MICROSERVICE_NAMESPACE) --output wide
      displayName: 'Get Services' 
