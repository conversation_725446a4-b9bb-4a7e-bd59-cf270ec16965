#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo "🔍 Validating branch name..."
pnpm run validate-branch

echo "📡 Checking remote updates..."
git fetch origin

echo "🔄 Pulling latest changes from dev..."
git pull origin dev

# Check if current branch is behind dev (non-blocking warning)
if git merge-base --is-ancestor origin/dev HEAD; then
  echo "✅ Your branch includes latest changes from dev"
else
  echo "⚠️  Warning: origin/dev has newer commits"
  echo "💡 Consider running: git rebase origin/dev"
  echo "🔄 Continuing with push..."
fi

# Temporarily disabled lint checks
# echo "🧹 Running lint and format checks..."
# cd core/microservices/nestjsMicroservice
# pnpm run lint
# pnpm run format

echo "🧪 Running tests..."
cd core/microservices/nestjsMicroservice
pnpm run test

echo "🏗️ Building project..."
pnpm run build 